.DS_Store
node_modules
/dist

package-lock.json

/tests/e2e/videos/
/tests/e2e/screenshots/
tests/unit/coverage/

# local env files
.env.local
.env.*.local

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw*

build/env.js
/node_modules/
/vue.config.js
.eslintignore

# 配置文件
vue.config.js
.qodo

# 忽略所有说明文档
*.md
!README.md
**/docs/**/*.md
**/docs/
.cursor/
