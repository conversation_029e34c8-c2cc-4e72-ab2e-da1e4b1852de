(function(e){function t(t){for(var a,c,r=t[0],s=t[1],l=t[2],u=0,d=[];u<r.length;u++)c=r[u],Object.prototype.hasOwnProperty.call(o,c)&&o[c]&&d.push(o[c][0]),o[c]=0;for(a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a]);m&&m(t);while(d.length)d.shift()();return i.push.apply(i,l||[]),n()}function n(){for(var e,t=0;t<i.length;t++){for(var n=i[t],a=!0,c=1;c<n.length;c++){var r=n[c];0!==o[r]&&(a=!1)}a&&(i.splice(t--,1),e=s(s.s=n[0]))}return e}var a={},c={app:0},o={app:0},i=[];function r(e){return s.p+"js/"+({}[e]||e)+"."+{"chunk-00ea03aa":"677531aa","chunk-01175ac8":"22219791","chunk-02262664":"f0d31e3a","chunk-0b1cf41e":"8f09227d","chunk-0c0cc883":"35fb2ca3","chunk-0ca3207b":"1903d8bf","chunk-0d6f39f2":"ce857724","chunk-0e219e0a":"d0059aa8","chunk-102c213c":"66cd5947","chunk-10a60bb9":"eff9a95b","chunk-11a770b5":"185af4a1","chunk-14b9857b":"099a918b","chunk-14e34fd6":"34ee4c38","chunk-15317ec4":"6bd52a1b","chunk-12cd12ee":"d938893e","chunk-15426580":"915d2169","chunk-16dd0f66":"c228c77d","chunk-18b344ff":"b11cd983","chunk-1942c74d":"d055eda2","chunk-2d237ad7":"c874fd58","chunk-a7f9d97c":"65f57e0d","chunk-1ade1f66":"5e844019","chunk-1dc45689":"0de77e25","chunk-1e40043a":"96da3ebe","chunk-1f5746f4":"820539fc","chunk-21556441":"11f18c60","chunk-22e8860b":"e228ac02","chunk-2301e1ef":"4ad124a5","chunk-23c6e1af":"b70a8695","chunk-23d3b750":"15f3c2c9","chunk-24c32cac":"c4e68efe","chunk-2521a24a":"e0ce55f2","chunk-26853dd0":"f5385422","chunk-28e8e413":"f9138a97","chunk-2c359864":"d05498c5","chunk-2d0aa5b8":"7d5f546e","chunk-068f3754":"8a2e6db4","chunk-2d0bff35":"7266d0e8","chunk-2d22dd29":"34f0560d","chunk-347e71a8":"76c46a20","chunk-2d0baaeb":"6d2374e0","chunk-2d229b36":"f43e6854","chunk-6d455b82":"c9032f86","chunk-85384882":"a8efcfe2","chunk-2d0a45ef":"9b19b7b4","chunk-2d0e48bc":"e02be97b","chunk-d7c3d1f6":"b9a0f41b","chunk-2d0d660d":"e337c1c4","chunk-2d0decc5":"5e02a491","chunk-2d0e51c0":"bd9f285d","chunk-30f43d24":"e6654c34","chunk-313b4a34":"af742470","chunk-3147487a":"acbb4920","chunk-320e0938":"e2f02eab","chunk-3385141a":"a909543e","chunk-34cbb637":"4334259e","chunk-3545909f":"bcd88412","chunk-39eb3550":"27c5e5c7","chunk-35748faf":"0824e5ce","chunk-36d0a25f":"aafd949b","chunk-3700f517":"9baddb99","chunk-39807813":"7731640f","chunk-39d3d59e":"c2f64276","chunk-36315a4c":"33bc2787","chunk-392dc157":"c3bc8ce9","chunk-9a319a28":"469b28b9","chunk-de26f0aa":"aacd0967","chunk-3ac0a32f":"9e1c8d4d","chunk-3bfafdc2":"3fc08638","chunk-3d3a637e":"082a7c94","chunk-3da7f730":"c8a44151","chunk-420b7338":"116a370d","chunk-450656cc":"cf09a8fb","chunk-48264968":"4dab9645","chunk-4a02fcee":"b8c41afd","chunk-4daabb95":"21dabb39","chunk-4f20c4bb":"b3fe0a73","chunk-522d6fa4":"6ae90399","chunk-012bc5f4":"636e1445","chunk-012f97c7":"fca69f7f","chunk-028d8a59":"1679c777","chunk-0b51c142":"9f4a7826","chunk-0cffe042":"8da38196","chunk-0fab8be6":"dc68b7eb","chunk-11f2242b":"7d713ed7","chunk-13e901c7":"ee722579","chunk-0d065d94":"674de8f0","chunk-1708a62c":"a4777056","chunk-19278d2b":"181628a3","chunk-0b30fc26":"88d5db4c","chunk-1cc512a8":"d28a8b85","chunk-1fdfb292":"ce66d4e6","chunk-********":"d259909e","chunk-2329612d":"56a44cf8","chunk-23596e77":"20d26f7f","chunk-2838216f":"db07d94f","chunk-1cce1846":"9077a527","chunk-309af1d5":"ea5ceceb","chunk-48fb23e7":"a070bdd1","chunk-422aaf0c":"afb1280d","chunk-480b826e":"d9c0e923","chunk-2ed110c0":"b51b8a12","chunk-30925022":"03ab3893","chunk-33544970":"ff6d4076","chunk-33e028d4":"543e0881","chunk-368b7e8b":"f75e0a05","chunk-399f9916":"2f20c061","chunk-3a07fecf":"2226cb37","chunk-3d9cad84":"a014be8c","chunk-3dd62379":"e8e33434","chunk-3fd7faf3":"e21ced71","chunk-43b4168a":"c7ff7d41","chunk-47eccb06":"1a5c6e4a","chunk-4cc2a241":"1b1e1c39","chunk-4d50a3ca":"48add7bd","chunk-4fe0f6d8":"33c4f864","chunk-5215dd0c":"67febf92","chunk-58880d83":"98357410","chunk-5f1a6de2":"47b8d2c2","chunk-6120a01e":"aff50033","chunk-66ab560d":"cb2d8160","chunk-84f36746":"91061c02","chunk-8c9b7574":"a9ca4029","chunk-98ffbe16":"7497c56e","chunk-a1c5b23e":"1a6a76a1","chunk-a6062dfe":"d9bc8e37","chunk-a948aa0a":"eeb93578","chunk-ac9b06d4":"7f839471","chunk-ae66aca4":"ba2da2f2","chunk-bc1af0b8":"e6b42a4c","chunk-bdaf01ce":"5ffea589","chunk-c0cb18d6":"14c7acba","chunk-cf4c1516":"647166c2","chunk-cf78aefe":"9b6b8d08","chunk-dee6defa":"03619901","chunk-e492816e":"cbc67260","chunk-e766a746":"f2aa18f5","chunk-ee67481a":"208dae70","chunk-f6211cec":"30ad682c","chunk-18603135":"c5e3e55b","chunk-12cfd24a":"516e4acc","chunk-270d63d7":"0344fc2e","chunk-de7ad25e":"5aaeca36","chunk-2f52cadf":"5bd5c134","chunk-603786c0":"dee8a9d4","chunk-75b23b0c":"23e9735c","chunk-53230ece":"93ff9e5d","chunk-546983df":"27e0ae95","chunk-55d74b39":"07dcd874","chunk-56a3dd1d":"c5915ea5","chunk-5a32affb":"1cfee00b","chunk-5fca294c":"771c9946","chunk-611b4a0c":"38e8fa00","chunk-617963b5":"d6b36efb","chunk-6317f4f8":"5364830e","chunk-6822bcd8":"9e9eb864","chunk-68d8897c":"08107081","chunk-d71dc250":"4a3ff26b","chunk-da68b3ee":"d7b98c94","chunk-69ac30c6":"6b7d3b18","chunk-6c5d0770":"e9f53609","chunk-6c68df70":"1162cf67","chunk-718b22b1":"9c7c9fd8","chunk-72c49067":"89361c49","chunk-73112bd2":"c5f326b0","chunk-784b66ea":"6cb0d0ce","chunk-7a372520":"36ccc920","chunk-7cbfd403":"6636e79a","chunk-7ceed6ae":"f498d981","chunk-7e9b6ebc":"89a2a957","chunk-855242c8":"41a4f1d6","chunk-8567cddc":"10afa38b","chunk-879c9ab8":"d475c253","chunk-8b131d44":"013748f1","chunk-8b1aa502":"ddf9772a","chunk-8b38ae7a":"a8c087a3","chunk-8c1e4f6c":"a0f1fce5","chunk-8d04d5e0":"2512f052","chunk-aae304a4":"8c6263fd","chunk-b5c613fc":"29c0dd24","chunk-be3b1982":"cedf39cd","chunk-c9efabe2":"56dde933","chunk-cb7c82b8":"f4c61465","chunk-d0f84122":"120c24f3","chunk-d2138398":"bd1ced9a","chunk-da7cb97e":"8f80064d","chunk-de3e7d6c":"29845496","chunk-ea20651c":"495d6439","chunk-f4392dec":"a700bd37","chunk-f45bd9fc":"d0b1ddb3","chunk-f5868382":"c129a842"}[e]+".js"}function s(t){if(a[t])return a[t].exports;var n=a[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,s),n.l=!0,n.exports}s.e=function(e){var t=[],n={"chunk-00ea03aa":1,"chunk-02262664":1,"chunk-0ca3207b":1,"chunk-0e219e0a":1,"chunk-102c213c":1,"chunk-14b9857b":1,"chunk-14e34fd6":1,"chunk-12cd12ee":1,"chunk-15426580":1,"chunk-16dd0f66":1,"chunk-18b344ff":1,"chunk-1942c74d":1,"chunk-a7f9d97c":1,"chunk-1ade1f66":1,"chunk-1f5746f4":1,"chunk-21556441":1,"chunk-22e8860b":1,"chunk-2301e1ef":1,"chunk-23c6e1af":1,"chunk-23d3b750":1,"chunk-24c32cac":1,"chunk-2521a24a":1,"chunk-26853dd0":1,"chunk-28e8e413":1,"chunk-2c359864":1,"chunk-068f3754":1,"chunk-347e71a8":1,"chunk-6d455b82":1,"chunk-85384882":1,"chunk-d7c3d1f6":1,"chunk-30f43d24":1,"chunk-313b4a34":1,"chunk-3147487a":1,"chunk-320e0938":1,"chunk-3385141a":1,"chunk-34cbb637":1,"chunk-3545909f":1,"chunk-39eb3550":1,"chunk-35748faf":1,"chunk-36d0a25f":1,"chunk-3700f517":1,"chunk-39807813":1,"chunk-36315a4c":1,"chunk-392dc157":1,"chunk-9a319a28":1,"chunk-de26f0aa":1,"chunk-3ac0a32f":1,"chunk-3bfafdc2":1,"chunk-3d3a637e":1,"chunk-3da7f730":1,"chunk-420b7338":1,"chunk-450656cc":1,"chunk-48264968":1,"chunk-4daabb95":1,"chunk-4f20c4bb":1,"chunk-012bc5f4":1,"chunk-012f97c7":1,"chunk-028d8a59":1,"chunk-0b51c142":1,"chunk-0cffe042":1,"chunk-0fab8be6":1,"chunk-11f2242b":1,"chunk-0d065d94":1,"chunk-19278d2b":1,"chunk-1fdfb292":1,"chunk-********":1,"chunk-2329612d":1,"chunk-23596e77":1,"chunk-2838216f":1,"chunk-1cce1846":1,"chunk-309af1d5":1,"chunk-48fb23e7":1,"chunk-422aaf0c":1,"chunk-480b826e":1,"chunk-2ed110c0":1,"chunk-30925022":1,"chunk-33544970":1,"chunk-368b7e8b":1,"chunk-399f9916":1,"chunk-3a07fecf":1,"chunk-3d9cad84":1,"chunk-3dd62379":1,"chunk-3fd7faf3":1,"chunk-47eccb06":1,"chunk-4cc2a241":1,"chunk-4d50a3ca":1,"chunk-4fe0f6d8":1,"chunk-6120a01e":1,"chunk-66ab560d":1,"chunk-84f36746":1,"chunk-98ffbe16":1,"chunk-a1c5b23e":1,"chunk-a6062dfe":1,"chunk-ac9b06d4":1,"chunk-ae66aca4":1,"chunk-bc1af0b8":1,"chunk-bdaf01ce":1,"chunk-cf4c1516":1,"chunk-cf78aefe":1,"chunk-dee6defa":1,"chunk-e492816e":1,"chunk-ee67481a":1,"chunk-12cfd24a":1,"chunk-270d63d7":1,"chunk-de7ad25e":1,"chunk-2f52cadf":1,"chunk-603786c0":1,"chunk-75b23b0c":1,"chunk-53230ece":1,"chunk-546983df":1,"chunk-55d74b39":1,"chunk-56a3dd1d":1,"chunk-5a32affb":1,"chunk-5fca294c":1,"chunk-611b4a0c":1,"chunk-617963b5":1,"chunk-6317f4f8":1,"chunk-6822bcd8":1,"chunk-68d8897c":1,"chunk-d71dc250":1,"chunk-da68b3ee":1,"chunk-69ac30c6":1,"chunk-6c5d0770":1,"chunk-6c68df70":1,"chunk-718b22b1":1,"chunk-72c49067":1,"chunk-73112bd2":1,"chunk-784b66ea":1,"chunk-7cbfd403":1,"chunk-7ceed6ae":1,"chunk-7e9b6ebc":1,"chunk-855242c8":1,"chunk-879c9ab8":1,"chunk-8b38ae7a":1,"chunk-aae304a4":1,"chunk-b5c613fc":1,"chunk-be3b1982":1,"chunk-cb7c82b8":1,"chunk-d0f84122":1,"chunk-da7cb97e":1,"chunk-de3e7d6c":1,"chunk-ea20651c":1,"chunk-f4392dec":1,"chunk-f45bd9fc":1};c[e]?t.push(c[e]):0!==c[e]&&n[e]&&t.push(c[e]=new Promise((function(t,n){for(var a="css/"+({}[e]||e)+"."+{"chunk-00ea03aa":"716469ea","chunk-01175ac8":"31d6cfe0","chunk-02262664":"44960492","chunk-0b1cf41e":"31d6cfe0","chunk-0c0cc883":"31d6cfe0","chunk-0ca3207b":"ac16ebd9","chunk-0d6f39f2":"31d6cfe0","chunk-0e219e0a":"7a114978","chunk-102c213c":"6dec2853","chunk-10a60bb9":"31d6cfe0","chunk-11a770b5":"31d6cfe0","chunk-14b9857b":"0dc416de","chunk-14e34fd6":"f9307922","chunk-15317ec4":"31d6cfe0","chunk-12cd12ee":"54667fbd","chunk-15426580":"2adba2dd","chunk-16dd0f66":"e12e3212","chunk-18b344ff":"7ac23024","chunk-1942c74d":"9225c60d","chunk-2d237ad7":"31d6cfe0","chunk-a7f9d97c":"76e71af3","chunk-1ade1f66":"2b8abd6f","chunk-1dc45689":"31d6cfe0","chunk-1e40043a":"31d6cfe0","chunk-1f5746f4":"778fafc0","chunk-21556441":"17295d1a","chunk-22e8860b":"a3552cea","chunk-2301e1ef":"8a33387f","chunk-23c6e1af":"aafd66ae","chunk-23d3b750":"8b139fee","chunk-24c32cac":"ab1f8f3a","chunk-2521a24a":"2b8abd6f","chunk-26853dd0":"42ef1eb7","chunk-28e8e413":"54667fbd","chunk-2c359864":"0dc416de","chunk-2d0aa5b8":"31d6cfe0","chunk-068f3754":"a3bd80ac","chunk-2d0bff35":"31d6cfe0","chunk-2d22dd29":"31d6cfe0","chunk-347e71a8":"d25e5620","chunk-2d0baaeb":"31d6cfe0","chunk-2d229b36":"31d6cfe0","chunk-6d455b82":"3256581d","chunk-85384882":"a3bd80ac","chunk-2d0a45ef":"31d6cfe0","chunk-2d0e48bc":"31d6cfe0","chunk-d7c3d1f6":"7422a026","chunk-2d0d660d":"31d6cfe0","chunk-2d0decc5":"31d6cfe0","chunk-2d0e51c0":"31d6cfe0","chunk-30f43d24":"df483bc9","chunk-313b4a34":"b641a9cb","chunk-3147487a":"d65cb755","chunk-320e0938":"361c89fc","chunk-3385141a":"0dc416de","chunk-34cbb637":"0bdf5717","chunk-3545909f":"fcc755db","chunk-39eb3550":"9a33ad01","chunk-35748faf":"804e7af3","chunk-36d0a25f":"84399b28","chunk-3700f517":"9face867","chunk-39807813":"69032198","chunk-39d3d59e":"31d6cfe0","chunk-36315a4c":"4a638a9b","chunk-392dc157":"7c6efd50","chunk-9a319a28":"209c2ce3","chunk-de26f0aa":"5369509e","chunk-3ac0a32f":"6b27f322","chunk-3bfafdc2":"5d9e0e0f","chunk-3d3a637e":"6b186f19","chunk-3da7f730":"62229ef7","chunk-420b7338":"2ab673ea","chunk-450656cc":"5a56d27e","chunk-48264968":"fb56c19c","chunk-4a02fcee":"31d6cfe0","chunk-4daabb95":"778fafc0","chunk-4f20c4bb":"778fafc0","chunk-522d6fa4":"31d6cfe0","chunk-012bc5f4":"2602e3a5","chunk-012f97c7":"2602e3a5","chunk-028d8a59":"7dfac12f","chunk-0b51c142":"66ac9945","chunk-0cffe042":"9c55eb4f","chunk-0fab8be6":"ce567acd","chunk-11f2242b":"d70e08fe","chunk-13e901c7":"31d6cfe0","chunk-0d065d94":"a07639d4","chunk-1708a62c":"31d6cfe0","chunk-19278d2b":"b641a9cb","chunk-0b30fc26":"31d6cfe0","chunk-1cc512a8":"31d6cfe0","chunk-1fdfb292":"b641a9cb","chunk-********":"f16909c9","chunk-2329612d":"3df250dd","chunk-23596e77":"3696f379","chunk-2838216f":"758fffd2","chunk-1cce1846":"4d415999","chunk-309af1d5":"70399318","chunk-48fb23e7":"dcb813e1","chunk-422aaf0c":"1cbe75f7","chunk-480b826e":"37977405","chunk-2ed110c0":"1fe61cf9","chunk-30925022":"2b16f0ec","chunk-33544970":"2602e3a5","chunk-33e028d4":"31d6cfe0","chunk-368b7e8b":"2602e3a5","chunk-399f9916":"4c2a41fd","chunk-3a07fecf":"5a56d27e","chunk-3d9cad84":"051e25ba","chunk-3dd62379":"b2c012a1","chunk-3fd7faf3":"73eed694","chunk-43b4168a":"31d6cfe0","chunk-47eccb06":"0d6b211c","chunk-4cc2a241":"1d8988c8","chunk-4d50a3ca":"2bda77be","chunk-4fe0f6d8":"0c62125d","chunk-5215dd0c":"31d6cfe0","chunk-58880d83":"31d6cfe0","chunk-5f1a6de2":"31d6cfe0","chunk-6120a01e":"a3008733","chunk-66ab560d":"2602e3a5","chunk-84f36746":"9883a875","chunk-8c9b7574":"31d6cfe0","chunk-98ffbe16":"48e565d8","chunk-a1c5b23e":"bb2cef6d","chunk-a6062dfe":"2602e3a5","chunk-a948aa0a":"31d6cfe0","chunk-ac9b06d4":"114585bd","chunk-ae66aca4":"9a33ad01","chunk-bc1af0b8":"801f0bc4","chunk-bdaf01ce":"4b762768","chunk-c0cb18d6":"31d6cfe0","chunk-cf4c1516":"6e2eeecc","chunk-cf78aefe":"2fc40105","chunk-dee6defa":"1d002c06","chunk-e492816e":"6e4cd0a7","chunk-e766a746":"31d6cfe0","chunk-ee67481a":"e5e0eaf0","chunk-f6211cec":"31d6cfe0","chunk-18603135":"31d6cfe0","chunk-12cfd24a":"e1e883d7","chunk-270d63d7":"9652de86","chunk-de7ad25e":"4fe1e14c","chunk-2f52cadf":"8c3946c9","chunk-603786c0":"19a54cb4","chunk-75b23b0c":"c4aae687","chunk-53230ece":"6ce6579b","chunk-546983df":"28cffafb","chunk-55d74b39":"fce63aac","chunk-56a3dd1d":"5a56d27e","chunk-5a32affb":"c1ed9f13","chunk-5fca294c":"ef1bd083","chunk-611b4a0c":"1d4ccc34","chunk-617963b5":"cdcd3c72","chunk-6317f4f8":"3a53ef9a","chunk-6822bcd8":"1d531143","chunk-68d8897c":"f28fb7af","chunk-d71dc250":"1b28cc16","chunk-da68b3ee":"67cc16bc","chunk-69ac30c6":"9df53be3","chunk-6c5d0770":"b641a9cb","chunk-6c68df70":"7123b837","chunk-718b22b1":"244c15c9","chunk-72c49067":"e5e0eaf0","chunk-73112bd2":"eb88020d","chunk-784b66ea":"29ed61cf","chunk-7a372520":"31d6cfe0","chunk-7cbfd403":"dbf392da","chunk-7ceed6ae":"3e30af9d","chunk-7e9b6ebc":"cadb5f63","chunk-855242c8":"f08fe6c9","chunk-8567cddc":"31d6cfe0","chunk-879c9ab8":"71990d61","chunk-8b131d44":"31d6cfe0","chunk-8b1aa502":"31d6cfe0","chunk-8b38ae7a":"6d257a4f","chunk-8c1e4f6c":"31d6cfe0","chunk-8d04d5e0":"31d6cfe0","chunk-aae304a4":"9ab9d3f7","chunk-b5c613fc":"fa84b922","chunk-be3b1982":"e31d9721","chunk-c9efabe2":"31d6cfe0","chunk-cb7c82b8":"e33cf648","chunk-d0f84122":"d25e6579","chunk-d2138398":"31d6cfe0","chunk-da7cb97e":"a84efca2","chunk-de3e7d6c":"686242b2","chunk-ea20651c":"05abcfa2","chunk-f4392dec":"aa12f1d2","chunk-f45bd9fc":"778fafc0","chunk-f5868382":"31d6cfe0"}[e]+".css",o=s.p+a,i=document.getElementsByTagName("link"),r=0;r<i.length;r++){var l=i[r],u=l.getAttribute("data-href")||l.getAttribute("href");if("stylesheet"===l.rel&&(u===a||u===o))return t()}var d=document.getElementsByTagName("style");for(r=0;r<d.length;r++){l=d[r],u=l.getAttribute("data-href");if(u===a||u===o)return t()}var m=document.createElement("link");m.rel="stylesheet",m.type="text/css",m.onload=t,m.onerror=function(t){var a=t&&t.target&&t.target.src||o,i=new Error("Loading CSS chunk "+e+" failed.\n("+a+")");i.code="CSS_CHUNK_LOAD_FAILED",i.request=a,delete c[e],m.parentNode.removeChild(m),n(i)},m.href=o;var h=document.getElementsByTagName("head")[0];h.appendChild(m)})).then((function(){c[e]=0})));var a=o[e];if(0!==a)if(a)t.push(a[2]);else{var i=new Promise((function(t,n){a=o[e]=[t,n]}));t.push(a[2]=i);var l,u=document.createElement("script");u.charset="utf-8",u.timeout=120,s.nc&&u.setAttribute("nonce",s.nc),u.src=r(e);var d=new Error;l=function(t){u.onerror=u.onload=null,clearTimeout(m);var n=o[e];if(0!==n){if(n){var a=t&&("load"===t.type?"missing":t.type),c=t&&t.target&&t.target.src;d.message="Loading chunk "+e+" failed.\n("+a+": "+c+")",d.name="ChunkLoadError",d.type=a,d.request=c,n[1](d)}o[e]=void 0}};var m=setTimeout((function(){l({type:"timeout",target:u})}),12e4);u.onerror=u.onload=l,document.head.appendChild(u)}return Promise.all(t)},s.m=e,s.c=a,s.d=function(e,t,n){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},s.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(s.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)s.d(n,a,function(t){return e[t]}.bind(null,a));return n},s.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="/cmifront/",s.oe=function(e){throw console.error(e),e};var l=window["webpackJsonp"]=window["webpackJsonp"]||[],u=l.push.bind(l);l.push=t,l=l.slice();for(var d=0;d<l.length;d++)t(l[d]);var m=u;i.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("56d7")},"0938":function(e,t,n){e.exports=n.p+"img/logo-cn.c3415c73.jpg"},"0c92":function(e,t,n){"use strict";var a=function(){var e=this,t=e._self._c;return t("div",[t("Dropdown",{attrs:{trigger:"click"},on:{"on-click":e.selectMode}},[t("a",{attrs:{href:"javascript:void(0)"}},[e._v("\n\t\t\t"+e._s(e.$t("support.cooperationModel"))+"\n\t\t\t"),t("Icon",{attrs:{size:18,type:"md-arrow-dropdown"}})],1),t("DropdownMenu",{attrs:{slot:"list"},slot:"list"},e._l(e.modeList,(function(n,a){return t("DropdownItem",{key:a,attrs:{name:a}},[e._v(e._s(n))])})),1)],1)],1)},c=[],o=(n("caad"),n("d3b7"),n("2532"),n("6dfa")),i={name:"CooperationMode",inject:["reload"],data:function(){return{modeList:{},modeFlag:"",cooperationMode:""}},watch:{modeFlag:function(e,t){this.$emit("on-mode-change",e),this.reload()}},methods:{selectMode:function(e){this.modeFlag=e},modeShow:function(){var e=this;Object(o["s"])({corpId:sessionStorage.getItem("corpId")}).then((function(t){"0000"==t.code&&t.data.length>1&&(t.data.includes("1")&&t.data.includes("2")&&!t.data.includes("3")?"1"==e.cooperationMode?e.modeList={2:e.$t("A2Zmode")}:e.modeList={1:e.$t("consignmentSalesModel")}:t.data.includes("1")&&t.data.includes("3")&&!t.data.includes("2")?"1"==e.cooperationMode?e.modeList={3:e.$t("resourceMode")}:e.modeList={1:e.$t("consignmentSalesModel")}:t.data.includes("2")&&t.data.includes("3")&&!t.data.includes("1")?"2"==e.cooperationMode?e.modeList={3:e.$t("resourceMode")}:e.modeList={2:e.$t("A2Zmode")}:"1"==e.cooperationMode?e.modeList={2:e.$t("A2Zmode"),3:e.$t("resourceMode")}:"2"==e.cooperationMode?e.modeList={1:e.$t("consignmentSalesModel"),3:e.$t("resourceMode")}:e.modeList={1:e.$t("consignmentSalesModel"),2:e.$t("A2Zmode")})})).catch((function(e){console.error(e)})).finally((function(){}))}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),this.modeShow()}},r=i,s=n("2877"),l=Object(s["a"])(r,a,c,!1,null,null,null);t["a"]=l.exports},"0f29":function(e,t,n){var a={"./":"13aa","./error-store":"b7c7","./error-store/":"b7c7","./error-store/index":"b7c7","./error-store/index.js":"b7c7","./index":"13aa","./index.js":"13aa","./redirectPlugin":"e3e1","./redirectPlugin.js":"e3e1"};function c(e){var t=o(e);return n(t)}function o(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}c.keys=function(){return Object.keys(a)},c.resolve=o,e.exports=c,c.id="0f29"},1:function(e,t){},"13aa":function(e,t,n){"use strict";n.r(t);var a=n("53ca"),c=n("f121"),o=c["a"].plugin;t["default"]=function(e){for(var t in o){var c=o[t];e.use(n("0f29")("./".concat(t)).default,"object"===Object(a["a"])(c)?c:void 0)}}},"1a88":function(e,t,n){},"1ce5":function(e,t,n){},"1fff":function(e,t,n){},"217e":function(e,t,n){"use strict";n("913b")},2785:function(e,t,n){},3108:function(e,t,n){"use strict";n.d(t,"d",(function(){return o})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"e",(function(){return s})),n.d(t,"f",(function(){return l})),n.d(t,"g",(function(){return u})),n.d(t,"a",(function(){return d}));var a=n("66df"),c="",o=function(e){return a["a"].request({url:c+"/auth/login",data:e,method:"post"})},i=function(e){return a["a"].request({url:c+"/passport/smscode",params:e,method:"get"})},r=function(e){return a["a"].request({url:c+"/passport/captcha",params:e,method:"get"})},s=function(e){return a["a"].request({url:"/auth/logout?userName="+e,method:"delete"})},l=function(e){return a["a"].request({url:"/sys/api/v1/user/userForgetpasswd/resetPasswd",params:e,method:"PUT"})},u=function(e){return a["a"].request({url:"/aep/sso/ssoLoginGDS",data:e,method:"post"})},d=function(e){return a["a"].request({url:"/aep/sso/getSSOIsOpen",data:e,method:"get"})}},"326d":function(e,t,n){},"33ec":function(e,t,n){},"3cd4":function(e,t,n){e.exports=n.p+"img/logo-us.0a583b17.jpg"},"3daa":function(e,t,n){},4360:function(e,t,n){"use strict";var a=n("2b0e"),c=n("2f62"),o=(n("c740"),n("14d9"),n("a434"),n("b0c0"),n("d3b7"),n("25f0"),n("3108")),i=n("c276"),r={state:{istoken:!0,userName:"",userId:Object(i["k"])(),roleId:"",avatarImgPath:"",token:Object(i["j"])(),access:"",hasGetInfo:!1,unreadCount:0,isUpdatePassword:2,userBtnPriv:[],messageUnreadList:[],messageReadedList:[],messageTrashList:[],messageContentStore:{}},mutations:{setAvatar:function(e,t){e.avatarImgPath=t},setUserId:function(e,t){e.userId=t,Object(i["u"])(t)},setRoleId:function(e,t){e.roleId=t},setUserName:function(e,t){e.userName=t},setIsUpdatePassword:function(e,t){e.isUpdatePassword=t},setAccess:function(e,t){e.access=t},setToken:function(e,t){e.token=t,Object(i["t"])(t)},setHasGetInfo:function(e,t){e.hasGetInfo=t},setMessageCount:function(e,t){e.unreadCount=t},setMessageUnreadList:function(e,t){e.messageUnreadList=t},setUserPriv:function(e,t){e.userBtnPriv=t},setMessageReadedList:function(e,t){e.messageReadedList=t},setMessageTrashList:function(e,t){e.messageTrashList=t},updateMessageContentStore:function(e,t){var n=t.msg_id,a=t.content;e.messageContentStore[n]=a},moveMsg:function(e,t){var n=t.from,a=t.to,c=t.msg_id,o=e[n].findIndex((function(e){return e.msg_id===c})),i=e[n].splice(o,1)[0];i.loading=!1,e[a].unshift(i)}},getters:{messageUnreadCount:function(e){return e.messageUnreadList.length},messageReadedCount:function(e){return e.messageReadedList.length},messageTrashCount:function(e){return e.messageTrashList.length}},actions:{iistoken:function(e,t){e.state.istoken=t},handleLogin:function(e,t){var n=e.commit;return new Promise((function(e,a){Object(o["d"])(t).then((function(t){if("0000"===t.code){for(var a=t.data.userDetails.pagePrivileges,c=[],o=[],i=0;i<a.length;i++)c.push(a[i].access),o.push({url:a[i].url,priv:a[i].buttons});n("setUserPriv",o),n("setUserName",t.data.userDetails.username),n("setIsUpdatePassword",t.data.userDetails.rePassword),n("setUserId",t.data.userDetails.id),n("setRoleId",t.data.userDetails.roleId),n("setAccess",c),n("setHasGetInfo",!0),n("setToken",t.data.oauth2AccessToken.access_token)}e(t)})).catch((function(e){console.log(e),a(e)}))}))},handleSSOLogin:function(e,t){var n=e.commit;return new Promise((function(e,a){Object(o["g"])(t).then((function(t){if("0000"===t.code){for(var a=t.data.userDetails.pagePrivileges,c=[],o=[],i=0;i<a.length;i++)c.push(a[i].access),o.push({url:a[i].url,priv:a[i].buttons});n("setUserPriv",o),n("setUserName",t.data.userDetails.username),n("setIsUpdatePassword",t.data.userDetails.rePassword),n("setUserId",t.data.userDetails.id),n("setRoleId",t.data.userDetails.roleId),n("setAccess",c),n("setHasGetInfo",!0),n("setToken",t.data.oauth2AccessToken.access_token)}e(t)})).catch((function(e){console.log(e),a(e)}))}))},handleLogOut:function(e){e.state;var t=e.commit;return new Promise((function(e,n){t("setToken","",-1),t("setUserPriv","",-1),t("setUserId","",-1),t("setRoleId","",-1),t("setUserName",""),t("setAccess",[]),t("setHasGetInfo",!1),sessionStorage.clear();var a=localStorage.getItem("local");localStorage.clear(),localStorage.setItem("local",a),e()}))},getUserInfo:function(e){function t(t){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(e){var t=e.state,n=e.commit;return new Promise((function(e,a){try{getUserInfo(t.token).then((function(t){n("setAvatar",t.avatar),n("setUserName",t.name),n("setUserId",t.user_id),n("setAccess",t.access),n("setHasGetInfo",!0),e(t)})).catch((function(e){a(e)}))}catch(c){a(c)}}))}))}},s=n("2909"),l=(n("4de4"),n("a18c")),u=n("ddb9"),d=n("f121"),m=d["a"].homeName,h=function(e,t){var n=Object(i["g"])(e.tagNavList,t);e.tagNavList=e.tagNavList.filter((function(e){return!Object(i["o"])(e,t)})),l["a"].push(n)},p={state:{breadCrumbList:[],tagNavList:[],homeRoute:{},local:Object(i["m"])("local"),cooperationMode:sessionStorage.getItem("cooperationMode"),errorList:[],hasReadErrorPage:!1},getters:{menuList:function(e,t,n){return Object(i["e"])(u["a"],n.user.access)},errorCount:function(e){return e.errorList.length}},mutations:{setBreadCrumb:function(e,t){e.breadCrumbList=Object(i["c"])(t,e.homeRoute)},setHomeRoute:function(e,t){e.homeRoute=Object(i["d"])(t,m)},setTagNavList:function(e,t){var n=[];n=t?Object(s["a"])(t):Object(i["i"])()||[],n[0]&&n[0].name!==m&&n.shift();var a=n.findIndex((function(e){return e.name===m}));if(a>0){var c=n.splice(a,1)[0];n.unshift(c)}e.tagNavList=n,Object(i["r"])(Object(s["a"])(n))},closeTag:function(e,t){var n=e.tagNavList.filter((function(e){return Object(i["o"])(e,t)}));t=n[0]?n[0]:null,t&&h(e,t)},addTag:function(e,t){var n=t.route,a=t.type,c=void 0===a?"unshift":a,o=Object(i["h"])(n);Object(i["p"])(e.tagNavList,o)||("push"===c?e.tagNavList.push(o):o.name===m?e.tagNavList.unshift(o):e.tagNavList.splice(1,0,o),Object(i["r"])(Object(s["a"])(e.tagNavList)))},setLocal:function(e,t){Object(i["n"])("local",t),e.local=t},setAuthen:function(e,t){e.authen=t,sessionStorage.setItem("authen",t)},addError:function(e,t){e.errorList.push(t)},setHasReadErrorLoggerStatus:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];e.hasReadErrorPage=t}},actions:{changeAuthenStatus:function(e,t){var n=e.commit;n("setAuthen",t)}}},f=n("c7eb"),g=n("1da1"),b=(n("d9e2"),n("66df")),k="/sys/api/v1",y=function(e){return b["a"].request({url:k+"/log/getConfig",method:"get",params:{key:e}})},v={url:null,loading:!1,error:null},O={SET_URL:function(e,t){e.url=t},SET_LOADING:function(e,t){e.loading=t},SET_ERROR:function(e,t){e.error=t}},C={fetchRedirectUrl:function(e,t){return Object(g["a"])(Object(f["a"])().mark((function n(){var a,c,o;return Object(f["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=e.commit,c=e.state,!c.url||t){n.next=3;break}return n.abrupt("return",c.url);case 3:return a("SET_LOADING",!0),n.prev=4,n.next=7,y(t);case 7:if(o=n.sent,"0000"!==o.code){n.next=11;break}return a("SET_URL",o.data),n.abrupt("return",o.data);case 11:throw new Error(o.msg||"Failed to get redirect URL");case 14:throw n.prev=14,n.t0=n["catch"](4),a("SET_ERROR",n.t0.msg),n.t0;case 18:return n.prev=18,a("SET_LOADING",!1),n.finish(18);case 21:case"end":return n.stop()}}),n,null,[[4,14,18,21]])})))()}},P={redirectUrl:function(e){return e.url},isLoading:function(e){return e.loading},error:function(e){return e.error}},S={namespaced:!0,state:v,mutations:O,actions:C,getters:P},I=n("0e44");a["default"].use(c["a"]);t["a"]=new c["a"].Store({plugins:[Object(I["a"])({storage:window.sessionStorage})],state:{},mutations:{},actions:{},modules:{user:r,app:p,redirect:S}})},4472:function(e,t,n){"use strict";n("c7fd")},"4a0e":function(e,t,n){e.exports=n.p+"img/header.54841e29.jpg"},"56d7":function(e,t,n){"use strict";n.r(t);var a=n("3835"),c=(n("e260"),n("e6cf"),n("cca6"),n("a79d"),n("14d9"),n("b64b"),n("d3b7"),n("ac1f"),n("5319"),n("159b"),n("2b0e")),o=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[e.isRouterAlive?t("router-view"):e._e(),t("chat")],1)},i=[],r=n("4360"),s=n("a18c"),l=null;clearInterval(l);var u=function(){var e=this,t=e._self._c;return t("div")},d=[],m=function(){var e=this,t=e._self._c;return t("div",{staticClass:"floating-button",style:{left:e.position.x+"px",top:e.position.y+"px"},on:{mousedown:e.startDrag,touchstart:e.startDrag,click:e.handleClick}},[t("div",{staticClass:"button-content"},[t("Icon",{attrs:{type:"ios-chatbubbles",size:"24"}}),t("span",{staticClass:"button-text"},[e._v("AI助手")])],1)])},h=[],p={name:"FloatingButton",data:function(){return{position:{x:window.innerWidth-100,y:window.innerHeight-100},isDragging:!1,startX:0,startY:0}},methods:{handleClick:function(e){this.isDragging?this.isDragging=!1:this.$emit("click",e)},startDrag:function(e){this.isDragging=!1;var t="touchstart"===e.type?e.touches[0]:e;this.startX=t.clientX-this.position.x,this.startY=t.clientY-this.position.y,document.addEventListener("mousemove",this.onDrag),document.addEventListener("touchmove",this.onDrag),document.addEventListener("mouseup",this.stopDrag),document.addEventListener("touchend",this.stopDrag)},onDrag:function(e){this.isDragging=!0;var t="touchmove"===e.type?e.touches[0]:e;this.position.x=t.clientX-this.startX,this.position.y=t.clientY-this.startY},stopDrag:function(){document.removeEventListener("mousemove",this.onDrag),document.removeEventListener("touchmove",this.onDrag),document.removeEventListener("mouseup",this.stopDrag),document.removeEventListener("touchend",this.stopDrag)}}},f=p,g=(n("ebb7"),n("2877")),b=Object(g["a"])(f,m,h,!1,null,null,null),k=b.exports,y=function(){var e=this,t=e._self._c;return t("div",{staticClass:"chat-window"},[e.isCollapsed?t("div",{staticClass:"collapsed-sidebar"},[e._m(0),t("Tooltip",{attrs:{content:"打开边栏",placement:"right"}},[t("div",{staticClass:"sidebar-toggle",on:{click:e.toggleCollapse}},[t("Icon",{attrs:{type:"md-menu"}})],1)]),t("Tooltip",{attrs:{content:"开启新会话",placement:"right"}},[t("div",{staticClass:"new-chat",on:{click:e.createNewSession}},[t("Icon",{attrs:{type:"md-add"}})],1)])],1):t("div",{staticClass:"chat-sidebar"},[t("div",{staticClass:"sidebar-header"},[t("div",{staticClass:"logo-title"},[e._v("AI助手")]),t("Tooltip",{attrs:{content:"收起边栏",placement:"right"}},[t("div",{staticClass:"sidebar-toggle",on:{click:e.toggleCollapse}},[t("Icon",{attrs:{type:"md-menu"}})],1)])],1),t("div",{staticClass:"new-chat-btn",on:{click:e.createNewSession}},[t("Icon",{attrs:{type:"md-add"}}),t("span",[e._v("开启新会话")])],1),t("div",{staticClass:"session-list"},e._l(e.groupedSessions,(function(n,a){return t("div",{key:"group-"+a,staticClass:"session-group"},[t("div",{staticClass:"group-title"},[e._v(e._s(n.title))]),e._l(n.sessions,(function(n){return t("div",{key:n.id,staticClass:"session-item",class:{active:e.currentSessionId===n.id},on:{click:function(t){return e.selectSession(n)}}},[t("div",{staticClass:"session-info"},[t("span",{staticClass:"session-title"},[e._v(e._s(n.title||"新会话"))]),t("span",{staticClass:"session-time"},[e._v(e._s(e.formatTime(n.updateTime)))])]),t("Button",{attrs:{type:"text",icon:"md-trash"},on:{click:function(t){return t.stopPropagation(),e.deleteSession(n.id)}}})],1)}))],2)})),0)]),t("div",{staticClass:"chat-main",class:{expanded:e.isCollapsed}},[t("div",{staticClass:"chat-header"},[t("span",{staticClass:"chat-title"},[e._v(e._s(e.currentSession&&e.currentSession.title||"新会话"))]),t("div",{staticClass:"header-actions"},[t("Button",{attrs:{type:"text",icon:"md-exit"},on:{click:e.exitChat}},[e._v("退出")])],1)]),t("div",{ref:"messageList",staticClass:"message-list"},[t("div",{staticClass:"message-item-wrapper"},e._l(e.currentMessages,(function(n,a){return t("div",{key:a,staticClass:"message-item",class:n.role},["user"===n.role?[t("Icon",{staticStyle:{margin:"0 8px"},attrs:{type:"md-contact",size:"32"}})]:[t("Avatar",{attrs:{src:n.avatar}})],t("div",{staticClass:"message-content"},[n.thinking?t("div",{staticClass:"thinking"},[t("Icon",{staticClass:"loading-icon",attrs:{type:"ios-loading"}}),t("span",[e._v("思考中...")])],1):n.streaming?t("div",{staticClass:"streaming"},[t("div",{staticClass:"typewriter-container"},[t("div",{ref:"fullContent",refInFor:!0,staticClass:"full-content markdown-content",domProps:{innerHTML:e._s(e.renderMarkdown(n.content))}}),t("div",{staticClass:"typed-content markdown-content",attrs:{id:"typedText-".concat(a)}})])]):t("div",{staticClass:"content"},[n.hasImage?t("div",{staticClass:"image-content"},[t("img",{attrs:{src:n.imageUrl}}),t("div",{staticClass:"markdown-content",domProps:{innerHTML:e._s(e.renderMarkdown(n.content))}})]):t("div",{staticClass:"markdown-content",domProps:{innerHTML:e._s(e.renderMarkdown(n.content))}})])])],2)})),0)]),t("div",{staticClass:"input-area"},[t("div",{staticClass:"toolbar"},[t("Upload",{attrs:{action:"/api/upload","before-upload":e.handleBeforeUpload,"on-success":e.handleUploadSuccess}},[t("Button",{attrs:{type:"text"}},[t("Icon",{attrs:{type:"md-image"}})],1)],1)],1),t("Input",{attrs:{type:"textarea",rows:4,placeholder:"请输入您的问题..."},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.sendMessage.apply(null,arguments)}},model:{value:e.inputMessage,callback:function(t){e.inputMessage=t},expression:"inputMessage"}}),t("div",{staticClass:"action-buttons"},[t("Button",{attrs:{type:"primary",loading:e.sending},on:{click:e.sendMessage}},[e._v("\n          发送\n        ")]),t("Button",{on:{click:e.clearChat}},[e._v("清空对话")]),t("Button",{attrs:{disabled:!e.sending},on:{click:e.stopChat}},[e._v("停止回答")])],1)],1)])])},v=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"logo"},[t("img",{attrs:{src:"/img/logo-min.1703d006.jpg",alt:"CMI Logo"}})])}],O=n("c7eb"),C=n("2909"),P=n("b85c"),S=n("1da1"),I=(n("99af"),n("4de4"),n("7db0"),n("c740"),n("a15b"),n("a434"),n("b0c0"),n("e9c4"),n("25f0"),n("4d90"),n("2ca0"),n("498a"),n("89c1")),j=n.n(I),M=(n("c276"),n("d9e2"),n("66df"),n("f121")),_="/sys/api",w=M["a"].baseUrl.pro,A=function(){var e=Object(S["a"])(Object(O["a"])().mark((function e(t,n){var a,c,o;return Object(O["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=w+_+"/aily/knowledges",c={"Content-Type":"application/json",Accept:"text/event-stream"},r["a"].state.user.token&&(c.Authorization="bearer ".concat(r["a"].state.user.token)),e.next=5,fetch(a,{method:"POST",body:JSON.stringify(t),credentials:"same-origin",headers:c,signal:n?n.signal:null});case 5:if(o=e.sent,o.ok){e.next=8;break}throw new Error("API请求失败: ".concat(o.status));case 8:return e.abrupt("return",o);case 9:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),D={name:"ChatWindow",components:{VirtualList:j.a},data:function(){return{isCollapsed:!0,sidebarWidth:300,mainWidth:500,isResizing:!1,startX:0,startWidth:0,sessions:[{id:"1",title:"示例会话",createTime:new Date,updateTime:new Date,messages:[]}],currentSessionId:"1",currentMessages:[],inputMessage:"",sending:!1,abortController:null,defaultAvatars:{user:"",assistant:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"}}},computed:{currentSession:function(){var e=this;return this.sessions.find((function(t){return t.id===e.currentSessionId}))},groupedSessions:function(){var e=new Date;e.setHours(0,0,0,0);var t=new Date(e);t.setDate(t.getDate()-1);var n=new Date(e);n.setDate(n.getDate()-7);var a=new Date(e);a.setDate(a.getDate()-30);var c=[{title:"今天",sessions:[]},{title:"昨天",sessions:[]},{title:"7天内",sessions:[]},{title:"30天内",sessions:[]},{title:"更早",sessions:[]}];return this.sessions.forEach((function(o){var i=new Date(o.updateTime);i.setHours(0,0,0,0),i.getTime()===e.getTime()?c[0].sessions.push(o):i.getTime()===t.getTime()?c[1].sessions.push(o):i>=n?c[2].sessions.push(o):i>=a?c[3].sessions.push(o):c[4].sessions.push(o)})),c.filter((function(e){return e.sessions.length>0}))}},methods:{formatTime:function(e){if(!e)return"";var t=new Date(e),n=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),c=String(t.getDate()).padStart(2,"0"),o=String(t.getHours()).padStart(2,"0"),i=String(t.getMinutes()).padStart(2,"0");return"".concat(n,"-").concat(a,"-").concat(c," ").concat(o,":").concat(i)},startResize:function(e){this.isResizing=!0,this.startX=e.clientX,this.startWidth=this.sidebarWidth,document.addEventListener("mousemove",this.onResize),document.addEventListener("mouseup",this.stopResize)},onResize:function(e){if(this.isResizing){var t=e.clientX-this.startX,n=this.startWidth+t;n>=200&&n<=500&&(this.sidebarWidth=n)}},stopResize:function(){this.isResizing=!1,document.removeEventListener("mousemove",this.onResize),document.removeEventListener("mouseup",this.stopResize)},toggleCollapse:function(){this.isCollapsed=!this.isCollapsed},createNewSession:function(){var e=this.sessions.filter((function(e){return e.title.startsWith("新会话")})).length,t=e>0?"新会话".concat(e+1):"新会话",n={id:Date.now().toString(),title:t,createTime:new Date,updateTime:new Date,messages:[{role:"assistant",content:"你好！我是AI助手。我可以帮您：\n\n1. 回答问题和解决问题\n2. 提供专业建议\n3. 协助编写代码\n4. 分析数据和文档\n\n请告诉我您需要什么帮助？",timestamp:new Date,avatar:this.defaultAvatars.assistant}]};this.sessions.unshift(n),this.selectSession(n)},selectSession:function(e){var t=this;this.currentSessionId=e.id,this.currentMessages=e.messages||[],this.$nextTick((function(){t.scrollToBottom()}))},deleteSession:function(e){var t=this.sessions.findIndex((function(t){return t.id===e}));t>-1&&(this.sessions.splice(t,1),this.currentSessionId===e&&(this.currentSessionId=null,this.currentMessages=[]))},exitChat:function(){this.$emit("close")},updateSessionTitle:function(e,t){var n=this.sessions.find((function(t){return t.id===e}));n&&(n.title=t,n.updateTime=new Date)},loadSessionMessages:function(e){this.currentMessages=[{role:"assistant",content:"你好！我是AI助手。我可以帮您：\n\n1. 回答问题和解决问题\n2. 提供专业建议\n3. 协助编写代码\n4. 分析数据和文档\n\n请告诉我您需要什么帮助？",timestamp:new Date,avatar:this.defaultAvatars.assistant}]},sendMessage:function(){var e=this;return Object(S["a"])(Object(O["a"])().mark((function t(){var n,a,c,o,i,r,s,l,u,d,m,h,p,f,g,b,k,y,v,S,I,j,M,_,w,D,T,R,N,L,x,E;return Object(O["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.inputMessage.trim()&&e.currentSessionId){t.next=2;break}return t.abrupt("return");case 2:return n=e.inputMessage,e.inputMessage="",a={role:"user",content:n,timestamp:new Date,avatar:e.defaultAvatars.user},e.currentMessages.push(a),e.scrollToBottom(),c=e.sessions.find((function(t){return t.id===e.currentSessionId})),c&&c.title.startsWith("新会话")&&(o=n.length>20?n.substring(0,20)+"...":n,e.updateSessionTitle(e.currentSessionId,o)),e.currentMessages.push({role:"assistant",thinking:!0,content:"",avatar:e.defaultAvatars.assistant}),e.scrollToBottom(),t.prev=11,e.sending=!0,e.abortController=new AbortController,i={message:{content:n}},t.next=17,A(i,e.abortController);case 17:r=t.sent,console.log(r,"response-----"),s=r.body.getReader(),l=e.currentMessages[e.currentMessages.length-1],l.thinking&&e.currentMessages.pop(),u=e.currentMessages.length,e.currentMessages.push({role:"assistant",streaming:!0,content:"",avatar:e.defaultAvatars.assistant}),d="",m="",h=[],p=new TextDecoder,f=function(t){if(t.length>m.length){var n=t.substring(m.length);m=t,e.$nextTick((function(){var a=document.getElementById("typedText-".concat(u));if(a){var c=e.$refs.fullContent;if(c&&c.length>0){var o=c[c.length-1];o.innerHTML=e.renderMarkdown(t)}var i=0,r=function(){if(i<n.length){var t=m.substring(0,m.length-n.length+i+1);a.innerHTML=e.renderMarkdown(t),i++,setTimeout(r,10)}};r(),e.currentMessages[e.currentMessages.length-1].content=t,e.scrollToBottom()}}))}},e.$nextTick((function(){var e=document.getElementById("typedText-".concat(u));e&&(e.innerHTML="")})),g=function(e){try{return JSON.parse(e)}catch(t){return null}};case 31:return t.prev=32,t.next=35,s.read();case 35:if(b=t.sent,k=b.done,y=b.value,!k){t.next=40;break}return t.abrupt("break",58);case 40:v=p.decode(y,{stream:!0}),h.push(v),S=v.split("\n"),I=Object(P["a"])(S);try{for(I.s();!(j=I.n()).done;)M=j.value,M.startsWith("data:")&&(_=M.substring(5),w=g(_),w&&w.message&&"string"===typeof w.message.content&&(d=w.message.content,f(d)))}catch(O){I.e(O)}finally{I.f()}t.next=56;break;case 47:if(t.prev=47,t.t0=t["catch"](32),"AbortError"!==t.t0.name){t.next=55;break}return console.log("请求已被用户取消"),e.currentMessages[e.currentMessages.length-1].content+="\n\n[回答已中断]",t.abrupt("break",58);case 55:throw t.t0;case 56:t.next=31;break;case 58:if(!d&&h.length>0)try{console.log("尝试从累积响应中恢复内容..."),D=h.join(""),T=D.split("\n").filter((function(e){return e.startsWith("data:")})),T.length>0&&(R=T[T.length-1],N=R.substring(5),L=g(N),L&&L.message&&"string"===typeof L.message.content&&(d=L.message.content,f(d),console.log("内容恢复成功")))}catch(U){console.error("最终内容提取失败:",U)}e.currentMessages[e.currentMessages.length-1]={role:"assistant",content:d||"获取响应失败，请重试",avatar:e.defaultAvatars.assistant},x=e.sessions.find((function(t){return t.id===e.currentSessionId})),x&&(x.messages=Object(C["a"])(e.currentMessages)),e.scrollToBottom(),t.next=68;break;case 65:t.prev=65,t.t1=t["catch"](11),"AbortError"!==t.t1.name&&(e.$Message.error("发送失败"),console.error("发送消息失败:",t.t1),E=e.currentMessages[e.currentMessages.length-1],E.thinking&&e.currentMessages.pop(),e.currentMessages.push({role:"assistant",content:"抱歉，发生了错误，请稍后再试。",avatar:e.defaultAvatars.assistant}));case 68:return t.prev=68,e.sending=!1,e.abortController=null,t.finish(68);case 72:case"end":return t.stop()}}),t,null,[[11,65,68,72],[32,47]])})))()},stopChat:function(){this.abortController&&(console.log("停止回答"),this.abortController.abort(),this.abortController=null,this.sending=!1);var e=this.currentMessages[this.currentMessages.length-1];e&&e.thinking&&(this.currentMessages.pop(),this.currentMessages.push({role:"assistant",content:"已停止回答",avatar:this.defaultAvatars.assistant}))},clearChat:function(){var e=this;if(this.sending&&this.abortController&&(this.abortController.abort(),this.abortController=null,this.sending=!1),this.currentSessionId){var t=this.sessions.find((function(t){return t.id===e.currentSessionId}));if(t){var n=this.sessions.filter((function(e){return e.title.startsWith("新会话")})).length;t.title=n>0?"新会话".concat(n+1):"新会话"}this.currentMessages=[];var a=this.sessions.find((function(t){return t.id===e.currentSessionId}));a&&(a.messages=[])}},handleBeforeUpload:function(e){return!0},handleUploadSuccess:function(e){},renderMarkdown:function(e){return e?(e=e.replace(/```(?:json)?\s*([\s\S]*?)```/g,(function(e,t){try{if(t.trim().startsWith("{")||t.trim().startsWith("[")){var n=JSON.parse(t.trim()),a=JSON.stringify(n,null,2);return'<pre><code style="white-space: pre-wrap; word-break: break-word;">'.concat(a.replace(/</g,"&lt;").replace(/>/g,"&gt;"),"</code></pre>")}}catch(c){}return'<pre><code style="white-space: pre-wrap; word-break: break-word;">'.concat(t.replace(/</g,"&lt;").replace(/>/g,"&gt;"),"</code></pre>")})),e=e.replace(/`([^`]+)`/g,"<code>$1</code>"),e=e.replace(/\*\*([^*]+)\*\*/g,"<strong>$1</strong>"),e=e.replace(/\*([^*]+)\*/g,"<em>$1</em>"),e=e.replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" target="_blank">$1</a>'),e=e.replace(/\n/g,"<br>"),e):""},scrollToBottom:function(){var e=this;this.$nextTick((function(){var t=e.$refs.messageList;t&&(t.scrollTop=t.scrollHeight)}))}},mounted:function(){this.loadSessionMessages(this.currentSessionId)},beforeDestroy:function(){this.abortController&&(this.abortController.abort(),this.abortController=null)}},T=D,R=(n("6f36"),Object(g["a"])(T,y,v,!1,null,"28a0e23c",null)),N=R.exports,L={name:"Chat",components:{FloatingButton:k,ChatWindow:N},data:function(){return{showWindow:!1}},methods:{toggleChatWindow:function(){this.showWindow=!this.showWindow}}},x=L,E=(n("f997"),Object(g["a"])(x,u,d,!1,null,null,null)),U=E.exports,B={name:"App",components:{Chat:U},provide:function(){return{reload:this.reload}},data:function(){return{isRouterAlive:!0}},methods:{reload:function(){this.isRouterAlive=!1,this.$nextTick((function(){this.isRouterAlive=!0}))}}},F=B,z=(n("217e"),Object(g["a"])(F,o,i,!1,null,null,null)),q=z.exports,$=n("8c4f"),H=(n("e069"),n("5a0c")),V=n.n(H),W=n("f825"),G=n.n(W),Y=n("fe07"),Q=(n("fb6a"),n("90de")),Z={inserted:function(e,t,n){var a=document.querySelector(t.value.trigger);a.style.cursor="move";var c=document.querySelector(t.value.body),o=0,i=0,r=0,s=0,l=!1,u=function(e){var t=/\(.*\)/.exec(c.style.transform);if(t){t=t[0].slice(1,t[0].length-1);var n=t.split("px, ");r=parseFloat(n[0]),s=parseFloat(n[1].split("px")[0])}o=e.pageX,i=e.pageY,l=!0},d=function(e){var t=e.pageX-o+r,n=e.pageY-i+s;l&&(c.style.transform="translate(".concat(t,"px, ").concat(n,"px)"))},m=function(e){l=!1};Object(Q["i"])(a,"mousedown",u),Object(Q["i"])(document,"mousemove",d),Object(Q["i"])(document,"mouseup",m)},update:function(e,t,n){if(t.value.recover){var a=document.querySelector(t.value.body);a.style.transform=""}}},K=n("b311"),X=n.n(K),J={bind:function(e,t){var n=new X.a(e,{text:function(){return t.value.value}});e.__success_callback__=t.value.success,e.__error_callback__=t.value.error,n.on("success",(function(t){var n=e.__success_callback__;n&&n(t)})),n.on("error",(function(t){var n=e.__error_callback__;n&&n(t)})),e.__clipboard__=n},update:function(e,t){e.__clipboard__.text=function(){return t.value.value},e.__success_callback__=t.value.success,e.__error_callback__=t.value.error},unbind:function(e,t){delete e.__success_callback__,delete e.__error_callback__,e.__clipboard__.destroy(),delete e.__clipboard__}},ee={draggable:Z,clipboard:J},te=ee,ne=function(e){e.directive("draggable",te.draggable),e.directive("clipboard",te.clipboard)},ae=ne,ce=n("df4d"),oe=n("13aa"),ie=(n("ca62"),n("33ec"),n("749a"),n("f95d"),n("caad"),n("2532"),{install:function(e,t){e.directive("has",{inserted:function(e,t,n){var a=n.context.$route.meta.permTypes;if("[object Array]"==Object.prototype.toString.call(t.value)){for(var c=0;c<t.value.length;c++)if(a.includes(t.value[c]))return;e.parentNode.removeChild(e)}if("[object Object]"==Object.prototype.toString.call(t.value)){var o=t.value.have,i=t.value.haveNot;if(o&&void 0!==o)for(var r=0;r<o.length;r++)if(!a.includes(o[r]))return void e.parentNode.removeChild(e);if(i&&void 0!==i)for(r=0;r<i.length;r++)if(a.includes(i[r]))return void e.parentNode.removeChild(e)}"[object String]"==Object.prototype.toString.call(t.value)&&(a.includes(t.value)||e.parentNode.removeChild(e))}})}}),re=ie,se={install:function(e){e.directive("preventReClick",{inserted:function(e,t){e.addEventListener("click",(function(){e.disabled||(e.disabled=!0,setTimeout((function(){e.disabled=!1}),t.value||1e3))}))}})}},le=n("e3e1"),ue=n("bc3a"),de=n.n(ue),me=n("c70b");de.a.defaults.withCredentials=!0,de.a.defaults.timeout=6e4,c["default"].use(re),c["default"].use(se),c["default"].use(G.a,{i18n:function(e,t){return Y["a"].t(e,t)}}),c["default"].use(le["default"]),Object(oe["default"])(c["default"]),c["default"].config.productionTip=!1,c["default"].prototype.$config=M["a"],ae(c["default"]),c["default"].directive("clickOutside",ce["a"]),c["default"].prototype.$mockFormData=function(e){function t(e){return e.replace(/\_(\w)/g,(function(e,t){return t.toUpperCase()}))}var n=[];return e.forEach((function(e){var a={};Object.keys(e).forEach((function(n){a[t(n)]=e[n].value})),n.push(a)})),console.log(n[0]),n},c["default"].prototype.$moneyCover=function(e,t){return e?me.multiply(me.bignumber(e),t).toNumber():e},c["default"].prototype.$time=function(e,t,n){var a="",c="";return e.indexOf("-")>-1?(a=V()(e,"YYYY-MM-DD"),c=V()(n,"YYYY-MM-DD")):(a=V()(e,"YYYYMMDD"),c=V()(n,"YYYYMMDD")),">"===t?a.isAfter(c):a.isBefore(c)},c["default"].directive("defaultSelect",{componentUpdated:function(e,t){var n=t.value||[[]],c=Object(a["a"])(n,1),o=c[0];if(Array.isArray(o)){var i=function(e){e.forEach((function(e,t){e&&t<o.length?e.style.display="none":e&&(e.style.display="")}))},r=e.querySelectorAll(".ivu-tag .ivu-icon-ios-close");0===r.length?setTimeout((function(){var t=e.querySelectorAll(".ivu-tag .ivu-icon-ios-close");i(t)}),0):i(r)}}});var he=$["a"].prototype.push;$["a"].prototype.push=function(e){return he.call(this,e).catch((function(e){return e}))},new c["default"]({el:"#app",router:s["a"],i18n:Y["a"],store:r["a"],render:function(e){return e(q)}})},"66df":function(e,t,n){"use strict";var a=n("d4ec"),c=n("bee2"),o=(n("14d9"),n("b64b"),n("d3b7"),n("bc3a")),i=n.n(o),r=n("4360"),s=n("e069"),l=n("a18c"),u=n("fe07"),d=function(e){"4000"!==e.code?"4001"===e.code||"4002"===e.code?(r["a"].dispatch("handleLogOut",!1).then((function(){l["a"].push({name:"login"})})),setTimeout((function(){s["Notice"].error({title:u["a"].t("common.Error"),desc:u["a"].t("common.token")})}),6e4)):s["Notice"].error({title:u["a"].t("common.Error"),desc:e.msg}):l["a"].push({name:"authenError"})},m=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:baseURL;Object(a["a"])(this,e),this.baseUrl=t,this.queue={}}return Object(c["a"])(e,[{key:"getInsideConfig",value:function(){var e={baseURL:this.baseUrl,headers:{}};return e}},{key:"destroy",value:function(e){delete this.queue[e],Object.keys(this.queue).length}},{key:"interceptors",value:function(e,t){var n=this;e.interceptors.request.use((function(e){if(r["a"].state.user.token){var a=r["a"].state.user.token;r["a"].state.user.userId;0!=e.params?e.headers.Authorization="bearer ".concat(a):r["a"].state.user.token=""}return e.headers.userName=encodeURIComponent(r["a"].state.user.userName,"utf-8"),n.queue[t]=!0,e}),(function(e){return s["Notice"].error({title:u["a"].t("common.Error"),desc:u["a"].t("common.Request")}),Promise.reject(e)})),e.interceptors.response.use((function(e){if(n.destroy(t),"blob"===e.request.responseType){if("application/json;charset=UTF-8"===e.headers["content-type"]){var a=new FileReader;return a.onload=function(e){if(e.target.readyState===FileReader.DONE){var t=JSON.parse(e.target.result);"0000"!==t.code&&d(t)}},void a.readAsText(e.data)}return e}var c=e.data;return"0000"===c.code?c:(d(c),Promise.reject(c))}),(function(e){var a;if(e&&e.response)switch(e.response.status){case 401:r["a"].dispatch("handleLogOut",!1).then((function(){l["a"].push({name:"login"})})),a=u["a"].t("common.token");break;case 402:r["a"].dispatch("handleLogOut",!1).then((function(){l["a"].push({name:"login"})})),a=u["a"].t("common.token");break;case 400:a=e.response.data.msg;break;case 404:a=u["a"].t("common.errorcode");break;case 408:a=u["a"].t("common.timedout");break;case 500:a=u["a"].t("sys.Serverwrong");break;case 503:a=u["a"].t("common.feature");break;default:a=u["a"].t("common.Servererror")}return"ECONNABORTED"==e.code&&-1!=e.message.indexOf("timeout")&&(a=u["a"].t("common.Requestout")),s["Notice"].error({title:u["a"].t("common.Error"),desc:a||u["a"].t("common.Servererror")}),n.destroy(t),Promise.reject(e)}))}},{key:"request",value:function(e){var t=i.a.create();return e=Object.assign(this.getInsideConfig(),e),this.interceptors(t,e.url),t(e)}}])}(),h=m,p=n("f121"),f=p["a"].baseUrl.pro,g=new h(f);t["a"]=g},"6dfa":function(e,t,n){"use strict";n.d(t,"r",(function(){return o})),n.d(t,"F",(function(){return i})),n.d(t,"s",(function(){return r})),n.d(t,"p",(function(){return s})),n.d(t,"x",(function(){return l})),n.d(t,"a",(function(){return u})),n.d(t,"H",(function(){return d})),n.d(t,"d",(function(){return m})),n.d(t,"h",(function(){return h})),n.d(t,"j",(function(){return p})),n.d(t,"k",(function(){return f})),n.d(t,"G",(function(){return g})),n.d(t,"g",(function(){return b})),n.d(t,"f",(function(){return k})),n.d(t,"D",(function(){return y})),n.d(t,"w",(function(){return v})),n.d(t,"e",(function(){return O})),n.d(t,"n",(function(){return C})),n.d(t,"m",(function(){return P})),n.d(t,"C",(function(){return S})),n.d(t,"b",(function(){return I})),n.d(t,"y",(function(){return j})),n.d(t,"B",(function(){return M})),n.d(t,"J",(function(){return _})),n.d(t,"I",(function(){return w})),n.d(t,"l",(function(){return A})),n.d(t,"E",(function(){return D})),n.d(t,"c",(function(){return T})),n.d(t,"z",(function(){return R})),n.d(t,"M",(function(){return N})),n.d(t,"i",(function(){return L})),n.d(t,"o",(function(){return x})),n.d(t,"A",(function(){return E})),n.d(t,"K",(function(){return U})),n.d(t,"q",(function(){return B})),n.d(t,"L",(function(){return F})),n.d(t,"u",(function(){return z})),n.d(t,"t",(function(){return q})),n.d(t,"v",(function(){return $}));var a=n("66df"),c="/cms",o=function(e){return a["a"].request({url:c+"/channel/getChannelA2zOperator",params:e,method:"get"})},i=function(e){return a["a"].request({url:"/sys/api/v1/channel/userName",params:e,method:"get"})},r=function(e){return a["a"].request({url:"cms/channel/getChannelCooperationModeForLogin",params:e,method:"get"})},s=function(e){return a["a"].request({url:c+"/channel/getAccountManagement",params:e,method:"get"})},l=function(e){return a["a"].request({url:c+"/channelSelfServer/cash/package",params:e,method:"get"})},u=function(e){return a["a"].request({url:c+"/channelSelfServer/export/packageDetail",params:e,method:"get",responseType:"blob"})},d=function(e){return a["a"].request({url:c+"/channel/distributors/channelBill",data:e,method:"post"})},m=function(e){return a["a"].request({url:c+"/channel/distributors/channelBill/export",data:e,method:"post"})},h=function(e){return a["a"].request({url:c+"/channelSelfServer/task/detailAlpha",params:e,method:"get"})},p=function(e){return a["a"].request({url:c+"/channelSelfServer/task/detailBeta",params:e,method:"get"})},f=function(e){return a["a"].request({url:c+"/channelSelfServer/task/detailCharlie",params:e,method:"get"})},g=function(e){return a["a"].request({url:c+"/channelSelfServer/support/storeExport",params:e,method:"post"})},b=function(e){return a["a"].request({url:c+"/channelSelfServer/package/packageDetail",params:e,method:"get"})},k=function(e){return a["a"].request({url:c+"/channelSelfServer/package/buySingle",data:e,method:"post"})},y=function(e){return a["a"].request({url:c+"/channelSelfServer/package/price",data:e,method:"post"})},v=function(e){return a["a"].request({url:c+"/channelSelfServer/package/buyBatch",data:e,method:"post",contentType:"multipart/form-data"})},O=function(e){return a["a"].request({url:c+"/channelSelfServer/package/buyBatch",params:e,method:"get"})},C=function(e){return a["a"].request({url:c+"/channelSelfServer/package/file/".concat(e),method:"get",responseType:"blob"})},P=function(e){return a["a"].request({url:c+"/channelSelfServer/package/failFile/".concat(e),method:"get",responseType:"blob"})},S=function(e){return a["a"].request({url:c+"/channelSelfServer/order",params:e,method:"get"})},I=function(e,t){return a["a"].request({url:c+"/channelSelfServer/order?corpId=".concat(e),data:t,method:"post"})},j=function(e){return a["a"].request({url:c+"/logs/searchByLocal",params:e,method:"get"})},M=function(e){return a["a"].request({url:c+"/channelSelfServer/orderExport",data:e,method:"post"})},_=function(e){return a["a"].request({url:c+"/channelSelfServer/support/packageDetail",params:e,method:"get"})},w=function(e){return a["a"].request({url:c+"/channelSelfServer/support/packageUsed",params:e,method:"get"})},A=function(e){return a["a"].request({url:c+"/channelSelfServer/support/active",data:e,method:"post"})},D=function(e){return a["a"].request({url:c+"/channelSelfServer/support/recoveryPackage",params:e,method:"post"})},T=function(e){return a["a"].request({url:"/sys/api/v1/channel",params:e,method:"get"})},R=function(e){return a["a"].request({url:c+"/logs/searchByLocal",params:e,method:"get"})},N=function(e){return a["a"].request({url:"/sys/api/v1/channel",data:e,method:"put"})},L=function(e){return a["a"].request({url:"/sys/api/v1/channel",params:e,method:"delete"})},x=function(e){return a["a"].request({url:"/sys/api/v1/channel/password",data:e,method:"put"})},E=function(e){return a["a"].request({url:"/oms/api/v1/country/queryCounrtyList",params:e,method:"get"})},U=function(e){return a["a"].request({url:c+"/channel/getCorpFlowDetail",params:e,method:"get"})},B=function(e){return a["a"].request({url:c+"/channel/getLowerChannel",params:e,method:"get"})},F=function(e){return a["a"].request({url:c+"/channelSelfServer/stockTransfer",data:e,method:"put"})},z=function(e){return a["a"].request({url:c+"/channel/distributors/getKanbanInfo",params:e,method:"get"})},q=function(e){return a["a"].request({url:c+"/channel/distributors/getChannelSmsTemplate",params:e,method:"get"})},$=function(e){return a["a"].request({url:c+"/channel/emailList ",params:e,method:"get"})}},"6f36":function(e,t,n){"use strict";n("1fff")},7141:function(e,t,n){},7786:function(e,t,n){},"7a3c":function(e,t,n){},"90de":function(e,t,n){"use strict";n.d(t,"a",(function(){return c})),n.d(t,"e",(function(){return o})),n.d(t,"f",(function(){return i})),n.d(t,"i",(function(){return r})),n.d(t,"h",(function(){return s})),n.d(t,"g",(function(){return l})),n.d(t,"d",(function(){return u})),n.d(t,"c",(function(){return m})),n.d(t,"b",(function(){return h}));var a=n("2909"),c=(n("99af"),n("a630"),n("14d9"),n("a9e3"),n("b64b"),n("d3b7"),n("ac1f"),n("25f0"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("3ca3"),n("4d90"),n("5319"),n("ddb0"),function(e,t){if(e.length&&t){var n=-1,a=e.length;while(++n<a){var c=e[n];t(c,n,e)}}}),o=function(e,t){return Array.from(new Set([].concat(Object(a["a"])(e),Object(a["a"])(t))))},i=function(e,t){return e.some((function(e){return t.indexOf(e)>-1}))};var r=function(){return document.addEventListener?function(e,t,n){e&&t&&n&&e.addEventListener(t,n,!1)}:function(e,t,n){e&&t&&n&&e.attachEvent("on"+t,n)}}(),s=function(){return document.removeEventListener?function(e,t,n){e&&t&&e.removeEventListener(t,n,!1)}:function(e,t,n){e&&t&&e.detachEvent("on"+t,n)}}(),l=function(e,t){var n=Object.keys(e),a=Object.keys(t);return n.length===a.length&&(0===n.length&&0===a.length||!n.some((function(n){return e[n]!=t[n]})))},u=function(e,t){var n=["Y","M","D","h","m","s"],a=[],c=new Date(e);for(var o in a.push(c.getFullYear()),a.push(d(c.getMonth()+1)),a.push(d(c.getDate())),a.push(d(c.getHours())),a.push(d(c.getMinutes())),a.push(d(c.getSeconds())),a)t=t.replace(n[o],a[o]);return t},d=function(e){return e=e.toString(),e[1]?e:"0"+e},m=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"";var n=new Date(e),a=n.getFullYear(),c=String(n.getMonth()+1).padStart(2,"0"),o=String(n.getDate()).padStart(2,"0"),i=t?"23:59:59":"00:00:00";return"".concat(a,"-").concat(c,"-").concat(o," ").concat(i)},h=function(e){if(!e)return"";var t=new Date(e),n=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),c=String(t.getDate()).padStart(2,"0");return"".concat(n,"-").concat(a,"-").concat(c)}},"913b":function(e,t,n){},9754:function(e,t,n){"use strict";n("f93a")},a18c:function(e,t,n){"use strict";n("7db0"),n("b0c0"),n("d3b7");var a=n("2b0e"),c=n("8c4f"),o=n("ddb9"),i=n("4360"),r=n("e069"),s=n.n(r),l=n("c276"),u=n("f121"),d=u["a"].homeName;a["default"].use(c["a"]);var m=new c["a"]({mode:"history",routes:o["a"],base:"/cmifront"}),h="login",p=function(e,t,n){Object(l["a"])(e.name,t,o["a"])?n():n({replace:!0,name:"error_401"})};m.beforeEach((function(e,t,n){s.a.LoadingBar.start();var a=i["a"].state.user.token,c=i["a"].state.user.userId,o=sessionStorage.getItem("corpId"),r=sessionStorage.getItem("cooperationMode");if(a&&c||e.name===h)if(a&&c||e.name!==h)if(a&&c&&o&&!r&&e.name===h)n();else if(a&&c&&e.name===h)n({name:d});else if(i["a"].state.user.hasGetInfo){var u=[];try{if(i["a"].state.user.userBtnPriv&&i["a"].state.user.userBtnPriv.length>0){var m=i["a"].state.user.userBtnPriv.find((function(t){return t.url===e.path}));u=m?m.priv:["view"]}else u=["view"];Object(l["l"])(e,u),p(e,i["a"].state.user.access,n)}catch(f){i["a"].commit("setToken",""),i["a"].commit("setUserId",""),i["a"].commit("setUserPriv",[]),i["a"].commit("setAccess",[]),i["a"].commit("setHasGetInfo",!1),n({name:h})}}else i["a"].dispatch("getUserInfo").then((function(t){p(e,t.access,n)})).catch((function(){i["a"].commit("setToken",""),i["a"].commit("setUserId",""),i["a"].commit("setUserPriv",[]),i["a"].commit("setAccess",[]),i["a"].commit("setHasGetInfo",!1),n({name:h})}));else n();else"operationAgencyApproval"==e.name||"proxyReviewed"==e.name||"costToDo"==e.name||"paymentOrderPaySuccess"==e.name||"paymentOrderPayFailed"==e.name||"aprvDetails"==e.name?n():n({name:h})})),m.afterEach((function(e){Object(l["s"])(e,m.app),s.a.LoadingBar.finish(),window.scrollTo(0,0)})),t["a"]=m},a201:function(e,t,n){},b7c7:function(e,t,n){"use strict";n.r(t);n("4360");t["default"]={install:function(e,t){t.developmentOff,e.config.errorHandler=function(t,n,a){t.message,window.location.href;e.nextTick((function(){}))}}}},c276:function(e,t,n){"use strict";n.d(t,"t",(function(){return p})),n.d(t,"j",(function(){return f})),n.d(t,"u",(function(){return g})),n.d(t,"k",(function(){return b})),n.d(t,"e",(function(){return v})),n.d(t,"c",(function(){return O})),n.d(t,"h",(function(){return C})),n.d(t,"v",(function(){return P})),n.d(t,"r",(function(){return S})),n.d(t,"i",(function(){return I})),n.d(t,"d",(function(){return j})),n.d(t,"f",(function(){return M})),n.d(t,"a",(function(){return w})),n.d(t,"g",(function(){return A})),n.d(t,"b",(function(){return T})),n.d(t,"o",(function(){return R})),n.d(t,"p",(function(){return N})),n.d(t,"n",(function(){return L})),n.d(t,"m",(function(){return x})),n.d(t,"q",(function(){return E})),n.d(t,"s",(function(){return U})),n.d(t,"l",(function(){return B}));var a=n("2909"),c=n("5530"),o=(n("d9e2"),n("99af"),n("4de4"),n("c740"),n("caad"),n("d81d"),n("14d9"),n("b0c0"),n("e9c4"),n("b64b"),n("d3b7"),n("ac1f"),n("2532"),n("5319"),n("1276"),n("498a"),n("159b"),n("a78e")),i=n.n(o),r=n("f121"),s=n("90de"),l=r["a"].title,u=r["a"].cookieExpires,d=r["a"].useI18n,m="token",h="userId",p=function(e){i.a.set(m,e,{expires:u||1})},f=function(){var e=i.a.get(m);return e||!1},g=function(e){i.a.set(h,e,{expires:u||1})},b=function(){var e=i.a.get(h);return e||!1},k=function(e){return e.children&&0!==e.children.length},y=function(e,t){return!(e.meta&&e.meta.access&&e.meta.access.length)||!!Object(s["f"])(e.meta.access,t)},v=function(e,t){var n=[];return Object(s["a"])(e,(function(e){if(!e.meta||e.meta&&!e.meta.hideInMenu){var a={icon:e.meta&&e.meta.icon||"",name:e.name,meta:e.meta};(k(e)||e.meta&&e.meta.showAlways)&&y(e,t)&&(a.children=v(e.children,t)),e.meta&&e.meta.href&&(a.href=e.meta.href),y(e,t)&&n.push(a)}})),n},O=function(e,t){var n=Object(c["a"])(Object(c["a"])({},t),{},{icon:t.meta.icon}),o=e.matched;if(o.some((function(e){return e.name===t.name})))return[n];var i=o.filter((function(e){return void 0===e.meta||!e.meta.hideInBread})).map((function(t){var n=Object(c["a"])({},t.meta);n.title&&"function"===typeof n.title&&(n.__titleIsFunction__=!0,n.title=n.title(e));var a={icon:t.meta&&t.meta.icon||"",name:t.name,meta:n};return a}));return i=i.filter((function(e){return!e.meta.hideInMenu})),[Object(c["a"])(Object(c["a"])({},n),{},{to:t.path})].concat(Object(a["a"])(i))},C=function(e){var t=Object(c["a"])({},e),n=Object(c["a"])({},e.meta),a="";return n.title&&("function"===typeof n.title?(n.__titleIsFunction__=!0,a=n.title(t)):a=n.title),n.title=a,t.meta=n,t},P=function(e,t){var n=e.meta,a=n.title,c=n.__titleIsFunction__;if(a)return a=d?a.includes("{{")&&a.includes("}}")&&d?a.replace(/({{[\s\S]+?}})/,(function(e,n){return n.replace(/{{([\s\S]*)}}/,(function(e,n){return t.$t(n.trim())}))})):c?e.meta.title:t.$t(e.name):e.meta&&e.meta.title||e.name,a},S=function(e){localStorage.tagNaveList=JSON.stringify(e)},I=function(){var e=localStorage.tagNaveList;return e?JSON.parse(e):[]},j=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"home",n=-1,a=e.length,c={};while(++n<a){var o=e[n];if(o.children&&o.children.length){var i=j(o.children,t);if(i.name)return i}else o.name===t&&(c=o)}return c},M=function(e,t){var n=t.name,c=t.path,o=t.meta,i=Object(a["a"])(e);return i.findIndex((function(e){return e.name===n}))>=0||i.push({name:n,path:c,meta:o}),i},_=function(e,t){return!t.meta||!t.meta.access||Object(s["f"])(e,t.meta.access)},w=function(e,t,n){var a=function(n){return n.some((function(n){return n.children&&n.children.length?a(n.children):n.name===e?_(t,n):void 0}))};return a(n)},A=function(e,t){var n={};if(2===e.length)n=j(e);else{var a=e.findIndex((function(e){return R(e,t)}));n=a===e.length-1?e[e.length-2]:e[a+1]}return n},D=function(e,t){var n=-1;while(++n<e)t(n)},T=function(e,t){var n=e.parentNode;if(n){var a=n.classList;return a&&t.every((function(e){return a.contains(e)}))?n:T(n,t)}},R=function(e,t){var n=e.params||{},a=t.params||{},c=e.query||{},o=t.query||{};return e.name===t.name&&Object(s["g"])(n,a)&&Object(s["g"])(c,o)},N=function(e,t){var n=e.length,a=!1;return D(n,(function(n){R(e[n],t)&&(a=!0)})),a},L=function(e,t){localStorage.setItem(e,t)},x=function(e){return localStorage.getItem(e)||""},E=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2?arguments[2]:void 0,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:500,c=arguments.length>4?arguments[4]:void 0;window.requestAnimationFrame||(window.requestAnimationFrame=window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||function(e){return window.setTimeout(e,1e3/60)});var o=Math.abs(t-n),i=Math.ceil(o/a*50),r=function(t,n,a){if(t!==n){var o=t+a>n?n:t+a;t>n&&(o=t-a<n?n:t-a),e===window?window.scrollTo(o,o):e.scrollTop=o,window.requestAnimationFrame((function(){return r(o,n,a)}))}else c&&c()};r(t,n,i)},U=function(e,t){var n=C(e),a=P(n,t),c=a?"".concat(l," - ").concat(a):l;window.document.title=c},B=function(e,t){e.meta.permTypes=t}},c7fd:function(e,t,n){},c8c0:function(e,t,n){e.exports=n.p+"img/logo-min.1703d006.jpg"},ca62:function(e,t,n){},cb21:function(e,t,n){"use strict";var a=function(){var e=this,t=e._self._c;return t(e.iconType,{tag:"component",attrs:{type:e.iconName,color:e.iconColor,size:e.iconSize}})},c=[],o=(n("fb6a"),n("a9e3"),function(){var e=this,t=e._self._c;return t("i",{class:"iconfont icon-".concat(e.type),style:e.styles})}),i=[],r={name:"Icons",props:{type:{type:String,required:!0},color:{type:String,default:"#5c6b77"},size:{type:Number,default:16}},computed:{styles:function(){return{fontSize:"".concat(this.size,"px"),color:this.color}}}},s=r,l=n("2877"),u=Object(l["a"])(s,o,i,!1,null,null,null),d=u.exports,m=d,h={name:"CommonIcon",components:{Icons:m},props:{type:{type:String,required:!0},color:String,size:Number},computed:{iconType:function(){return 0===this.type.indexOf("_")?"Icons":"Icon"},iconName:function(){return"Icons"===this.iconType?this.getCustomIconName(this.type):this.type},iconSize:function(){return this.size||("Icons"===this.iconType?12:void 0)},iconColor:function(){return this.color||""}},methods:{getCustomIconName:function(e){return e.slice(1)}}},p=h,f=Object(l["a"])(p,a,c,!1,null,null,null),g=f.exports;t["a"]=g},cf8f:function(e,t,n){"use strict";n("326d")},db6f:function(e,t,n){"use strict";n("a201")},ddb9:function(e,t,n){"use strict";n("d3b7"),n("3ca3"),n("ddb0"),n("b0c0");var a=function(){var e=this,t=e._self._c;return t("Layout",{staticClass:"main",staticStyle:{height:"100%"}},[t("Sider",{staticClass:"left-sider",style:{overflow:"hidden"},attrs:{"hide-trigger":"",collapsible:"",width:256,"collapsed-width":64},model:{value:e.collapsed,callback:function(t){e.collapsed=t},expression:"collapsed"}},[t("side-menu",{ref:"sideMenu",attrs:{accordion:"","active-name":e.$route.name,collapsed:e.collapsed,"menu-list":e.menuList},on:{"on-select":e.turnToPage}},[t("div",{staticClass:"logo-con"},[t("img",{directives:[{name:"show",rawName:"v-show",value:!e.collapsed,expression:"!collapsed"}],key:"max-logo",staticStyle:{width:"240px",height:"60px"},attrs:{src:e.maxLogo}}),t("img",{directives:[{name:"show",rawName:"v-show",value:e.collapsed,expression:"collapsed"}],key:"min-logo",attrs:{src:e.minLogo}})])])],1),t("Layout",[t("Header",{staticClass:"header-con"},[t("header-bar",{attrs:{collapsed:e.collapsed},on:{"on-coll-change":e.handleCollapsedChange}},[t("user",{attrs:{"message-unread-count":e.unreadCount,"user-avatar":e.userAvatar}}),e.$config.useI18n?t("language",{staticStyle:{"margin-right":"10px"},attrs:{lang:e.local},on:{"on-lang-change":e.setLocal}}):e._e(),e.modeLength>1?t("cooperation-mode",{staticStyle:{"margin-right":"10px"},on:{"on-mode-change":e.setCooperationMode}}):e._e(),t("fullscreen",{staticStyle:{"margin-right":"20px"},model:{value:e.isFullscreen,callback:function(t){e.isFullscreen=t},expression:"isFullscreen"}}),t("strong",{staticStyle:{color:"rgb(81, 90, 110)",margin:"0 20px 0 20px","font-size":"15px"}},[e._v("\n\t\t\t\t\t"+e._s(e.$t("common.welcome"))+e._s(e.$store.state.user.userName)+"\n\t\t\t\t")])],1)],1),t("Content",{staticClass:"main-content-con"},[t("Layout",{staticClass:"main-layout-con"},[t("div",{staticClass:"tag-nav-wrapper"},[t("tags-nav",{attrs:{value:e.$route,list:e.tagNavList},on:{input:e.handleClick,"on-close":e.handleCloseTag}})],1),t("Content",{staticClass:"content-wrapper"},[t("keep-alive",{attrs:{include:e.cacheList}},[t("router-view")],1),t("ABackTop",{attrs:{height:100,bottom:80,right:50,container:".content-wrapper"}})],1)],1)],1)],1)],1)},c=[],o=n("2909"),i=n("5530"),r=(n("99af"),n("4de4"),n("7db0"),n("d81d"),n("14d9"),function(){var e=this,t=e._self._c;return t("div",{staticClass:"side-menu-wrapper"},[e._t("default"),t("Menu",{directives:[{name:"show",rawName:"v-show",value:!e.collapsed,expression:"!collapsed"}],ref:"menu",attrs:{"active-name":e.activeName,"open-names":e.openedNames,accordion:e.accordion,theme:e.theme,width:"auto"},on:{"on-select":e.handleSelect}},[e._l(e.menuList,(function(n){return[n.children&&1===n.children.length?[e.showChildren(n)?t("side-menu-item",{key:"menu-".concat(n.name),attrs:{"parent-item":n}}):t("menu-item",{key:"menu-".concat(n.children[0].name),attrs:{name:e.getNameOrHref(n,!0)}},[t("common-icon",{attrs:{type:n.children[0].icon||""}}),t("span",[e._v(e._s(e.showTitle(n.children[0])))])],1)]:[e.showChildren(n)?t("side-menu-item",{key:"menu-".concat(n.name),attrs:{"parent-item":n}}):t("menu-item",{key:"menu-".concat(n.name),attrs:{name:e.getNameOrHref(n)}},[t("common-icon",{attrs:{type:n.icon||""}}),t("span",[e._v(e._s(e.showTitle(n)))])],1)]]}))],2),t("div",{directives:[{name:"show",rawName:"v-show",value:e.collapsed,expression:"collapsed"}],staticClass:"menu-collapsed",attrs:{list:e.menuList}},[e._l(e.menuList,(function(n){return[n.children&&n.children.length>1?t("collapsed-menu",{key:"drop-menu-".concat(n.name),attrs:{"hide-title":"","root-icon-size":e.rootIconSize,"icon-size":e.iconSize,theme:e.theme,"parent-item":n},on:{"on-click":e.handleSelect}}):t("Tooltip",{key:"drop-menu-".concat(n.name),attrs:{transfer:"",content:e.showTitle(n.children&&n.children[0]?n.children[0]:n),placement:"right"}},[t("a",{staticClass:"drop-menu-a",style:{textAlign:"center"},on:{click:function(t){e.handleSelect(e.getNameOrHref(n,!0))}}},[t("common-icon",{attrs:{size:e.rootIconSize,color:e.textColor,type:n.icon||n.children&&n.children[0].icon}})],1)])]}))],2)],2)}),s=[],l=(n("a9e3"),function(){var e=this,t=e._self._c;return t("Submenu",{attrs:{name:"".concat(e.parentName)}},[t("template",{slot:"title"},[t("common-icon",{attrs:{type:e.parentItem.icon||""}}),t("span",[e._v(e._s(e.showTitle(e.parentItem)))])],1),e._l(e.children,(function(n){return[n.children&&1===n.children.length?[e.showChildren(n)?t("side-menu-item",{key:"menu-".concat(n.name),attrs:{"parent-item":n}}):t("menu-item",{key:"menu-".concat(n.children[0].name),attrs:{name:e.getNameOrHref(n,!0)}},[t("common-icon",{attrs:{type:n.children[0].icon||""}}),t("span",[e._v(e._s(e.showTitle(n.children[0])))])],1)]:[e.showChildren(n)?t("side-menu-item",{key:"menu-".concat(n.name),attrs:{"parent-item":n}}):t("menu-item",{key:"menu-".concat(n.name),attrs:{name:e.getNameOrHref(n)}},[t("common-icon",{attrs:{type:n.icon||""}}),t("span",[e._v(e._s(e.showTitle(n)))])],1)]]}))],2)}),u=[],d=n("cb21"),m=n("c276"),h={components:{CommonIcon:d["a"]},methods:{showTitle:function(e){return Object(m["v"])(e,this)},showChildren:function(e){return e.children&&(e.children.length>0||e.meta&&e.meta.showAlways)},getNameOrHref:function(e,t){return e.href?"isTurnByHref_".concat(e.href):t?e.children[0].name:e.name}}},p={props:{parentItem:{type:Object,default:function(){}},theme:String,iconSize:Number},computed:{parentName:function(){return this.parentItem.name},children:function(){return this.parentItem.children},textColor:function(){return"dark"===this.theme?"#fff":"#495060"}}},f={name:"SideMenuItem",mixins:[h,p]},g=f,b=n("2877"),k=Object(b["a"])(g,l,u,!1,null,null,null),y=k.exports,v=function(){var e=this,t=e._self._c;return t("Dropdown",{ref:"dropdown",class:e.hideTitle?"":"collased-menu-dropdown",attrs:{transfer:e.hideTitle,placement:e.placement},on:{"on-click":e.handleClick}},[t("a",{staticClass:"drop-menu-a",style:{textAlign:e.hideTitle?"":"left"},attrs:{type:"text"},on:{mouseover:function(t){return e.handleMousemove(t,e.children)}}},[t("common-icon",{attrs:{size:e.rootIconSize,color:e.textColor,type:e.parentItem.icon}}),e.hideTitle?e._e():t("span",{staticClass:"menu-title"},[e._v(e._s(e.showTitle(e.parentItem)))]),e.hideTitle?e._e():t("Icon",{staticStyle:{float:"right"},attrs:{type:"ios-arrow-forward",size:16}})],1),t("DropdownMenu",{ref:"dropdown",attrs:{slot:"list"},slot:"list"},[e._l(e.children,(function(n){return[e.showChildren(n)?t("collapsed-menu",{key:"drop-".concat(n.name),attrs:{"icon-size":e.iconSize,"parent-item":n}}):t("DropdownItem",{key:"drop-".concat(n.name),attrs:{name:n.name}},[t("common-icon",{attrs:{size:e.iconSize,type:n.icon}}),t("span",{staticClass:"menu-title"},[e._v(e._s(e.showTitle(n)))])],1)]}))],2)],1)},O=[],C={name:"CollapsedMenu",mixins:[h,p],props:{hideTitle:{type:Boolean,default:!1},rootIconSize:{type:Number,default:16}},data:function(){return{placement:"right-end"}},methods:{handleClick:function(e){this.$emit("on-click",e)},handleMousemove:function(e,t){var n=e.pageY,a=38*t.length,c=n+a<window.innerHeight;this.placement=c?"right-start":"right-end"}},mounted:function(){var e=Object(m["b"])(this.$refs.dropdown.$el,["ivu-select-dropdown","ivu-dropdown-transfer"]);e&&(e.style.overflow="visible")}},P=C,S=Object(b["a"])(P,v,O,!1,null,null,null),I=S.exports,j=n("90de"),M={name:"SideMenu",mixins:[h],components:{SideMenuItem:y,CollapsedMenu:I},props:{menuList:{type:Array,default:function(){return[]}},collapsed:{type:Boolean},theme:{type:String,default:"dark"},rootIconSize:{type:Number,default:20},iconSize:{type:Number,default:16},accordion:Boolean,activeName:{type:String,default:""},openNames:{type:Array,default:function(){return[]}}},data:function(){return{openedNames:[]}},methods:{handleSelect:function(e){this.$emit("on-select",e)},getOpenedNamesByActiveName:function(e){return this.$route.matched.map((function(e){return e.name})).filter((function(t){return t!==e}))},updateOpenName:function(e){e===this.$config.homeName?this.openedNames=[]:this.openedNames=this.getOpenedNamesByActiveName(e)}},computed:{textColor:function(){return"dark"===this.theme?"#fff":"#495060"}},watch:{activeName:function(e){this.accordion?this.openedNames=this.getOpenedNamesByActiveName(e):this.openedNames=Object(j["e"])(this.openedNames,this.getOpenedNamesByActiveName(e))},openNames:function(e){this.openedNames=e},openedNames:function(){var e=this;this.$nextTick((function(){e.$refs.menu.updateOpened()}))}},mounted:function(){this.openedNames=Object(j["e"])(this.openedNames,this.getOpenedNamesByActiveName(name))}},_=M,w=(n("4472"),Object(b["a"])(_,r,s,!1,null,null,null)),A=w.exports,D=A,T=function(){var e=this,t=e._self._c;return t("div",{staticClass:"header-bar"},[t("sider-trigger",{attrs:{collapsed:e.collapsed,icon:"md-menu"},on:{"on-change":e.handleCollpasedChange}}),t("custom-bread-crumb",{staticStyle:{"margin-left":"30px"},attrs:{"show-icon":"",list:e.breadCrumbList}}),t("div",{staticClass:"custom-content-con"},[e._t("default")],2)],1)},R=[],N=function(){var e=this,t=e._self._c;return t("a",{class:["sider-trigger-a",e.collapsed?"collapsed":""],attrs:{type:"text"},on:{click:e.handleChange}},[t("Icon",{attrs:{type:e.icon,size:e.size}})],1)},L=[],x={name:"siderTrigger",props:{collapsed:Boolean,icon:{type:String,default:"navicon-round"},size:{type:Number,default:26}},methods:{handleChange:function(){this.$emit("on-change",!this.collapsed)}}},E=x,U=(n("ed00"),Object(b["a"])(E,N,L,!1,null,null,null)),B=U.exports,F=B,z=function(){var e=this,t=e._self._c;return t("div",{staticClass:"custom-bread-crumb"},[t("Breadcrumb",{style:{fontSize:"".concat(e.fontSize,"px")}},e._l(e.list,(function(n){return t("BreadcrumbItem",{key:"bread-crumb-".concat(n.name),attrs:{to:n.to}},[t("common-icon",{staticStyle:{"margin-right":"4px"},attrs:{type:n.icon||""}}),e._v("\n      "+e._s(e.showTitle(n))+"\n    ")],1)})),1)],1)},q=[],$=(n("fb6a"),n("2785"),{name:"customBreadCrumb",components:{CommonIcon:d["a"]},props:{list:{type:Array,default:function(){return[]}},fontSize:{type:Number,default:14},showIcon:{type:Boolean,default:!1}},methods:{showTitle:function(e){return Object(m["v"])(e,this)},isCustomIcon:function(e){return 0===e.indexOf("_")},getCustomIconName:function(e){return e.slice(1)}}}),H=$,V=Object(b["a"])(H,z,q,!1,null,null,null),W=V.exports,G=W,Y=(n("1a88"),{name:"HeaderBar",components:{siderTrigger:F,customBreadCrumb:G},props:{collapsed:Boolean},computed:{breadCrumbList:function(){return this.$store.state.app.breadCrumbList}},methods:{handleCollpasedChange:function(e){this.$emit("on-coll-change",e)}}}),Q=Y,Z=Object(b["a"])(Q,T,R,!1,null,null,null),K=Z.exports,X=K,J=function(){var e=this,t=e._self._c;return t("div",{staticClass:"tags-nav"},[t("div",{staticClass:"close-con"},[t("Dropdown",{staticStyle:{"margin-top":"7px"},attrs:{transfer:""},on:{"on-click":e.handleTagsOption}},[t("Button",{attrs:{size:"small",type:"text"}},[t("Icon",{attrs:{size:18,type:"ios-close-circle-outline"}})],1),t("DropdownMenu",{attrs:{slot:"list"},slot:"list"},[t("DropdownItem",{attrs:{name:"close-all"}},[e._v(e._s(e.$t("common.Closeall")))]),t("DropdownItem",{attrs:{name:"close-others"}},[e._v(e._s(e.$t("common.Closeother")))])],1)],1)],1),t("ul",{directives:[{name:"show",rawName:"v-show",value:e.visible,expression:"visible"}],staticClass:"contextmenu",style:{left:e.contextMenuLeft+"px",top:e.contextMenuTop+"px"}},e._l(e.menuList,(function(n,a){return t("li",{key:a,on:{click:function(t){return e.handleTagsOption(a)}}},[e._v(e._s(n))])})),0),t("div",{staticClass:"btn-con left-btn"},[t("Button",{attrs:{type:"text"},on:{click:function(t){return e.handleScroll(240)}}},[t("Icon",{attrs:{size:18,type:"ios-arrow-back"}})],1)],1),t("div",{staticClass:"btn-con right-btn"},[t("Button",{attrs:{type:"text"},on:{click:function(t){return e.handleScroll(-240)}}},[t("Icon",{attrs:{size:18,type:"ios-arrow-forward"}})],1)],1),t("div",{ref:"scrollOuter",staticClass:"scroll-outer",on:{DOMMouseScroll:e.handlescroll,mousewheel:e.handlescroll}},[t("div",{ref:"scrollBody",staticClass:"scroll-body",style:{left:e.tagBodyLeft+"px"}},[t("transition-group",{attrs:{name:"taglist-moving-animation"}},e._l(e.list,(function(n,a){return t("Tag",{key:"tag-nav-".concat(a),ref:"tagsPageOpened",refInFor:!0,attrs:{type:"dot",name:n.name,"data-route-item":n,closable:n.name!==e.$config.homeName,color:e.isCurrentTag(n)?"primary":"default"},on:{"on-close":function(t){return e.handleClose(n)}},nativeOn:{click:function(t){return e.handleClick(n)},contextmenu:function(t){return t.preventDefault(),e.contextMenu(n,t)}}},[e._v(e._s(e.showTitleInside(n)))])})),1)],1)])])},ee=[],te=(n("caad"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("2532"),n("159b"),n("e069")),ne={before_close_normal:function(e){te["Modal"].confirm({title:"确定要关闭这一页吗",onOk:function(){e(!0)},onCancel:function(){e(!1)}})}},ae=ne,ce={name:"TagsNav",props:{value:Object,list:{type:Array,default:function(){return[]}}},data:function(){return{tagBodyLeft:0,rightOffset:40,outerPadding:4,contextMenuLeft:0,contextMenuTop:0,visible:!1,menuList:{others:this.$t("common.Closeother"),all:this.$t("common.Closeall")}}},computed:{currentRouteObj:function(){var e=this.value,t=e.name,n=e.params,a=e.query;return{name:t,params:n,query:a}}},methods:{handlescroll:function(e){var t=e.type,n=0;"DOMMouseScroll"!==t&&"mousewheel"!==t||(n=e.wheelDelta?e.wheelDelta:40*-(e.detail||0)),this.handleScroll(n)},handleScroll:function(e){var t=this.$refs.scrollOuter.offsetWidth,n=this.$refs.scrollBody.offsetWidth;e>0?this.tagBodyLeft=Math.min(0,this.tagBodyLeft+e):t<n?this.tagBodyLeft<-(n-t)?this.tagBodyLeft=this.tagBodyLeft:this.tagBodyLeft=Math.max(this.tagBodyLeft+e,t-n):this.tagBodyLeft=0},handleTagsOption:function(e){var t=this;if(e.includes("all")){var n=this.list.filter((function(e){return e.name===t.$config.homeName}));this.$emit("on-close",n,"all")}else if(e.includes("others")){var a=this.list.filter((function(e){return Object(m["o"])(t.currentRouteObj,e)||e.name===t.$config.homeName}));this.$emit("on-close",a,"others",this.currentRouteObj),setTimeout((function(){t.getTagElementByRoute(t.currentRouteObj)}),100)}},handleClose:function(e){var t=this;e.meta&&e.meta.beforeCloseName&&e.meta.beforeCloseName in ae?new Promise(ae[e.meta.beforeCloseName]).then((function(n){n&&t.close(e)})):this.close(e)},close:function(e){var t=this.list.filter((function(t){return!Object(m["o"])(e,t)}));this.$emit("on-close",t,void 0,e)},handleClick:function(e){this.$emit("input",e)},showTitleInside:function(e){return Object(m["v"])(e,this)},isCurrentTag:function(e){return Object(m["o"])(this.currentRouteObj,e)},moveToView:function(e){var t=this.$refs.scrollOuter.offsetWidth,n=this.$refs.scrollBody.offsetWidth;n<t?this.tagBodyLeft=0:e.offsetLeft<-this.tagBodyLeft?this.tagBodyLeft=-e.offsetLeft+this.outerPadding:e.offsetLeft>-this.tagBodyLeft&&e.offsetLeft+e.offsetWidth<-this.tagBodyLeft+t?this.tagBodyLeft=Math.min(0,t-e.offsetWidth-e.offsetLeft-this.outerPadding):this.tagBodyLeft=-(e.offsetLeft-(t-this.outerPadding-e.offsetWidth))},getTagElementByRoute:function(e){var t=this;this.$nextTick((function(){t.refsTag=t.$refs.tagsPageOpened,t.refsTag.forEach((function(n,a){if(Object(m["o"])(e,n.$attrs["data-route-item"])){var c=t.refsTag[a].$el;t.moveToView(c)}}))}))},contextMenu:function(e,t){if(e.name!==this.$config.homeName){this.visible=!0;var n=this.$el.getBoundingClientRect().left;this.contextMenuLeft=t.clientX-n+10,this.contextMenuTop=t.clientY-64}},closeMenu:function(){this.visible=!1}},watch:{$route:function(e){this.getTagElementByRoute(e)},list:function(e){var t=this,n=e,a=new Set,c=null;n.forEach((function(e,n){if(e.meta&&e.meta.access&&e.meta.access.length>0){var o=e.meta.access[0];a.has(o)?t.close(c):(a.add(o),c=e)}}))},visible:function(e){e?document.body.addEventListener("click",this.closeMenu):document.body.removeEventListener("click",this.closeMenu)}},mounted:function(){var e=this;setTimeout((function(){e.getTagElementByRoute(e.$route)}),200)}},oe=ce,ie=(n("db6f"),Object(b["a"])(oe,J,ee,!1,null,null,null)),re=ie.exports,se=re,le=function(){var e=this,t=e._self._c;return t("div",{staticClass:"user-avatar-dropdown"},[t("Dropdown",{on:{"on-click":e.handleClick}},[t("Badge",{attrs:{dot:!!e.messageUnreadCount}},[t("Avatar",{attrs:{src:e.userAvatar}})],1),t("Icon",{attrs:{size:18,type:"md-arrow-dropdown"}}),t("DropdownMenu",{attrs:{slot:"list"},slot:"list"},[t("DropdownItem",{attrs:{name:"logout"}},[e._v(e._s(e.$t("common.logOut")))])],1)],1)],1)},ue=[],de=(n("3daa"),n("2f62")),me=n("3108"),he={name:"User",props:{userAvatar:{type:String,default:""},messageUnreadCount:{type:Number,default:0}},methods:Object(i["a"])(Object(i["a"])({},Object(de["b"])(["handleLogOut"])),{},{logout:function(){var e=this;this.handleLogOut().then((function(){e.$router.push({name:"login"})}))},repwd:function(){this.$router.push({name:"pwd_mngr"})},handleClick:function(e){var t=this;switch(e){case"logout":Object(me["e"])(this.$store.state.user.userName).then((function(e){"0000"===e.code&&t.logout()}));break;case"repwd":this.repwd();break}}})},pe=he,fe=Object(b["a"])(pe,le,ue,!1,null,null,null),ge=fe.exports,be=ge,ke=function(){var e=this,t=e._self._c;return t("div",{class:e.classes,style:e.styles,on:{click:e.back}},[e._t("default",(function(){return[t("div",{class:e.innerClasses},[t("i",{staticClass:"ivu-icon ivu-icon-ios-arrow-up"})])]}))],2)},ye=[],ve=n("ade3"),Oe="ivu-back-top",Ce={name:"ABackTop",props:{height:{type:Number,default:400},bottom:{type:Number,default:30},right:{type:Number,default:30},duration:{type:Number,default:1e3},container:{type:null,default:window}},data:function(){return{backTop:!1}},mounted:function(){Object(j["i"])(this.containerEle,"scroll",this.handleScroll),Object(j["i"])(this.containerEle,"resize",this.handleScroll)},beforeDestroy:function(){Object(j["h"])(this.containerEle,"scroll",this.handleScroll),Object(j["h"])(this.containerEle,"resize",this.handleScroll)},computed:{classes:function(){return["".concat(Oe),Object(ve["a"])({},"".concat(Oe,"-show"),this.backTop)]},styles:function(){return{bottom:"".concat(this.bottom,"px"),right:"".concat(this.right,"px")}},innerClasses:function(){return"".concat(Oe,"-inner")},containerEle:function(){return this.container===window?window:document.querySelector(this.container)}},methods:{handleScroll:function(){this.backTop=this.containerEle.scrollTop>=this.height},back:function(){var e="string"===typeof this.container?this.containerEle:document.documentElement||document.body,t=e.scrollTop;Object(m["q"])(this.containerEle,t,0,this.duration),this.$emit("on-click")}}},Pe=Ce,Se=Object(b["a"])(Pe,ke,ye,!1,null,null,null),Ie=Se.exports,je=Ie,Me=function(){var e=this,t=e._self._c;return e.showFullScreenBtn?t("div",{staticClass:"full-screen-btn-con"},[t("Tooltip",{attrs:{content:e.value?e.$t("common.Exitscreen"):e.$t("common.Fullscreen"),placement:"bottom"}},[t("Icon",{attrs:{type:e.value?"md-contract":"md-expand",size:23},nativeOn:{click:function(t){return e.handleChange.apply(null,arguments)}}})],1)],1):e._e()},_e=[],we={name:"Fullscreen",computed:{showFullScreenBtn:function(){return window.navigator.userAgent.indexOf("MSIE")<0}},props:{value:{type:Boolean,default:!1}},methods:{handleFullscreen:function(){var e=document.body;this.value?document.exitFullscreen?document.exitFullscreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen?document.webkitCancelFullScreen():document.msExitFullscreen&&document.msExitFullscreen():e.requestFullscreen?e.requestFullscreen():e.mozRequestFullScreen?e.mozRequestFullScreen():e.webkitRequestFullScreen?e.webkitRequestFullScreen():e.msRequestFullscreen&&e.msRequestFullscreen()},handleChange:function(){this.handleFullscreen()}},mounted:function(){var e=this,t=document.fullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||document.fullScreen||document.mozFullScreen||document.webkitIsFullScreen;t=!!t,document.addEventListener("fullscreenchange",(function(){e.$emit("input",!e.value),e.$emit("on-change",!e.value)})),document.addEventListener("mozfullscreenchange",(function(){e.$emit("input",!e.value),e.$emit("on-change",!e.value)})),document.addEventListener("webkitfullscreenchange",(function(){e.$emit("input",!e.value),e.$emit("on-change",!e.value)})),document.addEventListener("msfullscreenchange",(function(){e.$emit("input",!e.value),e.$emit("on-change",!e.value)})),this.$emit("input",t)}},Ae=we,De=(n("cf8f"),Object(b["a"])(Ae,Me,_e,!1,null,null,null)),Te=De.exports,Re=Te,Ne=function(){var e=this,t=e._self._c;return t("div",[t("Dropdown",{attrs:{trigger:"click"},on:{"on-click":e.selectLang}},[t("a",{attrs:{href:"javascript:void(0)"}},[e._v("\n      "+e._s(e.title)+"\n      "),t("Icon",{attrs:{size:18,type:"md-arrow-dropdown"}})],1),t("DropdownMenu",{attrs:{slot:"list"},slot:"list"},e._l(e.localList,(function(n,a){return t("DropdownItem",{key:"lang-".concat(a),attrs:{name:a}},[e._v(e._s(n))])})),1)],1)],1)},Le=[],xe={name:"Language",inject:["reload"],props:{lang:String},data:function(){return{langList:{"zh-CN":"语言","en-US":"Lang"},localList:{"zh-CN":"中文简体","en-US":"English"}}},watch:{lang:function(e){this.$i18n.locale=e,this.reload()}},computed:{title:function(){return this.langList[this.lang]}},methods:{selectLang:function(e){this.$emit("on-lang-change",e)}}},Ee=xe,Ue=Object(b["a"])(Ee,Ne,Le,!1,null,null,null),Be=Ue.exports,Fe=Be,ze=function(){var e=this,t=e._self._c;return t("div",{staticClass:"error-store"},[t("Badge",{attrs:{dot:"",count:e.countComputed}},[t("Button",{attrs:{type:"text"},on:{click:e.openErrorLoggerPage}},[t("Icon",{attrs:{size:20,type:"ios-bug"}})],1)],1)],1)},qe=[],$e={name:"ErrorStore",props:{count:{type:Number,default:0},hasRead:{type:Boolean,default:!1}},computed:{countComputed:function(){return this.hasRead?0:this.count}},methods:{openErrorLoggerPage:function(){this.$router.push({name:"error_logger_page"})}}},He=$e,Ve=(n("9754"),Object(b["a"])(He,ze,qe,!1,null,null,null)),We=Ve.exports,Ge=We,Ye=n("0c92"),Qe=Ye["a"],Ze=n("c8c0"),Ke=n.n(Ze),Xe=n("0938"),Je=n.n(Xe),et=n("3cd4"),tt=n.n(et),nt=(n("7a3c"),{name:"Main",components:{SideMenu:D,HeaderBar:X,Language:Fe,TagsNav:se,Fullscreen:Re,ErrorStore:Ge,User:be,ABackTop:je,CooperationMode:Qe},data:function(){return{collapsed:!1,minLogo:Ke.a,maxLogo:Je.a,userAvatar:n("4a0e"),isFullscreen:!1,modeLength:""}},computed:Object(i["a"])(Object(i["a"])({},Object(de["c"])(["errorCount"])),{},{tagNavList:function(){return this.$store.state.app.tagNavList},tagRouter:function(){return this.$store.state.app.tagRouter},cacheList:function(){var e=["ParentView"].concat(Object(o["a"])(this.tagNavList.length?this.tagNavList.filter((function(e){return!(e.meta&&e.meta.notCache)})).map((function(e){return e.name})):[]));return e},menuList:function(){return this.$store.getters.menuList},local:function(){return this.$store.state.app.local},hasReadErrorPage:function(){return this.$store.state.app.hasReadErrorPage},unreadCount:function(){return this.$store.state.user.unreadCount}}),methods:Object(i["a"])(Object(i["a"])(Object(i["a"])({},Object(de["d"])(["setBreadCrumb","setTagNavList","addTag","setLocal","setHomeRoute","closeTag","setCooperationMode"])),Object(de["b"])(["handleLogin","handleSSOLogin"])),{},{turnToPage:function(e){var t={},n=t.name,a=t.params,c=t.query;1!==this.$store.state.user.isUpdatePassword?("string"===typeof e?n=e:(n=e.name,a=e.params,c=e.query),n.indexOf("isTurnByHref_")>-1?window.open(n.split("_")[1]):this.$router.push({name:n,params:a,query:c})):this.$router.push({name:"pwd_mngr"})},handleCollapsedChange:function(e){this.collapsed=e},handleCloseTag:function(e,t,n){"others"!==t&&("all"===t?this.turnToPage(this.$config.homeName):Object(m["o"])(this.$route,n)&&this.closeTag(n)),this.setTagNavList(e)},handleClick:function(e){this.turnToPage(e)},setCooperationMode:function(e){sessionStorage.setItem("cooperationMode",e)}}),watch:{$route:function(e){var t=e.name,n=e.query,a=e.params,c=e.meta;this.addTag({route:{name:t,query:n,params:a,meta:c},type:"push"}),this.setBreadCrumb(e),this.setTagNavList(Object(m["f"])(this.tagNavList,e)),this.$refs.sideMenu.updateOpenName(e.name)}},mounted:function(){var e=this;this.setTagNavList(),this.setHomeRoute(pt);var t=this.$route,n=t.name,a=t.params,c=t.query,o=t.meta;this.addTag({route:{name:n,params:a,query:c,meta:o}}),this.setBreadCrumb(this.$route),this.modeLength=sessionStorage.getItem("modeLength");var i=this.$i18n.locale;this.setLocal(i),this.tagNavList.find((function(t){return t.name===e.$route.name}))||this.$router.push({name:this.$config.homeName}),"en-US"===i&&(this.maxLogo=tt.a)}}),at=nt,ct=Object(b["a"])(at,a,c,!1,null,null,null),ot=ct.exports,it=ot,rt=function(){var e=this,t=e._self._c;return t("keep-alive",{attrs:{include:e.cacheList,exclude:e.notCacheName}},[t("router-view",{ref:"child"})],1)},st=[],lt={name:"ParentView",computed:{tagNavList:function(){return this.$store.state.app.tagNavList},notCacheName:function(){return[this.$route.meta&&this.$route.meta.notCache?this.$route.name:""]},cacheList:function(){return["ParentView"].concat(Object(o["a"])(this.tagNavList.length?this.tagNavList.filter((function(e){return!(e.meta&&e.meta.notCache)})).map((function(e){return e.name})):[]))}}},ut=lt,dt=Object(b["a"])(ut,rt,st,!1,null,null,null),mt=dt.exports,ht=mt,pt=t["a"]=[{path:"/login",name:"login",meta:{title:"Login - 登录",hideInMenu:!0},component:function(){return n.e("chunk-16dd0f66").then(n.bind(null,"e49c"))}},{path:"/channelCooperationMode",name:"channelCooperationMode",meta:{title:"渠道商合作模式",hideInMenu:!0},component:function(){return n.e("chunk-26853dd0").then(n.bind(null,"1e7c"))}},{path:"/spinLoading",name:"spinLoading",meta:{title:"加载中",hideInMenu:!0},component:function(){return n.e("chunk-00ea03aa").then(n.bind(null,"000a"))}},{path:"/paymentOrder/successed",name:"paymentOrderPaySuccess",meta:{hideInMenu:!0,title:"支付成功"},component:function(){return n.e("chunk-5a32affb").then(n.bind(null,"71eb"))}},{path:"/paymentOrder/failed",name:"paymentOrderPayFailed",meta:{hideInMenu:!0,title:"支付失败"},component:function(){return n.e("chunk-7ceed6ae").then(n.bind(null,"63ca"))}},{path:"/",name:"_home",redirect:"/home",component:it,meta:{hideInMenu:!0,notCache:!0},children:[{path:"/home",name:"home",meta:{hideInMenu:!0,title:"首页",notCache:!0,icon:"md-home"},component:function(){return n.e("chunk-53230ece").then(n.bind(null,"5be2"))}}]},{path:"/sys",name:"system_mngr",meta:{title:"系统管理",icon:"logo-buffer",access:["system_mngr"]},component:it,children:[{path:"/accountList",name:"account_list",meta:{icon:"md-people",title:"账户管理",access:["account_list"]},component:function(){return n.e("chunk-18b344ff").then(n.bind(null,"178f"))}},{path:"/pwdmngr",name:"pwd_mngr",meta:{icon:"md-person",title:"账户密码管理",access:["pwd_mngr"]},component:function(){return n.e("chunk-f4392dec").then(n.bind(null,"cf08"))}},{path:"/roleMngr",name:"pri_mngr",meta:{icon:"md-contacts",title:"角色管理",access:["pri_mngr"]},component:function(){return n.e("chunk-3d3a637e").then(n.bind(null,"b3b5"))}},{path:"/loginLog",name:"login_mngr",meta:{icon:"md-document",title:"登录日志",access:["login_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-********")]).then(n.bind(null,"58ac"))}},{path:"/operatorLog",name:"log_mngr",meta:{icon:"ios-paper",title:"操作日志",access:["log_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-ac9b06d4")]).then(n.bind(null,"b25f"))}},{path:"/Announcement",name:"Announcement_mngr",meta:{icon:"ios-book",title:"公告管理",access:["Announcement_mngr"]},component:function(){return n.e("chunk-102c213c").then(n.bind(null,"b6c0"))}},{path:"/lockModeApproval",name:"lockModeApproval_mngr",meta:{icon:"ios-lock",title:"金锁模式管理",access:["lockModeApproval_mngr"]},component:function(){return n.e("chunk-546983df").then(n.bind(null,"b344"))}}]},{path:"/resource",name:"resource",meta:{icon:"md-cart",title:"码号资源管理",access:["resource"]},component:it,children:[{path:"/msisdn",name:"msisdn",meta:{access:["msisdn"],icon:"md-bowtie",title:"MSISDN管理"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-2ed110c0")]).then(n.bind(null,"9d33"))}},{path:"/iccid",name:"iccid",meta:{access:["iccid"],icon:"md-bug",title:"ICCID管理"},component:function(){return n.e("chunk-1ade1f66").then(n.bind(null,"1694"))}},{path:"/imsi",name:"imsi",meta:{access:["imsi"],icon:"md-funnel",title:"IMSI管理"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-84f36746")]).then(n.bind(null,"fd05"))}},{path:"/supplyImsi",name:"supplyImsi",meta:{access:["supplyImsi"],icon:"md-funnel",title:"消息上报IMSI管理"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-bdaf01ce")]).then(n.bind(null,"9539"))}},{path:"/makeCardFile",name:"makeCardFile_mngr",meta:{access:["makeCardFile_mngr"],icon:"md-card",title:"制卡文件管理"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-f6211cec"),n.e("chunk-18603135"),n.e("chunk-270d63d7")]).then(n.bind(null,"d8f7"))}},{path:"/addCard",name:"addCard_mngr",meta:{icon:"md-card",title:"新建制卡任务",hideInMenu:!0},component:function(){return n.e("chunk-784b66ea").then(n.bind(null,"28b2"))}},{path:"/otaData_mngr",name:"otaData_mngr",meta:{access:["otaData_mngr"],icon:"md-analytics",title:"OTA数据管理"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-4cc2a241")]).then(n.bind(null,"6881"))}}]},{path:"/product",name:"product",meta:{access:["product"],icon:"md-menu",title:"产品管理"},component:it,children:[{path:"/makeCard",name:"makeCard",meta:{access:["makeCard"],icon:"md-funnel",title:"制卡管理"},component:function(){return n.e("chunk-2521a24a").then(n.bind(null,"df92"))}},{path:"/masterCard",name:"masterCard",meta:{access:["masterCard"],icon:"md-filing",title:"主卡管理"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-3fd7faf3")]).then(n.bind(null,"6288"))}},{path:"/cardPool",name:"cardPool",meta:{icon:"md-medical",access:["cardPool"],title:"卡池管理"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-0b51c142")]).then(n.bind(null,"3409"))}},{path:"/vimsi",name:"vimsi",meta:{icon:"md-send",access:["vimsi"],title:"VIMSI管理"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-dee6defa")]).then(n.bind(null,"6262"))}},{path:"/associationGroup",name:"associationGroup",meta:{icon:"ios-beaker",access:["associationGroup"],title:"国家卡池关联组"},component:function(){return n.e("chunk-39807813").then(n.bind(null,"d82c"))}},{path:"/addCardPool",name:"addCardPool",meta:{icon:"ios-beaker",title:"新增关联组",access:["addCardPool"],hideInMenu:!0},component:function(){return n.e("chunk-3700f517").then(n.bind(null,"38c6"))}},{path:"/updateCardPool",name:"updateCardPool",meta:{icon:"ios-beaker",title:"编辑关联组",access:["updateCardPool"],hideInMenu:!0},component:function(){return n.e("chunk-3700f517").then(n.bind(null,"38c6"))}},{path:"/copyCardPool",name:"copyCardPool",meta:{icon:"ios-beaker",title:"复制关联组",access:["copyCardPool"],hideInMenu:!0},component:function(){return n.e("chunk-3700f517").then(n.bind(null,"38c6"))}},{path:"/cardPooldetails",name:"cardPooldetails",meta:{icon:"ios-beaker",access:["cardPooldetails"],title:"关联组详情",hideInMenu:!0},component:function(){return n.e("chunk-3700f517").then(n.bind(null,"38c6"))}},{path:"/specialCountryRule",name:"specialCountryRule",meta:{icon:"md-globe",access:["specialCountryRule"],title:"特殊国家规则管理"},component:function(){return Promise.all([n.e("chunk-2d0aa5b8"),n.e("chunk-6d455b82")]).then(n.bind(null,"a91b"))}},{path:"/specialCountryRuleAdd",name:"specialCountryRuleAdd",meta:{icon:"md-add",access:["specialCountryRuleAdd"],title:"新增特殊国家规则",hideInMenu:!0},component:function(){return n.e("chunk-14e34fd6").then(n.bind(null,"614b"))}},{path:"/specialCountryRuleEdit",name:"specialCountryRuleEdit",meta:{icon:"md-create",access:["specialCountryRuleEdit"],title:"编辑特殊国家规则",hideInMenu:!0},component:function(){return n.e("chunk-14e34fd6").then(n.bind(null,"614b"))}}]},{path:"/packageMngr",name:"packageManage",meta:{access:["packageManage"],icon:"md-pricetags",title:"套餐管理",showAlways:!0},component:it,children:[{path:"/package",name:"packageIndex",meta:{icon:"md-pricetag",title:"套餐管理",access:["packageIndex"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-4d50a3ca")]).then(n.bind(null,"a0dd"))}},{path:"/packageAdd",name:"packageAdd",meta:{icon:"ios-list",title:"套餐新增",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-39d3d59e"),n.e("chunk-de26f0aa")]).then(n.bind(null,"ec3e"))}},{path:"/packageEdit",name:"packageUpdate",meta:{icon:"ios-list",title:"套餐编辑",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-39d3d59e"),n.e("chunk-392dc157")]).then(n.bind(null,"1fe5"))}},{path:"/packageDetails",name:"packageInfo",meta:{icon:"ios-list",title:"套餐详情",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-39d3d59e"),n.e("chunk-36315a4c")]).then(n.bind(null,"7ea5"))}},{path:"/cardPoolDetail",name:"cardPoolDetail",meta:{icon:"ios-list",title:"套餐卡池详情",hideInMenu:!0},component:function(){return n.e("chunk-611b4a0c").then(n.bind(null,"1a59"))}},{path:"/packageCopy",name:"packageCopy",meta:{icon:"ios-list",title:"套餐复制",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-39d3d59e"),n.e("chunk-9a319a28")]).then(n.bind(null,"e8ff"))}},{path:"/trafficPool",name:"trafficPool_mngr",meta:{icon:"md-wifi",title:"流量池管理",access:["trafficPool_mngr"]},component:function(){return n.e("chunk-7cbfd403").then(n.bind(null,"4f8b"))}},{path:"/addPool",name:"addPool_mngr",meta:{icon:"md-wifi",title:"新建流量池",hideInMenu:!0},component:function(){return n.e("chunk-f45bd9fc").then(n.bind(null,"6716"))}},{path:"/editPool",name:"editPool_mngr",meta:{icon:"md-wifi",title:"修改流量池",hideInMenu:!0},component:function(){return n.e("chunk-4f20c4bb").then(n.bind(null,"c770"))}},{path:"/copyPool",name:"copyPool_mngr",meta:{icon:"md-wifi",title:"复制流量池",hideInMenu:!0},component:function(){return n.e("chunk-4daabb95").then(n.bind(null,"ae73"))}},{path:"/detailsPool",name:"detailsPool_mngr",meta:{icon:"md-wifi",title:"流量池详情",hideInMenu:!0},component:function(){return n.e("chunk-1f5746f4").then(n.bind(null,"257f"))}},{path:"/fuelPack",name:"fuelPack_mngr",meta:{icon:"md-briefcase",title:"加油包管理",access:["fuelPack_mngr"]},component:function(){return n.e("chunk-34cbb637").then(n.bind(null,"0230"))}},{path:"/upccTemplate",name:"upccTemplate_mngr",meta:{icon:"md-list-box",title:"UPCC速度模板管理",access:["upccTemplate_mngr"]},component:function(){return n.e("chunk-55d74b39").then(n.bind(null,"d63f"))}},{path:"/channelProviderPackage",name:"channelProvider_mngr",meta:{icon:"md-contact",title:"渠道商套餐管理",access:["channelProvider_mngr"]},component:function(){return Promise.all([n.e("chunk-68d8897c"),n.e("chunk-da68b3ee")]).then(n.bind(null,"1a2de"))}},{path:"/targetedApplicationManagement",name:"targetedApplication_mngr",meta:{icon:"md-ionic",title:"定向应用管理",access:["targetedApplication_mngr"]},component:function(){return n.e("chunk-be3b1982").then(n.bind(null,"1777"))}},{path:"/whiteListPackageMngr",name:"whiteListPackage_mngr",meta:{icon:"md-ionic",title:"白名单套餐管理",access:["whiteListPackage_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-2d0aa5b8"),n.e("chunk-480b826e")]).then(n.bind(null,"8581"))}}]},{path:"/cdr",name:"cdr_mngr",meta:{title:"CDR话单管理",icon:"md-list-box",access:["cdr_mngr"]},component:it,children:[{path:"/callQuery",name:"call_query",meta:{icon:"md-search",title:"话单查询",access:["call_query"]},component:function(){return n.e("chunk-02262664").then(n.bind(null,"54cc"))}},{path:"/priceRules",name:"price_rules",meta:{icon:"md-settings",title:"批价规则管理",access:["price_rules"]},component:function(){return n.e("chunk-718b22b1").then(n.bind(null,"8d8a"))}},{path:"/companyPrice",name:"company_price",meta:{icon:"md-podium",title:"企业运营商账单统计",access:["company_price"]},component:function(){return n.e("chunk-da7cb97e").then(n.bind(null,"d084"))}},{path:"/callListDetail",name:"callListinfo",meta:{icon:"md-podium",title:"话单详情",hideInMenu:!0},component:function(){return n.e("chunk-d2138398").then(n.bind(null,"5774"))}}]},{path:"/download",name:"download_mngr",meta:{title:"下载管理",icon:"md-arrow-round-down",access:["download_mngr"]},component:it,children:[{path:"/taskList",name:"taskListinfo",meta:{icon:"ios-cloud-download",title:"下载列表",access:["taskListinfo"]},component:function(){return n.e("chunk-0d6f39f2").then(n.bind(null,"8f3a"))}}]},{path:"/productOperation",name:"product_operation",meta:{title:"产品运营",icon:"md-ionic",access:["product_operation"]},component:it,children:[{path:"/packageConfig",name:"package_config",meta:{access:["package_config"],showAlways:!0,icon:"md-list",title:"套餐配置"},component:ht,children:[{path:"/individualConfig",name:"individual_config",meta:{icon:"md-build",access:["individual_config"],title:"单独配置"},component:function(){return n.e("chunk-320e0938").then(n.bind(null,"45fc"))}},{path:"/batchConfig",name:"batch_config",meta:{access:["batch_config"],icon:"md-construct",title:"批量配置"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-f6211cec"),n.e("chunk-18603135"),n.e("chunk-12cfd24a")]).then(n.bind(null,"4b07"))}}]},{path:"/inventoryMngr",name:"inventory_mngr",meta:{icon:"md-home",title:"库存管理",access:["inventory_mngr"]},component:function(){return n.e("chunk-de3e7d6c").then(n.bind(null,"a595"))}},{path:"/porderMngr",name:"porder_mngr",meta:{icon:"md-ribbon",title:"个人订单管理",access:["porder_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-2838216f")]).then(n.bind(null,"6d60"))}},{path:"/packageDelay",name:"package_delay",meta:{access:["package_delay"],icon:"md-calendar",title:"套餐延期"},component:ht,children:[{path:"/packageSearch",name:"package_search",meta:{access:["package_search"],icon:"md-search",title:"未激活套餐查询"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-23596e77")]).then(n.bind(null,"ffa6"))}},{path:"/delayTaskAudit",name:"delayTask_audit",meta:{access:["delayTask_audit"],icon:"md-finger-print",title:"延期任务审核"},component:function(){return n.e("chunk-6c68df70").then(n.bind(null,"af20"))}}]},{path:"/activationRcord",name:"activation_record",meta:{access:["activation_record"],icon:"md-list",title:"激活记录查询"},component:ht,children:[{path:"/ordinaryPackage",name:"ordinary_package",meta:{access:["ordinary_package"],icon:"md-search",title:"全球卡普通套餐"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-4fe0f6d8")]).then(n.bind(null,"557d"))}},{path:"/offlineMode",name:"offline_mode",meta:{access:["offline_mode"],icon:"md-search",title:"终端厂商线下模式"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-66ab560d")]).then(n.bind(null,"fb57"))}},{path:"/onlineMode",name:"online_mode",meta:{access:["online_mode"],icon:"md-search",title:"终端厂商线上模式"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-368b7e8b")]).then(n.bind(null,"fc15"))}},{path:"/cooperativeModel",name:"cooperative_model",meta:{access:["cooperative_model"],icon:"md-search",title:"合作运营商模式"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-012f97c7")]).then(n.bind(null,"6bfc"))}},{path:"/activateStatAndSearch",name:"activateStat_search",meta:{access:["activateStat_search"],icon:"md-list",showAlways:!0,title:"激活统计查询"},component:ht,children:[{path:"/activateStat",name:"activate_stat",meta:{access:["activate_stat"],icon:"md-search",title:"激活统计查询"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-a6062dfe")]).then(n.bind(null,"aea1"))}},{path:"/availabilityStat",name:"availability_stat",meta:{access:["availability_stat"],icon:"md-search",title:"激活统计分类"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-012bc5f4")]).then(n.bind(null,"3b33"))}},{path:"/statEndMonth",name:"statEnd_month",meta:{access:["statEnd_month"],icon:"md-search",title:"月底未激活统计"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-33544970")]).then(n.bind(null,"d58a"))}}]}]},{path:"/SupplierOrder",name:"SupplierOrder_management",meta:{icon:"ios-contacts",title:"渠道商订单管理",access:["SupplierOrder_management"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-cf78aefe")]).then(n.bind(null,"3121"))}},{path:"/ChannelCardOrders",name:"ChannelCardOrders_management",meta:{icon:"ios-card-outline",title:"渠道商白卡订单管理",access:["ChannelCardOrders_management"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-2d0aa5b8"),n.e("chunk-309af1d5")]).then(n.bind(null,"a7d2"))}}]},{path:"/channel",name:"channel_mngr",meta:{icon:"ios-people",access:["channel_mngr"],title:"渠道自服务",showAlways:!0},component:it,children:[{path:"/deposit",name:"deposit_mngr",meta:{icon:"logo-usd",title:"账户管理",access:["deposit_mngr"]},component:function(){return n.e("chunk-8c1e4f6c").then(n.bind(null,"6a11"))}},{path:"/mealList",name:"mealList_mngr",meta:{icon:"md-funnel",hideInMenu:!0,title:"套餐详情",access:["mealList_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-8c9b7574")]).then(n.bind(null,"ec4e"))}},{path:"/streamList",name:"streamList_mngr",meta:{icon:"md-funnel",hideInMenu:!0,title:"流水详情",access:["streamList_mngr"]},component:function(){return n.e("chunk-2d0e51c0").then(n.bind(null,"92a2"))}},{path:"/offlinePayment",name:"offlinePayment",meta:{icon:"md-funnel",hideInMenu:!0,title:"充值页面",access:["offlinePayment"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-1942c74d"),n.e("chunk-0b30fc26")]).then(n.bind(null,"a874"))}},{path:"/marketingAccount",name:"marketingAccount",meta:{icon:"md-funnel",hideInMenu:!0,title:"营销活动详情",access:["marketingAccount"]},component:function(){return n.e("chunk-aae304a4").then(n.bind(null,"f3c4"))}},{path:"/stock",name:"stock_mngr",meta:{icon:"ios-home",title:"库存管理",access:["stock_mngr"]},component:function(){return n.e("chunk-72c49067").then(n.bind(null,"ee7a"))}},{path:"/cardList",name:"cardList_mngr",meta:{icon:"md-funnel",hideInMenu:!0,title:"卡片详情",access:["cardList_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-ee67481a")]).then(n.bind(null,"b66a"))}},{path:"/showiccid",name:"showiccid_mngr",meta:{icon:"md-funnel",hideInMenu:!0,title:"iccid列表",access:["showiccid_mngr"]},component:function(){return n.e("chunk-72c49067").then(n.bind(null,"ee7a"))}},{path:"/aqCode",name:"aqCode_mngr",meta:{icon:"md-apps",title:"二维码管理",access:["aqCode_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-3d9cad84")]).then(n.bind(null,"4a33"))}},{path:"/channelfuelPack",name:"channelfuelPack_mngr",meta:{icon:"md-briefcase",title:"加油包管理",access:["channelfuelPack_mngr"]},component:function(){return n.e("chunk-7a372520").then(n.bind(null,"0b8e"))}},{path:"/channelpackage",name:"channelpackage",meta:{icon:"md-pricetag",title:"套餐管理",access:["channelpackage_mngr"]},component:function(){return Promise.all([n.e("chunk-68d8897c"),n.e("chunk-d71dc250")]).then(n.bind(null,"f30e"))}},{path:"/fuelPackManagement",name:"fuelPackManagement",meta:{icon:"md-pricetag",title:"加油包管理",hideInMenu:!0},component:function(){return n.e("chunk-879c9ab8").then(n.bind(null,"c69c"))}},{path:"/buymeal",name:"buymeal_mngr",meta:{icon:"md-cart",title:"套餐购买",access:["buymeal_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-15317ec4"),n.e("chunk-0d065d94")]).then(n.bind(null,"a692"))}},{path:"/order",name:"order_mngr",meta:{icon:"ios-clipboard",title:"订单管理",access:["order_mngr"]},component:function(){return n.e("chunk-8b1aa502").then(n.bind(null,"1ec4"))}},{path:"/bill",name:"bill_mngr",meta:{icon:"md-funnel",hideInMenu:!0,title:"月账单",access:["bill_mngr"]},component:function(){return n.e("chunk-1dc45689").then(n.bind(null,"297c"))}},{path:"/support",name:"support_mngr",meta:{icon:"md-contacts",title:"服务与支持",access:["support_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-f6211cec"),n.e("chunk-18603135"),n.e("chunk-de7ad25e"),n.e("chunk-2f52cadf")]).then(n.bind(null,"a2cc"))}},{path:"/detailsList",name:"detailsList_mngr",meta:{icon:"md-funnel",title:"服务与支持套餐详情",hideInMenu:!0,access:["detailsList_mngr"]},component:function(){return n.e("chunk-0c0cc883").then(n.bind(null,"b560"))}},{path:"/useList",name:"useList_mngr",meta:{icon:"md-funnel",title:"使用详情",hideInMenu:!0,access:["useList_mngr"]},component:function(){return n.e("chunk-1e40043a").then(n.bind(null,"cd36"))}},{path:"/splocationPackage",name:"splocation_package",meta:{icon:"md-pin",hideInMenu:!0,access:["splocation_package"],title:"当前位置套餐"},component:function(){return n.e("chunk-0e219e0a").then(n.bind(null,"bc18"))}},{path:"/sppurchasedPackage",name:"sppurchased_package",meta:{icon:"md-cart",hideInMenu:!0,access:["sppurchased_package"],title:"已购买套餐"},component:function(){return n.e("chunk-420b7338").then(n.bind(null,"7480"))}},{path:"/address",name:"address_mngr",meta:{icon:"md-pin",title:"账户管理",permTypes:[],access:["address_mngr"]},component:function(){return n.e("chunk-c9efabe2").then(n.bind(null,"8663"))}},{path:"/flowpool",name:"flowpool_mngr",meta:{access:["flowpool_mngr"],icon:"md-wifi",showAlways:!1,title:"流量池"},component:ht,children:[{path:"/cardnumlist",name:"cardlist_mngr",meta:{icon:"md-list-box",access:["cardlist_mngr"],title:"卡号列表"},component:function(){return n.e("chunk-8b38ae7a").then(n.bind(null,"917e"))}},{path:"/flowlist",name:"flowlist_mngr",meta:{icon:"md-list",access:["flowlist_mngr"],title:"流量池列表"},component:function(){return n.e("chunk-2301e1ef").then(n.bind(null,"a4ca"))}},{path:"/iccidlist",name:"iccidlist_mngr",meta:{icon:"md-list",hideInMenu:!0,access:["iccidlist_mngr"],title:"ICCID列表"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-47eccb06")]).then(n.bind(null,"0c36"))}},{path:"/userecord",name:"userecord_mngr",meta:{icon:"ios-create-outline",access:["userecord_mngr"],title:"使用记录"},component:function(){return n.e("chunk-23c6e1af").then(n.bind(null,"8117"))}}]},{path:"/subChannelProvider",name:"subChannelProvider_mngr",meta:{icon:"md-body",title:"子渠道商管理",access:["subChannelProvider_mngr"]},component:function(){return n.e("chunk-6317f4f8").then(n.bind(null,"7538"))}},{path:"/whiteCardOrders",name:"whiteCardOrders_management",meta:{icon:"ios-card-outline",title:"白卡订单管理",access:["whiteCardOrders_management"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-2d0aa5b8"),n.e("chunk-422aaf0c")]).then(n.bind(null,"3482"))}},{path:"/channelResourceCooperation",name:"channelResourceCooperation",meta:{icon:"md-infinite",access:["channelResourceCooperation"],title:"资源管理"},component:function(){return Promise.all([n.e("chunk-2d0aa5b8"),n.e("chunk-347e71a8"),n.e("chunk-2d229b36")]).then(n.bind(null,"df20"))}},{path:"/channelViewResources",name:"channelViewResources",meta:{hideInMenu:!0,access:["channelViewResources"],title:"查看资源"},component:function(){return Promise.all([n.e("chunk-2d0aa5b8"),n.e("chunk-d7c3d1f6"),n.e("chunk-2d0decc5")]).then(n.bind(null,"86d5"))}},{path:"/channelCallOrderDetails",name:"channelCallOrderDetails",meta:{hideInMenu:!0,access:["channelCallOrderDetails"],title:"话单明细"},component:function(){return Promise.all([n.e("chunk-2d0aa5b8"),n.e("chunk-85384882"),n.e("chunk-2d0a45ef")]).then(n.bind(null,"05c7"))}},{path:"/channelBillingStatistics",name:"channelBillingStatistics",meta:{hideInMenu:!0,access:["channelBillingStatistics"],title:"账单统计"},component:function(){return Promise.all([n.e("chunk-2d0aa5b8"),n.e("chunk-068f3754"),n.e("chunk-2d0bff35")]).then(n.bind(null,"4069"))}},{path:"/channelBillingQuery",name:"channelBillingQuery",meta:{icon:"md-football",access:["channelBillingQuery"],title:"渠道商账单查询"},component:function(){return Promise.all([n.e("chunk-1942c74d"),n.e("chunk-a7f9d97c")]).then(n.bind(null,"460d"))}},{path:"/paymentOrder/management",name:"paymentOrderManagement",meta:{icon:"md-list",access:["paymentOrderManagement"],title:"支付记录查询"},component:function(){return Promise.all([n.e("chunk-1942c74d"),n.e("chunk-2d237ad7")]).then(n.bind(null,"fbaf"))}}]},{path:"/operators",name:"operators_mngr",meta:{icon:"md-person",title:"运营商管理",access:["operators_mngr"]},component:it,children:[{path:"/operatorsindex",name:"operatorsindex_mngr",meta:{title:"运营商管理",icon:"md-person",access:["operators_mngr"]},component:function(){return n.e("chunk-21556441").then(n.bind(null,"5c92"))}},{path:"/resourceSupplier",name:"ResourceSupplier_mngr",meta:{title:"资源供应商管理",icon:"md-person",access:["ResourceSupplier_mngr"]},component:function(){return n.e("chunk-8567cddc").then(n.bind(null,"3d2b"))}}]},{path:"/channelSell",name:"channelSell_mngr",meta:{icon:"md-person",title:"渠道商销售数据",access:["channelSell_mngr"]},component:it,children:[{path:"/channelSellindex",name:"channelSellindex_mngr",meta:{title:"首页",icon:"md-git-compare",access:["channelSellindex_mngr"]},component:function(){return n.e("chunk-855242c8").then(n.bind(null,"705b"))}},{path:"/channelSellHistory",name:"channelSellHistory_mngr",meta:{title:"历史账单",icon:"md-analytics",access:["channelSellHistory_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-ae66aca4")]).then(n.bind(null,"caaa"))}},{path:"/topUpRecords",name:"topUp_records",meta:{title:"充值记录",icon:"md-recording",access:["topUp_records"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-0cffe042")]).then(n.bind(null,"0851"))}}]},{path:"/report",name:"report_mngr",meta:{icon:"md-print",title:"报表功能",access:["report_mngr"]},component:it,children:[{path:"/exchange",name:"exchange_mngr",meta:{icon:"logo-yen",title:"汇率管理",access:["exchange_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-e766a746")]).then(n.bind(null,"09e8"))}},{path:"/cardsell",name:"cardsell_mngr",meta:{icon:"ios-card",title:"卡销售报表",access:["cardsell_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-13e901c7")]).then(n.bind(null,"4b0e"))}},{path:"/cardactive",name:"cardactive_mngr",meta:{icon:"md-card",title:"卡激活报表",access:["cardactive_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-33e028d4")]).then(n.bind(null,"3d74"))}},{path:"/subscription",name:"subscription_mngr",meta:{icon:"md-cart",title:"套餐订购报表",access:["subscription_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-5215dd0c")]).then(n.bind(null,"1797"))}},{path:"/activation",name:"activation_mngr",meta:{icon:"md-pulse",title:"套餐激活报表",access:["activation_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-a948aa0a")]).then(n.bind(null,"a225"))}},{path:"/reuse",name:"reuse_mngr",meta:{icon:"md-refresh",title:"重复使用查询",access:["reuse_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-1708a62c")]).then(n.bind(null,"3028"))}},{path:"/analysis",name:"analysis_mngr",meta:{icon:"md-stats",title:"套餐分析报表",access:["analysis_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-cf4c1516")]).then(n.bind(null,"70d2"))}},{path:"/terminal",name:"terminal_mngr",meta:{icon:"ios-phone-portrait",title:"终端结算报表",access:["terminal_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-58880d83")]).then(n.bind(null,"c4f1"))}},{path:"/afterPay",name:"terminal_after_pay",meta:{icon:"logo-bitcoin",title:"后付费结算报表",access:["terminal_after_pay"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-43b4168a")]).then(n.bind(null,"b6a0"))}},{path:"/channelReport",name:"channelReport_mngr",meta:{icon:"ios-contact",title:"运营商结算报表",access:["channelReport_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-1cc512a8")]).then(n.bind(null,"917d"))}},{path:"/income",name:"income_mngr",meta:{icon:"ios-home",title:"线下收入报表",access:["income_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-c0cb18d6")]).then(n.bind(null,"ca4d"))}},{path:"/costReport",name:"costReport_mngr",meta:{icon:"logo-yen",title:"成本报表",access:["costReport_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-5f1a6de2")]).then(n.bind(null,"4d51"))}},{path:"/esimDownloadReport",name:"esimDownloadReport_mngr",meta:{icon:"logo-euro",title:"ESIM Download报表",access:["esimDownloadReport"]},component:function(){return n.e("chunk-10a60bb9").then(n.bind(null,"bbaa"))}}]},{path:"/sms",name:"smsManage",meta:{icon:"md-mail",access:["smsManage"],title:"短信管理",showAlways:!0},component:it,children:[{path:"/noticeSMSMngr",name:"notificationSMS",meta:{icon:"ios-mail",access:["notificationSMS"],showAlways:!1,title:"通知短信"},component:ht,children:[{path:"/noticeSMS",name:"notificationIndex",meta:{icon:"ios-mail",access:["notificationIndex"],title:"通知短信"},component:function(){return n.e("chunk-30f43d24").then(n.bind(null,"38f9"))}},{path:"/areaWelcomeSMSIndex",name:"areaWelcomeSMSIndex",meta:{icon:"ios-mail",access:["areaWelcomeSMSIndex"],title:"地区欢迎短信"},component:function(){return n.e("chunk-35748faf").then(n.bind(null,"b073"))}},{path:"/areaWelcomeSMSDetails",name:"areaWelcomeInfo",meta:{icon:"md-list",access:["marketingInfo"],title:"地区欢迎短信详情",hideInMenu:!0},component:function(){return n.e("chunk-617963b5").then(n.bind(null,"cba7"))}},{path:"/areaWelcomeEdit",name:"areaWelcomeEdit",meta:{icon:"ios-mail",access:["areaWelcomeEdit"],title:"地区欢迎短信编辑",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-f6211cec"),n.e("chunk-75b23b0c")]).then(n.bind(null,"a9af"))}},{path:"/areaWelcomeEditAdd",name:"areaWelcomeEditAdd",meta:{icon:"ios-mail",access:["areaWelcomeEditAdd"],title:"地区欢迎短信新增",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-f6211cec"),n.e("chunk-75b23b0c")]).then(n.bind(null,"a9af"))}}]},{path:"/customerSMSMngr",name:"customerSMS",meta:{icon:"ios-mail",access:["customerSMS"],showAlways:!1,title:"客服短信"},component:ht,children:[{path:"/customerSMS",name:"customerIndex",meta:{access:["customerIndex"],icon:"ios-mail",title:"客服短信"},component:function(){return n.e("chunk-313b4a34").then(n.bind(null,"198b"))}}]},{path:"/marketingSMSMngr",name:"marketingSMS",meta:{icon:"ios-mail",access:["marketingSMS"],showAlways:!1,title:"营销短信"},component:ht,children:[{path:"/marketingSMS",name:"marketingIndex",meta:{icon:"ios-mail",access:["marketingIndex"],title:"营销短信"},component:function(){return n.e("chunk-5fca294c").then(n.bind(null,"08c9"))}},{path:"/marketingSMSDetails",name:"marketingInfo",meta:{icon:"md-list",access:["marketingInfo"],title:"营销短信详情",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-bc1af0b8")]).then(n.bind(null,"c1b4"))}}]}]},{path:"/customer",name:"customerManage",meta:{access:["customerManage"],icon:"ios-people",title:"客户管理",showAlways:!0},component:it,children:[{path:"/channelMngr",name:"channelManage",meta:{access:["channelManage"],icon:"md-people",showAlways:!1,title:"渠道商"},component:ht,children:[{path:"/channelInfo",name:"channelIndex",meta:{icon:"md-people",access:["channelIndex"],title:"渠道商"},component:function(){return n.e("chunk-6822bcd8").then(n.bind(null,"3e33"))}},{path:"/billCheck",name:"billCheck",meta:{icon:"md-people",access:["billCheck"],title:"账单核对"},component:function(){return Promise.all([n.e("chunk-3545909f"),n.e("chunk-39eb3550")]).then(n.bind(null,"a919"))}},{path:"/packageGroup",name:"packageGroup",meta:{icon:"md-people",access:["packageGroup"],title:"套餐组"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-6120a01e")]).then(n.bind(null,"7a6c"))}},{path:"/zeroLevelChannel",name:"zeroLevelChannel",meta:{icon:"md-people",access:["zeroLevelChannel"],title:"零级渠道商"},component:function(){return n.e("chunk-15426580").then(n.bind(null,"f6e3"))}},{path:"/a2zBillPriceMngr",name:"a2zBillPriceMngr",meta:{icon:"md-people",access:["a2zBillPriceMngr"],title:"流量计费价格管理"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-30925022")]).then(n.bind(null,"070d"))}},{path:"/costPricingMngr",name:"costPricingMngr",meta:{icon:"md-people",access:["costPricingMngr"],title:"成本价格管理"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-11f2242b")]).then(n.bind(null,"e8ce"))}},{path:"/imsiFeeMngr",name:"imsiFeeMngr",meta:{icon:"md-people",access:["imsiFeeMngr"],title:"IMSI费管理"},component:function(){return n.e("chunk-3ac0a32f").then(n.bind(null,"dbe3"))}},{path:"/channelAdd",name:"channelAdd",meta:{icon:"md-list",access:["channelAdd"],title:"渠道商新增",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-e492816e")]).then(n.bind(null,"0cd5"))}},{path:"/channelDetails",name:"channelInfo",meta:{icon:"md-list",access:["channelInfo"],title:"渠道商详情",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-e492816e")]).then(n.bind(null,"0cd5"))}},{path:"/channelEdit",name:"channelUpdate",meta:{icon:"md-list",access:["channelUpdate"],title:"渠道商编辑",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-e492816e")]).then(n.bind(null,"0cd5"))}},{path:"/zeroChannelAdd",name:"zeroChannelAdd",meta:{icon:"md-list",access:["zeroChannelAdd"],title:"新建零级渠道商",hideInMenu:!0},component:function(){return n.e("chunk-24c32cac").then(n.bind(null,"4ccb"))}},{path:"/zeroChannelUpdate",name:"zeroChannelUpdate",meta:{icon:"md-list",access:["zeroChannelUpdate"],title:"修改零级渠道商",hideInMenu:!0},component:function(){return n.e("chunk-36d0a25f").then(n.bind(null,"7300"))}},{path:"/a2zBillPriceDetail",name:"a2zBillPriceDetail",meta:{icon:"md-list",access:["a2zBillPriceDetail"],title:"流量计费价格管理—规则详情",hideInMenu:!0},component:function(){return n.e("chunk-8b131d44").then(n.bind(null,"2abb"))}},{path:"/costPricingDetails",name:"costPricingDetails",meta:{icon:"md-list",access:["costPricingDetails"],title:"成本价格管理—详情",hideInMenu:!0},component:function(){return n.e("chunk-01175ac8").then(n.bind(null,"3183"))}},{path:"/adminMarketingAccount",name:"adminMarketingAccount",meta:{icon:"md-people",access:["adminMarketingAccount"],title:"营销活动详情",hideInMenu:!0},component:function(){return n.e("chunk-aae304a4").then(n.bind(null,"f3c4"))}}]},{path:"/cooperativeMngr",name:"cooperativeManage",meta:{icon:"md-people",showAlways:!1,access:["cooperativeManage"],title:"合作运营商"},component:ht,children:[{path:"/cooperative",name:"cooperativeIndex",meta:{icon:"md-people",access:["cooperativeIndex"],title:"合作运营商"},component:function(){return n.e("chunk-d0f84122").then(n.bind(null,"7c6d"))}},{path:"/cooperativeDetails",name:"cooperativeInfo",meta:{icon:"md-list",access:["cooperativeInfo"],title:"合作运营商详情",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-19278d2b")]).then(n.bind(null,"4d54"))}}]},{path:"/postPaidMngr",name:"postPaymentChannel",meta:{icon:"md-people",access:["postPaymentChannel"],showAlways:!1,title:"后付费渠道"},component:ht,children:[{path:"/postPaid",name:"paymentChannelIndex",meta:{icon:"md-people",access:["paymentChannelIndex"],title:"后付费渠道"},component:function(){return Promise.all([n.e("chunk-15317ec4"),n.e("chunk-12cd12ee")]).then(n.bind(null,"fce4"))}},{path:"/postPaidDetails",name:"paymentChannelInfo",meta:{icon:"md-list",access:["paymentChannelInfo"],title:"后付费渠道详情",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-3a07fecf")]).then(n.bind(null,"73d9"))}}]},{path:"/manufacturerMngr",name:"manufacturerManage",meta:{icon:"md-people",showAlways:!1,access:["manufacturerManage"],title:"终端厂商"},component:ht,children:[{path:"/manufacturer",name:"manufacturerIndex",meta:{icon:"md-people",access:["manufacturerIndex"],title:"终端厂商"},component:function(){return n.e("chunk-28e8e413").then(n.bind(null,"4b56"))}},{path:"/testImsi",name:"test_imsi",meta:{icon:"md-people",access:["test_imsi"],title:"测试IMSI管理"},component:function(){return n.e("chunk-0b1cf41e").then(n.bind(null,"b959"))}},{path:"/manufacturerUpdate",name:"manufacturer_update",meta:{icon:"md-list",access:["manufacturer_update"],title:"终端厂商编辑",hideInMenu:!0},component:function(){return n.e("chunk-6c5d0770").then(n.bind(null,"30e3"))}},{path:"/manufacturerDetails",name:"manufacturerInfo",meta:{icon:"md-list",access:["manufacturerInfo"],title:"终端厂商详情",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-1fdfb292")]).then(n.bind(null,"6e38"))}},{path:"/billDetails",name:"billInfo",meta:{icon:"md-list",access:["billInfo"],title:"账单详情",hideInMenu:!0},component:function(){return n.e("chunk-3da7f730").then(n.bind(null,"803e"))}},{path:"/flowDetails",name:"flowInfo",meta:{icon:"md-list",access:["flowInfo"],title:"流量明细",hideInMenu:!0},component:function(){return n.e("chunk-11a770b5").then(n.bind(null,"6105"))}}]},{path:"/channelPool",name:"channelPool_mngr",meta:{icon:"md-wifi",access:["channelPool_mngr"],title:"流量池管理"},component:function(){return n.e("chunk-8d04d5e0").then(n.bind(null,"9463"))}},{path:"/channelcardlist",name:"channelcardlist_mngr",meta:{icon:"md-list-box",access:["cardlist_mngr"],title:"卡号列表",hideInMenu:!0},component:function(){return n.e("chunk-4a02fcee").then(n.bind(null,"1c64"))}},{path:"/channelflowlist",name:"channelflowlist_mngr",meta:{icon:"md-list",access:["flowlist_mngr"],title:"流量池列表",hideInMenu:!0},component:function(){return n.e("chunk-f5868382").then(n.bind(null,"9ff0"))}},{path:"/channeliccidlist",name:"channeliccidlist_mngr",meta:{icon:"md-list",hideInMenu:!0,access:["iccidlist_mngr"],title:"ICCID列表"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-028d8a59")]).then(n.bind(null,"a2c1"))}},{path:"/channeluserecord",name:"channeluserecord_mngr",meta:{icon:"ios-create-outline",access:["userecord_mngr"],title:"使用记录",hideInMenu:!0},component:function(){return n.e("chunk-73112bd2").then(n.bind(null,"472b"))}},{path:"/resourceCooperation",name:"resourceCooperation",meta:{icon:"md-infinite",access:["resourceCooperation"],title:"资源管理"},component:function(){return Promise.all([n.e("chunk-2d0aa5b8"),n.e("chunk-347e71a8"),n.e("chunk-2d0baaeb")]).then(n.bind(null,"37f7"))}},{path:"/viewResources",name:"viewResources",meta:{hideInMenu:!0,access:["viewResources"],title:"查看资源"},component:function(){return Promise.all([n.e("chunk-2d0aa5b8"),n.e("chunk-d7c3d1f6"),n.e("chunk-2d0d660d")]).then(n.bind(null,"71fc"))}},{path:"/callOrderDetails",name:"callOrderDetails",meta:{hideInMenu:!0,access:["callOrderDetails"],title:"话单明细"},component:function(){return Promise.all([n.e("chunk-2d0aa5b8"),n.e("chunk-85384882"),n.e("chunk-2d0e48bc")]).then(n.bind(null,"914b"))}},{path:"/billingStatistics",name:"billingStatistics",meta:{hideInMenu:!0,access:["billingStatistics"],title:"账单统计"},component:function(){return Promise.all([n.e("chunk-2d0aa5b8"),n.e("chunk-068f3754"),n.e("chunk-2d22dd29")]).then(n.bind(null,"f8ab"))}}]},{path:"/service",name:"service_brace",meta:{title:"客服支撑管理",showAlways:!0,icon:"logo-octocat",access:["service_brace"]},component:it,children:[{path:"/serviceIndex",name:"service_index",meta:{icon:"md-card",access:["service_index"],title:"卡片信息"},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-f6211cec"),n.e("chunk-18603135"),n.e("chunk-de7ad25e"),n.e("chunk-603786c0")]).then(n.bind(null,"a699"))}},{path:"/faultHandling",name:"fault_mngr",meta:{icon:"md-alert",title:"故障处理",access:["fault_mngr"]},component:function(){return n.e("chunk-69ac30c6").then(n.bind(null,"cad9"))}},{path:"/faultHandlingAdd",name:"fault_add",meta:{icon:"md-add",title:"新建故障处理",hideInMenu:!0,access:["fault_add"]},component:function(){return n.e("chunk-22e8860b").then(n.bind(null,"a70c"))}},{path:"/locationPackage",name:"location_package",meta:{icon:"md-pin",hideInMenu:!0,access:["location_package"],title:"当前位置套餐"},component:function(){return n.e("chunk-0ca3207b").then(n.bind(null,"71a7"))}},{path:"/purchasedPackage",name:"purchased_package",meta:{icon:"md-cart",hideInMenu:!0,access:["purchased_package"],title:"已购买套餐"},component:function(){return n.e("chunk-23d3b750").then(n.bind(null,"2274"))}},{path:"/GTPAddressConfig",name:"GTPAddressConfig",meta:{icon:"md-build",title:"GTP话单地址配置",access:["GTPAddressConfig_mngr"]},component:function(){return n.e("chunk-3bfafdc2").then(n.bind(null,"507b"))}}]},{path:"/finance",name:"finance_mngr",meta:{icon:"logo-yen",title:"财务系统管理",access:["finance_mngr"]},component:it,children:[{path:"/billing",name:"billing_mngr",meta:{icon:"md-print",title:"出账管理",access:["billing_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-2d0aa5b8"),n.e("chunk-3545909f"),n.e("chunk-48fb23e7")]).then(n.bind(null,"e28c"))}},{path:"/history",name:"history_mngr",meta:{icon:"md-print",hideInMenu:!0,title:"历史真实账单"},component:function(){return n.e("chunk-b5c613fc").then(n.bind(null,"6e49"))}},{path:"/acountingPeriod",name:"acounting_period",meta:{icon:"md-menu",title:"账期管理",access:["acounting_period"]},component:function(){return n.e("chunk-ea20651c").then(n.bind(null,"7327"))}},{path:"/serviceRechargeApproval",name:"serviceRecharge_approval",meta:{icon:"md-menu",title:"自服务充值审批",access:["serviceRecharge_approval"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-0fab8be6")]).then(n.bind(null,"2857"))}},{path:"/billingAdjust",name:"billing_adjust",meta:{icon:"md-menu",title:"账单调整",access:["billing_adjust"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-a1c5b23e")]).then(n.bind(null,"f8b6"))}},{path:"/addBillingAdjust",name:"addBilling_adjust",meta:{icon:"md-menu",title:"新增调账",hideInMenu:!0,access:["addBilling_adjust"]},component:function(){return n.e("chunk-7e9b6ebc").then(n.bind(null,"1c97"))}}]},{path:"/realname",name:"realname_mngr",meta:{icon:"ios-create",title:"实名制管理",access:["realname_mngr"]},component:it,children:[{path:"/rule",name:"rule_mngr",meta:{icon:"md-settings",title:"规则管理",access:["rule_mngr"]},component:function(){return n.e("chunk-450656cc").then(n.bind(null,"47c2"))}},{path:"/ruleDetail",name:"ruleDetail_mngr",meta:{icon:"md-print",hideInMenu:!0,title:"规则详情",access:["rule_mngr"]},component:function(){return n.e("chunk-56a3dd1d").then(n.bind(null,"b54b"))}},{path:"/certification",name:"certification_mngr",meta:{icon:"md-contacts",title:"人工认证",access:["certification_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-2329612d")]).then(n.bind(null,"4915"))}},{path:"/certificationinfo",name:"certificationinfo_mngr",meta:{icon:"ios-chatbubbles",title:"认证信息",access:["certificationinfo_mngr"]},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-399f9916")]).then(n.bind(null,"93bd"))}}]},{path:"/marketingActivity",name:"marketing_mngr",meta:{icon:"ios-pie",title:"营销管理",access:["marketing_mngr"]},component:it,children:[{path:"/marketingActivityIndex",name:"marketingActivityIndex",meta:{icon:"md-list",title:"营销活动管理",access:["marketingActivityIndex"]},component:function(){return n.e("chunk-48264968").then(n.bind(null,"ceeb"))}},{path:"/marketingActivityUpdate",name:"marketingActivityUpdate",meta:{icon:"md-list",title:"修改营销活动",hideInMenu:!0,access:["marketingActivityUpdate"]},component:function(){return n.e("chunk-3147487a").then(n.bind(null,"44cf"))}},{path:"/marketingActivityAdd",name:"marketingActivityAdd",meta:{icon:"md-list",title:"新增营销活动",hideInMenu:!0,access:["marketingActivityAdd"]},component:function(){return n.e("chunk-3147487a").then(n.bind(null,"44cf"))}}]},{path:"/operationAgencyApproval",name:"operationAgencyApproval",meta:{title:"代办审批详情页",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-98ffbe16")]).then(n.bind(null,"1f1b"))}},{path:"/proxyReviewed",name:"proxyReviewed",meta:{title:"代办审批详情页",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-2d0aa5b8"),n.e("chunk-1cce1846")]).then(n.bind(null,"aaac"))}},{path:"/costToDo",name:"costToDo",meta:{title:"成本报表待办审批页",hideInMenu:!0},component:function(){return n.e("chunk-cb7c82b8").then(n.bind(null,"28e1"))}},{path:"/aprvDetails",name:"aprvDetails",meta:{title:"代办审批详情页",hideInMenu:!0},component:function(){return Promise.all([n.e("chunk-522d6fa4"),n.e("chunk-3dd62379")]).then(n.bind(null,"a3a1"))}},{path:"/401",name:"error_401",meta:{hideInMenu:!0},component:function(){return n.e("chunk-14b9857b").then(n.bind(null,"f94f"))}},{path:"/500",name:"error_500",meta:{hideInMenu:!0},component:function(){return n.e("chunk-2c359864").then(n.bind(null,"88b2"))}},{path:"*",name:"error_404",meta:{hideInMenu:!0},component:function(){return n.e("chunk-3385141a").then(n.bind(null,"35f5"))}}]},e3e1:function(e,t,n){"use strict";n.r(t),t["default"]={install:function(e){e.prototype.$getRedirectUrl=function(e){return this.$store.dispatch("redirect/fetchRedirectUrl",e)},e.prototype.$redirectUrl=function(){return this.$store.state.redirect.url}}}},ebb7:function(e,t,n){"use strict";n("7786")},ed00:function(e,t,n){"use strict";n("1ce5")},f121:function(e,t,n){"use strict";t["a"]={title:"CMI",cookieExpires:1,useI18n:!0,baseUrl:{dev:"/cmiweb",pro:"/api/"},homeName:"home",plugin:{"error-store":{showInHeader:!0,developmentOff:!0}},customerSMSType:{code:"{code}",iccid:"{iccid}"},keytype:"ClearKey"}},f93a:function(e,t,n){},f95d:function(e,t,n){},f997:function(e,t,n){"use strict";n("7141")},fe07:function(e,t,n){"use strict";var a,c,o,i,r,s=n("2b0e"),l=n("a925"),u=(n("c276"),n("ade3")),d=(o={home:"首页",login:"登录",spinLoading:"加载页面",account_list:"账号管理",system_mngr:"系统管理",log_mngr:"操作日志",Announcement_mngr:"公告管理",lockModeApproval_mngr:"金锁模式管理",login_mngr:"登录日志",pwd_mngr:"账户密码管理",pri_mngr:"角色管理",sys:{wholesalerName:"请输入账号...",roleType:"请选择角色...",add:"新增账户",account:"用户名：",paccount:"请输入用户名...",oldPwd:"原密码：",poldPwd:"请输入原始密码...",newPwd:"新密码：",pnewPwd:"请输入新密码...",rePwd:"确认密码：",prePwd:"请确认密码...",pwdNullMsg:"密码不能为空",pwdLengthMsh:"长度不能小于6位",accountNummMsg:"账户名不能为空",logInto:"登入",logOut:"登出",optAccoount:"登录账号",optType:"登录类型",serviceUrl:"IP地址",optTime:"登录时间",accountT:"账号",role:"用户角色",status:"用户状态",opt:"操作",accountempty:"账户名不允许为空",newpassword:"输入新密码",oldpassword:"输入原密码",Enteraccount:"输入账户",successfully:"用户基本信息修改成功，请重新登录。",wrong:"出错啦",Serverwrong:"服务器内部错误",smsCode:"请输入6位数字验证码",sendSmsCode:"验证码下发成功",enterEmail:"输入邮箱：",inputEmail:"输入邮箱...",setPassword:"设置密码：",enterPassword:"输入密码...",phone:"手机号码",enterPhone:"输入手机号码...",pwNotMatch:"两次输入的密码不一致，请仔细核对！",inputName:"请先输入用户名!",codeSendPhone:"验证码已发送至你的手机/邮箱，请注意查收！",second:"秒",inputTwoPw:"两次密码输入不一致",sendVercode:"下发验证码",persona:"角色",exitModifyPassword:"退出修改密码",userNameEmpty:"用户名不能为空",pleaseSetRoles:"请设置用户角色",phoneNoEmpty:"手机号不能为空",onlyNumbers:"只支持数字",userNameSpaces:"用户名有空格",userNameAmpersand:"用户名不能包含&符号",twentyDigits:"最长支持20位",passwordsNotMatch:"两次输入的密码不一致，请重新输入",successAddedUser:"新增用户成功!",userInforupdated:"用户信息已完成更新",successDeleted:"已成功删除用户",operationWill:"当前操作将",user:"用户",continueExecution:"，是否继续执行？",userStatusUpdated:"用户状态已更新！",confirmDeleteUser:"请确定删除已选中用户",obtainPermissionData:"正在获取权限数据，请稍候...",Saving:"正在保存，请稍候...",roleName:"角色名称",newRole:"新增角色",enterName:"输入角色名称...",viewPermissions:"查看权限",rolePermissionManagement:"角色权限管理",editAccount:"编辑",characterSpaces:"角色名称有空格",purview:"权限",successAddedRole:"角色新增成功",failedObtainRole:"获取角色权限失败，请稍后再试!",rolePpermissionsUpdated:"角色权限已更新"},common:{welcome:"欢迎你：",edit:"修改",frozen:"冻结",thaw:"解冻",del:"删除",search:"搜索",reSet:"重置",timeSection:"选择时间段",beginTime:"开始时间",endTime:"结束时间",optResult:"结果",optType:"请选择登录类型",success:"成功",failure:"失败",logOut:"退出登录",rePwd:"修改密码",determine:"确定",cancel:"取消",Error:"错误提示",token:"token校验失败，请重新登录",Request:"请求错误",errorcode:"请求接口异常，错误码[404]",timedout:"请求超时",feature:"该功能暂不可用，请稍候再试",Servererror:"服务器错误，请联系管理员",Requestout:"请求超时,请稍候再试",Successful:"操作成功",Closeall:"关闭所有",Closeother:"关闭其他",Fullscreen:"全屏",Exitscreen:"退出全屏",manageOperate:"操作",pleaseChoose:"请选择",PhysicalSIM:"CMLINK卡",cardType:"主卡形态",quantity:"卡片数量",simplifiedChinese:"简体中文",traditionalChinese:"繁体中文",english:"英文",tips:"提示"},cdr_mngr:"CDR话单管理",call_query:"话单查询",country_operators:"按国家/运营商查询",search_number:"按号码查询",flowtotal:"流量总量",search_no:"按号段查询",search_enterprise:"按企业查询",search_setMeal:"按套餐查询",price_rules:"批价规则管理",company_price:"企业/运营商账单统计",callListinfo:"话单详情",download_mngr:"下载管理",taskListinfo:"下载列表",Period:"选择时间段",TaskID:"请输入任务ID",FileName:"请输入文件名称",Tasks:"任务ID",Description:"任务描述",TasksStatus:"任务状态",File:"文件名称",CreationTime:"任务创建时间",FinishedTime:"任务结束时间",Operation:"文件下载",DownloadFlie:"点击下载",FailedFlie:"下载失败",Notexist:"文件不存在",exportMS:"导出提示",exportID:"你本次导出任务ID为:",exportFlie:"你本次导出的文件名为:",downloadResult:"请前往下载管理-下载列表查看及下载",Goto:"立即前往",product_operation:"产品运营",package_config:"套餐配置",individual_config:"单独配置",batch_config:"批量配置",inventory_mngr:"库存管理",task_query:"任务查询",porder_mngr:"个人订单管理",package_delay:"套餐延期",package_search:"未激活套餐查询",pakg_delay:"未激活套餐延期",delayTask_audit:"延期任务审核",activation_record:"激活记录查询",ordinary_package:"全球卡普通套餐",offline_mode:"终端厂商线下模式",online_mode:"终端厂商线上模式",cooperative_model:"合作运营商模式",SupplierOrder_management:"渠道商订单管理",ChannelCardOrders_management:"渠道商白卡订单管理",activateStat_search:"激活统计",activate_stat:"销量统计查询",availability_stat:"可用套餐统计",statEnd_month:"未激活套餐统计",packageManage:"套餐管理",packageIndex:"套餐管理",packageBatch:"批量编辑",packageAdd:"套餐添加",packageUpdate:"套餐编辑",packageInfo:"套餐详情",cardPoolDetail:"套餐卡池详情",packageCopy:"套餐复制",trafficPool_mngr:"流量池管理",addPool_mngr:"新建流量池",editPool_mngr:"修改流量池",copyPool_mngr:"复制流量池",detailsPool_mngr:"流量池详情",channelPool_mngr:"流量池管理",resourceCooperation:"资源管理",fuelPack_mngr:"加油包管理",upccTemplate_mngr:"UPCC速度模板管理",channelProvider_mngr:"渠道商套餐管理",targetedApplication_mngr:"定向应用管理",whiteListPackage_mngr:"白名单套餐管理",viewResources:"查看资源",callOrderDetails:"话单明细",billingStatistics:"账单统计",resource:"码号资源管理",msisdn:"MSISDN管理",iccid:"ICCID管理",imsi:"IMSI管理",supplyImsi:"供应商IMSI管理",makeCardFile_mngr:"制卡文件管理",addCard_mngr:"新建制卡任务",otaData_mngr:"OTA数据管理",smsManage:"短信管理",notificationSMS:"通知短信管理",notificationIndex:"通知短信",notificationAdd:"通知短信新增",notificationUpdate:"通知短信编辑",notificationInfo:"通知短信详情",areaWelcomeSMSIndex:"地区欢迎短信管理",areaWelcomeInfo:"地区欢迎短信详情",areaWelcomeEdit:"地区欢迎短信编辑",areaWelcomeEditAdd:"地区欢迎短信新增",customerSMS:"客服短信管理",customerIndex:"客服短信",marketingSMS:"营销短信管理",marketingIndex:"营销短信",marketingInfo:"营销短信详情",customerManage:"客户管理",channelManage:"渠道商管理",channelIndex:"渠道商",billCheck:"账单核对",channelAdd:"渠道商新增",channelInfo:"渠道商详情",channelUpdate:"渠道商编辑",packageGroup:"套餐组",zeroLevelChannel:"零级渠道商",a2zBillPriceMngr:"流量计费价格管理",costPricingMngr:"成本价格管理",imsiFeeMngr:"IMSI费管理",zeroChannelAdd:"新建零级渠道商",zeroChannelUpdate:"修改零级渠道商",a2zBillPriceDetail:"流量计费价格管理—规则详情",costPricingDetails:"成本价格管理—详情",cooperativeManage:"合作运营商管理",cooperativeIndex:"合作运营商",cooperativeInfo:"合作运营商详情",postPaymentChannel:"后付费渠道管理",paymentChannelIndex:"后付费渠道",paymentChannelInfo:"后付费渠道详情",manufacturerManage:"终端厂商管理",manufacturerIndex:"终端厂商",manufacturerInfo:"终端厂商详情",manufacturer_update:"终端厂商编辑",billInfo:"账单详情",flowInfo:"流量明细",test_imsi:"测试IMSI管理",channel_mngr:"渠道自服务",deposit_mngr:"账户管理",mealList_mngr:"套餐详情",streamList_mngr:"流水详情",offlinePayment:"充值页面",marketingAccount:"营销活动详情",adminMarketingAccount:"营销活动详情",stock_mngr:"库存管理",cardList_mngr:"卡片详情",buymeal_mngr:"套餐购买",order_mngr:"订单管理",bill_mngr:"月账单",support_mngr:"服务与支持",address_mngr:"地址管理",detailsList_mngr:"服务与支持套餐详情",useList_mngr:"使用详情",operators_mngr:"运营商管理",ResourceSupplier_mngr:"资源供应商管理",operatorsindex_mngr:"运营商管理",report_mngr:"报表功能",exchange_mngr:"汇率管理",cardsell_mngr:"卡销售报表",cardactive_mngr:"卡激活报表",terminal_after_pay:"后付费结算报表",subscription_mngr:"套餐订购报表",activation_mngr:"套餐激活报表",reuse_mngr:"重复使用查询",analysis_mngr:"套餐分析报表",terminal_mngr:"终端结算报表",channelReport_mngr:"运营商结算报表",income_mngr:"线下收入报表",costReport_mngr:"成本报表",esimDownloadReport_mngr:"ESIM Download报表",flowPool:"流量池管理",poolList:"流量池详情",showiccid_mngr:"ICCID列表",flowpool_mngr:"流量池",cardlist_mngr:"卡号列表",flowlist_mngr:"流量池列表",userecord_mngr:"使用记录",iccidlist_mngr:"ICCID列表",channelcardlist_mngr:"卡号列表",channelflowlist_mngr:"流量池列表",channeluserecord_mngr:"使用记录",channeliccidlist_mngr:"ICCID列表",channelfuelPack_mngr:"加油包管理",channelpackage:"套餐管理",fuelPackManagement:"加油包管理",aqCode_mngr:"ESIM二维码管理",subChannelProvider_mngr:"子渠道商管理",whiteCardOrders_management:"白卡订单管理",channelResourceCooperation:"资源管理",channelViewResources:"查看资源",channelCallOrderDetails:"话单明细",channelBillingStatistics:"账单统计",channelBillingQuery:"渠道商账单查询",paymentOrderManagement:"支付记录查询",product:"产品管理",makeCard:"制卡管理",masterCard:"主卡管理",cardPool:"卡池管理",vimsi:"VIMSI管理",associationGroup:"国家卡池关联组",addCardPool:"新增关联组",updateCardPool:"编辑关联组",copyCardPool:"复制关联组",cardPooldetails:"卡池详情",specialCountryRule:"特殊国家规则管理",specialCountryRuleAdd:"新增特殊国家规则",specialCountryRuleEdit:"编辑特殊国家规则",service_brace:"客服支撑",service_index:"卡片信息",local_search:"位置信息",purchased_package:"已购买套餐",location_package:"当前位置套餐",sppurchased_package:"已购买套餐",splocation_package:"当前位置套餐",fault_mngr:"故障处理",fault_add:"新建故障处理",GTPAddressConfig:"GTP话单地址配置",finance_mngr:"财务系统管理",billing_mngr:"出账管理",history_mngr:"历史真实账单",acounting_period:"账期管理",serviceRecharge_approval:"自服务充值审批"},Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(o,"finance_mngr","财务系统管理"),"billing_mngr","出账管理"),"history_mngr","历史真实账单"),"acounting_period","账期管理"),"serviceRecharge_approval","自服务充值审批"),"billing_adjust","账单调整"),"addBilling_adjust","新增调账"),"aprv_details","审批详情"),"channelSellindex_mngr","首页"),"channelSellHistory_mngr","历史账单"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(o,"channelSell_mngr","渠道商销售数据"),"topUp_records","充值记录"),"realname_mngr","实名制管理"),"rule_mngr","规则管理"),"ruleDetail_mngr","规则详情"),"certification_mngr","人工认证"),"certificationinfo_mngr","认证信息"),"marketing_mngr","营销管理"),"marketingActivityIndex","营销管理"),"marketingActivityUpdate","修改营销活动"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(o,"marketingActivityAdd","新增营销活动"),"deposit",{deposit_money:"可用额度",mealList:"套餐详情",streamList:"流水详情",mealname:"套餐名称",search:"搜索",charge_time:"时间",canmeal:"可订购套餐",flow:"流水",mealId:"套餐id",mealprice:"套餐价格",charge_price:"充值金额",currency:"币种",chosetime:"请选择充值时间",ProductList:"可购买套餐导出",charge_type:"类型",Amount:"交易金额",accountdeposit:"账户金额",startTime:"请选择开始时间",endTime:"请选择结束时间",a2zDepositLimit:"A2Z预存款额度",channelMode:"渠道商模式",depositAccount:"押金账户",preDepositAccount:"预存账户",marketingAccount:"营销账户",creditAccount:"信用账户",marketingAccountFlow:"营销账户流水",marketingActivities:"营销活动",eventStartTime:"活动开始时间",eventEndTime:"活动结束时间",campaignStatus:"活动状态",rebateAmount:"返利金额",usedAmount:"已用金额",rebateBalance:"返利余额",arrivalTime:"到账时间",expirationTime:"到期时间",spendTime:"消费时间",effectiveTime:"生效时间",connectedActivities:"关联活动",consumptionAmount:"消费金额",accountTotalBalance:"营销账户总余额",settlementDate:"结算日期",pricingDate:"批价日期",singleActivityBalance:"单笔营销活动余额",marketingActivityDetails:"营销活动返利流水详情",marketingAccountDetails:"营销账户详情",totalOrderId:"总单ID",toBeStarted:"待开始",started:"已开始",ended:"已结束",obsolete:"已作废",earlyTerminate:"提前结束",increaseMarketinRebates:"增加营销返利款",distributionPackageOrdering:"代销套餐订购",startEndDate:"请选择起止日期！",totalOrderNumber:"订单编号",SubOrderNumber:"订单编号（子单）",ResetMarketingBudget:"营销额过期清零",dataUsageSettlemtn:"流量结算",OffsetMarketingRebateAmount:"扣减营销返利款"}),"stock",{order_number:"任务名称",input_number:"请输入任务名称",timeslot:"时间段",chose_time:"请选择时间段",search:"搜索",details:"详情",Card_status:"卡状态",chose_status:"请选择卡状态",exporttb:"导出",card_number:"卡片数量",addtime:"创建时间",action:"操作",usedstate:"卡使用状态",cardtype:"卡类别",Code:"验证码:",PhysicalSIM:"普通卡（实体卡)",eSIM:"Esim卡",TSIM:"贴片卡",showiccid:"ICCID列表",Storagetime:"入库时间",attributableChannel:"归属渠道商",transfer:"划拨",subChannel:"子渠道商",subChannelProviderEmpty:"子渠道商不能为空",availablePackages:"是否有可用套餐",whitelistPackage:"白名单套餐",WhitelistPackageID:"白名单套餐ID"}),"buymeal",{manual_batch:"手动输入",file_batch:"文件批量",chose_meal:"已选套餐",chose_mealtext:"请选择套餐",chose_number:"请输入卡号",input_number:"输入卡号",confirm:"确定",Reset:"重置",HK_dollar:"港元",payment:"共需支付",mealname:"套餐名称",search:"搜索",input_mealname:"请输入套餐名称",Selectall:"设置全选",Deselectall:"取消全选",upload:"点击或拖拽文件上传",Country:"国家/地区",amount:"套餐金额/元",selectCountry:"请选择国家/地区",Download:"下载模板文件",Downloadmsg:"文件仅支持csv格式文件,大小不能超过5MB",Choosemeal:"请选择套餐!",cycletype:"套餐周期类型",cycleNumber:"套餐周期数",filename:"文件名",time:"批量购买时间",chooseprice:"请选择套餐,获取折扣价!",choose:"选择",hour:"24小时",day:"自然日",month:"自然月",year:"自然年",cday:"每日",cmonth:"每月",cyear:"每年",Failedfile:"失败文件",clickdownload:"点击下载",tasksTotal:"任务总条数",successes:"成功条数",failed:"失败条数",Nofailed:"无失败文件",taskview:"历史任务查看",Taskstatus:"任务状态",Processing:"处理中",completed:"已完成",templatename:"套餐购买批量文件模板",Insufficient:"押金不足，请充值后再购买",purchase:"购买成功",toupload:"请选择需要上传的文件!",fileformat:"文件格式不正确",incorrect:"格式不正确，请上传.csv格式文件",Filesize:"文件大小超过限制",Exceeds:"超过了最大限制范围5MB",fileresult:"套餐购买批量文件_失败",Uploadfile:"确定"}),"order",{mealname:"套餐名称",input_mealname:"请输入套餐名称",chose_number:"请输入卡号",input_number:"卡号",timeslot:"时间段",chose_time:"请选择时间段",search:"搜索",monthly_bill:"查看月账单",exporttb:"导出",month:"月份",chose_month:"请选择月份",expenditure:"总支出",order_number:"订单号",order_state:"订单状态",count:"数量",order_money:"订单金额",addtime:"创建时间",isused:"套餐是否已使用",action:"操作",unsubscribe:"退订",ifunsubscribe:"确认退订该项",channels:"购买渠道",Website:"官网（H5)",BulkOrder:"批量售卖",Trial:"推广活动",Testing:"测试渠道",Datapool:"流量池web",ActivationTime:"使用时间",LocationUpdate:"最新位置",BeijingMobile:"北京移动",issuance:"合作发卡",Postpaid:"后付费发卡",Normal:"正常",Suspend:"注销",Expired:"过期",Terminated:"暂停",WeChat:"微信公众号",yes:"是",no:"否",delivered:"待发货",Completed:"完成",Cancelled:"已退订",approval:"激活退订待审批",Recycled:"已回收",Numberform:"号码登记表格.txt",Unsubscribe:"退订成功",channelOrderMoney:"渠道商订单金额"}),"support",(a={cardtype:"主卡形态",chose_type:"请选择卡类型",cardstate:"卡片状态",chose_state:"请选择卡状态",pause:"暂停",input:"请输入",search:"搜索",mealList:"套餐详情",mealname:"套餐名称",input_mealname:"请输入套餐名称",timeslot:"时间段",chose_time:"请选择时间段",used_details:"使用详情",activation:"激活",frozen:"冻结",action:"操作",time:"结束时间",Verification_Code:"发送验证码",Activation:"H卡激活方式",isused:"是否已使用",meal_time:"套餐失效时间",cmeal_time:"套餐过期时间",Activation_state:"激活状态",used_flow:"使用流量",used_cycle:"使用周期",Report_time:"最新上报时间",Report_address:"上报位置",Targeting:"定位方式",template:"短信模板",position:"当前位置",Locationrecord:"位置更新记录",SendSMS:"下发客服短信",Flowdetails:"流量详情",Packagestatus:"套餐状态",Activationtype:"激活类型",Ordernumber:"订单编号",Ordertime:"订单时间",Orderchannel:"订单渠道",Activationmethod:"激活方式",sendingmethod:"发送方式",chosesend:"请选择发送方式",phone:"手机号码",phoneprompt:"请输入手机号",chosetemplate:"请选择短信模板",sending:"发送短信",usedetails:"流量使用详情",date:"日期",useflow:"使用流量(G)",close:"关闭",Periodtype:"周期类型",Continuouscycle:"持续周期",Activatepackage:"激活套餐",MSISDNenter:"请输入MSISDN",ICCIDenter:"请输入ICCID",IMSIenter:"请输入IMSI",Recycle:"提前回收",ReplaceVIMSI:"更换VIMSI",Automatic:"自动激活",Manual:"手动激活",Sendingempty:"发送方式不能为空",SMSempty:"短信模板不能为空",Phoneempty:"手机号码不能为空",PhoneWrong:"手机号码格式错误",Unuse:"待激活",InUse:"使用中",Expired:"已过期",Activatedpending:"已激活待计费",Activating:"激活中",activated:"已激活",Used:"已使用",CNY:"人民币",USD:"美元",HKD:"港币",VIMSILocation:"VIMSI位置更新详情",Cardtype:"卡片类型",Flowg:"流量(G)",VIMSIphone:"IMSI号码",TimeLocation:"位置上报时间",Location:"位置上报地点",Termination:"确认提前回收?",replacement:"确认更换VIMSI?",VIMSIdetails:"VIMSI分配详情",IMSIdetails:"IMSI上网详情",targetedAppDetails:"定向应用上网详情",cardtraffic:"查询V卡流量",QueryH:"查询H卡流量",cardflow:"H卡/V卡流量",packageflow:"套餐总流量(MB)：",remainingflow:"当前剩余流量(MB)：",Usedtraffic:"已使用流量(MB)：",Usedflow:"已使用流量",countryregion:"使用国家/地区",flowquery:"套餐流量查询",back:"返回",Vcard:"V卡",Hcard:"H卡",searchcondition:"请至少填写一项搜索条件",obtained:"因未获取到当前卡位置,列表加载失败",advance:"套餐已过期,不能提前回收",Sentsuccessfully:"发送成功",SIMDate:"过期时间",Locationexport:"位置更新记录导出",recordexport:"流量详情记录导出",operationFailed:"操作失败,仅支持使用中状态套餐",VoperationFailed:"因未获取到当前卡位置,无法更换VIMSI",usageTotal:"流量总量",operatorName:"运营商名称",Complete:"实名认证状态",registration:"实名认证详情",IDnumber:"证件ID:",IDtype:"证件类型:",Issuingcountry:"护照国家:",approval:"待认证",process:"认证中",Approved:"认证通过",Rejected:"认证失败",IDexpired:"证件过期",registrationrules:"实名制认证规则",registrationcountry:"规则覆盖国家",Passport:"护照",Permit:"港澳通行证",HKIdentityCard:"香港身份证",MacauIdentityCard:"澳门身份证",view:"点击查看",DataUsedDay:"当日已用流量",DataRestrictionType:"流量限制类型",DataRestrictionCycle:"周期内限量",DataRestrictionSingle:"按周期类型重置",ControlLogicLimit:"达量后控制逻辑",RestrictedSpeedLimit:"达量限速",ReleaseAfterLimit:"达量释放",InternetStatus:"上网状态",Normal:"正常",RestrictedSpeed:"限速",DataCap:"流量上限",NameChinese:"姓名(中文):",NameEnglish:"姓名(英文):",hightDataCap:"高速流量上限",payBills:"缴付账单",remunerationReturn:"酬金返还",increaseDeposit:"增加押金",PreDeposit:"增加预存款",packageOrder:"套餐订购",fuelPackpackageOrder:"加油包订购",packageCancellation:"套餐退订",fuelPackUnsubscribe:"加油包退订"},Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"hightDataCap","套餐高速流量上限"),"UsedAddonPack","加油包已用流量"),"ToppedAddonPack","加油包充值流量"),"WaitingUser","待用户提交认证信息"),"AuthFailed","【认证失败】，待用户重新提交认证信息"),"NotCertified","未认证"),"flowpoolApi","流量池API"),"NoCertification","无需认证"),"querycontent","操作失败,查询内容不存在"),"imsiType","IMSI类型"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"useCountry","使用国家/地区"),"imsiResource","IMSI资源供应商"),"activeTime","激活时间"),"latestActivationDate","最晚激活时间"),"Refunded","已退款"),"Available","可选运营商"),"channelIncomeAdjustment","渠道商收入调账"),"marketingRebate","营销返利"),"imsiFeeStatistics","imsi费统计"),"indemnity","赔付"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"errorDesc","认证失败原因:"),"nameIsInconsistent","姓名校验不一致"),"certificateHasExpired","证件已过期"),"IDIsInconsistent","证件ID校验不一致"),"sixteenyYearsOld","未满16周岁"),"picturesAreNotSatisfied","上传图片不满足"),"EsimDetails","ESIM信息查询"),"smDpAddress","SM-DP+地址"),"activationCode","激活码"),"esimStatus","ESIM-状态"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"eid","EID"),"installationTime","安装时间"),"installationEquipment","安装设备"),"instalAmount","安装次数"),"updateTime","更新时间"),"generateQRCode","生成二维码"),"msisdnImsiIccid","请选择msisdn、imsi、iccid其中一项！"),"timeFrame","时间范围"),"orderBatch","订单批次"),"inputOrderBatch","请输入订单批次"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"segmentSearch","号段搜索"),"startNumber","起始号码"),"endNumber","结束号码"),"codeExport","二维码导出"),"fileExport","文件导出"),"xiazai","下载"),"operate","操作"),"inputPackageID","请输入套餐ID"),"edit2","编辑"),"copy","复制"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"approvalStatus","审批状态"),"newApproval","新建待审核"),"approve","通过"),"notApprove","不通过"),"modificationApproval","修改待审批"),"deleteApproval","删除待审批"),"fiveCharacters","最多支持500字符"),"fourtCharacters","最多可支持4000字符"),"packageDescription","套餐描述"),"selectType","请选择周期类型"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"selectDuration","请选择持续周期"),"packageValidity","套餐购买有效期"),"inputPackageValidity","请输入套餐购买有效期"),"selectDataResetType","请选择流量限制类型"),"selectRestrictionLogic","请选择控制逻辑"),"aupportHotspot","是否支持热点"),"isAupportHotspot","请选择是否支持热点"),"usage2","用量值"),"selectUsage","请选择用量值"),"inputUsage","请输入用量值"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"wrongUsageFormat","请输入1-10位整数数字"),"speedTemplate","速度模板"),"selectSpeedTemplate","请选择速度模板"),"unlimitedUsageTemplate","无上限模板"),"selectUnlimitedUsageTemplate","请选择无上限模板"),"packageNameNotNull","套餐名称不能为空"),"packageDescriptionNotNull","套餐描述不能为空"),"prongDurationFormat","持续周期格式错误"),"DurationValueLarge","持续周期数值过大"),"wrongPackageValidity","购买有效期(天)格式错误"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"packageValidityLarge","购买有效期(天)数值过大"),"qrPicture","二维码图片"),"pakageId","套餐ID"),"day","天"),"add","添加"),"create","新增"),"createPackage","新增套餐"),"operator","运营商"),"network","网络类型"),"subChannelName","子渠道商名称"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"contactEmail","联系人邮箱"),"purchasePackage","可购买套餐"),"profitMargin","利润率"),"totalAmount","总额度"),"accountPermissions","账号权限"),"channelAppSecret","渠道商App Secret"),"channelAppKey","渠道商App Key"),"selectCharacter","选择角色"),"submit","提交"),"clickToUpload","点击上传"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"downloadfile","下载模版"),"pleaseUploadFile","请上传文件"),"subChannelEmpty","子渠道商名称不能为空"),"subChannelAmpersand","子渠道商名称不能包含&符号"),"contactEmailEmpty","联系人邮箱不能为空"),"EmailFormatError","联系人邮箱格式错误"),"fuelPackProfitMarginEmpty","加油包利润率不能为空"),"totalAmountEmpty","总额度不能为空"),"accountPermissionsEmpty","账号权限不能为空"),"subChannelDetails","子渠道商详情"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"editSubChannel","编辑子渠道商"),"addSubChannel","新增子渠道商"),"uploadFailed","上传失败!"),"files","文件"),"fileUploadedAndProgressDisappears","文件上传中、待进度条消失后再操作"),"downTemplateFilelAndUpload","请先下载模板文件，并按格式填写后上传"),"imsi","IMSI号"),"packageProfitMargin","套餐利润率"),"fuelPackProfitMargin","加油包利润率"),"failureReason","不通过原因"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"supportAddon","是否支持加油包"),"bindAddon","绑定加油包"),"AddonList","加油包列表"),"newAddon","添加加油包"),"AddonName","加油包名称"),"AddonID","加油包ID"),"AddonAmount","加油包流量值"),"AddonListMandatory","加油包列表不能空"),"createOrder","新增订单"),"revoke","撤销"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"downloadInvoice","下载Invoice"),"downloadList","下载号码列表"),"uploadPayslip","上传付款证明"),"customizedSIM","定制卡"),"PhysicalSIM","普通卡"),"receiveeAddress","收货地址"),"receiverName","收件人"),"contactNumber","联系电话"),"distribution","代销"),"atoz","A2Z"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"cooperationModel","合作模式"),"payslip","付款证明"),"uploadPicture","点击或拖拽文件上传到此处"),"pictureSize","超过了最大限制范围10MB"),"paymentMethod","收费模式"),"deliveryCompany","物流公司"),"trackingNumber","物流编号"),"ordered","已下单"),"cancelled","已取消"),"pendingPayment","待付款"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"paid","已付款"),"deliveryProgress","发货中"),"delivered","已发货"),"deliveryFailed","发货失败"),"PaymentConfirmed","付款已确认"),"PaymentNotConfirmed","付款未到账"),"PaymentMandatory","普通/定制卡不能为空"),"cardTypeMandatory","主卡形态不能为空"),"cardMumberMandatory","卡片数量不能为空"),"wrongFormat","格式不正确"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"addressMandatory","收货地址不能为空"),"receiverMandatory","收件人不能为空"),"contactNumberMandatory","联系电话不能为空"),"cooperationMandatory","合作模式不能为空"),"picetureMandatory","文件不能为空"),"confirmRevocation","确认撤销？"),"paymentMethod","收费模式"),"description","说明！"),"newAddonPack","新增加油包"),"determine","确认"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"flowValue","流量值"),"addAmountMandatory","加油包流量值不能为空"),"cannotExceed","超过了最大限制范围104857600MB"),"generateInvoice","生成Invoice时间"),"uploadPayslipTime","上传付款证明时间"),"orderConfirmTime","订单确认时间"),"deliveryTime","发货时间"),"paymentConfirmed","付款待确认"),"physicalOrCustomized","CMLINK/定制卡"),"inputFlowValue","请输入加油包流量值"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"inputAddonName","请输入加油包名称"),"inputAddonId","请输入加油包ID"),"inputCardNumber","请输入卡片数量"),"inputRecrver","请输入收件人"),"inputAddress","请输入收货地址"),"selectCardtype","请选择主卡形态"),"productPackaging","产品包装"),"selectProductPackaging","请选择产品包装"),"productPackagingEmpty","产品包装不能为空"),"packagingCard","包装卡"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"nakedCard","裸卡"),"receivingCountry","收货国家"),"selectReceivingCountry","请输入收货国家"),"receivingCountryEmpty","收货国家不能为空"),"postalCode","邮编"),"selectPostalCode","请输入邮编"),"postalCodeEmpty","邮编不能为空"),"language","语言"),"selectLanguage","请选择语言"),"languageEmpty","语言不能为空"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"agreement","请勾选协议"),"agree","我同意此"),"Agreements","协议"),"orderId","订单ID"),"replaceHIMSI","替换HIMSI"),"isSupportHot","当前上网是否支持热点"),"internetTemplateSpeed","当前模板上网速度"),"AgreementContent","协议内容"),"resourceCooperation","资源合作"),"coverageTime","覆盖时间"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(a,"informationQuery","信息查询"),"cdrDetails","CDR明细"),"moreInformation","更多信息"),"dataInformation","流量信息"),"rollbackInProgress","回滚中"),"rollbackSuccess","回滚成功"),"rollbackFailure","存在回滚失败"),"emptyData","未查询到套餐信息"),"validityPeriod","实名制有效期"),"longTerm","长期有效"),Object(u["a"])(a,"singlePackageEffective","单次套餐生效"))),"directionalApp",{application:"应用",freeFlow:"免流",supportUsage:"是否支持定向流量",selectSupportUsage:"请选择是否支持定向流量",selectAPP:"选择应用",pleaseSelectAPP:"请选择应用",deleteAPP:"删除应用",addAPP:"添加应用",specificAPPLogic:"定向使用逻辑",inputDataValue:"请输入流量值",deleteDataValue:"删除流量值",addDataValue:"添加流量值",dataValue:"流量值",selectUPCCTemplate:"选择UPCC模板",pleaseSelectUPCCTemplate:"请选择UPCC模板",continueDataUsage:"是否继续使用通用流量",PleaseDataUsage:"请选择是否继续使用通用流量",restrictedTemplate:"定向限速模板",pleasepRrestrictedTemplate:"请选择定向限速模板",FreeTemplate:"定向免流限速模板",pleaseFree:"请选择定向免流限速模板",FreeContinueTemplate:"定向免流继续使用模板",pleaseFreeContinue:"请选择定向免流继续使用模板",SupportDataMandatory:"是否支持定向流量不能为空",number9:"选择应用总数不能超过9个",APPMandatory:"选择应用不能为空",LogicMandatory:"定向使用逻辑不能为空",valueMandatory:"流量值不能为空",twoTemplate:"定向免流继续使用模板/定向免流限速模板不能为空",ContinueMandatory:"是否继续使用通用流量不能为空",upccMandatory:"选择UPCC模板不能为空",valueRepeat:"流量值不能重复",appRepeat:"应用不能重复",dingTemMandatory:"定向限速模板不能为空",useTemMandatory:"定向免流继续使用模板不能为空",freeTemMandatory:"定向免流限速模板不能为空",gearRule:"流量值逻辑不正确，每档的流量值需大于上一档次的流量值",usageRule:"用量值逻辑不正确，每档的用量值需大于上一档次的用量值"}),"address",{deleteitem:"确认删除该项?",fullname:"姓名",input_fullname:"请输入姓名",mailbox:"邮箱",input_mailbox:"请输入邮箱地址",Newaddress:"新建地址",modify:"修改",modifyaddress:"修改邮箱",Delete:"删除",Forgetpwd:"修改密码",setdefault:"设置默认",search:"搜索",password:"密码",password_ok:"确认密码",input_pwd:"请输入密码",newpwd:"新密码",account:"账号",action:"操作",oldPwd:"原密码",PwdRules:"密码设置时需要满足给定规则，点击",watch:"查看",more:"了解详情",Rules1:"确保口令满足以下通用原则",Rules2:"1、口令至少由8位及以上大写字母、小写字母、数字与特殊符号等4类中3类混合、随机组成，尽量不要以姓名、电话号码以及出生日期等作为口令或者口令的组成部分；",Rules3:"2、口令应与用户名无相关性，口令中不得包含用户名的完整字符串、大小写变位或形似变换的字符串，如teleadmin:teleadmin、teleadmin:teleadmin2017、teleadmin:TeleAdmin、teleadmin:te!e@dmin等；",Rules4:"3、应更换系统或设备的出厂默认口令，如huawei:huawei@123，oracle数据库中SYS:CHANGE_ON_INSTALL,某移动定制版光猫默认帐号CMCCAdmin:aDm8H%MdA等；",Rules5:"4、口令设置应避免3位以上（含3位）键盘排序密码，如qwe（键盘第1行前三个字母）、asd（键盘第2行前三个字母）、qaz（键盘第1列三个字母）、1qaz（键盘第1列数字加前三个字母）、！QAZ（键盘第1列特殊字符加前三个字母）等；",Rules6:"5、口令中不能出现3位以上（含三位）连续字母、数字、特殊字符，如ABC、Abc、123、！@#等；",Rules7:"6、口令中不能出现3位以上（含三位）重复字母、数字、特殊字符，如AAA、Aaa、111、###等。",Rules8:"避免以下易猜解口令规则",Rules9:"1、省份、地市名称、邮箱、电话区号、邮政编码及缩写和简单数字或shift键+简单数字，如BJYD123、HBYD!@#等；",Rules10:"2、单位名称、专业名称、系统名称、厂家名称（含缩写）和简单数字，如HBnmc123、HBsmc_123等；",Rules11:"3、维护人员名字全拼大小写缩写等变形+设备IP地址（一位或两位）或出生年月日等，如维护人员张三，维护设备地址为************和************,出生日期为19951015，则其可能的弱口令为zhangsan100、zhangsan101，zhangsan10100，zhangsan10101，zhangsan19951015，ZS19951015等；",Operationreminder:"操作提醒",deleted:"删除成功!",inconsistent:"两次密码，输入不一致",emailaddress:"请输入有效的邮箱地址",reset:"密码格式不规范，请重新填写",appear:"不可出现3个连续且相同的字符",allowed:"不可出现3位连续的数字",determine:"确定"}),"flow",(c={inputICCID:"请输入ICCID",Channel:"渠道商",Status:"状态",chooseStatus:"请选择状态",toassigned:"待分配",Assigned:"已分配",typelimit:"单周期类型限量",Totallimits:"总限量",Controllogic:"控制逻辑",Stoplimit:"达量停用",speedlimit:"达量限速",Continuelimit:"达量继续使用",Originlimit:"归属流量池",inputPoolname:"请输入流量池名称",poolName:"流量池名称",Usagestatus:"使用状态"},Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(c,"chooseStatus","请选择使用状态"),"Normal","正常"),"Restricted","限速"),"Stop","停用"),"Pleasechoose","请选择上架状态"),"upStatus","上架状态"),"Online","上架"),"Offline","下架"),"Useddata","已用流量"),"Numbericid","卡号数量"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(c,"threshold","提醒阈值"),"ImportICCID","ICCID导入"),"Batchdelete","批量删除"),"Batchupdate","批量修改"),"ImportTime","入池时间"),"Singleimport","单个导入"),"Batchimport","批量导入"),"Choosepool","选择流量池"),"plesepool","请选择流量池"),"ICCIDempty","ICCID不能为空"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(c,"Monocyleempty","单周期类型上限不能为空"),"Totalempty","总上限不能为空"),"choosecontrollogic","请选择控制逻辑"),"Unit","单位"),"units","单位"),"UploadICCID","上传ICCID列表"),"iccidFileEmpty","ICCID列表文件不能为空"),"Batchtime","批量导入时间"),"Importtotal","导入总数"),"Numbersuccess","成功数量"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(c,"Numberfailure","失败数量"),"Successfile","成功文件"),"Failurefile","失败文件"),"Usagethreshold","用量提醒阈值"),"Percentage","百分比"),"Usageempty","用量提醒阈值不能为空"),"Usagerecord","使用记录"),"Choosemonth","选择月份"),"Choosedate","选择起止日期"),"PleaseChoosedate","请选择日期范围"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(c,"Pleasemonth","请选择月份"),"Billmonth","账单月份"),"UsagestartTime","使用开始时间"),"UsageendTime","使用结束时间"),"Ratedtotalusage","额定总流量"),"Actualtotalusage","实际使用总流量"),"Excessusage","超额流量"),"Ratedcharge","额定费用(元)"),"Excesscharge","超额费用(元)"),"Totalcharge","总费用(元)"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(c,"Details","详情"),"Clickview","点击查看"),"IMSI","HIMSI号码"),"VIMSI","VIMSI号码"),"Numbertype","号码类型"),"StartTime","开始时间"),"EndTime","结束时间"),"Usage","用量"),"totalusage","总流量"),"year"," 年 "),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(c,"month"," 月 "),"dday"," 日 "),"flowyear","自然年"),"flowmonth","自然月"),"Totallimit","总上限"),"Usagedetails","使用记录详情"),"Pleasethreshold","请输入用量提醒阈值"),"Pleaseinteger","请输入正整数"),"Validdate","有效日期"),"Resetnumber","重置周期数"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(c,"Resettype","重置周期类型"),"Monocyletype","单周期类型上限"),"Cardcycle","单卡周期达量限速"),"Stopdatalimit","单卡周期达量停用"),"Restrictedspeed","单卡总量达量限速"),"Totallimitcard","单卡总量达量停用"),"Datapoollimit","流量池总量达量限速"),"stoppoollimit","流量池总量达量停用"),"chooserecord","请至少选择一条记录"),"Confirmdelete","确认删除"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(c,"Dataused","已使用流量"),"Originenterprise","归属企业"),"Positivenumber","最高支持8位整数和2位小数的正数或零"),"yearandmonthdate","账单日期"),"day","自然日"),"Country","支持国家"),"deleteNumber","移除"),"poolAvailableTime","入池可用时长"),"fillNumber","填写可用的单周期数量"),"exportflowsum","流量汇总导出"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(c,"remark","备注"),"inputRemark","请输入备注"),"enterRemark","填写备注信息"),"internetStatus","上网状态"),"inputinternetStatus","请输入上网状态"),"recover","恢复"),"cardManager","单卡管理"),"mb","单位MB"),"totallimit","总上限"),"availableday","可用时长"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(c,"none","无"),"confirmPause","确认暂停该项？"),"confirmResume","确认恢复该项？"),"kongge","不允许输入空格"),"expirationDate","到期时间"),"SelectDestination","选择国家/地区"),"select","查询"),"usageMB","用量(MB)"),"requiredDataExport","请选择以下需要的导出数据"),"chooseOne","请至少选择一个"),Object(u["a"])(c,"negative","不能为负数"))),"fuelPack",{startDate:"套餐开始时间",endDate:"套餐结束时间",packagedata:"当前套餐流量上限(MB)",adddata:"已购买加油包流量(MB)",usedhigh:"已使用高速流量(MB)",Purchase:"购买加油包",SelectfuelPack:"选择加油包",PleaseSelectfuelPack:"请选择加油包",quantity:"选择加油包数量",Currentday:"当日加油包",Daily:"每日加油包",CurrentMonthly:"当月加油包",Monthly:"每月加油包",CurrentYear:"当年加油包",Yearly:"每年加油包",Amount:"金额",onlinestatus:"当前上网状态",purchasequantity:"请输入购买数量",Specificdate:"指定激活日期",SelectDate:"选择日期",PleaseSelectDate:"请选择日期",activationType:"套餐确认激活方式",Activatelimit:"达量激活",ActivateSpecific:"指定日期激活",ActivateLU:"LU激活",packageTtart:"选择套餐生效日期",activationdate:"限定激活日期",buyfuelPack:"加油包购买后，立即生效，不支持退订，是否确认购买？",price:"价格",Remainingdays:"剩余天数:"}),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(o,"selectCooperationMode","请选择渠道商合作模式"),"consignmentSalesModel","代销模式"),"A2Zmode","A2Z模式"),"resourceMode","资源合作模式"),"welcomeWebsite","欢迎使用CMI全球卡业务网站"),"totalCards","卡总数"),"announcement","公告栏"),"esimTotal","ESIM卡总数"),"tsimTotal","贴片卡总数"),"imsiTotal","IMSI卡总数"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(o,"imsika","IMSI卡"),"shitika","实体卡"),"simTotal","实体卡总数"),"quotaUsedMonth","当月已用额度"),"AtoZTotal","A2Z总信用额度"),"creditsUsed","已用信用额度"),"unused","未使用"),"usedLimit","已用额度"),"yuan","元"),"packageOrdered","套餐订购数"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(o,"months12","近12个月数据"),"twofivebefore","25日之前: 上月25日至今"),"twofiveafter","25日之后: 本月25日至今"),"esimkazhang","ESIM卡(张)"),"imsikazhang","IMSI卡(张)"),"simkazhang","实体卡(张)"),"cardkazhang","卡总数(张)"),"resourceManage",{channelName:"渠道商名称",enterChannelName:"请输入渠道商名称",allocateResources:"分配资源",resourceView:"资源查看",resourceSupplier:"资源供应商",selectResourceSupplier:"请选择资源供应商",imsiNumber:"IMSI数量",selecyImsiNumber:"请输入IMSI数量",routingID:"路由ID",selectRoutingID:"请输入路由ID",resourceSupplierMandatory:"资源供应商不能为空",imsiNumberMandatory:"IMSI数量不能为空",routingIDMandatory:"路由ID不能为空",selectVIMSIphone:"请输入IMSI号码",batchFreeze:"批量冻结",BatchRecovery:"批量恢复",seeInformation:"查看信息",modifyingResources:"修改资源",BatchModifyingResources:"批量修改资源",FreezeItem:"确认冻结该项?",RecoverItem:"确认恢复该项",confirmFreeze:"确认冻结?",confirmRecover:"确认恢复?",operationFail:"操作失败",Dimension:"选择统计维度",selectDimension:"请选择统计维度",dateOrMonth:"日期/月份",fee:"费用",imsiPhone:"IMSI号码",selectImsiPhone:"请输入IMSI号码",routingExceeds:"超过了最大限制范围**********",numberExceeds:"超过了最大限制范围100000"}),"channelBill",{startMonth:"开始月份",endMonth:"结束月份",selectStart:"请选择开始月份",selectEnd:"请选择结束月份",dataUsage:"流量计费查询",inputChargesName:"请输入计费名称",imsiFee:"IMSI费查询",detailed:"明细",billFileDownload:"账单明细下载",invoiceDownload:"Invoice下载",payslip:"付款",reUpload:"重新上传",dataFee:"流量费",cardFee:"卡费",packageFee:"套餐费",paymentPage:"缴付页面",inputAmount:"请输入金额",checkNumber:"最高支持8位整数和2位小数的正数",imsiFeeType:"IMSI费规则",imsiFeeAmount:"IMSI费金额",quantityRange:"数量区间",chargesName:"计费名称",country:"国家",cny:"人民币单价",hkd:"港币单价",usd:"美元单价",billId:"账单ID",billType:"账单类型",paymentMonth:"出账月份",totalBillAmount:"账单总金额",accountsPayableAmount:"应缴账单金额",paymentStatus:"缴费状态",verified:"已核销",unpaidPayment:"未付款",confirmationReceipt:"待确认到账",Arrived:"付款成功",NotCredited:"付款失败",merge:"合并",endGreaterStart:"结束月份需大于等于开始月份！",lessThan0:"不能小于0",onlinePayment:"线上支付",offlinePayment:"线下支付",OnlinePaymentInProgress:"线上缴费中",cnInvoice:"CN Invoice",uploadCnInvoice:"上传CN Invoice",cnInvoiceDownload:"CN Invoice下载",pleaseUploadCnInvoice:"请上传CN Invoice文件"}),"onlineOrder",{orderUniqueId:"订单ID",orderName:"产品名称",orderType:"订购类型",corpId:"渠道商ID",orderUserName:"购买用户名称",productId:"产品ID或账单ID",thirdOrderNo:"第三方支付订单标识",thirdMchorderNo:"第三方支付订单号",thirdTransactionNo:"第三方支付交易流水号",currencyCode:"货币类型",amount:"金额",orderStatus:"订单状态",paymentMethod:"支付方式",paymentStatus:"支付状态",paymentTime:"支付时间",paymentIp:"用户IP",paymentReference:"支付凭证ID或脱敏后的卡号",exprieTime:"支付截止时间",asyncNotifyType:"异步结果通知类型",sendLang:"语言",isDeleted:"逻辑删除标记",createTime:"创建时间",updateTime:"更新时间",email:"邮箱",choosePaymentStatusPlaceholder:"请选择订单状态",thirdOrderNoPlaceholder:"请输入第三方支付订单标识",thirdTransactionNoPlaceholder:"请输入第三方支付交易流水号",thirdMchorderNoPlaceholder:"请输入第三方支付订单号",chooseCreateDate:"请选择创建时间段",onlineModalTitle:"线上支付",payTxt01:"交易将在",payTxt02:"后关闭",payBtn:"支付",orderTypeBill:"账单缴费",orderTypedeposit:"押金充值",wechat:"微信",alipay:"支付宝",card:"银行卡",paying:"待支付",paySuccess:"支付成功",payExpired:"订单已过期",payclosed:"订单已关闭",closeOrderContent:"确定要关闭此订单吗？",deleteOrderContent:"确定要删除此订单吗？",deleteSuccess:"删除成功",closeSuccess:"关闭成功",paidAmount:"实付金额：",depositAmount:"押金金额：",weChatPayChina:"微信支付（中国内地版）",alipayChina:"支付宝（中国内地版）",debitCreditCard:"储蓄卡/信用卡",depositAmountPlaceholder:"请输入押金金额",loadingStatus:"加载中",correctDepositAmount:"请输入正确的金额",depositAmountGreaterThanZero:"金额必须大于0",selectPaymentMethod:"请选择支付方式",correctBankCardInfo:"请输入正确的银行卡信息",amountCannotBeEmpty:"金额不能为空",validPositiveNumber:"请输入有效的正数，最多两位小数",positiveNumberGreaterThanZero:"请输入大于0的正数",stateIsValid:"state.isValid:",paymentMethodType:"state.data.paymentMethod.type:"}),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(o,"paymentResultpageTexts",{paymentFailed:"支付提醒",paymentSuccessful:"支付提醒",errorDetails:"支付已提交，请点击下方按钮查看支付详细信息。",successDetails:"支付已提交，请点击下方按钮查看支付详细信息。",errorReason:"失败原因：",thanksMessage:"感谢您的支持",viewOrder:"查看订单",goToHome:"去首页"}),"offlinePay",{applyInvoice:"申请发票",pay:"缴付",reApplyInvoice:"重新申请发票",rePay:"重新上传付款证明",invoiceType:"发票类型",topupID:"充值ID",invoiceNumber:"发票号",deposit:"押金",Prepayment:"预存",applyTime:"申请时间",unpaid:"未缴费",invoicePendingApro:"发票审批中",invoiceAproReject:"发票审批拒绝",payable:"可缴费",payPendingApro:"缴费审批中",paymentAproReject:"缴费审批拒绝",paid:"已缴费",attachment:"附件",onlineTopup:"线上充值",offlineTopup:"线下充值",topup:"充值",topupAmount:"充值金额"}),"country",{select:"选择国家",selectAll:"全选",selected:"已选国家",nameCn:"国家名称(中文)",nameEn:"国家名称(英文)",continentCn:"所属大洲(中文)",continentEn:"所属大洲(英文)",specialRuleMngr:"特殊国家规则管理",all:"全部",noData:"暂无数据",viewAll:"查看全部国家",selectingContinent:"正在选择当前大洲国家...",deselectingContinent:"正在取消选择当前大洲国家...",getContinentFail:"获取大洲列表失败",getCountryFail:"获取国家列表失败",getAllCountryFail:"获取全部国家数据失败",alreadySelected:"已经选择了国家: ",operationFail:"操作失败：",editCountry:"编辑国家"}),"sessionInfo",{sessionDetail:"会话详情",realtimeSession:"实时会话信息",historySession:"历史会话信息",field:"字段",currentVlaue:"当前值",accessSite:"接入站点",online:"是否在线",sessionStartTime:"会话开始时间",dataUsageDuringSession:"会话内产生的流量",serviceProviderUsed:"使用的运营商",userIPAddress:"用户IP地址",PGWSiteAccessedSession:"会话接入的PGW站点",sessionQuery:"Session查询",UsageMax:"用量值超过最大限制 ",UsageMin:"用量值不能小于10MB"})),m={home:"Home",login:"Login",spinLoading:"Loading",system_mngr:"System Management",account_list:"Account Management",log_mngr:"Operation Log",Announcement_mngr:"Announcement Management",lockModeApproval_mngr:"Golden Lock Mode Management",login_mngr:"Log In Log",pwd_mngr:"Password Management",pri_mngr:"Role Management",sys:{wholesalerName:"Please Enter Account Number...",roleType:"Please Select Role...",add:"New Account",account:"User Name:",paccount:"Please Enter User Name...",oldPwd:"Original Password:",poldPwd:"Please Enter The Original Password...",newPwd:"New Password:",pnewPwd:"Please Enter A New Password...",rePwd:"Confirm Password:",prePwd:"Please Confirm The Password...",pwdNullMsg:"Password Cannot Be Empty",pwdLengthMsh:"Length Cannot Be Less Than 6 Bits",accountNummMsg:"Account Name Cannot Be Empty",logInto:"Login",logOut:"Log Out",optAccoount:"Operation Account",optType:"Operation Type",serviceUrl:"Server Address",optTime:"Operation Time",accountT:"Account",role:"User Role",status:"User State",opt:"Operation",accountempty:"The Account Name Cannot Be Empty",newpassword:"Enter A New Password",oldpassword:"Enter The Original Password",Enteraccount:"Enter Account",successfully:"The Users Basic Information Has Been Successfully Modified, Please Log In Again.",wrong:"Something Went Wrong",Serverwrong:"Server Internal Error",smsCode:"Please Enter A 6-digit Verification Code",sendSmsCode:"Verification Code Sent Successfully",enterEmail:"Enter email:",inputEmail:"Please input your email",setPassword:"set password:",enterPassword:"Please enter password...",phone:"cellphone number",enterPhone:"Please enter the phone number...",pwNotMatch:"The two passwords entered do not match. Please check carefully.",inputName:"Please enter login name first!",codeSendPhone:"Verification code has already sent to your phone/email, please check.",second:"Second",inputTwoPw:"The two passwords entered do not match.",sendVercode:"Verification code sent",persona:"Role",exitModifyPassword:"Exit to change password",userNameEmpty:"User name is mandatory",pleaseSetRoles:"Please select the user role",phoneNoEmpty:"Mobile number is mandatory",onlyNumbers:"Only support numbers",userNameSpaces:"Users name contains space",userNameAmpersand:"Login name can't include symbol '&'",twentyDigits:"Maximum 20 characters supported",passwordsNotMatch:"The passwords entered are inconsistent, please re-enter",successAddedUser:"Added users successfully!",userInforupdated:"User information has been updated",successDeleted:"User deleted successfully",operationWill:"Current operation",user:"Users",continueExecution:",Continue process?",userStatusUpdated:"User status updated!",confirmDeleteUser:"Please confirm delete the selected user",obtainPermissionData:"Extracting permission data, please wait",Saving:"Saving, please wait",roleName:"Role name",newRole:"Add role",enterName:"Enter role name",viewPermissions:"View permissions",rolePermissionManagement:"Role permission management",editAccount:"Edit",characterSpaces:"Role name contains space",purview:"Permission",successAddedRole:"Role added successfully",failedObtainRole:"Failed to obtain user permission, please try again!",rolePpermissionsUpdated:"Role permission updated"},common:{welcome:"Hello ",edit:"Modify",frozen:"Frozen",thaw:"Thaw",del:"Delete",search:"Search",timeSection:"Select Time Period",beginTime:"Start Time",endTime:"End Time",optResult:"Results",optType:"please Select An Operation Type",success:"Success",failure:"Failure",logOut:"Log Out",rePwd:"Confirm Password",determine:"Confirm",cancel:"Cancel",Error:"Error Message",token:"Token Verification Failed, Please Log In Again",Request:"Request Error",errorcode:"Request Interface Exception, Error Code [404]",timedout:"Request Timed Out",feature:"This Feature Is Temporarily Unavailable, Please Try Again Later",Servererror:"Server Error, Please Contact The Administrator",Requestout:"Request Timed Out, Please Try Again Later",Successful:"Successful operation",Closeall:"Close All",Closeother:"Close Others",Fullscreen:"Full Screen",Exitscreen:"Exit Full Screen",manageOperate:"Manage",pleaseChoose:"Please Select",PhysicalSIM:"CMLink SIM",cardType:"Card Type",quantity:"Quantity",simplifiedChinese:"Simplified Chinese",traditionalChinese:"Traditional Chinese",english:"English",tips:"Tips"},cdr_mngr:"CDR Bill Management",call_query:"Bill Query",country_operators:"Search By Country/Operator",search_number:"Query By Number",flowtotal:"Flow Total",search_no:"Query By sectionNo",price_rules:"Rating Rules",search_enterprise:"Query By Enterprise",search_setMeal:"Query By Package",company_price:"Enterprise/Carrier Billing Statistics",product:"Product Manager",download_mngr:"Downloads",taskListinfo:"Downloads",Period:"Period",TaskID:"Task ID",FileName:"File Name",Tasks:"Task ID",Description:"Description",TasksStatus:"Status",File:"File Name",CreationTime:"Creation Time",FinishedTime:"Finished Time",Operation:"Operation",DownloadFlie:"Download",FailedFlie:"Failed To Download",Notexist:"File Does Not Exist",exportMS:"Message",exportID:"The Task ID:",exportFlie:"The File Name:",downloadResult:'Please Go To The Page Of "Downloads" To Check And Download The Result',Goto:'Go To "Downloads"',product_operation:"Product Operation",package_config:"Package Configuration",individual_config:"Individual Configuration",batch_config:"Batch Configuration",inventory_mngr:"Inventory Management",task_query:"Task Query",porder_mngr:"Personal Order Management",package_delay:"Package Delay",package_search:"Inactive Package Query",pakg_delay:"Unactivated Packages Delay",delayTask_audit:"Deferred Task Review",activation_record:" Activate Record Query",ordinary_package:" Global Card Ordinary Package",offline_mode:" Terminal Vendor Offline Mode",online_mode:" Terminal Vendor Online Mode",cooperative_model:" Partner Operator Mode",SupplierOrder_management:"Channel supplier order management",ChannelCardOrders_management:"Channel Blank SIM Management",activateStat_search:"Activate Record Query",activate_stat:"Sales Statistical Query",availability_stat:"Available Package Statistics",statEnd_month:"Statistics Of Inactive Packages",masterCard:"Master Card Management",cardPool:"Card Pool Management",associationGroup:"National Card Pool Association Group",addCardPool:"Add Association Group",updateCardPool:"Edit Association Group",copyCardPool:"Copy Association Group",cardPooldetails:"Card Pool Details",specialCountryRule:"Special Destination Rules Management",specialCountryRuleAdd:"Add Special Destination Rules",specialCountryRuleEdit:"Edit Special Destination Rules",packageManage:"Package Management",packageIndex:"Package",packageBatch:"Package Batch Edit",packageAdd:"Package Add",packageUpdate:"Package Edit",packageInfo:"Package Details",cardPoolDetail:"Package Card Pool Details",packageCopy:"Package Copy",resource:"Number Resource Management",msisdn:"MSISDN Management",iccid:"ICCID Management",imsi:"IMSI Management",supplyImsi:"Supplier IMSI Management",makeCardFile_mngr:"Card file Management",addCard_mngr:"Create card tasks",otaData_mngr:"OTA Data Management",imsi_list:"IMSI List",vimsi:"VIMSI Management",trafficPool_mngr:"Traffic Pool Management",addPool_mngr:"Create A New Traffic Pool",editPool_mngr:"Modify The Traffic Pool",copyPool_mngr:"Copy Traffic Pool",detailsPool_mngr:"Flow Pool Details",channelPool_mngr:"Traffic Pool Management",resourceCooperation:"Resource Management",fuelPack_mngr:"Add-on Package Management",upccTemplate_mngr:"UPCC speed template Management",channelProvider_mngr:"Channel provider package Management",targetedApplication_mngr:"Specific APP Management",whiteListPackage_mngr:"White list package management",viewResources:"Resource",callOrderDetails:"CDR details",billingStatistics:"Invoice statistics",finance_mngr:"Financial System Management",billing_mngr:"Billing Management",history_mngr:"Historical Real Bill",acounting_period:"Account Period Management",serviceRecharge_approval:"Self-service recharge approval",billing_adjust:"Billing adjustments",addBilling_adjust:"Added account adjustments",aprv_details:"Approval details",realname_mngr:"Real Name Management",rule_mngr:"Rule Management",ruleDetail_mngr:"Rule Details",certification_mngr:"Manual Authentication",certificationinfo_mngr:"Certification Information",marketing_mngr:"Marketing management",marketingActivityIndex:"Marketing management",marketingActivityUpdate:"Marketing activity Edit",marketingActivityAdd:"Marketing activity Add",smsManage:"SMS Management",notificationSMS:"Notification SMS Management",notificationIndex:"Notification SMS",notificationAdd:"Notification SMS Add",notificationUpdate:"Notification SMS Edit",notificationInfo:"Notification SMS Details",areaWelcomeSMSIndex:"Regional Welcome SMS Management",areaWelcomeInfo:"Regional Welcome SMS Details",areaWelcomeEdit:"Regional Welcome SMS Editor",areaWelcomeEditAdd:"Regional Welcome SMS Add",customerSMS:"Customer service SMS Management",customerIndex:"Customer Service SMS",marketingSMS:"Marketing SMS Management",marketingIndex:"Marketing SMS",marketingInfo:"Marketing SMS Details",customerManage:"Customer Management",channelManage:"Channel Management",channelIndex:"Channel",billCheck:"Bill Check",channelAdd:"Channel Business Add",channelInfo:"Channel Business Details",channelUpdate:"Channel Dealer Editor",packageGroup:"Package Group Management",zeroLevelChannel:"Zero level channel provider",a2zBillPriceMngr:"Traffic Billing and Pricing Management",costPricingMngr:"Cost Pricing Management",imsiFeeMngr:"IMSI Fee Management",zeroChannelAdd:"New zero level channel providers",zeroChannelUpdate:"Modify zero level channel supplier",a2zBillPriceDetail:"Traffic Billing and Pricing Management - Rule Details",costPricingDetails:"Cost Pricing Management - Details",cooperativeManage:"Cooperative Operator Management",cooperativeIndex:"Cooperative Operator",cooperativeInfo:"Details Of Cooperative Operators",postPaymentChannel:"Post-paid Channel Management",paymentChannelIndex:"Post-paid Channel",paymentChannelInfo:"Post-paid Channel Details",manufacturerManage:"Terminal Manufacturer Management",manufacturerIndex:"Terminal Manufacturer",manufacturerInfo:"Terminal Manufacturer Details",manufacturer_update:"Terminal Manufactureredit",test_imsi:"Test IMSI Management",service_brace:"Customer Service",service_index:"SIM Details",local_search:"Location Query ",purchased_package:"Purchased Package",location_package:"Current Activated Package",sppurchased_package:"Purchased Package",splocation_package:"Current Activated Package",fault_mngr:"Fault Handling",fault_add:"New Fault Handling",GTPAddressConfig:"GTP Call Ticket Address Configuration",flowPool:"Flow Pool Management",poolList:"Flow Pool Details",channel_mngr:"Channel Portal",deposit_mngr:"Account Management",mealList_mngr:"Package Details",streamList_mngr:"Flow Details",offlinePayment:"Top up page",marketingAccount:"Marketing Activity Details",adminMarketingAccount:"Marketing Activity Details",stock_mngr:"Inventory Management",cardList_mngr:"SIM Details",buymeal_mngr:"Purchase Package",order_mngr:"Order Management",bill_mngr:"Monthly Bill",support_mngr:"Customer Service",address_mngr:"Address Management",detailsList_mngr:"Service And Support Package Details",useList_mngr:"Usage Details",showiccid_mngr:"ICCID List",flowpool_mngr:"Data Pool",cardlist_mngr:"ICCID List",flowlist_mngr:"Data Pool List",userecord_mngr:"Usage Record",iccidlist_mngr:"ICCID List",channelcardlist_mngr:"ICCID List",channelflowlist_mngr:"Data Pool List",channeluserecord_mngr:"Usage Record",channeliccidlist_mngr:"ICCID List",channelfuelPack_mngr:"Add-on Package Management",channelpackage:"Package Management",fuelPackManagement:"Fuel Pack Management",aqCode_mngr:"ESIM QR code Management",subChannelProvider_mngr:"Sub-channel Management",whiteCardOrders_management:"Blank SIM Management",channelResourceCooperation:"Resource Management",channelViewResources:"Resource",channelCallOrderDetails:"CDR details",channelBillingStatistics:"Invoice statistics",channelBillingQuery:"Partner invoice",paymentOrderManagement:"Payment order management",operators_mngr:"Operator Management",ResourceSupplier_mngr:"Resource supplier management",operatorsindex_mngr:"Operator Management",channelSellindex_mngr:"ChannelSell Management",channelSellHistory_mngr:"channelSellHistory Management",channelSell_mngr:"ChannelSell Management",topUp_records:"Top-Tp Records",report_mngr:"Report Function",exchange_mngr:"Exchange Rate Management",cardsell_mngr:"Card Sales Report",cardactive_mngr:"Card Activation Report",subscription_mngr:"Package Subscription Report",activation_mngr:"Package Activation Report",reuse_mngr:"Reuse Query",analysis_mngr:"Package Analysis Report",terminal_mngr:"Terminal Settlement Report",channelReport_mngr:"Operator Settlement Report",income_mngr:"Offline Revenue Report",terminal_after_pay:"After Payment Report",costReport_mngr:"Cost Report",esimDownloadReport_mngr:"ESIM Download Report",deposit:{deposit_money:"Current Balance",mealList:"Package Details",streamList:"Detail",mealname:"Package Name",search:"Search",charge_time:"Period",canmeal:"Product List",flow:"Records of Increased Deposit Balance",mealId:"Package ID",mealprice:"Charges",charge_price:"Recharge Amount",currency:"Currency",chosetime:"Please Select Recharge Time",ProductList:"Product List Export",charge_type:"Type",Amount:"Transaction amount",accountdeposit:"Account balance",startTime:"Please select a start date",endTime:"Please select an end time",a2zDepositLimit:"Deposit/Prepayment",channelMode:"Business Model",depositAccount:"Deposit Account",preDepositAccount:"Prepayment Account",marketingAccount:"Marketing Account",creditAccount:"Credit Account",marketingAccountFlow:"Marketing Account Transaction Details",marketingActivities:"Marketing Activity",eventStartTime:"Activity Start Time",eventEndTime:"Activity End Time",campaignStatus:"Activity Status",rebateAmount:"Rebate Amount",usedAmount:"Amount Used",rebateBalance:"Rebate Balance",arrivalTime:"Arrival Time",expirationTime:"Expiration Time",spendTime:"Consumption Date",effectiveTime:"Effective Time",connectedActivities:"Related Activities",consumptionAmount:"Consumption Amount",accountTotalBalance:"Total Balance of Marketing Account",settlementDate:"Settlement Date",pricingDate:"Price Approval Date",singleActivityBalance:"Single Marketing Activity Balance",marketingActivityDetails:"Marketing Activity Rebate Details",marketingAccountDetails:"Marketing Account Details",totalOrderId:"Order ID",toBeStarted:"To Be Started",started:"Started",ended:"Ended",obsolete:"Obsolete",earlyTerminate:"Early Terminate",increaseMarketinRebates:"Increase Marketing Rebates",distributionPackageOrdering:"Distribution Package Ordering",startEndDate:"Please Select The Start And End Date!",totalOrderNumber:"Order Number",SubOrderNumber:"Order Number(Sub-Order)",ResetMarketingBudget:"Reset The Expired Marketing Budget",dataUsageSettlemtn:"Data Usage Settlement",OffsetMarketingRebateAmount:"Offset Marketing Rebate Amount"},stock:{order_number:"Task",input_number:"Please Fill In Task Name",timeslot:"Period",chose_time:"Please Select",search:"Search",details:"Details",Card_status:"Card Status",chose_status:"Please Select Card Status",exporttb:"Export",card_number:"QTY",addtime:"Import Time",action:"Operation",usedstate:"Status",cardtype:"Card Type",Code:"Verification Code:",PhysicalSIM:"Physical SIM",eSIM:"ESIM",TSIM:"TSIM",showiccid:"ICCID List",Storagetime:"Date",attributableChannel:"Channel",transfer:"Transfer",subChannel:"Sub-channel",subChannelProviderEmpty:"Sub-channel is mandatory",availablePackages:"Are there any packages available",whitelistPackage:"Whitelist Package",WhitelistPackageID:"Whitelist package ID"},buymeal:{manual_batch:"Single Purchase",file_batch:" Batch Upload",chose_meal:"Selected Package",input_number:"ICCID",chose_mealtext:"Please Choose A Set Meal",chose_number:"ICCID",confirm:"Submit Order",Reset:"Reset",HK_dollar:"Hong Kong Dollar",payment:"Order Amount",mealname:"Package Name",search:"Search",input_mealname:"Fill In Package Name",Selectall:"Set All",Deselectall:"Deselect All",upload:"Upload",Country:"Country/Region",amount:"Original Package Amount",selectCountry:"Select Country/Region",Download:"Download File Template",Downloadmsg:"Only Support Csv, And FIle Size Cannot Over 5M",Choosemeal:"Please Select The Package!",cycletype:"Package Type",cycleNumber:"Package Cycle",filename:"Export Uploaded File",time:"Uploaded Time",chooseprice:"Please Choose A Package To Get A Discounted Price!",choose:"Select",hour:"24hours",day:"By day",month:"By month",year:"By year",cday:"By day",cmonth:"By month",cyear:"By year",Failedfile:"Export Failed Result",clickdownload:"Click To Download",tasksTotal:"Total Uploaded Record",successes:"Succeed",failed:"Failed",Nofailed:"No Failed Files",taskview:"Historical Task View",Taskstatus:"Task Status",Processing:"Loading",completed:"Completed",templatename:"File Template",Insufficient:"Insufficient Deposit, Please Recharge Before Buying",purchase:"Successful Purchase",toupload:"Please Select The File To Upload!",fileformat:"Incorrect File Format",incorrect:"The Format Is Incorrect, Please Upload A .Csv File",Filesize:"File Size Exceeds Limit",Exceeds:"Exceeds The Maximum Limit Of 5MB",fileresult:"Failed Result",Uploadfile:"Upload"},order:{mealname:"Package Name",input_mealname:"Fill In Package Name",chose_number:"ICCID",input_number:" ICCID",timeslot:"Period",chose_time:"Please Select",search:"Search",monthly_bill:"View Monthly Bill",exporttb:"Export",month:"Month",chose_month:"Please Select Month",expenditure:"Total Expenditure",order_number:"Order Number",order_state:"Order Status",count:"QTY",order_money:"Order Amount",addtime:"Creation Time",isused:"Is The Package Used",action:"Operation",unsubscribe:"Unsubscribe",ifunsubscribe:"Confirm To Unsubscribe This Item",channels:"Method",Website:"Website",BulkOrder:"Bulk Order",Trial:"Trial",Testing:"Testing",Datapool:"Data Pool",ActivationTime:"Activation Time",LocationUpdate:"Latest Location Update",BeijingMobile:"Beijing Mobile",issuance:"Cooperative Card Issuance",Postpaid:"Post-paid Card Issuance",Normal:"Normal",Suspend:"Terminated",Expired:"Expire",Terminated:"Suspend",WeChat:"WeChat Public Account",yes:"Yes",no:"No",delivered:"To Be Delivered",Completed:"Completed",Cancelled:"Cancelled",approval:"Activate Unsubscribe Pending Approval",Recycled:"Recycled",Numberform:"Number Registration Form.txt",Unsubscribe:"Unsubscribe Successfully",channelOrderMoney:"Channel Order Amount"},support:(i={cardtype:"Main Card Type",chose_type:"Please Select Card Type",cardstate:"SIM status",chose_state:"Please choose SIM status",pause:"Pause",input:"Please Input",search:"Search",mealList:"Package Details",mealname:"Package Name",input_mealname:"Fill In Package Name",timeslot:"Period",chose_time:"Please Select",used_details:"Usage Details",activation:"Activation",frozen:"Frozen",action:"Operation",time:"Package End Date",Verification_Code:"Send Verification Code",Activation:"H IMSI Activation Method",isused:"Is It Used",meal_time:"Package Valid Date",cmeal_time:"Package Valid Date",Activation_state:"Package Status",used_flow:"Usage Flow",used_cycle:"Life Cycle",Report_time:"Latest Location Time",Report_address:"Reporting Location",Targeting:"H/V IMSI",template:"SMS",position:"Current Location",Locationrecord:"Location Update",SendSMS:"SMS",Flowdetails:"Data Usage",Packagestatus:"Package Status",Activationtype:"Activation Type",Ordernumber:"Order Number",Ordertime:"Order Date",Orderchannel:"Channel",Activationmethod:"Activation Type",sendingmethod:"Sending Method",chosesend:"Please Select The Sending Method",phone:"MSISDN",phoneprompt:"Please Enter Phone Number",chosetemplate:"Please Select SMS Template",sending:"Send SMS",usedetails:"Historical Usage Details",date:"Date",useflow:"Data Usage(G)",close:"Shut Down",Periodtype:"Package Type",Continuouscycle:"Package Cycle",Activatepackage:"Activate",MSISDNenter:"Please Enter MSISDN",ICCIDenter:"Please Enter ICCID",IMSIenter:"Please Enter IMSI",Recycle:"Early Termination",ReplaceVIMSI:"Replace VIMSI",Automatic:"Auto",Manual:"Manual",Sendingempty:"Sending Method Cannot Be Empty",SMSempty:"SMS Template Cannot Be Empty",Phoneempty:"Phone Number Can Not Be Blank",PhoneWrong:"Wrong Format Of Phone Number",Unuse:"Unuse",InUse:"In Use",Expired:"Expired",Activatedpending:"Activated Pending",Activating:"Activating",activated:"Activated",Used:"Used",CNY:"CNY",USD:"USD",HKD:"HKD",VIMSILocation:"VIMSI Location Update Details",Cardtype:"Card Type",Flowg:"Data Usage(G)",VIMSIphone:"VIMSI Number",TimeLocation:"Time",Location:"Location",Termination:"Confirm Early Termination?",replacement:"Confirm Replacement Of VIMSI?",VIMSIdetails:"VIMSI Allocation Details",IMSIdetails:"IMSI online details",targetedAppDetails:"Specific APP online details",cardtraffic:"Query V Card Traffic",QueryH:"Query H Card Traffic",cardflow:"H Card/V Card Traffic",packageflow:"Total Package Traffic/ High Speed Traffic(MB)：",remainingflow:"Current Remainging Traffic(MB)：",Usedtraffic:"Used Traffic(MB)：",Usedflow:"Used Traffic",countryregion:"Country/Region",flowquery:"Usage Query",back:"Return",Vcard:"VIMSI ",Hcard:"HIMSI ",searchcondition:"Please Fill In At Least One Search Condition",obtained:"The List Failed To Load Because The Current Card Position Was Not Obtained",advance:"The Package Has Expired And Cannot Be Recycled In Advance",Sentsuccessfully:"Sent Successfully",SIMDate:"SIM Expiry Date",Locationexport:"Location Update Export",recordexport:"Data Usage Details Export",operationFailed:"The Operation Failed, Only The In-use State Package Is Supported",VoperationFailed:"The VIMSI Cannot Be Replaced Because The Current Card Position Has Not Been Obtained",usageTotal:"Total data usage",operatorName:"Operator",Complete:"Complete ID Registration",registration:"ID Registration Details",IDnumber:"ID Number:",IDtype:"ID Type:",Issuingcountry:"Issuing Country:",approval:"Pending Approval",process:"Approval In Process",Approved:"Approved",Rejected:"Rejected",IDexpired:"ID Expired",registrationrules:"ID Registration Rules",registrationcountry:"ID Registration Country",Passport:"Passport",Permit:"Exit/Entry Permit For Travelling To And From Hong Kong And Macau",HKIdentityCard:"Hong Kong Identity Card",MacauIdentityCard:"Macau Identity Card",view:"View",DataUsedDay:"Data Used For The Day",DataRestrictionType:"Data Restriction Type",DataRestrictionCycle:"Data Restriction In Cycle",DataRestrictionSingle:"Reset by cycle type",ControlLogicLimit:"Control Logic After Data Limit",RestrictedSpeedLimit:"Restricted Speed After Data Limit",ReleaseAfterLimit:"Release After Data Limit",InternetStatus:"Internet Status",Normal:"Normal",RestrictedSpeed:"Restricted Speed",DataCap:"Data Cap",NameChinese:"Name(Chinese):",NameEnglish:"Name(English):",hightDataCap:"High Speed Data Limit",payBills:"Pay Bill",remunerationReturn:"Commission Return",increaseDeposit:"Deposit Topup",PreDeposit:"Prepayment Topup",packageOrder:"Package purchase",fuelPackpackageOrder:"Add-on Pack Purchase",packageCancellation:"Package Refund",fuelPackUnsubscribe:"Add-on Pack Refund",UsedAddonPack:"Data Used For Add-on Pack",ToppedAddonPack:"Data Topped Up For Add-on Pack"},Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"hightDataCap","High Speed Data Limit"),"WaitingUser","Waiting For User To Submit Authentication Information"),"AuthFailed","[Authentication Failed], Wait For The User To Re-submit The Authentication Information"),"NotCertified","Not Certified"),"flowpoolApi","Data Pool API"),"NoCertification","No Certification Required"),"querycontent","Card number does not exist"),"imsiType","IMSI type"),"useCountry","Used destination"),"imsiResource","IMSI provider"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"activeTime","Activation time"),"latestActivationDate","Latest activation time"),"Refunded","Refunded"),"Available","Available operator"),"channelIncomeAdjustment","Channel income adjustment"),"marketingRebate","Marketing rebate"),"imsiFeeStatistics","IMSI fee statistics"),"indemnity","Indemnity"),"errorDesc","Failure reason:"),"nameIsInconsistent","Name mismatch"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"certificateHasExpired","Document expired"),"IDIsInconsistent","Document ID mismatch"),"sixteenyYearsOld","Under 16 years old"),"picturesAreNotSatisfied","Image uploaded unsatisfactory"),"EsimDetails","ESIM Information Enquiry"),"smDpAddress","SM-DP+ address"),"activationCode","Activation code"),"esimStatus","ESIM status"),"eid","EID"),"installationTime","Installation time"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"installationEquipment","Installation device"),"instalAmount","Number of installation"),"updateTime","Update time"),"generateQRCode","Generate QR code"),"msisdnImsiIccid","Please select msisdn, imsi or iccid！"),"timeFrame","Time range"),"orderBatch","Order batch"),"inputOrderBatch","Please input order batch"),"segmentSearch","Number range search"),"startNumber","Starting number"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"endNumber","Ending number"),"codeExport","Export QR code"),"fileExport","Export file"),"xiazai","Download"),"operate","Operate"),"inputPackageID","Please input package ID"),"edit2","Edit"),"copy","Copy"),"approvalStatus","Approval status"),"newApproval","Waiting approval for creation"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"approve","Approve"),"notApprove","Reject"),"modificationApproval","Waiting approval for modification"),"deleteApproval","Waiting approval for deletion"),"fiveCharacters","Maximum 500 characters"),"fourtCharacters","Maximum 4000 characters"),"packageDescription","Package description"),"selectType","Please select cycle type"),"selectDuration","Please select duration"),"packageValidity","Package validity"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"inputPackageValidity","Please input package validity"),"selectDataResetType","Please select data reset type"),"selectRestrictionLogic","Please select restriction logic"),"aupportHotspot","Support hotspot sharing"),"isAupportHotspot","Hotspot sharing ON/OFF?"),"usage2","Usage"),"selectUsage","Please select IMSI provider"),"inputUsage","Please enter IMSI provider"),"wrongUsageFormat","Please enter an integer between 1 and 10 digits"),"speedTemplate","Speed template"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"selectSpeedTemplate","Please select speed template"),"unlimitedUsageTemplate","Unlimited usage template"),"selectUnlimitedUsageTemplate","Please select unlimited usage template"),"packageNameNotNull","Package name is mandatory"),"packageDescriptionNotNull","Package description is mandatory"),"prongDurationFormat","Wrong duration format"),"DurationValueLarge","Duration value is too large"),"wrongPackageValidity","Wrong package validity"),"packageValidityLarge","Package validity is too large"),"qrPicture","QR code"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"pakageId","Package ID"),"day","Day"),"add","Add"),"create","Create"),"createPackage","Create package"),"operator","Operator"),"network","Network Type"),"subChannelName","Sub-channel Name"),"contactEmail","Contact email"),"purchasePackage","Package available for purchase"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"profitMargin","Profit rate"),"totalAmount","Credit Limit"),"accountPermissions","Account permissions"),"channelAppSecret","channel App Secret"),"channelAppKey","channel App Key"),"selectCharacter","Select role"),"submit","Submit"),"clickToUpload","Upload"),"downloadfile","Download template"),"pleaseUploadFile","Please upload"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"subChannelEmpty","Sub-channel name is mandatory"),"subChannelAmpersand","Sub-channel can't include symbol '&'"),"contactEmailEmpty","Contact email address is mandatory"),"EmailFormatError","Contact email address wrong format"),"fuelPackProfitMarginEmpty","Add-on pack margin is mandatory"),"totalAmountEmpty","Total quota is mandatory"),"accountPermissionsEmpty","Account permission is mandatory"),"subChannelDetails","Sub-channel details"),"editSubChannel","Edit sub-channel"),"addSubChannel","Create sub-channel"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"uploadFailed","Upload failed!"),"files","Document"),"fileUploadedAndProgressDisappears","Document is uploading, please wait until the progress bar disappears"),"downTemplateFilelAndUpload","Please download the template first, then upload after fill in"),"imsi","IMSI"),"packageProfitMargin","Package profit rate"),"fuelPackProfitMargin","Add-on pack profit rate"),"failureReason","Reason for failure"),"supportAddon","Support add on"),"bindAddon","Bind add on"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"AddonList","Add on list"),"newAddon","Create add-on pack"),"AddonName","Add on pack name"),"AddonID","Add on pack ID"),"AddonAmount","Add on data amount"),"AddonListMandatory","Add on list is mandatory"),"createOrder","Create order"),"revoke","Revoke"),"downloadInvoice","Download Invoice"),"downloadList","Download list"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"uploadPayslip","Upload payslip"),"customizedSIM","Customized SIM"),"PhysicalSIM","Physical SIM"),"receiveeAddress","Delivery Address"),"receiverName","Contact Name"),"contactNumber","Contact Number"),"distribution","Distribution"),"atoz","A~Z"),"cooperationModel","Cooperation Model"),"payslip","Payslip"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"uploadPicture","Click or drag to upload file"),"pictureSize","Exceeds The Maximum Limit Of 10MB"),"paymentMethod","Payment method"),"deliveryCompany","Delivery company"),"trackingNumber","Tracking number"),"ordered","Ordered"),"cancelled","Cancelled"),"pendingPayment","Pending for payment"),"paid","Paid"),"deliveryProgress","Delivery in progress"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"delivered","Delivered"),"deliveryFailed","Delivery failed"),"PaymentMandatory","CMLink/Customized SIM is mandatory"),"cardTypeMandatory","Card type is mandatory"),"cardMumberMandatory","Card quantity is mandatory"),"wrongFormat","Wrong format"),"addressMandatory","Delivery address is mandatory"),"receiverMandatory","Contact name is mandatory"),"contactNumberMandatory","Contact number is mandatory"),"cooperationMandatory","Cooperation model is mandatory"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"picetureMandatory","File is mandatory"),"confirmRevocation","Confirm revert?"),"paymentMethod","Payment method"),"description","Description!"),"newAddonPack","Add-on Pack"),"determine","Confirm"),"flowValue","Add on data amount"),"addAmountMandatory","Add on data amount is mandatory"),"cannotExceed","Exceeds The Maximum Limit Of 104857600MB"),"generateInvoice","Generate Invoice Time"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"uploadPayslipTime","Upload payslip time"),"orderConfirmTime","Order confirmation time"),"deliveryTime","Delivery Time"),"paymentConfirmed","Pending payment confirmed"),"PaymentNotConfirmed","Payment not yet receive"),"physicalOrCustomized","CMLink/Customized SIM"),"inputFlowValue","Please Enter Add on data amount"),"inputAddonName","Please Enter Add on pack name"),"inputAddonId","Please Enter Add on pack ID"),"inputCardNumber","Please enter card quantity"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"inputRecrver","Please enter contact name"),"inputAddress","Please enter delivery address"),"selectCardtype","Please Select Card Type"),"productPackaging","Packaging"),"selectProductPackaging","Please select packaging"),"productPackagingEmpty","Packaging is mandatory"),"packagingCard","SIM with package"),"nakedCard","SIM without package"),"receivingCountry","Receiver country"),"selectReceivingCountry","Please select receiver country"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"receivingCountryEmpty","Receiver country is mandatory"),"postalCode","Postal code"),"selectPostalCode","Please input postal code"),"postalCodeEmpty","Postal code is mandatory"),"language","Language"),"selectLanguage","Please select language"),"languageEmpty","Language is mandatory"),"agreement","Please agree the agreement first"),"agree","I agree this "),"Agreements","agreement"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"orderId","Order ID"),"replaceHIMSI","Swap HIMSI"),"isSupportHot","Current internet access supports hotspot"),"internetTemplateSpeed","Current speed template"),"AgreementContent","Agreement"),"resourceCooperation","Resource Cooperation"),"coverageTime","Coverage time"),"informationQuery","Information query"),"cdrDetails","CDR details"),"moreInformation","More information"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(i,"dataInformation","Data information"),"rollbackInProgress","Rollback in progress"),"rollbackSuccess","Rollback success"),"rollbackFailure","Rollback failure"),"emptyData","Package information not found"),"validityPeriod","Real Name Registration Validity"),"longTerm","Long-term Validity"),"singlePackageEffective","Single Package Effective")),directionalApp:{application:"APP",freeFlow:"Exception",supportUsage:"Support specific APP data usage",selectSupportUsage:"Please select support specific APP data usage or not",selectAPP:"Select APP",pleaseSelectAPP:"Please select APP",deleteAPP:"Delete APP",addAPP:"Add APP",specificAPPLogic:"Specific APP usage logic",inputDataValue:"Please input data value",deleteDataValue:"Delete data value",addDataValue:"Add data value",dataValue:"data value",selectUPCCTemplate:"Select UPCC template",pleaseSelectUPCCTemplate:"Please select UPCC template",continueDataUsage:"Continue to use normal data usage",PleaseDataUsage:"Please select whether continuing using normal data usage",restrictedTemplate:"Specific APP restricted speed template",pleasepRrestrictedTemplate:"Please select specific APP restricted speed template",FreeTemplate:"Specific APP data exception restricted speed template",pleaseFree:"Please select specific APP data exception restricted speed template",FreeContinueTemplate:"Specific APP data exception continue using data speed template",pleaseFreeContinue:"Please select specific APP data exception continue using data speed template",SupportDataMandatory:"Support specific APP data usage is mandatory",number9:"The number of selected APP can't exceed 9",APPMandatory:"Select APP is mandatory",LogicMandatory:"Specific APP usage logic is mandatory",folwUpcc:"Data value and 和 UPCC template",valueMandatory:"Data value is mandatory",twoTemplate:"Specific APP data exception restricted speed template or Specific APP data exception continue using data speed template  is mandatory",ContinueMandatory:"Continue to use normal data usage is mandatory",upccMandatory:"Select UPCC template is mandatory",valueRepeat:"Data value can't repeat",appRepeat:"APP can't repeat",dingTemMandatory:"Specific APP restricted speed template is mandatory",useTemMandatory:"Specific APP data exception continue using data speed template is mandatory",freeTemMandatory:"Specific APP data exception restricted speed template is mandatory",gearRule:"Data value is incorrect. The data value of each level must be greater than the data value of the previous level.",usageRule:"Usage logic is incorrect. The usage value of each level must be greater than the usage value of the previous level."},address:{deleteitem:"Confirm To Delete This Item?",fullname:"Full Name",input_fullname:"Please Enter Your Name",mailbox:"Mailbox",input_mailbox:"Please Fill In Email Address",Newaddress:"New Address",modify:"Modify",modifyaddress:"Reset Mailbox",Delete:"Delete",Forgetpwd:"Reset Password",setdefault:"Set Default",search:"Search",password:"Password",password_ok:"Confirm Password",input_pwd:"Please Input A Password",newpwd:"New Password",account:"Account Name",action:"Operation",oldPwd:"Original Password",PwdRules:"When Setting The Password, You Need To Meet The Given Rules, Click  ",watch:"View  ",more:"Learn More",Rules1:"Password Character Policy:",Rules2:"1. The password must be 8 characters or more and contain at least one uppercase character, at least one lowercase character, at least one number and at least one special symbol;",Rules3:"2. The password shall not contain any three identical consecutive (ABC, Abc, 123, !@# etc) and repetitive characters (AAA, Aaa, 111, ### etc)",Rules4:"3. The factory default password of the system or device should be replaced, such as huawei:huawei@123, SYS:CHANGE_ON_INSTALL in the oracle database, and the default account of a mobile customized version of optical modem CMCCAdmin:aDm8H%MdA, etc.;",Rules5:"4. The password setting should avoid keyboard sorting passwords of more than 3 digits (including 3 digits), such as qwe (the first three letters of the first row of the keyboard), asd (the first three letters of the second row of the keyboard), qaz (the first three letters of the keyboard) Letters), 1qaz (the number in the first column of the keyboard plus the first three letters),! QAZ (Special characters in the first column of the keyboard plus the first three letters), etc.;",Rules6:"5. No more than 3 (including 3) consecutive letters, numbers, and special characters, such as ABC, Abc, 123,! @#Wait;",Rules7:"6. No more than 3 (including 3) repeated letters, numbers, and special characters, such as AAA, Aaa, 111, ###, etc., can appear in the password.",Rules8:"Avoid the following easy-to-guess password rules",Rules9:"1. Province, city name, email address, telephone area code, postal code and abbreviation, and simple numbers or shift key + simple numbers, such as BJYD123, HBYD!@#, etc.;",Rules10:"2. Unit name, professional name, system name, manufacturer name (including abbreviations) and simple numbers, such as HBnmc123, HBsmc_123, etc.;",Rules11:"3. The name of the maintenance personnel is spelled out, uppercase and lowercase abbreviations, etc. + device IP address (one or two) or date of birth, etc., such as maintenance personnel Zhang San, maintenance equipment address ************ and ************, date of birth If it is 19951015, the possible weak passwords are zhangsan100, zhangsan101, zhangsan10100, zhangsan10101, zhangsan19951015, ZS19951015, etc.;",Operationreminder:"Operation Reminder",deleted:"Successfully Deleted!",inconsistent:"The Two Passwords Are Inconsistent",emailaddress:"Please Enter A Valid Email Address",reset:"Please Reset",appear:"3 Consecutive And Identical Characters Cannot Appear",allowed:"3 Consecutive Digits Are Not Allowed",determine:"Submit"},flow:(r={inputICCID:"Please Enter ICCID",Channel:"Channel",Status:"Status",chooseStatus:"Please Choose Status",toassigned:"To Be Assigned",Assigned:"Assigned",typelimit:"Monocycle Type Limit",Totallimits:"Total Limit",Controllogic:"Control Logic",Stoplimit:"Stop After Data Limit",speedlimit:"Restricted Speed After Data Limit",Continuelimit:"Continue After Data Limit",Originlimit:"Origin Data Pool",inputPoolname:"Please Enter Data Pool Name",poolName:"Data Pool Name",Usagestatus:"Usage Status"},Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(r,"chooseStatus","Please Choose Usage Status"),"Normal","Normal"),"Restricted","Restricted Speed"),"Stop","Stop"),"Pleasechoose","Please Choose"),"upStatus","Status"),"Online","Online"),"Offline","Offline"),"Useddata","Data used"),"Numbericid","Number Of ICCID"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(r,"threshold","Alert Threshold"),"ImportICCID","Import ICCID"),"Batchdelete","Batch Delete"),"Batchupdate","Batch Edit"),"ImportTime","Import Time"),"Singleimport","Single Import"),"Batchimport","Batch Import"),"Choosepool","Choose Data Pool"),"plesepool","Please Choose Data Pool"),"ICCIDempty","ICCID Cannot Be Empty"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(r,"Monocyleempty","Monocyle Type Limit Cannot Be Empty"),"Totalempty","Total Limit Cannot Be Empty"),"choosecontrollogic","Please Choose Control Logic"),"Unit","Unit"),"units","Unit"),"UploadICCID","Upload ICCID list"),"iccidFileEmpty","ICCID List Cannot Be Empty"),"Batchtime","Batch Import Time"),"Importtotal","Import Total"),"Numbersuccess","Number Of Success"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(r,"Numberfailure","Number Of Failure"),"Successfile","Success File"),"Failurefile","Failure File"),"Usagethreshold","Usage Alert Threshold"),"Percentage","Percentage"),"Usageempty","Usage Alert Threshold Cannot Be empty"),"Usagerecord","Usage Record"),"Choosemonth","Choose Month"),"Choosedate","Invoice period"),"PleaseChoosedate","Select Period"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(r,"Pleasemonth","Please Choose Month"),"Billmonth","Bill Month"),"UsagestartTime","Usage Start Time"),"UsageendTime","Usage End Time"),"Ratedtotalusage","Rated Total Usage"),"Actualtotalusage","Actual Total Usage"),"Excessusage","Excess Usage"),"Ratedcharge","Rated Charge ($)"),"Excesscharge","Excess Charge ($)"),"Totalcharge","Total Charge ($)"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(r,"Details","Details"),"Clickview","Click To View"),"IMSI","HIMSI"),"VIMSI","VIMSI"),"Numbertype","Number Type"),"StartTime","Start Time"),"EndTime","End Time"),"Usage","Usage"),"totalusage","Total Usage"),"year"," Year "),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(r,"month"," Month "),"dday"," Day "),"flowyear","Year"),"flowmonth","Month"),"Totallimit","Total Limit"),"Usagedetails","Usage Record Details"),"Pleasethreshold","Please Enter Usage Alert Threshold"),"Pleaseinteger","Please Enter A Positive Integer"),"Validdate","Valid Date"),"Resetnumber","Reset Cycle Number"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(r,"Resettype","Reset Cycle Type"),"Monocyletype","Monocyle Type Limit"),"Cardcycle","Card Cycle (Restricted Speed After Data Limit)"),"Stopdatalimit","Card Cycle (Stop After Data Limit)"),"Restrictedspeed","Total No Of Cards (Restricted Speed After Data Limit)"),"Totallimitcard","Total No Of Cards (Stop After Data Limit)"),"Datapoollimit","Total no of Data pool (Restricted speed after data limit)"),"stoppoollimit","Total no of Data pool (Stop after data limit)"),"chooserecord","Please Choose At Least 1 Record"),"Confirmdelete","Confirm Delete"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(r,"Dataused","Data Used"),"Originenterprise","Origin Enterprise"),"Positivenumber","Positive Number With Maximum 8 Digits And 2 Decimal Places"),"yearandmonthdate","Invoice date"),"day","Day"),"Country","Supporting Country"),"deleteNumber","Delete"),"poolAvailableTime","Available day to enter pool"),"fillNumber","Enter available day"),"exportflowsum","Export data usage summary"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(r,"remark","Remark"),"inputRemark","Please enter remark"),"enterRemark","Enter remark"),"internetStatus","Online status"),"inputinternetStatus","Please choose online status"),"recover","Resume"),"cardManager","SIM management"),"mb","MB"),"totallimit","Total data limit"),"availableday","Available day"),Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(Object(u["a"])(r,"none","None"),"confirmPause","Confirm Pause"),"confirmResume","Confirm Resume"),"kongge","Spaces are not allowed"),"expirationDate","Expiry date"),"SelectDestination","Select destination"),"select","query"),"usageMB","Usage (MB)"),"requiredDataExport","Please select the required data to export"),"chooseOne","Please select at least one"),Object(u["a"])(r,"negative","Can't be negative number")),fuelPack:{startDate:"Package Start Date",endDate:"Package End Date",packagedata:"Current Package Data (MB)",adddata:"Purchased Add-on Data (MB)",usedhigh:"Used High Speed Data (MB)",Purchase:"Purchase Add-on",SelectfuelPack:"Select Add-on",PleaseSelectfuelPack:"Please Select Add-on",quantity:"Select Add-on Quantity",Currentday:"Current Day Add-on",Daily:"Daily Add-on",CurrentMonthly:"Current month add on pack",Monthly:"Monthly add on pack",CurrentYear:"Current year add on pack",Yearly:"Yearly add on pack",Amount:"Amount",onlinestatus:"Current Online Status",purchasequantity:"Please Input Purchase Quantity",Specificdate:"Specific Activation Date",SelectDate:"Select Date",PleaseSelectDate:"Please Select Date",activationType:"Package Activation Type",Activatelimit:"Activate After Data Limit",ActivateSpecific:"Activate After Specific Date",ActivateLU:"Activate After LU",packageTtart:"Select Package Start Date",activationdate:"Specific Activation Date",buyfuelPack:"Please double confirm before submit - It is effective immediately and non-refundable once Add-on pack purchased?",price:"Price",Remainingdays:"Remaining Days:"},selectCooperationMode:"Please select cooperation model",consignmentSalesModel:"Distribution Model",A2Zmode:"A~Z Model",resourceMode:"Resource cooperation Model",welcomeWebsite:"Welcome to Global Data SIM Portal",totalCards:"Total cards",announcement:"Notice",esimTotal:"Total number of eSIM",tsimTotal:"Total number of TSIM",imsiTotal:"Total number of IMSI",imsika:"IMSI",shitika:"Physical SIM",simTotal:"Total number of physical SIM",quotaUsedMonth:"Credit Used in Bill Cycle",AtoZTotal:"Credit Limit",creditsUsed:"Credit Used in Bill Cycle",unused:"Unused",usedLimit:"Credit Used",yuan:"Dollars",packageOrdered:"Number of package ordered",months12:"Data for last 12 months",twofivebefore:"Before the 25th: from the 25th of the previous month to present",twofiveafter:"After the 25th: from the 25th of this month to present",esimkazhang:"ESIM Card(s)",imsikazhang:"IMSI(s)",simkazhang:"Physical SIM Card(s)",cardkazhang:"Total Card(s)",resourceManage:{channelName:"Channel Name",enterChannelName:"Please enter channel name",allocateResources:"Resource allocation",resourceView:"Resource",resourceSupplier:"Resource Provider",selectResourceSupplier:"Please select resource provider",imsiNumber:"Number of IMSI",selecyImsiNumber:"Please enter IMSI quantity",routingID:"Route ID",selectRoutingID:"Please enter route ID",resourceSupplierMandatory:"Resource provider is mandatory",imsiNumberMandatory:"IMSI quantity is mandatory",routingIDMandatory:"Route ID is mandatory",selectVIMSIphone:"Please enter IMSI",batchFreeze:"Batch Freeze",BatchRecovery:"Batch Resume",seeInformation:"View information",modifyingResources:"Modify Resources",BatchModifyingResources:"Batch Edit Resources",FreezeItem:"Confirm to freeze this item?",RecoverItem:"Confirm to resume this item?",confirmFreeze:"Confirm Freeze?",confirmRecover:"Confirm Resume?",operationFail:"Failed operation",Dimension:"Select Statistics dimension",selectDimension:"Please select statistics dimension",dateOrMonth:"Date/Month",fee:"Fee",imsiPhone:"IMSI",selectImsiPhone:"Please enter IMSI",routingExceeds:"Exceeds The Maximum Limit Of **********",numberExceeds:"Exceeds The Maximum Limit Of 100000"},channelBill:{startMonth:"Start Month",endMonth:"End Month",selectStart:"Please select start month",selectEnd:"Please select end month",dataUsage:"Data usage charges",inputChargesName:"Please input charges name",imsiFee:"IMSI fee",detailed:"Details",billFileDownload:"Invoice details download",invoiceDownload:"Invoice download",payslip:"Payment",reUpload:"Upload again",dataFee:"Data usage fee",cardFee:"Card fee",packageFee:"Package fee",paymentPage:"Payment page",inputAmount:"Please input amount",checkNumber:"Positive Number With Maximum 8 Digits And 2 Decimal Places",imsiFeeType:"IMSI fee type",imsiFeeAmount:"IMSI fee amount",quantityRange:"Quantity range",chargesName:"Charges name",country:"Country",cny:"CNY",hkd:"HKD",usd:"USD",billId:"Invoice ID",billType:"Invoice type",paymentMonth:"Bill month",totalBillAmount:"Invoice amount",accountsPayableAmount:"Invoice balance",paymentStatus:"Payment status",verified:"Written off",unpaidPayment:"Unpaid",confirmationReceipt:"Pending payment confirmed",Arrived:"Payment successful",NotCredited:"Payment failed",merge:"Combine",endGreaterStart:"End month should be the same/larger than start month!",lessThan0:"Cannot Be Less Than 0",onlinePayment:"Online payment",offlinePayment:"Offline payment",OnlinePaymentInProgress:"Online payment in progress",cnInvoice:"CN Invoice",uploadCnInvoice:"Upload CN Invoice",cnInvoiceDownload:"CN Invoice Download",pleaseUploadCnInvoice:"Please upload CN Invoice file"},onlineOrder:{orderUniqueId:"Order ID",orderName:"orderName",orderType:"Order type",corpId:"Channel ID",orderUserName:"Purchaser name",productId:"Product ID or Invoice ID",thirdOrderNo:"Payment GW order identification",thirdMchorderNo:"Payment GW order number",thirdTransactionNo:"Payment GW transaction number",currencyCode:"Currency",amount:"Amount",orderStatus:"Order Status",paymentMethod:"Payment method",paymentStatus:"Payment status",paymentTime:"Payment time",paymentIp:"User IP",paymentReference:"paymentReference",exprieTime:"Payment deadline",asyncNotifyType:"Asynchronous result notification type",sendLang:"Language",isDeleted:"Logic remove marker",createTime:"Creation Time",updateTime:"Update time",email:"Email",choosePaymentStatusPlaceholder:"Please select order status",thirdOrderNoPlaceholder:"Please input payment GW order identification",thirdTransactionNoPlaceholder:"Please input payment GW transaction number",thirdMchorderNoPlaceholder:"Please input payment GW order number",chooseCreateDate:"Please select create period",onlineModalTitle:"Online payment",payTxt01:"Transaction will be closed in ",payTxt02:"",payBtn:"Pay",orderTypeBill:"Bill payment",orderTypedeposit:"Deposit topup",wechat:"Wechat",alipay:"Alipay",card:"Bank card",paying:"Pending payment",paySuccess:"Payment success",payExpired:"Order expired",payclosed:"Order closed",closeOrderContent:"Confirm to close the order?",deleteOrderContent:"Confirm to delete the order?",deleteSuccess:"Successfully Deleted!",closeSuccess:"Closed successfully",paidAmount:"Actual paid amount：",depositAmount:"Deposit amount：",weChatPayChina:"WeChat Pay (Mainland China version)",alipayChina:"Alipay (Mainland China version)",debitCreditCard:"Debit card/Credit card",depositAmountPlaceholder:"Please input deposit amount",loadingStatus:"Loading",correctDepositAmount:"Please input correct amount",depositAmountGreaterThanZero:"Amount should be larger than 0",selectPaymentMethod:"Please select payment method",correctBankCardInfo:"Please input correct bank information",amountCannotBeEmpty:"Amount is mandatory",validPositiveNumber:"Please input valid number (2 decimal places)",positiveNumberGreaterThanZero:"Please input positive number larger than 0",stateIsValid:"state.isValid:",paymentMethodType:"state.data.paymentMethod.type:"},paymentResultpageTexts:{paymentFailed:"Warning",paymentSuccessful:"Warning",errorDetails:"Payment has been submitted, please click the button to view payment details.",successDetails:"Payment has been submitted, please click the button to view payment details.",errorReason:"Failure reason",thanksMessage:"Thank you for your support",viewOrder:"Check orders",goToHome:"Back to main page"},offlinePay:{applyInvoice:"Apply invoice",pay:"Pay",reApplyInvoice:"Re-apply invoice",rePay:"Re-upload payment advice",invoiceType:"Invoice type",topupID:"Top up ID",invoiceNumber:"Invoice number",deposit:"Deposit",Prepayment:"Prepayment",applyTime:"Apply time",unpaid:"Unpaid",invoicePendingApro:"Invoice pending approval",invoiceAproReject:"Invoice approval rejected",payable:"Payable",payPendingApro:"Payment pending approval",paymentAproReject:"Payment approval rejected",paid:"Paid",attachment:"Attachment",onlineTopup:"Online top up",offlineTopup:"Offline top up",topup:"Top Up",topupAmount:"Top up amount"},country:{select:"Select destinations",selectAll:"Select all",selected:"Selected destinations",nameCn:"Destination name (Chinese)",nameEn:"Destination name (English)",continentCn:"Continent (Chinese)",continentEn:"Continent (English)",specialRuleMngr:"Special destination rules management",all:"All",noData:"No Data",viewAll:"View All Countries",selectingContinent:"Selecting current continent destinations...",deselectingContinent:"Deselecting current continent destinations...",getContinentFail:"Failed to get continent list",getCountryFail:"Failed to get country list",getAllCountryFail:"Failed to get all country data",alreadySelected:"Already selected destination: ",operationFail:"Operation failed: ",editCountry:"Edit Country"},sessionInfo:{sessionDetail:"Session Details",realtimeSession:"Real time Session Information",historySession:"Historical Session Information",field:"Fields",currentVlaue:"Current Value",accessSite:"Access Site",online:"Online",sessionStartTime:"Session Start Time",dataUsageDuringSession:"Traffic generated within a session",serviceProviderUsed:"Operators used",userIPAddress:"User's IP address",PGWSiteAccessedSession:"PGW site accessed by the session",sessionQuery:"Session Query",UsageMax:"Usage Exceeds The Maximum Limit Of ",UsageMin:"Usage Cannot Be Less Than 10MB"}},h=n("cea3"),p=n("641c");n("97b7");s["default"].use(l["a"]);0===navigator.language.indexOf("en")||navigator.language.indexOf("zh");var f=localStorage.getItem("local"),g=("zh-CN"===f||"en-US"===f)&&f,b=g||"en-US";s["default"].config.lang=b,s["default"].locale=function(){};var k={"zh-CN":Object.assign(h["a"],d),"en-US":Object.assign(p["a"],m)},y=new l["a"]({locale:b,messages:k,silentTranslationWarn:!0});t["a"]=y}});