(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0b30fc26"],{"466d":function(e,t,o){"use strict";var a=o("c65b"),n=o("d784"),i=o("825a"),r=o("7234"),s=o("50c4"),l=o("577e"),c=o("1d80"),d=o("dc4a"),u=o("8aa5"),p=o("14c3");n("match",(function(e,t,o){return[function(t){var o=c(this),n=r(t)?void 0:d(t,e);return n?a(n,t,o):new RegExp(t)[e](l(o))},function(e){var a=i(this),n=l(e),r=o(t,a,n);if(r.done)return r.value;if(!a.global)return p(a,n);var c=a.unicode;a.lastIndex=0;var d,f=[],m=0;while(null!==(d=p(a,n))){var h=l(d[0]);f[m]=h,""===h&&(a.lastIndex=u(n,s(a.lastIndex),c)),m++}return 0===m?null:f}]}))},"4b86":function(e,t,o){"use strict";o.d(t,"d",(function(){return i})),o.d(t,"c",(function(){return r})),o.d(t,"b",(function(){return s})),o.d(t,"a",(function(){return l})),o.d(t,"g",(function(){return c})),o.d(t,"e",(function(){return d})),o.d(t,"f",(function(){return u}));var a=o("66df"),n="/cms",i=function(e){return a["a"].request({url:n+"/channelSelfServer/deposit/info",params:e,method:"get"})},r=function(e){return a["a"].request({url:n+"/channelSelfServer/deposit/record",data:e,method:"post"})},s=function(e){return a["a"].request({url:n+"/IBoss/downLoad3",params:e,method:"get",responseType:"blob"})},l=function(e){return a["a"].request({url:n+"/IBoss/downLoad/paymentProof",params:e,method:"get",responseType:"blob"})},c=function(e){return a["a"].request({url:n+"/IBoss/payBill",data:e,method:"POST",contentType:"multipart/form-data"})},d=function(e){return a["a"].request({url:n+"/channelSelfServer/deposit/applyInvoice",data:e,method:"post"})},u=function(e){return a["a"].request({url:n+"/channelSelfServer/deposit/cancelInvoice/".concat(e,"/"),method:"put"})}},a874:function(e,t,o){"use strict";o.r(t);o("caad"),o("b0c0");var a=function(){var e=this,t=e._self._c;return t("Card",[t("div",{staticStyle:{width:"100%","margin-top":"50px",margin:"auto"}},[t("div",{staticStyle:{display:"flex",width:"100%","align-items":"center"}},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"addInvoice",expression:"'addInvoice'"}],staticStyle:{margin:"0 2px","margin-left":"20px"},attrs:{type:"primary",icon:"md-add",disabled:"3"==e.cooperationMode},on:{click:function(t){return e.addInvoice(null,"1")}}},[e._v(e._s(e.$t("offlinePay.applyInvoice")))])],1),t("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:e.columns,data:e.data,border:"",loading:e.loading},scopedSlots:e._u([{key:"file",fn:function(o){var a=o.row;o.index;return[t("Button",{directives:[{name:"show",rawName:"v-show",value:["4","5","6","7","9"].includes(a.chargeStatus),expression:"['4','5','6','7','9'].includes(row.chargeStatus)"},{name:"has",rawName:"v-has",value:"invoice",expression:"'invoice'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"warning",ghost:"",size:"small"},on:{click:function(t){return e.downloadInvoice(a)}}},[e._v("Invoice")]),t("Button",{directives:[{name:"show",rawName:"v-show",value:["5","6","7"].includes(a.chargeStatus)&&null!=a.paymentProofAddress&&""!=a.paymentProofAddress,expression:"['5','6','7'].includes(row.chargeStatus)&&row.paymentProofAddress!=null&&row.paymentProofAddress!=''"},{name:"has",rawName:"v-has",value:"payslip",expression:"'payslip'"}],attrs:{type:"info",ghost:"",size:"small"},on:{click:function(t){return e.downloadPaymentProof(a)}}},[e._v(e._s(e.$t("support.payslip")))])]}},{key:"action",fn:function(o){var a=o.row;o.index;return[t("Button",{directives:[{name:"show",rawName:"v-show",value:"1"==a.chargeStatus,expression:"row.chargeStatus == '1'"},{name:"has",rawName:"v-has",value:"revoke",expression:"'revoke'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"error",ghost:"",size:"small"},on:{click:function(t){return e.revokeItem(a)}}},[e._v(e._s(e.$t("support.revoke")))]),t("Button",{directives:[{name:"show",rawName:"v-show",value:"4"==a.chargeStatus,expression:"row.chargeStatus == '4'"},{name:"has",rawName:"v-has",value:"onlinePayment",expression:"'onlinePayment'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"info",ghost:"",size:"small"},on:{click:function(t){return e.onlineRecharge(a)}}},[e._v(e._s(e.$t("channelBill.onlinePayment")))]),t("Button",{directives:[{name:"show",rawName:"v-show",value:"4"==a.chargeStatus,expression:"row.chargeStatus == '4'"},{name:"has",rawName:"v-has",value:"offlinePayment",expression:"'offlinePayment'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",ghost:"",size:"small"},on:{click:function(t){return e.pay(a,"1")}}},[e._v(e._s(e.$t("channelBill.offlinePayment")))]),t("Button",{directives:[{name:"show",rawName:"v-show",value:"3"==a.chargeStatus,expression:"row.chargeStatus == '3'"},{name:"has",rawName:"v-has",value:"reAddInvoice",expression:"'reAddInvoice'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",ghost:"",size:"small"},on:{click:function(t){return e.addInvoice(a,"2")}}},[e._v(e._s(e.$t("offlinePay.reApplyInvoice")))]),t("Button",{directives:[{name:"show",rawName:"v-show",value:"6"==a.chargeStatus,expression:"row.chargeStatus == '6'"},{name:"has",rawName:"v-has",value:"rePay",expression:"'rePay'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",ghost:"",size:"small"},on:{click:function(t){return e.pay(a,"2")}}},[e._v(e._s(e.$t("offlinePay.rePay")))])]}}])}),t("div",{staticStyle:{"margin-left":"38%","margin-top":"50px","margin-bottom":"70px"}},[t("Page",{attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1)],1),t("Modal",{attrs:{title:e.$t("offlinePay.applyInvoice"),"mask-closable":!1,width:400},on:{"on-cancel":e.cancelModal},model:{value:e.addmodel,callback:function(t){e.addmodel=t},expression:"addmodel"}},[t("Form",{ref:"formmodel",attrs:{model:e.formmodel,rules:e.rules,"label-width":120}},[t("div",[t("FormItem",{attrs:{label:e.$t("offlinePay.invoiceType")+"："}},[e._v("\n            "+e._s(e.formmodel.type)+"\n          ")]),t("FormItem",{attrs:{label:e.$t("deposit.currency")+"："}},[e._v("\n          \t"+e._s(e.formmodel.currencyCode)+"\n          ")]),t("FormItem",{attrs:{label:e.$t("fuelPack.Amount")+"：",prop:"amount"}},[t("Input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:e.$t("channelBill.inputAmount")},model:{value:e.formmodel.amount,callback:function(t){e.$set(e.formmodel,"amount",t)},expression:"formmodel.amount"}})],1)],1)]),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.cancelModal}},[e._v(e._s(e.$t("support.back")))]),t("Button",{attrs:{type:"primary",loading:e.addLoading},on:{click:e.addBesure}},[e._v(e._s(e.$t("address.determine")))])],1)],1),t("Modal",{attrs:{title:e.$t("channelBill.paymentPage"),"footer-hide":!0,"mask-closable":!1,width:"450px"},on:{"on-cancel":e.cancelModal},model:{value:e.PaymentModal,callback:function(t){e.PaymentModal=t},expression:"PaymentModal"}},[t("div",[t("Form",{ref:"formobj",staticStyle:{"font-weight":"bold"},attrs:{model:e.formobj,rules:e.ruleobj,"label-width":100,"label-height":100,inline:""}},[t("FormItem",{staticStyle:{"font-size":"14px","font-weight":"bold"},attrs:{label:e.$t("support.payslip")+":",prop:"file"}},[t("Upload",{staticStyle:{width:"250px","margin-top":"50px"},attrs:{type:"drag",action:e.uploadUrl,"before-upload":e.handleBeforeUpload,"on-progress":e.fileUploading},model:{value:e.formobj.file,callback:function(t){e.$set(e.formobj,"file",t)},expression:"formobj.file"}},[t("div",{staticStyle:{padding:"20px 0"}},[t("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),t("p",[e._v(e._s(e.$t("support.uploadPicture")))])],1)]),e.file?t("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"300px"}},[t("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[t("span",[t("Icon",{attrs:{type:"ios-folder"}}),e._v(e._s(e.file.name)+"\n    \t\t\t\t\t\t")],1),t("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:e.removeFile}})])]):e._e()],1),t("FormItem",{attrs:{label:e.$t("fuelPack.Amount")+":",prop:"amount"}},[t("Input",{staticStyle:{width:"250px"},attrs:{clearable:"",disabled:"",placeholder:e.$t("channelBill.inputAmount")},model:{value:e.formobj.amount,callback:function(t){e.$set(e.formobj,"amount",t)},expression:"formobj.amount"}})],1)],1),t("div",{staticStyle:{"text-align":"center",margin:"40px 0 0 0"}},[t("Button",{staticStyle:{"margin-right":"30px"},on:{click:e.cancelModal}},[e._v(e._s(e.$t("support.back")))]),t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:e.pictureLoading},on:{click:e.pictureSubmit}},[e._v(e._s(e.$t("common.determine")))])],1)],1)]),t("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":e.exportcancelModal},model:{value:e.exportModalr,callback:function(t){e.exportModalr=t},expression:"exportModalr"}},[t("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex","flex-wrap":"wrap"}},[t("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[t("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[e._v(e._s(e.$t("exportMS")))]),t("FormItem",{attrs:{label:e.$t("exportID")}},[t("span",[e._v(e._s(e.taskId))])]),t("FormItem",{attrs:{label:e.$t("exportFlie")}},[t("span",{staticClass:"task-name"},[e._v(e._s(e.taskName))])]),t("span",{staticStyle:{"text-align":"left"}},[e._v(e._s(e.$t("downloadResult")))])],1)],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.exportcancelModal}},[e._v(e._s(e.$t("common.cancel")))]),t("Button",{attrs:{type:"primary"},on:{click:e.Gotor}},[e._v(e._s(e.$t("Goto")))])],1)]),t("a",{ref:"downloadLink",staticStyle:{display:"none"}}),t("Modal",{attrs:{title:e.$t("onlineOrder.onlineModalTitle"),"mask-closable":!1,width:720},model:{value:e.payModal,callback:function(t){e.payModal=t},expression:"payModal"}},[e.payModal?t("PaymentComponent",{attrs:{orderType:"deposit",corpId:e.corpId,billId:e.proofDetail.id,showDeposit:!0,paySuccessInfo:e.paySuccessInfo,amount:e.proofDetail.chargeAmount,currencyCode:e.proofDetail.currencyCode,payLoading:e.payStatus},on:{onlinePay:e.onlinePay}}):e._e(),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"})],1)],1)},n=[],i=(o("d9e2"),o("99af"),o("14d9"),o("a9e3"),o("d3b7"),o("ac1f"),o("00b4"),o("25f0"),o("3ca3"),o("466d"),o("4d90"),o("ddb0"),o("2b3d"),o("bf19"),o("9861"),o("88a7"),o("271a"),o("5494"),o("4b86")),r=o("4fc8"),s=o("8bde"),l={components:{PaymentComponent:s["a"]},data:function(){var e=this,t=function(t,o,a){if(parseFloat(o)<0)a(new Error(e.$t("channelBill.lessThan0")));else{var n=o;"-"===o.substr(0,1)&&(n=o.substr(1,o.length));var i=/^(?!0(\.0+)?$)([1-9]\d{0,7}(\.\d{1,2})?|\d{1,8}(\.\d{0,2})?)$/;!n||i.test(n)?a():a(new Error(e.$t("channelBill.checkNumber")))}},o=function(t,o,a){e.file?a():a(new Error(e.$t("support.pleaseUploadFile")))};return{taskId:"",taskName:"",loading:!1,total:0,currentPage:1,addmodel:!1,exportModalr:!1,PaymentModal:!1,addLoading:!1,pictureLoading:!1,columns:[{title:this.$t("offlinePay.topupID"),key:"billId",align:"center",minWidth:180,tooltip:!0},{title:this.$t("offlinePay.invoiceNumber"),key:"invoiceNo",align:"center",minWidth:180,tooltip:!0},{title:this.$t("offlinePay.invoiceType"),key:"channelType",align:"center",minWidth:150,tooltip:!0,render:function(t,o){var a=o.row,n="";switch(a.channelType){case"1":n=e.$t("offlinePay.deposit");break;case"2":n=e.$t("offlinePay.Prepayment");break;default:n=""}return t("label",n)}},{title:this.$t("offlinePay.topupAmount"),key:"chargeAmount",align:"center",minWidth:150,tooltip:!0},{title:this.$t("deposit.currency"),key:"currencyCode",align:"center",minWidth:150,tooltip:!0,render:function(t,o){var a=o.row,n="";switch(a.currencyCode){case"156":n=e.$t("support.CNY");break;case"344":n=e.$t("support.HKD");break;case"840":n=e.$t("support.USD");break;default:n=""}return t("label",n)}},{title:this.$t("offlinePay.applyTime"),key:"chargeTime",align:"center",minWidth:220,tooltip:!0,render:function(e,t){var o=t.row,a="",n=new Date(o.chargeTime),i=n.getFullYear(),r=String(n.getMonth()+1).padStart(2,"0"),s=String(n.getDate()).padStart(2,"0"),l=String(n.getHours()).padStart(2,"0"),c=String(n.getMinutes()).padStart(2,"0"),d=String(n.getSeconds()).padStart(2,"0");return a=o.chargeTime?"".concat(i,"-").concat(r,"-").concat(s," ").concat(l,":").concat(c,":").concat(d):"",e("label",a)}},{title:this.$t("flow.Status"),key:"chargeStatus",align:"center",minWidth:180,tooltip:!0,render:function(t,o){var a=o.row,n="";switch(a.chargeStatus){case"1":n=e.$t("offlinePay.unpaid");break;case"2":n=e.$t("offlinePay.invoicePendingApro");break;case"3":n=e.$t("offlinePay.invoiceAproReject");break;case"4":n=e.$t("offlinePay.payable");break;case"5":n=e.$t("offlinePay.payPendingApro");break;case"6":n=e.$t("offlinePay.paymentAproReject");break;case"7":n=e.$t("offlinePay.paid");break;case"8":n=e.$t("support.cancelled");break;case"9":n=e.$t("channelBill.OnlinePaymentInProgress");break;default:n=""}return t("label",n)}},{title:this.$t("support.failureReason"),key:"noPassReason",align:"center",minWidth:150,tooltip:!0},{title:this.$t("offlinePay.attachment"),slot:"file",align:"center",fixed:"right",minWidth:170},{title:this.$t("support.action"),slot:"action",align:"center",fixed:"right",minWidth:200}],data:[],supplierList:[],uploadUrl:"",uploadList:[],file:null,rowType:"",payRowData:{},invoiceType:"",invoiceId:"",invoiceRowData:{},cooperationMode:"",formobj:{file:"",amount:""},formmodel:{type:"",currencyCode:"",amount:""},ruleobj:{file:[{required:!0,validator:o,trigger:"change"}],amount:[{required:!0,message:this.$t("channelBill.inputAmount"),trigger:"change"},{validator:t}]},rules:{amount:[{required:!0,message:this.$t("channelBill.inputAmount"),trigger:"change"},{validator:t}]},corpId:"",proofDetail:{},payModal:!1,cardPayFromParent:!1,payDetail:{},payStatus:!1,paySuccessInfo:{},payCont:""}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),"1"!=this.cooperationMode&&"2"!=this.cooperationMode||(this.goPageFirst(1),this.goPageInfo())},methods:{goPageInfo:function(){var e=this;Object(i["d"])({corpId:sessionStorage.getItem("corpId"),cooperationMode:this.cooperationMode}).then((function(t){if("0000"==t.code){var o=t.data.currencyCode;e.formmodel.currencyCode="156"==o?e.$t("support.CNY"):"344"==o?e.$t("support.HKD"):"840"==o?e.$t("support.USD"):"",e.formmodel.type="1"==t.data.channelType?e.$t("offlinePay.deposit"):e.$t("offlinePay.Prepayment")}})).catch((function(e){console.error(e)})).finally((function(){e.loading=!1}))},goPageFirst:function(e){var t=this;this.loading=!0;var o=this,a=e,n=10;Object(i["c"])({corpId:sessionStorage.getItem("corpId"),cooperationMode:this.cooperationMode,pageNum:a,pageSize:n,isDisplay:1}).then((function(a){"0000"==a.code&&(o.loading=!1,t.page=e,t.total=Number(a.count),t.data=a.data)})).catch((function(e){console.error(e)})).finally((function(){t.loading=!1}))},goPage:function(e){this.goPageFirst(e)},addInvoice:function(e,t){this.invoiceType=t,this.invoiceRowData=e,this.formmodel.amount="2"==t?e.chargeAmount.toString():"",this.invoiceId=e?e.id:"",this.addmodel=!0},addBesure:function(){var e=this;this.$refs.formmodel.validate((function(t){t&&(e.addLoading=!0,Object(i["e"])({corpId:sessionStorage.getItem("corpId"),amount:e.formmodel.amount,cooperationMode:e.cooperationMode,repeatedApply:"2"==e.invoiceType,id:"2"==e.invoiceType?e.invoiceId:void 0}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:e.$t("address.Operationreminder"),desc:e.$t("common.Successful")}),e.goPageFirst(1),e.currentPage=1,e.cancelModal(),e.addLoading=!1,e.addmodel=!1})).catch((function(e){console.log(e)})).finally((function(){e.addLoading=!1})))}))},revokeItem:function(e){var t=this,o=e.billId;this.$Modal.confirm({title:this.$t("support.confirmRevocation"),onOk:function(){Object(i["f"])(o).then((function(e){"0000"===e.code&&t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.goPageFirst(1)}))}})},pay:function(e,t){this.rowType=t,this.payRowData=e,this.formobj.amount=e.chargeAmount.toString(),this.PaymentModal=!0},pictureSubmit:function(){var e=this;this.$refs["formobj"].validate((function(t){if(t){var o=new FormData;o.append("amount",e.formobj.amount),o.append("corpId",e.payRowData.corpId),o.append("accountId",e.payRowData.id),o.append("paymentProofs",e.file),o.append("repeatedUpload","2"==e.rowType),o.append("chargeType","2"),e.pictureLoading=!0,Object(i["g"])(o).then((function(t){if("0000"!==t.code)throw t;e.$Notice.success({title:e.$t("address.Operationreminder"),desc:e.$t("common.Successful")}),e.pictureLoading=!1,e.PaymentModal=!1,e.goPageFirst(1),e.file="",e.$refs["formobj"].resetFields()})).catch((function(t){e.pictureLoading=!1})).finally((function(){}))}}))},downloadInvoice:function(e){var t=this;Object(i["b"])({fileAddress:e.invoiceAddress}).then((function(e){var o=e.data,a=decodeURIComponent(escape(e.headers["content-disposition"].match(/=(.*)$/)[1]));if("download"in document.createElement("a")){var n=t.$refs.downloadLink,i=URL.createObjectURL(o);n.download=a,n.href=i,n.click(),URL.revokeObjectURL(i)}else navigator.msSaveBlob(o,a)})).catch((function(e){console.log(e)}))},downloadPaymentProof:function(e){var t=this;Object(i["a"])({id:e.id}).then((function(e){var o=e.data,a=decodeURIComponent(escape(e.headers["content-disposition"].match(/=(.*)$/)[1]));if("download"in document.createElement("a")){var n=t.$refs.downloadLink,i=URL.createObjectURL(o);n.download=a,n.href=i,n.click(),URL.revokeObjectURL(i)}else navigator.msSaveBlob(o,a)})).catch()},exportcancelModal:function(){this.exportModalr=!1,this.file=""},Gotor:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportcancelModal(),this.exportModalr=!1},handleBeforeUpload:function(e,t){var o=e.size/1024/1024>10;return o?(this.$Notice.warning({title:this.$t("address.Operationreminder"),desc:this.$t("support.pictureSize")}),!1):(this.file=e,this.uploadList=t,!1)},fileUploading:function(e,t,o){this.message=this.$t("support.fileUploadedAndProgressDisappears")},removeFile:function(){this.file=""},cancelModal:function(){this.addmodel=!1,this.$refs.formmodel.resetFields(),this.PaymentModal=!1,this.file="",this.$refs["formobj"].resetFields()},getSupplierList:function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(){var e=this;getSupplierList({}).then((function(t){"0000"===t.code&&(e.supplierList=t.data)})).catch((function(e){console.log(e)}))})),onlineRecharge:function(e){console.log(e),this.proofDetail=e,this.payModal=!0},onlinePay:function(e){var t=this;if(this.payStatus)console.log("请求中...");else{this.payStatus=!0,console.log("支付信息：",e);var o={paymentMethod:e.paymentMethod,orderType:"deposit",corpId:sessionStorage.getItem("corpId"),billId:this.proofDetail.id,adyenStateData:e.adyenStateData,language:localStorage.getItem("local")};Object(r["a"])(o).then((function(e){console.log(e.data,"订购详情"),"0000"==e.code&&(t.payStatus=!1,t.paySuccessInfo=e.data,t.payModal=!1,t.payCont=e.data.payUrl,console.log(e.data.redirectUrl),document.querySelector("body").innerHTML=e.data.payUrl,document.forms[0].submit())})).catch((function(e){console.error("请求失败:",e),t.payStatus=!1}))}}}},c=l,d=o("2877"),u=Object(d["a"])(c,a,n,!1,null,null,null);t["default"]=u.exports}}]);