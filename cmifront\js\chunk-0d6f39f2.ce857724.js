(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0d6f39f2"],{"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"841c":function(t,e,a){"use strict";var n=a("c65b"),i=a("d784"),r=a("825a"),s=a("7234"),o=a("1d80"),l=a("129f"),c=a("577e"),d=a("dc4a"),u=a("14c3");i("search",(function(t,e,a){return[function(e){var a=o(this),i=s(e)?void 0:d(e,t);return i?n(i,e,a):new RegExp(e)[t](c(a))},function(t){var n=r(this),i=c(t),s=a(e,n,i);if(s.done)return s.value;var o=n.lastIndex;l(o,0)||(n.lastIndex=0);var d=u(n,i);return l(n.lastIndex,o)||(n.lastIndex=o),null===d?-1:d.index}]}))},"8f3a":function(t,e,a){"use strict";a.r(e);a("ac1f"),a("841c");var n=function(){var t=this,e=t._self._c;return e("Card",[e("DatePicker",{staticStyle:{width:"200px",margin:"0 10px 0 0"},attrs:{format:"yyyyMMdd",editable:!1,type:"daterange",placeholder:t.$t("Period"),clearable:""},on:{"on-change":t.handleDateChange,"on-clear":t.hanldeDateClear},model:{value:t.time,callback:function(e){t.time=e},expression:"time"}}),e("Input",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:t.$t("TaskID"),clearable:""},model:{value:t.taskId,callback:function(e){t.taskId=e},expression:"taskId"}}),e("Input",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:t.$t("FileName"),clearable:""},model:{value:t.fileName,callback:function(e){t.fileName=e},expression:"fileName"}}),e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v(t._s(t.$t("deposit.search")))]),e("div",{staticStyle:{"margin-top":"20px"}},[e("Table",{attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(a){var n=a.row;a.index;return["2"===n.taskStatus?e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary"},on:{click:function(e){return t.downloadFile(n)}}},[t._v("\n\t\t\t\t\t"+t._s(t.$t("DownloadFlie"))+"\n\t\t\t\t")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",disabled:""},on:{click:function(e){return t.downloadFile(n)}}},[t._v("\n\t\t\t\t\t"+t._s(t.$t("DownloadFlie"))+"\n\t\t\t\t")])]}}])})],1),e("a",{ref:"downloadLink",staticStyle:{display:"none"}}),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1)],1)},i=[],r=a("3835"),s=(a("d3b7"),a("99af"),a("66df")),o="/sys/api/v1",l=function(t){return s["a"].request({url:"/sys/api/v1/sync/queryTask",params:t,method:"get"})},c=function(t,e){return s["a"].baseUrl+o+"/sync/download/".concat(t,"/").concat(e)},d=a("6dfa"),u={data:function(){var t=this;return{time:[],fileName:"",taskId:"",corpId:"",startTime:"",endTime:"",channelCorpId:"",searchloading:!1,loading:!1,total:0,currentPage:1,page:1,tableData:[{}],columns:[{title:this.$t("Tasks"),key:"id",align:"center"},{title:this.$t("Description"),key:"taskDesc",align:"center"},{title:this.$t("TasksStatus"),key:"taskStatus",align:"center",render:function(e,a){var n=a.row,i="1"==n.taskStatus?t.$t("buymeal.Processing"):"2"==n.taskStatus?t.$t("order.Completed"):"3"==n.taskStatus?t.$t("common.failure"):"";return e("label",i)}},{title:this.$t("File"),key:"fileName",align:"center"},{title:this.$t("CreationTime"),key:"createTime",align:"center"},{title:this.$t("FinishedTime"),key:"endTime",align:"center"},{title:this.$t("Operation"),slot:"action",align:"center"}]}},methods:{goPageFirst:function(t){var e=this,a="";Object(d["F"])({userName:this.$store.state.user.userName}).then((function(t){return"0000"==t.code&&(a=e.corpId||t.data),a})).then((function(a){l({taskId:e.taskId,fileName:e.fileName,pageNum:t,pageSize:10,startTime:""===e.startTime?null:e.startTime,endTime:""===e.endTime?null:e.endTime,corpId:a||e.$store.state.user.userId,roleId:e.$store.state.user.roleId}).then((function(a){if(!a||"0000"!=a.code)throw a;e.currentPage=t,e.tableData=a.data.records,e.total=a.data.total})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1,e.searchloading=!1}))})).catch((function(t){})).finally((function(){}))},goPage:function(t){this.goPageFirst(t)},search:function(){this.searchloading=!0,this.goPageFirst(1)},handleDateChange:function(t){var e=this.time[0]||"",a=this.time[1]||"";if(""!=e&&""!=a){var n=Object(r["a"])(t,2);this.startTime=n[0],this.endTime=n[1]}},hanldeDateClear:function(){this.startTime="",this.endTime=""},downloadFile:function(t){var e=t.id,a=null;this.corpId&&(a=this.$store.state.user.userId);var n=this.$refs.downloadLink;n.href=c(e,a),n.click()}},mounted:function(){this.taskId=decodeURIComponent(this.$route.query.taskId),this.fileName=decodeURIComponent(this.$route.query.fileName),this.corpId=decodeURIComponent(this.$route.query.corpId),"undefined"===this.taskId&&(this.taskId=""),"undefined"===this.fileName&&(this.fileName=""),"undefined"===this.corpId&&(this.corpId=""),this.goPageFirst(1)}},h=u,m=a("2877"),f=Object(m["a"])(h,n,i,!1,null,null,null);e["default"]=f.exports}}]);