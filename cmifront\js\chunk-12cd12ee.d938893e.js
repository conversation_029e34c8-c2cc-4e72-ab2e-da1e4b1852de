(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-12cd12ee"],{3890:function(e,t,a){"use strict";a("6586")},"4ec9":function(e,t,a){"use strict";a("6f48")},6586:function(e,t,a){},"6f48":function(e,t,a){"use strict";var n=a("6d61"),i=a("6566");n("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),i)},addb:function(e,t,a){"use strict";var n=a("f36a"),i=Math.floor,r=function(e,t){var a=e.length;if(a<8){var s,o,c=1;while(c<a){o=c,s=e[c];while(o&&t(e[o-1],s)>0)e[o]=e[--o];o!==c++&&(e[o]=s)}}else{var l=i(a/2),d=r(n(e,0,l),t),p=r(n(e,l),t),u=d.length,m=p.length,h=0,g=0;while(h<u||g<m)e[h+g]=h<u&&g<m?t(d[h],p[g])<=0?d[h++]:p[g++]:h<u?d[h++]:p[g++]}return e};e.exports=r},b9dc:function(e,t,a){"use strict";a.d(t,"p",(function(){return r})),a.d(t,"d",(function(){return s})),a.d(t,"c",(function(){return o})),a.d(t,"b",(function(){return c})),a.d(t,"f",(function(){return l})),a.d(t,"e",(function(){return d})),a.d(t,"a",(function(){return p})),a.d(t,"k",(function(){return u})),a.d(t,"n",(function(){return m})),a.d(t,"q",(function(){return h})),a.d(t,"l",(function(){return g})),a.d(t,"g",(function(){return f})),a.d(t,"o",(function(){return y})),a.d(t,"m",(function(){return b})),a.d(t,"i",(function(){return k})),a.d(t,"j",(function(){return v})),a.d(t,"h",(function(){return O}));var n=a("66df"),i="/cms/api/v2/postpaid",r=function(e){return n["a"].request({url:i+"/queryChannel",params:e,method:"get"})},s=function(e){return n["a"].request({url:i+"/newChannel",data:e,method:"post"})},o=function(e){return n["a"].request({url:i+"/order",data:e,method:"post",contentType:"multipart/form-data"})},c=function(e){return n["a"].request({url:i+"/batchDeleteChannel",data:e,method:"delete"})},l=function(e){return n["a"].request({url:i+"/updateChannel",data:e,method:"post"})},d=function(e){return n["a"].request({url:i+"/deleteChannel",params:e,method:"delete"})},p=function(e){return n["a"].request({url:i+"/channelCheck",params:e,method:"put"})},u=function(e){return n["a"].request({url:i+"/queryFlowBills",params:e,method:"get"})},m=function(e){return n["a"].request({url:i+"/queryPackageBills",params:e,method:"get"})},h=function(e){return n["a"].request({url:i+"/queryPackageDetails",params:e,method:"get"})},g=function(e){return n["a"].request({url:i+"/queryFlowDetails",params:e,method:"get"})},f=function(e){return n["a"].request({url:i+"/detailDownload",params:e,method:"get",responseType:"blob"})},y=function(e){return n["a"].request({url:i+"/queryPackageListForChannel",params:e,method:"get"})},b=function(e){return n["a"].request({url:i+"/queryPackageListForOrder",params:e,method:"get"})},k=function(e){return n["a"].request({url:i+"/queryChannelList",params:e,method:"get"})},v=function(e){return n["a"].request({url:i+"/queryDetailForUpdate",params:e,method:"get"})},O=function(e){return n["a"].request({url:"/oms/api/v1/country/queryCounrtyList",params:e,method:"get"})}},fce4:function(e,t,a){"use strict";a.r(t);a("b0c0");var n=function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padiing:"16px"}},[t("Form",{ref:"searchForm",staticStyle:{display:"flex"},attrs:{model:e.searchObj}},[t("FormItem",{staticStyle:{display:"flex"}},[t("span",{staticClass:"input_notice"},[e._v("后付费渠道名称：")]),e._v("  \n\t\t\t"),t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入后付费渠道名称",clearable:""},model:{value:e.searchObj.paymentChannel,callback:function(t){e.$set(e.searchObj,"paymentChannel",t)},expression:"searchObj.paymentChannel"}})],1),t("FormItem",{staticStyle:{display:"flex",margin:"0 5px"}},[t("Button",{staticStyle:{margin:"0 2px"},attrs:{type:"primary",icon:"md-search",loading:e.searchLoading},on:{click:function(t){return e.searchPaymentChannel()}}},[e._v("搜索")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 2px"},attrs:{type:"info"},on:{click:e.paymentChannelAdd}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"md-add"}}),e._v(" 新增")],1)]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"orderAdd",expression:"'orderAdd'"}],staticStyle:{margin:"0 2px"},attrs:{type:"success"},on:{click:e.orderAdd}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"md-add"}}),e._v(" 订单新增")],1)]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"batchDelete",expression:"'batchDelete'"}],staticStyle:{margin:"0 2px"},attrs:{type:"error"},on:{click:e.deleteList}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-trash"}}),e._v(" 批量删除")],1)])],1)],1),t("div",[t("Table",{ref:"selection",attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.tableLoading},on:{"on-selection-change":e.handleRowChange},scopedSlots:e._u([{key:"action",fn:function(a){var n=a.row;a.index;return[t("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.paymentChannelCommon(n,"Info")}}},[e._v("详情")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(t){return e.paymentChannelCommon(n,"Update")}}},[e._v("编辑")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],attrs:{type:"error",size:"small"},on:{click:function(t){return e.paymentChannelDel(n)}}},[e._v("删除")])]}},{key:"approval",fn:function(a){var n=a.row;a.index;return["1"==n.checkStatus||"4"==n.checkStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(t){return e.paymentChannelApproval(n,"2")}}},[e._v("通过")]):e._e(),"1"==n.checkStatus||"4"==n.checkStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],attrs:{type:"error",size:"small"},on:{click:function(t){return e.paymentChannelApproval(n,"3")}}},[e._v("不通过")]):e._e()]}}])}),t("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[t("Page",{staticStyle:{margin:"10px 0"},attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1)],1),t("Modal",{attrs:{title:e.paymentChannelTitle,"footer-hide":!0,"mask-closable":!1,width:"750px"},on:{"on-cancel":e.AddcancelModal},model:{value:e.paymentChannelEditFlag,callback:function(t){e.paymentChannelEditFlag=t},expression:"paymentChannelEditFlag"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("Form",{ref:"editObj",attrs:{model:e.editObj,"label-width":120,rules:e.ruleEditValidate}},[t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"后付费渠道名称",prop:"paymentChannelName"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入后付费渠道名称"},model:{value:e.editObj.paymentChannelName,callback:function(t){e.$set(e.editObj,"paymentChannelName",t)},expression:"editObj.paymentChannelName"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"EBS Code",prop:"EBSCode"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入EBSCode"},model:{value:e.editObj.EBSCode,callback:function(t){e.$set(e.editObj,"EBSCode",t)},expression:"editObj.EBSCode"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"内部订单",prop:"internalOrder"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择是否内部订单",clearable:!0},model:{value:e.editObj.internalOrder,callback:function(t){e.$set(e.editObj,"internalOrder",t)},expression:"editObj.internalOrder"}},[t("Option",{attrs:{value:"0"}},[e._v("是")]),t("Option",{attrs:{value:"1"}},[e._v("否")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"币种",prop:"currency"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择币种",clearable:!0},model:{value:e.editObj.currency,callback:function(t){e.$set(e.editObj,"currency",t)},expression:"editObj.currency"}},[t("Option",{attrs:{value:"156"}},[e._v("人民币")]),t("Option",{attrs:{value:"344"}},[e._v("港币")]),t("Option",{attrs:{value:"840"}},[e._v("美元")])],1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"公司名称",prop:"companyName"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入公司名称"},model:{value:e.editObj.companyName,callback:function(t){e.$set(e.editObj,"companyName",t)},expression:"editObj.companyName"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"地址",prop:"address"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入地址"},model:{value:e.editObj.address,callback:function(t){e.$set(e.editObj,"address",t)},expression:"editObj.address"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"付费模式",prop:"paymentMode"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择付费模式",clearable:!0},on:{"on-change":e.getpaymentMode},model:{value:e.editObj.paymentMode,callback:function(t){e.$set(e.editObj,"paymentMode",t)},expression:"editObj.paymentMode"}},e._l(e.paymentModeList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1)],1)],1),"2"===e.editObj.paymentMode?t("div",[e._l(e.editObj.flowList,(function(a,n){return t("div",{key:n},[t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐名称",prop:"flowList."+n+".packageName",rules:[{required:!0,message:"请选择套餐"}]}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择套餐",clearable:!0,multiple:""},model:{value:a.packageName,callback:function(t){e.$set(a,"packageName",t)},expression:"obj.packageName"}},e._l(e.packageList,(function(a){return t("Option",{key:a.packageId,attrs:{value:a.packageShowName}},[e._v(e._s(a.packageShowName))])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"流量方向",prop:"flowList."+n+".mcc",rules:[{required:!0,message:"请选择流量方向"}]}},[t("Select",{staticStyle:{width:"200px","margin-right":"20px"},attrs:{filterable:"",placeholder:"请选择流量方向",clearable:!0,multiple:""},model:{value:a.mcc,callback:function(t){e.$set(a,"mcc",t)},expression:"obj.mcc"}},e._l(e.continentList,(function(a){return t("Option",{key:a.id,attrs:{value:a.mcc}},[e._v(e._s(a.countryEn))])})),1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{staticStyle:{position:"relative"},attrs:{label:"结算价格",prop:"flowList."+n+".price",rules:[{required:!0,message:"请输入结算价格"},{pattern:/^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger:"blur",message:"请输入1-12位数字，整数首位非0（可精确到小数点后2位）"}]}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入结算价格"},model:{value:a.price,callback:function(t){e.$set(a,"price",t)},expression:"obj.price"}},[t("span",{attrs:{slot:"append"},slot:"append"},[e._v("元/G")])]),t("div",{staticStyle:{position:"absolute",top:"0",right:"-15px"},on:{click:function(t){return e.delFlowBtn(n)}}},[t("Tooltip",{attrs:{content:"删除该项方向",placement:"right"}},[t("Icon",{attrs:{type:"md-trash",size:"22",color:"#ff3300"}})],1)],1)],1)],1)],1)],1)})),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"方向",prop:"flowList"}},[t("Button",{staticClass:"inputSty",attrs:{type:"dashed",long:"",icon:"md-add"},on:{click:e.addFlow}},[e._v("添加方向")])],1)],1)],1)],2):e._e(),"1"===e.editObj.paymentMode?t("div",[e._l(e.editObj.packages,(function(a,n){return t("Row",{key:n},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐名称",prop:"packages."+n+".packageName",rules:[{required:!0,message:"请选择套餐"}]}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择套餐",clearable:!0,multiple:""},on:{"on-change":e.getpackages},model:{value:a.packageName,callback:function(t){e.$set(a,"packageName",t)},expression:"obj.packageName"}},e._l(e.packageList,(function(a){return t("Option",{key:a.packageId,attrs:{value:a.packageShowName}},[e._v(e._s(a.packageShowName))])})),1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{staticStyle:{position:"relative"},attrs:{label:"结算价格",prop:"packages."+n+".price",rules:[{required:!0,message:"请输入结算价格"},{pattern:/^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger:"blur",message:"请输入1-12位数字，整数首位非0（可精确到小数点后2位）"}]}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入结算价格"},model:{value:a.price,callback:function(t){e.$set(a,"price",t)},expression:"obj.price"}},[t("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])]),t("div",{staticStyle:{position:"absolute",top:"0",right:"-15px"},on:{click:function(t){return e.delPackageBtn(n)}}},[t("Tooltip",{attrs:{content:"删除该项套餐",placement:"right"}},[t("Icon",{attrs:{type:"md-trash",size:"22",color:"#ff3300"}})],1)],1)],1)],1)],1)})),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐",prop:"packages"}},[t("Button",{staticClass:"inputSty",attrs:{type:"dashed",long:"",icon:"md-add"},on:{click:e.addPackage}},[e._v("添加套餐")])],1)],1)],1)],2):e._e()],1),t("div",{staticStyle:{"text-align":"center"}},["Add"==e.typeFlag?t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{type:"primary",loading:e.AddLoading},on:{click:function(t){return e.submit("Add")}}},[e._v("提交")]):e._e(),"Update"==e.typeFlag?t("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],attrs:{type:"primary",loading:e.AddLoading},on:{click:function(t){return e.submit("Update")}}},[e._v("提交")]):e._e(),t("Button",{staticStyle:{"margin-left":"8px"},on:{click:function(t){return e.reset("editObj")}}},[e._v("重置")])],1)],1)]),t("Modal",{attrs:{title:"订单新增","footer-hide":!0,"mask-closable":!1,width:"420px"},on:{"on-cancel":e.ordercancelModal},model:{value:e.orderAddFlag,callback:function(t){e.orderAddFlag=t},expression:"orderAddFlag"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("Form",{ref:"orderObj",attrs:{model:e.orderObj,"label-width":100,rules:e.ruleOrderValidate}},[t("FormItem",{attrs:{label:"订单名称",prop:"orderName"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入订单名称"},model:{value:e.orderObj.orderName,callback:function(t){e.$set(e.orderObj,"orderName",t)},expression:"orderObj.orderName"}})],1),t("FormItem",{attrs:{label:"后付费渠道",prop:"paymentChannel"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择后付费渠道",clearable:!0},on:{"on-change":e.getorder},model:{value:e.orderObj.paymentChannel,callback:function(t){e.$set(e.orderObj,"paymentChannel",t)},expression:"orderObj.paymentChannel"}},e._l(e.ChannelList,(function(a,n){return t("Option",{key:n,attrs:{value:a.corpId}},[e._v(e._s(a.corpName))])})),1)],1),t("FormItem",{attrs:{label:"付费模式",prop:"paymentMode"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择付费模式",clearable:!0},model:{value:e.orderObj.paymentMode,callback:function(t){e.$set(e.orderObj,"paymentMode",t)},expression:"orderObj.paymentMode"}},e._l(e.paymentModeList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1),t("div",[t("FormItem",{attrs:{label:"套餐名称",prop:"package"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择套餐名称",clearable:!0},model:{value:e.orderObj.package,callback:function(t){e.$set(e.orderObj,"package",t)},expression:"orderObj.package"}},e._l(e.orderpackageList,(function(a){return t("Option",{key:a.packageId,attrs:{value:a.packageId}},[e._v(e._s(a.packageName))])})),1)],1)],1),t("FormItem",{attrs:{label:"卡号列表",rules:[{required:!0,message:"请上传文件",trigger:"blur"}]}},[t("Upload",{attrs:{action:"","on-success":e.fileSuccess,"before-upload":e.handleBeforeUpload,"on-progress":e.fileUploading}},[t("Button",{staticClass:"inputSty",attrs:{type:"dashed",long:"",icon:"md-add"}},[e._v("上传卡号")])],1),e.file?t("ul",{staticClass:"ivu-upload-list"},[t("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[t("span",[t("i",{staticClass:"ivu-icon ivu-icon-ios-stats"}),e._v(e._s(e.file.name))]),t("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:e.removeFile}})])]):e._e()],1),t("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:e.modelColumns,data:e.modelData}})],1),t("div",{staticStyle:{"text-align":"center"}},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"orderAdd",expression:"'orderAdd'"}],attrs:{type:"primary",loading:e.orderLoading},on:{click:e.orderSubmit}},[e._v("提交")]),t("Button",{staticStyle:{"margin-left":"8px"},on:{click:function(t){return e.reset("orderObj")}}},[e._v("重置")]),t("Button",{staticStyle:{"margin-left":"8px"},attrs:{type:"primary",icon:"ios-download"},on:{click:e.downloadFile}},[e._v("下载模板")])],1)],1)]),t("Modal",{attrs:{title:"确认提示","footer-hide":!0,width:"350px"},model:{value:e.confirmmark,callback:function(t){e.confirmmark=t},expression:"confirmmark"}},[t("span",{staticStyle:{"text-align":"center",margin:"4px 0"}},[e._v("\n\t\t\t请选择企业是否可用？\n\t\t")]),t("div",{staticStyle:{"text-align":"center",margin:"10px 0"}},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"orderAdd",expression:"'orderAdd'"}],attrs:{type:"primary",loading:e.orderLoading},on:{click:function(t){return e.confirmmethod(!0)}}},[e._v("可用")]),t("Button",{staticStyle:{"margin-left":"8px"},attrs:{loading:e.orderLoading},on:{click:function(t){return e.confirmmethod(!1)}}},[e._v("不可用")])],1)])],1)},i=[],r=(a("d81d"),a("14d9"),a("4e82"),a("a434"),a("e9c4"),a("4ec9"),a("b680"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0"),a("3ca3"),a("159b"),a("ddb0"),a("fea3"),a("b9dc")),s=(a("2ef0"),a("c70b")),o={components:{},data:function(){return{index:0,continentList:"",ChannelList:[],searchObj:{paymentChannelName:""},corpId:"",status:"",confirmmark:!1,paymentChannelEditFlag:!1,paymentChannelTitle:"后付费渠道新增",typeFlag:"Add",editObj:{paymentChannelName:"",currency:"",internalOrder:"",EBSCode:"",paymentMode:"",packages:[],flowList:[],address:"",companyName:""},orderAddFlag:!1,orderObj:{orderName:"",paymentChannel:"",renewal:"",paymentMode:"",direction:"",package:"",cardList:[],file:null},file:null,paymentModeList:[{label:"流量付费",value:"2"},{label:"套餐付费",value:"1"}],paymentChannellList:[{label:"后付费渠道商1",value:"1"},{label:"后付费渠道商2",value:"2"}],renewalList:[{label:"自动续订",value:"1"}],packageList:[],orderpackageList:[],packageNameList:[],ruleEditValidate:{paymentChannelName:[{required:!0,message:"请输入后付费渠道名称",trigger:"blur"},{min:0,max:50,message:"请输入50位字符以内的后付费渠道名称",trigger:"blur"}],internalOrder:[{required:!0,type:"string",message:"内部订单不能为空",trigger:"change"}],companyName:[{required:!0,type:"string",message:"公司名称不能为空",trigger:"blur"},{max:50,message:"最长50位"}],address:[{required:!0,type:"string",message:"地址不能为空",trigger:"blur"},{max:200,message:"最长200位"}],EBSCode:[{required:!0,message:"请输入EBSCode",trigger:"blur"},{min:0,max:50,message:"请输入50位字符以内EBSCode",trigger:"blur"}],currency:[{required:!0,message:"请选择币种"}],paymentMode:[{required:!0,message:"请选择付费模式"}],packages:[{type:"array",required:!0,message:"请添加套餐",trigger:"blur,change"}],flowList:[{type:"array",required:!0,message:"请添加方向",trigger:"blur,change"}]},ruleOrderValidate:{orderName:[{required:!0,message:"请输入订单名称",trigger:"blur"},{min:0,max:50,message:"请输入50位字符以内订单名称",trigger:"blur"}],paymentChannel:[{required:!0,message:"请选择后付费渠道"}],paymentMode:[{required:!0,message:"请选择付费模式"}],package:[{required:!0,message:"请选择套餐名称"}]},tableData:[],selection:[],selectionIds:[],tableLoading:!1,searchLoading:!1,AddLoading:!1,orderLoading:!1,packagesLists:[],packages:[],flowLists:[],flowList:[],firstpaymentMode:"",total:0,pageSize:10,page:1,currentPage:1,updateindex:0,updateflg:!1,modelData:[{iccid:"********"}],modelColumns:[{title:"iccid",key:"iccid"}],columns:[{type:"selection",minWidth:60,align:"center"},{title:"后付费渠道名称",key:"corpName",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"EBS Code",key:"ebsCode",align:"center",minWidth:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"付费模式",key:"settleType",align:"center",minWidth:150,tooltip:!0,render:function(e,t){var a=t.row,n="1"==a.settleType?"套餐付费":"2"==a.settleType?"流量付费":"";return e("label",n)}},{title:"币种",key:"currencyCode",align:"center",minWidth:150,tooltip:!0,render:function(e,t){var a=t.row,n="156"==a.currencyCode?"人民币":"840"==a.currencyCode?"美元":"344"==a.currencyCode?"港币":"";return e("label",n)}},{title:"操作",slot:"action",minWidth:220,align:"center"}]}},methods:{init:function(){this.columns.push({title:"审批状态",key:"checkStatus",align:"center",minWidth:120,render:function(e,t){var a=t.row,n="1"==a.checkStatus?"#27A1FF":"2"==a.checkStatus?"#00cc66":(a.checkStatus,"#ff0000"),i="1"==a.checkStatus?"新建待审批":"2"==a.checkStatus?"通过":"3"==a.checkStatus?"未通过":"4"==a.checkStatus?"删除待审批":"";return e("label",{style:{color:n}},i)}}),this.columns.push({title:"审批操作",slot:"approval",minWidth:220,align:"center"}),this.goPageFirst(1)},downloadFile:function(){this.$refs.modelTable.exportCsv({filename:"卡号列表模板",columns:this.modelColumns,data:this.modelData})},goPageFirst:function(e){var t=this;this.tableLoading=!0;var a=this,n=this.searchObj.paymentChannel,i=e,s=10;Object(r["p"])({pageNum:i,pageSize:s,channelName:n}).then((function(n){"0000"==n.code&&(a.tableLoading=!1,t.searchLoading=!1,t.page=e,t.total=n.count,t.tableData=n.data)})).catch((function(e){console.error(e)})).finally((function(){t.searchLoading=!1,t.tableLoading=!1}))},goPage:function(e){this.goPageFirst(e)},searchPaymentChannel:function(){this.searchLoading=!0,this.currentPage=1,this.goPageFirst(1)},getLocalList:function(){var e=this;Object(r["h"])().then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.continentList=a,e.continentList.sort((function(e,t){return e.countryEn.localeCompare(t.countryEn)}))})).catch((function(e){})).finally((function(){}))},getorder:function(e){this.queryOrderlList(e)},encapsulation:function(){var e=this;this.paymentChannelEditFlag=!1,"1"===this.editObj.paymentMode?this.editObj.packages.forEach((function(t){e.packageList.forEach((function(a){t.packageName.forEach((function(t){a.packageShowName===t&&e.packageNameList.push({packageId:a.packageId,packageName:t})}))})),e.packagesLists.push({packages:e.packageNameList,price:s.multiply(s.bignumber(t.price),100).toString()}),e.packageNameList=[],e.editObj.packages=[]})):this.editObj.flowList.forEach((function(t){e.packageList.forEach((function(a){t.packageName.forEach((function(t){a.packageShowName===t&&e.packageNameList.push({packageId:a.packageId,packageName:t})}))})),e.flowLists.push({packages:e.packageNameList,price:s.multiply(s.bignumber(t.price),100).toString(),mcc:t.mcc}),e.packageNameList=[],e.editObj.flowList=[]}))},submit:function(e){var t=this;"Add"===e?this.$refs["editObj"].validate((function(e){if(e){var a=new Map;t.packageList.forEach((function(e){a.set(e.packageShowName,e.packageName)})),t.AddLoading=!0,t.encapsulation();var n=t.editObj.paymentChannelName,i=t.editObj.currency,s=t.editObj.EBSCode,o=t.editObj.internalOrder,c=null,l=null,d=t.editObj.paymentMode,p=t.editObj.address,u=t.editObj.companyName;"1"===t.editObj.paymentMode?t.packagesLists.length>0&&(t.packagesLists.map((function(e,t){e.packages.map((function(e,t){e.packageName=a.get(e.packageName)}))})),l=t.packagesLists):t.flowLists.length>0&&(t.flowLists.map((function(e,t){e.packages.map((function(e,t){e.packageName=a.get(e.packageName)}))})),c=t.flowLists),Object(r["d"])({channelName:n,currencyCode:i,ebsCode:s,internalOrder:o,flowlist:c,packagelist:l,settleType:d,address:p,companyName:u}).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"操作成功",desc:"操作成功"}),t.AddLoading=!1,t.paymentChannelEditFlag=!1,t.goPageFirst(1),t.currentPage=1,t.reset())})).catch((function(e){t.AddLoading=!1,t.paymentChannelEditFlag=!1,t.reset(),console.log(e)}))}})):this.$refs["editObj"].validate((function(e){if(e){var a=new Map;t.packageList.forEach((function(e){a.set(e.packageShowName,e.packageName)})),t.AddLoading=!0,t.encapsulation();var n=t.editObj.corpId,i=t.editObj.paymentChannelName,s=t.editObj.currency,o=t.editObj.EBSCode,c=t.editObj.internalOrder,l=null,d=null,p=t.editObj.paymentMode,u=t.editObj.address,m=t.editObj.companyName;"1"===t.editObj.paymentMode?t.packagesLists.length>0&&(t.packagesLists.map((function(e,t){e.packages.map((function(e,t){e.packageName=a.get(e.packageName)}))})),d=t.packagesLists):t.flowLists.length>0&&(t.flowLists.map((function(e,t){e.packages.map((function(e,t){e.packageName=a.get(e.packageName)}))})),l=t.flowLists),Object(r["f"])({channelId:n,channelName:i,currencyCode:s,ebsCode:o,internalOrder:c,flowlist:l,packagelist:d,settleType:p,address:u,companyName:m}).then((function(e){"0000"===e.code?(t.$Notice.success({title:"操作成功",desc:"操作成功"}),t.AddLoading=!1,t.currentPage=1,t.goPageFirst(1),t.AddcancelModal()):(t.AddLoading=!1,t.currentPage=1,t.goPageFirst(1),t.AddcancelModal())})).catch((function(e){t.AddLoading=!1,t.AddcancelModal()}))}}))},orderSubmit:function(){var e=this;this.file?this.$refs["orderObj"].validate((function(t){if(t){e.orderLoading=!0;var a=new FormData;a.append("corpId",e.orderObj.paymentChannel),a.append("orderName",e.orderObj.orderName),a.append("packageId",e.orderObj.package),a.append("payType",e.orderObj.paymentMode),a.append("file",e.orderObj.file),Object(r["c"])(a).then((function(t){"0000"===t.code&&(e.$Notice.success({title:"操作成功",desc:"操作成功"}),e.orderLoading=!1,e.ordercancelModal(),e.currentPage=1,e.goPageFirst(1))})).catch((function(t){e.orderLoading=!1,e.ordercancelModal(),console.log(t)}))}})):this.$Message.warning("请上传卡号！")},reset:function(e){this.editObj.packages.splice(0,this.index),this.editObj.flowList.splice(0,this.index),this.$refs.editObj.resetFields(),this.$refs.orderObj.resetFields(),this.packagesLists=[],this.packageNameList=[],this.flowLists=[]},paymentChannelAdd:function(){this.typeFlag="Add",this.paymentChannelTitle="后付费渠道新增",this.editObj.packages=[],this.editObj.flowList=[],this.editObj={paymentChannelName:"",currency:"",internalOrder:"",paymentMode:"",packages:[{packageName:"",price:""}],flowList:[{packageName:"",mcc:"",price:""}],address:"",companyName:""},this.paymentChannelEditFlag=!0},orderAdd:function(){this.orderObj={orderName:"",paymentChannel:"",renewal:"",paymentMode:"",package:"",cardList:[]},this.queryChannelList(),this.orderAddFlag=!0},addPackage:function(){this.index++,this.editObj.packages.push({currency:"",index:this.index})},addFlow:function(){this.index++,this.editObj.flowList.push({packageName:"",index:this.index})},delPackageBtn:function(e){this.editObj.packages.splice(e,1)},delFlowBtn:function(e){this.editObj.flowList.splice(e,1)},getpackages:function(e){},paymentChannelCommon:function(e,t){var a=this;"Info"===t&&(this.typeFlag=t,this.$router.push({name:"paymentChannelInfo",query:{paymentChannel:encodeURIComponent(JSON.stringify(e))}})),"Update"===t&&(this.editObj.packages.splice(0,this.index),this.editObj.flowList.splice(0,this.index),this.editObj.flowList=[],this.editObj.packages=[],this.packages=[],this.flowList=[],this.updateindex=0,this.index=0,Object(r["j"])({channelId:e.corpId}).then((function(t){"0000"===t.code&&t.data.map((function(t){"1"===e.settleType?(a.packages.push({packageName:t.packageName,price:parseFloat(s.divide(s.bignumber(t.price),100).toFixed(2)).toString()}),a.updateindex++,0===a.packages.length&&(a.packages=[{packageName:"",price:""}])):(a.flowList.push({packageName:t.packageName,price:parseFloat(s.divide(s.bignumber(t.price),100).toFixed(2)).toString(),mcc:t.mcc}),a.updateindex++,0===a.flowList.length&&(a.flowList=[{packageName:"",price:"",mcc:""}])),a.index++,a.firstpaymentMode=e.settleType}))})).catch((function(e){console.log(e)})),this.editObj={corpId:e.corpId,paymentChannelName:e.corpName,currency:e.currencyCode,internalOrder:e.internalOrder,paymentMode:e.settleType,packages:this.packages,flowList:this.flowList,EBSCode:e.ebsCode,address:e.address,companyName:e.companyName},this.typeFlag=t,this.paymentChannelTitle="后付费渠道编辑",this.paymentChannelEditFlag=!0)},paymentChannelApproval:function(e,t){var a=this,n=e.corpId,i=t;"3"===i&&"4"===e.checkStatus?(this.corpId=n,this.status=i,this.confirmmark=!0):this.$Modal.confirm({title:"确认审批？",onOk:function(){Object(r["a"])({corpId:n,status:i}).then((function(e){"0000"===e.code&&(a.$Notice.success({title:"操作成功",desc:"操作成功"}),a.currentPage=1,a.goPageFirst(1))})).catch((function(e){console.log(e)}))}})},confirmmethod:function(e){var t=this,a=this.corpId,n=this.status,i=e;this.confirmmark=!1,Object(r["a"])({corpId:a,status:n,frostCrop:i}).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"操作成功",desc:"操作成功"}),t.currentPage=1,t.goPageFirst(1))})).catch((function(e){console.log(e)}))},paymentChannelDel:function(e){var t=this;this.$Modal.confirm({title:"确认删除？",onOk:function(){var a=e.corpId;Object(r["e"])({channelId:a}).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"操作成功",desc:"操作成功"}),t.currentPage=1,t.goPageFirst(1))})).catch((function(e){console.log(e)}))}})},handleRowChange:function(e){var t=this;this.selection=e,e.map((function(e,a){t.selectionIds.push(e.corpId)}))},deleteList:function(){var e=this,t=this.selection.length;t<1?this.$Message.warning("请至少选择一条记录"):this.$Modal.confirm({title:"确认删除？",onOk:function(){Object(r["b"])({channelIdList:e.selectionIds}).then((function(t){"0000"===t.code&&(e.$Notice.success({title:"操作成功",desc:"操作成功"}),e.selectionIds=[],e.selection=[],e.currentPage=1,e.goPageFirst(1))})).catch((function(t){e.selectionIds=[],e.selection=[],console.log(t)}))}})},handleBeforeUpload:function(e){return/^.+(\.csv)$/.test(e.name)?(this.file=e,this.orderObj.file=e):this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+e.name+" 格式不正确，请上传.csv格式文件。"}),!1},fileUploading:function(e,t,a){this.message="文件上传中、待进度条消失后再操作"},fileSuccess:function(e,t,a){this.message="请先下载模板文件，并按格式填写后上传"},removeFile:function(){this.file=""},AddcancelModal:function(){this.paymentChannelEditFlag=!1,this.editObj.packages.splice(0,this.index),this.editObj.flowList.splice(0,this.index),this.$refs.editObj.resetFields(),this.packagesLists=[],this.packageNameList=[],this.flowLists=[],this.currentPage=1,this.goPageFirst(1)},getpaymentMode:function(){"1"===this.firstpaymentMode?this.editObj.flowList.splice(0,this.index):this.editObj.packages.splice(0,this.index)},ordercancelModal:function(){this.orderAddFlag=!1,this.$refs.orderObj.resetFields(),this.file=""},queryPackageList:function(){var e=this;Object(r["o"])().then((function(t){if(!t||"0000"!=t.code)throw t;e.packageList=t.data})).catch((function(e){})).finally((function(){}))},queryOrderlList:function(e){var t=this,a="";e&&(a=e),Object(r["m"])({channelId:a}).then((function(e){if(!e||"0000"!=e.code)throw e;t.orderpackageList=e.data})).catch((function(e){})).finally((function(){}))},queryChannelList:function(){var e=this;Object(r["i"])().then((function(t){if(!t||"0000"!=t.code)throw t;e.ChannelList=t.data})).catch((function(e){})).finally((function(){}))}},mounted:function(){this.init(),this.getLocalList(),this.queryPackageList()}},c=o,l=(a("3890"),a("2877")),d=Object(l["a"])(c,n,i,!1,null,null,null);t["default"]=d.exports}}]);