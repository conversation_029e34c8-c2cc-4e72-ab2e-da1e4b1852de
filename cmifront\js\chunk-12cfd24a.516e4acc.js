(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-12cfd24a"],{"00b4":function(e,a,t){"use strict";t("ac1f");var i=t("23e7"),n=t("c65b"),o=t("1626"),r=t("825a"),l=t("577e"),s=function(){var e=!1,a=/[ac]/;return a.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===a.test("abc")&&e}(),c=/./.test;i({target:"RegExp",proto:!0,forced:!s},{test:function(e){var a=r(this),t=l(e),i=a.exec;if(!o(i))return n(c,a,t);var s=n(i,a,t);return null!==s&&(r(s),!0)}})},"4b07":function(e,a,t){"use strict";t.r(a);t("caad"),t("b0c0"),t("2532");var i=function(){var e=this,a=e._self._c;return a("div",[a("Card",[a("div",{staticClass:"search_head_i"},[a("div",{staticClass:"search_box"},[a("span",{staticClass:"search_box_label"},[e._v("任务名称")]),a("Input",{staticStyle:{width:"150px"},attrs:{placeholder:"输入任务名称",clearable:""},model:{value:e.searchCondition.taskName,callback:function(a){e.$set(e.searchCondition,"taskName",a)},expression:"searchCondition.taskName"}})],1),a("div",{staticClass:"search_box"},[a("span",{staticClass:"search_box_label"},[e._v("所属渠道")]),a("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",filterable:"",placeholder:"选择所属渠道"},model:{value:e.searchCondition.corpId,callback:function(a){e.$set(e.searchCondition,"corpId",a)},expression:"searchCondition.corpId"}},e._l(e.corpList,(function(t,i){return a("Option",{key:i,attrs:{value:t.corpId}},[e._v(e._s(t.corpName)+"\n\t\t\t\t\t\t")])})),1)],1),a("div",{staticClass:"search_box"},[a("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 4px"},attrs:{type:"primary",loading:e.loading},on:{click:function(a){return e.searchByCondition()}}},[a("Icon",{attrs:{type:"ios-search"}}),e._v(" 搜索\n\t\t\t\t\t")],1),a("Button",{directives:[{name:"has",rawName:"v-has",value:"addBatch",expression:"'addBatch'"}],staticStyle:{margin:"0 4px"},attrs:{type:"info"},on:{click:e.showBatchModal}},[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("Icon",{attrs:{type:"md-add"}}),e._v(" 批量配置\n\t\t\t\t\t\t")],1)])],1)]),a("div",{staticStyle:{"margin-top":"20px"}},[a("Table",{attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.loading},scopedSlots:e._u([{key:"action",fn:function(t){var i=t.row;t.index;return[a("div",["2"==i.taskStatus||"5"==i.taskStatus||"6"==i.taskStatus?a("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"success"},on:{click:function(a){return e.downLoadFile(i.taskId,"1")}}},[e._v("成功文件")]):a("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],staticStyle:{"margin-right":"5px"},attrs:{disabled:"",size:"small",type:"success"}},[e._v("成功文件")]),"2"==i.taskStatus||"5"==i.taskStatus||"6"==i.taskStatus?a("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"error"},on:{click:function(a){return e.downLoadFile(i.taskId,"2")}}},[e._v("失败文件")]):a("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],staticStyle:{"margin-right":"5px"},attrs:{disabled:"",size:"small",type:"error"}},[e._v("失败文件")]),"2"==i.taskStatus||"6"==i.taskStatus?a("Button",{directives:[{name:"has",rawName:"v-has",value:"back",expression:"'back'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"warning"},on:{click:function(a){return e.reback(i.taskId)}}},[e._v("回滚")]):a("Button",{directives:[{name:"has",rawName:"v-has",value:"back",expression:"'back'"}],staticStyle:{"margin-right":"5px"},attrs:{disabled:"",size:"small",type:"warning"}},[e._v("回滚")]),"5"==i.taskStatus||"6"==i.taskStatus?a("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"success"},on:{click:function(a){return e.downLoadFile(i.taskId,"3")}}},[e._v("回滚成功文件")]):a("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],staticStyle:{"margin-right":"5px"},attrs:{disabled:"",size:"small",type:"success"}},[e._v("回滚成功文件")]),"5"==i.taskStatus||"6"==i.taskStatus?a("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"error"},on:{click:function(a){return e.downLoadFile(i.taskId,"4")}}},[e._v("回滚失败文件")]):a("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],staticStyle:{"margin-right":"5px"},attrs:{disabled:"",size:"small",type:"error"}},[e._v("回滚失败文件")])],1)]}}])})],1),a("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[a("Page",{staticStyle:{margin:"10px 0"},attrs:{total:e.total,current:e.currentPage,"page-size":e.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(a){e.currentPage=a},"on-change":e.goPage}})],1)]),a("Modal",{attrs:{title:"批量配置","footer-hide":!0,"mask-closable":!1},on:{"on-cancel":e.cancelUpload},model:{value:e.batchModal,callback:function(a){e.batchModal=a},expression:"batchModal"}},[a("Tabs",{attrs:{type:"card",value:"name1"},on:{"on-click":e.choseTab}},[a("TabPane",{attrs:{label:"页面配置",name:"name1"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:"name1"===e.tabName,expression:"tabName === 'name1'"}],staticStyle:{width:"100%",margin:"10px 0","margin-bottom":"58px"}},[a("Form",{ref:"pagFormValidate",attrs:{model:e.pageData,rules:e.pagRuleValidate}},[a("FormItem",{attrs:{prop:"taskName"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("任务名称")]),a("Input",{attrs:{placeholder:"请输入任务名称",maxlength:20,clearable:""},model:{value:e.pageData.taskName,callback:function(a){e.$set(e.pageData,"taskName",a)},expression:"pageData.taskName"}})],1)]),a("FormItem",{attrs:{prop:"iccid"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("ICCID")]),a("Input",{attrs:{placeholder:"请输入ICCID",maxlength:20,clearable:""},model:{value:e.pageData.iccid,callback:function(a){e.$set(e.pageData,"iccid",a)},expression:"pageData.iccid"}})],1)]),a("FormItem",{attrs:{prop:"quantity"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("数量")]),a("Input",{attrs:{placeholder:"请输入数量",clearable:""},model:{value:e.pageData.quantity,callback:function(a){e.$set(e.pageData,"quantity",a)},expression:"pageData.quantity"}})],1)]),a("FormItem",{attrs:{prop:"packageId"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("套餐ID")]),a("Input",{attrs:{placeholder:"请输入套餐ID",clearable:""},model:{value:e.pageData.packageId,callback:function(a){e.$set(e.pageData,"packageId",a)},expression:"pageData.packageId"}})],1)]),a("FormItem",{attrs:{prop:"effectiveDay"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("有效期")]),a("Input",{attrs:{placeholder:"请输入有效期",clearable:""},model:{value:e.pageData.effectiveDay,callback:function(a){e.$set(e.pageData,"effectiveDay",a)},expression:"pageData.effectiveDay"}})],1)]),a("FormItem",{attrs:{prop:"currency"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("币种")]),a("Select",{attrs:{clearable:!0,placeholder:"请选择币种"},model:{value:e.pageData.currency,callback:function(a){e.$set(e.pageData,"currency",a)},expression:"pageData.currency"}},[a("Option",{attrs:{value:"人民币"}},[e._v("人民币")]),a("Option",{attrs:{value:"美元"}},[e._v("美元")]),a("Option",{attrs:{value:"港币"}},[e._v("港币")])],1)],1)]),a("FormItem",{attrs:{prop:"amount"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("金额")]),a("Input",{attrs:{placeholder:"请输入金额",clearable:""},model:{value:e.pageData.amount,callback:function(a){e.$set(e.pageData,"amount",a)},expression:"pageData.amount"}})],1)]),a("FormItem",{attrs:{prop:"channel"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("售卖渠道")]),a("Select",{attrs:{clearable:!0,placeholder:"请选择种售卖渠道"},model:{value:e.pageData.channel,callback:function(a){e.$set(e.pageData,"channel",a)},expression:"pageData.channel"}},[a("Option",{attrs:{value:"批量售卖"}},[e._v("批量售卖")]),a("Option",{attrs:{value:"推广活动"}},[e._v("推广活动")]),a("Option",{attrs:{value:"测试渠道"}},[e._v("测试渠道")])],1)],1)]),a("FormItem",{attrs:{prop:"language"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("语言")]),a("Select",{attrs:{clearable:!0,placeholder:"请选择语言"},model:{value:e.pageData.language,callback:function(a){e.$set(e.pageData,"language",a)},expression:"pageData.language"}},[a("Option",{attrs:{value:"简体中文"}},[e._v("简体中文")]),a("Option",{attrs:{value:"繁体中文"}},[e._v("繁体中文")]),a("Option",{attrs:{value:"英文"}},[e._v("英文")])],1)],1)]),a("FormItem",{attrs:{prop:"corpId"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("渠道商")]),a("Select",{attrs:{clearable:!0,placeholder:"请选择渠道商",filterable:!0},on:{"on-change":e.getmodes,"on-clear":e.modesclear},model:{value:e.pageData.corpId,callback:function(a){e.$set(e.pageData,"corpId",a)},expression:"pageData.corpId"}},e._l(e.corpList,(function(t,i){return a("Option",{key:i,attrs:{value:t.corpId}},[e._v(e._s(t.corpName))])})),1)],1)]),a("FormItem",{attrs:{prop:"isSingapore"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("是否新加坡卡")]),a("Select",{attrs:{clearable:!0,placeholder:"请选择是否为新加坡卡"},model:{value:e.pageData.isSingapore,callback:function(a){e.$set(e.pageData,"isSingapore",a)},expression:"pageData.isSingapore"}},[a("Option",{attrs:{value:"1"}},[e._v("是")]),a("Option",{attrs:{value:"2"}},[e._v("否")])],1)],1)]),a("FormItem",{attrs:{prop:"activeAt"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("套餐激活日期")]),a("Input",{attrs:{placeholder:"请输入套餐激活日期",clearable:""},model:{value:e.pageData.activeAt,callback:function(a){e.$set(e.pageData,"activeAt",a)},expression:"pageData.activeAt"}})],1)]),a("FormItem",{attrs:{prop:"orderBatch"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("输入订单批次")]),a("Input",{attrs:{placeholder:"请输入输入订单批次",maxlength:200,clearable:""},model:{value:e.pageData.orderBatch,callback:function(a){e.$set(e.pageData,"orderBatch",a)},expression:"pageData.orderBatch"}})],1)]),a("FormItem",{attrs:{prop:"cooperationMode"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("合作模式：")]),a("Select",{attrs:{clearable:!0,placeholder:"请选择合作模式",placement:"top-start"},model:{value:e.pageData.cooperationMode,callback:function(a){e.$set(e.pageData,"cooperationMode",a)},expression:"pageData.cooperationMode"}},e._l(e.modeList,(function(t,i){return a("Option",{key:i,attrs:{value:t.value}},[e._v(e._s(t.name)+"\n\t\t\t\t\t\t\t\t\t\t")])})),1)],1)]),a("FormItem",{attrs:{prop:"remark",rules:e.isNeedCorpList.includes(e.pageData.corpId)?e.pagRuleValidate.remark:[{required:!1}]}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("备注：")]),a("Input",{attrs:{placeholder:"请输入输入备注",maxlength:200,clearable:""},model:{value:e.pageData.remark,callback:function(a){e.$set(e.pageData,"remark",a)},expression:"pageData.remark"}})],1)])],1)],1)]),a("TabPane",{attrs:{label:"文件配置",name:"name2"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:"name2"===e.tabName,expression:"tabName === 'name2'"}],staticStyle:{padding:"10px","margin-bottom":"25px"}},[a("div",[a("Upload",{attrs:{type:"drag",action:e.uploadUrl,"on-success":e.fileSuccess,"before-upload":e.handleBeforeUpload,"on-progress":e.fileUploading}},[a("div",{staticStyle:{padding:"20px 0"}},[a("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),a("p",[e._v("点击或拖拽文件上传")])],1)]),e.modalData.file?a("ul",{staticClass:"ivu-upload-list"},[a("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[a("span",[a("i",{staticClass:"ivu-icon ivu-icon-ios-stats"}),e._v(e._s(e.modalData.file.name))]),a("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:e.removeFile}})])]):e._e()],1),a("div",{staticStyle:{width:"100%",padding:"10px 0"}},[a("Button",{attrs:{type:"primary",loading:e.downloading,icon:"ios-download"},on:{click:e.downloadTempFile}},[e._v("下载模板文件")]),a("Alert",{staticStyle:{float:"right",padding:"8px 10px 8px 10px"},attrs:{type:"warning"}},[e._v(e._s(e.message))])],1),a("div",{staticStyle:{width:"100%",margin:"10px 0"}},[a("Form",{ref:"formValidate",attrs:{model:e.modalData,rules:e.ruleValidate}},[a("FormItem",{attrs:{prop:"taskName"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("输入任务名称：")]),a("Input",{attrs:{placeholder:"请输入任务名称",maxlength:20,clearable:""},model:{value:e.modalData.taskName,callback:function(a){e.$set(e.modalData,"taskName",a)},expression:"modalData.taskName"}})],1)]),a("FormItem",{attrs:{prop:"corpId"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("选择所属渠道：")]),a("Select",{attrs:{transfer:"",clearable:!0,placeholder:"请选择所属渠道",filterable:!0},on:{"on-change":e.modalgetmodes,"on-clear":e.modalmodesclear},model:{value:e.modalData.corpId,callback:function(a){e.$set(e.modalData,"corpId",a)},expression:"modalData.corpId"}},e._l(e.corpList,(function(t,i){return a("Option",{key:i,attrs:{value:t.corpId}},[e._v(e._s(t.corpName)+"\n\t\t\t\t\t\t\t\t\t\t\t")])})),1)],1)]),a("FormItem",{attrs:{prop:"isSingapore"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("是否新加坡卡：")]),a("Select",{attrs:{clearable:!0,placeholder:"请选择是否为新加坡卡"},model:{value:e.modalData.isSingapore,callback:function(a){e.$set(e.modalData,"isSingapore",a)},expression:"modalData.isSingapore"}},[a("Option",{attrs:{value:"1"}},[e._v("是新加坡卡")]),a("Option",{attrs:{value:"2"}},[e._v("非新加坡卡")])],1)],1)]),a("FormItem",{attrs:{prop:"orderBatch"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("输入订单批次：")]),a("Input",{attrs:{placeholder:"请输入输入订单批次",maxlength:200,clearable:""},model:{value:e.modalData.orderBatch,callback:function(a){e.$set(e.modalData,"orderBatch",a)},expression:"modalData.orderBatch"}})],1)]),a("FormItem",{attrs:{prop:"cooperationMode"}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("合作模式：")]),a("Select",{attrs:{clearable:!0,placeholder:"请选择合作模式",placement:"top-start"},model:{value:e.modalData.cooperationMode,callback:function(a){e.$set(e.modalData,"cooperationMode",a)},expression:"modalData.cooperationMode"}},e._l(e.modalmodeList,(function(t,i){return a("Option",{key:i,attrs:{value:t.value}},[e._v(e._s(t.name)+"\n\t\t\t\t\t\t\t\t\t\t\t")])})),1)],1)]),a("FormItem",{attrs:{prop:"remark",rules:e.isNeedCorpList.includes(e.modalData.corpId)?e.ruleValidate.remark:[{required:!1}]}},[a("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[a("span",{staticStyle:{width:"30%","font-weight":"bold"}},[e._v("备注：")]),a("Input",{attrs:{placeholder:"请输入输入备注",maxlength:200,clearable:""},model:{value:e.modalData.remark,callback:function(a){e.$set(e.modalData,"remark",a)},expression:"modalData.remark"}})],1)])],1)],1)])])],1),a("div",{staticStyle:{"text-align":"center",margin:"4px 0"}},[a("Button",{directives:[{name:"has",rawName:"v-has",value:"addBatch",expression:"'addBatch'"},{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:e.uploading},on:{click:e.handleUpload}},[e._v("提交")]),a("Button",{staticStyle:{"margin-left":"8px"},on:{click:e.cancelUpload}},[e._v("取消")])],1)],1),a("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:e.modelColumns,data:e.modelData}})],1)},n=[],o=t("c7eb"),r=t("1da1"),l=(t("4de4"),t("d81d"),t("14d9"),t("4ec9"),t("4fadc"),t("d3b7"),t("ac1f"),t("00b4"),t("3ca3"),t("5319"),t("159b"),t("ddb0"),t("2b3d"),t("bf19"),t("9861"),t("88a7"),t("271a"),t("5494"),t("0a21"),t("951d")),s=(t("c70b"),{data:function(){return{searchCondition:{taskName:"",corpId:""},corpList:[],isNeedCorpList:[],loading:!1,currentPage:1,pageSize:10,total:0,columns:[],tableData:[],pageData:{taskName:"",iccid:"",quantity:"",packageId:"",effectiveDay:"",currency:"",amount:"",channel:"",language:"",corpId:"",isSingapore:"",activeAt:"",orderBatch:"",cooperationMode:"",remark:""},modalData:{file:null,taskName:"",corpId:"",isSingapore:"",orderBatch:"",cooperationMode:"",remark:""},downloading:!1,message:"文件仅支持csv格式文件,大小不能超过5MB",ruleValidate:{taskName:[{required:!0,message:"请输入任务名称",type:"string"}],isSingapore:[{required:!0,message:"请选择是否为新加坡卡",trigger:"change"}],orderBatch:[{required:!0,message:"请输入订单批次",trigger:"change"}],cooperationMode:[{required:!0,message:"请选择合作模式"}],remark:[{required:!0,message:"备注不能为空"}]},changePackageId:["effectiveDay"],pagRuleValidate:{taskName:[{required:!0,message:"任务名称不能为空",type:"string"}],iccid:[{required:!0,message:"iccid不能为空",type:"string"}],quantity:[{required:!0,message:"数量不能为空",type:"string"},{pattern:/^(\+?[1-9]\d{0,5}|\+?999999)$/,trigger:"blur",message:"取值1-999999之间的正整数"}],effectiveDay:[{required:!0,message:"有效期不能为空",type:"string"}],currency:[{required:!0,message:"币种不能为空",type:"string"}],amount:[{required:!0,message:"金额不能为空",type:"string"},{pattern:/^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/,trigger:"blur",message:"金额最高支持8位整数和2位小数正数或零"}],channel:[{required:!0,message:"售卖渠道不能为空",type:"string"}],language:[{required:!0,message:"语言不能为空",type:"string"}],corpId:[{required:!0,message:"渠道商不能为空",type:"string"}],isSingapore:[{required:!0,message:"是否是新加坡卡不能为空",type:"string"}],activeAt:[{pattern:/^\d{4}\/\d{2}\/\d{2}$/,trigger:"blur",message:"日期格式为yyyy/mm/dd"}],orderBatch:[{required:!0,message:"订单批次不能为空",type:"string"}],cooperationMode:[{required:!0,message:"合作模式不能为空"}],remark:[{required:!0,message:"备注不能为空"}]},batchModal:!1,uploadUrl:"",uploading:!1,modelColumns:[{title:"ICCID",key:"iccid"},{title:"套餐ID",key:"packageId"},{title:"套餐有效期",key:"effectiveDay"},{title:"销售渠道[批量售卖/推广活动/测试渠道]",key:"corpName"},{title:"币种[请填写：人民币、港币、美元]",key:"currencyCode"},{title:"金额",key:"amount"},{title:"语言[请填写：简体中文、繁体中文、英文]",key:"language"},{title:"套餐激活日期[YYYY/MM/DD]",key:"date"}],modelData:[],tabName:"name1",modeList:[{value:1,name:"代销"},{value:2,name:"A2Z"}],modalmodeList:[{value:1,name:"代销"},{value:2,name:"A2Z"}]}},created:function(){var e=this;this.$nextTick((function(){e.changePackageId.forEach((function(a){e.$set(e.pagRuleValidate[a][0],"required",!1)}))}))},methods:{init:function(){this.columns=[{title:"任务名称",key:"taskName",align:"center",tooltip:!0,minWidth:120},{title:"所属渠道",key:"corpName",align:"center",tooltip:!0,minWidth:120},{title:"是否新加坡卡",key:"isSingapore",align:"center",width:110,render:function(e,a){var t=a.row,i="1"==t.isSingapore?"是":"2"==t.isSingapore?"否":"未知";return e("label",i)}},{title:"账户名",key:"username",align:"center",minWidth:120,tooltip:!0},{title:"金额(人民币)",align:"center",minWidth:110,render:function(e,a){var t=a.row,i=new Map(Object.entries(t.amount)),n=i.get("156"),o=null==n?"-":n;return e("label",o)}},{title:"金额(港币)",align:"center",minWidth:110,render:function(e,a){var t=a.row,i=new Map(Object.entries(t.amount)),n=i.get("344"),o=null==n?"-":n;return e("label",o)}},{title:"金额(美元)",align:"center",minWidth:110,render:function(e,a){var t=a.row,i=new Map(Object.entries(t.amount)),n=i.get("840"),o=null==n?"-":n;return e("label",o)}},{title:"处理状态",key:"taskStatus",align:"center",width:100,render:function(e,a){var t=a.row,i="";switch(t.taskStatus){case"1":i="处理中";break;case"2":i="已完成";break;case"4":i="回滚中";break;case"5":i="已回滚";break;case"6":i="可回滚";break;default:i="未知状态"}return e("label",i)}},{title:"任务总数",key:"sourceFileCount",tooltip:!0,width:100,align:"center"},{title:"成功条数",key:"successFileCount",tooltip:!0,width:100,align:"center"},{title:"失败条数",key:"failFileCount",tooltip:!0,width:100,align:"center"},{title:"创建时间",key:"createTime",tooltip:!0,minWidth:150,align:"center"},{title:"完成时间",key:"completeTime",tooltip:!0,minWidth:150,align:"center"}];var e=this.$route.meta.permTypes,a=0;e.filter((function(e){"download".indexOf(e)>-1&&(a+=330),"back".indexOf(e)>-1&&(a+=75)})),a>0&&this.columns.push({title:"操作",slot:"action",width:a,fixed:"right",align:"center"}),this.getCorpList(),this.getIsNeedRemark(),this.goPageFirst(1)},searchByCondition:function(){this.goPageFirst(1)},goPageFirst:function(e){var a=this;this.currentPage=e,this.loading=!0;var t={taskName:this.searchCondition.taskName.replace(/\s/g,""),corpId:this.searchCondition.corpId,current:e,size:this.pageSize};Object(l["g"])(t).then((function(e){if(!e||"0000"!=e.code)throw e;a.total=e.count,a.tableData=e.data})).catch((function(e){})).finally((function(){a.loading=!1}))},goPage:function(e){this.goPageFirst(e)},getmodes:function(){var e=this;this.corpList.map((function(a){a.corpId===e.pageData.corpId&&(a.modes.includes("1")&&a.modes.includes("2")?e.modeList=[{value:1,name:"代销"},{value:2,name:"A2Z"}]:a.modes.includes("2")?(e.modeList=[{value:2,name:"A2Z"}],e.pageData.cooperationMode=2):a.modes.includes("1")&&(e.modeList=[{value:1,name:"代销"}],e.pageData.cooperationMode=1))}))},modesclear:function(){this.pageData.cooperationMode="",this.modeList=[{value:1,name:"代销"},{value:2,name:"A2Z"}]},modalgetmodes:function(){var e=this;this.corpList.map((function(a){a.corpId===e.modalData.corpId&&(a.modes.includes("1")&&a.modes.includes("2")?e.modalmodeList=[{value:1,name:"代销"},{value:2,name:"A2Z"}]:a.modes.includes("2")?(e.modalmodeList=[{value:2,name:"A2Z"}],e.modalData.cooperationMode=2):a.modes.includes("1")&&(e.modalmodeList=[{value:1,name:"代销"}],e.modalData.cooperationMode=1))}))},modalmodesclear:function(){this.modalData.cooperationMode="",this.modalmodeList=[{value:1,name:"代销"},{value:2,name:"A2Z"}]},getCorpList:function(){var e=this;Object(l["e"])({type:1,status:1,checkStatus:2}).then((function(a){if(!a||"0000"!=a.code)throw a;e.corpList=a.data})).catch((function(e){})).finally((function(){}))},getIsNeedRemark:function(){var e=this;Object(l["f"])({}).then((function(a){if(!a||"0000"!=a.code)throw a;e.isNeedCorpList=a.data})).catch((function(e){})).finally((function(){}))},showBatchModal:function(){this.$refs["formValidate"].resetFields(),this.$refs["pagFormValidate"].resetFields(),this.modalData={file:null,taskName:"",corpId:"",isSingapore:"",orderBatch:"",cooperationMode:"",remark:""},this.pageData={taskName:"",iccid:"",quantity:"",packageId:"",effectiveDay:"",currency:"",amount:"",channel:"",language:"",corpId:"",isSingapore:"",activeAt:"",orderBatch:"",cooperationMode:"",remark:""},this.batchModal=!0},removeFile:function(){this.modalData.file=""},handleBeforeUpload:function(e){return/^.+(\.csv)$/.test(e.name)?e.size>5242880?this.$Notice.warning({title:"文件大小超过限制",desc:"文件超过了最大限制范围5MB"}):this.modalData.file=e:this.$Notice.warning({title:"文件格式错误",desc:"文件格式错误，请上传.csv格式文件"}),!1},fileUploading:function(e,a,t){this.message="文件上传中,待进度条消失后再操作"},fileSuccess:function(e,a,t){this.message="请先下载模板文件,并按格式填写后上传"},cancelUpload:function(){this.batchModal=!1,this.modeList=[{value:1,name:"代销"},{value:2,name:"A2Z"}],this.modalmodeList=[{value:1,name:"代销"},{value:2,name:"A2Z"}]},choseTab:function(e){this.tabName=e},handleUpload:function(){var e=this;return Object(r["a"])(Object(o["a"])().mark((function a(){return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if("name1"!==e.tabName){a.next=5;break}e.pageData.packageId?(e.changePackageId.forEach((function(a){e.pagRuleValidate["effectiveDay"][0].required=!0})),e.$refs["pagFormValidate"].validate()):(e.changePackageId.forEach((function(a){e.pagRuleValidate["effectiveDay"][0].required=!1})),e.$refs["pagFormValidate"].validate()),e.$refs.pagFormValidate.validate((function(a){a&&(e.uploading=!0,Object(l["a"])({activeAt:e.pageData.activeAt,amount:e.pageData.amount,channel:e.pageData.channel,corpId:e.pageData.corpId,currency:e.pageData.currency,effectiveDay:e.pageData.effectiveDay,iccid:e.pageData.iccid,isSingapore:e.pageData.isSingapore,language:e.pageData.language,quantity:e.pageData.quantity,taskName:e.pageData.taskName,orderBatch:e.pageData.orderBatch,cooperationMode:e.pageData.cooperationMode,username:e.$store.state.user.userName,operatorId:e.$store.state.user.userId,packageId:e.pageData.packageId,remark:e.pageData.remark}).then((function(a){if("0000"!==a.code)throw a;a.data;e.$Notice.success({title:"操作提示",desc:a.msg}),e.cancelUpload(),e.goPageFirst(1)})).catch((function(e){console.log(e)})).finally((function(){e.uploading=!1})))})),a.next=9;break;case 5:if(e.modalData.file){a.next=8;break}return e.$Message.warning("请选择需要上传的文件"),a.abrupt("return",!1);case 8:e.$refs.formValidate.validate((function(a){if(a){e.uploading=!0;var t=new FormData;void 0!=e.modalData.corpId&&t.append("corpId",e.modalData.corpId),t.append("isSingapore",e.modalData.isSingapore),t.append("orderBatch",e.modalData.orderBatch),t.append("cooperationMode",e.modalData.cooperationMode),t.append("operatorId",e.$store.state.user.userId),t.append("username",e.$store.state.user.userName),t.append("file",e.modalData.file),t.append("taskName",e.modalData.taskName),t.append("remark",e.modalData.remark),Object(l["b"])(t).then((function(a){if("0000"!==a.code)throw a;a.data;e.$Notice.success({title:"操作提示",desc:a.msg}),e.cancelUpload(),e.goPageFirst(1)})).catch((function(e){console.log(e)})).finally((function(){e.uploading=!1}))}}));case 9:case"end":return a.stop()}}),a)})))()},downLoadFile:function(e,a){Object(l["d"])(e,a).then((function(e){var t=e.data,i=new Blob([t]),n="";switch(a){case"1":n="成功";break;case"2":n="失败";break;case"3":n="回滚成功";break;case"4":n="回滚失败";break;default:n=""}var o="任务执行"+n+"信息.csv";if("download"in document.createElement("a")){var r=document.createElement("a");r.download=o,r.style.display="none",r.href=URL.createObjectURL(i),document.body.appendChild(r),r.click(),URL.revokeObjectURL(r.href),document.body.removeChild(r)}else navigator.msSaveBlob(i,o)})).catch((function(e){console.log(e)})).finally((function(){}))},downloadTempFile:function(){this.$refs.modelTable.exportCsv({filename:"套餐批量配置",columns:this.modelColumns,data:this.modelData})},reback:function(e){var a=this;this.$Modal.confirm({title:"确认回滚？",onOk:function(){Object(l["c"])(e).then((function(e){if(!e||"0000"!=e.code)throw e;a.$Notice.success({title:"操作提示",desc:e.msg}),a.goPageFirst(a.currentPage)})).catch((function(e){console.log(e)}))}})}},mounted:function(){this.init()},watch:{}}),c=s,d=(t("ab2a"),t("2877")),p=Object(d["a"])(c,i,n,!1,null,"a3c23d5e",null);a["default"]=p.exports},"4ec9":function(e,a,t){"use strict";t("6f48")},"4fadc":function(e,a,t){"use strict";var i=t("23e7"),n=t("6f53").entries;i({target:"Object",stat:!0},{entries:function(e){return n(e)}})},"6f48":function(e,a,t){"use strict";var i=t("6d61"),n=t("6566");i("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),n)},"6f53":function(e,a,t){"use strict";var i=t("83ab"),n=t("d039"),o=t("e330"),r=t("e163"),l=t("df75"),s=t("fc6a"),c=t("d1e7").f,d=o(c),p=o([].push),u=i&&n((function(){var e=Object.create(null);return e[2]=2,!d(e,2)})),m=function(e){return function(a){var t,n=s(a),o=l(n),c=u&&null===r(n),m=o.length,g=0,h=[];while(m>g)t=o[g++],i&&!(c?t in n:d(n,t))||p(h,e?[t,n[t]]:n[t]);return h}};e.exports={entries:m(!0),values:m(!1)}},"7ff9":function(e,a,t){},"951d":function(e,a,t){"use strict";t.d(a,"g",(function(){return o})),t.d(a,"d",(function(){return r})),t.d(a,"c",(function(){return l})),t.d(a,"b",(function(){return s})),t.d(a,"a",(function(){return c})),t.d(a,"e",(function(){return d})),t.d(a,"f",(function(){return p}));var i=t("66df"),n="/cms/package/config",o=function(e){return i["a"].request({url:n+"/task/pageList",data:e,method:"post"})},r=function(e,a){return i["a"].request({url:n+"/task/download/".concat(e,"?status=")+a,method:"POST",responseType:"blob"})},l=function(e){return i["a"].request({url:n+"/task/rollback/".concat(e),method:"POST"})},s=function(e){return i["a"].request({url:n+"/task",data:e,method:"POST",contentType:"multipart/form-data"})},c=function(e){return i["a"].request({url:n+"/taskPage",data:e,method:"POST"})},d=function(e){return i["a"].request({url:"/cms/channel/searchList",data:e,method:"post"})},p=function(e){return i["a"].request({url:"/cms/package/config/getTextChannel",data:e,method:"get"})}},ab2a:function(e,a,t){"use strict";t("7ff9")}}]);