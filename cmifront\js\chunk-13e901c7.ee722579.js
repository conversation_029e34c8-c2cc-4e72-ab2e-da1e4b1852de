(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-13e901c7"],{"129f":function(e,t,r){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},"1c31":function(e,t,r){"use strict";r.d(t,"t",(function(){return o})),r.d(t,"s",(function(){return i})),r.d(t,"a",(function(){return u})),r.d(t,"b",(function(){return l})),r.d(t,"c",(function(){return s})),r.d(t,"d",(function(){return c})),r.d(t,"e",(function(){return d})),r.d(t,"f",(function(){return m})),r.d(t,"g",(function(){return f})),r.d(t,"h",(function(){return p})),r.d(t,"i",(function(){return h})),r.d(t,"j",(function(){return g})),r.d(t,"k",(function(){return v})),r.d(t,"l",(function(){return b})),r.d(t,"x",(function(){return y})),r.d(t,"m",(function(){return x})),r.d(t,"n",(function(){return w})),r.d(t,"o",(function(){return k})),r.d(t,"p",(function(){return T})),r.d(t,"q",(function(){return q})),r.d(t,"v",(function(){return F})),r.d(t,"r",(function(){return I})),r.d(t,"w",(function(){return S})),r.d(t,"u",(function(){return P}));var a=r("66df"),n="/stat",o=function(e){return a["a"].request({url:"/cms/api/v1/packageCard/countReuseExport",data:e,responseType:"blob",method:"post"})},i=function(e){return a["a"].request({url:"/cms/api/v1/packageCard/countReuse",data:e,method:"post"})},u=function(e){return a["a"].request({url:n+"/activereport/detailDownload",params:e,responseType:"blob",method:"get"})},l=function(e){return a["a"].request({url:n+"/activereport/pageList",data:e,method:"post"})},s=function(e){return a["a"].request({url:n+"/cardReport",params:e,method:"get"})},c=function(e){return a["a"].request({url:n+"/cardReport/export",params:e,responseType:"blob",method:"get"})},d=function(e){return a["a"].request({url:n+"/offline/export",params:e,responseType:"blob",method:"get"})},m=function(e){return a["a"].request({url:n+"/offline/import",data:e,method:"post"})},f=function(e){return a["a"].request({url:n+"/offline/pageList",data:e,method:"post"})},p=function(e){return a["a"].request({url:n+"/operatorsettle/detailDownload",params:e,responseType:"blob",method:"get"})},h=function(e){return a["a"].request({url:n+"/operatorsettle/pageList",data:e,method:"post"})},g=function(e){return a["a"].request({url:n+"/postpaidsettle/detailDownload",params:e,responseType:"blob",method:"get"})},v=function(e){return a["a"].request({url:n+"/postpaidsettle/pageList",data:e,method:"post"})},b=function(e){return a["a"].request({url:n+"/rate",params:e,method:"get"})},y=function(e){return a["a"].request({url:n+"/rate",data:e,method:"post"})},x=function(e){return a["a"].request({url:n+"/rate/export",params:e,responseType:"blob",method:"get"})},w=function(e){return a["a"].request({url:n+"/report/package/analysis/export",params:e,responseType:"blob",method:"get"})},k=function(e){return a["a"].request({url:n+"/report/package/analysis/search",data:e,method:"post"})},T=function(e){return a["a"].request({url:n+"/terminalsettle/detailDownload",params:e,responseType:"blob",method:"get"})},q=function(e){return a["a"].request({url:n+"/terminalsettle/pageList",data:e,method:"post"})},F=function(e){return a["a"].request({url:"/charging/cost/supplierCostQuery",data:e,method:"post"})},I=function(e){return a["a"].request({url:"/charging/cost/supplierCostExport",data:e,responseType:"blob",method:"post"})},S=function(e){return a["a"].request({url:"/cms/esim/getEsimcardStats",params:e,method:"get"})},P=function(e){return a["a"].request({url:"/cms/esim/exportEsimcardStats",params:e,method:"get"})}},"4b0e":function(e,t,r){"use strict";r.r(t);r("ac1f"),r("841c");var a=function(){var e=this,t=e._self._c;return t("Card",[t("div",{staticStyle:{display:"flex",width:"100%"}},[t("Form",{ref:"form",attrs:{"label-width":0,model:e.form,rules:e.rule,inline:""}},[t("FormItem",{attrs:{prop:"saleChannel"}},[t("Select",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{clearable:!0,placeholder:"请选择销售渠道"},model:{value:e.form.saleChannel,callback:function(t){e.$set(e.form,"saleChannel",t)},expression:"form.saleChannel"}},e._l(e.sellList,(function(r,a){return t("Option",{key:a,attrs:{value:r.value}},[e._v(e._s(r.label))])})),1)],1),t("FormItem",{attrs:{prop:"unit"}},[t("Select",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{clearable:!0,placeholder:"请选择统计维度"},on:{"on-change":function(t){e.date="",e.resetField(["startTime","endTime"])}},model:{value:e.form.unit,callback:function(t){e.$set(e.form,"unit",t)},expression:"form.unit"}},e._l(e.cycleList,(function(r,a){return t("Option",{key:a,attrs:{value:r.id}},[e._v(e._s(r.value))])})),1)],1),t("FormItem",{attrs:{prop:"cardForm"}},[t("Select",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{clearable:!0,placeholder:"请选择卡类别"},model:{value:e.form.cardForm,callback:function(t){e.$set(e.form,"cardForm",t)},expression:"form.cardForm"}},e._l(e.typeList,(function(r,a){return t("Option",{key:a,attrs:{value:r.value}},[e._v(e._s(r.label))])})),1)],1),t("FormItem",{attrs:{prop:"endTime"}},["1"!=e.form.unit?t("FormItem",{attrs:{prop:"startTime"}},[t("DatePicker",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{format:"yyyyMMdd",editable:!1,type:"daterange",placeholder:"选择时间段",clearable:""},on:{"on-change":e.checkDatePicker},model:{value:e.date,callback:function(t){e.date=t},expression:"date"}})],1):e._e(),"1"==e.form.unit?t("FormItem",{attrs:{prop:"startTime"}},[t("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择开始月份",editable:!1},on:{"on-change":function(t){return e.checkDatePicker(t,1)}}})],1):e._e(),"1"==e.form.unit?t("FormItem",{attrs:{prop:"endTime"}},[t("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择结束月份",editable:!1},on:{"on-change":function(t){return e.checkDatePicker(t,2)}}})],1):e._e()],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",size:"large"},on:{click:function(t){return e.search()}}},[e._v("搜索")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{"margin-left":"20px"},attrs:{type:"success",icon:"ios-cloud-download-outline",size:"large"},on:{click:function(t){return e.exportTable()}}},[e._v("导出")])],1)],1)],1),t("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:e.columns12,data:e.data,loading:e.loading}}),t("div",{staticStyle:{"margin-left":"38%","margin-top":"100px","margin-bottom":"160px"}},[t("Page",{attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1),e.data1.length?t("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:e.columns12,data:e.data1,loading:e.loading}}):e._e()],1)},n=[],o=r("5530"),i=(r("caad"),r("14d9"),r("b680"),r("d3b7"),r("2532"),r("3ca3"),r("159b"),r("ddb0"),r("2b3d"),r("bf19"),r("9861"),r("88a7"),r("271a"),r("5494"),r("1c31")),u=r("b35e"),l={mixins:[u["a"]],data:function(){return{date:"",loading:!1,form:{cardForm:null,endTime:"",saleChannel:null,startTime:"",unit:""},rule:{startTime:[{required:!0,message:"请选择时间"}],endTime:[{required:!0,message:"请选择时间"}],unit:[{required:!0,message:"请选择维度"}]},total:0,currentPage:1,cycleList:[{id:2,value:"日"},{id:1,value:"月"}],typeList:[{value:"1",label:"普通卡（实体卡）"},{value:"2",label:"Esim卡"},{value:"3",label:"贴片卡"},{value:"4",label:"IMSI号"}],sellList:[{value:"102",label:"API"},{value:"103",label:"官网（H5）"},{value:"104",label:"北京移动"},{value:"105",label:"批量售卖"},{value:"106",label:"推广活动"},{value:"110",label:"测试渠道"},{value:"111",label:"合作发卡"},{value:"112",label:"后付费发卡"},{value:"113",label:"WEB"},{value:"114",label:"流量池WEB"}],columns12:[{title:"卡类型",key:"cardForm",align:"center",render:function(e,t){var r={1:"普通卡（实体卡）",2:"Esim卡",3:"贴片卡",4:"IMSI号"};return e("span",t.row.cardForm?r[t.row.cardForm]:"合计")}},{title:"销售渠道",key:"salesChannel",align:"center",render:function(e,t){var r={102:"API",103:"官网（H5）",104:"北京移动",105:"批量售卖",106:"推广活动",110:"测试渠道",111:"合作发卡",112:"后付费发卡",113:"WEB",114:"流量池WEB"};return e("span",r[t.row.salesChannel])}},{title:"时间",key:"statTime",align:"center"},{title:"本期销量",key:"salesVolume",align:"center"},{title:"港币收入",key:"hkdIncome",align:"center",render:function(e,t){return e("span",t.row.hkdIncome.toFixed(6))}},{title:"人民币收入",key:"cnyIncome",align:"center",render:function(e,t){return e("span",t.row.cnyIncome.toFixed(6))}},{title:"美元收入",key:"usdIncome",align:"center",render:function(e,t){return e("span",t.row.usdIncome.toFixed(6))}},{title:"总收入(单位港币)",key:"totalIncome",align:"center",render:function(e,t){return e("span",t.row.totalIncome+"")}}],data:[],data1:[]}},created:function(){this.rule.startTime.push({validator:this.validateDate,trigger:"change"}),this.rule.endTime.push({validator:this.validateDate,trigger:"change"})},mounted:function(){},methods:{resetField:function(e){this.$refs["form"].fields.forEach((function(t){e.includes(t.prop)&&t.resetField()}))},checkDatePicker:function(e,t){Array.isArray(e)?(this.form.startTime=e[0],this.form.endTime=e[1]):1===t?this.form.startTime=e:this.form.endTime=e},goPageFirst:function(e){var t=this;0===e&&(this.currentPage=1);var r=this,a=this.currentPage,n=10;this.$refs["form"].validate((function(u){u?(t.loading=!0,Object(i["c"])(Object(o["a"])({pageNum:a,pageSize:n},t.form)).then((function(a){"0000"==a.code&&(r.loading=!1,t.page=e,t.total=a.data.total,t.data=a.data.record[0].cardSaleDTO,a.data.record[0].cardSaleTotal?t.data1=[a.data.record[0].cardSaleTotal]:t.data1=[])})).catch((function(e){console.error(e)})).finally((function(){t.loading=!1}))):t.$Message.error("参数校验不通过")}))},goPage:function(e){this.goPageFirst(e)},search:function(){this.goPageFirst(0)},exportTable:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(i["d"])(Object(o["a"])({},e.form)).then((function(e){var t=e.data,r="卡销售报表.csv";if("download"in document.createElement("a")){var a=document.createElement("a"),n=URL.createObjectURL(t);a.download=r,a.href=n,a.click(),URL.revokeObjectURL(n)}else navigator.msSaveBlob(t,r)})).catch((function(){return e.downloading=!1}))}))},details:function(e){this.$router.push({path:"/channel/detailsList"})}}},s=l,c=r("2877"),d=Object(c["a"])(s,a,n,!1,null,null,null);t["default"]=d.exports},"841c":function(e,t,r){"use strict";var a=r("c65b"),n=r("d784"),o=r("825a"),i=r("7234"),u=r("1d80"),l=r("129f"),s=r("577e"),c=r("dc4a"),d=r("14c3");n("search",(function(e,t,r){return[function(t){var r=u(this),n=i(t)?void 0:c(t,e);return n?a(n,t,r):new RegExp(t)[e](s(r))},function(e){var a=o(this),n=s(e),i=r(t,a,n);if(i.done)return i.value;var u=a.lastIndex;l(u,0)||(a.lastIndex=0);var c=d(a,n);return l(a.lastIndex,u)||(a.lastIndex=u),null===c?-1:c.index}]}))},b35e:function(e,t,r){"use strict";r("d9e2");t["a"]={methods:{validateDate:function(e,t,r){var a=this.form.endDate||this.form.endTime,n=this.form.startDate||this.form.startTime;a&&n?"startDate"===e.field||"startTime"===e.field?this.$time(t,">",a)?r(new Error("开始时间不能大于结束时间")):r():this.$time(t,"<",a)?r(new Error("结束时间不能小于开始时间")):r():r()}}}},b680:function(e,t,r){"use strict";var a=r("23e7"),n=r("e330"),o=r("5926"),i=r("408a"),u=r("1148"),l=r("d039"),s=RangeError,c=String,d=Math.floor,m=n(u),f=n("".slice),p=n(1..toFixed),h=function(e,t,r){return 0===t?r:t%2===1?h(e,t-1,r*e):h(e*e,t/2,r)},g=function(e){var t=0,r=e;while(r>=4096)t+=12,r/=4096;while(r>=2)t+=1,r/=2;return t},v=function(e,t,r){var a=-1,n=r;while(++a<6)n+=t*e[a],e[a]=n%1e7,n=d(n/1e7)},b=function(e,t){var r=6,a=0;while(--r>=0)a+=e[r],e[r]=d(a/t),a=a%t*1e7},y=function(e){var t=6,r="";while(--t>=0)if(""!==r||0===t||0!==e[t]){var a=c(e[t]);r=""===r?a:r+m("0",7-a.length)+a}return r},x=l((function(){return"0.000"!==p(8e-5,3)||"1"!==p(.9,0)||"1.25"!==p(1.255,2)||"1000000000000000128"!==p(0xde0b6b3a7640080,0)}))||!l((function(){p({})}));a({target:"Number",proto:!0,forced:x},{toFixed:function(e){var t,r,a,n,u=i(this),l=o(e),d=[0,0,0,0,0,0],p="",x="0";if(l<0||l>20)throw new s("Incorrect fraction digits");if(u!==u)return"NaN";if(u<=-1e21||u>=1e21)return c(u);if(u<0&&(p="-",u=-u),u>1e-21)if(t=g(u*h(2,69,1))-69,r=t<0?u*h(2,-t,1):u/h(2,t,1),r*=4503599627370496,t=52-t,t>0){v(d,0,r),a=l;while(a>=7)v(d,1e7,0),a-=7;v(d,h(10,a,1),0),a=t-1;while(a>=23)b(d,1<<23),a-=23;b(d,1<<a),v(d,1,1),b(d,2),x=y(d)}else v(d,0,r),v(d,1<<-t,0),x=y(d)+m("0",l);return l>0?(n=x.length,x=p+(n<=l?"0."+m("0",l-n)+x:f(x,0,n-l)+"."+f(x,n-l))):x=p+x,x}})}}]);