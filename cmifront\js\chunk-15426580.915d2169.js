(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-15426580"],{"646c":function(e,t,n){"use strict";n("9331")},"7e89":function(e,t,n){"use strict";n.d(t,"d",(function(){return r})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return l})),n.d(t,"e",(function(){return c})),n.d(t,"a",(function(){return u}));var a=n("66df"),i="cms",r=function(e){return a["a"].request({url:i+"/channel/getTopChannel",params:e,method:"get"})},o=function(e){return a["a"].request({url:i+"/channel/deleteTopChannel",params:e,method:"delete"})},l=function(e){return a["a"].request({url:i+"/channel/getCorpList",params:e,method:"get"})},c=function(e){return a["a"].request({url:i+"/channel/newTopChannel",data:e,method:"post"})},u=function(e){return a["a"].request({url:i+"/channel/updateTopChannel",data:e,method:"put"})}},9331:function(e,t,n){},f6e3:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padiing:"16px"}},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 2px"},attrs:{type:"info"},on:{click:function(t){return e.ZeroChannel()}}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"md-add"}}),e._v(" 新增\n\t\t")],1)]),t("div",{staticStyle:{margin:"20px 2px"}},[t("Table",{attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.tableLoading},scopedSlots:e._u([{key:"action",fn:function(n){var a=n.row;n.index;return[t("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(t){return e.zeroChannelupdate(a)}}},[e._v("编辑")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],attrs:{type:"error",size:"small"},on:{click:function(t){return e.deleteList(a)}}},[e._v("删除")])]}}])}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.total,"page-size":e.pageSize,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.loadByPage}})],1)],1)},i=[],r=(n("d81d"),n("14d9"),n("e9c4"),n("d3b7"),n("7e89")),o={data:function(){var e=this;return{columns:[{title:"渠道商名称",key:"corpName",align:"center",minWidth:120,tooltip:!0},{title:"渠道商状态",key:"isSub",align:"center",minWidth:120,render:function(e,t){var n=t.row,a="1"==n.isSub?"#00cc66":"#ff0000",i="1"==n.isSub?"允许订购":"2"==n.isSub?"不允许订购":"";return e("label",{style:{color:a}},i)}},{title:"渠道商AppKey",key:"appkey",align:"center",minWidth:130,tooltip:!0},{title:"渠道商AppSecret",key:"appsecret",align:"center",minWidth:140,tooltip:!0},{title:"关联子渠道商列表",key:"subChannel",align:"center",minWidth:140,render:function(t,n){var a=n.row,i="";return null!=a.relationChannel&&(i=a.relationChannel[0].corpName),null!=a.relationChannel&&a.relationChannel.length>1?t("div",[t("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[t("span",{style:{display:"block"}},a.relationChannel[0].corpName),t("span",{},a.relationChannel[1].corpName),t("span",{},"…"),t("ul",{slot:"content",style:{whiteSpace:"normal",wordBreak:"break-all"}},e.tableData[n.index].relationChannel.map((function(e){return t("li",e.corpName)})))])]):(i=i,t("label",i))}},{title:"操作",slot:"action",minWidth:200,align:"center"}],tableData:[{}],tableLoading:!1,total:0,pageSize:10,currentPage:1}},methods:{init:function(){this.goPageFirst(1)},goPageFirst:function(e){var t=this;this.tableLoading=!0;var n=this;Object(r["d"])({pageSize:this.pageSize,pageNum:e}).then((function(a){"0000"==a.code&&(n.loading=!1,t.tableLoading=!1,t.currentPage=e,t.total=a.count,t.tableData=a.data)})).catch((function(e){console.error(e)})).finally((function(){n.loading=!1,t.tableLoading=!1}))},loadByPage:function(e){this.goPageFirst(e)},deleteList:function(e){var t=this;null!==e.relationChannel?this.$Modal.warning({title:"存在关联子渠道商，不允许删除零级渠道!"}):this.$Modal.confirm({title:"确认删除该零级渠道商？",onOk:function(){Object(r["b"])({corpId:e.corpId}).then((function(e){"0000"===e.code?(t.init(),t.$Notice.success({title:"操作提示",desc:"操作成功"})):t.$Notice.error({title:"操作提示",desc:"操作失败"})}))}})},ZeroChannel:function(){this.$router.push({name:"zeroChannelAdd"})},zeroChannelupdate:function(e){var t=e;this.$router.push({name:"zeroChannelUpdate",query:{obj:encodeURIComponent(JSON.stringify(t))}})}},mounted:function(){this.init()}},l=o,c=(n("646c"),n("2877")),u=Object(c["a"])(l,a,i,!1,null,null,null);t["default"]=u.exports}}]);