(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-18b344ff"],{"00b4":function(t,e,a){"use strict";a("ac1f");var s=a("23e7"),n=a("c65b"),i=a("1626"),o=a("825a"),r=a("577e"),l=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),c=/./.test;s({target:"RegExp",proto:!0,forced:!l},{test:function(t){var e=o(this),a=r(t),s=e.exec;if(!i(s))return n(c,e,a);var l=n(s,e,a);return null!==l&&(o(l),!0)}})},"12d7":function(t,e,a){"use strict";e["a"]={roleList:[{key:"0",value:"角色1"},{key:"1",value:"角色2"},{key:"2",value:"角色3"}],currencyList:[{key:"0",value:"人民币"},{key:"1",value:"美元"},{key:"2",value:"港币"}],queryTypeList:[{key:"0",value:"年统计"},{key:"1",value:"季度统计"},{key:"2",value:"月统计"}],seasonList:[{key:"1",value:"第一季度"},{key:"2",value:"第二季度"},{key:"3",value:"第三季度"},{key:"4",value:"第四季度"}],operatorList:[{key:"1",value:"运营商1"},{key:"2",value:"运营商2"},{key:"3",value:"运营商3"},{key:"4",value:"运营商4"}],userTypeList:[{key:"1",value:"运营商"},{key:"2",value:"企业"}]}},"178f":function(t,e,a){"use strict";a.r(e);a("498a");var s=function(){var t=this,e=t._self._c;return e("div",[e("Card",[e("div",{staticClass:"search_head"},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.$t("sys.accountT"))+"：")]),e("Input",{staticStyle:{width:"200px","margin-right":"20px"},attrs:{placeholder:t.$t("sys.wholesalerName"),clearable:""},model:{value:t.username,callback:function(e){t.username="string"===typeof e?e.trim():e},expression:"username"}}),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.$t("sys.persona"))+"：")]),e("Select",{staticStyle:{width:"200px","margin-right":"20px"},attrs:{placeholder:t.$t("sys.roleType"),clearable:""},model:{value:t.roleType,callback:function(e){t.roleType=e},expression:"roleType"}},t._l(t.roleListFirst,(function(a){return e("Option",{key:a.id,attrs:{value:a.id}},[t._v(t._s(a.roleName))])})),1),e("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{"margin-right":"20px"},attrs:{icon:"md-search",type:"primary"},on:{click:function(e){return t.searchByCondition()}}},[t._v(t._s(t.$t("common.search")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{icon:"md-add",type:"success"},on:{click:function(e){return t.showUserModal(null,0)}}},[t._v(t._s(t.$t("sys.add")))])],1),e("div",{staticStyle:{"margin-top":"20px"}},[e("Table",{attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.loading},scopedSlots:t._u([{key:"username",fn:function(e){var a=e.row;return[t._v("\n\t\t\t\t\t\t"+t._s(a.username)+"\n\t\t\t\t\t")]}},{key:"type",fn:function(e){var a=e.row;return[t._v("\n\t\t\t\t\t\t"+t._s(a.roleName)+"\n\t\t\t\t\t")]}},{key:"status",fn:function(e){var a=e.row;return[t._v("\n\t\t\t\t\t\t"+t._s("1"===a.status?t.$t("order.Normal"):t.$t("common.frozen"))+"\n\t\t\t\t\t")]}},{key:"action",fn:function(a){var s=a.row;a.index;return[e("Button",{directives:[{name:"show",rawName:"v-show",value:t.userId!==s.id,expression:"userId !== row.id"},{name:"has",rawName:"v-has",value:"edit",expression:"'edit'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.showUserModal(s,1)}}},[t._v(t._s(t.$t("common.edit")))]),"1"!==s.status?e("Button",{directives:[{name:"show",rawName:"v-show",value:t.userId!==s.id,expression:"userId !== row.id"},{name:"preventReClick",rawName:"v-preventReClick"},{name:"has",rawName:"v-has",value:"unfreeze",expression:"'unfreeze'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"success",size:"small"},on:{click:function(e){return t.changeStatus(s)}}},[t._v(t._s(t.$t("common.thaw")))]):e("Button",{directives:[{name:"show",rawName:"v-show",value:t.userId!==s.id,expression:"userId !== row.id"},{name:"has",rawName:"v-has",value:"freeze",expression:"'freeze'"},{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{"margin-right":"10px"},attrs:{type:"warning",size:"small"},on:{click:function(e){return t.changeStatus(s)}}},[t._v(t._s(t.$t("common.frozen")))]),e("Button",{directives:[{name:"show",rawName:"v-show",value:t.userId!==s.id,expression:"userId !== row.id"},{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"},{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"error",size:"small"},on:{click:function(e){return t.deleteWarning(s)}}},[t._v(t._s(t.$t("common.del")))])]}}])})],1),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1)]),e("Modal",{attrs:{title:t.$t("support.edit2"),"mask-closable":!1},on:{"on-cancel":t.cancelModal},model:{value:t.userModal,callback:function(e){t.userModal=e},expression:"userModal"}},[t.userModal?e("Form",{ref:"editForm",attrs:{model:t.modalData,rules:t.rules},nativeOn:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.userAddOrEdit.apply(null,arguments)}}},[e("FormItem",{attrs:{prop:"username"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"15%"}},[t._v(t._s(t.$t("sys.account")))]),e("Input",{staticStyle:{width:"83%"},attrs:{disabled:1===t.optType,placeholder:t.$t("sys.paccount")},model:{value:t.modalData.username,callback:function(e){t.$set(t.modalData,"username","string"===typeof e?e.trim():e)},expression:"modalData.username"}})],1)]),e("FormItem",{attrs:{prop:"email"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"15%"}},[t._v(t._s(t.$t("sys.enterEmail")))]),e("Input",{staticStyle:{width:"82%"},attrs:{placeholder:t.$t("sys.inputEmail")},model:{value:t.modalData.email,callback:function(e){t.$set(t.modalData,"email",e)},expression:"modalData.email"}})],1)]),0==t.optType||1==t.optType&&t.toRepwd?e("FormItem",{attrs:{prop:"newPwd"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"15%"}},[t._v(t._s(t.$t("sys.setPassword")))]),e("Input",{staticStyle:{width:"82%"},attrs:{type:"password",password:"",placeholder:t.$t("sys.enterPassword")},model:{value:t.modalData.newPwd,callback:function(e){t.$set(t.modalData,"newPwd",e)},expression:"modalData.newPwd"}})],1)]):t._e(),0==t.optType||1==t.optType&&t.toRepwd?e("FormItem",{attrs:{prop:"rePwd"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"15%"}},[t._v(t._s(t.$t("sys.rePwd")))]),e("Input",{staticStyle:{width:"82%"},attrs:{type:"password",password:"",placeholder:t.$t("sys.prePwd")},model:{value:t.modalData.rePwd,callback:function(e){t.$set(t.modalData,"rePwd",e)},expression:"modalData.rePwd"}})],1)]):t._e(),0==t.optType||1==t.optType&&t.toRepwd?e("div",[e("Alert",{attrs:{type:"warning","show-icon":""}},[t._v(t._s(t.$t("address.PwdRules"))),e("a",{attrs:{href:"#"},on:{click:function(e){t.showRules=!0}}},[t._v(t._s(t.$t("address.watch")))]),t._v(t._s(t.$t("address.more"))+"\n\t\t\t\t\t")]),t.showRules?e("div",{staticClass:"password-rule-notice"},["zh-CN"===this.$i18n.locale?e("Alert",{attrs:{type:"warning",closable:""},on:{"on-close":function(e){t.showRules=!1}}},[e("text-view")],1):e("Alert",{attrs:{type:"warning",closable:""},on:{"on-close":function(e){t.showRules=!1}}},[e("texten-view")],1)],1):t._e()],1):t._e(),e("FormItem",{staticStyle:{"margin-top":"20px"},attrs:{prop:"type"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"15%"}},[t._v(t._s(t.$t("sys.role"))+"：")]),e("Select",{staticStyle:{width:"82%"},attrs:{placeholder:t.$t("sys.roleType")},model:{value:t.modalData.type,callback:function(e){t.$set(t.modalData,"type",e)},expression:"modalData.type"}},t._l(t.roleList,(function(a){return e("Option",{key:a.id,attrs:{value:a.id}},[t._v(t._s(a.roleName)+"\n\t\t\t\t\t\t\t")])})),1)],1)]),e("FormItem",{attrs:{prop:"mobileNumber"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"15%"}},[t._v(t._s(t.$t("sys.phone"))+":")]),e("Input",{staticStyle:{width:"82%"},attrs:{placeholder:t.$t("sys.enterPhone")},model:{value:t.modalData.mobileNumber,callback:function(e){t.$set(t.modalData,"mobileNumber",e)},expression:"modalData.mobileNumber"}})],1)]),""==t.isCorp||"undefined"==t.isCorp||"null"==t.isCorp?e("FormItem",{attrs:{prop:"functionStatus"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"15%"}},[t._v("账号职能:")]),e("RadioGroup",{on:{"on-change":t.changeFunction},model:{value:t.modalData.functionStatus,callback:function(e){t.$set(t.modalData,"functionStatus",e)},expression:"modalData.functionStatus"}},[e("Radio",{attrs:{label:"2"}},[e("Icon",{attrs:{type:"md-people"}}),e("span",[t._v("销售人员")])],1),e("Radio",{attrs:{label:"1"}},[e("Icon",{attrs:{type:"ios-people"}}),e("span",[t._v("普通账号")])],1),e("Radio",{attrs:{label:"3"}},[e("Icon",{attrs:{type:"md-person"}}),e("span",[t._v("大区经理")])],1)],1)],1)]):t._e(),"2"==t.modalData.functionStatus||"3"==t.modalData.functionStatus?e("FormItem",{attrs:{prop:"region",rules:"2"==t.modalData.functionStatus?t.rules.region2:"3"==t.modalData.functionStatus?t.rules.region3:[{required:!1}]}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"15%"}},[t._v("归属大区:")]),e("Select",{staticStyle:{width:"82%"},attrs:{placeholder:"请选择归属大区",clearable:"",filterable:"",multiple:t.isMultiple},model:{value:t.modalData.region,callback:function(e){t.$set(t.modalData,"region",e)},expression:"modalData.region"}},t._l(t.regionList,(function(a){return e("Option",{key:a.id,attrs:{value:a.id}},[t._v(t._s(a.regionName))])})),1)],1)]):t._e(),1==t.optType&&t.toRepwd?e("div",{staticStyle:{width:"100%",display:"flex","justify-content":"flex-end"}},[e("a",{attrs:{href:"#"},on:{click:t.exitUpdatePassword}},[t._v(t._s(t.$t("sys.exitModifyPassword")))])]):t._e(),1!=t.optType||t.toRepwd?t._e():e("div",{staticStyle:{width:"100%",display:"flex","justify-content":"flex-end"}},[e("a",{attrs:{href:"#"},on:{click:function(e){t.toRepwd=!0}}},[t._v(t._s(t.$t("common.rePwd")))])])],1):t._e(),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.userAddOrEdit}},[t._v(t._s(t.$t("common.determine")))])],1)],1)],1)},n=[],i=(a("d9e2"),a("99af"),a("4de4"),a("caad"),a("14d9"),a("d3b7"),a("ac1f"),a("00b4"),a("2532"),a("159b"),a("fe07")),o=a("5d38"),r=a("631c"),l=(a("12d7"),a("8963")),c=a("a287"),u={components:{TextView:o["default"],TextenView:r["default"]},data:function(){var t=this,e=function(e,a,s){var n=/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/;if(0!=n.test(a))if(/(.)\1{2}/i.test(a))s(new Error(t.$t("address.appear")));else{var i=/((?:0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)|9(?=0)){2}\d)/;i.test(a)?s(new Error(t.$t("address.allowed"))):s()}else s(new Error(t.$t("address.reset")))},a=function(e,a,s){a&&-1!=a.indexOf("@")?s():s(new Error(t.$t("address.emailaddress")))},s=function(e,a,s){a?s():s(new Error(t.$t("sys.pleaseSetRoles")))},n=function(e,a,s){if(!a)return s(new Error(t.$t("sys.phoneNoEmpty")));/^[0-9]*$/.test(a)?s():s(t.$t("sys.onlyNumbers"))};return{tableData:[],toRepwd:!1,cooling:"",modalData:{username:"",type:"",memberId:"",mobileNumber:"",functionStatus:"",region:[]},optType:0,roleListFirst:[],roleList:[],regionList:[],roleType:null,username:"",userModal:!1,columns:[{title:i["a"].t("sys.accountT"),slot:"username",align:"center"},{title:this.$t("address.mailbox"),key:"email",align:"center"},{title:this.$t("sys.phone"),key:"mobileNumber",align:"center"},{title:i["a"].t("sys.role"),slot:"type",align:"center"},{title:"账号职能",key:"functionStatus",align:"center",render:function(t,e){var a=e.row,s="1"==a.functionStatus?"普通账号":"2"==a.functionStatus?"销售人员":"3"==a.functionStatus?"大区经理":"";return t("label",s)}},{title:i["a"].t("sys.status"),slot:"status",align:"center"}],public:[{title:i["a"].t("sys.opt"),slot:"action",align:"center"}],loading:!1,currentPage:1,page:0,total:0,rules:{newPwd:[{validator:e,trigger:"blur"}],rePwd:[{validator:e,trigger:"blur"}],email:[{validator:a,trigger:"blur"}],username:[{required:!0,message:this.$t("sys.paccount"),trigger:"blur"},{validator:function(e,a,s){a&&a.includes("&")?s(new Error(t.$t("sys.userNameAmpersand"))):s()},trigger:"blur"}],type:[{validator:s,trigger:"blur"}],mobileNumber:[{validator:n,trigger:"blur"},{max:20,message:this.$t("sys.twentyDigits")}],functionStatus:[{required:!0,message:"请选择账号职能",trigger:"change"}],region2:[{required:!0,message:"请选择归属大区",trigger:"change"}],region3:[{type:"array",required:!0,message:"请选择归属大区",trigger:"change"}]},userId:"",isCorp:"",showRules:!1,isMultiple:!0}},created:function(){this.userId=this.$store.state.user.userId,this.isCorp=sessionStorage.getItem("corpId")},computed:{},methods:{exitUpdatePassword:function(){this.toRepwd=!1,this.resetField(["newPwd",["rePwd"]]),delete this.modalData.newPwd,delete this.modalData.rePwd},resetField:function(t){this.$refs["editForm"].fields.forEach((function(e){t.includes(e.prop)&&e.resetField()}))},showUserModal:function(t,e){this.optType=e,0==e?this.modalData={username:null,type:null,memberId:null,mobileNumber:null,functionStatus:"1",region:[]}:(this.modalData={type:t.roleId,status:t.status,id:t.id,username:t.username,newPwd:null,email:t.email,mobileNumber:t.mobileNumber,functionStatus:t.functionStatus,region:t.region},"2"==t.functionStatus?this.isMultiple=!1:this.isMultiple=!0),this.getRoleList(),this.getRegionList(),this.userModal=!0},cancelModal:function(){this.userModal=!1,this.showRules=!1,this.toRepwd=!1,delete this.modalData.newPwd,delete this.modalData.rePwd},userAddOrEdit:function(){var t=this;this.$refs.editForm.validate((function(e){e&&(1===t.optType?t.edit():t.add())}))},add:function(){var t=this;if(this.modalData.newPwd===this.modalData.rePwd){var e,a=[];"2"==this.modalData.functionStatus?(a.push(this.modalData.region),e=a):e="1"==this.modalData.functionStatus?[]:this.modalData.region,Object(c["a"])({userName:this.modalData.username,passwd:this.modalData.newPwd,email:this.modalData.email,role:this.modalData.type,mobileNumber:this.modalData.mobileNumber,functionStatus:this.modalData.functionStatus,region:e}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("sys.successAddedUser")}),t.userModal=!1,t.goPageFirst(0)})).catch((function(t){console.log(t)})).finally((function(){t.loading=!1}))}else this.$Notice.warning({title:this.$t("address.Operationreminder"),desc:this.$t("sys.passwordsNotMatch")})},edit:function(){var t=this;if(this.toRepwd&&this.modalData.newPwd!==this.modalData.rePwd)this.$Notice.warning({title:this.$t("address.Operationreminder"),desc:this.$t("sys.passwordsNotMatch")});else{var e,a=[];"2"==this.modalData.functionStatus?(a.push(this.modalData.region),e=a):e="1"==this.modalData.functionStatus?[]:this.modalData.region,Object(c["c"])({id:this.modalData.id,userName:this.modalData.username,passwd:this.modalData.newPwd,email:this.modalData.email,status:this.modalData.status,role:this.modalData.type,mobileNumber:this.modalData.mobileNumber,functionStatus:this.modalData.functionStatus,region:e}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("sys.userInforupdated")}),t.userModal=!1,t.toRepwd=!1,t.currentPage=1,t.goPageFirst(0)})).catch((function(t){console.log(t)})).finally((function(){t.loading=!1}))}},goPageFirst:function(t){var e=this;this.page=t,this.loading=!0;var a={userName:this.username,roleId:this.roleType,page:t,pageSize:10};0===t&&(this.currentPage=1),Object(c["d"])(a).then((function(t){if(!t||"0000"!=t.code)throw t;e.tableData=t.data.records,e.total=t.data.total})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))},searchByCondition:function(){this.goPageFirst(0)},delete:function(t){var e=this;Object(c["b"])({id:t}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:e.$t("common.Successful"),desc:e.$t("sys.successDeleted")}),e.goPageFirst(e.page)})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))},changeStatus:function(t){var e=this;this.$Notice.warning({title:this.$t("address.Operationreminder"),name:"changeStatus",desc:"",render:function(a){return a("div",[e.$t("sys.operationWill"),a("span",{style:{marginRight:"10px",marginLeft:"10px",color:"#ED4014"}},(1==t.status?e.$t("common.frozen"):e.$t("common.thaw"))+e.$t("sys.user")+t.username),e.$t("sys.continueExecution"),a("br"),a("div",[a("Button",{props:{type:"dashed",size:"small"},style:{marginTop:"15px",marginLeft:"100px"},on:{click:function(){e.$Notice.close("changeStatus")}}},e.$t("common.cancel")),a("Button",{props:{type:"info",size:"small"},style:{marginTop:"15px",marginLeft:"20px"},on:{click:function(){e.$Notice.close("changeStatus"),e.doChangeStatus(t)}}},1==t.status?e.$t("common.frozen"):e.$t("common.thaw"))])])},duration:0})},doChangeStatus:function(t){var e=this;Object(c["c"])({id:t.id,userName:t.username,email:t.email,status:"1"===t.status?"2":"1",role:t.roleId,mobileNumber:t.mobileNumber,functionStatus:t.functionStatus,region:t.region}).then((function(t){if("0000"!==t.code)throw t;e.$Notice.success({title:e.$t("common.Successful"),desc:e.$t("sys.userStatusUpdated")}),e.goPageFirst(e.page)})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))},goPage:function(t){this.goPageFirst(t)},getRoleListFirstPage:function(){var t=this;Object(l["c"])({containChannel:!0}).then((function(e){if("0000"!==e.code)throw e;t.roleListFirst=e.data})).catch((function(t){console.log(t)})).finally((function(){}))},getRoleList:function(){var t=this;Object(l["c"])({}).then((function(e){if("0000"!==e.code)throw e;t.roleList=e.data})).catch((function(t){console.log(t)})).finally((function(){}))},error:function(t){this.$Notice.error({title:this.$t("sys.wrong"),desc:t||this.$t("sys.Serverwrong")})},cancel:function(){},deleteWarning:function(t){var e=this;this.$Notice.warning({title:this.$t("address.Operationreminder"),name:"delete",desc:this.$t("sys.confirmDeleteUser")+"？",render:function(a){return a("div",[e.$t("sys.confirmDeleteUser")+"[",a("span",{style:{marginRight:"10px",marginLeft:"10px",color:"#ff0000"}},t.username),"]？",a("br"),a("div",[a("Button",{props:{type:"dashed",size:"small"},style:{marginTop:"10px",marginLeft:"130px"},on:{click:function(){e.$Notice.close("delete")}}},e.$t("common.cancel")),a("Button",{props:{type:"error",size:"small"},style:{marginTop:"10px",marginLeft:"10px"},on:{click:function(){e.$Notice.close("delete"),e.delete(t.id)}}},e.$t("common.del"))])])},duration:0})},changeFunction:function(){this.modalData.region="","2"==this.modalData.functionStatus?this.isMultiple=!1:this.isMultiple=!0},getRegionList:function(){var t=this;Object(c["e"])({}).then((function(e){if("0000"!==e.code)throw e;t.regionList=e.data})).catch((function(t){console.log(t)})).finally((function(){}))}},beforeMount:function(){var t=this.$route.meta.permTypes;(t.includes("edit")||t.includes("delete")||t.includes("freeze")||t.includes("unfreeze"))&&(this.columns=this.columns.concat(this.public))},mounted:function(){this.getRoleListFirstPage(),this.goPageFirst(0),""==this.isCorp||"undefined"==this.isCorp||"null"==this.isCorp?this.columns=this.columns:this.columns=this.columns.filter((function(t){return"functionStatus"!==t.key}))},watch:{getLoading:function(t){1==t?this.loadmsg=this.$Message.loading({content:this.$t("sys.obtainPermissionData"),duration:0}):setTimeout(this.loadmsg,1)},setLoading:function(t){1==t?this.loadmsg=this.$Message.loading({content:this.$t("sys.Saving"),duration:0}):setTimeout(this.loadmsg,1)}}},d=u,p=(a("8472"),a("2877")),m=Object(p["a"])(d,s,n,!1,null,null,null);e["default"]=m.exports},"30af":function(t,e,a){"use strict";var s=a("44c9"),n=a.n(s);e["default"]=n.a},"44c9":function(t,e){},"5d38":function(t,e,a){"use strict";var s=a("7cd1"),n=a("5f69"),i=a("2877"),o=Object(i["a"])(n["default"],s["a"],s["b"],!1,null,null,null);e["default"]=o.exports},"5f69":function(t,e,a){"use strict";var s=a("7e63"),n=a.n(s);e["default"]=n.a},"631c":function(t,e,a){"use strict";var s=a("dc67"),n=a("30af"),i=a("2877"),o=Object(i["a"])(n["default"],s["a"],s["b"],!1,null,null,null);e["default"]=o.exports},"6db0":function(t,e,a){},"7cd1":function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),a.d(e,"b",(function(){return n}));var s=function(){var t=this;t._self._c,t._self._setupProxy;return t._m(0)},n=[function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticStyle:{padding:"10px 10px"}},[e("h4",{staticStyle:{"text-align":"left"}},[t._v("确保口令满足以下通用原则")]),e("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        1、口令至少由8位及以上大写字母、小写字母、数字与特殊符号等4类中3类混合、随机组成，尽量不要以姓名、电话号码以及出生日期等作为口令或者口令的组成部分；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        2、口令应与用户名无相关性，口令中不得包含用户名的完整字符串、大小写变位或形似变换的字符串，如teleadmin:teleadmin、teleadmin:teleadmin2017、teleadmin:TeleAdmin、teleadmin:te!e@dmin等；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        3、应更换系统或设备的出厂默认口令，如huawei:huawei@123，oracle数据库中SYS:CHANGE_ON_INSTALL,某移动定制版光猫默认帐号CMCCAdmin:aDm8H%MdA等；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        4、口令设置应避免3位以上（含3位）键盘排序密码，如qwe（键盘第1行前三个字母）、asd（键盘第2行前三个字母）、qaz（键盘第1列三个字母）、1qaz（键盘第1列数字加前三个字母）、！QAZ（键盘第1列特殊字符加前三个字母）等；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        5、口令中不能出现3位以上（含三位）连续字母、数字、特殊字符，如ABC、Abc、123、！@#等；\n      ")]),e("span",[t._v("\n        6、口令中不能出现3位以上（含三位）重复字母、数字、特殊字符，如AAA、Aaa、111、###等。\n      ")]),e("span",[t._v("\n\t\t\t7、当前密码不能与近10次使用密码重复。\n\t\t")])]),e("h4",{staticStyle:{"text-align":"left"}},[t._v("避免以下易猜解口令规则")]),e("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        1、省份、地市名称、邮箱、电话区号、邮政编码及缩写和简单数字或shift键+简单数字，如BJYD123、HBYD!@#等；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        2、单位名称、专业名称、系统名称、厂家名称（含缩写）和简单数字，如HBnmc123、HBsmc_123等；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        3、维护人员名字全拼大小写缩写等变形+设备IP地址（一位或两位）或出生年月日等，如维护人员张三，维护设备地址,出生日期为19951015，则其可能的弱口令为zhangsan100、zhangsan101，zhangsan10100，zhangsan10101，zhangsan19951015，ZS19951015等；\n      ")])])])}]},"7e63":function(t,e){},8472:function(t,e,a){"use strict";a("6db0")},8963:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"d",(function(){return o})),a.d(e,"c",(function(){return r})),a.d(e,"e",(function(){return l})),a.d(e,"a",(function(){return c}));var s=a("66df"),n="/sys/api/v1",i=function(t){return s["a"].request({url:n+"/user/pagePrivilegesByRole/tree",params:t,method:"get"})},o=function(t){return s["a"].request({url:n+"/user/pagePrivilegesByUser/tree",params:t,method:"get"})},r=function(t){return s["a"].request({url:n+"/role/roles",data:t,method:"post"})},l=function(t){return s["a"].request({url:n+"/role/privileges/set",data:t,method:"post"})},c=function(t){return s["a"].request({url:n+"/role/add",params:t,method:"post"})}},a287:function(t,e,a){"use strict";a.d(e,"d",(function(){return i})),a.d(e,"b",(function(){return o})),a.d(e,"a",(function(){return r})),a.d(e,"c",(function(){return l})),a.d(e,"f",(function(){return c})),a.d(e,"e",(function(){return u}));var s=a("66df"),n="/sys/api/v1",i=function(t){return s["a"].request({url:n+"/account/list",data:t,method:"post"})},o=function(t){return s["a"].request({url:n+"/user/deleteUser",params:t,method:"PUT"})},r=function(t){return s["a"].request({url:n+"/user/signIn",data:t,method:"post"})},l=function(t){return s["a"].request({url:n+"/user/update",data:t,method:"PUT"})},c=function(t){return s["a"].request({url:n+"/user/resetPasswd",data:t,method:"PUT"})},u=function(t){return s["a"].request({url:n+"/region/list",params:t,method:"get"})}},dc67:function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),a.d(e,"b",(function(){return n}));var s=function(){var t=this;t._self._c,t._self._setupProxy;return t._m(0)},n=[function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticStyle:{padding:"10px 10px"}},[e("h4",{staticStyle:{"text-align":"left"}},[t._v("Password character policy:")]),e("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n     1. The password must be 8 characters or more and contain at least one uppercase character, at least one lowercase character, at least one number and at least one special symbol;\n    ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n      2. The password shall not contain any three identical consecutive (ABC, Abc, 123, !@# etc) and repetitive characters (AAA, Aaa, 111, ### etc)\n    ")])])])}]}}]);