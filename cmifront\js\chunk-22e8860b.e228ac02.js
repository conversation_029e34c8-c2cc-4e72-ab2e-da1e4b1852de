(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-22e8860b"],{"10df":function(t,e,n){"use strict";n("81c6")},"3f7e":function(t,e,n){"use strict";var r=n("b5db"),a=r.match(/firefox\/(\d+)/i);t.exports=!!a&&+a[1]},"4e82":function(t,e,n){"use strict";var r=n("23e7"),a=n("e330"),i=n("59ed"),o=n("7b0b"),c=n("07fa"),s=n("083a"),u=n("577e"),l=n("d039"),p=n("addb"),h=n("a640"),f=n("3f7e"),d=n("99f4"),g=n("1212"),m=n("ea83"),C=[],b=a(C.sort),k=a(C.push),v=l((function(){C.sort(void 0)})),y=l((function(){C.sort(null)})),S=h("sort"),D=!l((function(){if(g)return g<70;if(!(f&&f>3)){if(d)return!0;if(m)return m<603;var t,e,n,r,a="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(r=0;r<47;r++)C.push({k:e+r,v:n})}for(C.sort((function(t,e){return e.v-t.v})),r=0;r<C.length;r++)e=C[r].k.charAt(0),a.charAt(a.length-1)!==e&&(a+=e);return"DGBEFHACIJK"!==a}})),L=v||!y||!S||!D,w=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:u(e)>u(n)?1:-1}};r({target:"Array",proto:!0,forced:L},{sort:function(t){void 0!==t&&i(t);var e=o(this);if(D)return void 0===t?b(e):b(e,t);var n,r,a=[],u=c(e);for(r=0;r<u;r++)r in e&&k(a,e[r]);p(a,w(t)),n=c(a),r=0;while(r<n)e[r]=a[r++];while(r<u)s(e,r++);return e}})},"5ea3":function(t,e,n){"use strict";(function(t){var n="object"==typeof t&&t&&t.Object===Object&&t;e["a"]=n}).call(this,n("c8ba"))},"81c6":function(t,e,n){},"91e6":function(t,e,n){"use strict";n("a581")},"99f4":function(t,e,n){"use strict";var r=n("b5db");t.exports=/MSIE|Trident/.test(r)},a581:function(t,e,n){},a70c:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("Card",[e("Form",{ref:"formRef",attrs:{model:t.formData,rules:t.ruleValidate,"label-width":120}},[e("Row",[e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:"规则名称",prop:"ruleName"}},[e("Input",{staticStyle:{width:"400px"},attrs:{placeholder:"请输入规则名称",maxlength:"100"},model:{value:t.formData.ruleName,callback:function(e){t.$set(t.formData,"ruleName",e)},expression:"formData.ruleName"}})],1)],1),e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:"故障资源供应商",prop:"faultSupplier"}},[e("Select",{staticStyle:{width:"400px"},attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:t.formData.faultSupplier,callback:function(e){t.$set(t.formData,"faultSupplier",e)},expression:"formData.faultSupplier"}},t._l(t.supplierList,(function(n){return e("Option",{key:n.supplierId,attrs:{value:n.supplierId}},[t._v(t._s(n.supplierName))])})),1)],1)],1)],1),e("FormItem",{attrs:{label:"目标国家",prop:"targetCountries",required:!0,"show-message":!1}},[e("TargetCountrySelector",{attrs:{"all-countries":t.filteredCountries,"continent-list":t.continentList,"current-continent":t.selectedContinent,"check-all":t.countryCheckAll,indeterminate:t.countryIndeterminate,placeholder:"请选择目标国家","show-error":t.targetCountryError,"error-message":"目标国家不能为空"},on:{"continent-change":t.handleContinentChange,"selection-change":t.handleSelectionChange,"check-all":t.handleCheckAll,input:t.triggerTargetCountriesValidation},model:{value:t.formData.targetCountries,callback:function(e){t.$set(t.formData,"targetCountries",e)},expression:"formData.targetCountries"}}),t.loadingCountries||t.loadingContinents?e("Spin",{attrs:{fix:"",size:"large"}}):t._e()],1),t._l(t.formData.backups,(function(n,r){return e("div",{key:r},[e("Row",{attrs:{gutter:16}},[e("Col",{attrs:{span:"6"}},[e("FormItem",{attrs:{label:"备用资源供应商",prop:"backups.".concat(r,".supplier"),rules:t.ruleValidate.backupSupplier}},[e("Select",{attrs:{filterable:"",clearable:"",placeholder:"请选择",remote:"","remote-method":function(e){return t.remoteSearchSupplier(e,r)},loading:t.loadingBackupSuppliers},on:{"on-change":function(e){return t.handleBackupSupplierChange(r,e)},"on-open-change":function(e){return t.handleOpenBackupSupplierSelect(r,e)}},model:{value:n.supplier,callback:function(e){t.$set(n,"supplier",e)},expression:"backup.supplier"}},[t.loadingBackupSuppliers?e("Option",{attrs:{value:"",disabled:""}},[t._v("加载中")]):t._e(),t._l(t.availableBackupSuppliers(r),(function(n){return e("Option",{key:n.supplierId,attrs:{value:n.supplierId}},[t._v(t._s(n.supplierName))])})),!t.loadingBackupSuppliers&&t.backupSupplierListChecked&&0===t.backupSupplierList.length?e("Option",{attrs:{value:"",disabled:""}},[t._v("无数据")]):t._e()],2)],1)],1),e("Col",{attrs:{span:"7"}},[e("FormItem",{attrs:{label:"备用卡池",prop:"backups.".concat(r,".pool"),rules:t.ruleValidate.backupPool}},[e("Select",{staticClass:"backups-pool-select",attrs:{placeholder:"请选择",clearable:"",filterable:"",remote:"","remote-method":function(e){return t.remoteSearchPool(e,r)},loading:n.loadingPools},on:{"on-open-change":function(e){return e&&t.handleOpenCardPoolSelect(r)}},model:{value:n.pool,callback:function(e){t.$set(n,"pool",e)},expression:"backup.pool"}},[n.loadingPools?e("Option",{attrs:{value:"",disabled:""}},[t._v("加载中")]):t._e(),t._l(n.poolList,(function(n){return e("Option",{key:n.poolId,attrs:{value:n.poolId}},[t._v(t._s(n.poolName))])})),!n.loadingPools&&n.poolListChecked&&n.poolList&&0===n.poolList.length?e("Option",{attrs:{value:"",disabled:""}},[t._v("无数据")]):t._e()],2)],1)],1),e("Col",{attrs:{span:"5"}},[e("FormItem",{attrs:{label:"比例",prop:"backups.".concat(r,".percentage"),rules:t.ruleValidate.percentage,"label-width":60}},[e("InputNumber",{staticStyle:{width:"80%"},attrs:{min:0,max:100,placeholder:"比例"},on:{"on-change":function(e){return t.handlePercentageChange(r,e)}},model:{value:n.percentage,callback:function(e){t.$set(n,"percentage",e)},expression:"backup.percentage"}}),t._v(" %\n        ")],1)],1),e("Col",{attrs:{span:"6"}},[t.formData.backups.length>1?e("FormItem",{attrs:{"label-width":0}},[e("Button",{attrs:{type:"error",size:"small",icon:"ios-remove",title:"移除此备用供应商"},on:{click:function(e){return t.removeBackup(r)}}})],1):t._e(),r===t.formData.backups.length-1?e("FormItem",{attrs:{"label-width":0}},[e("Button",{attrs:{type:"dashed",size:"small",icon:"ios-add",title:"添加备用供应商"},on:{click:t.addBackup}})],1):t._e()],1)],1)],1)})),e("Divider"),e("div",{staticStyle:{"text-align":"center"}},[e("Button",{attrs:{type:"primary",loading:t.submitLoading},on:{click:t.handleSubmit}},[t._v("确认")]),e("Button",{staticStyle:{"margin-left":"8px"},on:{click:t.handleCancel}},[t._v("取消")])],1)],2)],1)},a=[],i=n("2909"),o=n("c7eb"),c=n("1da1"),s=(n("d9e2"),n("99af"),n("4de4"),n("caad"),n("a15b"),n("d81d"),n("14d9"),n("fb6a"),n("4e82"),n("a434"),n("e9c4"),n("a9e3"),n("d3b7"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("2532"),n("3ca3"),n("159b"),n("ddb0"),function(){var t=this,e=t._self._c;return e("div",{staticClass:"multi-column-selector"},[e("Select",{staticClass:"target-country-main-select",staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"",placeholder:t.placeholder},on:{"on-change":t.handleTopSelectChange},model:{value:t.localValue,callback:function(e){t.localValue=e},expression:"localValue"}},t._l(t.sortedCountriesForSelect,(function(n){return e("Option",{key:n.mcc,attrs:{value:n.mcc}},[t._v(t._s(n.countryEn))])})),1),t.showError?e("div",{staticClass:"validation-error"},[t._v(t._s(t.errorMessage))]):t._e(),e("div",{staticClass:"continent-buttons"},[e("Button",{attrs:{type:"all"===t.currentContinent?"primary":"default",size:"small"},on:{click:function(e){return t.selectContinent("all")}}},[t._v("\n      全部\n    ")]),t._l(t.continentList,(function(n){return e("Button",{key:n.continentEn,attrs:{type:t.currentContinent===n.continentEn?"primary":"default",size:"small"},on:{click:function(e){return t.selectContinent(n.continentEn)}}},[t._v("\n      "+t._s(n.continentCn)+"\n    ")])}))],2),e("div",{staticClass:"check-all-container"},[e("Checkbox",{attrs:{indeterminate:t.indeterminate,value:t.checkAll},nativeOn:{click:function(e){return e.preventDefault(),t.handleCheckAll.apply(null,arguments)}}},[t._v("全选")])],1),e("div",{staticClass:"content-wrapper vertical-scroll"},[e("CheckboxGroup",{on:{"on-change":t.handleCheckboxGroupChange},model:{value:t.localValue,callback:function(e){t.localValue=e},expression:"localValue"}},[e("Row",{attrs:{gutter:16}},[t._l(t.displayCountryData,(function(n,r){return e("Col",{key:r,attrs:{xs:24,sm:12,md:8,lg:6}},[e("div",{staticClass:"column-card"},[e("div",{staticClass:"column-header"},[e("span",{staticClass:"header-check"}),e("span",{staticClass:"header-en"},[t._v("国家 (英文)")]),e("span",{staticClass:"header-cn"},[t._v("国家 (中文)")])]),t._l(n.countries,(function(n){return e("div",{key:n.mcc,staticClass:"country-item"},[e("Checkbox",{staticClass:"item-check",attrs:{label:n.mcc}},[e("span")]),e("span",{staticClass:"item-en",attrs:{title:n.countryEn}},[t._v(t._s(n.countryEn))]),e("span",{staticClass:"item-cn",attrs:{title:n.countryCn}},[t._v(t._s(n.countryCn))])],1)}))],2)])})),t.allCountries&&0!==t.allCountries.length?t._e():e("div",{staticClass:"no-data-placeholder"},[t._v("\n          无数据\n        ")])],2)],1)],1)],1)}),u=[];n("a630");function l(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}var p=l,h=n("5ea3"),f="object"==typeof self&&self&&self.Object===Object&&self,d=h["a"]||f||Function("return this")(),g=d,m=function(){return g.Date.now()},C=m,b=/\s/;function k(t){var e=t.length;while(e--&&b.test(t.charAt(e)));return e}var v=k,y=/^\s+/;function S(t){return t?t.slice(0,v(t)+1).replace(y,""):t}var D=S,L=g.Symbol,w=L,$=Object.prototype,x=$.hasOwnProperty,O=$.toString,B=w?w.toStringTag:void 0;function _(t){var e=x.call(t,B),n=t[B];try{t[B]=void 0;var r=!0}catch(i){}var a=O.call(t);return r&&(e?t[B]=n:delete t[B]),a}var j=_,A=Object.prototype,E=A.toString;function T(t){return E.call(t)}var M=T,K="[object Null]",I="[object Undefined]",P=w?w.toStringTag:void 0;function F(t){return null==t?void 0===t?I:K:P&&P in Object(t)?j(t):M(t)}var N=F;function R(t){return null!=t&&"object"==typeof t}var V=R,q="[object Symbol]";function z(t){return"symbol"==typeof t||V(t)&&N(t)==q}var J=z,W=NaN,G=/^[-+]0x[0-9a-f]+$/i,U=/^0b[01]+$/i,H=/^0o[0-7]+$/i,Q=parseInt;function X(t){if("number"==typeof t)return t;if(J(t))return W;if(p(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=p(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=D(t);var n=U.test(t);return n||H.test(t)?Q(t.slice(2),n?2:8):G.test(t)?W:+t}var Y=X,Z="Expected a function",tt=Math.max,et=Math.min;function nt(t,e,n){var r,a,i,o,c,s,u=0,l=!1,h=!1,f=!0;if("function"!=typeof t)throw new TypeError(Z);function d(e){var n=r,i=a;return r=a=void 0,u=e,o=t.apply(i,n),o}function g(t){return u=t,c=setTimeout(k,e),l?d(t):o}function m(t){var n=t-s,r=t-u,a=e-n;return h?et(a,i-r):a}function b(t){var n=t-s,r=t-u;return void 0===s||n>=e||n<0||h&&r>=i}function k(){var t=C();if(b(t))return v(t);c=setTimeout(k,m(t))}function v(t){return c=void 0,f&&r?d(t):(r=a=void 0,o)}function y(){void 0!==c&&clearTimeout(c),u=0,r=s=a=c=void 0}function S(){return void 0===c?o:v(C())}function D(){var t=C(),n=b(t);if(r=arguments,a=this,s=t,n){if(void 0===c)return g(s);if(h)return clearTimeout(c),c=setTimeout(k,e),d(s)}return void 0===c&&(c=setTimeout(k,e)),o}return e=Y(e)||0,p(n)&&(l=!!n.leading,h="maxWait"in n,i=h?tt(Y(n.maxWait)||0,e):i,f="trailing"in n?!!n.trailing:f),D.cancel=y,D.flush=S,D}var rt=nt,at="Expected a function";function it(t,e,n){var r=!0,a=!0;if("function"!=typeof t)throw new TypeError(at);return p(n)&&(r="leading"in n?!!n.leading:r,a="trailing"in n?!!n.trailing:a),rt(t,e,{leading:r,maxWait:e,trailing:a})}var ot=it,ct={name:"TargetCountrySelector",props:{value:{type:Array,default:function(){return[]}},allCountries:{type:Array,required:!0,default:function(){return[]}},placeholder:{type:String,default:"请选择目标国家"},continentList:{type:Array,required:!0,default:function(){return[]}},currentContinent:{type:String,default:"all"},checkAll:{type:Boolean,default:!1},indeterminate:{type:Boolean,default:!1},showError:{type:Boolean,default:!1},errorMessage:{type:String,default:""}},data:function(){return{activeColumns:4,handleResizeThrottled:null,localValue:Object(i["a"])(this.value),sortedCountriesForSelect:[]}},computed:{currentViewCountryMccs:function(){return this.allCountries.map((function(t){return t.mcc})).filter((function(t){return null!=t}))},displayCountryData:function(){var t=this.allCountries.filter((function(t){return null!=t.mcc})),e=t.length;if(0===e)return[];var n=this.activeColumns,r=Array.from({length:n},(function(){return{countries:[]}}));return t.forEach((function(t,e){if(n>0){var a=e%n;r[a]?r[a].countries.push(t):console.error("Error: Trying to push to non-existent column index ".concat(a))}})),r}},methods:{initSortedCountries:function(){0===this.sortedCountriesForSelect.length&&(console.log("初始化全量国家数据，总数:",this.allCountries.length),this.sortedCountriesForSelect=Object(i["a"])(this.allCountries).sort((function(t,e){var n=t.countryEn||"",r=e.countryEn||"";return t.mcc&&e.mcc||console.warn("国家数据缺少 MCC:",t,e),n.localeCompare(r)})))},selectContinent:function(t){this.currentContinent!==t&&this.$emit("continent-change",t)},handleTopSelectChange:function(t){this.$emit("selection-change",t,this.currentViewCountryMccs)},handleCheckAll:function(){var t=this.currentViewCountryMccs,e=this.checkAll;this.$emit("check-all",{currentMccs:t,isCurrentlyAllSelected:e})},handleCheckboxGroupChange:function(t){this.$emit("selection-change",t,this.currentViewCountryMccs)},updateActiveColumns:function(){var t=window.innerWidth;this.activeColumns=t>=1400?4:t>=1100?3:t>=800?2:1},handleResize:function(){this.updateActiveColumns()}},watch:{value:{handler:function(t){JSON.stringify(this.localValue)!==JSON.stringify(t)&&(this.localValue=Object(i["a"])(t))},deep:!0},localValue:{handler:function(t){JSON.stringify(this.value)!==JSON.stringify(t)&&this.$emit("input",t)},deep:!0},allCountries:{handler:function(t){t&&t.length>0&&this.initSortedCountries()},immediate:!0,deep:!0}},created:function(){this.handleResizeThrottled=ot(this.handleResize,200)},mounted:function(){var t=this;this.updateActiveColumns(),window.addEventListener("resize",this.handleResizeThrottled),this.$nextTick((function(){t.initSortedCountries()}))},beforeDestroy:function(){window.removeEventListener("resize",this.handleResizeThrottled),this.handleResizeThrottled&&this.handleResizeThrottled.cancel&&this.handleResizeThrottled.cancel()}},st=ct,ut=(n("ed43"),n("10df"),n("2877")),lt=Object(ut["a"])(st,s,u,!1,null,"6201ca1d",null),pt=lt.exports,ht=n("da8c"),ft={name:"FaultHandlingNew",components:{TargetCountrySelector:pt},data:function(){var t=this,e=function(e,n,r){var a=t.formData.backups.reduce((function(t,e){return t+(Number(e.percentage)||0)}),0);Math.abs(a-100)>.01?r(new Error("比例总和必须等于100%")):r()};return{submitLoading:!1,formData:{ruleName:"",faultSupplier:"",targetCountries:[],backups:[this.createNewBackupItem()]},selectedContinent:"all",countryCheckAll:!1,countryIndeterminate:!1,allCountries:[],filteredCountries:[],continentList:[],supplierList:[],backupSupplierList:[],loadingCountries:!1,loadingContinents:!1,loadingBackupSuppliers:!1,ruleValidate:{ruleName:[{required:!0,message:"规则名称不能为空",trigger:"blur"}],faultSupplier:[{required:!0,message:"故障资源供应商不能为空",trigger:"change"}],targetCountries:[{required:!0,type:"array",min:1,message:"目标国家不能为空",trigger:"change"}],backupSupplier:[{required:!0,message:"备用资源供应商不能为空",trigger:"change"}],backupPool:[{required:!0,message:"备用卡池不能为空",trigger:"change"}],percentage:[{required:!0,type:"number",message:"比例不能为空",trigger:"blur"},{type:"number",min:.01,max:100,message:"比例必须在 0 到 100 之间",trigger:"blur"},{validator:e,trigger:"blur"}]},supplierCache:{mccKey:"",isLoading:!1,noDataKeys:[]},poolCache:{noDataKeys:[]},backupSupplierListChecked:!1,targetCountryError:!1}},computed:{availableBackupSuppliers:function(){var t=this;return function(e){return t.backupSupplierList}},currentViewCountryMccs:function(){return this.filteredCountries.map((function(t){return t.mcc})).filter((function(t){return null!=t}))}},methods:{fetchInitialData:function(){var t=this;return Object(c["a"])(Object(o["a"])().mark((function e(){var n,r;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.loadingContinents=!0,t.loadingCountries=!0,n=Object(ht["d"])().then((function(e){"0000"==e.code&&Array.isArray(e.data)?t.continentList=e.data:t.continentList=[]})).catch((function(e){console.error("获取大洲列表失败:",e),t.continentList=[]})).finally((function(){t.loadingContinents=!1})),r=Object(ht["g"])().then((function(e){"0000"==e.code&&Array.isArray(e.data)?t.supplierList=e.data:t.supplierList=[]})).catch((function(e){console.error("获取供应商列表失败:",e),t.supplierList=[]})),e.next=6,Promise.all([n,r]);case 6:return e.next=8,t.fetchCountries(t.selectedContinent);case 8:case"end":return e.stop()}}),e)})))()},fetchSuppliersByMcc:function(){var t=this;return Object(c["a"])(Object(o["a"])().mark((function e(){var n,r;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.formData.targetCountries&&0!==t.formData.targetCountries.length){e.next=4;break}return t.backupSupplierList=[],t.backupSupplierListChecked=!1,e.abrupt("return");case 4:if(n=t.getMccCacheKey(),!t.supplierCache.noDataKeys||!t.supplierCache.noDataKeys.includes(n)){e.next=11;break}return console.log("此目标国家组合已确认没有可用的备用供应商，跳过请求"),t.backupSupplierList=[],t.loadingBackupSuppliers=!1,t.backupSupplierListChecked=!0,e.abrupt("return");case 11:return t.supplierCache.isLoading=!0,t.loadingBackupSuppliers=!0,t.backupSupplierListChecked=!1,e.prev=14,console.log("获取目标国家对应的备用供应商，请求参数:",{mccList:t.formData.targetCountries}),e.next=18,Object(ht["h"])({mccList:t.formData.targetCountries});case 18:r=e.sent,"0000"===r.code&&Array.isArray(r.data)?(t.backupSupplierList=r.data,console.log("获取备用供应商成功:",t.backupSupplierList),t.supplierCache.mccKey=n,0===r.data.length&&(t.supplierCache.noDataKeys||(t.supplierCache.noDataKeys=[]),t.supplierCache.noDataKeys.includes(n)||t.supplierCache.noDataKeys.push(n),console.log("此目标国家组合没有可用的备用供应商，已记录")),t.checkAndUpdateBackupSuppliers(),t.backupSupplierListChecked=!0):(console.warn("获取备用供应商列表失败:",r),t.backupSupplierList=[],t.backupSupplierListChecked=!0),e.next=27;break;case 22:e.prev=22,e.t0=e["catch"](14),console.error("获取目标国家对应的备用供应商失败:",e.t0),t.backupSupplierList=[],t.backupSupplierListChecked=!0;case 27:return e.prev=27,t.loadingBackupSuppliers=!1,t.supplierCache.isLoading=!1,e.finish(27);case 31:case"end":return e.stop()}}),e,null,[[14,22,27,31]])})))()},checkAndUpdateBackupSuppliers:function(){var t=this;if(0!==this.backupSupplierList.length){var e=this.backupSupplierList.map((function(t){return t.supplierId}));this.formData.backups.forEach((function(n,r){n.supplier&&!e.includes(n.supplier)?(t.$set(n,"supplier",""),t.$set(n,"pool",""),t.$set(n,"poolList",[])):n.supplier&&t.fetchBackupPools(r)}))}},fetchCountries:function(){var t=arguments,e=this;return Object(c["a"])(Object(o["a"])().mark((function n(){var r,a,i,c,s;return Object(o["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return r=t.length>0&&void 0!==t[0]?t[0]:"all",e.loadingCountries=!0,e.filteredCountries=[],n.prev=3,n.next=6,Object(ht["e"])({continent:"all"===r?"":r});case 6:a=n.sent,"0000"==a.code&&Array.isArray(a.data)?(i=function(t){return t.slice().sort((function(t,e){return(t.countryEn||"").localeCompare(e.countryEn||"")}))},c=a.data.filter((function(t){return null!=t.mcc})),e.filteredCountries=i(c),e.allCountries||(e.allCountries=[]),c.forEach((function(t){e.allCountries.some((function(e){return e.mcc===t.mcc}))||e.allCountries.push(t)})),s=a.data.length-e.filteredCountries.length,s>0&&console.warn("".concat(s," 个国家缺少 MCC，已被过滤。")),e.formData.targetCountries&&e.formData.targetCountries.length>0&&(e.formData.targetCountries=e.sortTargetCountriesByEn(e.formData.targetCountries)),e.updateCheckAllStatus()):e.filteredCountries=[],n.next=14;break;case 10:n.prev=10,n.t0=n["catch"](3),e.filteredCountries=[],e.$Message.error("操作失败");case 14:return n.prev=14,e.loadingCountries=!1,n.finish(14);case 17:case"end":return n.stop()}}),n,null,[[3,10,14,17]])})))()},handleContinentChange:function(t){console.log("接收到大洲变更事件:",t),this.selectedContinent!==t&&(this.selectedContinent=t,this.fetchCountries(t))},sortTargetCountriesByEn:function(t){var e={},n=[].concat(Object(i["a"])(this.filteredCountries||[]),Object(i["a"])(this.allCountries||[]));n.forEach((function(t){t&&t.mcc&&(!e[t.mcc]||!e[t.mcc].countryEn&&t.countryEn)&&(e[t.mcc]=t)})),console.log("排序前:",JSON.stringify(t));var r=t.slice().sort((function(t,n){var r=e[t]||{},a=e[n]||{},i=r.countryEn||"",o=a.countryEn||"";return console.log("比较: ".concat(i," vs ").concat(o)),i.localeCompare(o)}));return console.log("排序后:",JSON.stringify(r)),r},handleSelectionChange:function(t,e){var n=this,r=Object(i["a"])(new Set(t));r=this.sortTargetCountriesByEn(r);var a=new Set(this.formData.targetCountries),o=new Set(r),c=r.some((function(t){return!a.has(t)})),s=this.formData.targetCountries.some((function(t){return!o.has(t)})),u=c||s;this.formData.targetCountries=r,this.updateCheckAllStatus(),u&&(console.log("目标国家内容发生变化，重置备用资源供应商和卡池"),this.backupSupplierList=[],this.supplierCache.mccKey="",this.supplierCache.noDataKeys=[],this.poolCache={noDataKeys:[]},this.formData.backups.forEach((function(t,e){t&&(n.$set(t,"supplier",""),n.$set(t,"pool",""),n.$set(t,"poolList",[]))})),this.formData.targetCountries.length>0&&this.fetchSuppliersByMcc()),this.$nextTick((function(){n.triggerTargetCountriesValidation(!0)}))},handleCheckAll:function(t){var e=this,n=t.currentMccs,r=t.isCurrentlyAllSelected,a=Object(i["a"])(this.formData.targetCountries);if(r)this.formData.targetCountries=this.formData.targetCountries.filter((function(t){return!n.includes(t)}));else{var o=n.filter((function(t){return!e.formData.targetCountries.includes(t)}));this.formData.targetCountries=[].concat(Object(i["a"])(this.formData.targetCountries),Object(i["a"])(o))}this.formData.targetCountries=this.sortTargetCountriesByEn(this.formData.targetCountries),this.updateCheckAllStatus();var c=this.formData.targetCountries,s=new Set(a),u=new Set(c),l=c.some((function(t){return!s.has(t)})),p=a.some((function(t){return!u.has(t)})),h=l||p;h&&(console.log("全选/取消全选导致目标国家变化，重置备用资源供应商和卡池"),this.backupSupplierList=[],this.supplierCache.mccKey="",this.supplierCache.noDataKeys=[],this.poolCache={noDataKeys:[]},this.formData.backups.forEach((function(t,n){t&&(e.$set(t,"supplier",""),e.$set(t,"pool",""),e.$set(t,"poolList",[]))})),this.formData.targetCountries.length>0&&this.fetchSuppliersByMcc()),this.$nextTick((function(){e.triggerTargetCountriesValidation(!0)}))},updateCheckAllStatus:function(){var t=this.currentViewCountryMccs;if(!t||0===t.length)return this.countryIndeterminate=!1,void(this.countryCheckAll=!1);var e=this.formData.targetCountries.filter((function(e){return t.includes(e)})).length;0===e?(this.countryIndeterminate=!1,this.countryCheckAll=!1):e===t.length?(this.countryIndeterminate=!1,this.countryCheckAll=!0):(this.countryIndeterminate=!0,this.countryCheckAll=!1)},handleFaultSupplierChange:function(t){console.log("故障供应商变更:",t)},handleBackupSupplierChange:function(t,e){var n=this;if(console.log("备用供应商 ".concat(t+1," 变更:"),e),0!==this.formData.targetCountries.length){var r=this.formData.backups[t];r&&(this.$set(r,"pool",""),this.$set(r,"poolList",[]),e&&this.fetchBackupPools(t),this.formData.backups.length>1&&this.$nextTick((function(){n.formData.backups.forEach((function(t,e){n.$refs.formRef&&n.$refs.formRef.validateField("backups.".concat(e,".percentage"))}))})))}else this.$Message.warning("请先选择目标国家")},handlePercentageChange:function(t,e){var n=this;console.log("备用项 ".concat(t+1," 百分比变更:"),e),this.formData.backups.length>1&&this.$nextTick((function(){n.formData.backups.forEach((function(t,e){n.$refs.formRef.validateField("backups.".concat(e,".percentage"))}))}))},handleOpenCardPoolSelect:function(t){var e=this.formData.backups[t];if(e&&e.supplier)if(0!==this.formData.targetCountries.length){var n=e.supplier,r=this.getMccCacheKey(),a="".concat(n,"_").concat(t),i="".concat(n,"_").concat(r);if(this.poolCache.noDataKeys&&this.poolCache.noDataKeys.includes(i))return console.log("备用供应商 ".concat(n," 在当前目标国家下已确认没有可用的卡池，跳过请求")),this.$set(this.formData.backups[t],"poolList",[]),void this.$set(e,"poolListChecked",!0);if(this.poolCache[a]||this.$set(this.poolCache,a,{lastMccKey:"",isLoading:!1}),e.poolList&&e.poolList.length>0&&this.poolCache[a].lastMccKey===r)return console.log("使用备用 ".concat(t+1," 供应商 ").concat(n," 的卡池列表缓存")),void this.$set(e,"poolListChecked",!0);this.poolCache[a].isLoading?console.log("备用 ".concat(t+1," 供应商 ").concat(n," 的卡池列表正在加载中，跳过请求")):(console.log("为备用 ".concat(t+1," (供应商: ").concat(e.supplier,") 获取卡池列表...")),this.fetchBackupPools(t))}else this.$Message.warning("请先选择目标国家")},fetchBackupPools:function(t){var e=this,n=this.formData.backups[t];if(n&&n.supplier){var r=n.supplier,a=this.getMccCacheKey(),i="".concat(r,"_").concat(t),o="".concat(r,"_").concat(a);if(this.poolCache[i]||this.$set(this.poolCache,i,{lastMccKey:"",isLoading:!1}),this.poolCache.noDataKeys&&this.poolCache.noDataKeys.includes(o))return console.log("备用供应商 ".concat(r," 在当前目标国家下已确认没有可用的卡池，跳过请求")),this.$set(this.formData.backups[t],"poolList",[]),void this.$set(n,"poolListChecked",!0);this.$set(this.poolCache[i],"isLoading",!0),this.$set(n,"loadingPools",!0),this.$set(n,"poolListChecked",!1),Object(ht["c"])({mccList:this.formData.targetCountries,supplierId:n.supplier}).then((function(c){"0000"===c.code&&Array.isArray(c.data)?(e.$set(e.formData.backups[t],"poolList",c.data),console.log("备用 ".concat(t+1," 卡池列表加载完成:"),c.data),0===c.data.length&&(e.poolCache.noDataKeys||(e.poolCache.noDataKeys=[]),e.poolCache.noDataKeys.includes(o)||(e.poolCache.noDataKeys.push(o),console.log("备用供应商 ".concat(r," 在当前目标国家下没有可用的卡池，已记录")))),e.poolCache[i]&&e.$set(e.poolCache[i],"lastMccKey",a),e.$set(n,"poolListChecked",!0)):(e.$set(e.formData.backups[t],"poolList",[]),e.$set(n,"poolListChecked",!0))})).catch((function(r){console.error("获取备用 ".concat(t+1," 卡池列表失败:"),r),e.$set(e.formData.backups[t],"poolList",[]),e.$set(n,"poolListChecked",!0)})).finally((function(){e.formData.backups[t]&&e.$set(e.formData.backups[t],"loadingPools",!1),e.poolCache[i]&&e.$set(e.poolCache[i],"isLoading",!1)}))}else n&&(this.$set(n,"poolList",[]),this.$set(n,"poolListChecked",!1))},getMccCacheKey:function(){return this.formData.targetCountries.sort().join(",")},clearBackupPools:function(){var t=this;this.formData.backups.forEach((function(e,n){t.$set(e,"pool",""),t.$set(e,"poolList",[])}))},createNewBackupItem:function(){return{supplier:"",pool:"",percentage:null,loadingPools:!1,poolList:[],poolListChecked:!1,_uid:Date.now()}},addBackup:function(){var t=this;this.formData.backups.push(this.createNewBackupItem()),this.formData.backups.length>1&&this.$nextTick((function(){t.$refs.formRef.validateField("backups.0.percentage")}))},removeBackup:function(t){var e=this;if(this.formData.backups.splice(t,1),1===this.formData.backups.length){var n=this.formData.backups[0];n.percentage=null,this.$nextTick((function(){e.$refs.formRef.validateField("backups.0.percentage",(function(t){}))}))}else this.formData.backups.length>0&&this.$nextTick((function(){e.$refs.formRef.validateField("backups.0.percentage")}));this.$nextTick((function(){e.formData.backups.forEach((function(t,n){t.supplier&&e.$refs.formRef.validateField("backups.".concat(n,".supplier"))}))}))},handleSubmit:function(){var t=this;this.$refs.formRef.validate(function(){var e=Object(c["a"])(Object(o["a"])().mark((function e(n){var r,a,i,s,u,l;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!n){e.next=26;break}r=new Set,a=!1,i=0;case 4:if(!(i<t.formData.backups.length)){e.next=15;break}if(s=t.formData.backups[i],!s.supplier||!s.pool){e.next=12;break}if(u="".concat(s.supplier,"-").concat(s.pool),!r.has(u)){e.next=11;break}return a=!0,e.abrupt("break",15);case 11:r.add(u);case 12:i++,e.next=4;break;case 15:if(!a){e.next=18;break}return t.$Message.error("备用供应商和备用卡池不能重复选择"),e.abrupt("return");case 18:if(!(t.formData.backups.length>1)){e.next=23;break}if(l=t.formData.backups.reduce((function(t,e){return t+(Number(e.percentage)||0)}),0),!(Math.abs(l-100)>.01)){e.next=23;break}return t.$Message.error("比例总和必须等于100%"),e.abrupt("return");case 23:t.$Modal.confirm({title:"提示",content:"确定要新建此故障处理规则吗？",onOk:function(){var e=Object(c["a"])(Object(o["a"])().mark((function e(){var n,r;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n={faultName:t.formData.ruleName,faultSupplierId:t.formData.faultSupplier,mcc:t.formData.targetCountries,bakSupplier:t.formData.backups.map((function(e){return{bakSupplierId:e.supplier,carPoolId:e.pool,rate:1===t.formData.backups.length?100:e.percentage}}))},t.submitLoading=!0,e.prev=2,e.next=5,Object(ht["j"])(n);case 5:r=e.sent,"0000"===r.code?(t.$Message.success("新建成功!"),t.$router.push({name:"fault_mngr"})):t.$Message.error(r.msg||"未知错误"),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](2),console.error("新建规则失败:",e.t0);case 12:return e.prev=12,t.submitLoading=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[2,9,12,15]])})));function n(){return e.apply(this,arguments)}return n}()}),e.next=28;break;case 26:t.$Message.error("表单校验失败，请检查输入!"),t.triggerTargetCountriesValidation(!0);case 28:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleCancel:function(){console.log("取消新建"),this.$router.go(-1)},handleOpenBackupSupplierSelect:function(t,e){if(e){if(this.formData.targetCountries&&0!==this.formData.targetCountries.length){var n=this.getMccCacheKey();return this.supplierCache.noDataKeys&&this.supplierCache.noDataKeys.includes(n)?(console.log("此目标国家组合已确认没有可用的备用供应商，跳过请求"),this.backupSupplierList=[],void(this.backupSupplierListChecked=!0)):this.backupSupplierList.length>0&&this.supplierCache.mccKey===n?(console.log("使用备用供应商列表缓存"),void(this.backupSupplierListChecked=!0)):void(this.supplierCache.isLoading?console.log("备用供应商列表正在加载中，跳过请求"):(this.loadingBackupSuppliers=!0,this.fetchSuppliersByMcc()))}this.backupSupplierListChecked=!1}},triggerTargetCountriesValidation:function(t){var e=this;this.$refs.formRef&&this.$nextTick((function(){e.$refs.formRef.validateField("targetCountries",(function(n){(t||e.targetCountryError)&&(e.targetCountryError=!!n)}))}))},removeSelectedCountry:function(t){this.formData.targetCountries=this.formData.targetCountries.filter((function(e){return e!==t})),this.formData.targetCountries=this.sortTargetCountriesByEn(this.formData.targetCountries),this.handleSelectionChange(this.formData.targetCountries,this.currentViewCountryMccs)},remoteSearchSupplier:function(t,e){this.formData.targetCountries&&0!==this.formData.targetCountries.length&&(0!==this.backupSupplierList.length||this.loadingBackupSuppliers||this.fetchSuppliersByMcc())},remoteSearchPool:function(t,e){var n=this.formData.backups[e];n&&n.supplier&&(0!==n.poolList.length||n.loadingPools||this.fetchBackupPools(e))}},mounted:function(){this.fetchInitialData()},watch:{"formData.targetCountries":{handler:function(t,e){if(e&&t&&t.length>0&&Array.isArray(t)){var n=this.sortTargetCountriesByEn(t);if(JSON.stringify(n)!==JSON.stringify(t))return void(this.formData.targetCountries=n)}}}}},dt=ft,gt=(n("91e6"),Object(ut["a"])(dt,r,a,!1,null,"b9db10de",null));e["default"]=gt.exports},addb:function(t,e,n){"use strict";var r=n("f36a"),a=Math.floor,i=function(t,e){var n=t.length;if(n<8){var o,c,s=1;while(s<n){c=s,o=t[s];while(c&&e(t[c-1],o)>0)t[c]=t[--c];c!==s++&&(t[c]=o)}}else{var u=a(n/2),l=i(r(t,0,u),e),p=i(r(t,u),e),h=l.length,f=p.length,d=0,g=0;while(d<h||g<f)t[d+g]=d<h&&g<f?e(l[d],p[g])<=0?l[d++]:p[g++]:d<h?l[d++]:p[g++]}return t};t.exports=i},bbb7:function(t,e,n){},da8c:function(t,e,n){"use strict";n.d(e,"g",(function(){return c})),n.d(e,"e",(function(){return s})),n.d(e,"d",(function(){return u})),n.d(e,"j",(function(){return l})),n.d(e,"c",(function(){return p})),n.d(e,"a",(function(){return h})),n.d(e,"i",(function(){return f})),n.d(e,"k",(function(){return d})),n.d(e,"f",(function(){return g})),n.d(e,"h",(function(){return m})),n.d(e,"b",(function(){return C}));var r=n("66df"),a="/rms/api/v1",i="/oms/api/v1",o="/pms/api/v1",c=function(){return r["a"].request({url:a+"/supplier/query",method:"get"})},s=function(t){return r["a"].request({url:i+"/country/getCountryByContinent",method:"get",params:t})},u=function(){return r["a"].request({url:i+"/country/getContinent",method:"get"})},l=function(t){return r["a"].request({url:a+"/supplierFaultRule/save",method:"post",data:t})},p=function(t){return r["a"].request({url:o+"/cardPool/queryCardPoolByMccList",method:"post",data:t})},h=function(t){return r["a"].request({url:o+"/cardPool/getList",method:"post",data:t})},f=function(){return r["a"].request({url:a+"/supplierFaultRule/getTargetCountry",method:"get"})},d=function(t){return r["a"].request({url:a+"/supplierFaultRule/updateFaultStatus",method:"post",params:{id:t}})},g=function(t){return r["a"].request({url:a+"/supplierFaultRule/getPageList",method:"post",data:t})};function m(t){return r["a"].request({url:i+"/country/getSuppliersByMccList",method:"post",data:t})}var C=function(t){return r["a"].request({url:o+"/cardPool/getPackageListByPoolId",method:"get",params:t})}},ea83:function(t,e,n){"use strict";var r=n("b5db"),a=r.match(/AppleWebKit\/(\d+)\./);t.exports=!!a&&+a[1]},ed43:function(t,e,n){"use strict";n("bbb7")}}]);