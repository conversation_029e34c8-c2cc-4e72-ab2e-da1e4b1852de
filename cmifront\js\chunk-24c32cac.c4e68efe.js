(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-24c32cac"],{"129f":function(e,t,a){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},"1e03":function(e,t,a){},"4ccb":function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c");var n=function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padiing:"16px"}},[t("div",{staticStyle:{margin:"20px 10%"}},[t("div",[t("Form",{ref:"formObj",staticStyle:{"font-size":"25px"},attrs:{model:e.formObj,"label-width":150,rules:e.ruleValidate}},[t("Row",{attrs:{justify:"center"}},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"渠道商名称",prop:"corpName"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:"50",clearable:!0,placeholder:"请输入渠道商名称"},model:{value:e.formObj.corpName,callback:function(t){e.$set(e.formObj,"corpName",t)},expression:"formObj.corpName"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"渠道商状态",prop:"isSub"}},[t("Select",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请选择渠道商状态"},model:{value:e.formObj.isSub,callback:function(t){e.$set(e.formObj,"isSub",t)},expression:"formObj.isSub"}},e._l(e.purchaseStatusList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v("\n\t\t\t\t\t\t\t\t"+e._s(a.label)+"\n\t\t\t\t\t\t\t")])})),1)],1)],1)],1),t("Row",{attrs:{justify:"center"}},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"公司名称",prop:"companyName"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:"200",clearable:!0,placeholder:"请输入公司名称"},model:{value:e.formObj.companyName,callback:function(t){e.$set(e.formObj,"companyName",t)},expression:"formObj.companyName"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"地址",prop:"address"}},[t("Input",{staticClass:"inputSty",attrs:{placeholder:"请输入地址",clearable:!0},model:{value:e.formObj.address,callback:function(t){e.$set(e.formObj,"address",t)},expression:"formObj.address"}})],1)],1)],1),t("Row",{attrs:{justify:"center"}},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"EBS Code",prop:"ebsCode"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,maxlength:"50",placeholder:"请输入EBS Code"},model:{value:e.formObj.ebsCode,callback:function(t){e.$set(e.formObj,"ebsCode",t)},expression:"formObj.ebsCode"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"联系人邮箱",prop:"email"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,type:"email",maxlength:"50",placeholder:"请输入联系人邮箱"},model:{value:e.formObj.email,callback:function(t){e.$set(e.formObj,"email",t)},expression:"formObj.email"}})],1)],1)],1),t("Row",{attrs:{justify:"center"}},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"关联子渠道商"}},[t("Button",{staticStyle:{"margin-left":"8px"},attrs:{type:"info",icon:"md-add"},on:{click:e.getSubChannel}},[e._v("查看")])],1)],1)],1),t("div",{staticStyle:{"text-align":"center","margin-top":"20px"}},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"reset",expression:"'reset'"}],staticStyle:{"margin-left":"8px"},on:{click:function(t){return e.reset("formObj")}}},[e._v("重置")]),e._v("    \n\t\t\t\t\t"),t("Button",{directives:[{name:"has",rawName:"v-has",value:"submit",expression:"'submit'"}],attrs:{type:"primary",loading:e.submitLoading},on:{click:e.submit}},[e._v("提交")])],1)],1),t("Modal",{attrs:{title:"关联子渠道商","footer-hide":!0,"mask-closable":!1,closable:!1,width:"700px"},model:{value:e.subChannelFlage,callback:function(t){e.subChannelFlage=t},expression:"subChannelFlage"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("div",{staticStyle:{display:"flex","justify-content":"flex-start","align-items":"flex-start"}},[t("Form",{ref:"searchObj",attrs:{model:e.searchObj,rules:e.subChannelRule,"label-width":120}},[t("FormItem",{attrs:{label:"渠道商名称",prop:"corpName"}},[t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入渠道商名称",clearable:""},model:{value:e.searchObj.corpName,callback:function(t){e.$set(e.searchObj,"corpName",t)},expression:"searchObj.corpName"}})],1)],1),e._v("        \n\t\t\t\t\t\t"),t("Button",{attrs:{type:"primary",loading:e.searchloading}},[t("div",{staticStyle:{display:"flex","align-items":"center"},on:{click:e.search}},[t("Icon",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"md-search"}}),e._v(" 搜索\n\t\t\t\t\t\t\t")],1)])],1),t("div",[t("Table",{ref:"table",staticStyle:{display:"flex","justify-content":"space-evenly"},attrs:{columns:e.columns,data:e.subChannelData,loading:e.subChannelLoading,"no-data-text":"暂无数据"},scopedSlots:e._u([{key:"corpName",fn:function(a){var n=a.row;a.index;return[t("strong",[e._v(e._s(n.corpName))])]}},{key:"checkBox",fn:function(a){var n=a.row,r=a.index;return[t("i-Switch",{on:{"on-change":function(t){return e.showCheckBox(n,n.checked,r)}},model:{value:n.checked,callback:function(t){e.$set(n,"checked",t)},expression:"row.checked"}})]}},{key:"action",fn:function(a){var n=a.row,r=a.index;return[t("Input",{attrs:{type:"number",readonly:!n.checked},on:{"on-blur":function(t){return e.show(n,n.provinceCode,r)}},model:{value:n.provinceCode,callback:function(t){e.$set(n,"provinceCode",t)},expression:"row.provinceCode"}})]}}])}),t("div",{staticStyle:{"margin-top":"40px"}},[t("Page",{attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1)],1),t("div",{staticStyle:{"text-align":"center","margin-top":"20px"}},[t("Button",{on:{click:e.cancelModal}},[e._v("返回")]),e._v("    \n\t\t\t\t\t\t"),t("Button",{attrs:{type:"primary",loading:e.besureLoading},on:{click:e.besure}},[e._v("确定")])],1)])])],1)])])},r=[],o=a("ade3"),s=(a("d9e2"),a("4de4"),a("caad"),a("14d9"),a("a434"),a("e9c4"),a("b64b"),a("d3b7"),a("2532"),a("159b"),a("7e89")),c={data:function(){var e=function(e,t,a){t&&-1!=t.indexOf("@")?a():a(new Error("请输入有效的邮箱地址"))};return{checked:!1,submitLoading:!1,searchloading:!1,subChannelLoading:!1,besureLoading:!1,subChannelFlage:!1,total:0,currentPage:1,purchaseStatusList:[{label:"允许订购",value:"1"},{label:"不允许订购",value:"2"}],formObj:{corpName:"",isSub:"",companyName:"",address:"",ebsCode:"",email:"",relationChannel:[]},searchObj:{corpName:""},ruleValidate:{corpName:[{required:!0,type:"string",message:"渠道商名称不能为空"},{validator:function(e,t,a){t&&t.includes("&")?a(new Error("渠道商名称不能包含&符号")):a()},trigger:"blur"}],isSub:[{required:!0,message:"渠道商状态不能为空",trigger:"change"}],companyName:[{required:!0,type:"string",message:"公司名称不能为空",trigger:"blur"},{validator:function(e,t,a){t&&t.includes("&")?a(new Error("公司名称不能包含&符号")):a()},trigger:"blur"}],address:[{required:!0,type:"string",message:"地址不能为空",trigger:"blur"},{max:200,message:"最长200位"}],ebsCode:[{required:!0,type:"string",message:"EBSCode不能为空"}],email:[{required:!0,message:"联系人邮箱不能为空"},{validator:e,trigger:"blur"},{required:!0,type:"email",trigger:"blur",message:"联系人邮箱格式错误"}]},subChannelRule:{},columns:[{title:"渠道商名称",slot:"corpName",align:"center",minWidth:200,tooltip:!0},{title:"关联",width:120,align:"center",slot:"checkBox"},{title:"渠道编码",width:200,align:"center",slot:"action"}],subChannelData:[],corps:[],beforeUpdateChannelDate:[],List:[]}},mounted:function(){localStorage.removeItem("relationChannel")},methods:{getSubChannel:function(){this.subChannelFlage=!0,this.goPageFirst(1,0)},goPageFirst:function(e,t){var a=this;this.subChannelLoading=!0;var n=this;Object(s["c"])({pageSize:10,pageNum:e,corpName:this.searchObj.corpName,topChannelCorpId:this.formObj.corpName}).then((function(r){if("0000"==r.code){n.loading=!1,a.subChannelLoading=!1,a.currentPage=e,a.total=r.count,a.subChannelData=r.data;var o=JSON.parse(localStorage.getItem("relationChannel"))===[]?"":JSON.parse(localStorage.getItem("relationChannel")),s=0===t?o:a.corps;s&&a.subChannelData.forEach((function(e){s.forEach((function(t){t.corpId===e.corpId&&(e.provinceCode=t.provinceCode,e.checked=t.checked)}))}))}})).catch((function(e){console.error(e)})).finally((function(){n.loading=!1,a.subChannelLoading=!1}))},search:function(){this.goPageFirst(1)},goPage:function(e){this.goPageFirst(e,1),this.List=this.beforeUpdateChannelDate},showCheckBox:function(e,t,a){var n=this;this.subChannelData.filter((function(t){return t.corpId===e.corpId})).forEach((function(a){a.checked=t,t?(a.provinceCode=e.provinceCode,n.corps.push({corpId:e.corpId,corpName:e.corpName,provinceCode:e.provinceCode,checked:t})):(e.provinceCode=null,a.provinceCode=e.provinceCode),t||n.corps.forEach((function(t,a){t.corpId===e.corpId&&n.corps.splice(a,1)}))}))},show:function(e,t,a){var n=this;this.subChannelData.forEach((function(a,r){if(a.checked){var o=!1;n.corps.forEach((function(e){a.corpId===e.corpId&&(o=!0)})),o?n.corps.forEach((function(r,o){r.corpId===e.corpId&&(n.corps[o]={corpId:e.corpId,corpName:e.corpName,provinceCode:t,checked:a.checked})})):n.corps.push({corpId:e.corpId,corpName:e.corpName,provinceCode:t,checked:a.checked})}}))},besure:function(){this.formObj.relationChannel=this.corps,localStorage.setItem("relationChannel",JSON.stringify(this.formObj.relationChannel)),this.subChannelFlage=!1,this.searchObj.corpName=""},cancelModal:function(){var e=JSON.parse(localStorage.getItem("relationChannel"))===[]?"":JSON.parse(localStorage.getItem("relationChannel"));this.corps=e,this.subChannelFlage=!1,this.searchObj.corpName=""},reset:function(){this.formObj={corpName:"",isSub:"",companyName:"",address:"",ebsCode:"",email:"",relationChannel:[]},this.subChannelData=[],this.searchObj.corpName="",this.$refs["formObj"].resetFields()},submit:function(){var e=this;this.$refs["formObj"].validate((function(t){if(t){var a=function(e){for(var t=0;t<e.length;t++)for(var a=t+1;a<e.length;a++)e[t].corpId==e[a].corpId&&(e.splice(a,1),a--);return e},n=e.formObj,r=n.relationChannel,c=Object(o["a"])(Object(o["a"])(Object(o["a"])({corpName:n.corpName,isSub:n.isSub,address:n.address,ebsCode:n.ebsCode,email:n.email},"ebsCode",n.ebsCode),"companyName",n.companyName),"relationChannel",a(r));e.submitLoading=!0,Object(s["e"])(c).then((function(t){if(!t||"0000"!=t.code)throw e.submitLoading=!1,t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.$router.push({name:"zeroLevelChannel"})})).catch((function(t){e.submitLoading=!1}))}}))}}},i=c,l=(a("a4ba"),a("2877")),u=Object(l["a"])(i,n,r,!1,null,null,null);t["default"]=u.exports},"7e89":function(e,t,a){"use strict";a.d(t,"d",(function(){return o})),a.d(t,"b",(function(){return s})),a.d(t,"c",(function(){return c})),a.d(t,"e",(function(){return i})),a.d(t,"a",(function(){return l}));var n=a("66df"),r="cms",o=function(e){return n["a"].request({url:r+"/channel/getTopChannel",params:e,method:"get"})},s=function(e){return n["a"].request({url:r+"/channel/deleteTopChannel",params:e,method:"delete"})},c=function(e){return n["a"].request({url:r+"/channel/getCorpList",params:e,method:"get"})},i=function(e){return n["a"].request({url:r+"/channel/newTopChannel",data:e,method:"post"})},l=function(e){return n["a"].request({url:r+"/channel/updateTopChannel",data:e,method:"put"})}},"841c":function(e,t,a){"use strict";var n=a("c65b"),r=a("d784"),o=a("825a"),s=a("7234"),c=a("1d80"),i=a("129f"),l=a("577e"),u=a("dc4a"),d=a("14c3");r("search",(function(e,t,a){return[function(t){var a=c(this),r=s(t)?void 0:u(t,e);return r?n(r,t,a):new RegExp(t)[e](l(a))},function(e){var n=o(this),r=l(e),s=a(t,n,r);if(s.done)return s.value;var c=n.lastIndex;i(c,0)||(n.lastIndex=0);var u=d(n,r);return i(n.lastIndex,c)||(n.lastIndex=c),null===u?-1:u.index}]}))},a4ba:function(e,t,a){"use strict";a("1e03")}}]);