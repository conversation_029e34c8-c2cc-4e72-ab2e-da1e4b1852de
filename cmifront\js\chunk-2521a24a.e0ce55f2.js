(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2521a24a"],{"129f":function(e,t,a){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},"30b3":function(e,t,a){"use strict";a("846d")},"841c":function(e,t,a){"use strict";var r=a("c65b"),l=a("d784"),s=a("825a"),i=a("7234"),o=a("1d80"),n=a("129f"),c=a("577e"),d=a("dc4a"),u=a("14c3");l("search",(function(e,t,a){return[function(t){var a=o(this),l=i(t)?void 0:d(t,e);return l?r(l,t,a):new RegExp(t)[e](c(a))},function(e){var r=s(this),l=c(e),i=a(t,r,l);if(i.done)return i.value;var o=r.lastIndex;n(o,0)||(r.lastIndex=0);var d=u(r,l);return n(r.lastIndex,o)||(r.lastIndex=o),null===d?-1:d.index}]}))},"846d":function(e,t,a){},df92:function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c");var r=function(){var e=this,t=e._self._c;return t("div",[t("Card",[t("div",{staticClass:"search_head"},[t("span",{staticStyle:{"font-weight":"bold"}},[e._v("选择供应商：")]),e._v("  \n      "),t("Select",{staticStyle:{width:"300px"},attrs:{placeholder:"下拉选择供应商"},on:{"on-change":e.getProviders},model:{value:e.provider,callback:function(t){e.provider=t},expression:"provider"}},e._l(e.providers,(function(a,r){return t("Option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1),e._v("  \n      "),t("span",{staticStyle:{"font-weight":"bold"}},[e._v("选择卡类型：")]),e._v("  \n      "),t("Select",{staticStyle:{width:"250px"},attrs:{placeholder:"下拉选择卡类型"},on:{"on-change":e.getProviders},model:{value:e.cardType,callback:function(t){e.cardType=t},expression:"cardType"}},e._l(e.cardTypes,(function(a,r){return t("Option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1),e._v("  \n      "),t("span",{staticStyle:{"font-weight":"bold"}},[e._v("选择日期：")]),e._v("  \n      "),t("Row",[t("Col",{attrs:{span:"12"}},[t("DatePicker",{staticStyle:{width:"320px"},attrs:{type:"daterange",format:"yyyyMMdd",placement:"bottom-end",placeholder:"选择日期范围"},on:{"on-change":e.handleDateChange,"on-clear":e.hanldeDateClear},model:{value:e.timeRangeArray,callback:function(t){e.timeRangeArray=t},expression:"timeRangeArray"}})],1)],1),e._v("  \n      "),t("Button",{attrs:{type:"primary",icon:"md-search"},on:{click:function(t){return e.search()}}},[e._v("搜索")]),e._v("  \n      "),t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{icon:"md-add",type:"success"},on:{click:function(t){return e.editCard()}}},[e._v("填写表单")]),e._v("  \n    ")],1),t("div",{staticStyle:{"margin-top":"20px"}},[t("Table",{attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.loading},scopedSlots:e._u([{key:"action",fn:function(a){var r=a.row;a.index;return[t("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"success",size:"small"},on:{click:function(t){return e.download(r)}}},[e._v("下载")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"error",size:"small"},on:{click:function(t){return e.remove(r)}}},[e._v("删除")])]}}])})],1),t("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[t("Page",{staticStyle:{margin:"10px 0"},attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1)]),t("Modal",{attrs:{title:"填写表单",width:"600px"},on:{"on-ok":e.ok,"on-cancel":e.cancel},model:{value:e.modal1,callback:function(t){e.modal1=t},expression:"modal1"}},[t("div",{staticClass:"search_head"},[t("Form",{ref:"formValidate1",staticStyle:{"font-weight":"bold"},attrs:{model:e.formValidate1,rules:e.ruleValidate1,"label-width":160,"label-height":100,inline:""}},[t("FormItem",{attrs:{label:"供应商",prop:"provider"}},[t("Select",{staticStyle:{width:"250px"},attrs:{placeholder:"下拉选择供应商"},on:{"on-change":e.getProviders},model:{value:e.formValidate1.provider,callback:function(t){e.$set(e.formValidate1,"provider",t)},expression:"formValidate1.provider"}},e._l(e.providers,(function(a,r){return t("Option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1),t("FormItem",{attrs:{label:"卡类型",prop:"cardType"}},[t("Select",{staticStyle:{width:"250px"},attrs:{placeholder:"下拉选择卡类型"},on:{"on-change":e.getProviders},model:{value:e.formValidate1.cardType,callback:function(t){e.$set(e.formValidate1,"cardType",t)},expression:"formValidate1.cardType"}},e._l(e.cardTypes,(function(a,r){return t("Option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1),t("FormItem",{attrs:{label:"Customer",prop:"customer"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Customer...",clearable:""},model:{value:e.formValidate1.customer,callback:function(t){e.$set(e.formValidate1,"customer",t)},expression:"formValidate1.customer"}}),e._v("  \n            "),t("span",{staticStyle:{color:"#c5c8ce"}},[e._v("客户名")])],1),t("FormItem",{attrs:{label:"Quantity",prop:"quantity"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Quantity...",clearable:""},model:{value:e.formValidate1.quantity,callback:function(t){e.$set(e.formValidate1,"quantity",t)},expression:"formValidate1.quantity"}}),e._v("  \n            "),t("span",{staticStyle:{color:"#c5c8ce"}},[e._v("需分配主IMSI的数量")])],1),t("FormItem",{attrs:{label:"Type",prop:"type"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Type...",clearable:""},model:{value:e.formValidate1.type,callback:function(t){e.$set(e.formValidate1,"type",t)},expression:"formValidate1.type"}}),e._v("  \n        ")],1),t("FormItem",{attrs:{label:"Batch",prop:"batch"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Batch...",clearable:""},model:{value:e.formValidate1.batch,callback:function(t){e.$set(e.formValidate1,"batch",t)},expression:"formValidate1.batch"}}),e._v("  \n        ")],1),t("FormItem",{attrs:{label:"Transport_key",prop:"transport_key"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Transport_key...",clearable:""},model:{value:e.formValidate1.transport_key,callback:function(t){e.$set(e.formValidate1,"transport_key",t)},expression:"formValidate1.transport_key"}}),e._v("  \n            "),t("span",{staticStyle:{color:"#c5c8ce"}},[e._v("传输key")])],1),t("FormItem",{attrs:{label:"OP_key",prop:"OP_key"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"OP_key...",clearable:""},model:{value:e.formValidate1.OP_key,callback:function(t){e.$set(e.formValidate1,"OP_key",t)},expression:"formValidate1.OP_key"}}),e._v("  \n            "),t("span",{staticStyle:{color:"#c5c8ce"}},[e._v("OPC")])],1),t("FormItem",{attrs:{label:"OTA_Transport_Key",prop:"OTA_Transport_Key"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"OTA_Transport_Key...",clearable:""},model:{value:e.formValidate1.OTA_Transport_Key,callback:function(t){e.$set(e.formValidate1,"OTA_Transport_Key",t)},expression:"formValidate1.OTA_Transport_Key"}}),e._v("  \n            "),t("span",{staticStyle:{color:"#c5c8ce"}},[e._v("目标OTA")])],1),t("FormItem",{attrs:{label:"Address1",prop:"address1"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Address1...",clearable:""},model:{value:e.formValidate1.address1,callback:function(t){e.$set(e.formValidate1,"address1",t)},expression:"formValidate1.address1"}}),e._v("  \n            "),t("span",{staticStyle:{color:"#c5c8ce"}},[e._v("地址1")])],1),t("FormItem",{attrs:{label:"Address2",prop:"address2"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Address2...",clearable:""},model:{value:e.formValidate1.address2,callback:function(t){e.$set(e.formValidate1,"address2",t)},expression:"formValidate1.address2"}}),e._v("  \n            "),t("span",{staticStyle:{color:"#c5c8ce"}},[e._v("地址2")])],1),t("FormItem",{attrs:{label:"Address3",prop:"address3"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Address3...",clearable:""},model:{value:e.formValidate1.address3,callback:function(t){e.$set(e.formValidate1,"address3",t)},expression:"formValidate1.address3"}}),e._v("  \n            "),t("span",{staticStyle:{color:"#c5c8ce"}},[e._v("地址3")])],1),t("FormItem",{attrs:{label:"Address4",prop:"address4"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Address4...",clearable:""},model:{value:e.formValidate1.address4,callback:function(t){e.$set(e.formValidate1,"address4",t)},expression:"formValidate1.address4"}}),e._v("  \n            "),t("span",{staticStyle:{color:"#c5c8ce"}},[e._v("地址4")])],1),t("FormItem",{attrs:{label:"Product Code",prop:"productCode"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Product Code...",clearable:""},model:{value:e.formValidate1.productCode,callback:function(t){e.$set(e.formValidate1,"productCode",t)},expression:"formValidate1.productCode"}}),e._v("  \n            "),t("span",{staticStyle:{color:"#c5c8ce"}},[e._v("产品编码")])],1),t("FormItem",{attrs:{label:"PO_Ref",prop:"PO_Ref"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"PO_Ref...",clearable:""},model:{value:e.formValidate1.PO_Ref,callback:function(t){e.$set(e.formValidate1,"PO_Ref",t)},expression:"formValidate1.PO_Ref"}}),e._v("  \n        ")],1),t("FormItem",{attrs:{label:"Artwork",prop:"artwork"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Artwork...",clearable:""},model:{value:e.formValidate1.artwork,callback:function(t){e.$set(e.formValidate1,"artwork",t)},expression:"formValidate1.artwork"}}),e._v("  \n        ")],1),t("FormItem",{attrs:{label:"VAR_OUT",prop:"varOut"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"VAR_OUT...",clearable:""},model:{value:e.formValidate1.varOut,callback:function(t){e.$set(e.formValidate1,"varOut",t)},expression:"formValidate1.varOut"}}),e._v("  \n        ")],1),t("FormItem",{attrs:{label:"Roaming Exclusive List",prop:"roamingExclusiveList"}},[t("Input",{staticStyle:{width:"250px"},attrs:{placeholder:"Roaming Exclusive List...",clearable:""},model:{value:e.formValidate1.roamingExclusiveList,callback:function(t){e.$set(e.formValidate1,"roamingExclusiveList",t)},expression:"formValidate1.roamingExclusiveList"}}),e._v("  \n            "),t("span",{staticStyle:{color:"#c5c8ce"}},[e._v("贴片卡填写，可为空")])],1)],1)],1)])],1)},l=[],s=a("3835"),i=(a("d81d"),a("14d9"),{components:{},data:function(){return{formValidate:{file:"",provider:""},ruleValidate:{file:[{required:!0,message:"请上传文件",trigger:"blur"}],provider:[{required:!0,message:"请选择供应商",trigger:"blur"}]},formValidate1:{provider:"",cardType:"",customer:"China Mobile International Ltd",quantity:"",type:"PLUG-IN",batch:"",Transport_key:"36",OP_key:"36",OTA_Transport_Key:"NA",address1:"China Mobile International Ltd",address2:"30/F Tower 1 Kowloon Commerce Ctr",address3:"51 Kwai Cheong Road",address4:"Kwai Chung, NT, Hong Kong",productCode:"CMI0000000000000001",PO_Ref:"CMIGTO0000000000002",artwork:"V1053539A_U1058946A",varOut:"PIN1/PUK1/PIN2/PUK2/ADM/ACCESS_CONTROL",roamingExclusiveList:""},ruleValidate1:{provider:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],cardType:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],customer:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],quantity:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],type:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],batch:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],Transport_key:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],OP_key:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],OTA_Transport_Key:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],address1:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],address2:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],address3:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],address4:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],productCode:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],PO_Ref:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],artwork:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],varOut:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}],roamingExclusiveList:[{required:!0,message:"请输入VIMSI号码",trigger:"blur"}]},columns:[{title:"文件名",key:"fileName",align:"center"},{title:"供应商",key:"supply",align:"center"},{title:"时间",key:"time",align:"center"},{title:"操作",slot:"action",width:300,align:"center"}],cardTypes:[{label:"卡类型1",value:0},{label:"卡类型2",value:1}],providers:[{label:"供应商1",value:0},{label:"供应商2",value:1}],statuses:[{label:"状态1",value:0},{label:"状态2",value:1}],tableData:[],loading:!1,currentPage:1,total:0,vimsiCondition:"",vimsiChoosed:"",ids:[],modal1:!1,status:"",provider:"",cardType:"",timeRangeArray:[],searchBeginTime:"",searchEndTime:""}},watch:{$route:"reload"},computed:{},methods:{goPageFirst:function(e){this.loading=!0;var t=[{fileName:"文件1",supply:"供应商1",time:"2021-02-28 15:30:28",status:0},{fileName:"文件2",supply:"供应商2",time:"2021-02-28 15:30:28",status:1},{fileName:"文件3",supply:"供应商3",time:"2021-02-28 15:30:28",status:2},{fileName:"文件4",supply:"供应商4",time:"2021-02-28 15:30:28",status:3},{fileName:"文件5",supply:"供应商5",time:"2021-02-28 15:30:28",status:4}];this.tableData=t,this.total=5,this.loading=!1},goPage:function(e){this.goPageFirst(e)},handleRowChange:function(e){var t=this;this.selection=e,this.ids=[],e.map((function(e,a){t.ids.push(e.fallbackId)}))},hanldeDateClear:function(){this.searchBeginTime="",this.searchEndTime=""},handleDateChange:function(e){var t=this.timeRangeArray[0]||"",a=this.timeRangeArray[1]||"";if(""!=t&&""!=a){var r=Object(s["a"])(e,2);this.searchBeginTime=r[0],this.searchEndTime=r[1]}},error:function(e){this.$Notice.error({title:"出错啦",desc:e?"":"服务器内部错误"})},search:function(){this.goPageFirst(1)},delteNumber:function(e){var t=this;this.$Modal.confirm({title:"确认删除？",onOk:function(){t.$Notice.success({title:"成功",desc:"操作成功"}),t.goPageFirst(t.page)}})},editCard:function(){this.modal1=!0},updateBatch:function(){this.modal3=!0},download:function(){},remove:function(){},getProviders:function(){},getStatus:function(){},ok:function(){this.formValidate1={}},cancel:function(){this.formValidate1={}}},mounted:function(){this.goPageFirst(1)}}),o=i,n=(a("30b3"),a("2877")),c=Object(n["a"])(o,r,l,!1,null,null,null);t["default"]=c.exports}}]);