(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-270d63d7"],{"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"1da9":function(t,e,a){"use strict";a.d(e,"f",(function(){return r})),a.d(e,"b",(function(){return o})),a.d(e,"e",(function(){return s})),a.d(e,"d",(function(){return c})),a.d(e,"c",(function(){return l})),a.d(e,"a",(function(){return d}));a("99af");var n=a("66df"),i="/rms/api/v1",r=function(t){return n["a"].request({url:i+"/cardfile/queryList",params:t,method:"get"})},o=function(t){return n["a"].request({url:i+"/cardfile/view/".concat(t),method:"get"})},s=function(t,e){return n["a"].request({url:i+"/cardfile/download/".concat(t,"/").concat(e),method:"get",responseType:"blob"})},c=function(t){return n["a"].request({url:i+"/cardfile/rollback/".concat(t),method:"get"})},l=function(t){return n["a"].request({url:i+"/cardfile/getDefaultContent",params:t,method:"get"})},d=function(t){return n["a"].request({url:i+"/cardfile/add",data:t,method:"post"})}},"466d":function(t,e,a){"use strict";var n=a("c65b"),i=a("d784"),r=a("825a"),o=a("7234"),s=a("50c4"),c=a("577e"),l=a("1d80"),d=a("dc4a"),u=a("8aa5"),f=a("14c3");i("match",(function(t,e,a){return[function(e){var a=l(this),i=o(e)?void 0:d(e,t);return i?n(i,e,a):new RegExp(e)[t](c(a))},function(t){var n=r(this),i=c(t),o=a(e,n,i);if(o.done)return o.value;if(!n.global)return f(n,i);var l=n.unicode;n.lastIndex=0;var d,h=[],g=0;while(null!==(d=f(n,i))){var m=c(d[0]);h[g]=m,""===m&&(n.lastIndex=u(i,s(n.lastIndex),l)),g++}return 0===g?null:h}]}))},"841c":function(t,e,a){"use strict";var n=a("c65b"),i=a("d784"),r=a("825a"),o=a("7234"),s=a("1d80"),c=a("129f"),l=a("577e"),d=a("dc4a"),u=a("14c3");i("search",(function(t,e,a){return[function(e){var a=s(this),i=o(e)?void 0:d(e,t);return i?n(i,e,a):new RegExp(e)[t](l(a))},function(t){var n=r(this),i=l(t),o=a(e,n,i);if(o.done)return o.value;var s=n.lastIndex;c(s,0)||(n.lastIndex=0);var d=u(n,i);return c(n.lastIndex,s)||(n.lastIndex=s),null===d?-1:d.index}]}))},"991a":function(t,e,a){"use strict";a("e999")},d8f7:function(t,e,a){"use strict";a.r(e);a("fb6a"),a("ac1f"),a("841c"),a("498a");var n=function(){var t=this,e=t._self._c;return e("div",[e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box1"},[e("span",{staticClass:"search_box_label"},[t._v("任务名称:")]),e("Input",{staticStyle:{width:"220px"},attrs:{placeholder:"请输入任务名称",clearable:""},model:{value:t.searchObj.taskName,callback:function(e){t.$set(t.searchObj,"taskName","string"===typeof e?e.trim():e)},expression:"searchObj.taskName"}})],1),t._v("   \n\t\t\t"),e("div",{staticClass:"search_box1"},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v("搜索")]),t._v("   \n\t\t\t\t"),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 2px","margin-left":"20px"},attrs:{type:"warning",icon:"md-add"},on:{click:function(e){return t.addTask()}}},[t._v("新建制卡任务")])],1)]),e("div",{staticStyle:{"margin-top":"20px"}},[e("Table",{staticStyle:{width:"100%","margin-to":"20px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"rollbackSuccessFile",fn:function(a){var n=a.row;a.index;return[n.rollbackSuccessFile?e("a",{attrs:{loading:t.exporting},on:{click:function(e){return t.exportfile(n.id,1)}}},[t._v(t._s(n.rollbackSuccessFile.slice(0,-4)))]):t._e()]}},{key:"rollbackFailFile",fn:function(a){var n=a.row;a.index;return[n.rollbackFailFile?e("a",{attrs:{loading:t.exporting},on:{click:function(e){return t.exportfile(n.id,2)}}},[t._v(t._s(n.rollbackFailFile.slice(0,-4)))]):t._e()]}},{key:"action",fn:function(a){var n=a.row;a.index;return["2"===n.taskStatus||"3"===n.taskStatus?e("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"info",size:"small",disabled:""},on:{click:function(e){return t.Info(n)}}},[t._v("查看")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"info",size:"small"},on:{click:function(e){return t.Info(n)}}},[t._v("查看")]),"2"===n.taskStatus||"3"===n.taskStatus?e("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"success",size:"small",disabled:""},on:{click:function(e){return t.exportfile(n.id,3)}}},[t._v("下载")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"success",size:"small"},on:{click:function(e){return t.exportfile(n.id,3)}}},[t._v("下载")]),"2"===n.taskStatus||"3"===n.taskStatus?e("Button",{directives:[{name:"has",rawName:"v-has",value:"rollback",expression:"'rollback'"}],attrs:{type:"warning",size:"small",disabled:""},on:{click:function(e){return t.Reback(n.id)}}},[t._v("回滚")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"rollback",expression:"'rollback'"}],attrs:{type:"warning",size:"small"},on:{click:function(e){return t.Reback(n.id)}}},[t._v("回滚")])]}}])}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:"任务详情",width:"620px"},on:{"on-ok":t.ok,"on-cancel":t.cancelModal},model:{value:t.modal5,callback:function(e){t.modal5=e},expression:"modal5"}},[e("div",{staticStyle:{padding:"15px"}},[e("div",{staticStyle:{"font-weight":"bold",height:"25px","line-height":"25px"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v("任务名称:")]),t._v("  \n\t\t\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.taskName))]),t._v("  \n\t\t\t\t")]),e("div",{staticStyle:{"font-weight":"bold",height:"25px","line-height":"25px"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v("制卡文件名:")]),t._v("  \n\t\t\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"},model:{value:t.taskName,callback:function(e){t.taskName=e},expression:"taskName"}},[t._v(t._s(t.cardfileName))]),t._v("  \n\t\t\t\t")]),e("div",{staticStyle:{"font-weight":"bold",height:"25px","line-height":"25px"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v("是否需要GTP PROXY指定号码:")]),t._v(" \n\t\t\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"},model:{value:t.taskName,callback:function(e){t.taskName=e},expression:"taskName"}},[t._v(t._s(t.gtpProxy))]),t._v("  \n\t\t\t\t")]),e("ul",t._l(t.items,(function(a,n){return e("li",{key:t.items.i,attrs:{id:"space"}},[t._v("\n\t\t\t\t\t\t"+t._s(a)+"\n\t\t\t\t\t")])})),0),t.remind?e("div",[e("p",{staticStyle:{"font-weight":"bold",color:"firebrick"}},[t._v("内容超长，请下载查看！")])]):t._e()]),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:function(e){return t.cancelModal()}}},[t._v("返回")]),t._v("    \n\t\t\t\t"),e("Button",{attrs:{type:"primary"},on:{click:function(e){return t.exportfile(t.id,3)}}},[t._v("下载")])],1)]),e("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1)],1)},i=[],r=(a("4de4"),a("14d9"),a("a434"),a("e9c4"),a("b64b"),a("d3b7"),a("3ca3"),a("466d"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("0a21"),a("1da9")),o={data:function(){return{total:0,currentPage:1,page:1,id:"",cardfileName:"",gtpProxy:"",taskName:"",items:[],searchloading:!1,loading:!1,updatetimeLoading:!1,exporting:!1,remind:!1,modal5:!1,searchObj:{taskName:""},columns:[{title:"任务名称",key:"taskName",minWidth:120,align:"center",tooltip:!0},{title:"资源供应商",key:"supplierrms",minWidth:100,align:"center"},{title:"制卡类型",key:"cardType",minWidth:90,align:"center",render:function(t,e){var a=e.row,n="";switch(a.cardType){case"1":n="普通卡";break;case"2":n="省移动";break;case"3":n="欧洲卡";default:n=""}return t("label",n)}},{title:"执行状态",key:"taskStatus",minWidth:90,align:"center",render:function(t,e){var a=e.row,n="";switch(a.taskStatus){case"1":n="成功";break;case"2":n="失败";break;case"3":n="制卡中";break;case"4":n="回滚中";default:n=""}return t("label",n)}},{title:"任务开始时间",key:"taskStartTime",minWidth:150,align:"center"},{title:"任务结束时间",key:"taskEndTime",minWidth:150,align:"center"},{title:"制卡文件名",key:"cardfileName",minWidth:120,align:"center",tooltip:!0},{title:"任务失败原因",key:"taskFailLog",width:160,align:"center",tooltip:!0}],data:[]}},mounted:function(){var t=null===JSON.parse(localStorage.getItem("searchObj"))?"":JSON.parse(localStorage.getItem("searchObj"));t&&(this.searchObj.taskName=void 0===t.taskName?"":t.taskName),localStorage.removeItem("searchObj"),this.init()},methods:{init:function(){var t=["download"],e=["download"],a=["view","download","rollback"],n=this.$route.meta.permTypes,i=t.filter((function(t){return n.indexOf(t)>-1})),r=(e.filter((function(t){return n.indexOf(t)>-1})),a.filter((function(t){return n.indexOf(t)>-1})));if(i.length>0){var o=100+100*i.length;this.columns.splice(9,0,{title:"回滚成功文件",slot:"rollbackSuccessFile",width:o,align:"center",tooltip:!0})}if(i.length>0){o=100+100*i.length;this.columns.splice(10,0,{title:"回滚失败文件",slot:"rollbackFailFile",width:o,align:"center",tooltip:!0})}if(r.length>0){o=40+50*r.length;this.columns.splice(11,0,{title:"操作",slot:"action",width:o,align:"center"})}this.goPageFirst(1)},goPageFirst:function(t){var e=this;this.loading=!0;var a=this;Object(r["f"])({pageSize:10,pageNumber:t,taskName:this.searchObj.taskName}).then((function(n){"0000"==n.code&&(a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.total=n.count,e.data=n.data)})).catch((function(t){console.log(t)})).finally((function(){a.loading=!1,e.searchloading=!1}))},search:function(){this.searchloading=!0,this.goPageFirst(1)},goPage:function(t){this.goPageFirst(t)},addTask:function(){this.$router.push({path:"/addCard",query:{searchObj:encodeURIComponent(JSON.stringify(this.searchObj))}})},exportfile:function(t,e){var a=this;this.exporting=!0;Object(r["e"])(t,e).then((function(t){var e=t.data,n=decodeURI(t.headers["content-disposition"].match(/=(.*)$/)[1]);if("download"in document.createElement("a")){var i=a.$refs.downloadLink,r=URL.createObjectURL(e);i.download=n,i.href=r,i.click(),URL.revokeObjectURL(r)}else navigator.msSaveBlob(e,n)})).catch((function(t){return a.exporting=!1}))},ok:function(){},cancelModal:function(){this.modal5=!1},Info:function(t){var e=this,a=t.id,n=t.taskName,i=t.cardfileName;this.gtpProxy="1"==t.gtpProxy?"是":"2"==t.gtpProxy?"否":"",Object(r["b"])(a).then((function(t){"0000"===t.code&&(e.modal5=!0,e.id=a,e.items=t.data,e.taskName=n,e.cardfileName=i,e.items.length>35&&(e.remind=!0))})).catch((function(t){console.log(t)}))},Reback:function(t){var e=this;this.$Modal.confirm({title:"是否回滚？",onOk:function(){Object(r["d"])(t).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提示",desc:t.msg}),e.goPageFirst(e.currentPage)})).catch((function(t){console.log(t)}))}})}}},s=o,c=(a("991a"),a("2877")),l=Object(c["a"])(s,n,i,!1,null,null,null);e["default"]=l.exports},e999:function(t,e,a){}}]);