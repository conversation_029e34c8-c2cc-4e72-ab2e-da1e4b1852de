(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-28e8e413"],{"00b4":function(e,t,a){"use strict";a("ac1f");var r=a("23e7"),n=a("c65b"),i=a("1626"),o=a("825a"),s=a("577e"),c=function(){var e=!1,t=/[ac]/;return t.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===t.test("abc")&&e}(),l=/./.test;r({target:"RegExp",proto:!0,forced:!c},{test:function(e){var t=o(this),a=s(e),r=t.exec;if(!i(r))return n(l,t,a);var c=n(r,t,a);return null!==c&&(o(c),!0)}})},"09b5":function(e,t,a){},"2a0d":function(e,t,a){"use strict";a.d(t,"i",(function(){return i})),a.d(t,"a",(function(){return o})),a.d(t,"j",(function(){return s})),a.d(t,"b",(function(){return c})),a.d(t,"c",(function(){return l})),a.d(t,"g",(function(){return u})),a.d(t,"d",(function(){return d})),a.d(t,"h",(function(){return p})),a.d(t,"f",(function(){return m})),a.d(t,"e",(function(){return f}));var r=a("66df"),n="/cms",i=function(e){return r["a"].request({url:n+"/cooperation",params:e,method:"get"})},o=function(e){return r["a"].request({url:n+"/cooperation",data:e,method:"post"})},s=function(e){return r["a"].request({url:n+"/cooperation/updateCooperation",data:e,method:"post"})},c=function(e){return r["a"].request({url:n+"/cooperation",params:e,method:"put"})},l=function(e){return r["a"].request({url:n+"/cooperation",data:e,method:"delete"})},u=function(e){return r["a"].request({url:n+"/cooperation/detail",params:e,method:"get"})},d=function(e){return r["a"].request({url:n+"/cooperation/derive",params:e,responseType:"blob",method:"get"})},p=function(e){return r["a"].request({url:"/pms/api/v1/package/getList",data:e,method:"post"})},m=function(e){return r["a"].request({url:n+"/cooperation/getCooperationMoney",params:e,method:"get"})},f=function(e){return r["a"].request({url:"/oms/api/v1/country/queryCounrtyList",method:"get"})}},"3f7e":function(e,t,a){"use strict";var r=a("b5db"),n=r.match(/firefox\/(\d+)/i);e.exports=!!n&&+n[1]},"4b56":function(e,t,a){"use strict";a.r(t);var r=a("ade3"),n=(a("b0c0"),a("ac1f"),a("5319"),function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padiing:"16px"}},[t("Form",{ref:"searchForm",attrs:{model:e.searchObj,inline:""}},[t("FormItem",[t("span",{staticClass:"input_notice"},[e._v("终端厂商：")]),e._v("  \n\t\t\t"),t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入终端厂商名称",clearable:""},model:{value:e.searchObj.manufacturerName,callback:function(t){e.$set(e.searchObj,"manufacturerName",t)},expression:"searchObj.manufacturerName"}})],1),t("FormItem",[t("span",{staticClass:"input_notice"},[e._v("类型：")]),e._v("  \n\t\t\t"),t("Select",{staticStyle:{width:"200px"},attrs:{filterable:"",placeholder:"请选择终端厂商类型",clearable:!0},model:{value:e.searchObj.manufacturerType,callback:function(t){e.$set(e.searchObj,"manufacturerType",t)},expression:"searchObj.manufacturerType"}},e._l(e.manufacturerTypeList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{margin:"0 2px"},attrs:{type:"primary",loading:e.searchLoading},on:{click:e.searchManufacturer}},[e._v("\n\t\t\t\t搜索\n\t\t\t")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 2px"},attrs:{type:"info"},on:{click:e.manufacturerAdd}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"md-add"}}),e._v(" 厂商新增")],1)]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"batchDelete",expression:"'batchDelete'"}],staticStyle:{margin:"0 2px"},attrs:{type:"error",loading:e.batchDeleteLoading},on:{click:e.deleteList}},[t("Icon",{attrs:{type:"ios-trash"}}),e._v(" 批量删除\n\t\t\t")],1)],1)],1),t("div",[t("Table",{ref:"selection",attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.tableLoading},on:{"on-selection-change":e.handleRowChange},scopedSlots:e._u([{key:"action",fn:function(a){var r=a.row;a.index;return[t("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.manufacturerCommon(r,"Info")}}},[e._v("使用记录")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(t){return e.manufacturerCommon(r,"Update")}}},[e._v("编辑")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"plmnlist",expression:"'plmnlist'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"warning",size:"small"},on:{click:function(t){return e.plmnlistManage(r)}}},[e._v("PLMNLIST管理")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],attrs:{type:"error",size:"small",loading:r.delLoading},on:{click:function(t){return e.manufacturerDel(r)}}},[e._v("删除")])]}},{key:"approval",fn:function(a){var r=a.row;a.index;return["1"===r.checkStatus||"4"===r.checkStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small",loading:r.checkPassLoading},on:{click:function(t){return e.manufacturerApproval(r,"Pass")}}},[e._v("通过")]):e._e(),"1"===r.checkStatus||"4"===r.checkStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],attrs:{type:"error",size:"small",loading:r.checkUnPassLoading},on:{click:function(t){return e.manufacturerApproval(r,"Fail")}}},[e._v("不通过")]):e._e()]}}])}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.total,"page-size":e.pageSize,current:e.page,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.page=t},"on-change":e.loadByPage}})],1),t("Modal",{attrs:{title:e.title,"footer-hide":!0,"mask-closable":!1,width:"1000px"},on:{"on-cancel":function(t){return e.reset("editObj")}},model:{value:e.manufacturerEditFlag,callback:function(t){e.manufacturerEditFlag=t},expression:"manufacturerEditFlag"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("Form",{ref:"editObj",attrs:{model:e.editObj,"label-width":180,rules:e.ruleEditValidate}},[t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"终端厂商名称",prop:"manufacturerName"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入终端厂商名称"},model:{value:e.editObj.manufacturerName,callback:function(t){e.$set(e.editObj,"manufacturerName",t)},expression:"editObj.manufacturerName"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"APPkey&APPSecret生成方式",prop:"eopCreateType"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择生成方式",clearable:!0},model:{value:e.editObj.eopCreateType,callback:function(t){e.$set(e.editObj,"eopCreateType",t)},expression:"editObj.eopCreateType"}},e._l(e.appMethodList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1)],1)],1),"2"===e.editObj.eopCreateType?t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"AppKey",prop:"AppKey"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入AppKey"},model:{value:e.editObj.AppKey,callback:function(t){e.$set(e.editObj,"AppKey",t)},expression:"editObj.AppKey"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"APPSecret",prop:"AppSecret"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入APPSecret"},model:{value:e.editObj.AppSecret,callback:function(t){e.$set(e.editObj,"AppSecret",t)},expression:"editObj.AppSecret"}})],1)],1)],1):e._e(),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"公司名称",prop:"companyName"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入公司名称"},model:{value:e.editObj.companyName,callback:function(t){e.$set(e.editObj,"companyName",t)},expression:"editObj.companyName"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"地址",prop:"address"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入地址"},model:{value:e.editObj.address,callback:function(t){e.$set(e.editObj,"address",t)},expression:"editObj.address"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"内部订单",prop:"internalOrder"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择是否内部订单",clearable:!0},model:{value:e.editObj.internalOrder,callback:function(t){e.$set(e.editObj,"internalOrder",t)},expression:"editObj.internalOrder"}},[t("Option",{attrs:{value:"0"}},[e._v("是")]),t("Option",{attrs:{value:"1"}},[e._v("否")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"币种",prop:"currencyCode"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择币种",clearable:!0},model:{value:e.editObj.currencyCode,callback:function(t){e.$set(e.editObj,"currencyCode",t)},expression:"editObj.currencyCode"}},[t("Option",{attrs:{value:"156"}},[e._v("人民币")]),t("Option",{attrs:{value:"344"}},[e._v("港币")]),t("Option",{attrs:{value:"840"}},[e._v("美元")])],1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"EBS Code",prop:"EBSCode"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入EBS Code"},model:{value:e.editObj.EBSCode,callback:function(t){e.$set(e.editObj,"EBSCode",t)},expression:"editObj.EBSCode"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"通知URL",prop:"URL"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入URL"},model:{value:e.editObj.URL,callback:function(t){e.$set(e.editObj,"URL",t)},expression:"editObj.URL"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"激活通知",prop:"activeNotifyType"}},[t("i-switch",{attrs:{size:"large"},model:{value:e.editObj.activeNotifyType,callback:function(t){e.$set(e.editObj,"activeNotifyType",t)},expression:"editObj.activeNotifyType"}},[t("span",{attrs:{slot:"open"},slot:"open"},[e._v("开")]),t("span",{attrs:{slot:"close"},slot:"close"},[e._v("关")])])],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"到期通知",prop:"dueNotifyType"}},[t("i-switch",{attrs:{size:"large"},model:{value:e.editObj.dueNotifyType,callback:function(t){e.$set(e.editObj,"dueNotifyType",t)},expression:"editObj.dueNotifyType"}},[t("span",{attrs:{slot:"open"},slot:"open"},[e._v("开")]),t("span",{attrs:{slot:"close"},slot:"close"},[e._v("关")])])],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"厂商类型",prop:"manufacturerType"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择厂商类型",clearable:!0},model:{value:e.editObj.manufacturerType,callback:function(t){e.$set(e.editObj,"manufacturerType",t)},expression:"editObj.manufacturerType"}},e._l(e.manufacturerTypeList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},["8"===e.editObj.manufacturerType?t("Tabs",{staticStyle:{"margin-left":"100px"},attrs:{value:e.checked,animated:!1},on:{"on-click":e.tagChange}},[t("TabPane",{attrs:{label:"套餐计费",name:"packagesInfo"}},[e._l(e.editObj.packages,(function(a,r){return"packagesInfo"==e.checked?t("div",{key:r},[t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{staticStyle:{"margin-left":"-100px"},attrs:{label:"套餐名称",prop:"packages."+r+".packageName",rules:[{required:!0,message:"套餐名称不能为空",trigger:"blur"},{max:200,message:"最长200位"}]}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入套餐名称"},model:{value:a.packageName,callback:function(t){e.$set(a,"packageName",t)},expression:"obj.packageName"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{staticStyle:{"margin-left":"-30px"},attrs:{label:"套餐价格",prop:"packages."+r+".price",rules:[{required:!0,message:"套餐价格不能为空",trigger:"blur"},{pattern:/^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger:"blur",message:"请输入1-12位数字，整数首位非0（可精确到小数点后2位）"}]}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入套餐价格"},model:{value:a.price,callback:function(t){e.$set(a,"price",t)},expression:"obj.price"}},[t("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])]),t("div",{staticStyle:{position:"absolute",top:"0",right:"40px"},on:{click:function(t){return e.delPackageBtn(r)}}},[t("Tooltip",{attrs:{content:"删除该项套餐",placement:"left"}},[t("Icon",{attrs:{type:"md-trash",size:"22",color:"#ff3300"}})],1)],1)],1)],1)],1)],1):e._e()})),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{staticStyle:{"margin-left":"-100px"},attrs:{label:"套餐",prop:"packages"}},[t("Button",{staticClass:"inputSty",attrs:{type:"dashed",long:"",icon:"md-add"},on:{click:e.addPackage}},[e._v("添加套餐")])],1)],1)],1)],2),t("TabPane",{attrs:{label:"流量计费",name:"flowInfo"}},[t("div",{staticStyle:{"padding-bottom":"20px"}},[t("span",[e._v("套餐名称:"+e._s(e.editObj.manufacturerName)+"流量套餐")])]),e._l(e.editObj.flowList,(function(a,r){return"flowInfo"==e.checked?t("div",{key:r},[t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{staticStyle:{"margin-left":"-50px"},attrs:{label:"选择流量方向",prop:"flowList."+r+".direction",rules:[{type:"array",required:!0,message:"流量方向不能为空",trigger:"change"}]}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",transfer:"",placeholder:"请选择流量方向",clearable:!0,multiple:""},model:{value:a.direction,callback:function(t){e.$set(a,"direction",t)},expression:"obj.direction"}},e._l(e.continentList,(function(a){return t("Option",{key:a.id,attrs:{value:a.mcc}},[e._v(e._s(a.countryEn))])})),1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{staticStyle:{"margin-left":"-30px"},attrs:{label:"流量单价",prop:"flowList."+r+".price",rules:[{required:!0,message:"流量单价不能为空",trigger:"blur"},{pattern:/^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger:"blur",message:"请输入1-12位数字，整数首位非0（可精确到小数点后2位）"}]}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入流量单价"},model:{value:a.price,callback:function(t){e.$set(a,"price",t)},expression:"obj.price"}},[t("span",{attrs:{slot:"append"},slot:"append"},[e._v("元/GB")])]),t("div",{staticStyle:{position:"absolute",top:"0",right:"20px"},on:{click:function(t){return e.delFlowBtn(r)}}},[t("Tooltip",{attrs:{content:"删除该项方向",placement:"left"}},[t("Icon",{attrs:{type:"md-trash",size:"22",color:"#ff3300"}})],1)],1)],1)],1)],1)],1):e._e()})),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{staticStyle:{"margin-left":"-50px"},attrs:{label:"方向",prop:"flowList"}},[t("Button",{staticClass:"inputSty",attrs:{type:"dashed",long:"",icon:"md-add"},on:{click:e.addFlow}},[e._v("添加方向")])],1)],1)],1)],2)],1):e._e()],1)],1)],1),t("div",{staticStyle:{"text-align":"center","margin-top":"20px"}},["Add"==e.operationType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{type:"primary",loading:e.addLoading},on:{click:function(t){return e.add("editObj")}}},[e._v("提交")]):e._e(),"Update"==e.operationType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],attrs:{type:"primary",loading:e.addLoading},on:{click:function(t){return e.update("editObj")}}},[e._v("提交")]):e._e(),t("Button",{staticStyle:{"margin-left":"8px"},on:{click:function(t){return e.reset("editObj")}}},[e._v("重置")])],1)],1)]),t("Modal",{attrs:{title:"删除审核不通过","mask-closable":!1},on:{"on-cancel":e.cancel2},model:{value:e.modal2,callback:function(t){e.modal2=t},expression:"modal2"}},[t("h3",[e._v("确认企业是否还可用？")]),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{attrs:{type:"primary"},on:{click:function(t){return e.checkAvailable("true")}}},[e._v("可用")]),t("Button",{on:{click:function(t){return e.checkAvailable("fail")}}},[e._v("不可用")])],1)]),t("Modal",{attrs:{title:"PLMNLIST管理","mask-closable":!1,width:"700px"},on:{"on-cancel":e.cancelModal},model:{value:e.modal1,callback:function(t){e.modal1=t},expression:"modal1"}},[t("Tabs",{attrs:{type:"card",value:"name1"},on:{"on-click":e.choseTab}},[t("TabPane",{attrs:{label:"添加MCC",name:"name1"}},[t("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold","font-size":"18px",margin:"15px 10px"}},[t("span",[e._v("厂商名称:")]),e._v("  \n\t\t\t\t\t"),t("span",{staticStyle:{"font-weight":"bold"}},[e._v(e._s(e.corpName))]),e._v("  \n\t\t\t\t")]),t("div",{staticClass:"search_head",staticStyle:{margin:"30px 0px 80px 0px"}},[t("Form",{ref:"formmodel",staticStyle:{"font-weight":"bold"},attrs:{model:e.formmodel,"label-width":70,inline:""}},[e._l(e.formmodel.MCCList,(function(a,r){return t("div",{key:r,staticStyle:{display:"flex","justify-content":"flex-start"}},[t("FormItem",{attrs:{label:"国家",prop:"MCCList."+r+".mcc",rules:[{required:!0,message:"请输入MCC码",trigger:"blur"}]}},[t("Select",{staticStyle:{width:"180px"},attrs:{filterable:"",placeholder:"选择或输入国家",clearable:!0},model:{value:a.mcc,callback:function(t){e.$set(a,"mcc",t)},expression:"item.mcc"}},e._l(e.continentList,(function(a){return t("Option",{key:a.id,attrs:{value:a.mcc}},[e._v(e._s(a.countryEn))])})),1)],1),t("FormItem",{attrs:{label:"MNC",prop:"MCCList."+r+".mnc",rules:[{required:!0,message:"请输入MNC码",trigger:"blur"}]}},[t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"多个MNC用|分隔",clearable:"",onkeyup:a.mnc=a.mnc.replace(/\s+/g,"")},model:{value:a.mnc,callback:function(t){e.$set(a,"mnc",t)},expression:"item.mnc"}})],1),t("FormItem",[t("Button",{staticStyle:{"margin-left":"10px"},attrs:{type:"error",size:"small"},on:{click:function(t){return e.removemcc(r)}}},[e._v("删除")])],1)],1)})),t("FormItem",[t("Button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.addmcc()}}},[e._v("添加国家")])],1)],2)],1)]),t("TabPane",{attrs:{label:"添加文件",name:"name2"}},[t("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold","font-size":"18px",margin:"15px 10px"}},[t("span",[e._v("厂商名称:")]),e._v("  \n\t\t\t\t\t"),t("span",{staticStyle:{"font-weight":"bold"}},[e._v(e._s(e.corpName))]),e._v("  \n\t\t\t\t")]),t("div",{staticClass:"search_head",staticStyle:{margin:"50px 0"}},[t("Form",{ref:"formValidate",staticStyle:{"font-weight":"bold"},attrs:{model:e.formValidate,"label-width":100,"label-height":100,inline:""}},[t("FormItem",{staticStyle:{width:"510px"},attrs:{label:"文件",prop:"file"}},[t("Upload",{attrs:Object(r["a"])(Object(r["a"])(Object(r["a"])(Object(r["a"])(Object(r["a"])({multiple:"",type:"drag",action:"//jsonplaceholder.typicode.com/posts/"},"action",e.uploadUrl),"on-success",e.fileSuccess),"on-error",e.handleError),"before-upload",e.handleBeforeUpload),"on-progress",e.fileUploading),model:{value:e.formValidate.file,callback:function(t){e.$set(e.formValidate,"file",t)},expression:"formValidate.file"}},[t("div",{staticStyle:{padding:"20px 0"}},[t("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),t("p",[e._v("点击或拖拽文件上传")])],1)]),e.formValidate.file?t("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"100%"}},[t("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[t("span",[t("Icon",{attrs:{type:"ios-folder"}}),e._v(e._s(e.formValidate.file.name))],1),t("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:e.removeFile}})])]):e._e(),t("div",{staticStyle:{width:"100%"}},[t("Button",{attrs:{type:"primary",loading:e.downloading,icon:"ios-download"},on:{click:e.downloadFile}},[e._v("下载模板文件")]),t("Alert",{staticStyle:{float:"right",padding:"8px 10px 8px 10px"},attrs:{type:"warning"}},[e._v(e._s(e.message))]),t("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1)],1)],1)],1)])],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.cancelModal}},[e._v("取消")]),t("Button",{attrs:{type:"primary",loading:e.addmccLoading},on:{click:e.handleUpload}},[e._v("确定")])],1)],1),t("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:e.modelColumns,data:e.modelData}})],1)}),i=[],o=a("c7eb"),s=a("1da1"),c=(a("d81d"),a("14d9"),a("4e82"),a("a434"),a("e9c4"),a("d3b7"),a("00b4"),a("25f0"),a("159b"),a("f7fa")),l=a("2a0d"),u=a("c70b"),d={components:{},data:function(){return{checked:"packagesInfo",title:"终端厂商新增",searchObj:{manufacturerName:"",manufacturerType:""},manufacturerEditFlag:!1,operationType:"Add",editObj:{corpId:"",manufacturerName:"",eopCreateType:"",AppKey:"",AppSecret:"",currencyCode:"",internalOrder:"",address:"",companyName:"",URL:"",EBSCode:"",activeNotifyType:!1,dueNotifyType:!1,manufacturerType:"",paymentMode:"",packages:[],flowList:[]},orderObj:{manufacturerName:"",paymentMode:"",packages:[],flowList:[],flowPool:[]},appMethodList:[{value:"1",label:"自动生成"},{value:"2",label:"手动输入"}],manufacturerTypeList:[{value:"7",label:"线上厂商"},{value:"8",label:"线下厂商"}],paymentModeList:[{value:"1",label:"按套餐"},{value:"2",label:"按流量"}],continentList:[],ruleEditValidate:{manufacturerName:[{required:!0,type:"string",message:"终端厂商名称不能为空",trigger:"blur"},{max:50,message:"最长50位"}],eopCreateType:[{required:!0,type:"string",message:"APPkey&APPSecret生成方式不能为空",trigger:"change"}],AppKey:[{required:!0,type:"string",message:"APPkey不能为空",trigger:"blur"},{max:255,message:"最长255位"}],AppSecret:[{required:!0,type:"string",message:"APPSecret不能为空",trigger:"blur"},{max:255,message:"最长255位"}],companyName:[{required:!0,type:"string",message:"公司名称不能为空",trigger:"blur"},{max:50,message:"最长50位"}],address:[{required:!0,type:"string",message:"地址不能为空",trigger:"blur"},{max:200,message:"最长200位"}],internalOrder:[{required:!0,type:"string",message:"内部订单不能为空",trigger:"change"}],EBSCode:[{required:!0,type:"string",message:"EBS Code不能为空",trigger:"blur"},{max:50,message:"最长50位"}],URL:[{required:!0,type:"string",message:"URL不能为空",trigger:"blur"},{max:255,message:"最长255位"}],activeNotifyType:[{required:!0,type:"boolean",message:"激活通知开关不能为空",trigger:"blur"}],dueNotifyType:[{required:!0,type:"boolean",message:"到期通知开关不能为空",trigger:"blur"}],manufacturerType:[{required:!0,type:"string",message:"厂商类型不能为空",trigger:"change"}],currencyCode:[{required:!0,type:"string",message:"币种不能为空",trigger:"change"}],paymentMode:[{required:!0,type:"string",message:"付费模式不能为空",trigger:"change"}],packages:[{type:"array",required:!0,message:"请选择套餐",trigger:"change"}],flowList:[{type:"array",required:!0,message:"请填写方向",trigger:"change"}]},ruleOrderValidate:{},tableData:[],selection:[],selectionIds:[],total:0,pageSize:10,page:1,columns:[{type:"selection",minWidth:60,align:"center"},{title:"厂商名称",key:"corpName",align:"center",minWidth:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"APPKey",key:"appKey",align:"center",minWidth:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"AppSecret",key:"appSecret",align:"center",minWidth:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"EBS Code",key:"ebsCode",align:"center",minWidth:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"通知URL",key:"notifyUrl",align:"center",minWidth:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"厂商类型",key:"type",align:"center",minWidth:120,tooltip:!0,render:function(e,t){var a=t.row,r=7==a.type?"线上厂商":"线下厂商";return e("label",r)}},{title:"操作",slot:"action",minWidth:280,align:"center"},{title:"审批状态",key:"checkStatus",align:"center",minWidth:120,render:function(e,t){var a=t.row,r=1==a.checkStatus?"#2d8cf0":2==a.checkStatus?"#00cc66":3==a.checkStatus?"#ff0000":"#ed4014",n=1==a.checkStatus?"新建待审批":2==a.checkStatus?"通过":3==a.checkStatus?"不通过":4==a.checkStatus?"删除待审批":"";return e("label",{style:{color:r}},n)}},{title:"审批操作",slot:"approval",minWidth:160,align:"center"}],modalHeight:"100%",modalOverflowX:"visible",tableLoading:!1,searchLoading:!1,addLoading:!1,batchDeleteLoading:!1,downloading:!1,addmccLoading:!1,modal2:!1,checkItem:{},modal1:!1,index:0,tabid:"name1",uploadUrl:"",formValidate:{file:null},formmodel:{mcc:"",mnc:"",MCCList:[{index:0,mnc:"",mcc:"",corpId:""}]},plmnCorpId:"",MCCListArr:[],message:"文件仅支持csv格式文件,大小不能超过5MB",modelColumns:[{title:"国家名称（英文）",minWidth:"300",key:"countryName"},{title:"MNC|MNC|MNC",minWidth:"300",key:"mnc"}],modelData:[],corpName:""}},methods:{goPageFirst:function(e){var t=this;this.selection=[],this.selectionIds=[];var a=this;a.tableLoading=!0;var r=10,n=e;Object(c["p"])({corpName:a.searchObj.manufacturerName,corpType:a.searchObj.manufacturerType,pageNumber:n,pageSize:r}).then((function(e){if(!e||"0000"!=e.code)throw e;var r=e.data;a.tableData=r.records,a.total=r.totalCount,a.tableLoading=!1,a.searchLoading=!1,t.tableData.length&&t.tableData.map((function(e){return t.$set(e,"delLoading",!1),t.$set(e,"checkPassLoading",!1),t.$set(e,"checkUnPassLoading",!1),e}))})).catch((function(e){a.tableLoading=!1,a.searchLoading=!1,t.tableData.length&&t.tableData.map((function(e){return t.$set(e,"delLoading",!1),t.$set(e,"checkPassLoading",!1),t.$set(e,"checkUnPassLoading",!1),e}))}))},getLocalList:function(){var e=this;Object(l["e"])().then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.continentList=a,e.continentList.sort((function(e,t){return e.countryEn.localeCompare(t.countryEn)}))})).catch((function(e){}))},add:function(e){var t=this,a=this;a.$refs[e].validate((function(e){if(e){a.addLoading=!0;var r=a.editObj.packages,n=a.editObj.flowList,i=[],o=[];null!=r&&""!=r[0].packageName&&r.forEach((function(e){var t={};t.packageName=e.packageName,t.price=u.multiply(u.bignumber(e.price),100).toString(),t.currencyCode=e.currency,i.push(t)})),null!=n&&""!=n[0].direction&&n.forEach((function(e){var t={};t.mccList=e.direction,t.packageName=a.editObj.manufacturerName+"流量套餐",t.price=u.multiply(u.bignumber(e.price),100).toString(),o.push(t)})),Object(c["a"])({corpName:a.editObj.manufacturerName,eopCreateType:t.editObj.eopCreateType,appKey:a.editObj.AppKey,appSecret:a.editObj.AppSecret,notifyUrl:a.editObj.URL,ebsCode:a.editObj.EBSCode,activeNotifyType:!0===a.editObj.activeNotifyType?"1":"2",dueNotifyType:!0===a.editObj.dueNotifyType?"1":"2",type:a.editObj.manufacturerType,settleType:a.editObj.paymentMode,packageInfo:i,flowInfo:o,currencyCode:a.editObj.currencyCode,address:a.editObj.address,companyName:a.editObj.companyName,internalOrder:a.editObj.internalOrder}).then((function(e){if(!e||"0000"!=e.code)throw e;a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.manufacturerEditFlag=!1,a.addLoading=!1,a.page=1,a.goPageFirst(1)})).catch((function(e){a.addLoading=!1}))}}))},reset:function(e){this.editObj={corpId:this.editObj.corpId,manufacturerName:"",AppKey:"",AppSecret:"",CMIAppKey:"",CMIAppSecret:"",URL:"",EBSCode:"",activeNotifyType:!1,dueNotifyType:!1,manufacturerType:"",paymentMode:"",packages:[{packageName:"",price:""}],flowList:[{packageName:"",direction:"",price:""}],currencyCode:"",address:"",companyName:"",internalOrder:""},this.$refs.editObj.resetFields()},cancel2:function(){this.modal2=!1,this.checkItem={},this.page=1,this.goPageFirst(1)},loadByPage:function(e){this.page=e,this.goPageFirst(e)},searchManufacturer:function(){this.searchLoading=!0,this.page=1,this.goPageFirst(1)},setModalHeight:function(e){},tagChange:function(e){this.checked=e},manufacturerAdd:function(){this.reset(),this.manufacturerEditFlag=!0,this.operationType="Add"},addPackage:function(){this.editObj.packages.push({packageName:"",price:"",currency:""})},addFlow:function(){this.editObj.flowList.push({packageName:"",direction:"",price:"",currency:""})},delPackageBtn:function(e){this.editObj.packages.splice(e,1)},delFlowBtn:function(e){this.editObj.flowList.splice(e,1)},delOrderPackageBtn:function(e){this.orderObj.packages.splice(e,1)},delOrderFlowBtn:function(e){this.orderObj.flowList.splice(e,1)},manufacturerCommon:function(e,t){"Info"===t&&(this.operationType=t,this.$router.push({name:"manufacturerInfo",query:{manufacturer:encodeURIComponent(JSON.stringify(e))}})),"Update"===t&&(this.operationType=t,this.$router.push({name:"manufacturer_update",query:{manufacturer:encodeURIComponent(JSON.stringify(e))}}))},manufacturerApproval:function(e,t){var a=this,r={};r.id=e.corpId,"Pass"===t&&this.$Modal.confirm({title:"确认通过？",onOk:function(){r.passed=!0,e.checkPassLoading=!0,a.check(r)}}),"4"===e.checkStatus&&"Fail"===t&&(this.checkItem=e,e.checkUnPassLoading=!0,this.modal2=!0),"4"!==e.checkStatus&&"Fail"===t&&this.$Modal.confirm({title:"确认不通过？",onOk:function(){r.passed=!1,e.checkUnPassLoading=!0,a.check(r)}})},checkAvailable:function(e){var t={};t.id=this.checkItem.corpId,"true"===e&&(t.passed=!1,t.available=!0,this.check(t)),"fail"===e&&(t.passed=!1,t.available=!1,this.check(t)),this.modal2=!1},check:function(e){var t=this;Object(c["d"])(e.id,e).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.page=1,t.goPageFirst(1)})).catch((function(e){t.$Notice.error({title:"操作提示",desc:"操作失败"})}))},manufacturerDel:function(e){var t=this;this.$Modal.confirm({title:"确认删除？",onOk:function(){e.delLoading=!0;var a=[];a.push(e.corpId),Object(c["f"])(a).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.page=1,t.goPageFirst(1)})).catch((function(e){t.$Notice.error({title:"操作提示",desc:"操作失败"})}))}})},handleRowChange:function(e){var t=this;this.selection=e,this.selectionIds=[],e.map((function(e,a){t.selectionIds.push(e.corpId)}))},deleteList:function(){var e=this,t=this.selection.length;t<1?this.$Message.warning("请至少选择一条记录"):this.$Modal.confirm({title:"确认删除？",onOk:function(){e.batchDeleteLoading=!0,Object(c["f"])(e.selectionIds).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.batchDeleteLoading=!1,e.page=1,e.goPageFirst(1)})).catch((function(t){e.batchDeleteLoading=!1,e.$Notice.error({title:"操作提示",desc:"操作失败"})}))}})},plmnlistManage:function(e){this.corpName=e.corpName,this.plmnCorpId=e.corpId,this.getPlmnlist(),this.modal1=!0},cancelModal:function(){this.modal1=!1,this.$refs.formmodel.resetFields(),this.formmodel.MCCList.splice(1,this.index),this.formValidate.file="",this.addmccLoading=!1,this.goPageFirst(1),this.page=1},choseTab:function(e){this.tabid=e},removemcc:function(e){this.formmodel.MCCList.splice(e,1),this.index--},addmcc:function(){this.index++,this.formmodel.MCCList.push({index:this.index,mnc:"",mcc:"",corpId:""})},fileUploading:function(e,t,a){this.message="文件上传中、待进度条消失后再操作"},fileSuccess:function(e,t,a){this.message="请先下载模板文件，并按格式填写后上传"},handleError:function(e,t){var a=this;setTimeout((function(){a.uploading=!1,a.$Notice.warning({title:"错误提示",desc:"上传失败！"})}),3e3)},handleBeforeUpload:function(e){return/^.+(\.csv)$/.test(e.name)?this.formValidate.file=e:this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+e.name+" 格式不正确，请上传.csv格式文件。"}),!1},removeFile:function(){this.formValidate.file=""},downloadFile:function(){this.$refs.modelTable.exportCsv({filename:"终端厂商plmnlist",type:"csv",columns:this.modelColumns,data:this.modelData})},getPlmnlist:function(){var e=this;Object(c["q"])({corpId:this.plmnCorpId}).then((function(t){"0000"==t.code&&(e.formmodel.MCCList=t.data)})).catch((function(e){console.error(e)}))},handleUpload:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("name1"!==e.tabid){t.next=8;break}if(a=e.formmodel.MCCList?e.formmodel.MCCList.length:"",!(a<1)){t.next=5;break}return e.$Message.warning("请添加至少一组mcc"),t.abrupt("return");case 5:e.$refs.formmodel.validate((function(t){if(t){e.addmccLoading=!0;new FormData;e.formmodel.MCCList.forEach((function(t){t.corpId=e.plmnCorpId,delete t.index,delete t.id,e.MCCListArr=JSON.stringify(e.formmodel.MCCList)})),Object(c["w"])(e.MCCListArr).then((function(t){if("0000"!==t.code)throw t;t.data;e.$Notice.success({title:"操作提醒",desc:"修改成功"}),e.addmccLoading=!1,e.cancelModal(),e.page=1,e.goPageFirst(1)})).catch((function(e){console.log(e)})).finally((function(){e.addmccLoading=!1,e.cancelModal()}))}})),t.next=14;break;case 8:if(e.formValidate.file){t.next=13;break}return e.$Message.warning("请选择需要上传的文件"),t.abrupt("return");case 13:e.$refs.formValidate.validate((function(t){if(t){var a=new FormData;a.append("file",e.formValidate.file),a.append("corpId",e.plmnCorpId),Object(c["c"])(a).then((function(t){if("0000"!==t.code)throw t;e.$Notice.success({title:"操作成功",desc:"添加成功"}),e.addmccLoading=!1,e.cancelModal(),e.page=1,e.goPageFirst(1)})).catch((function(e){console.log(e)})).finally((function(){e.addmccLoading=!1,e.cancelModal()}))}}));case 14:case"end":return t.stop()}}),t)})))()}},mounted:function(){this.goPageFirst(1),this.getLocalList()}},p=d,m=(a("f2b2"),a("2877")),f=Object(m["a"])(p,n,i,!1,null,null,null);t["default"]=f.exports},"4e82":function(e,t,a){"use strict";var r=a("23e7"),n=a("e330"),i=a("59ed"),o=a("7b0b"),s=a("07fa"),c=a("083a"),l=a("577e"),u=a("d039"),d=a("addb"),p=a("a640"),m=a("3f7e"),f=a("99f4"),h=a("1212"),g=a("ea83"),b=[],y=n(b.sort),v=n(b.push),k=u((function(){b.sort(void 0)})),C=u((function(){b.sort(null)})),x=p("sort"),O=!u((function(){if(h)return h<70;if(!(m&&m>3)){if(f)return!0;if(g)return g<603;var e,t,a,r,n="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(r=0;r<47;r++)b.push({k:t+r,v:a})}for(b.sort((function(e,t){return t.v-e.v})),r=0;r<b.length;r++)t=b[r].k.charAt(0),n.charAt(n.length-1)!==t&&(n+=t);return"DGBEFHACIJK"!==n}})),S=k||!C||!x||!O,w=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:l(t)>l(a)?1:-1}};r({target:"Array",proto:!0,forced:S},{sort:function(e){void 0!==e&&i(e);var t=o(this);if(O)return void 0===e?y(t):y(t,e);var a,r,n=[],l=s(t);for(r=0;r<l;r++)r in t&&v(n,t[r]);d(n,w(e)),a=s(n),r=0;while(r<a)t[r]=n[r++];while(r<l)c(t,r++);return t}})},"99f4":function(e,t,a){"use strict";var r=a("b5db");e.exports=/MSIE|Trident/.test(r)},addb:function(e,t,a){"use strict";var r=a("f36a"),n=Math.floor,i=function(e,t){var a=e.length;if(a<8){var o,s,c=1;while(c<a){s=c,o=e[c];while(s&&t(e[s-1],o)>0)e[s]=e[--s];s!==c++&&(e[s]=o)}}else{var l=n(a/2),u=i(r(e,0,l),t),d=i(r(e,l),t),p=u.length,m=d.length,f=0,h=0;while(f<p||h<m)e[f+h]=f<p&&h<m?t(u[f],d[h])<=0?u[f++]:d[h++]:f<p?u[f++]:d[h++]}return e};e.exports=i},ea83:function(e,t,a){"use strict";var r=a("b5db"),n=r.match(/AppleWebKit\/(\d+)\./);e.exports=!!n&&+n[1]},f2b2:function(e,t,a){"use strict";a("09b5")},f7fa:function(e,t,a){"use strict";a.d(t,"p",(function(){return i})),a.d(t,"r",(function(){return o})),a.d(t,"a",(function(){return s})),a.d(t,"u",(function(){return c})),a.d(t,"d",(function(){return l})),a.d(t,"f",(function(){return u})),a.d(t,"o",(function(){return d})),a.d(t,"i",(function(){return p})),a.d(t,"b",(function(){return m})),a.d(t,"v",(function(){return f})),a.d(t,"e",(function(){return h})),a.d(t,"h",(function(){return g})),a.d(t,"g",(function(){return b})),a.d(t,"s",(function(){return y})),a.d(t,"l",(function(){return v})),a.d(t,"k",(function(){return k})),a.d(t,"t",(function(){return C})),a.d(t,"m",(function(){return x})),a.d(t,"n",(function(){return O})),a.d(t,"j",(function(){return S})),a.d(t,"w",(function(){return w})),a.d(t,"c",(function(){return j})),a.d(t,"q",(function(){return L}));var r=a("66df"),n="/cms/api/v1",i=function(e){return r["a"].request({url:n+"/terminal/pages",params:e,method:"get"})},o=function(e){return r["a"].request({url:n+"/terminal/settleRule/queryList",params:e,method:"get"})},s=function(e){return r["a"].request({url:n+"/terminal",data:e,method:"post"})},c=function(e,t){return r["a"].request({url:n+"/terminal/"+e,data:t,method:"put"})},l=function(e,t){return r["a"].request({url:n+"/terminal/audit/"+e,params:t,method:"put"})},u=function(e){return r["a"].request({url:n+"/terminal",data:e,method:"delete"})},d=function(e){return r["a"].request({url:n+"/terminal/details",params:e,method:"get"})},p=function(e){return r["a"].request({url:n+"/terminal/details/export",params:e,responseType:"blob",method:"get"})},m=function(e){return r["a"].request({url:n+"/terminal/settleRule/add",data:e,method:"post"})},f=function(e){return r["a"].request({url:n+"/terminal/settleRule/update",data:e,method:"put"})},h=function(e){return r["a"].request({url:"/pms/api/v1/cardPool/checkPackage",params:e,method:"get"})},g=function(e){return r["a"].request({url:n+"/terminal/settleRule/delete/"+e,method:"delete"})},b=function(e){return r["a"].request({url:n+"/terminal/settleRule/deleteBatch",data:e,method:"post"})},y=function(e){return r["a"].request({url:"/stat/cdr/flow/get/list",params:e,method:"get"})},v=function(e){return r["a"].request({url:"/stat/cdr/flow/export/details",params:e,method:"get"})},k=function(e){return r["a"].request({url:"/stat/cdr/flow/export/info",params:e,method:"get"})},C=function(e){return r["a"].request({url:"/stat/cdr/flow/get/info",params:e,method:"get"})},x=function(e){return r["a"].request({url:"/stat/cdr/flow/export/list",params:e,method:"get"})},O=function(e){return r["a"].request({url:"/stat/cdr/flow/get/details",params:e,method:"get"})},S=function(e){return r["a"].request({url:"/stat/cdr/flow/export/info/all",params:e,method:"get"})},w=function(e){return r["a"].request({url:n+"/terminal/plmnlist/update",data:e,method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"}})},j=function(e){return r["a"].request({url:n+"/terminal/plmnlist/createByFile",data:e,method:"post",contentType:"multipart/form-data"})},L=function(e){return r["a"].request({url:n+"/terminal/plmnlist/get",params:e,method:"get"})}}}]);