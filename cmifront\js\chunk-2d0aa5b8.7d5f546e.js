(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0aa5b8"],{1157:function(e,t,n){var r,i;
/*!
 * jQuery JavaScript Library v3.7.1
 * https://jquery.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2023-08-28T13:37Z
 */(function(t,n){"use strict";"object"===typeof e.exports?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)})("undefined"!==typeof window?window:this,(function(n,o){"use strict";var a=[],s=Object.getPrototypeOf,u=a.slice,l=a.flat?function(e){return a.flat.call(e)}:function(e){return a.concat.apply([],e)},c=a.push,f=a.indexOf,p={},d=p.toString,h=p.hasOwnProperty,g=h.toString,v=g.call(Object),y={},m=function(e){return"function"===typeof e&&"number"!==typeof e.nodeType&&"function"!==typeof e.item},x=function(e){return null!=e&&e===e.window},b=n.document,w={type:!0,src:!0,nonce:!0,noModule:!0};function T(e,t,n){n=n||b;var r,i,o=n.createElement("script");if(o.text=e,t)for(r in w)i=t[r]||t.getAttribute&&t.getAttribute(r),i&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function C(e){return null==e?e+"":"object"===typeof e||"function"===typeof e?p[d.call(e)]||"object":typeof e}var k="3.7.1",S=/HTML$/i,E=function(e,t){return new E.fn.init(e,t)};function j(e){var t=!!e&&"length"in e&&e.length,n=C(e);return!m(e)&&!x(e)&&("array"===n||0===t||"number"===typeof t&&t>0&&t-1 in e)}function A(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}E.fn=E.prototype={jquery:k,constructor:E,length:0,toArray:function(){return u.call(this)},get:function(e){return null==e?u.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=E.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return E.each(this,e)},map:function(e){return this.pushStack(E.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(u.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(E.grep(this,(function(e,t){return(t+1)%2})))},odd:function(){return this.pushStack(E.grep(this,(function(e,t){return t%2})))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:c,sort:a.sort,splice:a.splice},E.extend=E.fn.extend=function(){var e,t,n,r,i,o,a=arguments[0]||{},s=1,u=arguments.length,l=!1;for("boolean"===typeof a&&(l=a,a=arguments[s]||{},s++),"object"===typeof a||m(a)||(a={}),s===u&&(a=this,s--);s<u;s++)if(null!=(e=arguments[s]))for(t in e)r=e[t],"__proto__"!==t&&a!==r&&(l&&r&&(E.isPlainObject(r)||(i=Array.isArray(r)))?(n=a[t],o=i&&!Array.isArray(n)?[]:i||E.isPlainObject(n)?n:{},i=!1,a[t]=E.extend(l,o,r)):void 0!==r&&(a[t]=r));return a},E.extend({expando:"jQuery"+(k+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==d.call(e))&&(t=s(e),!t||(n=h.call(t,"constructor")&&t.constructor,"function"===typeof n&&g.call(n)===v))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){T(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(j(e)){for(n=e.length;r<n;r++)if(!1===t.call(e[r],r,e[r]))break}else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n="",r=0,i=e.nodeType;if(!i)while(t=e[r++])n+=E.text(t);return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(j(Object(e))?E.merge(n,"string"===typeof e?[e]:e):c.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:f.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!S.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r,i=[],o=0,a=e.length,s=!n;o<a;o++)r=!t(e[o],o),r!==s&&i.push(e[o]);return i},map:function(e,t,n){var r,i,o=0,a=[];if(j(e))for(r=e.length;o<r;o++)i=t(e[o],o,n),null!=i&&a.push(i);else for(o in e)i=t(e[o],o,n),null!=i&&a.push(i);return l(a)},guid:1,support:y}),"function"===typeof Symbol&&(E.fn[Symbol.iterator]=a[Symbol.iterator]),E.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){p["[object "+t+"]"]=t.toLowerCase()}));var D=a.pop,N=a.sort,q=a.splice,L="[\\x20\\t\\r\\n\\f]",H=new RegExp("^"+L+"+|((?:^|[^\\\\])(?:\\\\.)*)"+L+"+$","g");E.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var O=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function P(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}E.escapeSelector=function(e){return(e+"").replace(O,P)};var M=b,R=c;(function(){var e,t,r,i,o,s,l,c,p,d,g=R,v=E.expando,m=0,x=0,b=te(),w=te(),T=te(),C=te(),k=function(e,t){return e===t&&(o=!0),0},S="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",j="(?:\\\\[\\da-fA-F]{1,6}"+L+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",O="\\["+L+"*("+j+")(?:"+L+"*([*^$|!~]?=)"+L+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+j+"))|)"+L+"*\\]",P=":("+j+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+O+")*)|.*)\\)|)",I=new RegExp(L+"+","g"),W=new RegExp("^"+L+"*,"+L+"*"),F=new RegExp("^"+L+"*([>+~]|"+L+")"+L+"*"),$=new RegExp(L+"|>"),B=new RegExp(P),_=new RegExp("^"+j+"$"),z={ID:new RegExp("^#("+j+")"),CLASS:new RegExp("^\\.("+j+")"),TAG:new RegExp("^("+j+"|[*])"),ATTR:new RegExp("^"+O),PSEUDO:new RegExp("^"+P),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+L+"*(even|odd|(([+-]|)(\\d*)n|)"+L+"*(?:([+-]|)"+L+"*(\\d+)|))"+L+"*\\)|)","i"),bool:new RegExp("^(?:"+S+")$","i"),needsContext:new RegExp("^"+L+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+L+"*((?:-\\d)?\\d*)"+L+"*\\)|)(?=[^-]|$)","i")},X=/^(?:input|select|textarea|button)$/i,U=/^h\d$/i,V=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,G=/[+~]/,Y=new RegExp("\\\\[\\da-fA-F]{1,6}"+L+"?|\\\\([^\\r\\n\\f])","g"),J=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},Q=function(){le()},K=de((function(e){return!0===e.disabled&&A(e,"fieldset")}),{dir:"parentNode",next:"legend"});function Z(){try{return s.activeElement}catch(e){}}try{g.apply(a=u.call(M.childNodes),M.childNodes),a[M.childNodes.length].nodeType}catch(Te){g={apply:function(e,t){R.apply(e,u.call(t))},call:function(e){R.apply(e,u.call(arguments,1))}}}function ee(e,t,n,r){var i,o,a,u,l,f,d,h=t&&t.ownerDocument,m=t?t.nodeType:9;if(n=n||[],"string"!==typeof e||!e||1!==m&&9!==m&&11!==m)return n;if(!r&&(le(t),t=t||s,c)){if(11!==m&&(l=V.exec(e)))if(i=l[1]){if(9===m){if(!(a=t.getElementById(i)))return n;if(a.id===i)return g.call(n,a),n}else if(h&&(a=h.getElementById(i))&&ee.contains(t,a)&&a.id===i)return g.call(n,a),n}else{if(l[2])return g.apply(n,t.getElementsByTagName(e)),n;if((i=l[3])&&t.getElementsByClassName)return g.apply(n,t.getElementsByClassName(i)),n}if(!C[e+" "]&&(!p||!p.test(e))){if(d=e,h=t,1===m&&($.test(e)||F.test(e))){h=G.test(e)&&ue(t.parentNode)||t,h==t&&y.scope||((u=t.getAttribute("id"))?u=E.escapeSelector(u):t.setAttribute("id",u=v)),f=fe(e),o=f.length;while(o--)f[o]=(u?"#"+u:":scope")+" "+pe(f[o]);d=f.join(",")}try{return g.apply(n,h.querySelectorAll(d)),n}catch(x){C(e,!0)}finally{u===v&&t.removeAttribute("id")}}}return we(e.replace(H,"$1"),t,n,r)}function te(){var e=[];function n(r,i){return e.push(r+" ")>t.cacheLength&&delete n[e.shift()],n[r+" "]=i}return n}function ne(e){return e[v]=!0,e}function re(e){var t=s.createElement("fieldset");try{return!!e(t)}catch(Te){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function ie(e){return function(t){return A(t,"input")&&t.type===e}}function oe(e){return function(t){return(A(t,"input")||A(t,"button"))&&t.type===e}}function ae(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&K(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function se(e){return ne((function(t){return t=+t,ne((function(n,r){var i,o=e([],n.length,t),a=o.length;while(a--)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))}))}))}function ue(e){return e&&"undefined"!==typeof e.getElementsByTagName&&e}function le(e){var n,r=e?e.ownerDocument||e:M;return r!=s&&9===r.nodeType&&r.documentElement?(s=r,l=s.documentElement,c=!E.isXMLDoc(s),d=l.matches||l.webkitMatchesSelector||l.msMatchesSelector,l.msMatchesSelector&&M!=s&&(n=s.defaultView)&&n.top!==n&&n.addEventListener("unload",Q),y.getById=re((function(e){return l.appendChild(e).id=E.expando,!s.getElementsByName||!s.getElementsByName(E.expando).length})),y.disconnectedMatch=re((function(e){return d.call(e,"*")})),y.scope=re((function(){return s.querySelectorAll(":scope")})),y.cssHas=re((function(){try{return s.querySelector(":has(*,:jqfake)"),!1}catch(Te){return!0}})),y.getById?(t.filter.ID=function(e){var t=e.replace(Y,J);return function(e){return e.getAttribute("id")===t}},t.find.ID=function(e,t){if("undefined"!==typeof t.getElementById&&c){var n=t.getElementById(e);return n?[n]:[]}}):(t.filter.ID=function(e){var t=e.replace(Y,J);return function(e){var n="undefined"!==typeof e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},t.find.ID=function(e,t){if("undefined"!==typeof t.getElementById&&c){var n,r,i,o=t.getElementById(e);if(o){if(n=o.getAttributeNode("id"),n&&n.value===e)return[o];i=t.getElementsByName(e),r=0;while(o=i[r++])if(n=o.getAttributeNode("id"),n&&n.value===e)return[o]}return[]}}),t.find.TAG=function(e,t){return"undefined"!==typeof t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},t.find.CLASS=function(e,t){if("undefined"!==typeof t.getElementsByClassName&&c)return t.getElementsByClassName(e)},p=[],re((function(e){var t;l.appendChild(e).innerHTML="<a id='"+v+"' href='' disabled='disabled'></a><select id='"+v+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||p.push("\\["+L+"*(?:value|"+S+")"),e.querySelectorAll("[id~="+v+"-]").length||p.push("~="),e.querySelectorAll("a#"+v+"+*").length||p.push(".#.+[+~]"),e.querySelectorAll(":checked").length||p.push(":checked"),t=s.createElement("input"),t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),l.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&p.push(":enabled",":disabled"),t=s.createElement("input"),t.setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||p.push("\\["+L+"*name"+L+"*="+L+"*(?:''|\"\")")})),y.cssHas||p.push(":has"),p=p.length&&new RegExp(p.join("|")),k=function(e,t){if(e===t)return o=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&n||!y.sortDetached&&t.compareDocumentPosition(e)===n?e===s||e.ownerDocument==M&&ee.contains(M,e)?-1:t===s||t.ownerDocument==M&&ee.contains(M,t)?1:i?f.call(i,e)-f.call(i,t):0:4&n?-1:1)},s):s}for(e in ee.matches=function(e,t){return ee(e,null,null,t)},ee.matchesSelector=function(e,t){if(le(e),c&&!C[t+" "]&&(!p||!p.test(t)))try{var n=d.call(e,t);if(n||y.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(Te){C(t,!0)}return ee(t,s,null,[e]).length>0},ee.contains=function(e,t){return(e.ownerDocument||e)!=s&&le(e),E.contains(e,t)},ee.attr=function(e,n){(e.ownerDocument||e)!=s&&le(e);var r=t.attrHandle[n.toLowerCase()],i=r&&h.call(t.attrHandle,n.toLowerCase())?r(e,n,!c):void 0;return void 0!==i?i:e.getAttribute(n)},ee.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},E.uniqueSort=function(e){var t,n=[],r=0,a=0;if(o=!y.sortStable,i=!y.sortStable&&u.call(e,0),N.call(e,k),o){while(t=e[a++])t===e[a]&&(r=n.push(a));while(r--)q.call(e,n[r],1)}return i=null,e},E.fn.uniqueSort=function(){return this.pushStack(E.uniqueSort(u.apply(this)))},t=E.expr={cacheLength:50,createPseudo:ne,match:z,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(Y,J),e[3]=(e[3]||e[4]||e[5]||"").replace(Y,J),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ee.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ee.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return z.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&B.test(n)&&(t=fe(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(Y,J).toLowerCase();return"*"===e?function(){return!0}:function(e){return A(e,t)}},CLASS:function(e){var t=b[e+" "];return t||(t=new RegExp("(^|"+L+")"+e+"("+L+"|$)"))&&b(e,(function(e){return t.test("string"===typeof e.className&&e.className||"undefined"!==typeof e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(r){var i=ee.attr(r,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace(I," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,u){var l,c,f,p,d,h=o!==a?"nextSibling":"previousSibling",g=t.parentNode,y=s&&t.nodeName.toLowerCase(),x=!u&&!s,b=!1;if(g){if(o){while(h){f=t;while(f=f[h])if(s?A(f,y):1===f.nodeType)return!1;d=h="only"===e&&!d&&"nextSibling"}return!0}if(d=[a?g.firstChild:g.lastChild],a&&x){c=g[v]||(g[v]={}),l=c[e]||[],p=l[0]===m&&l[1],b=p&&l[2],f=p&&g.childNodes[p];while(f=++p&&f&&f[h]||(b=p=0)||d.pop())if(1===f.nodeType&&++b&&f===t){c[e]=[m,p,b];break}}else if(x&&(c=t[v]||(t[v]={}),l=c[e]||[],p=l[0]===m&&l[1],b=p),!1===b)while(f=++p&&f&&f[h]||(b=p=0)||d.pop())if((s?A(f,y):1===f.nodeType)&&++b&&(x&&(c=f[v]||(f[v]={}),c[e]=[m,b]),f===t))break;return b-=i,b===r||b%r===0&&b/r>=0}}},PSEUDO:function(e,n){var r,i=t.pseudos[e]||t.setFilters[e.toLowerCase()]||ee.error("unsupported pseudo: "+e);return i[v]?i(n):i.length>1?(r=[e,e,"",n],t.setFilters.hasOwnProperty(e.toLowerCase())?ne((function(e,t){var r,o=i(e,n),a=o.length;while(a--)r=f.call(e,o[a]),e[r]=!(t[r]=o[a])})):function(e){return i(e,0,r)}):i}},pseudos:{not:ne((function(e){var t=[],n=[],r=be(e.replace(H,"$1"));return r[v]?ne((function(e,t,n,i){var o,a=r(e,null,i,[]),s=e.length;while(s--)(o=a[s])&&(e[s]=!(t[s]=o))})):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}})),has:ne((function(e){return function(t){return ee(e,t).length>0}})),contains:ne((function(e){return e=e.replace(Y,J),function(t){return(t.textContent||E.text(t)).indexOf(e)>-1}})),lang:ne((function(e){return _.test(e||"")||ee.error("unsupported lang: "+e),e=e.replace(Y,J).toLowerCase(),function(t){var n;do{if(n=c?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===l},focus:function(e){return e===Z()&&s.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:ae(!1),disabled:ae(!0),checked:function(e){return A(e,"input")&&!!e.checked||A(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!t.pseudos.empty(e)},header:function(e){return U.test(e.nodeName)},input:function(e){return X.test(e.nodeName)},button:function(e){return A(e,"input")&&"button"===e.type||A(e,"button")},text:function(e){var t;return A(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:se((function(){return[0]})),last:se((function(e,t){return[t-1]})),eq:se((function(e,t,n){return[n<0?n+t:n]})),even:se((function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e})),odd:se((function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e})),lt:se((function(e,t,n){var r;for(r=n<0?n+t:n>t?t:n;--r>=0;)e.push(r);return e})),gt:se((function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e}))}},t.pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[e]=ie(e);for(e in{submit:!0,reset:!0})t.pseudos[e]=oe(e);function ce(){}function fe(e,n){var r,i,o,a,s,u,l,c=w[e+" "];if(c)return n?0:c.slice(0);s=e,u=[],l=t.preFilter;while(s){for(a in r&&!(i=W.exec(s))||(i&&(s=s.slice(i[0].length)||s),u.push(o=[])),r=!1,(i=F.exec(s))&&(r=i.shift(),o.push({value:r,type:i[0].replace(H," ")}),s=s.slice(r.length)),t.filter)!(i=z[a].exec(s))||l[a]&&!(i=l[a](i))||(r=i.shift(),o.push({value:r,type:a,matches:i}),s=s.slice(r.length));if(!r)break}return n?s.length:s?ee.error(e):w(e,u).slice(0)}function pe(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function de(e,t,n){var r=t.dir,i=t.next,o=i||r,a=n&&"parentNode"===o,s=x++;return t.first?function(t,n,i){while(t=t[r])if(1===t.nodeType||a)return e(t,n,i);return!1}:function(t,n,u){var l,c,f=[m,s];if(u){while(t=t[r])if((1===t.nodeType||a)&&e(t,n,u))return!0}else while(t=t[r])if(1===t.nodeType||a)if(c=t[v]||(t[v]={}),i&&A(t,i))t=t[r]||t;else{if((l=c[o])&&l[0]===m&&l[1]===s)return f[2]=l[2];if(c[o]=f,f[2]=e(t,n,u))return!0}return!1}}function he(e){return e.length>1?function(t,n,r){var i=e.length;while(i--)if(!e[i](t,n,r))return!1;return!0}:e[0]}function ge(e,t,n){for(var r=0,i=t.length;r<i;r++)ee(e,t[r],n);return n}function ve(e,t,n,r,i){for(var o,a=[],s=0,u=e.length,l=null!=t;s<u;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),l&&t.push(s)));return a}function ye(e,t,n,r,i,o){return r&&!r[v]&&(r=ye(r)),i&&!i[v]&&(i=ye(i,o)),ne((function(o,a,s,u){var l,c,p,d,h=[],v=[],y=a.length,m=o||ge(t||"*",s.nodeType?[s]:s,[]),x=!e||!o&&t?m:ve(m,h,e,s,u);if(n?(d=i||(o?e:y||r)?[]:a,n(x,d,s,u)):d=x,r){l=ve(d,v),r(l,[],s,u),c=l.length;while(c--)(p=l[c])&&(d[v[c]]=!(x[v[c]]=p))}if(o){if(i||e){if(i){l=[],c=d.length;while(c--)(p=d[c])&&l.push(x[c]=p);i(null,d=[],l,u)}c=d.length;while(c--)(p=d[c])&&(l=i?f.call(o,p):h[c])>-1&&(o[l]=!(a[l]=p))}}else d=ve(d===a?d.splice(y,d.length):d),i?i(null,a,d,u):g.apply(a,d)}))}function me(e){for(var n,i,o,a=e.length,s=t.relative[e[0].type],u=s||t.relative[" "],l=s?1:0,c=de((function(e){return e===n}),u,!0),p=de((function(e){return f.call(n,e)>-1}),u,!0),d=[function(e,t,i){var o=!s&&(i||t!=r)||((n=t).nodeType?c(e,t,i):p(e,t,i));return n=null,o}];l<a;l++)if(i=t.relative[e[l].type])d=[de(he(d),i)];else{if(i=t.filter[e[l].type].apply(null,e[l].matches),i[v]){for(o=++l;o<a;o++)if(t.relative[e[o].type])break;return ye(l>1&&he(d),l>1&&pe(e.slice(0,l-1).concat({value:" "===e[l-2].type?"*":""})).replace(H,"$1"),i,l<o&&me(e.slice(l,o)),o<a&&me(e=e.slice(o)),o<a&&pe(e))}d.push(i)}return he(d)}function xe(e,n){var i=n.length>0,o=e.length>0,a=function(a,u,l,f,p){var d,h,v,y=0,x="0",b=a&&[],w=[],T=r,C=a||o&&t.find.TAG("*",p),k=m+=null==T?1:Math.random()||.1,S=C.length;for(p&&(r=u==s||u||p);x!==S&&null!=(d=C[x]);x++){if(o&&d){h=0,u||d.ownerDocument==s||(le(d),l=!c);while(v=e[h++])if(v(d,u||s,l)){g.call(f,d);break}p&&(m=k)}i&&((d=!v&&d)&&y--,a&&b.push(d))}if(y+=x,i&&x!==y){h=0;while(v=n[h++])v(b,w,u,l);if(a){if(y>0)while(x--)b[x]||w[x]||(w[x]=D.call(f));w=ve(w)}g.apply(f,w),p&&!a&&w.length>0&&y+n.length>1&&E.uniqueSort(f)}return p&&(m=k,r=T),b};return i?ne(a):a}function be(e,t){var n,r=[],i=[],o=T[e+" "];if(!o){t||(t=fe(e)),n=t.length;while(n--)o=me(t[n]),o[v]?r.push(o):i.push(o);o=T(e,xe(i,r)),o.selector=e}return o}function we(e,n,r,i){var o,a,s,u,l,f="function"===typeof e&&e,p=!i&&fe(e=f.selector||e);if(r=r||[],1===p.length){if(a=p[0]=p[0].slice(0),a.length>2&&"ID"===(s=a[0]).type&&9===n.nodeType&&c&&t.relative[a[1].type]){if(n=(t.find.ID(s.matches[0].replace(Y,J),n)||[])[0],!n)return r;f&&(n=n.parentNode),e=e.slice(a.shift().value.length)}o=z.needsContext.test(e)?0:a.length;while(o--){if(s=a[o],t.relative[u=s.type])break;if((l=t.find[u])&&(i=l(s.matches[0].replace(Y,J),G.test(a[0].type)&&ue(n.parentNode)||n))){if(a.splice(o,1),e=i.length&&pe(a),!e)return g.apply(r,i),r;break}}}return(f||be(e,p))(i,n,!c,r,!n||G.test(e)&&ue(n.parentNode)||n),r}ce.prototype=t.filters=t.pseudos,t.setFilters=new ce,y.sortStable=v.split("").sort(k).join("")===v,le(),y.sortDetached=re((function(e){return 1&e.compareDocumentPosition(s.createElement("fieldset"))})),E.find=ee,E.expr[":"]=E.expr.pseudos,E.unique=E.uniqueSort,ee.compile=be,ee.select=we,ee.setDocument=le,ee.tokenize=fe,ee.escape=E.escapeSelector,ee.getText=E.text,ee.isXML=E.isXMLDoc,ee.selectors=E.expr,ee.support=E.support,ee.uniqueSort=E.uniqueSort})();var I=function(e,t,n){var r=[],i=void 0!==n;while((e=e[t])&&9!==e.nodeType)if(1===e.nodeType){if(i&&E(e).is(n))break;r.push(e)}return r},W=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},F=E.expr.match.needsContext,$=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function B(e,t,n){return m(t)?E.grep(e,(function(e,r){return!!t.call(e,r,e)!==n})):t.nodeType?E.grep(e,(function(e){return e===t!==n})):"string"!==typeof t?E.grep(e,(function(e){return f.call(t,e)>-1!==n})):E.filter(t,e,n)}E.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?E.find.matchesSelector(r,e)?[r]:[]:E.find.matches(e,E.grep(t,(function(e){return 1===e.nodeType})))},E.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!==typeof e)return this.pushStack(E(e).filter((function(){for(t=0;t<r;t++)if(E.contains(i[t],this))return!0})));for(n=this.pushStack([]),t=0;t<r;t++)E.find(e,i[t],n);return r>1?E.uniqueSort(n):n},filter:function(e){return this.pushStack(B(this,e||[],!1))},not:function(e){return this.pushStack(B(this,e||[],!0))},is:function(e){return!!B(this,"string"===typeof e&&F.test(e)?E(e):e||[],!1).length}});var _,z=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,X=E.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||_,"string"===typeof e){if(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:z.exec(e),!r||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof E?t[0]:t,E.merge(this,E.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:b,!0)),$.test(r[1])&&E.isPlainObject(t))for(r in t)m(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return i=b.getElementById(r[2]),i&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):m(e)?void 0!==n.ready?n.ready(e):e(E):E.makeArray(e,this)};X.prototype=E.fn,_=E(b);var U=/^(?:parents|prev(?:Until|All))/,V={children:!0,contents:!0,next:!0,prev:!0};function G(e,t){while((e=e[t])&&1!==e.nodeType);return e}E.fn.extend({has:function(e){var t=E(e,this),n=t.length;return this.filter((function(){for(var e=0;e<n;e++)if(E.contains(this,t[e]))return!0}))},closest:function(e,t){var n,r=0,i=this.length,o=[],a="string"!==typeof e&&E(e);if(!F.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&E.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?E.uniqueSort(o):o)},index:function(e){return e?"string"===typeof e?f.call(E(e),this[0]):f.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(E.uniqueSort(E.merge(this.get(),E(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),E.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return I(e,"parentNode")},parentsUntil:function(e,t,n){return I(e,"parentNode",n)},next:function(e){return G(e,"nextSibling")},prev:function(e){return G(e,"previousSibling")},nextAll:function(e){return I(e,"nextSibling")},prevAll:function(e){return I(e,"previousSibling")},nextUntil:function(e,t,n){return I(e,"nextSibling",n)},prevUntil:function(e,t,n){return I(e,"previousSibling",n)},siblings:function(e){return W((e.parentNode||{}).firstChild,e)},children:function(e){return W(e.firstChild)},contents:function(e){return null!=e.contentDocument&&s(e.contentDocument)?e.contentDocument:(A(e,"template")&&(e=e.content||e),E.merge([],e.childNodes))}},(function(e,t){E.fn[e]=function(n,r){var i=E.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"===typeof r&&(i=E.filter(r,i)),this.length>1&&(V[e]||E.uniqueSort(i),U.test(e)&&i.reverse()),this.pushStack(i)}}));var Y=/[^\x20\t\r\n\f]+/g;function J(e){var t={};return E.each(e.match(Y)||[],(function(e,n){t[n]=!0})),t}function Q(e){return e}function K(e){throw e}function Z(e,t,n,r){var i;try{e&&m(i=e.promise)?i.call(e).done(t).fail(n):e&&m(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}E.Callbacks=function(e){e="string"===typeof e?J(e):E.extend({},e);var t,n,r,i,o=[],a=[],s=-1,u=function(){for(i=i||e.once,r=t=!0;a.length;s=-1){n=a.shift();while(++s<o.length)!1===o[s].apply(n[0],n[1])&&e.stopOnFalse&&(s=o.length,n=!1)}e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},l={add:function(){return o&&(n&&!t&&(s=o.length-1,a.push(n)),function t(n){E.each(n,(function(n,r){m(r)?e.unique&&l.has(r)||o.push(r):r&&r.length&&"string"!==C(r)&&t(r)}))}(arguments),n&&!t&&u()),this},remove:function(){return E.each(arguments,(function(e,t){var n;while((n=E.inArray(t,o,n))>-1)o.splice(n,1),n<=s&&s--})),this},has:function(e){return e?E.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=a=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=a=[],n||t||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=n||[],n=[e,n.slice?n.slice():n],a.push(n),t||u()),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!r}};return l},E.extend({Deferred:function(e){var t=[["notify","progress",E.Callbacks("memory"),E.Callbacks("memory"),2],["resolve","done",E.Callbacks("once memory"),E.Callbacks("once memory"),0,"resolved"],["reject","fail",E.Callbacks("once memory"),E.Callbacks("once memory"),1,"rejected"]],r="pending",i={state:function(){return r},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return i.then(null,e)},pipe:function(){var e=arguments;return E.Deferred((function(n){E.each(t,(function(t,r){var i=m(e[r[4]])&&e[r[4]];o[r[1]]((function(){var e=i&&i.apply(this,arguments);e&&m(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[r[0]+"With"](this,i?[e]:arguments)}))})),e=null})).promise()},then:function(e,r,i){var o=0;function a(e,t,r,i){return function(){var s=this,u=arguments,l=function(){var n,l;if(!(e<o)){if(n=r.apply(s,u),n===t.promise())throw new TypeError("Thenable self-resolution");l=n&&("object"===typeof n||"function"===typeof n)&&n.then,m(l)?i?l.call(n,a(o,t,Q,i),a(o,t,K,i)):(o++,l.call(n,a(o,t,Q,i),a(o,t,K,i),a(o,t,Q,t.notifyWith))):(r!==Q&&(s=void 0,u=[n]),(i||t.resolveWith)(s,u))}},c=i?l:function(){try{l()}catch(n){E.Deferred.exceptionHook&&E.Deferred.exceptionHook(n,c.error),e+1>=o&&(r!==K&&(s=void 0,u=[n]),t.rejectWith(s,u))}};e?c():(E.Deferred.getErrorHook?c.error=E.Deferred.getErrorHook():E.Deferred.getStackHook&&(c.error=E.Deferred.getStackHook()),n.setTimeout(c))}}return E.Deferred((function(n){t[0][3].add(a(0,n,m(i)?i:Q,n.notifyWith)),t[1][3].add(a(0,n,m(e)?e:Q)),t[2][3].add(a(0,n,m(r)?r:K))})).promise()},promise:function(e){return null!=e?E.extend(e,i):i}},o={};return E.each(t,(function(e,n){var a=n[2],s=n[5];i[n[1]]=a.add,s&&a.add((function(){r=s}),t[3-e][2].disable,t[3-e][3].disable,t[0][2].lock,t[0][3].lock),a.add(n[3].fire),o[n[0]]=function(){return o[n[0]+"With"](this===o?void 0:this,arguments),this},o[n[0]+"With"]=a.fireWith})),i.promise(o),e&&e.call(o,o),o},when:function(e){var t=arguments.length,n=t,r=Array(n),i=u.call(arguments),o=E.Deferred(),a=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?u.call(arguments):n,--t||o.resolveWith(r,i)}};if(t<=1&&(Z(e,o.done(a(n)).resolve,o.reject,!t),"pending"===o.state()||m(i[n]&&i[n].then)))return o.then();while(n--)Z(i[n],a(n),o.reject);return o.promise()}});var ee=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;E.Deferred.exceptionHook=function(e,t){n.console&&n.console.warn&&e&&ee.test(e.name)&&n.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},E.readyException=function(e){n.setTimeout((function(){throw e}))};var te=E.Deferred();function ne(){b.removeEventListener("DOMContentLoaded",ne),n.removeEventListener("load",ne),E.ready()}E.fn.ready=function(e){return te.then(e).catch((function(e){E.readyException(e)})),this},E.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--E.readyWait:E.isReady)||(E.isReady=!0,!0!==e&&--E.readyWait>0||te.resolveWith(b,[E]))}}),E.ready.then=te.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?n.setTimeout(E.ready):(b.addEventListener("DOMContentLoaded",ne),n.addEventListener("load",ne));var re=function(e,t,n,r,i,o,a){var s=0,u=e.length,l=null==n;if("object"===C(n))for(s in i=!0,n)re(e,t,s,n[s],!0,o,a);else if(void 0!==r&&(i=!0,m(r)||(a=!0),l&&(a?(t.call(e,r),t=null):(l=t,t=function(e,t,n){return l.call(E(e),n)})),t))for(;s<u;s++)t(e[s],n,a?r:r.call(e[s],s,t(e[s],n)));return i?e:l?t.call(e):u?t(e[0],n):o},ie=/^-ms-/,oe=/-([a-z])/g;function ae(e,t){return t.toUpperCase()}function se(e){return e.replace(ie,"ms-").replace(oe,ae)}var ue=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function le(){this.expando=E.expando+le.uid++}le.uid=1,le.prototype={cache:function(e){var t=e[this.expando];return t||(t={},ue(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"===typeof t)i[se(t)]=n;else for(r in t)i[se(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][se(t)]},access:function(e,t,n){return void 0===t||t&&"string"===typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){Array.isArray(t)?t=t.map(se):(t=se(t),t=t in r?[t]:t.match(Y)||[]),n=t.length;while(n--)delete r[t[n]]}(void 0===t||E.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!E.isEmptyObject(t)}};var ce=new le,fe=new le,pe=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,de=/[A-Z]/g;function he(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:pe.test(e)?JSON.parse(e):e)}function ge(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(de,"-$&").toLowerCase(),n=e.getAttribute(r),"string"===typeof n){try{n=he(n)}catch(i){}fe.set(e,t,n)}else n=void 0;return n}E.extend({hasData:function(e){return fe.hasData(e)||ce.hasData(e)},data:function(e,t,n){return fe.access(e,t,n)},removeData:function(e,t){fe.remove(e,t)},_data:function(e,t,n){return ce.access(e,t,n)},_removeData:function(e,t){ce.remove(e,t)}}),E.fn.extend({data:function(e,t){var n,r,i,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(i=fe.get(o),1===o.nodeType&&!ce.get(o,"hasDataAttrs"))){n=a.length;while(n--)a[n]&&(r=a[n].name,0===r.indexOf("data-")&&(r=se(r.slice(5)),ge(o,r,i[r])));ce.set(o,"hasDataAttrs",!0)}return i}return"object"===typeof e?this.each((function(){fe.set(this,e)})):re(this,(function(t){var n;if(o&&void 0===t)return n=fe.get(o,e),void 0!==n?n:(n=ge(o,e),void 0!==n?n:void 0);this.each((function(){fe.set(this,e,t)}))}),null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each((function(){fe.remove(this,e)}))}}),E.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=ce.get(e,t),n&&(!r||Array.isArray(n)?r=ce.access(e,t,E.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=E.queue(e,t),r=n.length,i=n.shift(),o=E._queueHooks(e,t),a=function(){E.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,a,o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return ce.get(e,n)||ce.access(e,n,{empty:E.Callbacks("once memory").add((function(){ce.remove(e,[t+"queue",n])}))})}}),E.fn.extend({queue:function(e,t){var n=2;return"string"!==typeof e&&(t=e,e="fx",n--),arguments.length<n?E.queue(this[0],e):void 0===t?this:this.each((function(){var n=E.queue(this,e,t);E._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&E.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){E.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=E.Deferred(),o=this,a=this.length,s=function(){--r||i.resolveWith(o,[o])};"string"!==typeof e&&(t=e,e=void 0),e=e||"fx";while(a--)n=ce.get(o[a],e+"queueHooks"),n&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(t)}});var ve=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,ye=new RegExp("^(?:([+-])=|)("+ve+")([a-z%]*)$","i"),me=["Top","Right","Bottom","Left"],xe=b.documentElement,be=function(e){return E.contains(e.ownerDocument,e)},we={composed:!0};xe.getRootNode&&(be=function(e){return E.contains(e.ownerDocument,e)||e.getRootNode(we)===e.ownerDocument});var Te=function(e,t){return e=t||e,"none"===e.style.display||""===e.style.display&&be(e)&&"none"===E.css(e,"display")};function Ce(e,t,n,r){var i,o,a=20,s=r?function(){return r.cur()}:function(){return E.css(e,t,"")},u=s(),l=n&&n[3]||(E.cssNumber[t]?"":"px"),c=e.nodeType&&(E.cssNumber[t]||"px"!==l&&+u)&&ye.exec(E.css(e,t));if(c&&c[3]!==l){u/=2,l=l||c[3],c=+u||1;while(a--)E.style(e,t,c+l),(1-o)*(1-(o=s()/u||.5))<=0&&(a=0),c/=o;c*=2,E.style(e,t,c+l),n=n||[]}return n&&(c=+c||+u||0,i=n[1]?c+(n[1]+1)*n[2]:+n[2],r&&(r.unit=l,r.start=c,r.end=i)),i}var ke={};function Se(e){var t,n=e.ownerDocument,r=e.nodeName,i=ke[r];return i||(t=n.body.appendChild(n.createElement(r)),i=E.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),ke[r]=i,i)}function Ee(e,t){for(var n,r,i=[],o=0,a=e.length;o<a;o++)r=e[o],r.style&&(n=r.style.display,t?("none"===n&&(i[o]=ce.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&Te(r)&&(i[o]=Se(r))):"none"!==n&&(i[o]="none",ce.set(r,"display",n)));for(o=0;o<a;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}E.fn.extend({show:function(){return Ee(this,!0)},hide:function(){return Ee(this)},toggle:function(e){return"boolean"===typeof e?e?this.show():this.hide():this.each((function(){Te(this)?E(this).show():E(this).hide()}))}});var je=/^(?:checkbox|radio)$/i,Ae=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,De=/^$|^module$|\/(?:java|ecma)script/i;(function(){var e=b.createDocumentFragment(),t=e.appendChild(b.createElement("div")),n=b.createElement("input");n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),t.appendChild(n),y.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,t.innerHTML="<option></option>",y.option=!!t.lastChild})();var Ne={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function qe(e,t){var n;return n="undefined"!==typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!==typeof e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&A(e,t)?E.merge([e],n):n}function Le(e,t){for(var n=0,r=e.length;n<r;n++)ce.set(e[n],"globalEval",!t||ce.get(t[n],"globalEval"))}Ne.tbody=Ne.tfoot=Ne.colgroup=Ne.caption=Ne.thead,Ne.th=Ne.td,y.option||(Ne.optgroup=Ne.option=[1,"<select multiple='multiple'>","</select>"]);var He=/<|&#?\w+;/;function Oe(e,t,n,r,i){for(var o,a,s,u,l,c,f=t.createDocumentFragment(),p=[],d=0,h=e.length;d<h;d++)if(o=e[d],o||0===o)if("object"===C(o))E.merge(p,o.nodeType?[o]:o);else if(He.test(o)){a=a||f.appendChild(t.createElement("div")),s=(Ae.exec(o)||["",""])[1].toLowerCase(),u=Ne[s]||Ne._default,a.innerHTML=u[1]+E.htmlPrefilter(o)+u[2],c=u[0];while(c--)a=a.lastChild;E.merge(p,a.childNodes),a=f.firstChild,a.textContent=""}else p.push(t.createTextNode(o));f.textContent="",d=0;while(o=p[d++])if(r&&E.inArray(o,r)>-1)i&&i.push(o);else if(l=be(o),a=qe(f.appendChild(o),"script"),l&&Le(a),n){c=0;while(o=a[c++])De.test(o.type||"")&&n.push(o)}return f}var Pe=/^([^.]*)(?:\.(.+)|)/;function Me(){return!0}function Re(){return!1}function Ie(e,t,n,r,i,o){var a,s;if("object"===typeof t){for(s in"string"!==typeof n&&(r=r||n,n=void 0),t)Ie(e,s,n,r,t[s],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"===typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Re;else if(!i)return e;return 1===o&&(a=i,i=function(e){return E().off(e),a.apply(this,arguments)},i.guid=a.guid||(a.guid=E.guid++)),e.each((function(){E.event.add(this,t,i,r,n)}))}function We(e,t,n){n?(ce.set(e,t,!1),E.event.add(e,t,{namespace:!1,handler:function(e){var n,r=ce.get(this,t);if(1&e.isTrigger&&this[t]){if(r)(E.event.special[t]||{}).delegateType&&e.stopPropagation();else if(r=u.call(arguments),ce.set(this,t,r),this[t](),n=ce.get(this,t),ce.set(this,t,!1),r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n}else r&&(ce.set(this,t,E.event.trigger(r[0],r.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=Me)}})):void 0===ce.get(e,t)&&E.event.add(e,t,Me)}E.event={global:{},add:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=ce.get(e);if(ue(e)){n.handler&&(o=n,n=o.handler,i=o.selector),i&&E.find.matchesSelector(xe,i),n.guid||(n.guid=E.guid++),(u=v.events)||(u=v.events=Object.create(null)),(a=v.handle)||(a=v.handle=function(t){return"undefined"!==typeof E&&E.event.triggered!==t.type?E.event.dispatch.apply(e,arguments):void 0}),t=(t||"").match(Y)||[""],l=t.length;while(l--)s=Pe.exec(t[l])||[],d=g=s[1],h=(s[2]||"").split(".").sort(),d&&(f=E.event.special[d]||{},d=(i?f.delegateType:f.bindType)||d,f=E.event.special[d]||{},c=E.extend({type:d,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&E.expr.match.needsContext.test(i),namespace:h.join(".")},o),(p=u[d])||(p=u[d]=[],p.delegateCount=0,f.setup&&!1!==f.setup.call(e,r,h,a)||e.addEventListener&&e.addEventListener(d,a)),f.add&&(f.add.call(e,c),c.handler.guid||(c.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,c):p.push(c),E.event.global[d]=!0)}},remove:function(e,t,n,r,i){var o,a,s,u,l,c,f,p,d,h,g,v=ce.hasData(e)&&ce.get(e);if(v&&(u=v.events)){t=(t||"").match(Y)||[""],l=t.length;while(l--)if(s=Pe.exec(t[l])||[],d=g=s[1],h=(s[2]||"").split(".").sort(),d){f=E.event.special[d]||{},d=(r?f.delegateType:f.bindType)||d,p=u[d]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=p.length;while(o--)c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||s&&!s.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));a&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,v.handle)||E.removeEvent(e,d,v.handle),delete u[d])}else for(d in u)E.event.remove(e,d+t[l],n,r,!0);E.isEmptyObject(u)&&ce.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,a,s=new Array(arguments.length),u=E.event.fix(e),l=(ce.get(this,"events")||Object.create(null))[u.type]||[],c=E.event.special[u.type]||{};for(s[0]=u,t=1;t<arguments.length;t++)s[t]=arguments[t];if(u.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,u)){a=E.event.handlers.call(this,u,l),t=0;while((i=a[t++])&&!u.isPropagationStopped()){u.currentTarget=i.elem,n=0;while((o=i.handlers[n++])&&!u.isImmediatePropagationStopped())u.rnamespace&&!1!==o.namespace&&!u.rnamespace.test(o.namespace)||(u.handleObj=o,u.data=o.data,r=((E.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,s),void 0!==r&&!1===(u.result=r)&&(u.preventDefault(),u.stopPropagation()))}return c.postDispatch&&c.postDispatch.call(this,u),u.result}},handlers:function(e,t){var n,r,i,o,a,s=[],u=t.delegateCount,l=e.target;if(u&&l.nodeType&&!("click"===e.type&&e.button>=1))for(;l!==this;l=l.parentNode||this)if(1===l.nodeType&&("click"!==e.type||!0!==l.disabled)){for(o=[],a={},n=0;n<u;n++)r=t[n],i=r.selector+" ",void 0===a[i]&&(a[i]=r.needsContext?E(i,this).index(l)>-1:E.find(i,this,null,[l]).length),a[i]&&o.push(r);o.length&&s.push({elem:l,handlers:o})}return l=this,u<t.length&&s.push({elem:l,handlers:t.slice(u)}),s},addProp:function(e,t){Object.defineProperty(E.Event.prototype,e,{enumerable:!0,configurable:!0,get:m(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[E.expando]?e:new E.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return je.test(t.type)&&t.click&&A(t,"input")&&We(t,"click",!0),!1},trigger:function(e){var t=this||e;return je.test(t.type)&&t.click&&A(t,"input")&&We(t,"click"),!0},_default:function(e){var t=e.target;return je.test(t.type)&&t.click&&A(t,"input")&&ce.get(t,"click")||A(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},E.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},E.Event=function(e,t){if(!(this instanceof E.Event))return new E.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Me:Re,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&E.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[E.expando]=!0},E.Event.prototype={constructor:E.Event,isDefaultPrevented:Re,isPropagationStopped:Re,isImmediatePropagationStopped:Re,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Me,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Me,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Me,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},E.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},E.event.addProp),E.each({focus:"focusin",blur:"focusout"},(function(e,t){function n(e){if(b.documentMode){var n=ce.get(this,"handle"),r=E.event.fix(e);r.type="focusin"===e.type?"focus":"blur",r.isSimulated=!0,n(e),r.target===r.currentTarget&&n(r)}else E.event.simulate(t,e.target,E.event.fix(e))}E.event.special[e]={setup:function(){var r;if(We(this,e,!0),!b.documentMode)return!1;r=ce.get(this,t),r||this.addEventListener(t,n),ce.set(this,t,(r||0)+1)},trigger:function(){return We(this,e),!0},teardown:function(){var e;if(!b.documentMode)return!1;e=ce.get(this,t)-1,e?ce.set(this,t,e):(this.removeEventListener(t,n),ce.remove(this,t))},_default:function(t){return ce.get(t.target,e)},delegateType:t},E.event.special[t]={setup:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=ce.get(i,t);o||(b.documentMode?this.addEventListener(t,n):r.addEventListener(e,n,!0)),ce.set(i,t,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=ce.get(i,t)-1;o?ce.set(i,t,o):(b.documentMode?this.removeEventListener(t,n):r.removeEventListener(e,n,!0),ce.remove(i,t))}}})),E.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){E.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,o=e.handleObj;return i&&(i===r||E.contains(r,i))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}})),E.fn.extend({on:function(e,t,n,r){return Ie(this,e,t,n,r)},one:function(e,t,n,r){return Ie(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,E(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"===typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!==typeof t||(n=t,t=void 0),!1===n&&(n=Re),this.each((function(){E.event.remove(this,e,n,t)}))}});var Fe=/<script|<style|<link/i,$e=/checked\s*(?:[^=]|=\s*.checked.)/i,Be=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function _e(e,t){return A(e,"table")&&A(11!==t.nodeType?t:t.firstChild,"tr")&&E(e).children("tbody")[0]||e}function ze(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Xe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Ue(e,t){var n,r,i,o,a,s,u;if(1===t.nodeType){if(ce.hasData(e)&&(o=ce.get(e),u=o.events,u))for(i in ce.remove(t,"handle events"),u)for(n=0,r=u[i].length;n<r;n++)E.event.add(t,i,u[i][n]);fe.hasData(e)&&(a=fe.access(e),s=E.extend({},a),fe.set(t,s))}}function Ve(e,t){var n=t.nodeName.toLowerCase();"input"===n&&je.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function Ge(e,t,n,r){t=l(t);var i,o,a,s,u,c,f=0,p=e.length,d=p-1,h=t[0],g=m(h);if(g||p>1&&"string"===typeof h&&!y.checkClone&&$e.test(h))return e.each((function(i){var o=e.eq(i);g&&(t[0]=h.call(this,i,o.html())),Ge(o,t,n,r)}));if(p&&(i=Oe(t,e[0].ownerDocument,!1,e,r),o=i.firstChild,1===i.childNodes.length&&(i=o),o||r)){for(a=E.map(qe(i,"script"),ze),s=a.length;f<p;f++)u=i,f!==d&&(u=E.clone(u,!0,!0),s&&E.merge(a,qe(u,"script"))),n.call(e[f],u,f);if(s)for(c=a[a.length-1].ownerDocument,E.map(a,Xe),f=0;f<s;f++)u=a[f],De.test(u.type||"")&&!ce.access(u,"globalEval")&&E.contains(c,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?E._evalUrl&&!u.noModule&&E._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},c):T(u.textContent.replace(Be,""),u,c))}return e}function Ye(e,t,n){for(var r,i=t?E.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||E.cleanData(qe(r)),r.parentNode&&(n&&be(r)&&Le(qe(r,"script")),r.parentNode.removeChild(r));return e}E.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,a,s=e.cloneNode(!0),u=be(e);if(!y.noCloneChecked&&(1===e.nodeType||11===e.nodeType)&&!E.isXMLDoc(e))for(a=qe(s),o=qe(e),r=0,i=o.length;r<i;r++)Ve(o[r],a[r]);if(t)if(n)for(o=o||qe(e),a=a||qe(s),r=0,i=o.length;r<i;r++)Ue(o[r],a[r]);else Ue(e,s);return a=qe(s,"script"),a.length>0&&Le(a,!u&&qe(e,"script")),s},cleanData:function(e){for(var t,n,r,i=E.event.special,o=0;void 0!==(n=e[o]);o++)if(ue(n)){if(t=n[ce.expando]){if(t.events)for(r in t.events)i[r]?E.event.remove(n,r):E.removeEvent(n,r,t.handle);n[ce.expando]=void 0}n[fe.expando]&&(n[fe.expando]=void 0)}}}),E.fn.extend({detach:function(e){return Ye(this,e,!0)},remove:function(e){return Ye(this,e)},text:function(e){return re(this,(function(e){return void 0===e?E.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)}))}),null,e,arguments.length)},append:function(){return Ge(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=_e(this,e);t.appendChild(e)}}))},prepend:function(){return Ge(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=_e(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return Ge(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return Ge(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(E.cleanData(qe(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map((function(){return E.clone(this,e,t)}))},html:function(e){return re(this,(function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"===typeof e&&!Fe.test(e)&&!Ne[(Ae.exec(e)||["",""])[1].toLowerCase()]){e=E.htmlPrefilter(e);try{for(;n<r;n++)t=this[n]||{},1===t.nodeType&&(E.cleanData(qe(t,!1)),t.innerHTML=e);t=0}catch(i){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(){var e=[];return Ge(this,arguments,(function(t){var n=this.parentNode;E.inArray(this,e)<0&&(E.cleanData(qe(this)),n&&n.replaceChild(t,this))}),e)}}),E.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){E.fn[e]=function(e){for(var n,r=[],i=E(e),o=i.length-1,a=0;a<=o;a++)n=a===o?this:this.clone(!0),E(i[a])[t](n),c.apply(r,n.get());return this.pushStack(r)}}));var Je=new RegExp("^("+ve+")(?!px)[a-z%]+$","i"),Qe=/^--/,Ke=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=n),t.getComputedStyle(e)},Ze=function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r},et=new RegExp(me.join("|"),"i");function tt(e,t,n){var r,i,o,a,s=Qe.test(t),u=e.style;return n=n||Ke(e),n&&(a=n.getPropertyValue(t)||n[t],s&&a&&(a=a.replace(H,"$1")||void 0),""!==a||be(e)||(a=E.style(e,t)),!y.pixelBoxStyles()&&Je.test(a)&&et.test(t)&&(r=u.width,i=u.minWidth,o=u.maxWidth,u.minWidth=u.maxWidth=u.width=a,a=n.width,u.width=r,u.minWidth=i,u.maxWidth=o)),void 0!==a?a+"":a}function nt(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}(function(){function e(){if(c){l.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",xe.appendChild(l).appendChild(c);var e=n.getComputedStyle(c);r="1%"!==e.top,u=12===t(e.marginLeft),c.style.right="60%",a=36===t(e.right),i=36===t(e.width),c.style.position="absolute",o=12===t(c.offsetWidth/3),xe.removeChild(l),c=null}}function t(e){return Math.round(parseFloat(e))}var r,i,o,a,s,u,l=b.createElement("div"),c=b.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===c.style.backgroundClip,E.extend(y,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),a},pixelPosition:function(){return e(),r},reliableMarginLeft:function(){return e(),u},scrollboxSize:function(){return e(),o},reliableTrDimensions:function(){var e,t,r,i;return null==s&&(e=b.createElement("table"),t=b.createElement("tr"),r=b.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="box-sizing:content-box;border:1px solid",t.style.height="1px",r.style.height="9px",r.style.display="block",xe.appendChild(e).appendChild(t).appendChild(r),i=n.getComputedStyle(t),s=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===t.offsetHeight,xe.removeChild(e)),s}}))})();var rt=["Webkit","Moz","ms"],it=b.createElement("div").style,ot={};function at(e){var t=e[0].toUpperCase()+e.slice(1),n=rt.length;while(n--)if(e=rt[n]+t,e in it)return e}function st(e){var t=E.cssProps[e]||ot[e];return t||(e in it?e:ot[e]=at(e)||e)}var ut=/^(none|table(?!-c[ea]).+)/,lt={position:"absolute",visibility:"hidden",display:"block"},ct={letterSpacing:"0",fontWeight:"400"};function ft(e,t,n){var r=ye.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function pt(e,t,n,r,i,o){var a="width"===t?1:0,s=0,u=0,l=0;if(n===(r?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(l+=E.css(e,n+me[a],!0,i)),r?("content"===n&&(u-=E.css(e,"padding"+me[a],!0,i)),"margin"!==n&&(u-=E.css(e,"border"+me[a]+"Width",!0,i))):(u+=E.css(e,"padding"+me[a],!0,i),"padding"!==n?u+=E.css(e,"border"+me[a]+"Width",!0,i):s+=E.css(e,"border"+me[a]+"Width",!0,i));return!r&&o>=0&&(u+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-u-s-.5))||0),u+l}function dt(e,t,n){var r=Ke(e),i=!y.boxSizingReliable()||n,o=i&&"border-box"===E.css(e,"boxSizing",!1,r),a=o,s=tt(e,t,r),u="offset"+t[0].toUpperCase()+t.slice(1);if(Je.test(s)){if(!n)return s;s="auto"}return(!y.boxSizingReliable()&&o||!y.reliableTrDimensions()&&A(e,"tr")||"auto"===s||!parseFloat(s)&&"inline"===E.css(e,"display",!1,r))&&e.getClientRects().length&&(o="border-box"===E.css(e,"boxSizing",!1,r),a=u in e,a&&(s=e[u])),s=parseFloat(s)||0,s+pt(e,t,n||(o?"border":"content"),a,r,s)+"px"}function ht(e,t,n,r,i){return new ht.prototype.init(e,t,n,r,i)}E.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=tt(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,a,s=se(t),u=Qe.test(t),l=e.style;if(u||(t=st(s)),a=E.cssHooks[t]||E.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(i=a.get(e,!1,r))?i:l[t];o=typeof n,"string"===o&&(i=ye.exec(n))&&i[1]&&(n=Ce(e,t,i),o="number"),null!=n&&n===n&&("number"!==o||u||(n+=i&&i[3]||(E.cssNumber[s]?"":"px")),y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,r))||(u?l.setProperty(t,n):l[t]=n))}},css:function(e,t,n,r){var i,o,a,s=se(t),u=Qe.test(t);return u||(t=st(s)),a=E.cssHooks[t]||E.cssHooks[s],a&&"get"in a&&(i=a.get(e,!0,n)),void 0===i&&(i=tt(e,t,r)),"normal"===i&&t in ct&&(i=ct[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),E.each(["height","width"],(function(e,t){E.cssHooks[t]={get:function(e,n,r){if(n)return!ut.test(E.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?dt(e,t,r):Ze(e,lt,(function(){return dt(e,t,r)}))},set:function(e,n,r){var i,o=Ke(e),a=!y.scrollboxSize()&&"absolute"===o.position,s=a||r,u=s&&"border-box"===E.css(e,"boxSizing",!1,o),l=r?pt(e,t,r,u,o):0;return u&&a&&(l-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-pt(e,t,"border",!1,o)-.5)),l&&(i=ye.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=E.css(e,t)),ft(e,n,l)}}})),E.cssHooks.marginLeft=nt(y.reliableMarginLeft,(function(e,t){if(t)return(parseFloat(tt(e,"marginLeft"))||e.getBoundingClientRect().left-Ze(e,{marginLeft:0},(function(){return e.getBoundingClientRect().left})))+"px"})),E.each({margin:"",padding:"",border:"Width"},(function(e,t){E.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"===typeof n?n.split(" "):[n];r<4;r++)i[e+me[r]+t]=o[r]||o[r-2]||o[0];return i}},"margin"!==e&&(E.cssHooks[e+t].set=ft)})),E.fn.extend({css:function(e,t){return re(this,(function(e,t,n){var r,i,o={},a=0;if(Array.isArray(t)){for(r=Ke(e),i=t.length;a<i;a++)o[t[a]]=E.css(e,t[a],!1,r);return o}return void 0!==n?E.style(e,t,n):E.css(e,t)}),e,t,arguments.length>1)}}),E.Tween=ht,ht.prototype={constructor:ht,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||E.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(E.cssNumber[n]?"":"px")},cur:function(){var e=ht.propHooks[this.prop];return e&&e.get?e.get(this):ht.propHooks._default.get(this)},run:function(e){var t,n=ht.propHooks[this.prop];return this.options.duration?this.pos=t=E.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):ht.propHooks._default.set(this),this}},ht.prototype.init.prototype=ht.prototype,ht.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=E.css(e.elem,e.prop,""),t&&"auto"!==t?t:0)},set:function(e){E.fx.step[e.prop]?E.fx.step[e.prop](e):1!==e.elem.nodeType||!E.cssHooks[e.prop]&&null==e.elem.style[st(e.prop)]?e.elem[e.prop]=e.now:E.style(e.elem,e.prop,e.now+e.unit)}}},ht.propHooks.scrollTop=ht.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},E.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},E.fx=ht.prototype.init,E.fx.step={};var gt,vt,yt=/^(?:toggle|show|hide)$/,mt=/queueHooks$/;function xt(){vt&&(!1===b.hidden&&n.requestAnimationFrame?n.requestAnimationFrame(xt):n.setTimeout(xt,E.fx.interval),E.fx.tick())}function bt(){return n.setTimeout((function(){gt=void 0})),gt=Date.now()}function wt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)n=me[r],i["margin"+n]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function Tt(e,t,n){for(var r,i=(St.tweeners[t]||[]).concat(St.tweeners["*"]),o=0,a=i.length;o<a;o++)if(r=i[o].call(n,t,e))return r}function Ct(e,t,n){var r,i,o,a,s,u,l,c,f="width"in t||"height"in t,p=this,d={},h=e.style,g=e.nodeType&&Te(e),v=ce.get(e,"fxshow");for(r in n.queue||(a=E._queueHooks(e,"fx"),null==a.unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,p.always((function(){p.always((function(){a.unqueued--,E.queue(e,"fx").length||a.empty.fire()}))}))),t)if(i=t[r],yt.test(i)){if(delete t[r],o=o||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!v||void 0===v[r])continue;g=!0}d[r]=v&&v[r]||E.style(e,r)}if(u=!E.isEmptyObject(t),u||!E.isEmptyObject(d))for(r in f&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],l=v&&v.display,null==l&&(l=ce.get(e,"display")),c=E.css(e,"display"),"none"===c&&(l?c=l:(Ee([e],!0),l=e.style.display||l,c=E.css(e,"display"),Ee([e]))),("inline"===c||"inline-block"===c&&null!=l)&&"none"===E.css(e,"float")&&(u||(p.done((function(){h.display=l})),null==l&&(c=h.display,l="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always((function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]}))),u=!1,d)u||(v?"hidden"in v&&(g=v.hidden):v=ce.access(e,"fxshow",{display:l}),o&&(v.hidden=!g),g&&Ee([e],!0),p.done((function(){for(r in g||Ee([e]),ce.remove(e,"fxshow"),d)E.style(e,r,d[r])}))),u=Tt(g?v[r]:0,r,p),r in v||(v[r]=u.start,g&&(u.end=u.start,u.start=0))}function kt(e,t){var n,r,i,o,a;for(n in e)if(r=se(n),i=t[r],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),a=E.cssHooks[r],a&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}function St(e,t,n){var r,i,o=0,a=St.prefilters.length,s=E.Deferred().always((function(){delete u.elem})),u=function(){if(i)return!1;for(var t=gt||bt(),n=Math.max(0,l.startTime+l.duration-t),r=n/l.duration||0,o=1-r,a=0,u=l.tweens.length;a<u;a++)l.tweens[a].run(o);return s.notifyWith(e,[l,o,n]),o<1&&u?n:(u||s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l]),!1)},l=s.promise({elem:e,props:E.extend({},t),opts:E.extend(!0,{specialEasing:{},easing:E.easing._default},n),originalProperties:t,originalOptions:n,startTime:gt||bt(),duration:n.duration,tweens:[],createTween:function(t,n){var r=E.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(r),r},stop:function(t){var n=0,r=t?l.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)l.tweens[n].run(1);return t?(s.notifyWith(e,[l,1,0]),s.resolveWith(e,[l,t])):s.rejectWith(e,[l,t]),this}}),c=l.props;for(kt(c,l.opts.specialEasing);o<a;o++)if(r=St.prefilters[o].call(l,e,c,l.opts),r)return m(r.stop)&&(E._queueHooks(l.elem,l.opts.queue).stop=r.stop.bind(r)),r;return E.map(c,Tt,l),m(l.opts.start)&&l.opts.start.call(e,l),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always),E.fx.timer(E.extend(u,{elem:e,anim:l,queue:l.opts.queue})),l}E.Animation=E.extend(St,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return Ce(n.elem,e,ye.exec(t),n),n}]},tweener:function(e,t){m(e)?(t=e,e=["*"]):e=e.match(Y);for(var n,r=0,i=e.length;r<i;r++)n=e[r],St.tweeners[n]=St.tweeners[n]||[],St.tweeners[n].unshift(t)},prefilters:[Ct],prefilter:function(e,t){t?St.prefilters.unshift(e):St.prefilters.push(e)}}),E.speed=function(e,t,n){var r=e&&"object"===typeof e?E.extend({},e):{complete:n||!n&&t||m(e)&&e,duration:e,easing:n&&t||t&&!m(t)&&t};return E.fx.off?r.duration=0:"number"!==typeof r.duration&&(r.duration in E.fx.speeds?r.duration=E.fx.speeds[r.duration]:r.duration=E.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){m(r.old)&&r.old.call(this),r.queue&&E.dequeue(this,r.queue)},r},E.fn.extend({fadeTo:function(e,t,n,r){return this.filter(Te).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=E.isEmptyObject(e),o=E.speed(t,n,r),a=function(){var t=St(this,E.extend({},e),o);(i||ce.get(this,"finish"))&&t.stop(!0)};return a.finish=a,i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!==typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each((function(){var t=!0,i=null!=e&&e+"queueHooks",o=E.timers,a=ce.get(this);if(i)a[i]&&a[i].stop&&r(a[i]);else for(i in a)a[i]&&a[i].stop&&mt.test(i)&&r(a[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||E.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=ce.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=E.timers,a=r?r.length:0;for(n.finish=!0,E.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<a;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish}))}}),E.each(["toggle","show","hide"],(function(e,t){var n=E.fn[t];E.fn[t]=function(e,r,i){return null==e||"boolean"===typeof e?n.apply(this,arguments):this.animate(wt(t,!0),e,r,i)}})),E.each({slideDown:wt("show"),slideUp:wt("hide"),slideToggle:wt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){E.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}})),E.timers=[],E.fx.tick=function(){var e,t=0,n=E.timers;for(gt=Date.now();t<n.length;t++)e=n[t],e()||n[t]!==e||n.splice(t--,1);n.length||E.fx.stop(),gt=void 0},E.fx.timer=function(e){E.timers.push(e),E.fx.start()},E.fx.interval=13,E.fx.start=function(){vt||(vt=!0,xt())},E.fx.stop=function(){vt=null},E.fx.speeds={slow:600,fast:200,_default:400},E.fn.delay=function(e,t){return e=E.fx&&E.fx.speeds[e]||e,t=t||"fx",this.queue(t,(function(t,r){var i=n.setTimeout(t,e);r.stop=function(){n.clearTimeout(i)}}))},function(){var e=b.createElement("input"),t=b.createElement("select"),n=t.appendChild(b.createElement("option"));e.type="checkbox",y.checkOn=""!==e.value,y.optSelected=n.selected,e=b.createElement("input"),e.value="t",e.type="radio",y.radioValue="t"===e.value}();var Et,jt=E.expr.attrHandle;E.fn.extend({attr:function(e,t){return re(this,E.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){E.removeAttr(this,e)}))}}),E.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"===typeof e.getAttribute?E.prop(e,t,n):(1===o&&E.isXMLDoc(e)||(i=E.attrHooks[t.toLowerCase()]||(E.expr.match.bool.test(t)?Et:void 0)),void 0!==n?null===n?void E.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:(r=E.find.attr(e,t),null==r?void 0:r))},attrHooks:{type:{set:function(e,t){if(!y.radioValue&&"radio"===t&&A(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(Y);if(i&&1===e.nodeType)while(n=i[r++])e.removeAttribute(n)}}),Et={set:function(e,t,n){return!1===t?E.removeAttr(e,n):e.setAttribute(n,n),n}},E.each(E.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=jt[t]||E.find.attr;jt[t]=function(e,t,r){var i,o,a=t.toLowerCase();return r||(o=jt[a],jt[a]=i,i=null!=n(e,t,r)?a:null,jt[a]=o),i}}));var At=/^(?:input|select|textarea|button)$/i,Dt=/^(?:a|area)$/i;function Nt(e){var t=e.match(Y)||[];return t.join(" ")}function qt(e){return e.getAttribute&&e.getAttribute("class")||""}function Lt(e){return Array.isArray(e)?e:"string"===typeof e&&e.match(Y)||[]}E.fn.extend({prop:function(e,t){return re(this,E.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each((function(){delete this[E.propFix[e]||e]}))}}),E.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&E.isXMLDoc(e)||(t=E.propFix[t]||t,i=E.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=E.find.attr(e,"tabindex");return t?parseInt(t,10):At.test(e.nodeName)||Dt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),y.optSelected||(E.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),E.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){E.propFix[this.toLowerCase()]=this})),E.fn.extend({addClass:function(e){var t,n,r,i,o,a;return m(e)?this.each((function(t){E(this).addClass(e.call(this,t,qt(this)))})):(t=Lt(e),t.length?this.each((function(){if(r=qt(this),n=1===this.nodeType&&" "+Nt(r)+" ",n){for(o=0;o<t.length;o++)i=t[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");a=Nt(n),r!==a&&this.setAttribute("class",a)}})):this)},removeClass:function(e){var t,n,r,i,o,a;return m(e)?this.each((function(t){E(this).removeClass(e.call(this,t,qt(this)))})):arguments.length?(t=Lt(e),t.length?this.each((function(){if(r=qt(this),n=1===this.nodeType&&" "+Nt(r)+" ",n){for(o=0;o<t.length;o++){i=t[o];while(n.indexOf(" "+i+" ")>-1)n=n.replace(" "+i+" "," ")}a=Nt(n),r!==a&&this.setAttribute("class",a)}})):this):this.attr("class","")},toggleClass:function(e,t){var n,r,i,o,a=typeof e,s="string"===a||Array.isArray(e);return m(e)?this.each((function(n){E(this).toggleClass(e.call(this,n,qt(this),t),t)})):"boolean"===typeof t&&s?t?this.addClass(e):this.removeClass(e):(n=Lt(e),this.each((function(){if(s)for(o=E(this),i=0;i<n.length;i++)r=n[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==e&&"boolean"!==a||(r=qt(this),r&&ce.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===e?"":ce.get(this,"__className__")||""))})))},hasClass:function(e){var t,n,r=0;t=" "+e+" ";while(n=this[r++])if(1===n.nodeType&&(" "+Nt(qt(n))+" ").indexOf(t)>-1)return!0;return!1}});var Ht=/\r/g;E.fn.extend({val:function(e){var t,n,r,i=this[0];return arguments.length?(r=m(e),this.each((function(n){var i;1===this.nodeType&&(i=r?e.call(this,n,E(this).val()):e,null==i?i="":"number"===typeof i?i+="":Array.isArray(i)&&(i=E.map(i,(function(e){return null==e?"":e+""}))),t=E.valHooks[this.type]||E.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))}))):i?(t=E.valHooks[i.type]||E.valHooks[i.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:(n=i.value,"string"===typeof n?n.replace(Ht,""):null==n?"":n)):void 0}}),E.extend({valHooks:{option:{get:function(e){var t=E.find.attr(e,"value");return null!=t?t:Nt(E.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,a="select-one"===e.type,s=a?null:[],u=a?o+1:i.length;for(r=o<0?u:a?o:0;r<u;r++)if(n=i[r],(n.selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!A(n.parentNode,"optgroup"))){if(t=E(n).val(),a)return t;s.push(t)}return s},set:function(e,t){var n,r,i=e.options,o=E.makeArray(t),a=i.length;while(a--)r=i[a],(r.selected=E.inArray(E.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),E.each(["radio","checkbox"],(function(){E.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=E.inArray(E(e).val(),t)>-1}},y.checkOn||(E.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}));var Ot=n.location,Pt={guid:Date.now()},Mt=/\?/;E.parseXML=function(e){var t,r;if(!e||"string"!==typeof e)return null;try{t=(new n.DOMParser).parseFromString(e,"text/xml")}catch(i){}return r=t&&t.getElementsByTagName("parsererror")[0],t&&!r||E.error("Invalid XML: "+(r?E.map(r.childNodes,(function(e){return e.textContent})).join("\n"):e)),t};var Rt=/^(?:focusinfocus|focusoutblur)$/,It=function(e){e.stopPropagation()};E.extend(E.event,{trigger:function(e,t,r,i){var o,a,s,u,l,c,f,p,d=[r||b],g=h.call(e,"type")?e.type:e,v=h.call(e,"namespace")?e.namespace.split("."):[];if(a=p=s=r=r||b,3!==r.nodeType&&8!==r.nodeType&&!Rt.test(g+E.event.triggered)&&(g.indexOf(".")>-1&&(v=g.split("."),g=v.shift(),v.sort()),l=g.indexOf(":")<0&&"on"+g,e=e[E.expando]?e:new E.Event(g,"object"===typeof e&&e),e.isTrigger=i?2:3,e.namespace=v.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+v.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=r),t=null==t?[e]:E.makeArray(t,[e]),f=E.event.special[g]||{},i||!f.trigger||!1!==f.trigger.apply(r,t))){if(!i&&!f.noBubble&&!x(r)){for(u=f.delegateType||g,Rt.test(u+g)||(a=a.parentNode);a;a=a.parentNode)d.push(a),s=a;s===(r.ownerDocument||b)&&d.push(s.defaultView||s.parentWindow||n)}o=0;while((a=d[o++])&&!e.isPropagationStopped())p=a,e.type=o>1?u:f.bindType||g,c=(ce.get(a,"events")||Object.create(null))[e.type]&&ce.get(a,"handle"),c&&c.apply(a,t),c=l&&a[l],c&&c.apply&&ue(a)&&(e.result=c.apply(a,t),!1===e.result&&e.preventDefault());return e.type=g,i||e.isDefaultPrevented()||f._default&&!1!==f._default.apply(d.pop(),t)||!ue(r)||l&&m(r[g])&&!x(r)&&(s=r[l],s&&(r[l]=null),E.event.triggered=g,e.isPropagationStopped()&&p.addEventListener(g,It),r[g](),e.isPropagationStopped()&&p.removeEventListener(g,It),E.event.triggered=void 0,s&&(r[l]=s)),e.result}},simulate:function(e,t,n){var r=E.extend(new E.Event,n,{type:e,isSimulated:!0});E.event.trigger(r,null,t)}}),E.fn.extend({trigger:function(e,t){return this.each((function(){E.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];if(n)return E.event.trigger(e,t,n,!0)}});var Wt=/\[\]$/,Ft=/\r?\n/g,$t=/^(?:submit|button|image|reset|file)$/i,Bt=/^(?:input|select|textarea|keygen)/i;function _t(e,t,n,r){var i;if(Array.isArray(t))E.each(t,(function(t,i){n||Wt.test(e)?r(e,i):_t(e+"["+("object"===typeof i&&null!=i?t:"")+"]",i,n,r)}));else if(n||"object"!==C(t))r(e,t);else for(i in t)_t(e+"["+i+"]",t[i],n,r)}E.param=function(e,t){var n,r=[],i=function(e,t){var n=m(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!E.isPlainObject(e))E.each(e,(function(){i(this.name,this.value)}));else for(n in e)_t(n,e[n],t,i);return r.join("&")},E.fn.extend({serialize:function(){return E.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=E.prop(this,"elements");return e?E.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!E(this).is(":disabled")&&Bt.test(this.nodeName)&&!$t.test(e)&&(this.checked||!je.test(e))})).map((function(e,t){var n=E(this).val();return null==n?null:Array.isArray(n)?E.map(n,(function(e){return{name:t.name,value:e.replace(Ft,"\r\n")}})):{name:t.name,value:n.replace(Ft,"\r\n")}})).get()}});var zt=/%20/g,Xt=/#.*$/,Ut=/([?&])_=[^&]*/,Vt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Gt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Yt=/^(?:GET|HEAD)$/,Jt=/^\/\//,Qt={},Kt={},Zt="*/".concat("*"),en=b.createElement("a");function tn(e){return function(t,n){"string"!==typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(Y)||[];if(m(n))while(r=o[i++])"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function nn(e,t,n,r){var i={},o=e===Kt;function a(s){var u;return i[s]=!0,E.each(e[s]||[],(function(e,s){var l=s(t,n,r);return"string"!==typeof l||o||i[l]?o?!(u=l):void 0:(t.dataTypes.unshift(l),a(l),!1)})),u}return a(t.dataTypes[0])||!i["*"]&&a("*")}function rn(e,t){var n,r,i=E.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&E.extend(!0,e,r),e}function on(e,t,n){var r,i,o,a,s=e.contents,u=e.dataTypes;while("*"===u[0])u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}a||(a=i)}o=o||a}if(o)return o!==u[0]&&u.unshift(o),n[o]}function an(e,t,n,r){var i,o,a,s,u,l={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)l[a.toLowerCase()]=e.converters[a];o=c.shift();while(o)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift(),o)if("*"===o)o=u;else if("*"!==u&&u!==o){if(a=l[u+" "+o]||l["* "+o],!a)for(i in l)if(s=i.split(" "),s[1]===o&&(a=l[u+" "+s[0]]||l["* "+s[0]],a)){!0===a?a=l[i]:!0!==l[i]&&(o=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(f){return{state:"parsererror",error:a?f:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}en.href=Ot.href,E.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ot.href,type:"GET",isLocal:Gt.test(Ot.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Zt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":E.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?rn(rn(e,E.ajaxSettings),t):rn(E.ajaxSettings,e)},ajaxPrefilter:tn(Qt),ajaxTransport:tn(Kt),ajax:function(e,t){"object"===typeof e&&(t=e,e=void 0),t=t||{};var r,i,o,a,s,u,l,c,f,p,d=E.ajaxSetup({},t),h=d.context||d,g=d.context&&(h.nodeType||h.jquery)?E(h):E.event,v=E.Deferred(),y=E.Callbacks("once memory"),m=d.statusCode||{},x={},w={},T="canceled",C={readyState:0,getResponseHeader:function(e){var t;if(l){if(!a){a={};while(t=Vt.exec(o))a[t[1].toLowerCase()+" "]=(a[t[1].toLowerCase()+" "]||[]).concat(t[2])}t=a[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return l?o:null},setRequestHeader:function(e,t){return null==l&&(e=w[e.toLowerCase()]=w[e.toLowerCase()]||e,x[e]=t),this},overrideMimeType:function(e){return null==l&&(d.mimeType=e),this},statusCode:function(e){var t;if(e)if(l)C.always(e[C.status]);else for(t in e)m[t]=[m[t],e[t]];return this},abort:function(e){var t=e||T;return r&&r.abort(t),k(0,t),this}};if(v.promise(C),d.url=((e||d.url||Ot.href)+"").replace(Jt,Ot.protocol+"//"),d.type=t.method||t.type||d.method||d.type,d.dataTypes=(d.dataType||"*").toLowerCase().match(Y)||[""],null==d.crossDomain){u=b.createElement("a");try{u.href=d.url,u.href=u.href,d.crossDomain=en.protocol+"//"+en.host!==u.protocol+"//"+u.host}catch(S){d.crossDomain=!0}}if(d.data&&d.processData&&"string"!==typeof d.data&&(d.data=E.param(d.data,d.traditional)),nn(Qt,d,t,C),l)return C;for(f in c=E.event&&d.global,c&&0===E.active++&&E.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!Yt.test(d.type),i=d.url.replace(Xt,""),d.hasContent?d.data&&d.processData&&0===(d.contentType||"").indexOf("application/x-www-form-urlencoded")&&(d.data=d.data.replace(zt,"+")):(p=d.url.slice(i.length),d.data&&(d.processData||"string"===typeof d.data)&&(i+=(Mt.test(i)?"&":"?")+d.data,delete d.data),!1===d.cache&&(i=i.replace(Ut,"$1"),p=(Mt.test(i)?"&":"?")+"_="+Pt.guid+++p),d.url=i+p),d.ifModified&&(E.lastModified[i]&&C.setRequestHeader("If-Modified-Since",E.lastModified[i]),E.etag[i]&&C.setRequestHeader("If-None-Match",E.etag[i])),(d.data&&d.hasContent&&!1!==d.contentType||t.contentType)&&C.setRequestHeader("Content-Type",d.contentType),C.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+Zt+"; q=0.01":""):d.accepts["*"]),d.headers)C.setRequestHeader(f,d.headers[f]);if(d.beforeSend&&(!1===d.beforeSend.call(h,C,d)||l))return C.abort();if(T="abort",y.add(d.complete),C.done(d.success),C.fail(d.error),r=nn(Kt,d,t,C),r){if(C.readyState=1,c&&g.trigger("ajaxSend",[C,d]),l)return C;d.async&&d.timeout>0&&(s=n.setTimeout((function(){C.abort("timeout")}),d.timeout));try{l=!1,r.send(x,k)}catch(S){if(l)throw S;k(-1,S)}}else k(-1,"No Transport");function k(e,t,a,u){var f,p,x,b,w,T=t;l||(l=!0,s&&n.clearTimeout(s),r=void 0,o=u||"",C.readyState=e>0?4:0,f=e>=200&&e<300||304===e,a&&(b=on(d,C,a)),!f&&E.inArray("script",d.dataTypes)>-1&&E.inArray("json",d.dataTypes)<0&&(d.converters["text script"]=function(){}),b=an(d,b,C,f),f?(d.ifModified&&(w=C.getResponseHeader("Last-Modified"),w&&(E.lastModified[i]=w),w=C.getResponseHeader("etag"),w&&(E.etag[i]=w)),204===e||"HEAD"===d.type?T="nocontent":304===e?T="notmodified":(T=b.state,p=b.data,x=b.error,f=!x)):(x=T,!e&&T||(T="error",e<0&&(e=0))),C.status=e,C.statusText=(t||T)+"",f?v.resolveWith(h,[p,T,C]):v.rejectWith(h,[C,T,x]),C.statusCode(m),m=void 0,c&&g.trigger(f?"ajaxSuccess":"ajaxError",[C,d,f?p:x]),y.fireWith(h,[C,T]),c&&(g.trigger("ajaxComplete",[C,d]),--E.active||E.event.trigger("ajaxStop")))}return C},getJSON:function(e,t,n){return E.get(e,t,n,"json")},getScript:function(e,t){return E.get(e,void 0,t,"script")}}),E.each(["get","post"],(function(e,t){E[t]=function(e,n,r,i){return m(n)&&(i=i||r,r=n,n=void 0),E.ajax(E.extend({url:e,type:t,dataType:i,data:n,success:r},E.isPlainObject(e)&&e))}})),E.ajaxPrefilter((function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")})),E._evalUrl=function(e,t,n){return E.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){E.globalEval(e,t,n)}})},E.fn.extend({wrapAll:function(e){var t;return this[0]&&(m(e)&&(e=e.call(this[0])),t=E(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){var e=this;while(e.firstElementChild)e=e.firstElementChild;return e})).append(this)),this},wrapInner:function(e){return m(e)?this.each((function(t){E(this).wrapInner(e.call(this,t))})):this.each((function(){var t=E(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)}))},wrap:function(e){var t=m(e);return this.each((function(n){E(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(e){return this.parent(e).not("body").each((function(){E(this).replaceWith(this.childNodes)})),this}}),E.expr.pseudos.hidden=function(e){return!E.expr.pseudos.visible(e)},E.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},E.ajaxSettings.xhr=function(){try{return new n.XMLHttpRequest}catch(e){}};var sn={0:200,1223:204},un=E.ajaxSettings.xhr();y.cors=!!un&&"withCredentials"in un,y.ajax=un=!!un,E.ajaxTransport((function(e){var t,r;if(y.cors||un&&!e.crossDomain)return{send:function(i,o){var a,s=e.xhr();if(s.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(a in e.xhrFields)s[a]=e.xhrFields[a];for(a in e.mimeType&&s.overrideMimeType&&s.overrideMimeType(e.mimeType),e.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)s.setRequestHeader(a,i[a]);t=function(e){return function(){t&&(t=r=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===e?s.abort():"error"===e?"number"!==typeof s.status?o(0,"error"):o(s.status,s.statusText):o(sn[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!==typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=t(),r=s.onerror=s.ontimeout=t("error"),void 0!==s.onabort?s.onabort=r:s.onreadystatechange=function(){4===s.readyState&&n.setTimeout((function(){t&&r()}))},t=t("abort");try{s.send(e.hasContent&&e.data||null)}catch(u){if(t)throw u}},abort:function(){t&&t()}}})),E.ajaxPrefilter((function(e){e.crossDomain&&(e.contents.script=!1)})),E.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return E.globalEval(e),e}}}),E.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")})),E.ajaxTransport("script",(function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(r,i){t=E("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),b.head.appendChild(t[0])},abort:function(){n&&n()}}}));var ln=[],cn=/(=)\?(?=&|$)|\?\?/;E.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=ln.pop()||E.expando+"_"+Pt.guid++;return this[e]=!0,e}}),E.ajaxPrefilter("json jsonp",(function(e,t,r){var i,o,a,s=!1!==e.jsonp&&(cn.test(e.url)?"url":"string"===typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&cn.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=m(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(cn,"$1"+i):!1!==e.jsonp&&(e.url+=(Mt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return a||E.error(i+" was not called"),a[0]},e.dataTypes[0]="json",o=n[i],n[i]=function(){a=arguments},r.always((function(){void 0===o?E(n).removeProp(i):n[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,ln.push(i)),a&&m(o)&&o(a[0]),a=o=void 0})),"script"})),y.createHTMLDocument=function(){var e=b.implementation.createHTMLDocument("").body;return e.innerHTML="<form></form><form></form>",2===e.childNodes.length}(),E.parseHTML=function(e,t,n){return"string"!==typeof e?[]:("boolean"===typeof t&&(n=t,t=!1),t||(y.createHTMLDocument?(t=b.implementation.createHTMLDocument(""),r=t.createElement("base"),r.href=b.location.href,t.head.appendChild(r)):t=b),i=$.exec(e),o=!n&&[],i?[t.createElement(i[1])]:(i=Oe([e],t,o),o&&o.length&&E(o).remove(),E.merge([],i.childNodes)));var r,i,o},E.fn.load=function(e,t,n){var r,i,o,a=this,s=e.indexOf(" ");return s>-1&&(r=Nt(e.slice(s)),e=e.slice(0,s)),m(t)?(n=t,t=void 0):t&&"object"===typeof t&&(i="POST"),a.length>0&&E.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done((function(e){o=arguments,a.html(r?E("<div>").append(E.parseHTML(e)).find(r):e)})).always(n&&function(e,t){a.each((function(){n.apply(this,o||[e.responseText,t,e])}))}),this},E.expr.pseudos.animated=function(e){return E.grep(E.timers,(function(t){return e===t.elem})).length},E.offset={setOffset:function(e,t,n){var r,i,o,a,s,u,l,c=E.css(e,"position"),f=E(e),p={};"static"===c&&(e.style.position="relative"),s=f.offset(),o=E.css(e,"top"),u=E.css(e,"left"),l=("absolute"===c||"fixed"===c)&&(o+u).indexOf("auto")>-1,l?(r=f.position(),a=r.top,i=r.left):(a=parseFloat(o)||0,i=parseFloat(u)||0),m(t)&&(t=t.call(e,n,E.extend({},s))),null!=t.top&&(p.top=t.top-s.top+a),null!=t.left&&(p.left=t.left-s.left+i),"using"in t?t.using.call(e,p):f.css(p)}},E.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each((function(t){E.offset.setOffset(this,e,t)}));var t,n,r=this[0];return r?r.getClientRects().length?(t=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===E.css(r,"position"))t=r.getBoundingClientRect();else{t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;while(e&&(e===n.body||e===n.documentElement)&&"static"===E.css(e,"position"))e=e.parentNode;e&&e!==r&&1===e.nodeType&&(i=E(e).offset(),i.top+=E.css(e,"borderTopWidth",!0),i.left+=E.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-E.css(r,"marginTop",!0),left:t.left-i.left-E.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){var e=this.offsetParent;while(e&&"static"===E.css(e,"position"))e=e.offsetParent;return e||xe}))}}),E.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n="pageYOffset"===t;E.fn[e]=function(r){return re(this,(function(e,r,i){var o;if(x(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i)return o?o[t]:e[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i}),e,r,arguments.length)}})),E.each(["top","left"],(function(e,t){E.cssHooks[t]=nt(y.pixelPosition,(function(e,n){if(n)return n=tt(e,t),Je.test(n)?E(e).position()[t]+"px":n}))})),E.each({Height:"height",Width:"width"},(function(e,t){E.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,r){E.fn[r]=function(i,o){var a=arguments.length&&(n||"boolean"!==typeof i),s=n||(!0===i||!0===o?"margin":"border");return re(this,(function(t,n,i){var o;return x(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?E.css(t,n,s):E.style(t,n,i,s)}),t,a?i:void 0,a)}}))})),E.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){E.fn[t]=function(e){return this.on(t,e)}})),E.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),E.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(e,t){E.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}));var fn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;E.proxy=function(e,t){var n,r,i;if("string"===typeof t&&(n=e[t],t=e,e=n),m(e))return r=u.call(arguments,2),i=function(){return e.apply(t||this,r.concat(u.call(arguments)))},i.guid=e.guid=e.guid||E.guid++,i},E.holdReady=function(e){e?E.readyWait++:E.ready(!0)},E.isArray=Array.isArray,E.parseJSON=JSON.parse,E.nodeName=A,E.isFunction=m,E.isWindow=x,E.camelCase=se,E.type=C,E.now=Date.now,E.isNumeric=function(e){var t=E.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},E.trim=function(e){return null==e?"":(e+"").replace(fn,"$1")},r=[],i=function(){return E}.apply(t,r),void 0===i||(e.exports=i);var pn=n.jQuery,dn=n.$;return E.noConflict=function(e){return n.$===E&&(n.$=dn),e&&n.jQuery===E&&(n.jQuery=pn),E},"undefined"===typeof o&&(n.jQuery=n.$=E),E}))}}]);