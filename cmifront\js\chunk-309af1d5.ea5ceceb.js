(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-309af1d5"],{"00b4":function(e,t,a){"use strict";a("ac1f");var i=a("23e7"),o=a("c65b"),r=a("1626"),n=a("825a"),s=a("577e"),l=function(){var e=!1,t=/[ac]/;return t.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===t.test("abc")&&e}(),c=/./.test;i({target:"RegExp",proto:!0,forced:!l},{test:function(e){var t=n(this),a=s(e),i=t.exec;if(!r(i))return o(c,t,a);var l=o(i,t,a);return null!==l&&(n(l),!0)}})},"0ae5":function(e,t,a){"use strict";a("d9e2"),a("d3b7"),a("25f0");t["a"]={methods:{validateDate:function(e,t,a){var i=this.form.endDate||this.form.endTime||this.searchEndTime.toString(),o=this.form.startDate||this.form.startTime||this.searchBeginTime.toString();i&&o?"startDate"===e.field||"startTime"===e.field||"beginMonth"===e.field?this.$time(o,">",i)?a(new Error("开始时间不能大于结束时间")):a():i<o?a(new Error("结束时间不能小于开始时间")):a():a()}}}},"129f":function(e,t,a){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},"260f":function(e,t,a){},"466d":function(e,t,a){"use strict";var i=a("c65b"),o=a("d784"),r=a("825a"),n=a("7234"),s=a("50c4"),l=a("577e"),c=a("1d80"),d=a("dc4a"),u=a("8aa5"),m=a("14c3");o("match",(function(e,t,a){return[function(t){var a=c(this),o=n(t)?void 0:d(t,e);return o?i(o,t,a):new RegExp(t)[e](l(a))},function(e){var i=r(this),o=l(e),n=a(t,i,o);if(n.done)return n.value;if(!i.global)return m(i,o);var c=i.unicode;i.lastIndex=0;var d,p=[],g=0;while(null!==(d=m(i,o))){var f=l(d[0]);p[g]=f,""===f&&(i.lastIndex=u(o,s(i.lastIndex),c)),g++}return 0===g?null:p}]}))},"4fadc":function(e,t,a){"use strict";var i=a("23e7"),o=a("6f53").entries;i({target:"Object",stat:!0},{entries:function(e){return o(e)}})},6410:function(e,t,a){"use strict";a("87d6")},"6f53":function(e,t,a){"use strict";var i=a("83ab"),o=a("d039"),r=a("e330"),n=a("e163"),s=a("df75"),l=a("fc6a"),c=a("d1e7").f,d=r(c),u=r([].push),m=i&&o((function(){var e=Object.create(null);return e[2]=2,!d(e,2)})),p=function(e){return function(t){var a,o=l(t),r=s(o),c=m&&null===n(o),p=r.length,g=0,f=[];while(p>g)a=r[g++],i&&!(c?a in o:d(o,a))||u(f,e?[a,o[a]]:o[a]);return f}};e.exports={entries:p(!0),values:p(!1)}},"7f6b":function(e,t,a){"use strict";a("260f")},"82c4":function(e,t,a){e.exports=a.p+"img/cmLink.4340350e.png"},"841c":function(e,t,a){"use strict";var i=a("c65b"),o=a("d784"),r=a("825a"),n=a("7234"),s=a("1d80"),l=a("129f"),c=a("577e"),d=a("dc4a"),u=a("14c3");o("search",(function(e,t,a){return[function(t){var a=s(this),o=n(t)?void 0:d(t,e);return o?i(o,t,a):new RegExp(t)[e](c(a))},function(e){var i=r(this),o=c(e),n=a(t,i,o);if(n.done)return n.value;var s=i.lastIndex;l(s,0)||(i.lastIndex=0);var d=u(i,o);return l(i.lastIndex,s)||(i.lastIndex=s),null===d?-1:d.index}]}))},"87d6":function(e,t,a){},"951d":function(e,t,a){"use strict";a.d(t,"g",(function(){return r})),a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return s})),a.d(t,"b",(function(){return l})),a.d(t,"a",(function(){return c})),a.d(t,"e",(function(){return d})),a.d(t,"f",(function(){return u}));var i=a("66df"),o="/cms/package/config",r=function(e){return i["a"].request({url:o+"/task/pageList",data:e,method:"post"})},n=function(e,t){return i["a"].request({url:o+"/task/download/".concat(e,"?status=")+t,method:"POST",responseType:"blob"})},s=function(e){return i["a"].request({url:o+"/task/rollback/".concat(e),method:"POST"})},l=function(e){return i["a"].request({url:o+"/task",data:e,method:"POST",contentType:"multipart/form-data"})},c=function(e){return i["a"].request({url:o+"/taskPage",data:e,method:"POST"})},d=function(e){return i["a"].request({url:"/cms/channel/searchList",data:e,method:"post"})},u=function(e){return i["a"].request({url:"/cms/package/config/getTextChannel",data:e,method:"get"})}},a7d2:function(e,t,a){"use strict";a.r(t);a("caad"),a("b0c0"),a("ac1f"),a("841c");var i=function(){var e=this,t=e._self._c;return t("Card",[t("div",{staticClass:"search_head_i"},[t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("渠道商")]),t("Select",{attrs:{clearable:"",placeholder:"请选择渠道商",filterable:""},model:{value:e.corpId,callback:function(t){e.corpId=t},expression:"corpId"}},e._l(e.corpList,(function(a,i){return t("Option",{key:i,attrs:{value:a.corpId,title:a.corpName}},[e._v(e._s(a.corpName))])})),1)],1),t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("订单状态")]),t("Select",{attrs:{clearable:"",placeholder:"请选择订单状态",filterable:""},model:{value:e.orderStatus,callback:function(t){e.orderStatus=t},expression:"orderStatus"}},[t("Option",{attrs:{value:1}},[e._v("已下单")]),t("Option",{attrs:{value:2}},[e._v("已取消")]),t("Option",{attrs:{value:3}},[e._v("待付款")]),t("Option",{attrs:{value:4}},[e._v("付款已确认")]),t("Option",{attrs:{value:5}},[e._v("待发货")]),t("Option",{attrs:{value:6}},[e._v("发货中")]),t("Option",{attrs:{value:7}},[e._v("已发货")]),t("Option",{attrs:{value:8}},[e._v("发货失败")]),t("Option",{attrs:{value:9}},[e._v("付款未到账")]),t("Option",{attrs:{value:10}},[e._v("付款待确认")]),t("Option",{attrs:{value:11}},[e._v("回滚中")]),t("Option",{attrs:{value:12}},[e._v("回滚成功")]),t("Option",{attrs:{value:13}},[e._v("回滚失败")])],1)],1),t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("主卡形态")]),t("Select",{attrs:{clearable:"",placeholder:"请选择主卡形态",filterable:""},model:{value:e.cardForms,callback:function(t){e.cardForms=t},expression:"cardForms"}},[t("Option",{attrs:{value:1}},[e._v("普通卡(实体卡)")]),t("Option",{attrs:{value:2}},[e._v("Esim卡")]),t("Option",{attrs:{value:4}},[e._v("IMSI")])],1)],1),t("div",{staticClass:"search_box"},[t("span",{staticStyle:{"font-weight":"bold","text-align":"center",width:"180px"}},[e._v("CMLINK/定制卡")]),t("Select",{attrs:{clearable:"",placeholder:"请选择",filterable:""},model:{value:e.chargingMode,callback:function(t){e.chargingMode=t},expression:"chargingMode"}},[t("Option",{attrs:{value:2}},[e._v("CMLINK卡")]),t("Option",{attrs:{value:1}},[e._v("定制卡")])],1)],1),t("div",{staticClass:"search_box"},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:e.searchloading},on:{click:function(t){return e.search()}}},[e._v("搜索")])],1),t("div",{staticClass:"search_box"},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],attrs:{icon:"ios-cloud-download-outline",type:"success",loading:e.exportLoading},on:{click:function(t){return e.exportList()}}},[e._v("导出")])],1)]),t("Table",{ref:"selection",staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:e.columns,data:e.data,loading:e.loading},scopedSlots:e._u([{key:"action",fn:function(a){var i=a.row;a.index;return[t("div",{staticStyle:{padding:"10px 5px 5px 0"}},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"confirm",expression:"'confirm'"},{name:"show",rawName:"v-show",value:"1"==i.orderStatus&&"2"==i.chargingMode||"4"==i.orderStatus&&"1"==i.chargingMode,expression:"(row.orderStatus == '1' && row.chargingMode == '2') || (row.orderStatus == '4' && row.chargingMode == '1')"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"success",ghost:"",size:"small"},on:{click:function(t){return e.orderConfirmation(i)}}},[e._v("订单确认")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"cancel",expression:"'cancel'"},{name:"show",rawName:"v-show",value:[1,3,4].includes(+i.orderStatus),expression:"[1,3,4].includes(+row.orderStatus)"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"error",ghost:"",size:"small"},on:{click:function(t){return e.orderCancellation(i.orderId)}}},[e._v("订单取消")]),"1"==i.orderStatus&&"1"==i.chargingMode?t("Button",{directives:[{name:"has",rawName:"v-has",value:"createinvoice",expression:"'createinvoice'"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"info",ghost:"",size:"small"},on:{click:function(t){return e.showInvoiceView(i,"1")}}},[e._v("生成Invoice")]):e._e(),"3"==i.orderStatus&&"1"==i.chargingMode?t("Button",{directives:[{name:"has",rawName:"v-has",value:"createinvoiceAgain",expression:"'createinvoiceAgain'"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"info",ghost:"",size:"small"},on:{click:function(t){return e.showInvoiceView(i,"2")}}},[e._v("重新生成Invoice")]):e._e(),t("Button",{directives:[{name:"has",rawName:"v-has",value:"downloadinvoice",expression:"'downloadinvoice'"},{name:"show",rawName:"v-show",value:![1,2].includes(+i.orderStatus)&&"1"==i.chargingMode,expression:"![1,2].includes(+row.orderStatus) && row.chargingMode == '1'"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"warning",ghost:"",size:"small"},on:{click:function(t){return e.downloadFile("1",i.orderId)}}},[e._v("下载Invoice")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"paymentProof",expression:"'paymentProof'"},{name:"show",rawName:"v-show",value:![1,2,3].includes(+i.orderStatus)&&"1"==i.chargingMode,expression:"![1,2,3].includes(+row.orderStatus) && row.chargingMode == '1'"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"primary",ghost:"",size:"small"},on:{click:function(t){return e.downloadFile("2",i.orderId)}}},[e._v("下载付款证明")]),i.deliverFailPath?t("Button",{directives:[{name:"has",rawName:"v-has",value:"downloadFailFile",expression:"'downloadFailFile'"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"error",ghost:"",size:"small"},on:{click:function(t){return e.downloadFile("3",i.orderId)}}},[e._v("下载失败文件")]):e._e(),"5"==i.orderStatus||"12"==i.orderStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"delivery",expression:"'delivery'"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"success",ghost:"",size:"small"},on:{click:function(t){return e.delivery(i)}}},[e._v("发货")]):e._e(),"7"==i.orderStatus||"13"==i.orderStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"rollbackOrder",expression:"'rollbackOrder'"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"error",ghost:"",size:"small"},on:{click:function(t){return e.rollbackOrder(i)}}},[e._v("回滚")]):e._e(),"7"==i.orderStatus&&i.deliverCardfilePath?t("Button",{directives:[{name:"has",rawName:"v-has",value:"downloadDeliverFlie",expression:"'downloadDeliverFlie'"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"success",ghost:"",size:"small"},on:{click:function(t){return e.downloadFile("4",i.orderId)}}},[e._v("下载发货列表")]):e._e()],1)]}}])}),t("div",{staticStyle:{"margin-top":"15px"}},[t("Page",{attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1),t("Modal",{attrs:{title:"发货","footer-hide":!0,"mask-closable":!1,width:"450px"},on:{"on-cancel":e.cancelModal},model:{value:e.deliveryFlag,callback:function(t){e.deliveryFlag=t},expression:"deliveryFlag"}},[t("div",[t("Form",{ref:"formValidate",staticStyle:{"font-weight":"bold"},attrs:{model:e.formValidate,rules:e.ruleValidate,"label-width":100,"label-height":100,inline:""}},[t("FormItem",{staticStyle:{width:"400px"},attrs:{label:"合作模式",prop:"cooperationMode"}},[t("Select",{attrs:{disabled:""},model:{value:e.formValidate.cooperationMode,callback:function(t){e.$set(e.formValidate,"cooperationMode",t)},expression:"formValidate.cooperationMode"}},[t("Option",{attrs:{value:"1"}},[e._v("代销")]),t("Option",{attrs:{value:"2"}},[e._v("A2Z")])],1)],1),t("FormItem",{staticStyle:{width:"400px"},attrs:{label:"短信模板",prop:"smsTemplate"}},[t("Select",{attrs:{filterable:"",clearable:"",placeholder:"下拉选择短信模板"},model:{value:e.formValidate.smsTemplate,callback:function(t){e.$set(e.formValidate,"smsTemplate",t)},expression:"formValidate.smsTemplate"}},e._l(e.smsAll,(function(a,i){return t("Option",{key:a.templateId,attrs:{value:a.templateId,title:a.templateName}},[e._v(e._s(a.templateName))])})),1)],1),t("FormItem",{staticStyle:{width:"400px"},attrs:{label:"语言",prop:"language"}},[t("Select",{attrs:{clearable:"",placeholder:"请选择语言"},model:{value:e.formValidate.language,callback:function(t){e.$set(e.formValidate,"language",t)},expression:"formValidate.language"}},e._l(e.languageList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1),t("FormItem",{staticStyle:{width:"400px"},attrs:{label:"是否新加坡卡",prop:"singaporeCard"}},[t("Select",{attrs:{clearable:!0,placeholder:"请选择是否为新加坡卡"},model:{value:e.formValidate.singaporeCard,callback:function(t){e.$set(e.formValidate,"singaporeCard",t)},expression:"formValidate.singaporeCard"}},[t("Option",{attrs:{value:"1"}},[e._v("是")]),t("Option",{attrs:{value:"2"}},[e._v("否")])],1)],1),t("FormItem",{attrs:{label:"卡号列表",prop:"file"}},[t("div",{staticStyle:{display:"flex"}},[t("Upload",{ref:"upload",attrs:{action:e.uploadUrl,"on-success":e.fileSuccess,"on-error":e.handleError,"before-upload":e.handleBeforeUpload,"on-progress":e.fileUploading},model:{value:e.formValidate.file,callback:function(t){e.$set(e.formValidate,"file",t)},expression:"formValidate.file"}},[t("Button",{attrs:{icon:"ios-cloud-upload-outline"}},[e._v("上传ICCID列表")])],1),t("div",{staticStyle:{width:"250px","margin-left":"45px"}},[t("Button",{attrs:{type:"info",icon:"ios-download"},on:{click:e.downloadTemplate}},[e._v("下载模版文件")])],1)],1),e.file?t("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"310px"}},[t("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[t("span",[t("Icon",{attrs:{type:"ios-folder"}}),e._v(e._s(e.file.name)+"\n\t\t\t\t\t\t\t\t")],1),t("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:e.removeFile}})])]):e._e()]),t("FormItem",{staticStyle:{width:"400px"},attrs:{label:"物流公司",prop:"logisticCompany",rules:"1"==e.cardForm?e.ruleValidate.logisticCompany:[{required:!1}]}},[t("Input",{attrs:{placeholder:"请输入物流公司",clearable:""},model:{value:e.formValidate.logisticCompany,callback:function(t){e.$set(e.formValidate,"logisticCompany",t)},expression:"formValidate.logisticCompany"}})],1),t("FormItem",{staticStyle:{width:"400px"},attrs:{label:"物流编号",prop:"logistic",rules:"1"==e.cardForm?e.ruleValidate.logistic:[{required:!1}]}},[t("Input",{attrs:{placeholder:"请输入物流编号",clearable:""},model:{value:e.formValidate.logistic,callback:function(t){e.$set(e.formValidate,"logistic",t)},expression:"formValidate.logistic"}})],1),t("FormItem",{staticStyle:{width:"400px"},attrs:{label:"订单批次",prop:"orderBatch"}},[t("Input",{attrs:{placeholder:"请输入订单批次",clearable:""},model:{value:e.formValidate.orderBatch,callback:function(t){e.$set(e.formValidate,"orderBatch",t)},expression:"formValidate.orderBatch"}})],1)],1),t("div",{staticStyle:{"text-align":"center",margin:"4px 0"}},[t("Button",{staticStyle:{"margin-right":"30px"},on:{click:e.cancelModal}},[e._v("返回")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"deliverySubmit",expression:"'deliverySubmit'"},{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:e.submitLoading},on:{click:e.submit}},[e._v("确定")])],1)],1)]),t("Modal",{attrs:{title:"订单确认","footer-hide":!0,"mask-closable":!1,width:"450px"},on:{"on-cancel":e.cancelModal},model:{value:e.orderModal,callback:function(t){e.orderModal=t},expression:"orderModal"}},[e.spinShow?t("Spin",{attrs:{size:"large",fix:""}}):e._e(),t("Form",{ref:"formValidate2",staticStyle:{"font-weight":"bold"},attrs:{model:e.formValidate2,rules:e.ruleValidate2,"label-width":120,"label-height":100,inline:""}},["2"==e.orderBIndCooperationMode?t("FormItem",{staticStyle:{width:"400px"},attrs:{label:"流量计费规则",prop:"a2zRuleId"}},[t("Select",{attrs:{filterable:"",clearable:"",placeholder:"请选择流量计费规则"},model:{value:e.formValidate2.a2zRuleId,callback:function(t){e.$set(e.formValidate2,"a2zRuleId",t)},expression:"formValidate2.a2zRuleId"}},e._l(e.a2zRuleList,(function(a){return t("Option",{key:a.charging,attrs:{value:a.charging}},[e._v(e._s(a.chargingName)+"\n\t\t\t\t\t\t")])})),1)],1):e._e(),"2"==e.orderBIndCooperationMode&&"1"==e.allowCreatePackage?t("FormItem",{staticStyle:{width:"400px"},attrs:{label:"国家卡池关联组",prop:"groupId"}},[t("Select",{attrs:{filterable:"",clearable:"",placeholder:"请选择国家卡池关联组"},model:{value:e.formValidate2.groupId,callback:function(t){e.$set(e.formValidate2,"groupId",t)},expression:"formValidate2.groupId"}},e._l(e.groupIdList,(function(a){return t("Option",{key:a.groupId,attrs:{value:a.groupId,title:a.groupName}},[e._v(e._s(a.groupName.length>30?a.groupName.substring(0,30)+"…":a.groupName))])})),1)],1):e._e(),"4"==e.cardForm?t("FormItem",{staticStyle:{width:"400px"},attrs:{label:"IMSI费规则",prop:"imsiFee"}},[t("Select",{attrs:{filterable:"",clearable:"",placeholder:"请选择IMSI费规则"},model:{value:e.formValidate2.imsiFee,callback:function(t){e.$set(e.formValidate2,"imsiFee",t)},expression:"formValidate2.imsiFee"}},e._l(e.imsiFeeList,(function(a){return t("Option",{key:a.imsi,attrs:{value:a.imsi}},[e._v(e._s(a.imsiName))])})),1)],1):e._e()],1),t("div",{staticStyle:{"text-align":"center",margin:"4px 0"}},[t("Button",{staticStyle:{"margin-right":"30px"},on:{click:e.cancelModal}},[e._v("返回")]),t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:e.submitOrderLoading},on:{click:e.submitOrder}},[e._v("确定")])],1)],1),t("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:e.modelColumns,data:e.modelData}}),t("Modal",{attrs:{title:"生成发票预览",width:"800px",styles:{top:"10px"}},on:{"on-ok":e.createInvoice,"on-cancel":e.cancelInvoice},model:{value:e.invoiceModel,callback:function(t){e.invoiceModel=t},expression:"invoiceModel"}},[t("Card",{attrs:{width:"750px"}},[t("invoiceCard",{ref:"dataForm",attrs:{customerName:e.invoiceInfo.customerName,InvoiceNo:e.invoiceInfo.InvoiceNo,address:e.invoiceInfo.address,columns:e.invoiceColumns,FileTitle:e.invoiceInfo.FileTitle,data:e.invoiceInfo.data,InvoiceDate:e.invoiceInfo.InvoiceDate,InvoiceDescValue:e.invoiceInfo.InvoiceDesc,currency:e.invoiceInfo.currency,invoiceForm:e.invoiceForm,quantityValue:e.quantityValue},on:{getdesc:e.getdesc,"update:invoiceForm":function(t){e.invoiceForm=t},"update:invoice-form":function(t){e.invoiceForm=t}}})],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.cancelInvoice}},[e._v("取消")]),t("Button",{attrs:{type:"primary",loading:e.Invoiceloading},on:{click:e.createInvoice}},[e._v("生成Invoice")])],1)],1),t("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1)},o=[],r=a("3835"),n=(a("d9e2"),a("d81d"),a("4fadc"),a("d3b7"),a("00b4"),a("25f0"),a("2532"),a("3ca3"),a("466d"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"_img_view"},[t("img",{staticClass:"img",attrs:{src:e.heard_src,width:"100%",height:"100%"}})]),t("div",{staticStyle:{width:"100%",margin:"20px 0"}},[t("div",{staticStyle:{display:"flex","flex-wrap":"wrap","align-items":"flex-start","justify-content":"space-between"}},[t("div",{staticClass:"color_front"},[e._v(e._s(e.customerName))]),t("div",{staticClass:"color_fronts"},[t("span",{staticClass:"fontBlod"},[e._v("Invoice No:")]),e._v(" "+e._s(e.InvoiceNo))]),t("div",{staticClass:"color_front"},[e._v(e._s(e.address))]),t("div",{staticClass:"color_fronts"},[t("span",{staticClass:"fontBlod"},[e._v("Invoice Date:")]),e._v(" "+e._s(e.InvoiceDate))]),e._m(0),t("div",{staticStyle:{width:"100%",padding:"25px","text-align":"center","font-size":"25px","font-weight":"600",color:"#000000"}},[e._v("INVOICE")]),t("div",{staticStyle:{width:"100%",padding:"5px","text-align":"right","font-size":"17px","font-weight":"600",color:"#000000"}},[e._v("\n\t\t\t  Payment Currency:"+e._s(e.currency)+"\n\t\t\t")]),t("div",{staticStyle:{width:"100%"}},[t("Form",{ref:"invoiceForm",attrs:{model:e.invoiceForm,rules:e.rule}},[t("Table",{attrs:{border:"",columns:e.columns,data:e.data},scopedSlots:e._u([{key:"description",fn:function(a){a.row,a.index;return[t("FormItem",{attrs:{prop:"productName"}},[t("Input",{staticStyle:{width:"200px","margin-top":"15px"},attrs:{placeholder:"请输入productName",clearable:!0},model:{value:e.invoiceForm.productName,callback:function(t){e.$set(e.invoiceForm,"productName",t)},expression:"invoiceForm.productName"}})],1)]}},{key:"listedPrice",fn:function(a){a.row,a.index;return[t("FormItem",{attrs:{prop:"listedPrice"}},[t("Input",{staticStyle:{width:"200px","margin-top":"15px"},attrs:{placeholder:"请输入listedPrice",clearable:!0},on:{"on-blur":e.getAmount},model:{value:e.invoiceForm.listedPrice,callback:function(t){e.$set(e.invoiceForm,"listedPrice",t)},expression:"invoiceForm.listedPrice"}})],1)]}},{key:"amount",fn:function(a){a.row,a.index;return[t("FormItem",{attrs:{prop:"amount"}},[t("Input",{staticStyle:{width:"100px","margin-top":"15px"},attrs:{disabled:!0,placeholder:""},model:{value:e.InvoiceAmount,callback:function(t){e.InvoiceAmount=t},expression:"InvoiceAmount"}})],1)]}}])})],1)],1),t("div",{staticStyle:{width:"100%",padding:"10px",display:"flex","justify-content":"space-between","font-weight":"600",color:"#000"}},[t("p",[e._v("Total Amount(Tax Exclusive)")]),t("p",[e._v(e._s(e.InvoiceAmount))])]),t("Input",{staticClass:"input-call",staticStyle:{"margin-top":"50px"},attrs:{type:"textarea",autosize:!0,placeholder:"发票说明"},model:{value:e.InvoiceDescValue,callback:function(t){e.InvoiceDescValue=t},expression:"InvoiceDescValue"}})],1)])])}),s=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"color_fronts",staticStyle:{"margin-left":"60%"}},[t("span",{staticClass:"fontBlod"},[e._v("Page ")]),e._v("1"),t("span",[e._v(" of ")]),e._v("1")])}],l=(a("a9e3"),a("b680"),{data:function(){return{heard_src:a("82c4"),InvoiceAmount:"",rule:{listedPrice:[{required:!0,message:"请输入Listed Price",trigger:"blur"},{validator:function(e,t,a){var i=/^(?:(?:[1-9]\d{0,7})(?:\.\d{1,2})?|0\.(?:[1-9]\d?|0[1-9]))$/;return i.test(t)},message:"最高支持8位整数和2位小数正数"}]}}},props:{customerName:{type:String,default:""},AccountNo:{type:String,default:""},address:{type:String,default:""},InvoiceNo:{type:String,default:""},InvoiceDate:{type:String,default:""},currency:{type:String,default:""},InvoiceDescValue:{type:String,default:""},quantityValue:{type:Number,default:""},invoiceForm:{},columns:{type:Array,default:function(){return[]}},data:{type:Array,default:function(){return[]}}},watch:{InvoiceDescValue:function(e,t){this.$emit("getdesc",e)}},computed:{getAmount:function(){if(this.invoiceForm.listedPrice){var e=(this.invoiceForm.listedPrice*this.quantityValue).toFixed(2),t=new Intl.NumberFormat("en-US",{style:"decimal",minimumFractionDigits:2,maximumFractionDigits:2});return this.InvoiceAmount=t.format(parseFloat(e)),this.InvoiceAmount}return this.InvoiceAmount="",this.InvoiceAmount}}}),c=l,d=(a("6410"),a("2877")),u=Object(d["a"])(c,n,s,!1,null,null,null),m=u.exports,p=a("951d"),g=a("ba27"),f=a("6dfa"),v=a("0ae5"),h={mixins:[v["a"]],components:{invoiceCard:m},data:function(){var e=this,t=function(t,a,i){e.uploadList&&0===e.uploadList.length?i(new Error("请上传文件")):i()};return{total:0,currentPage:1,corpId:"",orderStatus:"",cardForms:"",chargingMode:"",cardForm:"",orderId:"",uploadUrl:"",file:null,id:"",address:"",orderUserId:"",InvoiceAmount:"",quantityValue:0,invoiceNo:"",invoiceType:"",orderBIndCooperationMode:"",orderBIndId:"",allowCreatePackage:"",default:"*All the above listed prices do not include any Tax. \n\nPayment Instruction\n\nPlease remit payment to beneficiary China Mobile International Limited by telegraph transfer.\n\nBeneficiary Name: China Mobile International Limited\nBeneficiary Address: Level 30, Tower 1, Kowloon Commerce Centre, No. 51 Kwai Cheong Road, Kwai Chung. N.T. Hong Kon\nBank Name: The Hongkong and Shanghai Banking Corporation Limited\nBank Address: HSBC Main Building, 1 Queen's Road Central, Hong Kong\nSwift Code: HSBCHKHHHKH\nAccount No.:**********-838\n\n*Please quote our Invoice No. with your payment instructions to the bank upon remittance. \n*Please email remittance <NAME_EMAIL> for update of your account.\n\nThis computer generated document requires no signature.",loading:!1,searchloading:!1,submitLoading:!1,invoiceModel:!1,Invoiceloading:!1,deliveryFlag:!1,orderModal:!1,submitOrderLoading:!1,spinShow:!1,corpList:[],smsAll:[],languageList:[{value:"1",label:"繁体中文"},{value:"2",label:"英文"},{value:"3",label:"简体中文"}],uploadList:[],a2zRuleList:[],groupIdList:[],imsiFeeList:[],data:[],columns:[{title:"订单ID",key:"orderId",minWidth:180,align:"center",tooltip:!0},{title:"订单时间",key:"createTime",minWidth:150,align:"center",tooltip:!0,render:function(e,t){var a=t.row,i="";if(a.createTime){var o=new Date(a.createTime),r=o.getFullYear(),n=o.getMonth()+1<10?"0"+(o.getMonth()+1):o.getMonth()+1,s=o.getDate()<10?"0"+o.getDate():o.getDate(),l=o.getHours()<10?"0"+o.getHours():o.getHours(),c=o.getMinutes()<10?"0"+o.getMinutes():o.getMinutes(),d=o.getSeconds()<10?"0"+o.getSeconds():o.getSeconds();i=r+"-"+n+"-"+s+" "+l+":"+c+":"+d}else i="";return e("label",i)}},{title:"渠道商",key:"corpName",minWidth:150,align:"center",tooltip:!0},{title:"主卡形态",key:"cardForm",minWidth:140,align:"center",tooltip:!0,render:function(e,t){var a=t.row,i="1"==a.cardForm?"普通卡（实体卡）":"2"==a.cardForm?"Esim卡":"4"==a.cardForm?"IMSI":"";return e("label",i)}},{title:"卡片类型",key:"cooperationMode",minWidth:135,align:"center",tooltip:!0,render:function(e,t){var a=t.row,i="1"==a.cooperationMode?"代销":"2"==a.cooperationMode?"A~Z":"";return e("label",i)}},{title:"CMLINK/定制卡",key:"chargingMode",minWidth:150,align:"center",tooltip:!0,render:function(e,t){var a=t.row,i=1==a.chargingMode?"定制卡":2==a.chargingMode?"CMLINK卡":"";return e("label",i)}},{title:"卡片数量",key:"count",minWidth:120,align:"center",tooltip:!0},{title:"订单状态",key:"orderStatus",align:"center",minWidth:100,render:function(e,t){var a=t.row,i="1"===a.orderStatus?"#2b85e4":"2"===a.orderStatus?"#00aa00":"3"===a.orderStatus?"#ff0000":"4"===a.orderStatus?"#69e457":"5"===a.orderStatus?"#e47a49":"6"===a.orderStatus?"#dd74e4":"7"===a.orderStatus?"#24cbe4":"8"===a.orderStatus?"#7009e4":"9"===a.orderStatus?"#e4b809":"10"===a.orderStatus?"#ff9900":"11"===a.orderStatus?"#e4e424":"12"===a.orderStatus?"#24e424":"13"===a.orderStatus?"#e42424":"",o="1"===a.orderStatus?"已下单":"2"===a.orderStatus?"已取消":"3"===a.orderStatus?"待付款":"4"===a.orderStatus?"付款已确认":"5"===a.orderStatus?"待发货":"6"===a.orderStatus?"发货中":"7"===a.orderStatus?"已发货":"8"===a.orderStatus?"发货失败":"9"===a.orderStatus?"付款未到账":"10"===a.orderStatus?"付款待确认":"11"===a.orderStatus?"回滚中":"12"===a.orderStatus?"回滚成功":"13"===a.orderStatus?"存在回滚失败":"";return e("label",{style:{color:i}},o)}},{title:"国家",key:"countryName",minWidth:120,align:"center",tooltip:!0},{title:"邮编",key:"postcode",minWidth:120,align:"center",tooltip:!0},{title:"收货地址",key:"address",minWidth:120,align:"center",tooltip:!0},{title:"收件人",key:"addressee",minWidth:120,align:"center",tooltip:!0},{title:"联系电话",key:"phoneNumber",minWidth:120,align:"center",tooltip:!0},{title:"物流公司",key:"logisticCompany",minWidth:120,align:"center",tooltip:!0},{title:"物流编号",key:"logistic",minWidth:130,align:"center",tooltip:!0},{title:"订单批次",key:"orderBatch",minWidth:120,align:"center",tooltip:!0},{title:"生成Invoice时间",key:"invoiceTime",minWidth:150,align:"center",tooltip:!0,render:function(e,t){var a=t.row,i="";if(a.invoiceTime){var o=new Date(a.invoiceTime),r=o.getFullYear(),n=o.getMonth()+1<10?"0"+(o.getMonth()+1):o.getMonth()+1,s=o.getDate()<10?"0"+o.getDate():o.getDate(),l=o.getHours()<10?"0"+o.getHours():o.getHours(),c=o.getMinutes()<10?"0"+o.getMinutes():o.getMinutes(),d=o.getSeconds()<10?"0"+o.getSeconds():o.getSeconds();i=r+"-"+n+"-"+s+" "+l+":"+c+":"+d}else i="";return e("label",i)}},{title:"上传付款证明时间",key:"paymentProofsTime",minWidth:150,align:"center",tooltip:!0,render:function(e,t){var a=t.row,i="";if(a.paymentProofsTime){var o=new Date(a.paymentProofsTime),r=o.getFullYear(),n=o.getMonth()+1<10?"0"+(o.getMonth()+1):o.getMonth()+1,s=o.getDate()<10?"0"+o.getDate():o.getDate(),l=o.getHours()<10?"0"+o.getHours():o.getHours(),c=o.getMinutes()<10?"0"+o.getMinutes():o.getMinutes(),d=o.getSeconds()<10?"0"+o.getSeconds():o.getSeconds();i=r+"-"+n+"-"+s+" "+l+":"+c+":"+d}else i="";return e("label",i)}},{title:"订单确认时间",key:"confirmTime",minWidth:150,align:"center",tooltip:!0,render:function(e,t){var a=t.row,i="";if(a.confirmTime){var o=new Date(a.confirmTime),r=o.getFullYear(),n=o.getMonth()+1<10?"0"+(o.getMonth()+1):o.getMonth()+1,s=o.getDate()<10?"0"+o.getDate():o.getDate(),l=o.getHours()<10?"0"+o.getHours():o.getHours(),c=o.getMinutes()<10?"0"+o.getMinutes():o.getMinutes(),d=o.getSeconds()<10?"0"+o.getSeconds():o.getSeconds();i=r+"-"+n+"-"+s+" "+l+":"+c+":"+d}else i="";return e("label",i)}},{title:"发货时间",key:"deliverTime",minWidth:150,align:"center",tooltip:!0,render:function(e,t){var a=t.row,i="";if(a.deliverTime){var o=new Date(a.deliverTime),r=o.getFullYear(),n=o.getMonth()+1<10?"0"+(o.getMonth()+1):o.getMonth()+1,s=o.getDate()<10?"0"+o.getDate():o.getDate(),l=o.getHours()<10?"0"+o.getHours():o.getHours(),c=o.getMinutes()<10?"0"+o.getMinutes():o.getMinutes(),d=o.getSeconds()<10?"0"+o.getSeconds():o.getSeconds();i=r+"-"+n+"-"+s+" "+l+":"+c+":"+d}else i="";return e("label",i)}},{title:"短信模板",key:"templateName",minWidth:160,align:"center",tooltip:!0},{title:"语言",key:"language",minWidth:160,align:"center",tooltip:!0,render:function(e,t){var a=t.row,i=1==a.language?"繁体中文":2==a.language?"英文":3==a.language?"简体中文":"";return e("label",i)}},{title:"产品包装",key:"cardPackage",minWidth:160,align:"center",tooltip:!0,render:function(e,t){var a=t.row,i=1==a.cardPackage?"裸卡":2==a.cardPackage?"包装卡":"";return e("label",i)}},{title:"操作",slot:"action",minWidth:330,align:"center",fixed:"right"}],formValidate:{cooperationMode:"",smsTemplate:"",language:"",singaporeCard:"",file:"",logisticCompany:"",logistic:"",orderBatch:""},formValidate2:{a2zRuleId:"",groupId:"",imsiFee:""},ruleValidate:{smsTemplate:[{required:!0,message:"短信模板不能为空"}],language:[{required:!0,message:"语言不能为空"}],singaporeCard:[{required:!0,message:"是否新加坡卡不能为空"}],file:[{required:!0,validator:t,trigger:"change"}],logisticCompany:[{required:!0,message:"物流公司不能为空"}],logistic:[{required:!0,message:"物流编号不能为空"}],orderBatch:[{required:!0,message:"订单批次不能为空"},{validator:function(e,t,a){t&&t.includes("&")?a(new Error("订单批次不能包含&符号")):a()},trigger:"blur"}]},ruleValidate2:{a2zRuleId:[{required:!0,message:"计费规则不能为空"}],groupId:[{required:!0,message:"国家卡池关联组不能为空"}],imsiFee:[{required:!0,message:"IMSI费不能为空"}]},modelData:[{ICCID:"********"}],modelColumns:[{title:"ICCID",key:"ICCID"}],invoiceForm:{productName:"GDS-SIM card fees",listedPrice:""},invoiceInfo:{customerName:"",InvoiceNo:"",address:"",InvoiceDate:"",currency:"",InvoiceDesc:"*All the above listed prices do not include any Tax. \n\nPayment Instruction\n\nPlease remit payment to beneficiary China Mobile International Limited by telegraph transfer.\n\nBeneficiary Name: China Mobile International Limited\nBeneficiary Address: Level 30, Tower 1, Kowloon Commerce Centre, No. 51 Kwai Cheong Road, Kwai Chung. N.T. Hong Kon\nBank Name: The Hongkong and Shanghai Banking Corporation Limited\nBank Address: HSBC Main Building, 1 Queen's Road Central, Hong Kong\nSwift Code: HSBCHKHHHKH\nAccount No.:**********-838\n\n*Please quote our Invoice No. with your payment instructions to the bank upon remittance. \n*Please email remittance <NAME_EMAIL> for update of your account.\n\nThis computer generated document requires no signature.",data:[{description:"",listedPrice:"",quantity:"",amount:""}]},invoiceColumns:[{title:"Product Name",align:"center",width:230,slot:"description",renderHeader:function(e,t){return e("div",[e("span",{style:{color:"red"}},"*"),e("span"," Product Name")])}},{title:"Listed Price",align:"center",width:240,slot:"listedPrice",renderHeader:function(e,t){return e("div",[e("span",{style:{color:"red"}},"*"),e("span"," Listed Price")])}},{title:"Quantity",align:"center",width:131,key:"quantity",render:function(e,t){var a=t.row,i=a.quantity,o=new Intl.NumberFormat("en-US",{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}),r=o.format(parseFloat(i));return e("label",r)}},{title:"Amount",align:"center",width:131,slot:"amount"}],exportLoading:!1}},mounted:function(){this.getCorpList(),this.goPageFirst(1)},methods:{goPageFirst:function(e){var t=this;this.data=[],this.loading=!0;var a=this;Object(g["i"])({pageNum:e,pageSize:10,orderUserId:this.corpId,status:this.orderStatus,cardForms:this.cardForms,chargingMode:this.chargingMode}).then((function(i){"0000"==i.code&&(a.loading=!1,t.searchloading=!1,t.page=e,t.currentPage=e,t.data=i.data,t.total=i.count)})).catch((function(e){console.error(e)})).finally((function(){a.loading=!1,t.searchloading=!1}))},search:function(){this.searchloading=!0,this.goPageFirst(1)},goPage:function(e){this.goPageFirst(e)},exportList:function(){var e=this;console.log("导出"),this.exportLoading=!0,Object(g["b"])({pageNum:-1,pageSize:-1,orderUserId:this.corpId,status:this.orderStatus,cardForms:this.cardForms,chargingMode:this.chargingMode}).then((function(t){e.exportLoading=!1;var a=t.data,i=decodeURI(t.headers["content-disposition"].match(/=(.*)$/)[1]);if("download"in document.createElement("a")){var o=e.$refs.downloadLink,r=URL.createObjectURL(a);o.download=i,o.href=r,o.click(),URL.revokeObjectURL(r)}else navigator.msSaveBlob(a,i);e.$Notice.success({title:"操作提示",desc:"导出成功！"})})).catch((function(t){e.exportLoading=!1,console.error(t)}))},orderCancellation:function(e){var t=this;this.$Modal.confirm({title:"订单取消？",onOk:function(){Object(g["c"])(e).then((function(e){"0000"===e.code&&t.$Notice.success({title:"操作提示",desc:"订单取消成功！"}),t.goPageFirst(1)}))}})},orderConfirmation:function(e){var t=this;"1"==e.cooperationMode&&"4"!=e.cardForm?this.$Modal.confirm({title:"订单确认？",onOk:function(){Object(g["d"])({id:e.orderId,freeimsiId:""}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提醒",desc:"操作成功"})})).catch((function(e){console.error(e)})).finally((function(){t.goPageFirst(1)}))}}):(this.orderModal=!0,this.spinShow=!0,this.orderBIndCooperationMode=e.cooperationMode,this.orderBIndId=e.orderId,this.cardForm=e.cardForm,this.allowCreatePackage=e.allowCreatePackage,this.getInfoOrder(e.orderUserId,e.cooperationMode))},submitOrder:function(){var e=this;this.$refs["formValidate2"].validate((function(t){if(t){var a={id:e.orderBIndId,freeimsiId:e.formValidate2.imsiFee},i={id:e.orderBIndId,ruleId:e.formValidate2.a2zRuleId,groupId:e.formValidate2.groupId?e.formValidate2.groupId:"",freeimsiId:e.formValidate2.imsiFee},o="2"==e.orderBIndCooperationMode?i:a;e.submitOrderLoading=!0,Object(g["d"])(o).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提醒",desc:"操作成功"}),e.submitOrderLoading=!1,e.cancelModal()})).catch((function(e){console.error(e)})).finally((function(){e.submitOrderLoading=!1,e.goPageFirst(1)}))}}))},downloadFile:function(e,t){var a=this;Object(g["h"])({id:t,type:e}).then((function(e){var t=e.data,i=decodeURIComponent(escape(e.headers["content-disposition"].match(/=(.*)$/)[1]));if("download"in document.createElement("a")){var o=a.$refs.downloadLink,r=URL.createObjectURL(t);o.download=i,o.href=r,o.click(),URL.revokeObjectURL(r)}else navigator.msSaveBlob(t,i)})).catch()},rollbackOrder:function(e){var t=this;this.$Modal.confirm({title:"提示",content:"确定执行回滚操作吗？",onOk:function(){Object(g["j"])({orderId:e.orderId,retry:"13"==e.orderStatus?"1":""}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"})})).catch((function(e){console.error(e)})).finally((function(){t.goPageFirst(1)}))},onCancel:function(){}})},getdesc:function(e){this.invoiceInfo.InvoiceDesc=e},showInvoiceView:function(e,t){this.invoiceType=t,this.orderId=e.orderId,this.orderUserId=e.orderUserId,this.id=e.id,this.createInvoiceNo(e.orderId,e.orderUserId)},createInvoice:function(){var e=this;this.invoiceInfo.InvoiceDesc?this.$refs.dataForm.$refs.invoiceForm.validate((function(t){if(t){var a="1"==e.invoiceType?g["e"]:g["f"];e.Invoiceloading=!0,a({id:e.orderId,corpId:e.orderUserId,desc:e.invoiceInfo.InvoiceDesc,productName:e.invoiceForm.productName,listedPrice:e.invoiceForm.listedPrice,invoiceNo:e.invoiceNo}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提示",desc:"发票生成成功"}),e.$refs.dataForm.$refs.invoiceForm.resetFields(),e.invoiceModel=!1,e.Invoiceloading=!1,e.goPageFirst(1)})).catch((function(t){e.Invoiceloading=!1,console.log(t)}))}})):this.$Message.error("发票说明不能为空")},createInvoiceNo:function(e,t){var a=this;Object(g["g"])({orderId:e,corpId:t}).then((function(e){if(!e||"0000"!=e.code)throw e;a.quantityValue=e.data.quantity,a.invoiceNo=e.data.invoiceNo;var t=new Date,i=t.getFullYear(),o=t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1,r=t.getDate()<10?"0"+t.getDate():t.getDate();a.invoiceInfo={customerName:e.data.companyName,address:e.data.companyAddress,currency:e.data.currency,InvoiceNo:e.data.invoiceNo,InvoiceDate:i+"-"+o+"-"+r,InvoiceDesc:a.default,data:[{quantity:e.data.quantity}]},a.invoiceForm={productName:"GDS-SIM card fees"},a.invoiceModel=!0})).catch((function(e){console.log(e)}))},cancelInvoice:function(){this.id=null,this.invoiceModel=!1,this.invoiceInfo=[],this.$refs.dataForm.$refs.invoiceForm.resetFields()},delivery:function(e){this.getChannelSmsTemplate(e.orderUserId,e.cooperationMode),this.formValidate.cooperationMode=e.cooperationMode,this.formValidate.language=e.language,this.formValidate.smsTemplate=e.templateId.toString(),this.cardForm=e.cardForm,this.orderId=e.orderId,this.deliveryFlag=!0},cancelModal:function(){this.$refs["formValidate"].resetFields(),this.file=null,this.deliveryFlag=!1,this.formValidate2.a2zRuleId="",this.formValidate2.groupId="",this.formValidate2.imsiFee="",this.$refs["formValidate2"].resetFields(),this.orderModal=!1},submit:function(){var e=this;this.$refs["formValidate"].validate((function(t){if(t){e.submitLoading=!0;var a=new FormData;a.append("id",e.orderId),a.append("iccidFile",e.file),a.append("smsTemplate",e.formValidate.smsTemplate),a.append("language",e.formValidate.language),a.append("singaporeCard",e.formValidate.singaporeCard),a.append("logisticCompany",e.formValidate.logisticCompany),a.append("logistic",e.formValidate.logistic),a.append("orderBatch",e.formValidate.orderBatch),Object(g["k"])(a).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提醒",desc:"操作成功"}),e.submitLoading=!1,e.uploadList=[],e.cancelModal(),e.goPageFirst(1)})).catch((function(t){console.error(t),e.submitLoading=!1,e.goPageFirst(1)})).finally((function(){}))}}))},fileSuccess:function(e,t,a){this.message="请先下载模板文件，并按格式填写后上传"},handleError:function(e,t){var a=this;setTimeout((function(){a.uploading=!1,a.$Notice.warning({title:"错误提示",desc:"上传失败!"})}),3e3)},handleBeforeUpload:function(e,t){return/^.+(\.csv)$/.test(e.name)?(this.file=e,this.uploadList=t):this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+e.name+" 格式不正确，请上传.csv格式文件"}),!1},fileUploading:function(e,t,a){this.message="文件上传中、待进度条消失后再操作"},removeFile:function(){this.file=""},downloadTemplate:function(){this.$refs.modelTable.exportCsv({filename:"ICCID列表",columns:this.modelColumns,data:this.modelData})},getCorpList:function(){var e=this;Object(p["e"])({type:1,status:1,checkStatus:2}).then((function(t){if(!t||"0000"!=t.code)throw t;e.corpList=t.data})).catch((function(e){})).finally((function(){}))},getChannelSmsTemplate:function(e,t){var a=this;Object(f["t"])({corpId:e,cooperationMode:t}).then((function(e){"0000"===e.code&&(a.smsAll=e.data)})).catch((function(e){console.log(e)})).finally((function(){}))},getInfoOrder:function(e,t){var a=this;Object(g["a"])({corpId:e,cooperationMode:t}).then((function(e){if("0000"!==e.code)throw e;a.a2zRuleList=Object.entries(e.data.chargings).map((function(e){var t=Object(r["a"])(e,2),a=t[0],i=t[1];return{charging:a,chargingName:i}})),a.groupIdList=Object.entries(e.data.groupIds).map((function(e){var t=Object(r["a"])(e,2),a=t[0],i=t[1];return{groupId:a,groupName:i}})),a.imsiFeeList=Object.entries(e.data.freeImsiList).map((function(e){var t=Object(r["a"])(e,2),a=t[0],i=t[1];return{imsi:a,imsiName:i}})),1==a.a2zRuleList.length&&(a.formValidate2.a2zRuleId=a.a2zRuleList[0].charging),"1"==a.allowCreatePackage&&1==a.groupIdList.length&&(a.formValidate2.groupId=a.groupIdList[0].groupId),"4"==a.cardForm&&1==a.imsiFeeList.length&&(a.formValidate2.imsiFee=a.imsiFeeList[0].imsi)})).catch((function(e){console.log(e)})).finally((function(){a.spinShow=!1}))}}},I=h,y=(a("7f6b"),Object(d["a"])(I,i,o,!1,null,"ede6dd74",null));t["default"]=y.exports},b680:function(e,t,a){"use strict";var i=a("23e7"),o=a("e330"),r=a("5926"),n=a("408a"),s=a("1148"),l=a("d039"),c=RangeError,d=String,u=Math.floor,m=o(s),p=o("".slice),g=o(1..toFixed),f=function(e,t,a){return 0===t?a:t%2===1?f(e,t-1,a*e):f(e*e,t/2,a)},v=function(e){var t=0,a=e;while(a>=4096)t+=12,a/=4096;while(a>=2)t+=1,a/=2;return t},h=function(e,t,a){var i=-1,o=a;while(++i<6)o+=t*e[i],e[i]=o%1e7,o=u(o/1e7)},I=function(e,t){var a=6,i=0;while(--a>=0)i+=e[a],e[a]=u(i/t),i=i%t*1e7},y=function(e){var t=6,a="";while(--t>=0)if(""!==a||0===t||0!==e[t]){var i=d(e[t]);a=""===a?i:a+m("0",7-i.length)+i}return a},b=l((function(){return"0.000"!==g(8e-5,3)||"1"!==g(.9,0)||"1.25"!==g(1.255,2)||"1000000000000000128"!==g(0xde0b6b3a7640080,0)}))||!l((function(){g({})}));i({target:"Number",proto:!0,forced:b},{toFixed:function(e){var t,a,i,o,s=n(this),l=r(e),u=[0,0,0,0,0,0],g="",b="0";if(l<0||l>20)throw new c("Incorrect fraction digits");if(s!==s)return"NaN";if(s<=-1e21||s>=1e21)return d(s);if(s<0&&(g="-",s=-s),s>1e-21)if(t=v(s*f(2,69,1))-69,a=t<0?s*f(2,-t,1):s/f(2,t,1),a*=4503599627370496,t=52-t,t>0){h(u,0,a),i=l;while(i>=7)h(u,1e7,0),i-=7;h(u,f(10,i,1),0),i=t-1;while(i>=23)I(u,1<<23),i-=23;I(u,1<<i),h(u,1,1),I(u,2),b=y(u)}else h(u,0,a),h(u,1<<-t,0),b=y(u)+m("0",l);return l>0?(o=b.length,b=g+(o<=l?"0."+m("0",l-o)+b:p(b,0,o-l)+"."+p(b,o-l))):b=g+b,b}})},ba27:function(e,t,a){"use strict";a.d(t,"i",(function(){return r})),a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return s})),a.d(t,"h",(function(){return l})),a.d(t,"e",(function(){return c})),a.d(t,"f",(function(){return d})),a.d(t,"k",(function(){return u})),a.d(t,"g",(function(){return m})),a.d(t,"a",(function(){return p})),a.d(t,"j",(function(){return g})),a.d(t,"b",(function(){return f}));var i=a("66df"),o=(a("1157"),"/order/blankCardOrder"),r=function(e){return i["a"].request({url:o+"/getOrder",params:e,method:"get"})},n=function(e){return i["a"].request({url:o+"/comfirmOrder/"+e.id,data:e,method:"put"})},s=function(e){return i["a"].request({url:o+"/cancelOrder/".concat(e),method:"put"})},l=function(e){return i["a"].request({url:o+"/download",params:e,method:"get",responseType:"blob"})},c=function(e){return i["a"].request({url:o+"/generateInvoice",data:e,method:"post"})},d=function(e){return i["a"].request({url:o+"/regenerateInvoice",data:e,method:"post"})},u=function(e){return i["a"].request({url:o+"/deliver",data:e,method:"put",contentType:"multipart/form-data"})},m=function(e){return i["a"].request({url:o+"/getInvoiceInfo",params:e,method:"get"})},p=function(e){return i["a"].request({url:"cms/channel/getInfo4Order",params:e,method:"get"})},g=function(e){return i["a"].request({url:o+"/rollback",params:e,method:"put"})},f=function(e){return i["a"].request({url:o+"/export",data:e,method:"post",responseType:"blob"})}}}]);