(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-313b4a34"],{1684:function(t,e,a){"use strict";a("44e5")},"198b":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("Card",{staticStyle:{width:"100%",padiing:"16px"}},[e("Form",{attrs:{inline:""}},[e("FormItem",[e("Button",{staticStyle:{margin:"0 2px"},attrs:{type:"info"},on:{click:t.SMSAdd}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("Icon",{attrs:{type:"md-add"}}),t._v(" 新增")],1)])],1),e("FormItem",[e("Button",{directives:[{name:"has",rawName:"v-has",value:"batchDelete",expression:"'batchDelete'"}],staticStyle:{margin:"0 2px"},attrs:{type:"error"},on:{click:t.deleteList}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("Icon",{attrs:{type:"ios-trash"}}),t._v(" 批量删除")],1)])],1)],1),e("div",[e("Table",{ref:"selection",attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.tableLoading},on:{"on-selection-change":t.handleRowChange},scopedSlots:t._u([{key:"action",fn:function(a){var n=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(e){return t.SMSCommon(n,"Update")}}},[t._v("编辑")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],attrs:{type:"error",size:"small"},on:{click:function(e){return t.SMSDel(n.id)}}},[t._v("删除")])]}}])}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage}})],1),e("Modal",{attrs:{title:t.SMSTitle,"footer-hide":!0,"mask-closable":!1,width:"500px"},on:{"on-cancel":function(e){return t.reset("editObj")}},model:{value:t.SMSEditFlag,callback:function(e){t.SMSEditFlag=e},expression:"SMSEditFlag"}},[e("div",{staticStyle:{padding:"0 16px"}},[e("Form",{ref:"editObj",attrs:{model:t.editObj,rules:t.ruleEditValidate,"label-position":"top"}},[e("FormItem",{attrs:{label:"客服短信名称",prop:"templateName"}},[e("Input",{attrs:{clearable:!0,placeholder:"请输入客服短信名称",type:"text"},model:{value:t.editObj.templateName,callback:function(e){t.$set(t.editObj,"templateName",e)},expression:"editObj.templateName"}})],1),e("FormItem",{attrs:{label:"客服短信内容",prop:"contentCn"}},[e("Input",{attrs:{clearable:!0,placeholder:"请输入短信内容,最多支持600个字符",type:"textarea",rows:5},on:{"on-blur":function(e){return t.handleInputBlur()}},model:{value:t.editObj.contentCn,callback:function(e){t.$set(t.editObj,"contentCn",e)},expression:"editObj.contentCn"}}),"Info"!=t.operationType?e("CheckboxGroup",{staticClass:"checkbox-group",staticStyle:{"text-align":"left"},on:{"on-change":function(e){return t.setContentParamSC(e,t.editObj.contentCn)}},model:{value:t.CheckBoxParams.paramSC,callback:function(e){t.$set(t.CheckBoxParams,"paramSC",e)},expression:"CheckBoxParams.paramSC"}},t._l(t.CheckBoxList,(function(a,n){return e("Checkbox",{key:n,attrs:{label:a.value}},[t._v(t._s(a.labelCn))])})),1):t._e()],1)],1),e("div",{staticStyle:{"text-align":"center"}},["Add"==t.operationType?e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{type:"primary"},on:{click:t.submit}},[t._v("提交")]):t._e(),"Update"==t.operationType?e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],attrs:{type:"primary"},on:{click:t.submit}},[t._v("提交")]):t._e(),e("Button",{staticStyle:{"margin-left":"8px"},on:{click:function(e){return t.reset("editObj")}}},[t._v("重置")])],1)],1)])],1)},i=[],s=(a("caad"),a("d81d"),a("14d9"),a("d3b7"),a("ac1f"),a("2532"),a("5319"),a("159b"),a("66df")),o="/sms",r=function(t){return s["a"].request({url:o+"/customer/pageList",data:t,method:"POST"})},l=function(t){return s["a"].request({url:o+"/customer",data:t,method:"POST"})},c=function(t){return s["a"].request({url:o+"/customer",data:t,method:"PUT"})},d=function(t){return s["a"].request({url:o+"/customer/delete",method:"post",data:t})},u={components:{},data:function(){return{insertLocal:0,SMSEditFlag:!1,SMSTitle:"通知短信新增",editObj:{contentCn:"",templateName:""},ruleEditValidate:{templateName:[{required:!0,message:"客服短信名称不能为空",trigger:"blur"}],contentCn:[{required:!0,message:"客服短信内容不能为空",trigger:"blur"},{min:0,max:600,message:"最多支持600个字符"}]},tableData:[],selection:[],selectionIds:[],CheckBoxParams:{paramSC:[],paramTempSC:[]},CheckBoxList:[{labelCn:"iccid",value:"{iccid}"},{labelCn:"验证码",value:"{code}"}],tableLoading:!1,total:0,pageSize:10,page:1,columns:[{type:"selection",minWidth:60,maxWidth:60,align:"center"},{title:"模板名称",key:"templateName",align:"center",minWidth:150,tooltip:!0},{title:"模板内容",key:"contentCn",align:"center",minWidth:150,tooltip:!0},{title:"操作",slot:"action",minWidth:150,maxWidth:220,align:"center"}],operationType:"Add"}},methods:{init:function(){this.loadByPage(0)},submit:function(){var t=this;this.$refs["editObj"].validate((function(e){var a;e&&("Add"===t.operationType&&(a=l),"Update"===t.operationType&&(a=c),a(t.editObj).then((function(e){"0000"===e.code?(t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.init()):t.$Notice.success({title:"操作提示",desc:"操作失败"}),t.SMSEditFlag=!1,t.reset("editObj")})))}))},reset:function(t){this.$refs[t].resetFields(),this.resetContentParam()},loadByPage:function(t){var e=this;0===t&&(this.page=1),r({current:t,size:"10"}).then((function(t){"0000"===t.code&&(e.tableData=t.paging.data,e.total=t.paging.total)}))},SMSAdd:function(){this.operationType="Add",this.SMSTitle="客服短信新增",this.SMSEditFlag=!0,this.editObj={contentCn:"",templateName:""}},SMSCommon:function(t,e){this.operationType=e,this.SMSTitle="客服短信编辑",this.SMSEditFlag=!0,this.editObj={id:t.id,contentCn:t.contentCn,templateName:t.templateName},this.fillCheckBox(t.contentCn)},fillCheckBox:function(t){var e=[];this.CheckBoxList.forEach((function(a){t.indexOf(a.value)>-1&&e.push(a.value)})),this.CheckBoxParams.paramSC=e,this.CheckBoxParams.paramTempSC=e},SMSDel:function(t){var e=this;this.$Modal.confirm({title:"确认删除？",onOk:function(){Array.isArray(t)||(t=[t]),d(t).then((function(t){"0000"===t.code?(e.init(),e.selection=[],e.$Notice.success({title:"操作提示",desc:"操作成功"})):e.$Notice.success({title:"操作提示",desc:"操作失败"})}))}})},handleRowChange:function(t){var e=this;this.selection=t,this.selectionIds=[],t.map((function(t,a){e.selectionIds.push(t.id)}))},deleteList:function(){var t=this.selection.length;t<1?this.$Message.warning("请至少选择一条记录"):this.SMSDel(this.selectionIds)},handleInputBlur:function(){this.insertLocal=event.srcElement.selectionStart},setContentParamSC:function(t,e){var a=t.length,n=e.substring(0,this.insertLocal),i=e.substring(this.insertLocal,e.length);a>=this.CheckBoxParams.paramTempSC.length?(this.editObj.contentCn=n+t[a-1]+i,this.insertLocal=this.editObj.contentCn.length):this.filterContent(t,"contentCn"),this.CheckBoxParams.paramTempSC=t},resetContentParam:function(){this.insertLocal=0,this.CheckBoxParams.paramSC=[],this.CheckBoxParams.paramTempSC=[]},filterContent:function(t,e){var a=this,n=this.CheckBoxList.map((function(t){return t.value}));n.forEach((function(n){t.includes(n)||(a.editObj[e]=a.editObj[e].replace(n,""))}))}},mounted:function(){this.init()}},m=u,p=(a("1684"),a("2877")),h=Object(p["a"])(m,n,i,!1,null,null,null);e["default"]=h.exports},"44e5":function(t,e,a){}}]);