(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3147487a"],{"00b4":function(e,t,i){"use strict";i("ac1f");var a=i("23e7"),n=i("c65b"),r=i("1626"),s=i("825a"),o=i("577e"),l=function(){var e=!1,t=/[ac]/;return t.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===t.test("abc")&&e}(),c=/./.test;a({target:"RegExp",proto:!0,forced:!l},{test:function(e){var t=s(this),i=o(e),a=t.exec;if(!r(a))return n(c,t,i);var l=n(a,t,i);return null!==l&&(s(l),!0)}})},"07ac":function(e,t,i){"use strict";var a=i("23e7"),n=i("6f53").values;a({target:"Object",stat:!0},{values:function(e){return n(e)}})},"44cf":function(e,t,i){"use strict";i.r(t);i("b0c0");var a=function(){var e=this,t=e._self._c;return t("Card",{staticClass:"card-wrapper"},[t("Form",{ref:"campaignForm",staticClass:"form-wrapper",attrs:{model:e.campaign,rules:e.formRules,"label-width":120}},[t("div",{staticClass:"form-section"},[t("div",{staticClass:"section-header"},[e._v("基本信息")]),t("div",{staticClass:"section-content"},[t("Row",{attrs:{gutter:24}},[t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"活动名称",prop:"name"}},[t("Input",{staticClass:"uniform-input",attrs:{placeholder:"请输入活动名称",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.name},model:{value:e.campaign.name,callback:function(t){e.$set(e.campaign,"name",t)},expression:"campaign.name"}})],1)],1),t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"合作模式",prop:"cooperationMode"}},[t("Select",{staticClass:"uniform-input",attrs:{placeholder:"请选择合作模式",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.cooperationMode},on:{"on-change":e.handleCooperationModeChange},model:{value:e.campaign.cooperationMode,callback:function(t){e.$set(e.campaign,"cooperationMode",t)},expression:"campaign.cooperationMode"}},[t("Option",{attrs:{value:"1"}},[e._v("代销")]),t("Option",{attrs:{value:"2"}},[e._v("A2Z")])],1)],1)],1),t("Col",{attrs:{span:"2"}})],1),1==e.campaign.cooperationMode?[t("Row",{attrs:{gutter:24}},[t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"活动起止时间",prop:"activityTimeRange"}},[t("DatePicker",{staticClass:"uniform-input",attrs:{type:"daterange",placement:"bottom-start",placeholder:"请选择活动起止时间范围",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.activityTimeRange,options:e.datePickerOptions},on:{"on-change":e.handleActivityTimeChange},model:{value:e.campaign.activityTimeRange,callback:function(t){e.$set(e.campaign,"activityTimeRange",t)},expression:"campaign.activityTimeRange"}})],1)],1),t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"返还金有效期",prop:"restitutionFundsTime"}},[t("DatePicker",{staticClass:"uniform-input",attrs:{type:"date",placeholder:"请选择返还金有效期",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.restitutionFundsTime},model:{value:e.campaign.restitutionFundsTime,callback:function(t){e.$set(e.campaign,"restitutionFundsTime",t)},expression:"campaign.restitutionFundsTime"}})],1)],1),t("Col",{attrs:{span:"2"}})],1),t("Row",{attrs:{gutter:24}},[t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"返还金总额",prop:"totalAmountRefund"}},[t("div",{staticClass:"amount-input-group"},[t("InputNumber",{staticClass:"uniform-input",attrs:{placeholder:"请输入返还金总额",min:.01,disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.totalAmountRefund},model:{value:e.campaign.totalAmountRefund,callback:function(t){e.$set(e.campaign,"totalAmountRefund",t)},expression:"campaign.totalAmountRefund"}}),t("span",{staticClass:"unit-label"},[e._v("港币")])],1)])],1),t("Col",{attrs:{span:"10"}},[e.mcId?t("FormItem",{attrs:{label:"活动状态",prop:"campaignStatus"}},[t("Select",{staticClass:"uniform-input",attrs:{placeholder:"请选择活动状态",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.campaignStatus},model:{value:e.campaign.campaignStatus,callback:function(t){e.$set(e.campaign,"campaignStatus",t)},expression:"campaign.campaignStatus"}},e._l(e.availableStatusOptions,(function(i){return t("Option",{key:i.value,attrs:{value:i.value}},[e._v("\n                  "+e._s(i.label)+"\n                ")])})),1)],1):e._e()],1),t("Col",{attrs:{span:"2"}})],1)]:[t("Row",{attrs:{gutter:24}},[t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"环比起止时间",prop:"sequentialTimeRange"}},[t("DatePicker",{staticClass:"uniform-input",attrs:{type:"daterange",placement:"bottom-start",placeholder:"请选择环比起止时间范围",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.sequentialStartTime},on:{"on-change":e.handleSequentialTimeChange},model:{value:e.campaign.sequentialTimeRange,callback:function(t){e.$set(e.campaign,"sequentialTimeRange",t)},expression:"campaign.sequentialTimeRange"}})],1)],1),t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"活动起止时间",prop:"activityTimeRange"}},[t("DatePicker",{staticClass:"uniform-input",attrs:{type:"daterange",placement:"bottom-start",placeholder:"请选择活动起止时间范围",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.activityTimeRange},on:{"on-change":e.handleActivityTimeChange},model:{value:e.campaign.activityTimeRange,callback:function(t){e.$set(e.campaign,"activityTimeRange",t)},expression:"campaign.activityTimeRange"}})],1)],1),t("Col",{attrs:{span:"2"}})],1),t("Row",{attrs:{gutter:24}},[t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"返还金有效期",prop:"restitutionFundsTime"}},[t("DatePicker",{staticClass:"uniform-input",attrs:{type:"date",placeholder:"请选择返还金有效期",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.restitutionFundsTime},model:{value:e.campaign.restitutionFundsTime,callback:function(t){e.$set(e.campaign,"restitutionFundsTime",t)},expression:"campaign.restitutionFundsTime"}})],1)],1),t("Col",{attrs:{span:"10"}},[e.mcId?t("FormItem",{attrs:{label:"活动状态",prop:"campaignStatus"}},[t("Select",{staticClass:"uniform-input",attrs:{placeholder:"请选择活动状态",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.campaignStatus},model:{value:e.campaign.campaignStatus,callback:function(t){e.$set(e.campaign,"campaignStatus",t)},expression:"campaign.campaignStatus"}},e._l(e.availableStatusOptions,(function(i){return t("Option",{key:i.value,attrs:{value:i.value}},[e._v("\n                  "+e._s(i.label)+"\n                ")])})),1)],1):e._e()],1),t("Col",{attrs:{span:"2"}})],1)]],2),t("div",{staticClass:"section-header"},[e._v(e._s(1==e.campaign.cooperationMode?"累计返还规则":"规则信息"))]),t("div",{staticClass:"section-content"},[t("div",{staticClass:"rules-section"},[t("FormItem",{staticClass:"rules-header",attrs:{"label-width":5}},[t("div",{staticClass:"rules-container"},[t("Row",{attrs:{gutter:24}},[1==e.campaign.cooperationMode?[e._l(e.campaign.ruleList,(function(i,a){return t("div",{key:a,staticClass:"rule-item"},[t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"预存款",prop:"ruleList."+a+".deposit",rules:e.formRules.deposit}},[t("div",{staticClass:"input-wrapper"},[t("InputNumber",{staticClass:"uniform-input",attrs:{min:.01,step:1,placeholder:"请输入预存款金额",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.ruleList},model:{value:i.deposit,callback:function(t){e.$set(i,"deposit",t)},expression:"rule.deposit"}}),t("span",{staticClass:"unit-label"},[e._v("港币")])],1)])],1),t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"返还比例",prop:"ruleList."+a+".rebateRate",rules:e.formRules.rebateRate}},[t("div",{staticClass:"input-wrapper"},[t("InputNumber",{staticClass:"uniform-input",attrs:{min:1,max:100,step:1,placeholder:"请输入返还比例",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.ruleList},model:{value:i.rebateRate,callback:function(t){e.$set(i,"rebateRate",t)},expression:"rule.rebateRate"}}),t("span",{staticClass:"unit-label"},[e._v("%")])],1)])],1),t("Col",{staticClass:"action-buttons",attrs:{span:"2"}},[e.campaign.ruleList.length>1?t("div",{staticClass:"rule-action"},[t("Button",{staticClass:"delete-button",attrs:{type:"error",size:"small",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.ruleList},on:{click:function(t){return e.removeRule(a)}}},[t("Icon",{attrs:{type:"md-remove"}})],1)],1):e._e()])],1)})),t("div",{staticClass:"rule-item"},[t("Col",{attrs:{span:"20"}}),t("Col",{staticClass:"action-buttons",attrs:{span:"2"}},[t("Button",{staticClass:"add-button",attrs:{type:"success",size:"small",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.ruleList},on:{click:e.addRule}},[t("Icon",{attrs:{type:"md-add"}})],1)],1)],1)]:[e._l(e.campaign.ruleList,(function(i,a){return t("div",{key:a,staticClass:"rule-item"},[t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"环比增幅",prop:"ruleList."+a+".growthRate",rules:e.formRules.growthRate}},[t("div",{staticClass:"input-wrapper"},[t("InputNumber",{staticClass:"uniform-input",attrs:{min:1,max:100,step:1,placeholder:"请输入环比增幅",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.ruleList},model:{value:i.growthRate,callback:function(t){e.$set(i,"growthRate",t)},expression:"rule.growthRate"}}),t("span",{staticClass:"unit-label"},[e._v("%")])],1)])],1),t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"返还比例",prop:"ruleList."+a+".rebateRate",rules:e.formRules.rebateRate}},[t("div",{staticClass:"input-wrapper"},[t("InputNumber",{staticClass:"uniform-input",attrs:{min:1,max:100,step:1,placeholder:"请输入返还比例",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.ruleList},model:{value:i.rebateRate,callback:function(t){e.$set(i,"rebateRate",t)},expression:"rule.rebateRate"}}),t("span",{staticClass:"unit-label"},[e._v("%")])],1)])],1),t("Col",{staticClass:"action-buttons",attrs:{span:"2"}},[e.campaign.ruleList.length>1?t("div",{staticClass:"rule-action"},[t("Button",{staticClass:"delete-button",attrs:{type:"error",size:"small",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.ruleList},on:{click:function(t){return e.removeRule(a)}}},[t("Icon",{attrs:{type:"md-remove"}})],1)],1):e._e()])],1)})),t("div",{staticClass:"rule-item"},[t("Col",{attrs:{span:"20"}}),t("Col",{staticClass:"action-buttons",attrs:{span:"2"}},[t("Button",{staticClass:"add-button",attrs:{type:"success",size:"small",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.ruleList},on:{click:e.addRule}},[t("Icon",{attrs:{type:"md-add"}})],1)],1)],1)]],2)],1)])],1)]),1==e.campaign.cooperationMode?t("div",{staticClass:"section-header"},[e._v("立即返还规则")]):e._e(),1==e.campaign.cooperationMode?t("div",{staticClass:"section-content"},[t("div",{staticClass:"rules-section"},[t("FormItem",{staticClass:"rules-header",attrs:{"label-width":5}},[t("div",{staticClass:"rules-container"},[t("Row",{attrs:{gutter:24}},[t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"返还金生效期",prop:"effectiveTime",required:e.isImmediateReturnSectionActivelyFilled}},[t("div",{staticClass:"input-wrapper"},[t("DatePicker",{staticClass:"uniform-input",attrs:{type:"date",placement:"bottom-start",placeholder:"请选择返还金生效期",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.returnImmediatelyList,options:e.effectiveTimeDatePickerOptions},model:{value:e.campaign.effectiveTime,callback:function(t){e.$set(e.campaign,"effectiveTime",t)},expression:"campaign.effectiveTime"}})],1)])],1)],1),t("Row",{attrs:{gutter:24}},[[e._l(e.campaign.returnImmediatelyList,(function(i,a){return t("div",{key:a,staticClass:"rule-item"},[t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"预存款",prop:"returnImmediatelyList."+a+".predeposit",rules:e.formRules.predeposit,"label-width":120}},[t("div",{staticClass:"input-wrapper"},[t("InputNumber",{staticClass:"uniform-input",attrs:{min:0,step:1,placeholder:"请输入预存款金额",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.returnImmediatelyList},model:{value:i.predeposit,callback:function(t){e.$set(i,"predeposit",t)},expression:"rule2.predeposit"}}),t("span",{staticClass:"unit-label"},[e._v("港币")])],1)])],1),t("Col",{attrs:{span:"10"}},[t("FormItem",{attrs:{label:"返还比例",prop:"returnImmediatelyList."+a+".distributionReturnRatio",rules:e.formRules.distributionReturnRatio,"label-width":120}},[t("div",{staticClass:"input-wrapper"},[t("InputNumber",{staticClass:"uniform-input",attrs:{min:0,max:100,step:1,placeholder:"请输入返还比例",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.returnImmediatelyList},model:{value:i.distributionReturnRatio,callback:function(t){e.$set(i,"distributionReturnRatio",t)},expression:"rule2.distributionReturnRatio"}}),t("span",{staticClass:"unit-label"},[e._v("%")])],1)])],1),t("Col",{staticClass:"action-buttons",attrs:{span:"2"}},[e.campaign.returnImmediatelyList.length>0?t("div",{staticClass:"rule-action"},[t("Button",{staticClass:"delete-button",attrs:{type:"error",size:"small",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.returnImmediatelyList},on:{click:function(t){return e.removeRule2(a)}}},[t("Icon",{attrs:{type:"md-remove"}})],1)],1):e._e()])],1)})),t("div",{staticClass:"rule-item"},[t("Col",{attrs:{span:"20"}}),t("Col",{staticClass:"action-buttons",attrs:{span:"2"}},[t("Button",{staticClass:"add-button",attrs:{type:"success",size:"small",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.returnImmediatelyList},on:{click:e.addRule2}},[t("Icon",{attrs:{type:"md-add"}})],1)],1)],1)]],2)],1)])],1)]):e._e(),t("div",{staticClass:"section-header"},[e._v("公司信息")]),t("div",{staticClass:"section-content"},[t("div",{staticClass:"company-section"},[t("div",{staticClass:"company-header"},[t("Button",{staticClass:"add-company-button",attrs:{type:"primary",disabled:!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.company},on:{click:e.editCompany}},[e._v("\n              添加公司\n            ")])],1),t("div",{staticClass:"company-table-container"},[t("Table",{attrs:{border:"",columns:e.selectedCompaniesColumns,data:e.finalSelectedCompanies,height:"400"}})],1)])]),t("div",{staticClass:"form-actions"},[t("Button",{attrs:{type:"primary",disabled:e.isAllFieldsDisabled,loading:e.submitLoading},on:{click:e.submitCampaign}},[e._v("提交")]),t("Button",{staticStyle:{"margin-left":"16px"},on:{click:e.callBackPage}},[e._v("返回")])],1)])]),t("Modal",{attrs:{title:"参与公司编辑",width:"900","mask-closable":!1},on:{"on-ok":e.handleOk,"on-cancel":e.handleCancel},nativeOn:{keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault()}},model:{value:e.modalVisible,callback:function(t){e.modalVisible=t},expression:"modalVisible"}},[t("Form",{ref:"searchForm",staticStyle:{display:"flex"},attrs:{model:e.searchForm,"label-width":80}},[t("FormItem",{attrs:{label:"公司名称"}},[t("Input",{staticClass:"uniform-input",attrs:{placeholder:"请输入公司名称",clearable:""},model:{value:e.searchForm.name,callback:function(t){e.$set(e.searchForm,"name",t)},expression:"searchForm.name"}})],1),t("FormItem",{attrs:{"label-width":30}},[t("Button",{staticStyle:{margin:"0 2px"},attrs:{type:"info",loading:e.searchCompaniesLoading,icon:"ios-search"},on:{click:function(t){return e.searchCompanies(1)}}},[e._v("搜索")])],1)],1),t("div",{staticStyle:{margin:"0 20px"}},[t("Checkbox",{staticStyle:{"margin-bottom":"10px"},attrs:{border:"",disabled:0===e.companiesData.length},on:{"on-change":e.handleAllChange},model:{value:e.selectAll,callback:function(t){e.selectAll=t},expression:"selectAll"}},[e._v("全选")]),t("Table",{ref:"selection",attrs:{border:"",columns:e.companiesColumns,height:"400",ellipsis:!0,data:e.companiesData,loading:e.tableLoading},on:{"on-selection-change":e.handleRowChange,"on-select-cancel":e.cancelPackage,"on-select-all-cancel":e.cancelPackageAll}}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.total,"page-size":e.pageSize,current:e.page,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.page=t},"on-change":e.searchCompanies}})],1)],1)],1)},n=[],r=i("3835"),s=i("2909"),o=i("c7eb"),l=i("5530"),c=i("1da1"),u=(i("d9e2"),i("99af"),i("4de4"),i("c740"),i("a630"),i("caad"),i("d81d"),i("14d9"),i("a434"),i("4ec9"),i("a9e3"),i("d3b7"),i("07ac"),i("ac1f"),i("00b4"),i("25f0"),i("2532"),i("3ca3"),i("4d90"),i("1276"),i("498a"),i("159b"),i("ddb0"),i("7bfc")),d=i("90de"),m={data:function(){var e=this,t=function(t,i,a){if(!i&&0!==i)return a(new Error("预存款不能为空"));var n=parseFloat(i);if(n<=0)return a(new Error("预存款必须大于0"));var r=e.campaign.ruleList.findIndex((function(e){return e.deposit===n}));return r>0&&n<=e.campaign.ruleList[r-1].deposit||r<e.campaign.ruleList.length-1&&n>=e.campaign.ruleList[r+1].deposit?a(new Error("预存款需要递增")):void a()},i=function(t,i,a){if(!i&&0!==i)return a(new Error("返还比例不能为空"));var n=parseFloat(i);if(n<=0)return a(new Error("返还比例必须大于0"));if(n>100)return a(new Error("返还比例不能大于100"));var r=e.campaign.ruleList.findIndex((function(e){return e.rebateRate===n}));return r>0&&n<=e.campaign.ruleList[r-1].rebateRate||r<e.campaign.ruleList.length-1&&n>=e.campaign.ruleList[r+1].rebateRate?a(new Error("返还比例必须递增")):void a()},a=function(t,i,a){if(!i&&0!==i)return a(new Error("环比增幅不能为空"));var n=parseFloat(i);if(n<=0)return a(new Error("环比增幅必须大于0"));if(n>100)return a(new Error("环比增幅不能大于100"));var r=e.campaign.ruleList.findIndex((function(e){return e.growthRate===n}));r>0&&n<=e.campaign.ruleList[r-1].growthRate?a(new Error("当前环比增幅必须大于上一级环比增幅")):a()},n=function(e,t,i){if(!t&&0!==t)return i(new Error("返还金总额不能为空"));var a=parseFloat(t);if(isNaN(a))return i(new Error("返还金总额必须为数字"));var n=/^([1-9]\d{0,7})(\.\d{0,2})?$/;return n.test(t)?a<=0?i(new Error("返还金总额必须大于0")):void i():i(new Error("最高支持8位整数和2位小数的正数"))},r=function(t,i,a){if(!i)return a(new Error("返还金有效期不能为空"));var n=e.campaign.endTime||e.campaign.activityTimeRange&&e.campaign.activityTimeRange[1];if(n){var r=new Date(n).getTime(),s=new Date(i).getTime();if(s<=r)return a(new Error("返还金有效期必须晚于活动结束时间"))}a()},s=function(t,i,a){if(null===i||void 0===i||""===i)return e.isImmediateReturnSectionActivelyFilled?a(new Error("返还金生效期不能为空")):a();var n=new Date(i);if("Invalid Date"===n.toString())return a(new Error("返还金生效期格式不正确"));if("1"===e.campaign.cooperationMode){n.setHours(0,0,0,0);var r=e.campaign.activityTimeRange&&e.campaign.activityTimeRange[1];if(r){var s=new Date(r);if(s.setHours(0,0,0,0),n<s)return a(new Error("活动结束时间—返还金有效期"))}}a()},o=function(t,i,a){try{var n=Number(t.field&&t.field.split&&t.field.split(".")[1]);if(console.log("【立即返还-预存款校验】index:",n,"field:",t.field,"list:",e.campaign.returnImmediatelyList),isNaN(n)||!e.campaign.returnImmediatelyList||!e.campaign.returnImmediatelyList[n])return a();var r=e.campaign.returnImmediatelyList[n],s=null!==i&&""!==i,o=null!==r.distributionReturnRatio&&""!==r.distributionReturnRatio;if((s||o)&&!s)return a(new Error("预存款不能为空"));if(s){var l=parseFloat(i);if(isNaN(l))return a(new Error("预存款必须为数字"));if(l<=0)return a(new Error("预存款必须大于0"));if(n>0){var c=e.campaign.returnImmediatelyList[n-1];if(c&&null!==c.predeposit&&""!==c.predeposit&&l<=parseFloat(c.predeposit))return a(new Error("预存款需要递增"))}if(n<e.campaign.returnImmediatelyList.length-1){var u=e.campaign.returnImmediatelyList[n+1];if(u&&null!==u.predeposit&&""!==u.predeposit&&l>=parseFloat(u.predeposit))return a(new Error("预存款需要递增"))}}a()}catch(d){console.error("【立即返还-预存款校验异常】",d),a()}},l=function(t,i,a){try{var n=Number(t.field&&t.field.split&&t.field.split(".")[1]);if(console.log("【立即返还-返还比例校验】index:",n,"field:",t.field,"list:",e.campaign.returnImmediatelyList),isNaN(n)||!e.campaign.returnImmediatelyList||!e.campaign.returnImmediatelyList[n])return a();var r=e.campaign.returnImmediatelyList[n],s=null!==r.predeposit&&""!==r.predeposit,o=null!==i&&""!==i;if((s||o)&&!o)return a(new Error("返还比例不能为空"));if(o){var l=parseFloat(i);if(isNaN(l))return a(new Error("返还比例必须为数字"));if(l<1||l>100)return a(new Error("返还比例必须在1到100之间"));if(n>0){var c=e.campaign.returnImmediatelyList[n-1];if(c&&null!==c.distributionReturnRatio&&""!==c.distributionReturnRatio&&l<=parseFloat(c.distributionReturnRatio))return a(new Error("返还比例需要递增"))}if(n<e.campaign.returnImmediatelyList.length-1){var u=e.campaign.returnImmediatelyList[n+1];if(u&&null!==u.distributionReturnRatio&&""!==u.distributionReturnRatio&&l>=parseFloat(u.distributionReturnRatio))return a(new Error("返还比例需要递增"))}}a()}catch(d){console.error("【立即返还-返还比例校验异常】",d),a()}};return{mcId:null,searchForm:{name:""},campaign:{name:"",cooperationMode:"1",activityTimeRange:[],sequentialTimeRange:[],startTime:"",endTime:"",sequentialStartTime:"",sequentialEndTime:"",totalAmountRefund:0,restitutionFundsTime:"",effectiveTime:"",campaignStatus:"1",ruleList:[{returnType:"1",deposit:1,rebateRate:1,growthRate:1}],returnImmediatelyList:[{returnType:"2",predeposit:null,distributionReturnRatio:null}],selectedCompanies:[]},formRules:{name:[{required:!0,message:"活动名称不能为空",trigger:"blur"}],cooperationMode:[{required:!0,message:"合作模式不能为空",trigger:"change"}],activityTimeRange:[{required:!0,type:"array",message:"活动时间不能为空",trigger:"change"},{validator:this.validateActivityTimeRange,trigger:"change"}],sequentialTimeRange:[{required:!0,type:"array",message:"环比时间不能为空",trigger:"change"},{validator:this.validateSequentialTimeRange,trigger:"change"}],startTime:[{required:!0,type:"date",message:"开始时间不能为空",trigger:"change"},{validator:this.validateStartTime,trigger:"change"}],endTime:[{required:!0,type:"date",message:"结束时间不能为空",trigger:"change"},{validator:this.validateEndTime,trigger:"change"}],sequentialStartTime:[{required:!0,type:"date",message:"环比开始时间不能为空",trigger:"change"},{validator:this.validateSequentialStartTime,trigger:"change"}],sequentialEndTime:[{required:!0,type:"date",message:"环比结束时间不能为空",trigger:"change"},{validator:this.validateSequentialEndTime,trigger:"change"}],restitutionFundsTime:[{required:!0,type:"date",message:"返还金有效期不能为空",trigger:"change"},{validator:r,trigger:"change"}],effectiveTime:[{validator:s,trigger:"change"}],totalAmountRefund:[{validator:n,trigger:"blur"}],deposit:[{validator:t,trigger:"blur"}],rebateRate:[{validator:i,trigger:"blur"}],growthRate:[{validator:a,trigger:"blur"}],predeposit:[{validator:o,trigger:"blur"}],distributionReturnRatio:[{validator:l,trigger:"blur"}]},modalVisible:!1,companiesData:[],selectedCompaniesColumns:[{title:"公司名称",key:"companyName",align:"center",tooltip:!0},{title:"渠道商简称",key:"corpName",align:"center",tooltip:!0},{title:"操作",width:100,align:"center",render:function(t,i){var a=i.row,n=i.index;return t("div",[t("Button",{props:{type:"error",size:"small",icon:"md-close",disabled:a.isInitial&&"0"!==e.originalStatus||!e.isFieldEditable||!0!==e.isFieldEditable&&!e.isFieldEditable.company},on:{click:function(){return e.removePackage(n)}}})])}}],companiesColumns:[{type:"selection",width:60,align:"center"},{title:"公司名称",key:"companyName",minWidth:150,align:"center",tooltip:!0},{title:"渠道商简称",key:"corpName",minWidth:150,align:"center",tooltip:!0}],selectedCompanies:[],searchCompaniesLoading:!1,spinShow:!1,selectAll:!1,currentPageSelected:!1,page:1,pageSize:10,total:0,totalCount:0,tableLoading:!1,filteredData:[],activityStatusOptions:[{value:"0",label:"待开始"},{value:"1",label:"已开始"},{value:"2",label:"已结束"},{value:"3",label:"已作废"},{value:"4",label:"提前结束"},{value:"5",label:"重新结算中"}],originalStatus:"",originalEndTime:null,datePickerOptions:{},submitLoading:!1,initialSelectedCompanies:[],finalSelectedCompanies:[]}},computed:{effectiveTimeDatePickerOptions:function(){var e=this;return{disabledDate:function(t){var i=e.campaign.activityTimeRange&&e.campaign.activityTimeRange[1],a=e.campaign.restitutionFundsTime,n=null,r=null;return i&&(n=new Date(i),n.setDate(n.getDate()+1),n.setHours(0,0,0,0)),a&&(r=new Date(a),r.setHours(0,0,0,0)),n&&r?t<n||t>r:n?t<n:!!r&&t>r}}},isFieldEditable:function(){var e=this.originalStatus;if(!this.mcId)return!0;switch(e){case"0":return!0;case"1":return{campaignStatus:!0,endTime:!0,activityTimeRange:!0,totalAmountRefund:"1"===this.campaign.cooperationMode,name:!1,cooperationMode:!1,startTime:!1,sequentialTimeRange:!1,sequentialStartTime:!1,sequentialEndTime:!1,restitutionFundsTime:!1,ruleList:!1,returnImmediatelyList:!1,company:"1"===this.campaign.cooperationMode};case"2":case"3":case"4":case"5":return{name:!1,cooperationMode:!1,activityTimeRange:!1,sequentialTimeRange:!1,startTime:!1,endTime:!1,sequentialStartTime:!1,sequentialEndTime:!1,totalAmountRefund:!1,restitutionFundsTime:!1,campaignStatus:!1,ruleList:!1,returnImmediatelyList:!1,company:!1};default:return!1}},availableStatusOptions:function(){if(!this.mcId)return this.activityStatusOptions;var e=this.originalStatus;return"1"===e?"2"===this.campaign.cooperationMode?this.activityStatusOptions.filter((function(e){return["1","3"].includes(e.value)})):this.activityStatusOptions.filter((function(e){return["1","3","4"].includes(e.value)})):"0"===e?this.activityStatusOptions.filter((function(e){return["0","3"].includes(e.value)})):this.activityStatusOptions.filter((function(t){return t.value===e}))},isAllFieldsDisabled:function(){return!this.isFieldEditable||("boolean"===typeof this.isFieldEditable?!this.isFieldEditable:Object.values(this.isFieldEditable).every((function(e){return!e})))},isImmediateReturnSectionActivelyFilled:function(){return"1"===this.campaign.cooperationMode&&(!!this.campaign.effectiveTime||this.campaign.returnImmediatelyList.some((function(e){return null!==e.predeposit&&""!==e.predeposit||null!==e.distributionReturnRatio&&""!==e.distributionReturnRatio})))}},methods:{searchCompanies:function(e){var t=this;return Object(c["a"])(Object(o["a"])().mark((function i(){var a,n,r,s;return Object(o["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,Array.isArray(t.companiesData)||(t.companiesData=[]),1===e&&(t.page=1),t.searchCompaniesLoading=!0,t.tableLoading=!0,a={companyName:t.searchForm.name.trim()||"",pageNum:t.page,pageSize:t.pageSize,cooperationMode:t.campaign.cooperationMode},i.next=8,Object(u["i"])(a);case 8:if(n=i.sent,"0000"===(null===n||void 0===n?void 0:n.code)&&null!==n&&void 0!==n&&n.data){i.next=11;break}throw new Error((null===n||void 0===n?void 0:n.message)||"获取公司列表失败");case 11:return r=Array.isArray(n.data)?n.data:[],t.companiesData=r.map((function(e){return Object(l["a"])(Object(l["a"])({},e),{},{_disabled:t.initialSelectedCompanies.some((function(t){return t.corpId===e.corpId}))&&"0"!==t.originalStatus})})),t.total=parseInt(n.count||0),1!==t.page||t.searchForm.name||(t.totalCount=t.total),i.next=17,t.$nextTick();case 17:t.updatePageData(!0),i.next=28;break;case 20:i.prev=20,i.t0=i["catch"](0),s=i.t0.message||"获取公司列表失败",t.$Message.error(s),console.error("获取公司列表错误:",i.t0),t.companiesData=[],t.total=0,t.totalCount=0;case 28:return i.prev=28,t.searchCompaniesLoading=!1,t.tableLoading=!1,i.finish(28);case 32:case"end":return i.stop()}}),i,null,[[0,20,28,32]])})))()},getSelectedCompanies:function(){var e=this;return Object(c["a"])(Object(o["a"])().mark((function t(){var i,a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.mcId){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=5,Object(u["g"])({mcId:e.mcId});case 5:if(i=t.sent,"0000"===(null===i||void 0===i?void 0:i.code)&&null!==i&&void 0!==i&&i.data){t.next=8;break}throw new Error((null===i||void 0===i?void 0:i.message)||"获取已选公司失败");case 8:a=Array.isArray(i.data)?i.data.map((function(e){return Object(l["a"])(Object(l["a"])({},e),{},{corpId:e.corpId||e.id,companyName:e.companyName||e.nameCn,isInitial:!0})})):[],e.initialSelectedCompanies=Object(s["a"])(a),e.selectedCompanies=Object(s["a"])(a),e.finalSelectedCompanies=Object(s["a"])(a),t.next=21;break;case 14:t.prev=14,t.t0=t["catch"](2),console.error("获取已选公司失败:",t.t0),e.$Message.error("获取已选公司失败"),e.selectedCompanies=[],e.initialSelectedCompanies=[],e.finalSelectedCompanies=[];case 21:case"end":return t.stop()}}),t,null,[[2,14]])})))()},init:function(){var e=this;return Object(c["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.mcId=e.$route.query.mcId,!e.mcId){t.next=6;break}return t.next=4,e.getActivityDetail(e.mcId);case 4:return t.next=6,e.getSelectedCompanies();case 6:case"end":return t.stop()}}),t)})))()},updatePageData:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$nextTick((function(){if(e.$refs.selection){var i=e.$refs.selection;if(t){var a=i.$children[0];a.$listeners["on-selection-change"];a.$off("on-selection-change")}e.companiesData.forEach((function(t,a){e.selectedCompanies.some((function(e){return e.corpId===t.corpId}))&&i.toggleSelect(a)}));var n=e.companiesData.every((function(t){return e.selectedCompanies.some((function(e){return e.corpId===t.corpId}))}));if(n&&e.total>0&&i.selectAll(!0),t){var r=i.$children[0];r.$on("on-selection-change",e.handleRowChange)}e.selectAll=e.selectedCompanies.length===e.totalCount}}))},handleAllChange:function(e){var t=this;this.selectAll=e,e?(this.searchCompaniesLoading=!0,Object(u["i"])({companyName:this.searchForm.name,pageNum:-1,pageSize:-1,cooperationMode:this.campaign.cooperationMode}).then((function(e){if("0000"==e.code){var i=new Map(e.data.map((function(e){return[e.corpId,e]})));t.initialSelectedCompanies.forEach((function(e){i.delete(e.corpId)})),t.selectedCompanies.forEach((function(e){i.delete(e.corpId)}));var a=new Map;t.initialSelectedCompanies.forEach((function(e){a.set(e.corpId,e)})),t.selectedCompanies.forEach((function(e){a.set(e.corpId,e)})),Array.from(i.values()).forEach((function(e){a.set(e.corpId,e)})),t.selectedCompanies=Array.from(a.values()),t.$nextTick((function(){t.$refs.selection&&t.$refs.selection.selectAll(!0)}))}})).catch((function(e){t.$Message.error("获取公司列表失败")})).finally((function(){t.searchCompaniesLoading=!1}))):(this.selectedCompanies=this.initialSelectedCompanies,this.$nextTick((function(){t.$refs.selection&&t.$refs.selection.selectAll(!1)})))},handleRowChange:function(e){var t=this;console.log(e,"选中列表"),e.forEach((function(e){if(!t.selectedCompanies.some((function(t){return t.corpId===e.corpId}))){var i=t.initialSelectedCompanies.some((function(t){return t.corpId===e.corpId}));t.selectedCompanies.push(Object(l["a"])(Object(l["a"])({},e),{},{isInitial:i}))}})),this.selectAll=this.selectedCompanies.length===this.total},removePackage:function(e){var t=this,i=this.selectedCompanies[e];if(i)if(i.isInitial&&"0"!==this.originalStatus)this.$Message.warning("初始已选中的公司不能删除");else{this.selectedCompanies.splice(e,1),this.finalSelectedCompanies.splice(e,1);var a=this.companiesData.findIndex((function(e){return e.corpId===i.corpId}));a>-1&&this.$nextTick((function(){t.$refs.selection.toggleSelect(a)})),this.selectAll=this.selectedCompanies.length===this.totalCount}},cancelPackage:function(e,t){var i=this.selectedCompanies.findIndex((function(e){return e.corpId===t.corpId}));i>-1&&this.selectedCompanies.splice(i,1),this.selectAll=this.selectedCompanies.length===this.totalCount},cancelPackageAll:function(){var e=this.companiesData.map((function(e){return e.corpId}));this.selectedCompanies=this.selectedCompanies.filter((function(t){return!e.includes(t.corpId)})),this.selectAll=this.selectedCompanies.length===this.totalCount},addRule:function(){var e=this.campaign.ruleList[this.campaign.ruleList.length-1],t=e.deposit+1,i=0,a=0;1==this.campaign.cooperationMode?(i=Math.min(e.rebateRate+1,100),this.campaign.ruleList.push({deposit:t,rebateRate:i})):(a=Math.min(e.growthRate+1,100),i=Math.min(e.rebateRate+1,100),this.campaign.ruleList.push({rebateRate:i,growthRate:a}))},removeRule:function(e){this.campaign.ruleList.length>1?this.campaign.ruleList.splice(e,1):this.$Message.warning("至少需要保留一条返还规则")},addRule2:function(){this.campaign.returnImmediatelyList.push({returnType:"2",predeposit:null,distributionReturnRatio:null})},removeRule2:function(e){this.campaign.returnImmediatelyList.splice(e,1)},editCompany:function(){this.modalVisible=!0,this.selectedCompanies=Object(s["a"])(this.finalSelectedCompanies||[]),this.initialSelectedCompanies=Object(s["a"])(this.initialSelectedCompanies||[]),this.page=1,this.searchForm.name="",this.searchCompanies(1)},handleOk:function(){this.campaign.selectedCompanies=Object(s["a"])(this.selectedCompanies),this.finalSelectedCompanies=Object(s["a"])(this.selectedCompanies),this.modalVisible=!1,this.page=1,this.searchForm.name=""},handleCancel:function(){this.modalVisible=!1,this.page=1,this.searchForm.name=""},submitCampaign:function(){var e=this;this.$refs.campaignForm.validate(function(){var t=Object(c["a"])(Object(o["a"])().mark((function t(i){var a,n,r,s;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!i||!e.validateStatusChange()){t.next=24;break}if("1"!==e.campaign.cooperationMode||!e.campaign.effectiveTime){t.next=7;break}if(a=e.campaign.returnImmediatelyList.some((function(e){return null!==e.predeposit&&""!==e.predeposit&&null!==e.distributionReturnRatio&&""!==e.distributionReturnRatio})),a){t.next=7;break}return e.submitLoading=!1,e.$Message["warning"]({background:!0,content:"您已填写返还金生效期，请至少填写一组立即返还规则的预存款和返还比例信息",duration:5}),t.abrupt("return",!1);case 7:return t.prev=7,e.submitLoading=!0,n=e.formatSubmitData(),r=e.mcId?u["j"]:u["a"],t.next=13,r(n);case 13:s=t.sent,e.submitLoading=!1,"0000"==s.code&&(e.$Message.success(e.mcId?"修改成功":"新增成功"),e.$router.push({name:"marketingActivityIndex"})),t.next=22;break;case 18:t.prev=18,t.t0=t["catch"](7),e.submitLoading=!1,e.$Message.error(e.mcId?"修改失败":"新增失败");case 22:t.next=27;break;case 24:return e.submitLoading=!1,e.$Message.error("表单验证失败，请检查所有字段"),t.abrupt("return",!1);case 27:case"end":return t.stop()}}),t,null,[[7,18]])})));return function(e){return t.apply(this,arguments)}}())},resetForm:function(){this.$refs.campaignForm.resetFields(),1==this.campaign.cooperationMode?this.campaign.ruleList=[{deposit:1,rebateRate:1}]:this.campaign.ruleList=[{deposit:1,growthRate:1}]},callBackPage:function(){this.$router.push({name:"marketingActivityIndex"})},getActivityDetail:function(e){var t=this;return Object(c["a"])(Object(o["a"])().mark((function i(){var a,n,r;return Object(o["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,i.next=3,Object(u["h"])({mcId:e});case 3:a=i.sent,"0000"===a.code&&(n=a.data,t.campaign.name=n.campaignName,t.campaign.cooperationMode=n.cooperationMode,t.campaign.activityTimeRange=[new Date(n.startTime),new Date(n.endTime)],t.campaign.startTime=new Date(n.startTime),t.campaign.endTime=new Date(n.endTime),t.originalEndTime=new Date(n.endTime),"2"===n.cooperationMode&&(t.campaign.sequentialTimeRange=[new Date(n.sequentialStartTime),new Date(n.sequentialEndTime)],t.campaign.sequentialStartTime=new Date(n.sequentialStartTime),t.campaign.sequentialEndTime=new Date(n.sequentialEndTime)),t.campaign.restitutionFundsTime=new Date(n.restitutionFundsTime),t.campaign.totalAmountRefund=parseFloat(n.totalAmountRefund)||0,t.campaign.effectiveTime=n.effectiveTime?new Date(n.effectiveTime):"",t.campaign.ruleList=[],t.campaign.returnImmediatelyList=[],n.campaignRuleList&&n.campaignRuleList.length>0&&(r=!1,n.campaignRuleList.forEach((function(e){if("1"===e.returnType||null===e.returnType){var i={rebateRate:parseFloat(e.returnRatio)||0,id:e.id,returnType:"1"};"1"===n.cooperationMode?i.deposit=parseFloat(e.amountDeposited)||0:"2"===n.cooperationMode&&(i.growthRate=parseFloat(e.sequentialGrowthRate)||0),t.campaign.ruleList.push(i)}else"2"===e.returnType&&(t.campaign.returnImmediatelyList.push({predeposit:null!==e.amountDeposited?parseFloat(e.amountDeposited):null,distributionReturnRatio:null!==e.returnRatio?parseFloat(e.returnRatio):null,id:e.id,returnType:"2"}),!r&&e.effectiveTime&&(t.campaign.effectiveTime=new Date(e.effectiveTime),r=!0))}))),"1"===t.campaign.cooperationMode&&0===t.campaign.returnImmediatelyList.length&&t.campaign.returnImmediatelyList.push({returnType:"2",predeposit:null,distributionReturnRatio:null}),"1"===t.campaign.cooperationMode&&0===t.campaign.ruleList.length&&t.campaign.ruleList.push({returnType:"1",deposit:1,rebateRate:1}),"2"===t.campaign.cooperationMode&&0===t.campaign.ruleList.length&&t.campaign.ruleList.push({returnType:"1",growthRate:1,rebateRate:1}),t.campaign.selectedCompanies=n.company?n.company.map((function(e){return{id:e.corpId,nameCn:e.companyName}})):[],t.campaign.campaignStatus=n.campaignStatus,t.originalStatus=n.campaignStatus),i.next=10;break;case 7:i.prev=7,i.t0=i["catch"](0),t.$Message.error("获取活动详情失败");case 10:case"end":return i.stop()}}),i,null,[[0,7]])})))()},formatSubmitData:function(){var e=this,t=[];this.campaign.ruleList.forEach((function(i){t.push({returnType:"1",amountDeposited:"1"===e.campaign.cooperationMode?parseFloat(i.deposit):void 0,sequentialGrowthRate:"2"===e.campaign.cooperationMode?parseFloat(i.growthRate):void 0,returnRatio:parseFloat(i.rebateRate),id:i.id})})),"1"===this.campaign.cooperationMode&&this.campaign.returnImmediatelyList&&this.campaign.returnImmediatelyList.length>0&&this.campaign.returnImmediatelyList.forEach((function(i){(null!==i.predeposit&&""!==i.predeposit||null!==i.distributionReturnRatio&&""!==i.distributionReturnRatio)&&t.push({returnType:"2",amountDeposited:parseFloat(i.predeposit)||void 0,returnRatio:parseFloat(i.distributionReturnRatio)||void 0,effectiveTime:e.campaign.effectiveTime?Object(d["c"])(e.campaign.effectiveTime,!1):void 0,id:i.id})}));var i={campaignName:this.campaign.name.trim(),cooperationMode:this.campaign.cooperationMode,startTime:Object(d["c"])(this.campaign.startTime),endTime:Object(d["c"])(this.campaign.endTime,!0),restitutionFundsTime:Object(d["c"])(this.campaign.restitutionFundsTime,!0),companyList:this.selectedCompanies,ruleList:t,campaignStatus:this.campaign.campaignStatus};return"2"===this.campaign.cooperationMode&&(i.sequentialStartTime=Object(d["c"])(this.campaign.sequentialStartTime),i.sequentialEndTime=Object(d["c"])(this.campaign.sequentialEndTime,!0)),"1"===this.campaign.cooperationMode&&(i.totalAmountRefund=parseFloat(this.campaign.totalAmountRefund)),this.mcId&&(i.id=this.mcId),i},handleCooperationModeChange:function(){this.campaign.ruleList=["1"===this.campaign.cooperationMode?{returnType:"1",deposit:1,rebateRate:1}:{returnType:"1",growthRate:1,rebateRate:1}],this.campaign.returnImmediatelyList=[{returnType:"2",predeposit:null,distributionReturnRatio:null}],this.campaign.effectiveTime=""},formatDate:function(e){if(!e)return"";var t=new Date(e);return"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-").concat(String(t.getDate()).padStart(2,"0")," 00:00:00")},validateStatusChange:function(){var e=this.campaign,t=(e.campaignStatus,e.endTime);if("1"===this.originalStatus){var i=new Date(t),a=new Date(this.campaign.activityTimeRange[1]);if(i<a)return this.$Message.error("已开始状态只能延长结束时间，不能缩短"),!1}return!0},validateSequentialStartTime:function(e,t,i){return t?"2"===this.campaign.cooperationMode&&this.campaign.sequentialEndTime&&t>=this.campaign.sequentialEndTime?i(new Error("环比开始时间必须早于环比结束时间")):void i():i()},validateSequentialEndTime:function(e,t,i){if(!t)return i();if("2"===this.campaign.cooperationMode){if(this.campaign.sequentialStartTime&&t<=this.campaign.sequentialStartTime)return i(new Error("环比结束时间必须晚于环比开始时间"));if(this.campaign.startTime&&t>=this.campaign.startTime)return i(new Error("环比结束时间必须早于活动开始时间"))}i()},validateStartTime:function(e,t,i){return t?this.campaign.endTime&&t>=this.campaign.endTime?i(new Error("活动开始时间必须早于结束时间")):"2"===this.campaign.cooperationMode&&this.campaign.sequentialEndTime&&t<=this.campaign.sequentialEndTime?i(new Error("活动开始时间必须晚于环比结束时间")):void i():i()},validateEndTime:function(e,t,i){return t?this.campaign.startTime&&t<=this.campaign.startTime?i(new Error("活动结束时间必须晚于开始时间")):"2"===this.campaign.cooperationMode&&this.campaign.sequentialEndTime&&t<=this.campaign.sequentialEndTime?i(new Error("活动结束时间必须晚于环比结束时间")):void i():i()},handleActivityTimeChange:function(e){var t=this;if(e&&2===e.length){var i=e[0],a=e[1];if("1"===this.originalStatus){var n=this.campaign.startTime;this.campaign.startTime=n,this.campaign.endTime=a,this.campaign.activityTimeRange=[n,a]}else this.campaign.startTime=i,this.campaign.endTime=a,this.campaign.activityTimeRange=[i,a];if(this.campaign.effectiveTime){var r=new Date(this.campaign.effectiveTime),s=new Date;s.setHours(0,0,0,0),r.setHours(0,0,0,0);var o=this.campaign.activityTimeRange&&this.campaign.activityTimeRange[1],l=new Date(o);l.setHours(0,0,0,0);(r<s||r>l)&&!1,this.$nextTick((function(){t.$refs.campaignForm.validateField("effectiveTime"),t.$refs.campaignForm.validateField("restitutionFundsTime")}))}}else this.campaign.startTime="",this.campaign.endTime="",this.campaign.activityTimeRange=[],this.campaign.effectiveTime=null,this.$nextTick((function(){}))},handleSequentialTimeChange:function(e){e&&2===e.length?(this.campaign.sequentialStartTime=e[0],this.campaign.sequentialEndTime=e[1]):(this.campaign.sequentialStartTime="",this.campaign.sequentialEndTime="")},validateActivityTimeRange:function(e,t,i){var a;if(!t||2!==t.length)return i(new Error("请选择完整的活动时间范围"));var n=Object(r["a"])(t,2),s=n[0],o=n[1],l=new Date(s).getTime(),c=new Date(o).getTime();if(l>=c)return i(new Error("活动开始时间必须早于结束时间"));if("2"===this.campaign.cooperationMode&&2===(null===(a=this.campaign.sequentialTimeRange)||void 0===a?void 0:a.length)){var u=this.campaign.sequentialTimeRange[1],d=(new Date(u).getTime(),new Date(s)),m=new Date(u),p=new Date(d.getFullYear(),d.getMonth(),d.getDate()).getTime(),g=new Date(m.getFullYear(),m.getMonth(),m.getDate()).getTime();if(p<=g)return i(new Error("活动时间必须晚于环比结束时间"))}i()},validateSequentialTimeRange:function(e,t,i){var a;if(!t||2!==t.length)return i(new Error("请选择完整的环比时间范围"));var n=Object(r["a"])(t,2),s=n[0],o=n[1],l=new Date(s).getTime(),c=new Date(o).getTime();if(l>=c)return i(new Error("环比开始时间必须早于环比结束时间"));if(2===(null===(a=this.campaign.activityTimeRange)||void 0===a?void 0:a.length)){var u=this.campaign.activityTimeRange[0],d=new Date(o),m=new Date(u),p=new Date(d.getFullYear(),d.getMonth(),d.getDate()).getTime(),g=new Date(m.getFullYear(),m.getMonth(),m.getDate()).getTime();if(p>=g)return i(new Error("环比时间必须早于活动开始时间"))}i()}},mounted:function(){this.init()},watch:{ruleDetails:function(e){console.log(e),this.loading=!0,e&&e.campaignRuleList&&(this.cumulativeRules=e.campaignRuleList.filter((function(e){return null===e.returnType||"1"===e.returnType})),this.immediateRules=e.campaignRuleList.filter((function(e){return"2"===e.returnType})))},"campaign.effectiveTime":{handler:function(e,t){var i=this;"1"===this.campaign.cooperationMode&&this.$refs.campaignForm&&this.campaign.returnImmediatelyList.forEach((function(a,n){var r="returnImmediatelyList.".concat(n,".predeposit"),s="returnImmediatelyList.".concat(n,".distributionReturnRatio");(e&&(null!==a.predeposit&&""!==a.predeposit||null!==a.distributionReturnRatio&&""!==a.distributionReturnRatio)||!e&&t)&&(i.$refs.campaignForm.validateField(r),i.$refs.campaignForm.validateField(s))}))},deep:!0},isImmediateReturnSectionActivelyFilled:{handler:function(e){this.$refs.campaignForm&&(e||(this.campaign.effectiveTime=null),this.$refs.campaignForm.validateField("effectiveTime"))},immediate:!0}}},p=m,g=(i("9494"),i("2877")),f=Object(g["a"])(p,a,n,!1,null,"65a85b8b",null);t["default"]=f.exports},"4ec9":function(e,t,i){"use strict";i("6f48")},"6f48":function(e,t,i){"use strict";var a=i("6d61"),n=i("6566");a("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),n)},"6f53":function(e,t,i){"use strict";var a=i("83ab"),n=i("d039"),r=i("e330"),s=i("e163"),o=i("df75"),l=i("fc6a"),c=i("d1e7").f,u=r(c),d=r([].push),m=a&&n((function(){var e=Object.create(null);return e[2]=2,!u(e,2)})),p=function(e){return function(t){var i,n=l(t),r=o(n),c=m&&null===s(n),p=r.length,g=0,f=[];while(p>g)i=r[g++],a&&!(c?i in n:u(n,i))||d(f,e?[i,n[i]]:n[i]);return f}};e.exports={entries:p(!0),values:p(!1)}},"7bfc":function(e,t,i){"use strict";i.d(t,"a",(function(){return s})),i.d(t,"j",(function(){return o})),i.d(t,"i",(function(){return l})),i.d(t,"f",(function(){return c})),i.d(t,"h",(function(){return u})),i.d(t,"d",(function(){return d})),i.d(t,"e",(function(){return m})),i.d(t,"c",(function(){return p})),i.d(t,"b",(function(){return g})),i.d(t,"g",(function(){return f}));var a=i("66df"),n="/mkt",r="/cms",s=function(e){return a["a"].request({url:n+"/campaign/add",data:e,method:"post"})},o=function(e){return a["a"].request({url:n+"/campaign/edit",data:e,method:"post"})},l=function(e){return a["a"].request({url:r+"/channel/mktChannelPage",data:e,method:"post"})},c=function(e){return a["a"].request({url:n+"/campaign/getCorpList",params:e,method:"get"})},u=function(e){return a["a"].request({url:n+"/campaign/getRuleDetails",method:"get",params:e})},d=function(e){return a["a"].request({url:n+"/campaign/getCorpDetails",method:"get",params:e})},m=function(e){return a["a"].request({url:n+"/campaign/getCorpDetailsPage",data:e,method:"post"})},p=function(e){return a["a"].request({url:n+"/campaign/page",data:e,method:"post"})},g=function(e){return a["a"].request({url:n+"/settlement/addSettlementLog",data:e,method:"post"})},f=function(e){return a["a"].request({url:n+"/campaign/getMktCorp",params:e,method:"get"})}},9494:function(e,t,i){"use strict";i("e250")},e250:function(e,t,i){}}]);