(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-347e71a8"],{"00b4":function(e,t,a){"use strict";a("ac1f");var r=a("23e7"),i=a("c65b"),s=a("1626"),n=a("825a"),o=a("577e"),l=function(){var e=!1,t=/[ac]/;return t.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===t.test("abc")&&e}(),c=/./.test;r({target:"RegExp",proto:!0,forced:!l},{test:function(e){var t=n(this),a=o(e),r=t.exec;if(!s(r))return i(c,t,a);var l=i(r,t,a);return null!==l&&(n(l),!0)}})},"129f":function(e,t,a){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},2281:function(e,t,a){"use strict";a("cdcb")},"470c":function(e,t,a){"use strict";a("b0c0"),a("ac1f"),a("841c"),a("498a");var r=function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padiing:"16px"}},[t("Form",{ref:"searchForm",staticStyle:{margin:"30px 0"},attrs:{model:e.searchObj,inline:"","label-width":100},nativeOn:{submit:function(e){e.preventDefault()}}},[t("FormItem",{staticStyle:{"font-weight":"bold"},attrs:{label:e.$t("resourceManage.channelName")}},[t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:e.$t("resourceManage.enterChannelName"),clearable:1==e.showAllocate&&"3"!=e.cooperationMode,disabled:0==e.showAllocate},model:{value:e.searchObj.corpName,callback:function(t){e.$set(e.searchObj,"corpName","string"===typeof t?t.trim():t)},expression:"searchObj.corpName"}})],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{margin:"0 2px"},attrs:{type:"primary",loading:e.searchloading,disabled:0==e.showAllocate&&"3"!=e.cooperationMode},on:{click:e.search}},[t("Icon",{attrs:{type:"ios-search"}}),e._v(" "+e._s(e.$t("common.search"))+"\n\t\t\t\t")],1)],1)],1),t("div",[t("Table",{ref:"selection",attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.loading},scopedSlots:e._u([{key:"action",fn:function(a){var r=a.row;a.index;return[t("Button",{directives:[{name:"has",rawName:"v-has",value:"allocateResources",expression:"'allocateResources'"}],staticStyle:{"margin-right":"20px"},attrs:{type:"info",size:"small"},on:{click:function(t){return e.allocateResources(r)}}},[e._v(e._s(e.$t("resourceManage.allocateResources")))]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"resourceView",expression:"'resourceView'"}],staticStyle:{"margin-right":"20px"},attrs:{type:"success",size:"small"},on:{click:function(t){return e.resourceView(r)}}},[e._v(e._s(e.$t("resourceManage.resourceView")))]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"callOrderDetails",expression:"'callOrderDetails'"}],staticStyle:{"margin-right":"20px"},attrs:{type:"warning",size:"small"},on:{click:function(t){return e.callOrderDetails(r)}}},[e._v(e._s(e.$t("callOrderDetails")))]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"billingStatistics",expression:"'billingStatistics'"}],attrs:{type:"error",size:"small"},on:{click:function(t){return e.billingStatistics(r)}}},[e._v(e._s(e.$t("channelBillingStatistics")))])]}}])}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.total,"page-size":e.pageSize,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.loadByPage}})],1),t("Modal",{attrs:{title:"分配资源","footer-hide":!0,"mask-closable":!1,width:"600px"},on:{"on-cancel":e.cancelModal},model:{value:e.allocateModal,callback:function(t){e.allocateModal=t},expression:"allocateModal"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("Form",{ref:"allocateObj",attrs:{model:e.allocateObj,"label-width":190,rules:e.ruleValidate}},[t("FormItem",{attrs:{label:"渠道商名称:"}},[t("p",[e._v(e._s(e.corpName))])]),t("FormItem",{attrs:{label:"是否使用供应商IMSI",prop:"supportImsi"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择是否支持供应商IMSI",clearable:!0},model:{value:e.allocateObj.supportImsi,callback:function(t){e.$set(e.allocateObj,"supportImsi",t)},expression:"allocateObj.supportImsi"}},[t("Option",{attrs:{value:"true"}},[e._v("是")]),t("Option",{attrs:{value:"false"}},[e._v("否")])],1)],1),t("FormItem",{attrs:{label:"资源供应商",prop:"supplierId"}},["true"==e.allocateObj.supportImsi?t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择资源供应商",clearable:!0,disabled:!e.allocateObj.supportImsi},model:{value:e.allocateObj.supplierId,callback:function(t){e.$set(e.allocateObj,"supplierId",t)},expression:"allocateObj.supplierId"}},e._l(e.supplierListYes,(function(a,r){return t("Option",{key:a.supplierId,attrs:{value:a.supplierId}},[e._v(e._s(a.supplierName))])})),1):t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择资源供应商",clearable:!0,disabled:!e.allocateObj.supportImsi},model:{value:e.allocateObj.supplierId,callback:function(t){e.$set(e.allocateObj,"supplierId",t)},expression:"allocateObj.supplierId"}},e._l(e.supplierListNo,(function(a,r){return t("Option",{key:a.supplierId,attrs:{value:a.supplierId}},[e._v(e._s(a.supplierName))])})),1)],1),t("FormItem",{attrs:{label:"流量计费规则",prop:"a2zRuleId"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",clearable:"",placeholder:"请选择流量计费规则"},model:{value:e.allocateObj.a2zRuleId,callback:function(t){e.$set(e.allocateObj,"a2zRuleId",t)},expression:"allocateObj.a2zRuleId"}},e._l(e.a2zRuleList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.name))])})),1)],1),t("FormItem",{attrs:{label:"IMSI费规则",prop:"imsiFee"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",clearable:"",placeholder:"请选择IMSI费规则"},model:{value:e.allocateObj.imsiFee,callback:function(t){e.$set(e.allocateObj,"imsiFee",t)},expression:"allocateObj.imsiFee"}},e._l(e.imsiFeeList,(function(a){return t("Option",{key:a.imsi,attrs:{value:a.imsi}},[e._v(e._s(a.imsiName))])})),1)],1),t("FormItem",{attrs:{label:"起始IMSI号",prop:"startImsiNumber"}},[t("Input",{staticClass:"inputSty",attrs:{placeholder:"请输入起始IMSI号",type:"number",clearable:""},model:{value:e.allocateObj.startImsiNumber,callback:function(t){e.$set(e.allocateObj,"startImsiNumber",t)},expression:"allocateObj.startImsiNumber"}})],1),t("FormItem",{attrs:{label:"IMSI数量",prop:"imsiNumber"}},[t("Input",{staticClass:"inputSty",attrs:{"active-change":!1,clearable:!0,placeholder:"请输入IMSI数量",type:"number"},model:{value:e.allocateObj.imsiNumber,callback:function(t){e.$set(e.allocateObj,"imsiNumber",t)},expression:"allocateObj.imsiNumber"}})],1),t("FormItem",{attrs:{label:"是否需要GTP PROXY新增路由",prop:"needGtpRouter"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",clearable:"",placeholder:"请选择是否需要GTP PROXY新增路由"},model:{value:e.allocateObj.needGtpRouter,callback:function(t){e.$set(e.allocateObj,"needGtpRouter",t)},expression:"allocateObj.needGtpRouter"}},[t("Option",{attrs:{value:"true"}},[e._v("是")]),t("Option",{attrs:{value:"false"}},[e._v("否")])],1)],1),t("FormItem",{attrs:{label:"路由ID",prop:"routingID"}},[t("Input",{staticClass:"inputSty",attrs:{type:"number",clearable:!0,placeholder:"请输入路由ID"},model:{value:e.allocateObj.routingID,callback:function(t){e.$set(e.allocateObj,"routingID",t)},expression:"allocateObj.routingID"}})],1)],1),t("div",{staticStyle:{"text-align":"center"}},[t("Button",{on:{click:e.cancelModal}},[e._v("返回")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"submit",expression:"'submit'"}],staticStyle:{"margin-left":"20px"},attrs:{loading:e.submitFlag,type:"primary"},on:{click:e.submit}},[e._v("提交")])],1)],1)])],1)},i=[],s=a("3835"),n=(a("d9e2"),a("d81d"),a("14d9"),a("4fadc"),a("d3b7"),a("00b4"),a("159b"),a("8b16")),o=a("e472"),l=a("6dfa"),c={props:["showAllocate"],components:{},data:function(){var e=function(e,t,a){t>1e5?a(new Error("超过了最大限制范围100000")):a()},t=function(e,t,a){var r=/^[0-9]\d*$/;t?r.test(t)?t.length>30?a(new Error("请输入1-30位的纯数字")):a():a(new Error("请输入1-30位的纯数字")):a()},a=function(e,t,a){t>**********?a(new Error("超过了最大限制范围**********")):a()};return{cooperationMode:"",searchObj:{corpName:""},allocateObj:{supportImsi:"",supplierId:"",imsiNumber:"",a2zRuleId:"",startImsiNumber:"",imsiFee:"",needGtpRouter:"",routingID:""},total:0,pageSize:10,page:1,currentPage:1,corpId:"",corpName:"",loading:!1,searchloading:!1,submitFlag:!1,allocateModal:!1,tableData:[],supplierListYes:[],supplierListNo:[],a2zRuleList:[],imsiFeeList:[],columns:[{title:this.$t("resourceManage.channelName"),key:"corpName",align:"center",minWidth:120,tooltip:!0},{title:this.$t("support.action"),slot:"action",minWidth:500,align:"center"}],ruleValidate:{supportImsi:[{required:!0,message:"是否使用供应商IMSI不能为空",trigger:"blur"}],supplierId:[{required:!0,message:"资源供应商不能为空",trigger:"change"}],a2zRuleId:[{required:!0,message:"流量计费规则不能为空",trigger:"change"}],imsiFee:[{required:!0,message:"IMSI费规则不能为空",trigger:"change"}],startImsiNumber:[{validator:t,trigger:"blur"}],imsiNumber:[{required:!0,message:"IMSI数量不能为空",trigger:"blur"},{validator:function(e,t,a){var r=/^[1-9]\d*$/;return r.test(t)},message:"请输入正整数"},{validator:e,trigger:"blur"}],needGtpRouter:[{required:!0,message:"是否需要GTP PROXY新增路由不能为空"}],routingID:[{required:!0,message:"路由ID不能为空",trigger:"blur"},{pattern:/^[1-9]\d*$/,message:"请输入正整数",trigger:"blur"},{validator:a,trigger:"blur"}]}}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),0==this.showAllocate?"3"==this.cooperationMode&&(this.getChannel(),this.total=1):this.goPageFirst(1)},methods:{goPageFirst:function(e){var t=this;this.loading=!0;var a=this;Object(n["n"])({pageSize:10,pageNumber:e,corpName:this.searchObj.corpName,corpId:sessionStorage.getItem("corpId")}).then((function(r){"0000"==r.code&&(a.loading=!1,t.searchloading=!1,t.page=e,t.currentPage=e,t.total=r.count,t.tableData=r.data)})).catch((function(e){console.error(e)})).finally((function(){a.loading=!1,t.searchloading=!1}))},loadByPage:function(e){this.goPageFirst(e)},search:function(){this.searchloading=!0,this.goPageFirst(1)},allocateResources:function(e){this.getInfoOrder(e.corpId,e.cooperationMode),this.getsupplier(),this.allocateModal=!0,this.corpName=e.corpName,this.corpId=e.corpId},cancelModal:function(){this.allocateModal=!1,this.$refs["allocateObj"].resetFields()},submit:function(){var e=this;this.$refs["allocateObj"].validate((function(t){if(t){var a=e.allocateObj,r={corpId:e.corpId,supplierId:a.supplierId,imsiNumber:a.imsiNumber,routerId:a.routingID,useSupplierImsi:a.supportImsi,flowRuleId:a.a2zRuleId,imsiRuleId:a.imsiFee,startImsiNumber:a.startImsiNumber,needGtpRouter:a.needGtpRouter};e.submitFlag=!0,Object(n["r"])(r).then((function(t){if(!t||"0000"!=t.code)throw e.submitFlag=!1,t;setTimeout((function(){e.$Notice.success({title:"操作提醒：",desc:"操作成功！"}),e.submitFlag=!1,e.allocateModal=!1,e.goPageFirst(e.currentPage),e.cancelModal()}),1500)})).catch((function(t){e.submitFlag=!1})).finally((function(){}))}}))},resourceView:function(e){var t=1==this.showAllocate?"viewResources":"channelViewResources";this.$router.push({name:t,query:{corpId:e.corpId,corpName:e.corpName}})},callOrderDetails:function(e){var t=1==this.showAllocate?"callOrderDetails":"channelCallOrderDetails";this.$router.push({name:t,query:{corpId:e.corpId,corpName:e.corpName}})},billingStatistics:function(e){var t=1==this.showAllocate?"billingStatistics":"channelBillingStatistics";this.$router.push({name:t,query:{corpId:e.corpId,corpName:e.corpName}})},getsupplier:function(){var e=this;Object(o["d"])({pageNum:-1,pageSize:-1}).then((function(t){if("0000"==t.code){var a=[];t.data.forEach((function(e,t){"CMHK"==e.supplierName&&a.push(e)})),e.supplierListNo=a,e.supplierListYes=t.data}})).catch((function(e){console.error(e)})).finally((function(){}))},getChannel:function(){var e=this;Object(l["q"])({corpId:sessionStorage.getItem("corpId"),selfContain:!0}).then((function(t){"0000"===t.code&&(e.searchObj.corpName=t.data[0].corpName,e.tableData=[{corpName:t.data[0].corpName,corpId:t.data[0].corpId}])})).catch((function(e){console.log(e)}))},getInfoOrder:function(e,t){var a=this;Object(n["a"])({corpId:e,cooperationMode:"3"}).then((function(e){"0000"===e.code&&(a.a2zRuleList=e.data.atzChargings,a.imsiFeeList=Object.entries(e.data.freeImsiList).map((function(e){var t=Object(s["a"])(e,2),a=t[0],r=t[1];return{imsi:a,imsiName:r}})))})).catch((function(e){console.log(e)}))}}},u=c,p=(a("2281"),a("2877")),d=Object(p["a"])(u,r,i,!1,null,null,null),m=d.exports;t["a"]=m},"4fadc":function(e,t,a){"use strict";var r=a("23e7"),i=a("6f53").entries;r({target:"Object",stat:!0},{entries:function(e){return i(e)}})},"6f53":function(e,t,a){"use strict";var r=a("83ab"),i=a("d039"),s=a("e330"),n=a("e163"),o=a("df75"),l=a("fc6a"),c=a("d1e7").f,u=s(c),p=s([].push),d=r&&i((function(){var e=Object.create(null);return e[2]=2,!u(e,2)})),m=function(e){return function(t){var a,i=l(t),s=o(i),c=d&&null===n(i),m=s.length,f=0,h=[];while(m>f)a=s[f++],r&&!(c?a in i:u(i,a))||p(h,e?[a,i[a]]:i[a]);return h}};e.exports={entries:m(!0),values:m(!1)}},"841c":function(e,t,a){"use strict";var r=a("c65b"),i=a("d784"),s=a("825a"),n=a("7234"),o=a("1d80"),l=a("129f"),c=a("577e"),u=a("dc4a"),p=a("14c3");i("search",(function(e,t,a){return[function(t){var a=o(this),i=n(t)?void 0:u(t,e);return i?r(i,t,a):new RegExp(t)[e](c(a))},function(e){var r=s(this),i=c(e),n=a(t,r,i);if(n.done)return n.value;var o=r.lastIndex;l(o,0)||(r.lastIndex=0);var u=p(r,i);return l(r.lastIndex,o)||(r.lastIndex=o),null===u?-1:u.index}]}))},"8b16":function(e,t,a){"use strict";a.d(t,"o",(function(){return c})),a.d(t,"a",(function(){return u})),a.d(t,"n",(function(){return d})),a.d(t,"r",(function(){return m})),a.d(t,"p",(function(){return f})),a.d(t,"f",(function(){return h})),a.d(t,"g",(function(){return b})),a.d(t,"k",(function(){return g})),a.d(t,"q",(function(){return I})),a.d(t,"s",(function(){return v})),a.d(t,"b",(function(){return O})),a.d(t,"c",(function(){return S})),a.d(t,"e",(function(){return j})),a.d(t,"d",(function(){return N})),a.d(t,"j",(function(){return w})),a.d(t,"m",(function(){return y})),a.d(t,"i",(function(){return x})),a.d(t,"l",(function(){return F})),a.d(t,"h",(function(){return M}));var r=a("5530"),i=(a("1157"),a("66df")),s=a("4360"),n="/rms/api/v1/assigned",o="/stat/imsiFlow",l="/cms/channel",c=function(e){return i["a"].request({url:n+"/Imsi",data:e,method:"post"})},u=function(e){return i["a"].request({url:l+"/getInfo4Order2",params:e,method:"get"})},p=function(e){var t=sessionStorage.getItem("corpId");return i["a"].request({url:e.url,params:Object(r["a"])(Object(r["a"])({},e.data),{},{userId:t&&"null"!=t&&"undefined"!=t&&""!=t?t:s["a"].state.user.userId}),method:"post"})},d=function(e){return i["a"].request({url:l+"/distributors/getPage",params:e,method:"get"})},m=function(e){return i["a"].request({url:n+"/Imsi",data:e,method:"POST"})},f=function(e){return i["a"].request({url:n+"/getImsiPage",params:e,method:"get"})},h=function(e){return i["a"].request({url:n+"/checkImsi",params:e,method:"get"})},b=function(e){return i["a"].request({url:n+"/deleteImsi",params:e,method:"get"})},g=function(e){return i["a"].request({url:n+"/freezeImsi",params:e,method:"get"})},I=function(e){return i["a"].request({url:n+"/recoverImsi",params:e,method:"get"})},v=function(e){return i["a"].request({url:n+"/updateImsi",params:e,method:"get"})},O=function(e){return i["a"].request({url:n+"/deleteImsiList",data:e,method:"post"})},S=function(e){return i["a"].request({url:n+"/freezeImsiList",data:e,method:"post"})},j=function(e){return i["a"].request({url:n+"/recoverImsiList",data:e,method:"post"})},N=function(e){return i["a"].request({url:n+"/updateImsiList",data:e,method:"post"})},w=function(e){return i["a"].request({url:n+"/exportImsi",params:e,method:"get"})},y=function(e){return i["a"].request({url:o+"/getImsiFlow",params:e,method:"get"})},x=function(e){return p({url:o+"/exportResourceFlowDetail",data:e})},F=function(e){return i["a"].request({url:l+"/getResourceFlowDetail",params:e,method:"get"})},M=function(e){return p({url:l+"/exportResourceFlowDetail",data:e})}},cdcb:function(e,t,a){},e472:function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"e",(function(){return l})),a.d(t,"c",(function(){return c})),a.d(t,"b",(function(){return u}));var r=a("66df"),i="/rms/api/v1",s="/pms",n=function(e){return r["a"].request({url:i+"/supplier/selectSupplier",params:e,method:"get"})},o=function(e){return r["a"].request({url:i+"/supplier/saveSupplier",data:e,method:"post"})},l=function(e){return r["a"].request({url:i+"/supplier/updateSupplier",data:e,method:"post"})},c=function(e){return r["a"].request({url:i+"/supplier/queryShorten",data:e,method:"get"})},u=function(e){return r["a"].request({url:s+"/pms-realname/getMccList",data:e,method:"get"})}}}]);