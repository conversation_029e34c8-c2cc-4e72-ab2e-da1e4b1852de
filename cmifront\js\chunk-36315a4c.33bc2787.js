(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-36315a4c"],{"7ea5":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padiing:"16px"}},[t("div",{staticStyle:{display:"flex","justify-content":"center",margin:"20px 0"}},[t("Form",{ref:"formObj",attrs:{model:e.formObj,"label-width":150,rules:e.ruleAddValidate}},[t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐名称(简中)",prop:"nameCn"}},[t("Input",{attrs:{maxlength:100,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐名称(简中)"},model:{value:e.formObj.nameCn,callback:function(t){e.$set(e.formObj,"nameCn",t)},expression:"formObj.nameCn"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐名称(繁中)",prop:"nameTw"}},[t("Input",{attrs:{maxlength:100,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐名稱(繁中)"},model:{value:e.formObj.nameTw,callback:function(t){e.$set(e.formObj,"nameTw",t)},expression:"formObj.nameTw"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐名称(英文)",prop:"nameEn"}},[t("Input",{attrs:{maxlength:100,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"Please enter package name (EN)"},model:{value:e.formObj.nameEn,callback:function(t){e.$set(e.formObj,"nameEn",t)},expression:"formObj.nameEn"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐描述(简中)",prop:"descCn"}},[t("Input",{attrs:{maxlength:4e3,readonly:"Info"==e.typeFlag,type:"textarea",rows:3,placeholder:"请输入套餐描述(简中)"},model:{value:e.formObj.descCn,callback:function(t){e.$set(e.formObj,"descCn",t)},expression:"formObj.descCn"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐描述(繁中)",prop:"descTw"}},[t("Input",{attrs:{maxlength:4e3,readonly:"Info"==e.typeFlag,type:"textarea",rows:3,placeholder:"请输入套餐描述(繁中)"},model:{value:e.formObj.descTw,callback:function(t){e.$set(e.formObj,"descTw",t)},expression:"formObj.descTw"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐描述(英文)",prop:"descEn"}},[t("Input",{attrs:{maxlength:4e3,readonly:"Info"==e.typeFlag,type:"textarea",rows:3,placeholder:"Please enter package description (EN)"},model:{value:e.formObj.descEn,callback:function(t){e.$set(e.formObj,"descEn",t)},expression:"formObj.descEn"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"周期类型",prop:"periodUnit"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择周期类型",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},model:{value:e.formObj.periodUnit,callback:function(t){e.$set(e.formObj,"periodUnit",t)},expression:"formObj.periodUnit"}},e._l(e.periodUnitList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v("\n\t\t\t\t\t\t\t"+e._s(a.label))])})),1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"持续周期",prop:"keepPeriod"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:11,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入持续周期"},model:{value:e.formObj.keepPeriod,callback:function(t){e.$set(e.formObj,"keepPeriod",t)},expression:"formObj.keepPeriod"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐购买有效期(天)",prop:"effectiveDay"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:11,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐购买有效期(天)"},model:{value:e.formObj.effectiveDay,callback:function(t){e.$set(e.formObj,"effectiveDay",t)},expression:"formObj.effectiveDay"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐封面",prop:"picture"}},["Info"!=e.typeFlag?t("div",{staticStyle:{display:"flex","flex-direction":"column",width:"200px",height:"100px"}},[""==e.pictureUrl?t("Upload",{ref:"upload",attrs:{type:"drag",accept:"image/*",action:"#","before-upload":e.handleUpload,"show-upload-list":!1}},[t("div",{staticStyle:{padding:"20px",height:"100px",width:"100%"}},[t("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),t("p",[e._v("上传套餐封面图片")])],1)]):t("div",{staticStyle:{display:"flex","flex-direction":"column",width:"100%",height:"100%",border:"1px dashed #dcdee2","border-radius":"4px",position:"relative"}},[t("Icon",{staticClass:"mediaShowDelSty",attrs:{type:"md-close-circle",color:"#ff3300",size:"22"},on:{click:function(t){return e.cancelSelected()}}}),t("img",{staticStyle:{"object-fit":"contain"},attrs:{src:e.pictureUrl,width:"100%",height:"100%"},on:{click:function(t){e.pictureShowFlag=!0}}})],1)],1):t("div",{staticStyle:{display:"flex","flex-direction":"column",width:"200px",height:"100px"}},[t("div",{staticStyle:{display:"flex","flex-direction":"column",width:"100%",height:"100%",border:"1px dashed #dcdee2","border-radius":"4px",position:"relative"}},[t("img",{staticStyle:{"object-fit":"contain"},attrs:{src:e.pictureUrl,width:"100%",height:"100%"},on:{click:function(t){e.pictureShowFlag=!0}}})])])])],1),"Add"!=e.typeFlag?t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐状态",prop:"status"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择套餐状态",disabled:!0},model:{value:e.formObj.status,callback:function(t){e.$set(e.formObj,"status",t)},expression:"formObj.status"}},e._l(e.statusList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v(e._s(a.label)+"\n\t\t\t\t\t\t")])})),1)],1)],1):e._e()],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"终端厂商套餐",prop:"isTerminal"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"是否为终端厂商套餐",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":e.isTerminalChange},model:{value:e.formObj.isTerminal,callback:function(t){e.$set(e.formObj,"isTerminal",t)},expression:"formObj.isTerminal"}},[t("Option",{attrs:{value:"1"}},[e._v("是")]),t("Option",{attrs:{value:"2"}},[e._v("否")])],1)],1)],1),"1"==e.formObj.isTerminal?t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"选择厂商",prop:"corpId"}},[t("Select",{attrs:{placeholder:"请选择厂商",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},model:{value:e.formObj.corpId,callback:function(t){e.$set(e.formObj,"corpId",t)},expression:"formObj.corpId"}},e._l(e.corpIdList,(function(a,i){return t("Option",{key:i,attrs:{value:a.corpId}},[e._v("\n\t\t\t\t\t\t\t"+e._s(a.corpName))])})),1)],1)],1):e._e()],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"促销套餐",prop:"isPromotion"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"是否为促销套餐",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},model:{value:e.formObj.isPromotion,callback:function(t){e.$set(e.formObj,"isPromotion",t)},expression:"formObj.isPromotion"}},[t("Option",{attrs:{value:"1"}},[e._v("是")]),t("Option",{attrs:{value:"2"}},[e._v("否")])],1)],1)],1),t("Col",{attrs:{span:"12"}},["1"==e.formObj.isPromotion?t("FormItem",{attrs:{label:"促销限购份数",prop:"saleLimit"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:11,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入促销限购份数"},model:{value:e.formObj.saleLimit,callback:function(t){e.$set(e.formObj,"saleLimit",t)},expression:"formObj.saleLimit"}})],1):e._e()],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"流量限制类型",prop:"flowLimitType"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择流量限制类型",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},model:{value:e.formObj.flowLimitType,callback:function(t){e.$set(e.formObj,"flowLimitType",t)},expression:"formObj.flowLimitType"}},[t("Option",{attrs:{value:"1"}},[e._v("周期内限量")]),t("Option",{attrs:{value:"2"}},[e._v("按周期类型重置")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"达量后控制逻辑",prop:"controlLogic"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择达量后控制逻辑",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},model:{value:e.formObj.controlLogic,callback:function(t){e.$set(e.formObj,"controlLogic",t)},expression:"formObj.controlLogic"}},["1"!==e.formObj.isTerminal||"1"!==e.formObj.flowLimitType?t("Option",{attrs:{value:"1"}},[e._v("达量限速\n\t\t\t\t\t\t")]):e._e(),"1"===e.formObj.flowLimitType?t("Option",{attrs:{value:"2"}},[e._v("达量释放")]):e._e()],1)],1)],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"1"==e.formObj.isTerminal,expression:"formObj.isTerminal == '1'"}]},[t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"流量上限",prop:"flowLimitSum"}},[t("Input",{attrs:{maxlength:12,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入流量上限"},model:{value:e.formObj.flowLimitSum,callback:function(t){e.$set(e.formObj,"flowLimitSum",t)},expression:"formObj.flowLimitSum"}},[t("Select",{staticStyle:{width:"80px"},attrs:{slot:"append",disabled:"Info"==e.typeFlag},slot:"append",model:{value:e.formObj.flowLimitUnit,callback:function(t){e.$set(e.formObj,"flowLimitUnit",t)},expression:"formObj.flowLimitUnit"}},[t("Option",{attrs:{value:"1"}},[e._v("GB")]),t("Option",{attrs:{value:"2"}},[e._v("MB")])],1)],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"签约业务(高速)",prop:"signBizId"}},[t("Input",{attrs:{maxlength:50,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"签约业务(高速)"},model:{value:e.formObj.signBizId,callback:function(t){e.$set(e.formObj,"signBizId",t)},expression:"formObj.signBizId"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"签约业务(低速)",prop:"limitSignBizId"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:50,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入签约业务(低速)"},model:{value:e.formObj.limitSignBizId,callback:function(t){e.$set(e.formObj,"limitSignBizId",t)},expression:"formObj.limitSignBizId"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"签约业务(限速)",prop:"slowSignBizId"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:50,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入签约业务(限速)"},model:{value:e.formObj.slowSignBizId,callback:function(t){e.$set(e.formObj,"slowSignBizId",t)},expression:"formObj.slowSignBizId"}})],1)],1)],1)],1),t("Row",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.isTerminal,expression:"formObj.isTerminal == '2'"}]},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"是否支持热点",prop:"isSupportedHotspots"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择是否支持热点",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":function(t){return e.getcountryList(e.formObj.isSupportedHotspots)}},model:{value:e.formObj.isSupportedHotspots,callback:function(t){e.$set(e.formObj,"isSupportedHotspots",t)},expression:"formObj.isSupportedHotspots"}},[t("Option",{attrs:{value:1}},[e._v("是")]),t("Option",{attrs:{value:2}},[e._v("否")])],1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"允许订购开始时间",prop:"startTime"}},[t("DatePicker",{staticClass:"inputSty",attrs:{type:"datetime",format:"yyyy/MM/dd HH:mm:ss",placeholder:"请选择开始时间",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":e.changeTime},model:{value:e.formObj.startTime,callback:function(t){e.$set(e.formObj,"startTime",t)},expression:"formObj.startTime"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"允许订购结束时间",prop:"endTime"}},[t("DatePicker",{staticClass:"inputSty",attrs:{type:"datetime",format:"yyyy/MM/dd HH:mm:ss",placeholder:"请选择结束时间",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":e.changeTime},model:{value:e.formObj.endTime,callback:function(t){e.$set(e.formObj,"endTime",t)},expression:"formObj.endTime"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"计费激活流量限额",prop:"billFlowLimit"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:11,readonly:"Info"==e.typeFlag,placeholder:"请输入计费激活流量限额"},model:{value:e.formObj.billFlowLimit,callback:function(t){e.$set(e.formObj,"billFlowLimit",t)},expression:"formObj.billFlowLimit"}},[t("span",{attrs:{slot:"append"},slot:"append"},[e._v("MB")])])],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐价格(人民币)",prop:"cny"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:13,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐价格(人民币)"},model:{value:e.formObj.cny,callback:function(t){e.$set(e.formObj,"cny",t)},expression:"formObj.cny"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐价格(港币)",prop:"hkd"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:13,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐价格(港币)"},model:{value:e.formObj.hkd,callback:function(t){e.$set(e.formObj,"hkd",t)},expression:"formObj.hkd"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐价格(美元)",prop:"usd"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:13,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐价格(美元)"},model:{value:e.formObj.usd,callback:function(t){e.$set(e.formObj,"usd",t)},expression:"formObj.usd"}})],1)],1)],1),t("Row",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.isTerminal,expression:"formObj.isTerminal == '2'"}]},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"卡池绑定方式",prop:"bindCardPoolType"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":e.changeBindCardpoolType},model:{value:e.formObj.bindCardPoolType,callback:function(t){e.$set(e.formObj,"bindCardPoolType",t)},expression:"formObj.bindCardPoolType"}},[t("Option",{attrs:{value:1}},[e._v("关联卡池")]),t("Option",{attrs:{value:2}},[e._v("国家卡池关联组")])],1)],1)],1)],1),t("Row",[t("Col",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.bindCardPoolType,expression:"formObj.bindCardPoolType == '2'"}],attrs:{span:"24"}},[t("FormItem",{attrs:{label:"选择国家卡池关联组",prop:"groupId"}},[t("Select",{attrs:{multiple:"",placeholder:"选择国家卡池关联组",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,filterable:!0},model:{value:e.formObj.groupId,callback:function(t){e.$set(e.formObj,"groupId",t)},expression:"formObj.groupId"}},e._l(e.groupIdtList,(function(a,i){return t("Option",{key:a.groupId,attrs:{title:a.groupName,value:a.groupId}},[e._v(e._s(a.groupName.length>30?a.groupName.substring(0,30)+"…":a.groupName))])})),1)],1)],1)],1),t("Row",{directives:[{name:"show",rawName:"v-show",value:"1"==e.formObj.isTerminal,expression:"formObj.isTerminal == '1'"}]},[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"选择国家/地区",prop:"mccList"}},[t("Select",{attrs:{multiple:"",placeholder:"请选择国家/地区",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,filterable:!0},on:{"on-change":e.mccListChange},model:{value:e.formObj.mccList,callback:function(t){e.$set(e.formObj,"mccList",t)},expression:"formObj.mccList"}},e._l(e.continentList,(function(a){return t("Option",{key:a.id,attrs:{value:a.mcc}},[e._v(e._s(a.countryEn))])})),1)],1)],1)],1),t("Row",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.isTerminal,expression:"formObj.isTerminal == '2'"}]},[t("Col",{attrs:{span:"23"}},["1"==e.formObj.bindCardPoolType?t("FormItem",{attrs:{label:"选择国家/地区",prop:"mccList"}},[t("Select",{attrs:{multiple:"",placeholder:"请选择国家/地区",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,filterable:!0},on:{"on-change":e.mccListChange},model:{value:e.formObj.mccList,callback:function(t){e.$set(e.formObj,"mccList",t)},expression:"formObj.mccList"}},e._l(e.continentList,(function(a){return t("Option",{key:a.id,attrs:{value:a.mcc}},[e._v(e._s(a.countryEn))])})),1)],1):t("FormItem",{attrs:{label:"选择国家/地区",prop:"mccList"}},[t("Select",{attrs:{multiple:"",placeholder:"请选择国家/地区",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,filterable:!0},on:{"on-change":e.mccListChange},model:{value:e.formObj.mccList,callback:function(t){e.$set(e.formObj,"mccList",t)},expression:"formObj.mccList"}},e._l(e.continentList1,(function(a,i){return t("Option",{key:i,attrs:{value:i}},[e._v(e._s(a))])})),1)],1)],1),t("Col",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.bindCardPoolType,expression:"formObj.bindCardPoolType == '2'"}],attrs:{span:"1"}},[t("Button",{staticStyle:{"margin-left":"10px"},attrs:{type:"info",ghost:""},on:{click:e.showdetail}},[e._v("查看卡池")])],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.isTerminal,expression:"formObj.isTerminal == '2'"}]},e._l(e.formObj.packageConsumptionStr,(function(a,i){return t("Row",{key:i,staticStyle:{display:"flex","flex-wrap":"wrap","margin-bottom":"10px"}},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"用量值",prop:"packageConsumptionStr."+i+".consumption"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入用量值"},model:{value:a.consumption,callback:function(t){e.$set(a,"consumption",t)},expression:"item.consumption"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"选择模板",prop:"packageConsumptionStr."+i+".upccTemplateId"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入模板"},model:{value:a.upccTemplateId,callback:function(t){e.$set(a,"upccTemplateId",t)},expression:"item.upccTemplateId"}})],1)],1)],1)})),1),t("Row",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.isTerminal,expression:"formObj.isTerminal == '2'"}]},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"无上限模板",prop:"noLimitTemplateId"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"选择模板",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},model:{value:e.formObj.noLimitTemplateId,callback:function(t){e.$set(e.formObj,"noLimitTemplateId",t)},expression:"formObj.noLimitTemplateId"}},e._l(e.TemplateList,(function(a){return t("Option",{key:a.templateId,attrs:{title:a.templateDesc,value:a.templateId}},[e._v(e._s(a.templateName.length>30?a.templateName.substring(0,30)+"…":a.templateName))])})),1)],1)],1)],1),t("Row",[t("Col",{directives:[{name:"show",rawName:"v-show",value:"1"==e.formObj.bindCardPoolType||"1"==e.formObj.isTerminal,expression:"formObj.bindCardPoolType == '1' || formObj.isTerminal == '1'"}],attrs:{span:"24"}},[t("FormItem",{attrs:{label:"关联卡池",prop:"cardPool"}},[t("Button",{staticClass:"inputSty",attrs:{type:"dashed",long:"",disabled:0==e.formObj.mccList.length},on:{click:function(t){return e.loadCardPoolView(e.formObj.mccList)}}},[e._v("点击查看")])],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"是否支持加油包",prop:"hasRefuelPackage"}},[t("i-switch",{attrs:{size:"large",disabled:"Info"==e.typeFlag},model:{value:e.formObj.hasRefuelPackage,callback:function(t){e.$set(e.formObj,"hasRefuelPackage",t)},expression:"formObj.hasRefuelPackage"}},[t("span",{attrs:{slot:"open"},slot:"open"},[e._v("是")]),t("span",{attrs:{slot:"close"},slot:"close"},[e._v("否")])])],1)],1),t("Col",{attrs:{span:"12"}},[e.formObj.hasRefuelPackage?t("Button",{staticClass:"inputSty",attrs:{type:"dashed",long:""},on:{click:e.RefuelPackageList}},[e._v("加油包列表")]):e._e()],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},["2"==e.formObj.isTerminal?t("FormItem",{attrs:{label:"允许中国激活",prop:"supportChina"}},[t("i-switch",{attrs:{size:"large",disabled:"Info"==e.typeFlag},model:{value:e.formObj.supportChina,callback:function(t){e.$set(e.formObj,"supportChina",t)},expression:"formObj.supportChina"}},[t("span",{attrs:{slot:"open"},slot:"open"},[e._v("是")]),t("span",{attrs:{slot:"close"},slot:"close"},[e._v("否")])])],1):e._e()],1)],1),t("Row",["2"==e.formObj.isTerminal?t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐扣费模式",prop:"deductionModel"}},[t("Select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择套餐扣费模式",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":e.deductionModelChange},model:{value:e.formObj.deductionModel,callback:function(t){e.$set(e.formObj,"deductionModel",t)},expression:"formObj.deductionModel"}},[t("Option",{attrs:{value:"1"}},[e._v("标准模式")]),t("Option",{attrs:{value:"2"}},[e._v("绑定模式")])],1)],1)],1):e._e()],1),t("Row",["2"==e.formObj.isTerminal&&"2"==e.formObj.deductionModel?t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"扣费URL",prop:"deductionUrl"}},[t("Input",{attrs:{maxlength:100,readonly:"Info"==e.typeFlag,placeholder:"请输入扣费URL"},model:{value:e.formObj.deductionUrl,callback:function(t){e.$set(e.formObj,"deductionUrl",t)},expression:"formObj.deductionUrl"}})],1)],1):e._e()],1),"2"==e.formObj.isTerminal&&"1"==e.formObj.deductionModel?t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"是否支持定向流量",prop:"isSupportDirect"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择是否支持定向流量",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":function(t){return e.changeDirect(t)}},model:{value:e.formObj.isSupportDirect,callback:function(t){e.$set(e.formObj,"isSupportDirect",t)},expression:"formObj.isSupportDirect"}},[t("Option",{attrs:{value:"1"}},[e._v("是")]),t("Option",{attrs:{value:"2"}},[e._v("否")])],1)],1)],1)],1):e._e(),t("div",[t("directApp",{ref:"procedureEdit",attrs:{typeFlag:e.typeFlag,isSupportDirect:e.formObj.isSupportDirect,notClick:null,packageId:e.packageId},on:{onDirectApp:e.onDirectApp}})],1),t("Modal",{attrs:{title:"添加加油包","footer-hide":!0,"mask-closable":!1,width:"1000px"},on:{"on-cancel":e.cancelModal},model:{value:e.addRefuelModel,callback:function(t){e.addRefuelModel=t},expression:"addRefuelModel"}},[t("Table",{staticStyle:{width:"100%"},attrs:{columns:e.Unitedcolumns,data:e.Uniteddata,loading:e.Unitedloading},on:{"on-selection-change":e.handleRowChange,"on-select-cancel":e.cancelPackage,"on-select-all-cancel":e.cancelPackageAll}}),t("div",{staticStyle:{"margin-top":"15px"}},[t("Page",{attrs:{total:e.Unitedtotal,current:e.UnitedcurrentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.UnitedcurrentPage=t},"on-change":e.UnitedgoPage}})],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{staticStyle:{"margin-left":"8px"},on:{click:e.cancelModal}},[e._v("取消")]),t("Button",{attrs:{type:"primary"},on:{click:e.Confirm}},[e._v("确定")])],1)],1),"Info"!=e.typeFlag?t("div",{staticStyle:{"text-align":"center"}},[t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:e.submitFlag},on:{click:e.submit}},[e._v("提交")]),t("Button",{staticStyle:{"margin-left":"8px"},on:{click:e.reset}},[e._v("重置")])],1):e._e(),"Info"==e.typeFlag?t("div",{staticStyle:{"text-align":"center"}},[t("Button",{staticStyle:{width:"100px"},on:{click:e.reback}},[e._v("返回")])],1):e._e()],1)],1),t("Modal",{attrs:{title:"封面预览","footer-hide":!0,width:"532px"},model:{value:e.pictureShowFlag,callback:function(t){e.pictureShowFlag=t},expression:"pictureShowFlag"}},[t("div",{staticStyle:{display:"flex","justify-content":"center","align-items":"center",width:"500px"}},[t("img",{staticStyle:{"object-fit":"contain"},attrs:{src:e.pictureUrl,width:"100%"}})])]),t("Drawer",{attrs:{title:"关联卡池管理",width:"350","mask-closable":!1,styles:e.styles},on:{"on-close":e.drawerClose},model:{value:e.drawer,callback:function(t){e.drawer=t},expression:"drawer"}},["Info"!=e.typeFlag?t("Button",{staticStyle:{margin:"0 15px"},attrs:{type:"success",size:"small"},on:{click:e.cardPoolEdit}},[e._v("编辑")]):e._e(),t("Tree",{ref:"cardPool",staticClass:"demo-tree-render",attrs:{data:e.cardPoolTree,"empty-text":e.emptyText}}),"Info"!=e.typeFlag?t("div",{staticClass:"demo-drawer-footer"},[t("Button",{staticStyle:{"margin-right":"8px"},on:{click:e.drawerClose}},[e._v("取消")]),t("Button",{attrs:{type:"primary"},on:{click:function(t){return e.toSetCardPool()}}},[e._v("确定")])],1):e._e()],1),t("Modal",{attrs:{title:"卡池编辑","mask-closable":!1,width:"730px"},on:{"on-cancel":e.cardPoolEditConfirm},model:{value:e.cardPoolEditFlag,callback:function(t){e.cardPoolEditFlag=t},expression:"cardPoolEditFlag"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("Form",{ref:"cpEditForm",staticStyle:{"font-weight":"bold"},attrs:{model:e.filterSearchObj,inline:""}},[t("FormItem",[t("Input",{attrs:{type:"text",clearable:"",placeholder:"卡池名称"},model:{value:e.filterSearchObj.cpName,callback:function(t){e.$set(e.filterSearchObj,"cpName",t)},expression:"filterSearchObj.cpName"}})],1),t("FormItem",[t("Input",{attrs:{type:"text",clearable:"",placeholder:"供应商名称"},model:{value:e.filterSearchObj.sName,callback:function(t){e.$set(e.filterSearchObj,"sName",t)},expression:"filterSearchObj.sName"}})],1),t("FormItem",[t("Input",{attrs:{type:"text",clearable:"",placeholder:"国家/地区名称"},model:{value:e.filterSearchObj.cName,callback:function(t){e.$set(e.filterSearchObj,"cName",t)},expression:"filterSearchObj.cName"}})],1),t("FormItem",[t("Button",{attrs:{type:"primary",loading:e.cardPoolEditTreeLoad},on:{click:e.doCPTreeFilter}},[e._v("搜索")])],1)],1),t("div",{staticClass:"demo-spin-article"},[t("div",{staticStyle:{height:"295px","overflow-y":"auto"}},[t("Tree",{ref:"cardPool",staticClass:"demo-tree-render",attrs:{data:e.cardPoolEditTree,"empty-text":e.emptyText}})],1),e.cardPoolEditTreeLoad?t("Spin",{attrs:{size:"large",fix:""}}):e._e()],1)],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{attrs:{type:"primary"},on:{click:e.cardPoolEditConfirm}},[e._v("确定")])],1)])],1)},o=[],r=a("5530"),l=a("ade3"),s=(a("99af"),a("d81d"),a("14d9"),a("4e82"),a("a434"),a("e9c4"),a("4ec9"),a("a9e3"),a("b64b"),a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("3ca3"),a("159b"),a("ddb0"),a("2f62")),n=a("90fe"),c=a("78c0"),d=a("2b6c"),p=a("f7fa"),m=a("3177"),u=a("0aa3"),f=a("c70b"),b={components:{directApp:u["a"]},data:function(){var e,t=this,a=function(e,t,a){var i=/^(([1-9]\d{0,9})|0)(\.\d{1,2})?$/;return i.test(t)},i=function(e,t,a){var i=/^[0-9]\d*$/;return i.test(t)},o=function(e,t,a){if(t){var i=/^[1-9]\d*$/;return i.test(t)}a()};return{addRefuelModel:!1,searchObjloading:!1,Unitedloading:!1,searchObj:{gaspackname:"",gaspacknameid:""},Unitedcolumns:[{type:"selection",width:60,align:"center"},{title:"加油包ID",key:"id",minWidth:120,align:"center",tooltip:!0},{title:"加油包名称(简体中文)",key:"nameCn",minWidth:180,align:"center",tooltip:!0},{title:"加油包价格(人民币)",key:"cny",minWidth:180,align:"center",tooltip:!0},{title:"加油包价格(港币)",key:"hkd",minWidth:180,align:"center",tooltip:!0},{title:"加油包价格(美元)",key:"usd",minWidth:180,align:"center",tooltip:!0}],Unitedtotal:0,UnitedcurrentPage:1,Unitedpage:0,Uniteddata:[],submitFlag:!1,en:/^[0-9a-zA-Z\/\(\)\,\.\:\<\>\"\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/,mccListTemp:"",usageType:"1",drawer:!1,emptyText:"未查询到任何卡池数据",styles:{height:"calc(100% - 55px)",overflow:"auto",paddingBottom:"53px",position:"static"},continentList:[],continentList1:[],statusList:[{label:"待上架",value:"1"},{label:"正常",value:"2"},{label:"下架",value:"3"}],cardPoolList:[],typeFlag:"Info",formObj:(e={nameCn:"",nameTw:"",nameEn:"",descCn:"",descTw:"",descEn:"",cny:"",hkd:"",usd:"",mccList:[],periodUnit:"",keepPeriod:"",effectiveDay:"",isTerminal:"",corpId:"",signBizId:"",limitSignBizId:"",slowSignBizId:"",status:"",startTime:"",endTime:"",isPromotion:"",saleLimit:"",billFlowLimit:"",cardPool:[],hasRefuelPackage:!1,refuelList:[],picture:null,flowLimitType:"",controlLogic:"",flowLimitUnit:"",selectionTypes:[],supportChina:!1,deductionModel:"",deductionUrl:"",flowLimitSum:""},Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(e,"signBizId",""),"limitSignBizId",""),"slowSignBizId",""),"isSupportedHotspots",""),"packageConsumptionStr",[{index:0,consumption:"",upccTemplateId:""}]),"groupId",""),"noLimitTemplateName",""),"bindCardPoolType","1"),"mccMap",[]),"isSupportDirect","1"),Object(l["a"])(e,"directAppInfos",[])),index:1,pictureUrl:"",pictureShowFlag:!1,corpIdList:[],changedIsTerminal:["flowLimitSum","signBizId","limitSignBizId","slowSignBizId","isSupportedHotspots","noLimitTemplateId","bindCardPoolType"],ruleAddValidate:Object(l["a"])(Object(l["a"])({nameCn:[{required:!0,type:"string",message:"套餐名称(简中)不能为空"}],nameEn:[{validator:function(e,t,a){var i=/^[0-9a-zA-Z\/\(\)\,\.\:\"\<\>\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;return i.test(t)||""==t},message:"Package name (EN) format error"}],descCn:[{required:!0,type:"string",message:"套餐描述(简中)不能为空"}],descEn:[{validator:function(e,t,a){var i=/^[0-9a-zA-Z\/\(\)\,\.\:\"\<\>\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;return i.test(t)||""==t},message:"Package description (EN) format error"}],periodUnit:[{required:!0,type:"string",message:"周期类型不能为空"}],keepPeriod:[{required:!0,message:"持续周期不能为空"},{validator:i,message:"持续周期格式错误"},{validator:function(e,t,a){return Number(2147483647)>=Number(t)},message:"持续周期数值过大"}],effectiveDay:[{required:!0,message:"购买有效期(天)不能为空"},{validator:i,message:"购买有效期(天)格式错误"},{validator:function(e,t,a){return Number(2147483647)>=Number(t)},message:"购买有效期(天)数值过大"}],corpId:[{required:!0,type:"string",message:"厂商不能为空"}],status:[],isTerminal:[{required:!0,type:"string",message:"是否为厂商套餐不能为空"}],isPromotion:[{required:!0,type:"string",message:"是否为促销套餐不能为空"}],signBizId:[{required:!0,type:"string",message:"签约业务(高速)不能为空"}],limitSignBizId:[{required:!0,type:"string",message:"签约业务(低速)不能为空"}],slowSignBizId:[{required:!0,type:"string",message:"签约业务(限速)不能为空"}],isSupportedHotspots:[{required:!0,message:"是否支持热点不能为空"}],consumption:[{required:!0,message:"用量值不能为空"}],upccTemplateId:[{required:!0,message:"模板不能为空"}],noLimitTemplateId:[{required:!0,message:"无上限模板不能为空"}],startTime:[{required:!0,type:"date",message:"开始时间不能为空"}],endTime:[{required:!0,type:"date",message:"结束时间不能为空"}],saleLimit:[{required:!0,message:"促销限购份数不能为空"},{validator:i,message:"促销限购份数格式错误"},{validator:function(e,t,a){return Number(2147483647)>=Number(t)},message:"促销限购份数数值过大"}],flowLimitType:[{required:!0,message:"请选择流量限制类型"}],flowLimitSum:[{required:!0,message:"请选择输入流量上限"},{validator:function(e,t,a){var i=/^[1-9]\d*$/;return i.test(t)},message:"请输入正整数"}],controlLogic:[{required:!0,message:"请选择达量后控制逻辑"}],cny:[{required:!0,message:"套餐价格(人民币)不能为空"},{validator:a,message:"套餐价格(人民币)格式错误"}],hkd:[{required:!0,message:"套餐价格(港币)不能为空"},{validator:a,message:"套餐价格(港币)格式错误"}],usd:[{required:!0,message:"套餐价格(美元)不能为空"},{validator:a,message:"套餐价格(美元)格式错误"}],mccList:[{required:!0,type:"array",message:"支持国家/地区不能为空"}],billFlowLimit:[{validator:o,message:"激活流量限额大于0的正整数"},{validator:function(e,t,a){return Number(2147483647)>=Number(t)},message:"激活流量限额数值过大"}],bindCardPoolType:[{required:!0,message:"卡池绑定方式不能为空"}],groupId:[{required:!0,message:"国家卡池关联组不能为空"}],cardPool:[],picture:[{validator:function(e,a,i){return""!=t.pictureUrl||null!=t.formObj.picture},message:"封面图片不能为空"}]},"consumption",[{required:!0,message:"用量值不能为空"}]),"upccTemplateId",[{required:!0,type:"string",message:"选择模板不能为空"}]),periodUnitList:[{value:"1",label:"24小时"},{value:"2",label:"自然日"},{value:"3",label:"自然月"},{value:"4",label:"自然年"}],cardPoolEditFlag:!1,cardPoolTree:[],cardPoolEditTree:[],cardPoolEditTreeLoad:!1,filterPool:[],filterTempPool:[],totalPool:[],totalTempPool:[],cpcrvList:[],firstLoad:!1,filterSearchObj:{cpName:"",sName:"",cName:""},localMap:new Map,TemplateList:[],groupIdtList:[],packageId:""}},created:function(){var e=this;this.$nextTick((function(){e.changedIsTerminal.forEach((function(t){e.$set(e.ruleAddValidate[t][0],"required",!1)}))}))},methods:Object(r["a"])(Object(r["a"])({},Object(s["d"])(["closeTag"])),{},{onDirectApp:function(){this.formObj.directAppInfos=this.$refs["procedureEdit"].childMethod()},submit:function(){var e=this;"1"==this.formObj.isTerminal?(this.changedIsTerminal.forEach((function(t){e.ruleAddValidate["isSupportedHotspots"][0].required=!1,e.ruleAddValidate["noLimitTemplateId"][0].required=!1,e.ruleAddValidate["bindCardPoolType"][0].required=!1,e.ruleAddValidate["flowLimitSum"].required=!0,e.ruleAddValidate["signBizId"][0].required=!0,e.ruleAddValidate["limitSignBizId"][0].required=!0,e.ruleAddValidate["slowSignBizId"][0].required=!0})),this.$refs["formObj"].validate()):(this.changedIsTerminal.forEach((function(t){e.ruleAddValidate["isSupportedHotspots"][0].required=!0,e.ruleAddValidate["noLimitTemplateId"][0].required=!0,e.ruleAddValidate["bindCardPoolType"][0].required=!0,e.ruleAddValidate["flowLimitSum"]=[],e.ruleAddValidate["signBizId"][0].required=!1,e.ruleAddValidate["limitSignBizId"][0].required=!1,e.ruleAddValidate["slowSignBizId"][0].required=!1})),this.$refs["formObj"].validate()),this.$refs["formObj"].validate((function(t){if(t){var a=Object.assign({},e.formObj),i=[];if(a.hasRefuelPackage){i=JSON.parse(JSON.stringify(a.refuelList));for(var o=0;o<i.length;o++)i[o].cny=Number(f.format(100*Number(i[o].cny),12)),i[o].hkd=Number(f.format(100*Number(i[o].hkd),12)),i[o].usd=Number(f.format(100*Number(i[o].usd),12))}if(a.startTime>a.endTime)return e.$Notice.error({title:"操作提示",desc:"操作失败,允许订购时间有误"}),!1;a.cny=Number(f.format(100*Number(a.cny),12)),a.hkd=Number(f.format(100*Number(a.hkd),12)),a.usd=Number(f.format(100*Number(a.usd),12));var r=e.typeFlag,l=new FormData;l.append("nameCn",a.nameCn),l.append("nameTw",a.nameTw),l.append("nameEn",a.nameEn),l.append("descCn",a.descCn),l.append("descTw",a.descTw),l.append("descEn",a.descEn),l.append("cny",a.cny),l.append("hkd",a.hkd),l.append("usd",a.usd),l.append("mccList",a.mccList),l.append("periodUnit",a.periodUnit),l.append("keepPeriod",a.keepPeriod),l.append("effectiveDay",a.effectiveDay),l.append("isTerminal",a.isTerminal),l.append("corpId","1"==a.isTerminal?a.corpId:""),l.append("status",a.status),l.append("startTime",a.startTime),l.append("endTime",a.endTime),l.append("isPromotion",a.isPromotion),l.append("saleLimit","1"==a.isPromotion?a.saleLimit:""),l.append("billFlowLimit",a.billFlowLimit),l.append("refuelListStr",JSON.stringify(i)),l.append("deductionModel",a.deductionModel),l.append("deductionUrl",a.deductionUrl),"1"==a.bindCardPoolType?l.append("cpcrvListStr",JSON.stringify(e.cpcrvList)):l.append("groupId",a.groupId),"1"==e.formObj.isTerminal?(l.append("flowLimitSum",a.flowLimitSum),l.append("signBizId",a.signBizId),l.append("slowSignBizId",a.slowSignBizId),l.append("limitSignBizId",a.limitSignBizId)):(l.append("bindCardPoolType",a.bindCardPoolType),l.append("packageConsumptionStr",JSON.stringify(a.packageConsumptionStr)),l.append("noLimitTemplateId",a.noLimitTemplateId),l.append("isSupportedHotspots",a.isSupportedHotspots)),null!=a.picture&&l.append("file",a.picture),"Add"==r&&(e.submitFlag=!0,Object(c["a"])(l).then((function(t){if(!t||"0000"!=t.code)throw e.submitFlag=!1,t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),setTimeout((function(){e.submitFlag=!1,e.reback()}),1500)})).catch((function(t){e.submitFlag=!1}))),"Copy"==r&&(l.append("id",a.id),e.submitFlag=!0,Object(c["a"])(l).then((function(t){if(!t||"0000"!=t.code)throw e.submitFlag=!1,t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),setTimeout((function(){e.submitFlag=!1,e.reback()}),1500)})).catch((function(t){e.submitFlag=!1}))),"Update"==r&&(l.append("id",a.id),e.submitFlag=!0,Object(c["z"])(l).then((function(t){if(!t||"0000"!=t.code)throw e.submitFlag=!1,t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),setTimeout((function(){e.submitFlag=!1,e.reback()}),1500)})).catch((function(t){e.submitFlag=!1})))}}))},reset:function(){this.formObj=Object(l["a"])({nameCn:"",nameTw:"",nameEn:"",descCn:"",descTw:"",descEn:"",cny:"",hkd:"",usd:"",mccList:[],periodUnit:"",keepPeriod:"",effectiveDay:"",isTerminal:"",corpId:"",signBizId:"",limitSignBizId:"",status:"",startTime:"",endTime:"",isPromotion:"",saleLimit:"",billFlowLimit:"",cardPool:[],hasRefuelPackage:!1,refuelList:[],picture:null,selectionTypes:[],deductionModel:"",deductionUrl:"",packageConsumptionStr:[],bindCardPoolType:"1",noLimitTemplateId:"",groupId:"",flowLimitSum:"",slowSignBizId:""},"limitSignBizId",""),this.pictureUrl="",this.$refs["formObj"].resetFields()},getLocalList:function(){var e=this;Object(n["f"])().then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.continentList=a,e.continentList.sort((function(e,t){return e.countryEn.localeCompare(t.countryEn)}));var i=new Map;a.map((function(e,t){i.set(e.mcc,e.countryEn)})),e.localMap=i})).catch((function(e){})).finally((function(){}))},getLocalList2:function(){var e=this;Object(m["e"])({groupId:this.formObj.groupId,isSupportedHotspots:""}).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.continentList1=a})).catch((function(e){})).finally((function(){}))},handleRowChange:function(e){var t=this;this.selection=e,e.map((function(e,a){var i=!0;t.formObj.selectionTypes.map((function(t,a){e.id===t.id&&(i=!1)})),i&&t.formObj.selectionTypes.push(e)}))},cancelPackage:function(e,t){var a=this;this.formObj.selectionTypes.forEach((function(e,i){e.id===t.id&&a.formObj.selectionTypes.splice(i,1)}))},cancelPackageAll:function(e,t){this.formObj.selectionTypes=[]},getDetailsRefuelList:function(e){var t=this;this.Unitedloading=!0,Object(c["r"])({pageNum:e,pageSize:10,refuelID:this.searchObj.gaspacknameid,refuelName:this.searchObj.gaspackname,packageID:this.formObj.id}).then((function(a){if(!a||"0000"!=a.code)throw a;t.Uniteddata=a.data,t.Unitedtotal=a.count,t.UnitedcurrentPage=e,t.formObj.selectionTypes.forEach((function(e){a.data.forEach((function(a){a.id==e.id&&(t.$set(a,"_checked",!0),t.$set(a,"_disabled",!0))}))})),t.addRefuelModel=!0})).catch((function(e){})).finally((function(){t.Unitedloading=!1,t.searchObjloading=!1}))},RefuelPackageList:function(){this.getDetailsRefuelList(1)},deductionModelChange:function(){this.formObj.deductionUrl=""},search:function(){this.searchObjloading=!0,this.getDetailsRefuelList(1)},UnitedgoPage:function(e){this.getDetailsRefuelList(e)},addRefuelPackage:function(){var e=this.formObj.refuelList.length,t=this.formObj.refuelList[e-1];0==e||""!=t.nameCn&&""!=t.cny&&""!=t.hkd&&""!=t.hkd?this.formObj.refuelList.push({nameCn:"",nameTw:"",nameEn:"",flowValue:"",flowUnit:"",cny:"",hkd:"",usd:""}):this.$Message.error("请完善上条加油包信息")},delRefuelPackageBtn:function(e){this.formObj.refuelList.splice(e,1)},handleUpload:function(e){var t=this,a=e.type;if(-1!=a.indexOf("image")){this.formObj.picture=e,this.pictureUrl="";var i=new FileReader;i.readAsDataURL(e),i.onload=function(){var e=i.result;t.pictureUrl=e},this.$refs["formObj"].validateField("picture")}else this.$Notice.error({title:"操作提示",desc:"仅支持图片格式文件"});return!1},cancelSelected:function(){this.formObj.picture=null,this.pictureUrl="",this.$refs["formObj"].validateField("picture")},changeTime:function(){var e=this.formObj.startTime,t=this.formObj.endTime;""==e||""==t||e>t?this.formObj.status="":e>new Date?this.formObj.status="1":e<new Date&&t>new Date?this.formObj.status="2":t<new Date&&(this.formObj.status="3")},dateToStr:function(e){var t=new Date(e),a=t.getFullYear(),i=t.getMonth()+1,o=t.getDate(),r=t.getHours(),l=t.getMinutes(),s=t.getSeconds();return i<10&&(i="0"+i),o<10&&(o="0"+o),r<10&&(r="0"+r),l<10&&(l="0"+l),s<10&&(s="0"+s),a+"-"+i+"-"+o+" "+r+":"+l+":"+s},groupIdListChange:function(){var e=this;Object(c["k"])({isSupportedHotspots:this.formObj.isSupportedHotspots}).then((function(t){if(!t||"0000"!=t.code)throw t;e.groupIdtList=t.data})).catch((function(e){}))},mccListChange:function(e){this.firstLoad?this.firstLoad=!1:(this.mccListTemp="",this.formObj.cardPool=[],this.totalPool=[],this.cpcrvList=[],this.$refs["formObj"].validateField("cardPool"))},showdetail:function(){this.$router.push({path:"/cardPoolDetail",query:{packageId:encodeURIComponent(JSON.stringify(this.formObj.id))}})},cardPoolEdit:function(){this.filterSearchObj={cpName:"",sName:"",cName:""},this.filterRateList("","","","all","edit"),this.drawer=!1,this.cardPoolEditFlag=!0},doCPTreeFilter:function(){this.cardPoolEditTreeLoad=!0;var e=this;this.saveTreeIntoTotalPool(),this.filterRateList(this.filterSearchObj.cpName,this.filterSearchObj.sName,this.filterSearchObj.cName,"all","edit"),setTimeout((function(){e.cardPoolEditTreeLoad=!1}),500)},saveTreeIntoTotalPool:function(){if(this.totalPool.length>0){var e=new Map;this.filterPool.map((function(t,a){t.children.map((function(a,i){null!=a.rate&&0!=a.rate&&e.set(t.id+a.mcc,a.rate)}))})),this.totalPool.map((function(t,a){t.children.map((function(a,i){e.has(t.id+a.mcc)&&(a.rate=e.get(t.id+a.mcc))}))}))}},cardPoolEditConfirm:function(){this.filterRateList("","","","filled","show"),this.cardPoolEditFlag=!1,this.drawer=!0},cardPoolEditCancle:function(){this.filterRateList("","","","filled","show"),this.cardPoolEditFlag=!1,this.drawer=!0},Confirm:function(){},cancelModal:function(){this.addRefuelModel=!1},loadTreeData:function(e){var t=this,a=[],i=e.length;try{for(var o,r=function(){var i=l,r=e[i],s={title:r.poolName+"-("+r.supplierName+")",id:r.poolId,poolName:r.poolName,supplierName:r.supplierName,expand:!0,children:[]};if(r.regionList&&r.regionList.length>0){var n=function(){var a=o,l=r.regionList[a];s.children.push({expand:!0,poolId:r.poolId,poolName:r.poolName,supplierName:r.supplierName,countryCn:l.countryCn,countryTw:l.countryTw,countryEn:l.countryEn,mcc:l.mcc,rate:l.rate,render:function(o,l){l.root,l.node,l.data;return o("div",{style:{display:"flex",width:"100%",height:"25px",flexDirection:"row",alignItems:"center"}},[o("Tooltip",{props:{placement:"left",content:r.regionList[a].countryEn},style:{width:"100px",display:"inline-block"}},[o("div",{style:{width:"100px",height:"25px",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",lineHeight:"30px"}},r.regionList[a].countryEn+"：")]),o("input",{domProps:{type:"Number",value:void 0==r.regionList[a].rate?null:r.regionList[a].rate,placeholder:"请输入分配比列(%)",max:100,min:0,disabled:"Info"==t.typeFlag},style:{width:"150px",height:"20px",textAlign:"center",border:"#ccc 1px solid",borderRadius:"5px",mozBorderRadius:"5px",webkitBorderRadius:"5px",marginLeft:"8px"},on:{input:function(t){var o=t.target.value;e[i].regionList[a].rate=o,s.children[a].rate=o}}})])}})};for(o=0;o<r.regionList.length;o++)n()}a.push(s)},l=0;l<i;l++)r();this.totalPool=a}catch(s){this.totalPool=[]}},filterRateList:function(e,t,a,i,o){var r=[];this.totalPool.length>0&&this.totalPool.map((function(o,l){var s=null!=o.poolName&&-1!=o.poolName.indexOf(e),n=null!=o.supplierName&&-1!=o.supplierName.indexOf(t),c={title:o.title,id:o.id,poolName:o.poolName,supplierName:o.supplierName,expand:!0,children:[]};s&&n&&o.children.map((function(e,t){var o=null!=e.countryEn&&-1!=e.countryEn.indexOf(a);o&&("all"==i&&c.children.push(e),"filled"==i&&null!=e.rate&&""!=e.rate&&c.children.push(e))})),c.children.length>0&&r.push(c)})),0!=r.length?("edit"==o&&(this.cardPoolEditTree=[{title:"关联卡池",expand:!0,children:[]}],this.cardPoolEditTree[0].children=r.concat(),this.filterPool=r.concat(),this.$forceUpdate()),"show"==o&&(this.cardPoolTree=[{title:"关联卡池",expand:!0,children:[]}],this.cardPoolTree[0].children=r.concat(),this.filterPool=r.concat(),this.$forceUpdate())):(this.cardPoolEditTree=[],this.cardPoolTree=[],this.filterPool=[])},loadTotalRateList:function(){var e=[];this.totalPool.map((function(t,a){t.children.map((function(t,a){null!=t.rate&&e.push({poolId:t.poolId,poolName:t.poolName,mcc:t.mcc,rate:String(t.rate)})}))})),this.cpcrvList=e},firstLoadCardPool:function(e){var t=this,a=this.formObj.id;this.mccListTemp!=JSON.stringify(e)?(this.mccListTemp=JSON.stringify(e),Object(d["b"])({usageType:"1"==this.formObj.isTerminal?"3":"1",mccList:e,packageId:void 0==a?null:a,cardIsUpdate:!0}).then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;t.formObj.cardPool=a.data,t.formObj.cardPool.sort((function(e,t){return e.poolName.localeCompare(t.poolName)})),t.loadTreeData(t.formObj.cardPool),t.filterRateList("","","","filled","show"),t.loadTotalRateList()})).catch((function(e){})).finally((function(){}))):(this.loadTreeData(this.formObj.cardPool),this.filterRateList("","","","filled","show"),this.loadTotalRateList())},loadCardPoolView:function(e){var t=this,a=this.formObj.id;this.mccListTemp!=JSON.stringify(e)?(this.mccListTemp=JSON.stringify(e),Object(d["b"])({usageType:"1"==this.formObj.isTerminal?"3":"1",mccList:e,packageId:void 0==a?null:a,cardIsUpdate:!0}).then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;t.formObj.cardPool=a.data,t.formObj.cardPool.sort((function(e,t){return e.poolName.localeCompare(t.poolName)})),t.loadTreeData(t.formObj.cardPool),t.filterRateList("","","","filled","show"),t.drawer=!0})).catch((function(e){})).finally((function(){}))):(this.loadTreeData(this.formObj.cardPool),this.filterRateList("","","","filled","show"),this.drawer=!0)},drawerClose:function(){"Info"!=this.typeFlag&&(this.mccListTemp="",this.formObj.cardPool=[],this.cpcrvList=[],this.$refs["formObj"].validateField("cardPool")),this.drawer=!1},toSetCardPool:function(){var e=[],t=[];this.totalPool.length>0&&this.totalPool.map((function(a,i){a.children.map((function(a,i){null!=a.rate&&0!=a.rate&&(e.push({poolId:a.poolId,poolName:a.poolName,mcc:a.mcc,rate:String(a.rate)}),t.push(a.mcc))}))}));for(var a=new RegExp("^(\\d|[0-9]\\d|100)$"),i=!0,o=0;o<e.length;o++)if(!a.test(e[o].rate)&&""!=e[o].rate)return this.$Notice.warning({title:"操作提示",desc:"分配比输入错误(仅支持0-100)"}),i=!1,!1;var r=[];for(var l in e.map((function(e,t){var a=e.mcc;r[a]||(r[a]=[]),r[a].push({key:t,value:Number(e.rate)})})),r){var s=r[l];if(1==s.length)e[s[0].key].rate="100";else{var n=0;if(s.map((function(e,t){n+=e.value})),100!=n){var c=this.localMap.has(l)?this.localMap.get(l):"各国家";return this.$Notice.warning({title:"操作提示",desc:c+"分配比需满足100%"}),i=!1,!1}}if(!i)return!1}for(var d=this.formObj.mccList,p=0;p<d.length;p++)if(-1==t.indexOf(d[p])){c=this.localMap.has(d[p])?this.localMap.get(d[p]):"存在国家/地区";return this.$Notice.warning({title:"操作提示",desc:c+"未分配比例"}),i=!1,!1}if(!i)return!1;this.cpcrvList=e,this.$refs["formObj"].validateField("cardPool"),this.drawer=!1},isTerminalChange:function(e){"1"==e?(this.formObj.isSupportedHotspots="",this.formObj.noLimitTemplateId="",this.formObj.packageConsumptionStr=[],this.formObj.mccList=[],this.formObj.bindCardPoolType="",this.formObj.flowLimitSum="",this.getCompanyList()):(this.formObj.flowLimitSum="",this.formObj.signBizId="",this.formObj.limitSignBizId="",this.formObj.slowSignBizId=""),this.mccListTemp="",this.formObj.cardPool=[],this.cpcrvList=[],this.$refs["formObj"].validateField("cardPool"),this.formObj.deductionModel="",this.formObj.deductionUrl=""},getCompanyList:function(){var e=this;Object(p["p"])({pageNumber:1,pageSize:-1,corpType:"7"}).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.corpIdList=a.records})).catch((function(e){})).finally((function(){}))},reback:function(){this.$router.push({name:"packageIndex"})},changeBindCardpoolType:function(){"1"==this.formObj.bindCardPoolType&&(this.formObj.mccList=[],this.formObj.groupId="",this.getLocalList()),"2"==this.formObj.bindCardPoolType&&(this.formObj.cardPool=[],this.formObj.mccList=[])},getcountryList:function(e){e&&this.getSelectTemplate(e),e&&"2"==this.formObj.bindCardPoolType&&(this.formObj.mccList=[])},changeGroupId:function(e){this.formObj.mccList="",e&&this.getLocalList2()},getSelectTemplate:function(){var e=this;Object(c["w"])().then((function(t){if(!t||"0000"!=t.code)throw t;e.TemplateList=t.data})).catch((function(e){}))},removeTemplate:function(e){this.formObj.packageConsumptionStr.splice(e,1),this.index--},addTemplate:function(){this.formObj.packageConsumptionStr.push({consumption:"",upccTemplateId:""})}}),mounted:function(){var e=this;this.groupIdListChange();try{if(null!=this.$route.query.package){this.firstLoad=!0;var t=JSON.parse(decodeURIComponent(this.$route.query.package));this.formObj.mccMap=t.mccMap,"1"==t.isTerminal?(this.changedIsTerminal.forEach((function(t){e.$set(e.ruleAddValidate["isSupportedHotspots"][0],"required",!1),e.$set(e.ruleAddValidate["noLimitTemplateId"][0],"required",!1),e.$set(e.ruleAddValidate["flowLimitSum"][0],"required",!0),e.$set(e.ruleAddValidate["signBizId"][0],"required",!0),e.$set(e.ruleAddValidate["limitSignBizId"][0],"required",!0),e.$set(e.ruleAddValidate["slowSignBizId"][0],"required",!0)})),this.getLocalList()):this.changedIsTerminal.forEach((function(t){e.$set(e.ruleAddValidate["isSupportedHotspots"][0],"required",!0),e.$set(e.ruleAddValidate["noLimitTemplateId"][0],"required",!0),e.$set(e.ruleAddValidate["flowLimitSum"][0],"required",!1),e.$set(e.ruleAddValidate["signBizId"][0],"required",!1),e.$set(e.ruleAddValidate["limitSignBizId"][0],"required",!1),e.$set(e.ruleAddValidate["slowSignBizId"][0],"required",!1)})),"2"==t.bindCardpoolType?(this.formObj.groupId=t.groupId,this.formObj.isSupportedHotspots=t.isSupportedHotspots,this.getLocalList2()):this.getLocalList(),this.typeFlag=t.type,t.startTime=this.dateToStr(t.startTime),t.endTime=this.dateToStr(t.endTime),"1"==t.isTerminal&&this.getCompanyList(),this.formObj=Object.assign({},t),this.formObj.picture=null,this.formObj.selectionTypes=this.formObj.refuelIDList,this.formObj.billFlowLimit=0===this.formObj.billFlowLimit?"":this.formObj.billFlowLimit,this.formObj.hasRefuelPackage="1"===this.formObj.supportRefuel||(this.formObj.supportRefuel,!1),this.formObj.supportChina="1"===this.formObj.supportChina||(this.formObj.supportChina,!1),this.firstLoadCardPool(t.mccList),this.pictureUrl=t.coverUrl,this.formObj.isSupportedHotspots=Number(t.isSupportedHotspots),this.getcountryList(this.formObj.isSupportedHotspots),this.formObj.packageConsumptionStr=[],t.packageConsumptions.map((function(t){e.formObj.packageConsumptionStr.push({consumption:t.displayConsumption,upccTemplateId:t.templateName})})),this.formObj.bindCardPoolType=Number(t.bindCardpoolType),this.packageId=t.id}}catch(a){}}},h=b,g=(a("ba89"),a("2877")),y=Object(g["a"])(h,i,o,!1,null,"728f8658",null);t["default"]=y.exports},ba89:function(e,t,a){"use strict";a("f405")},f405:function(e,t,a){}}]);