(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3d3a637e"],{"00b4":function(e,t,a){"use strict";a("ac1f");var r=a("23e7"),i=a("c65b"),n=a("1626"),s=a("825a"),o=a("577e"),c=function(){var e=!1,t=/[ac]/;return t.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===t.test("abc")&&e}(),l=/./.test;r({target:"RegExp",proto:!0,forced:!c},{test:function(e){var t=s(this),a=o(e),r=t.exec;if(!n(r))return i(l,t,a);var c=i(r,t,a);return null!==c&&(s(c),!0)}})},"3e70":function(e,t,a){},"53dd":function(e,t,a){"use strict";a("3e70")},"87c6":function(e){e.exports=JSON.parse("{}")},8963:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"d",(function(){return s})),a.d(t,"c",(function(){return o})),a.d(t,"e",(function(){return c})),a.d(t,"a",(function(){return l}));var r=a("66df"),i="/sys/api/v1",n=function(e){return r["a"].request({url:i+"/user/pagePrivilegesByRole/tree",params:e,method:"get"})},s=function(e){return r["a"].request({url:i+"/user/pagePrivilegesByUser/tree",params:e,method:"get"})},o=function(e){return r["a"].request({url:i+"/role/roles",data:e,method:"post"})},c=function(e){return r["a"].request({url:i+"/role/privileges/set",data:e,method:"post"})},l=function(e){return r["a"].request({url:i+"/role/add",params:e,method:"post"})}},b3b5:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",[t("Card",[t("div",{staticClass:"search_head"},[t("span",{staticStyle:{"font-weight":"bold"}},[e._v(e._s(e.$t("sys.roleName"))+"：")]),t("Input",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:e.$t("sys.enterName"),clearable:""},model:{value:e.roleName,callback:function(t){e.roleName=t},expression:"roleName"}}),t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticClass:"search-bt",attrs:{type:"primary",icon:"md-search"},on:{click:function(t){return e.searchByCondition()}}},[e._v(e._s(e.$t("common.search")))]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{icon:"md-add",type:"success"},on:{click:function(t){return e.showUserModal(null,0)}}},[e._v(e._s(e.$t("sys.newRole")))])],1),t("div",{staticStyle:{"margin-top":"20px"}},[t("Table",{attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.loading},scopedSlots:e._u([{key:"priv",fn:function(a){var r=a.row;return[t("a",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],attrs:{href:"#"},on:{click:function(t){return e.searchLimit(r.id)}}},[e._v(e._s(e.$t("sys.viewPermissions")))])]}}])})],1),t("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[t("Page",{attrs:{"page-size":e.pageSize,total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1)]),t("Drawer",{attrs:{title:e.$t("sys.rolePermissionManagement"),width:"350","mask-closable":!0,styles:e.styles},on:{"on-close":function(t){e.drawer=!1,e.setLoading=!1}},model:{value:e.drawer,callback:function(t){e.drawer=t},expression:"drawer"}},[t("Tree",{directives:[{name:"has",rawName:"v-has",value:{have:["setPriv","view"]},expression:"{'have':['setPriv','view']}"}],ref:"treeOne",attrs:{data:e.treeData,"show-checkbox":"",multiple:"","empty-text":e.emptyText}}),t("Tree",{directives:[{name:"has",rawName:"v-has",value:{have:["view"],haveNot:["setPriv"]},expression:"{'have':['view'],'haveNot':['setPriv']}"}],ref:"treeOne",attrs:{data:e.treeData,"show-checkbox":!1,multiple:"","empty-text":e.emptyText}}),t("div",{directives:[{name:"has",rawName:"v-has",value:["setPriv"],expression:"['setPriv']"}],staticClass:"demo-drawer-footer"},[t("Button",{staticStyle:{"margin-right":"8px"},on:{click:function(t){e.drawer=!1,e.setLoading=!1}}},[e._v(e._s(e.$t("common.cancel")))]),t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary"},on:{click:function(t){e.drawer=!1,e.toSetRoleLimits()}}},[e._v(e._s(e.$t("common.determine")))])],1)],1),t("Modal",{attrs:{title:e.$t("sys.editAccount"),"mask-closable":!1},on:{"on-cancel":e.cancelModal},model:{value:e.userModal,callback:function(t){e.userModal=t},expression:"userModal"}},[e.userModal?t("Form",{ref:"editForm",attrs:{model:e.modalData,rules:e.rules},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.addUser.apply(null,arguments)}}},[t("FormItem",{attrs:{prop:"roleName"}},[t("div",{staticClass:"input_modal"},[t("span",{staticStyle:{width:"2%",color:"red"}},[e._v("*")]),t("span",{staticStyle:{width:"13%"}},[e._v(e._s(e.$t("sys.roleName"))+"：")]),t("Input",{staticStyle:{width:"85%"},attrs:{placeholder:e.$t("sys.enterName")},model:{value:e.modalData.roleName,callback:function(t){e.$set(e.modalData,"roleName",t)},expression:"modalData.roleName"}})],1)])],1):e._e(),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.cancelModal}},[e._v(e._s(e.$t("common.cancel")))]),t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary"},on:{click:e.addUser}},[e._v(e._s(e.$t("common.determine")))])],1)],1)],1)},i=[],n=(a("d9e2"),a("99af"),a("caad"),a("d81d"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("00b4"),a("2532"),a("5319"),a("498a"),a("87c6"),a("8963")),s={data:function(){var e=this,t=function(t,a,r){var i=/^[^\s]+(\s+[^\s]+)*$/;a&&0!=a.length?i.test(a)?r():r(new Error(e.$t("sys.characterSpaces"))):r(new Error(e.$t("sys.enterName")))};return{rules:{roleName:[{validator:t,trigger:"blur"}]},corpId:"",userModal:!1,modalData:{},roleName:"",roleId:"",setLoading:!1,getLoading:!1,phonenum:"",emptyText:"未查询到任何权限数据",drawer:!1,allLimitsData:[],treeData:[],data4:[],styles:{height:"calc(100% - 55px)",overflow:"auto",paddingBottom:"53px",position:"static"},columns:[{title:this.$t("sys.persona"),key:"roleName",align:"center"}],public:[{title:this.$t("sys.purview"),slot:"priv",align:"center"}],tableData:[],loading:!1,currentPage:1,pageSize:10,total:0}},computed:{},methods:{searchByCondition:function(){this.goPageFirst(0)},goPageFirst:function(e){var t=this;this.page=e,this.loading=!0,Object(n["c"])({pageSize:this.pageSize,page:e,roleName:this.roleName.trim()}).then((function(e){if("0000"!==e.code)throw resp;t.tableData=e.data.records,t.total=e.data.total,t.loading=!1})).catch((function(e){t.loading=!1}))},showUserModal:function(e,t){this.optType=t,this.modalData=0==t?{roleName:null}:e,this.userModal=!0},cancelModal:function(){this.modalData=[{roleName:"",corp:""}],this.userModal=!1},addUser:function(){var e=this;this.$refs.editForm.validate((function(t){t&&Object(n["a"])(e.modalData).then((function(t){if("0000"!==t.code)throw resp;e.$Notice.success({title:e.$t("common.Successful"),desc:e.$t("sys.successAddedRole")}),e.cancelModal(),e.goPageFirst(0)})).catch((function(t){e.loading=!1}))}))},goPage:function(e){this.goPageFirst(e)},searchLimit:function(e){this.roleId=e,this.showDrawer(e)},showDrawer:function(e){var t=this;this.getLoading=!0,Object(n["d"])({userId:this.$store.state.user.userId,isEnglish:this.corpId&&"en-US"===this.$i18n.locale?1:void 0}).then((function(a){if("0000"===a.code){var r=a.data,i=JSON.parse(JSON.stringify(r).replace(/privilName/g,"title"));Object(n["b"])({roleId:e,isEnglish:t.corpId&&"en-US"===t.$i18n.locale?1:void 0}).then((function(e){t.treeData=t.getTreeDataUtil(i,e.data),t.drawer=!0,t.getLoading=!1})).catch((function(e){t.getLoading=!1,t.error(t.$t("sys.failedObtainRole"))}))}})).catch((function(e){t.getLoading=!1})).finally((function(){t.getLoading=!1,t.loading=!1}))},getTreeDataUtil:function(e,t){try{for(var a=0;a<t.length;a++)for(var r=0;r<e.length;r++)if(t[a].id==e[r].id){if(!t[a].children){e[r].expand=!0,e[r].checked=!0;break}for(var i=0;i<t[a].children.length;i++)for(var n=0;n<e[r].children.length;n++)if(t[a].children[i].id==e[r].children[n].id){if(!t[a].children[i].children){e[r].children[n].expand=!0,e[r].expand=!0,e[r].children[n].checked=!0;break}for(var s=0;s<t[a].children[i].children.length;s++)for(var o=0;o<e[r].children[n].children.length;o++)if(t[a].children[i].children[s].id==e[r].children[n].children[o].id){if(!t[a].children[i].children[s].children){e[r].children[n].children[o].expand=!0,e[r].expand=!0,e[r].children[n].expand=!0,e[r].children[n].children[o].checked=!0;break}for(var c=0;c<t[a].children[i].children[s].children.length;c++)for(var l=0;l<e[r].children[n].children[o].children.length;l++)if(t[a].children[i].children[s].children[c].id==e[r].children[n].children[o].children[l].id){e[r].children[n].children[o].children[l].expand=!0,e[r].expand=!0,e[r].children[n].expand=!0,e[r].children[n].children[o].expand=!0,e[r].children[n].children[o].children[l].checked=!0;break}}}}return e}catch(d){throw"error"}},toSetRoleLimits:function(){var e=this;this.setLoading=!0;var t=[];try{t=this.$refs.treeOne.getCheckedAndIndeterminateNodes().map((function(e){return{privilegeId:e.id,category:e.category}})),Object(n["e"])({roleId:this.roleId,privileges:t}).then((function(t){"0000"===t.code&&e.$Notice.success({title:e.$t("common.Successful"),desc:e.$t("sys.rolePpermissionsUpdated")}),e.setLoading=!1})).catch((function(t){e.setLoading=!1,e.loading=!1}))}catch(a){this.setLoading=!1,this.loading=!1}},error:function(e){this.$Notice.error({title:this.$t("sys.wrong"),desc:e||this.$t("sys.Serverwrong")})}},beforeMount:function(){var e=this.$route.meta.permTypes;(e.includes("view")||e.includes("setPriv"))&&(this.columns=this.columns.concat(this.public))},mounted:function(){this.$i18n.locale;this.corpId=sessionStorage.getItem("corpId"),this.goPageFirst(0)},watch:{getLoading:function(e){1==e?this.loadmsg=this.$Message.loading({content:this.$t("sys.obtainPermissionData"),duration:0}):setTimeout(this.loadmsg,1)},setLoading:function(e){1==e?this.loadmsg=this.$Message.loading({content:this.$t("sys.obtainPermissionData"),duration:0}):setTimeout(this.loadmsg,1)}}},o=s,c=(a("53dd"),a("2877")),l=Object(c["a"])(o,r,i,!1,null,null,null);t["default"]=l.exports}}]);