(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3dd62379"],{"005b":function(t,e,a){},"00b4":function(t,e,a){"use strict";a("ac1f");var o=a("23e7"),n=a("c65b"),i=a("1626"),r=a("825a"),s=a("577e"),c=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),l=/./.test;o({target:"RegExp",proto:!0,forced:!c},{test:function(t){var e=r(this),a=s(t),o=e.exec;if(!i(o))return n(l,e,a);var c=n(o,e,a);return null!==c&&(r(c),!0)}})},"0b43":function(t,e,a){"use strict";var o=a("04f8");t.exports=o&&!!Symbol["for"]&&!!Symbol.keyFor},"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"18e7":function(t,e,a){"use strict";a.d(e,"t",(function(){return n})),a.d(e,"q",(function(){return i})),a.d(e,"y",(function(){return r})),a.d(e,"g",(function(){return s})),a.d(e,"m",(function(){return c})),a.d(e,"j",(function(){return l})),a.d(e,"u",(function(){return d})),a.d(e,"r",(function(){return u})),a.d(e,"s",(function(){return p})),a.d(e,"i",(function(){return f})),a.d(e,"b",(function(){return g})),a.d(e,"a",(function(){return m})),a.d(e,"v",(function(){return h})),a.d(e,"o",(function(){return v})),a.d(e,"p",(function(){return b})),a.d(e,"e",(function(){return y})),a.d(e,"d",(function(){return x})),a.d(e,"w",(function(){return I})),a.d(e,"n",(function(){return S})),a.d(e,"x",(function(){return w})),a.d(e,"h",(function(){return k})),a.d(e,"f",(function(){return _})),a.d(e,"l",(function(){return C})),a.d(e,"k",(function(){return R})),a.d(e,"c",(function(){return T}));var o=a("66df"),n=function(t){return o["a"].request({url:"/aep/IBoss/getPage",data:t,method:"post"})},i=function(t){return o["a"].request({url:"/aep/blankCardOrder/getPayInfo",data:t,method:"post"})},r=function(t){return o["a"].request({url:"/aep/blankCardOrder/payConfirm",data:t,method:"POST"})},s=function(t){return o["a"].request({url:"/aep/blankCardOrder/download",data:t,method:"POST",responseType:"blob"})},c=function(t){return o["a"].request({url:"/aep/channelSellData/getPage",data:t,method:"post"})},l=function(t){return o["a"].request({url:"/aep/channelIncome/queryTodo",data:t,method:"post"})},d=function(t){return o["a"].request({url:"/aep/channelSellData/pushFinish",data:t,method:"post"})},u=function(t){return o["a"].request({url:"/aep/getreminders/getPage",data:t,method:"post"})},p=function(t){return o["a"].request({url:"/aep/getreminders/pushFinish",data:t,method:"post"})},f=function(t){return o["a"].request({url:"/aep/channelPush/getChannelPromiseDTO",data:t,method:"post"})},g=function(t){return o["a"].request({url:"/aep/channelPush/finish",data:t,method:"post"})},m=function(t){return o["a"].request({url:"/aep/IBoss/auth",data:t,method:"post"})},h=function(t){return o["a"].request({url:"/aep/IBoss/pushFinish",data:t,method:"post"})},v=function(t){return o["a"].request({url:"/aep/getTodoStatus/?todoUniqueId="+t.todoUniqueId,data:t,method:"post"})},b=function(t){return o["a"].request({url:"/aep/sso/ssoLogin",data:t,method:"post"})},y=function(t){return o["a"].request({url:"/aep/IBoss/download",data:t,method:"post",responseType:"blob"})},x=function(t){return o["a"].request({url:"/aep/file/download",data:t,method:"post",responseType:"blob"})},I=function(t){return o["a"].request({url:"/aep/blankCardOrder/operatorConfirm",data:t,method:"post"})},S=function(t){return o["a"].request({url:"/aep/channelSelfServer/deposit/record",data:t,method:"post"})},w=function(t){return o["a"].request({url:"/aep/channelSelfServer/deposit/salesAppro",data:t,method:"post"})},k=function(t){return o["a"].request({url:"/aep/channelSelfServer/deposit/finanAppro",data:t,method:"post"})},_=function(t){return o["a"].request({url:"/aep/getreminders/download",data:t,method:"post",responseType:"blob"})},C=function(t){return o["a"].request({url:"/aep/getreminders/getOpinion",data:t,method:"post"})},R=function(t){return o["a"].request({url:"/aep/channelSelfServer/deposit/getInvoiceAuditRecord",data:t,method:"post"})},T=function(t){return o["a"].request({url:"/aep/IBoss/cnInvoiceDownload",data:t,method:"post",responseType:"blob"})}},"466d":function(t,e,a){"use strict";var o=a("c65b"),n=a("d784"),i=a("825a"),r=a("7234"),s=a("50c4"),c=a("577e"),l=a("1d80"),d=a("dc4a"),u=a("8aa5"),p=a("14c3");n("match",(function(t,e,a){return[function(e){var a=l(this),n=r(e)?void 0:d(e,t);return n?o(n,e,a):new RegExp(e)[t](c(a))},function(t){var o=i(this),n=c(t),r=a(e,o,n);if(r.done)return r.value;if(!o.global)return p(o,n);var l=o.unicode;o.lastIndex=0;var d,f=[],g=0;while(null!==(d=p(o,n))){var m=c(d[0]);f[g]=m,""===m&&(o.lastIndex=u(n,s(o.lastIndex),l)),g++}return 0===g?null:f}]}))},"57b9":function(t,e,a){"use strict";var o=a("c65b"),n=a("d066"),i=a("b622"),r=a("cb2d");t.exports=function(){var t=n("Symbol"),e=t&&t.prototype,a=e&&e.valueOf,s=i("toPrimitive");e&&!e[s]&&r(e,s,(function(t){return o(a,this)}),{arity:1})}},"5a47":function(t,e,a){"use strict";var o=a("23e7"),n=a("04f8"),i=a("d039"),r=a("7418"),s=a("7b0b"),c=!n||i((function(){r.f(1)}));o({target:"Object",stat:!0,forced:c},{getOwnPropertySymbols:function(t){var e=r.f;return e?e(s(t)):[]}})},"779e":function(t,e,a){"use strict";a("005b")},"841c":function(t,e,a){"use strict";var o=a("c65b"),n=a("d784"),i=a("825a"),r=a("7234"),s=a("1d80"),c=a("129f"),l=a("577e"),d=a("dc4a"),u=a("14c3");n("search",(function(t,e,a){return[function(e){var a=s(this),n=r(e)?void 0:d(e,t);return n?o(n,e,a):new RegExp(e)[t](l(a))},function(t){var o=i(this),n=l(t),r=a(e,o,n);if(r.done)return r.value;var s=o.lastIndex;c(s,0)||(o.lastIndex=0);var d=u(o,n);return c(o.lastIndex,s)||(o.lastIndex=s),null===d?-1:d.index}]}))},a3a1:function(t,e,a){"use strict";a.r(e);var o=a("ade3"),n=(a("caad"),a("b0c0"),function(){var t=this,e=t._self._c;return e("div",{class:["4"].includes(t.pageType)?"content-box1":"content-box"},[e("a",{ref:"downloadLink",staticStyle:{display:"none"}}),["1","2","3"].includes(t.pageType)?e("Card",{staticStyle:{width:"90%",height:"100%","overflow-y":"auto"}},[t.spinShow?e("Spin",{attrs:{size:"large",fix:""}},[e("Icon",{staticClass:"demo-spin-icon-load",attrs:{type:"ios-loading",size:"24"}}),e("div",[t._v("Loading")])],1):t._e(),e("h1",{staticStyle:{"align-items":"center",padding:"30px 0"}},[t._v("调账审批详情页")]),e("Row",{staticClass:"row-box"},[e("Col",{staticClass:"col-box",attrs:{xs:24,md:12,lg:8,span:"8"}},[e("span",{staticClass:"nowarp"},[t._v("申请编号：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.applyId))])]),e("Col",{staticClass:"col-box",attrs:{xs:24,md:12,lg:8,span:"8"}},[e("span",{staticClass:"nowarp"},[t._v("调整方式：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.adjustWay))])]),e("Col",{staticClass:"col-box",attrs:{xs:24,md:12,lg:8,span:"8"}},[e("span",{staticClass:"nowarp"},[t._v("调账类型：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.adjustType))])]),e("Col",{staticClass:"col-box",attrs:{xs:24,md:12,lg:8,span:"8"}},[e("span",{staticClass:"nowarp"},[t._v("客户名称：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.corpName))])]),e("Col",{staticClass:"col-box",attrs:{xs:24,md:12,lg:8,span:"8"}},[e("span",{staticClass:"nowarp"},[t._v("业务类型：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.businessType))])]),e("Col",{staticClass:"col-box",attrs:{xs:24,md:12,lg:8,span:"8"}},[e("span",{staticClass:"nowarp"},[t._v("EBSCode：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.ebscode))])]),e("Col",{staticClass:"col-box",attrs:{xs:24,md:12,lg:8,span:"8"}},[e("span",{staticClass:"nowarp"},[t._v("原账单发票号：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.invoiceNo))])]),e("Col",{staticClass:"col-box",attrs:{xs:24,md:12,lg:8,span:"8"}},[e("span",{staticClass:"nowarp"},[t._v("调账单号：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.adjustId))])]),e("Col",{staticClass:"col-box",attrs:{xs:24,md:12,lg:8,span:"8"}},[e("span",{staticClass:"nowarp"},[t._v("调账总额：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.totalAdjustment))])]),e("Col",{staticClass:"col-box",attrs:{span:"24"}},[e("span",{staticClass:"nowarp"},[t._v("调账原因：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.adjustReason))])]),e("Col",{staticClass:"col-box",attrs:{span:"24"}},[e("span",{staticClass:"nowarp"},[t._v("调账描述：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.message))])]),"3"==t.pageType?e("Col",{staticClass:"col-box",attrs:{span:"24"}},[e("span",{staticClass:"nowarp"},[t._v("审批结果：")]),t._v("  "),e("span",[t._v(t._s(t.propinfo.authStatus)+t._s(t.propinfo.noPassReason))])]):t._e()],1),e("Row",{staticClass:"row-box2"},[e("Col",{staticClass:"col-box",attrs:{xs:24,md:12,lg:8,span:"8"}},[e("span",[t._v("说明文件：")]),t._v("  \n      "),e("Button",{attrs:{type:"warning",ghost:"",loading:t.downInfoLoading},on:{click:function(e){return t.rebuild("1")}}},[t._v("点击下载")])],1),"审核完成"==t.propinfo.authStatus&&"3"==t.pageType?e("Col",{staticClass:"col-box",attrs:{xs:24,md:12,lg:8,span:"8"}},[e("span",[t._v("重新生成Invoice：")]),t._v("  \n      "),e("Button",{attrs:{type:"info",ghost:"",loading:t.downLoading},on:{click:function(e){return t.rebuild("2")}}},[t._v("点击下载")])],1):t._e()],1),e("div",{staticClass:"table_box"},[e("Table",{attrs:{columns:t.columns,data:t.data,ellipsis:!0,loading:t.loading}})],1),e("div",{staticClass:"op_btn_box"},["1"==t.pageType||"2"==t.pageType?e("div",[e("Button",{staticStyle:{margin:"0 4px"},attrs:{type:"success",disabled:t.showButton,loading:t.passLoading},on:{click:function(e){return t.approval("1",t.pageType)}}},[t._v("审批通过")]),e("Button",{staticStyle:{margin:"0 4px"},attrs:{type:"error",disabled:t.showButton},on:{click:function(e){return t.approval("2",t.pageType)}}},[t._v("审批不通过")])],1):t._e(),"3"==t.pageType?e("div",[e("Button",{staticStyle:{margin:"0 4px"},attrs:{type:"success",disabled:t.showButton,loading:t.receivedLoading},on:{click:t.received}},[t._v("已阅")])],1):t._e()]),e("Modal",{attrs:{title:"确认执行审批不通过？","mask-closable":!1,width:"60%"},on:{"on-cancel":t.cancelModal},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[e("Form",{ref:"formItemReason",attrs:{model:t.formItemReason,rules:t.ruleValidate},nativeOn:{submit:function(t){t.preventDefault()}}},[e("FormItem",{attrs:{prop:"reasonText"}},[e("Input",{attrs:{maxlength:"200",placeholder:"请输入不通过原因……"},model:{value:t.formItemReason.reasonText,callback:function(e){t.$set(t.formItemReason,"reasonText",e)},expression:"formItemReason.reasonText"}})],1)],1),e("div",{staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{staticStyle:{"margin-left":"8px"},on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary",loading:t.noPassLoading},on:{click:t.confirm}},[t._v("确定")])],1)],1)],1):t._e(),["4"].includes(t.pageType)?e("div",{staticClass:"card-box2"},[t.showInfo?e("div",[t.spinShow?e("Spin",{attrs:{size:"large",fix:""}},[e("Icon",{staticClass:"demo-spin-icon-load",attrs:{type:"ios-loading",size:"24"}}),e("div",[t._v("Loading")])],1):t._e(),e("h1",{staticClass:"h1-box",staticStyle:{"text-align":"center",margin:"20px 0"}},[t._v("添加充值记录审批")]),e("div",{staticStyle:{display:"flex","flex-wrap":"wrap",gap:"16px"}},[e("div",{staticStyle:{flex:"7","min-width":"300px",display:"flex","flex-direction":"column",gap:"16px"}},[e("Card",{staticStyle:{flex:"1"}},[e("div",{staticClass:"info-header",attrs:{slot:"title"},slot:"title"},[e("Icon",{attrs:{type:"ios-information-circle"}}),t._v(" 基本信息\n            ")],1),e("Form",{ref:"formObj",attrs:{model:t.formItem,"label-width":80}},[e("Row",{attrs:{gutter:16}},[e("Col",{attrs:{xs:24,md:12}},[e("FormItem",{attrs:{label:"公司名称:"}},[e("Input",{attrs:{readonly:""},model:{value:t.formItem.companyName,callback:function(e){t.$set(t.formItem,"companyName",e)},expression:"formItem.companyName"}})],1)],1),e("Col",{attrs:{xs:24,md:12}},[e("FormItem",{attrs:{label:"公司币种:"}},[e("Input",{attrs:{readonly:""},model:{value:t.formItem.currencyCode,callback:function(e){t.$set(t.formItem,"currencyCode",e)},expression:"formItem.currencyCode"}})],1)],1),e("Col",{attrs:{span:"24"}},[e("div",{staticStyle:{"max-height":"400px","overflow-y":"auto"}},[e("Table",{staticStyle:{width:"100%","margin-bottom":"16px"},attrs:{columns:t.talbeColumns,data:t.talbedata,loading:t.loading}})],1)])],1)],1)],1),e("Card",{staticStyle:{flex:"1","min-height":"150px"}},[e("div",{staticClass:"info-header",attrs:{slot:"title"},slot:"title"},[e("Icon",{attrs:{type:"ios-download"}}),t._v(" 文件下载\n            ")],1)])],1),e("div",{staticStyle:{flex:"3","min-width":"250px",display:"flex","flex-direction":"column",gap:"16px"}},[e("Card",{staticStyle:{flex:"1"}},[e("div",{staticClass:"info-header",attrs:{slot:"title"},slot:"title"},[e("Icon",{attrs:{type:"ios-build"}}),t._v(" 操作信息\n            ")],1),e("Form",{ref:"formItemReason",attrs:{model:t.formItemReason,rules:t.ruleValidate},nativeOn:{submit:function(t){t.preventDefault()}}},[e("FormItem",[e("Input",{attrs:{clearable:"",type:"textarea",autosize:{minRows:3,maxRows:10},placeholder:"不通过原因..."},model:{value:t.formItemReason.reasonText,callback:function(e){t.$set(t.formItemReason,"reasonText",e)},expression:"formItemReason.reasonText"}})],1)],1),e("div",{staticStyle:{display:"flex","justify-content":"center",gap:"24px","margin-top":"16px"}},[e("Button",{attrs:{type:"error",icon:"md-close",disabled:t.showButton,loading:t.noPassLoading},on:{click:function(e){return t.examine("3")}}},[t._v("\n                审批不通过\n              ")]),e("Button",{attrs:{type:"success",icon:"md-checkmark",disabled:t.showButton,loading:t.passLoading},on:{click:function(e){return t.examine("0")}}},[t._v("\n                审批通过\n              ")])],1)],1),e("Card",{staticStyle:{flex:"1","min-height":"150px"}})],1)])],1):e("div",{staticClass:"error-message"},[e("h4",[t._v("请求数据失败，请重新进入！")])])]):t._e(),["5"].includes(t.pageType)?e("div",{staticStyle:{width:"100%",height:"100vh",display:"flex","flex-direction":"column"}},[t.spinShow?e("Spin",{attrs:{size:"large",fix:""}},[e("Icon",{staticClass:"demo-spin-icon-load",attrs:{type:"ios-loading",size:"24"}}),e("div",[t._v("Loading")])],1):t._e(),e("h1",{staticStyle:{margin:"30px 0 20px 30px"}},[t._v("营销活动结算审批")]),e("div",{staticStyle:{flex:"1",display:"flex",overflow:"hidden",padding:"0 16px"}},[e("div",{staticStyle:{flex:"6",display:"flex","flex-direction":"column","min-width":"0",padding:"0 8px",overflow:"hidden"}},[e("Card",{staticStyle:{flex:"6",display:"flex","flex-direction":"column","margin-bottom":"16px","min-height":"0",overflow:"hidden"}},[e("div",{staticStyle:{color:"#1890ff","font-size":"16px","font-weight":"bold","text-align":"left"},attrs:{slot:"title"},slot:"title"},[e("Icon",{attrs:{type:"ios-information-circle"}}),t._v(" 基本信息\n          ")],1),e("div",{staticStyle:{flex:"1",display:"flex","flex-direction":"column","min-height":"0",overflow:"hidden"}},[e("Row",{staticStyle:{"margin-bottom":"16px","flex-shrink":"0"},attrs:{gutter:16}},[e("Col",{staticStyle:{"margin-bottom":"16px"},attrs:{xs:24,md:12}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("span",{staticStyle:{flex:"1","text-align":"right","padding-right":"10px"}},[t._v("活动名称：")]),e("span",{staticStyle:{flex:"1","text-align":"left"}},[t._v(t._s(t.marketingActivities.campaignName))])])]),e("Col",{staticStyle:{"margin-bottom":"16px"},attrs:{xs:24,md:12}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("span",{staticStyle:{flex:"1","text-align":"right","padding-right":"10px"}},[t._v("适用合作模式：")]),e("span",{staticStyle:{flex:"1","text-align":"left"}},[t._v("\n                  "+t._s("1"==t.cooperationMode?"代销":"2"==t.cooperationMode?"A2Z":"")+"\n                ")])])]),e("Col",{staticStyle:{"margin-bottom":"16px"},attrs:{xs:24,md:12}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("span",{staticStyle:{flex:"1","text-align":"right","padding-right":"10px"}},[t._v("活动开始时间：")]),e("span",{staticStyle:{flex:"1","text-align":"left"}},[t._v(t._s(t.marketingActivities.startTime))])])]),e("Col",{staticStyle:{"margin-bottom":"16px"},attrs:{xs:24,md:12}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("span",{staticStyle:{flex:"1","text-align":"right","padding-right":"10px"}},[t._v("活动结束时间：")]),e("span",{staticStyle:{flex:"1","text-align":"left"}},[t._v(t._s(t.marketingActivities.endTime))])])]),e("Col",{staticStyle:{"margin-bottom":"16px"},attrs:{xs:24,md:12}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("span",{staticStyle:{flex:"1","text-align":"right","padding-right":"10px"}},[t._v("总计返利：")]),e("span",{staticStyle:{flex:"1","text-align":"left"}},[t._v(t._s(t.marketingActivities.totalRebate))])])]),e("Col",{staticStyle:{"margin-bottom":"16px"},attrs:{xs:24,md:12}},[e("div",{staticStyle:{display:"flex","justify-content":"center"}},[e("Button",{staticStyle:{width:"200px"},attrs:{type:"warning",ghost:"",disabled:t.showButton,loading:t.downLoading},on:{click:function(e){return t.exportMarketingFile(null,"1")}}},[t._v("导出")])],1)])],1),e("div",{staticStyle:{flex:"1","min-height":"0",display:"flex","flex-direction":"column",overflow:"hidden"}},[e("div",{staticStyle:{"max-height":"240px",overflow:"auto"}},[e("Table",{ref:"table",staticStyle:{width:"100%"},attrs:{data:t.mergedData,columns:"1"==t.cooperationMode?t.distributionColumns:t.A2ZColumns,"row-key":"id"},on:{"on-select":t.handleSingleSelect,"on-select-cancel":t.handleSelectCancel,"on-select-all":t.handleSelectAll,"on-select-all-cancel":t.handleSelectAllCancel},scopedSlots:t._u([{key:"action",fn:function(a){var o=a.row;a.index;return[e("Button",{attrs:{type:"success",ghost:"",size:"small",disabled:t.isEditable||"1"!=o.status},on:{click:function(e){return t.showRebateEditModal(o)}}},[t._v("编辑")])]}}],null,!1,1105585522)})],1),e("Page",{staticStyle:{"margin-top":"16px","text-align":"center","flex-shrink":"0"},attrs:{current:t.currentPage,total:t.total,"page-size":t.pageSize,"show-total":"","show-elevator":""},on:{"on-change":t.handlePageChange}})],1)],1)]),e("Card",{staticStyle:{flex:"4",display:"flex","flex-direction":"column","min-height":"0",overflow:"hidden"}},[e("div",{staticStyle:{color:"#1890ff","font-size":"16px","font-weight":"bold","text-align":"left"},attrs:{slot:"title"},slot:"title"},[e("Icon",{attrs:{type:"ios-download"}}),t._v(" 文件下载\n          ")],1),e("div",{staticStyle:{flex:"1",display:"flex","flex-direction":"column","min-height":"0",overflow:"hidden"}},[e("div",{staticStyle:{"max-height":"220px",overflow:"auto"}},[e("Table",{staticStyle:{width:"100%"},attrs:{data:t.fileData,columns:t.fileColumns},scopedSlots:t._u([{key:"action",fn:function(a){var o=a.row;a.index;return[e("Button",{attrs:{type:"info",ghost:"",size:"small"},on:{click:function(e){return t.exportMarketingFile(o,"2")}}},[t._v("下载")])]}}],null,!1,3597951856)})],1)])])],1),e("div",{staticStyle:{flex:"4",display:"flex","flex-direction":"column","min-width":"0",padding:"0 8px",overflow:"hidden"}},[e("Card",{staticStyle:{"margin-bottom":"16px","flex-shrink":"0"}},[e("div",{staticStyle:{color:"#1890ff","font-size":"16px","font-weight":"bold","text-align":"left"},attrs:{slot:"title"},slot:"title"},[e("Icon",{attrs:{type:"ios-settings"}}),t._v(" 操作信息\n          ")],1),e("div",{staticStyle:{padding:"16px"}},[e("Input",{staticStyle:{"margin-bottom":"16px",width:"100%"},attrs:{maxlength:"200",type:"textarea",rows:4},model:{value:t.infoValue,callback:function(e){t.infoValue=e},expression:"infoValue"}}),e("div",{staticStyle:{display:"flex","flex-wrap":"wrap",gap:"8px"}},[e("Button",{staticStyle:{flex:"1","min-width":"120px"},attrs:{type:"info",ghost:"",loading:t.approveAllLoading,disabled:t.showButton},on:{click:t.approveAll}},[t._v("一键全部审批通过")]),e("Button",{staticStyle:{flex:"1","min-width":"120px"},attrs:{type:"success",ghost:"",loading:t.toggleApproveLoading,disabled:t.showButton},on:{click:t.toggleApprove}},[t._v("审批通过")]),e("Button",{staticStyle:{flex:"1","min-width":"120px"},attrs:{type:"error",ghost:"",loading:t.toggleNoApproveLoading,disabled:t.showButton},on:{click:t.toggleNoApprove}},[t._v("审批不通过")]),e("Button",{staticStyle:{flex:"1","min-width":"120px"},attrs:{type:"primary",ghost:"",disabled:t.showButton},on:{click:t.fileApprove}},[t._v("文件审批")])],1)],1)]),e("Card",{staticStyle:{flex:"1",display:"flex","flex-direction":"column","min-height":"0",overflow:"hidden"}},[e("div",{staticStyle:{color:"#1890ff","font-size":"16px","font-weight":"bold","text-align":"left"},attrs:{slot:"title"},slot:"title"},[e("Icon",{attrs:{type:"ios-chatboxes"}}),t._v(" 流转意见\n          ")],1),e("div",{staticStyle:{flex:"1","min-height":"0",display:"flex","flex-direction":"column",overflow:"hidden"}},[e("div",{staticStyle:{"max-height":"400px",overflow:"auto"}},[e("Table",{staticStyle:{width:"100%"},attrs:{data:t.opinionData,columns:t.opinionColumns},scopedSlots:t._u([{key:"id",fn:function(e){var a=e.index;return[t._v("\n                  "+t._s(a+1)+"\n                ")]}}],null,!1,3422960813)})],1)])])],1)]),e("Modal",{attrs:{title:"文件审批","mask-closable":!1,width:"600px"},on:{"on-cancel":t.cancelFileModal},model:{value:t.fileModal,callback:function(e){t.fileModal=e},expression:"fileModal"}},[e("Form",{ref:"fileObj",staticStyle:{"font-weight":"bold"},attrs:{model:t.fileObj,rules:t.ruleobj,"label-width":100,"label-height":100,inline:""},nativeOn:{submit:function(t){t.preventDefault()}}},[e("FormItem",{staticStyle:{width:"510px"},attrs:{label:"文件",prop:"file"}},[e("Upload",{attrs:Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])({multiple:"",type:"drag",action:"//jsonplaceholder.typicode.com/posts/"},"action",t.uploadUrl),"on-success",t.fileSuccess),"on-error",t.handleError),"before-upload",t.handleBeforeUpload),"on-progress",t.fileUploading)},[e("div",{staticStyle:{padding:"20px 0"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v("点击或拖拽文件上传")])],1)]),t.file?e("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"100%"}},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name)+"\n              ")],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e(),e("div",{staticStyle:{width:"100%"}},[e("Alert",{staticStyle:{float:"right",padding:"8px 10px 8px 10px"},attrs:{type:"warning"}},[t._v(t._s(t.message))])],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelFileModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary",loading:t.uploadLoading},on:{click:t.handleUpload}},[t._v("确定")])],1)],1),e("Modal",{attrs:{title:"编辑预计返利","mask-closable":!1},model:{value:t.rebateModal,callback:function(e){t.rebateModal=e},expression:"rebateModal"}},[e("Form",{ref:"rebateEditForm",attrs:{model:t.currentRebateEdit,rules:t.ruleEdit,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[e("FormItem",{attrs:{label:"预计返利",prop:"value"}},[e("Input",{attrs:{placeholder:"请输入预计返利",clearable:""},model:{value:t.currentRebateEdit.value,callback:function(e){t.$set(t.currentRebateEdit,"value",e)},expression:"currentRebateEdit.value"}})],1)],1),e("div",{attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:function(e){t.rebateModal=!1}}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.saveRebateEdit}},[t._v("确定")])],1)],1)],1):t._e()],1)}),i=[],r=a("2909"),s=a("c7eb"),c=a("1da1"),l=a("3835"),d=a("b85c"),u=a("5530"),p=(a("a4d3"),a("e01a"),a("d9e2"),a("99af"),a("4de4"),a("a15b"),a("d81d"),a("14d9"),a("fb6a"),a("a434"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("2532"),a("3ca3"),a("466d"),a("4d90"),a("5319"),a("841c"),a("2ca0"),a("498a"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("4360")),f=a("18e7"),g=a("66df"),m=function(t){return g["a"].request({url:"/aep/businessManagerAudits",data:t,method:"post"})},h=function(t){return g["a"].request({url:"/aep/financialAudits",data:t,method:"post"})},v=function(t){return g["a"].request({url:"/aep/applicantConfirmation",data:t,method:"post"})},b=function(t){return g["a"].request({url:"/aep/searchAdjustInfo",data:t,method:"post"})},y=function(t){return g["a"].request({url:"/aep/downAdjustFile",data:t,method:"POST",responseType:"blob"})},x=function(t){return g["a"].request({url:"/aep/campaign/getManualRecharges",data:t,method:"post"})},I=function(t){return g["a"].request({url:"/aep/settlementReview",data:t,method:"post"})},S=function(t){return g["a"].request({url:"/aep/campaign/getCampaign",data:t,method:"post"})},w=function(t){return g["a"].request({url:"/aep/exportMarketingFile",data:t,method:"POST",responseType:"blob"})},k=function(t,e){return g["a"].request({url:"/aep/settlement/approvalSettlement",data:t,method:"POST",headers:{"Content-Type":"multipart/form-data",ssoAccessToken:e}})},_=function(t){return g["a"].request({url:"/aep/getMktSettlementPage",data:t,method:"POST"})},C=function(t){return g["a"].request({url:"/aep/settlement/getOpinions",data:t,method:"POST"})},R=function(t,e){return g["a"].request({url:"/aep/settlement/download",params:t,method:"POST",responseType:"blob",headers:{ssoAccessToken:e}})},T={data:function(){var t=this,e=function(e,a,o){t.file?o():o(new Error("请上传文件！"))};return{showInfo:!0,showButton:!1,spinShow:!0,modal:!1,fileModal:!1,rebateModal:!1,loading:!1,searchloading:!1,downLoading:!1,downInfoLoading:!1,noPassLoading:!1,passLoading:!1,receivedLoading:!1,uploadLoading:!1,approveAllLoading:!1,toggleApproveLoading:!1,toggleNoApproveLoading:!1,pageType:"",token:"",userName:"",ticket:"",queryParams:"",isEditable:!1,currentPage:1,pageSize:10,rawData:[],total:0,cooperationMode:"",fileType:"",uploadUrl:"",message:"请上传.xlsx文件，大小限制为10MB以内。",file:"",infoValue:"",selectedRows:[],editedRebates:{},currentRebateEdit:{rowId:null,value:""},fileObj:{file:""},propinfo:{applyId:"",adjustWay:"",adjustType:"",corpName:"",businessType:"",ebscode:"",invoiceNo:"",adjustId:"",totalAdjustment:"",adjustReason:"",message:""},reasons:[{value:"1",label:"渠道商收入"},{value:"2",label:"其他客户收入"},{value:"3",label:"其他客户收入"}],formItem:{companyName:"",currencyCode:""},marketingActivities:{campaignName:"",cooperationMode:"",startTime:"",endTime:"",rechargeAmount:"",totalRebate:""},data:[],talbedata:[],fileData:[],opinionData:[],columns:[{title:"服务开始时间",key:"svcStartTime",minWidth:160,align:"center",tooltip:!0},{title:"服务结束时间",key:"svcEndTime",minWidth:160,align:"center",tooltip:!0},{title:"合同主体",key:"contract",minWidth:150,align:"center",tooltip:!0},{title:"原账单币种",key:"currency",minWidth:150,align:"center",render:function(t,e){var a=e.row,o="156"==a.currency?"CNY":"840"==a.currency?"USD":"344"==a.currency?"HKD":"";return t("label",o)}},{title:"原发票金额",key:"oldAmount",minWidth:150,align:"center",tooltip:!0},{title:"调账币种",key:"currency",minWidth:150,align:"center",render:function(t,e){var a=e.row,o="156"==a.currency?"CNY":"840"==a.currency?"USD":"344"==a.currency?"HKD":"";return t("label",o)}},{title:"调账金额",key:"totalAdjustment",minWidth:150,align:"center",tooltip:!0},{title:"账期",key:"billingPeriod",minWidth:150,align:"center",tooltip:!0}],talbeColumns:[{title:"充值金额",key:"rechargeAmount",minWidth:160,align:"left",tooltip:!0},{title:"充值时间",key:"settlementTime",minWidth:160,align:"left",tooltip:!0}],distributionColumns:[{type:"selection",width:60,align:"center",disabled:function(t){return t._disabled}},{title:"客户名称",key:"companyName",align:"center",tooltip:!0,minWidth:150},{title:"已充值金额",key:"rechargeAmount",align:"center",tooltip:!0,minWidth:150},{title:"预计返利",key:"actualReturn",align:"center",tooltip:!0,minWidth:150},{title:"操作",slot:"action",align:"center",tooltip:!0,width:100}],A2ZColumns:[{type:"selection",width:60,align:"center",disabled:function(t){return t._disabled}},{title:"客户名称",key:"companyName",align:"center",tooltip:!0,minWidth:150},{title:"上期金额",key:"preAmount",align:"center",tooltip:!0,minWidth:150},{title:"本期金额",key:"currAmount",align:"center",tooltip:!0,minWidth:150},{title:"预计返利",key:"actualReturn",align:"center",tooltip:!0,minWidth:150},{title:"操作",slot:"action",align:"center",tooltip:!0,width:100}],fileColumns:[{title:"文件名",key:"paramFile",align:"center",tooltip:!0,minWidth:100,render:function(t,e){var a=e.row,o=a.paramFile.split("_"),n=o.slice(1).join("_");return t("span",n)}},{title:"上传时间",key:"createTime",align:"center",tooltip:!0,minWidth:150},{title:"上传人",key:"username",align:"center",tooltip:!0,minWidth:120},{title:"点击下载",slot:"action",align:"center",tooltip:!0,width:90}],opinionColumns:[{title:"序号",slot:"id",align:"center",tooltip:!0,minWidth:80},{title:"操作人",key:"username",align:"center",tooltip:!0,minWidth:120},{title:"操作时间",key:"createTime",align:"center",tooltip:!0,minWidth:150},{title:"操作类型",key:"operationType",align:"center",tooltip:!0,minWidth:150,render:function(t,e){var a=e.row,o="1"==a.operationType?"一键全部审批通过":"2"==a.operationType?"审批通过":"3"==a.operationType?"审批不通过":"4"==a.operationType?"文件审批":"";return t("span",o)}},{title:"处理意见",key:"msg",align:"center",tooltip:!0,minWidth:140}],formItemReason:{reasonText:""},ruleValidate:{reasonText:[{required:!0,message:"原因不能为空"}]},ruleobj:{file:[{required:!0,validator:e,trigger:"change"}]},ruleEdit:{value:[{required:!0,message:"预计返利不能为空"},{pattern:/^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/,message:"最高支持8位整数和2位小数正数或零"}]}}},computed:{mergedData:function(){var t=this;return(this.rawData||[]).map((function(e){return Object(u["a"])(Object(u["a"])({},e),{},{actualReturn:void 0!==t.editedRebates[e.id]?t.editedRebates[e.id]:e.actualReturn,_checked:t.selectedRows.includes(e.id)})}))}},created:function(){var t=new URLSearchParams(window.location.search);if(t.get("webType")&&localStorage.setItem("webType",t.get("webType")),["5"].includes(t.get("webType"))){var e=Math.random().toString(36).substr(2,9);sessionStorage.setItem("currentPageId",e),console.log(111),t.get("procUniqueId")&&localStorage.setItem("page_".concat(e,"_procUniqueId"),t.get("procUniqueId")),t.get("campaignId")&&localStorage.setItem("page_".concat(e,"_campaignId"),t.get("campaignId")),t.get("batchId")&&localStorage.setItem("page_".concat(e,"_batchId"),t.get("batchId")),t.get("todoNodeId")&&localStorage.setItem("page_".concat(e,"_todoNodeId"),t.get("todoNodeId")),t.get("todoUniqueId")&&localStorage.setItem("page_".concat(e,"_todoUniqueId"),t.get("todoUniqueId")),t.get("secondEdit")&&localStorage.setItem("page_".concat(e,"_secondEdit"),t.get("secondEdit"))}else console.log(222),t.get("id")&&localStorage.setItem("id",t.get("id")),t.get("procUniqueId")&&localStorage.setItem("procUniqueId",t.get("procUniqueId")),t.get("todoNodeId")&&localStorage.setItem("todoNodeId",t.get("todoNodeId")),t.get("todoUniqueId")&&localStorage.setItem("todoUniqueId",t.get("todoUniqueId")),t.get("campaignId")&&localStorage.setItem("campaignId",t.get("campaignId")),t.get("corpId")&&localStorage.setItem("corpId",t.get("corpId")),t.get("secondEdit")&&localStorage.setItem("secondEdit",t.get("secondEdit")),t.get("batchId")&&localStorage.setItem("batchId",t.get("batchId"));this.parseUrlParams();var a=localStorage.getItem("selectedRows"),o=localStorage.getItem("editedRebates");a&&(this.selectedRows=JSON.parse(a)),o&&(this.editedRebates=JSON.parse(o))},mounted:function(){if(this.pageType=localStorage.getItem("webType"),"5"==this.pageType){var t=sessionStorage.getItem("currentPageId");this.isEditable="true"===localStorage.getItem("page_".concat(t,"_secondEdit"))}},methods:{getShowButton:function(t){var e,a=this,o=sessionStorage.getItem("currentPageId");console.log(o,"pageId"),e="5"===localStorage.getItem("webType")&&o?localStorage.getItem("page_".concat(o,"_todoUniqueId")):localStorage.getItem("todoUniqueId"),Object(f["o"])({ssoAccessToken:this.token,todoUniqueId:e}).then((function(t){"0000"===t.code&&("1"==t.data?a.showButton=!1:a.showButton=!0)})).catch((function(t){console.error(t,"err1")})).finally((function(){}))},parseUrlParams:function(){var t,e=new URLSearchParams(window.location.search),a={},o=Object(d["a"])(e.entries());try{for(o.s();!(t=o.n()).done;){var n=Object(l["a"])(t.value,2),i=n[0],r=n[1];a[i]=r.trim()}}catch(err){o.e(err)}finally{o.f()}this.queryParams=a,this.queryParams.ticket?(this.getToken(this.queryParams.ticket),sessionStorage.setItem("ticket",this.queryParams.ticket)):this.checkRedirect()},checkRedirect:function(){var t=this;return Object(c["a"])(Object(s["a"])().mark((function e(){var a,o,n,i;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(window.location.hash.includes("#redirected")){e.next=12;break}return e.prev=1,a="todo-url",e.next=5,t.$getRedirectUrl(a);case 5:o=e.sent,o&&(n=t.getUrlWithoutParams(),i=o+"?service="+n,window.location.replace("".concat(i,"#redirected"))),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](1),console.error("重定向失败:",e.t0);case 12:case"end":return e.stop()}}),e,null,[[1,9]])})))()},getToken:function(t){var e=this;Object(f["p"])({ticket:t,service:this.getUrlWithoutParams()}).then((function(t){"0000"===t.code&&(e.showInfo=!0,e.token=t.accessToken,p["a"].state.user.userName=t.tryUser,e.userName=t.tryUser,e.getShowButton(),["1","2","3"].includes(e.pageType)?e.getAdjustInfo():["4"].includes(e.pageType)?e.getRechargeRecord():["5"].includes(e.pageType)&&e.getCampaign())})).catch((function(t){console.error(t,"err2"),e.$Notice.error({title:"操作提示",desc:t.msg||t.description||"接口失败"})})).finally((function(){sessionStorage.removeItem("ticket")}))},getUrlWithoutParams:function(){var t=window.location.href,e=t.indexOf("?");return-1!==e?t.substring(0,e):t},formatDate:function(t){var e=new Date(t.replace(/T/," ").replace(/\+.+/,"")),a=e.getFullYear(),o=String(e.getMonth()+1).padStart(2,"0"),n=String(e.getDate()).padStart(2,"0");return"".concat(a,"年").concat(parseInt(o),"月").concat(parseInt(n),"日")},getAdjustInfo:function(){var t=this;b({id:localStorage.getItem("id"),ssoAccessToken:this.token,num:"1",size:"10"}).then((function(e){"0000"==e.code&&(t.data=e.data,t.propinfo=JSON.parse(JSON.stringify(e.data[0])),t.propinfo.noPassReason="审核拒绝"==e.data[0].authStatus?"（"+e.data[0].noPassReason+"）":"")})).catch((function(e){console.error(e,"err3"),t.$Notice.error({title:"操作提示",desc:e.msg||e.description||"接口失败"})})).finally((function(){t.spinShow=!1}))},showModal:function(t){this.modal=!0},rebuild:function(t){var e=this;"1"==t?this.downInfoLoading=!0:this.downLoading=!0,y({id:localStorage.getItem("id"),type:t,ssoAccessToken:this.token}).then((function(t){var a=t.data,o=decodeURIComponent(escape(t.headers["content-disposition"].match(/=(.*)$/)[1]));if("download"in document.createElement("a")){var n=e.$refs.downloadLink,i=URL.createObjectURL(a);n.download=o,n.href=i,n.click(),URL.revokeObjectURL(i)}else navigator.msSaveBlob(a,o)})).catch((function(t){console.error(t,"err4")})).finally((function(){e.downInfoLoading=!1,e.downLoading=!1}))},approval:function(t,e){var a=this;if("1"==t){var o="1"==e?m:h;this.$Modal.confirm({title:"确定执行审核通过？",onOk:function(){a.passLoading=!0,o({ssoAccessToken:a.token,id:localStorage.getItem("id"),outcome:t,procUniqueId:localStorage.getItem("procUniqueId"),noPassReason:"",userName:a.userName}).then((function(t){"0000"===t.code&&(a.$Notice.success({title:"操作提示",desc:"操作成功！"}),a.getShowButton())})).catch((function(t){console.error(t,"err5"),a.$Notice.error({title:"操作提示",desc:t.msg||t.description||"接口失败"})})).finally((function(){a.passLoading=!1}))}})}else this.modal=!0},cancelModal:function(){this.$refs["formItemReason"].resetFields(),this.modal=!1},cancelFileModal:function(){this.file="",this.$refs["fileObj"].resetFields(),this.fileModal=!1},confirm:function(){var t=this,e="1"==this.pageType?m:h;this.$refs.formItemReason.validate((function(a){a&&(t.noPassLoading=!0,e({ssoAccessToken:t.token,id:localStorage.getItem("id"),outcome:"2",procUniqueId:localStorage.getItem("procUniqueId"),noPassReason:t.formItemReason.reasonText,userName:t.userName}).then((function(e){e&&"0000"==e.code&&(t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.$refs["formItemReason"].resetFields(),t.modal=!1,t.getShowButton())})).catch((function(e){console.error(e,"err6"),t.$Notice.error({title:"操作提示",desc:e.msg||e.description||"接口失败"})})).finally((function(){t.noPassLoading=!1})))}))},received:function(){var t=this;this.$Modal.confirm({title:"确定执行已阅？",onOk:function(){t.receivedLoading=!0,v({id:localStorage.getItem("id"),ssoAccessToken:t.token,procUniqueId:localStorage.getItem("procUniqueId"),userName:t.userName}).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"操作提示",desc:"操作成功！"}),t.getShowButton())})).catch((function(e){console.error(e,"err7"),t.$Notice.error({title:"操作提示",desc:e.msg||e.description||"接口失败"})})).finally((function(){t.receivedLoading=!1}))}})},getRechargeRecord:function(){var t=this;x({campaignId:localStorage.getItem("campaignId"),corpId:localStorage.getItem("corpId"),ssoAccessToken:this.token,batchId:localStorage.getItem("batchId")}).then((function(e){"0000"==e.code&&(t.formItem.companyName=e.data.companyName,t.formItem.currencyCode="156"==e.data.currencyCode?"人民币":"840"==e.data.currencyCode?"美元":"344"==e.data.currencyCode?"港币":"",t.talbedata=e.data.paymentHistorys,t.formItemReason.reasonText=e.data.msg)})).catch((function(e){console.error(e,"err8"),t.$Notice.error({title:"操作提示",desc:e.msg||e.description||"接口失败"})})).finally((function(){t.spinShow=!1}))},examine:function(t){var e=this,a="0"==t?"确定执行审批通过？":"确定执行审批不通过？",o="0"==t?"":this.formItemReason.reasonText,n=!0,i="";"3"===t&&(this.formItemReason.reasonText&&""!==this.formItemReason.reasonText.trim()||(n=!1,i="请输入不通过原因！")),n?this.$Modal.confirm({title:a,onOk:function(){"0"==t?e.passLoading=!0:e.noPassLoading=!0,I({ssoAccessToken:e.token,procUniqueId:localStorage.getItem("procUniqueId"),ids:e.talbedata.map((function(t){return t.id})),remark:o,status:t,userName:e.userName}).then((function(t){"0000"===t.code&&(e.$Notice.success({title:"操作提示",desc:"操作成功！"}),e.$refs["formItemReason"].resetFields(),e.getShowButton())})).catch((function(t){console.error(t,"err9"),e.$Notice.error({title:"操作提示",desc:t.msg||t.description||"接口失败"})})).finally((function(){e.passLoading=!1,e.noPassLoading=!1}))}}):this.$Notice.warning({title:"提示",desc:i})},handleSingleSelect:function(t,e){var a=this.selectedRows.indexOf(e.id);-1===a?this.selectedRows.push(e.id):this.selectedRows.splice(a,1)},handleSelectCancel:function(t,e){this.$store.commit("toggleSelection",e.id);var a=this.selectedRows.indexOf(e.id);-1!==a&&this.selectedRows.splice(a,1)},handleSelectAll:function(t){var e=this.rawData.filter((function(t){return!t._disabled})).map((function(t){return t.id}));this.selectedRows=Object(r["a"])(new Set([].concat(Object(r["a"])(this.selectedRows),Object(r["a"])(e))))},handleSelectAllCancel:function(t){var e=this.rawData.map((function(t){return t.id}));this.selectedRows=this.selectedRows.filter((function(t){return!e.includes(t)}))},showRebateEditModal:function(t){this.currentRebateEdit={rowId:t.id,value:t.actualReturn},this.rebateModal=!0},saveRebateEdit:function(){var t=this;this.$refs.rebateEditForm.validate((function(e){e&&(t.$set(t.editedRebates,t.currentRebateEdit.rowId,t.currentRebateEdit.value),t.rebateModal=!1,t.$Notice.success({title:"操作提示",desc:"修改成功！"}))}))},handlePageChange:function(t){this.currentPage=t,this.fetchData(t)},fileApprove:function(){this.fileModal=!0},handleBeforeUpload:function(t){return/^.+(\.xlsx)$/.test(t.name)?t.size>10485760?this.$Notice.warning({title:"文件大小超过限制",desc:"文件 "+t.name+"超过了最大限制范围10MB"}):this.file=t:this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+t.name+" 格式不正确，请上传.xlsx。"}),!1},fileUploading:function(t,e,a){this.message="文件上传中、待进度条消失后再操作"},fileSuccess:function(t,e,a){this.message="请先下载模板文件，并按格式填写后上传"},handleError:function(t,e){var a=this;setTimeout((function(){a.uploading=!1,a.$Notice.warning({title:"错误提示",desc:"上传失败！"})}),3e3)},removeFile:function(){this.file=""},getCSVHeaders:function(){return"1"==this.cooperationMode?[{title:"Id",key:"id"},{title:"客户名称",key:"corpName"},{title:"已充值金额",key:"rechargeAmount"},{title:"预计返利",key:"actualReturn"}]:"2"==this.cooperationMode?[{title:"Id",key:"id"},{title:"客户名称",key:"corpName"},{title:"上期金额",key:"currAmount"},{title:"本期金额",key:"preAmount"},{title:"预计返利",key:"actualReturn"}]:[]},ConvertToCSV:function(t){var e=this.getCSVHeaders(),a=e.map((function(t){return t.title})).join(","),o=t.map((function(t){return e.map((function(e){var a=t[e.key];return"string"===typeof a&&(a.includes(",")||a.includes("\n"))?'"'.concat(a.replace(/"/g,'""'),'"'):""===a||void 0===a||null===a?"":a})).join(",")})),n=[a].concat(Object(r["a"])(o)).join("\n");return n},getCommonData:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,a=sessionStorage.getItem("currentPageId"),o=localStorage.getItem("page_".concat(a,"_campaignId")),n=localStorage.getItem("page_".concat(a,"_batchId")),i={campaignId:o,batchId:n,current:-1,size:-1,ssoAccessToken:this.token};return null!==e&&(i.operationType=e),C(i).then((function(t){if("0000"===t.code)return t.data;throw new Error(t.description||"接口报错")})).catch((function(e){throw console.error(e,"err10"),t.$Notice.error({title:"操作提示",desc:e.msg||e.description||"接口失败"}),e}))},getFileList:function(){var t=this;this.getCommonData("4").then((function(e){t.fileData=e})).catch((function(){console.error(err,"err18")}))},getOpinions:function(){var t=this;this.getCommonData().then((function(e){t.opinionData=e})).catch((function(){console.error(err,"err19")}))},exportMarketingFile:function(t,e){var a=this,o="1"==e?w:R,n={};if("1"==e){var i=sessionStorage.getItem("currentPageId"),r=localStorage.getItem("page_".concat(i,"_campaignId")),s=localStorage.getItem("page_".concat(i,"_batchId"));this.downLoading=!0,n={campaignId:r,cooperationMode:this.cooperationMode,ssoAccessToken:this.token,batchId:s}}else n={name:t.paramFile,ssoAccessToken:this.token};console.log(n,"resultData"),o(n,this.token).then((function(t){var e=t.data,o=decodeURIComponent(escape(t.headers["content-disposition"].match(/=(.*)$/)[1]));if("download"in document.createElement("a")){var n=a.$refs.downloadLink,i=URL.createObjectURL(e);n.download=o,n.href=i,n.click(),URL.revokeObjectURL(i)}else navigator.msSaveBlob(e,o)})).catch((function(t){console.error(t,"err11")})).finally((function(){a.downLoading=!1}))},getCampaign:function(){var t=this,e=sessionStorage.getItem("currentPageId");S({type:"1",campaignId:localStorage.getItem("page_".concat(e,"_campaignId")),ssoAccessToken:this.token}).then((function(e){"0000"==e.code&&(t.cooperationMode=e.data.cooperationMode,t.marketingActivities=e.data,t.marketingActivities.startTime=t.formatDate(e.data.startTime),t.marketingActivities.endTime=t.formatDate(e.data.endTime),t.marketingActivities.totalRebate="1"==t.cooperationMode?e.data.expectAmountRefund+"/"+e.data.totalAmountRefund:"2"==t.cooperationMode?e.data.expectAmountRefund:"",t.fetchData(1),t.getOpinions(),t.getFileList())})).catch((function(e){console.error(e,"err12"),t.$Notice.error({title:"操作提示",desc:e.msg||e.description||"接口失败"})})).finally((function(){t.spinShow=!1}))},fetchData:function(t){var e=this;return Object(c["a"])(Object(s["a"])().mark((function a(){var o,n,i,r;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return o=sessionStorage.getItem("currentPageId"),n=localStorage.getItem("page_".concat(o,"_campaignId")),i=localStorage.getItem("page_".concat(o,"_batchId")),e.loading=!0,a.prev=4,a.next=7,_({campaignId:n,ssoAccessToken:e.token,batchId:i,cooperationMode:e.cooperationMode,current:t,size:e.pageSize});case 7:r=a.sent,"0000"===r.code?(e.rawData=r.data.records,e.total=r.data.total,e.currentPage=t,e.rawData.forEach((function(t){e.$set(t,"_disabled","1"!=t.status),t._checked=e.selectedRows.includes(t.id),t.actualReturn=e.editedRebates[t.id]||t.actualReturn}))):(console.log(err,"err21"),e.$Notice.error({title:"操作提示",desc:"获取数据失败：".concat(r.description||"未知错误")})),a.next=15;break;case 11:a.prev=11,a.t0=a["catch"](4),console.log(a.t0,"err22"),e.$Notice.error({title:"操作提示",desc:"接口调用失败：".concat(a.t0.description||"网络错误")});case 15:return a.prev=15,e.loading=!1,e.spinShow=!1,a.finish(15);case 19:case"end":return a.stop()}}),a,null,[[4,11,15,19]])})))()},toggleApprove:function(){var t=this;return Object(c["a"])(Object(s["a"])().mark((function e(){var a,o,n,i,r,c,l;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.toggleApproveLoading){e.next=2;break}return e.abrupt("return");case 2:return t.toggleApproveLoading=!0,a=sessionStorage.getItem("currentPageId"),o=localStorage.getItem("page_".concat(a,"_campaignId")),n=localStorage.getItem("page_".concat(a,"_batchId")),e.prev=6,e.next=9,_({campaignId:o,batchId:n,ssoAccessToken:t.token,cooperationMode:t.cooperationMode,current:-1,size:-1,status:1});case 9:if(i=e.sent,"0000"!==i.code){e.next=20;break}if(r=t.selectedRows,0!==r.length){e.next=15;break}return t.$Notice.warning({title:"提示",desc:"至少勾选一条数据！"}),e.abrupt("return");case 15:return c=i.data.records.filter((function(t){return r.includes(t.id)})).map((function(e){return Object(u["a"])(Object(u["a"])({},e),{},{actualReturn:void 0!==t.editedRebates[e.id]?t.editedRebates[e.id]:e.actualReturn})})),console.log(c,"data"),l=t.ConvertToCSV(c),e.next=20,t.submitApprove(l,"1","2","2");case 20:e.next=26;break;case 22:e.prev=22,e.t0=e["catch"](6),console.log(e.t0,"err23"),t.$Notice.error({title:"操作提示",desc:e.t0.description});case 26:return e.prev=26,t.toggleApproveLoading=!1,e.finish(26);case 29:case"end":return e.stop()}}),e,null,[[6,22,26,29]])})))()},toggleNoApprove:function(){var t=this;return Object(c["a"])(Object(s["a"])().mark((function e(){var a,o,n,i,r,c,l;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.toggleNoApproveLoading){e.next=2;break}return e.abrupt("return");case 2:return t.toggleNoApproveLoading=!0,a=sessionStorage.getItem("currentPageId"),o=localStorage.getItem("page_".concat(a,"_campaignId")),n=localStorage.getItem("page_".concat(a,"_batchId")),e.prev=6,e.next=9,_({campaignId:o,batchId:n,ssoAccessToken:t.token,cooperationMode:t.cooperationMode,current:-1,size:-1,status:1});case 9:if(i=e.sent,"0000"!==i.code){e.next=20;break}if(r=t.selectedRows,0!==r.length){e.next=15;break}return t.$Notice.warning({title:"提示",desc:"至少勾选一条数据！"}),e.abrupt("return");case 15:return c=i.data.records.filter((function(t){return r.includes(t.id)})).map((function(e){return Object(u["a"])(Object(u["a"])({},e),{},{actualReturn:void 0!==t.editedRebates[e.id]?t.editedRebates[e.id]:e.actualReturn})})),console.log(c,"data"),l=t.ConvertToCSV(c),e.next=20,t.submitApprove(l,"1","3","3");case 20:e.next=26;break;case 22:e.prev=22,e.t0=e["catch"](6),console.log(e.t0,"err24"),t.$Notice.error({title:"操作提示",desc:e.t0.description});case 26:return e.prev=26,t.toggleNoApproveLoading=!1,e.finish(26);case 29:case"end":return e.stop()}}),e,null,[[6,22,26,29]])})))()},approveAll:function(){var t=this;return Object(c["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$Modal.confirm({title:"确认执行全部审批通过？",onOk:function(){var e=Object(c["a"])(Object(s["a"])().mark((function e(){var a,o,n,i,r,c;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,a=sessionStorage.getItem("currentPageId"),o=localStorage.getItem("page_".concat(a,"_campaignId")),n=localStorage.getItem("page_".concat(a,"_batchId")),!t.approveAllLoading){e.next=6;break}return e.abrupt("return");case 6:return t.approveAllLoading=!0,e.next=9,_({campaignId:o,ssoAccessToken:t.token,batchId:n,cooperationMode:t.cooperationMode,current:-1,size:-1,status:1});case 9:if(i=e.sent,"0000"!==i.code){e.next=18;break}if(r=i.data.records.filter((function(t){return!t._disabled})).map((function(e){return Object(u["a"])(Object(u["a"])({},e),{},{actualReturn:void 0!==t.editedRebates[e.id]?t.editedRebates[e.id]:e.actualReturn})})),0!==r.length){e.next=15;break}return t.$Notice.warning({title:"操作提示",desc:"没有可审批的数据（所有行均被禁用）"}),e.abrupt("return");case 15:return c=t.ConvertToCSV(r),e.next=18,t.submitApprove(c,"1","2","1");case 18:e.next=24;break;case 20:e.prev=20,e.t0=e["catch"](0),console.log(e.t0,"err25"),t.$Notice.error({title:"操作提示",desc:e.t0.description});case 24:return e.prev=24,t.approveAllLoading=!1,e.finish(24);case 27:case"end":return e.stop()}}),e,null,[[0,20,24,27]])})));function a(){return e.apply(this,arguments)}return a}(),onCancel:function(){t.$Message.info("已取消审批")}});case 1:case"end":return e.stop()}}),e)})))()},handleUpload:function(){var t=this;this.$refs["fileObj"].validate((function(e){e&&(t.uploadLoading=!0,t.submitApprove(null,"2","2","4"))}))},resetState:function(){this.selectedRows=[],this.editedRebates={}},submitApprove:function(t,e,a,o){var n=this;return Object(c["a"])(Object(s["a"])().mark((function i(){var r,c,l,d,u,p,f,g;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return console.log(t,"csvString"),r=sessionStorage.getItem("currentPageId"),c=localStorage.getItem("page_".concat(r,"_campaignId")),l=localStorage.getItem("page_".concat(r,"_batchId")),d=localStorage.getItem("page_".concat(r,"_secondEdit")),u=localStorage.getItem("page_".concat(r,"_procUniqueId")),p=new FormData,"1"===e?(f=new Blob([t],{type:"text/csv"}),p.append("file",f,"data.csv")):p.append("file",n.file),p.append("type",n.cooperationMode),p.append("campaignId",c),p.append("secondEdit",d),p.append("procUniqueId",u),p.append("batchId",l),p.append("status",a),p.append("msg",n.infoValue),p.append("userName",n.userName),p.append("operationType",o),i.prev=17,i.next=20,k(p,n.token);case 20:if(g=i.sent,console.log("接口返回的完整响应:",g),"0000"!==g.code){i.next=27;break}n.$Notice.success({title:"操作提示",desc:"操作成功"}),n.getShowButton(),i.next=28;break;case 27:throw new Error(g.description||"审批失败，未知原因");case 28:i.next=34;break;case 30:i.prev=30,i.t0=i["catch"](17),console.error(i.t0,"err26"),n.$Notice.error({title:"操作提示",desc:i.t0.description||i.t0.msg||"审批失败，请检查网络或数据"});case 34:return i.prev=34,n.getCampaign(),n.resetState(),n.infoValue="",n.file="",n.$refs["fileObj"].resetFields(),n.fileModal=!1,n.uploadLoading=!1,n.approveAllLoading=!1,n.toggleApproveLoading=!1,i.finish(34);case 45:case"end":return i.stop()}}),i,null,[[17,30,34,45]])})))()}},beforeDestroy:function(){var t=sessionStorage.getItem("currentPageId"),e=localStorage.getItem("webType");if("5"===e&&t){var a="page_".concat(t,"_");Object.keys(localStorage).forEach((function(t){t.startsWith(a)&&localStorage.removeItem(t)})),sessionStorage.removeItem("currentPageId")}else localStorage.removeItem("id"),localStorage.removeItem("procUniqueId"),localStorage.removeItem("todoNodeId"),localStorage.removeItem("todoUniqueId"),localStorage.removeItem("campaignId"),localStorage.removeItem("corpId"),localStorage.removeItem("secondEdit"),localStorage.removeItem("batchId");localStorage.removeItem("webType"),localStorage.setItem("selectedRows",JSON.stringify(this.selectedRows)),localStorage.setItem("editedRebates",JSON.stringify(this.editedRebates))}},O=T,A=(a("779e"),a("2877")),j=Object(A["a"])(O,n,i,!1,null,"9a3b4be2",null);e["default"]=j.exports},a4d3:function(t,e,a){"use strict";a("d9f5"),a("b4f8"),a("c513"),a("e9c4"),a("5a47")},b4f8:function(t,e,a){"use strict";var o=a("23e7"),n=a("d066"),i=a("1a2d"),r=a("577e"),s=a("5692"),c=a("0b43"),l=s("string-to-symbol-registry"),d=s("symbol-to-string-registry");o({target:"Symbol",stat:!0,forced:!c},{for:function(t){var e=r(t);if(i(l,e))return l[e];var a=n("Symbol")(e);return l[e]=a,d[a]=e,a}})},c513:function(t,e,a){"use strict";var o=a("23e7"),n=a("1a2d"),i=a("d9b5"),r=a("0d51"),s=a("5692"),c=a("0b43"),l=s("symbol-to-string-registry");o({target:"Symbol",stat:!0,forced:!c},{keyFor:function(t){if(!i(t))throw new TypeError(r(t)+" is not a symbol");if(n(l,t))return l[t]}})},d9f5:function(t,e,a){"use strict";var o=a("23e7"),n=a("cfe9"),i=a("c65b"),r=a("e330"),s=a("c430"),c=a("83ab"),l=a("04f8"),d=a("d039"),u=a("1a2d"),p=a("3a9b"),f=a("825a"),g=a("fc6a"),m=a("a04b"),h=a("577e"),v=a("5c6c"),b=a("7c73"),y=a("df75"),x=a("241c"),I=a("057f"),S=a("7418"),w=a("06cf"),k=a("9bf2"),_=a("37e8"),C=a("d1e7"),R=a("cb2d"),T=a("edd0"),O=a("5692"),A=a("f772"),j=a("d012"),N=a("90e3"),q=a("b622"),L=a("e538"),P=a("e065"),M=a("57b9"),U=a("d44e"),$=a("69f3"),B=a("b727").forEach,E=A("hidden"),D="Symbol",F="prototype",W=$.set,z=$.getterFor(D),V=Object[F],J=n.Symbol,H=J&&J[F],Y=n.RangeError,Z=n.TypeError,K=n.QObject,Q=w.f,G=k.f,X=I.f,tt=C.f,et=r([].push),at=O("symbols"),ot=O("op-symbols"),nt=O("wks"),it=!K||!K[F]||!K[F].findChild,rt=function(t,e,a){var o=Q(V,e);o&&delete V[e],G(t,e,a),o&&t!==V&&G(V,e,o)},st=c&&d((function(){return 7!==b(G({},"a",{get:function(){return G(this,"a",{value:7}).a}})).a}))?rt:G,ct=function(t,e){var a=at[t]=b(H);return W(a,{type:D,tag:t,description:e}),c||(a.description=e),a},lt=function(t,e,a){t===V&&lt(ot,e,a),f(t);var o=m(e);return f(a),u(at,o)?(a.enumerable?(u(t,E)&&t[E][o]&&(t[E][o]=!1),a=b(a,{enumerable:v(0,!1)})):(u(t,E)||G(t,E,v(1,b(null))),t[E][o]=!0),st(t,o,a)):G(t,o,a)},dt=function(t,e){f(t);var a=g(e),o=y(a).concat(mt(a));return B(o,(function(e){c&&!i(pt,a,e)||lt(t,e,a[e])})),t},ut=function(t,e){return void 0===e?b(t):dt(b(t),e)},pt=function(t){var e=m(t),a=i(tt,this,e);return!(this===V&&u(at,e)&&!u(ot,e))&&(!(a||!u(this,e)||!u(at,e)||u(this,E)&&this[E][e])||a)},ft=function(t,e){var a=g(t),o=m(e);if(a!==V||!u(at,o)||u(ot,o)){var n=Q(a,o);return!n||!u(at,o)||u(a,E)&&a[E][o]||(n.enumerable=!0),n}},gt=function(t){var e=X(g(t)),a=[];return B(e,(function(t){u(at,t)||u(j,t)||et(a,t)})),a},mt=function(t){var e=t===V,a=X(e?ot:g(t)),o=[];return B(a,(function(t){!u(at,t)||e&&!u(V,t)||et(o,at[t])})),o};l||(J=function(){if(p(H,this))throw new Z("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?h(arguments[0]):void 0,e=N(t),a=function(t){var o=void 0===this?n:this;o===V&&i(a,ot,t),u(o,E)&&u(o[E],e)&&(o[E][e]=!1);var r=v(1,t);try{st(o,e,r)}catch(s){if(!(s instanceof Y))throw s;rt(o,e,r)}};return c&&it&&st(V,e,{configurable:!0,set:a}),ct(e,t)},H=J[F],R(H,"toString",(function(){return z(this).tag})),R(J,"withoutSetter",(function(t){return ct(N(t),t)})),C.f=pt,k.f=lt,_.f=dt,w.f=ft,x.f=I.f=gt,S.f=mt,L.f=function(t){return ct(q(t),t)},c&&(T(H,"description",{configurable:!0,get:function(){return z(this).description}}),s||R(V,"propertyIsEnumerable",pt,{unsafe:!0}))),o({global:!0,constructor:!0,wrap:!0,forced:!l,sham:!l},{Symbol:J}),B(y(nt),(function(t){P(t)})),o({target:D,stat:!0,forced:!l},{useSetter:function(){it=!0},useSimple:function(){it=!1}}),o({target:"Object",stat:!0,forced:!l,sham:!c},{create:ut,defineProperty:lt,defineProperties:dt,getOwnPropertyDescriptor:ft}),o({target:"Object",stat:!0,forced:!l},{getOwnPropertyNames:gt}),M(),U(J,D),j[E]=!0},e01a:function(t,e,a){"use strict";var o=a("23e7"),n=a("83ab"),i=a("cfe9"),r=a("e330"),s=a("1a2d"),c=a("1626"),l=a("3a9b"),d=a("577e"),u=a("edd0"),p=a("e893"),f=i.Symbol,g=f&&f.prototype;if(n&&c(f)&&(!("description"in g)||void 0!==f().description)){var m={},h=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:d(arguments[0]),e=l(g,this)?new f(t):void 0===t?f():f(t);return""===t&&(m[e]=!0),e};p(h,f),h.prototype=g,g.constructor=h;var v="Symbol(description detection)"===String(f("description detection")),b=r(g.valueOf),y=r(g.toString),x=/^Symbol\((.*)\)[^)]+$/,I=r("".replace),S=r("".slice);u(g,"description",{configurable:!0,get:function(){var t=b(this);if(s(m,t))return"";var e=y(t),a=v?S(e,7,-1):I(e,x,"$1");return""===a?void 0:a}}),o({global:!0,constructor:!0,forced:!0},{Symbol:h})}},e065:function(t,e,a){"use strict";var o=a("428f"),n=a("1a2d"),i=a("e538"),r=a("9bf2").f;t.exports=function(t){var e=o.Symbol||(o.Symbol={});n(e,t)||r(e,t,{value:i.f(t)})}},e538:function(t,e,a){"use strict";var o=a("b622");e.f=o}}]);