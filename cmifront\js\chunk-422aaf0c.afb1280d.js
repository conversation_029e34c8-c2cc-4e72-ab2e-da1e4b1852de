(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-422aaf0c"],{"01ad":function(t,e,r){"use strict";r("9e64")},"0b43":function(t,e,r){"use strict";var o=r("04f8");t.exports=o&&!!Symbol["for"]&&!!Symbol.keyFor},"129f":function(t,e,r){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},3482:function(t,e,r){"use strict";r.r(e);r("a4d3"),r("e01a"),r("caad"),r("b0c0"),r("ac1f"),r("841c"),r("498a");var o=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticStyle:{padding:"20px 5px",display:"flex","justify-content":"flex-start","flex-wrap":"wrap"}},[e("div",{staticClass:"search_box"},[e("span",[e("strong",[t._v(t._s(t.$t("common.cardType")))])]),t._v("  \n\t\t\t\t"),e("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:t.$t("support.selectCardtype"),filterable:""},model:{value:t.cardForm,callback:function(e){t.cardForm=e},expression:"cardForm"}},[e("Option",{attrs:{value:1}},[t._v(t._s(t.$t("stock.PhysicalSIM")))]),e("Option",{attrs:{value:2}},[t._v(t._s(t.$t("stock.eSIM")))]),e("Option",{attrs:{value:4}},[t._v("IMSI")])],1)],1),t._v("      \n\t\t\t"),e("div",{staticClass:"search_box"},[e("span",[e("strong",[t._v(t._s(t.$t("support.physicalOrCustomized")))])]),t._v("  \n\t\t\t\t"),e("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:t.$t("common.pleaseChoose"),filterable:""},model:{value:t.chargingMode,callback:function(e){t.chargingMode=e},expression:"chargingMode"}},[e("Option",{attrs:{value:2}},[t._v(t._s(t.$t("common.PhysicalSIM")))]),e("Option",{attrs:{value:1}},[t._v(t._s(t.$t("support.customizedSIM")))])],1)],1),t._v("      \n\t\t\t"),e("div",{staticClass:"search_box"},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{disabled:!t.cooperationMode||!t.corpId||"3"==t.cooperationMode,type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v(t._s(t.$t("address.search")))]),t._v("      \n\t\t\t\t"),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{disabled:!t.cooperationMode||!t.corpId||"3"==t.cooperationMode,type:"primary",ghost:"",icon:"md-add"},on:{click:function(e){return t.add()}}},[t._v(t._s(t.$t("support.createOrder")))])],1)]),e("Table",{ref:"selection",staticStyle:{width:"100%","margin-top":"20px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(r){var o=r.row;r.index;return[e("div",{staticStyle:{padding:"10px 5px 5px 0"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"revoke",expression:"'revoke'"},{name:"show",rawName:"v-show",value:1==o.orderStatus,expression:"row.orderStatus == 1"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"error",ghost:"",size:"small"},on:{click:function(e){return t.revoke(o)}}},[t._v(t._s(t.$t("support.revoke")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"invoice",expression:"'invoice'"},{name:"show",rawName:"v-show",value:![1,2].includes(+o.orderStatus)&&1==o.chargingMode,expression:"![1,2].includes(+row.orderStatus) && row.chargingMode == 1"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"primary",ghost:"",size:"small"},on:{click:function(e){return t.downloadInvoice(o)}}},[t._v(t._s(t.$t("support.downloadInvoice")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"list",expression:"'list'"},{name:"show",rawName:"v-show",value:[7,8].includes(+o.orderStatus),expression:"[7,8].includes(+row.orderStatus)"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"success",ghost:"",size:"small"},on:{click:function(e){return t.downloadNumberList(o)}}},[t._v(t._s(t.$t("support.downloadList")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"payslip",expression:"'payslip'"},{name:"show",rawName:"v-show",value:(3==o.orderStatus||9==o.orderStatus)&&1==o.chargingMode,expression:"(row.orderStatus == 3 || row.orderStatus == 9) && row.chargingMode == 1"}],staticStyle:{margin:"0 5px 5px 0"},attrs:{type:"warning",ghost:"",size:"small"},on:{click:function(e){return t.upload(o)}}},[t._v(t._s(t.$t("support.uploadPayslip")))])],1)]}}])}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:t.$t("support.createOrder"),"footer-hide":!0,"mask-closable":!1,width:"550px"},on:{"on-cancel":t.cancelModal},model:{value:t.ordersModal,callback:function(e){t.ordersModal=e},expression:"ordersModal"}},[e("div",[e("Form",{ref:"formValidate",staticStyle:{"font-weight":"bold"},attrs:{model:t.formValidate,rules:t.ruleValidate,"label-width":135,inline:""}},[e("div",{staticStyle:{width:"540px",display:"flex","flex-direction":"column"}},[e("FormItem",{attrs:{prop:"chargingMode"}},[e("RadioGroup",{staticStyle:{display:"flex","justify-content":"flex-start"},model:{value:t.formValidate.chargingMode,callback:function(e){t.$set(t.formValidate,"chargingMode",e)},expression:"formValidate.chargingMode"}},[e("div",{staticStyle:{"line-height":"30px"}},[e("Radio",{staticStyle:{"font-size":"16px"},attrs:{label:2}},[t._v(t._s(t.$t("common.PhysicalSIM")))])],1),t._v("\n\t\t\t\t\t\t\t\t     \n\t\t\t\t\t\t        "),e("div",{staticStyle:{"line-height":"30px"}},[e("Radio",{staticStyle:{"font-size":"16px"},attrs:{label:1}},[t._v(t._s(t.$t("support.customizedSIM")))]),t._v("    \n\t\t\t\t\t\t\t\t\t"),e("Tooltip",{staticStyle:{color:"blue","font-weight":"bold","font-size":"30px","vertical-align":"middle",pointer:"cursor","word-break":"break-all"},attrs:{"max-width":"400",content:t.description}},[e("Icon",{attrs:{type:"ios-alert-outline"}})],1)],1)])],1)],1),e("FormItem",{staticStyle:{width:"450px"},attrs:{label:t.$t("common.cardType"),prop:"cardForm"}},[e("Select",{attrs:{disabled:!t.formValidate.chargingMode,clearable:"",placeholder:t.$t("support.selectCardtype")},model:{value:t.formValidate.cardForm,callback:function(e){t.$set(t.formValidate,"cardForm",e)},expression:"formValidate.cardForm"}},[e("Option",{attrs:{value:1}},[t._v(t._s(t.$t("stock.PhysicalSIM")))]),e("Option",{attrs:{value:2}},[t._v(t._s(t.$t("stock.eSIM")))]),"2"==t.formValidate.chargingMode?e("Option",{attrs:{value:4}},[t._v("IMSI")]):t._e()],1)],1),"1"==t.formValidate.cardForm?e("FormItem",{staticStyle:{width:"450px"},attrs:{label:t.$t("support.productPackaging"),prop:"cardPackage",rules:"1"==t.formValidate.cardForm?t.ruleValidate.cardPackage:[{required:!1}]}},[e("Select",{attrs:{clearable:"",placeholder:t.$t("support.selectProductPackaging")},model:{value:t.formValidate.cardPackage,callback:function(e){t.$set(t.formValidate,"cardPackage",e)},expression:"formValidate.cardPackage"}},[e("Option",{attrs:{value:1}},[t._v(t._s(t.$t("support.nakedCard")))]),e("Option",{attrs:{value:2}},[t._v(t._s(t.$t("support.packagingCard")))])],1)],1):t._e(),e("FormItem",{staticStyle:{width:"450px"},attrs:{label:t.$t("common.quantity"),prop:"count"}},[e("Input",{attrs:{placeholder:t.$t("support.inputCardNumber"),clearable:""},model:{value:t.formValidate.count,callback:function(e){t.$set(t.formValidate,"count",e)},expression:"formValidate.count"}})],1),e("FormItem",{staticStyle:{width:"450px"},attrs:{label:t.$t("support.receivingCountry"),prop:"mcc",rules:"1"==t.formValidate.cardForm?t.ruleValidate.mcc:[{required:!1}]}},[e("Select",{attrs:{filterable:"",clearable:"",placeholder:t.$t("support.selectReceivingCountry")},model:{value:t.formValidate.mcc,callback:function(e){t.$set(t.formValidate,"mcc",e)},expression:"formValidate.mcc"}},t._l(t.localList,(function(r){return e("Option",{key:r.id,attrs:{value:r.mcc}},[t._v(t._s(r.countryEn))])})),1)],1),e("FormItem",{staticStyle:{width:"450px"},attrs:{label:t.$t("support.receiveeAddress"),prop:"address",rules:"1"==t.formValidate.cardForm?t.ruleValidate.logistic:[{required:!1}]}},[e("Input",{attrs:{placeholder:t.$t("support.inputAddress"),clearable:""},model:{value:t.formValidate.address,callback:function(e){t.$set(t.formValidate,"address","string"===typeof e?e.trim():e)},expression:"formValidate.address"}})],1),e("FormItem",{staticStyle:{width:"450px"},attrs:{label:t.$t("support.postalCode"),prop:"postcode",rules:"1"==t.formValidate.cardForm?t.ruleValidate.postcode:[{required:!1}]}},[e("Input",{attrs:{placeholder:t.$t("support.selectPostalCode"),clearable:""},model:{value:t.formValidate.postcode,callback:function(e){t.$set(t.formValidate,"postcode","string"===typeof e?e.trim():e)},expression:"formValidate.postcode"}})],1),e("FormItem",{staticStyle:{width:"450px"},attrs:{label:t.$t("support.receiverName"),prop:"recipient",rules:"1"==t.formValidate.cardForm?t.ruleValidate.logistic:[{required:!1}]}},[e("Input",{attrs:{placeholder:t.$t("support.inputRecrver"),clearable:""},model:{value:t.formValidate.recipient,callback:function(e){t.$set(t.formValidate,"recipient","string"===typeof e?e.trim():e)},expression:"formValidate.recipient"}})],1),e("FormItem",{staticStyle:{width:"450px"},attrs:{label:t.$t("support.contactNumber"),prop:"phoneNumber",rules:"1"==t.formValidate.cardForm?t.ruleValidate.logistic:[{required:!1}]}},[e("Input",{attrs:{placeholder:t.contactNumberFormat,clearable:""},model:{value:t.formValidate.phoneNumber,callback:function(e){t.$set(t.formValidate,"phoneNumber",e)},expression:"formValidate.phoneNumber"}})],1),e("FormItem",{staticStyle:{width:"450px"},attrs:{label:t.$t("support.cooperationModel"),prop:"cooperationMode"}},[e("Select",{attrs:{clearable:"",placeholder:t.$t("selectCooperationMode")},model:{value:t.formValidate.cooperationMode,callback:function(e){t.$set(t.formValidate,"cooperationMode",e)},expression:"formValidate.cooperationMode"}},[1==this.cooperationMode?e("Option",{attrs:{value:1}},[t._v(t._s(t.$t("support.distribution")))]):e("Option",{attrs:{value:2}},[t._v(t._s(t.$t("support.atoz")))])],1)],1),e("FormItem",{staticStyle:{width:"450px"},attrs:{label:t.$t("support.template"),prop:"templateId"}},[e("Select",{attrs:{clearable:"",filterable:"",placeholder:t.$t("support.chosetemplate")},model:{value:t.formValidate.templateId,callback:function(e){t.$set(t.formValidate,"templateId",e)},expression:"formValidate.templateId"}},t._l(t.tempList,(function(r,o){return e("Option",{key:o,attrs:{value:r.templateId}},[t._v(t._s(r.templateName))])})),1)],1),e("FormItem",{staticStyle:{width:"450px"},attrs:{label:t.$t("support.language"),prop:"language"}},[e("Select",{attrs:{clearable:"",placeholder:t.$t("support.selectLanguage")},model:{value:t.formValidate.language,callback:function(e){t.$set(t.formValidate,"language",e)},expression:"formValidate.language"}},[e("Option",{attrs:{value:1}},[t._v(t._s(t.$t("common.traditionalChinese")))]),e("Option",{attrs:{value:2}},[t._v(t._s(t.$t("common.english")))]),e("Option",{attrs:{value:3}},[t._v(t._s(t.$t("common.simplifiedChinese")))])],1)],1),"1"==t.formValidate.chargingMode?e("div",{staticStyle:{width:"540px",margin:"10px 0 30px 0"}},[e("FormItem",{staticStyle:{width:"450px"},attrs:{prop:"agree",rules:"1"==t.formValidate.chargingMode?t.ruleValidate.agree:[{required:!1}]}},[e("Row",[e("Col",{attrs:{span:"12"}},[e("CheckboxGroup",{model:{value:t.formValidate.agree,callback:function(e){t.$set(t.formValidate,"agree",e)},expression:"formValidate.agree"}},[e("Checkbox",[t._v("\n                     "+t._s(t.$t("support.agree"))+"\n                     "),e("a",{staticStyle:{color:"blue","text-decoration":"underline",cursor:"pointer"},attrs:{href:"javascript:void(0);"},on:{click:function(e){return t.instance("info")}}},[t._v(t._s(t.$t("support.Agreements")))])])],1)],1)],1)],1)],1):t._e()],1),e("div",{staticStyle:{"text-align":"center",margin:"4px 0"}},[e("Button",{staticStyle:{"margin-right":"30px"},on:{click:t.cancelModal}},[t._v(t._s(t.$t("support.back")))]),e("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:t.submitLoading},on:{click:t.submit}},[t._v(t._s(t.$t("common.determine")))])],1)],1)]),e("Modal",{attrs:{title:t.$t("support.uploadPayslip"),"footer-hide":!0,"mask-closable":!1,width:"450px"},on:{"on-cancel":t.cancelModal},model:{value:t.PaymentModal,callback:function(e){t.PaymentModal=e},expression:"PaymentModal"}},[e("div",[e("Form",{ref:"formobj",staticStyle:{"font-weight":"bold"},attrs:{model:t.formobj,rules:t.ruleobj,"label-width":100,"label-height":100,inline:""}},[e("FormItem",{staticStyle:{"font-size":"14px","font-weight":"bold"},attrs:{label:t.$t("support.payslip"),prop:"file"}},[e("Upload",{staticStyle:{width:"250px","margin-top":"50px"},attrs:{type:"drag",action:t.uploadUrl,"before-upload":t.handleBeforeUpload,"on-progress":t.fileUploading},model:{value:t.formobj.file,callback:function(e){t.$set(t.formobj,"file",e)},expression:"formobj.file"}},[e("div",{staticStyle:{padding:"20px 0"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v(t._s(t.$t("support.uploadPicture")))])],1)]),t.file?e("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"300px"}},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name)+"\n\t\t\t\t\t\t\t\t")],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e()],1)],1),e("div",{staticStyle:{"text-align":"center",margin:"4px 0"}},[e("Button",{staticStyle:{"margin-right":"30px"},on:{click:t.cancelModal}},[t._v(t._s(t.$t("support.back")))]),e("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:t.pictureLoading},on:{click:t.pictureSubmit}},[t._v(t._s(t.$t("common.determine")))])],1)],1)]),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelExportModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v(t._s(t.$t("exportMS")))]),e("FormItem",{attrs:{label:t.$t("exportID")}},[e("span",{staticStyle:{width:"100px"}},[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:t.$t("exportFlie")}},[e("span",[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v(t._s(t.$t("downloadResult")))])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelExportModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v(t._s(t.$t("Goto")))])],1)]),e("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1)},a=[],i=(r("d9e2"),r("14d9"),r("4e82"),r("d3b7"),r("3ca3"),r("466d"),r("ddb0"),r("2b3d"),r("bf19"),r("9861"),r("88a7"),r("271a"),r("5494"),r("66df")),n="/order/blankCardOrder",s=function(t){return i["a"].request({url:n+"/blankCardIllustrate",data:t,method:"get"})},c=function(t){return i["a"].request({url:n+"/addCardOrders",data:t,method:"post"})},l=function(t){return i["a"].request({url:n+"/cancelOrder/".concat(t,"/"),method:"put"})},d=function(t){return i["a"].request({url:n+"/blankCardExport",params:t,method:"post"})},u=function(t){return i["a"].request({url:n+"/uploadPaymentProof",data:t,method:"POST",contentType:"multipart/form-data"})},p=function(t){return i["a"].request({url:n+"/getProtocol",data:t,method:"get"})},m=r("ba27"),f=r("6dfa"),g={data:function(){var t=this,e=function(e,r,o){t.file?o():o(new Error(t.$t("support.pleaseUploadFile")))};return{total:0,currentPage:1,cooperationMode:"",corpId:"",orderStatus:"",cardForm:"",chargingMode:"",orderId:"",payOrderStatus:"",taskId:"",taskName:"",description:"",uploadUrl:"",contactNumberFormat:"",content:"",uploadList:[],file:null,loading:!1,submitLoading:!1,pictureLoading:!1,searchloading:!1,exportModal:!1,ordersModal:!1,PaymentModal:!1,data:[],tempList:[],localList:[],columns:[{title:this.$t("support.orderId"),key:"orderId",minWidth:180,align:"center",tooltip:!0},{title:this.$t("support.Ordertime"),key:"createTime",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var r=e.row,o="";if(r.createTime){var a=new Date(r.createTime),i=a.getFullYear(),n=a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1,s=a.getDate()<10?"0"+a.getDate():a.getDate(),c=a.getHours()<10?"0"+a.getHours():a.getHours(),l=a.getMinutes()<10?"0"+a.getMinutes():a.getMinutes(),d=a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds();o=i+"-"+n+"-"+s+" "+c+":"+l+":"+d}else o="";return t("label",o)}},{title:this.$t("support.Cardtype"),key:"cooperationMode",minWidth:135,align:"center",tooltip:!0,render:function(e,r){var o=r.row,a="1"==o.cooperationMode?t.$t("support.distribution"):"2"==o.cooperationMode?t.$t("support.atoz"):"";return e("label",a)}},{title:this.$t("common.cardType"),key:"cardForm",minWidth:135,align:"center",tooltip:!0,render:function(e,r){var o=r.row,a="1"==o.cardForm?t.$t("stock.PhysicalSIM"):"2"==o.cardForm?t.$t("stock.eSIM"):"4"==o.cardForm?"IMSI":"";return e("label",a)}},{title:this.$t("support.physicalOrCustomized"),key:"chargingMode",minWidth:160,align:"center",tooltip:!0,render:function(e,r){var o=r.row,a=1==o.chargingMode?t.$t("support.customizedSIM"):2==o.chargingMode?t.$t("common.PhysicalSIM"):"";return e("label",a)}},{title:this.$t("common.quantity"),key:"count",minWidth:120,align:"center",tooltip:!0},{title:this.$t("order.order_state"),key:"orderStatus",align:"center",minWidth:186,render:function(e,r){var o=r.row,a="1"==o.orderStatus?"#2b85e4":"2"==o.orderStatus?"#00aa00":"3"==o.orderStatus?"#ff0000":"4"==o.orderStatus?"#69e457":"5"==o.orderStatus?"#e47a49":"6"==o.orderStatus?"#dd74e4":"7"==o.orderStatus?"#24cbe4":"8"==o.orderStatus?"#7009e4":"9"==o.orderStatus?"#e4b809":"10"==o.orderStatus?"#ff9900":"11"==o.orderStatus?"#e4e424":"12"==o.orderStatus?"#24e424":"13"==o.orderStatus?"#e42424":"",i="1"==o.orderStatus?t.$t("support.ordered"):"2"==o.orderStatus?t.$t("support.cancelled"):"3"==o.orderStatus?t.$t("support.pendingPayment"):"4"==o.orderStatus?t.$t("support.PaymentConfirmed"):"5"==o.orderStatus?t.$t("order.delivered"):"6"==o.orderStatus?t.$t("support.deliveryProgress"):"7"==o.orderStatus?t.$t("support.delivered"):"8"==o.orderStatus?t.$t("support.deliveryFailed"):"9"==o.orderStatus?t.$t("support.PaymentNotConfirmed"):"10"==o.orderStatus?t.$t("support.paymentConfirmed"):"11"==o.orderStatus?t.$t("support.rollbackInProgress"):"12"==o.orderStatus?t.$t("support.rollbackSuccess"):"13"==o.orderStatus?t.$t("support.rollbackFailure"):"";return e("label",{style:{color:a}},i)}},{title:this.$t("support.receiveeAddress"),key:"address",minWidth:140,align:"center",tooltip:!0},{title:this.$t("support.receiverName"),key:"addressee",minWidth:130,align:"center",tooltip:!0},{title:this.$t("support.contactNumber"),key:"phoneNumber",minWidth:130,align:"center",tooltip:!0},{title:this.$t("support.deliveryCompany"),key:"logisticCompany",minWidth:140,align:"center",tooltip:!0},{title:this.$t("support.trackingNumber"),key:"logistic",minWidth:140,align:"center",tooltip:!0},{title:this.$t("support.orderBatch"),key:"orderBatch",minWidth:120,align:"center",tooltip:!0},{title:this.$t("support.generateInvoice"),key:"invoiceTime",minWidth:165,align:"center",tooltip:!0,render:function(t,e){var r=e.row,o="";if(r.invoiceTime){var a=new Date(r.invoiceTime),i=a.getFullYear(),n=a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1,s=a.getDate()<10?"0"+a.getDate():a.getDate(),c=a.getHours()<10?"0"+a.getHours():a.getHours(),l=a.getMinutes()<10?"0"+a.getMinutes():a.getMinutes(),d=a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds();o=i+"-"+n+"-"+s+" "+c+":"+l+":"+d}else o="";return t("label",o)}},{title:this.$t("support.uploadPayslipTime"),key:"paymentProofsTime",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var r=e.row,o="";if(r.paymentProofsTime){var a=new Date(r.paymentProofsTime),i=a.getFullYear(),n=a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1,s=a.getDate()<10?"0"+a.getDate():a.getDate(),c=a.getHours()<10?"0"+a.getHours():a.getHours(),l=a.getMinutes()<10?"0"+a.getMinutes():a.getMinutes(),d=a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds();o=i+"-"+n+"-"+s+" "+c+":"+l+":"+d}else o="";return t("label",o)}},{title:this.$t("support.orderConfirmTime"),key:"confirmTime",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var r=e.row,o="";if(r.confirmTime){var a=new Date(r.confirmTime),i=a.getFullYear(),n=a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1,s=a.getDate()<10?"0"+a.getDate():a.getDate(),c=a.getHours()<10?"0"+a.getHours():a.getHours(),l=a.getMinutes()<10?"0"+a.getMinutes():a.getMinutes(),d=a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds();o=i+"-"+n+"-"+s+" "+c+":"+l+":"+d}else o="";return t("label",o)}},{title:this.$t("support.deliveryTime"),key:"deliverTime",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var r=e.row,o="";if(r.deliverTime){var a=new Date(r.deliverTime),i=a.getFullYear(),n=a.getMonth()+1<10?"0"+(a.getMonth()+1):a.getMonth()+1,s=a.getDate()<10?"0"+a.getDate():a.getDate(),c=a.getHours()<10?"0"+a.getHours():a.getHours(),l=a.getMinutes()<10?"0"+a.getMinutes():a.getMinutes(),d=a.getSeconds()<10?"0"+a.getSeconds():a.getSeconds();o=i+"-"+n+"-"+s+" "+c+":"+l+":"+d}else o="";return t("label",o)}},{title:this.$t("address.action"),slot:"action",minWidth:220,align:"center",fixed:"right"},{title:this.$t("support.template"),key:"templateName",minWidth:160,align:"center",tooltip:!0},{title:this.$t("support.language"),key:"language",minWidth:160,align:"center",tooltip:!0,render:function(e,r){var o=r.row,a=1==o.language?t.$t("common.traditionalChinese"):2==o.language?t.$t("common.english"):3==o.language?t.$t("common.simplifiedChinese"):"";return e("label",a)}},{title:this.$t("support.productPackaging"),key:"cardPackage",minWidth:160,align:"center",tooltip:!0,render:function(e,r){var o=r.row,a=1==o.cardPackage?t.$t("support.nakedCard"):2==o.cardPackage?t.$t("support.packagingCard"):"";return e("label",a)}}],formValidate:{chargingMode:"",cardForm:"",count:"",address:"",recipient:"",phoneNumber:"",cooperationMode:"",cardPackage:"",mcc:"",postcode:"",templateId:"",language:"",agree:[]},ruleValidate:{chargingMode:[{required:!0,message:this.$t("support.PaymentMandatory")}],cardForm:[{required:!0,message:this.$t("support.cardTypeMandatory")}],count:[{required:!0,message:this.$t("support.cardMumberMandatory")},{pattern:/^[0-9]*[1-9][0-9]*$/,trigger:"blur",message:this.$t("support.wrongFormat")}],address:[{required:!0,message:this.$t("support.addressMandatory")}],recipient:[{required:!0,message:this.$t("support.receiverMandatory")}],phoneNumber:[{required:!0,message:this.$t("support.contactNumberMandatory")}],cooperationMode:[{required:!0,message:this.$t("support.cooperationMandatory")}],cardPackage:[{required:!0,message:this.$t("support.productPackagingEmpty")}],mcc:[{required:!0,message:this.$t("support.receivingCountryEmpty")}],postcode:[{required:!0,message:this.$t("support.postalCodeEmpty")}],templateId:[{required:!0,message:this.$t("support.SMSempty")}],language:[{required:!0,message:this.$t("support.languageEmpty")}],agree:[{required:!0,type:"array",min:1,message:this.$t("support.agreement"),trigger:"change"}]},formobj:{file:""},ruleobj:{file:[{required:!0,validator:e,trigger:"change"}]}}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),this.corpId=sessionStorage.getItem("corpId"),this.cooperationMode&&this.corpId&&"3"!=this.cooperationMode&&(this.goPageFirst(1),this.getDescription())},methods:{goPageFirst:function(t){var e=this;this.data=[],this.loading=!0;var r=this;Object(m["i"])({pageNum:t,pageSize:10,orderUserId:this.corpId,cooperationMode:this.cooperationMode,cardForms:this.cardForm,chargingMode:this.chargingMode}).then((function(o){"0000"==o.code&&(r.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.data=o.data,e.total=o.count)})).catch((function(t){console.error(t)})).finally((function(){r.loading=!1,e.searchloading=!1}))},goPage:function(t){this.goPageFirst(t)},search:function(){this.searchloading=!0,this.goPageFirst(1)},add:function(){this.ordersModal=!0,this.formValidate.cooperationMode=1==this.cooperationMode?this.formValidate.cooperationMode=1:this.formValidate.cooperationMode=2,this.getChannelSmsTemplate(),this.getLocalList(),this.getContent()},revoke:function(t){var e=this;this.$Modal.confirm({title:this.$t("support.confirmRevocation"),onOk:function(){l(t.orderId).then((function(t){"0000"===t.code&&e.$Notice.success({title:e.$t("address.Operationreminder"),desc:e.$t("common.Successful")}),e.goPageFirst(1)}))}})},downloadNumberList:function(t){var e=this;d({corpId:this.corpId,orderBatch:t.orderBatch}).then((function(t){e.exportModal=!0,e.taskId=t.data.data.taskId,e.taskName=t.data.data.taskName})).catch()},downloadInvoice:function(t){var e=this;Object(m["h"])({id:t.orderId,type:1}).then((function(t){var r=t.data,o=decodeURIComponent(escape(t.headers["content-disposition"].match(/=(.*)$/)[1]));if("download"in document.createElement("a")){var a=e.$refs.downloadLink,i=URL.createObjectURL(r);a.download=o,a.href=i,a.click(),URL.revokeObjectURL(i)}else navigator.msSaveBlob(r,o)})).catch()},cancelExportModal:function(){this.exportModal=!1},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},cancelModal:function(){this.formValidate.chargingMode="",this.$refs["formValidate"].resetFields(),this.ordersModal=!1,this.PaymentModal=!1,this.file="",this.$refs["formobj"].resetFields()},upload:function(t){this.PaymentModal=!0,this.payOrderStatus=t.orderStatus,this.orderId=t.orderId},handleBeforeUpload:function(t,e){var r=t.size/1024/1024>10;return r?(this.$Notice.warning({title:this.$t("address.Operationreminder"),desc:this.$t("support.pictureSize")}),!1):(this.file=t,this.uploadList=e,!1)},fileUploading:function(t,e,r){this.message=this.$t("support.fileUploadedAndProgressDisappears")},removeFile:function(){this.file=""},instance:function(t){this.$Modal.info({title:this.$t("support.AgreementContent"),content:this.content,width:"800px"})},submit:function(){var t=this;this.$refs["formValidate"].validate((function(e){e&&(t.submitLoading=!0,c({orderUserId:sessionStorage.getItem("corpId"),chargingMode:t.formValidate.chargingMode,cardForm:t.formValidate.cardForm,count:t.formValidate.count,address:t.formValidate.address,recipient:t.formValidate.recipient,phoneNumber:t.formValidate.phoneNumber,cooperationMode:t.formValidate.cooperationMode,cardPackage:"1"==t.formValidate.cardForm?t.formValidate.cardPackage:void 0,mcc:t.formValidate.mcc,postcode:t.formValidate.postcode,templateId:t.formValidate.templateId,language:t.formValidate.language}).then((function(e){if("0000"!==e.code)throw e;t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.submitLoading=!1,t.ordersModal=!1,t.goPageFirst(1),t.$refs["formValidate"].resetFields()})).catch((function(e){t.submitLoading=!1})).finally((function(){})))}))},pictureSubmit:function(){var t=this;this.$refs["formobj"].validate((function(e){if(e){var r=new FormData;r.append("corpId",t.corpId),r.append("orderId",t.orderId),r.append("orderStatus",t.payOrderStatus),r.append("paymentProofs",t.file),t.pictureLoading=!0,u(r).then((function(e){if("0000"!==e.code)throw e;t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.pictureLoading=!1,t.PaymentModal=!1,t.goPageFirst(1),t.file="",t.$refs["formobj"].resetFields()})).catch((function(e){t.pictureLoading=!1})).finally((function(){}))}}))},getDescription:function(){var t=this;s({corpId:this.corpId}).then((function(e){if(!e||"0000"!=e.code)throw e;t.description=e.data.illustrate,t.contactNumberFormat=e.data.phoneIllustrate})).catch((function(t){})).finally((function(){}))},getChannelSmsTemplate:function(){var t=this;Object(f["t"])({corpId:sessionStorage.getItem("corpId"),cooperationMode:sessionStorage.getItem("cooperationMode")}).then((function(e){"0000"===e.code&&(t.tempList=e.data)})).catch((function(t){console.log(t)})).finally((function(){}))},getLocalList:function(){var t=this;Object(f["A"])().then((function(e){if(!e||"0000"!=e.code)throw e;var r=e.data;t.localList=r,t.localList.sort((function(t,e){return t.countryEn.localeCompare(e.countryEn)}))})).catch((function(t){})).finally((function(){}))},getContent:function(){var t=this;p().then((function(e){if(!e||"0000"!=e.code)throw e;t.content="zh-CN"===t.$i18n.locale?e.data.protocolCn:e.data.protocolEn})).catch((function(t){})).finally((function(){}))}}},h=g,v=(r("01ad"),r("2877")),b=Object(v["a"])(h,o,a,!1,null,"104dc1f9",null);e["default"]=b.exports},"3f7e":function(t,e,r){"use strict";var o=r("b5db"),a=o.match(/firefox\/(\d+)/i);t.exports=!!a&&+a[1]},"466d":function(t,e,r){"use strict";var o=r("c65b"),a=r("d784"),i=r("825a"),n=r("7234"),s=r("50c4"),c=r("577e"),l=r("1d80"),d=r("dc4a"),u=r("8aa5"),p=r("14c3");a("match",(function(t,e,r){return[function(e){var r=l(this),a=n(e)?void 0:d(e,t);return a?o(a,e,r):new RegExp(e)[t](c(r))},function(t){var o=i(this),a=c(t),n=r(e,o,a);if(n.done)return n.value;if(!o.global)return p(o,a);var l=o.unicode;o.lastIndex=0;var d,m=[],f=0;while(null!==(d=p(o,a))){var g=c(d[0]);m[f]=g,""===g&&(o.lastIndex=u(a,s(o.lastIndex),l)),f++}return 0===f?null:m}]}))},"4e82":function(t,e,r){"use strict";var o=r("23e7"),a=r("e330"),i=r("59ed"),n=r("7b0b"),s=r("07fa"),c=r("083a"),l=r("577e"),d=r("d039"),u=r("addb"),p=r("a640"),m=r("3f7e"),f=r("99f4"),g=r("1212"),h=r("ea83"),v=[],b=a(v.sort),y=a(v.push),S=d((function(){v.sort(void 0)})),$=d((function(){v.sort(null)})),M=p("sort"),k=!d((function(){if(g)return g<70;if(!(m&&m>3)){if(f)return!0;if(h)return h<603;var t,e,r,o,a="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:r=3;break;case 68:case 71:r=4;break;default:r=2}for(o=0;o<47;o++)v.push({k:e+o,v:r})}for(v.sort((function(t,e){return e.v-t.v})),o=0;o<v.length;o++)e=v[o].k.charAt(0),a.charAt(a.length-1)!==e&&(a+=e);return"DGBEFHACIJK"!==a}})),x=S||!$||!M||!k,w=function(t){return function(e,r){return void 0===r?-1:void 0===e?1:void 0!==t?+t(e,r)||0:l(e)>l(r)?1:-1}};o({target:"Array",proto:!0,forced:x},{sort:function(t){void 0!==t&&i(t);var e=n(this);if(k)return void 0===t?b(e):b(e,t);var r,o,a=[],l=s(e);for(o=0;o<l;o++)o in e&&y(a,e[o]);u(a,w(t)),r=s(a),o=0;while(o<r)e[o]=a[o++];while(o<l)c(e,o++);return e}})},"57b9":function(t,e,r){"use strict";var o=r("c65b"),a=r("d066"),i=r("b622"),n=r("cb2d");t.exports=function(){var t=a("Symbol"),e=t&&t.prototype,r=e&&e.valueOf,s=i("toPrimitive");e&&!e[s]&&n(e,s,(function(t){return o(r,this)}),{arity:1})}},"5a47":function(t,e,r){"use strict";var o=r("23e7"),a=r("04f8"),i=r("d039"),n=r("7418"),s=r("7b0b"),c=!a||i((function(){n.f(1)}));o({target:"Object",stat:!0,forced:c},{getOwnPropertySymbols:function(t){var e=n.f;return e?e(s(t)):[]}})},"841c":function(t,e,r){"use strict";var o=r("c65b"),a=r("d784"),i=r("825a"),n=r("7234"),s=r("1d80"),c=r("129f"),l=r("577e"),d=r("dc4a"),u=r("14c3");a("search",(function(t,e,r){return[function(e){var r=s(this),a=n(e)?void 0:d(e,t);return a?o(a,e,r):new RegExp(e)[t](l(r))},function(t){var o=i(this),a=l(t),n=r(e,o,a);if(n.done)return n.value;var s=o.lastIndex;c(s,0)||(o.lastIndex=0);var d=u(o,a);return c(o.lastIndex,s)||(o.lastIndex=s),null===d?-1:d.index}]}))},"99f4":function(t,e,r){"use strict";var o=r("b5db");t.exports=/MSIE|Trident/.test(o)},"9e64":function(t,e,r){},a4d3:function(t,e,r){"use strict";r("d9f5"),r("b4f8"),r("c513"),r("e9c4"),r("5a47")},b4f8:function(t,e,r){"use strict";var o=r("23e7"),a=r("d066"),i=r("1a2d"),n=r("577e"),s=r("5692"),c=r("0b43"),l=s("string-to-symbol-registry"),d=s("symbol-to-string-registry");o({target:"Symbol",stat:!0,forced:!c},{for:function(t){var e=n(t);if(i(l,e))return l[e];var r=a("Symbol")(e);return l[e]=r,d[r]=e,r}})},ba27:function(t,e,r){"use strict";r.d(e,"i",(function(){return i})),r.d(e,"d",(function(){return n})),r.d(e,"c",(function(){return s})),r.d(e,"h",(function(){return c})),r.d(e,"e",(function(){return l})),r.d(e,"f",(function(){return d})),r.d(e,"k",(function(){return u})),r.d(e,"g",(function(){return p})),r.d(e,"a",(function(){return m})),r.d(e,"j",(function(){return f})),r.d(e,"b",(function(){return g}));var o=r("66df"),a=(r("1157"),"/order/blankCardOrder"),i=function(t){return o["a"].request({url:a+"/getOrder",params:t,method:"get"})},n=function(t){return o["a"].request({url:a+"/comfirmOrder/"+t.id,data:t,method:"put"})},s=function(t){return o["a"].request({url:a+"/cancelOrder/".concat(t),method:"put"})},c=function(t){return o["a"].request({url:a+"/download",params:t,method:"get",responseType:"blob"})},l=function(t){return o["a"].request({url:a+"/generateInvoice",data:t,method:"post"})},d=function(t){return o["a"].request({url:a+"/regenerateInvoice",data:t,method:"post"})},u=function(t){return o["a"].request({url:a+"/deliver",data:t,method:"put",contentType:"multipart/form-data"})},p=function(t){return o["a"].request({url:a+"/getInvoiceInfo",params:t,method:"get"})},m=function(t){return o["a"].request({url:"cms/channel/getInfo4Order",params:t,method:"get"})},f=function(t){return o["a"].request({url:a+"/rollback",params:t,method:"put"})},g=function(t){return o["a"].request({url:a+"/export",data:t,method:"post",responseType:"blob"})}},c513:function(t,e,r){"use strict";var o=r("23e7"),a=r("1a2d"),i=r("d9b5"),n=r("0d51"),s=r("5692"),c=r("0b43"),l=s("symbol-to-string-registry");o({target:"Symbol",stat:!0,forced:!c},{keyFor:function(t){if(!i(t))throw new TypeError(n(t)+" is not a symbol");if(a(l,t))return l[t]}})},d9f5:function(t,e,r){"use strict";var o=r("23e7"),a=r("cfe9"),i=r("c65b"),n=r("e330"),s=r("c430"),c=r("83ab"),l=r("04f8"),d=r("d039"),u=r("1a2d"),p=r("3a9b"),m=r("825a"),f=r("fc6a"),g=r("a04b"),h=r("577e"),v=r("5c6c"),b=r("7c73"),y=r("df75"),S=r("241c"),$=r("057f"),M=r("7418"),k=r("06cf"),x=r("9bf2"),w=r("37e8"),I=r("d1e7"),_=r("cb2d"),V=r("edd0"),F=r("5692"),P=r("f772"),C=r("d012"),O=r("90e3"),N=r("b622"),q=r("e538"),L=r("e065"),T=r("57b9"),j=r("d44e"),D=r("69f3"),E=r("b727").forEach,R=P("hidden"),W="Symbol",z="prototype",B=D.set,U=D.getterFor(W),H=Object[z],A=a.Symbol,G=A&&A[z],Y=a.RangeError,J=a.TypeError,K=a.QObject,Q=k.f,X=x.f,Z=$.f,tt=I.f,et=n([].push),rt=F("symbols"),ot=F("op-symbols"),at=F("wks"),it=!K||!K[z]||!K[z].findChild,nt=function(t,e,r){var o=Q(H,e);o&&delete H[e],X(t,e,r),o&&t!==H&&X(H,e,o)},st=c&&d((function(){return 7!==b(X({},"a",{get:function(){return X(this,"a",{value:7}).a}})).a}))?nt:X,ct=function(t,e){var r=rt[t]=b(G);return B(r,{type:W,tag:t,description:e}),c||(r.description=e),r},lt=function(t,e,r){t===H&&lt(ot,e,r),m(t);var o=g(e);return m(r),u(rt,o)?(r.enumerable?(u(t,R)&&t[R][o]&&(t[R][o]=!1),r=b(r,{enumerable:v(0,!1)})):(u(t,R)||X(t,R,v(1,b(null))),t[R][o]=!0),st(t,o,r)):X(t,o,r)},dt=function(t,e){m(t);var r=f(e),o=y(r).concat(gt(r));return E(o,(function(e){c&&!i(pt,r,e)||lt(t,e,r[e])})),t},ut=function(t,e){return void 0===e?b(t):dt(b(t),e)},pt=function(t){var e=g(t),r=i(tt,this,e);return!(this===H&&u(rt,e)&&!u(ot,e))&&(!(r||!u(this,e)||!u(rt,e)||u(this,R)&&this[R][e])||r)},mt=function(t,e){var r=f(t),o=g(e);if(r!==H||!u(rt,o)||u(ot,o)){var a=Q(r,o);return!a||!u(rt,o)||u(r,R)&&r[R][o]||(a.enumerable=!0),a}},ft=function(t){var e=Z(f(t)),r=[];return E(e,(function(t){u(rt,t)||u(C,t)||et(r,t)})),r},gt=function(t){var e=t===H,r=Z(e?ot:f(t)),o=[];return E(r,(function(t){!u(rt,t)||e&&!u(H,t)||et(o,rt[t])})),o};l||(A=function(){if(p(G,this))throw new J("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?h(arguments[0]):void 0,e=O(t),r=function(t){var o=void 0===this?a:this;o===H&&i(r,ot,t),u(o,R)&&u(o[R],e)&&(o[R][e]=!1);var n=v(1,t);try{st(o,e,n)}catch(s){if(!(s instanceof Y))throw s;nt(o,e,n)}};return c&&it&&st(H,e,{configurable:!0,set:r}),ct(e,t)},G=A[z],_(G,"toString",(function(){return U(this).tag})),_(A,"withoutSetter",(function(t){return ct(O(t),t)})),I.f=pt,x.f=lt,w.f=dt,k.f=mt,S.f=$.f=ft,M.f=gt,q.f=function(t){return ct(N(t),t)},c&&(V(G,"description",{configurable:!0,get:function(){return U(this).description}}),s||_(H,"propertyIsEnumerable",pt,{unsafe:!0}))),o({global:!0,constructor:!0,wrap:!0,forced:!l,sham:!l},{Symbol:A}),E(y(at),(function(t){L(t)})),o({target:W,stat:!0,forced:!l},{useSetter:function(){it=!0},useSimple:function(){it=!1}}),o({target:"Object",stat:!0,forced:!l,sham:!c},{create:ut,defineProperty:lt,defineProperties:dt,getOwnPropertyDescriptor:mt}),o({target:"Object",stat:!0,forced:!l},{getOwnPropertyNames:ft}),T(),j(A,W),C[R]=!0},e01a:function(t,e,r){"use strict";var o=r("23e7"),a=r("83ab"),i=r("cfe9"),n=r("e330"),s=r("1a2d"),c=r("1626"),l=r("3a9b"),d=r("577e"),u=r("edd0"),p=r("e893"),m=i.Symbol,f=m&&m.prototype;if(a&&c(m)&&(!("description"in f)||void 0!==m().description)){var g={},h=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:d(arguments[0]),e=l(f,this)?new m(t):void 0===t?m():m(t);return""===t&&(g[e]=!0),e};p(h,m),h.prototype=f,f.constructor=h;var v="Symbol(description detection)"===String(m("description detection")),b=n(f.valueOf),y=n(f.toString),S=/^Symbol\((.*)\)[^)]+$/,$=n("".replace),M=n("".slice);u(f,"description",{configurable:!0,get:function(){var t=b(this);if(s(g,t))return"";var e=y(t),r=v?M(e,7,-1):$(e,S,"$1");return""===r?void 0:r}}),o({global:!0,constructor:!0,forced:!0},{Symbol:h})}},e065:function(t,e,r){"use strict";var o=r("428f"),a=r("1a2d"),i=r("e538"),n=r("9bf2").f;t.exports=function(t){var e=o.Symbol||(o.Symbol={});a(e,t)||n(e,t,{value:i.f(t)})}},e538:function(t,e,r){"use strict";var o=r("b622");e.f=o},ea83:function(t,e,r){"use strict";var o=r("b5db"),a=o.match(/AppleWebKit\/(\d+)\./);t.exports=!!a&&+a[1]}}]);