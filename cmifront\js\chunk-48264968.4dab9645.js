(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-48264968"],{"00b4":function(e,t,a){"use strict";a("ac1f");var r=a("23e7"),n=a("c65b"),o=a("1626"),i=a("825a"),s=a("577e"),c=function(){var e=!1,t=/[ac]/;return t.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===t.test("abc")&&e}(),l=/./.test;r({target:"RegExp",proto:!0,forced:!c},{test:function(e){var t=i(this),a=s(e),r=t.exec;if(!o(r))return n(l,t,a);var c=n(r,t,a);return null!==c&&(i(c),!0)}})},"1ccf6":function(e,t,a){"use strict";a("890d")},"366f":function(e,t,a){"use strict";a("f874")},"4a57":function(e,t,a){"use strict";a("ae1f")},"7bfc":function(e,t,a){"use strict";a.d(t,"a",(function(){return i})),a.d(t,"j",(function(){return s})),a.d(t,"i",(function(){return c})),a.d(t,"f",(function(){return l})),a.d(t,"h",(function(){return u})),a.d(t,"d",(function(){return d})),a.d(t,"e",(function(){return m})),a.d(t,"c",(function(){return p})),a.d(t,"b",(function(){return h})),a.d(t,"g",(function(){return g}));var r=a("66df"),n="/mkt",o="/cms",i=function(e){return r["a"].request({url:n+"/campaign/add",data:e,method:"post"})},s=function(e){return r["a"].request({url:n+"/campaign/edit",data:e,method:"post"})},c=function(e){return r["a"].request({url:o+"/channel/mktChannelPage",data:e,method:"post"})},l=function(e){return r["a"].request({url:n+"/campaign/getCorpList",params:e,method:"get"})},u=function(e){return r["a"].request({url:n+"/campaign/getRuleDetails",method:"get",params:e})},d=function(e){return r["a"].request({url:n+"/campaign/getCorpDetails",method:"get",params:e})},m=function(e){return r["a"].request({url:n+"/campaign/getCorpDetailsPage",data:e,method:"post"})},p=function(e){return r["a"].request({url:n+"/campaign/page",data:e,method:"post"})},h=function(e){return r["a"].request({url:n+"/settlement/addSettlementLog",data:e,method:"post"})},g=function(e){return r["a"].request({url:n+"/campaign/getMktCorp",params:e,method:"get"})}},"890d":function(e,t,a){},ae1f:function(e,t,a){},ceeb:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padiing:"16px"}},[t("Form",{ref:"searchForm",attrs:{"label-position":"right",model:e.searchObj,inline:"",rules:e.formRuleList}},[t("FormItem",{attrs:{label:"活动名称","label-width":80}},[t("Input",{attrs:{placeholder:"请输入活动名称",clearable:""},model:{value:e.searchObj.campaignName,callback:function(t){e.$set(e.searchObj,"campaignName",t)},expression:"searchObj.campaignName"}})],1),t("FormItem",{attrs:{label:"合作模式","label-width":80}},[t("Select",{staticClass:"fixed-width-select",attrs:{filterable:"",placeholder:"请选择合作模式",clearable:""},model:{value:e.searchObj.cooperationMode,callback:function(t){e.$set(e.searchObj,"cooperationMode",t)},expression:"searchObj.cooperationMode"}},[t("Option",{attrs:{value:"1"}},[e._v("代销")]),t("Option",{attrs:{value:"2"}},[e._v("A2Z")])],1)],1),t("FormItem",{attrs:{label:"活动开始时间","label-width":100}},[t("DatePicker",{attrs:{type:"date",placeholder:"请选择开始时间"},model:{value:e.searchObj.startTime,callback:function(t){e.$set(e.searchObj,"startTime",t)},expression:"searchObj.startTime"}})],1),t("FormItem",{attrs:{label:"活动结束时间","label-width":100,prop:"endTime"}},[t("DatePicker",{attrs:{type:"date",placeholder:"请选择结束时间"},model:{value:e.searchObj.endTime,callback:function(t){e.$set(e.searchObj,"endTime",t)},expression:"searchObj.endTime"}})],1),t("FormItem",{attrs:{label:"参与客户","label-width":80}},[t("Select",{attrs:{placeholder:"请选择参与客户",filterable:"",clearable:""},model:{value:e.searchObj.corpId,callback:function(t){e.$set(e.searchObj,"corpId",t)},expression:"searchObj.corpId"}},e._l(e.customerList,(function(a){return t("Option",{key:a.corpId,staticClass:"company-option",attrs:{value:a.corpId}},[e._v(e._s(a.companyName))])})),1)],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"ios-search"},on:{click:function(t){return e.loadByPage(1)}}},[e._v("搜索")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"addMarketingActivity",expression:"'addMarketingActivity'"}],staticStyle:{"margin-left":"20px"},attrs:{type:"info",icon:"ios-add"},on:{click:e.newMarketing}},[e._v("新建")])],1)],1),t("Table",{attrs:{columns:e.columns,data:e.tableData,loading:e.tableLoading,ellipsis:!0},scopedSlots:e._u([{key:"campaignStatus",fn:function(a){var r=a.row;return[t("span",{style:{color:e.getStatusColor(r.campaignStatus)}},[e._v(e._s(e.getStatusText(r.campaignStatus)))])]}},{key:"company",fn:function(a){var r=a.row;return[t("Button",{directives:[{name:"has",rawName:"v-has",value:"ruleDescription",expression:"'ruleDescription'"}],attrs:{type:"primary",size:"small"},on:{click:function(t){return e.viewCompanyDetails(r)}}},[e._v("点击查看")])]}},{key:"rules",fn:function(a){var r=a.row;return[t("Button",{directives:[{name:"has",rawName:"v-has",value:"participateInCompany",expression:"'participateInCompany'"}],attrs:{type:"primary",size:"small"},on:{click:function(t){return e.viewRulesPopup(r)}}},[e._v("点击查看")])]}},{key:"action",fn:function(a){var r=a.row;return[t("div",{directives:[{name:"has",rawName:"v-has",value:"updateMarketingActivity",expression:"'updateMarketingActivity'"}]},["0"===r.campaignStatus||"1"===r.campaignStatus?t("Button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.editMarketingObj(r)}}},[e._v("编辑")]):e._e()],1)]}}])}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.total,"show-total":"","show-elevator":"","page-size":e.searchObj.pageSize,current:e.searchObj.pageNum},on:{"update:current":function(t){return e.$set(e.searchObj,"pageNum",t)},"on-change":e.loadByPage}}),t("CompanyModal",{attrs:{companyVisible:e.companyModalVisible,selectedRow:e.selectedRow,companyDetails:e.companyDetails},on:{companyClose:e.companyClose,"refresh-table":function(t){return e.loadByPage(1)}}}),t("RulesModal",{attrs:{rulesVisible:e.rulesModalVisible,selectedRow:e.selectedRow,ruleDetails:e.ruleDetails},on:{ruleClose:e.ruleClose}})],1)},n=[],o=a("5530"),i=a("c7eb"),s=a("1da1"),c=(a("d9e2"),a("14d9"),a("498a"),a("7bfc")),l=function(){var e=this,t=e._self._c;return t("Modal",{attrs:{value:e.rulesVisible,title:"详细规则",width:"800","mask-closable":!1},on:{"on-cancel":e.ruleClose},nativeOn:{keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault()}}},[e.loading?t("Form",{attrs:{"label-position":"right","label-width":100}},[t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"活动名称："}},[e._v("\n        "+e._s(e.selectedRow.campaignName)+"\n      ")])],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"适用合作模式："}},[e._v("\n        "+e._s("1"===e.selectedRow.cooperationMode?"代销":"A2Z")+"\n      ")])],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"活动开始时间："}},[e._v("\n        "+e._s(e.formatDate(e.selectedRow.startTime))+"\n      ")])],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"活动结束时间："}},[e._v("\n        "+e._s(e.formatDate(e.selectedRow.endTime))+"\n      ")])],1),"2"===e.selectedRow.cooperationMode?[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"环比开始时间："}},[e._v("\n          "+e._s(e.formatDate(e.selectedRow.sequentialStartTime))+"\n        ")])],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"环比结束时间："}},[e._v("\n          "+e._s(e.formatDate(e.selectedRow.sequentialEndTime))+"\n        ")])],1)]:e._e()],2),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"详细规则：","label-width":100}},[t("div",{staticClass:"detail-rule"},["1"===e.selectedRow.cooperationMode?[t("div",{staticClass:"rule-content"},[t("div",{staticClass:"rule-header"},[e._v("本次活动设定总返利金额为： "+e._s(e.ruleDetails.totalAmountRefund)+"港币")]),t("div",{staticClass:"rule-list"},[t("div",{staticStyle:{"font-weight":"bold",margin:"10px 0"}},[e._v("具体累计返还策略如下：")]),e._l(e.cumulativeRules,(function(a,r){return t("div",{key:a.id,staticClass:"rule-row"},[t("div",{staticClass:"rule-left"},[e._v("预存款: "+e._s(a.amountDeposited))]),t("div",{staticClass:"rule-right"},[e._v("返还比例: "+e._s(a.returnRatio)+"%")])])}))],2),e.immediateRules.length>0?t("div",{staticClass:"rule-list",staticStyle:{"margin-top":"20px"}},[t("div",{staticStyle:{"font-weight":"bold",margin:"10px 0"}},[e._v("具体立即返还策略如下：")]),t("div",{staticClass:"rule-row"},[t("div",{staticClass:"rule-left"},[e._v("返还金生效期: "+e._s(e.formatDate(e.immediateRules[0].effectiveTime)))])]),e._l(e.immediateRules,(function(a,r){return t("div",{key:a.id,staticClass:"rule-row"},[t("div",{staticClass:"rule-left"},[e._v("预存款: "+e._s(a.amountDeposited))]),t("div",{staticClass:"rule-right"},[e._v("返还比例: "+e._s(a.returnRatio)+"%")])])}))],2):e._e()])]:[t("div",{staticClass:"rule-content"},[t("div",{staticClass:"rule-list"},[t("div",[e._v("具体返还策略如下：")]),e._l(e.ruleDetails.campaignRuleList,(function(a,r){return t("div",{key:a.id,staticClass:"rule-row"},[t("div",{staticClass:"rule-left"},[e._v("环比增幅: "+e._s(a.sequentialGrowthRate)+"%")]),t("div",{staticClass:"rule-right"},[e._v("返还比例: "+e._s(a.returnRatio)+"%")])])}))],2)])]],2)])],1)],1)],1):t("div",{staticClass:"loading"},[t("Spin",{attrs:{size:"large",fix:""}})],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:function(t){return e.ruleClose()}}},[e._v("返回")])],1)],1)},u=[],d=(a("4de4"),a("d3b7"),a("90de")),m={props:{rulesVisible:{type:Boolean,default:!1},selectedRow:{type:Object,default:function(){return{}}},ruleDetails:{type:Object,default:function(){return{}}}},watch:{ruleDetails:function(e){console.log(e),this.loading=!0,e&&e.campaignRuleList&&(this.cumulativeRules=e.campaignRuleList.filter((function(e){return null==e.returnType||"1"==e.returnType})),this.immediateRules=e.campaignRuleList.filter((function(e){return"2"==e.returnType})))}},data:function(){return{loading:!1,cumulativeRules:[],immediateRules:[]}},methods:{formatDate:function(e){return Object(d["b"])(e)},ruleClose:function(){this.$emit("ruleClose")}}},p=m,h=(a("1ccf6"),a("2877")),g=Object(h["a"])(p,l,u,!1,null,"47becfd0",null),f=g.exports,b=function(){var e=this,t=e._self._c;return t("div",[t("Modal",{attrs:{value:e.companyVisible,title:"参与公司详情",width:"900","mask-closable":!1,"label-width":100},on:{"on-cancel":e.companyClose,input:function(t){return e.$emit("update:companyVisible",t)}},nativeOn:{keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault()}}},[t("Form",{attrs:{"label-position":"right","label-width":100}},[t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"活动名称:"}},[e._v("\n          "+e._s(e.selectedRow.campaignName)+"\n        ")])],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"适用合作模式:"}},[e._v("\n          "+e._s("1"===e.selectedRow.cooperationMode?"代销":"A2Z")+"\n        ")])],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"活动开始时间:"}},[e._v("\n          "+e._s(e.formatDateToClient(e.selectedRow.startTime))+"\n        ")])],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"活动结束时间:"}},[e._v("\n          "+e._s(e.formatDateToClient(e.selectedRow.endTime))+"\n        ")])],1),"2"===e.selectedRow.cooperationMode?[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"环比开始时间:"}},[e._v("\n            "+e._s(e.formatDateToClient(e.selectedRow.sequentialStartTime))+"\n          ")])],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"环比结束时间:"}},[e._v("\n            "+e._s(e.formatDateToClient(e.selectedRow.sequentialEndTime))+"\n          ")])],1)]:e._e(),"1"===e.selectedRow.cooperationMode?t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"总计返利:"}},[e._v("\n          "+e._s(e.selectedRow.expectAmountRefund)+" / "+e._s(e.selectedRow.totalAmountRefund)+"\n        ")])],1):e._e(),t("Col",{attrs:{span:"12"}},["2"===e.selectedRow.cooperationMode?t("FormItem",{attrs:{label:"总预计返利:"}},[e._v("\n          "+e._s(e.selectedRow.expectAmountRefund)+"\n        ")]):e._e()],1),"1"===e.selectedRow.campaignStatus&&"1"===e.selectedRow.cooperationMode?t("Col",{attrs:{span:"12"}},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"addRechargeItem",expression:"'addRechargeItem'"}],staticClass:"add-recharge-btn",attrs:{type:"primary",icon:"md-add"},on:{click:e.addRechargeRecord}},[e._v("\n          添加充值记录\n        ")])],1):e._e()],2),t("div",{staticClass:"table-container"},[t("Table",{attrs:{columns:e.getColumns,data:e.localCompanyList,ellipsis:!0}})],1),t("div",{staticClass:"page-info"},[t("Page",{attrs:{total:e.total,"show-total":"","show-elevator":"","page-size":e.pageSize,current:e.pageNum},on:{"on-change":e.handlePageChange}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.companyClose}},[e._v("返回")])],1)],1),t("Modal",{attrs:{title:"添加充值记录",width:"800","mask-closable":!1},on:{"on-cancel":e.handleRechargeCancel},model:{value:e.rechargeVisible,callback:function(t){e.rechargeVisible=t},expression:"rechargeVisible"}},[t("Form",{ref:"rechargeForm",attrs:{model:e.rechargeForm,"label-position":"right",rules:e.rechargeRules,"label-width":80}},[t("Row",{attrs:{gutter:24}},[t("Col",{attrs:{span:"11"}},[t("FormItem",{attrs:{label:"公司名称",prop:"companyId",required:""}},[t("Select",{attrs:{placeholder:"请选择公司",filterable:"",clearable:""},on:{"on-change":e.handleCompanyChange},model:{value:e.rechargeForm.companyId,callback:function(t){e.$set(e.rechargeForm,"companyId",t)},expression:"rechargeForm.companyId"}},e._l(e.companyOptions,(function(a){return t("Option",{key:a.corpId,staticClass:"company-option",attrs:{value:a.corpId}},[e._v(e._s(a.companyName))])})),1)],1)],1),t("Col",{attrs:{span:"11"}},[t("FormItem",{attrs:{label:"公司币种"}},[t("span",[e._v(e._s(e.companyCurrency||"--"))])])],1)],1),e._l(e.rechargeForm.rechargeList,(function(a,r){return t("div",{key:r,staticClass:"recharge-item"},[t("div",{staticClass:"recharge-content"},[t("Row",{attrs:{gutter:24,align:"middle"}},[t("Col",{attrs:{span:"11"}},[t("FormItem",{attrs:{label:"充值金额",prop:"rechargeList."+r+".amount",rules:{required:!0,trigger:"blur",validator:e.validateAmount},required:"","label-width":80}},[t("InputNumber",{staticStyle:{width:"100%"},attrs:{min:1,max:99999999,step:1,placeholder:"请输入充值金额"},model:{value:a.amount,callback:function(t){e.$set(a,"amount",t)},expression:"item.amount"}})],1)],1),t("Col",{attrs:{span:"11"}},[t("FormItem",{attrs:{label:"充值时间",prop:"rechargeList."+r+".rechargeTime",rules:{required:!0,type:"date",message:"请选择充值时间",trigger:"change",validator:function(t,a,r){if(a){var n=new Date(a),o=new Date(e.selectedRow.startTime),i=new Date(e.selectedRow.endTime);n<o||n>i?r(new e.Error("充值时间必须在活动时间范围内")):r()}else r(new e.Error("请选择充值时间"))}},required:"","label-width":80}},[t("DatePicker",{staticStyle:{width:"100%"},attrs:{type:"datetime",format:"yyyy-MM-dd HH:mm:ss",options:{disabledDate:function(t){var a=new Date(e.selectedRow.startTime),r=new Date(e.selectedRow.endTime);return t<a||t>r}},placeholder:"请选择充值时间"},model:{value:a.rechargeTime,callback:function(t){e.$set(a,"rechargeTime",t)},expression:"item.rechargeTime"}})],1)],1),e.rechargeForm.rechargeList.length>1?t("Col",{attrs:{span:"2"}},[t("Button",{staticClass:"delete-btn",attrs:{type:"error",size:"small"},on:{click:function(t){return e.removeRechargeItem(r)}}},[e._v("删除")])],1):e._e()],1)],1)])})),t("div",{staticClass:"add-button-wrapper"},[t("Button",{attrs:{type:"success",size:"small",icon:"md-add"},on:{click:e.addRechargeItem}},[e._v("添加充值记录")])],1)],2),t("div",{attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.handleRechargeCancel}},[e._v("取消")]),t("Button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.handleRechargeSubmit}},[e._v("确认")])],1)],1)],1)},v=[],y=(a("7db0"),a("d81d"),a("a434"),a("ac1f"),a("00b4"),a("159b"),{props:{companyVisible:{type:Boolean,default:!1},selectedRow:{type:Object,default:function(){return{}}},companyDetails:{type:Object,default:function(){return{}}}},data:function(){var e=function(e,t,a){if(!t&&0!==t)return a(new Error("请输入充值金额"));var r=parseFloat(t);return isNaN(r)?a(new Error("请输入正确的金额")):/^\d+$/.test(t)?r<=0?a(new Error("充值金额必须大于0")):void a():a(new Error("请输入正整数"))};return{rechargeVisible:!1,companyLoading:!1,submitLoading:!1,companyOptions:[],companyCurrency:"",currencyCode:"",rechargeForm:{companyId:"",rechargeList:[{amount:null,rechargeTime:new Date}]},rechargeRules:{companyId:[{required:!0,message:"请选择公司",trigger:"change"}]},validateAmount:e,pageNum:1,pageSize:5,total:0,localCompanyList:[],activityStartTime:new Date,activityEndTime:new Date}},computed:{getColumns:function(){return"1"===this.selectedRow.cooperationMode?[{title:"公司名称",key:"companyName",align:"center",tooltip:!0},{title:"已充值金额",key:"rechargeAmount",align:"center",tooltip:!0},{title:"预计返利",key:"expectedReturn",align:"center",tooltip:!0},{title:"实际返利",key:"actualReturn",align:"center",tooltip:!0},{title:"返利类型",key:"returnType",align:"center",tooltip:!0,render:function(e,t){var a=t.row.returnType;return e("span","2"==a?"单笔充值":"累计充值")}}]:[{title:"公司名称",key:"companyName",align:"center",tooltip:!0},{title:"上期金额",key:"preAmount",align:"center",tooltip:!0},{title:"本期金额",key:"currAmount",align:"center",tooltip:!0},{title:"预计返利",key:"expectedReturn",align:"center",tooltip:!0},{title:"实际返利",key:"actualReturn",align:"center",tooltip:!0}]}},watch:{companyVisible:{handler:function(e){e&&this.selectedRow.id&&(this.pageNum=1,this.getTableData())},immediate:!0}},methods:{formatDateToClient:function(e){return Object(d["b"])(e)},getTableData:function(){var e=this;return Object(s["a"])(Object(i["a"])().mark((function t(){var a,r,n;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,a={mcId:e.selectedRow.id,pageNum:e.pageNum,pageSize:e.pageSize},t.next=4,Object(c["e"])(a);case 4:r=t.sent,"0000"===r.code?(n=r.data,"1"===e.selectedRow.cooperationMode?n.forEach((function(e){e.rechargeAmount=null===e.rechargeAmount?0:e.rechargeAmount,e.expectedReturn=null===e.expectedReturn?0:e.expectedReturn})):n.forEach((function(e){e.preAmount=null===e.preAmount?0:e.preAmount,e.currAmount=null===e.currAmount?0:e.currAmount,e.expectedReturn=null===e.expectedReturn?0:e.expectedReturn})),e.localCompanyList=n||[],e.total=parseInt(r.count)||0):e.$Message.error(r.message||"获取数据失败"),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),console.error("获取表格数据失败:",t.t0),e.$Message.error("获取数据失败");case 12:case"end":return t.stop()}}),t,null,[[0,8]])})))()},handlePageChange:function(e){this.pageNum=e,this.getTableData()},companyClose:function(){this.$emit("companyClose",!1),this.pageNum=1},handleCompanyChange:function(e){if(e){var t=this.companyOptions.find((function(t){return t.corpId===e}));if(t)switch(this.currencyCode=t.currencyCode,t.currencyCode){case"156":this.companyCurrency="人民币";break;case"840":this.companyCurrency="美元";break;case"344":this.companyCurrency="港币";break;default:this.companyCurrency="--"}}else this.companyCurrency="",this.currencyCode=""},getCompanyList:function(){var e=this;return Object(s["a"])(Object(i["a"])().mark((function t(){var a,r;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,a={mcId:e.selectedRow.id},t.next=4,Object(c["f"])(a);case 4:r=t.sent,"0000"===r.code?e.companyOptions=r.data||[]:e.$Message.error(r.message||"获取公司列表失败"),t.next=12;break;case 8:t.prev=8,t.t0=t["catch"](0),console.error("获取公司列表失败:",t.t0),e.$Message.error("获取公司列表失败");case 12:case"end":return t.stop()}}),t,null,[[0,8]])})))()},addRechargeRecord:function(e){this.rechargeForm.companyId=e.corpId,this.rechargeVisible=!0,this.getCompanyList()},addRechargeItem:function(){this.rechargeForm.rechargeList.push({amount:null,rechargeTime:new Date})},removeRechargeItem:function(e){this.rechargeForm.rechargeList.splice(e,1)},handleRechargeCancel:function(){this.rechargeVisible=!1,this.resetRechargeForm()},handleRechargeSubmit:function(){var e=this;this.$refs.rechargeForm.validate(function(){var t=Object(s["a"])(Object(i["a"])().mark((function t(a){var r,n;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=16;break}return e.submitLoading=!0,t.prev=2,r={corpId:e.rechargeForm.companyId,cooperationMode:e.selectedRow.cooperationMode,currencyCode:e.currencyCode,campaignId:e.selectedRow.id,paymentHistorys:e.rechargeForm.rechargeList.map((function(e){return{rechargeAmount:e.amount,settlementTime:Object(d["d"])(e.rechargeTime,"Y-M-D h:m:s")}}))},t.next=6,Object(c["b"])(r);case 6:n=t.sent,"0000"===n.code?(e.$Message.success("添加充值记录成功"),e.rechargeVisible=!1,e.$emit("refresh-table"),e.resetRechargeForm()):e.$Message.error(n.message||"添加充值记录失败"),t.next=13;break;case 10:t.prev=10,t.t0=t["catch"](2),e.$Message.error("添加充值记录失败");case 13:return t.prev=13,e.submitLoading=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,null,[[2,10,13,16]])})));return function(e){return t.apply(this,arguments)}}())},resetRechargeForm:function(){this.$refs.rechargeForm&&this.$refs.rechargeForm.resetFields(),this.rechargeForm={companyId:"",rechargeList:[{amount:null,rechargeTime:new Date}]},this.companyCurrency="",this.currencyCode=""}}}),w=y,C=(a("4a57"),Object(h["a"])(w,b,v,!1,null,"06e24065",null)),R=C.exports,k={components:{RulesModal:f,CompanyModal:R},data:function(){return{searchObj:{campaignName:"",cooperationMode:"",startTime:"",endTime:"",corpId:"",pageSize:10,pageNum:1},customerList:[],customerLoading:!1,tableData:[],tableLoading:!1,total:0,columns:[{title:"活动名称",key:"campaignName",align:"center"},{title:"合作模式",key:"cooperationMode",align:"center",render:function(e,t){return e("span","1"===t.row.cooperationMode?"代销":"A2Z")}},{title:"活动开始时间",key:"startTime",align:"center",render:function(e,t){return e("span",Object(d["b"])(t.row.startTime))}},{title:"活动结束时间",key:"endTime",align:"center",render:function(e,t){return e("span",Object(d["b"])(t.row.endTime))}},{title:"活动状态",slot:"campaignStatus",align:"center"},{title:"规则描述",slot:"rules",align:"center"},{title:"参与公司",slot:"company",align:"center"},{title:"操作",slot:"action",align:"center"}],rulesModalVisible:!1,companyModalVisible:!1,editMarketingModalVisible:!1,selectedRow:{},involvedCompaniesColumnsDx:[{title:"公司名称",key:"companyName",align:"center"},{title:"已充值金额",key:"rechargeAmount",align:"center"},{title:"预计返利金额",key:"expectedRebate",align:"center"}],involvedCompaniesColumnsA2Z:[{title:"公司名称",key:"companyName",align:"center"},{title:"上期金额",key:"rechargeAmount",align:"center"},{title:"本期金额",key:"expectedRebate",align:"center"},{title:"预计返利",key:"expectedRebate",align:"center"}],involvedCompaniesTableData:[],involvedCompaniesPageSize:10,involvedCompaniesPage:1,involvedCompaniesTotal:0,involvedCompaniesLoading:!1,ruleDetails:{sequentialGrowth:0,sequentialGrowth2:0,sequentialGrowth3:0,returnRatio:0,returnRatio2:0},companyDetails:{totalExpectedRebate:0,corpList:[]},formRuleList:{endTime:[{validator:this.validateEndTime,trigger:"change"}]}}},methods:{getCorpList:function(){var e=this;this.customerLoading=!0,Object(c["f"])().then((function(t){e.customerList=t.data,e.customerLoading=!1})).catch((function(){e.customerLoading=!1}))},searchMarketing:function(){var e=this;this.$refs.searchForm.validate(function(){var t=Object(s["a"])(Object(i["a"])().mark((function t(a){var r,n,o,s,l,u,d,m;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:a&&(e.tableLoading=!0,r=e.searchObj,n=r.campaignName,o=r.cooperationMode,s=r.startTime,l=r.endTime,u=r.corpId,d=r.pageSize,m=r.pageNum,Object(c["c"])({campaignName:n.trim(),cooperationMode:o,startTime:e.formatDateToServer(s),endTime:e.formatDateToServer(l),corpId:u,pageSize:d,pageNum:m}).then((function(t){"0000"===t.code?(e.tableData=t.data,e.total=parseInt(t.count)):e.$Message.error(t.message||"获取数据失败"),e.tableLoading=!1})).catch((function(){e.$Message.error("获取数据失败"),e.tableLoading=!1})));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},newMarketing:function(){this.$router.push({name:"marketingActivityAdd",query:{type:"add"}})},viewRulesPopup:function(e){var t=this;this.rulesModalVisible=!0,this.ruleDetails={},this.selectedRow={},Object(c["h"])({mcId:e.id}).then((function(e){t.selectedRow=e.data,t.ruleDetails=e.data})).catch((function(){t.rulesModalVisible=!1}))},viewCompanyDetails:function(e){var t=this;Object(c["d"])({mcId:e.id}).then((function(a){"0000"===a.code?(t.selectedRow=Object(o["a"])(Object(o["a"])({},e),a.data),t.companyDetails=a.data,t.companyModalVisible=!0):t.$Message.error(a.message||"获取公司详情失败")})).catch((function(){t.$Message.error("获取公司详情失败")}))},ruleClose:function(){this.rulesModalVisible=!1},companyClose:function(){this.companyModalVisible=!1},editMarketingObj:function(e){this.selectedRow=e,this.$router.push({name:"marketingActivityUpdate",query:{mcId:e.id}})},saveEditMarketing:function(){this.editMarketingModalVisible=!1},getStatusText:function(e){switch(e){case"0":return"待开始";case"1":return"已开始";case"2":return"已结束";case"3":return"已作废";case"4":return"提前结束";case"5":return"重新结算中";default:return""}},getStatusColor:function(e){switch(e){case"0":return"gray";case"1":return"green";case"2":return"red";case"3":return"black";case"4":return"orange";case"5":return"blue";default:return""}},formatDateToClient:function(e){return Object(d["b"])(e)},formatDateToServer:function(e){return e?Object(d["c"])(e,this.searchObj.endTime===e):""},loadByPage:function(e){this.searchObj.pageNum=e,this.searchMarketing()},init:function(){this.loadByPage(1),this.getCorpList()},involvedCompaniesLoadByPage:function(e){this.involvedCompaniesPage=e},validateEndTime:function(e,t,a){t&&t<this.searchObj.startTime?a(new Error("结束时间不能小于开始时间")):a()}},mounted:function(){this.init()}},_=k,O=(a("366f"),Object(h["a"])(_,r,n,!1,null,"3a09ad3e",null));t["default"]=O.exports},f874:function(e,t,a){}}]);