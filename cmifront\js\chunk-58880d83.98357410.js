(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-58880d83"],{"129f":function(e,t,r){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},"1c31":function(e,t,r){"use strict";r.d(t,"t",(function(){return o})),r.d(t,"s",(function(){return i})),r.d(t,"a",(function(){return s})),r.d(t,"b",(function(){return u})),r.d(t,"c",(function(){return c})),r.d(t,"d",(function(){return l})),r.d(t,"e",(function(){return d})),r.d(t,"f",(function(){return p})),r.d(t,"g",(function(){return m})),r.d(t,"h",(function(){return f})),r.d(t,"i",(function(){return h})),r.d(t,"j",(function(){return g})),r.d(t,"k",(function(){return y})),r.d(t,"l",(function(){return b})),r.d(t,"x",(function(){return v})),r.d(t,"m",(function(){return x})),r.d(t,"n",(function(){return k})),r.d(t,"o",(function(){return w})),r.d(t,"p",(function(){return q})),r.d(t,"q",(function(){return D})),r.d(t,"v",(function(){return T})),r.d(t,"r",(function(){return P})),r.d(t,"w",(function(){return F})),r.d(t,"u",(function(){return L}));var n=r("66df"),a="/stat",o=function(e){return n["a"].request({url:"/cms/api/v1/packageCard/countReuseExport",data:e,responseType:"blob",method:"post"})},i=function(e){return n["a"].request({url:"/cms/api/v1/packageCard/countReuse",data:e,method:"post"})},s=function(e){return n["a"].request({url:a+"/activereport/detailDownload",params:e,responseType:"blob",method:"get"})},u=function(e){return n["a"].request({url:a+"/activereport/pageList",data:e,method:"post"})},c=function(e){return n["a"].request({url:a+"/cardReport",params:e,method:"get"})},l=function(e){return n["a"].request({url:a+"/cardReport/export",params:e,responseType:"blob",method:"get"})},d=function(e){return n["a"].request({url:a+"/offline/export",params:e,responseType:"blob",method:"get"})},p=function(e){return n["a"].request({url:a+"/offline/import",data:e,method:"post"})},m=function(e){return n["a"].request({url:a+"/offline/pageList",data:e,method:"post"})},f=function(e){return n["a"].request({url:a+"/operatorsettle/detailDownload",params:e,responseType:"blob",method:"get"})},h=function(e){return n["a"].request({url:a+"/operatorsettle/pageList",data:e,method:"post"})},g=function(e){return n["a"].request({url:a+"/postpaidsettle/detailDownload",params:e,responseType:"blob",method:"get"})},y=function(e){return n["a"].request({url:a+"/postpaidsettle/pageList",data:e,method:"post"})},b=function(e){return n["a"].request({url:a+"/rate",params:e,method:"get"})},v=function(e){return n["a"].request({url:a+"/rate",data:e,method:"post"})},x=function(e){return n["a"].request({url:a+"/rate/export",params:e,responseType:"blob",method:"get"})},k=function(e){return n["a"].request({url:a+"/report/package/analysis/export",params:e,responseType:"blob",method:"get"})},w=function(e){return n["a"].request({url:a+"/report/package/analysis/search",data:e,method:"post"})},q=function(e){return n["a"].request({url:a+"/terminalsettle/detailDownload",params:e,responseType:"blob",method:"get"})},D=function(e){return n["a"].request({url:a+"/terminalsettle/pageList",data:e,method:"post"})},T=function(e){return n["a"].request({url:"/charging/cost/supplierCostQuery",data:e,method:"post"})},P=function(e){return n["a"].request({url:"/charging/cost/supplierCostExport",data:e,responseType:"blob",method:"post"})},F=function(e){return n["a"].request({url:"/cms/esim/getEsimcardStats",params:e,method:"get"})},L=function(e){return n["a"].request({url:"/cms/esim/exportEsimcardStats",params:e,method:"get"})}},"841c":function(e,t,r){"use strict";var n=r("c65b"),a=r("d784"),o=r("825a"),i=r("7234"),s=r("1d80"),u=r("129f"),c=r("577e"),l=r("dc4a"),d=r("14c3");a("search",(function(e,t,r){return[function(t){var r=s(this),a=i(t)?void 0:l(t,e);return a?n(a,t,r):new RegExp(t)[e](c(r))},function(e){var n=o(this),a=c(e),i=r(t,n,a);if(i.done)return i.value;var s=n.lastIndex;u(s,0)||(n.lastIndex=0);var l=d(n,a);return u(n.lastIndex,s)||(n.lastIndex=s),null===l?-1:l.index}]}))},b35e:function(e,t,r){"use strict";r("d9e2");t["a"]={methods:{validateDate:function(e,t,r){var n=this.form.endDate||this.form.endTime,a=this.form.startDate||this.form.startTime;n&&a?"startDate"===e.field||"startTime"===e.field?this.$time(t,">",n)?r(new Error("开始时间不能大于结束时间")):r():this.$time(t,"<",n)?r(new Error("结束时间不能小于开始时间")):r():r()}}}},c4f1:function(e,t,r){"use strict";r.r(t);r("ac1f"),r("841c");var n=function(){var e=this,t=e._self._c;return t("Card",[t("div",{staticStyle:{display:"flex",width:"100%"}},[t("Form",{ref:"form",attrs:{"label-width":0,model:e.form,rules:e.rule,inline:""}},[t("FormItem",{attrs:{prop:"corpname"}},[t("Input",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{placeholder:"输入合作渠道",clearable:""},model:{value:e.form.corpname,callback:function(t){e.$set(e.form,"corpname",t)},expression:"form.corpname"}})],1),t("FormItem",{attrs:{prop:"type"}},[t("Select",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{clearable:!0,placeholder:"请选择渠道类型"},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},e._l(e.sellList,(function(r,n){return t("Option",{key:n,attrs:{value:r.value}},[e._v(e._s(r.label))])})),1)],1),t("FormItem",{attrs:{prop:"dimension"}},[t("Select",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{clearable:!0,placeholder:"请选择统计维度"},on:{"on-change":function(t){e.date="",e.resetField(["startDate","endDate"])}},model:{value:e.form.dimension,callback:function(t){e.$set(e.form,"dimension",t)},expression:"form.dimension"}},e._l(e.cycleList,(function(r,n){return t("Option",{key:n,attrs:{value:r.id}},[e._v(e._s(r.value))])})),1)],1),t("FormItem",{attrs:{prop:"endDate"}},["2"!=e.form.dimension?t("FormItem",{attrs:{prop:"startDate"}},[t("DatePicker",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{format:"yyyyMMdd",editable:!1,type:"daterange",placeholder:"选择时间段",clearable:""},on:{"on-change":e.checkDatePicker},model:{value:e.date,callback:function(t){e.date=t},expression:"date"}})],1):e._e(),"2"==e.form.dimension?t("FormItem",{attrs:{prop:"startDate"}},[t("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择开始月份",editable:!1},on:{"on-change":function(t){return e.checkDatePicker(t,1)}}})],1):e._e(),"2"==e.form.dimension?t("FormItem",{attrs:{prop:"endDate"}},[t("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择结束月份",editable:!1},on:{"on-change":function(t){return e.checkDatePicker(t,2)}}})],1):e._e()],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",size:"large"},on:{click:function(t){return e.search()}}},[e._v("搜索")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{"margin-left":"20px"},attrs:{type:"success",icon:"ios-cloud-download-outline",size:"large"},on:{click:function(t){return e.exportTable()}}},[e._v("导出")])],1)],1)],1),t("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:e.columns12,data:e.data,loading:e.loading}}),t("div",{staticStyle:{"margin-left":"38%","margin-top":"100px","margin-bottom":"160px"}},[t("Page",{attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1),e.data1.length?t("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:e.columns12,data:e.data1,loading:e.loading}}):e._e()],1)},a=[],o=r("5530"),i=(r("caad"),r("14d9"),r("d3b7"),r("2532"),r("3ca3"),r("159b"),r("ddb0"),r("2b3d"),r("bf19"),r("9861"),r("88a7"),r("271a"),r("5494"),r("1c31")),s=r("b35e"),u={mixins:[s["a"]],data:function(){return{date:"",loading:!1,form:{corpname:"",endDate:"",dimension:"",startDate:"",type:""},rule:{startDate:[{required:!0,message:"请选择时间"}],endDate:[{required:!0,message:"请选择时间"}],dimension:[{required:!0,message:"请选择维度"}]},total:0,currentPage:1,cycleList:[{id:1,value:"日"},{id:2,value:"月"}],typeList:[{value:"1",label:"普通卡（实体卡）"},{value:"2",label:"Esim卡"},{value:"3",label:"贴片卡"}],sellList:[{value:"7",label:"终端线上"},{value:"8",label:"终端线下"}],columns12:[{title:"合作渠道",key:"corpname",align:"center",render:function(e,t){return e("span",t.row.corpname||"合计")}},{title:"渠道类型",key:"type",align:"center",render:function(e,t){var r=t.row.type,n={3:"合作商",4:"后付费",7:"终端线上",8:"终端线下"};return e("span",n[r]||"-")}},{title:"套餐名称",key:"packagename",align:"center",render:function(e,t){return e("span",t.row.packagename||"-")}},{title:"货币",key:"currencycode",align:"center",render:function(e,t){var r=t.row,n="156"==r.currencycode?"人民币":"840"==r.currencycode?"美元":"344"==r.currencycode?"港币":"-";return e("label",n)}},{title:"时间",key:"statTime",align:"center",render:function(e,t){return e("span",t.row.statTime||"-")}},{title:"销量",key:"salesvolume",align:"center"},{title:"使用量",key:"usevolume",align:"center"},{title:"当前货币收入",key:"salesincome",align:"center",render:function(e,t){return e("span",t.row.salesincome||"-")}},{title:"港币收入",key:"hkdincome",align:"center"}],data:[],data1:[],rules:{}}},mounted:function(){},methods:{resetField:function(e){this.$refs["form"].fields.forEach((function(t){e.includes(t.prop)&&t.resetField()}))},checkDatePicker:function(e,t){Array.isArray(e)?(this.form.startDate=e[0],this.form.endDate=e[1]):1===t?this.form.startDate=e:this.form.endDate=e},goPageFirst:function(e){var t=this;0===e&&(this.currentPage=1);var r=this,n=this.currentPage,a=10;this.$refs["form"].validate((function(s){s?(t.loading=!0,Object(i["q"])(Object(o["a"])({pageNum:n,pageSize:a},t.form)).then((function(n){"0000"==n.code&&(r.loading=!1,t.page=e,t.total=n.data.total,t.data=n.data.record,t.data1=n.data.records1[0]?n.data.records1:[])})).catch((function(e){console.error(e)})).finally((function(){t.loading=!1}))):t.$Message.error("参数校验不通过")}))},goPage:function(e){this.goPageFirst(e)},search:function(){this.goPageFirst(0)},exportTable:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(i["p"])(Object(o["a"])({},e.form)).then((function(e){var t=e.data,r="终端结算报表.csv";if("download"in document.createElement("a")){var n=document.createElement("a"),a=URL.createObjectURL(t);n.download=r,n.href=a,n.click(),URL.revokeObjectURL(a)}else navigator.msSaveBlob(t,r)})).catch((function(){return e.downloading=!1}))}))},details:function(e){this.$router.push({path:"/channel/detailsList"})}}},c=u,l=r("2877"),d=Object(l["a"])(c,n,a,!1,null,null,null);t["default"]=d.exports}}]);