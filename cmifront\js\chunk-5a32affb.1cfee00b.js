(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5a32affb"],{"71eb":function(s,t,e){"use strict";e.r(t);var a=function(){var s=this,t=s._self._c;return t("div",{staticClass:"payment-page"},[t("div",{staticClass:"payment-status"},[t("div",{staticClass:"status-icon",class:{success:s.isSuccess,error:!s.isSuccess}},[t("span",[s._v("!")])]),t("div",{staticClass:"status-message"},[t("p",{staticClass:"success-details"},[s._v(s._s(s.$t("paymentResultpageTexts.successDetails")))])])]),t("div",{staticClass:"button-group"},[t("button",{staticClass:"btn btn-blue",on:{click:s.viewOrder}},[s._v(s._s(s.$t("paymentResultpageTexts.viewOrder")))]),t("button",{staticClass:"btn",on:{click:s.goToHome}},[s._v(s._s(s.$t("paymentResultpageTexts.goToHome")))])])])},n=[],c=(e("14d9"),{name:"PaymentPage",data:function(){return{isSuccess:!0}},methods:{viewOrder:function(){console.log("查看订单被点击"),this.$router.push({path:"/paymentOrder/management"})},goToHome:function(){this.$router.push({path:"/"})}},mounted:function(){this.isSuccess=!0}}),i=c,u=(e("d76d"),e("2877")),o=Object(u["a"])(i,a,n,!1,null,"7b203fa0",null);t["default"]=o.exports},"80a3":function(s,t,e){},d76d:function(s,t,e){"use strict";e("80a3")}}]);