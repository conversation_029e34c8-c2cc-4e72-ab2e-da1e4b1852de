(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6317f4f8"],{"00b4":function(t,e,a){"use strict";a("ac1f");var i=a("23e7"),n=a("c65b"),o=a("1626"),s=a("825a"),r=a("577e"),l=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),c=/./.test;i({target:"RegExp",proto:!0,forced:!l},{test:function(t){var e=s(this),a=r(t),i=e.exec;if(!o(i))return n(c,e,a);var l=n(i,e,a);return null!==l&&(s(l),!0)}})},"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},4235:function(t,e,a){},7538:function(t,e,a){"use strict";a.r(e);a("b0c0"),a("ac1f"),a("841c");var i=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v(t._s(t.$t("support.subChannelName")))]),e("Input",{staticStyle:{width:"300px"},attrs:{clearable:!0,placeholder:t.$t("support.input")},model:{value:t.subChannelName,callback:function(e){t.subChannelName=e},expression:"subChannelName"}})],1),e("div",{staticClass:"search_box"},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{"margin-right":"20px"},attrs:{disabled:"2"==t.cooperationMode||"3"==t.cooperationMode,type:"info",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v(t._s(t.$t("common.search")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{"margin-left":"10px"},attrs:{disabled:"2"==t.cooperationMode||"3"==t.cooperationMode,type:"primary",icon:"md-add"},on:{click:t.add}},[t._v(t._s(t.$t("support.create")))])],1)]),e("Table",{ref:"selection",staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},on:{"on-selection-change":t.handleRowChange,"on-select-cancel":t.cancelSigle,"on-select-all-cancel":t.cancelAll},scopedSlots:t._u([{key:"action",fn:function(a){var i=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"info",expression:"'info'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"info",ghost:""},on:{click:function(e){return t.showdetail(i)}}},[t._v(t._s(t.$t("stock.details")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"success",ghost:""},on:{click:function(e){return t.update(i)}}},[t._v(t._s(t.$t("support.edit2")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"warning",ghost:""},on:{click:function(e){return t.delItem(i)}}},[t._v(t._s(t.$t("address.Delete")))])]}}])}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:t.subChannelTitle,"footer-hide":!0,"mask-closable":!1,width:"780px"},on:{"on-cancel":t.cancelModal},model:{value:t.subChannelModal,callback:function(e){t.subChannelModal=e},expression:"subChannelModal"}},[e("div",{staticStyle:{padding:"0 10px"}},[e("Form",{ref:"editObj",staticStyle:{display:"flex","flex-wrap":"wrap"},attrs:{model:t.editObj,"label-width":110,rules:t.ruleEditValidate}},[e("FormItem",{attrs:{label:t.$t("support.subChannelName"),prop:"subChannelName"}},[e("Input",{staticStyle:{width:"250px"},attrs:{maxlength:"50",placeholder:t.$t("support.input"),clearable:""},model:{value:t.editObj.subChannelName,callback:function(e){t.$set(t.editObj,"subChannelName",e)},expression:"editObj.subChannelName"}})],1),e("FormItem",{attrs:{label:t.$t("support.contactEmail"),prop:"email"}},[e("Input",{staticStyle:{width:"250px"},attrs:{placeholder:t.$t("address.input_mailbox"),clearable:""},model:{value:t.editObj.email,callback:function(e){t.$set(t.editObj,"email",e)},expression:"editObj.email"}})],1),e("FormItem",{staticStyle:{width:"500px"},attrs:{label:t.$t("support.purchasePackage"),prop:"file",rules:"Update"==t.subChannelFlag?[{required:!1}]:t.ruleEditValidate.file}},[e("div",{staticStyle:{display:"flex"}},[e("Upload",{ref:"upload",attrs:{action:t.uploadUrl,"on-success":t.fileSuccess,"on-error":t.handleError,"before-upload":t.handleBeforeUpload,"on-progress":t.fileUploading},model:{value:t.editObj.file,callback:function(e){t.$set(t.editObj,"file",e)},expression:"editObj.file"}},[e("Button",{attrs:{icon:"ios-cloud-upload-outline"}},[t._v(t._s(t.$t("support.clickToUpload")))])],1),e("div",{staticStyle:{width:"500px","margin-left":"50px"}},[e("Button",{attrs:{type:"primary",icon:"ios-download"},on:{click:t.downloadFile}},[t._v(t._s(t.$t("support.downloadfile")))])],1)],1),t.file?e("ul",{staticClass:"ivu-upload-list"},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name)+"\n\t\t\t\t\t\t\t")],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e()]),e("FormItem",{attrs:{label:t.$t("support.packageProfitMargin"),prop:"profitMargin"}},[e("Input",{staticStyle:{width:"250px"},attrs:{type:"number",placeholder:t.$t("support.input"),clearable:""},model:{value:t.editObj.profitMargin,callback:function(e){t.$set(t.editObj,"profitMargin",e)},expression:"editObj.profitMargin"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("%")])])],1),e("FormItem",{attrs:{label:t.$t("support.fuelPackProfitMargin"),prop:"refuelProfitMargin"}},[e("Input",{staticStyle:{width:"250px"},attrs:{type:"number",placeholder:t.$t("support.input"),clearable:""},model:{value:t.editObj.refuelProfitMargin,callback:function(e){t.$set(t.editObj,"refuelProfitMargin",e)},expression:"editObj.refuelProfitMargin"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("%")])])],1),e("FormItem",{attrs:{label:t.$t("support.totalAmount"),prop:"totalDeposit"}},[e("Input",{staticStyle:{width:"250px"},attrs:{placeholder:t.$t("support.input"),clearable:""},model:{value:t.editObj.totalDeposit,callback:function(e){t.$set(t.editObj,"totalDeposit",e)},expression:"editObj.totalDeposit"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v(t._s(t.$t("yuan")))])])],1),e("FormItem",{attrs:{label:t.$t("support.accountPermissions"),prop:"accountPermissions"}},[e("Select",{staticStyle:{width:"250px"},attrs:{filterable:"",placeholder:t.$t("support.selectCharacter"),clearable:"",disabled:"Update"==t.subChannelFlag},model:{value:t.editObj.accountPermissions,callback:function(e){t.$set(t.editObj,"accountPermissions",e)},expression:"editObj.accountPermissions"}},t._l(t.accountPermissionsList,(function(a){return e("Option",{key:a.id,attrs:{value:a.id}},[t._v(t._s(a.roleName))])})),1)],1)],1),e("div",{staticStyle:{"text-align":"center"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"submit",expression:"'submit'"}],staticStyle:{"margin-right":"20px"},attrs:{type:"primary",loading:t.submitLoading},on:{click:function(e){return t.submit()}}},[t._v(t._s(t.$t("support.submit")))]),e("Button",{staticStyle:{"margin-left":"8px"},on:{click:t.cancelModal}},[t._v(t._s(t.$t("support.back")))])],1)],1)]),e("Modal",{attrs:{title:t.subChannelTitleInfo,"footer-hide":!0,"mask-closable":!1,width:"800px"},on:{"on-cancel":t.cancelModalInfo},model:{value:t.subChannelModalInfo,callback:function(e){t.subChannelModalInfo=e},expression:"subChannelModalInfo"}},[e("div",{staticStyle:{padding:"0 10px"}},[e("Form",{ref:"editObjInfo",staticStyle:{display:"flex","flex-wrap":"wrap"},attrs:{model:t.editObjInfo,"label-width":120}},[e("FormItem",{attrs:{label:t.$t("support.subChannelName"),prop:"subChannelName"}},[e("Input",{staticStyle:{width:"250px"},attrs:{maxlength:"50",readonly:"",placeholder:t.$t("support.input")},model:{value:t.editObjInfo.subChannelName,callback:function(e){t.$set(t.editObjInfo,"subChannelName",e)},expression:"editObjInfo.subChannelName"}})],1),e("FormItem",{attrs:{label:t.$t("support.contactEmail"),prop:"email"}},[e("Input",{staticStyle:{width:"250px"},attrs:{readonly:"",placeholder:t.$t("address.input_mailbox")},model:{value:t.editObjInfo.email,callback:function(e){t.$set(t.editObjInfo,"email",e)},expression:"editObjInfo.email"}})],1),e("FormItem",{staticStyle:{width:"370px",display:"felx","justify-content":"center"},attrs:{label:t.$t("support.purchasePackage")}},[e("Button",{attrs:{icon:"ios-albums",type:"info"},on:{click:t.showPackageModal}},[t._v(t._s(t.$t("flow.Clickview")))])],1),e("FormItem",{attrs:{label:t.$t("support.packageProfitMargin"),prop:"profitMargin"}},[e("Input",{staticStyle:{width:"250px"},attrs:{type:"number",readonly:"",placeholder:t.$t("support.input")},model:{value:t.editObjInfo.profitMargin,callback:function(e){t.$set(t.editObjInfo,"profitMargin",e)},expression:"editObjInfo.profitMargin"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("%")])])],1),e("FormItem",{attrs:{label:t.$t("support.fuelPackProfitMargin"),prop:"refuelProfitMargin"}},[e("Input",{staticStyle:{width:"250px"},attrs:{type:"number",readonly:"",placeholder:t.$t("support.input")},model:{value:t.editObjInfo.refuelProfitMargin,callback:function(e){t.$set(t.editObjInfo,"refuelProfitMargin",e)},expression:"editObjInfo.refuelProfitMargin"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("%")])])],1),e("FormItem",{attrs:{label:t.$t("support.totalAmount"),prop:"totalDeposit"}},[e("Input",{staticStyle:{width:"250px"},attrs:{readonly:"",placeholder:t.$t("support.input")},model:{value:t.editObjInfo.totalDeposit,callback:function(e){t.$set(t.editObjInfo,"totalDeposit",e)},expression:"editObjInfo.totalDeposit"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v(t._s(t.$t("yuan")))])])],1),e("FormItem",{attrs:{label:t.$t("support.accountPermissions"),prop:"accountPermissions"}},[e("Select",{staticStyle:{width:"250px"},attrs:{filterable:"",disabled:"",placeholder:t.$t("support.selectCharacter")},model:{value:t.editObjInfo.accountPermissions,callback:function(e){t.$set(t.editObjInfo,"accountPermissions",e)},expression:"editObjInfo.accountPermissions"}},t._l(t.accountPermissionsList,(function(a){return e("Option",{key:a.id,attrs:{value:a.id}},[t._v(t._s(a.roleName))])})),1)],1),e("FormItem",{attrs:{label:t.$t("support.channelAppSecret"),prop:"appSecret"}},[e("Input",{staticStyle:{width:"450px"},attrs:{readonly:""},model:{value:t.editObjInfo.appSecret,callback:function(e){t.$set(t.editObjInfo,"appSecret",e)},expression:"editObjInfo.appSecret"}})],1),e("FormItem",{attrs:{label:t.$t("support.channelAppKey"),prop:"appKey"}},[e("Input",{staticStyle:{width:"450px"},attrs:{readonly:""},model:{value:t.editObjInfo.appKey,callback:function(e){t.$set(t.editObjInfo,"appKey",e)},expression:"editObjInfo.appKey"}})],1)],1),e("div",{staticStyle:{"text-align":"center"}},[e("Button",{on:{click:t.cancelModalInfo}},[t._v(t._s(t.$t("support.back")))])],1)],1)]),e("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:t.SubmodelColumns,data:t.SubmodelData}}),e("Modal",{attrs:{title:t.$t("support.purchasePackage"),"mask-closable":!1,width:"600px"},on:{"on-cancel":t.close},model:{value:t.purchasePackageModal,callback:function(e){t.purchasePackageModal=e},expression:"purchasePackageModal"}},[t.purchasePackageModal?e("purchase-packge",{attrs:{CCorpId:t.CCorpId,local:t.purchasePackageLocal}}):t._e(),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.close}},[t._v(t._s(t.$t("support.back")))])],1)],1)],1)},n=[],o=(a("d9e2"),a("caad"),a("d81d"),a("14d9"),a("a434"),a("d3b7"),a("00b4"),a("2532"),a("159b"),function(){var t=this,e=t._self._c;return e("div",{staticStyle:{padding:"0 16px"}},[e("div",[e("Table",{attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.loading,"max-height":"600"}})],1),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"page-size":t.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.loadRecords}})],1)])}),s=[],r=a("6dfa"),l={props:{CCorpId:{type:String,default:""}},data:function(){var t=this;return{localName:"",currentPage:1,loading:!1,total:0,pageSize:10,corpId:"",cooperationMode:"",columns:[{title:this.$t("deposit.mealname"),key:"packageName",align:"center",minWidth:100,render:function(e,a){var i=a.row,n="zh-CN"===t.$i18n.locale?i.packageName:"en-US"===t.$i18n.locale?i.packageNameEn:"";return e("label",n)}},{title:this.$t("support.pakageId"),key:"packageId",align:"center",minWidth:100},{title:this.$t("fuelPack.price"),key:"packagePrice",align:"center",minWidth:100,render:function(t,e){var a=e.row,i=a.packagePrice/100;return t("label",i)}}],tableData:[]}},methods:{loadRecords:function(t){var e=this;this.loading=!0;var a=t,i=10,n=this.cooperationMode,o=this.CCorpId;Object(r["x"])({corpId:o,pageNumber:a,pageSize:i,cooperationMode:n}).then((function(a){"0000"==a.code&&(e.searchloading=!1,e.page=t,e.currentPage=t,e.total=a.data.total,e.tableData=a.data.record)})).catch((function(t){console.error(t)})).finally((function(){e.loading=!1}))}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),this.loadRecords(1)}},c=l,d=a("2877"),u=Object(d["a"])(c,o,s,!1,null,"85a28a38",null),p=u.exports,h=a("66df"),f="/cms",m=function(t){return h["a"].request({url:f+"/subChannel/queryPage",data:t,method:"post"})},b=function(t){return h["a"].request({url:f+"/subChannel/del/".concat(t),method:"delete"})},g=function(t){return h["a"].request({url:f+"/subChannel/add",data:t,method:"post",contentType:"multipart/form-data"})},v=function(t){return h["a"].request({url:f+"/subChannel/edit",data:t,method:"post",contentType:"multipart/form-data"})},$=a("8963"),I={components:{purchasePackge:p},data:function(){var t=this,e=function(e,a,i){a&&-1!=a.indexOf("@")?i():i(new Error(t.$t("address.emailaddress")))},a=function(e,a,i){t.file?i():i(new Error(t.$t("support.pleaseUploadFile")))};return{cooperationMode:"",total:0,currentPage:1,page:0,subChannelName:"",loading:!1,searchloading:!1,submitLoading:!1,selection:[],selectionIds:[],iccidList:[],accountPermissionsList:[],uploadList:[],data:[],CCorpId:"",columns:[{type:"selection",width:60,align:"center"},{title:this.$t("support.subChannelName"),key:"subChannelName",minWidth:120,align:"center"},{title:this.$t("order.addtime"),key:"createTime",minWidth:120,align:"center"},{title:this.$t("support.totalAmount"),key:"totalDeposit",minWidth:120,align:"center",render:function(t,e){var a=e.row,i=a.totalDeposit/100;return t("label",i)}},{title:this.$t("deposit.deposit_money"),key:"deposit",minWidth:120,align:"center",render:function(t,e){var a=e.row,i=a.deposit/100;return t("label",i)}},{title:this.$t("usedLimit"),key:"usedAmount",minWidth:120,align:"center",render:function(t,e){var a=e.row,i=(a.totalDeposit-a.deposit)/100;return t("label",i)}},{title:this.$t("common.manageOperate"),slot:"action",minWidth:200,align:"center",fixed:"right"}],subChannelTitle:"",subChannelTitleInfo:"",subChannelFlag:"",subChannelModal:!1,subChannelModalInfo:!1,purchasePackageModal:!1,purchasePackageLocal:"",editObj:{subChannelName:"",file:"",email:"",profitMargin:"",refuelProfitMargin:"",totalDeposit:"",accountPermissions:""},editObjInfo:{subChannelName:"",email:"",profitMargin:"",refuelProfitMargin:"",totalDeposit:"",accountPermissions:""},uploadUrl:"",file:null,ruleEditValidate:{subChannelName:[{required:!0,type:"string",message:this.$t("support.subChannelEmpty")},{pattern:/^[^\s]+(\s+[^\s]+)*$/,trigger:"blur",message:this.$t("flow.kongge")},{validator:function(e,a,i){a&&a.includes("&")?i(new Error(t.$t("support.subChannelAmpersand"))):i()},trigger:"blur"}],email:[{required:!0,message:this.$t("support.contactEmailEmpty")},{validator:e,trigger:"blur"},{required:!0,type:"email",trigger:"blur",message:this.$t("support.EmailFormatError")}],file:[{required:!0,validator:a,trigger:"change"}],profitMargin:[{pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message:this.$t("flow.negative")}],refuelProfitMargin:[{required:!0,message:this.$t("support.fuelPackProfitMarginEmpty")},{pattern:/^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,message:this.$t("flow.negative")}],totalDeposit:[{required:!0,message:this.$t("support.totalAmountEmpty")},{pattern:/^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/,message:this.$t("flow.Positivenumber")}],accountPermissions:[{required:!0,message:this.$t("support.accountPermissionsEmpty")}]},SubmodelColumns:[{title:"Package ID",key:"Package ID"},{title:"Price",key:"Price"}],SubmodelData:[{"Package ID":"********",Price:""}]}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),this.corpId=sessionStorage.getItem("corpId"),"1"==this.cooperationMode&&(this.goPageFirst(1),this.getAccountPermissionsList())},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var a=this;m({parentCorpId:this.corpId,pageSize:10,pageNum:t,subChannelName:this.subChannelName?this.subChannelName:""}).then((function(i){if("0000"==i.code)if(a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,i.data){var n=i.data,o=[];n.map((function(t,e){o.push(t)})),e.iccidList.forEach((function(t){o.forEach((function(a){a.iccid==t.iccid&&e.$set(a,"_checked",!0)}))})),e.data=o,e.total=i.count}else e.data=[]})).catch((function(t){console.error(t)})).finally((function(){a.loading=!1,e.searchloading=!1}))},search:function(){this.searchloading=!0,this.goPageFirst(1)},goPage:function(t){this.goPageFirst(t)},handleRowChange:function(t){var e=this;this.selection=t,t.map((function(t,a){var i=!0;e.iccidList.map((function(e,a){t.iccid===e.iccid&&(i=!1)})),i&&e.iccidList.push(t)}))},cancelSigle:function(t,e){var a=this;this.iccidList.forEach((function(t,i){t.iccid===e.iccid&&a.iccidList.splice(i,1)}))},cancelAll:function(t,e){this.iccidList=[]},showdetail:function(t){this.subChannelFlag="Info",this.subChannelTitleInfo=this.$t("support.subChannelDetails"),this.editObjInfo=Object.assign({},t),this.editObjInfo.totalDeposit=this.editObjInfo.totalDeposit/100,this.CCorpId=t.ccorpId,this.subChannelModalInfo=!0},update:function(t){this.subChannelFlag="Update",this.subChannelTitle=this.$t("support.editSubChannel"),this.editObj=Object.assign({},t),this.editObj.totalDeposit=this.editObj.totalDeposit/100,this.CCorpId=t.ccorpId,this.subChannelModal=!0},add:function(){this.subChannelFlag="Add",this.subChannelTitle=this.$t("support.addSubChannel"),this.subChannelModal=!0},delItem:function(t){var e=this,a=t.ccorpId;this.$Modal.confirm({title:this.$t("flow.Confirmdelete"),onOk:function(){b(a).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:e.$t("address.Operationreminder"),desc:e.$t("common.Successful")}),e.goPageFirst(1)})).catch((function(t){}))}})},cancelModal:function(){this.file="",this.$refs.editObj.resetFields(),this.subChannelModal=!1},cancelModalInfo:function(){this.file="",this.subChannelModalInfo=!1},fileSuccess:function(t,e,a){this.message=this.$t("support.downTemplateFilelAndUpload")},handleError:function(t,e){var a=this;setTimeout((function(){a.uploading=!1,a.$Notice.warning({title:this.$t("common.Error"),desc:this.$t("support.uploadFailed")})}),3e3)},handleBeforeUpload:function(t,e){return/^.+(\.csv)$/.test(t.name)?(this.file=t,this.uploadList=e):this.$Notice.warning({title:this.$t("buymeal.fileformat"),desc:this.$t("support.files")+t.name+this.$t("buymeal.incorrect")}),!1},fileUploading:function(t,e,a){this.message=this.$t("support.fileUploadedAndProgressDisappears")},removeFile:function(){this.file=""},downloadFile:function(){this.$refs.modelTable.exportCsv({filename:"Sub-channel available package list",columns:this.SubmodelColumns,data:this.SubmodelData})},submit:function(){var t=this;this.$refs["editObj"].validate((function(e){if(e){var a;t.submitLoading=!0;var i=new FormData;i.append("cCorpId",t.CCorpId),i.append("pCorpId",t.corpId),i.append("subChannelName",t.editObj.subChannelName),i.append("email",t.editObj.email),(t.editObj.profitMargin||0===t.editObj.profitMargin)&&i.append("profitMargin",t.editObj.profitMargin),i.append("refuelProfitMargin",t.editObj.refuelProfitMargin),i.append("deposit",t.editObj.totalDeposit),i.append("accountPermissions",t.editObj.accountPermissions),"Add"===t.subChannelFlag?(i.append("file",t.file),a=g):(t.file&&i.append("file",t.file),a=v),a(i).then((function(e){"0000"===e.code&&(t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.submitLoading=!1,t.file=null,t.subChannelModal=!1,t.uploadList=[],t.goPageFirst(1),t.$refs["editObj"].resetFields())})).catch((function(e){console.error(e),t.submitLoading=!1})).finally((function(){t.submitLoading=!1}))}}))},showPackageModal:function(){this.purchasePackageModal=!0},close:function(){this.purchasePackageModal=!1},getAccountPermissionsList:function(){var t=this;Object($["c"])({corpId:this.corpId?this.corpId:void 0}).then((function(e){if("0000"!==e.code)throw e;t.accountPermissionsList=e.data})).catch((function(t){console.log(t)})).finally((function(){}))}}},y=I,C=(a("b8cc"),Object(d["a"])(y,i,n,!1,null,"011ea493",null));e["default"]=C.exports},"841c":function(t,e,a){"use strict";var i=a("c65b"),n=a("d784"),o=a("825a"),s=a("7234"),r=a("1d80"),l=a("129f"),c=a("577e"),d=a("dc4a"),u=a("14c3");n("search",(function(t,e,a){return[function(e){var a=r(this),n=s(e)?void 0:d(e,t);return n?i(n,e,a):new RegExp(e)[t](c(a))},function(t){var i=o(this),n=c(t),s=a(e,i,n);if(s.done)return s.value;var r=i.lastIndex;l(r,0)||(i.lastIndex=0);var d=u(i,n);return l(i.lastIndex,r)||(i.lastIndex=r),null===d?-1:d.index}]}))},8963:function(t,e,a){"use strict";a.d(e,"b",(function(){return o})),a.d(e,"d",(function(){return s})),a.d(e,"c",(function(){return r})),a.d(e,"e",(function(){return l})),a.d(e,"a",(function(){return c}));var i=a("66df"),n="/sys/api/v1",o=function(t){return i["a"].request({url:n+"/user/pagePrivilegesByRole/tree",params:t,method:"get"})},s=function(t){return i["a"].request({url:n+"/user/pagePrivilegesByUser/tree",params:t,method:"get"})},r=function(t){return i["a"].request({url:n+"/role/roles",data:t,method:"post"})},l=function(t){return i["a"].request({url:n+"/role/privileges/set",data:t,method:"post"})},c=function(t){return i["a"].request({url:n+"/role/add",params:t,method:"post"})}},b8cc:function(t,e,a){"use strict";a("4235")}}]);