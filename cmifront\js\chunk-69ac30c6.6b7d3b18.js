(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-69ac30c6"],{"18fa":function(t,e,a){},"67f3":function(t,e,a){"use strict";a("18fa")},cad9:function(t,e,a){"use strict";a.r(e);a("a15b");var r=function(){var t=this,e=t._self._c;return e("Card",{staticStyle:{width:"100%",padding:"16px"}},[e("Form",{ref:"searchForm",attrs:{model:t.filters,"label-width":100}},[e("Row",{attrs:{gutter:16}},[e("Col",{attrs:{span:"6"}},[e("FormItem",{attrs:{label:"规则名称",prop:"ruleName"}},[e("Input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入规则名称",clearable:""},model:{value:t.filters.ruleName,callback:function(e){t.$set(t.filters,"ruleName",e)},expression:"filters.ruleName"}})],1)],1),e("Col",{attrs:{span:"6"}},[e("FormItem",{attrs:{label:"故障资源供应商",prop:"faultSupplier"}},[e("Select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:t.filters.faultSupplier,callback:function(e){t.$set(t.filters,"faultSupplier",e)},expression:"filters.faultSupplier"}},t._l(t.supplierList,(function(a){return e("Option",{key:a.supplierId,attrs:{value:a.supplierId}},[t._v(t._s(a.supplierName))])})),1)],1)],1),e("Col",{attrs:{span:"6"}},[e("FormItem",{attrs:{label:"目标国家",prop:"targetCountries"}},[e("Select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"",placeholder:"请选择",clearable:""},model:{value:t.filters.targetCountries,callback:function(e){t.$set(t.filters,"targetCountries",e)},expression:"filters.targetCountries"}},t._l(t.countryList,(function(a){return e("Option",{key:a.mcc,attrs:{value:a.mcc}},[t._v(t._s(a.countryEn))])})),1)],1)],1)],1),e("Row",{attrs:{gutter:16}},[e("Col",{attrs:{span:"6"}},[e("FormItem",{attrs:{label:"备用资源供应商",prop:"backupSupplier"}},[e("Select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:"",filterable:""},model:{value:t.filters.backupSupplier,callback:function(e){t.$set(t.filters,"backupSupplier",e)},expression:"filters.backupSupplier"}},t._l(t.supplierList,(function(a){return e("Option",{key:a.supplierId,attrs:{value:a.supplierId}},[t._v(t._s(a.supplierName))])})),1)],1)],1),e("Col",{attrs:{span:"6"}},[e("FormItem",{attrs:{label:"备用卡池",prop:"backupPool"}},[e("Input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入备用卡池名称",clearable:""},model:{value:t.filters.backupPool,callback:function(e){t.$set(t.filters,"backupPool",e)},expression:"filters.backupPool"}})],1)],1),e("Col",{attrs:{span:"6"}},[e("FormItem",{attrs:{label:"故障状态",prop:"status"}},[e("Select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",clearable:""},model:{value:t.filters.status,callback:function(e){t.$set(t.filters,"status",e)},expression:"filters.status"}},[e("Option",{attrs:{value:"0"}},[t._v("故障中")]),e("Option",{attrs:{value:"1"}},[t._v("已恢复")])],1)],1)],1),e("Col",{attrs:{span:"6"}},[e("FormItem",{attrs:{"label-width":10}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"ios-search"},on:{click:t.handleSearch}},[t._v("查询")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{"margin-left":"8px"},attrs:{type:"info",icon:"ios-add"},on:{click:t.handleNew}},[t._v("新建")])],1)],1)],1)],1),e("Table",{staticStyle:{"margin-top":"16px"},attrs:{columns:t.columns,data:t.tableData,loading:t.loading,ellipsis:!0,tooltip:!0},scopedSlots:t._u([{key:"backupSuppliers",fn:function(a){var r=a.row;return t._l(r.standbySupplierNames,(function(a,r){return e("div",{key:"sup_"+r,staticStyle:{"line-height":"1.8","min-height":"24px"}},[e("span",[t._v(t._s(a))])])}))}},{key:"backupPools",fn:function(a){var r=a.row;return t._l(r.cardPools,(function(a,n){return e("div",{key:"pool_"+n,staticStyle:{"line-height":"1.8","min-height":"24px"}},[e("a",{on:{click:function(e){return t.showPoolDetails(a)}}},[t._v("\n          "+t._s(a.poolName)),void 0!==a.rate&&r.cardPools.length>1?e("span",[t._v("（"+t._s(a.rate)+"%）")]):t._e()])])}))}},{key:"status",fn:function(a){var r=a.row;return[e("span",[t._v(t._s("0"===r.faultStatus?"故障中":"已恢复"))])]}},{key:"action",fn:function(a){var r=a.row;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"restore",expression:"'restore'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small",disabled:"0"!==r.faultStatus},on:{click:function(e){return t.handleRestore(r)}}},[t._v("恢复")])]}}])}),e("Page",{staticStyle:{"margin-top":"16px","text-align":"right"},attrs:{total:t.pagination.total,"show-total":"","show-sizer":"","page-size":t.pagination.pageSize,current:t.pagination.currentPage},on:{"update:current":function(e){return t.$set(t.pagination,"currentPage",e)},"on-change":t.handleCurrentChange,"on-page-size-change":t.handleSizeChange}}),e("Modal",{attrs:{title:"卡池详情","footer-hide":!0,"mask-closable":!1,width:"900px"},model:{value:t.poolDetailModalVisible,callback:function(e){t.poolDetailModalVisible=e},expression:"poolDetailModalVisible"}},[t.poolDetailLoading?e("Spin",{attrs:{size:"large",fix:""}}):t._e(),!t.poolDetailLoading&&t.currentPoolDetails?e("div",{staticClass:"modal-content"},[e("div",{staticClass:"box"},[e("span",{},[t._v("卡池类型：  "+t._s("1"==t.currentPoolDetails.usageType?"全球卡普通卡池":"2"==t.currentPoolDetails.usageType?"终端线下卡池":"3"==t.currentPoolDetails.usageType?"终端线上卡池":""))])]),e("div",{staticClass:"box"},[e("span",{},[t._v("卡池名称：  "+t._s(t.currentPoolDetails.poolName))])]),e("div",{staticClass:"box"},[e("span",{},[t._v("支持国家/地区：  "+t._s(t.currentPoolDetails.mccsCn?t.currentPoolDetails.mccsCn.join(","):""))])]),e("div",{staticClass:"box"},[e("span",{},[t._v("供应商：  "+t._s(t.currentPoolDetails.supplierName))])]),e("div",{staticClass:"box"},[e("span",{},[t._v("TPLID：  "+t._s(t.currentPoolDetails.tplId))])]),"2"!=t.currentPoolDetails.usageType?e("div",{staticClass:"box"},[e("span",{},[t._v("是否动态签约：  "+t._s("1"==t.currentPoolDetails.isSignUpcc?"是":"否"))])]):t._e(),"1"!=t.currentPoolDetails.usageType?e("div",{staticClass:"box"},[e("span",{},[t._v("是否动态开户：  "+t._s("1"==t.currentPoolDetails.isOpenAccount?"是":"否"))])]):t._e(),"1"==t.currentPoolDetails.usageType?e("div",{staticClass:"box"},[e("span",{},[t._v("支持HIMSI-4G上网：  "+t._s("1"==t.currentPoolDetails.isSupportHimsi?"是":"否"))])]):t._e(),"2"==t.currentPoolDetails.usageType?e("div",{staticClass:"box"},[e("span",{},[t._v("厂商：  "+t._s(t.detilCorpname))])]):t._e(),"2"==t.currentPoolDetails.usageType?e("div",{staticClass:"box"},[e("span",{},[t._v("套餐名称：  "+t._s(t.currentPoolDetails.packageName||"暂无套餐名称"))])]):t._e(),"2"==t.currentPoolDetails.usageType?e("div",{staticClass:"box"},[e("span",{},[t._v("套餐计算周期类型：  "+t._s("1"==t.currentPoolDetails.periodUnit?"24小时":"2"==t.currentPoolDetails.periodUnit?"自然日":"3"==t.currentPoolDetails.periodUnit?"自然月":"4"==t.currentPoolDetails.periodUnit?"自然年":""))])]):t._e(),"2"==t.currentPoolDetails.usageType?e("div",{staticClass:"box"},[e("span",{},[t._v("持续周期数：  "+t._s(t.currentPoolDetails.keepPeriod))])]):t._e(),"2"==t.currentPoolDetails.usageType?e("div",{staticClass:"box"},[e("span",{},[t._v("到期后是否重置：  "+t._s("1"==t.currentPoolDetails.isExpireReset?"是":"否"))])]):t._e(),"1"!=t.currentPoolDetails.isSupportHimsi?e("div",{staticClass:"box"},[e("span",{},[t._v("VIMSI冻结周期：  "+t._s(t.currentPoolDetails.vimsiFreezeDay))])]):t._e(),"1"!=t.currentPoolDetails.isSupportHimsi?e("div",{staticClass:"box"},[e("span",{},[t._v("告警阈值：  "+t._s(t.currentPoolDetails.alarmThreshold))])]):t._e(),e("div",{staticStyle:{color:"#878787","line-height":"30px","background-color":"#f7f7f7"}},[e("Collapse",{on:{"on-change":t.handleCollapseChange},model:{value:t.activeCollapsePanel,callback:function(e){t.activeCollapsePanel=e},expression:"activeCollapsePanel"}},[e("Panel",{attrs:{name:"1"}},[t._v("\n            已绑定套餐\n            "),e("div",{style:{height:t.currentPoolDetails.packageList&&t.currentPoolDetails.packageList.length>5?"150px":"100%",overflowY:t.currentPoolDetails.packageList&&t.currentPoolDetails.packageList.length>5?"auto":"visible"},attrs:{slot:"content"},slot:"content"},[t.currentPoolDetails.packageList&&0!==t.currentPoolDetails.packageList.length?t._l(t.currentPoolDetails.packageList,(function(a,r){return e("div",{key:r},[e("Row",[e("Col",{attrs:{span:"10"}},[e("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap","text-align":"left"}},[t._v("\n                    套餐ID：  "+t._s(a.packageId)+"\n                  ")])]),e("Col",{attrs:{span:"8"}},[e("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap","text-align":"left"}},[t._v("\n                    套餐名称：  "+t._s(a.nameCn||"暂无名称")+"\n                  ")])]),e("Col",{attrs:{span:"6"}},[e("div",{staticStyle:{overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap","text-align":"left"}},[t._v("\n                    绑定时间：  "+t._s(t._f("formatDateTime")(a.createTime))+"\n                  ")])])],1)],1)})):e("div",[t.currentPoolDetails.packageList?e("div",{staticStyle:{display:"flex","justify-content":"center"}},[e("div",{staticStyle:{color:"#999"}},[t._v("暂未绑定套餐")])]):e("div",[e("div",{staticStyle:{display:"flex","justify-content":"center"}},[e("Icon",{staticStyle:{"margin-top":"100px"},attrs:{type:"ios-loading",size:"large"}})],1)])])],2)])],1)],1)]):t._e(),t.poolDetailLoading||t.currentPoolDetails?t._e():e("div",{staticStyle:{"text-align":"center",padding:"20px"}},[t._v("\n      无法加载卡池详情\n    ")])],1)],1)},n=[],o=(a("caad"),a("14d9"),a("d3b7"),a("2532"),a("159b"),a("da8c")),i=a("f7fa"),l={name:"FaultHandling",data:function(){return{filters:{ruleName:"",faultSupplier:"",targetCountries:[],backupSupplier:"",backupPool:"",status:""},loading:!1,tableData:[],supplierList:[],countryList:[],pagination:{currentPage:1,pageSize:10,total:0},columns:[{title:"规则名称",key:"ruleName",minWidth:200,ellipsis:!0,tooltip:!0,align:"center"},{title:"故障资源供应商",key:"faultSupplierName",minWidth:100,ellipsis:!0,tooltip:!0,align:"center"},{title:"目标国家",key:"targetCountries",width:200,ellipsis:!0,tooltip:!0,align:"center"},{title:"备用资源供应商",slot:"backupSuppliers",minWidth:100,ellipsis:!0,tooltip:!0,align:"center"},{title:"备用卡池",slot:"backupPools",minWidth:250,ellipsis:!0,tooltip:!0,align:"center"},{title:"故障状态",slot:"status",width:100,align:"center"},{title:"故障开始时间",key:"faultStartTime",width:160,align:"center"},{title:"故障恢复时间",key:"faultRestoreTime",width:160,align:"center"},{title:"操作",slot:"action",width:150,align:"center"}],poolDetailModalVisible:!1,currentPoolDetails:null,poolDetailLoading:!1,activeCollapsePanel:"",localLanguage:this.$i18n.locale,detilCorpname:"",corpIdDetails:[]}},methods:{handleSearch:function(){this.pagination.currentPage=1,this.fetchData()},handleReset:function(){this.filters={ruleName:"",faultSupplier:"",targetCountries:[],backupSupplier:"",backupPool:"",status:""},this.handleSearch()},handleNew:function(){this.$router.push({name:"fault_add"})},handleRestore:function(t){var e=this;console.log("恢复:",t),this.$Modal.confirm({title:"确认恢复",content:"<p>确认恢复该故障记录?</p>",onOk:function(){Object(o["k"])(t.id).then((function(t){"0000"===t.code?(e.$Message.success("操作成功!"),e.fetchData()):e.$Message.error("操作失败: "+(t.msg||"未知错误"))})).catch((function(t){console.error("恢复故障失败:",t),e.$Message.error("操作失败")}))},onCancel:function(){e.$Message.info("取消")}})},handleDelete:function(t){var e=this;console.log("删除:",t),this.$Modal.confirm({title:"删除",content:"<p>此操作将永久删除该记录, 是否继续?</p>",okText:"确定",cancelText:"取消",onOk:function(){console.log("调用删除API, ID:",t.id),e.$Message.success("操作成功!"),e.fetchData()},onCancel:function(){e.$Message.info("取消")}})},showPoolDetails:function(t){var e=this;console.log("请求显示卡池详情:",t),this.currentPoolDetails=null,this.detilCorpname="",this.poolDetailModalVisible=!0,this.poolDetailLoading=!0,this.activeCollapsePanel="";var a=this,r={poolId:t.poolId,page:1,pageSize:9999};Object(o["a"])(r).then((function(t){"0000"===t.code&&t.data?(e.currentPoolDetails=t.data.data[0],console.log("获取到卡池详情:",t.data),e.currentPoolDetails.corpId&&(console.log("获取厂商名称",e.currentPoolDetails.usageType,e.currentPoolDetails.corpId),a.getCompanyList(e.currentPoolDetails.usageType,e.currentPoolDetails.corpId))):(e.$Message.warning("操作失败: "+(t.msg||"未知错误")),e.currentPoolDetails=null)})).catch((function(t){console.error("获取卡池详情失败:",t),e.$Message.error("操作失败"),e.currentPoolDetails=null})).finally((function(){e.poolDetailLoading=!1}))},getCardPoolPackageList:function(){var t=this;Object(o["b"])({poolId:this.currentPoolDetails.poolId}).then((function(e){console.log("套餐列表:",e),t.currentPoolDetails.packageList=e.data})).catch((function(t){console.log("套餐列表获取失败:",t)}))},handleCollapseChange:function(t){t.includes("1")?this.currentPoolDetails.packageList||this.getCardPoolPackageList():console.log("关闭套餐面板")},getCompanyList:function(t,e){var a=this;console.log("获取厂商名称getCompanyList",t,e),Object(i["p"])({pageNumber:1,pageSize:-1,corpType:"2"==t?"8":"7"}).then((function(t){if(t&&"0000"==t.code){var r=t.data.records;a.corpIdDetails=r,r.forEach((function(t){t.corpId===e&&(a.detilCorpname=t.corpName,console.log("找到匹配厂商:",t.corpName))})),a.detilCorpname||(console.log("未找到匹配厂商，corpId:",e),a.detilCorpname="未知厂商")}else console.error("获取厂商列表失败:",t),a.detilCorpname="未知厂商"})).catch((function(t){console.error("获取厂商列表异常:",t),a.detilCorpname="未知厂商"}))},handleSizeChange:function(t){this.pagination.pageSize=t,this.fetchData()},handleCurrentChange:function(t){this.pagination.currentPage=t,this.fetchData()},fetchData:function(){var t=this;console.log("获取数据 for page:",this.pagination.currentPage,"size:",this.pagination.pageSize,"filters:",this.filters),this.loading=!0;var e={pageNum:this.pagination.currentPage,pageSize:this.pagination.pageSize,ruleName:this.filters.ruleName||void 0,faultSupplierId:this.filters.faultSupplier||void 0,standbySupplierId:this.filters.backupSupplier||void 0,faultStatus:this.filters.status||void 0,mccs:this.filters.targetCountries.length>0?this.filters.targetCountries:void 0,cardPoolName:this.filters.backupPool||void 0};Object(o["f"])(e).then((function(e){"0000"===e.code&&e.data?(t.tableData=e.data.records||[],t.tableData.forEach((function(t){t.targetCountries=t.countryEn?t.countryEn.join(", "):""})),t.pagination.total=e.data.total||0,console.log("获取故障规则列表成功:",t.tableData)):(t.tableData=[],t.pagination.total=0,console.warn("获取故障规则列表失败:",e),t.$Message.warning("操作失败: "+(e.msg||"未知错误")))})).catch((function(e){console.error("获取故障规则列表失败:",e),t.tableData=[],t.pagination.total=0,t.$Message.error("操作失败")})).finally((function(){t.loading=!1}))},fetchSuppliers:function(){var t=this;console.log("获取供应商列表..."),Object(o["g"])().then((function(e){"0000"==e.code?(t.supplierList=e.data,console.log("供应商列表获取成功: ",t.supplierList)):(t.supplierList=[],console.warn("获取供应商列表成功，但数据为空或格式不符: ",e),t.$Message.warning("操作失败"))})).catch((function(e){console.error("获取供应商列表失败:",e),t.supplierList=[],t.$Message.error("操作失败")}))},fetchCountries:function(){var t=this;console.log("获取国家列表..."),Object(o["i"])().then((function(e){"0000"===e.code?(t.countryList=e.data||[],console.log("目标国家列表获取成功: ",t.countryList)):(t.countryList=[],console.warn("获取目标国家列表失败: ",e),t.$Message.warning("操作失败: "+(e.msg||"未知错误")))})).catch((function(e){console.error("获取目标国家列表失败:",e),t.countryList=[],t.$Message.error("操作失败")}))}},filters:{formatUsageType:function(t){var e={1:"全球卡普通卡池",2:"终端线下卡池",3:"终端线上卡池"};return e[t]||"未知类型"},formatDateTime:function(t){if(!t)return"";try{var e=new Date(t);return e.toLocaleString()}catch(a){return t}}},mounted:function(){this.fetchData(),this.fetchSuppliers(),this.fetchCountries()},watch:{"filters.targetCountries":function(t){this.filters.mccs=t}}},s=l,u=(a("67f3"),a("2877")),c=Object(u["a"])(s,r,n,!1,null,"56ac1010",null);e["default"]=c.exports},da8c:function(t,e,a){"use strict";a.d(e,"g",(function(){return l})),a.d(e,"e",(function(){return s})),a.d(e,"d",(function(){return u})),a.d(e,"j",(function(){return c})),a.d(e,"c",(function(){return p})),a.d(e,"a",(function(){return d})),a.d(e,"i",(function(){return f})),a.d(e,"k",(function(){return g})),a.d(e,"f",(function(){return h})),a.d(e,"h",(function(){return m})),a.d(e,"b",(function(){return v}));var r=a("66df"),n="/rms/api/v1",o="/oms/api/v1",i="/pms/api/v1",l=function(){return r["a"].request({url:n+"/supplier/query",method:"get"})},s=function(t){return r["a"].request({url:o+"/country/getCountryByContinent",method:"get",params:t})},u=function(){return r["a"].request({url:o+"/country/getContinent",method:"get"})},c=function(t){return r["a"].request({url:n+"/supplierFaultRule/save",method:"post",data:t})},p=function(t){return r["a"].request({url:i+"/cardPool/queryCardPoolByMccList",method:"post",data:t})},d=function(t){return r["a"].request({url:i+"/cardPool/getList",method:"post",data:t})},f=function(){return r["a"].request({url:n+"/supplierFaultRule/getTargetCountry",method:"get"})},g=function(t){return r["a"].request({url:n+"/supplierFaultRule/updateFaultStatus",method:"post",params:{id:t}})},h=function(t){return r["a"].request({url:n+"/supplierFaultRule/getPageList",method:"post",data:t})};function m(t){return r["a"].request({url:o+"/country/getSuppliersByMccList",method:"post",data:t})}var v=function(t){return r["a"].request({url:i+"/cardPool/getPackageListByPoolId",method:"get",params:t})}},f7fa:function(t,e,a){"use strict";a.d(e,"p",(function(){return o})),a.d(e,"r",(function(){return i})),a.d(e,"a",(function(){return l})),a.d(e,"u",(function(){return s})),a.d(e,"d",(function(){return u})),a.d(e,"f",(function(){return c})),a.d(e,"o",(function(){return p})),a.d(e,"i",(function(){return d})),a.d(e,"b",(function(){return f})),a.d(e,"v",(function(){return g})),a.d(e,"e",(function(){return h})),a.d(e,"h",(function(){return m})),a.d(e,"g",(function(){return v})),a.d(e,"s",(function(){return P})),a.d(e,"l",(function(){return b})),a.d(e,"k",(function(){return y})),a.d(e,"t",(function(){return D})),a.d(e,"m",(function(){return _})),a.d(e,"n",(function(){return C})),a.d(e,"j",(function(){return S})),a.d(e,"w",(function(){return k})),a.d(e,"c",(function(){return w})),a.d(e,"q",(function(){return x}));var r=a("66df"),n="/cms/api/v1",o=function(t){return r["a"].request({url:n+"/terminal/pages",params:t,method:"get"})},i=function(t){return r["a"].request({url:n+"/terminal/settleRule/queryList",params:t,method:"get"})},l=function(t){return r["a"].request({url:n+"/terminal",data:t,method:"post"})},s=function(t,e){return r["a"].request({url:n+"/terminal/"+t,data:e,method:"put"})},u=function(t,e){return r["a"].request({url:n+"/terminal/audit/"+t,params:e,method:"put"})},c=function(t){return r["a"].request({url:n+"/terminal",data:t,method:"delete"})},p=function(t){return r["a"].request({url:n+"/terminal/details",params:t,method:"get"})},d=function(t){return r["a"].request({url:n+"/terminal/details/export",params:t,responseType:"blob",method:"get"})},f=function(t){return r["a"].request({url:n+"/terminal/settleRule/add",data:t,method:"post"})},g=function(t){return r["a"].request({url:n+"/terminal/settleRule/update",data:t,method:"put"})},h=function(t){return r["a"].request({url:"/pms/api/v1/cardPool/checkPackage",params:t,method:"get"})},m=function(t){return r["a"].request({url:n+"/terminal/settleRule/delete/"+t,method:"delete"})},v=function(t){return r["a"].request({url:n+"/terminal/settleRule/deleteBatch",data:t,method:"post"})},P=function(t){return r["a"].request({url:"/stat/cdr/flow/get/list",params:t,method:"get"})},b=function(t){return r["a"].request({url:"/stat/cdr/flow/export/details",params:t,method:"get"})},y=function(t){return r["a"].request({url:"/stat/cdr/flow/export/info",params:t,method:"get"})},D=function(t){return r["a"].request({url:"/stat/cdr/flow/get/info",params:t,method:"get"})},_=function(t){return r["a"].request({url:"/stat/cdr/flow/export/list",params:t,method:"get"})},C=function(t){return r["a"].request({url:"/stat/cdr/flow/get/details",params:t,method:"get"})},S=function(t){return r["a"].request({url:"/stat/cdr/flow/export/info/all",params:t,method:"get"})},k=function(t){return r["a"].request({url:n+"/terminal/plmnlist/update",data:t,method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"}})},w=function(t){return r["a"].request({url:n+"/terminal/plmnlist/createByFile",data:t,method:"post",contentType:"multipart/form-data"})},x=function(t){return r["a"].request({url:n+"/terminal/plmnlist/get",params:t,method:"get"})}}}]);