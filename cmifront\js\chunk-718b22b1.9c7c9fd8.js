(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-718b22b1"],{"12d7":function(t,e,a){"use strict";e["a"]={roleList:[{key:"0",value:"角色1"},{key:"1",value:"角色2"},{key:"2",value:"角色3"}],currencyList:[{key:"0",value:"人民币"},{key:"1",value:"美元"},{key:"2",value:"港币"}],queryTypeList:[{key:"0",value:"年统计"},{key:"1",value:"季度统计"},{key:"2",value:"月统计"}],seasonList:[{key:"1",value:"第一季度"},{key:"2",value:"第二季度"},{key:"3",value:"第三季度"},{key:"4",value:"第四季度"}],operatorList:[{key:"1",value:"运营商1"},{key:"2",value:"运营商2"},{key:"3",value:"运营商3"},{key:"4",value:"运营商4"}],userTypeList:[{key:"1",value:"运营商"},{key:"2",value:"企业"}]}},"3d06":function(t,e,a){"use strict";a("8961")},"3f7e":function(t,e,a){"use strict";var n=a("b5db"),o=n.match(/firefox\/(\d+)/i);t.exports=!!o&&+o[1]},"477a":function(t,e,a){"use strict";a.d(e,"a",(function(){return r}));var n=a("66df"),o="/api/v1",r=function(t){return n["a"].request({url:o+"/logs/searchByNumber",params:t,method:"get"})}},"4e82":function(t,e,a){"use strict";var n=a("23e7"),o=a("e330"),r=a("59ed"),i=a("7b0b"),l=a("07fa"),c=a("083a"),s=a("577e"),u=a("d039"),d=a("addb"),h=a("a640"),f=a("3f7e"),p=a("99f4"),v=a("1212"),m=a("ea83"),y=[],g=o(y.sort),k=o(y.push),w=u((function(){y.sort(void 0)})),b=u((function(){y.sort(null)})),_=h("sort"),I=!u((function(){if(v)return v<70;if(!(f&&f>3)){if(p)return!0;if(m)return m<603;var t,e,a,n,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)y.push({k:e+n,v:a})}for(y.sort((function(t,e){return e.v-t.v})),n=0;n<y.length;n++)e=y[n].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}})),x=w||!b||!_||!I,S=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:s(e)>s(a)?1:-1}};n({target:"Array",proto:!0,forced:x},{sort:function(t){void 0!==t&&r(t);var e=i(this);if(I)return void 0===t?g(e):g(e,t);var a,n,o=[],s=l(e);for(n=0;n<s;n++)n in e&&k(o,e[n]);d(o,S(t)),a=l(o),n=0;while(n<a)e[n]=o[n++];while(n<s)c(e,n++);return e}})},"6abd":function(t,e,a){"use strict";a.d(e,"e",(function(){return r})),a.d(e,"d",(function(){return i})),a.d(e,"c",(function(){return l})),a.d(e,"a",(function(){return c})),a.d(e,"b",(function(){return s}));var n=a("66df"),o="/api/v1",r=function(t){return n["a"].request({url:o+"/logs/searchByLocal",params:t,method:"get"})},i=function(t){return n["a"].request({url:o+"/account/operator",data:t,method:"PUT"})},l=function(t){return n["a"].request({url:o+"/account/operator",data:t,method:"DELETE"})},c=function(t){return n["a"].request({url:o+"/account/operator",data:t,method:"post"})},s=function(t){return n["a"].request({url:o+"/account/changeStatus",data:t,method:"PUT"})}},8961:function(t,e,a){},"8d8a":function(t,e,a){"use strict";a.r(e);a("b0c0");var n=function(){var t=this,e=t._self._c;return e("div",[e("Card",[e("div",{staticClass:"search_head"},[e("Select",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请选择国家/地区",clearable:""},on:{"on-clear":t.clean,"on-change":t.getOperatorList},model:{value:t.localId,callback:function(e){t.localId=e},expression:"localId"}},t._l(t.localList,(function(a){return e("Option",{key:a.key,attrs:{value:a.key}},[t._v(t._s(a.name))])})),1),e("Select",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请选择运营商",clearable:""},model:{value:t.operatorId,callback:function(e){t.operatorId=e},expression:"operatorId"}},t._l(t.operatorList,(function(a){return e("Option",{key:a.key,attrs:{value:a.key}},[t._v(t._s(a.name))])})),1),e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",icon:"md-search"},on:{click:function(e){return t.searchByCondition()}}},[t._v("搜索")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{type:"success",icon:"ios-add"},on:{click:t.showAdd}},[t._v("新增批价规则")])],1),e("div",{staticStyle:{"margin-top":"20px"}},[e("Table",{attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.loading},scopedSlots:t._u([{key:"status",fn:function(e){var a=e.row;return[t._v("\n          "+t._s("0"===a.status?"已通过":"未通过")+"\n        ")]}},{key:"check",fn:function(a){var n=a.row;a.index;return["1"===n.status?e("Button",{directives:[{name:"has",rawName:"v-has",value:"edit",expression:"'edit'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"warning",size:"small"},on:{click:function(e){return t.changeStatus(n,"0")}}},[t._v("通过")]):t._e(),"1"===n.status?e("Button",{directives:[{name:"has",rawName:"v-has",value:"edit",expression:"'edit'"}],attrs:{type:"error",size:"small"},on:{click:function(e){return t.changeStatus(n,"1")}}},[t._v("不通过")]):e("span",[t._v("...")])]}},{key:"action",fn:function(a){var n=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.editPrice(n)}}},[t._v(t._s(t.$t("common.edit")))]),e("Button",{attrs:{type:"error",size:"small"},on:{click:function(e){return t.deleteWarning(n)}}},[t._v(t._s(t.$t("common.del")))])]}}])})],1),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1)]),e("Modal",{attrs:{title:"编辑批价信息","mask-closable":!1},on:{"on-cancel":t.cancelModal},model:{value:t.editModal,callback:function(e){t.editModal=e},expression:"editModal"}},[t.editModal?e("Form",{ref:"editForm",attrs:{model:t.modalData,rules:t.rules},nativeOn:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.userAddOrEdit.apply(null,arguments)}}},[e("FormItem",{attrs:{prop:"wholesalerName"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"20%"}},[t._v("客户名称：")]),e("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入用户账号...",disabled:t.reset},model:{value:t.modalData.wholesalerName,callback:function(e){t.$set(t.modalData,"wholesalerName",e)},expression:"modalData.wholesalerName"}})],1)]),e("FormItem",{attrs:{prop:"localId"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"20%"}},[t._v("选择国家/地区：")]),e("Select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择国家/地区",multiple:""},model:{value:t.modalData.localId,callback:function(e){t.$set(t.modalData,"localId",e)},expression:"modalData.localId"}},t._l(t.localList,(function(a){return e("Option",{key:a.key,attrs:{value:a.key}},[t._v(t._s(a.name))])})),1)],1)]),e("FormItem",{attrs:{prop:"UnitPrice"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"20%"}},[t._v("设置单价：")]),e("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入单价..."},model:{value:t.modalData.UnitPrice,callback:function(e){t.$set(t.modalData,"UnitPrice",e)},expression:"modalData.UnitPrice"}})],1)]),e("FormItem",{attrs:{prop:"currencyType"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"20%"}},[t._v("选择币种：")]),e("Select",{staticStyle:{width:"80%"},attrs:{placeholder:"请选择币种"},model:{value:t.modalData.currencyType,callback:function(e){t.$set(t.modalData,"currencyType",e)},expression:"modalData.currencyType"}},t._l(t.currencyList,(function(a){return e("Option",{key:a.key,attrs:{value:a.key}},[t._v(t._s(a.value))])})),1)],1)])],1):t._e(),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("确定")])],1)],1)],1)},o=[],r=(a("d9e2"),a("99af"),a("14d9"),a("4e82"),a("d3b7"),a("ac1f"),a("5319"),a("6abd")),i=a("477a"),l=a("fea3"),c=a("12d7"),s={data:function(){var t=function(t,e,a){e&&0!=e.length?a():a(new Error("请选择国家/地区"))},e=function(t,e,a){e&&""!=e.replace(/\s/g,"")?a():a(new Error("请输入客户名称"))},a=function(t,e,a){e&&0!=e.length?a():a(new Error("请设置单价"))},n=function(t,e,a){e&&0!=e.length?a():a(new Error("请设置币种"))};return{localList:[],loading4:!1,operatorList:[],currencyList:[],continentList:[],editModal:!1,reset:!1,localId:"",operatorId:"",columns:[{title:"客户名称",key:"wholesalerName",align:"center"},{title:"国家/地区",key:"local",align:"center"},{title:"单价/G",key:"UnitPrice",align:"center"},{title:"币种",key:"currency",align:"center"},{title:"审批状态",slot:"status",align:"center"},{title:"审核",slot:"check",align:"center"},{title:"操作",slot:"action",align:"center"}],tableData:[{wholesalerName:"客户",wholesalerId:"iii",local:"中国",localId:"101",UnitPrice:"100",currency:"人民币",currencyType:"0",status:"0"},{wholesalerName:"客户",wholesalerId:"iii",local:"日本",localId:"102",UnitPrice:"100",currencyType:"1",currency:"美元",status:"1"}],modalData:null,loading:!1,currentPage:1,page:0,startTime:null,endTime:null,total:0,rules:{wholesalerName:[{validator:e,trigger:"blur"}],localId:[{validator:t,trigger:"blur"}],UnitPrice:[{validator:a,trigger:"blur"}],currencyType:[{validator:n,trigger:"blur"}]}}},computed:{},methods:{goPageFirst:function(t){var e=this;this.page=t,this.loading=!0;var a={pageNumber:t,pageSize:10,localId:this.localId,operatorId:this.operatorId};Object(r["e"])(a).then((function(t){if(!t||"0000"!=t.code)throw t;e.tableData=t.data.records,e.total=t.data.total})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))},clean:function(){this.operatorList=[]},getOperatorList:function(t){var e=this;Object(i["a"])({localId:t}).then((function(t){if(!t||"0000"!=t.code)throw t;e.operatorList=t.data})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))},editPrice:function(t){this.reset=!0,this.modalData=t,this.editModal=!0},showAdd:function(){this.reset=!1,this.modalData={wholesalerId:"",wholesalerName:"",localId:[],UnitPrice:"",currencyType:""},this.editModal=!0},save:function(){var t=this;this.$refs.editForm.validate((function(e){e&&(t.reset?t.edit():t.add())}))},add:function(){for(var t=this,e=[],a=0;a<this.modalData.localId.length;a++)e.push(this.modalData.localId[a]);this.modalData.localId=e,Object(r["a"])(this.modalData).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提醒",desc:"新增批价规则成功！"}),t.editModal=!1,t.goPageFirst(0)})).catch((function(t){console.log(t)})).finally((function(){t.loading=!1}))},edit:function(){for(var t=this,e=[],a=0;a<this.modalData.localId.length;a++)e.push(this.modalData.localId[a]);this.modalData.localId=e,Object(r["d"])(this.modalData).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作成功",desc:"修改批价规则成功！"}),t.editModal=!1,t.goPageFirst(0)})).catch((function(t){console.log(t)})).finally((function(){t.loading=!1}))},delete:function(t){var e=this;Object(r["c"])({wholesalerId:t.wholesalerId,status:status}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作成功",desc:"删除成功！"}),e.editModal=!1,e.goPageFirst(0)})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))},changeStatus:function(t,e){var a=this;Object(r["b"])({wholesalerId:t.wholesalerId,status:e}).then((function(t){if(!t||"0000"!=t.code)throw t;a.$Notice.success({title:"操作成功",desc:"状态已变更！"}),a.editModal=!1,a.goPageFirst(0)})).catch((function(t){console.log(t)})).finally((function(){a.loading=!1}))},searchByCondition:function(){this.goPageFirst(0)},goPage:function(t){this.goPageFirst(t)},cancelModal:function(){this.editModal=!1},deleteWarning:function(t){var e=this;this.$Notice.warning({title:"操作提醒",name:"delete",desc:"请确定删除已选中账户？",render:function(a){return a("div",["请确定删除已选中批量规则？",a("br"),a("div",[a("Button",{props:{type:"dashed",size:"small"},style:{marginTop:"10px",marginLeft:"130px"},on:{click:function(){e.$Notice.close("delete")}}},"取消"),a("Button",{props:{type:"error",size:"small"},style:{marginTop:"10px",marginLeft:"10px"},on:{click:function(){e.$Notice.close("delete"),e.delete(t)}}},"删除")])])},duration:0})}},mounted:function(){var t=l["a"].localList;this.currencyList=c["a"].currencyList;for(var e=0;e<t.length;e++)this.localList=this.localList.concat(t[e].value);var a=this.$i18n.locale;this.localList.sort((function(t,e){return t.name.localeCompare(e.name,a.split("-")[0])}));this.goPageFirst(0)},watch:{}},u=s,d=(a("3d06"),a("2877")),h=Object(d["a"])(u,n,o,!1,null,null,null);e["default"]=h.exports},"99f4":function(t,e,a){"use strict";var n=a("b5db");t.exports=/MSIE|Trident/.test(n)},addb:function(t,e,a){"use strict";var n=a("f36a"),o=Math.floor,r=function(t,e){var a=t.length;if(a<8){var i,l,c=1;while(c<a){l=c,i=t[c];while(l&&e(t[l-1],i)>0)t[l]=t[--l];l!==c++&&(t[l]=i)}}else{var s=o(a/2),u=r(n(t,0,s),e),d=r(n(t,s),e),h=u.length,f=d.length,p=0,v=0;while(p<h||v<f)t[p+v]=p<h&&v<f?e(u[p],d[v])<=0?u[p++]:d[v++]:p<h?u[p++]:d[v++]}return t};t.exports=r},ea83:function(t,e,a){"use strict";var n=a("b5db"),o=n.match(/AppleWebKit\/(\d+)\./);t.exports=!!o&&+o[1]},fea3:function(t,e,a){"use strict";e["a"]={localList:[{key:"1",name:"亚洲",value:[{key:"101",name:"中国(China)"},{key:"102",name:"日本(Japan)"},{key:"103",name:"韩国(the republic of korea)"}]},{key:"2",name:"欧洲",value:[{key:"201",name:"意大利(Italy)"},{key:"202",name:"德国(Germany)"},{key:"203",name:"英国(Britain)"}]}]}}}]);