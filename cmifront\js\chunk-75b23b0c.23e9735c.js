(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-75b23b0c"],{"2a0d":function(t,e,a){"use strict";a.d(e,"i",(function(){return i})),a.d(e,"a",(function(){return c})),a.d(e,"j",(function(){return r})),a.d(e,"b",(function(){return s})),a.d(e,"c",(function(){return l})),a.d(e,"g",(function(){return u})),a.d(e,"d",(function(){return p})),a.d(e,"h",(function(){return d})),a.d(e,"f",(function(){return f})),a.d(e,"e",(function(){return g}));var n=a("66df"),o="/cms",i=function(t){return n["a"].request({url:o+"/cooperation",params:t,method:"get"})},c=function(t){return n["a"].request({url:o+"/cooperation",data:t,method:"post"})},r=function(t){return n["a"].request({url:o+"/cooperation/updateCooperation",data:t,method:"post"})},s=function(t){return n["a"].request({url:o+"/cooperation",params:t,method:"put"})},l=function(t){return n["a"].request({url:o+"/cooperation",data:t,method:"delete"})},u=function(t){return n["a"].request({url:o+"/cooperation/detail",params:t,method:"get"})},p=function(t){return n["a"].request({url:o+"/cooperation/derive",params:t,responseType:"blob",method:"get"})},d=function(t){return n["a"].request({url:"/pms/api/v1/package/getList",data:t,method:"post"})},f=function(t){return n["a"].request({url:o+"/cooperation/getCooperationMoney",params:t,method:"get"})},g=function(t){return n["a"].request({url:"/oms/api/v1/country/queryCounrtyList",method:"get"})}},3446:function(t,e,a){"use strict";a.d(e,"i",(function(){return i})),a.d(e,"f",(function(){return c})),a.d(e,"h",(function(){return r})),a.d(e,"b",(function(){return s})),a.d(e,"e",(function(){return l})),a.d(e,"l",(function(){return u})),a.d(e,"d",(function(){return p})),a.d(e,"g",(function(){return d})),a.d(e,"k",(function(){return f})),a.d(e,"m",(function(){return g})),a.d(e,"a",(function(){return h})),a.d(e,"c",(function(){return m})),a.d(e,"j",(function(){return y}));var n=a("66df"),o="/sms",i=function(t){return n["a"].request({url:o+"/regionalWelcome/introduce",method:"GET"})},c=function(t){return n["a"].request({url:o+"/regionalWelcome/page",data:t,method:"POST"})},r=function(t){return n["a"].request({url:"/oms/api/v1/country/queryCounrtyList",method:"get"})},s=function(t){return n["a"].request({url:o+"/regionalWelcome/del",method:"post",params:t})},l=function(t){return n["a"].request({url:o+"/regionalWelcome/getRegional",method:"post",data:t})},u=function(t){return n["a"].request({url:o+"/regionalWelcome/getPackage",method:"post",data:t})},p=function(t){return n["a"].request({url:o+"/regionalWelcome/getAllPackageFile",method:"post",data:t,responseType:"blob"})},d=function(t){return n["a"].request({url:"oms/api/v1/country/queryCounrtyByContinent",method:"get"})},f=function(t){return n["a"].request({url:"pms/api/v1/package/smsGetPackage",data:t,method:"post",responseType:"blob"})},g=function(t){return n["a"].request({url:"pms/api/v1/package/importPackageId",data:t,method:"post",responseType:"blob"})},h=function(t){return n["a"].request({url:"sms/regionalWelcome/add",data:t,method:"post"})},m=function(t){return n["a"].request({url:"sms/regionalWelcome/edit",data:t,method:"post"})},y=function(t){return n["a"].request({url:"/pms/api/v1/package/downSmsPackageFile",params:t,method:"get",responseType:"blob"})}},"4e82":function(t,e,a){"use strict";var n=a("23e7"),o=a("e330"),i=a("59ed"),c=a("7b0b"),r=a("07fa"),s=a("083a"),l=a("577e"),u=a("d039"),p=a("addb"),d=a("a640"),f=a("3f7e"),g=a("99f4"),h=a("1212"),m=a("ea83"),y=[],k=o(y.sort),b=o(y.push),v=u((function(){y.sort(void 0)})),O=u((function(){y.sort(null)})),w=d("sort"),C=!u((function(){if(h)return h<70;if(!(f&&f>3)){if(g)return!0;if(m)return m<603;var t,e,a,n,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(n=0;n<47;n++)y.push({k:e+n,v:a})}for(y.sort((function(t,e){return e.v-t.v})),n=0;n<y.length;n++)e=y[n].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}})),x=v||!O||!w||!C,S=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:l(e)>l(a)?1:-1}};n({target:"Array",proto:!0,forced:x},{sort:function(t){void 0!==t&&i(t);var e=c(this);if(C)return void 0===t?k(e):k(e,t);var a,n,o=[],l=r(e);for(n=0;n<l;n++)n in e&&b(o,e[n]);p(o,S(t)),a=r(o),n=0;while(n<a)e[n]=o[n++];while(n<l)s(e,n++);return e}})},"951d":function(t,e,a){"use strict";a.d(e,"g",(function(){return i})),a.d(e,"d",(function(){return c})),a.d(e,"c",(function(){return r})),a.d(e,"b",(function(){return s})),a.d(e,"a",(function(){return l})),a.d(e,"e",(function(){return u})),a.d(e,"f",(function(){return p}));var n=a("66df"),o="/cms/package/config",i=function(t){return n["a"].request({url:o+"/task/pageList",data:t,method:"post"})},c=function(t,e){return n["a"].request({url:o+"/task/download/".concat(t,"?status=")+e,method:"POST",responseType:"blob"})},r=function(t){return n["a"].request({url:o+"/task/rollback/".concat(t),method:"POST"})},s=function(t){return n["a"].request({url:o+"/task",data:t,method:"POST",contentType:"multipart/form-data"})},l=function(t){return n["a"].request({url:o+"/taskPage",data:t,method:"POST"})},u=function(t){return n["a"].request({url:"/cms/channel/searchList",data:t,method:"post"})},p=function(t){return n["a"].request({url:"/cms/package/config/getTextChannel",data:t,method:"get"})}},a9af:function(t,e,a){"use strict";a.r(e);var n=a("ade3"),o=(a("b0c0"),a("498a"),function(){var t=this,e=t._self._c;return e("Card",{staticStyle:{width:"100%",padiing:"16px"}},[e("Form",{ref:"formValidate",staticStyle:{padding:"0 5%"},attrs:{model:t.formValidate,rules:t.ruleValidate,"label-width":80},nativeOn:{keydown:function(e){if(!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;e.preventDefault()}}},[e("Row",{staticStyle:{"margin-top":"30px","margin-bottom":"20px"}},[e("Col",{attrs:{span:"24"}},[e("FormItem",{attrs:{label:"模板名称",prop:"templateName"}},[e("Input",{attrs:{placeholder:"请输入模板名称",maxlength:"255",clearable:""},model:{value:t.formValidate.templateName,callback:function(e){t.$set(t.formValidate,"templateName","string"===typeof e?e.trim():e)},expression:"formValidate.templateName"}})],1)],1)],1),e("Row",{staticStyle:{"margin-bottom":"20px"},attrs:{gutter:16}},t._l(t.languages,(function(a,n){return e("Col",{key:n,attrs:{xs:24,sm:24,md:8,lg:8}},[e("FormItem",{attrs:{label:0===n?"模板内容":"",prop:"content".concat(a.code),rules:t.ruleValidate.templateRule}},[e("h3",{staticStyle:{"text-align":"center"}},[t._v(t._s(a.label))]),e("Input",{ref:"textInputs",refInFor:!0,staticStyle:{"min-width":"200px","word-break":"break-all"},attrs:{type:"textarea",rows:4,placeholder:a.placeholder},on:{focus:function(e){return t.setInputFocus(n)}},model:{value:t.formValidate["content".concat(a.code)],callback:function(e){t.$set(t.formValidate,"content".concat(a.code),"string"===typeof e?e.trim():e)},expression:"formValidate[`content${language.code}`]"}}),e("div",{staticStyle:{"text-align":"left","margin-top":"10px"}},[e("Button",{attrs:{type:"info",ghost:"",size:"small"},on:{click:function(e){return t.insertVariable(n,"iccid")}}},[t._v("\n            ICCID\n          ")]),e("span",{staticStyle:{"margin-left":"20px"}}),e("Button",{attrs:{type:"info",ghost:"",size:"small"},on:{click:function(e){return t.insertVariable(n,"position")}}},[t._v("\n            位置\n          ")])],1)],1)],1)})),1),e("Tabs",{staticStyle:{"margin-bottom":"50px"},attrs:{value:"name1"}},[e("TabPane",{attrs:{label:"适用国家/地区",name:"name1"}},[e("div",{staticClass:"custom-textarea"},[e("Row",{staticClass:"options-container1"},t._l(t.countryOptions,(function(a,n){return e("Col",{key:a.mcc,staticClass:"option-item",attrs:{span:"24"}},[e("span",[t._v(t._s(a.countryEn+"（"+a.countryCn+"）"))]),e("Button",{staticClass:"remove-button",attrs:{icon:"md-close-circle"},on:{click:function(e){return t.removeCountry(n)}}}),t._v("\n                    \n            ")],1)})),1),e("div",{staticClass:"button-container"},[e("Button",{attrs:{type:"primary",ghost:""},on:{click:t.addCountry}},[t._v("添加国家")]),e("Button",{attrs:{type:"success",ghost:""},on:{click:t.batchAddCountry}},[t._v("批量添加")]),e("Button",{attrs:{type:"error",ghost:""},on:{click:t.clearAllCountry}},[t._v("清空国家")])],1)],1)]),e("TabPane",{attrs:{label:"适用套餐",name:"name2"}},[e("div",{staticClass:"custom-textarea"},[t.spinShow?e("div",{staticClass:"spinBox"},[e("Spin",{attrs:{size:"large"}})],1):t._e(),e("Scroll",{staticStyle:{width:"100%"},attrs:{"on-reach-edge":t.handleReachEdge}},[t.spinShow?t._e():e("div",{ref:"optionsContainer",staticClass:"options-container"},[t._l(t.tempPackageOptions,(function(a,n){return 0==t.showPackageType?e("div",{key:a.id,staticClass:"option-item",attrs:{span:"24"}},[e("span",[t._v(t._s(a.id+"（"+a.nameCn+"）"))]),e("Button",{staticClass:"remove-button",attrs:{icon:"md-close-circle"},on:{click:function(e){return t.removePackage(n)}}}),t._v("\n                        \n              ")],1):t._e()})),1==t.showPackageType?e("div",{staticClass:"option-item"},[e("h3",[t._v(t._s(t.packageTypeShow))])]):t._e()],2)]),t.spinShow?t._e():e("div",{staticClass:"button-container"},[e("Button",{attrs:{type:"info",ghost:""},on:{click:t.updatePackage}},[t._v("编辑")]),0==t.showPackageType?e("Button",{attrs:{type:"error",ghost:""},on:{click:t.clearAllPackage}},[t._v("清空")]):t._e()],1)],1)])],1),e("footer",{staticClass:"footer-textarea"},[e("Button",{attrs:{type:"primary",loading:t.sumbitLoading,icon:"md-checkmark"},on:{click:t.sumbit}},[t._v("确定")]),t._v("\n                          \n      "),e("Button",{attrs:{icon:"md-arrow-back"},on:{click:t.reback}},[t._v("返回")])],1)],1),e("Modal",{attrs:{title:"添加适用国家/地区","footer-hide":!0,"mask-closable":!1,width:"500px"},on:{"on-cancel":t.closeModal1},model:{value:t.modal1,callback:function(e){t.modal1=e},expression:"modal1"}},[e("Form",{ref:"mccObj",staticStyle:{padding:"0 5%"},attrs:{model:t.mccObj,rules:t.mccValidate}},[e("FormItem",{attrs:{prop:"country"}},[e("Select",{staticStyle:{"margin-top":"20px"},attrs:{multiple:"",filterable:""},model:{value:t.mccObj.country,callback:function(e){t.$set(t.mccObj,"country",e)},expression:"mccObj.country"}},t._l(t.countryList,(function(a){return e("Option",{key:a.mcc,attrs:{value:a.mcc}},[t._v("\n            "+t._s(a.countryEn+"（"+a.countryCn+"）")+"\n          ")])})),1)],1)],1),e("div",{staticClass:"footer-textarea",staticStyle:{margin:"30px"}},[e("Button",{attrs:{type:"primary",icon:"md-checkmark"},on:{click:t.sumbitModal1}},[t._v("确定")]),t._v("\n                          \n      "),e("Button",{attrs:{icon:"md-arrow-back"},on:{click:t.closeModal1}},[t._v("返回")])],1)],1),e("Modal",{attrs:{title:"批量添加适用国家/地区","footer-hide":!0,"mask-closable":!1,width:"90%",styles:{top:"0px"}},on:{"on-cancel":t.closeModal1},model:{value:t.modal2,callback:function(e){t.modal2=e},expression:"modal2"}},[e("div",{staticStyle:{height:"750px",overflow:"auto"}},[e("Tabs",{attrs:{type:"card"}},t._l(t.continentsData,(function(a,n){return e("TabPane",{key:n,attrs:{label:n}},[e("Checkbox",{staticStyle:{margin:"30px 0"},attrs:{indeterminate:t.isIndeterminate(n),value:t.isAllSelected(n)},on:{"on-change":function(e){return t.toggleSelectAll(n)}}},[e("span",{staticStyle:{color:"brown"}},[t._v("全选"+t._s(n))])]),e("Row",{attrs:{gutter:16}},t._l(a,(function(a){return e("Col",{key:a.mcc,staticStyle:{"margin-bottom":"20px"},attrs:{xs:24,sm:24,md:12,lg:8}},[e("Checkbox",{attrs:{value:t.selectedCountriesMap[a.mcc]},on:{"on-change":function(e){return t.toggleCountry(e,a)}}},[t._v("\n              "+t._s(a.countryEn)+"（"+t._s(a.countryCn)+"）\n            ")])],1)})),1),e("Row",[e("Col",{staticStyle:{"margin-top":"30px"},attrs:{span:"24"}},[e("p",{staticStyle:{"font-weight":"bold",color:"brown"}},[t._v("已勾选国家：")]),e("ul",{staticClass:"selected-countries-list"},t._l(t.selectedCountries,(function(a){return e("li",{key:a.mcc,staticClass:"selected-country"},[t._v("\n                "+t._s(a.countryEn)+"（"+t._s(a.countryCn)+"）\n              ")])})),0)])],1)],1)})),1)],1),e("div",{staticClass:"footer-textarea",staticStyle:{margin:"30px"}},[e("Button",{attrs:{type:"primary",icon:"md-checkmark"},on:{click:t.sumbitModal2}},[t._v("确定")]),t._v("\n                          \n      "),e("Button",{attrs:{icon:"md-arrow-back"},on:{click:t.closeModal1}},[t._v("返回")])],1)]),e("Modal",{attrs:{title:"适用套餐","footer-hide":!0,"mask-closable":!1,width:"1100px"},on:{"on-cancel":t.closeModal1},model:{value:t.modal3,callback:function(e){t.modal3=e},expression:"modal3"}},[e("Form",{ref:"packageObj",staticStyle:{padding:"20px"},attrs:{model:t.packageObj,rules:t.packageValidate}},[e("FormItem",{attrs:{prop:"applyPackageType"}},[e("RadioGroup",{model:{value:t.packageObj.applyPackageType,callback:function(e){t.$set(t.packageObj,"applyPackageType",e)},expression:"packageObj.applyPackageType"}},[e("Radio",{attrs:{label:"1"}},[e("span",[t._v("全部套餐")])]),t._v("        \n          "),e("Radio",{attrs:{label:"2"}},[e("span",[t._v("全部CMI套餐")])]),t._v("        \n          "),e("Radio",{attrs:{label:"3"}},[e("span",[t._v("全部渠道自建套餐")])]),t._v("        \n          "),e("Radio",{attrs:{label:"4"}},[e("span",[t._v("自选套餐")])])],1)],1),"4"==t.packageObj.applyPackageType?e("div",{staticStyle:{display:"flex","justify-content":"flex-start","flex-wrap":"wrap","align-items":"center"}},[e("FormItem",{attrs:{prop:"packageName"}},[e("Input",{attrs:{placeholder:"请输入套餐名称",clearable:""},model:{value:t.packageObj.packageName,callback:function(e){t.$set(t.packageObj,"packageName","string"===typeof e?e.trim():e)},expression:"packageObj.packageName"}})],1),t._v("      \n        "),e("FormItem",{attrs:{prop:"packageId"}},[e("Input",{attrs:{placeholder:"请输入套餐ID",clearable:""},model:{value:t.packageObj.packageId,callback:function(e){t.$set(t.packageObj,"packageId","string"===typeof e?e.trim():e)},expression:"packageObj.packageId"}})],1),t._v("      \n        "),e("FormItem",{attrs:{prop:"mcc"}},[e("Select",{attrs:{placeholder:"请选择套餐覆盖国家",filterable:"",clearable:""},model:{value:t.packageObj.mcc,callback:function(e){t.$set(t.packageObj,"mcc",e)},expression:"packageObj.mcc"}},t._l(t.continentList,(function(a){return e("Option",{key:a.id,attrs:{value:a.mcc}},[t._v(t._s(a.countryEn))])})),1)],1),t._v("      \n        "),e("FormItem",{attrs:{prop:"corpId"}},[e("Select",{attrs:{placeholder:"请选择渠道商",filterable:"",clearable:""},model:{value:t.packageObj.corpId,callback:function(e){t.$set(t.packageObj,"corpId",e)},expression:"packageObj.corpId"}},t._l(t.corpList,(function(a,n){return e("Option",{key:n,attrs:{value:a.corpId}},[t._v(t._s(a.corpName))])})),1)],1),t._v("      \n        "),e("FormItem",[e("Button",{staticStyle:{margin:"0 2px"},attrs:{type:"info",loading:t.searchExoprtLoading,icon:"ios-search"},on:{click:t.searchPackage}},[t._v("搜索")])],1),t._v("      \n        "),e("FormItem",[e("Button",{staticStyle:{margin:"0 2px"},attrs:{type:"warning",icon:"md-arrow-round-up"},on:{click:t.exportPackage}},[t._v("导入")])],1)],1):t._e()],1),"4"==t.packageObj.applyPackageType?e("div",{staticStyle:{margin:"30px 20px"}},[e("Checkbox",{attrs:{border:"",disabled:0==t.data.length},on:{"on-change":t.handleAllChange},model:{value:t.selectAll,callback:function(e){t.selectAll=e},expression:"selectAll"}},[t._v("全选")]),e("Table",{ref:"selection",staticStyle:{width:"800px"},attrs:{border:"",columns:t.columns,data:t.data,loading:t.tableLoading},on:{"on-selection-change":t.handleRowChange,"on-select-cancel":t.cancelPackage,"on-select-all-cancel":t.cancelPackageAll}}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage}})],1):t._e(),e("div",{staticClass:"footer-textarea",staticStyle:{margin:"30px"}},[e("Button",{attrs:{type:"primary",icon:"md-checkmark"},on:{click:t.sumbitModal3}},[t._v("确定")]),t._v("\n                          \n      "),e("Button",{attrs:{icon:"md-arrow-back"},on:{click:t.closeModal1}},[t._v("返回")])],1)],1),e("Modal",{attrs:{title:"导入文件","footer-hide":!0,"mask-closable":!1,width:"600px"},on:{"on-cancel":t.closeModal4},model:{value:t.modal4,callback:function(e){t.modal4=e},expression:"modal4"}},[e("div",[e("Form",{ref:"formobj",staticStyle:{"font-weight":"bold"},attrs:{model:t.formobj,rules:t.ruleobj}},[e("FormItem",{staticStyle:{"font-size":"14px","font-weight":"bold"},attrs:{label:"上传文件",prop:"file"}},[e("Upload",{attrs:Object(n["a"])(Object(n["a"])(Object(n["a"])(Object(n["a"])(Object(n["a"])({multiple:"",type:"drag",action:"//jsonplaceholder.typicode.com/posts/"},"action",t.uploadUrl),"on-success",t.fileSuccess),"on-error",t.handleError),"before-upload",t.handleBeforeUpload),"on-progress",t.fileUploading)},[e("div",{staticStyle:{padding:"20px 0"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v("点击或拖拽文件上传")])],1)]),t.file?e("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"100%"}},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name)+"\n              ")],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e(),e("div",{staticStyle:{width:"500px","padding-left":"70px"}},[e("Button",{attrs:{type:"primary",ghost:"",icon:"ios-download",loading:t.downLoading},on:{click:t.downloadFile}},[t._v("模板下载")]),e("Alert",{staticStyle:{float:"right",padding:"8px 10px 8px 10px"},attrs:{type:"warning"}},[t._v(t._s(t.message))]),e("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1)],1)],1),e("div",{staticClass:"footer-textarea",staticStyle:{margin:"30px"}},[e("Button",{attrs:{type:"primary",loading:t.uploadLoading,icon:"md-checkmark"},on:{click:t.sumbitModal4}},[t._v("确定")]),t._v("\n                            \n        "),e("Button",{attrs:{icon:"md-arrow-back"},on:{click:t.closeModal4}},[t._v("返回")])],1)],1)]),e("Modal",{attrs:{title:"导入失败数据","footer-hide":!0,"mask-closable":!1,width:"900px",styles:{top:"0px"}},on:{"on-cancel":t.closeModal5},model:{value:t.modal5,callback:function(e){t.modal5=e},expression:"modal5"}},[e("div",{staticStyle:{height:"600px","overflow-y":"auto"}},[e("div",{staticStyle:{margin:"30px 20px"}},[e("h3",{staticStyle:{color:"#3399ff"}},[t._v("不可用的套餐： "+t._s(t.unAvailableDataTip))]),e("Table",{ref:"selection",staticStyle:{width:"800px"},attrs:{border:"",columns:t.unAvailableColumns,data:t.unAvailableData}})],1),e("div",{staticStyle:{margin:"30px 20px"}},[e("h3",{staticStyle:{color:"#3399ff"}},[t._v("未找到的套餐")]),e("div",{staticStyle:{display:"flex","justify-content":"flex-start"}},[t._v("套餐ID："+t._s(t.notFoundTip))]),e("div",{staticStyle:{display:"flex","flex-wrap":"wrap"}},[t._v(t._s(t.notFoundData))])]),e("div",{staticClass:"footer-textarea",staticStyle:{margin:"30px"}},[e("Button",{attrs:{icon:"md-arrow-back"},on:{click:t.closeModal5}},[t._v("返回")])],1)])])],1)}),i=[],c=a("2909"),r=a("5530"),s=(a("d9e2"),a("99af"),a("4de4"),a("7db0"),a("c740"),a("a15b"),a("d81d"),a("14d9"),a("fb6a"),a("4e82"),a("a434"),a("c19f"),a("ace4"),a("2c66"),a("249d"),a("40e9"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("25f0"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("3ca3"),a("5cc6"),a("907a"),a("9a8c"),a("a975"),a("735e"),a("c1ac"),a("d139"),a("3a7b"),a("986a"),a("1d02"),a("d5d6"),a("82f8"),a("e91f"),a("60bd"),a("5f96"),a("3280"),a("3fcc"),a("ca91"),a("25a1"),a("cd26"),a("3c5d"),a("2954"),a("649e"),a("219c"),a("170b"),a("b39a"),a("6ce5"),a("2834"),a("72f7"),a("4ea1"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("2a0d")),l=a("951d"),u=a("3446"),p={data:function(){var t=this,e=function(e,a,n){t.file?n():n(new Error("请上传文件"))},a=function(e,a,n){var o=t.packageObj,i=(o.applyPackageType,o.packageName),c=o.packageId,r=o.mcc,s=o.corpId,l=[i,c,r,s].some((function(t){return""!==t&&void 0!==t}));l?n():n(new Error("至少一项搜索条件不能为空"))},n=function(e,a,n){var o=t.formValidate,i=o.contentCn,c=o.contentEn,r=o.contentTw,s=[i,c,r].some((function(t){var e=t?t.trim():"";return""!==e}));s?n():n(new Error("至少填写一项模板内容"))};return{formValidate:{templateName:"",contentCn:"",contentEn:"",contentTw:""},mccObj:{country:""},packageObj:{applyPackageType:"",packageName:"",packageId:"",mcc:"",corpId:""},formobj:{file:""},message:"最大限制范围10MB",pageType:"",templateId:"",focusedInputIndex:null,file:null,uploadUrl:"",uploadList:[],country:[],countryOptions:[],applyPackageTypeCopy:"",selectionListCopy:[],showPackageType:!1,packageTypeShow:!1,unAvailableDataTip:"",notFoundTip:"",packageOptions:[],tempPackageOptions:[],continentList:[],selection:[],selectionList:[],selectAll:!1,countryList:[],continentsData:{},selectedCountriesMap:{},selectedCountries:[],corpList:[],total:0,pageSize:10,page:1,languages:[{label:"简体中文",code:"Cn",placeholder:"请输入模板内容(简体中文)"},{label:"繁体中文",code:"Tw",placeholder:"請輸入模板內容（繁體中文）"},{label:"英文",code:"En",placeholder:"Please input template content."}],iccid:"iccid",position:"position",columns:[{type:"selection",minWidth:60,align:"center"},{title:"套餐名称",key:"nameCn",minWidth:150,align:"center",tooltip:!0},{title:"套餐ID",key:"id",minWidth:150,align:"center",tooltip:!0}],unAvailableColumns:[{title:"套餐ID",key:"id",minWidth:150,align:"center",tooltip:!0},{title:"套餐名称",key:"nameCn",minWidth:150,align:"center",tooltip:!0}],data:[],unAvailableData:[],notFoundData:[],modal1:!1,modal2:!1,modal3:!1,modal4:!1,modal5:!1,tableLoading:!1,uploadLoading:!1,sumbitLoading:!1,searchExoprtLoading:!1,downLoading:!1,spinShow:!1,ruleValidate:{templateName:[{required:!0,validator:function(t,e,a){var n=e?e.trim():"";""===n?a(new Error("模板名称不能为空或仅包含空格")):a()}}],templateRule:[{required:!0,validator:n}]},mccValidate:{country:[{required:!0,message:"请选择适用国家/地区"}]},packageValidate:{applyPackageType:[{required:!0,message:"请选择"}],packageName:[{validator:a}],packageId:[{validator:a}],mcc:[{validator:a}],corpId:[{validator:a}]},ruleobj:{file:[{required:!0,validator:e,trigger:"change"}]},countOfRender:0}},mounted:function(){this.templateId=this.$route.query.id,this.pageType="Add"==this.$route.query.type?"1":"2","2"==this.pageType&&(this.loadTemplates(),this.initAreaList()),this.getCountryList(),this.getCorpList(),this.getContinentMcc()},methods:{sumbit:function(){var t=this;this.$refs["formValidate"].validate((function(e){if(e){if(0==t.countryOptions.length)return void t.$Message["warning"]({background:!0,content:"请添加适用国家/地区！",duration:3});if("4"==t.applyPackageTypeCopy&&0===Object.keys(t.packageOptions).length||!t.applyPackageTypeCopy)return void t.$Message["warning"]({background:!0,content:"请添加适用套餐！",duration:3});t.sumbitLoading=!0;var a=t.formValidate,n=a.contentCn,o=a.contentEn,i=a.contentTw,c=a.templateName,r=new FormData;if("4"==t.applyPackageTypeCopy){var s=t.packageOptions.reduce((function(t,e){return t[e.id]=e.isChannelCreate,t}),{});console.log(s,"适用套餐-提交");var l=JSON.stringify(s),p=new Blob([l],{type:"application/json"}),d=new FileReader;d.onload=function(t){console.log(t.target.result)},d.readAsText(p),r.append("packageMapFile",p)}"2"==t.pageType&&r.append("id",t.templateId.toString()),r.append("contentCn",n),r.append("contentEn",o),r.append("contentTw",i),r.append("templateName",c),r.append("applyPackageType",t.applyPackageTypeCopy),r.append("mccList",t.countryOptions.map((function(t){return t.mcc})));var f="1"==t.pageType?u["a"]:u["c"];f(r).then((function(e){if(!e||"0000"!=e.code)throw e;setTimeout((function(){t.$Notice.success({title:"操作提醒：",desc:e.msg}),t.$router.push({name:"areaWelcomeSMSIndex"})}),1500)})).catch((function(t){})).finally((function(){t.sumbitLoading=!1}))}}))},reback:function(){this.$router.push({name:"areaWelcomeSMSIndex"})},setInputFocus:function(t){this.focusedInputIndex=t},insertVariable:function(t,e){var a="content".concat(this.languages[t].code),n=this.formValidate[a],o=this.$refs.textInputs[t].$el.querySelector("textarea"),i=o.selectionStart,c=n.slice(0,i)+"{".concat(e,"}")+n.slice(i);this.$set(this.formValidate,a,c),this.$nextTick((function(){var t=i+"{".concat(e,"}").length+1,a=c.length,n=Math.min(t,a);o.focus(),o.setSelectionRange(n,n)}))},removeCountry:function(t){this.countryOptions.splice(t,1)},clearAllCountry:function(){var t=this;0!=this.countryOptions.length&&this.$Modal.confirm({title:"确认清空所有国家？",onOk:function(){t.countryOptions=[]}})},closeModal1:function(){this.country=[],this.$refs["mccObj"].resetFields(),this.modal1=!1,this.modal2=!1,this.$refs["packageObj"].resetFields(),this.selectAll=!1,this.modal3=!1,this.selectionList=[],this.data=[],this.total=0,this.page=1,this.packageObj.packageName="",this.packageObj.packageId="",this.packageObj.mcc="",this.packageObj.corpId=""},closeModal4:function(){this.modal4=!1},addCountry:function(){this.mccObj.country=this.countryOptions.map((function(t){return t.mcc})),this.modal1=!0},sumbitModal1:function(){var t=this;this.$refs["mccObj"].validate((function(e){if(!e)return!1;t.countryOptions=[];var a=t.mccObj.country.map((function(e){return t.countryList.find((function(t){return t.mcc===e}))})).filter((function(t){return void 0!==t}));t.countryOptions=a,t.modal1=!1}))},batchAddCountry:function(){var t=this;this.selectedCountries=[],this.selectedCountriesMap={},this.countryOptions.forEach((function(e){t.$set(t.selectedCountriesMap,e.mcc,!0),t.selectedCountries.some((function(t){return t.mcc===e.mcc}))||t.selectedCountries.push(Object(r["a"])({},e))})),this.modal2=!0},isCountryPreselected:function(t){return!0},toggleCountry:function(t,e){if(this.$set(this.selectedCountriesMap,e.mcc,t),t)this.selectedCountries.push(e);else{var a=this.selectedCountries.findIndex((function(t){return t.mcc===e.mcc}));-1!==a&&this.selectedCountries.splice(a,1)}},toggleSelectAll:function(t){var e=this,a=this.continentsData[t],n=this.isAllSelected(t),o=!n;this.selectedCountries=this.selectedCountries.filter((function(t){return!a.some((function(e){return e.mcc===t.mcc}))})),a.forEach((function(t){e.$set(e.selectedCountriesMap,t.mcc,!1)})),o&&a.forEach((function(t){e.$set(e.selectedCountriesMap,t.mcc,!0),e.selectedCountries.push(Object(r["a"])({},t))}))},isAllSelected:function(t){var e=this,a=this.continentsData[t];return a.every((function(t){return!0===e.selectedCountriesMap[t.mcc]}))},isIndeterminate:function(t){var e=this,a=this.continentsData[t]||[],n=a.filter((function(t){return e.selectedCountriesMap[t.mcc]})).length;return n>0&&n<a.length},sumbitModal2:function(){if(0===this.selectedCountries.length)return this.$Message.warning("请至少选择一个国家");this.countryOptions=JSON.parse(JSON.stringify(this.selectedCountries)),this.modal2=!1},removePackage:function(t){this.packageOptions.splice(t,1),this.tempPackageOptions.splice(t,1)},clearAllPackage:function(){var t=this;0!=this.packageOptions.length&&this.$Modal.confirm({title:"确认清空所有套餐？",onOk:function(){t.packageOptions=[],t.tempPackageOptions=[]}})},updatePackage:function(){var t=this;this.packageObj.applyPackageType=this.applyPackageTypeCopy,"4"==this.packageObj.applyPackageType&&(this.selectionList=JSON.parse(JSON.stringify(this.packageOptions)),this.selectionList.forEach((function(e){t.data.forEach((function(a){a.id==e.id&&t.$set(a,"_checked",!0)}))}))),this.modal3=!0},handleRowChange:function(t){var e=this;this.selection=t,t.map((function(t,a){var n=!0;e.selectionList.map((function(e,a){t.id===e.id&&(n=!1)})),n&&e.selectionList.push(t)}))},cancelPackage:function(t,e){var a=this;this.selectionList.forEach((function(t,n){t.id===e.id&&a.selectionList.splice(n,1)})),this.selectAll=!1},cancelPackageAll:function(t,e){var a=this;this.selectionList=this.selectionList.filter((function(t){return!a.data.some((function(e){return e.id===t.id}))})),this.data.forEach((function(t){a.$set(t,"_checked",!1)})),this.selectAll=!1},handleAllChange:function(t){var e=this;t?(this.tableLoading=!0,this.$refs["packageObj"].validate((function(t){if(t){var a=e;Object(u["k"])({packageNameCn:e.packageObj.packageName,packageId:e.packageObj.packageId,mcc:e.packageObj.mcc,corpId:e.packageObj.corpId,page:-1,pageSize:-1}).then((function(t){if(t.data instanceof Blob){var n=new FileReader;n.onload=function(t){var e=t.target.result;try{var n=JSON.parse(e);a.handleParsedData3(n)}catch(o){console.error("Failed to parse JSON:",o)}},n.readAsText(t.data)}else if(t.data instanceof ArrayBuffer)new Blob([new Uint8Array(t.data)],{type:"text/plain"});e.tableLoading=!1}))}}))):(this.selectionList=[],this.data.forEach((function(t){e.$set(t,"_checked",!1)})))},handleParsedData3:function(t){var e=this,a=JSON.parse(JSON.stringify(this.selectionList));this.selectionList=this.mergeAndDeduplicate(t.records,a,"id"),this.data.forEach((function(t){e.$set(t,"_checked",!0)}))},mergeAndDeduplicate:function(t,e,a){var n=[],o={};function i(t){t.forEach((function(t){o[t[a]]||(o[t[a]]=!0,n.push(t))}))}return i(t),i(e),n},getPackageList:function(t){var e=this;this.tableLoading=!0,Object(u["k"])({packageNameCn:this.packageObj.packageName,packageId:this.packageObj.packageId,mcc:this.packageObj.mcc,corpId:this.packageObj.corpId,page:t,pageSize:10}).then((function(a){if(a.data instanceof Blob){var n=new FileReader;n.onload=function(a){var n=a.target.result;try{var o=JSON.parse(n);e.handleParsedData2(t,o)}catch(i){console.error("Failed to parse JSON:",i)}},n.readAsText(a.data)}else if(a.data instanceof ArrayBuffer)new Blob([new Uint8Array(a.data)],{type:"text/plain"})})).catch((function(t){})).finally((function(){e.tableLoading=!1,e.searchExoprtLoading=!1}))},handleParsedData2:function(t,e){var a=this,n=e.records,o=[];n.map((function(t,e){o.push(t)})),this.selectionList.forEach((function(t){e.records.forEach((function(e){e.id==t.id&&a.$set(e,"_checked",!0)}))})),this.page=t,this.total=Number(e.total),this.data=o,this.selectionList.length==this.total?this.selectAll=!0:this.selectAll=!1},loadByPage:function(t){var e=this;this.$refs["packageObj"].validate((function(a){a&&e.getPackageList(t)}))},searchPackage:function(){var t=this;this.$refs["packageObj"].validate((function(e){e&&(t.searchExoprtLoading=!0,t.getPackageList(1))}))},exportPackage:function(){this.modal4=!0},handleBeforeUpload:function(t,e){var a=t.size/1024/1024>10;return a?(this.$Notice.warning({title:"操作提示",desc:"超过了最大限制范围10MB"}),!1):(this.file=t,this.uploadList=e,!1)},fileUploading:function(t,e,a){this.message="文件上传中、待进度条消失后再操作"},fileSuccess:function(t,e,a){this.message="请先下载模板文件，并按格式填写后上传"},handleError:function(t,e){var a=this;setTimeout((function(){a.uploading=!1,a.$Notice.warning({title:"错误提示",desc:"上传失败！"})}),3e3)},removeFile:function(){this.file=""},downloadFile:function(){var t=this;this.downLoading=!0,Object(u["j"])().then((function(e){var a=e.data,n="地区欢迎短信适用套餐导入模板.xlsx";if("download"in document.createElement("a")){var o=t.$refs.downloadLink,i=URL.createObjectURL(a);o.download=n,o.href=i,o.click(),URL.revokeObjectURL(i)}else navigator.msSaveBlob(a,n)})).catch((function(t){console.error(t)})).finally((function(){t.downLoading=!1}))},sumbitModal3:function(){var t=this;if(this.packageObj.applyPackageType){var e=this.packageObj.applyPackageType;if("4"===e){var a=this.selectionList.length;a<1?this.$Message.warning("请至少选择一条自选套餐"):(this.packageOptions=JSON.parse(JSON.stringify(this.selectionList)),this.loop(!0),this.applyPackageTypeCopy=this.packageObj.applyPackageType,this.showPackageType=!1,this.closeModal1())}else this.$refs["packageObj"].validate((function(e){e&&(t.packageTypeShow="1"==t.packageObj.applyPackageType?"全部套餐":"2"==t.packageObj.applyPackageType?"全部CMI套餐":"3"==t.packageObj.applyPackageType?"全部渠道自建套餐":"",t.applyPackageTypeCopy=t.packageObj.applyPackageType,t.showPackageType=!0,t.closeModal1())}))}else this.$refs["packageObj"].validate((function(t){}))},sumbitModal4:function(){var t=this;this.$refs["formobj"].validate((function(e){if(e){var a=new FormData;a.append("file",t.file),t.uploadLoading=!0;var n=t;Object(u["m"])(a).then((function(t){if(t.data instanceof Blob){var e=new FileReader;e.onload=function(t){var e=t.target.result;try{var a=JSON.parse(e);n.handleParsedData(a)}catch(o){console.error("Failed to parse JSON:",o)}},e.readAsText(t.data)}else if(t.data instanceof ArrayBuffer)new Blob([new Uint8Array(t.data)],{type:"text/plain"})})).catch((function(t){console.error("Upload failed:",t)})).finally((function(){n.uploadLoading=!1}))}}))},handleParsedData:function(t){var e=t.unAvailablePackages,a=t.notFoundPackages,n=t.availablePackages;if(this.unAvailableData=[],this.notFoundData="",this.packageOptions=Object(c["a"])(this.packageOptions),this.unAvailableDataTip="",this.notFoundTip="",Array.isArray(e)&&(e.length>50?(this.unAvailableData=e.slice(0,50),this.unAvailableDataTip="数据量过大，仅截取并展示前50条记录"):this.unAvailableData=e),Array.isArray(a)){var o=a.length>50?a.slice(0,50):a;this.notFoundData=o.join(", "),a.length>50&&(this.notFoundTip="数据量过大，仅截取并展示前50条记录")}var i=new Set(this.packageOptions.map((function(t){return t.id}))),r=n.filter((function(t){return!i.has(t.id)}));this.packageOptions=[].concat(Object(c["a"])(this.packageOptions),Object(c["a"])(r)),this.applyPackageTypeCopy=this.packageObj.applyPackageType,this.showPackageType="4"!==this.applyPackageTypeCopy;var s=e.length>0||a.length>0;s?this.modal5=!0:(this.closeModal1(),this.modal4=!1,this.loop(!0)),this.file=""},closeModal5:function(){this.modal3=!1,this.modal4=!1,this.modal5=!1,this.loop()},loadTemplates:function(){var t=this,e={id:this.templateId,current:-1,size:-1};Object(u["f"])(e).then((function(e){if("0000"===e.code){var a=e.paging.data[0].applyPackageType;"4"==a?(t.showPackageType=!1,t.loadPackagesForTemplate()):(t.showPackageType=!0,t.packageTypeShow="1"==a?"全部套餐":"2"==a?"全部CMI套餐":"3"==a?"全部渠道自建套餐":""),t.applyPackageTypeCopy=e.paging.data[0].applyPackageType,t.formValidate.templateName=e.paging.data[0].templateName,t.formValidate.contentCn=e.paging.data[0].contentCn,t.formValidate.contentEn=e.paging.data[0].contentEn,t.formValidate.contentTw=e.paging.data[0].contentTw}})).catch((function(t){console.error(t)})).finally((function(){}))},initAreaList:function(){var t=this;Object(u["e"])({id:this.templateId,current:-1,size:-1}).then((function(e){"0000"===e.code&&(t.countryOptions=e.paging.data)}))},loadPackagesForTemplate:function(){var t=this,e=this;this.spinShow=!0,Object(u["d"])({id:this.templateId}).then((function(a){if(a.data instanceof Blob){var n=new FileReader;n.onload=function(t){var a=t.target.result;try{e.packageOptions=JSON.parse(a),console.log(a,"text"),console.log(e.packageOptions,"适用套餐-回显"),e.loop()}catch(n){console.error("Failed to parse JSON:",n)}},n.readAsText(a.data)}else if(a.data instanceof ArrayBuffer)new Blob([new Uint8Array(a.data)],{type:"text/plain"});t.spinShow=!1})).catch((function(e){t.spinShow=!1,console.error(e)})).finally((function(){}))},loop:function(t){var e=this;t&&(e.countOfRender=0),e.tempPackageOptions=[];var a=e.packageOptions.length-e.countOfRender,n=Math.min(100,a);if(n<=0)e.tempPackageOptions=e.packageOptions;else{for(var o=[],i=0;i<n;i++)o.push(e.packageOptions[e.countOfRender+i]);e.countOfRender+=n,e.tempPackageOptions=e.tempPackageOptions.concat(o)}},handleReachEdge:function(t){console.log("监听滚动条",t),this.loop()},getCountryList:function(){var t=this;Object(s["e"])().then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;t.continentList=a,t.continentList.sort((function(t,e){return t.countryEn.localeCompare(e.countryEn)}))})).catch((function(t){}))},getCorpList:function(){var t=this;Object(l["e"])({type:1,status:1,checkStatus:2}).then((function(e){if(!e||"0000"!=e.code)throw e;t.corpList=e.data})).catch((function(t){})).finally((function(){}))},getContinentMcc:function(){var t=this;Object(u["g"])().then((function(e){if(!e||"0000"!=e.code)throw e;t.continentsData=e.data;var a=[];for(var n in e.data)e.data.hasOwnProperty(n)&&(a=a.concat(e.data[n]));a.sort((function(t,e){var a=t.countryEn.charAt(0).toLowerCase(),n=e.countryEn.charAt(0).toLowerCase();return a<n?-1:a>n?1:0})),t.countryList=a})).catch((function(t){})).finally((function(){}))}}},d=p,f=(a("c2c9"),a("2877")),g=Object(f["a"])(d,o,i,!1,null,"7685c984",null);e["default"]=g.exports},c2c9:function(t,e,a){"use strict";a("ce6c")},ce6c:function(t,e,a){}}]);