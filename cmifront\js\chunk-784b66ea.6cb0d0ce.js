(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-784b66ea"],{"00b4":function(t,e,r){"use strict";r("ac1f");var a=r("23e7"),s=r("c65b"),o=r("1626"),l=r("825a"),i=r("577e"),n=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),d=/./.test;a({target:"RegExp",proto:!0,forced:!n},{test:function(t){var e=l(this),r=i(t),a=e.exec;if(!o(a))return s(d,e,r);var n=s(a,e,r);return null!==n&&(l(n),!0)}})},"19f0":function(t,e,r){},"1da9":function(t,e,r){"use strict";r.d(e,"f",(function(){return o})),r.d(e,"b",(function(){return l})),r.d(e,"e",(function(){return i})),r.d(e,"d",(function(){return n})),r.d(e,"c",(function(){return d})),r.d(e,"a",(function(){return m}));r("99af");var a=r("66df"),s="/rms/api/v1",o=function(t){return a["a"].request({url:s+"/cardfile/queryList",params:t,method:"get"})},l=function(t){return a["a"].request({url:s+"/cardfile/view/".concat(t),method:"get"})},i=function(t,e){return a["a"].request({url:s+"/cardfile/download/".concat(t,"/").concat(e),method:"get",responseType:"blob"})},n=function(t){return a["a"].request({url:s+"/cardfile/rollback/".concat(t),method:"get"})},d=function(t){return a["a"].request({url:s+"/cardfile/getDefaultContent",params:t,method:"get"})},m=function(t){return a["a"].request({url:s+"/cardfile/add",data:t,method:"post"})}},"28b2":function(t,e,r){"use strict";r.r(e);var a=function(){var t=this,e=t._self._c;return e("card",{staticStyle:{width:"100%",padding:"20px",display:"flex","justify-content":"space-evenly"}},[e("Form",{ref:"formObj",staticStyle:{"font-weight":"bold"},attrs:{model:t.formObj,rules:t.ruleInfoValidate,"label-width":190,"label-height":100}},[e("div",[e("Row",{attrs:{type:"flex",justify:"space-around"}},[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"选择供应商",prop:"supplierId"}},[e("Select",{staticStyle:{width:"330px"},attrs:{placeholder:"下拉选择供应商",clearable:""},on:{"on-change":t.getProviders},model:{value:t.formObj.supplierId,callback:function(e){t.$set(t.formObj,"supplierId",e)},expression:"formObj.supplierId"}},t._l(t.providers,(function(r,a){return e("Option",{key:a,attrs:{value:r.supplierId}},[t._v("\n\t\t\t\t\t\t\t"+t._s(r.supplierName)+"\n\t\t\t\t\t\t")])})),1)],1)],1),e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"选择卡类型",prop:"makeCardType"}},[e("Select",{staticStyle:{width:"330px"},attrs:{placeholder:"下拉选择卡类型",clearable:""},model:{value:t.formObj.makeCardType,callback:function(e){t.$set(t.formObj,"makeCardType",e)},expression:"formObj.makeCardType"}},[e("Option",{attrs:{value:1}},[t._v("普通卡")]),e("Option",{attrs:{value:2}},[t._v("省移动")]),e("Option",{attrs:{value:3}},[t._v("欧洲卡")])],1)],1)],1)],1),e("Row",{attrs:{justify:"center"}},[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"是否使用连续资源下拉框",prop:"isContinuous"}},[e("Select",{staticStyle:{width:"330px"},attrs:{placeholder:"是否连续资源",clearable:""},model:{value:t.formObj.isContinuous,callback:function(e){t.$set(t.formObj,"isContinuous",e)},expression:"formObj.isContinuous"}},t._l(t.isContinuousList,(function(r){return e("Option",{key:r.value,attrs:{value:r.value}},[t._v("\n\t\t\t\t\t\t\t"+t._s(r.label)+"\n\t\t\t\t\t\t")])})),1)],1)],1),e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"是否VIMSI制卡",prop:"isVimsi"}},[e("Select",{staticStyle:{width:"330px"},attrs:{placeholder:"是否VIMSI制卡",clearable:""},model:{value:t.formObj.isVimsi,callback:function(e){t.$set(t.formObj,"isVimsi",e)},expression:"formObj.isVimsi"}},[e("Option",{attrs:{value:1}},[t._v("是")]),e("Option",{attrs:{value:2}},[t._v("否")])],1)],1)],1)],1),e("Row",{staticStyle:{"margin-bottom":"30px"},attrs:{justify:"center"}},[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"是否需要GTP PROXY指定号码",prop:"gtpProxy"}},[e("Select",{staticStyle:{width:"330px"},attrs:{placeholder:"是否需要GTP PROXY指定号码",clearable:""},model:{value:t.formObj.gtpProxy,callback:function(e){t.$set(t.formObj,"gtpProxy",e)},expression:"formObj.gtpProxy"}},[e("Option",{attrs:{value:1}},[t._v("是")]),e("Option",{attrs:{value:2}},[t._v("否")])],1)],1)],1)],1),e("Row",{attrs:{justify:"center"}},[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"任务名称",prop:"taskName"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,placeholder:"请输入任务名称",clearable:""},model:{value:t.formObj.taskName,callback:function(e){t.$set(t.formObj,"taskName",e)},expression:"formObj.taskName"}})],1)],1),e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Customer",prop:"customer"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.customer,callback:function(e){t.$set(t.formObj,"customer",e)},expression:"formObj.customer"}})],1)],1)],1),e("Row",[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Quantity",prop:"quantity"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.quantity,callback:function(e){t.$set(t.formObj,"quantity",e)},expression:"formObj.quantity"}})],1)],1),e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Type",prop:"type"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.type,callback:function(e){t.$set(t.formObj,"type",e)},expression:"formObj.type"}})],1)],1)],1),e("Row",[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Batch",prop:"batch"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.batch,callback:function(e){t.$set(t.formObj,"batch",e)},expression:"formObj.batch"}})],1)],1),e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Transport_key",prop:"transportkey"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.transportkey,callback:function(e){t.$set(t.formObj,"transportkey",e)},expression:"formObj.transportkey"}})],1)],1)],1),e("Row",[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"OP_Key",prop:"opKey"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.opKey,callback:function(e){t.$set(t.formObj,"opKey",e)},expression:"formObj.opKey"}})],1)],1),e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"OTA_Transport_Key",prop:"otaTransportKey"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.otaTransportKey,callback:function(e){t.$set(t.formObj,"otaTransportKey",e)},expression:"formObj.otaTransportKey"}})],1)],1)],1),e("Row",[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Address1",prop:"address1"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.address1,callback:function(e){t.$set(t.formObj,"address1",e)},expression:"formObj.address1"}})],1)],1),e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Address2",prop:"address2"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.address2,callback:function(e){t.$set(t.formObj,"address2",e)},expression:"formObj.address2"}})],1)],1)],1),e("Row",[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Address3",prop:"address3"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.address3,callback:function(e){t.$set(t.formObj,"address3",e)},expression:"formObj.address3"}})],1)],1),e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Address4",prop:"address4"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.address4,callback:function(e){t.$set(t.formObj,"address4",e)},expression:"formObj.address4"}})],1)],1)],1),e("Row",[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Product_Code",prop:"productCode"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.productCode,callback:function(e){t.$set(t.formObj,"productCode",e)},expression:"formObj.productCode"}})],1)],1),e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"PO_Ref",prop:"poRef"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.poRef,callback:function(e){t.$set(t.formObj,"poRef",e)},expression:"formObj.poRef"}})],1)],1)],1),e("Row",[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Artwork",prop:"artwork"}},[e("Input",{staticStyle:{width:"330px"},attrs:{maxlength:50,clearable:!0},model:{value:t.formObj.artwork,callback:function(e){t.$set(t.formObj,"artwork",e)},expression:"formObj.artwork"}})],1)],1),e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"Roaming Exclusive List",prop:"roamingExclusiveList"}},[e("Input",{staticStyle:{width:"330px"},attrs:{placeholder:"贴片卡填写，可为空",maxlength:50,clearable:!0},model:{value:t.formObj.roamingExclusiveList,callback:function(e){t.$set(t.formObj,"roamingExclusiveList",e)},expression:"formObj.roamingExclusiveList"}})],1)],1)],1),e("Row",[e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{directives:[{name:"show",rawName:"v-show",value:"3"!=t.formObj.makeCardType,expression:"formObj.makeCardType != '3'"}],attrs:{label:"MSISDN",prop:"msisdn"}},[e("Input",{staticStyle:{width:"330px"},attrs:{placeholder:"如填写则从填写号码开始使用资源，为空则系统自动分配",clearable:!0},model:{value:t.formObj.msisdn,callback:function(e){t.$set(t.formObj,"msisdn",e)},expression:"formObj.msisdn"}})],1)],1),e("Col",{attrs:{md:24,lg:12}},[e("FormItem",{attrs:{label:"IMSI",prop:"imsi"}},[e("Input",{staticStyle:{width:"330px"},attrs:{placeholder:"如填写则从填写号码开始使用资源，为空则系统自动分配",clearable:!0},model:{value:t.formObj.imsi,callback:function(e){t.$set(t.formObj,"imsi",e)},expression:"formObj.imsi"}})],1)],1)],1)],1),e("div",{staticStyle:{"margin-top":"30px",width:"100%",display:"flex","align-items":"center","justify-content":"center"}},[e("Button",{attrs:{type:"primary",loading:t.generateLoading},on:{click:t.makeCard}},[t._v("生成Inputfile")]),t._v("      \n\t\t\t"),e("Button",{on:{click:t.back}},[t._v("返回")])],1)])],1)},s=[],o=(r("d9e2"),r("14d9"),r("e9c4"),r("b64b"),r("d3b7"),r("ac1f"),r("00b4"),r("c15a")),l=r("1da9"),i={data:function(){var t=function(t,e,r){var a=/^[0-9]*[1-9][0-9]*$/;a.test(e)?r():r(new Error("只支持正整数"))};return{updatetimeLoading:!1,generateLoading:!1,providers:[],isContinuousList:[{value:"true",label:"是"},{value:"false",label:"否"}],isVimsiList:[{value:"1",label:"是"},{value:"0",label:"否"}],formObj:{supplierId:"",makeCardType:"",isContinuous:"",isVimsi:"",gtpProxy:"",taskName:"",customer:"",quantity:"",type:"",batch:"",transportkey:"",opKey:"",otaTransportKey:"",address1:"",address2:"",address3:"",address4:"",productCode:"",poRef:"",artwork:"",roamingExclusiveList:"",msisdn:"",imsi:""},ruleInfoValidate:{supplierId:[{required:!0,message:"请选择供应商"}],makeCardType:[{required:!0,message:"请选择制卡类型"}],isContinuous:[{required:!0,message:"请选择是否连续资源"}],isVimsi:[{required:!0,message:"请选择是否vimsi制卡"}],gtpProxy:[{required:!0,message:"请选择是否需要GTP PROXY指定号码"}],taskName:[{required:!0,message:"请输入任务名称",trigger:"blur"}],customer:[{required:!0,message:"请输入客户名",trigger:"blur"}],quantity:[{required:!0,message:"请输入需分配主IMSI的数量"},{validator:t,trigger:"blur"}],type:[{required:!0,message:"请输入type",trigger:"blur"}],batch:[{required:!0,message:"请输入batch",trigger:"blur"}],transportkey:[{required:!0,message:"请输入transportKey",trigger:"blur"}],opKey:[{required:!0,message:"请输入opKey",trigger:"blur"}],otaTransportKey:[{required:!0,message:"请输入otaTransportKey",trigger:"blur"}],address1:[{required:!0,message:"请输入地址1",trigger:"blur"}],address2:[{required:!0,message:"请输入地址2",trigger:"blur"}],address3:[{required:!0,message:"请输入地址3",trigger:"blur"}],address4:[{required:!0,message:"请输入地址4",trigger:"blur"}],productCode:[{required:!0,message:"请输入产品编码",trigger:"blur"}],poRef:[{required:!0,message:"请输入poRef",trigger:"blur"}],artwork:[{required:!0,message:"请输入artwork",trigger:"blur"}]}}},mounted:function(){var t=this;this.getProviders(),Object(l["c"])().then((function(e){if("0000"!==e.code)throw e;t.formObj.customer=e.data["customer"],t.formObj.address1=e.data["address1"],t.formObj.address2=e.data["address2"],t.formObj.address3=e.data["address3"],t.formObj.address4=e.data["address4"],t.formObj.artwork=e.data["artwork"],t.formObj.productCode=e.data["productCode"],t.formObj.poRef=e.data["poRef"],t.formObj.type=e.data["type"]})),localStorage.setItem("searchObj",decodeURIComponent(this.$route.query.searchObj))},methods:{getProviders:function(){var t=this;Object(o["a"])().then((function(e){if(!e||"0000"!=e.code)throw e;t.providers=e.data})).catch((function(t){}))},makeCard:function(){var t=this;this.$refs["formObj"].validate((function(e){if(e){t.generateLoading=!0;var r=JSON.parse(JSON.stringify(t.formObj));Object(l["a"])(r).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.$router.push({name:"makeCardFile_mngr"}))})).finally((function(){t.generateLoading=!1}))}}))},back:function(){this.$router.push({path:"/makeCardFile"})}}},n=i,d=(r("7e48"),r("2877")),m=Object(d["a"])(n,a,s,!1,null,null,null);e["default"]=m.exports},"7e48":function(t,e,r){"use strict";r("19f0")},c15a:function(t,e,r){"use strict";r.d(e,"a",(function(){return o}));var a=r("66df"),s="/rms/api/v1",o=function(){return a["a"].request({url:s+"/supplier/query",method:"get"})}}}]);