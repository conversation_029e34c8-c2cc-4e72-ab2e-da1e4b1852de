(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8b1aa502"],{"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"1ec4":function(t,e,a){"use strict";a.r(e);a("ac1f"),a("841c"),a("498a");var r=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticStyle:{"margin-top":"80px",margin:"auto"}},[e("div",{staticStyle:{display:"flex","flex-wrap":"wrap","justify-content":"flex-start","align-items":"flex-start"}},[e("div",{staticStyle:{"margin-left":"20px","margin-top":"20px"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.$t("order.mealname"))+":")]),t._v("  \n          "),"zh-CN"===t.$i18n.locale?e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("order.input_mealname"),clearable:""},model:{value:t.PackageName,callback:function(e){t.PackageName="string"===typeof e?e.trim():e},expression:"PackageName"}}):t._e(),"en-US"===t.$i18n.locale?e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("order.input_mealname"),clearable:""},model:{value:t.PackageNameEn,callback:function(e){t.PackageNameEn=e},expression:"PackageNameEn"}}):t._e(),t._v("  \n        ")],1),e("div",{staticStyle:{"margin-left":"20px","margin-top":"20px"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.$t("order.input_number"))+":")]),t._v("  \n          "),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("order.chose_number"),prop:"showTitle",clearable:""},model:{value:t.iccid,callback:function(e){t.iccid="string"===typeof e?e.trim():e},expression:"iccid"}})],1),e("div",{staticStyle:{"margin-left":"20px","margin-top":"20px"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.$t("order.timeslot"))+":")]),t._v("  \n          "),e("DatePicker",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{type:"daterange",format:"yyyy-MM-dd",placement:"bottom-end",placeholder:t.$t("order.chose_time")},on:{"on-change":t.handleDateChange,"on-clear":t.hanldeDateClear},model:{value:t.time_slot,callback:function(e){t.time_slot=e},expression:"time_slot"}})],1),e("div",{staticStyle:{"margin-left":"20px","margin-top":"20px"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.$t("stock.attributableChannel"))+":")]),t._v("  \n          "),e("Select",{staticStyle:{width:"200px"},attrs:{clearable:""},model:{value:t.attributableChannel,callback:function(e){t.attributableChannel=e},expression:"attributableChannel"}},t._l(t.attributableChannelList,(function(a,r){return e("Option",{key:a.corpId,attrs:{value:a.corpId}},[t._v(t._s(a.corpName))])})),1)],1),e("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{margin:"0 2px","margin-left":"20px","margin-top":"20px"},attrs:{type:"success"},on:{click:function(e){return t.showbill()}}},[t._v(t._s(t.$t("order.monthly_bill")))]),t._v("  \n\t\t\t\t"),e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{margin:"0 2px","margin-left":"20px","margin-top":"20px"},attrs:{disabled:"3"==t.cooperationMode,type:"primary",loading:t.searchloading},on:{click:function(e){return t.search()}}},[e("Icon",{attrs:{type:"md-search"}}),t._v(t._s(t.$t("order.search")))],1),e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{margin:"0 2px","margin-left":"20px","margin-top":"20px"},attrs:{disabled:"3"==t.cooperationMode,type:"success",loading:t.downloading},on:{click:t.exportFile}},[e("Icon",{attrs:{type:"ios-cloud-download-outline"}}),t._v(t._s(t.$t("stock.exporttb"))+"\n\t\t\t\t")],1),e("Button",{directives:[{name:"has",rawName:"v-has",value:"trafficDetails",expression:"'trafficDetails'"}],staticStyle:{margin:"0 2px","margin-left":"20px","margin-top":"20px"},attrs:{disabled:"1"===t.cooperationMode||"3"==t.cooperationMode,icon:"md-search",type:"info"},on:{click:t.getTrafficDetails}},[e("Icon",{attrs:{type:"ios-cloud-download-outline"}}),t._v(t._s(t.$t("flowInfo"))+"\n\t\t\t\t")],1)],1),e("Table",{staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:1==t.fatherOrChild?t.columnsSon:t.columnsFather,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(a){var r=a.row;a.index;return[!1===r.isUsed&&"7"!=r.orderType?e("Button",{staticStyle:{"margin-right":"5px"},attrs:{type:"error",size:"small"},on:{click:function(e){return t.Delete(r)}}},[t._v(t._s(t.$t("order.unsubscribe")))]):t._e()]}}])}),e("div",{staticStyle:{"margin-top":"100px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v(t._s(t.$t("exportMS")))]),e("FormItem",{attrs:{label:t.$t("exportID")}},[e("span",{staticStyle:{width:"100px"}},[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:t.$t("exportFlie")}},[e("span",[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v(t._s(t.$t("downloadResult")))])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v(t._s(t.$t("Goto")))])],1)]),e("Modal",{attrs:{title:t.$t("flowInfo"),"footer-hide":!0,"mask-closable":!1,width:"900px"},on:{"on-cancel":t.cancelModal},model:{value:t.trafficDetailsFlage,callback:function(e){t.trafficDetailsFlage=e},expression:"trafficDetailsFlage"}},[e("div",{staticStyle:{padding:"0 16px"}},[e("div",{staticStyle:{width:"100%",display:"flex","justify-content":"space-evenly","align-items":"self-start"}},[e("Form",{ref:"form",staticStyle:{display:"flex","justify-content":"center","align-items":"center"},attrs:{model:t.trafficForm,rules:t.trafficRule,inline:"","label-width":110}},[e("FormItem",{attrs:{label:t.$t("fuelPack.SelectDate"),prop:"date"}},[e("DatePicker",{staticClass:"recordBtnSty",staticStyle:{width:"200px"},attrs:{type:"daterange",format:"yyyy-MM-dd",placement:"bottom-end",clearable:!0,placeholder:t.$t("fuelPack.SelectDate")},on:{"on-change":t.handleDateChange1,"on-clear":t.hanldeDateClear1},model:{value:t.trafficForm.date,callback:function(e){t.$set(t.trafficForm,"date",e)},expression:"trafficForm.date"}})],1),e("FormItem",{attrs:{label:t.$t("flow.SelectDestination"),prop:"localId"}},[e("Select",{staticStyle:{width:"200px"},attrs:{filterable:"",placeholder:t.$t("flow.SelectDestination"),clearable:!0},on:{"on-change":t.getLocalList},model:{value:t.trafficForm.localId,callback:function(e){t.$set(t.trafficForm,"localId",e)},expression:"trafficForm.localId"}},t._l(t.localList,(function(a){return e("Option",{key:a.id,attrs:{value:a.mcc}},[t._v("\n\t\t\t\t\t\t\t\t\t\t"+t._s(a.countryEn)+"\n\t\t\t\t\t\t\t\t\t")])})),1)],1)],1),e("div",{staticStyle:{display:"flex","justify-content":"center","align-items":"center"}},[e("Button",{attrs:{type:"primary",loading:t.searchTrafficLoading,icon:"md-search"},on:{click:function(e){return t.searchTraffic()}}},[t._v(t._s(t.$t("flow.select")))]),t._v("\n                    \n              "),e("Button",{attrs:{type:"info",loading:t.exportTrafficLoading,icon:"md-arrow-down"},on:{click:function(e){return t.exportTraffic()}}},[t._v(t._s(t.$t("order.exporttb")))])],1)],1),e("div",[e("Table",{attrs:{columns:t.columnsTraffic,data:t.trafficData,loading:t.trafficLoading}}),e("div",{staticStyle:{"margin-top":"40px"}},[e("Page",{attrs:{total:t.totalTraffic,current:t.currentPageTraffic,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPageTraffic=e},"on-change":t.goTrafficPage}})],1)],1),e("div",{staticStyle:{margin:"20px","text-align":"center"}},[e("Button",{on:{click:t.cancelModal}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("Icon",{attrs:{type:"ios-arrow-back"}}),t._v(" "+t._s(t.$t("support.back"))+"\n\t\t\t\t\t\t\t")],1)])],1)])]),e("Modal",{attrs:{"footer-hide":!0},model:{value:t.modal3,callback:function(e){t.modal3=e},expression:"modal3"}},[e("p",{staticStyle:{"margin-top":"20px",display:"flex","justify-content":"center","font-size":"16px","font-weight":"bold"}},[t._v("\n          "+t._s(t.$t("order.ifunsubscribe")+"?")+"\n        ")]),e("div",{staticStyle:{"margin-top":"30px",display:"flex","justify-content":"center"}},[e("Button",{attrs:{size:"small",type:"default"},on:{click:t.cancelModal}},[e("Icon",{attrs:{type:"ios-arrow-back"}}),t._v("\n            返回\n          ")],1),t._v("\n                \n          "),e("Button",{attrs:{size:"small",type:"success",loading:t.beSureLoading},on:{click:t.beSure}},[t._v("确定")])],1)])],1)])},n=[],i=a("3835"),o=(a("14d9"),a("4e82"),a("a9e3"),a("b680"),a("d3b7"),a("25f0"),a("6dfa")),c=a("e3b7"),l=a("66df"),s="/cms",d=function(t){return l["a"].request({url:s+"/channel/getIsSubChannel",params:t,method:"get"})},u=a("c70b"),f={data:function(){var t=this;return{cooperationMode:"",total:0,currentPage:1,totalTraffic:0,currentPageTraffic:1,corpId:"",searchBeginTime:"",searchEndTime:"",time_slot:"",page:0,taskId:"",taskName:"",PackageName:"",PackageNameEn:"",attributableChannel:"",iccid:"",loading:!1,searchloading:!1,downloading:!1,searchTrafficLoading:!1,exportTrafficLoading:!1,trafficLoading:!1,beSureLoading:!1,exportModal:!1,trafficDetailsFlage:!1,modal3:!1,form:{},trafficForm:{date:[],startTime:"",endTime:"",localId:""},localList:[],columnsSon:[{title:this.$t("order.order_number"),key:"orderId",minWidth:120,align:"center"},{title:this.$t("order.input_number"),key:"iccid",minWidth:120,align:"center"},{title:this.$t("order.mealname"),key:"packageName",align:"center",minWidth:140,render:function(e,a){var r=a.row,n="zh-CN"===t.$i18n.locale?r.packageName:"en-US"===t.$i18n.locale?r.packageNameEn:"";return e("label",{style:{"word-break":"break-word"}},n)}},{title:this.$t("order.channels"),key:"orderChannel",align:"center",minWidth:120,render:function(e,a){var r=a.row,n="102"==r.orderChannel?"API":"103"==r.orderChannel?t.$t("order.Website"):"104"==r.orderChannel?t.$t("order.BeijingMobile"):"105"==r.orderChannel?t.$t("order.BulkOrder"):"106"==r.orderChannel?t.$t("order.Trial"):"110"==r.orderChannel?t.$t("order.Testing"):"111"==r.orderChannel?t.$t("order.issuance"):"112"==r.orderChannel?t.$t("order.Postpaid"):"113"==r.orderChannel?"WEB":"114"==r.orderChannel?t.$t("order.Datapool"):"";return e("label",n)}},{title:this.$t("stock.attributableChannel"),key:"revertChannelName",align:"center",minWidth:120},{title:this.$t("order.order_state"),key:"orderStatus",align:"center",minWidth:120,render:function(e,a){var r=a.row,n="1"==r.orderStatus?t.$t("order.delivered"):"2"==r.orderStatus?t.$t("order.Completed"):"3"==r.orderStatus?t.$t("order.Cancelled"):"4"==r.orderStatus?t.$t("order.approval"):"5"==r.orderStatus?t.$t("order.Recycled"):"";return e("label",n)}},{title:this.$t("order.count"),key:"count",minWidth:120,align:"center"},{title:this.$t("order.order_money"),key:"subAmount",align:"center",minWidth:120,render:function(t,e){var a=e.row,r=parseFloat(u.divide(u.bignumber(a.subAmount),100).toFixed(2)).toString();return t("label",r)}},{title:this.$t("order.addtime"),key:"orderDate",minWidth:120,align:"center"},{title:this.$t("order.action"),slot:"action",minWidth:120,align:"center"}],columnsFather:[{title:this.$t("order.order_number"),key:"orderId",minWidth:120,align:"center"},{title:this.$t("order.input_number"),key:"iccid",minWidth:120,align:"center"},{title:this.$t("order.mealname"),key:"packageName",align:"center",minWidth:140,render:function(e,a){var r=a.row,n="zh-CN"===t.$i18n.locale?r.packageName:"en-US"===t.$i18n.locale?r.packageNameEn:"";return e("label",{style:{"word-break":"break-word"}},n)}},{title:this.$t("order.channels"),key:"orderChannel",align:"center",minWidth:120,render:function(e,a){var r=a.row,n="102"==r.orderChannel?"API":"103"==r.orderChannel?t.$t("order.Website"):"104"==r.orderChannel?t.$t("order.BeijingMobile"):"105"==r.orderChannel?t.$t("order.BulkOrder"):"106"==r.orderChannel?t.$t("order.Trial"):"110"==r.orderChannel?t.$t("order.Testing"):"111"==r.orderChannel?t.$t("order.issuance"):"112"==r.orderChannel?t.$t("order.Postpaid"):"113"==r.orderChannel?"WEB":"114"==r.orderChannel?t.$t("order.Datapool"):"";return e("label",n)}},{title:this.$t("stock.attributableChannel"),key:"revertChannelName",align:"center",minWidth:120},{title:this.$t("order.order_state"),key:"orderStatus",align:"center",minWidth:120,render:function(e,a){var r=a.row,n="1"==r.orderStatus?t.$t("order.delivered"):"2"==r.orderStatus?t.$t("order.Completed"):"3"==r.orderStatus?t.$t("order.Cancelled"):"4"==r.orderStatus?t.$t("order.approval"):"5"==r.orderStatus?t.$t("order.Recycled"):"";return e("label",n)}},{title:this.$t("order.count"),key:"count",minWidth:120,align:"center"},{title:this.$t("order.order_money"),key:"amount",align:"center",minWidth:120,render:function(t,e){var a=e.row,r=parseFloat(u.divide(u.bignumber(a.amount),100).toFixed(2)).toString();return t("label",r)}},{title:this.$t("order.channelOrderMoney"),key:"subAmount",align:"center",minWidth:170,render:function(t,e){var a=e.row,r=parseFloat(u.divide(u.bignumber(a.subAmount),100).toFixed(2)).toString(),n=a.subAmount?r:"";return t("label",n)}},{title:this.$t("order.addtime"),key:"orderDate",minWidth:120,align:"center"},{title:this.$t("order.action"),slot:"action",minWidth:120,align:"center"}],columnsTraffic:[{title:this.$t("support.date"),key:"date",align:"center",minWidth:100,tooltip:!0},{title:this.$t("buymeal.Country"),key:"countryOrRegion",align:"center",minWidth:100,tooltip:!0},{title:this.$t("flow.usageMB"),key:"usedTraffic",align:"center",minWidth:100,tooltip:!0},{title:this.$t("fuelPack.Amount"),key:"amount",align:"center",minWidth:100,tooltip:!0}],data:[],trafficData:[],rules:{},trafficRule:{},attributableChannelList:[],fatherOrChild:!1,rowData:{}}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),"3"!=this.cooperationMode&&(this.getFatherOrChildChannel(),this.getAttributableChannelList(),this.getLocalList())},methods:{goPageFirst:function(t){var e=this;if(this.PackageName||this.PackageNameEn||this.iccid||this.searchBeginTime||this.searchEndTime||this.attributableChannel){this.searchloading=!0,this.loading=!0;var a=this;Object(o["F"])({userName:this.$store.state.user.userName}).then((function(r){if("0000"==r.code){var n=r.data;e.corpId=n;var i=e.cooperationMode,c=t,l=10,s=""===e.searchBeginTime?null:e.searchBeginTime+" 00:00:00",d=""===e.searchEndTime?null:e.searchEndTime+" 23:59:59",u=""===e.iccid?null:e.iccid,f=""===e.attributableChannel?null:e.attributableChannel,h=null,p=null;"zh-CN"===e.$i18n.locale&&(h=e.PackageName),"en-US"===e.$i18n.locale&&(p=e.PackageNameEn),Object(o["C"])({pageNumber:c,pageSize:l,startDate:s,endDate:d,corpId:n,iccid:u,packageName:h,packageNameEn:p,revertCorpId:f,cooperationMode:i}).then((function(r){"0000"==r.code&&(a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.total=r.data.total,e.data=r.data.records)})).catch((function(t){console.error(t)})).finally((function(){e.loading=!1,e.searchloading=!1}))}})).catch((function(t){console.error(t)})).finally((function(){}))}else this.total=0,this.data=[],this.$Message["warning"]({background:!0,content:this.$t("support.searchcondition")})},showbill:function(){this.$router.push({path:"/bill"})},goPage:function(t){this.goPageFirst(t)},search:function(){this.goPageFirst(1)},exportFile:function(){var t=this;if(this.PackageName||this.PackageNameEn||this.iccid||this.searchBeginTime||this.searchEndTime||this.attributableChannel){this.downloading=!0;var e=""===this.searchBeginTime?null:this.searchBeginTime+" 00:00:00",a=""===this.searchEndTime?null:this.searchEndTime+" 23:59:59",r=""===this.iccid?null:this.iccid,n=""===this.attributableChannel?null:this.attributableChannel,i=null,c=null;"zh-CN"===this.$i18n.locale&&(i=this.PackageName),"en-US"===this.$i18n.locale&&(c=this.PackageNameEn),Object(o["B"])({corpId:this.corpId,startDate:e,endDate:a,iccid:r,packageName:i,packageNameEn:c,revertCorpId:n,userId:this.corpId,cooperationMode:this.cooperationMode,isSubChannel:this.fatherOrChild}).then((function(e){t.exportModal=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName,t.downloading=!1})).catch((function(t){console.log(t)})).finally((function(){t.downloading=!1}))}else this.$Message["warning"]({background:!0,content:this.$t("support.searchcondition")})},cancelModal:function(){this.trafficDetailsFlage=!1,this.exportModal=!1,this.modal3=!1,this.trafficForm.date="",this.trafficForm.startTime="",this.trafficForm.endTime="",this.trafficForm.localId=""},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName),corpId:encodeURIComponent(this.corpId)}}),this.exportModal=!1},Delete:function(t){this.modal3=!0,this.rowData=t},beSure:function(){var t=this;this.beSureLoading=!0;var e=this.corpId;Object(o["b"])(e,{amount:this.rowData.amount,count:this.rowData.count,currencyCode:this.rowData.currencyCode,effectiveDay:this.rowData.effectiveDay,isUsed:this.rowData.isUsed,msisdn:this.rowData.msisdn,orderChannel:this.rowData.orderChannel,orderDate:this.rowData.orderDate,orderId:this.rowData.orderId,orderStatus:this.rowData.orderStatus,packageName:this.rowData.packageName,packageStatus:this.rowData.packageStatus,packageUniqueId:this.rowData.packageUniqueId}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("order.Unsubscribe")}),t.modal3=!1,t.goPageFirst(1)})).catch((function(t){console.log(t)})).finally((function(){t.beSureLoading=!1}))},hanldeDateClear:function(){this.searchBeginTime="",this.searchEndTime=""},handleDateChange:function(t){var e=this.time_slot[0]||"",a=this.time_slot[1]||"";if(""!=e&&""!=a){var r=Object(i["a"])(t,2);this.searchBeginTime=r[0],this.searchEndTime=r[1]}},getTrafficDetails:function(){this.trafficDetailsFlage=!0,this.goTrafficPageFirst(1)},handleDateChange1:function(t){Array.isArray(t)&&(this.trafficForm.startTime=t[0],this.trafficForm.endTime=t[1])},hanldeDateClear1:function(){this.trafficForm.startTime="",this.trafficForm.endTime=""},getLocalList:function(){var t=this;Object(o["A"])().then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;t.localList=a,t.localList.sort((function(t,e){return t.countryEn.localeCompare(e.countryEn)}))})).catch((function(t){})).finally((function(){}))},goTrafficPage:function(t){this.goTrafficPageFirst(t)},searchTraffic:function(){this.searchTrafficLoading=!0,this.goTrafficPageFirst(1)},exportTraffic:function(){var t=this;this.exportTrafficLoading=!0,Object(c["q"])({beginDate:""===this.trafficForm.startTime?null:this.trafficForm.startTime,endDate:""===this.trafficForm.endTime?null:this.trafficForm.endTime,country:""===this.trafficForm.localId?null:this.trafficForm.localId,corpId:this.corpId}).then((function(e){t.exportModal=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName,t.exportTrafficLoading=!1,t.trafficForm.startTime="",t.trafficForm.endTime="",t.trafficForm.localId=""})).catch((function(e){console.error(e),t.exportTrafficLoading=!1})).finally((function(){}))},goTrafficPageFirst:function(t){var e=this;this.trafficLoading=!0;var a=this;Object(o["K"])({beginDate:""===this.trafficForm.startTime?null:this.trafficForm.startTime,endDate:""===this.trafficForm.endTime?null:this.trafficForm.endTime,country:""===this.trafficForm.localId?null:this.trafficForm.localId,corpId:this.corpId,pageNum:t,pageSize:10}).then((function(r){"0000"==r.code&&(a.loading=!1,e.page=t,e.currentPageTraffic=t,e.totalTraffic=Number(r.count),e.trafficData=r.data,e.searchTrafficLoading=!1,e.trafficLoading=!1)})).catch((function(t){console.error(t),e.trafficLoading=!1,e.searchTrafficLoading=!1})).finally((function(){}))},getAttributableChannelList:function(){var t=this;Object(o["F"])({userName:this.$store.state.user.userName}).then((function(e){t.corpId=e.data,Object(o["q"])({corpId:t.corpId,selfContain:!0}).then((function(e){if("0000"===e.code){if(1==t.cooperationMode)t.attributableChannelList=e.data;else{var a=[{corpName:e.data[0].corpName,corpId:e.data[0].corpId}];t.attributableChannelList=a}1==t.attributableChannelList.length&&(t.attributableChannel=t.attributableChannelList[0].corpId)}})).catch((function(t){console.log(t)}))}))},getFatherOrChildChannel:function(){var t=this;d({corpId:sessionStorage.getItem("corpId")}).then((function(e){"0000"==e.code&&(t.fatherOrChild=e.data)})).catch((function(t){console.error(t)})).finally((function(){}))}}},h=f,p=a("2877"),m=Object(p["a"])(h,r,n,!1,null,null,null);e["default"]=m.exports},"3f7e":function(t,e,a){"use strict";var r=a("b5db"),n=r.match(/firefox\/(\d+)/i);t.exports=!!n&&+n[1]},"4e82":function(t,e,a){"use strict";var r=a("23e7"),n=a("e330"),i=a("59ed"),o=a("7b0b"),c=a("07fa"),l=a("083a"),s=a("577e"),d=a("d039"),u=a("addb"),f=a("a640"),h=a("3f7e"),p=a("99f4"),m=a("1212"),g=a("ea83"),b=[],v=n(b.sort),y=n(b.push),k=d((function(){b.sort(void 0)})),x=d((function(){b.sort(null)})),w=f("sort"),$=!d((function(){if(m)return m<70;if(!(h&&h>3)){if(p)return!0;if(g)return g<603;var t,e,a,r,n="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(r=0;r<47;r++)b.push({k:e+r,v:a})}for(b.sort((function(t,e){return e.v-t.v})),r=0;r<b.length;r++)e=b[r].k.charAt(0),n.charAt(n.length-1)!==e&&(n+=e);return"DGBEFHACIJK"!==n}})),C=k||!x||!w||!$,S=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:s(e)>s(a)?1:-1}};r({target:"Array",proto:!0,forced:C},{sort:function(t){void 0!==t&&i(t);var e=o(this);if($)return void 0===t?v(e):v(e,t);var a,r,n=[],s=c(e);for(r=0;r<s;r++)r in e&&y(n,e[r]);u(n,S(t)),a=c(n),r=0;while(r<a)e[r]=n[r++];while(r<s)l(e,r++);return e}})},"841c":function(t,e,a){"use strict";var r=a("c65b"),n=a("d784"),i=a("825a"),o=a("7234"),c=a("1d80"),l=a("129f"),s=a("577e"),d=a("dc4a"),u=a("14c3");n("search",(function(t,e,a){return[function(e){var a=c(this),n=o(e)?void 0:d(e,t);return n?r(n,e,a):new RegExp(e)[t](s(a))},function(t){var r=i(this),n=s(t),o=a(e,r,n);if(o.done)return o.value;var c=r.lastIndex;l(c,0)||(r.lastIndex=0);var d=u(r,n);return l(r.lastIndex,c)||(r.lastIndex=c),null===d?-1:d.index}]}))},"99f4":function(t,e,a){"use strict";var r=a("b5db");t.exports=/MSIE|Trident/.test(r)},addb:function(t,e,a){"use strict";var r=a("f36a"),n=Math.floor,i=function(t,e){var a=t.length;if(a<8){var o,c,l=1;while(l<a){c=l,o=t[l];while(c&&e(t[c-1],o)>0)t[c]=t[--c];c!==l++&&(t[c]=o)}}else{var s=n(a/2),d=i(r(t,0,s),e),u=i(r(t,s),e),f=d.length,h=u.length,p=0,m=0;while(p<f||m<h)t[p+m]=p<f&&m<h?e(d[p],u[m])<=0?d[p++]:u[m++]:p<f?d[p++]:u[m++]}return t};t.exports=i},b680:function(t,e,a){"use strict";var r=a("23e7"),n=a("e330"),i=a("5926"),o=a("408a"),c=a("1148"),l=a("d039"),s=RangeError,d=String,u=Math.floor,f=n(c),h=n("".slice),p=n(1..toFixed),m=function(t,e,a){return 0===e?a:e%2===1?m(t,e-1,a*t):m(t*t,e/2,a)},g=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},b=function(t,e,a){var r=-1,n=a;while(++r<6)n+=e*t[r],t[r]=n%1e7,n=u(n/1e7)},v=function(t,e){var a=6,r=0;while(--a>=0)r+=t[a],t[a]=u(r/e),r=r%e*1e7},y=function(t){var e=6,a="";while(--e>=0)if(""!==a||0===e||0!==t[e]){var r=d(t[e]);a=""===a?r:a+f("0",7-r.length)+r}return a},k=l((function(){return"0.000"!==p(8e-5,3)||"1"!==p(.9,0)||"1.25"!==p(1.255,2)||"1000000000000000128"!==p(0xde0b6b3a7640080,0)}))||!l((function(){p({})}));r({target:"Number",proto:!0,forced:k},{toFixed:function(t){var e,a,r,n,c=o(this),l=i(t),u=[0,0,0,0,0,0],p="",k="0";if(l<0||l>20)throw new s("Incorrect fraction digits");if(c!==c)return"NaN";if(c<=-1e21||c>=1e21)return d(c);if(c<0&&(p="-",c=-c),c>1e-21)if(e=g(c*m(2,69,1))-69,a=e<0?c*m(2,-e,1):c/m(2,e,1),a*=4503599627370496,e=52-e,e>0){b(u,0,a),r=l;while(r>=7)b(u,1e7,0),r-=7;b(u,m(10,r,1),0),r=e-1;while(r>=23)v(u,1<<23),r-=23;v(u,1<<r),b(u,1,1),v(u,2),k=y(u)}else b(u,0,a),b(u,1<<-e,0),k=y(u)+f("0",l);return l>0?(n=k.length,k=p+(n<=l?"0."+f("0",l-n)+k:h(k,0,n-l)+"."+h(k,n-l))):k=p+k,k}})},e3b7:function(t,e,a){"use strict";a.d(e,"t",(function(){return o})),a.d(e,"s",(function(){return c})),a.d(e,"k",(function(){return l})),a.d(e,"u",(function(){return s})),a.d(e,"n",(function(){return d})),a.d(e,"p",(function(){return u})),a.d(e,"d",(function(){return f})),a.d(e,"a",(function(){return h})),a.d(e,"f",(function(){return p})),a.d(e,"x",(function(){return m})),a.d(e,"w",(function(){return g})),a.d(e,"v",(function(){return b})),a.d(e,"r",(function(){return v})),a.d(e,"A",(function(){return y})),a.d(e,"l",(function(){return k})),a.d(e,"m",(function(){return x})),a.d(e,"e",(function(){return w})),a.d(e,"z",(function(){return $})),a.d(e,"g",(function(){return C})),a.d(e,"j",(function(){return S})),a.d(e,"o",(function(){return I})),a.d(e,"i",(function(){return T})),a.d(e,"h",(function(){return _})),a.d(e,"y",(function(){return F})),a.d(e,"q",(function(){return D})),a.d(e,"c",(function(){return N})),a.d(e,"b",(function(){return P}));var r=a("66df"),n="pms",i="cms",o=function(t){return r["a"].request({url:i+"/channel/distributors/detail",data:t,method:"post"})},c=function(t){return r["a"].request({url:i+"/channel/distributors/info",params:t,method:"get"})},l=function(t){return r["a"].request({url:i+"/channel/distributors/delete",data:t,method:"delete"})},s=function(t){return r["a"].request({url:i+"/channel/distributors/record",data:t,method:"post"})},d=function(t){return r["a"].request({url:i+"/channel/distributors/record/export/"+t.corpId,method:"get",responseType:"blob"})},u=function(t){return r["a"].request({url:i+"/channel/distributors/remunerate/export",params:t,method:"post",responseType:"blob"})},f=function(t){return r["a"].request({url:i+"/channel/newChannel",data:t,method:"post"})},h=function(t){return r["a"].request({url:i+"/channel/updateChannel",data:t,method:"put"})},p=function(t){return r["a"].request({url:i+"/channel/approvalChannel",params:t,method:"put"})},m=function(t){return r["a"].request({url:n+"/packageGroup/queryPackageGroupRelation",params:t,method:"get"})},g=function(t){return r["a"].request({url:n+"/packageGroup/queryPackageGroupDetail",params:t,method:"get"})},b=function(t){return r["a"].request({url:n+"/packageGroup/purchasePart",params:t,method:"get"})},v=function(t){return r["a"].request({url:n+"/packageGroup/queryPackageList",params:t,method:"get"})},y=function(t){return r["a"].request({url:n+"/update",params:t,method:"put"})},k=function(t){return r["a"].request({url:n+"/packageGroup/deleteBatchPackageGroup",data:t,method:"delete"})},x=function(t){return r["a"].request({url:n+"/packageGroup/deletePackageGroup",params:t,method:"delete"})},w=function(t){return r["a"].request({url:n+"/packageGroup/newPackageGroup",data:t,method:"post"})},$=function(t){return r["a"].request({url:n+"/packageGroup/updatePackageGroup",data:t,method:"put"})},C=function(t){return r["a"].request({url:n+"/packageGroup/approvalPackageGroup",params:t,method:"put"})},S=function(t){return r["a"].request({url:n+"/packageGroup/create/byFile",data:t,method:"post",contentType:"multipart/form-data"})},I=function(t){return r["a"].request({url:n+"/packageGroup/packageGroupDetailExport",params:t,method:"get",responseType:"blob"})},T=function(t){return r["a"].request({url:i+"/channel/distributors/channelBill",data:t,method:"post"})},_=function(t){return r["a"].request({url:i+"/channel/distributors/channelBill/export",data:t,method:"post"})},F=function(t){return r["a"].request({url:i+"/channel/getCorpFlowDetail",params:t,method:"get"})},D=function(t){return r["a"].request({url:i+"/channel/corpFlowDetailExport",params:t,method:"get"})},N=function(t){return r["a"].request({url:i+"/channel/distributors/card/suspend",params:t,method:"get"})},P=function(t){return r["a"].request({url:i+"/channel/distributors/card/recover",params:t,method:"get"})}},ea83:function(t,e,a){"use strict";var r=a("b5db"),n=r.match(/AppleWebKit\/(\d+)\./);t.exports=!!n&&+n[1]}}]);