(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a1c5b23e"],{"466d":function(t,e,a){"use strict";var n=a("c65b"),o=a("d784"),s=a("825a"),i=a("7234"),r=a("50c4"),l=a("577e"),c=a("1d80"),u=a("dc4a"),d=a("8aa5"),p=a("14c3");o("match",(function(t,e,a){return[function(e){var a=c(this),o=i(e)?void 0:u(e,t);return o?n(o,e,a):new RegExp(e)[t](l(a))},function(t){var n=s(this),o=l(t),i=a(e,n,o);if(i.done)return i.value;if(!n.global)return p(n,o);var c=n.unicode;n.lastIndex=0;var u,h=[],m=0;while(null!==(u=p(n,o))){var f=l(u[0]);h[m]=f,""===f&&(n.lastIndex=d(o,r(n.lastIndex),c)),m++}return 0===m?null:h}]}))},"944d":function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return r})),a.d(e,"p",(function(){return l})),a.d(e,"q",(function(){return c})),a.d(e,"k",(function(){return u})),a.d(e,"l",(function(){return d})),a.d(e,"m",(function(){return p})),a.d(e,"n",(function(){return h})),a.d(e,"o",(function(){return m})),a.d(e,"r",(function(){return f})),a.d(e,"x",(function(){return b})),a.d(e,"j",(function(){return y})),a.d(e,"g",(function(){return v})),a.d(e,"f",(function(){return g})),a.d(e,"w",(function(){return x})),a.d(e,"s",(function(){return C})),a.d(e,"i",(function(){return _})),a.d(e,"e",(function(){return k})),a.d(e,"y",(function(){return I})),a.d(e,"t",(function(){return S})),a.d(e,"c",(function(){return j})),a.d(e,"u",(function(){return w})),a.d(e,"d",(function(){return M})),a.d(e,"v",(function(){return P})),a.d(e,"h",(function(){return N}));a("99af");var n=a("66df"),o="/stat",s="/order",i=function(t){return n["a"].request({url:o+"/otherCustomerIncome/query/page",data:t,method:"post"})},r=function(t){return n["a"].request({url:o+"/otherCustomerIncome/modified",data:t,method:"post",contentType:"multipart/form-data"})},l=function(t){return n["a"].request({url:o+"/otherCustomerIncome/export/flowSettleSummary",params:t,method:"get"})},c=function(t){return n["a"].request({url:o+"/otherCustomerIncome/export/flowUsedDetail",params:t,method:"get"})},u=function(t){return n["a"].request({url:o+"/billAdjust/downAdjustFile",params:t,method:"post",responseType:"blob"})},d=function(t){return n["a"].request({url:o+"/otherCustomerIncome/export/packageSettleSummary",params:t,method:"get"})},p=function(t){return n["a"].request({url:o+"/otherCustomerIncome/export/packageUsedDetail",params:t,method:"get"})},h=function(t){return n["a"].request({url:o+"/otherCustomerIncome/export/summaryFile",data:t,method:"post"})},m=function(t){return n["a"].request({url:o+"/otherCustomerIncome/export/taxation",params:t,method:"get"})},f=function(t){return n["a"].request({url:o+"/otherCustomerIncome/generateActualBill",data:t,method:"post",contentType:"multipart/form-data"})},b=function(t){return n["a"].request({url:o+"/otherCustomerIncome/queryHistoryBill",data:t,method:"post"})},y=function(t){return n["a"].request({url:o+"/otherCustomerIncome/export/bill",params:t,method:"get"})},v=function(t,e,a){return n["a"].request({url:o+"/invoice/createForOther/no/".concat(t,"/").concat(e,"/").concat(a),method:"post"})},g=function(t){return n["a"].request({url:o+"/invoice/createForOther",data:t,method:"post"})},x=function(t){return n["a"].request({url:o+"/otherCustomerIncome/auth",params:t,method:"post"})},C=function(t){return n["a"].request({url:o+"/billAdjust/searchAdjustInfo",data:t,method:"post"})},_=function(t){return n["a"].request({url:o+"/billAdjust/exportAdjustInfo",data:t,method:"post"})},k=function(t){return n["a"].request({url:o+"/billAdjust/batchExportInvoice",data:t,method:"post"})},I=function(t){return n["a"].request({url:o+"/billAdjust/downAdjustFile",params:t,method:"post",responseType:"blob"})},S=function(t){return n["a"].request({url:o+"/channelincome/month/getChannelBill",data:t,method:"post"})},j=function(t){return n["a"].request({url:o+"/billAdjust/addAdjustInfo",data:t,method:"post",contentType:"multipart/form-data"})},w=function(t){return n["a"].request({url:o+"/billAdjust/getPayOutReason",data:t,method:"post"})},M=function(t,e){return n["a"].request({url:s+"/blankCardOrder/packInvoice?corpId="+e,data:t,method:"post"})},P=function(t){return n["a"].request({url:o+"/channelincome/month/getA2zAmountByMonth",params:t,method:"get"})},N=function(t){return n["a"].request({url:o+"/channelincome/month/exportA2zAmountByMonth",params:t,method:"get",responseType:"blob"})}},"951d":function(t,e,a){"use strict";a.d(e,"g",(function(){return s})),a.d(e,"d",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"b",(function(){return l})),a.d(e,"a",(function(){return c})),a.d(e,"e",(function(){return u})),a.d(e,"f",(function(){return d}));var n=a("66df"),o="/cms/package/config",s=function(t){return n["a"].request({url:o+"/task/pageList",data:t,method:"post"})},i=function(t,e){return n["a"].request({url:o+"/task/download/".concat(t,"?status=")+e,method:"POST",responseType:"blob"})},r=function(t){return n["a"].request({url:o+"/task/rollback/".concat(t),method:"POST"})},l=function(t){return n["a"].request({url:o+"/task",data:t,method:"POST",contentType:"multipart/form-data"})},c=function(t){return n["a"].request({url:o+"/taskPage",data:t,method:"POST"})},u=function(t){return n["a"].request({url:"/cms/channel/searchList",data:t,method:"post"})},d=function(t){return n["a"].request({url:"/cms/package/config/getTextChannel",data:t,method:"get"})}},bfb4:function(t,e,a){"use strict";a("f195")},f195:function(t,e,a){},f8b6:function(t,e,a){"use strict";a.r(e);a("498a");var n=function(){var t=this,e=t._self._c;return e("div",[e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("申请编号")]),e("Input",{staticClass:"seachSty",attrs:{maxlength:"50",clearable:"",placeholder:"请输入申请编号"},model:{value:t.searchCondition.applyId,callback:function(e){t.$set(t.searchCondition,"applyId","string"===typeof e?e.trim():e)},expression:"searchCondition.applyId"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("客户名称")]),e("Select",{staticClass:"seachSty",attrs:{clearable:"",filterable:"",placeholder:"下拉选择客户名称"},model:{value:t.searchCondition.corpId,callback:function(e){t.$set(t.searchCondition,"corpId",e)},expression:"searchCondition.corpId"}},t._l(t.corpLists,(function(a,n){return e("Option",{key:n,attrs:{value:a.corpId}},[t._v(t._s(a.corpName))])})),1)],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("业务类型")]),e("Select",{staticClass:"seachSty",attrs:{filterable:"",placeholder:"请选择业务类型",clearable:""},model:{value:t.searchCondition.businessType,callback:function(e){t.$set(t.searchCondition,"businessType",e)},expression:"searchCondition.businessType"}},t._l(t.businessType,(function(a,n){return e("Option",{key:n,attrs:{value:a.value}},[t._v(t._s(a.label))])})),1)],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("审批状态")]),e("Select",{staticClass:"seachSty",attrs:{filterable:"",placeholder:"请选择审批状态",clearable:""},model:{value:t.searchCondition.authStatus,callback:function(e){t.$set(t.searchCondition,"authStatus",e)},expression:"searchCondition.authStatus"}},t._l(t.approvStatus,(function(a,n){return e("Option",{key:n,attrs:{value:a.value}},[t._v(t._s(a.label))])})),1)],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("调账单号")]),e("Input",{staticClass:"seachSty",attrs:{maxlength:"50",clearable:"",placeholder:"请输入调账单号"},model:{value:t.searchCondition.adjustId,callback:function(e){t.$set(t.searchCondition,"adjustId","string"===typeof e?e.trim():e)},expression:"searchCondition.adjustId"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("原账单发票号")]),e("Input",{staticClass:"seachSty",attrs:{maxlength:"50",clearable:"",placeholder:"请输入原账单发票号"},model:{value:t.searchCondition.invoiceNo,callback:function(e){t.$set(t.searchCondition,"invoiceNo","string"===typeof e?e.trim():e)},expression:"searchCondition.invoiceNo"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("账期")]),e("DatePicker",{staticClass:"seachSty",attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择账期",editable:!0},on:{"on-change":t.handleBillingPeriodMonth},model:{value:t.searchCondition.billingPeriod,callback:function(e){t.$set(t.searchCondition,"billingPeriod",e)},expression:"searchCondition.billingPeriod"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("审批环节")]),e("Select",{staticClass:"seachSty",attrs:{filterable:"",placeholder:"请选择审批环节",clearable:""},model:{value:t.searchCondition.authProcess,callback:function(e){t.$set(t.searchCondition,"authProcess",e)},expression:"searchCondition.authProcess"}},t._l(t.approvProcess,(function(a,n){return e("Option",{key:n,attrs:{value:a.value}},[t._v(t._s(a.label))])})),1)],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("调整方式")]),e("Select",{staticClass:"seachSty",attrs:{filterable:"",placeholder:"请选择调整方式",clearable:""},model:{value:t.searchCondition.adjustWay,callback:function(e){t.$set(t.searchCondition,"adjustWay",e)},expression:"searchCondition.adjustWay"}},t._l(t.adjustWay,(function(a,n){return e("Option",{key:n,attrs:{value:a.value}},[t._v(t._s(a.label))])})),1)],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("调账类型")]),e("Select",{staticClass:"seachSty",attrs:{filterable:"",placeholder:"请选择调账类型",clearable:""},model:{value:t.searchCondition.adjustType,callback:function(e){t.$set(t.searchCondition,"adjustType",e)},expression:"searchCondition.adjustType"}},t._l(t.adjustTypeList,(function(a,n){return e("Option",{key:n,attrs:{value:a.value}},[t._v(t._s(a.label))])})),1)],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("申请人")]),e("Input",{staticClass:"seachSty",attrs:{maxlength:"50",clearable:"",placeholder:"请输入申请人"},model:{value:t.searchCondition.applyName,callback:function(e){t.$set(t.searchCondition,"applyName","string"===typeof e?e.trim():e)},expression:"searchCondition.applyName"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("同步RAP状态")]),e("Select",{staticClass:"seachSty",attrs:{filterable:"",placeholder:"请选择同步RAP状态",clearable:""},model:{value:t.searchCondition.rapStatus,callback:function(e){t.$set(t.searchCondition,"rapStatus",e)},expression:"searchCondition.rapStatus"}},t._l(t.rapStatus,(function(a,n){return e("Option",{key:n,attrs:{value:a.value}},[t._v(t._s(a.label))])})),1)],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("申请日期")]),e("DatePicker",{staticClass:"seachSty",attrs:{format:"yyyy-MM-dd",type:"date",placement:"bottom-start",placeholder:"请选择申请日期",editable:!0},on:{"on-change":t.handleApplyDate},model:{value:t.searchCondition.applyDate,callback:function(e){t.$set(t.searchCondition,"applyDate",e)},expression:"searchCondition.applyDate"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("结算月份")]),e("DatePicker",{staticClass:"seachSty",attrs:{format:"yyyy-MM",type:"month",placement:"bottom-start",placeholder:"请选择结算月份",editable:!0},on:{"on-change":t.handleSettlementMonth},model:{value:t.searchCondition.settlementMonth,callback:function(e){t.$set(t.searchCondition,"settlementMonth",e)},expression:"searchCondition.settlementMonth"}})],1)]),e("div",{staticClass:"search_head_i"},[e("div",{staticStyle:{width:"110px",display:"flex","justify-content":"center","margin-bottom":"20px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",ghost:"",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.searchOne()}}},[t._v("搜索")])],1),e("div",{staticStyle:{width:"110px",display:"flex","justify-content":"center","margin-bottom":"20px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{type:"info",ghost:"",icon:"md-add"},on:{click:t.addItem}},[t._v("新增调账")])],1),e("div",{staticStyle:{display:"flex","justify-content":"center","margin-bottom":"20px","margin-left":"20px","margin-right":"25px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"bill_list_export",expression:"'bill_list_export'"}],attrs:{type:"warning",ghost:"",icon:"ios-download"},on:{click:function(e){return t.exportSum()}}},[t._v("导出列表")])],1),e("div",{staticStyle:{display:"flex","justify-content":"center","margin-bottom":"20px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"batch_invoice_export",expression:"'batch_invoice_export'"}],attrs:{type:"info",ghost:"",icon:"ios-download",loading:t.batchInvoiceLoading},on:{click:t.batchExportInvoice}},[t._v("批量下载Invoice")])],1)]),e("Table",{ref:"selection",staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},on:{"on-selection-change":t.handleRowChangeInvoice,"on-select-cancel":t.cancelInvoice,"on-select-all-cancel":t.cancelInvoiceAll},scopedSlots:t._u([{key:"download",fn:function(a){var n=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"invoice_export",expression:"'invoice_export'"}],attrs:{disabled:"审核完成"!=n.authStatus,type:"success",ghost:"",size:"small"},on:{click:function(e){return t.exportInvoice(n)}}},[t._v("下载Invoice")])]}}])}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.exportcancelModal},model:{value:t.exportModalr,callback:function(e){t.exportModalr=e},expression:"exportModalr"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex","flex-wrap":"wrap"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v("导出提示")]),e("FormItem",{attrs:{label:"你本次导出任务ID为:"}},[e("span",[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:"你本次导出的文件名为:"}},[e("span",{staticClass:"task-name"},[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v("请前往下载管理-下载列表查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.exportcancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Gotor}},[t._v("立即前往")])],1)]),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{width:"500px","align-items":"center","justify-content":"center","margin-bottom":"30px"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"20px"}},[t._v("导出提示")]),e("FormItem",{attrs:{label:"你本次导出任务ID为:"}},[e("ul",{staticStyle:{"margin-bottom":"15px"}},[e("li",{attrs:{id:"space"}},[t._v("\n                    "+t._s(t.taskId)+"\n                  ")])]),t.remind?e("div",[e("span",[t._v("……")])]):t._e()]),e("FormItem",{attrs:{label:"你本次导出的文件名为:"}},[e("ul",{staticStyle:{"margin-bottom":"15px"}},[e("li",{staticClass:"task-name",attrs:{id:"space"}},[t._v("\n                    "+t._s(t.taskName)+"\n                  ")])]),t.remind?e("div",[e("span",[t._v("……")])]):t._e()]),e("span",{staticStyle:{"text-align":"left"}},[t._v("请前往"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v("下载管理-下载列表")]),t._v("查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v("立即前往")])],1)])],1)],1)},o=[],s=(a("d81d"),a("14d9"),a("a434"),a("a9e3"),a("d3b7"),a("ac1f"),a("3ca3"),a("466d"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("944d")),i=a("951d"),r={data:function(){return{total:0,currentPage:1,page:0,corpLists:[],billingPeriod:"",applyDate:"",settlementMonth:"",businessType:[{value:1,label:"A-Z Sales"},{value:2,label:"Channel Sales"},{value:4,label:"Combine"}],approvStatus:[{value:1,label:"草稿"},{value:2,label:"审核中"},{value:3,label:"审核完成"},{value:4,label:"审核拒绝"}],approvProcess:[{value:1,label:"调账申请提交"},{value:2,label:"业务经理审核"},{value:3,label:"财务审核"},{value:4,label:"DI确认"}],adjustWay:[{value:1,label:"重新生成"},{value:2,label:"赔付"}],adjustTypeList:[{value:1,label:"Credit Note"},{value:2,label:"Debit Note"}],rapStatus:[{value:1,label:"未同步"},{value:2,label:"待同步"},{value:3,label:"已同步"}],searchCondition:{applyId:"",corpId:"",businessType:"",authStatus:"",adjustId:"",invoiceNo:"",billingPeriod:"",authProcess:"",adjustWay:"",adjustType:"",applyName:"",rapStatus:"",applyDate:"",settlementMonth:""},loading:!1,data:[],columns:[{type:"selection",width:60,align:"center",fixed:"left"},{title:"申请编号",key:"applyId",minWidth:150,align:"center",tooltip:!0},{title:"客户名称",key:"corpName",minWidth:150,align:"center",tooltip:!0},{title:"审批环节",key:"authProcess",minWidth:150,align:"center",tooltip:!0},{title:"审批状态",key:"authStatus",minWidth:150,align:"center",tooltip:!0},{title:"申请人",key:"applyName",minWidth:150,align:"center",tooltip:!0},{title:"申请日期",key:"applyDateStr",minWidth:150,align:"center",tooltip:!0},{title:"EBS CODE",key:"ebscode",minWidth:150,align:"center",tooltip:!0},{title:"业务类型",key:"businessType",minWidth:150,align:"center",tooltip:!0},{title:"调账原因",key:"adjustReason",minWidth:150,align:"center",tooltip:!0},{title:"调账描述",key:"message",minWidth:150,align:"center",tooltip:!0},{title:"原账单发票号",key:"invoiceNo",minWidth:150,align:"center",tooltip:!0},{title:"调账单号",key:"adjustId",minWidth:150,align:"center",tooltip:!0},{title:"合同主体",key:"contract",minWidth:150,align:"center",tooltip:!0},{title:"调整方式",key:"adjustWay",minWidth:150,align:"center",tooltip:!0},{title:"调账类型",key:"adjustType",minWidth:150,align:"center",tooltip:!0},{title:"账单币种",key:"currency",minWidth:150,align:"center",render:function(t,e){var a=e.row,n="156"==a.currency?"CNY":"840"==a.currency?"USD":"344"==a.currency?"HKD":"";return t("label",n)}},{title:"调账金额",key:"totalAdjustment",minWidth:150,align:"center",tooltip:!0},{title:"服务期开始时间",key:"svcStartTime",minWidth:160,align:"center",tooltip:!0},{title:"服务期结束时间",key:"svcEndTime",minWidth:160,align:"center",tooltip:!0},{title:"账期",key:"billingPeriod",minWidth:150,align:"center",tooltip:!0},{title:"同步RAP状态",key:"rapStatus",minWidth:150,align:"center",tooltip:!0},{title:"文件下载",slot:"download",width:150,align:"center",fixed:"right"}],batchInvoiceLoading:!1,searchloading:!1,modal1:!1,selection:[],selectionList:[],taskName:"",taskId:"",remind:!1,exportModalr:!1,exportModal:!1}},created:function(){this.getCorpList(),this.goPageFirst(1)},methods:{getCorpList:function(){var t=this;Object(i["e"])({type:1,status:1,checkStatus:2}).then((function(e){"0000"==e.code&&(t.corpLists=e.data)}))},goPageFirst:function(t){var e=this;this.loading=!0;var a=this.searchCondition,n=this;Object(s["s"])({num:t,size:10,applyId:a.applyId,corpId:a.corpId,businessType:a.businessType,authStatus:a.authStatus,adjustId:a.adjustId,invoiceNo:a.invoiceNo,billingPeriod:this.billingPeriod,authProcess:a.authProcess,adjustWay:a.adjustWay,adjustType:a.adjustType,applyName:a.applyName,rapStatus:a.rapStatus,applyDate:this.applyDate,settlementMonth:this.settlementMonth?this.settlementMonth+"-01":""}).then((function(a){"0000"==a.code&&(n.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.total=Number(a.count),e.data=a.data,e.data.forEach((function(t){t.id||e.$set(t,"_disabled",!0)})))})).catch((function(t){console.error(t)})).finally((function(){n.loading=!1,e.searchloading=!1}))},searchOne:function(){this.searchCondition;this.searchloading=!0,this.goPageFirst(1)},goPage:function(t){this.goPageFirst(t)},handleBillingPeriodMonth:function(t){this.billingPeriod=t},handleApplyDate:function(t){this.applyDate=t},handleSettlementMonth:function(t){this.settlementMonth=t},addItem:function(){this.$router.push({path:"/addBillingAdjust"})},handleRowChangeInvoice:function(t){var e=this;this.selection=t,t.map((function(t,a){var n=!0;e.selectionList.map((function(e,a){t.id===e.id&&(n=!1)})),n&&e.selectionList.push(t)}))},cancelInvoice:function(t,e){var a=this;this.selectionList.forEach((function(t,n){t.id===e.id&&a.selectionList.splice(n,1)}))},cancelInvoiceAll:function(t,e){this.selectionList=[]},exportSum:function(){var t=this,e=this.searchCondition;Object(s["i"])({num:this.page,size:10,applyId:e.applyId,corpId:e.corpId,businessType:e.businessType,authStatus:e.authStatus,adjustId:e.adjustId,invoiceNo:e.invoiceNo,billingPeriod:this.billingPeriod,authProcess:e.authProcess,adjustWay:e.adjustWay,adjustType:e.adjustType,applyName:e.applyName,rapStatus:e.rapStatus,userId:this.$store.state.user.userId,applyDate:this.applyDate,settlementMonth:this.settlementMonth?this.settlementMonth+"-01":""}).then((function(e){if(!e||"0000"!=e.code)throw e;t.exportModal=!0,e.data&&(t.taskId=e.data.taskId,t.taskName=e.data.taskName)})).catch((function(t){console.log(t)}))},exportInvoice:function(t){Object(s["y"])({id:t.id,type:"2"}).then((function(t){var e=t.data,a=decodeURIComponent(escape(t.headers["content-disposition"].match(/=(.*)$/)[1]));if("download"in document.createElement("a")){var n=document.createElement("a"),o=URL.createObjectURL(e);n.download=a,n.href=o,n.click(),URL.revokeObjectURL(o)}else navigator.msSaveBlob(e,a)})).catch((function(t){})).finally((function(){}))},batchExportInvoice:function(){var t=this,e=this.selection.length;if(e<1)this.$Message.warning("请至少选择一条记录");else{var a=[];this.selectionList.forEach((function(t,e){a.push(t.id)})),this.batchInvoiceLoading=!0,Object(s["e"])({ids:a,userId:this.$store.state.user.userId}).then((function(e){if("0000"!==e.code)throw e;t.exportModalr=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName,t.selection=[],t.selectionList=[],t.getTableData(1)})).catch((function(t){})).finally((function(){t.batchInvoiceLoading=!1}))}},Gotor:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportcancelModal(),this.exportModalr=!1},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},cancelModal:function(){this.exportModal=!1,this.remind=!1},exportcancelModal:function(){this.exportModalr=!1}}},l=r,c=(a("bfb4"),a("2877")),u=Object(c["a"])(l,n,o,!1,null,"2c46f4cb",null);e["default"]=u.exports}}]);