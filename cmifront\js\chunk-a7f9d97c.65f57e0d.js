(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a7f9d97c"],{"0240":function(t,e,n){"use strict";n.d(e,"k",(function(){return i})),n.d(e,"f",(function(){return r})),n.d(e,"e",(function(){return l})),n.d(e,"m",(function(){return c})),n.d(e,"b",(function(){return s})),n.d(e,"a",(function(){return u})),n.d(e,"j",(function(){return d})),n.d(e,"d",(function(){return p})),n.d(e,"h",(function(){return h})),n.d(e,"g",(function(){return m})),n.d(e,"i",(function(){return f})),n.d(e,"c",(function(){return g})),n.d(e,"l",(function(){return y})),n.d(e,"n",(function(){return b}));n("99af");var a=n("66df"),o="/stat",i=function(t){return a["a"].request({url:o+"/channelIncome/query",data:t,method:"post"})},r=function(t,e,n){return a["a"].request({url:o+"/invoice/create/no/".concat(t,"/").concat(e,"/").concat(n),method:"post"})},l=function(t){return a["a"].request({url:o+"/invoice/create",data:t,method:"post"})},c=function(t){return a["a"].request({url:o+"/channelIncome/update",params:t,method:"put"})},s=function(t){return a["a"].request({url:o+"/channelIncome/createBillTotalTask",data:t,method:"post"})},u=function(t){return a["a"].request({url:o+"/channelIncome/createBillDetailTask",data:t,method:"post"})},d=function(t){return a["a"].request({url:o+"/channelIncome/export/invoice",params:t,method:"get"})},p=function(t){return a["a"].request({url:o+"/channelIncome/auth",params:t,method:"put"})},h=function(t){return a["a"].request({url:"/cms/channel/channelAtzFlowExport",params:t,method:"post"})},m=function(t){return a["a"].request({url:"/cms/channel/channelAtzSummaryExport",params:t,method:"post"})},f=function(t){return a["a"].request({url:"/cms/channelSelfServer/exportImsiCost",data:t,method:"post"})},g=function(t){return a["a"].request({url:o+"/channelIncome/export/batchInvoice",data:t,method:"post"})},y=function(t){return a["a"].request({url:o+"/financialReport/billedImmediately",data:t,method:"post"})},b=function(t){return a["a"].request({url:o+"/channelIncome/updateImsiFee",params:t,method:"get"})}},"129f":function(t,e,n){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},2315:function(t,e,n){"use strict";n.d(e,"h",(function(){return i})),n.d(e,"e",(function(){return r})),n.d(e,"f",(function(){return l})),n.d(e,"a",(function(){return c})),n.d(e,"i",(function(){return s})),n.d(e,"b",(function(){return u})),n.d(e,"g",(function(){return d})),n.d(e,"d",(function(){return p})),n.d(e,"c",(function(){return h}));var a=n("66df"),o="/stat",i=function(t){return a["a"].request({url:o+"/channelincome/month/getChannelBill",data:t,method:"post"})},r=function(t){return a["a"].request({url:"/charging/atzCharging/getChargingByChannel",params:t,method:"get"})},l=function(t){return a["a"].request({url:"pms/imsiAmount/getChannelImsiAmount",params:t,method:"post"})},c=function(t){return a["a"].request({url:"/cms/IBoss/payBill",data:t,method:"POST",contentType:"multipart/form-data"})},s=function(t){return a["a"].request({url:"/cms/IBoss/cancelPayBill/",params:t,method:"get"})},u=function(t){return a["a"].request({url:"/cms/channel/channelExport",data:t,method:"post"})},d=function(t){return a["a"].request({url:"cms/channel/deposit/record",data:t,method:"post"})},p=function(t){return a["a"].request({url:"cms/channel/deposit/recordExport",data:t,method:"post",responseType:"blob"})},h=function(t){return a["a"].request({url:o+"/channelincome/month/exportChannelBill",data:t,method:"post",responseType:"blob"})}},"3f7e":function(t,e,n){"use strict";var a=n("b5db"),o=a.match(/firefox\/(\d+)/i);t.exports=!!o&&+o[1]},"460d":function(t,e,n){"use strict";n.r(e);var a=n("ade3"),o=(n("caad"),n("b0c0"),n("ac1f"),n("841c"),function(){var t=this,e=t._self._c;return e("Card",[e("Form",{ref:"form",attrs:{"label-width":90,model:t.form,rules:t.ruleInline,inline:""}},[e("FormItem",{attrs:{label:t.$t("channelBill.startMonth")+":",prop:"beginMonth"}},[e("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:t.$t("channelBill.selectStart"),editable:!0},on:{"on-change":t.startChange},model:{value:t.form.beginMonth,callback:function(e){t.$set(t.form,"beginMonth",e)},expression:"form.beginMonth"}})],1),e("FormItem",{attrs:{label:t.$t("channelBill.endMonth")+":",prop:"endMonth"}},[e("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:t.$t("channelBill.selectEnd"),editable:!0},on:{"on-change":t.endChange},model:{value:t.form.endMonth,callback:function(e){t.$set(t.form,"endMonth",e)},expression:"form.endMonth"}})],1),e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:t.searchLoading},on:{click:function(e){return t.search("form")}}},[t._v(t._s(t.$t("common.search")))]),t._v("  \n      "),e("Button",{directives:[{name:"has",rawName:"v-has",value:"bill_search",expression:"'bill_search'"}],attrs:{type:"info",icon:"md-eye",loading:t.flowLoading,disabled:"1"==t.cooperationMode},on:{click:t.usageBilling}},[t._v(t._s(t.$t("channelBill.dataUsage")))]),t._v("  \n\t\t\t"),e("Button",{directives:[{name:"has",rawName:"v-has",value:"imsi_search",expression:"'imsi_search'"}],attrs:{type:"success",icon:"md-color-wand",loading:t.imsiLoading},on:{click:t.imsiView}},[t._v(t._s(t.$t("channelBill.imsiFee")))])],1),e("div",[e("Table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{"no-data-text":"",border:"","highlight-row":"",columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(n){var a=n.row;n.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"info",expression:"'info'"}],staticStyle:{margin:"5px"},attrs:{type:"primary",ghost:"",size:"small"},on:{click:function(e){return t.showInfo(a)}}},[t._v(t._s(t.$t("channelBill.detailed")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"bill_export",expression:"'bill_export'"}],staticStyle:{margin:"5px"},attrs:{type:"info",ghost:"",size:"small"},on:{click:function(e){return t.exportBillFile(a)}}},[t._v("\n            "+t._s(t.$t("channelBill.billFileDownload"))+"\n          ")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"invoice",expression:"'invoice'"}],staticStyle:{margin:"5px"},attrs:{type:"success",ghost:"",size:"small",disabled:!a.invoicePath},on:{click:function(e){return t.exportInvoice(a)}}},[t._v(t._s(t.$t("channelBill.invoiceDownload")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"onlinePayment",expression:"'onlinePayment'"}],staticStyle:{margin:"5px"},attrs:{type:"primary",ghost:"",size:"small",disabled:["1","2","3","5"].includes(a.chargeStatus)||"2"==a.channelType||!["0","1"].includes(a.accountingType)||0==a.realIncome},on:{click:function(e){return t.paymentMethods(a)}}},[t._v("\n            "+t._s(t.$t("channelBill.onlinePayment"))+"\n          ")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"payment",expression:"'payment'"}],staticStyle:{margin:"5px"},attrs:{type:"info",ghost:"",size:"small",disabled:!["0","4"].includes(a.chargeStatus)||"2"==a.channelType||0==a.realIncome},on:{click:function(e){return t.billsDelivery(a,"1")}}},[t._v("\n            "+t._s(t.$t("channelBill.offlinePayment"))+"\n          ")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"revoke",expression:"'revoke'"}],staticStyle:{margin:"5px"},attrs:{type:"error",ghost:"",size:"small",disabled:"1"!=a.chargeStatus||"2"==a.channelType||0==a.realIncome},on:{click:function(e){return t.revoke(a)}}},[t._v("\n            "+t._s(t.$t("support.revoke"))+"\n          ")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"rePayment",expression:"'rePayment'"}],staticStyle:{margin:"5px"},attrs:{type:"warning",ghost:"",size:"small",disabled:!["3"].includes(a.chargeStatus)||"2"==a.channelType||0==a.realIncome},on:{click:function(e){return t.billsDelivery(a,"2")}}},[t._v("\n            "+t._s(t.$t("channelBill.reUpload"))+"\n          ")])]}}])}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,current:t.page,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage}})],1),e("Modal",{attrs:{title:t.$t("channelBill.detailed"),"footer-hide":!0,"mask-closable":!1,width:"500px"},model:{value:t.infoModal,callback:function(e){t.infoModal=e},expression:"infoModal"}},[e("Row",{staticStyle:{margin:"20px"}},[e("Col",{staticStyle:{display:"flex","justify-content":"flex-start"},attrs:{span:"24"}},[e("span",[t._v(t._s(t.$t("channelBill.dataFee"))+"：")]),t._v("  "+t._s(t.a2zAmount)+"\n\t\t    ")])],1),e("Row",{staticStyle:{margin:"20px"}},[e("Col",{staticStyle:{display:"flex","justify-content":"flex-start"},attrs:{span:"24"}},[e("span",[t._v(t._s(t.$t("channelBill.cardFee"))+"：")]),t._v("  "+t._s(t.imsiAmount)+"\n        ")])],1),e("Row",{staticStyle:{margin:"20px"}},[e("Col",{staticStyle:{display:"flex","justify-content":"flex-start"},attrs:{span:"24"}},[e("span",[t._v(t._s(t.$t("channelBill.packageFee"))+"：")]),t._v("  "+t._s(t.directIncome)+"\n\t\t    ")])],1)],1),e("Modal",{attrs:{title:t.$t("channelBill.dataUsage"),"footer-hide":!0,"mask-closable":!1,width:"1000px"},on:{"on-cancel":t.cancelModal},model:{value:t.billModal,callback:function(e){t.billModal=e},expression:"billModal"}},[t.spinShow?e("Spin",{attrs:{size:"large",fix:""}}):t._e(),e("Form",{ref:"searchForm",attrs:{model:t.searchObj,inline:""},nativeOn:{submit:function(t){t.preventDefault()}}},[e("FormItem",[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("channelBill.inputChargesName"),clearable:""},model:{value:t.searchObj.name,callback:function(e){t.$set(t.searchObj,"name",e)},expression:"searchObj.name"}})],1),e("FormItem",[e("Select",{attrs:Object(a["a"])({filterable:"",clearable:"",placeholder:t.$t("buymeal.selectCountry")},"clearable",""),model:{value:t.searchObj.mcc,callback:function(e){t.$set(t.searchObj,"mcc",e)},expression:"searchObj.mcc"}},t._l(t.localList,(function(n){return e("Option",{key:n.mcc,attrs:{value:n.mcc}},[t._v(t._s(n.countryEn))])})),1)],1),e("FormItem",[e("Button",{attrs:{type:"info",icon:"md-search",loading:t.billLoading},on:{click:t.billSearch}},[t._v(t._s(t.$t("common.search")))])],1)],1),e("Table",{attrs:{columns:t.billColumns,data:t.billTableData,ellipsis:!0}})],1),e("Modal",{attrs:{title:t.$t("channelBill.imsiFee"),"footer-hide":!0,"mask-closable":!1,width:"600px"},model:{value:t.imsiModal,callback:function(e){t.imsiModal=e},expression:"imsiModal"}},[e("Table",{attrs:{columns:t.imsiColumns,data:t.imsiTableData,ellipsis:!0}})],1),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.exportcancelModal},model:{value:t.exportModalr,callback:function(e){t.exportModalr=e},expression:"exportModalr"}},[e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v(t._s(t.$t("exportMS")))]),e("FormItem",{attrs:{label:t.$t("exportID")}},[e("span",[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:t.$t("exportFlie")}},[e("span",{staticClass:"task-name"},[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v(t._s(t.$t("downloadResult")))])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.exportcancelModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.Gotor}},[t._v(t._s(t.$t("Goto")))])],1)]),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center"}},[e("Form",{staticStyle:{width:"500px","align-items":"center","justify-content":"center","margin-bottom":"30px"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"20px"}},[t._v(t._s(t.$t("exportMS")))]),e("FormItem",{attrs:{label:t.$t("exportID")}},[e("ul",{staticStyle:{"margin-bottom":"15px"}},t._l(t.taskIds,(function(n,a){return e("li",{key:t.taskIds.i,attrs:{id:"space"}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(n)+"\n\t\t\t\t\t\t\t")])})),0)]),e("FormItem",{attrs:{label:t.$t("exportFlie")}},[e("ul",{staticStyle:{"margin-bottom":"15px"}},t._l(t.taskNames,(function(n,a){return e("li",{key:t.taskNames.i,staticClass:"task-name",attrs:{id:"space"}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(n)+"\n\t\t\t\t\t\t\t")])})),0)]),e("span",{staticStyle:{"text-align":"left"}},[t._v(t._s(t.$t("downloadResult")))])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v(t._s(t.$t("Goto")))])],1)]),e("Modal",{attrs:{title:t.$t("channelBill.paymentPage"),"footer-hide":!0,"mask-closable":!1,width:"450px"},on:{"on-cancel":t.cancelModal},model:{value:t.PaymentModal,callback:function(e){t.PaymentModal=e},expression:"PaymentModal"}},[e("div",[e("Form",{ref:"formobj",staticStyle:{"font-weight":"bold"},attrs:{model:t.formobj,rules:t.ruleobj,"label-width":100,"label-height":100,inline:""}},[e("FormItem",{staticStyle:{"font-size":"14px","font-weight":"bold"},attrs:{label:t.$t("support.payslip"),prop:"file"}},[e("Upload",{staticStyle:{width:"250px","margin-top":"50px"},attrs:{type:"drag",action:t.uploadUrl,"before-upload":t.handleBeforeUpload,"on-progress":t.fileUploading},model:{value:t.formobj.file,callback:function(e){t.$set(t.formobj,"file",e)},expression:"formobj.file"}},[e("div",{staticStyle:{padding:"20px 0"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v(t._s(t.$t("support.uploadPicture")))])],1)]),t.file?e("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"300px"}},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name)+"\n    \t\t\t\t\t\t")],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e()],1),["2","3","5"].indexOf(t.billRow.accountingType)>-1?e("FormItem",{staticStyle:{"font-size":"14px","font-weight":"bold"},attrs:{label:t.$t("channelBill.cnInvoice"),prop:"cnInvoiceFile"}},[e("Upload",{staticStyle:{width:"250px","margin-top":"20px"},attrs:{type:"drag",action:t.uploadUrl,"before-upload":t.handleCnInvoiceBeforeUpload,"on-progress":t.cnInvoiceUploading},model:{value:t.formobj.cnInvoiceFile,callback:function(e){t.$set(t.formobj,"cnInvoiceFile",e)},expression:"formobj.cnInvoiceFile"}},[e("div",{staticStyle:{padding:"20px 0"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v(t._s(t.$t("channelBill.uploadCnInvoice")))])],1)]),t.cnInvoiceFile?e("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"300px"}},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.cnInvoiceFile.name)+"\n                ")],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeCnInvoiceFile}})])]):t._e()],1):t._e(),e("FormItem",{attrs:{label:t.$t("fuelPack.Amount"),prop:"amount"}},[e("Input",{staticStyle:{width:"250px"},attrs:{placeholder:t.$t("channelBill.inputAmount"),disabled:""},model:{value:t.formobj.amount,callback:function(e){t.$set(t.formobj,"amount",e)},expression:"formobj.amount"}})],1)],1),e("div",{staticStyle:{"text-align":"center",margin:"40px 0 0 0"}},[e("Button",{staticStyle:{"margin-right":"30px"},on:{click:t.cancelModal}},[t._v(t._s(t.$t("support.back")))]),e("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:t.pictureLoading},on:{click:t.pictureSubmit}},[t._v(t._s(t.$t("common.determine")))])],1)],1)]),e("Modal",{attrs:{title:t.$t("onlineOrder.onlineModalTitle"),"mask-closable":!1,width:720},model:{value:t.payModal,callback:function(e){t.payModal=e},expression:"payModal"}},[t.payModal?e("PaymentComponent",{attrs:{orderType:"channelBilling",corpId:t.corpId,billId:t.accountId,paySuccessInfo:t.paySuccessInfo,payLoading:t.payStatus,amount:t.cooperationDetail.realIncome,currencyCode:t.cooperationDetail.currency},on:{onlinePay:t.onlinePay}}):t._e(),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"})],1)],1)}),i=[],r=n("5530"),l=(n("d9e2"),n("99af"),n("4de4"),n("14d9"),n("fb6a"),n("4e82"),n("a9e3"),n("d3b7"),n("00b4"),n("25f0"),n("159b"),n("2315")),c=n("0240"),s=n("6dfa"),u=n("e3b7"),d=n("4fc8"),p=n("8bde"),h=(n("c70b"),{components:{PaymentComponent:p["a"]},data:function(){var t=this,e=function(e,n,a){if(parseFloat(n)<0)a(new Error(t.$t("channelBill.lessThan0")));else{var o=n;"-"===n.substr(0,1)&&(o=n.substr(1,n.length));var i=/^(?!0(\.0+)?$)([1-9]\d{0,7}(\.\d{1,2})?|\d{1,8}(\.\d{0,2})?)$/;!o||i.test(o)?a():a(new Error(t.$t("channelBill.checkNumber")))}},n=function(e,n,a){t.file?a():a(new Error(t.$t("support.pleaseUploadFile")))};return{form:{beginMonth:"",endMonth:""},searchObj:{name:"",countryName:""},total:0,pageSize:10,page:1,billModal:!1,imsiModal:!1,loading:!1,exportModal:!1,exportModalr:!1,searchLoading:!1,flowLoading:!1,imsiLoading:!1,billLoading:!1,pictureLoading:!1,PaymentModal:!1,infoModal:!1,spinShow:!1,taskName:"",taskId:"",accountId:"",corpId:"",a2zAmount:"",imsiAmount:"",directIncome:"",corpLists:[],localList:[],taskIds:[],taskNames:[],imsiColumns:[{title:this.$t("channelBill.imsiFeeType"),key:"ruleName",align:"center",tooltip:!0,minWidth:150},{title:this.$t("channelBill.imsiFeeAmount"),key:"amount",align:"center",tooltip:!0,minWidth:150},{title:this.$t("channelBill.quantityRange"),key:"begin",align:"center",tooltip:!0,minWidth:150,render:function(t,e){var n=e.row,a="**********"==n.end?"+":"-"+n.end,o=n.begin+a;return t("label",o)}}],imsiTableData:[],billColumns:[{title:this.$t("channelBill.chargesName"),key:"name",align:"center",tooltip:!0,minWidth:150},{title:this.$t("channelBill.country"),key:"countryName",align:"center",tooltip:!0,minWidth:150,render:function(e,n){var a=n.row,o="zh-CN"==t.$i18n.locale?a.countryName:a.countryNameEn;return e("label",o)}},{title:this.$t("support.operator"),key:"operatorName",align:"center",tooltip:!0,minWidth:150},{title:this.$t("channelBill.cny"),key:"cny",align:"center",tooltip:!0,minWidth:180,render:function(e,n){var a=n.row,o=t.$t("yuan"),i="1"==a.flowUnit?a.cny+" ("+o+"/GB)":"2"==a.flowUnit?a.cny+" ("+o+"/MB)":"";return e("label",i)}},{title:this.$t("channelBill.hkd"),key:"hkd",align:"center",tooltip:!0,minWidth:150,render:function(e,n){var a=n.row,o=t.$t("yuan"),i="1"==a.flowUnit?a.hkd+" ("+o+"/GB)":"2"==a.flowUnit?a.hkd+" ("+o+"/MB)":"";return e("label",i)}},{title:this.$t("channelBill.usd"),key:"usd",align:"center",tooltip:!0,minWidth:150,render:function(e,n){var a=n.row,o=t.$t("yuan"),i="1"==a.flowUnit?a.usd+" ("+o+"/GB)":"2"==a.flowUnit?a.usd+" ("+o+"/MB)":"";return e("label",i)}}],billTableData:[],data:[],columns:[{title:this.$t("channelBill.billId"),key:"billId",align:"center",tooltip:!0,minWidth:160},{title:this.$t("channelBill.billType"),key:"accountingType",align:"center",tooltip:!0,minWidth:150,render:function(e,n){var a=n.row,o="0"==a.accountingType||"1"==a.accountingType?t.$t("support.distribution"):"2"==a.accountingType||"3"==a.accountingType?t.$t("support.atoz"):"4"==a.accountingType?t.$t("channelBill.merge"):"5"==a.accountingType?t.$t("support.atoz"):"6"==a.accountingType||"7"==a.accountingType?t.$t("channelBill.merge"):"";return e("label",o)}},{title:this.$t("channelBill.paymentMonth"),key:"statTime",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var n=e.row,a=n.statTime.slice(0,4),o=n.statTime.slice(4,6),i="".concat(a,"-").concat(o);return t("label",i)}},{title:this.$t("channelBill.totalBillAmount"),key:"saleIncome",align:"center",tooltip:!0,minWidth:150},{title:this.$t("channelBill.accountsPayableAmount"),key:"realIncome",align:"center",minWidth:200,render:function(t,e){var n=e.row,a="2"==n.channelType?"0":n.realIncome;return t("label",a)}},{title:this.$t("channelBill.paymentStatus"),key:"chargeStatus",align:"center",minWidth:190,tooltip:!0,tooltipMaxWidth:1e3,render:function(e,n){var a=n.row,o="";return o="2"==a.channelType?t.$t("channelBill.verified"):"0"==a.chargeStatus?t.$t("channelBill.unpaidPayment"):"1"==a.chargeStatus?t.$t("channelBill.confirmationReceipt"):"2"==a.chargeStatus?t.$t("channelBill.Arrived"):"3"==a.chargeStatus?t.$t("channelBill.NotCredited"):"4"==a.chargeStatus?t.$t("support.cancelled"):"5"==a.chargeStatus?t.$t("channelBill.OnlinePaymentInProgress"):"",e("label",o)}},{title:this.$t("Operation"),slot:"action",width:450,align:"center",fixed:"right"}],ruleInline:{beginMonth:[{type:"date",required:!0,message:this.$t("channelBill.selectStart"),trigger:"blur"}],endMonth:[{type:"date",required:!0,message:this.$t("channelBill.selectEnd"),trigger:"blur"}]},searchBeginTime:"",searchEndTime:"",type:"",cooperationMode:"",rowType:"",currencyCode:"",uploadUrl:"",uploadList:[],file:null,formobj:{file:"",amount:""},ruleobj:{file:[{required:!0,validator:n,trigger:"change"}],amount:[{required:!0,message:this.$t("channelBill.inputAmount"),trigger:"change"},{validator:e}]},payModal:!1,cardPayFromParent:!1,payDetail:{},cooperationDetail:{},payStatus:!1,payCont:"",paySuccessInfo:{},cnInvoiceFile:"",cnInvoiceLoading:!1,cnInvoiceMessage:"",billRow:{}}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),this.getDistributorsDetail()},methods:{startChange:function(t){this.searchBeginTime=t},endChange:function(t){this.searchEndTime=t},getTableData:function(t,e){var n=this,a=sessionStorage.getItem("corpId"),o=[];o.push(a),this.loading=!0,Object(l["h"])({dateStart:e||this.searchBeginTime,dateEnd:this.searchEndTime,corpId:o,pageNum:this.page,pageSize:this.pageSize,billType:this.cooperationMode}).then((function(e){"0000"==e.code&&(n.loading=!1,n.searchLoading=!1,n.page=t,n.currentPage=t,n.total=Number(e.count),n.data=e.data)})).catch((function(t){console.error(t)})).finally((function(){n.loading=!1,n.searchLoading=!1}))},search:function(t){var e=this,n="202409";this.$refs[t].validate((function(t){if(t)if(new Date(e.searchBeginTime)>new Date(e.searchEndTime))e.$Message["warning"]({background:!0,content:e.$t("channelBill.endGreaterStart")});else if(new Date(e.searchBeginTime)<new Date(n)&&new Date(e.searchEndTime)<new Date(n))e.page=1,e.currentPage=1,e.total=0,e.data=[];else if(new Date(e.searchBeginTime)<new Date(n)&&new Date(e.searchEndTime)>=new Date(n)){var a="202409";e.searchLoading=!0,e.getTableData(1,a)}else e.searchLoading=!0,e.getTableData(1)}))},loadByPage:function(t){var e=this;this.page=t;var n="202409";this.$refs["form"].validate((function(a){if(a)if(new Date(e.searchBeginTime)<new Date(n)){var o="202409";e.getTableData(t,o)}else e.getTableData(t)}))},usageBilling:function(){this.spinShow=!0,this.getBillInfo(),this.getLocalList(),this.billModal=!0},imsiView:function(){this.getImsiFee(),this.imsiModal=!0},exportInvoice:function(t){var e=this;Object(c["j"])({corpName:t.corpName,invoicePath:t.invoicePath,month:t.statTime,corpId:t.corpId,billType:t.accountingType,incomeId:t.id}).then((function(t){if(!t||"0000"!=t.code)throw t;e.exportModalr=!0,e.taskId=t.data.taskId,e.taskName=t.data.taskName})).catch((function(t){console.log(t)}))},showInfo:function(t){this.a2zAmount=t.a2zAmount,this.imsiAmount=t.imsiAmount,this.directIncome=t.directIncome,this.infoModal=!0},exportBillFile:function(t){var e=this,n="",a="202409";n=new Date(this.searchBeginTime)<new Date(a)&&new Date(this.searchEndTime)>=new Date(a)?"202409":this.searchBeginTime;var o={corpId:t.corpId,beginMonth:n,endMonth:this.searchEndTime,cooperationMode:t.accountingType,corpName:t.corpName,currencyCode:t.currency,billType:t.accountingType,id:t.id,statTime:t.statTime,dateStart:t.svcStartTime,dateEnd:t.svcEndTime,userId:t.corpId,type:"2"==t.accountingType?"1":"3"==t.accountingType?"2":"4"==t.accountingType?1:"5"==t.accountingType?"3":"6"==t.accountingType?"2":"7"==t.accountingType?"3":""};Object(l["b"])(o).then((function(t){if(!t||"0000"!=t.code)throw t;e.exportModal=!0,t.data.forEach((function(t){e.taskIds.push(t.taskId),e.taskNames.push(t.taskName)}))})).catch((function(t){console.log(t)}))},billsDelivery:function(t,e){this.accountId=t.id,this.corpId=t.corpId,this.rowType=e,this.formobj.amount=t.realIncome.toString(),this.billRow=t,this.PaymentModal=!0},pictureSubmit:function(){var t=this;this.$refs["formobj"].validate((function(e){if(e){var n=new FormData;n.append("amount",t.formobj.amount),n.append("corpId",t.corpId),n.append("accountId",t.accountId),n.append("paymentProofs",t.file),t.cnInvoiceFile&&n.append("cnInvoice",t.cnInvoiceFile),n.append("repeatedUpload","2"==t.rowType),n.append("chargeType","1"),t.pictureLoading=!0,Object(l["a"])(n).then((function(e){if("0000"!==e.code)throw e;t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.pictureLoading=!1,t.PaymentModal=!1;var n="",a="202409";n=new Date(t.searchBeginTime)<new Date(a)?"202409":t.searchBeginTime,t.getTableData(1,n),t.file="",t.cnInvoiceFile="",t.$refs["formobj"].resetFields()})).catch((function(e){t.pictureLoading=!1})).finally((function(){}))}}))},revoke:function(t){var e=this;this.$Modal.confirm({title:this.$t("support.confirmRevocation"),onOk:function(){Object(l["i"])({id:t.id}).then((function(t){"0000"===t.code&&e.$Notice.success({title:e.$t("address.Operationreminder"),desc:e.$t("common.Successful")});var n="",a="202409";n=new Date(e.searchBeginTime)<new Date(a)?"202409":e.searchBeginTime,e.getTableData(1,n)}))}})},exportcancelModal:function(){this.exportModalr=!1,this.file="",this.cnInvoiceFile=""},cancelModal:function(){this.taskIds=[],this.taskNames=[],this.searchObj.name="",this.searchObj.mcc="",this.billModal=!1,this.imsiModal=!1,this.exportModal=!1,this.PaymentModal=!1,this.billRow={},this.file="",this.cnInvoiceFile="",this.$refs["formobj"].resetFields()},Gotor:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportcancelModal(),this.exportModalr=!1},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},billSearch:function(){this.billLoading=!0,this.getBillInfo()},flattenAndEnrichAtzChargingDetails:function(t){var e=[];return t.forEach((function(t){t.atzChargingDetails.forEach((function(n){var a=Object(r["a"])(Object(r["a"])({},n),{id:t.id,name:t.name,flowUnit:t.flowUnit,effectiveDate:t.effectiveDate,status:t.status});e.push(a)}))})),e},imsiFeeDetails:function(t){var e=[];return t.forEach((function(t){t.imsiAmountRanges.forEach((function(n){var a=Object(r["a"])(Object(r["a"])({},n),{ruleId:t.ruleId,ruleName:t.ruleName});e.push(a)}))})),e},handleBeforeUpload:function(t,e){var n=t.size/1024/1024>10;return n?(this.$Notice.warning({title:this.$t("address.Operationreminder"),desc:this.$t("support.pictureSize")}),!1):(this.file=t,this.uploadList=e,!1)},fileUploading:function(t,e,n){this.message=this.$t("support.fileUploadedAndProgressDisappears")},removeFile:function(){this.file=""},getBillInfo:function(){var t=this,e=sessionStorage.getItem("corpId");Object(l["e"])({corpId:e,name:this.searchObj.name,mcc:this.searchObj.mcc,cooperationMode:this.cooperationMode}).then((function(e){if("0000"!=e.code)throw t.billTableData=[],t.billLoading=!1,e;t.billTableData=t.flattenAndEnrichAtzChargingDetails(e.data),t.billLoading=!1})).catch((function(t){console.error(t)})).finally((function(){t.spinShow=!1}))},getImsiFee:function(){var t=this,e=sessionStorage.getItem("corpId");Object(l["f"])({corpId:e,cooperationMode:this.cooperationMode}).then((function(e){if("0000"!=e.code)throw t.imsiTableData=[],e;t.imsiTableData=t.imsiFeeDetails(e.data)})).catch((function(t){console.error(t)})).finally((function(){}))},getLocalList:function(){var t=this;Object(s["A"])().then((function(e){if(!e||"0000"!=e.code)throw e;var n=e.data;t.localList=n,t.localList.sort((function(t,e){return t.countryEn.localeCompare(e.countryEn)}))})).catch((function(t){})).finally((function(){}))},getDistributorsDetail:function(){var t=this;Object(u["s"])({corpId:sessionStorage.getItem("corpId")}).then((function(e){"0000"===e.code&&(t.currencyCode=e.data.currencyCode,"156"==t.currencyCode?t.billColumns=t.billColumns.filter((function(t){return"hkd"!==t.key&&"usd"!==t.key})):"344"==t.currencyCode?t.billColumns=t.billColumns.filter((function(t){return"cny"!==t.key&&"usd"!==t.key})):t.billColumns=t.billColumns.filter((function(t){return"cny"!==t.key&&"hkd"!==t.key})))}))},paymentMethods:function(t){console.log(t);var e=t;e.currencyCode=e.currency,this.payModal=!0,this.accountId=t.id,this.corpId=t.corpId,this.cardPayFromParent=!0,this.cooperationDetail=e},onlinePay:function(t){var e=this;if(console.log("支付信息：",t),this.payStatus)console.log("请求中...");else{this.payStatus=!0;var n={amount:this.cooperationDetail.realIncome,paymentMethod:t.paymentMethod,orderType:"channelBilling",adyenStateData:t.adyenStateData,billId:this.cooperationDetail.id,corpId:sessionStorage.getItem("corpId"),language:localStorage.getItem("local")};Object(d["a"])(n).then((function(t){console.log("res:",t),"0000"==t.code&&(e.payStatus=!1,e.paySuccessInfo=t.data,e.payModal=!1,e.payCont=t.data.payUrl,document.querySelector("body").innerHTML=t.data.payUrl,document.forms[0].submit())})).catch((function(t){e.payStatus=!1}))}},handleWeChatOrAlipayPayment:function(t){window.location.href=t},handleCreditCardOrUnionPayPayment:function(t){console.log("payUrl:",t),window.location.href=t},handleCnInvoiceBeforeUpload:function(t,e){var n=t.size/1024/1024>10;return n?(this.$Notice.warning({title:this.$t("address.Operationreminder"),desc:this.$t("support.pictureSize")}),!1):(this.cnInvoiceFile=t,!1)},cnInvoiceUploading:function(t,e,n){this.cnInvoiceMessage=this.$t("support.fileUploadedAndProgressDisappears")},removeCnInvoiceFile:function(){this.cnInvoiceFile="",this.cnInvoiceMessage=""}}}),m=h,f=(n("a89a"),n("2877")),g=Object(f["a"])(m,o,i,!1,null,null,null);e["default"]=g.exports},"4e82":function(t,e,n){"use strict";var a=n("23e7"),o=n("e330"),i=n("59ed"),r=n("7b0b"),l=n("07fa"),c=n("083a"),s=n("577e"),u=n("d039"),d=n("addb"),p=n("a640"),h=n("3f7e"),m=n("99f4"),f=n("1212"),g=n("ea83"),y=[],b=o(y.sort),v=o(y.push),I=u((function(){y.sort(void 0)})),k=u((function(){y.sort(null)})),x=p("sort"),w=!u((function(){if(f)return f<70;if(!(h&&h>3)){if(m)return!0;if(g)return g<603;var t,e,n,a,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)y.push({k:e+a,v:n})}for(y.sort((function(t,e){return e.v-t.v})),a=0;a<y.length;a++)e=y[a].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}})),$=I||!k||!x||!w,B=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:s(e)>s(n)?1:-1}};a({target:"Array",proto:!0,forced:$},{sort:function(t){void 0!==t&&i(t);var e=r(this);if(w)return void 0===t?b(e):b(e,t);var n,a,o=[],s=l(e);for(a=0;a<s;a++)a in e&&v(o,e[a]);d(o,B(t)),n=l(o),a=0;while(a<n)e[a]=o[a++];while(a<s)c(e,a++);return e}})},"841c":function(t,e,n){"use strict";var a=n("c65b"),o=n("d784"),i=n("825a"),r=n("7234"),l=n("1d80"),c=n("129f"),s=n("577e"),u=n("dc4a"),d=n("14c3");o("search",(function(t,e,n){return[function(e){var n=l(this),o=r(e)?void 0:u(e,t);return o?a(o,e,n):new RegExp(e)[t](s(n))},function(t){var a=i(this),o=s(t),r=n(e,a,o);if(r.done)return r.value;var l=a.lastIndex;c(l,0)||(a.lastIndex=0);var u=d(a,o);return c(a.lastIndex,l)||(a.lastIndex=l),null===u?-1:u.index}]}))},"99f4":function(t,e,n){"use strict";var a=n("b5db");t.exports=/MSIE|Trident/.test(a)},a89a:function(t,e,n){"use strict";n("b07c")},addb:function(t,e,n){"use strict";var a=n("f36a"),o=Math.floor,i=function(t,e){var n=t.length;if(n<8){var r,l,c=1;while(c<n){l=c,r=t[c];while(l&&e(t[l-1],r)>0)t[l]=t[--l];l!==c++&&(t[l]=r)}}else{var s=o(n/2),u=i(a(t,0,s),e),d=i(a(t,s),e),p=u.length,h=d.length,m=0,f=0;while(m<p||f<h)t[m+f]=m<p&&f<h?e(u[m],d[f])<=0?u[m++]:d[f++]:m<p?u[m++]:d[f++]}return t};t.exports=i},b07c:function(t,e,n){},e3b7:function(t,e,n){"use strict";n.d(e,"t",(function(){return r})),n.d(e,"s",(function(){return l})),n.d(e,"k",(function(){return c})),n.d(e,"u",(function(){return s})),n.d(e,"n",(function(){return u})),n.d(e,"p",(function(){return d})),n.d(e,"d",(function(){return p})),n.d(e,"a",(function(){return h})),n.d(e,"f",(function(){return m})),n.d(e,"x",(function(){return f})),n.d(e,"w",(function(){return g})),n.d(e,"v",(function(){return y})),n.d(e,"r",(function(){return b})),n.d(e,"A",(function(){return v})),n.d(e,"l",(function(){return I})),n.d(e,"m",(function(){return k})),n.d(e,"e",(function(){return x})),n.d(e,"z",(function(){return w})),n.d(e,"g",(function(){return $})),n.d(e,"j",(function(){return B})),n.d(e,"o",(function(){return M})),n.d(e,"i",(function(){return S})),n.d(e,"h",(function(){return T})),n.d(e,"y",(function(){return _})),n.d(e,"q",(function(){return C})),n.d(e,"c",(function(){return D})),n.d(e,"b",(function(){return F}));var a=n("66df"),o="pms",i="cms",r=function(t){return a["a"].request({url:i+"/channel/distributors/detail",data:t,method:"post"})},l=function(t){return a["a"].request({url:i+"/channel/distributors/info",params:t,method:"get"})},c=function(t){return a["a"].request({url:i+"/channel/distributors/delete",data:t,method:"delete"})},s=function(t){return a["a"].request({url:i+"/channel/distributors/record",data:t,method:"post"})},u=function(t){return a["a"].request({url:i+"/channel/distributors/record/export/"+t.corpId,method:"get",responseType:"blob"})},d=function(t){return a["a"].request({url:i+"/channel/distributors/remunerate/export",params:t,method:"post",responseType:"blob"})},p=function(t){return a["a"].request({url:i+"/channel/newChannel",data:t,method:"post"})},h=function(t){return a["a"].request({url:i+"/channel/updateChannel",data:t,method:"put"})},m=function(t){return a["a"].request({url:i+"/channel/approvalChannel",params:t,method:"put"})},f=function(t){return a["a"].request({url:o+"/packageGroup/queryPackageGroupRelation",params:t,method:"get"})},g=function(t){return a["a"].request({url:o+"/packageGroup/queryPackageGroupDetail",params:t,method:"get"})},y=function(t){return a["a"].request({url:o+"/packageGroup/purchasePart",params:t,method:"get"})},b=function(t){return a["a"].request({url:o+"/packageGroup/queryPackageList",params:t,method:"get"})},v=function(t){return a["a"].request({url:o+"/update",params:t,method:"put"})},I=function(t){return a["a"].request({url:o+"/packageGroup/deleteBatchPackageGroup",data:t,method:"delete"})},k=function(t){return a["a"].request({url:o+"/packageGroup/deletePackageGroup",params:t,method:"delete"})},x=function(t){return a["a"].request({url:o+"/packageGroup/newPackageGroup",data:t,method:"post"})},w=function(t){return a["a"].request({url:o+"/packageGroup/updatePackageGroup",data:t,method:"put"})},$=function(t){return a["a"].request({url:o+"/packageGroup/approvalPackageGroup",params:t,method:"put"})},B=function(t){return a["a"].request({url:o+"/packageGroup/create/byFile",data:t,method:"post",contentType:"multipart/form-data"})},M=function(t){return a["a"].request({url:o+"/packageGroup/packageGroupDetailExport",params:t,method:"get",responseType:"blob"})},S=function(t){return a["a"].request({url:i+"/channel/distributors/channelBill",data:t,method:"post"})},T=function(t){return a["a"].request({url:i+"/channel/distributors/channelBill/export",data:t,method:"post"})},_=function(t){return a["a"].request({url:i+"/channel/getCorpFlowDetail",params:t,method:"get"})},C=function(t){return a["a"].request({url:i+"/channel/corpFlowDetailExport",params:t,method:"get"})},D=function(t){return a["a"].request({url:i+"/channel/distributors/card/suspend",params:t,method:"get"})},F=function(t){return a["a"].request({url:i+"/channel/distributors/card/recover",params:t,method:"get"})}},ea83:function(t,e,n){"use strict";var a=n("b5db"),o=a.match(/AppleWebKit\/(\d+)\./);t.exports=!!o&&+o[1]}}]);