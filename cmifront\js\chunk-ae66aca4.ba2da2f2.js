(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ae66aca4"],{"00b4":function(t,e,n){"use strict";n("ac1f");var a=n("23e7"),o=n("c65b"),i=n("1626"),r=n("825a"),c=n("577e"),s=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),l=/./.test;a({target:"RegExp",proto:!0,forced:!s},{test:function(t){var e=r(this),n=c(t),a=e.exec;if(!i(a))return o(l,e,n);var s=o(a,e,n);return null!==s&&(r(s),!0)}})},"0240":function(t,e,n){"use strict";n.d(e,"k",(function(){return i})),n.d(e,"f",(function(){return r})),n.d(e,"e",(function(){return c})),n.d(e,"m",(function(){return s})),n.d(e,"b",(function(){return l})),n.d(e,"a",(function(){return u})),n.d(e,"j",(function(){return d})),n.d(e,"d",(function(){return p})),n.d(e,"h",(function(){return m})),n.d(e,"g",(function(){return f})),n.d(e,"i",(function(){return h})),n.d(e,"c",(function(){return g})),n.d(e,"l",(function(){return v})),n.d(e,"n",(function(){return b}));n("99af");var a=n("66df"),o="/stat",i=function(t){return a["a"].request({url:o+"/channelIncome/query",data:t,method:"post"})},r=function(t,e,n){return a["a"].request({url:o+"/invoice/create/no/".concat(t,"/").concat(e,"/").concat(n),method:"post"})},c=function(t){return a["a"].request({url:o+"/invoice/create",data:t,method:"post"})},s=function(t){return a["a"].request({url:o+"/channelIncome/update",params:t,method:"put"})},l=function(t){return a["a"].request({url:o+"/channelIncome/createBillTotalTask",data:t,method:"post"})},u=function(t){return a["a"].request({url:o+"/channelIncome/createBillDetailTask",data:t,method:"post"})},d=function(t){return a["a"].request({url:o+"/channelIncome/export/invoice",params:t,method:"get"})},p=function(t){return a["a"].request({url:o+"/channelIncome/auth",params:t,method:"put"})},m=function(t){return a["a"].request({url:"/cms/channel/channelAtzFlowExport",params:t,method:"post"})},f=function(t){return a["a"].request({url:"/cms/channel/channelAtzSummaryExport",params:t,method:"post"})},h=function(t){return a["a"].request({url:"/cms/channelSelfServer/exportImsiCost",data:t,method:"post"})},g=function(t){return a["a"].request({url:o+"/channelIncome/export/batchInvoice",data:t,method:"post"})},v=function(t){return a["a"].request({url:o+"/financialReport/billedImmediately",data:t,method:"post"})},b=function(t){return a["a"].request({url:o+"/channelIncome/updateImsiFee",params:t,method:"get"})}},"07ac":function(t,e,n){"use strict";var a=n("23e7"),o=n("6f53").values;a({target:"Object",stat:!0},{values:function(t){return o(t)}})},"129f":function(t,e,n){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},2315:function(t,e,n){"use strict";n.d(e,"h",(function(){return i})),n.d(e,"e",(function(){return r})),n.d(e,"f",(function(){return c})),n.d(e,"a",(function(){return s})),n.d(e,"i",(function(){return l})),n.d(e,"b",(function(){return u})),n.d(e,"g",(function(){return d})),n.d(e,"d",(function(){return p})),n.d(e,"c",(function(){return m}));var a=n("66df"),o="/stat",i=function(t){return a["a"].request({url:o+"/channelincome/month/getChannelBill",data:t,method:"post"})},r=function(t){return a["a"].request({url:"/charging/atzCharging/getChargingByChannel",params:t,method:"get"})},c=function(t){return a["a"].request({url:"pms/imsiAmount/getChannelImsiAmount",params:t,method:"post"})},s=function(t){return a["a"].request({url:"/cms/IBoss/payBill",data:t,method:"POST",contentType:"multipart/form-data"})},l=function(t){return a["a"].request({url:"/cms/IBoss/cancelPayBill/",params:t,method:"get"})},u=function(t){return a["a"].request({url:"/cms/channel/channelExport",data:t,method:"post"})},d=function(t){return a["a"].request({url:"cms/channel/deposit/record",data:t,method:"post"})},p=function(t){return a["a"].request({url:"cms/channel/deposit/recordExport",data:t,method:"post",responseType:"blob"})},m=function(t){return a["a"].request({url:o+"/channelincome/month/exportChannelBill",data:t,method:"post",responseType:"blob"})}},"3f7e":function(t,e,n){"use strict";var a=n("b5db"),o=a.match(/firefox\/(\d+)/i);t.exports=!!o&&+o[1]},"466d":function(t,e,n){"use strict";var a=n("c65b"),o=n("d784"),i=n("825a"),r=n("7234"),c=n("50c4"),s=n("577e"),l=n("1d80"),u=n("dc4a"),d=n("8aa5"),p=n("14c3");o("match",(function(t,e,n){return[function(e){var n=l(this),o=r(e)?void 0:u(e,t);return o?a(o,e,n):new RegExp(e)[t](s(n))},function(t){var a=i(this),o=s(t),r=n(e,a,o);if(r.done)return r.value;if(!a.global)return p(a,o);var l=a.unicode;a.lastIndex=0;var u,m=[],f=0;while(null!==(u=p(a,o))){var h=s(u[0]);m[f]=h,""===h&&(a.lastIndex=d(o,c(a.lastIndex),l)),f++}return 0===f?null:m}]}))},"4e82":function(t,e,n){"use strict";var a=n("23e7"),o=n("e330"),i=n("59ed"),r=n("7b0b"),c=n("07fa"),s=n("083a"),l=n("577e"),u=n("d039"),d=n("addb"),p=n("a640"),m=n("3f7e"),f=n("99f4"),h=n("1212"),g=n("ea83"),v=[],b=o(v.sort),y=o(v.push),I=u((function(){v.sort(void 0)})),x=u((function(){v.sort(null)})),w=p("sort"),k=!u((function(){if(h)return h<70;if(!(m&&m>3)){if(f)return!0;if(g)return g<603;var t,e,n,a,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)v.push({k:e+a,v:n})}for(v.sort((function(t,e){return e.v-t.v})),a=0;a<v.length;a++)e=v[a].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}})),S=I||!x||!w||!k,T=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:l(e)>l(n)?1:-1}};a({target:"Array",proto:!0,forced:S},{sort:function(t){void 0!==t&&i(t);var e=r(this);if(k)return void 0===t?b(e):b(e,t);var n,a,o=[],l=c(e);for(a=0;a<l;a++)a in e&&y(o,e[a]);d(o,T(t)),n=c(o),a=0;while(a<n)e[a]=o[a++];while(a<l)s(e,a++);return e}})},"6f53":function(t,e,n){"use strict";var a=n("83ab"),o=n("d039"),i=n("e330"),r=n("e163"),c=n("df75"),s=n("fc6a"),l=n("d1e7").f,u=i(l),d=i([].push),p=a&&o((function(){var t=Object.create(null);return t[2]=2,!u(t,2)})),m=function(t){return function(e){var n,o=s(e),i=c(o),l=p&&null===r(o),m=i.length,f=0,h=[];while(m>f)n=i[f++],a&&!(l?n in o:u(o,n))||d(h,t?[n,o[n]]:o[n]);return h}};t.exports={entries:m(!0),values:m(!1)}},"841c":function(t,e,n){"use strict";var a=n("c65b"),o=n("d784"),i=n("825a"),r=n("7234"),c=n("1d80"),s=n("129f"),l=n("577e"),u=n("dc4a"),d=n("14c3");o("search",(function(t,e,n){return[function(e){var n=c(this),o=r(e)?void 0:u(e,t);return o?a(o,e,n):new RegExp(e)[t](l(n))},function(t){var a=i(this),o=l(t),r=n(e,a,o);if(r.done)return r.value;var c=a.lastIndex;s(c,0)||(a.lastIndex=0);var u=d(a,o);return s(a.lastIndex,c)||(a.lastIndex=c),null===u?-1:u.index}]}))},"99f4":function(t,e,n){"use strict";var a=n("b5db");t.exports=/MSIE|Trident/.test(a)},"9a8a":function(t,e,n){},c4cb:function(t,e,n){"use strict";n("9a8a")},caaa:function(t,e,n){"use strict";n.r(e);n("caad"),n("b0c0"),n("ac1f"),n("841c");var a=function(){var t=this,e=t._self._c;return e("Card",[e("Form",{ref:"form",attrs:{"label-width":90,model:t.form,rules:t.ruleInline,inline:""}},[e("FormItem",{attrs:{label:"渠道商名称",prop:"corpId"}},[e("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",multiple:"",filterable:"",placeholder:"下拉选择渠道商"},model:{value:t.form.corpId,callback:function(e){t.$set(t.form,"corpId",e)},expression:"form.corpId"}},t._l(t.corpLists,(function(n,a){return e("Option",{key:a,attrs:{value:n.corpId}},[t._v(t._s(n.corpName))])})),1)],1),e("FormItem",{attrs:{label:"开始月份:",prop:"beginMonth"}},[e("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择开始月份",editable:!0},on:{"on-change":t.startChange},model:{value:t.form.beginMonth,callback:function(e){t.$set(t.form,"beginMonth",e)},expression:"form.beginMonth"}})],1),e("FormItem",{attrs:{label:"结束月份:",prop:"endMonth"}},[e("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择结束月份",editable:!0},on:{"on-change":t.endChange},model:{value:t.form.endMonth,callback:function(e){t.$set(t.form,"endMonth",e)},expression:"form.endMonth"}})],1),e("FormItem",{attrs:{label:"缴费状态",prop:"chargeStatus"}},[e("Select",{staticStyle:{width:"130px"},attrs:{clearable:"",filterable:"",placeholder:"下拉选择缴费状态"},model:{value:t.form.chargeStatus,callback:function(e){t.$set(t.form,"chargeStatus",e)},expression:"form.chargeStatus"}},[e("Option",{attrs:{value:"0"}},[t._v("未付款")]),e("Option",{attrs:{value:"6"}},[t._v("已核销")]),e("Option",{attrs:{value:"1"}},[t._v("待确认到账")]),e("Option",{attrs:{value:"2"}},[t._v("付款成功")]),e("Option",{attrs:{value:"3"}},[t._v("付款失败")]),e("Option",{attrs:{value:"4"}},[t._v("已取消")]),e("Option",{attrs:{value:"5"}},[t._v("线上缴费中")])],1)],1),t._v("  \n    "),e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:t.searchLoading},on:{click:function(e){return t.search("form")}}},[t._v(t._s(t.$t("common.search")))]),t._v("    \n    "),e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],attrs:{type:"success",icon:"md-arrow-down",loading:t.exportLoading},on:{click:function(e){return t.exportHistoryRecords("form")}}},[t._v(t._s(t.$t("stock.exporttb")))])],1),e("div",[e("Table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{"no-data-text":"",border:"","highlight-row":"",columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(n){var a=n.row;n.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"payment",expression:"'payment'"}],staticStyle:{margin:"5px"},attrs:{type:"warning",ghost:"",size:"small",disabled:!["0","4"].includes(a.chargeStatus)||"2"==a.channelType||0==a.realIncome},on:{click:function(e){return t.offinePay(a,"1")}}},[t._v("线下支付")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"rePay",expression:"'rePay'"}],staticStyle:{margin:"5px"},attrs:{type:"error",ghost:"",size:"small",disabled:!["3"].includes(a.chargeStatus)||"2"==a.channelType||0==a.realIncome},on:{click:function(e){return t.offinePay(a,"2")}}},[t._v("重新上传付款证明")])]}},{key:"file",fn:function(n){var a=n.row;n.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"info",expression:"'info'"}],staticStyle:{margin:"5px"},attrs:{type:"primary",ghost:"",size:"small"},on:{click:function(e){return t.showInfo(a)}}},[t._v("明细")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"bill_export",expression:"'bill_export'"}],staticStyle:{margin:"5px"},attrs:{type:"info",ghost:"",size:"small"},on:{click:function(e){return t.exportBillFile(a)}}},[t._v("\n          账单明细下载\n        ")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"invoice",expression:"'invoice'"}],staticStyle:{margin:"5px"},attrs:{type:"success",ghost:"",size:"small",disabled:!a.invoicePath},on:{click:function(e){return t.exportInvoice(a)}}},[t._v("Invoice下载")])]}}])}),e("Page",{attrs:{total:t.total,current:t.page,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage}})],1),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.exportcancelModal},model:{value:t.exportModalr,callback:function(e){t.exportModalr=e},expression:"exportModalr"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v("\n          "+t._s(t.$t("exportMS"))+"\n        ")]),e("FormItem",{attrs:{label:t.$t("exportID")}},[e("span",[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:t.$t("exportFlie")}},[e("span",{staticClass:"task-name"},[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v(t._s(t.$t("downloadResult")))])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.exportcancelModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.Gotor}},[t._v(t._s(t.$t("Goto")))])],1)]),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{width:"500px","align-items":"center","justify-content":"center","margin-bottom":"30px"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"20px"}},[t._v("\n          "+t._s(t.$t("exportMS"))+"\n        ")]),e("FormItem",{attrs:{label:t.$t("exportID")}},[e("ul",{staticStyle:{"margin-bottom":"15px"}},t._l(t.taskIds,(function(n,a){return e("li",{key:t.taskIds.i,attrs:{id:"space"}},[t._v("\n              "+t._s(n)+"\n            ")])})),0),t.remind?e("div",[e("span",[t._v("……")])]):t._e()]),e("FormItem",{attrs:{label:t.$t("exportFlie")}},[e("ul",{staticStyle:{"margin-bottom":"15px"}},t._l(t.taskNames,(function(n,a){return e("li",{key:t.taskNames.i,staticClass:"task-name",attrs:{id:"space"}},[t._v("\n              "+t._s(n)+"\n            ")])})),0),t.remind?e("div",[e("span",[t._v("……")])]):t._e()]),e("span",{staticStyle:{"text-align":"left"}},[t._v(t._s(t.$t("downloadResult")))])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v(t._s(t.$t("Goto")))])],1)]),e("Modal",{attrs:{title:"明细","footer-hide":!0,"mask-closable":!1,width:"500px"},model:{value:t.infoModal,callback:function(e){t.infoModal=e},expression:"infoModal"}},[e("Row",{staticStyle:{margin:"20px"}},[e("Col",{staticStyle:{display:"flex","justify-content":"flex-start"},attrs:{span:"24"}},[e("span",[t._v("流量费：")]),t._v("  "+t._s(t.a2zAmount)+"\n      ")])],1),e("Row",{staticStyle:{margin:"20px"}},[e("Col",{staticStyle:{display:"flex","justify-content":"flex-start"},attrs:{span:"24"}},[e("span",[t._v("卡费：")]),t._v("  "+t._s(t.imsiAmount)+"\n      ")])],1),e("Row",{staticStyle:{margin:"20px"}},[e("Col",{staticStyle:{display:"flex","justify-content":"flex-start"},attrs:{span:"24"}},[e("span",[t._v("套餐费：")]),t._v("  "+t._s(t.directIncome)+"\n      ")])],1)],1),e("Modal",{attrs:{title:t.$t("channelBill.paymentPage"),"footer-hide":!0,"mask-closable":!1,width:"450px"},on:{"on-cancel":t.cancelModal},model:{value:t.PaymentModal,callback:function(e){t.PaymentModal=e},expression:"PaymentModal"}},[e("div",[e("Form",{ref:"formobj",staticStyle:{"font-weight":"bold"},attrs:{model:t.formobj,rules:t.ruleobj,"label-width":100,"label-height":100,inline:""}},[e("FormItem",{staticStyle:{"font-size":"14px","font-weight":"bold"},attrs:{label:t.$t("support.payslip"),prop:"file"}},[e("Upload",{staticStyle:{width:"250px","margin-top":"50px"},attrs:{type:"drag",action:t.uploadUrl,"before-upload":t.handleBeforeUpload,"on-progress":t.fileUploading},model:{value:t.formobj.file,callback:function(e){t.$set(t.formobj,"file",e)},expression:"formobj.file"}},[e("div",{staticStyle:{padding:"20px 0"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v(t._s(t.$t("support.uploadPicture")))])],1)]),t.file?e("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"300px"}},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name)+"\n  \t\t\t\t\t\t")],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e()],1),["2","3","5"].includes(t.rowBillData.accountingType)?e("FormItem",{staticStyle:{"font-size":"14px","font-weight":"bold"},attrs:{label:t.$t("channelBill.cnInvoice"),prop:"cnInvoiceFile"}},[e("Upload",{staticStyle:{width:"250px","margin-top":"20px"},attrs:{type:"drag",action:t.uploadUrl,"before-upload":t.handleCnInvoiceBeforeUpload,"on-progress":t.cnInvoiceUploading},model:{value:t.formobj.cnInvoiceFile,callback:function(e){t.$set(t.formobj,"cnInvoiceFile",e)},expression:"formobj.cnInvoiceFile"}},[e("div",{staticStyle:{padding:"20px 0"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v(t._s(t.$t("channelBill.uploadCnInvoice")))])],1)]),t.cnInvoiceFile?e("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"300px"}},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.cnInvoiceFile.name)+"\n              ")],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeCnInvoiceFile}})])]):t._e()],1):t._e(),e("FormItem",{attrs:{label:t.$t("fuelPack.Amount"),prop:"amount"}},[e("Input",{staticStyle:{width:"250px"},attrs:{placeholder:t.$t("channelBill.inputAmount"),disabled:""},model:{value:t.formobj.amount,callback:function(e){t.$set(t.formobj,"amount",e)},expression:"formobj.amount"}})],1)],1),e("div",{staticStyle:{"text-align":"center",margin:"40px 0 0 0"}},[e("Button",{staticStyle:{"margin-right":"30px"},on:{click:t.cancelModal}},[t._v(t._s(t.$t("support.back")))]),e("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:t.pictureLoading},on:{click:t.pictureSubmit}},[t._v(t._s(t.$t("common.determine")))])],1)],1)]),e("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1)},o=[],i=n("ade3"),r=(n("d9e2"),n("99af"),n("14d9"),n("fb6a"),n("4e82"),n("a9e3"),n("d3b7"),n("07ac"),n("00b4"),n("25f0"),n("3ca3"),n("466d"),n("159b"),n("ddb0"),n("2b3d"),n("bf19"),n("9861"),n("88a7"),n("271a"),n("5494"),n("d4fb")),c=n("2315"),s=n("0240"),l=n("6dfa"),u=(n("c70b"),{data:function(){var t,e=this,n=function(t,n,a){if(parseFloat(n)<0)a(new Error(e.$t("channelBill.lessThan0")));else{var o=n;"-"===n.substr(0,1)&&(o=n.substr(1,n.length));var i=/^(?!0(\.0+)?$)([1-9]\d{0,7}(\.\d{1,2})?|\d{1,8}(\.\d{0,2})?)$/;!o||i.test(o)?a():a(new Error(e.$t("channelBill.checkNumber")))}},a=function(t,n,a){e.file?a():a(new Error(e.$t("support.pleaseUploadFile")))};return t={corpNameListCorpId:[],form:{beginMonth:"",endMonth:"",chargeStatus:"",corpId:[]},searchObj:{name:"",countryName:""},total:0,corpLists:[],pageSize:10,page:1,loading:!1,exportModal:!1,exportModalr:!1,searchLoading:!1,exportLoading:!1,pictureLoading:!1,remind:!1,infoModal:!1,PaymentModal:!1,taskName:"",taskId:"",accountId:"",corpId:""},Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(t,"corpLists",[]),"localList",[]),"taskIds",[]),"taskNames",[]),"data",[]),"columns",[{title:"渠道商名称",key:"corpName",align:"center",tooltip:!0,minWidth:150},{title:"账单ID",key:"billId",align:"center",tooltip:!0,minWidth:160},{title:"账单类型",key:"accountingType",align:"center",tooltip:!0,minWidth:150,render:function(t,e){var n=e.row,a="0"==n.accountingType||"1"==n.accountingType?"代销":"2"==n.accountingType||"3"==n.accountingType?"A2Z":"4"==n.accountingType?"合并":"5"==n.accountingType?"A2Z":"6"==n.accountingType||"7"==n.accountingType?"合并":"";return t("label",a)}},{title:"出账月份",key:"statTime",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var n=e.row,a=n.statTime.slice(0,4),o=n.statTime.slice(4,6),i="".concat(a,"-").concat(o);return t("label",i)}},{title:"账单总金额",key:"saleIncome",align:"center",tooltip:!0,minWidth:150},{title:"应缴账单金额",key:"realIncome",align:"center",minWidth:200,render:function(t,e){var n=e.row,a="2"==n.channelType?"0":n.realIncome;return t("label",a)}},{title:"缴费状态",key:"chargeStatus",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:1e3,render:function(t,e){var n=e.row,a="";return a="2"==n.channelType?"已核销":"0"==n.chargeStatus?"未付款":"1"==n.chargeStatus?"待确认到账":"2"==n.chargeStatus?"付款成功":"3"==n.chargeStatus?"付款失败":"4"==n.chargeStatus?"已取消":"5"==n.chargeStatus?"线上缴费中":"",t("label",a)}},{title:"操作",slot:"action",width:233,align:"center",fixed:"right"},{title:"文件下载",slot:"file",width:275,align:"center",fixed:"right"}]),"ruleInline",{beginMonth:[{type:"date",required:!0,message:"请选择开始月份",trigger:"blur"}],endMonth:[{type:"date",required:!0,message:"请选择结束月份",trigger:"blur"}]}),"uploadUrl",""),"uploadList",[]),"file",null),Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(t,"formobj",{file:"",amount:"",cnInvoiceFile:""}),"ruleobj",{file:[{required:!0,validator:a,trigger:"change"}],amount:[{required:!0,message:this.$t("channelBill.inputAmount"),trigger:"change"},{validator:n}],cnInvoiceFile:[{required:!1,validator:function(t,e,n){n()},trigger:"change"}]}),"cnInvoiceFile",""),"cnInvoiceMessage",""),"searchBeginTime",""),"searchEndTime",""),"type",""),"cooperationMode",""),"rowType",""),"a2zAmount",""),Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(t,"imsiAmount",""),"directIncome",""),"accountId",""),"rowBillData",{})},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode")},created:function(){this.getCoprList()},methods:{getCoprList:function(){var t=this;Object(r["b"])({userName:this.$store.state.user.userName}).then((function(e){"0000"==e.code&&(t.corpLists=e.data,e.data.forEach((function(e){t.corpNameListCorpId.push(e.corpId)})))}))},startChange:function(t){this.searchBeginTime=t},endChange:function(t){this.searchEndTime=t},getTableData:function(t,e){var n=this;this.loading=!0,Object(c["h"])({dateStart:e||this.searchBeginTime,dateEnd:this.searchEndTime,corpId:this.form.corpId.length?this.form.corpId:this.corpNameListCorpId,pageNum:t,pageSize:this.pageSize,billType:this.cooperationMode,chargeStatus:this.form.chargeStatus}).then((function(e){"0000"==e.code&&(n.loading=!1,n.searchLoading=!1,n.page=t,n.currentPage=t,n.total=Number(e.count),n.data=e.data)})).catch((function(t){console.error(t)})).finally((function(){n.loading=!1,n.searchLoading=!1}))},search:function(t){var e=this,n="202409";this.$refs[t].validate((function(t){if(t)if(new Date(e.searchBeginTime)>new Date(e.searchEndTime))e.$Message["warning"]({background:!0,content:"结束月份需大于等于开始月份！"});else if(new Date(e.searchBeginTime)<new Date(n)&&new Date(e.searchEndTime)<new Date(n))e.page=1,e.currentPage=1,e.total=0,e.data=[];else if(new Date(e.searchBeginTime)<new Date(n)&&new Date(e.searchEndTime)>=new Date(n)){var a="202409";e.searchLoading=!0,e.getTableData(1,a)}else e.searchLoading=!0,e.getTableData(1)}))},loadByPage:function(t){var e="202409";if(new Date(this.searchBeginTime)>new Date(this.searchEndTime))this.$Message["warning"]({background:!0,content:"结束月份需大于等于开始月份！"});else if(new Date(this.searchBeginTime)<new Date(e)&&new Date(this.searchEndTime)<new Date(e))this.page=1,this.currentPage=1,this.total=0,this.data=[];else if(new Date(this.searchBeginTime)<new Date(e)&&new Date(this.searchEndTime)>=new Date(e)){var n="202409";this.getTableData(t,n)}else this.getTableData(t)},exportHistoryRecords:function(t){var e=this;this.$refs[t].validate((function(t){if(t){var n="",a="202409";if(new Date(e.searchBeginTime)>new Date(e.searchEndTime))return void e.$Message["warning"]({background:!0,content:"结束月份需大于等于开始月份！"});if(new Date(e.searchBeginTime)<new Date(a)&&new Date(e.searchEndTime)<new Date(a))return void e.$Message["warning"]({background:!0,content:"未找到可导出的数据！"});n=new Date(e.searchBeginTime)<new Date(a)&&new Date(e.searchEndTime)>=new Date(a)?"202409":e.searchBeginTime,e.exportLoading=!0,Object(c["c"])({dateStart:n,dateEnd:e.searchEndTime,corpId:e.form.corpId.length?e.form.corpId:e.corpNameListCorpId,billType:e.cooperationMode,chargeStatus:e.form.chargeStatus}).then((function(t){var n=t.data,a=decodeURI(t.headers["content-disposition"].match(/=(.*)$/)[1]);if("download"in document.createElement("a")){var o=e.$refs.downloadLink,i=URL.createObjectURL(n);o.download=a,o.href=i,o.click(),URL.revokeObjectURL(i)}else navigator.msSaveBlob(n,a)})).catch((function(t){console.log(t)})).finally((function(){e.exportLoading=!1}))}}))},exportInvoice:function(t){var e=this;Object(s["j"])({corpName:t.corpName,invoicePath:t.invoicePath,month:t.statTime,corpId:t.corpId,billType:t.accountingType,incomeId:t.id}).then((function(t){if(!t||"0000"!=t.code)throw t;e.exportModalr=!0,e.taskId=t.data.taskId,e.taskName=t.data.taskName})).catch((function(t){console.log(t)}))},showInfo:function(t){this.a2zAmount=t.a2zAmount,this.imsiAmount=t.imsiAmount,this.directIncome=t.directIncome,this.infoModal=!0},exportBillFile:function(t){var e=this,n="",a="202409";n=new Date(this.searchBeginTime)<new Date(a)&&new Date(this.searchEndTime)>=new Date(a)?"202409":this.searchBeginTime;var o={corpId:t.corpId,beginMonth:n,endMonth:this.searchEndTime,cooperationMode:t.accountingType,corpName:t.corpName,currencyCode:t.currency,billType:t.accountingType,id:t.id,statTime:t.statTime,dateStart:t.svcStartTime,dateEnd:t.svcEndTime,userId:this.$store.state.user.userId,type:"2"==t.accountingType?"1":"3"==t.accountingType?"2":"4"==t.accountingType?1:"5"==t.accountingType?"3":"6"==t.accountingType?"2":"7"==t.accountingType?"3":""};Object(c["b"])(o).then((function(t){if(!t||"0000"!=t.code)throw t;e.exportModal=!0;var n=e;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(n.taskIds.push(t.taskId),n.taskNames.push(t.taskName),n.taskIds.length>3||n.taskNames.length>3){var e=n.taskIds.slice(0,3),a=n.taskNames.slice(0,3);n.taskIds=e,n.taskNames=a,n.remind=!0}}))})).catch((function(t){console.log(t)}))},exportcancelModal:function(){this.exportModalr=!1},offinePay:function(t,e){this.accountId=t.id,this.corpId=t.corpId,this.rowType=e,this.formobj.amount=t.realIncome.toString(),this.rowBillData=t,this.PaymentModal=!0},pictureSubmit:function(){var t=this;this.$refs["formobj"].validate((function(e){if(e){var n=new FormData;n.append("amount",t.formobj.amount),n.append("corpId",t.corpId),n.append("accountId",t.accountId),n.append("paymentProofs",t.file),n.append("repeatedUpload","2"==t.rowType),n.append("chargeType","1"),t.cnInvoiceFile&&n.append("cnInvoice",t.cnInvoiceFile),t.pictureLoading=!0,Object(c["a"])(n).then((function(e){if("0000"!==e.code)throw e;t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.pictureLoading=!1,t.PaymentModal=!1;var n="202409";t.getTableData(1,n),t.file="",t.cnInvoiceFile="",t.$refs["formobj"].resetFields()})).catch((function(e){t.pictureLoading=!1})).finally((function(){}))}}))},handleBeforeUpload:function(t,e){var n=t.size/1024/1024>10;return n?(this.$Notice.warning({title:this.$t("address.Operationreminder"),desc:this.$t("support.pictureSize")}),!1):(this.file=t,this.uploadList=e,!1)},fileUploading:function(t,e,n){this.message=this.$t("support.fileUploadedAndProgressDisappears")},removeFile:function(){this.file=""},handleCnInvoiceBeforeUpload:function(t,e){var n=t.size/1024/1024>10;return n?(this.$Notice.warning({title:this.$t("address.Operationreminder"),desc:this.$t("support.pictureSize")}),!1):(this.cnInvoiceFile=t,!1)},cnInvoiceUploading:function(t,e,n){this.cnInvoiceMessage=this.$t("support.fileUploadedAndProgressDisappears")},removeCnInvoiceFile:function(){this.cnInvoiceFile="",this.cnInvoiceMessage=""},cancelModal:function(){this.taskIds=[],this.taskNames=[],this.exportModal=!1,this.PaymentModal=!1,this.rowBillData={},this.file="",this.cnInvoiceFile="",this.$refs["formobj"].resetFields()},Gotor:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportcancelModal(),this.exportModalr=!1},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},getLocalList:function(){var t=this;Object(l["A"])().then((function(e){if(!e||"0000"!=e.code)throw e;var n=e.data;t.localList=n,t.localList.sort((function(t,e){return t.countryEn.localeCompare(e.countryEn)}))})).catch((function(t){})).finally((function(){}))}}}),d=u,p=(n("c4cb"),n("2877")),m=Object(p["a"])(d,a,o,!1,null,null,null);e["default"]=m.exports},d4fb:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"d",(function(){return r})),n.d(e,"c",(function(){return c})),n.d(e,"a",(function(){return s}));var a=n("66df"),o="/cms",i=function(t){return a["a"].request({url:o+"/channel/getChannelByEmail",params:t,method:"get"})},r=function(t){return a["a"].request({url:o+"/IBoss/getPage",data:t,method:"post"})},c=function(t){return a["a"].request({url:o+"/channel/getChannelSellData",data:t,method:"post"})},s=function(t){return a["a"].request({url:o+"/IBoss/downLoad",params:t,method:"get",responseType:"blob"})}},ea83:function(t,e,n){"use strict";var a=n("b5db"),o=a.match(/AppleWebKit\/(\d+)\./);t.exports=!!o&&+o[1]}}]);