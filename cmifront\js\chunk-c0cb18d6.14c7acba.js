(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c0cb18d6"],{"129f":function(e,t,r){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},"1c31":function(e,t,r){"use strict";r.d(t,"t",(function(){return o})),r.d(t,"s",(function(){return i})),r.d(t,"a",(function(){return l})),r.d(t,"b",(function(){return s})),r.d(t,"c",(function(){return c})),r.d(t,"d",(function(){return u})),r.d(t,"e",(function(){return d})),r.d(t,"f",(function(){return m})),r.d(t,"g",(function(){return p})),r.d(t,"h",(function(){return f})),r.d(t,"i",(function(){return h})),r.d(t,"j",(function(){return g})),r.d(t,"k",(function(){return b})),r.d(t,"l",(function(){return y})),r.d(t,"x",(function(){return v})),r.d(t,"m",(function(){return k})),r.d(t,"n",(function(){return x})),r.d(t,"o",(function(){return T})),r.d(t,"p",(function(){return w})),r.d(t,"q",(function(){return q})),r.d(t,"v",(function(){return O})),r.d(t,"r",(function(){return F})),r.d(t,"w",(function(){return j})),r.d(t,"u",(function(){return I}));var a=r("66df"),n="/stat",o=function(e){return a["a"].request({url:"/cms/api/v1/packageCard/countReuseExport",data:e,responseType:"blob",method:"post"})},i=function(e){return a["a"].request({url:"/cms/api/v1/packageCard/countReuse",data:e,method:"post"})},l=function(e){return a["a"].request({url:n+"/activereport/detailDownload",params:e,responseType:"blob",method:"get"})},s=function(e){return a["a"].request({url:n+"/activereport/pageList",data:e,method:"post"})},c=function(e){return a["a"].request({url:n+"/cardReport",params:e,method:"get"})},u=function(e){return a["a"].request({url:n+"/cardReport/export",params:e,responseType:"blob",method:"get"})},d=function(e){return a["a"].request({url:n+"/offline/export",params:e,responseType:"blob",method:"get"})},m=function(e){return a["a"].request({url:n+"/offline/import",data:e,method:"post"})},p=function(e){return a["a"].request({url:n+"/offline/pageList",data:e,method:"post"})},f=function(e){return a["a"].request({url:n+"/operatorsettle/detailDownload",params:e,responseType:"blob",method:"get"})},h=function(e){return a["a"].request({url:n+"/operatorsettle/pageList",data:e,method:"post"})},g=function(e){return a["a"].request({url:n+"/postpaidsettle/detailDownload",params:e,responseType:"blob",method:"get"})},b=function(e){return a["a"].request({url:n+"/postpaidsettle/pageList",data:e,method:"post"})},y=function(e){return a["a"].request({url:n+"/rate",params:e,method:"get"})},v=function(e){return a["a"].request({url:n+"/rate",data:e,method:"post"})},k=function(e){return a["a"].request({url:n+"/rate/export",params:e,responseType:"blob",method:"get"})},x=function(e){return a["a"].request({url:n+"/report/package/analysis/export",params:e,responseType:"blob",method:"get"})},T=function(e){return a["a"].request({url:n+"/report/package/analysis/search",data:e,method:"post"})},w=function(e){return a["a"].request({url:n+"/terminalsettle/detailDownload",params:e,responseType:"blob",method:"get"})},q=function(e){return a["a"].request({url:n+"/terminalsettle/pageList",data:e,method:"post"})},O=function(e){return a["a"].request({url:"/charging/cost/supplierCostQuery",data:e,method:"post"})},F=function(e){return a["a"].request({url:"/charging/cost/supplierCostExport",data:e,responseType:"blob",method:"post"})},j=function(e){return a["a"].request({url:"/cms/esim/getEsimcardStats",params:e,method:"get"})},I=function(e){return a["a"].request({url:"/cms/esim/exportEsimcardStats",params:e,method:"get"})}},"841c":function(e,t,r){"use strict";var a=r("c65b"),n=r("d784"),o=r("825a"),i=r("7234"),l=r("1d80"),s=r("129f"),c=r("577e"),u=r("dc4a"),d=r("14c3");n("search",(function(e,t,r){return[function(t){var r=l(this),n=i(t)?void 0:u(t,e);return n?a(n,t,r):new RegExp(t)[e](c(r))},function(e){var a=o(this),n=c(e),i=r(t,a,n);if(i.done)return i.value;var l=a.lastIndex;s(l,0)||(a.lastIndex=0);var u=d(a,n);return s(a.lastIndex,l)||(a.lastIndex=l),null===u?-1:u.index}]}))},b35e:function(e,t,r){"use strict";r("d9e2");t["a"]={methods:{validateDate:function(e,t,r){var a=this.form.endDate||this.form.endTime,n=this.form.startDate||this.form.startTime;a&&n?"startDate"===e.field||"startTime"===e.field?this.$time(t,">",a)?r(new Error("开始时间不能大于结束时间")):r():this.$time(t,"<",a)?r(new Error("结束时间不能小于开始时间")):r():r()}}}},ca4d:function(e,t,r){"use strict";r.r(t);r("ac1f"),r("841c");var a=function(){var e=this,t=e._self._c;return t("Card",[t("div",{staticStyle:{display:"flex",width:"100%"}},[t("Form",{ref:"form",attrs:{"label-width":0,model:e.form,rules:e.rule,inline:""}},[t("FormItem",{attrs:{prop:"feeName"}},[t("Input",{staticStyle:{width:"150px"},attrs:{placeholder:"输入费用名称",prop:"showTitle",clearable:""},model:{value:e.form.feeName,callback:function(t){e.$set(e.form,"feeName",t)},expression:"form.feeName"}})],1),t("FormItem",{attrs:{prop:"currencyCode"}},[t("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择币种"},on:{"on-select":function(t){return e.choose(t)}},model:{value:e.form.currencyCode,callback:function(t){e.$set(e.form,"currencyCode",t)},expression:"form.currencyCode"}},e._l(e.typeList,(function(r,a){return t("Option",{key:a,attrs:{value:r.id}},[e._v(e._s(r.value))])})),1)],1),t("FormItem",{attrs:{prop:"type"}},[t("Select",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{clearable:!0,placeholder:"请选择统计维度"},on:{"on-change":function(t){e.date="",e.resetField(["startTime","endTime"])}},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},e._l(e.cycleList,(function(r,a){return t("Option",{key:a,attrs:{value:r.id}},[e._v(e._s(r.value))])})),1)],1),t("FormItem",{attrs:{prop:"endTime"}},["2"!=e.form.type?t("FormItem",{attrs:{prop:"startTime"}},[t("DatePicker",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{format:"yyyyMMdd",editable:!1,type:"daterange",placeholder:"选择时间段",clearable:""},on:{"on-change":e.checkDatePicker},model:{value:e.date,callback:function(t){e.date=t},expression:"date"}})],1):e._e(),"2"==e.form.type?t("FormItem",{attrs:{prop:"startTime"}},[t("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择开始月份",editable:!1},on:{"on-change":function(t){return e.checkDatePicker(t,1)}}})],1):e._e(),"2"==e.form.type?t("FormItem",{attrs:{prop:"endTime"}},[t("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择结束月份",editable:!1},on:{"on-change":function(t){return e.checkDatePicker(t,2)}}})],1):e._e()],1),t("FormItem",[t("Button",{attrs:{type:"primary",icon:"md-search",size:"large"},on:{click:function(t){return e.search()}}},[e._v("搜索")]),t("Button",{staticStyle:{"margin-left":"20px"},attrs:{type:"success",icon:"ios-cloud-download-outline",size:"large"},on:{click:function(t){return e.exportTable()}}},[e._v("导出")])],1),t("FormItem",[t("Button",{staticStyle:{"margin-left":"20px"},attrs:{type:"success",icon:"ios-cloud-upload-outline",size:"large"},on:{click:e.openModal}},[e._v("导入")])],1)],1)],1),t("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:e.columns12,data:e.data,loading:e.loading}}),t("div",{staticStyle:{"margin-left":"38%","margin-top":"100px","margin-bottom":"160px"}},[t("Page",{attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1),e.data1.length?t("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:e.columns12,data:e.data1,loading:e.loading}}):e._e(),t("Modal",{attrs:{title:"导入线下报表","footer-hide":!0,"mask-closable":!1},on:{"on-cancel":e.cancelModal},model:{value:e.importModalFlag,callback:function(t){e.importModalFlag=t},expression:"importModalFlag"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("Form",{ref:"editObj",attrs:{model:e.editObj,"label-width":100,rules:e.ruleEditValidate}},[t("FormItem",{attrs:{label:"费用名称",prop:"feeName"}},[t("Input",{attrs:{clearable:!0,maxlength:"50",placeholder:"请输入费用名称"},model:{value:e.editObj.feeName,callback:function(t){e.$set(e.editObj,"feeName",t)},expression:"editObj.feeName"}})],1),t("FormItem",{attrs:{label:"币种",prop:"currencyCode"}},[t("Select",{attrs:{placeholder:"请选择币种"},on:{"on-select":function(t){return e.choose(t)}},model:{value:e.editObj.currencyCode,callback:function(t){e.$set(e.editObj,"currencyCode",t)},expression:"editObj.currencyCode"}},e._l(e.typeList,(function(r,a){return t("Option",{key:a,attrs:{value:r.id}},[e._v(e._s(r.value))])})),1)],1),t("FormItem",{attrs:{label:"费用产生时间",prop:"feeTime"}},[t("DatePicker",{staticStyle:{width:"100%"},attrs:{type:"date",format:"yyyyMMdd",placeholder:"请选择时间段"},on:{"on-change":e.changeTime},model:{value:e.date1,callback:function(t){e.date1=t},expression:"date1"}})],1),t("FormItem",{attrs:{label:"金额",prop:"amount"}},[t("Input",{attrs:{clearable:!0,maxlength:"13",placeholder:"请输入金额"},model:{value:e.editObj.amount,callback:function(t){e.$set(e.editObj,"amount",t)},expression:"editObj.amount"}})],1)],1),t("div",{staticStyle:{"text-align":"center"}},[t("Button",{attrs:{type:"primary"},on:{click:e.importSubmit}},[e._v("提交")]),t("Button",{staticStyle:{"margin-left":"8px"},on:{click:function(t){return e.reset("editObj")}}},[e._v("重置")])],1)],1)])],1)},n=[],o=r("5530"),i=(r("caad"),r("14d9"),r("e9c4"),r("b64b"),r("d3b7"),r("2532"),r("3ca3"),r("159b"),r("ddb0"),r("2b3d"),r("bf19"),r("9861"),r("88a7"),r("271a"),r("5494"),r("1c31")),l=r("b35e"),s={mixins:[l["a"]],data:function(){return{date:"",date1:"",loading:!1,form:{feeName:"",endTime:"",type:"",startTime:"",currencyCode:""},rule:{startTime:[{required:!0,message:"请选择时间"}],endTime:[{required:!0,message:"请选择时间"}],type:[{required:!0,message:"请选择维度"}]},total:0,currentPage:1,cycleList:[{id:1,value:"日"},{id:2,value:"月"}],typeList:[{id:156,value:"人民币"},{id:344,value:"港币"},{id:840,value:"美元"}],sellList:[{value:"102",label:"API"},{value:"103",label:"官网（H5）"},{value:"104",label:"北京移动"},{value:"105",label:"批量售卖"},{value:"106",label:"推广活动"},{value:"110",label:"测试渠道"},{value:"111",label:"合作发卡"},{value:"112",label:"后付费发卡"},{value:"113",label:"WEB"},{value:"114",label:"流量池WEB"}],columns12:[{title:"费用名称",key:"feeName",align:"center"},{title:"产生时间",key:"feeTime",align:"center"},{title:"币种",key:"currencyCode",align:"center",render:function(e,t){var r=t.row,a="156"==r.currencyCode?"人民币":"840"==r.currencyCode?"美元":"344"==r.currencyCode?"港币":"";return e("label",a)}},{title:"金额",key:"salesIncome",align:"center",render:function(e,t){return e("span",t.row.salesIncome)}},{title:"汇总港币",key:"hkdIncome",align:"center",render:function(e,t){return e("span",t.row.hkdIncome)}}],data:[],data1:[],editObj:{amount:"",currencyCode:"",feeName:"",feeTime:""},ruleEditValidate:{amount:[{required:!0,message:"时间不能为空"},{pattern:/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/,message:"押金金额最高支持10位整数和2位小数正数或零"}],currencyCode:[{required:!0,message:"币种不能为空"}],feeName:[{required:!0,message:"费用名称不能为空"}],feeTime:[{required:!0,message:"费用产生时间不能为空"}]},importModalFlag:!1}},created:function(){this.rule.startTime.push({validator:this.validateDate,trigger:"change"}),this.rule.endTime.push({validator:this.validateDate,trigger:"change"})},mounted:function(){},methods:{changeTime:function(e){this.editObj.feeTime=e},openModal:function(){this.importModalFlag=!0},cancelModal:function(){this.importModalFlag=!1},resetField:function(e){this.$refs["form"].fields.forEach((function(t){e.includes(t.prop)&&t.resetField()}))},checkDatePicker:function(e,t){Array.isArray(e)?(this.form.startTime=e[0],this.form.endTime=e[1]):1===t?this.form.startTime=e:this.form.endTime=e},goPageFirst:function(e){var t=this;0===e&&(this.currentPage=1);var r=this,a=this.currentPage,n=10;this.$refs["form"].validate((function(l){l?(t.loading=!0,Object(i["g"])(Object(o["a"])({page:a,pageSize:n},t.form)).then((function(a){"0000"==a.code&&(r.loading=!1,t.page=e,t.total=a.data.total,t.data=a.data.offlineDayList||a.data.offlineMonthList,t.data1=[{feeName:"合计",hkdIncome:a.data.sum}])})).catch((function(e){"1000"===e.code&&(t.data=t.data1=[])})).finally((function(){t.loading=!1}))):t.$Message.error("参数校验不通过")}))},goPage:function(e){this.goPageFirst(e)},search:function(){this.goPageFirst(0)},reset:function(e){this.$refs[e].resetFields()},importSubmit:function(){var e=this;this.$refs["editObj"].validate((function(t){if(t){var r=JSON.parse(JSON.stringify(e.editObj));r.amount=e.$moneyCover(r.amount,100),Object(i["f"])(r).then((function(t){"0000"===t.code&&e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.reset("editObj"),e.date1="",e.importModalFlag=!1}))}}))},exportTable:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(i["e"])(Object(o["a"])({},e.form)).then((function(e){var t=e.data,r="收入报表统计.csv";if("download"in document.createElement("a")){var a=document.createElement("a"),n=URL.createObjectURL(t);a.download=r,a.href=n,a.click(),URL.revokeObjectURL(n)}else navigator.msSaveBlob(t,r)})).catch((function(){return e.downloading=!1}))}))},details:function(e){this.$router.push({path:"/channel/detailsList"})}}},c=s,u=r("2877"),d=Object(u["a"])(c,a,n,!1,null,null,null);t["default"]=d.exports}}]);