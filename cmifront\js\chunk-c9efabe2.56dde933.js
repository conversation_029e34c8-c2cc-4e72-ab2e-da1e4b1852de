(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c9efabe2"],{"00b4":function(t,e,a){"use strict";a("ac1f");var s=a("23e7"),n=a("c65b"),r=a("1626"),o=a("825a"),d=a("577e"),i=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),l=/./.test;s({target:"RegExp",proto:!0,forced:!i},{test:function(t){var e=o(this),a=d(t),s=e.exec;if(!r(s))return n(l,e,a);var i=n(s,e,a);return null!==i&&(o(i),!0)}})},"0697":function(t,e){},"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"4f1f":function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),a.d(e,"b",(function(){return n}));var s=function(){var t=this;t._self._c,t._self._setupProxy;return t._m(0)},n=[function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticStyle:{padding:"10px 10px"}},[e("h4",{staticStyle:{"text-align":"left"}},[t._v("确保口令满足以下通用原则")]),e("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n\t\t\t1、口令至少由8位及以上大写字母、小写字母、数字与特殊符号等4类中3类混合、随机组成，尽量不要以姓名、电话号码以及出生日期等作为口令或者口令的组成部分；\n\t\t")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n\t\t\t2、口令应与用户名无相关性，口令中不得包含用户名的完整字符串、大小写变位或形似变换的字符串，如teleadmin:teleadmin、teleadmin:teleadmin2017、teleadmin:TeleAdmin、teleadmin:te!e@dmin等；\n\t\t")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n\t\t\t3、应更换系统或设备的出厂默认口令，如huawei:huawei@123，oracle数据库中SYS:CHANGE_ON_INSTALL,某移动定制版光猫默认帐号CMCCAdmin:aDm8H%MdA等；\n\t\t")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n\t\t\t4、口令设置应避免3位以上（含3位）键盘排序密码，如qwe（键盘第1行前三个字母）、asd（键盘第2行前三个字母）、qaz（键盘第1列三个字母）、1qaz（键盘第1列数字加前三个字母）、！QAZ（键盘第1列特殊字符加前三个字母）等；\n\t\t")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n\t\t\t5、口令中不能出现3位以上（含三位）连续字母、数字、特殊字符，如ABC、Abc、123、！@#等；\n\t\t")]),e("span",[t._v("\n\t\t\t6、口令中不能出现3位以上（含三位）重复字母、数字、特殊字符，如AAA、Aaa、111、###等。\n\t\t")]),e("span",[t._v("\n\t\t\t7、当前密码不能与近10次使用密码重复。\n\t\t")])]),e("h4",{staticStyle:{"text-align":"left"}},[t._v("避免以下易猜解口令规则")]),e("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n\t\t\t1、省份、地市名称、邮箱、电话区号、邮政编码及缩写和简单数字或shift键+简单数字，如BJYD123、HBYD!@#等；\n\t\t")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n\t\t\t2、单位名称、专业名称、系统名称、厂家名称（含缩写）和简单数字，如HBnmc123、HBsmc_123等；\n\t\t")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n\t\t\t3、维护人员名字全拼大小写缩写等变形+设备IP地址（一位或两位）或出生年月日等，如维护人员张三，维护设备地址,出生日期为19951015，则其可能的弱口令为zhangsan100、zhangsan101，zhangsan10100，zhangsan10101，zhangsan19951015，ZS19951015等；\n\t\t")])])])}]},5599:function(t,e){},"841c":function(t,e,a){"use strict";var s=a("c65b"),n=a("d784"),r=a("825a"),o=a("7234"),d=a("1d80"),i=a("129f"),l=a("577e"),c=a("dc4a"),u=a("14c3");n("search",(function(t,e,a){return[function(e){var a=d(this),n=o(e)?void 0:c(e,t);return n?s(n,e,a):new RegExp(e)[t](l(a))},function(t){var s=r(this),n=l(t),o=a(e,s,n);if(o.done)return o.value;var d=s.lastIndex;i(d,0)||(s.lastIndex=0);var c=u(s,n);return i(s.lastIndex,d)||(s.lastIndex=d),null===c?-1:c.index}]}))},8663:function(t,e,a){"use strict";a.r(e);a("b0c0"),a("ac1f"),a("841c");var s=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticStyle:{width:"100%","margin-top":"50px",margin:"auto"}},[e("div",{staticStyle:{display:"flex",width:"100%"}},[e("span",{staticStyle:{"margin-top":"4px","font-weight":"bold"}},[t._v(t._s(t.$t("address.account"))+":")]),t._v("  \n\t\t\t\t"),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("address.account"),prop:"showTitle",clearable:""},model:{value:t.fullname,callback:function(e){t.fullname=e},expression:"fullname"}}),t._v("  \n\t\t\t\t"),e("span",{staticStyle:{"margin-top":"4px","font-weight":"bold"}},[t._v(t._s(t.$t("address.mailbox"))+":")]),t._v("  \n\t\t\t\t"),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("address.input_mailbox"),prop:"showTitle",clearable:""},model:{value:t.mailbox,callback:function(e){t.mailbox=e},expression:"mailbox"}}),t._v("  \n\t\t\t\t"),e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{disabled:"3"==t.cooperationMode,type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v(t._s(t.$t("address.search")))])],1),e("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:t.columns12,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"username",fn:function(a){var s=a.row;return[e("strong",[t._v(t._s(s.username))])]}},{key:"email",fn:function(a){var s=a.row;return[e("strong",[t._v(t._s(s.email))])]}},{key:"address",fn:function(a){var s=a.row;return[e("strong",[t._v(t._s(s.address))])]}},{key:"action",fn:function(a){var s=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"info",size:"small"},on:{click:function(e){return t.update(s)}}},[t._v(t._s(t.$t("address.modify")))]),s.username!==t.username?e("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"error",size:"small"},on:{click:function(e){return t.Delete(s)}}},[t._v(t._s(t.$t("address.Delete")))]):t._e(),e("Button",{directives:[{name:"has",rawName:"v-has",value:"updatePwd",expression:"'updatePwd'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"warning",size:"small"},on:{click:function(e){return t.forgetpwd(s)}}},[t._v(t._s(t.$t("address.Forgetpwd")))])]}}])}),e("div",{staticStyle:{"margin-top":"100px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:t.$t("address.Newaddress"),"mask-closable":!1},on:{"on-cancel":t.cancelModal},model:{value:t.addmodel,callback:function(e){t.addmodel=e},expression:"addmodel"}},[e("Form",{ref:"addform",attrs:{model:t.addform,rules:t.rules,"label-width":100}},[e("FormItem",{attrs:{label:t.$t("address.fullname"),prop:"name"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("address.input_fullname"),prop:"showTitle",clearable:""},model:{value:t.addform.name,callback:function(e){t.$set(t.addform,"name",e)},expression:"addform.name"}})],1),e("FormItem",{attrs:{label:t.$t("address.mailbox"),prop:"email"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("address.input_mailbox"),prop:"showTitle",clearable:""},model:{value:t.addform.email,callback:function(e){t.$set(t.addform,"email",e)},expression:"addform.email"}})],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.addsave}},[t._v("确定")])],1)],1),e("Modal",{attrs:{title:t.$t("address.modifyaddress"),"mask-closable":!1},on:{"on-cancel":t.updatecancelModal},model:{value:t.updatemodel,callback:function(e){t.updatemodel=e},expression:"updatemodel"}},[e("Form",{ref:"updateform",attrs:{model:t.updateform,rules:t.rules,"label-width":80}},[e("FormItem",{attrs:{label:t.$t("address.mailbox"),prop:"email"}},[e("Input",{staticStyle:{width:"300px"},attrs:{placeholder:t.$t("address.input_mailbox"),prop:"showTitle",clearable:""},model:{value:t.updateform.email,callback:function(e){t.$set(t.updateform,"email",e)},expression:"updateform.email"}})],1)],1),e("div",{staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.updatecancelModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.updatesave}},[t._v(t._s(t.$t("address.determine")))])],1)],1),e("Modal",{attrs:{title:t.$t("address.Forgetpwd"),"mask-closable":!1},on:{"on-cancel":t.cancelModal},model:{value:t.updatepwd,callback:function(e){t.updatepwd=e},expression:"updatepwd"}},[e("Form",{ref:"pwdform",attrs:{model:t.pwdform,rules:t.pwdrules,"label-width":130}},[e("FormItem",{attrs:{label:t.$t("address.oldPwd"),prop:"password"}},[e("Input",{staticStyle:{width:"300px"},attrs:{type:"password",password:"",placeholder:t.$t("address.input_pwd")},model:{value:t.pwdform.password,callback:function(e){t.$set(t.pwdform,"password",e)},expression:"pwdform.password"}})],1),e("FormItem",{attrs:{label:t.$t("address.newpwd"),prop:"newpwd"}},[e("Input",{staticStyle:{width:"300px"},attrs:{type:"password",password:"",placeholder:t.$t("address.input_pwd")},model:{value:t.pwdform.newpwd,callback:function(e){t.$set(t.pwdform,"newpwd",e)},expression:"pwdform.newpwd"}})],1),e("FormItem",{attrs:{label:t.$t("address.password_ok"),prop:"password_ok"}},[e("Input",{staticStyle:{width:"300px"},attrs:{type:"password",password:"",placeholder:t.$t("address.input_pwd")},model:{value:t.pwdform.password_ok,callback:function(e){t.$set(t.pwdform,"password_ok",e)},expression:"pwdform.password_ok"}})],1)],1),e("Alert",{attrs:{type:"warning","show-icon":""}},[t._v(t._s(t.$t("address.PwdRules"))),e("a",{attrs:{href:"#"},on:{click:function(e){return t.showRules(1)}}},[t._v(t._s(t.$t("address.watch")))]),t._v(t._s(t.$t("address.more")))]),e("div",{staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.pwdsave}},[t._v(t._s(t.$t("address.determine")))])],1)],1),e("Modal",{attrs:{"mask-closable":!1},on:{"on-cancel":function(e){return t.showRules(2)}},model:{value:t.Rules,callback:function(e){t.Rules=e},expression:"Rules"}},[e("Alert",{attrs:{type:"warning"}},["zh-CN"===this.$i18n.locale?e("div",[e("text-view")],1):t._e(),"en-US"===this.$i18n.locale?e("div",[e("text-viewEn")],1):t._e()])],1)],1)])},n=[],r=a("5530"),o=(a("d9e2"),a("14d9"),a("d3b7"),a("00b4"),a("6dfa")),d=a("f40d"),i=a("df4de"),l=a("2f62"),c={components:{TextView:d["default"],TextViewEn:i["default"]},data:function(){var t=this,e=function(e,a,s){a==t.pwdform.newpwd?s():s(new Error(t.$t("address.inconsistent")))},a=function(e,a,s){a&&-1!=a.indexOf("@")?s():s(new Error(t.$t("address.emailaddress")))},s=function(e,a,s){var n=/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/;if(0!=n.test(a))if(/(.)\1{2}/i.test(a))s(new Error(t.$t("address.appear")));else{var r=/((?:0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)|9(?=0)){2}\d)/;r.test(a)?s(new Error(t.$t("address.allowed"))):s()}else s(new Error(t.$t("address.reset")))};return{cooperationMode:"",fullname:"",mailbox:"",addform:{},Rules:!1,updateform:{},pwdform:{},form:{},total:0,corpId:"",currentPage:1,page:0,addmodel:!1,updatepwd:!1,updatemodel:!1,defaultflg:!1,loading:!1,username:"",ifsame:!1,searchloading:!1,columns12:[{title:this.$t("address.account"),slot:"username",align:"center",render:function(e,a){var s=a.row;return t.ifsame=s.username!==t.username,e("label",s.username)}},{title:this.$t("address.mailbox"),slot:"email",align:"center"},{title:this.$t("address.action"),slot:"action",align:"center"}],data:[],data1:[],rules:{email:[{required:!0,message:this.$t("address.input_mailbox"),trigger:"blur"},{required:!0,validator:a}],password:[{required:!0,message:this.$t("address.input_pwd"),trigger:"blur"}],confirmPwd:[{required:!0,message:this.$t("address.input_pwd"),trigger:"blur"}]},pwdrules:{password:[{required:!0,message:this.$t("address.input_pwd"),trigger:"blur"},{validator:s,trigger:"blur"}],newpwd:[{required:!0,message:this.$t("address.input_pwd"),trigger:"blur"},{validator:s,trigger:"blur"}],password_ok:[{required:!0,message:this.$t("address.input_pwd"),trigger:"blur"},{required:!0,validator:e}]}}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),this.username=this.$store.state.user.userName,"3"!=this.cooperationMode&&this.goPageFirst(1)},methods:Object(r["a"])(Object(r["a"])({},Object(l["b"])(["handleLogOut"])),{},{goPageFirst:function(t){var e=this;this.loading=!0;var a=this;Object(o["F"])({userName:this.$store.state.user.userName}).then((function(s){if("0000"==s.code){var n=s.data;e.corpId=n;var r=t,d=10,i=""===e.fullname?null:e.fullname,l=""===e.mailbox?null:e.mailbox;Object(o["c"])({pageSize:d,pageNum:r,username:i,email:l,corpId:n}).then((function(s){"0000"==s.code&&(a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.total=s.data.total,e.data=s.data.records)})).catch((function(t){console.error(t)})).finally((function(){e.loading=!1,e.searchloading=!1}))}})).catch((function(t){console.error(t)})).finally((function(){e.loading=!1}))},goPage:function(t){this.goPageFirst(t)},search:function(){this.goPageFirst(1),this.searchloading=!0},address:function(){this.addmodel=!0},showRules:function(t){this.Rules=1===t},update:function(t){this.data1=t,this.updateform=Object.assign({},t),this.updatemodel=!0},forgetpwd:function(t){this.data1=t,this.updatepwd=!0},Delete:function(t){var e=this;this.$Modal.confirm({title:this.$t("address.deleteitem"),onOk:function(){var a=t.id;Object(o["i"])({id:a}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:e.$t("address.Operationreminder"),desc:e.$t("address.deleted")}),e.goPageFirst(1)})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))}})},addsave:function(){var t=this;this.$refs.addform.validate((function(e){e&&Object(o["z"])(t.addform).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:t.$t("address.Operationreminder"),desc:"新建地址成功！"}),t.addmodel=!1,t.goPageFirst(1)})).catch((function(t){console.log(t)})).finally((function(){t.loading=!1}))}))},updatesave:function(){var t=this;this.$refs.updateform.validate((function(e){e&&Object(o["M"])({id:t.data1.id,email:t.updateform.email}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.updatemodel=!1,t.goPageFirst(1)})).catch((function(t){console.log(t)})).finally((function(){t.updatemodel=!1,t.loading=!1}))}))},pwdsave:function(){var t=this;this.$refs.pwdform.validate((function(e){if(e){var a={id:t.data1.id,newPass:t.pwdform.newpwd,oldPass:t.pwdform.password};Object(o["o"])(a).then((function(e){if(!e||"0000"!=e.code)throw e;if(t.data1.username===t.username){t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("sys.successfully")});var a=t;setTimeout((function(){a.logout()}),1e3)}else t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.updatepwd=!1,t.pwdform={password:"",newpwd:"",password_ok:""},t.$refs.pwdform.resetFields(),t.goPageFirst(1)})).catch((function(t){console.log(t)})).finally((function(){t.loading=!1,t.updatepwd=!1,t.pwdform={password:"",newpwd:"",password_ok:""},t.$refs.pwdform.resetFields()}))}}))},logout:function(){var t=this;this.handleLogOut().then((function(){t.$router.push({name:"login"})}))},cancelModal:function(){this.updatepwd=!1,this.addmodel=!1,this.pwdform={password:"",newpwd:"",password_ok:""},this.$refs.pwdform.resetFields()},updatecancelModal:function(){this.updatemodel=!1,this.$refs.updateform.resetFields()}})},u=c,p=a("2877"),f=Object(p["a"])(u,s,n,!1,null,null,null);e["default"]=f.exports},a3a7:function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),a.d(e,"b",(function(){return n}));var s=function(){var t=this;t._self._c,t._self._setupProxy;return t._m(0)},n=[function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticStyle:{padding:"10px 10px"}},[e("h4",{staticStyle:{"text-align":"left"}},[t._v("Password character policy:")]),e("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n     1. The password must be 8 characters or more and contain at least one uppercase character, at least one lowercase character, at least one number and at least one special symbol;\n    ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n      2. The password shall not contain any three identical consecutive (ABC, Abc, 123, !@# etc) and repetitive characters (AAA, Aaa, 111, ### etc)\n    ")])])])}]},c5de:function(t,e,a){"use strict";var s=a("0697"),n=a.n(s);e["default"]=n.a},df4de:function(t,e,a){"use strict";var s=a("a3a7"),n=a("c5de"),r=a("2877"),o=Object(r["a"])(n["default"],s["a"],s["b"],!1,null,null,null);e["default"]=o.exports},e5ca:function(t,e,a){"use strict";var s=a("5599"),n=a.n(s);e["default"]=n.a},f40d:function(t,e,a){"use strict";var s=a("4f1f"),n=a("e5ca"),r=a("2877"),o=Object(r["a"])(n["default"],s["a"],s["b"],!1,null,null,null);e["default"]=o.exports}}]);