(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-da68b3ee"],{"1a2de":function(t,e,a){"use strict";a.r(e);a("caad"),a("b0c0"),a("498a");var i=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("套餐ID:")]),e("Input",{staticStyle:{width:"210px"},attrs:{clearable:"",placeholder:"请输入模板ID"},model:{value:t.packageId,callback:function(e){t.packageId=e},expression:"packageId"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("套餐名称:")]),e("Input",{staticStyle:{width:"210px"},attrs:{clearable:"",placeholder:"清输入模板名称"},model:{value:t.packageName,callback:function(e){t.packageName=e},expression:"packageName"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("支持国家:")]),e("Select",{staticStyle:{width:"210px"},attrs:{clearable:"",placeholder:"请选择国家/地区",filterable:""},model:{value:t.mcc,callback:function(e){t.mcc=e},expression:"mcc"}},t._l(t.continentList,(function(a){return e("Option",{key:a.id,attrs:{value:a.mcc}},[t._v(t._s(a.countryEn)+"\n\t\t\t\t")])})),1)],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("渠道商:")]),e("Select",{staticStyle:{width:"210px"},attrs:{clearable:"",placeholder:"请选择渠道商",filterable:""},model:{value:t.corpId,callback:function(e){t.corpId=e},expression:"corpId"}},t._l(t.corpList,(function(a,i){return e("Option",{key:i,attrs:{value:a.corpId}},[t._v(t._s(a.corpName)+"\n\t\t\t\t")])})),1)],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("审核状态:")]),e("Select",{staticStyle:{width:"210px"},attrs:{clearable:"",placeholder:"请选择审核状态",filterable:""},model:{value:t.auditStatus,callback:function(e){t.auditStatus=e},expression:"auditStatus"}},[e("Option",{attrs:{value:"1"}},[t._v("通过")]),e("Option",{attrs:{value:"2"}},[t._v("待审核")]),e("Option",{attrs:{value:"3"}},[t._v("不通过")])],1)],1),e("div",{staticStyle:{width:"110px",display:"flex","justify-content":"center","margin-bottom":"20px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.searchOne()}}},[t._v("搜索")])],1),e("div",{staticStyle:{width:"160px",display:"flex","justify-content":"center","margin-bottom":"20px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"unapproved",expression:"'unapproved'"}],attrs:{type:"error",icon:"md-cloud-download",loading:t.unapprovedloading},on:{click:function(e){return t.exportUnapproved()}}},[t._v("未审批记录导出")])],1),e("div",{staticStyle:{width:"155px",display:"flex","justify-content":"center","margin-bottom":"20px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"fileApproval",expression:"'fileApproval'"}],attrs:{type:"info",icon:"md-brush"},on:{click:function(e){return t.batchFileApproval()}}},[t._v("批量文件审批")])],1)]),e("Table",{ref:"selection",staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"info",fn:function(a){var i=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"detail",expression:"'detail'"}],attrs:{type:"info",ghost:"",size:"small"},on:{click:function(e){return t.details(i)}}},[t._v("详情")])]}},{key:"action",fn:function(a){var i=a.row;a.index;return[e("div",{directives:[{name:"show",rawName:"v-show",value:[1,4,5].includes(+i.auditStatus),expression:"[1,4,5].includes(+row.auditStatus)"}]},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"examine",expression:"'examine'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",ghost:"",size:"small"},on:{click:function(e){return t.examine(2,i.id)}}},[t._v("通过")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"examine",expression:"'examine'"}],attrs:{type:"error",ghost:"",size:"small"},on:{click:function(e){return t.examine(3,i.id)}}},[t._v("不通过")])],1)]}}])}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:"套餐详情","mask-closable":!1,width:"600px"},on:{"on-cancel":t.cancelModal},model:{value:t.modal1,callback:function(e){t.modal1=e},expression:"modal1"}},[e("Form",{ref:"formObj",attrs:{model:t.formObj,"label-width":140}},[e("FormItem",{attrs:{label:"套餐名称:",prop:"nameCn"}},[e("Input",{staticStyle:{width:"350px"},attrs:{type:"textarea",rows:5,maxlength:500,readonly:"",placeholder:"最多支持500字符"},model:{value:t.formObj.nameCn,callback:function(e){t.$set(t.formObj,"nameCn",e)},expression:"formObj.nameCn"}})],1),e("FormItem",{attrs:{label:"套餐描述:",prop:"descCn"}},[e("Input",{staticStyle:{width:"350px"},attrs:{type:"textarea",rows:5,maxlength:4e3,readonly:"",placeholder:"最多可支持4000字符"},model:{value:t.formObj.descCn,callback:function(e){t.$set(t.formObj,"descCn",e)},expression:"formObj.descCn"}})],1),e("FormItem",{attrs:{label:"周期类型",prop:"periodUnit"}},[e("Select",{staticStyle:{width:"350px"},attrs:{filterable:"",disabled:"",placeholder:"请选择周期类型"},model:{value:t.formObj.periodUnit,callback:function(e){t.$set(t.formObj,"periodUnit",e)},expression:"formObj.periodUnit"}},t._l(t.periodUnitList,(function(a){return e("Option",{key:a.value,attrs:{value:a.value}},[t._v(t._s(a.label)+"\n\t\t\t\t\t")])})),1)],1),e("FormItem",{attrs:{label:"持续周期:",prop:"keepPeriod"}},[e("Input",{staticStyle:{width:"350px"},attrs:{maxlength:11,readonly:"",placeholder:"请选择持续周期"},model:{value:t.formObj.keepPeriod,callback:function(e){t.$set(t.formObj,"keepPeriod",e)},expression:"formObj.keepPeriod"}})],1),e("FormItem",{attrs:{label:"套餐购买有效期:",prop:"effectiveDay"}},[e("Input",{staticStyle:{width:"350px"},attrs:{maxlength:11,readonly:"",placeholder:"请输入套餐购买有效期"},model:{value:t.formObj.effectiveDay,callback:function(e){t.$set(t.formObj,"effectiveDay",e)},expression:"formObj.effectiveDay"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("天")])])],1),e("FormItem",{attrs:{label:"流量限制类型:",prop:"flowLimitType"}},[e("Select",{staticStyle:{width:"350px"},attrs:{filterable:"",disabled:"",placeholder:"请选择流量限制类型"},model:{value:t.formObj.flowLimitType,callback:function(e){t.$set(t.formObj,"flowLimitType",e)},expression:"formObj.flowLimitType"}},[e("Option",{attrs:{value:1}},[t._v("周期内限量")]),e("Option",{attrs:{value:2}},[t._v("按周期类型重置")])],1)],1),e("FormItem",{attrs:{label:"控制逻辑:",prop:"controlLogic"}},[e("Select",{staticStyle:{width:"350px"},attrs:{filterable:"",disabled:"",placeholder:"请选择控制逻辑"},model:{value:t.formObj.controlLogic,callback:function(e){t.$set(t.formObj,"controlLogic",e)},expression:"formObj.controlLogic"}},[1!==t.formObj.flowLimitType&&2!==t.formObj.flowLimitType&&t.formObj.flowLimitType?t._e():e("Option",{attrs:{value:1}},[t._v("达量限速")]),1!==t.formObj.flowLimitType&&t.formObj.flowLimitType?t._e():e("Option",{attrs:{value:2}},[t._v("达量释放")])],1)],1),e("FormItem",{attrs:{label:"是否支持热点:",prop:"isSupportedHotspots"}},[e("Select",{staticStyle:{width:"350px"},attrs:{filterable:"",disabled:"",placeholder:"请选择是否支持热点"},model:{value:t.formObj.isSupportedHotspots,callback:function(e){t.$set(t.formObj,"isSupportedHotspots",e)},expression:"formObj.isSupportedHotspots"}},[e("Option",{attrs:{value:1}},[t._v("是")]),e("Option",{attrs:{value:2}},[t._v("否")])],1)],1),e("FormItem",{directives:[{name:"show",rawName:"v-show",value:t.formObj.isSupportedHotspots,expression:"formObj.isSupportedHotspots"}],attrs:{label:"选择国家/地区:",prop:"mccList"}},[e("Tooltip",{attrs:{content:t.formObj.mccList,placement:"top","max-width":"300"}},[e("Input",{staticClass:"ellipsis-input",staticStyle:{width:"350px"},attrs:{readonly:"",placeholder:"请选择国家/地区"},model:{value:t.formObj.mccList,callback:function(e){t.$set(t.formObj,"mccList",e)},expression:"formObj.mccList"}})],1)],1),t._l(t.formObj.combinationList,(function(a,i){return e("div",{directives:[{name:"show",rawName:"v-show",value:t.formObj.mccList.length>0,expression:"formObj.mccList.length>0"}],key:i},[e("FormItem",{attrs:{label:"用量值:",prop:"combinationList."+i+".displayConsumption"}},[e("Input",{staticStyle:{width:"350px"},attrs:{readonly:"",placeholder:"请选择用量值"},model:{value:a.displayConsumption,callback:function(e){t.$set(a,"displayConsumption",e)},expression:"item.displayConsumption"}})],1),e("FormItem",{attrs:{label:"速度模板:",prop:"combinationList."+i+".templateName"}},[e("Input",{staticStyle:{width:"350px"},attrs:{readonly:"",placeholder:"请选择速度模板"},model:{value:a.templateName,callback:function(e){t.$set(a,"templateName",e)},expression:"item.templateName"}})],1)],1)})),e("FormItem",{attrs:{label:"无上限模板:",prop:"noLimitTemplateName"}},[e("Input",{staticStyle:{width:"350px"},attrs:{readonly:"",placeholder:"请选择无上限模板"},model:{value:t.formObj.noLimitTemplateName,callback:function(e){t.$set(t.formObj,"noLimitTemplateName",e)},expression:"formObj.noLimitTemplateName"}})],1),e("FormItem",{attrs:{label:"是否支持加油包",prop:"hasRefuelPackage"}},[e("i-switch",{attrs:{size:"large",disabled:""},model:{value:t.formObj.hasRefuelPackage,callback:function(e){t.$set(t.formObj,"hasRefuelPackage",e)},expression:"formObj.hasRefuelPackage"}},[e("span",{attrs:{slot:"open"},slot:"open"},[t._v("是")]),e("span",{attrs:{slot:"close"},slot:"close"},[t._v("否")])])],1),t.formObj.hasRefuelPackage?e("FormItem",{attrs:{label:"绑定加油包",prop:"selectionTypes"}},[e("Button",{staticClass:"inputSty",staticStyle:{width:"350px"},attrs:{type:"dashed"},on:{click:t.RefuelPackageList}},[t._v("加油包列表")])],1):t._e(),0!=t.appInfos.length?e("FormItem",{attrs:{label:"是否支持定向流量",prop:"isSupportDirect"}},[e("Select",{staticClass:"inputSty",staticStyle:{width:"350px"},attrs:{placeholder:"请选择是否支持定向流量",disabled:""},on:{"on-change":function(e){return t.changeDirect(e)}},model:{value:t.formObj.isSupportDirect,callback:function(e){t.$set(t.formObj,"isSupportDirect",e)},expression:"formObj.isSupportDirect"}},[e("Option",{attrs:{value:"1"}},[t._v("是")]),e("Option",{attrs:{value:"2"}},[t._v("否")])],1)],1):t._e(),t._l(t.formObj.directAppInfos,(function(a,i){return e("div",{key:i},["1"==t.formObj.isSupportDirect?e("Row",{attrs:{type:"flex",justify:"start",align:"middle"}},[e("Col",{attrs:{span:"24"}},[e("FormItem",{attrs:{label:"选择应用",prop:"directAppInfos."+i+".appId"}},[e("Select",{staticStyle:{width:"350px"},attrs:{multiple:"",filterable:"",placeholder:"请选择应用",disabled:""},on:{"on-change":function(e){return t.changeAppId(e)}},model:{value:a.appId,callback:function(e){t.$set(a,"appId",e)},expression:"fitem.appId"}},t._l(t.appInfos,(function(a,i){return e("Option",{key:a.id,attrs:{value:a.id}},[t._v(t._s(a.appName))])})),1)],1)],1)],1):t._e(),"1"==t.formObj.isSupportDirect?e("Row",[e("Col",{attrs:{span:"24"}},[e("FormItem",{attrs:{label:"定向使用逻辑",prop:"directAppInfos."+i+".directType"}},[e("RadioGroup",{on:{"on-change":function(e){return t.changeLogic(e)}},model:{value:a.directType,callback:function(e){t.$set(a,"directType",e)},expression:"fitem.directType"}},[e("Radio",{attrs:{label:"1",disabled:""}},[t._v("限速")]),e("Radio",{attrs:{label:"2",disabled:""}},[t._v("免流")])],1)],1)],1)],1):t._e(),t._l(a.appConsumption,(function(o,n){return"1"==t.formObj.isSupportDirect&&"2"==a.directType?e("div",{key:n},[e("Row",[e("Col",{attrs:{span:"24"}},[e("FormItem",{attrs:{label:"流量值",prop:"directAppInfos."+i+".appConsumption."+n+".consumption"}},[e("Input",{staticStyle:{width:"350px"},attrs:{readonly:"",placeholder:"请输入流量值"},model:{value:o.consumption,callback:function(e){t.$set(o,"consumption","string"===typeof e?e.trim():e)},expression:"item1.consumption"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("MB")])])],1)],1)],1),e("Row",{attrs:{type:"flex",justify:"start",align:"middle"}},[e("Col",{attrs:{span:"24"}},[e("FormItem",{attrs:{label:"选择UPCC模板",prop:"directAppInfos."+i+".appConsumption."+n+".upccTemplateId"}},t._l(a.appId,(function(a,i){return e("div",{staticStyle:{display:"flex","justify-content":"flex-statrt","align-items":"flex-start"}},[e("Select",{key:a,staticStyle:{"margin-bottom":"20px",width:"350px"},attrs:{filterable:"",placeholder:"请选择UPCC模板",disabled:""},model:{value:o.upccTemplateId[i],callback:function(e){t.$set(o.upccTemplateId,i,e)},expression:"item1.upccTemplateId[uindex]"}},t._l(t.directTemplateList[a],(function(a){return e("Option",{key:a.upccTemplateId,attrs:{value:a.upccTemplateId}},[t._v(t._s(a.templateName))])})),1)],1)})),0)],1)],1)],1):t._e()})),"1"==t.formObj.isSupportDirect&&"2"==a.directType?e("Row",[e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:"是否继续使用通用模板",prop:"directAppInfos."+i+".isUsePackage"}},[e("Select",{staticStyle:{width:"350px"},attrs:{placeholder:"请选择是否继续使用通用模板",disabled:""},on:{"on-change":function(e){return t.changeUsePackage(e)}},model:{value:a.isUsePackage,callback:function(e){t.$set(a,"isUsePackage",e)},expression:"fitem.isUsePackage"}},[e("Option",{attrs:{value:"1"}},[t._v("是")]),e("Option",{attrs:{value:"2"}},[t._v("否")])],1)],1)],1)],1):t._e(),"1"==t.formObj.isSupportDirect?e("Row",["1"==a.directType?e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:"定向限速模板",prop:"directAppInfos."+i+".noLimitTemplateId"}},t._l(a.appId,(function(i,o){return e("div",[e("Select",{staticStyle:{"margin-bottom":"20px",width:"350px"},attrs:{filterable:"",placeholder:"请选择定向限速模板",disabled:"",clearable:"info"!=t.typeFlag},model:{value:a.noLimitTemplateId[o],callback:function(e){t.$set(a.noLimitTemplateId,o,e)},expression:"fitem.noLimitTemplateId[dindex]"}},t._l(t.directTemplateList[i],(function(a){return e("Option",{key:a.upccTemplateId,attrs:{value:a.upccTemplateId}},[t._v(t._s(a.templateName))])})),1)],1)})),0)],1):t._e(),a.isUsePackage&&"2"==a.directType?e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:"2"==a.isUsePackage?"定向免流限速模板":"定向免流继续使用模板",prop:"directAppInfos."+i+".noLimitTemplateId"}},t._l(a.appId,(function(i,o){return e("div",[e("Select",{staticStyle:{"margin-bottom":"20px",width:"350px"},attrs:{placeholder:"2"==a.isUsePackage?"请选择定向免流限速模板":"请选择定向免流继续使用模板",disabled:""},model:{value:a.noLimitTemplateId[o],callback:function(e){t.$set(a.noLimitTemplateId,o,e)},expression:"fitem.noLimitTemplateId[dindex]"}},t._l(t.directTemplateList[i],(function(a){return e("Option",{key:a.upccTemplateId,attrs:{value:a.upccTemplateId}},[t._v(t._s(a.templateName))])})),1)],1)})),0)],1):t._e()],1):t._e(),e("div",{staticStyle:{"margin-bottom":"30px"}})],2)}))],2),e("div",{staticClass:"footer_wrap",attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")])],1)],1),e("Modal",{attrs:{title:"确认执行审核不通过？","mask-closable":!1,width:"500px"},on:{"on-cancel":t.cancelModal},model:{value:t.modal2,callback:function(e){t.modal2=e},expression:"modal2"}},[e("Form",{ref:"formItemReason",attrs:{model:t.formItemReason,rules:t.ruleValidate},nativeOn:{submit:function(t){t.preventDefault()}}},[e("FormItem",{attrs:{prop:"reasonText"}},[e("Input",{attrs:{maxlength:"300",placeholder:"请输入不通过原因……"},model:{value:t.formItemReason.reasonText,callback:function(e){t.$set(t.formItemReason,"reasonText",e)},expression:"formItemReason.reasonText"}})],1)],1),e("div",{staticStyle:{"text-align":"right"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{staticStyle:{"margin-left":"8px"},on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.confirm}},[t._v("确定")])],1)],1),e("Modal",{attrs:{title:"添加加油包","footer-hide":!0,"mask-closable":!1,width:"1000px"},on:{"on-cancel":t.cancelRefuelModal},model:{value:t.addRefuelModel,callback:function(e){t.addRefuelModel=e},expression:"addRefuelModel"}},[e("Table",{staticStyle:{width:"100%"},attrs:{columns:t.Unitedcolumns,data:t.Uniteddata,loading:t.Unitedloading},on:{"on-selection-change":t.handleRowChange,"on-select-cancel":t.cancelPackage,"on-select-all-cancel":t.cancelPackageAll}}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.Unitedtotal,current:t.UnitedcurrentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.UnitedcurrentPage=e},"on-change":t.UnitedgoPage}})],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{staticStyle:{"margin-left":"8px"},on:{click:t.cancelRefuelModal}},[t._v("取消")])],1)],1),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v("导出提示")]),e("FormItem",{attrs:{label:"你本次导出任务ID为:"}},[e("span",{staticStyle:{width:"100px"}},[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:"你本次导出的文件名为:"}},[e("span",[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v("请前往下载管理-下载列表查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v("立即前往")])],1)]),e("Modal",{attrs:{title:"批量文件审批","footer-hide":!0,"mask-closable":!1,width:"550px"},on:{"on-cancel":t.fileCancelModal},model:{value:t.fileApprovalflag,callback:function(e){t.fileApprovalflag=e},expression:"fileApprovalflag"}},[e("div",{staticStyle:{padding:"0 16px"}},[e("Form",{ref:"fileApprovalObj",attrs:{model:t.fileApprovalObj,rules:t.ruleobj}},[e("FormItem",{staticStyle:{"font-size":"14px","font-weight":"bold"},attrs:{label:"上传文件审批",prop:"file"}},[e("Upload",{staticStyle:{width:"400px","margin-top":"50px","margin-left":"50px"},attrs:{type:"drag",action:t.uploadUrl,"on-success":t.fileSuccess,"on-error":t.handleError,"before-upload":t.handleBeforeUpload,"on-progress":t.fileUploading},model:{value:t.fileApprovalObj.file,callback:function(e){t.$set(t.fileApprovalObj,"file",e)},expression:"fileApprovalObj.file"}},[e("div",{staticStyle:{padding:"20px 0"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v("点击或拖拽文件上传")])],1)]),e("div",{staticStyle:{width:"450px"}},[e("Alert",{staticStyle:{padding:"8px 10px","margin-top":"10px","margin-left":"50px"},attrs:{type:"warning"}},[t._v("注：可使用“未审批记录导出”文件进行上传审批！")])],1),t.file?e("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"400px","margin-left":"50px"}},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name)+"\n\t\t\t\t\t\t\t")],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e()],1),e("div",{staticStyle:{width:"100%",display:"flex","justify-content":"center","margin-top":"50px"}},[e("Button",{on:{click:t.fileCancelModal}},[t._v("返回")]),t._v("    \n\t\t\t\t\t"),e("Button",{attrs:{type:"primary",loading:t.importLoading},on:{click:t.fileConfirm}},[t._v("确定")])],1)],1)],1)])],1)},o=[],n=(a("d9e2"),a("7db0"),a("a15b"),a("d81d"),a("14d9"),a("4e82"),a("a434"),a("4ec9"),a("a9e3"),a("b64b"),a("d3b7"),a("ac1f"),a("00b4"),a("3ca3"),a("159b"),a("ddb0"),a("ad00")),l=a("90fe"),s=a("951d"),r=a("66df"),c="/pms/api/v1/channelBuiltPackage",p=function(t){return r["a"].request({url:c+"/getList",data:t,method:"post"})},d=function(t){return r["a"].request({url:c+"/check",data:t,method:"post"})},u=function(t){return r["a"].request({url:c+"/exportCheckList",params:t,method:"post"})},m=function(t){return r["a"].request({url:c+"/batchCheck",data:t,method:"post",contentType:"multipart/form-data"})},f=(a("d2d0"),a("78c0")),h={components:{PackageModel:n["a"]},data:function(){var t=this,e=function(e,a,i){t.file?i():i(new Error("请上传文件"))};return{total:0,currentPage:1,page:0,packageId:"",packageName:"",mcc:"",auditStatus:"",corpId:"",corpIds:"",types:"",ids:"",continentList:[],corpList:[],loading:!1,searchloading:!1,unapprovedloading:!1,Unitedloading:!1,data:[],columns:[{title:"渠道商",key:"corpName",minWidth:120,align:"center",tooltip:!0},{title:"套餐ID",key:"id",minWidth:120,align:"center",tooltip:!0},{title:"套餐名称",key:"nameCn",minWidth:120,align:"center",tooltip:!0},{title:"详情",slot:"info",minWidth:120,align:"center"},{title:"审核状态",key:"auditStatus",align:"center",minWidth:120,render:function(t,e){var a=e.row,i="1"==a.auditStatus?"#2b85e4":"2"==a.auditStatus?"#19be6b":"3"==a.auditStatus?"#ff0000":"4"==a.auditStatus?"#ffa554":"5"==a.auditStatus?"#ff0000":"",o="1"==a.auditStatus?"新建待审核":"2"==a.auditStatus?"通过":"3"==a.auditStatus?"不通过":"4"==a.auditStatus?"修改待审批":"5"==a.auditStatus?"删除待审批":"";return t("label",{style:{color:i}},o)}},{title:"审核操作",slot:"action",minWidth:180,align:"center",fixed:"right"}],typeFlag:"",title:"",modal1:!1,modal2:!1,addRefuelModel:!1,exportModal:!1,fileApprovalflag:!1,searchObjloading:!1,importLoading:!1,formObj:{nameCn:"",descCn:"",periodUnit:"",keepPeriod:"",effectiveDay:"",flowLimitType:"",controlLogic:"",isSupportedHotspots:"",mccList:"",templateName:"",combinationList:[{displayConsumption:"",templateName:""}],noLimitTemplateName:"",groupId:"",hasRefuelPackage:!1,refuelList:[],directAppInfos:[{index:1,appId:[],directType:"",appConsumption:[{index1:1,consumption:"",upccTemplateId:[]}],isUsePackage:"",noLimitTemplateId:[]}]},periodUnitList:[{value:"1",label:"24小时"},{value:"2",label:"自然日"},{value:"3",label:"自然月"},{value:"4",label:"自然年"}],Unitedtotal:0,UnitedcurrentPage:1,Unitedpage:0,Uniteddata:[],Unitedcolumns:[{type:"selection",width:60,align:"center"},{title:"加油包ID",key:"id",minWidth:120,align:"center",tooltip:!0},{title:"加油包名称",key:"nameCn",minWidth:180,align:"center",tooltip:!0},{title:"加油包流量值(MB)",key:"flowValue",minWidth:120,align:"center",tooltip:!0}],formItemReason:{reasonText:""},ruleValidate:{reasonText:[{required:!0,message:"原因不能为空"}]},refuelIDList:[],searchObj:{gaspackname:"",gaspacknameid:""},taskId:"",taskName:"",file:null,uploadList:[],uploadUrl:"",updateuploadUrl:"",index:1,index1:1,appInfos:[],directTemplateList:{},fileApprovalObj:{file:""},ruleobj:{file:[{required:!0,validator:e,trigger:"change"}]}}},mounted:function(){this.goPageFirst(1),this.getLocalList1(),this.getCorpList()},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var a=this;p({page:t,pageSize:10,isNeedAuth:!0,isNeedMcc:!0,list:!0,packageId:this.packageId,packageNameCn:this.packageName,mcc:this.mcc,corpId:this.corpId,authStatus:this.auditStatus}).then((function(i){if("0000"==i.code){a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t;var o=[];i.data.data.map((function(t,e){t.authObj?o.push(t.authObj):o.push(t)})),e.data=o,e.total=i.data.total}})).catch((function(t){console.error(t)})).finally((function(){a.loading=!1,e.searchloading=!1}))},searchOne:function(){this.searchloading=!0,this.goPageFirst(1)},exportUnapproved:function(){var t=this;this.unapprovedloading=!0,u({userId:this.$store.state.user.userId}).then((function(e){t.exportModal=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName,t.unapprovedloading=!1})).catch((function(e){console.error(e),t.unapprovedloading=!1})).finally((function(){}))},batchFileApproval:function(){this.fileApprovalflag=!0},fileSuccess:function(t,e,a){this.message="请先下载模板文件，并按格式填写后上传"},handleError:function(t,e){var a=this;setTimeout((function(){a.uploading=!1,a.$Notice.warning({title:"错误提示",desc:"上传失败！"})}),3e3)},handleBeforeUpload:function(t,e){return/^.+(\.xlsx)$/.test(t.name)?(this.file=t,this.uploadList=e):this.$Notice.warning({title:"文件格式不正确",desc:t.name+"格式不正确，请上传.xlsx格式文件"}),!1},fileUploading:function(t,e,a){this.message="文件上传中、待进度条消失后再操作"},removeFile:function(){this.file=""},fileConfirm:function(){var t=this;this.$refs["fileApprovalObj"].validate((function(e){if(e){t.importLoading=!0;var a=new FormData;a.append("file",t.file),m(a).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.goPageFirst(1),t.file="",t.$refs["fileApprovalObj"].resetFields(),t.fileApprovalflag=!1})).catch((function(t){return!1})).finally((function(){t.importLoading=!1}))}}))},goPage:function(t){this.goPageFirst(t)},getLocalList1:function(){var t=this;Object(l["f"])().then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;t.continentList=a,t.continentList.sort((function(t,e){return t.countryEn.localeCompare(e.countryEn)}));var i=new Map;a.map((function(t,e){i.set(t.mcc,t.countryEn)})),t.localMap=i})).catch((function(t){})).finally((function(){}))},getCorpList:function(){var t=this;Object(s["e"])({type:1,status:1,checkStatus:2}).then((function(e){if(!e||"0000"!=e.code)throw e;t.corpList=e.data})).catch((function(t){})).finally((function(){}))},examine:function(t,e){var a=this;2==t?this.$Modal.confirm({title:"确认执行审核通过？",onOk:function(){d({id:e,auditStatus:t}).then((function(t){if(!t||"0000"!=t.code)throw t;a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.goPageFirst(a.page)})).catch((function(t){}))}}):(this.modal2=!0,this.types=t,this.ids=e)},confirm:function(){var t=this,e=this.formItemReason.reasonText;this.$refs.formItemReason.validate((function(a){a&&d({id:t.ids,auditStatus:t.types,noPassMessage:e}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.$refs["formItemReason"].resetFields(),t.modal2=!1,t.goPageFirst(t.page)})).catch((function(t){}))}))},details:function(t){var e=this;this.packageGetDirectional(t.corpId),this.modal1=!0,this.refuelIDList=t.refuelIDList,this.formObj.groupId=t.groupId,this.corpIds=t.corpId,this.formObj.id=t.id,this.formObj.nameCn=t.nameCn,this.formObj.descCn=t.descCn,this.formObj.periodUnit=t.periodUnit,this.formObj.effectiveDay=t.effectiveDay,this.formObj.keepPeriod=t.keepPeriod,this.formObj.noLimitTemplateName=t.noLimitTemplateName.length>35?t.noLimitTemplateName.substring(0,35)+"…":t.noLimitTemplateName,this.formObj.flowLimitType=Number(t.flowLimitType),this.formObj.controlLogic=Number(t.controlLogic),this.formObj.isSupportedHotspots=Number(t.isSupportedHotspots),this.formObj.templateName=t.noLimitTemplateId,this.formObj.combinationList=[];var a=[];Object.keys(t.mccMap).map((function(e){a.push(t.mccMap[e])})),this.formObj.mccList=a.join(" ,"),t.packageConsumptions.map((function(t){e.formObj.combinationList.push({consumption:t.consumption,displayConsumption:t.displayConsumption,upccTemplateId:t.upccTemplateId,templateName:t.templateName.length>35?t.templateName.substring(0,35)+"…":t.templateName})})),this.formObj.hasRefuelPackage=t.refuelIDList.length>0,this.formObj.selectionTypes=t.refuelIDList,this.formObj.isSupportDirect=t.isSupportDirect,this.setData()},cancelModal:function(){this.modal1=!1,this.modal2=!1,this.formObj.controlLogic="",this.formObj.combinationList=[{displayConsumption:"",templateName:""}],this.$refs["formItemReason"].resetFields(),this.exportModal=!1},fileCancelModal:function(){this.fileApprovalflag=!1,this.file="",this.$refs["fileApprovalObj"].resetFields()},RefuelPackageList:function(){this.getDetailsRefuelList(1)},search:function(){this.searchObjloading=!0,this.getDetailsRefuelList(1)},getDetailsRefuelList:function(t){var e=this;this.Unitedloading=!0,Object(f["r"])({pageNum:t,pageSize:10,refuelID:this.searchObj.gaspacknameid,refuelName:this.searchObj.gaspackname,packageID:this.formObj.id}).then((function(a){if(!a||"0000"!=a.code)throw a;e.Uniteddata=a.data,e.Unitedtotal=a.count,e.UnitedcurrentPage=t,e.formObj.selectionTypes.forEach((function(t){a.data.forEach((function(a){a.id==t.id&&(e.$set(a,"_checked",!0),e.$set(a,"_disabled",!0))}))})),e.addRefuelModel=!0})).catch((function(t){})).finally((function(){e.Unitedloading=!1,e.searchObjloading=!1}))},UnitedgoPage:function(t){this.getDetailsRefuelList(t)},handleRowChange:function(t){var e=this;this.selection=t,t.map((function(t,a){var i=!0;e.formObj.selectionTypes.map((function(e,a){t.id===e.id&&(i=!1)})),i&&e.formObj.selectionTypes.push(t)}))},cancelPackage:function(t,e){var a=this;this.formObj.selectionTypes.forEach((function(t,i){t.id===e.id&&a.formObj.selectionTypes.splice(i,1)}))},cancelPackageAll:function(t,e){this.formObj.selectionTypes=[]},cancelRefuelModal:function(){this.addRefuelModel=!1},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},packageGetDirectional:function(t){var e=this;Object(f["y"])({corpId:t}).then((function(t){if(!t||"0000"!=t.code)throw t;e.appInfos=t.data,e.appInfos.map((function(t,a){e.directTemplateList[t.id]=t.appUpccInfo}))})).catch((function(t){})).finally((function(){}))},setData:function(){var t=this;Object(f["l"])({packageId:this.formObj.id}).then((function(e){if(!e||"0000"!=e.code)throw e;var a=[];e.data.forEach((function(t,e){var i={index:e+1,appId:[],directType:t.directType,noLimitTemplateId:t.appDetailInfos.map((function(t){return t.noLimitTemplateId})),isUsePackage:t.isUsePackage};t.appDetailInfos.forEach((function(t){i.appId.push(t.appId),t.appConsumption.length>0&&t.appConsumption.forEach((function(t){i.appConsumption||(i.appConsumption=[]);var e=i.appConsumption.find((function(e){return e.consumption===t.consumption}));e?e.upccTemplateId.push(t.upccTemplateId):i.appConsumption.push({index1:i.index,consumption:t.consumption,upccTemplateId:[t.upccTemplateId],isUsePackage:i.isUsePackage,noLimitTemplateId:i.noLimitTemplateId})}))})),a.push(i)})),t.formObj.directAppInfos=a})).catch((function(t){})).finally((function(){}))}}},b=h,g=(a("7954"),a("2877")),v=Object(g["a"])(b,i,o,!1,null,"00f52614",null);e["default"]=v.exports},7070:function(t,e,a){},7954:function(t,e,a){"use strict";a("7070")},"951d":function(t,e,a){"use strict";a.d(e,"g",(function(){return n})),a.d(e,"d",(function(){return l})),a.d(e,"c",(function(){return s})),a.d(e,"b",(function(){return r})),a.d(e,"a",(function(){return c})),a.d(e,"e",(function(){return p})),a.d(e,"f",(function(){return d}));var i=a("66df"),o="/cms/package/config",n=function(t){return i["a"].request({url:o+"/task/pageList",data:t,method:"post"})},l=function(t,e){return i["a"].request({url:o+"/task/download/".concat(t,"?status=")+e,method:"POST",responseType:"blob"})},s=function(t){return i["a"].request({url:o+"/task/rollback/".concat(t),method:"POST"})},r=function(t){return i["a"].request({url:o+"/task",data:t,method:"POST",contentType:"multipart/form-data"})},c=function(t){return i["a"].request({url:o+"/taskPage",data:t,method:"POST"})},p=function(t){return i["a"].request({url:"/cms/channel/searchList",data:t,method:"post"})},d=function(t){return i["a"].request({url:"/cms/package/config/getTextChannel",data:t,method:"get"})}}}]);