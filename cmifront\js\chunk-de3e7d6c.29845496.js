(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-de3e7d6c"],{"00b4":function(t,e,a){"use strict";a("ac1f");var o=a("23e7"),n=a("c65b"),i=a("1626"),s=a("825a"),r=a("577e"),l=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),c=/./.test;o({target:"RegExp",proto:!0,forced:!l},{test:function(t){var e=s(this),a=r(t),o=e.exec;if(!i(o))return n(c,e,a);var l=n(o,e,a);return null!==l&&(s(l),!0)}})},"3f7e":function(t,e,a){"use strict";var o=a("b5db"),n=o.match(/firefox\/(\d+)/i);t.exports=!!n&&+n[1]},"4e82":function(t,e,a){"use strict";var o=a("23e7"),n=a("e330"),i=a("59ed"),s=a("7b0b"),r=a("07fa"),l=a("083a"),c=a("577e"),d=a("d039"),u=a("addb"),p=a("a640"),f=a("3f7e"),m=a("99f4"),h=a("1212"),v=a("ea83"),g=[],y=n(g.sort),w=n(g.push),x=d((function(){g.sort(void 0)})),M=d((function(){g.sort(null)})),k=p("sort"),b=!d((function(){if(h)return h<70;if(!(f&&f>3)){if(m)return!0;if(v)return v<603;var t,e,a,o,n="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(o=0;o<47;o++)g.push({k:e+o,v:a})}for(g.sort((function(t,e){return e.v-t.v})),o=0;o<g.length;o++)e=g[o].k.charAt(0),n.charAt(n.length-1)!==e&&(n+=e);return"DGBEFHACIJK"!==n}})),_=x||!M||!k||!b,I=function(t){return function(e,a){return void 0===a?-1:void 0===e?1:void 0!==t?+t(e,a)||0:c(e)>c(a)?1:-1}};o({target:"Array",proto:!0,forced:_},{sort:function(t){void 0!==t&&i(t);var e=s(this);if(b)return void 0===t?y(e):y(e,t);var a,o,n=[],c=r(e);for(o=0;o<c;o++)o in e&&w(n,e[o]);u(n,I(t)),a=r(n),o=0;while(o<a)e[o]=n[o++];while(o<c)l(e,o++);return e}})},"982c":function(t,e,a){},"99f4":function(t,e,a){"use strict";var o=a("b5db");t.exports=/MSIE|Trident/.test(o)},a595:function(t,e,a){"use strict";a.r(e);a("b0c0");var o=function(){var t=this,e=t._self._c;return e("div",[e("Card",[e("div",{staticClass:"search_head"},[e("span",{staticClass:"input_notice"},[t._v("仓库名称：")]),t._v("  \n\t\t\t"),e("Input",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{width:"200px","margin-right":"10px"},attrs:{clearable:"",placeholder:"输入仓库名称..."},model:{value:t.warehouse,callback:function(e){t.warehouse=e},expression:"warehouse"}}),e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{"margin-right":"10px"},attrs:{loading:t.searchLoading,icon:"md-search",type:"primary"},on:{click:function(e){return t.searchByCondition()}}},[t._v("搜索")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{"margin-right":"10px"},attrs:{icon:"md-add",type:"success"},on:{click:function(e){return t.showAddModal(null,"add")}}},[t._v("新增仓库")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"cardTransfer",expression:"'cardTransfer'"}],attrs:{icon:"md-card",type:"info"},on:{click:function(e){return t.showTransfersModal()}}},[t._v("卡片调拨")])],1),e("div",{staticStyle:{"margin-top":"20px"}},[e("Table",{attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(a){var o=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{color:"#2F54EB"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.showAddModal(o,"update")}}},[t._v("修改")])]}},{key:"downloadAction",fn:function(a){var o=a.row;a.index;return[e("row",{staticStyle:{padding:"5px 5px"}},[e("Col",{attrs:{span:"4"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"exportAll",expression:"'exportAll'"}],staticStyle:{color:"#2F54EB"},attrs:{loading:o.exAllLoading,type:"text",size:"small"},on:{click:function(e){return t.downLoadFile(o,"all")}}},[t._v("总库存")])],1),e("Col",{attrs:{span:"5"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"exportEntity",expression:"'exportEntity'"}],staticStyle:{color:"#2F54EB"},attrs:{loading:o.exEntityLoading,type:"text",size:"small"},on:{click:function(e){return t.downLoadFile(o,"entity")}}},[t._v("实体卡库存")])],1),e("Col",{attrs:{span:"5"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"exportFilm",expression:"'exportFilm'"}],staticStyle:{color:"#2F54EB"},attrs:{loading:o.exFilmLoading,type:"text",size:"small"},on:{click:function(e){return t.downLoadFile(o,"film")}}},[t._v("贴膜卡库存")])],1),e("Col",{attrs:{span:"5"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"exportEsim",expression:"'exportEsim'"}],staticStyle:{color:"#2F54EB"},attrs:{loading:o.exEsimLoading,type:"text",size:"small"},on:{click:function(e){return t.downLoadFile(o,"esim")}}},[t._v("ESIM卡库存")])],1),e("Col",{attrs:{span:"5"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"exportImsi",expression:"'exportImsi'"}],staticStyle:{color:"#2F54EB"},attrs:{loading:t.imsiLoading,type:"text",size:"small"},on:{click:function(e){return t.downLoadFile(o,"imsi")}}},[t._v("IMSI号库存")])],1)],1)]}}])})],1),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.page,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.goPage}})],1)]),e("Modal",{attrs:{title:"卡片调拨","mask-closable":!1},on:{"on-cancel":t.cancelUpload},model:{value:t.transfersModal,callback:function(e){t.transfersModal=e},expression:"transfersModal"}},[e("Upload",{attrs:{type:"drag",action:t.uploadUrl,"on-success":t.fileSuccess,"before-upload":t.handleBeforeUpload,"on-progress":t.fileUploading}},[e("div",{staticStyle:{padding:"20px 0"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v("点击或拖拽号码文件上传")])],1)]),t.file?e("ul",{staticClass:"ivu-upload-list"},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("i",{staticClass:"ivu-icon ivu-icon-ios-stats"}),t._v(t._s(t.file.name))]),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e(),e("div",{staticStyle:{width:"100%",padding:"10px 0"}},[e("Button",{attrs:{type:"primary",loading:t.downloading,icon:"ios-download"},on:{click:t.downloadTempFile}},[t._v("下载模板文件")]),e("Alert",{staticStyle:{float:"right",padding:"8px 10px 8px 10px"},attrs:{type:"warning"}},[t._v(t._s(t.message))])],1),e("div",{staticStyle:{width:"100%","margin-top":"10px"}},[t.transfersModal?e("Form",{ref:"uploadForm",attrs:{model:t.modalData,rules:t.rules},nativeOn:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleUpload.apply(null,arguments)}}},[e("FormItem",{attrs:{prop:"intoInventory"}},[e("div",{staticStyle:{display:"flex","flex-wrap":"nowrap",width:"100%","align-items":"center"}},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"30%"}},[t._v("选择转入仓库：")]),e("Select",{attrs:{filterable:"",clearable:!0,placeholder:"选择转入仓库..."},model:{value:t.modalData.intoInventory,callback:function(e){t.$set(t.modalData,"intoInventory",e)},expression:"modalData.intoInventory"}},t._l(t.inventoryList,(function(a){return e("Option",{key:a.storeId,attrs:{value:a.storeId}},[t._v(t._s(a.storeName))])})),1)],1)])],1):t._e()],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelUpload}},[t._v("取消")]),e("Button",{attrs:{type:"primary",loading:t.uploading},on:{click:t.handleUpload}},[t._v("确定")])],1),e("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1),e("Modal",{attrs:{title:t.title,"mask-closable":!1},on:{"on-cancel":t.cancelModal},model:{value:t.addModal,callback:function(e){t.addModal=e},expression:"addModal"}},[t.addModal?e("Form",{ref:"editForm",attrs:{model:t.addModalData,rules:t.addRules}},[e("FormItem",{attrs:{prop:"storeName"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"20%"}},[t._v("仓库名称：")]),e("Input",{staticStyle:{width:"80%"},attrs:{placeholder:"请输入仓库名称..."},model:{value:t.addModalData.storeName,callback:function(e){t.$set(t.addModalData,"storeName",e)},expression:"addModalData.storeName"}})],1)]),e("FormItem",{attrs:{prop:"mccList"}},[e("div",{staticClass:"input_modal"},[e("span",{staticStyle:{width:"2%",color:"red"}},[t._v("*")]),e("span",{staticStyle:{width:"20%"}},[t._v("覆盖国家/地区：")]),e("Select",{staticClass:"inputSty",staticStyle:{width:"80%"},attrs:{filterable:"",placeholder:"请选择国家/地区",multiple:""},model:{value:t.addModalData.mccList,callback:function(e){t.$set(t.addModalData,"mccList",e)},expression:"addModalData.mccList"}},t._l(t.continentList,(function(a){return e("Option",{key:a.id,attrs:{value:a.mcc}},[t._v(t._s(a.countryEn))])})),1)],1)])],1):t._e(),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),"add"===t.type?e("Button",{attrs:{type:"primary"},on:{click:t.addInventory}},[t._v("确定")]):t._e(),"update"===t.type?e("Button",{attrs:{type:"primary"},on:{click:t.editInventory}},[t._v("确定")]):t._e()],1)],1),e("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:t.modelColumns,data:t.modelData}}),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelExportModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v("导出提示")]),e("FormItem",{attrs:{label:"你本次导出任务ID为:"}},[e("span",{staticStyle:{width:"100px"}},[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:"你本次导出的文件名为:"}},[e("span",[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v("请前往下载管理-下载列表查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelExportModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v("立即前往")])],1)])],1)},n=[],i=(a("d9e2"),a("14d9"),a("4e82"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0"),a("66df")),s="/pms/api/v3/warehouse",r=function(t){return i["a"].request({url:s+"/queryWarehouse",params:t,method:"get"})},l=function(t){return i["a"].request({url:s+"/cardOut",params:t,method:"get"})},c=function(t){return i["a"].request({url:s+"/cardAllot",data:t,method:"put",contentType:"multipart/form-data"})},d=function(t){return i["a"].request({url:s+"/newWarehouse",data:t,method:"post"})},u=function(t){return i["a"].request({url:s+"/updateWarehouse",data:t,method:"post"})},p=function(t){return i["a"].request({url:s+"/queryStoreList",method:"get"})},f=function(t){return i["a"].request({url:"/oms/api/v1/country/queryCounrtyList",method:"get"})},m={props:{intoInventoryRules:{default:function(){return[{required:!0,message:"请选择调入仓库",trigger:"change"}]}}},data:function(){var t=function(t,e,a){e&&0!=e.length?a():a(new Error("请选择国家/地区"))},e=function(t,e,a){e&&0!=e.length?a():a(new Error("请设置仓库名称"))};return{type:"",inventoryList:[],addModalData:{storeName:"",mccList:[]},addRules:{mccList:[{type:"array",validator:t,required:!0,trigger:"blur"}],storeName:[{validator:e,required:!0,trigger:"blur"}]},warehouse:"",intoModal:!1,addModal:!1,continentList:[],tableData:[],columns:[{title:"仓库名称",key:"storeName",align:"center"},{title:"当前总库存量",key:"totle",align:"center"},{title:"实体卡库存",key:"entity",align:"center"},{title:"贴膜卡库存",key:"padPasting",align:"center"},{title:"ESIM卡库存",key:"esim",align:"center"},{title:"IMSI号库存",key:"imsiNum",align:"center"},{title:"覆盖国家",key:"coverCountry",align:"center",render:function(t,e){var a=e.row,o=""!=a.coverCountry&&null!=a.coverCountry?a.coverCountry.toString():"未知";return o.length>8?(o=o.substring(0,8)+"...",t("div",[t("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[o,t("label",{slot:"content",style:{whiteSpace:"normal"}},a.coverCountry.toString())])])):(o=o,t("label",o))}},{title:"操作",slot:"action",align:"center"},{title:"导出",width:500,slot:"downloadAction",align:"center"}],currentPage:1,page:1,total:0,transfersModal:!1,file:null,uploadUrl:"",message:"文件仅支持csv格式文件,大小不能超过5MB",modalData:{intoInventory:""},modelData:[{iccid:"********"}],modelColumns:[{title:"iccid号码",key:"iccid"}],searchLoading:!1,uploading:!1,downloading:!1,loading:!1,title:"新增仓库",imsiLoading:!1,exportModal:!1,taskId:"",taskName:""}},computed:{rules:function(){return{intoInventory:this.intoInventoryRules}}},methods:{goPageFirst:function(t){var e=this;this.page=t,this.loading=!0;var a={storeName:this.warehouse,pageNum:t,pageSize:10};r(a).then((function(t){if(!t||"0000"!=t.code)throw t;e.tableData=t.data.storeInfoList,e.total=t.data.count})).catch((function(t){e.loading=!1,e.searchLoading=!1,console.log(t)})).finally((function(){e.loading=!1,e.searchLoading=!1}))},showTransfersModal:function(){this.getAllInventory(),this.modalData={intoInventory:""},this.file=null,this.transfersModal=!0},showAddModal:function(t,e){this.getLocalList(),"add"===e?(this.type="add",this.addModalData={storeName:"",mccList:""}):(this.type="update",this.title="编辑仓库",this.addModalData={storeName:t.storeName,mccList:t.mccList,storeId:t.storeId}),this.addModal=!0},getAllInventory:function(){var t=this;p().then((function(e){if(!e||"0000"!=e.code)throw e;t.inventoryList=e.data})).catch((function(t){}))},getLocalList:function(){var t=this;f().then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;t.continentList=a,t.continentList.sort((function(t,e){return t.countryEn.localeCompare(e.countryEn)}))})).catch((function(t){}))},cancelModal:function(){this.addModal=!1},showUploadLoadNumModal:function(t){this.file=null,this.intoModal=!0},downloadTempFile:function(){this.$refs.modelTable.exportCsv({filename:"卡片调拨模板文件",columns:this.modelColumns,data:this.modelData})},removeFile:function(){this.file=""},handleBeforeUpload:function(t){return/^.+(\.csv)$/.test(t.name)?t.size>5242880?this.$Notice.warning({title:"文件大小超过限制",desc:"文件 "+t.name+"超过了最大限制范围5MB"}):this.file=t:this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+t.name+" 格式不正确，请上传.csv格式文件。"}),!1},downLoadFile:function(t,e){var a=this;this.$Modal.confirm({title:"确认导出？",onOk:function(){var o="";if("all"===e){t.exAllLoading=!0,o="5"}else if("entity"===e){t.exEntityLoading=!0,o="1"}else if("film"===e){t.exFilmLoading=!0,o="3"}else if("esim"===e){t.exEsimLoading=!0,o="2"}else if("imsi"===e){t.exEsimLoading=!0,o="4"}l({userId:a.$store.state.user.userId,storeId:t.storeId,cardForm:o}).then((function(t){a.exportModal=!0,a.taskId=t.data.id,a.taskName=t.data.fileName})).catch((function(t){}))}})},cancelExportModal:function(){this.exportModal=!1},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},searchByCondition:function(){this.searchLoading=!0,this.goPageFirst(1)},addInventory:function(){var t=this;this.$refs.editForm.validate((function(e){e&&d({mcc:t.addModalData.mccList,storeName:t.addModalData.storeName}).then((function(e){if(!e||"0000"!=e.code)throw e;t.success("新增仓库成功"),t.addModal=!1,t.goPageFirst(1)})).catch((function(t){console.log(t)})).finally((function(){t.loading=!1}))}))},editInventory:function(){var t=this;this.$refs.editForm.validate((function(e){e&&u({mcc:t.addModalData.mccList,storeId:t.addModalData.storeId,storeName:t.addModalData.storeName}).then((function(e){if(!e||"0000"!=e.code)throw e;t.success("修改仓库信息成功"),t.addModal=!1,t.goPageFirst(1)})).catch((function(t){console.log(t)})).finally((function(){t.loading=!1}))}))},goPage:function(t){this.goPageFirst(t)},cancelUpload:function(){this.file="",this.intoModal=!1,this.transfersModal=!1},fileUploading:function(t,e,a){this.message="文件上传中、待进度条消失后再操作"},fileSuccess:function(t,e,a){this.message="请先下载模板文件，并按格式填写后上传"},handleUpload:function(){var t=this;this.file?this.$refs.uploadForm.validate((function(e){if(e){t.uploading=!0;var a=new FormData;a.append("multipartFile",t.file),a.append("storeId",t.modalData.intoInventory),c(a).then((function(e){if("0000"!==e.code)throw e;t.success("操作成功"),t.cancelUpload(),t.goPageFirst(1)})).catch((function(t){console.log(t)})).finally((function(){t.uploading=!1}))}})):this.$Message.warning("请选择需要上传的文件")},success:function(t){this.$Notice.success({title:"操作成功",desc:t})}},mounted:function(){this.goPageFirst(1)},watch:{}},h=m,v=(a("ef7e"),a("2877")),g=Object(v["a"])(h,o,n,!1,null,null,null);e["default"]=g.exports},addb:function(t,e,a){"use strict";var o=a("f36a"),n=Math.floor,i=function(t,e){var a=t.length;if(a<8){var s,r,l=1;while(l<a){r=l,s=t[l];while(r&&e(t[r-1],s)>0)t[r]=t[--r];r!==l++&&(t[r]=s)}}else{var c=n(a/2),d=i(o(t,0,c),e),u=i(o(t,c),e),p=d.length,f=u.length,m=0,h=0;while(m<p||h<f)t[m+h]=m<p&&h<f?e(d[m],u[h])<=0?d[m++]:u[h++]:m<p?d[m++]:u[h++]}return t};t.exports=i},ea83:function(t,e,a){"use strict";var o=a("b5db"),n=o.match(/AppleWebKit\/(\d+)\./);t.exports=!!n&&+n[1]},ef7e:function(t,e,a){"use strict";a("982c")}}]);