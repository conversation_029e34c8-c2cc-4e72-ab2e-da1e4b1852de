(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e492816e"],{"00b4":function(e,t,a){"use strict";a("ac1f");var r=a("23e7"),o=a("c65b"),n=a("1626"),i=a("825a"),l=a("577e"),c=function(){var e=!1,t=/[ac]/;return t.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===t.test("abc")&&e}(),s=/./.test;r({target:"RegExp",proto:!0,forced:!c},{test:function(e){var t=i(this),a=l(e),r=t.exec;if(!n(r))return o(s,t,a);var c=o(r,t,a);return null!==c&&(i(c),!0)}})},"0cd5":function(e,t,a){"use strict";a.r(t);a("caad"),a("b0c0"),a("e9c4"),a("2532");var r=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{width:"100%",padiing:"16px"}},[t("div",[t("div",[t("Form",{ref:"formObj",attrs:{model:e.formObj,"label-width":170,rules:e.ruleAddValidate}},[t("Card",{staticStyle:{"margin-bottom":"16px",padiing:"16px"}},[t("p",{staticStyle:{color:"rgb(87,163,244)"},attrs:{slot:"title"},slot:"title"},[e._v("基础信息")]),t("Row",{attrs:{justify:"center"}},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"渠道商名称",prop:"corpName"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,maxlength:"50",clearable:"info"!=e.typeFlag,placeholder:"请输入渠道商名称"},model:{value:e.formObj.corpName,callback:function(t){e.$set(e.formObj,"corpName",t)},expression:"formObj.corpName"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"渠道商状态",prop:"channelStatus"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择渠道商状态",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.channelStatus,callback:function(t){e.$set(e.formObj,"channelStatus",t)},expression:"formObj.channelStatus"}},e._l(e.purchaseStatusList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"公司名称",prop:"companyName"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入公司名称",maxlength:"200"},model:{value:e.formObj.companyName,callback:function(t){e.$set(e.formObj,"companyName",t)},expression:"formObj.companyName"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"地址",prop:"address"}},[t("Input",{staticClass:"inputSty",attrs:{placeholder:"请输入地址",clearable:!0},model:{value:e.formObj.address,callback:function(t){e.$set(e.formObj,"address",t)},expression:"formObj.address"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"EBS Code",prop:"ebsCode"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,maxlength:"50",placeholder:"请输入EBS Code"},model:{value:e.formObj.ebsCode,callback:function(t){e.$set(e.formObj,"ebsCode",t)},expression:"formObj.ebsCode"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"联系人邮箱",prop:"mail"}},[t("Input",{staticClass:"inputSty",attrs:{type:"email",readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,maxlength:"50",placeholder:"请输入联系人邮箱"},model:{value:e.formObj.mail,callback:function(t){e.$set(e.formObj,"mail",t)},expression:"formObj.mail"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"货币种类",prop:"currencyCode"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择货币种类",disabled:"add"!=e.typeFlag,clearable:!1},model:{value:e.formObj.currencyCode,callback:function(t){e.$set(e.formObj,"currencyCode",t)},expression:"formObj.currencyCode"}},[t("Option",{attrs:{value:"156"}},[e._v("人民币")]),t("Option",{attrs:{value:"344"}},[e._v("港币")]),t("Option",{attrs:{value:"840"}},[e._v("美元")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"渠道合作模式",prop:"channelCooperationMode"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择渠道合作模式",multiple:"",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},on:{"on-change":e.getpackageInfo},model:{value:e.formObj.channelCooperationMode,callback:function(t){e.$set(e.formObj,"channelCooperationMode",t)},expression:"formObj.channelCooperationMode"}},[t("Option",{attrs:{disabled:e.modeList.includes("1"),value:"1"}},[e._v("代销")]),t("Option",{attrs:{disabled:e.modeList.includes("2"),value:"2"}},[e._v("A2Z")]),t("Option",{attrs:{disabled:e.modeList.includes("3"),value:"3"}},[e._v("资源合作")])],1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"内部订单",prop:"internalOrder"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择是否内部订单",clearable:!0},model:{value:e.formObj.internalOrder,callback:function(t){e.$set(e.formObj,"internalOrder",t)},expression:"formObj.internalOrder"}},[t("Option",{attrs:{value:"0"}},[e._v("是")]),t("Option",{attrs:{value:"1"}},[e._v("否")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"退订规则",prop:"unsubscribeRule",rules:"3"!=e.formObj.channelCooperationMode?e.ruleAddValidate.unsubscribeRule:[{required:!1}]}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择退订规则",readonly:"info"==e.typeFlag,disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.unsubscribeRule,callback:function(t){e.$set(e.formObj,"unsubscribeRule",t)},expression:"formObj.unsubscribeRule"}},[t("Option",{attrs:{value:"1"}},[e._v("自然月内退订")]),t("Option",{attrs:{value:"2"}},[e._v("有效期内退订")])],1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"销售经理",prop:"salesMail"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请输入销售经理邮箱",filterable:"",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.salesMail,callback:function(t){e.$set(e.formObj,"salesMail",t)},expression:"formObj.salesMail"}},e._l(e.salesMailList,(function(a,r){return t("Option",{key:r,attrs:{value:a}},[e._v(e._s(a))])})),1)],1)],1),t("Col",{attrs:{span:"12"}})],1)],1),t("Card",{staticStyle:{"margin-bottom":"16px",padiing:"16px"}},[t("p",{staticStyle:{color:"rgb(87,163,244)"},attrs:{slot:"title"},slot:"title"},[e._v("通知信息")]),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"激活通知",prop:"activateNotification",rules:"3"!=e.formObj.channelCooperationMode?e.ruleAddValidate.activateNotification:[{required:!1}]}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择是否激活通知",readonly:"info"==e.typeFlag,disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.activateNotification,callback:function(t){e.$set(e.formObj,"activateNotification",t)},expression:"formObj.activateNotification"}},[t("Option",{attrs:{value:"1"}},[e._v("开")]),t("Option",{attrs:{value:"2"}},[e._v("关")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"激活通知url",prop:"activateNotificationUrl",rules:"1"==e.formObj.activateNotification?e.ruleAddValidate.activateNotificationUrl:[{required:!1}]}},[t("Input",{staticClass:"inputSty",attrs:{placeholder:"请输入激活通知url",readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,maxlength:"255"},model:{value:e.formObj.activateNotificationUrl,callback:function(t){e.$set(e.formObj,"activateNotificationUrl",t)},expression:"formObj.activateNotificationUrl"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"到期通知",prop:"overdueNotify",rules:"3"!=e.formObj.channelCooperationMode?e.ruleAddValidate.overdueNotify:[{required:!1}]}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择到期通知",readonly:"info"==e.typeFlag,disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.overdueNotify,callback:function(t){e.$set(e.formObj,"overdueNotify",t)},expression:"formObj.overdueNotify"}},[t("Option",{attrs:{value:"1"}},[e._v("开")]),t("Option",{attrs:{value:"2"}},[e._v("关")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"到期通知url",prop:"overdueNotifyUrl",rules:"1"==e.formObj.overdueNotify?e.ruleAddValidate.overdueNotifyUrl:[{required:!1}]}},[t("Input",{staticClass:"inputSty",attrs:{placeholder:"请输入到期通知url",readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,maxlength:"255"},model:{value:e.formObj.overdueNotifyUrl,callback:function(t){e.$set(e.formObj,"overdueNotifyUrl",t)},expression:"formObj.overdueNotifyUrl"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"ESIM状态变更通知",prop:"esimNotifySwitch"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择ESIM状态变更通知",readonly:"info"==e.typeFlag,disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.esimNotifySwitch,callback:function(t){e.$set(e.formObj,"esimNotifySwitch",t)},expression:"formObj.esimNotifySwitch"}},[t("Option",{attrs:{value:"1"}},[e._v("开")]),t("Option",{attrs:{value:"2"}},[e._v("关")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"ESIM状态变更通知url",prop:"esimNotifyUrl",rules:"1"==e.formObj.esimNotifySwitch?e.ruleAddValidate.esimNotifyUrl:[{required:!1}]}},[t("Input",{staticClass:"inputSty",attrs:{placeholder:"请输入ESIM状态变更通知url",readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,maxlength:"255"},model:{value:e.formObj.esimNotifyUrl,callback:function(t){e.$set(e.formObj,"esimNotifyUrl",t)},expression:"formObj.esimNotifyUrl"}})],1)],1)],1),t("Row",["info"===e.typeFlag?t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"渠道商AppKey",prop:"appkey"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,maxlength:"32",placeholder:"请输入渠道商AppKey"},model:{value:e.formObj.appkey,callback:function(t){e.$set(e.formObj,"appkey",t)},expression:"formObj.appkey"}})],1)],1):e._e(),"info"===e.typeFlag?t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"渠道商AppSecret",prop:"appSecret"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,maxlength:"32",placeholder:"请输入渠道商AppSecret"},model:{value:e.formObj.appSecret,callback:function(t){e.$set(e.formObj,"appSecret",t)},expression:"formObj.appSecret"}})],1)],1):e._e()],1),t("div",{staticStyle:{display:"flex","flex-wrap":"wrap"}},[t("div",{staticStyle:{width:"50%"}},[e._l(e.formObj.packageUsePercentage,(function(a,r){return t("FormItem",{key:r,attrs:{label:"套餐用量提醒百分比(%)",prop:"packageUsePercentage."+r+".value",rules:e.ruleAddValidate.value}},[t("div",[t("Input",{staticClass:"input-text t inputSty",attrs:{type:"number",readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入"},model:{value:a.value,callback:function(t){e.$set(a,"value",t)},expression:"item.value"}}),e._v("    \n          \t\t\t\t"),1!=a.index&&"info"!=e.typeFlag?t("Button",{attrs:{type:"error",ghost:""},on:{click:function(t){return e.handleRemove(r)}}},[e._v("删除")]):e._e()],1)])})),"info"!==e.typeFlag?t("div",{staticStyle:{width:"80px","margin-left":"150px"}},[t("Button",{attrs:{type:"success",ghost:"",long:"",icon:"md-add"},on:{click:e.handleAdd}},[e._v("添加")])],1):e._e()],2),t("div",{staticStyle:{width:"50%"}},[t("FormItem",{attrs:{label:"流量通知URL",prop:"packageUseNotifyUrl"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入"},model:{value:e.formObj.packageUseNotifyUrl,callback:function(t){e.$set(e.formObj,"packageUseNotifyUrl",t)},expression:"formObj.packageUseNotifyUrl"}})],1)],1)])],1),e.formObj.channelCooperationMode.includes("1")?t("Card",{staticStyle:{"margin-bottom":"16px",padiing:"16px"}},[t("p",{staticStyle:{color:"rgb(87,163,244)"},attrs:{slot:"title"},slot:"title"},[e._v("代销信息")]),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"可用金额提醒阀值",prop:"depositNotify",rules:e.formObj.channelCooperationMode.includes("1")?e.ruleAddValidate.depositNotify:[{required:!1}]}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,maxlength:"13",placeholder:"请输入可用金额提醒阀值"},model:{value:e.formObj.depositNotify,callback:function(t){e.$set(e.formObj,"depositNotify",t)},expression:"formObj.depositNotify"}})],1)],1),t("Col",{attrs:{span:"12"}})],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"渠道商类型",prop:"channelType",rules:e.formObj.channelCooperationMode.includes("1")?e.ruleAddValidate.channelType:[{required:!1}]}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择渠道商类型",readonly:"info"==e.typeFlag,disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.channelType,callback:function(t){e.$set(e.formObj,"channelType",t)},expression:"formObj.channelType"}},[t("Option",{attrs:{value:"1"}},[e._v("押金模式")]),t("Option",{attrs:{value:"2"}},[e._v("预存模式")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"购买折扣(%)",prop:"discount"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:"3",readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入购买折扣(%)"},model:{value:e.formObj.discount,callback:function(t){e.$set(e.formObj,"discount",t)},expression:"formObj.discount"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"代销合约开始时间",prop:"contractBeginTime",rules:e.formObj.contractEndTime?e.ruleAddValidate.contractBeginTime:[{required:!1}]}},[t("DatePicker",{staticClass:"inputSty",attrs:{type:"datetime",format:"yyyy/MM/dd HH:mm:ss",placeholder:"请选择代销合约开始时间",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.contractBeginTime,callback:function(t){e.$set(e.formObj,"contractBeginTime",t)},expression:"formObj.contractBeginTime"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"代销合约结束时间",prop:"contractEndTime",rules:e.formObj.contractBeginTime?e.ruleAddValidate.contractEndTime:[{required:!1}]}},[t("DatePicker",{staticClass:"inputSty",attrs:{type:"datetime",format:"yyyy/MM/dd HH:mm:ss",placeholder:"请选择代销合约结束时间",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.contractEndTime,callback:function(t){e.$set(e.formObj,"contractEndTime",t)},expression:"formObj.contractEndTime"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"合约期承诺销售金额",prop:"contractSellAmount"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,maxlength:"13",placeholder:"请输入承诺销售金额"},model:{value:e.formObj.contractSellAmount,callback:function(t){e.$set(e.formObj,"contractSellAmount",t)},expression:"formObj.contractSellAmount"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"出账规则",prop:"distributionAccountingPeriodId"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择出账规则",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.distributionAccountingPeriodId,callback:function(t){e.$set(e.formObj,"distributionAccountingPeriodId",t)},expression:"formObj.distributionAccountingPeriodId"}},e._l(e.acountingList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.name)+"\n            \t\t")])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"IMSI费规则",prop:"consignmentImsiAmount"}},[t("Select",{staticStyle:{width:"85%"},attrs:{placeholder:"请选择IMSI费规则",multiple:"",filterable:"",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.consignmentImsiAmount,callback:function(t){e.$set(e.formObj,"consignmentImsiAmount",t)},expression:"formObj.consignmentImsiAmount"}},e._l(e.imsiFeeList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.ruleName))])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"短信模板",prop:"dxSmsTemplateVo",rules:e.formObj.channelCooperationMode.includes("1")?e.ruleAddValidate.dxSmsTemplateVo:[{required:!1}]}},[t("Select",{staticStyle:{width:"85%"},attrs:{filterable:"",multiple:"",placeholder:"请选择短信模板",readonly:"info"==e.typeFlag,disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.dxSmsTemplateVo,callback:function(t){e.$set(e.formObj,"dxSmsTemplateVo",t)},expression:"formObj.dxSmsTemplateVo"}},e._l(e.tempList,(function(a,r){return t("Option",{key:r,attrs:{value:JSON.stringify({templateId:a.id,templateName:a.templateName,cooperationMode:"1"})}},[e._v(e._s(a.templateName))])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[e.formObj.channelCooperationMode.includes("1")?t("FormItem",{attrs:{label:"可购买套餐组",prop:"dxPackageInfosString",rules:e.formObj.channelCooperationMode.includes("1")?e.ruleAddValidate.dxPackageInfosString:[{required:!1}]}},[t("Select",{staticStyle:{width:"85%"},attrs:{multiple:"",placeholder:"请选择可购买套餐组",disabled:"info"==e.typeFlag||e.packageFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.dxPackageInfosString,callback:function(t){e.$set(e.formObj,"dxPackageInfosString",t)},expression:"formObj.dxPackageInfosString"}},e._l(e.dxPackageList,(function(a){return t("Option",{key:a.groupId,attrs:{value:JSON.stringify({groupId:a.groupId,groupName:a.groupName,channelCooperationMode:a.cooperationMode})}},[e._v(e._s(a.groupName)+"\n            \t\t")])})),1)],1):e._e()],1)],1),t("Row",["info"==e.typeFlag?t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"营销账户",prop:"marketingAmount"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入营销账户"},model:{value:e.formObj.marketingAmount,callback:function(t){e.$set(e.formObj,"marketingAmount",t)},expression:"formObj.marketingAmount "}})],1)],1):e._e(),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"信用账户",prop:"creditAmount"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入信用账户"},model:{value:e.formObj.creditAmount,callback:function(t){e.$set(e.formObj,"creditAmount",t)},expression:"formObj.creditAmount "}})],1)],1)],1)],1):e._e(),e.formObj.channelCooperationMode.includes("2")?t("Card",{staticStyle:{"margin-bottom":"16px",padiing:"16px"}},[t("p",{staticStyle:{color:"rgb(87,163,244)"},attrs:{slot:"title"},slot:"title"},[e._v("A2Z信息")]),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"渠道商类型",prop:"a2zChannelType",rules:e.formObj.channelCooperationMode.includes("2")?e.ruleAddValidate.a2zChannelType:[{required:!1}]}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择渠道商类型",readonly:"info"==e.typeFlag,disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.a2zChannelType,callback:function(t){e.$set(e.formObj,"a2zChannelType",t)},expression:"formObj.a2zChannelType"}},[t("Option",{attrs:{value:"1"}},[e._v("押金模式")]),t("Option",{attrs:{value:"2"}},[e._v("预存模式")])],1)],1)],1),t("Col",{attrs:{span:"12"}})],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"A2Z合约开始时间",prop:"a2zContractStartTime",rules:e.formObj.a2zContractEndTime?e.ruleAddValidate.a2zContractStartTime:[{required:!1}]}},[t("DatePicker",{staticClass:"inputSty",attrs:{type:"datetime",format:"yyyy/MM/dd HH:mm:ss",placeholder:"请选择A2Z合约开始时间",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.a2zContractStartTime,callback:function(t){e.$set(e.formObj,"a2zContractStartTime",t)},expression:"formObj.a2zContractStartTime"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"A2Z合约结束时间",prop:"a2zContractEndTime",rules:e.formObj.a2zContractStartTime?e.ruleAddValidate.a2zContractEndTime:[{required:!1}]}},[t("DatePicker",{staticClass:"inputSty",attrs:{type:"datetime",format:"yyyy/MM/dd HH:mm:ss",placeholder:"请选择A2Z合约结束时间",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.a2zContractEndTime,callback:function(t){e.$set(e.formObj,"a2zContractEndTime",t)},expression:"formObj.a2zContractEndTime"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[e.formObj.channelCooperationMode.includes("2")?t("FormItem",{attrs:{label:"可购买套餐组",prop:"a2zPackageInfosString",rules:e.formObj.channelCooperationMode.includes("2")?e.ruleAddValidate.a2zPackageInfosString:[{required:!1}]}},[t("Select",{staticStyle:{width:"85%"},attrs:{multiple:"",placeholder:"请选择可购买套餐组",disabled:"info"==e.typeFlag||e.packageFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.a2zPackageInfosString,callback:function(t){e.$set(e.formObj,"a2zPackageInfosString",t)},expression:"formObj.a2zPackageInfosString"}},e._l(e.a2zPackageList,(function(a){return t("Option",{key:a.groupId,attrs:{value:JSON.stringify({groupId:a.groupId,groupName:a.groupName,channelCooperationMode:a.cooperationMode})}},[e._v(e._s(a.groupName)+"\n            \t\t")])})),1)],1):e._e()],1),t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"流量计费规则",prop:"a2zRuleId",rules:e.formObj.channelCooperationMode.includes("2")?e.ruleAddValidate.a2zRuleId:[{required:!1}]}},[t("Select",{staticStyle:{width:"85%"},attrs:{filterable:"",multiple:"",placeholder:"请选择流量计费规则",readonly:"info"==e.typeFlag,disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.a2zRuleId,callback:function(t){e.$set(e.formObj,"a2zRuleId",t)},expression:"formObj.a2zRuleId"}},e._l(e.a2zRuleList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.name))])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"允许自建套餐",prop:"allowNewPackage",rules:"3"!=e.formObj.channelCooperationMode?e.ruleAddValidate.allowNewPackage:[{required:!1}]}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择允许自建套餐",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},on:{"on-change":e.changeChannelCreat},model:{value:e.formObj.allowNewPackage,callback:function(t){e.$set(e.formObj,"allowNewPackage",t)},expression:"formObj.allowNewPackage"}},[t("Option",{attrs:{value:"1"}},[e._v("是")]),t("Option",{attrs:{value:"2"}},[e._v("否")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{directives:[{name:"show",rawName:"v-show",value:"1"==e.formObj.allowNewPackage,expression:"formObj.allowNewPackage == '1'"}],attrs:{label:"自建套餐上限",prop:"limitPacakageNum"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入自建套餐上限"},model:{value:e.formObj.limitPacakageNum,callback:function(t){e.$set(e.formObj,"limitPacakageNum",t)},expression:"formObj.limitPacakageNum"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{directives:[{name:"show",rawName:"v-show",value:"1"==e.formObj.allowNewPackage,expression:"formObj.allowNewPackage == '1'"}],attrs:{label:"国家卡池关联组",prop:"groupId"}},[t("Select",{staticStyle:{width:"85%"},attrs:{filterable:"",multiple:"",placeholder:"选择国家卡池关联组",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.groupId,callback:function(t){e.$set(e.formObj,"groupId",t)},expression:"formObj.groupId"}},e._l(e.groupIdList,(function(a,r){return t("Option",{key:a.groupId,attrs:{title:a.groupName,value:a.groupId}},[e._v(e._s(a.groupName.length>30?a.groupName.substring(0,30)+"…":a.groupName)+"\n            \t\t")])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{directives:[{name:"show",rawName:"v-show",value:"1"==e.formObj.allowNewPackage,expression:"formObj.allowNewPackage == '1'"}],attrs:{label:"可用速度模板",prop:"upccTemplateIds"}},[t("Select",{staticStyle:{width:"85%"},attrs:{filterable:"",multiple:"",placeholder:"选择速度模板",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.upccTemplateIds,callback:function(t){e.$set(e.formObj,"upccTemplateIds",t)},expression:"formObj.upccTemplateIds"}},e._l(e.TemplatesCreatList,(function(a){return t("Option",{key:a.templateId,attrs:{title:a.templateName,value:a.templateId}},[e._v(e._s(a.templateName.length>30?a.templateName.substring(0,30)+"…":a.templateName)+"\n\t\t\t\t\t\t\t\t")])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[e.formObj.channelCooperationMode.includes("2")&&"1"==e.formObj.allowNewPackage?t("FormItem",{attrs:{label:"可用定向应用",prop:"appids"}},[t("Select",{staticStyle:{width:"85%"},attrs:{filterable:"",multiple:"",placeholder:"选择应用列表",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.appids,callback:function(t){e.$set(e.formObj,"appids",t)},expression:"formObj.appids"}},e._l(e.appidsList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.appName))])})),1)],1):e._e()],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"自建套餐是否审批",prop:"approvalPackage",rules:e.formObj.channelCooperationMode.includes("2")&&"1"==e.formObj.allowNewPackage?e.ruleAddValidate.approvalPackage:[{required:!1}]}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"选择自建套餐是否审批",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.approvalPackage,callback:function(t){e.$set(e.formObj,"approvalPackage",t)},expression:"formObj.approvalPackage"}},[t("Option",{attrs:{value:"1"}},[e._v("是")]),t("Option",{attrs:{value:"2"}},[e._v("否")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"额度用尽提醒阈值",prop:"runoutofBalanceRemindThreshold"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入额度用尽提醒阈值"},model:{value:e.formObj.runoutofBalanceRemindThreshold,callback:function(t){e.$set(e.formObj,"runoutofBalanceRemindThreshold",t)},expression:"formObj.runoutofBalanceRemindThreshold "}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"禁止购买提醒阈值",prop:"prohibitiveBuyRemindThreshold"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入禁止购买提醒阈值"},model:{value:e.formObj.prohibitiveBuyRemindThreshold,callback:function(t){e.$set(e.formObj,"prohibitiveBuyRemindThreshold",t)},expression:"formObj.prohibitiveBuyRemindThreshold "}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"停止使用提醒阈值",prop:"stopUseRemindThreshold"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入停止使用提醒阈值"},model:{value:e.formObj.stopUseRemindThreshold,callback:function(t){e.$set(e.formObj,"stopUseRemindThreshold",t)},expression:"formObj.stopUseRemindThreshold "}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"合约期承诺销售金额",prop:"a2zDepositAmount"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,maxlength:"13",placeholder:"请输入承诺销售金额"},model:{value:e.formObj.a2zDepositAmount,callback:function(t){e.$set(e.formObj,"a2zDepositAmount",t)},expression:"formObj.a2zDepositAmount"}})],1)],1),"info"==e.typeFlag?t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"营销账户",prop:"a2zMarketingAmount"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入营销账户"},model:{value:e.formObj.a2zMarketingAmount,callback:function(t){e.$set(e.formObj,"a2zMarketingAmount",t)},expression:"formObj.a2zMarketingAmount "}})],1)],1):e._e()],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"出账规则",prop:"a2zAccountingPeriodId"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择出账规则",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.a2zAccountingPeriodId,callback:function(t){e.$set(e.formObj,"a2zAccountingPeriodId",t)},expression:"formObj.a2zAccountingPeriodId"}},e._l(e.acountingList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.name)+"\n            \t\t")])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"IMSI费规则",prop:"a2zImsiAmount"}},[t("Select",{staticStyle:{width:"85%"},attrs:{placeholder:"请选择IMSI费规则",multiple:"",filterable:"",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.a2zImsiAmount,callback:function(t){e.$set(e.formObj,"a2zImsiAmount",t)},expression:"formObj.a2zImsiAmount"}},e._l(e.imsiFeeList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.ruleName)+"\n          \t\t\t")])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"短信模板",multiple:"",prop:"a2zSmsTemplateVo",rules:e.formObj.channelCooperationMode.includes("2")?e.ruleAddValidate.a2zSmsTemplateVo:[{required:!1}]}},[t("Select",{staticStyle:{width:"85%"},attrs:{filterable:"",multiple:"",placeholder:"请选择短信模板",readonly:"info"==e.typeFlag,disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.a2zSmsTemplateVo,callback:function(t){e.$set(e.formObj,"a2zSmsTemplateVo",t)},expression:"formObj.a2zSmsTemplateVo"}},e._l(e.tempList,(function(a,r){return t("Option",{key:r,attrs:{value:JSON.stringify({templateId:a.id,templateName:a.templateName,cooperationMode:"2"})}},[e._v(e._s(a.templateName))])})),1)],1)],1)],1),"info"===e.typeFlag?t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"A2Z预存款额度",prop:"a2zPreDeposit"}},[t("Input",{staticClass:"inputSty",attrs:{placeholder:"请输入A2Z预存款额度",readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.a2zPreDeposit,callback:function(t){e.$set(e.formObj,"a2zPreDeposit",t)},expression:"formObj.a2zPreDeposit "}})],1)],1),e.formObj.channelCooperationMode.includes("2")?t("Col",{attrs:{span:"12"}},[t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"a2zStop",expression:"'a2zStop'"}],attrs:{type:"error",icon:"md-close",disabled:"1"!=e.formObj.a2zCardUseSwitch,ghost:""},on:{click:e.a2zStop}},[e._v(e._s(e.a2zStopStatus))]),e._v("    \n\t\t\t\t\t\t\t"),t("Button",{directives:[{name:"has",rawName:"v-has",value:"a2zRecover",expression:"'a2zRecover'"}],attrs:{type:"success",icon:"md-checkmark",disabled:"2"!==e.formObj.a2zCardUseSwitch,ghost:""},on:{click:e.a2zRecover}},[e._v(e._s(e.a2zRecoverStatus))])],1)],1):e._e()],1):e._e()],1):e._e(),e.formObj.channelCooperationMode.includes("2")?t("Card",{staticStyle:{"margin-bottom":"16px",padding:"16px"}},[t("p",{staticStyle:{color:"rgb(87,163,244)"},attrs:{slot:"title"},slot:"title"},[e._v("运营商配置信息")]),e._l(e.formObj.a2zOperators,(function(a,r){return t("Row",{key:r},[t("Col",{attrs:{span:"6"}},[t("FormItem",{attrs:{label:"国家/地区",prop:"a2zOperators."+r+".a2zOperatorMcc",rules:e.formObj.channelCooperationMode.includes("2")?e.ruleAddValidate.country:[{required:!1}]}},[t("Select",{staticStyle:{width:"85%"},attrs:{filterable:"",placeholder:"请选择国家/地区",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},on:{"on-change":function(t){return e.handleCountryChange(a.a2zOperatorMcc,r,1)}},model:{value:a.a2zOperatorMcc,callback:function(t){e.$set(a,"a2zOperatorMcc",t)},expression:"config.a2zOperatorMcc"}},e._l(e.localList,(function(a){return t("Option",{key:a.mcc,attrs:{value:a.mcc}},[e._v(e._s(a.countryEn))])})),1)],1)],1),t("Col",{attrs:{span:"6"}},[t("FormItem",{attrs:{label:"计费规则",multiple:"",prop:"a2zOperators."+r+".a2zOperatorChargeType",rules:e.formObj.channelCooperationMode.includes("2")?e.ruleAddValidate.rule:[{required:!1}]}},[t("Select",{staticStyle:{width:"85%"},attrs:{placeholder:"请选择计费规则",readonly:"info"==e.typeFlag,disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:a.a2zOperatorChargeType,callback:function(t){e.$set(a,"a2zOperatorChargeType",t)},expression:"config.a2zOperatorChargeType"}},e._l(e.chargingRule,(function(a){return t("Option",{key:a.code,attrs:{value:a.code}},[e._v(e._s(a.chargingName))])})),1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"运营商",prop:"a2zOperators."+r+".a2zOperator",rules:e.formObj.channelCooperationMode.includes("2")?e.ruleAddValidate.operator:[{required:!1}]}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Select",{staticClass:"inputSty",staticStyle:{width:"80%","margin-right":"10px"},attrs:{filterable:"",multiple:"",placeholder:"请选择运营商",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:a.a2zOperator,callback:function(t){e.$set(a,"a2zOperator",t)},expression:"config.a2zOperator"}},e._l(a.operatorsList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.operatorName))])})),1),e.formObj.a2zOperators.length>1&&0!=r&&"info"!==e.typeFlag?t("Button",{staticStyle:{width:"40px",height:"25px"},attrs:{type:"error",size:"small",loading:e.deleteLoading},on:{click:function(t){return e.removeRule(r)}}},[e._v("删除")]):e._e(),r===e.formObj.a2zOperators.length-1&&"info"!==e.typeFlag?t("Button",{attrs:{type:"info",size:"small",loading:e.addLoading},on:{click:e.addRuleHandle}},[e._v("添加")]):e._e()],1)])],1)],1)}))],2):e._e(),e.formObj.channelCooperationMode.includes("3")?t("Card",{staticStyle:{"margin-bottom":"16px",padiing:"16px"}},[t("p",{staticStyle:{color:"rgb(87,163,244)"},attrs:{slot:"title"},slot:"title"},[e._v("资源合作信息")]),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"渠道商类型",prop:"resourceChannelType",rules:e.formObj.channelCooperationMode.includes("3")?e.ruleAddValidate.resourceChannelType:[{required:!1}]}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择渠道商类型",readonly:"info"==e.typeFlag,disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.resourceChannelType,callback:function(t){e.$set(e.formObj,"resourceChannelType",t)},expression:"formObj.resourceChannelType"}},[t("Option",{attrs:{value:"1"}},[e._v("押金模式")]),t("Option",{attrs:{value:"2"}},[e._v("预存模式")])],1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"额度用尽提醒阈值",prop:"resourceRunoutofBalanceRemindThreshold"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入额度用尽提醒阈值"},model:{value:e.formObj.resourceRunoutofBalanceRemindThreshold,callback:function(t){e.$set(e.formObj,"resourceRunoutofBalanceRemindThreshold",t)},expression:"formObj.resourceRunoutofBalanceRemindThreshold "}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"禁止购买提醒阈值",prop:"resourceProhibitiveBuyRemindThreshold"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入禁止购买提醒阈值"},model:{value:e.formObj.resourceProhibitiveBuyRemindThreshold,callback:function(t){e.$set(e.formObj,"resourceProhibitiveBuyRemindThreshold",t)},expression:"formObj.resourceProhibitiveBuyRemindThreshold "}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"停止使用提醒阈值",prop:"resourceStopUseRemindThreshold"}},[t("Input",{staticClass:"inputSty",attrs:{readonly:"info"==e.typeFlag,clearable:"info"!=e.typeFlag,placeholder:"请输入停止使用提醒阈值"},model:{value:e.formObj.resourceStopUseRemindThreshold,callback:function(t){e.$set(e.formObj,"resourceStopUseRemindThreshold",t)},expression:"formObj.resourceStopUseRemindThreshold "}})],1)],1),t("Col",{attrs:{span:"12"}},[e.formObj.channelCooperationMode.includes("2")?t("FormItem",{attrs:{label:"出账规则",prop:"a2zAccountingPeriodId"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择出账规则",disabled:"",clearable:"info"!=e.typeFlag},model:{value:e.formObj.a2zAccountingPeriodId,callback:function(t){e.$set(e.formObj,"a2zAccountingPeriodId",t)},expression:"formObj.a2zAccountingPeriodId"}},e._l(e.acountingList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.name)+"\n            \t\t")])})),1)],1):t("FormItem",{attrs:{label:"出账规则",prop:"resourceAccountingPeriodId"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择出账规则",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.resourceAccountingPeriodId,callback:function(t){e.$set(e.formObj,"resourceAccountingPeriodId",t)},expression:"formObj.resourceAccountingPeriodId"}},e._l(e.acountingList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.name)+"\n            \t\t")])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"IMSI费规则",prop:"resourceImsiAmount"}},[t("Select",{staticStyle:{width:"85%"},attrs:{placeholder:"请选择IMSI费规则",multiple:"",filterable:"",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.resourceImsiAmount,callback:function(t){e.$set(e.formObj,"resourceImsiAmount",t)},expression:"formObj.resourceImsiAmount"}},e._l(e.imsiFeeList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.ruleName))])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"流量计费规则",prop:"resourceRuleId",rules:e.formObj.channelCooperationMode.includes("3")?e.ruleAddValidate.resourceRuleId:[{required:!1}]}},[t("Select",{staticStyle:{width:"85%"},attrs:{multiple:"",placeholder:"请选择流量计费规则",disabled:"info"==e.typeFlag,clearable:"info"!=e.typeFlag},model:{value:e.formObj.resourceRuleId,callback:function(t){e.$set(e.formObj,"resourceRuleId",t)},expression:"formObj.resourceRuleId"}},e._l(e.a2zRuleList,(function(a){return t("Option",{key:a.id,attrs:{value:a.id}},[e._v(e._s(a.name)+"\n              \t\t")])})),1)],1)],1)],1)],1):e._e(),"info"===e.typeFlag?t("Card",{staticStyle:{"margin-bottom":"16px",padiing:"16px",color:"brown"}},[t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"充值记录"}},[t("Button",{staticClass:"inputSty",attrs:{type:"primary",ghost:"",long:"",icon:"md-add"},on:{click:e.getRechargeRecord}},[e._v("查看充值记录")])],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐购买记录"}},[t("Button",{staticClass:"inputSty",attrs:{type:"info",ghost:"",long:"",icon:"md-add"},on:{click:e.getPackageRecord}},[e._v("查看套餐购买记录")])],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"账单流水"}},[t("Button",{staticClass:"inputSty",attrs:{type:"error",ghost:"",long:"",icon:"md-add"},on:{click:e.getBillFlow}},[e._v("查看账单流水")])],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{directives:[{name:"has",rawName:"v-has",value:"trafficDetails",expression:"'trafficDetails'"}],attrs:{label:"流量明细"}},[t("Button",{staticClass:"inputSty",attrs:{type:"warning",ghost:"",long:"",icon:"md-add"},on:{click:e.getTrafficDetails}},[e._v("查看流量明细")])],1)],1)],1),e.formObj.channelCooperationMode.includes("1")?t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{directives:[{name:"has",rawName:"v-has",value:"marketingAccountConsignment",expression:"'marketingAccountConsignment'"}],attrs:{label:"营销款账户详情【代销】"}},[t("Button",{staticClass:"inputSty",attrs:{type:"success",ghost:"",long:"",icon:"md-add"},on:{click:function(t){return e.toMarketAccout(1)}}},[e._v("营销款账户详情【代销】")])],1)],1)],1):e._e(),e.formObj.channelCooperationMode.includes("2")?t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{directives:[{name:"has",rawName:"v-has",value:"marketingAccountA2Z",expression:"'marketingAccountA2Z'"}],attrs:{label:"营销款账户详情【A2Z】"}},[t("Button",{staticClass:"inputSty",attrs:{type:"success",ghost:"",long:"",icon:"md-add"},on:{click:function(t){return e.toMarketAccout(2)}}},[e._v("营销款账户详情【A2Z】")])],1)],1)],1):e._e(),e.formObj.channelCooperationMode.includes("3")?t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{directives:[{name:"has",rawName:"v-has",value:"marketingAccountResource",expression:"'marketingAccountResource'"}],attrs:{label:"营销款账户详情【资源合作】"}},[t("Button",{staticClass:"inputSty",attrs:{type:"success",ghost:"",long:"",icon:"md-add"},on:{click:function(t){return e.toMarketAccout(3)}}},[e._v("营销款账户详情【资源合作】")])],1)],1)],1):e._e()],1):e._e(),"info"!=e.typeFlag?t("div",{staticStyle:{"text-align":"center"}},[t("Button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.submit}},[e._v("提交")]),t("Button",{staticStyle:{"margin-left":"8px"},on:{click:function(t){return e.reset("formObj")}}},[e._v("重置")])],1):e._e(),"info"===e.typeFlag?t("div",{staticStyle:{"text-align":"center","margin-top":"20px"}},[t("Button",{on:{click:e.back}},[e._v("返回")])],1):e._e()],1)],1)]),t("Modal",{attrs:{title:"充值记录","footer-hide":!0,"mask-closable":!1,width:"800px"},model:{value:e.rechargeRecordFlag,callback:function(t){e.rechargeRecordFlag=t},expression:"rechargeRecordFlag"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("Button",{directives:[{name:"has",rawName:"v-has",value:!0,expression:"true || 'export'"}],staticStyle:{margin:"0 2px"},attrs:{type:"success"},on:{click:e.exportDistributorsRecord}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-cloud-download-outline"}}),e._v(" 导出\n\t\t\t\t\t")],1)]),t("div",{staticStyle:{margin:"20px 0"}},[t("Table",{attrs:{columns:e.rechargeColumns,data:e.rechargeTableData,ellipsis:!0,loading:e.rechargeTableLoading}}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.rechargeTotal,"page-size":e.rechargePageSize,current:e.rechargePage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.rechargePage=t},"on-change":e.getDistributorsRecord}})],1)],1)]),t("Modal",{attrs:{title:"套餐购买记录","footer-hide":!0,"mask-closable":!1,width:"1300px"},on:{"on-cancel":e.packagePurchaseCancel},model:{value:e.packageRecordFlage,callback:function(t){e.packageRecordFlage=t},expression:"packageRecordFlage"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("div",{staticStyle:{display:"flex","flex-directratioion":"row"}},[t("DatePicker",{staticClass:"recordBtnSty",attrs:{type:"month",format:"yyyy-MM",placeholder:"请选择月份",clearable:!0},on:{"on-change":e.selectTime}}),t("Button",{staticStyle:{margin:"0 2px"},attrs:{type:"primary"},on:{click:function(t){return e.getPurchaseRecords(0)}}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-search"}}),e._v(" 查询\n\t\t\t\t\t\t")],1)]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"buyRecord",expression:"'buyRecord'"}],staticStyle:{margin:"0 2px"},attrs:{type:"success"},on:{click:e.exportPackageRecord}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-cloud-download-outline"}}),e._v(" 导出购买记录\n\t\t\t\t\t\t")],1)]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"payRecord",expression:"'payRecord'"}],staticStyle:{margin:"0 2px"},attrs:{type:"success"},on:{click:e.exportRemunerateDetail}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-cloud-download-outline"}}),e._v(" 导出酬金详情\n\t\t\t\t\t\t")],1)])],1),t("div",{staticStyle:{margin:"20px 0"}},[t("Table",{attrs:{columns:e.packageColumns,data:e.packageTableData,ellipsis:!0,loading:e.packageTableLoading},scopedSlots:e._u([{key:"orderInfo",fn:function(a){var r=a.row;a.index;return[t("a",{staticStyle:{color:"#55aaff"},on:{click:function(t){return e.showOrderInfo(r.orderId)}}},[e._v("查看详情")])]}},{key:"action",fn:function(a){var r=a.row;a.index;return["2"==r.orderStatus&&"2"==r.orderType||("1"==r.orderStatus||"2"==r.orderStatus)&&"3"==r.orderType||"2"==r.orderStatus&&"7"==r.orderType?t("Button",{staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"error"},on:{click:function(t){return e.unsubscribeAll(r.orderId)}}},[e._v("退订")]):e._e(),"4"==r.orderStatus?t("Button",{staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.examine(r.orderId,"2")}}},[e._v("通过")]):e._e(),"4"==r.orderStatus?t("Button",{staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.examine(r.orderId,"3")}}},[e._v("不通过")]):e._e()]}}])}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.packageTotal,"page-size":e.packagePageSize,current:e.packagePage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.packagePage=t},"on-change":e.getPurchaseRecords}})],1)])]),t("Modal",{attrs:{title:"子订单详情记录","footer-hide":!0,"mask-closable":!1,width:"900px"},model:{value:e.packageRecordDetailsFlage,callback:function(t){e.packageRecordDetailsFlage=t},expression:"packageRecordDetailsFlage"}},[t("div",{staticStyle:{margin:"20px 0"}},[t("Table",{attrs:{columns:e.packageDetailsColumns,data:e.packageDetailsTableData,ellipsis:!0,loading:e.packageTableDetailsLoading},scopedSlots:e._u([{key:"action1",fn:function(a){var r=a.row;a.index;return["2"==r.orderStatus&&"2"==r.orderType||("1"==r.orderStatus||"2"==r.orderStatus)&&"3"==r.orderType&&"7"!=r.orderType?t("Button",{staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"error"},on:{click:function(t){return e.unsubscribeModal(r.id)}}},[e._v("退订")]):e._e(),"4"==r.orderStatus&&"7"!=r.orderType?t("Button",{staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.examineModal(r.id,"2")}}},[e._v("通过")]):e._e(),"4"==r.orderStatus&&"7"!=r.orderType?t("Button",{staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.examineModal(r.id,"3")}}},[e._v("不通过")]):e._e()]}}])}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.packageDetailsTotal,"page-size":e.packageDetailsPageSize,current:e.packageDetailsPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.packageDetailsPage=t},"on-change":e.getPurchaseRecordsDetails}})],1)]),t("Modal",{attrs:{title:"账单流水","footer-hide":!0,"mask-closable":!1,width:"800px"},on:{"on-cancel":e.cancelModal},model:{value:e.billFlowFlage,callback:function(t){e.billFlowFlage=t},expression:"billFlowFlage"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("div",{staticStyle:{display:"flex","justify-content":"flex-start","align-items":"flex-start"}},[t("Form",{ref:"form",attrs:{model:e.form,rules:e.billRule,"label-width":120}},[t("FormItem",{attrs:{label:"选择起止日期",prop:"date"}},[t("DatePicker",{staticClass:"recordBtnSty",staticStyle:{width:"200px"},attrs:{type:"daterange",format:"yyyy-MM-dd",placement:"bottom-end",clearable:!0,placeholder:"选择日期范围"},on:{"on-change":e.handleDateChange,"on-clear":e.hanldeDateClear},model:{value:e.form.date,callback:function(t){e.$set(e.form,"date",t)},expression:"form.date"}})],1)],1),e._v("  \n\t\t\t\t\t"),t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",loading:e.searchloading},on:{click:function(t){return e.searchBill()}}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"md-search"}}),e._v(" 搜索\n\t\t\t\t\t\t")],1)]),e._v("    \n\t\t\t\t\t"),t("Button",{directives:[{name:"has",rawName:"v-has",value:"exportBillList",expression:"'exportBillList'"}],attrs:{type:"success",loading:e.billExportLoading},on:{click:e.exportBillFlow}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-cloud-download-outline"}}),e._v(" 导出\n\t\t\t\t\t\t")],1)]),e._v("    \n\t\t\t\t\t"),t("Button",{on:{click:e.cancelModal}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-arrow-back"}}),e._v(" 返回\n\t\t\t\t\t\t")],1)])],1),t("div",[t("Table",{attrs:{columns:e.columns12,data:e.billData,loading:e.billLoading,"no-data-text":"暂无数据"}}),t("div",{staticStyle:{"margin-top":"40px"}},[t("Page",{attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1)],1)])]),t("Modal",{attrs:{title:"flowInfo","footer-hide":!0,"mask-closable":!1,width:"900px"},on:{"on-cancel":e.cancelModal},model:{value:e.trafficDetailsFlage,callback:function(t){e.trafficDetailsFlage=t},expression:"trafficDetailsFlage"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("div",{staticStyle:{width:"100%",display:"flex","justify-content":"space-evenly","align-items":"self-start"}},[t("Form",{ref:"form",staticStyle:{display:"flex","justify-content":"center","align-items":"center"},attrs:{model:e.trafficForm,rules:e.trafficRule,inline:"","label-width":110}},[t("FormItem",{attrs:{label:"Select Date",prop:"date"}},[t("DatePicker",{staticClass:"recordBtnSty",staticStyle:{width:"200px"},attrs:{type:"daterange",format:"yyyy-MM-dd",placement:"bottom-end",clearable:!0,placeholder:"Please Select Date"},on:{"on-change":e.handleDateChange1,"on-clear":e.hanldeDateClear1},model:{value:e.trafficForm.date,callback:function(t){e.$set(e.trafficForm,"date",t)},expression:"trafficForm.date"}})],1),t("FormItem",{attrs:{label:"Select destination",prop:"localId"}},[t("Select",{staticStyle:{width:"200px"},attrs:{filterable:"",placeholder:"Select destination",clearable:!0},on:{"on-change":e.getLocalList},model:{value:e.trafficForm.localId,callback:function(t){e.$set(e.trafficForm,"localId",t)},expression:"trafficForm.localId"}},e._l(e.localList,(function(a){return t("Option",{key:a.id,attrs:{value:a.mcc}},[e._v(e._s(a.countryEn)+"\n\t\t\t\t\t\t\t\t")])})),1)],1)],1),t("div",{staticStyle:{display:"flex","justify-content":"center","align-items":"center"}},[t("Button",{attrs:{type:"primary",loading:e.searchTrafficLoading,icon:"md-search"},on:{click:function(t){return e.searchTraffic()}}},[e._v("query")]),e._v("\n                  \n\t\t\t\t\t\t"),t("Button",{attrs:{type:"info",loading:e.exportTrafficLoading,icon:"md-arrow-down"},on:{click:function(t){return e.exportTraffic()}}},[e._v("Export")])],1)],1),t("div",[t("Table",{attrs:{columns:e.columnsTraffic,data:e.trafficData,loading:e.trafficLoading,"no-data-text":"No Data"}}),t("div",{staticStyle:{"margin-top":"40px"}},[t("Page",{attrs:{total:e.totalTraffic,current:e.currentPageTraffic,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPageTraffic=t},"on-change":e.goTrafficPage}})],1)],1),t("div",{staticStyle:{margin:"20px","text-align":"center"}},[t("Button",{on:{click:e.cancelModal}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-arrow-back"}}),e._v(" Return\n\t\t\t\t\t\t")],1)])],1)])]),t("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":e.cancelModal},model:{value:e.exportModal,callback:function(t){e.exportModal=t},expression:"exportModal"}},[t("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[t("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[t("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[e._v("导出提示")]),t("FormItem",{attrs:{label:"你本次导出任务ID为:"}},[t("span",{staticStyle:{width:"100px"}},[e._v(e._s(e.taskId))])]),t("FormItem",{attrs:{label:"你本次导出的文件名为:"}},[t("span",[e._v(e._s(e.taskName))])]),t("span",{staticStyle:{"text-align":"left"}},[e._v("请前往下载管理-下载列表查看及下载。")])],1)],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.cancelModal}},[e._v("取消")]),t("Button",{attrs:{type:"primary"},on:{click:e.Goto}},[e._v("立即前往")])],1)]),t("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1)},o=[],n=a("2909"),i=a("b85c"),l=a("ade3"),c=(a("d9e2"),a("99af"),a("4de4"),a("d81d"),a("14d9"),a("4e82"),a("a434"),a("4ec9"),a("a9e3"),a("b64b"),a("d3b7"),a("ac1f"),a("00b4"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("3ca3"),a("5319"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("e3b7")),s=a("2d70"),u=a("6dfa"),d=a("6e08"),p=a("3177"),m=a("0fc3"),f=a("9819"),g=a("b818"),h=a("f79b"),b=a("f6a7"),y=a("90fe"),O={components:{},data:function(){var e,t=this,a=function(e,t,a){var r=/^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/;!t||r.test(t)?a():a(new Error(e.message))},r=function(e,t,a){var r=/^[0-9]\d*$/;r.test(t)?a():a(new Error(e.message))},o=function(e,t,a){t&&-1!=t.indexOf("@")?a():a(new Error("请输入有效的邮箱地址"))},n=function(e,a,r){var o=e.field.replace(/[^0-9]/gi,"");if(t.formObj.packageUsePercentage.length>1){for(var n in t.formObj.packageUsePercentage){var i=t.formObj.packageUsePercentage[n];if(i.value==a&&n!=o)return void r(new Error("套用用量提醒百分比唯一性,不能重复"))}r()}else r()},i=function(e,a,r){for(var o=JSON.parse(JSON.stringify(t.formObj.packageUsePercentage)),n=0;n<o.length;n++)""==o[n].value&&o.splice(n,1);0==o.length||a?r():r(new Error(e.message))},c=function(e,a,r){var o=new Date(t.formObj.contractBeginTime);new Date(a);!a||!o||a>o?r():r(new Error(e.message))},s=function(e,a,r){var o=new Date(t.formObj.a2zContractStartTime);new Date(a);!a||!o||a>o?r():r(new Error(e.message))};return{submitLoading:!1,dxPackageList:[],a2zPackageList:[],purchaseStatusList:[{label:"允许订购",value:"1"},{label:"不允许订购",value:"2"}],packageFlag:!1,typeFlag:"info",recordDetailId:"",corpId:"",index:1,byteCount:0,maxByteLength:220,searchObj:{},formObj:{resetPrice:"",deposit:"",internalOrder:"",activateNotification:"",overdueNotify:"",channelType:"",a2zChannelType:"",resourceChannelType:"",totalDeposit:0,corpName:"",channelStatus:1,appkey:"",appSecret:"",channelCode:"",activateNotificationUrl:"",overdueNotifyUrl:"",esimNotifySwitch:"",esimNotifyUrl:"",channelUrl:"",ebsCode:"",dxPackageInfosString:"",a2zPackageInfosString:"",unsubscribeRule:"",currencyCode:"",mail:"",depositNotify:"",threshold2:"",discount:"",limitType:"",indirectEarningsRatio:"",directEarningsRatio:"",createTime:"",accountNum:"",contractBeginTime:"",contractEndTime:"",contractSellAmount:"",a2zDepositAmount:"",accounts:"",address:"",companyName:"",allowNewPackage:"2",limitPacakageNum:"",groupId:"",upccTemplateIds:[],channelCooperationMode:"",runoutofBalanceRemindThreshold:"",resourceRunoutofBalanceRemindThreshold:"",prohibitiveBuyRemindThreshold:"",resourceProhibitiveBuyRemindThreshold:"",stopUseRemindThreshold:"",resourceStopUseRemindThreshold:"",a2zCardUseSwitch:"1",packageUsePercentage:[{value:"",index:1}],packageUseNotifyUrl:"",appids:[],salesMail:"",distributionAccountingPeriodId:"",a2zAccountingPeriodId:"",resourceAccountingPeriodId:"",a2zContractStartTime:"",a2zContractEndTime:"",a2zRuleId:"",resourceRuleId:"",approvalPackage:"",dxSmsTemplateVo:"",a2zSmsTemplateVo:"",consignmentImsiAmount:"",resourceImsiAmount:"",a2zImsiAmount:"",marketingAmount:"",creditAmount:"",a2zMarketingAmount:"",a2zOperators:[{a2zOperatorMcc:"",a2zOperatorChargeType:"",a2zOperator:[],operatorsList:[],index:0}]},deleteLoading:!1,addLoading:!1,changedRuleValidate:["limitType","createTime","accountNum"],changedChannelCreatValidate:["limitPacakageNum","groupId","upccTemplateIds"],ruleAddValidate:(e={corpName:[{required:!0,type:"string",message:"渠道商名称不能为空"},{pattern:/^[^\s]+(\s+[^\s]+)*$/,trigger:"blur",message:"渠道商名称有空格"},{validator:function(e,t,a){t&&t.includes("&")?a(new Error("渠道商名称不能包含&符号")):a()},trigger:"blur"},{}],channelStatus:[{required:!0,message:"渠道商状态不能为空",trigger:"change"}],companyName:[{required:!0,type:"string",message:"公司名称不能为空",trigger:"blur"},{validator:function(e,t,a){t&&t.includes("&")?a(new Error("公司名称不能包含&符号")):a()},trigger:"blur"},{}],address:[{required:!0,type:"string",message:"地址不能为空",trigger:"blur"},{max:200,message:"最长200位"}],ebsCode:[{required:!0,type:"string",message:"EBSCode不能为空"}],internalOrder:[{required:!0,type:"string",message:"内部订单不能为空",trigger:"change"}],channelType:[{required:!0,type:"string",trigger:"change",message:"渠道商类型不能为空"}],a2zChannelType:[{required:!0,type:"string",trigger:"change",message:"渠道商类型不能为空"}],resourceChannelType:[{required:!0,type:"string",trigger:"change",message:"渠道商类型不能为空"}],dxPackageInfosString:[{required:!0,type:"array",message:"可购买套餐组不能为空"}],a2zPackageInfosString:[{required:!0,type:"array",message:"可购买套餐组不能为空"}],unsubscribeRule:[{required:!0,type:"string",trigger:"change",message:"退订规则不能为空"}],activateNotification:[{required:!0,type:"string",trigger:"change",message:"是否激活通知不能为空"}],overdueNotify:[{required:!0,type:"string",trigger:"change",message:"到期通知不能为空"}],esimNotifySwitch:[{required:!0,type:"string",trigger:"change",message:"ESIM状态变更通知不能为空"}],esimNotifyUrl:[{required:!0,type:"string",trigger:"change",message:"ESIM状态变更通知url不能为空"}],currencyCode:[{required:!0,type:"string",message:"货币种类不能为空"}],mail:[{required:!0,message:"联系人邮箱不能为空"},{validator:o,trigger:"blur"},{required:!0,type:"email",trigger:"blur",message:"联系人邮箱格式错误"}],depositNotify:[{required:!0,message:"可用金额提醒阀值不能为空"},{pattern:/^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/,message:"押金提醒阀值最高支持8位整数和2位小数正数或零"}],threshold2:[{validator:a,message:"不允许购买阀值仅支持正数或零"}],activateNotificationUrl:[{required:!0,type:"string",message:"激活通知url不能为空"}],overdueNotifyUrl:[{required:!0,type:"string",message:"到期通知url不能为空"}],discount:[{pattern:/^([1-9]|[1-9]\d|100)$/,message:"折扣区间为1-100"},{validator:a,message:"购买折扣仅支持正数或零"}],indirectEarningsRatio:[{pattern:/^([1-9]|[1-9]\d|100)$/,message:"填入区间为1-100"},{validator:a,message:"间接比例仅支持正数或零"}],directEarningsRatio:[{pattern:/^([1-9]|[1-9]\d|100)$/,message:"填入区间为1-100"},{validator:a,message:"直接比例仅支持正数或零"}],accountNum:[{validator:r,message:"购买次数仅支持正整数或零"}],contractSellAmount:[{validator:a,message:"承诺金额最高支持8位整数和2位小数正数或零"}],a2zDepositAmount:[{validator:a,message:"承诺金额最高支持8位整数和2位小数正数或零"}],limitType:[{required:!0,message:"限制类型不能为空"}],createTime:[{required:!0,message:"购买时间不能为空"}]},Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(e,"accountNum",[{required:!0,message:"购买次数不能为空"}]),"contractBeginTime",[{required:!0,message:"代销合约开始时间不能为空"}]),"contractEndTime",[{required:!0,message:"代销合约结束时间不能为空"},{validator:c,message:"代销合约结束时间不能早于等于代销合约开始时间"}]),"allowNewPackage",[{required:!0,message:"是否允许自建不能为空"}]),"limitPacakageNum",[{required:!0,message:"自建上限不能为空"},{}]),"groupId",[{required:!0,type:"array",message:"国家卡池关联组不能为空"}]),"upccTemplateIds",[{required:!0,message:"可用速度模板不能为空"}]),"channelCooperationMode",[{required:!0,message:"渠道合作模式不能为空"}]),"runoutofBalanceRemindThreshold",[{validator:a,message:"最高支持8位整数和2位小数正数或零"}]),"resourceRunoutofBalanceRemindThreshold",[{validator:a,message:"最高支持8位整数和2位小数正数或零"}]),Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(e,"prohibitiveBuyRemindThreshold",[{validator:a,message:"最高支持8位整数和2位小数正数或零"}]),"resourceProhibitiveBuyRemindThreshold",[{validator:a,message:"最高支持8位整数和2位小数正数或零"}]),"stopUseRemindThreshold",[{validator:a,message:"最高支持8位整数和2位小数正数或零"}]),"resourceStopUseRemindThreshold",[{validator:a,message:"最高支持8位整数和2位小数正数或零"}]),"a2zPreDeposit",[{validator:a,message:"最高支持8位整数和2位小数正数或零"}]),"value",[{pattern:/^([0-9]{1,2}|100)$/,message:"请输入0~100区间内的正整数"},{validator:n,message:"套用用量提醒百分比不能重复"}]),"packageUseNotifyUrl",[{validator:i,message:"流量通知URL不能为空"}]),"salesMail",[{required:!0,message:"销售经理邮箱不能为空"}]),"acountRule",[{required:!0,type:"array",message:"出账规则不能为空"}]),"a2zRuleId",[{required:!0,type:"array",message:"流量计费规则不能为空"}]),Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(Object(l["a"])(e,"resourceRuleId",[{required:!0,type:"array",message:"流量计费规则不能为空"}]),"approvalPackage",[{required:!0,message:"自建套餐是否审批不能为空"}]),"dxSmsTemplateVo",[{required:!0,type:"array",message:"短信模板不能为空"}]),"a2zSmsTemplateVo",[{required:!0,type:"array",message:"短信模板不能为空"}]),"a2zContractStartTime",[{required:!0,message:"A2Z合约开始时间不能为空"}]),"a2zContractEndTime",[{required:!0,message:"A2Z合约开始时间不能为空"},{validator:s,message:"A2Z合约结束时间不能早于等于A2Z合约开始时间"}]),"creditAmount",[{validator:a,message:"信用账户最高支持8位整数和2位小数正数或零"}])),rechargeRecordFlag:!1,packageRecordFlage:!1,packageRecordDetailsFlage:!1,billFlowFlage:!1,trafficDetailsFlage:!1,exportModal:!1,rechargeTableData:[],rechargeTableLoading:!1,rechargeTotal:0,rechargePageSize:10,rechargePage:1,rechargeColumns:[{title:"充值时间",key:"chargeTime",align:"center",minWidth:150,tooltip:!0},{title:"充值金额",key:"amount",align:"center",minWidth:150,tooltip:!0,render:function(e,t){return e("span",t.row.amount)}},{title:"充值类型",key:"chargeType",align:"center",minWidth:180,tooltip:!0,render:function(e,t){var a=t.row,r="1"==a.chargeType?"缴付账单":"2"==a.chargeType?"增加押金":"3"==a.chargeType?"增加预存款":"4"==a.chargeType?"酬金返还":"5"==a.chargeType?"渠道商代销收入调账":"6"==a.chargeType?"A2Z押金充值":"7"==a.chargeType?"A2Z账单缴付":"8"==a.chargeType?"营销返利-代销":"9"==a.chargeType?"营销返利-A2Z":"10"==a.chargeType?"A~Z预存款充值":"11"==a.chargeType?"渠道商A2Z收入金额调账":"12"==a.chargeType?"渠道商资源合作收入金额调账":"13"==a.chargeType?"A2Z补计费":"14"==a.chargeType?"充值类型枚举值错误":"--";return e("span",r)}},{title:"币种",key:"currencyCode",align:"center",minWidth:150,tooltip:!0,render:function(e,t){var a=t.row,r="156"==a.currencyCode?"人民币":"840"==a.currencyCode?"美元":"344"==a.currencyCode?"港币":"--";return e("label",r)}}],searchMonth:"",packageListType:"1",packageTableData:[],packageTableLoading:!1,packageTableDetailsLoading:!1,searchloading:!1,searchTrafficLoading:!1,exportTrafficLoading:!1,billExportLoading:!1,billLoading:!1,trafficLoading:!1,packageTotal:0,packageDetailsTotal:0,packagePageSize:10,packageDetailsPageSize:10,packagePage:1,packageDetailsPage:1,packageColumns:[],packageDetailsTableData:[],packageDetailsColumns:[{title:"订单编号",key:"id",align:"center",minWidth:150,tooltip:!0},{title:"ICCID",key:"iccid",align:"center",minWidth:150,tooltip:!0},{title:"购买套餐",key:"nameEn",align:"center",minWidth:150,tooltip:!0},{title:"订单状态",key:"orderStatus",minWidth:150,align:"center",render:function(e,t){var a=t.row,r="";switch(a.orderStatus){case"1":r="待发货";break;case"2":r="已完成";break;case"3":r="已退订/已回滚";break;case"4":r="激活退订待审批";break;case"5":r="已回收";break;default:r="未知状态"}return e("label",r)}},{title:"金额",key:"amount",align:"center",minWidth:100,tooltip:!0,render:function(e,t){return e("span",t.row.amount)}},{title:"币种",key:"currencyCode",align:"center",minWidth:100,tooltip:!0,render:function(e,t){var a=t.row,r="156"==a.currencyCode?"人民币":"840"==a.currencyCode?"美元":"344"==a.currencyCode?"港币":"获取失败";return e("label",r)}},{title:"操作",slot:"action1",width:200,fixed:"right",align:"center"}],total:0,currentPage:1,totalTraffic:0,currentPageTraffic:1,form:{startTime:"",endTime:"",date:[]},trafficForm:{date:[],startTime:"",endTime:"",localId:""},columns12:[{title:"时间",key:"createTime",align:"center",minWidth:120,tooltip:!0},{title:"类型",key:"type",align:"center",minWidth:100,render:function(e,t){var a=t.row,r="1"==a.type?"缴付账单":"2"==a.type?"增加押金":"3"==a.type?"增加预存款":"4"==a.type?"酬金返还":"5"==a.type?"套餐订购":"6"==a.type?"加油包订购":"7"==a.type?"套餐退订":"8"==a.type?"加油包退订":"9"==a.type?"渠道商收入调账":"10"==a.type?"营销返利":"11"==a.type?"imsi费统计":"12"==a.type?"赔付":"";return e("label",r)}},{title:"币种",key:"currencyCode",align:"center",minWidth:100,tooltip:!0,render:function(e,t){var a=t.row,r="156"==a.currencyCode?"人民币":"840"==a.currencyCode?"美元":"344"==a.currencyCode?"港币":"获取失败";return e("label",r)}},{title:"交易金额",key:"amount",align:"center",minWidth:100,tooltip:!0},{title:"账户余额",key:"tdeposit",align:"center",minWidth:100,tooltip:!0}],columnsTraffic:[{title:"date",key:"date",align:"center",minWidth:100,tooltip:!0},{title:"Country/Region",key:"countryOrRegion",align:"center",minWidth:100,tooltip:!0},{title:"Usage (MB)",key:"usedTraffic",align:"center",minWidth:100,tooltip:!0},{title:"Amount",key:"amount",align:"center",minWidth:100,tooltip:!0}],billData:[],trafficData:[],taskId:"",taskName:"",localList:[],billRule:{date:[{type:"array",required:!0,message:"请选择时间段",trigger:"blur",fields:{0:{type:"date",required:!0,message:"请选择时间段"},1:{type:"date",required:!0,message:"请选择时间段"}}}]},trafficRule:{},groupIdList:[],TemplatesCreatList:[],newPackageNums:"",a2zStopStatus:"A2Z停用",a2zRecoverStatus:"A2Z恢复",a2zClick:!1,packageUseShow:!1,newArr:[],newArr1:[],newArr2:[],modeList:[],appidsList:[],appBindIds:[],appList:[],tempList:[],acountingList:[],a2zRuleList:[],imsiFeeList:[],salesMailList:[],operator:[],chargingRule:[{code:"1",chargingName:"按国家计费"},{code:"2",chargingName:"按运营商计费"}]}},created:function(){var e=this;this.$nextTick((function(){e.changedRuleValidate.forEach((function(t){e.$set(e.ruleAddValidate[t][0],"required",!1)}))})),this.$nextTick((function(){e.changedChannelCreatValidate.forEach((function(t){e.$set(e.ruleAddValidate[t][0],"required",!1)}))}))},methods:{examineModal:function(e,t){var a=this;this.$Modal.confirm({title:"2"==t?"确认执行通过操作？":"确认执行不通过操作？",onOk:function(){Object(s["f"])({id:e,status:t}).then((function(e){if(!e||"0000"!=e.code)throw e;a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.getPurchaseRecordsDetails(0)})).catch((function(e){}))}})},unsubscribeModal:function(e){var t=this;this.$Modal.confirm({title:"确认退订？",onOk:function(){Object(s["r"])(e).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),1===t.packageDetailsTableData.length?(t.packageDetailsTableData=[],t.packageRecordDetailsFlage=!1,t.getPurchaseRecords(0)):t.getPurchaseRecordsDetails(0,t.recordDetailId)})).catch((function(e){}))}})},handleCountryChange:function(e,t,a){e?this.getOperatorsByMcc(e,t,a):this.$set(this.formObj.a2zOperators[t],"operatorsList",[])},getOperatorsByMcc:function(e,t,a){var r=this;Object(y["c"])({mcc:e,pageSize:-1,pageNum:-1}).then((function(e){if("0000"===e.code){var o=e.data.records.map((function(e){return{id:e.operatorId,operatorName:e.operatorName}}));o.sort((function(e,t){return e.operatorName.localeCompare(t.operatorName)})),r.$set(r.formObj.a2zOperators[t],"operatorsList",o),1==a&&r.$set(r.formObj.a2zOperators[t],"a2zOperator",[])}})).catch((function(e){console.error("获取运营商列表失败:",e)}))},addRuleHandle:function(){this.formObj.a2zOperators.push({a2zOperatorMcc:"",a2zOperatorChargeType:"",a2zOperator:[],index:this.formObj.a2zOperators.length+1})},removeRule:function(e){this.formObj.a2zOperators.length>1&&0!==e&&this.formObj.a2zOperators.splice(e,1)},packageGroupsFormatData1:function(e){var t=this,a=[],r=[];return Object(c["v"])({list:1}).then((function(o){if("0000"===o.code){for(var n in e)t.newArr1.push({groupId:n,groupName:e[n].groupName,cooperationMode:e[n].cooperationMode});if(t.newArr1.length>0)for(var i=0;i<t.newArr1.length;i++){var l=t.newArr1[i].groupName,c=t.newArr1[i].groupId,s=t.newArr1[i].cooperationMode,u=!1;r=JSON.parse(JSON.stringify(o.data));for(var d=0;d<r.length;d++){r[d].groupName;var p=r[d].groupId;r[d].cooperationMode;if(c==p){u=!0;break}}u||r.push({groupId:c,groupName:l,cooperationMode:s}),o.data=r}t.dxPackageList=o.data;var m=function(r){if(Object.hasOwnProperty.call(e,r)){var o=e[r].groupName,n=e[r].cooperationMode;t.dxPackageList.filter((function(e){return e.groupId===r}));a.push(JSON.stringify({groupId:r,groupName:o,channelCooperationMode:n}))}};for(var f in e)m(f)}})),a},packageGroupsFormatData2:function(e){var t=this,a=[],r=[];return Object(c["v"])({list:2}).then((function(o){if("0000"===o.code){for(var n in e)t.newArr2.push({groupId:n,groupName:e[n].groupName,cooperationMode:e[n].cooperationMode});if(t.newArr2.length>0)for(var i=0;i<t.newArr2.length;i++){var l=t.newArr2[i].groupName,c=t.newArr2[i].groupId,s=t.newArr2[i].cooperationMode,u=!1;r=JSON.parse(JSON.stringify(o.data));for(var d=0;d<r.length;d++){r[d].groupName;var p=r[d].groupId;r[d].cooperationMode;if(c==p){u=!0;break}}u||r.push({groupId:c,groupName:l,cooperationMode:s}),o.data=r}t.dxPackageList=o.data;var m=function(r){if(Object.hasOwnProperty.call(e,r)){var o=e[r].groupName,n=e[r].cooperationMode;t.dxPackageList.filter((function(e){return e.groupId===r}));a.push(JSON.stringify({groupId:r,groupName:o,channelCooperationMode:n}))}};for(var f in e)m(f)}})),a},init:function(){var e=this;Object(c["s"])({corpId:this.corpId}).then((function(t){if("0000"===t.code){e.newPackageNums=t.data["newPackageNum"],e.$set(e.formObj,"accountNum",t.data["indirectCount"]),e.$set(e.formObj,"accounts",t.data["accounts"]),e.$set(e.formObj,"channelCooperationMode",t.data["channelCooperationMode"]),e.modeList=t.data["channelCooperationMode"],e.$set(e.formObj,"a2zPreDeposit",t.data["a2zPreDeposit"]),e.$set(e.formObj,"runoutofBalanceRemindThreshold",t.data["runoutofBalanceRemindThreshold"]),e.$set(e.formObj,"resourceRunoutofBalanceRemindThreshold",t.data["resourceRunoutofBalanceRemindThreshold"]),e.$set(e.formObj,"prohibitiveBuyRemindThreshold",t.data["prohibitiveBuyRemindThreshold"]),e.$set(e.formObj,"resourceProhibitiveBuyRemindThreshold",t.data["resourceProhibitiveBuyRemindThreshold"]),e.$set(e.formObj,"stopUseRemindThreshold",t.data["stopUseRemindThreshold"]),e.$set(e.formObj,"resourceStopUseRemindThreshold",t.data["resourceStopUseRemindThreshold"]),e.$set(e.formObj,"a2zCardUseSwitch",t.data["a2zCardUseSwitch"]),e.$set(e.formObj,"overdueNotify",t.data["overdueNotify"]),e.$set(e.formObj,"overdueNotifyUrl",t.data["overdueNotifyUrl"]),e.$set(e.formObj,"packageUseNotifyUrl",t.data["packageUseNotifyUrl"]),e.$set(e.formObj,"salesMail",t.data["salesMail"]);var a=t.data["smsTemplateVo"].filter((function(e){return"1"===e.cooperationMode})),r=t.data["smsTemplateVo"].filter((function(e){return"2"===e.cooperationMode}));e.$set(e.formObj,"dxSmsTemplateVo",a.map((function(e){return JSON.stringify(e)}))),e.$set(e.formObj,"a2zSmsTemplateVo",r.map((function(e){return JSON.stringify(e)}))),e.$set(e.formObj,"distributionAccountingPeriodId",t.data["distributionAccountingPeriodId"]),e.$set(e.formObj,"a2zAccountingPeriodId",t.data["a2zAccountingPeriodId"]),e.$set(e.formObj,"resourceAccountingPeriodId",t.data["resourceAccountingPeriodId"]);var o=t.data["freeImsiVo"].filter((function(e){return"1"===e.cooperationMode})),n=t.data["freeImsiVo"].filter((function(e){return"2"===e.cooperationMode})),i=t.data["freeImsiVo"].filter((function(e){return"3"===e.cooperationMode})),l=o.map((function(e){return e.ruleId})),c=n.map((function(e){return e.ruleId})),s=i.map((function(e){return e.ruleId}));e.$set(e.formObj,"consignmentImsiAmount",l),e.$set(e.formObj,"a2zImsiAmount",c),e.$set(e.formObj,"resourceImsiAmount",s),e.$set(e.formObj,"approvalPackage",t.data["approvalPackage"]),e.$set(e.formObj,"a2zContractStartTime",null===t.data["a2zContractStartTime"]?"":new Date(t.data["a2zContractStartTime"])),e.$set(e.formObj,"a2zContractEndTime",null===t.data["a2zContractEndTime"]?"":new Date(t.data["a2zContractEndTime"])),e.$set(e.formObj,"contractBeginTime",null===t.data["contractStartTime"]?"":new Date(t.data["contractStartTime"])),e.$set(e.formObj,"contractEndTime",null===t.data["contractEndTime"]?"":new Date(t.data["contractEndTime"])),e.$set(e.formObj,"a2zChannelType",t.data["a2zChannelType"]),e.$set(e.formObj,"resourceChannelType",t.data["resourceChannelType"]),e.$set(e.formObj,"a2zRuleId",t.data["a2zRuleId"]),e.$set(e.formObj,"resourceRuleId",t.data["resourceRuleId"]),e.$set(e.formObj,"a2zDepositAmount",t.data["a2zDepositAmount"]),e.$set(e.formObj,"groupId",t.data["groupId"]);var d={};if(d=t.data["authObj"]?t.data["authObj"]:t.data,e.$set(e.formObj,"unsubscribeRule",d["unsubscribeRule"]),e.$set(e.formObj,"corpName",d["corpName"]),e.$set(e.formObj,"channelStatus",d["isSub"]),e.$set(e.formObj,"appkey",d["appKey"]),e.$set(e.formObj,"appSecret",d["appSecret"]),e.$set(e.formObj,"channelCode",d["channelCode"]),e.$set(e.formObj,"activateNotificationUrl",d["activateNotificationUrl"]),e.$set(e.formObj,"channelUrl",d["channelUrl"]),e.$set(e.formObj,"ebsCode",d["ebsCode"]),e.$set(e.formObj,"activateNotification",d["activateNotification"]),e.$set(e.formObj,"address",d["address"]),e.$set(e.formObj,"companyName",d["companyName"]),e.$set(e.formObj,"currencyCode",d["currencyCode"]),e.$set(e.formObj,"mail",d["email"]),e.$set(e.formObj,"resetPrice",d["resetPrice"]),e.$set(e.formObj,"deposit",d["deposit"]),e.$set(e.formObj,"depositNotify",d["depositeRemindThreshold"]),e.$set(e.formObj,"discount",d["discount"]),e.$set(e.formObj,"internalOrder",d["internalOrder"]),e.$set(e.formObj,"channelType",d["channelType"]),e.$set(e.formObj,"totalDeposit",d["totalDeposit"]),e.$set(e.formObj,"limitType",d["indirectType"]?String(d["indirectType"]):""),e.$set(e.formObj,"esimNotifySwitch",d["esimNotification"]),e.$set(e.formObj,"esimNotifyUrl",d["esimNotificationUrl"]),e.$set(e.formObj,"marketingAmount",d["marketingAmount"]),e.$set(e.formObj,"creditAmount",d["creditAmount"]),e.$set(e.formObj,"a2zMarketingAmount",d["a2zMarketingAmount"]),e.appList=d["appids"],e.appBindIds=t.data["appBindIds"]?t.data["appBindIds"]:[],e.$set(e.formObj,"appids",d["appids"]),t.data["channelCooperationMode"].includes("2")&&Object(u["r"])({corpId:e.corpId}).then((function(t){if("0000"===t.code){var a=t.data;a=a&&a.length>0?a.map((function(t,a){return e.handleCountryChange(t.country.mcc,a),console.log(t.operators.map((function(e){return e.id}))),{a2zOperatorMcc:t.country.mcc,a2zOperatorChargeType:t.a2zOperatorChargeType,a2zOperator:t.operators.map((function(e){return e.id})),index:a}})):[{a2zOperatorMcc:"",a2zOperatorChargeType:"",a2zOperator:[],index:0}],e.$set(e.formObj,"a2zOperators",a)}})),d["indirectRatio"]&&e.changedRuleValidate.forEach((function(t){e.$set(e.ruleAddValidate[t][0],"required",!0)})),"1"==d["allowNewPackage"]?e.changedChannelCreatValidate.forEach((function(t){e.$set(e.ruleAddValidate[t][0],"required",!0)})):e.changedChannelCreatValidate.forEach((function(t){e.$set(e.ruleAddValidate[t][0],"required",!1)})),e.$set(e.formObj,"indirectEarningsRatio",d["indirectRatio"]),e.$set(e.formObj,"directEarningsRatio",d["directRatio"]),e.$set(e.formObj,"createTime",d["indirectCount"]),e.$set(e.formObj,"contractSellAmount",d["depositAmount"]),e.$set(e.formObj,"allowNewPackage",d["allowNewPackage"]),"info"===e.typeFlag?e.$set(e.formObj,"limitPacakageNum",t.data["newPackageNum"]+"/"+d["limitPackageNum"]):(e.$set(e.formObj,"limitPacakageNum",String(d["limitPackageNum"])),e.ruleAddValidate["limitPacakageNum"][1].pattern=/^[1-9]+[0-9]*$/,e.ruleAddValidate["limitPacakageNum"][1].message="只支持正整数"),e.$set(e.formObj,"upccTemplateIds",d["upccTemplateInfo"]),e.$set(e.formObj,"corpId",d["corpId"]),e.$set(e.formObj,"accounts",d["accounts"]),!e.a2zClick){var p=JSON.parse(JSON.stringify(d["packageGroups"])),m={},f={};for(var g in p)if(p.hasOwnProperty(g)){var h=p[g];"1"===h.cooperationMode?m[g]=h:"2"===h.cooperationMode&&(f[g]=h)}t.data["channelCooperationMode"].includes("1")&&e.$set(e.formObj,"dxPackageInfosString",e.packageGroupsFormatData1(m)),t.data["channelCooperationMode"].includes("2")&&e.$set(e.formObj,"a2zPackageInfosString",e.packageGroupsFormatData2(f))}0==d["packageUsePercentage"].length?e.formObj.packageUsePercentage=[{value:"",index:1}]:e.$set(e.formObj,"packageUsePercentage",d["packageUsePercentage"])}}))},unsubscribeAll:function(e){var t=this;this.$Modal.confirm({title:"确认全部退订？",onOk:function(){Object(s["s"])(e).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.getPurchaseRecords(0)})).catch((function(e){}))}})},examine:function(e,t){var a=this;this.$Modal.confirm({title:"2"==t?"确认执行通过操作？":"确认执行不通过操作？",onOk:function(){Object(s["g"])({id:e,status:t}).then((function(e){if(!e||"0000"!=e.code)throw e;a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.getPurchaseRecords(a.packagePage)})).catch((function(e){}))}})},limitTypeChange:function(e){"1"==e?(this.ruleAddValidate["accountNum"][0].required=!0,this.ruleAddValidate["createTime"][0].required=!1):(this.ruleAddValidate["accountNum"][0].required=!1,this.ruleAddValidate["createTime"][0].required=!0)},changeChannelCreat:function(){var e=this;1==this.formObj.allowNewPackage?(this.changedChannelCreatValidate.forEach((function(t){e.ruleAddValidate[t][0].required=!0})),this.formObj.channelCooperationMode.includes("2")&&this.getAppidsList()):(this.changedChannelCreatValidate.forEach((function(t){e.ruleAddValidate[t][0].required=!1})),this.formObj.limitPacakageNum="",this.formObj.groupId=[],this.formObj.upccTemplateIds=[]),this.formObj.channelCooperationMode.includes("2")&&("update"!=this.typeFlag?this.formObj.appids=[]:this.formObj.appids=this.appList)},changedIndirectEarningsRatio:function(){var e=this;this.formObj.indirectEarningsRatio?this.changedRuleValidate.forEach((function(t){e.ruleAddValidate[t][0].required=!0})):(this.changedRuleValidate.forEach((function(t){e.ruleAddValidate[t][0].required=!1})),this.$refs["formObj"].validate())},getDistributorsRecord:function(e){var t=this;0===e&&(this.rechargePage=1),this.rechargeTableLoading=!0,Object(c["u"])({corpId:this.corpId,pageNumber:this.rechargePage,pageSize:10}).then((function(e){"0000"===e.code&&(t.rechargeTableData=e.data.records,t.rechargeTotal=e.data.totalCount),t.rechargeTableLoading=!1,t.rechargeRecordFlag=!0}))},getpackageInfo:function(){this.formObj.channelCooperationMode.includes("1")&&this.getPackages1(),this.formObj.channelCooperationMode.includes("2")&&(this.getPackages2(),"add"==this.typeFlag&&(this.formObj.resourceAccountingPeriodId="")),"1"==this.formObj.allowNewPackage&&(this.formObj.channelCooperationMode.includes("2")?this.getAppidsList():"update"!=this.typeFlag?this.formObj.appids=[]:this.formObj.appids=this.appList)},getPackages1:function(){var e=this,t=[],a=[];return Object(c["v"])({list:1}).then((function(r){if("0000"===r.code)if("add"===e.typeFlag)e.dxPackageList=r.data.filter((function(e){return"1"===e.cooperationMode}));else{if(e.newArr1=e.newArr1.filter((function(e){return"1"===e.cooperationMode})),e.newArr1.length>0)for(var o=0;o<e.newArr1.length;o++){var n=e.newArr1[o].groupName,i=e.newArr1[o].groupId,l=e.newArr1[o].cooperationMode,c=!1;a=JSON.parse(JSON.stringify(r.data));for(var s=0;s<a.length;s++){a[s].groupName;var u=a[s].groupId;a[s].cooperationMode;if(i==u){c=!0;break}}c||a.push({groupId:i,groupName:n,cooperationMode:l}),r.data=a}e.dxPackageList=r.data;var d=function(a){if(Object.hasOwnProperty.call(e.newArr1,a)){var r=e.newArr1[a].groupName,o=e.newArr1[a].cooperationMode;e.dxPackageList.filter((function(e){return e.groupId===a}));t.push(JSON.stringify({groupId:a,groupName:r,channelCooperationMode:o}))}};for(var p in e.newArr1)d(p)}})),t},getPackages2:function(){var e=this,t=[],a=[];return Object(c["v"])({list:2}).then((function(r){if("0000"===r.code)if("add"===e.typeFlag)e.a2zPackageList=r.data.filter((function(e){return"2"===e.cooperationMode}));else{if(e.newArr2=e.newArr2.filter((function(e){return"2"===e.cooperationMode})),e.newArr2.length>0)for(var o=0;o<e.newArr2.length;o++){var n=e.newArr2[o].groupName,i=e.newArr2[o].groupId,l=e.newArr2[o].cooperationMode,c=!1;a=JSON.parse(JSON.stringify(r.data));for(var s=0;s<a.length;s++){a[s].groupName;var u=a[s].groupId;a[s].cooperationMode;if(i==u){c=!0;break}}c||a.push({groupId:i,groupName:n,cooperationMode:l}),r.data=a}e.a2zPackageList=r.data;var d=function(a){if(Object.hasOwnProperty.call(e.newArr2,a)){var r=e.newArr2[a].groupName,o=e.newArr2[a].cooperationMode;e.a2zPackageList.filter((function(e){return e.groupId===a}));t.push(JSON.stringify({groupId:a,groupName:r,channelCooperationMode:o}))}};for(var p in e.newArr2)d(p)}})),t},formatpackageInfosStringToJSON:function(e){var t=[];return e.forEach((function(e){t.push(JSON.parse(e))})),t},submit:function(){var e=this;"add"===this.typeFlag&&(this.ruleAddValidate["limitPacakageNum"][1].pattern=/^[1-9]+[0-9]*$/,this.ruleAddValidate["limitPacakageNum"][1].message="只支持正整数",this.ruleAddValidate["limitPacakageNum"][1].trigger="change"),1==this.formObj.allowNewPackage?this.changedChannelCreatValidate.forEach((function(t){e.ruleAddValidate[t][0].required=!0})):2==this.formObj.allowNewPackage?(this.changedChannelCreatValidate.forEach((function(t){e.ruleAddValidate[t][0].required=!1})),this.$refs["formObj"].validate(),this.formObj.limitPacakageNum="",this.formObj.groupId=[],this.formObj.upccTemplateIds=[]):(this.$refs["formObj"].validate(),this.formObj.limitPacakageNum="",this.formObj.groupId=[],this.formObj.upccTemplateIds=[]),this.$refs["formObj"].validate((function(t){if(t){var a,r,o=new Map,l=Object(i["a"])(e.formObj.a2zOperators);try{for(l.s();!(a=l.n()).done;){var s=a.value,u="".concat(s.a2zOperatorMcc,"-").concat(s.a2zOperatorChargeType);if(o.has(u)){var d,p=o.get(u),m=Object(i["a"])(s.a2zOperator);try{for(m.s();!(d=m.n()).done;){var f=d.value;if(p.has(f))return void e.$Notice.error({title:"操作提示",desc:"同一个国家和同一个计费方式下的运营商不能重复。"});p.add(f)}}catch(O){m.e(O)}finally{m.f()}}else o.set(u,new Set(s.a2zOperator))}}catch(O){l.e(O)}finally{l.f()}e.formObj.a2zOperators=e.formObj.a2zOperators.map((function(e,t){return{a2zOperatorMcc:e.a2zOperatorMcc||"",a2zOperatorChargeType:e.a2zOperatorChargeType||"",a2zOperator:e.a2zOperator||[],index:e.index||t}})).map((function(t){var a=t.a2zOperatorMcc,r=t.a2zOperatorChargeType,o=t.a2zOperator,n=""===a&&""===r&&0===o.length,i=""!==a&&""!==r&&o.length>0;if(n||i)return t;e.$Notice.error({title:"操作提示",desc:"请完整填入每行运营商配置信息"})})).filter((function(e){var t=e.a2zOperatorMcc,a=e.a2zOperatorChargeType,r=e.a2zOperator;return""!==t||""!==a||r.length>0})),r="add"===e.typeFlag?c["d"]:c["a"],e.submitLoading=!0,e.formObj.dxPackageInfosString&&0!=e.formObj.dxPackageInfosString.length?e.formObj.packageInfos=e.formatpackageInfosStringToJSON(e.formObj.dxPackageInfosString):(e.formObj.dxPackageInfosString=[],e.formObj.packageInfos=[]),e.formObj.a2zPackageInfosString&&0!=e.formObj.a2zPackageInfosString.length?e.formObj.packageInfos=e.formatpackageInfosStringToJSON(e.formObj.a2zPackageInfosString):(e.formObj.a2zPackageInfosString=[],e.formObj.packageInfos=[]);var g=[].concat(Object(n["a"])(e.formObj.a2zPackageInfosString),Object(n["a"])(e.formObj.dxPackageInfosString)),h=[].concat(Object(n["a"])(e.formObj.a2zPackageInfosString),Object(n["a"])(e.formObj.dxPackageInfosString)),b=[].concat(Object(n["a"])(e.formObj.dxSmsTemplateVo),Object(n["a"])(e.formObj.a2zSmsTemplateVo)),y=JSON.parse(JSON.stringify(e.formObj));y.contractSellAmount=e.$moneyCover(y.contractSellAmount,100),y.a2zDepositAmount=e.$moneyCover(y.a2zDepositAmount,100),y.resetPrice=e.$moneyCover(y.resetPrice,100),y.deposit=e.$moneyCover(y.deposit,100),y.totalDeposit=e.$moneyCover(y.totalDeposit,100),y.depositNotify=e.$moneyCover(y.depositNotify,100),y.runoutofBalanceRemindThreshold=e.$moneyCover(y.runoutofBalanceRemindThreshold,100),y.resourceRunoutofBalanceRemindThreshold=e.$moneyCover(y.resourceRunoutofBalanceRemindThreshold,100),y.prohibitiveBuyRemindThreshold=e.$moneyCover(y.prohibitiveBuyRemindThreshold,100),y.resourceProhibitiveBuyRemindThreshold=e.$moneyCover(y.resourceProhibitiveBuyRemindThreshold,100),y.stopUseRemindThreshold=e.formObj.channelCooperationMode.includes("2")&&y.stopUseRemindThreshold?e.$moneyCover(y.stopUseRemindThreshold,100):0,y.resourceStopUseRemindThreshold=e.$moneyCover(y.resourceStopUseRemindThreshold,100),y.smsTemplateVo=e.formatpackageInfosStringToJSON(b),y.packageInfosString=h,y.packageInfos=e.formatpackageInfosStringToJSON(g),y.distributionAccountingPeriodId=e.formObj.distributionAccountingPeriodId?e.formObj.distributionAccountingPeriodId:void 0,y.a2zAccountingPeriodId=e.formObj.a2zAccountingPeriodId?e.formObj.a2zAccountingPeriodId:void 0,y.resourceAccountingPeriodId=e.formObj.channelCooperationMode.includes("2")&&e.formObj.channelCooperationMode.includes("3")?e.formObj.a2zAccountingPeriodId:e.formObj.resourceAccountingPeriodId?e.formObj.resourceAccountingPeriodId:void 0,y.contractBeginTime=e.formObj.contractBeginTime?e.formObj.contractBeginTime:void 0,y.contractEndTime=e.formObj.contractEndTime?e.formObj.contractEndTime:void 0,y.a2zContractStartTime=e.formObj.a2zContractStartTime?e.formObj.a2zContractStartTime:void 0,y.a2zContractEndTime=e.formObj.a2zContractEndTime?e.formObj.a2zContractEndTime:void 0,y.consignmentImsiAmount=e.formObj.consignmentImsiAmount?e.formObj.consignmentImsiAmount:void 0,y.a2zImsiAmount=e.formObj.a2zImsiAmount?e.formObj.a2zImsiAmount:void 0,y.resourceImsiAmount=e.formObj.resourceImsiAmount?e.formObj.resourceImsiAmount:void 0,y.a2zPackageInfosString=e.formObj.a2zPackageInfosString?e.formObj.a2zPackageInfosString:void 0,y.dxPackageInfosString=e.formObj.dxPackageInfosString?e.formObj.dxPackageInfosString:void 0,y.dxSmsTemplateVo=e.formObj.dxSmsTemplateVo?e.formObj.dxSmsTemplateVo:void 0,y.a2zSmsTemplateVo=e.formObj.a2zSmsTemplateVo?e.formObj.a2zSmsTemplateVo:void 0,y.a2zRuleId=e.formObj.a2zRuleId?e.formObj.a2zRuleId:void 0,y.resourceRuleId=e.formObj.resourceRuleId?e.formObj.resourceRuleId:void 0,y.creditAmount=e.formObj.channelCooperationMode.includes("1")&&y.creditAmount?e.$moneyCover(y.creditAmount,100):0,2==y.allowNewPackage?(delete y.groupId,delete y.limitPacakageNum,delete y.upccTemplateIds):y.groupId=y.groupId,"add"!==e.typeFlag&&"update"!==e.typeFlag||(delete y.marketingAmount,delete y.a2zMarketingAmount),r(y).then((function(t){"0000"===t.code&&(e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.$router.push({name:"channelIndex"}))})).finally((function(){e.submitLoading=!1}))}}))},reset:function(e){"add"!=this.typeFlag&&(this.corpId=this.$route.query.id,"Add"===this.$route.query.type?(this.$refs[e].resetFields(),this.typeFlag="add",this.packageFlag=!1):"Update"===this.$route.query.type?(this.init(),this.$set(this.formObj,"dxPackageInfosString",this.packageGroupsFormatData1(null)),this.$set(this.formObj,"a2zPackageInfosString",this.packageGroupsFormatData2(null)),this.typeFlag="update",this.packageFlag=!1):this.init())},back:function(){this.$router.push({path:"/channelInfo"})},getRechargeRecord:function(){this.getDistributorsRecord(0)},exportRechargeRecord:function(){this.$Notice.success({title:"操作提示",desc:"操作成功"})},loadPackageRecordColumns:function(){var e=this.$i18n.locale,t={"zh-CN":"packageName","en-US":"nameEn"},a=[{title:"订单详情",width:100,slot:"orderInfo",align:"center"},{title:"订单日期",key:"orderDate",align:"center",minWidth:150,tooltip:!0},{title:"订单ID",key:"orderUniqueId",align:"center",minWidth:150,tooltip:!0},{title:"ICCID",key:"iccid",align:"center",minWidth:150,tooltip:!0},{title:"购买套餐",key:t[e],align:"center",minWidth:150,tooltip:!0},{title:"购买渠道",key:"orderChannel",align:"center",minWidth:150,tooltip:!0},{title:"订单状态",key:"orderStatus",align:"center",minWidth:150,tooltip:!0,render:function(e,t){var a=t.row,r="";switch(a.orderStatus){case"1":r="待发货";break;case"2":r="已完成";break;case"3":r="已退订";break;case"4":r="激活退订待审批";break;case"5":r="部分退订";break;case"6":r="部分发货";break;case"7":r="已回收";break;case"8":r="部分回收";break;case"9":r="复合状态";break;default:r="未知状态"}return e("label",r)}},{title:"购买份数",key:"count",align:"center",minWidth:150,tooltip:!0},{title:"金额",key:"amount",align:"center",minWidth:100,tooltip:!0,render:function(e,t){return e("span",t.row.amount)}},{title:"币种",key:"currencyCode",align:"center",minWidth:100,tooltip:!0,render:function(e,t){var a=t.row,r="156"==a.currencyCode?"人民币":"840"==a.currencyCode?"美元":"344"==a.currencyCode?"港币":"--";return e("label",r)}},{title:"操作",slot:"action",width:200,fixed:"right",align:"center"}];this.packageColumns=a},getPackageRecord:function(){this.loadPackageRecordColumns(),this.getPurchaseRecords(0)},getPurchaseRecords:function(e){var t=this;this.packagePage=e,0===e&&(this.packagePage=1),this.packageTableLoading=!0,Object(s["k"])({orderUserId:this.corpId,pageNumber:e,pageSize:10,startTime:this.searchMonth}).then((function(e){"0000"===e.code&&(t.packageTableData=e.data.records,t.packageTotal=e.data.totalCount),t.packageTableLoading=!1,t.packageRecordFlage=!0}))},getPurchaseRecordsDetails:function(e,t){var a=this;0===e&&(this.packageDetailsPage=1),this.packageTableDetailsLoading=!0,Object(s["h"])({orderId:t||this.recordDetailId,pageNumber:e,pageSize:10}).then((function(e){"0000"===e.code&&(a.packageDetailsTableData=e.data.records,a.packageDetailsTotal=e.data.totalCount),a.packageTableDetailsLoading=!1,a.packageRecordDetailsFlage=!0}))},showOrderInfo:function(e){this.packageRecordDetailsFlage=!0,this.recordDetailId=e,this.getPurchaseRecordsDetails(0,e)},selectTime:function(e){this.searchMonth=e},typeSelect:function(e){this.packageFlag=!1,"1"===e||"2"===e||(this.packageFlag=!0,this.formObj.dxPackageInfosString=[],this.formObj.a2zPackageInfosString=[])},packagePurchaseCancel:function(){this.searchMonth=""},fileDownload:function(e,t,a){var r=a,o=e+"."+t;if("download"in document.createElement("a")){var n=document.createElement("a"),i=URL.createObjectURL(r);n.download=o,n.href=i,n.click(),URL.revokeObjectURL(i)}else navigator.msSaveBlob(r,o);this.$Notice.success({title:"操作提示",desc:"操作成功"})},exportPackageRecord:function(){var e=this;Object(s["e"])({startTime:this.searchMonth,orderUserId:this.corpId,pageNumber:-1,pageSize:-1}).then((function(t){t&&e.fileDownload("套餐记录","xlsx",t.data)}))},exportRemunerateDetail:function(){var e=this;Object(c["p"])({month:this.searchMonth,corpId:this.corpId}).then((function(t){t&&e.fileDownload("酬金详情","xlsx",t.data)}))},exportDistributorsRecord:function(){var e=this;Object(c["n"])({corpId:this.corpId}).then((function(t){e.fileDownload("充值记录","csv",t.data)}))},getBillFlow:function(){this.billFlowFlage=!0},handleDateChange:function(e){Array.isArray(e)&&(this.form.startTime=e[0],this.form.endTime=e[1])},hanldeDateClear:function(){this.form.startTime="",this.form.endTime=""},goPageFirst:function(e){var t=this;this.billLoading=!0,this.searchloading=!0;var a=this;Object(c["i"])({startTime:""===this.form.startTime?null:this.form.startTime,endTime:""===this.form.endTime?null:this.form.endTime,corpId:this.corpId,pageNum:e,pageSize:10}).then((function(r){"0000"==r.code&&(a.loading=!1,t.page=e,t.currentPage=e,t.total=r.data.totalCount,t.billData=r.data.records,t.searchloading=!1,t.billLoading=!1)})).catch((function(e){console.error(e),t.billLoading=!1,t.searchloading=!1})).finally((function(){}))},searchBill:function(){var e=this;this.$refs["form"].validate((function(t){t&&e.goPageFirst(1)}))},goPage:function(e){this.goPageFirst(e)},exportBillFlow:function(){var e=this;this.$refs["form"].validate((function(t){t&&(e.billExportLoading=!0,Object(c["h"])({startTime:""===e.form.startTime?null:e.form.startTime,endTime:""===e.form.endTime?null:e.form.endTime,corpId:e.corpId,userId:e.$store.state.user.userId,pageNum:-1,pageSize:-1}).then((function(t){e.exportModal=!0,e.taskId=t.data.taskId,e.taskName=t.data.taskName,e.billExportLoading=!1,e.billData=[],e.form.date=[],e.total=0})).catch((function(t){console.error(t),e.billExportLoading=!1})).finally((function(){})))}))},getTrafficDetails:function(){this.trafficDetailsFlage=!0,this.goTrafficPageFirst(1)},handleDateChange1:function(e){Array.isArray(e)&&(this.trafficForm.startTime=e[0],this.trafficForm.endTime=e[1])},hanldeDateClear1:function(){this.trafficForm.startTime="",this.trafficForm.endTime=""},goTrafficPage:function(e){this.goTrafficPageFirst(e)},searchTraffic:function(){this.searchTrafficLoading=!0,this.goTrafficPageFirst(1)},exportTraffic:function(){var e=this;this.exportTrafficLoading=!0,Object(c["q"])({beginDate:""===this.trafficForm.startTime?null:this.trafficForm.startTime,endDate:""===this.trafficForm.endTime?null:this.trafficForm.endTime,country:""===this.trafficForm.localId?null:this.trafficForm.localId,corpId:this.corpId}).then((function(t){e.exportModal=!0,e.taskId=t.data.taskId,e.taskName=t.data.taskName,e.exportTrafficLoading=!1,e.trafficForm.startTime="",e.trafficForm.endTime="",e.trafficForm.localId=""})).catch((function(t){console.error(t),e.exportTrafficLoading=!1})).finally((function(){}))},goTrafficPageFirst:function(e){var t=this;this.trafficLoading=!0;var a=this;Object(c["y"])({beginDate:""===this.trafficForm.startTime?null:this.trafficForm.startTime,endDate:""===this.trafficForm.endTime?null:this.trafficForm.endTime,country:""===this.trafficForm.localId?null:this.trafficForm.localId,corpId:this.corpId,pageNum:e,pageSize:10}).then((function(r){"0000"==r.code&&(a.loading=!1,t.page=e,t.currentPageTraffic=e,t.totalTraffic=Number(r.count),t.trafficData=r.data,t.searchTrafficLoading=!1,t.trafficLoading=!1)})).catch((function(e){console.error(e),t.trafficLoading=!1,t.searchTrafficLoading=!1})).finally((function(){}))},cancelModal:function(){this.exportModal=!1,this.billFlowFlage=!1,this.trafficDetailsFlage=!1,this.$refs.form.resetFields(),this.trafficForm.date="",this.form.date="",this.billData=[],this.total=0,this.trafficForm.startTime="",this.trafficForm.endTime="",this.trafficForm.localId=""},Goto:function(){this.trafficDetailsFlage=!1,this.exportModal=!1,this.billFlowFlage=!1,this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}})},getLocalList:function(){var e=this;Object(u["A"])().then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.localList=a,e.localList.sort((function(e,t){return e.countryEn.localeCompare(t.countryEn)}))})).catch((function(e){})).finally((function(){}))},nationalCardPoolGroup:function(){var e=this;Object(p["c"])({num:-1,size:-1}).then((function(t){"0000"==t.code&&(e.groupIdList=t.data.records)})).catch((function(e){console.error(e)}))},getTemplate:function(){var e=this;Object(d["c"])({pageNum:-1,pageSize:-1}).then((function(t){"0000"==t.code&&(e.TemplatesCreatList=t.data.records)})).catch((function(e){console.error(e)}))},handleAdd:function(){this.index++,this.formObj.packageUsePercentage.push({value:"",index:this.index})},handleRemove:function(e){this.formObj.packageUsePercentage.splice(e,1),this.index--},a2zStop:function(){var e=this;Object(c["c"])({corpId:this.formObj.corpId}).then((function(t){if("0000"!=t.code)throw t;e.formObj.a2zCardUseSwitch="2",t.status,e.a2zStopStatus,e.a2zClick=!0,e.init()})).catch((function(e){})).finally((function(){}))},a2zRecover:function(){var e=this;Object(c["b"])({corpId:this.formObj.corpId}).then((function(t){if("0000"!=t.code)throw t;e.formObj.a2zCardUseSwitch="1",t.status,e.a2zRecoverStatus,e.a2zClick=!0,e.init()})).catch((function(e){})).finally((function(){}))},getAppidsList:function(e){var t=this;Object(m["b"])().then((function(e){"0000"==e.code&&(t.appidsList=e.data)})).catch((function(e){console.error(e)})).finally((function(){}))},gettemplateAll:function(){var e=this;Object(f["o"])().then((function(t){if("0000"===t.code){var a=t.data;e.tempList=a,e.tempList.sort((function(e,t){return e.templateName.localeCompare(t.templateName)}))}})).catch((function(e){console.log(e)}))},getAcountingPeriod:function(){var e=this;Object(g["c"])({current:-1,size:-1}).then((function(t){"0000"==t.code&&(e.acountingList=t.data)})).catch((function(e){console.error(e)})).finally((function(){}))},queryA2ZBillPrice:function(){var e=this;Object(h["f"])({size:-1,current:-1}).then((function(t){"0000"==t.code&&(e.a2zRuleList=t.data)})).catch((function(e){console.error(e)})).finally((function(){}))},getImsiFeeList:function(){var e=this;Object(b["d"])({size:-1,current:-1}).then((function(t){"0000"==t.code&&(e.imsiFeeList=t.data)})).catch((function(e){console.error(e)})).finally((function(){}))},getSalesMailList:function(){var e=this;Object(u["v"])().then((function(t){"0000"==t.code&&(e.salesMailList=t.data)})).catch((function(e){console.error(e)})).finally((function(){}))},calculateByteLength:function(e){for(var t=0,a=0;a<e.length;a++){var r=e.charAt(a);/[\u4e00-\u9fa5\u3000-\u303f\uff00-\uffef]/.test(r)?t+=3:t+=1}return t},handleInput:function(e){this.byteCount=this.calculateByteLength(e)},validateByteLength:function(e,t,a){var r=this.calculateByteLength(t);r>this.maxByteLength?a(new Error("已超过 220 字节！")):a()},toMarketAccout:function(e){this.$router.push({name:"adminMarketingAccount",query:{corpId:this.corpId,type:e,searchObj:encodeURIComponent(JSON.stringify(this.searchObj))}})}},mounted:function(){this.getLocalList(),this.getTemplate(),this.nationalCardPoolGroup(),this.gettemplateAll(),this.getAcountingPeriod(),this.queryA2ZBillPrice(),this.getImsiFeeList(),this.getSalesMailList(),"add"!=this.typeFlag&&(this.corpId=this.$route.query.id,"Add"===this.$route.query.type?(this.typeFlag="add",this.packageFlag=!1):"Update"===this.$route.query.type?(this.init(),this.typeFlag="update",this.packageFlag=!1):(this.init(),localStorage.setItem("searchObj",decodeURIComponent(this.$route.query.searchObj)),this.searchObj=null===JSON.parse(localStorage.getItem("searchObj"))?"":JSON.parse(localStorage.getItem("searchObj"))))}},v=O,k=(a("8e6c"),a("4fd7"),a("2877")),j=Object(k["a"])(v,r,o,!1,null,"737e583a",null);t["default"]=j.exports},"0fc3":function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"d",(function(){return i})),a.d(t,"e",(function(){return l})),a.d(t,"a",(function(){return c})),a.d(t,"b",(function(){return s}));var r=a("66df"),o="pms/api/v1/directional",n=function(e){return r["a"].request({url:o+"/getDirectionalApp",params:e,method:"get"})},i=function(e){return r["a"].request({url:o+"/newDirectionalApp",data:e,method:"POST"})},l=function(e){return r["a"].request({url:o+"/updateDirectionalApp",data:e,method:"PUT"})},c=function(e){return r["a"].request({url:o+"/delDirectionalApp",params:e,method:"delete"})},s=function(e){return r["a"].request({url:o+"/getAppInfo",data:e,method:"POST"})}},"2d70":function(e,t,a){"use strict";a.d(t,"j",(function(){return n})),a.d(t,"k",(function(){return i})),a.d(t,"d",(function(){return l})),a.d(t,"p",(function(){return c})),a.d(t,"q",(function(){return s})),a.d(t,"m",(function(){return u})),a.d(t,"c",(function(){return d})),a.d(t,"s",(function(){return p})),a.d(t,"t",(function(){return m})),a.d(t,"g",(function(){return f})),a.d(t,"e",(function(){return g})),a.d(t,"n",(function(){return h})),a.d(t,"a",(function(){return b})),a.d(t,"h",(function(){return y})),a.d(t,"o",(function(){return O})),a.d(t,"l",(function(){return v})),a.d(t,"b",(function(){return k})),a.d(t,"r",(function(){return j})),a.d(t,"f",(function(){return S})),a.d(t,"i",(function(){return I}));var r=a("66df"),o="/cms/api/v1/personalOrder",n=function(e){return r["a"].request({url:o+"/pages",params:e,method:"get"})},i=function(e){return r["a"].request({url:o+"/getPackagePurchaseRecord",params:e,method:"get"})},l=function(e){return r["a"].request({url:o+"/deliver/batch",data:e,method:"post",contentType:"multipart/form-data"})},c=function(e){return r["a"].request({url:o+"/unbind/".concat(e),method:"POST"})},s=function(e){return r["a"].request({url:o+"/big/unbind/".concat(e),method:"POST"})},u=function(e){return r["a"].request({url:o+"/recover/".concat(e),method:"POST"})},d=function(e,t){return r["a"].request({url:o+"/deliver/".concat(t),data:e,method:"post"})},p=function(e){return r["a"].request({url:o+"/unsubscribe/".concat(e),method:"POST"})},m=function(e){return r["a"].request({url:o+"/unsubscribeForBigOrder",data:e,method:"POST"})},f=function(e){return r["a"].request({url:o+"/audit",data:e,method:"post"})},g=function(e){return r["a"].request({url:o+"/pages/export",params:e,method:"GET",responseType:"blob"})},h=function(e){return r["a"].request({url:"cms/order/compensation",data:e,method:"POST"})},b=function(e){return r["a"].request({url:o+"/bigOrder/deliver",data:e,method:"POST",contentType:"multipart/form-data"})},y=function(e){return r["a"].request({url:o+"/orderDetailsByOrderId",params:e,method:"get"})},O=function(e){return r["a"].request({url:o+"/unbind/child/".concat(e),method:"POST"})},v=function(e){return r["a"].request({url:o+"/recover/child/".concat(e),method:"POST"})},k=function(e,t){return r["a"].request({url:o+"/deliver/child/".concat(t),data:e,method:"post"})},j=function(e){return r["a"].request({url:o+"/detail/unsubscribe/".concat(e),method:"POST"})},S=function(e){return r["a"].request({url:o+"/detail/audit",data:e,method:"post"})},I=function(e){return r["a"].request({url:o+"/detail/orderDetail",params:e,method:"get"})}},3177:function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"d",(function(){return i})),a.d(t,"a",(function(){return l})),a.d(t,"f",(function(){return c})),a.d(t,"e",(function(){return s})),a.d(t,"b",(function(){return u}));var r=a("66df"),o="/pms/api/v1/cardPoolMccGroup",n=function(e){return r["a"].request({url:o+"/getCardPoolGroup",data:e,method:"POST"})},i=function(e){return r["a"].request({url:o+"/getCardPoolGroupDetailNew",data:e,method:"POST"})},l=function(e){return r["a"].request({url:o+"/add",data:e,method:"POST"})},c=function(e){return r["a"].request({url:o+"/update",data:e,method:"POST"})},s=function(e){return r["a"].request({url:o+"/getCardPoolMcc",params:e,method:"get"})},u=function(e){return r["a"].request({url:o+"/batchDelete",data:e,method:"delete"})}},"3f7e":function(e,t,a){"use strict";var r=a("b5db"),o=r.match(/firefox\/(\d+)/i);e.exports=!!o&&+o[1]},"4e82":function(e,t,a){"use strict";var r=a("23e7"),o=a("e330"),n=a("59ed"),i=a("7b0b"),l=a("07fa"),c=a("083a"),s=a("577e"),u=a("d039"),d=a("addb"),p=a("a640"),m=a("3f7e"),f=a("99f4"),g=a("1212"),h=a("ea83"),b=[],y=o(b.sort),O=o(b.push),v=u((function(){b.sort(void 0)})),k=u((function(){b.sort(null)})),j=p("sort"),S=!u((function(){if(g)return g<70;if(!(m&&m>3)){if(f)return!0;if(h)return h<603;var e,t,a,r,o="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(r=0;r<47;r++)b.push({k:t+r,v:a})}for(b.sort((function(e,t){return t.v-e.v})),r=0;r<b.length;r++)t=b[r].k.charAt(0),o.charAt(o.length-1)!==t&&(o+=t);return"DGBEFHACIJK"!==o}})),I=v||!k||!j||!S,C=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:s(t)>s(a)?1:-1}};r({target:"Array",proto:!0,forced:I},{sort:function(e){void 0!==e&&n(e);var t=i(this);if(S)return void 0===e?y(t):y(t,e);var a,r,o=[],s=l(t);for(r=0;r<s;r++)r in t&&O(o,t[r]);d(o,C(e)),a=l(o),r=0;while(r<a)t[r]=o[r++];while(r<s)c(t,r++);return t}})},"4ec9":function(e,t,a){"use strict";a("6f48")},"4fd7":function(e,t,a){"use strict";a("b810")},"6e08":function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"b",(function(){return i})),a.d(t,"a",(function(){return l})),a.d(t,"d",(function(){return c}));var r=a("66df"),o="/pms/api/v1/upccTemplate",n=function(e){return r["a"].request({url:o+"/getUpccTemplate",data:e,method:"post"})},i=function(e){return r["a"].request({url:o+"/delUpccTemplate",data:e,method:"delete"})},l=function(e){return r["a"].request({url:o+"/newUpccTemplate",data:e,method:"post"})},c=function(e){return r["a"].request({url:o+"/updateUpccTemplate",data:e,method:"PUT"})}},"6f48":function(e,t,a){"use strict";var r=a("6d61"),o=a("6566");r("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),o)},"8e6c":function(e,t,a){"use strict";a("90ae")},"90ae":function(e,t,a){},"90fe":function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"f",(function(){return i})),a.d(t,"a",(function(){return l})),a.d(t,"g",(function(){return c})),a.d(t,"b",(function(){return s})),a.d(t,"d",(function(){return u})),a.d(t,"c",(function(){return d}));var r=a("66df"),o="/oms/api/v1",n=function(e){return r["a"].request({url:o+"/country/queryCounrty",params:e,method:"get"})},i=function(){return r["a"].request({url:o+"/country/queryCounrtyList",method:"get"})},l=function(e){return r["a"].request({url:o+"/country/addCounrty",data:e,method:"post",contentType:"multipart/form-data"})},c=function(e){return r["a"].request({url:o+"/country/updateCounrty",data:e,method:"post",contentType:"multipart/form-data"})},s=function(e){return r["a"].request({url:o+"/country/deleteCounrty",params:e,method:"delete"})},u=function(e){return r["a"].request({url:o+"/country/getOperators",params:e,method:"get"})},d=function(e){return r["a"].request({url:o+"/operator/a2zChannelOperator",params:e,method:"get"})}},9819:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"e",(function(){return i})),a.d(t,"f",(function(){return l})),a.d(t,"p",(function(){return c})),a.d(t,"h",(function(){return s})),a.d(t,"g",(function(){return u})),a.d(t,"m",(function(){return d})),a.d(t,"o",(function(){return p})),a.d(t,"l",(function(){return m})),a.d(t,"n",(function(){return f})),a.d(t,"a",(function(){return g})),a.d(t,"c",(function(){return h})),a.d(t,"d",(function(){return b})),a.d(t,"j",(function(){return y})),a.d(t,"q",(function(){return O})),a.d(t,"k",(function(){return v})),a.d(t,"i",(function(){return k}));var r=a("66df"),o="/pms/api/v1",n=function(e){return r["a"].request({url:o+"/card/pageList",data:e,method:"post"})},i=function(e){return r["a"].request({url:o+"/card/pageListAndBak",data:e,method:"post"})},l=function(e){return r["a"].request({url:o+"/card/import",data:e,method:"post"})},c=function(e){return r["a"].request({url:o+"/card/upload",data:e,method:"post",contentType:"multipart/form-data"})},s=function(e,t){return r["a"].request({url:o+"/card/".concat(e),method:"get"})},u=function(e){return r["a"].request({url:o+"/card/export",data:e,method:"post"})},d=function(e){return r["a"].request({url:"/pms/ota/ota",params:e,method:"get"})},p=function(e){return r["a"].request({url:"sms/notice/list",params:e,method:"post"})},m=function(e){return r["a"].request({url:"/cms/channel/package",params:e,method:"get"})},f=function(e){return r["a"].request({url:"/pms/pms-realname/getGroups",data:e,method:"get"})},g=function(e){return r["a"].request({url:o+"/card/batchUpdate",data:e,method:"post",contentType:"multipart/form-data"})},h=function(e){return r["a"].request({url:o+"/card/update",data:e,method:"put"})},b=function(e){return r["a"].request({url:o+"/card/extra/".concat(e),method:"get"})},y=function(e){return r["a"].request({url:o+"/card/getExpireTime",data:e,method:"get"})},O=function(e){return r["a"].request({url:o+"/card/updateExpireTimeBatch",data:e,method:"post"})},v=function(e){return r["a"].request({url:o+"/card/import/pageList",data:e,method:"post"})},k=function(e){return r["a"].request({url:o+"/card/download",params:e,method:"get",responseType:"blob"})}},"99f4":function(e,t,a){"use strict";var r=a("b5db");e.exports=/MSIE|Trident/.test(r)},b810:function(e,t,a){},b818:function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return i})),a.d(t,"b",(function(){return l}));var r=a("66df"),o="/charging/accountingPeriod",n=function(e){return r["a"].request({url:o+"/query",data:e,method:"post"})},i=function(e){return r["a"].request({url:o+"/add",data:e,method:"post"})},l=function(e){return r["a"].request({url:o+"/del",params:e,method:"post"})}},e3b7:function(e,t,a){"use strict";a.d(t,"t",(function(){return i})),a.d(t,"s",(function(){return l})),a.d(t,"k",(function(){return c})),a.d(t,"u",(function(){return s})),a.d(t,"n",(function(){return u})),a.d(t,"p",(function(){return d})),a.d(t,"d",(function(){return p})),a.d(t,"a",(function(){return m})),a.d(t,"f",(function(){return f})),a.d(t,"x",(function(){return g})),a.d(t,"w",(function(){return h})),a.d(t,"v",(function(){return b})),a.d(t,"r",(function(){return y})),a.d(t,"A",(function(){return O})),a.d(t,"l",(function(){return v})),a.d(t,"m",(function(){return k})),a.d(t,"e",(function(){return j})),a.d(t,"z",(function(){return S})),a.d(t,"g",(function(){return I})),a.d(t,"j",(function(){return C})),a.d(t,"o",(function(){return T})),a.d(t,"i",(function(){return F})),a.d(t,"h",(function(){return w})),a.d(t,"y",(function(){return x})),a.d(t,"q",(function(){return P})),a.d(t,"c",(function(){return N})),a.d(t,"b",(function(){return A}));var r=a("66df"),o="pms",n="cms",i=function(e){return r["a"].request({url:n+"/channel/distributors/detail",data:e,method:"post"})},l=function(e){return r["a"].request({url:n+"/channel/distributors/info",params:e,method:"get"})},c=function(e){return r["a"].request({url:n+"/channel/distributors/delete",data:e,method:"delete"})},s=function(e){return r["a"].request({url:n+"/channel/distributors/record",data:e,method:"post"})},u=function(e){return r["a"].request({url:n+"/channel/distributors/record/export/"+e.corpId,method:"get",responseType:"blob"})},d=function(e){return r["a"].request({url:n+"/channel/distributors/remunerate/export",params:e,method:"post",responseType:"blob"})},p=function(e){return r["a"].request({url:n+"/channel/newChannel",data:e,method:"post"})},m=function(e){return r["a"].request({url:n+"/channel/updateChannel",data:e,method:"put"})},f=function(e){return r["a"].request({url:n+"/channel/approvalChannel",params:e,method:"put"})},g=function(e){return r["a"].request({url:o+"/packageGroup/queryPackageGroupRelation",params:e,method:"get"})},h=function(e){return r["a"].request({url:o+"/packageGroup/queryPackageGroupDetail",params:e,method:"get"})},b=function(e){return r["a"].request({url:o+"/packageGroup/purchasePart",params:e,method:"get"})},y=function(e){return r["a"].request({url:o+"/packageGroup/queryPackageList",params:e,method:"get"})},O=function(e){return r["a"].request({url:o+"/update",params:e,method:"put"})},v=function(e){return r["a"].request({url:o+"/packageGroup/deleteBatchPackageGroup",data:e,method:"delete"})},k=function(e){return r["a"].request({url:o+"/packageGroup/deletePackageGroup",params:e,method:"delete"})},j=function(e){return r["a"].request({url:o+"/packageGroup/newPackageGroup",data:e,method:"post"})},S=function(e){return r["a"].request({url:o+"/packageGroup/updatePackageGroup",data:e,method:"put"})},I=function(e){return r["a"].request({url:o+"/packageGroup/approvalPackageGroup",params:e,method:"put"})},C=function(e){return r["a"].request({url:o+"/packageGroup/create/byFile",data:e,method:"post",contentType:"multipart/form-data"})},T=function(e){return r["a"].request({url:o+"/packageGroup/packageGroupDetailExport",params:e,method:"get",responseType:"blob"})},F=function(e){return r["a"].request({url:n+"/channel/distributors/channelBill",data:e,method:"post"})},w=function(e){return r["a"].request({url:n+"/channel/distributors/channelBill/export",data:e,method:"post"})},x=function(e){return r["a"].request({url:n+"/channel/getCorpFlowDetail",params:e,method:"get"})},P=function(e){return r["a"].request({url:n+"/channel/corpFlowDetailExport",params:e,method:"get"})},N=function(e){return r["a"].request({url:n+"/channel/distributors/card/suspend",params:e,method:"get"})},A=function(e){return r["a"].request({url:n+"/channel/distributors/card/recover",params:e,method:"get"})}},ea83:function(e,t,a){"use strict";var r=a("b5db"),o=r.match(/AppleWebKit\/(\d+)\./);e.exports=!!o&&+o[1]},f6a7:function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return l})),a.d(t,"e",(function(){return c})),a.d(t,"b",(function(){return s}));var r=a("66df"),o="pms/imsiAmount",n=function(e){return r["a"].request({url:o+"/getImsiAmount",params:e,method:"get"})},i=function(e){return r["a"].request({url:o+"/detail",params:e,method:"get"})},l=function(e){return r["a"].request({url:o+"/new",data:e,method:"post"})},c=function(e){return r["a"].request({url:o+"/update",data:e,method:"put"})},s=function(e){return r["a"].request({url:o+"/delete",params:e,method:"delete"})}},f79b:function(e,t,a){"use strict";a.d(t,"f",(function(){return n})),a.d(t,"a",(function(){return i})),a.d(t,"h",(function(){return l})),a.d(t,"c",(function(){return c})),a.d(t,"b",(function(){return s})),a.d(t,"g",(function(){return u})),a.d(t,"d",(function(){return d})),a.d(t,"i",(function(){return p})),a.d(t,"e",(function(){return m}));var r=a("66df"),o="/charging/atzCharging",n=function(e){return r["a"].request({url:o+"/query",data:e,method:"post"})},i=function(e){return r["a"].request({url:o+"/add",data:e,method:"post"})},l=function(e){return r["a"].request({url:o+"/edit?isCover="+e.isCover,data:e,method:"post"})},c=function(e){return r["a"].request({url:o+"/del",params:e,method:"post"})},s=function(e){return r["a"].request({url:o+"/audit",params:e,method:"post"})},u=function(e){return r["a"].request({url:o+"/queryDetail",data:e,method:"post"})},d=function(e){return r["a"].request({url:o+"/export",responseType:"blob",data:e,method:"post"})},p=function(e){return r["a"].request({url:o+"/imports",data:e,method:"post"})},m=function(e){return r["a"].request({url:"/oms/api/v1/country/getMccOperators",params:e,method:"get"})}}}]);