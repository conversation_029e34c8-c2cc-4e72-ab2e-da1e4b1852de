(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ee67481a"],{7838:function(t,a,e){"use strict";e("95e8")},"95e8":function(t,a,e){},b66a:function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t._self._c;return a("Card",[a("div",{staticStyle:{width:"100%"}},[a("Table",{staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns12,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(e){var i=e.row;return[a("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"warning",size:"small",loading:t.detailsloading},on:{click:function(a){return t.details(i)}}},[t._v(t._s(t.$t("stock.details")))])]}}])}),a("div",{staticStyle:{"margin-top":"100px"}},[a("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(a){t.currentPage=a},"on-change":t.goPage}})],1),a("Modal",{attrs:{title:t.Titlemessage,width:"620px"},on:{"on-ok":t.ok,"on-cancel":t.cancelModal},model:{value:t.modal5,callback:function(a){t.modal5=a},expression:"modal5"}},[a("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[a("span",[t._v("MSISDN:")]),t._v("  \n\t\t\t\t"),a("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.more.msisdn))]),t._v("  \n\t\t\t")]),a("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v("ICCID:")]),t._v("  \n\t\t\t\t"),a("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.more.iccid))]),t._v("  "),a("br")]),a("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v("IMSI:")]),t._v("  \n\t\t\t\t"),a("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.more.imsi))]),t._v("  "),a("br")]),a("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.$t("stock.Code")))]),t._v("  \n\t\t\t\t"),a("span",[t._v(t._s(t.more.pin2))]),t._v("  "),a("br")]),a("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[a("span",[t._v("PUK:")]),t._v("  \n\t\t\t\t"),a("span",[t._v(t._s(t.more.puk1))]),t._v("  "),a("br")]),a("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[a("span",[t._v("Output File:")]),t._v("  "),a("br")]),a("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[a("span",[t._v("ADM:")]),t._v("  \n\t\t\t\t"),a("span",[t._v(t._s(t.more.fileNameAdm))]),t._v("  "),a("br")]),a("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[a("span",[t._v("SDB:")]),t._v("  \n\t\t\t\t"),a("span",[t._v(t._s(t.more.fileNameSdb))]),t._v("  "),a("br")])])],1)])},n=[],s=e("3835"),o=(e("b64b"),e("d3b7"),e("3ca3"),e("ddb0"),e("2b3d"),e("bf19"),e("9861"),e("88a7"),e("271a"),e("5494"),e("6dfa")),c={data:function(){var t=this;return{modal5:!1,time_slot:"",taskId:"",searchBeginTime:"",searchEndTime:"",Titlemessage:this.$t("stock.details"),total:0,page:0,currentPage:1,loading:!1,searchloading:!1,detailsloading:!1,form:{},typeList:[],columns12:[{title:"IMSI",key:"imsi",align:"center"},{title:"MSISDN",key:"msisdn",align:"center"},{title:"ICCID",key:"iccid",align:"center"},{title:this.$t("stock.usedstate"),key:"cardStatus",align:"center",render:function(a,e){var i=e.row,n="1"==i.cardStatus?t.$t("order.Normal"):"2"==i.cardStatus?t.$t("order.Terminated"):"3"==i.cardStatus?t.$t("order.Suspend"):"";return a("label",n)}},{title:this.$t("stock.cardtype"),key:"cardForm",align:"center",render:function(a,e){var i=e.row,n="1"===i.cardForm?t.$t("stock.PhysicalSIM"):"2"===i.cardForm?t.$t("stock.eSIM"):"3"===i.cardForm?t.$t("stock.TSIM"):"4"===i.cardForm?t.$t("support.imsi"):"";return a("label",n)}},{title:this.$t("stock.action"),slot:"action",align:"center"}],data:[],more:{msisdn:"123",imsi:"1234",iccid:"12345",verCode:"234",pin:"12",puk:"23",adm:"test1.adm",sdb:"test2.adm"},rules:{}}},mounted:function(){this.goPageFirst(1)},methods:{goPageFirst:function(t){var a=this;this.loading=!0;var e=this,i=JSON.parse(decodeURIComponent(this.$route.query.paymentChannel)),n=t,s=10,c=i.taskId;Object(o["h"])({pageNumber:n,pageSize:s,taskId:c}).then((function(i){"0000"==i.code&&(e.loading=!1,a.searchloading=!1,a.page=t,a.currentPage=t,a.total=i.data.total,a.data=i.data.record)})).catch((function(t){console.error(t)})).finally((function(){a.loading=!1,a.searchloading=!1}))},details:function(t){var a=this;this.detailsloading=!0,Object(o["j"])({imsi:t.imsi}).then((function(t){"0000"==t.code&&(a.modal5=!0,a.more=t.data,a.detailsloading=!1)})).catch((function(t){console.error(t)})).finally((function(){a.detailsloading=!1}))},search:function(){this.goPageFirst(1),this.searchloading=!0},goPage:function(t){this.goPageFirst(t)},exportTable:function(){var t=this;Object(o["G"])("number").then((function(a){var e=a.data,i="号码登记表格.txt";if("download"in document.createElement("a")){var n=t.$refs.downloadLink,s=URL.createObjectURL(e);n.download=i,n.href=s,n.click(),URL.revokeObjectURL(s)}else navigator.msSaveBlob(e,i)})).catch((function(){return t.downloading=!1}))},hanldeDateClear:function(){this.searchBeginTime="",this.searchEndTime=""},handleDateChange:function(t){var a=this.time_slot[0]||"",e=this.time_slot[1]||"";if(""!=a&&""!=e){var i=Object(s["a"])(t,2);this.searchBeginTime=i[0],this.searchEndTime=i[1]}},ok:function(){},cancelModal:function(){}}},r=c,l=(e("7838"),e("2877")),d=Object(l["a"])(r,i,n,!1,null,null,null);a["default"]=d.exports}}]);