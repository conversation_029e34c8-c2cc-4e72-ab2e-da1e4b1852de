(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f4392dec"],{"00b4":function(t,e,n){"use strict";n("ac1f");var s=n("23e7"),a=n("c65b"),r=n("1626"),i=n("825a"),c=n("577e"),o=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),d=/./.test;s({target:"RegExp",proto:!0,forced:!o},{test:function(t){var e=i(this),n=c(t),s=e.exec;if(!r(s))return a(d,e,n);var o=a(s,e,n);return null!==o&&(i(o),!0)}})},"30af":function(t,e,n){"use strict";var s=n("44c9"),a=n.n(s);e["default"]=a.a},"44c9":function(t,e){},"5d38":function(t,e,n){"use strict";var s=n("7cd1"),a=n("5f69"),r=n("2877"),i=Object(r["a"])(a["default"],s["a"],s["b"],!1,null,null,null);e["default"]=i.exports},"5f69":function(t,e,n){"use strict";var s=n("7e63"),a=n.n(s);e["default"]=a.a},"631c":function(t,e,n){"use strict";var s=n("dc67"),a=n("30af"),r=n("2877"),i=Object(r["a"])(a["default"],s["a"],s["b"],!1,null,null,null);e["default"]=i.exports},"6aa0":function(t,e,n){"use strict";n("7e5b0")},"7cd1":function(t,e,n){"use strict";n.d(e,"a",(function(){return s})),n.d(e,"b",(function(){return a}));var s=function(){var t=this;t._self._c,t._self._setupProxy;return t._m(0)},a=[function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticStyle:{padding:"10px 10px"}},[e("h4",{staticStyle:{"text-align":"left"}},[t._v("确保口令满足以下通用原则")]),e("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        1、口令至少由8位及以上大写字母、小写字母、数字与特殊符号等4类中3类混合、随机组成，尽量不要以姓名、电话号码以及出生日期等作为口令或者口令的组成部分；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        2、口令应与用户名无相关性，口令中不得包含用户名的完整字符串、大小写变位或形似变换的字符串，如teleadmin:teleadmin、teleadmin:teleadmin2017、teleadmin:TeleAdmin、teleadmin:te!e@dmin等；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        3、应更换系统或设备的出厂默认口令，如huawei:huawei@123，oracle数据库中SYS:CHANGE_ON_INSTALL,某移动定制版光猫默认帐号CMCCAdmin:aDm8H%MdA等；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        4、口令设置应避免3位以上（含3位）键盘排序密码，如qwe（键盘第1行前三个字母）、asd（键盘第2行前三个字母）、qaz（键盘第1列三个字母）、1qaz（键盘第1列数字加前三个字母）、！QAZ（键盘第1列特殊字符加前三个字母）等；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        5、口令中不能出现3位以上（含三位）连续字母、数字、特殊字符，如ABC、Abc、123、！@#等；\n      ")]),e("span",[t._v("\n        6、口令中不能出现3位以上（含三位）重复字母、数字、特殊字符，如AAA、Aaa、111、###等。\n      ")]),e("span",[t._v("\n\t\t\t7、当前密码不能与近10次使用密码重复。\n\t\t")])]),e("h4",{staticStyle:{"text-align":"left"}},[t._v("避免以下易猜解口令规则")]),e("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        1、省份、地市名称、邮箱、电话区号、邮政编码及缩写和简单数字或shift键+简单数字，如BJYD123、HBYD!@#等；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        2、单位名称、专业名称、系统名称、厂家名称（含缩写）和简单数字，如HBnmc123、HBsmc_123等；\n      ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n        3、维护人员名字全拼大小写缩写等变形+设备IP地址（一位或两位）或出生年月日等，如维护人员张三，维护设备地址,出生日期为19951015，则其可能的弱口令为zhangsan100、zhangsan101，zhangsan10100，zhangsan10101，zhangsan19951015，ZS19951015等；\n      ")])])])}]},"7e5b0":function(t,e,n){},"7e63":function(t,e){},a287:function(t,e,n){"use strict";n.d(e,"d",(function(){return r})),n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return c})),n.d(e,"c",(function(){return o})),n.d(e,"f",(function(){return d})),n.d(e,"e",(function(){return u}));var s=n("66df"),a="/sys/api/v1",r=function(t){return s["a"].request({url:a+"/account/list",data:t,method:"post"})},i=function(t){return s["a"].request({url:a+"/user/deleteUser",params:t,method:"PUT"})},c=function(t){return s["a"].request({url:a+"/user/signIn",data:t,method:"post"})},o=function(t){return s["a"].request({url:a+"/user/update",data:t,method:"PUT"})},d=function(t){return s["a"].request({url:a+"/user/resetPasswd",data:t,method:"PUT"})},u=function(t){return s["a"].request({url:a+"/region/list",params:t,method:"get"})}},cf08:function(t,e,n){"use strict";n.r(e);var s=function(){var t=this,e=t._self._c;return e("div",[e("Card",[e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"}},[e("div",{staticClass:"view_out"},[e("Form",{ref:"editForm",attrs:{model:t.userInfo,rules:t.rules},nativeOn:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.edit.apply(null,arguments)}}},[e("FormItem",{attrs:{prop:"account"}},[e("div",{staticClass:"view_line"},[e("span",{staticStyle:{width:"25%"}},[t._v(t._s(t.$t("sys.account")))]),e("Input",{staticStyle:{width:"80%"},attrs:{disabled:"",placeholder:t.$t("sys.Enteraccount")},model:{value:t.userInfo.account,callback:function(e){t.$set(t.userInfo,"account",e)},expression:"userInfo.account"}})],1)]),e("FormItem",{attrs:{prop:"oldPwd"}},[e("div",{staticClass:"view_line"},[e("span",{staticStyle:{width:"25%"}},[t._v(t._s(t.$t("sys.oldPwd")))]),e("Input",{staticStyle:{width:"80%"},attrs:{type:"password",password:"",placeholder:t.$t("sys.oldpassword")},model:{value:t.userInfo.oldPwd,callback:function(e){t.$set(t.userInfo,"oldPwd",e)},expression:"userInfo.oldPwd"}})],1)]),e("FormItem",{attrs:{prop:"newPwd"}},[e("div",{staticClass:"view_line"},[e("span",{staticStyle:{width:"25%"}},[t._v(t._s(t.$t("sys.newPwd")))]),e("Input",{staticStyle:{width:"80%"},attrs:{type:"password",password:"",placeholder:t.$t("sys.newpassword")},model:{value:t.userInfo.newPwd,callback:function(e){t.$set(t.userInfo,"newPwd",e)},expression:"userInfo.newPwd"}})],1)]),e("FormItem",{attrs:{prop:"rePwd"}},[e("div",{staticClass:"view_line"},[e("span",{staticStyle:{width:"25%"}},[t._v(t._s(t.$t("sys.rePwd")))]),e("Input",{staticStyle:{width:"80%"},attrs:{type:"password",password:"",placeholder:t.$t("address.password_ok")},model:{value:t.userInfo.rePwd,callback:function(e){t.$set(t.userInfo,"rePwd",e)},expression:"userInfo.rePwd"}})],1)]),e("Alert",{attrs:{type:"warning","show-icon":""}},[t._v(t._s(t.$t("address.PwdRules"))),e("a",{attrs:{href:"#"},on:{click:function(e){t.showRules=!0}}},[t._v(t._s(t.$t("address.watch")))]),t._v(t._s(t.$t("address.more")))]),e("FormItem",[e("div",{staticClass:"view_butt"},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"edit",expression:"'edit'"},{name:"preventReClick",rawName:"v-preventReClick"}],staticClass:"searchBtn",attrs:{icon:"md-refresh",type:"success"},on:{click:t.reset}},[t._v(t._s(t.$t("buymeal.Reset")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"edit",expression:"'edit'"},{name:"preventReClick",rawName:"v-preventReClick"}],staticClass:"searchBtn",staticStyle:{"margin-left":"55px"},attrs:{icon:"md-finger-print",type:"primary"},on:{click:t.edit}},[t._v(t._s(t.$t("common.edit")))])],1)])],1)],1),t.showRules?e("div",{staticClass:"view_right"},[e("Alert",{attrs:{type:"warning",closable:""},on:{"on-close":function(e){t.showRules=!1}}},["zh-CN"===this.$i18n.locale?e("div",[e("text-view")],1):t._e(),"en-US"===this.$i18n.locale?e("div",[e("text-viewEn")],1):t._e()])],1):t._e()])])],1)},a=[],r=n("5530"),i=(n("d9e2"),n("caad"),n("14d9"),n("d3b7"),n("ac1f"),n("00b4"),n("2532"),n("5319"),n("159b"),n("5d38")),c=n("631c"),o=n("fe07"),d=n("a287"),u=n("2f62"),l={name:"InfoExperience",components:{TextView:i["default"],TextViewEn:c["default"]},data:function(){var t=this,e=function(e,n,s){var a=/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/;if(0!=a.test(n))if(/(.)\1{2}/i.test(n))s(new Error(t.$t("address.appear")));else{var r=/((?:0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)|9(?=0)){2}\d)/;r.test(n)?s(new Error(t.$t("address.allowed"))):s()}else s(new Error(t.$t("address.reset")))},n=function(t,e,n){e?n():n(new Error(o["a"].t("sys.accountNummMsg")))};return{showRules:!1,maxlength:20,userInfo:{account:"",oldPwd:"",newPwd:"",rePwd:""},rules:{oldPwd:[{required:!0,message:this.$t("sys.poldPwd"),trigger:"change"}],newPwd:[{validator:e,trigger:"blur"}],rePwd:[{validator:e,trigger:"blur"}],account:[{validator:n,trigger:"blur"}]}}},methods:Object(r["a"])(Object(r["a"])({},Object(u["b"])(["handleLogOut"])),{},{logout:function(){var t=this;this.handleLogOut().then((function(){t.$router.push({name:"login"})}))},resetField:function(t){this.$refs["editForm"].fields.forEach((function(e){t.includes(e.prop)&&e.resetField()}))},reset:function(){this.resetField(["rePwd","newPwd","oldPwd"])},reUserInfo:function(){this.userInfo.account=this.$store.state.user.userName},edit:function(){var t=this;this.$refs.editForm.validate((function(e){if(e){if(t.userInfo.newPwd!==t.userInfo.rePwd)return void t.$Notice.warning({title:t.$t("address.Operationreminder"),desc:t.$t("address.inconsistent")});t.reSetUserInfo()}}))},reSetUserInfo:function(){var t=this;Object(d["f"])({id:this.$store.state.user.userId,userName:this.userInfo.account,oldPasswd:this.userInfo.oldPwd.replace(/\s/g,""),newPasswd:this.userInfo.newPwd.replace(/\s/g,"")},this.$store.state.user.userId).then((function(e){if(!e||"0000"!=e.code)throw e;t.$store.state.user.userName=t.userInfo.account,t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("sys.successfully")});var n=t;setTimeout((function(){n.logout()}),1e3)})).catch((function(t){}))},checkPhone:function(t){return!(!t||""===t)&&!(!/^[0-9]*$/.test(t.replace(/\s/g,""))||!/^\d{4,16}$/.test(t.replace(/\s/g,"")))},checkPwd:function(t){return!(!t||""===t)},error:function(t){this.$Notice.error({title:this.$t("sys.wrong"),desc:t||this.$t("sys.Serverwrong")})},cancel:function(){}}),mounted:function(){this.reUserInfo()},watch:{}},f=l,p=(n("6aa0"),n("2877")),w=Object(p["a"])(f,s,a,!1,null,null,null);e["default"]=w.exports},dc67:function(t,e,n){"use strict";n.d(e,"a",(function(){return s})),n.d(e,"b",(function(){return a}));var s=function(){var t=this;t._self._c,t._self._setupProxy;return t._m(0)},a=[function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticStyle:{padding:"10px 10px"}},[e("h4",{staticStyle:{"text-align":"left"}},[t._v("Password character policy:")]),e("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n     1. The password must be 8 characters or more and contain at least one uppercase character, at least one lowercase character, at least one number and at least one special symbol;\n    ")]),e("span",{staticStyle:{padding:"5px 0"}},[t._v("\n      2. The password shall not contain any three identical consecutive (ABC, Abc, 123, !@# etc) and repetitive characters (AAA, Aaa, 111, ### etc)\n    ")])])])}]}}]);