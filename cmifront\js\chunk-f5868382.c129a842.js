(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f5868382"],{"00b4":function(t,e,o){"use strict";o("ac1f");var r=o("23e7"),n=o("c65b"),a=o("1626"),s=o("825a"),l=o("577e"),i=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),u=/./.test;r({target:"RegExp",proto:!0,forced:!i},{test:function(t){var e=s(this),o=l(t),r=e.exec;if(!a(r))return n(u,e,o);var i=n(r,e,o);return null!==i&&(s(i),!0)}})},"129f":function(t,e,o){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"13ee":function(t,e,o){"use strict";o.d(e,"h",(function(){return a})),o.d(e,"k",(function(){return s})),o.d(e,"j",(function(){return l})),o.d(e,"p",(function(){return i})),o.d(e,"u",(function(){return u})),o.d(e,"i",(function(){return c})),o.d(e,"q",(function(){return d})),o.d(e,"d",(function(){return f})),o.d(e,"a",(function(){return p})),o.d(e,"c",(function(){return h})),o.d(e,"b",(function(){return m})),o.d(e,"e",(function(){return g})),o.d(e,"n",(function(){return w})),o.d(e,"f",(function(){return v})),o.d(e,"o",(function(){return y})),o.d(e,"r",(function(){return b})),o.d(e,"s",(function(){return I})),o.d(e,"l",(function(){return P})),o.d(e,"m",(function(){return x})),o.d(e,"g",(function(){return C})),o.d(e,"v",(function(){return S})),o.d(e,"t",(function(){return k}));var r=o("66df"),n="/cms",a=function(t){return r["a"].request({url:n+"/flowPool/getCard",params:t,method:"get"})},s=function(t){return r["a"].request({url:n+"/flowPool/outCardList",params:t,method:"post"})},l=function(t){return r["a"].request({url:n+"/flowPool/getChannelFlowList",data:t,method:"post"})},i=function(t){return r["a"].request({url:n+"/flowPool/ChannelFlowListOut",data:t,method:"post"})},u=function(t){return r["a"].request({url:n+"/flowPool/updateFlowPoolReminder",params:t,method:"post"})},c=function(t){return r["a"].request({url:n+"/flowPool/getICCID",params:t,method:"get"})},d=function(t){return r["a"].request({url:n+"/flowPool/outICCID",params:t,method:"post"})},f=function(t){return r["a"].request({url:n+"/channelCard/flowPoolAddCard ",data:t,method:"post"})},p=function(t){return r["a"].request({url:n+"/channelCard/flowPoolAddCardBatch",data:t,method:"post",contentType:"multipart/form-data"})},h=function(t){return r["a"].request({url:n+"/flowPool/removeCards",data:t,method:"post"})},m=function(t){return r["a"].request({url:n+"/flowPool/ChannelRemoveCards",data:t,method:"post"})},g=function(t){return r["a"].request({url:n+"/flowPool/getFlowpoolUseRecord",params:t,method:"get"})},w=function(t){return r["a"].request({url:n+"/flowPool/outFlowpoolUseRecord",params:t,method:"post"})},v=function(t){return r["a"].request({url:n+"/flowPool/getCardUseDetailRecord",params:t,method:"get"})},y=function(t){return r["a"].request({url:n+"/flowPool/outFlowPoolDetailRecord",params:t,method:"post"})},b=function(t){return r["a"].request({url:n+"/channel/".concat(t),method:"get"})},I=function(t){return r["a"].request({url:n+"/flowPool/getIccidImportTaskList",params:t,method:"get"})},P=function(t){return r["a"].request({url:n+"/flowPool/getIccidImportTaskFile",params:t,method:"get",responseType:"blob"})},x=function(t){return r["a"].request({url:"/stat/finance/flowpoolBillExport",params:t,method:"get"})},C=function(t){return r["a"].request({url:n+"/flowPool/updateICCID",data:t,method:"post"})},S=function(t){return r["a"].request({url:n+"/flowPool/card/pause",params:t,method:"get"})},k=function(t){return r["a"].request({url:n+"/flowPool/card/resume",params:t,method:"get"})}},"841c":function(t,e,o){"use strict";var r=o("c65b"),n=o("d784"),a=o("825a"),s=o("7234"),l=o("1d80"),i=o("129f"),u=o("577e"),c=o("dc4a"),d=o("14c3");n("search",(function(t,e,o){return[function(e){var o=l(this),n=s(e)?void 0:c(e,t);return n?r(n,e,o):new RegExp(e)[t](u(o))},function(t){var r=a(this),n=u(t),s=o(e,r,n);if(s.done)return s.value;var l=r.lastIndex;i(l,0)||(r.lastIndex=0);var c=d(r,n);return i(r.lastIndex,l)||(r.lastIndex=l),null===c?-1:c.index}]}))},"9ff0":function(t,e,o){"use strict";o.r(e);o("ac1f"),o("841c");var r=function(){var t=this,e=t._self._c;return e("Card",[e("div",[e("span",{staticStyle:{"margin-top":"4px","font-weight":"bold"}},[t._v("渠道商:")]),t._v("  \n\t\t"),e("span",[t._v(t._s(t.corpName))]),t._v("  \n\t\t"),e("span",{staticStyle:{"margin-top":"4px","font-weight":"bold"}},[t._v("客户类型:")]),t._v("  \n\t\t"),e("span",[t._v(t._s(t.corpType))])]),e("div",{staticStyle:{display:"flex","margin-top":"20px"}},[e("span",{staticStyle:{"margin-top":"4px","font-weight":"bold"}},[t._v("流量池名称:")]),t._v("  \n\t\t"),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入流量池名称",clearable:""},model:{value:t.searchObj.flowpoolname,callback:function(e){t.$set(t.searchObj,"flowpoolname",e)},expression:"searchObj.flowpoolname"}}),t._v("    \n\t\t"),e("span",{staticStyle:{"margin-top":"4px","font-weight":"bold"}},[t._v("使用状态:")]),t._v("  \n\t\t"),e("Select",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{filterable:"",clearable:!0,placeholder:"请选择使用状态"},model:{value:t.searchObj.usestatus,callback:function(e){t.$set(t.searchObj,"usestatus",e)},expression:"searchObj.usestatus"}},[e("Option",{attrs:{value:1}},[t._v("达量限速")]),e("Option",{attrs:{value:2}},[t._v("达量停用")]),e("Option",{attrs:{value:3}},[t._v("正常")])],1),t._v("    \n\t\t"),e("span",{staticStyle:{"margin-top":"4px","font-weight":"bold"}},[t._v("上架状态:")]),t._v("  \n\t\t"),e("Select",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{filterable:"",clearable:!0,placeholder:"请选择上架状态"},model:{value:t.searchObj.shelfstatus,callback:function(e){t.$set(t.searchObj,"shelfstatus",e)},expression:"searchObj.shelfstatus"}},[e("Option",{attrs:{value:1}},[t._v("上架")]),e("Option",{attrs:{value:2}},[t._v("下架")])],1),t._v("    \n\t\t"),e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v("搜索")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{margin:"0 2px","margin-left":"20px"},attrs:{icon:"ios-cloud-download-outline",type:"success",loading:t.downloading},on:{click:t.exportFile}},[t._v("\n\t\t  导出\n\t\t")]),e("Button",{staticStyle:{margin:"0 4px"},on:{click:t.back}},[e("Icon",{attrs:{type:"ios-arrow-back"}}),t._v(" 返回\n\t\t")],1)],1),e("Table",{staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(o){var r=o.row;o.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"iccidlist",expression:"'iccidlist'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"warning",ghost:""},on:{click:function(e){return t.getIccid(r)}}},[t._v("ICCID列表")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"recharge",expression:"'recharge'"}],staticStyle:{"margin-right":"10px"},attrs:{disabled:"1"===r.useStatus,type:"primary",ghost:""},on:{click:function(e){return t.recharge(r)}}},[t._v("流量充值")])]}}])}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:"流量充值","mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.rechargeModal,callback:function(e){t.rechargeModal=e},expression:"rechargeModal"}},[e("Form",{ref:"form",attrs:{model:t.form,rules:t.rule}},[e("FormItem",{attrs:{label:"流量池名称:"}},[e("span",[t._v(t._s(t.form.poolname))])]),e("FormItem",{attrs:{label:"充值额度:",prop:"recharge"}},[e("Input",{staticStyle:{width:"300px"},attrs:{placeholder:"单位GB",clearable:""},model:{value:t.form.recharge,callback:function(e){t.$set(t.form,"recharge",e)},expression:"form.recharge"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("GB")])])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("返回")]),e("Button",{attrs:{type:"primary",loading:t.rechargeloading},on:{click:t.Confirm}},[t._v("确定")])],1)],1),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v("导出提示")]),e("FormItem",{attrs:{label:"你本次导出任务ID为:"}},[e("span",{staticStyle:{width:"100px"}},[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:"你本次导出的文件名为:"}},[e("span",[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v("请前往下载管理-下载列表查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v("立即前往")])],1)])],1)},n=[],a=(o("d81d"),o("14d9"),o("e9c4"),o("b64b"),o("d3b7"),o("00b4"),o("5319"),o("c01c")),s=o("13ee"),l={data:function(){return{corpName:"",corpType:"",corpId:"",form:{poolname:"",flowPoolId:"",recharge:"0"},searchObj:{flowpoolname:"",usestatus:"",shelfstatus:""},total:0,currentPage:1,page:0,taskId:"",taskName:"",loading:!1,searchloading:!1,downloading:!1,rechargeloading:!1,rechargeModal:!1,exportModal:!1,objlist:{},columns:[{title:"流量池名称",key:"flowPoolName",minWidth:120,align:"center",tooltip:!0},{title:"使用状态",key:"useStatus",minWidth:120,align:"center",render:function(t,e){var o=e.row,r="1"===o.useStatus?"达量限速":"2"===o.useStatus?"达量停用":"3"===o.useStatus?"正常":"";return t("label",r)}},{title:"上架状态",key:"shelfStatus",minWidth:120,align:"center",render:function(t,e){var o=e.row,r="1"===o.shelfStatus?"上架":"2"===o.shelfStatus?"下架":"";return t("label",r)}},{title:"总流量(GB)",key:"flowPoolTotal",minWidth:120,align:"center"},{title:"已用流量(GB)",key:"usedFlow",minWidth:120,align:"center"},{title:"卡号数量",key:"cardCount",minWidth:120,align:"center"},{title:"重置周期类型",key:"cycleType",minWidth:120,align:"center",render:function(t,e){var o=e.row,r="1"===o.cycleType?"24小时":"2"===o.cycleType?"自然日":"3"===o.cycleType?"自然月":"4"===o.cycleType?"自然年":"";return t("label",r)}},{title:"重置周期数",key:"cycleNum",minWidth:120,align:"center"},{title:"控制逻辑",key:"controlLogic",minWidth:120,align:"center",render:function(t,e){var o=e.row,r="1"===o.controlLogic?"达量限速":"2"===o.controlLogic?"达量停用":"3"===o.controlLogic?"达量继续使用":"";return t("label",r)}},{title:"有效日期",key:"effectiveDate",minWidth:280,align:"center"},{title:"支持国家",key:"supportCountry",minWidth:200,align:"center",render:function(t,e){var o=e.row,r="";o.supportCountry.map((function(t){r=""===r?r+""+t:r+", "+t}));var n=""===r||null===r?0:r.length;if(n>8){var a=r.replace(/\|/g,"</br>");return r=r.substring(0,8)+"...",t("div",[t("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[r,t("label",{slot:"content",style:{whiteSpace:"normal"}},a)])])}return r=r,t("label",r)}},{title:"操作",slot:"action",minWidth:250,align:"center",fixed:"right"}],data:[],rule:{recharge:[{required:!0,message:"请输入充值额度",trigger:"blur"},{validator:function(t,e,o){var r=/^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;return r.test(e)},message:"最高支持8位整数和2位小数的正数或零"}]}}},mounted:function(){localStorage.setItem("ObjList",decodeURIComponent(this.$route.query.ObjList));var t=null===JSON.parse(localStorage.getItem("flowList"))?"":JSON.parse(localStorage.getItem("flowList"));t&&(this.searchObj.flowpoolname=void 0===t.flowpoolname?"":t.flowpoolname,this.searchObj.usestatus=void 0===t.usestatus?"":t.usestatus,this.searchObj.shelfstatus=void 0===t.shelfstatus?"":t.shelfstatus),this.objlist=JSON.parse(decodeURIComponent(this.$route.query.obj)),this.getcorpName(this.objlist.corpId),this.corpType=this.objlist.type,this.corpType="1"===this.corpType?"渠道商":"3"===this.corpType?"合作商":"4"===this.corpType?"后付费":"",this.corpId=this.objlist.corpId,this.goPageFirst(1),localStorage.removeItem("flowList")},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var o=this;Object(a["p"])({pageSize:10,pageNum:t,flowPoolName:this.searchObj.flowpoolname,useStatus:this.searchObj.usestatus,shelfStatus:this.searchObj.shelfstatus,corpId:this.corpId,corpName:this.corpName,cooperationMode:1}).then((function(r){"0000"==r.code&&(o.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.total=r.count,e.data=r.data)})).catch((function(t){console.error(t)})).finally((function(){o.loading=!1,e.searchloading=!1}))},goPage:function(t){this.goPageFirst(t)},search:function(){this.searchloading=!0,this.goPageFirst(1)},exportFile:function(){var t=this;this.downloading=!0,Object(a["n"])({pageSize:-1,pageNum:-1,flowPoolName:this.searchObj.flowpoolname,useStatus:this.searchObj.usestatus,shelfStatus:this.searchObj.shelfstatus,corpId:this.corpId,corpName:this.corpName,userId:this.$store.state.user.userId,exportType:1}).then((function(e){t.exportModal=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName,t.downloading=!1})).catch((function(){return t.downloading=!1}))},getIccid:function(t){this.$router.push({path:"/channeliccidlist",query:{flowList:encodeURIComponent(JSON.stringify(this.searchObj)),list:encodeURIComponent(JSON.stringify(t)),obj:encodeURIComponent(JSON.stringify(this.objlist)),corpId:encodeURIComponent(JSON.stringify(this.corpId)),ObjList:encodeURIComponent(JSON.stringify(JSON.parse(decodeURIComponent(this.$route.query.ObjList))))}})},recharge:function(t){this.rechargeModal=!0,this.form.poolname=t.flowPoolName,this.form.flowPoolId=t.flowPoolId},cancelModal:function(){this.rechargeModal=!1,this.exportModal=!1,this.$refs["form"].resetFields()},Confirm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(t.rechargeloading=!0,Object(a["s"])({flowValue:t.form.recharge,flowPoolId:t.form.flowPoolId}).then((function(e){if(!e||"0000"!=e.code)throw e;t.goPageFirst(t.page),t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.cancelModal()})).catch((function(t){return!1})).finally((function(){t.rechargeloading=!1})))}))},back:function(){this.$router.push({path:"/channelPool",query:{}})},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},getcorpName:function(t){var e=this;Object(s["r"])(t).then((function(t){"0000"==t.code&&(e.corpName=t.data.corpName)})).catch((function(t){console.error(t)}))}}},i=l,u=o("2877"),c=Object(u["a"])(i,r,n,!1,null,null,null);e["default"]=c.exports},c01c:function(t,e,o){"use strict";o.d(e,"q",(function(){return a})),o.d(e,"b",(function(){return s})),o.d(e,"j",(function(){return l})),o.d(e,"p",(function(){return i})),o.d(e,"n",(function(){return u})),o.d(e,"s",(function(){return c})),o.d(e,"i",(function(){return d})),o.d(e,"o",(function(){return f})),o.d(e,"e",(function(){return p})),o.d(e,"a",(function(){return h})),o.d(e,"d",(function(){return m})),o.d(e,"c",(function(){return g})),o.d(e,"f",(function(){return w})),o.d(e,"l",(function(){return v})),o.d(e,"g",(function(){return y})),o.d(e,"m",(function(){return b})),o.d(e,"r",(function(){return I})),o.d(e,"k",(function(){return P})),o.d(e,"u",(function(){return x})),o.d(e,"t",(function(){return C})),o.d(e,"h",(function(){return S}));var r=o("66df"),n="/cms",a=function(t){return r["a"].request({url:n+"/flowPool/getCorpList",params:t,method:"get"})},s=function(t){return r["a"].request({url:n+"/flowPool/getCard",params:t,method:"get"})},l=function(t){return r["a"].request({url:n+"/flowPool/outCardList",params:t,method:"post"})},i=function(t){return r["a"].request({url:n+"/flowPool/getChannelFlowList",data:t,method:"post"})},u=function(t){return r["a"].request({url:n+"/flowPool/ChannelFlowListOut",data:t,method:"post"})},c=function(t){return r["a"].request({url:n+"/flowPool/rechargeFlow",params:t,method:"put"})},d=function(t){return r["a"].request({url:n+"/flowPool/getICCID",params:t,method:"get"})},f=function(t){return r["a"].request({url:n+"/flowPool/outICCID",params:t,method:"post"})},p=function(t){return r["a"].request({url:n+"/channelCard/flowPoolAddCard ",data:t,method:"post"})},h=function(t){return r["a"].request({url:n+"/channelCard/flowPoolAddCardBatch",data:t,method:"post",contentType:"multipart/form-data"})},m=function(t){return r["a"].request({url:n+"/flowPool/removeCards",data:t,method:"post"})},g=function(t){return r["a"].request({url:n+"/flowPool/ChannelRemoveCards",data:t,method:"post"})},w=function(t){return r["a"].request({url:n+"/flowPool/getFlowpoolUseRecord",params:t,method:"get"})},v=function(t){return r["a"].request({url:n+"/flowPool/outFlowpoolUseRecord",params:t,method:"post"})},y=function(t){return r["a"].request({url:n+"/flowPool/getCardUseDetailRecord",params:t,method:"get"})},b=function(t){return r["a"].request({url:n+"/flowPool/outFlowPoolDetailRecord",params:t,method:"post"})},I=function(t){return r["a"].request({url:n+"/flowPool/getIccidImportTaskList",params:t,method:"get"})},P=function(t){return r["a"].request({url:n+"/flowPool/getIccidImportTaskFile",params:t,method:"get",responseType:"blob"})},x=function(t){return r["a"].request({url:n+"/flowPool/card/pause",params:t,method:"get"})},C=function(t){return r["a"].request({url:n+"/flowPool/card/resume",params:t,method:"get"})},S=function(t){return r["a"].request({url:n+"/flowPool/updateICCID",data:t,method:"post"})}}}]);