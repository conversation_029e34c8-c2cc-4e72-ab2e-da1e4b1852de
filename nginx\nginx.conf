
#user  nobody;
worker_processes  1;

#error_log  /var/log/nginx/error.log;
#error_log  /var/log/nginx/error.log  notice;
#error_log  /var/log/nginx/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';


    #access_log  /var/log/nginx/access.log  main;
    sendfile        on;
    #tcp_nopush     on;
    keepalive_timeout  65;
    server_tokens off; # hide version
    gzip on;
    gzip_min_length  3k;
    gzip_buffers     4 16k;
    gzip_http_version 1.0;
    gzip_comp_level 2;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript;
    gzip_vary on;

    upstream api_gateway {
      server ***********:32005;
      server ***********:32005;
    }

    server {
        listen       5040;
        server_name  localhost;
        root  /etc/nginx/html;
        client_max_body_size 30m;


        location ^~ /cmifront {
            index  index.html index.htm;
            try_files $uri $uri/ /cmifront/index.html;
        }

    }

}
