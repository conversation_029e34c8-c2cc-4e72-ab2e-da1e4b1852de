{"name": "iview-admin", "version": "2.0.0", "author": "Lison<<EMAIL>>", "private": false, "scripts": {"dev": "vue-cli-service serve --open", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "test": "jest"}, "dependencies": {"@adyen/adyen-web": "^5.68.0", "@adyen/api-library": "v19.2.0", "axios": "^0.18.0", "clipboard": "^2.0.0", "codemirror": "^5.38.0", "core-js": "^3.30.2", "countup": "^1.8.2", "cropperjs": "^1.2.2", "dayjs": "^1.7.7", "dom-storage": "^2.1.0", "echarts": "^4.0.4", "fraction.js": "^4.1.1", "highlight.js": "^11.11.1", "html2canvas": "^1.0.0-alpha.12", "iview": "^3.2.2", "iview-area": "^1.5.17", "jquery": "^3.6.2", "js-cookie": "^2.2.0", "lodash-es": "^4.17.21", "marked": "^15.0.11", "mathjs": "^9.4.4", "more-tree-table": "^0.1.9", "simplemde": "^1.11.2", "sortablejs": "^1.7.0", "tree-table-vue": "^1.1.0", "typed-js": "^0.2.3", "v-org-tree": "^1.0.6", "view-design": "^4.5.0", "vue": "^2.5.10", "vue-i18n": "^7.8.0", "vue-pdf": "^4.2.0", "vue-router": "^3.0.1", "vue-uuid": "^2.0.2", "vue-virtual-scroll-list": "^2.3.5", "vuedraggable": "^2.16.0", "vuex": "^3.0.1", "vuex-persistedstate": "^4.0.0-beta.3", "wangeditor": "^3.1.1", "xlsx": "^0.13.3"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/preset-env": "^7.21.5", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^3.0.1", "@vue/cli-plugin-unit-jest": "~5.0.0", "@vue/cli-plugin-unit-mocha": "^3.0.1", "@vue/cli-service": "^3.0.1", "@vue/eslint-config-standard": "^3.0.0-beta.10", "@vue/test-utils": "^1.1.3", "@vue/vue2-jest": "^27.0.0-alpha.2", "babel-jest": "^26.6.3", "chai": "^4.1.2", "complex.js": "^2.1.1", "eslint-plugin-cypress": "^2.0.1", "jest": "^26.6.3", "less": "^2.7.3", "less-loader": "^4.0.5", "lint-staged": "^6.0.0", "mockjs": "^1.0.1-beta3", "stylus": "^0.54.8", "stylus-loader": "^5.0.0", "vue-loader": "^15.9.6", "vue-template-compiler": "^2.6.12", "vue-virtual-scroller": "^1.1.2", "webpack": "^4.46.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}