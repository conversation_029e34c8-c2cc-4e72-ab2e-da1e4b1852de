<template>
	<div id="app">
		<router-view v-if="isRouterAlive"></router-view>
		<chat />
	</div>
</template>

<script>
	import {
		isOperateFun
	} from '@/libs/isOperate'
	import Chat from '@/components/chat/index.vue'
	export default {
		name: 'App',
		components: {
			Chat
		},
		provide() {
			return {
				reload: this.reload
			}
		},
		data() {
			return {
				isRouterAlive: true
			}
		},
		// created() {
		// 	isOperateFun()
		// },
		methods: {
			//全局方法，解决语言切换时表头未刷新问题
			reload() {
				this.isRouterAlive = false
				this.$nextTick(function() {
					this.isRouterAlive = true
				})
			}
		}
	}
</script>

<style lang="less">
	.size {
		width: 100%;
		height: 100%;
	}

	html,
	body {
		.size;
		overflow: hidden;
		margin: 0;
		padding: 0;
	}

	#app {
		.size;
	}
</style>
