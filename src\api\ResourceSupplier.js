import axios from '@/libs/api.request'

const servicePre = '/rms/api/v1'
const pmsServicePre = '/pms'


// 资源供应商管理获取列表
export const supplier = data => {
	return axios.request({
		url: servicePre + '/supplier/selectSupplier',
		params: data,
		method: 'get'
	})
}

// 新增资源供应商
export const addSupplierList = data => {
	return axios.request({
		url: servicePre + '/supplier/saveSupplier',
		data,
		method: 'post',
		// contentType: 'multipart/form-data'
	})
}

// 修改资源供应商
export const updateSupplierList = data => {
	return axios.request({
		url: servicePre + '/supplier/updateSupplier',
		data,
		method: 'post',
		// contentType: 'multipart/form-data'
	})
}

//根据简称获取对应的编码接口
export const getSupplierShortenCode = data => {
	return axios.request({
		url: servicePre + '/supplier/queryShorten',
		data,
		method: 'get',
	})
}

//获取实名制地区列表
export const getRegionList = data => {
	return axios.request({
		url: pmsServicePre + '/pms-realname/getMccList',
		data,
		method: 'get',
	})
}