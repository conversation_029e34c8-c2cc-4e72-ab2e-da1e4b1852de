import axios from '@/libs/api.request'
import config from '@/config'
import store from '@/store'

const servicePre = '/sys/api'
const baseUrl = process.env.NODE_ENV === 'development' ? config.baseUrl.dev : config.baseUrl.pro

/**
 * AI聊天接口
 */

// 发送消息到AI接口获取回复（流式响应）
export const sendChatMessage = async (data, abortController) => {
  const apiUrl = baseUrl + servicePre + '/aily/knowledges'
  
  // 添加headers
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'text/event-stream'
  }

  // 如果用户已登录，添加token
  if (store.state.user.token) {
    headers.Authorization = `bearer ${store.state.user.token}`
  }
  
  // 使用fetch API直接进行流式请求
  const response = await fetch(apiUrl, {
    method: 'POST',
    body: JSON.stringify(data),
    credentials: 'same-origin',
    headers: headers,
    signal: abortController ? abortController.signal : null
  })

  if (!response.ok) {
    throw new Error(`API请求失败: ${response.status}`)
  }

  return response
}

// 发送非流式消息到AI接口
export const sendNormalChatMessage = (data) => {
  return axios.request({
    url: servicePre + '/aily/knowledges',
    data,
    method: 'POST'
  })
}

// 取消当前的AI对话请求
export const cancelChatRequest = (controller) => {
  if (controller) {
    controller.abort()
  }
}

// 上传图片/文件到AI助手
export const uploadFile = formData => {
  return axios.request({
    url: servicePre + '/upload',
    data: formData,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
