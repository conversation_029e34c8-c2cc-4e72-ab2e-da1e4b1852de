import axios from '@/libs/api.request'

const servicePre = '/cms'

// 获取二维码列表
export const esimList = data => {
	return axios.request({
		url: servicePre + '/esim/getList',
		data,
		method: 'post',
	})
}

// 二维码文件下载接口
export const exportFile = data => {
	return axios.request({
		url: servicePre + '/esim/infoExport',
		data,
		method: 'post',
	})
}

//二维码图片下载
export const downloadTask = data => {
	return axios.request({
		url: servicePre + '/esim/uploadQrCode',
		params: data,
		method: 'get',
		responseType: 'blob'
	})
}

// 二维码图片文件导出接口
export const exportQrFile = data => {
	return axios.request({
		url: servicePre + '/esim/qrCodeExport',
		data,
		method: 'post',
	})
}

// 获取esim信息
export const getEsimInfo = data => {
  return axios.request({
    url: servicePre + '/esim/getEsimInfo',
    params: data,
    method: 'get'
  })
}

//二维码图片查看
export const getQrCode = data => {
	return axios.request({
		url: servicePre + '/esim/getQrCode',
		params: data,
		method: 'get',
		responseType: 'blob'
	})
}