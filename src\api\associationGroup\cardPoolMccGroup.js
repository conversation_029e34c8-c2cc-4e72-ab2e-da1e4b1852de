import axios from '@/libs/api.request'

const servicePre = '/pms/api/v1/cardPoolMccGroup'

// 国家关联卡池组查询
export const getCardPoolGroup = data => {
	return axios.request({
		url: servicePre + '/getCardPoolGroup',
		data,
		method: 'POST',
	})
}
// 国家关联卡池组详情/编辑/复制查询
export const getCardPoolGroupDetailNew = (data) => {
	return axios.request({
		url: servicePre + '/getCardPoolGroupDetailNew',
		data,
		method: 'POST',
	})
}
/* 国家关联卡池组新增 */
export const addcardPoolMccGroup = data => {
	return axios.request({
		url: servicePre + '/add',
		data,
		method: 'POST',
	})
}
/* 国家关联卡池组修改 */
export const updatecardPoolMccGroup = data => {
	return axios.request({
		url: servicePre + '/update',
		data,
		method: 'POST',
	})
}
// 国家关联卡池组详情查询
export const getCardPoolGroupDetail = (data) => {
	return axios.request({
		url: servicePre + '/getCardPoolGroupDetail',
		data,
		method: 'POST',
	})
}
// 国家关联卡池组详情查询所有卡池
export const getCardPoolGroupDetailPoolData = (data) => {
	return axios.request({
		url: servicePre + '/getCardPoolGroupDetailPoolData',
		data,
		method: 'POST',
	})
}
// 查询卡池组支持的国家
export const getCardPoolMccList = data => {
	return axios.request({
		url: servicePre + '/getCardPoolMcc',
		params:data,
		method: 'get',
	})
}
// 套餐删除接口
export const batchDelete = data => {
	return axios.request({
		url: servicePre + '/batchDelete',
		data,
		method: 'delete',
	})
}
// 套餐查询卡池组
export const getCardPoolList = data => {
	return axios.request({
		url: servicePre + '/packageGetCardPool',
		params:data,
		method: 'get',
	})
}
// 国家关联卡池组修改是否支持热点
export const updateIsSupportedHotspots = data => {
	return axios.request({
		url: servicePre + '/updateIsSupportedHotspots',
		params:data,
		method: 'POST',
	})
}
