import axios from '@/libs/api.request'
// 获取列表
const servicePre = '/api/v1'
export const search = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}
//客户列表
export const getWholesalerList = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}
//编辑
export const edit = data => {
  return axios.request({
    url: servicePre + '/account/operator',
    data,
    method: 'PUT',
  })
}
export const dele = data => {
  return axios.request({
    url: servicePre + '/account/operator',
    data,
    method: 'DELETE',
  })
}

//新增
export const add = data => {
  return axios.request({
    url: servicePre + '/account/operator',
    data,
    method: 'post',
  })
}
//修改状态
export const changeStatus = data => {
  return axios.request({
    url: servicePre + '/account/changeStatus',
    data,
    method: 'PUT',
  })
}
changeStatus
