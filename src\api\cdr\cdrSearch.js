import axios from '@/libs/api.request'
const servicePre = '/stat'

// 运营商查询接口
export const getoperator = data => {
  return axios.request({
    url: '/oms/api/v1/operator',
    params: data,
    method: 'get'
  })
}
// 按套餐周期分页查询
export const period = data => {
  return axios.request({
    url: servicePre + '/cdr/get/by/period',
    params: data,
    method: 'get'
  })
}
// 按套餐周期详情分页查询
export const perioddetail = data => {
  return axios.request({
    url: servicePre + '/cdr/get/by/period/detail',
    params: data,
    method: 'get'
  })
}
// 按套餐周期详情导出
export const exportdetail = data => {
  return axios.request({
    url: servicePre + '/cdr/export/by/period/detail',
    params: data,
    method: 'get',
  })
}
// 按套餐周期导出
export const exportperiod = data => {
  return axios.request({
    url: servicePre + '/cdr/export/by/period',
    params: data,
    method: 'get',
  })
}
// 按套餐使用时间分页查询
export const usedTime = data => {
  return axios.request({
    url: servicePre + '/cdr/get/by/usedTime',
    params: data,
    method: 'get'
  })
}
// 按套餐使用时间详情分页查询
export const usedTimedetail = data => {
  return axios.request({
    url: servicePre + '/cdr/get/by/usedTime/detail',
    params: data,
    method: 'get'
  })
}
// 按套餐使用时间详情导出
export const exportusedTimedetail = data => {
  return axios.request({
    url: servicePre + '/cdr/export/by/usedTime/detail',
    params: data,
    method: 'get',
  })
}
// 按套餐使用时间导出
export const exportusedTime = data => {
  return axios.request({
    url: servicePre + '/cdr/export/by/usedTime',
    params: data,
    method: 'get',
  })
}
// CDR话单 按号码查询 分页查询接口
export const numberCdr = data => {
  return axios.request({
   url: servicePre + '/numberCdr/normal',
   params: data,
   method: 'get'
  })
}
// CDR话单 按号码查询 分页查询接口 详情
export const numberCdrdetail = data => {
  return axios.request({
    url: servicePre + '/numberCdr/detail',
    params: data,
    method: 'get'
  })
}
// CDR话单 按号码查询 导出接口 详情
export const Cdrdetailexport = data => {
  return axios.request({
    url: servicePre + '/numberCdr/detailexport',
    params: data,
    method: 'get',
  })
}
// CDR话单 按号码查询 导出接口
export const Cdrnormalexport = data => {
  return axios.request({
    url: servicePre + '/numberCdr/normalexport',
    params: data,
    method: 'get',
  })
}
// 按号码文件导入接口
export const importFile = data => {
  return axios.request({
    url: servicePre + '/numberCdr/importFile',
    data,
    method: 'post',
    contentType: 'multipart/form-data'
  })
}
// CDR话单“按国家\运营商”分页查询接口
export const operatorCdr = data => {
  return axios.request({
    url: servicePre + '/operatorCdr/normal',
    params: data,
    method: 'get'
  })
}
// CDR话单“按国家\运营商”分页查询接口 详情
export const operatorCdrdetail = data => {
  return axios.request({
    url: servicePre + '/operatorCdr/detail',
    params: data,
    method: 'get'
  })
}
// CDR话单“按国家\运营商”导出接口详情
export const operatorsexport = data => {
  return axios.request({
    url: servicePre + '/operatorCdr/detailexport',
    params: data,
    method: 'get',
  })
}
// CDR话单“按国家\运营商”导出接口
export const opnormalexport = data => {
  return axios.request({
    url: servicePre + '/operatorCdr/normalexport',
    params: data,
    method: 'get',
  })
}

// CDR话单 按号段分页查询接口
export const numbernormal = data => {
  return axios.request({
    url: servicePre + '/numberIntervalCdr/normal',
    params: data,
    method: 'get'
  })
}
// CDR话单 按号段分页详情查询接口
export const numberdetail = data => {
  return axios.request({
    url: servicePre + '/numberIntervalCdr/detail',
    params: data,
    method: 'get'
  })
}
// CDR话单 按号段导出接口详情
export const detailExport = data => {
  return axios.request({
    url: servicePre + '/numberIntervalCdr/detailExport',
    params: data,
    method: 'get',
  })
}
// CDR话单 按号段导出接口
export const normalExport = data => {
  return axios.request({
    url: servicePre + '/numberIntervalCdr/normalExport',
    params: data,
    method: 'get',
  })
}


// CDR话单 按渠道商查询 分页查询接口
export const corpnormal = data => {
  return axios.request({
    url: servicePre + '/corpCdr/normal',
    params: data,
    method: 'get'
  })
}
// CDR话单 按渠道商查询 分页查询接口详情
export const corpdetail = data => {
  return axios.request({
    url: servicePre + '/corpCdr/detail',
    params: data,
    method: 'get'
  })
}
// CDR话单 按渠道商查询 导出详情接口
export const corpdetailexport = data => {
  return axios.request({
    url: servicePre + '/corpCdr/detailexport',
    params: data,
    method: 'get',
  })
}
// CDR话单 按渠道商查询 导出接口
export const corpnormalexport = data => {
  return axios.request({
    url: servicePre + '/corpCdr/normalexport',
    params: data,
    method: 'get',
  })
}