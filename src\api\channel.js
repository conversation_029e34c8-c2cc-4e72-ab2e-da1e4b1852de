import axios from '@/libs/api.request'

const servicePre = '/cms'
// 绑定A2Z运营商查询
export const getChannelA2zOperator = data => {
  return axios.request({
    url:  servicePre +'/channel/getChannelA2zOperator',
    params: data,
    method: 'get'
  })
}
// 根据账户查询corpid
export const searchcorpid = data => {
  return axios.request({
    url:  '/sys/api/v1/channel/userName',
    params: data,
    method: 'get'
  })
}

// 查询渠道商合作模式1
export const channelCooperationMode = data => {
  return axios.request({
    url: 'cms/channel/getChannelCooperationMode',
    params: data,
    method: 'get'
  })
}

// 查询渠道商合作模式2
export const getChannelCooperationModeForLogin = data => {
  return axios.request({
    url: 'cms/channel/getChannelCooperationModeForLogin',
    params: data,
    method: 'get'
  })
}

// 获取押金列表
// export const searchDeposit = data => {
//   return axios.request({
//     url: servicePre + '/channelSelfServer/cash',
//     params: data,
//     method: 'get'
//   })
// }
export const getAccountManagement = data => {
  return axios.request({
    url: servicePre + '/channel/getAccountManagement',
    params: data,
    method: 'get'
  })
}

// 获取押金套餐详情列表
export const mealList = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/cash/package',
    params: data,
    method: 'get'
  })
}
//可购买套餐导出
export const  PackageExport = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/export/packageDetail',
	params: data,
    method: 'get',
	responseType: 'blob'
  })
}
// 获取押金流水详情列表
// export const  streamList = data => {
//   return axios.request({
//     url: servicePre + '/channelSelfServer/cash/record',
//     params: data,
//     method: 'get'
//   })
// }
export const  streamList = data => {
  return axios.request({
    url: servicePre + '/channel/distributors/channelBill',
    data,
    method: 'post'
  })
}
// 渠道商详情账单流水导出 文件下载
export const billflowData = data => {
  return axios.request({
   url: servicePre + '/channel/distributors/channelBill/export',
   data,
   method: 'post',
  })
}
// 获取库存管理列表
export const  stockList = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/task',
    params: data,
    method: 'get'
  })
}
// 获取库存卡片详情列表1
export const  cardList = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/task/detailAlpha',
    params: data,
    method: 'get'
  })
}
// 获取库存卡片详情列表2
export const  detailBeta = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/task/detailBeta',
    params: data,
    method: 'get'
  })
}
// 获取库存卡片详情列表3
export const  detailCharlie = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/task/detailCharlie',
    params: data,
    method: 'get'
  })
}
// 库存管理导出
export const  stockExport = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/support/storeExport',
    params: data,
    method: 'post'
  })
}
// 套餐购买的套餐列表
export const  buymealList = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/package/packageDetail',
    params: data,
    method: 'get'
  })
}
// 套餐单个购买
export const  buymeal = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/package/buySingle',
    data,
    method: 'post'
  })
}
// 获取套餐折扣价
export const  packageprice = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/package/price',
    data,
    method: 'post'
  })
}
// 购买套餐批量文件上传
export const  mealImport = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/package/buyBatch',
    data,
    method: 'post',
	contentType: 'multipart/form-data'
  })
}
// 查看批量文件模板
export const  buyBatch = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/package/buyBatch',
    params: data,
    method: 'get'
  })
}
// 下载成功文件
export const  filedownload = (id) => {
  return axios.request({
    url: servicePre + `/channelSelfServer/package/file/${id}`,
    method: 'get',
	responseType: 'blob'
  })
}
// 下载成功文件
export const  failFiledownload = (id) => {
  return axios.request({
    url: servicePre + `/channelSelfServer/package/failFile/${id}`,
    method: 'get',
	responseType: 'blob'
  })
}
//订单管理列表
export const  orderList = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/order',
    params: data,
    method: 'get'
  })
}
//订单退订
export const  Unsubscribe = (corpid,data) => {
  return axios.request({
    url: servicePre + `/channelSelfServer/order?corpId=${corpid}`,
    data,
    method: 'post'
  })
}
//订单管理月账单列表
export const  monthList = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}
// 订单导出接口
export const  orderExport = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/orderExport',
    data,
    method: 'post'
  })
}
// 服务与支持列表
export const  supportList = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/support/Hcard',
    data,
    method: 'post'
  })
}
// 服务支持套餐详情
export const  supmealList = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/support/packageDetail',
    params: data,
    method: 'get'
  })
}
// 服务支持使用详情
export const  supUseList = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/support/packageUsed',
    params: data,
    method: 'get'
  })
}
// 服务支持激活
export const  doActivation = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/support/active',
    data,
    method: 'post'
  })
}
// 服务支持提前回收
export const  recoveryPackage= data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/support/recoveryPackage',
    params: data,
    method: 'post'
  })
}
// 地址管理获取列表
export const  addressList = data => {
  return axios.request({
    url: '/sys/api/v1/channel',
    params: data,
    method: 'get'
  })
}
// 地址管理新建地址
export const  newAddress = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}
// 地址管理修改
export const  update = data => {
  return axios.request({
    url:'/sys/api/v1/channel',
    data,
    method: 'put'
  })
}
// 地址管理删除
export const  delAddress = data => {
  return axios.request({
    url:'/sys/api/v1/channel',
    params:data,
    method: 'delete'
  })
}
// 地址管理忘记密码
export const  forgetPwd = data => {
  return axios.request({
    url:'/sys/api/v1/channel/password',
    data,
    method: 'put'
  })
}
// 地址管理设置默认
export const  setdefault = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}

// 运营商管理获取列表
export const opsearch = data => {
  return axios.request({
    url:'/oms/api/v1/country/queryCounrtyList',
    params: data,
    method: 'get'
  })
 }

// 获取渠道商流量明细详情列表
export const  trafficList = data => {
  return axios.request({
    url: servicePre + '/channel/getCorpFlowDetail',
    params: data,
    method: 'get'
  })
}

//归属渠道商列表
export const getAttributableChannelList = data => {
  return axios.request({
    url: servicePre +'/channel/getLowerChannel',
    params: data,
    method: 'get'
  })
}

//子渠道商列表
export const getSubChannelList = data => {
  return axios.request({
    url: servicePre +'getSubChannelList',
    data: data,
    method: 'post'
  })
}

// 提交 划拨 子渠道商
export const transferChannel = data => {
  return axios.request({
    url: servicePre + '/channelSelfServer/stockTransfer',
    data: data,
    method: 'put'
  })
}

//获取看板信息
export const  getKanBan = data => {
  return axios.request({
    url: servicePre + '/channel/distributors/getKanbanInfo',
    params: data,
    method: 'get'
  })
}

// 获取短信模板列表
export const getChannelSmsTemplate = data => {
  return axios.request({
    url: servicePre + '/channel/distributors/getChannelSmsTemplate',
    params: data,
    method: 'get'
  })
}

//销售邮箱
export const getSalesMailList = data => {
  return axios.request({
    url: servicePre + '/channel/emailList ',
    params: data,
    method: 'get'
  })
}

//渠道商详情

export const getUserByCorpId = (corpId) => {
  return axios.request({
    url: `/cms/channel/${corpId}`, // 使用模板字符串将 corpId 插入到 URL 中
    method: "get",
  });
};
