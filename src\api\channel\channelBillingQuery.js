import axios from '@/libs/api.request'
const servicePre = '/stat'

// 渠道商账单查询分页接口
export const queryChannelBill = data => {
  return axios.request({
    url: servicePre +'/channelincome/month/getChannelBill',
    data,
    method: 'post'
  })
}

//获取流量计费查询列表
export const getBillInfo = data => {
  return axios.request({
    url: '/charging/atzCharging/getChargingByChannel',
    params: data,
    method: 'get'
  })
}

//获取IMSI费查询列表
export const getImsiFee = data => {
  return axios.request({
    url: 'pms/imsiAmount/getChannelImsiAmount',
    params: data,
    method: 'post'
  })
}

//上传付款证明
export const addPicture = data => {
	return axios.request({
		url: '/cms/IBoss/payBill',
		data,
		method: 'POST',
		contentType: 'multipart/form-data'
	})
}

//撤销
export const revoke = data => {
  return axios.request({
		url: '/cms/IBoss/cancelPayBill/',
    params: data,
    method: 'get'
  })
}

// 账单明细下载
export const exportBillFile = data => {
  return axios.request({
    url: '/cms/channel/channelExport',
    data: data,
    method: 'post'
  })
}

//充值记录——分页查询
export const getTopUpRecord = data => {
  return axios.request({
    url: 'cms/channel/deposit/record',
    data,
    method: 'post'
  })
}


// 按照条件导出充值记录
export const exportTopUpRecords = data => {
  return axios.request({
    url: 'cms/channel/deposit/recordExport',
    data,
    method: 'post',
    responseType: 'blob'
  })
}

// 按照条件导出历史记录
export const exportHistoryRecords = data => {
  return axios.request({
    url: servicePre + '/channelincome/month/exportChannelBill',
    data,
    method: 'post',
    responseType: 'blob'
  })
}
