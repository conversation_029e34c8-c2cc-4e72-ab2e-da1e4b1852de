import axios from '@/libs/api.request'

// const servicePre = '/cms'
const servicePre = 'cms/cms-channel-market-billflow'
// 营销账户详情-分页查询
export const getMarketingAaccountDetails = data => {
  return axios.request({
    url:  'cms/cms_channel_marketing_rebate/getMarketingDeatil',
    data,
    method: 'post'
  })
}

// 营销账户流水-分页查询
export const selectMarketingAccountFlow = data => {
  return axios.request({
    url: servicePre + '/selectMarketingAccountFlow',
    params: data,
    method: 'get'
  })
}

// 营销活动返利流水详情-分页查询
export const selectMarketingAccountFlowRebate = data => {
  return axios.request({
    url: servicePre + '/selectMarketingAccountFlowRebate',
    params: data,
    method: 'get'
  })
}

// 营销账户流水-导出
export const MarketingAccountFlowOut = data => {
  return axios.request({
   url: servicePre + '/MarketingAccountFlowOut',
   params: data,
   method: 'get',
  })
}

// 营销活动返利流水详情-导出
export const MarketingAccountFlowOutRebate = data => {
  return axios.request({
   url: servicePre + '/MarketingAccountFlowOutRebate',
   params: data,
   method: 'get',
  })
}
