import axios from '@/libs/api.request'
const servicePre = '/pms/api/v1/channelBuiltPackage'

// 渠道商自建套餐列表查询
export const getList = data => {
  return axios.request({
    url:  servicePre +'/getChannelList',
    data,
    method: 'post'
  })
}
// 渠道商自建套餐新增
export const addItem = data => {
  return axios.request({
    url:  servicePre +'/add',
    data,
    method: 'post',
	// contentType: 'multipart/form-data'
  })
}
// 渠道商自建套餐修改
export const updateItem= data => {
  return axios.request({
    url:  servicePre +'/update',
    data,
    method: 'post',
  })
}
// 套餐删除接口
export const batchDelete= data => {
  return axios.request({
    url:  servicePre +'/batchDelete',
    data,
    method: 'delete'
  })
}
//套餐获取upcc速度模板
export const getUpccList= data => {
  return axios.request({
    url:  '/pms/api/v1/upccTemplate/packageGetUpcc',
    params: data,
    method: 'get'
  })
}

//判断渠道商是否新增\删除\修改\复制按钮
export const judgeChannelCreatePackage= data => {
  return axios.request({
    url:  '/cms/channel/distributors/judgeChannelCreatePackage',
    params: data,
    method: 'get'
  })
}

// 套餐管理-> 加油包列表查询
export const getPageList = data => {
  return axios.request({
    url: '/pms/refuelPackage/channelSelf/get',
    data,
    method: 'post'
  })
}

// 套餐管理-> 加油包删除接口
export const deleteItem= (refuelId, cooperationMode) => {
  return axios.request({
    url: `/pms/refuelPackage/channelSelf/del/${refuelId}/${cooperationMode}`,
    method: 'delete'
  })
}

//渠道自服务-套餐管理-新增加油包
export const addRefuelingBag = data => {
  return axios.request({
    url: '/pms/refuelPackage/channelSelf/add',
    data,
    method: 'post',
  })
}