import axios from "@/libs/api.request";
const cmsServicePre = "/cms";
const pmsServicePre = "/pms/api/v1";
const omsServicePre = "/oms/api/v1";
const rmsServicePre = "/rms/api/v1";

// 编辑页面获取国家列表
export const getCountryList = (data) => {
  return axios.request({
    url: omsServicePre + "/country/queryCounrtyList",
    method: "get",
    data,
  });
};

// 查询渠道商列表||获取已选渠道商列表
export const getChannelList = (params) => {
  return axios.request({
    url: cmsServicePre + "/channel/pagelist",
    method: "post",
    data: params
  });
};

//查询卡池信息
// {{pms}}/api/v1/cardPool/getCardPoolinfoBymcc?mcc=460
export const getCardPoolInfoByMcc = (params) => {
  return axios.request({
    url: pmsServicePre + "/cardPool/getCardPoolinfoBymcc",
    method: "get",
    params
  });
};

// 新增特殊国家规则
export function createSpecialCountryRule(data) {
  return axios.request({
    url:pmsServicePre+ '/cardpoolSpecial/addSpecial',
    method: 'post',
    data
  })
}

// 更新特殊国家规则
export function updateSpecialCountryRule(data) {
  return axios.request({
    url: pmsServicePre + '/cardpoolSpecial/editSpecial',
    method: 'post',
    data
  })
}

// 获取特殊国家规则详情
export function getSpecialCountryRuleDetail(data) {
  return axios.request({
    url: pmsServicePre + '/cardpoolSpecial/pagelist',
    method: 'post',
    data
  })
}

//获取供应商列表
export const getSupplierList = data => {
  return axios.request({
    url: rmsServicePre + '/supplier/query',
    method: 'get',
    params: data
  })
}