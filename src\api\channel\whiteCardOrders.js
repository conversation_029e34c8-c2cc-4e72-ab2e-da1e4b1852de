import axios from '@/libs/api.request'
const servicePre = '/order/blankCardOrder'

// 获取说明文字
export const getDescription = data => {
  return axios.request({
    url: servicePre +'/blankCardIllustrate',
    data,
    method: 'get'
  })
}

//获取列表
export const getList = data => {
  return axios.request({
    url: servicePre + '/getBlankCardOrders',
    data,
    method: 'post'
  })
}


//新增订单
export const addOrder = data => {
  return axios.request({
    url: servicePre + '/addCardOrders',
    data,
    method: 'post'
  })
}

//撤销
export const revoke = id => {
  return axios.request({
		url: servicePre + `/cancelOrder/${id}/`,
		method: 'put',
  })
}

//下载失败文件
export const downloadFailedFile = data => {
	return axios.request({
		url: servicePre + '/downloadFailedFile',
		data,
		method: 'post',
	})
}

//下载号码列表
export const downloadNumberList = data => {
	return axios.request({
		url: servicePre + '/blankCardExport',
		params: data,
		method: 'post',
	})
}

//下载Invoice
export const downloadInvoice = data => {
	return axios.request({
		url: servicePre + '/downloadInvoice',
		data,
		method: 'post',
	})
}

//上传付款证明
export const addPicture = data => {
	return axios.request({
		url: servicePre + '/uploadPaymentProof',
		data,
		method: 'POST',
		contentType: 'multipart/form-data'
	})
}

//协议内容
export const getContent = data => {
  return axios.request({
    url: servicePre +'/getProtocol',
    data,
    method: 'get'
  })
}
