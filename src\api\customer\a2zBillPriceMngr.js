import axios from '@/libs/api.request'

const servicePre = '/charging/atzCharging'

/*A~Z计费价格管理 */

// 分页查询A~Z价格管理
export const queryA2ZBillPrice = data => {
  return axios.request({
    url: servicePre + '/query',
    data,
    method: 'post'
  })
}

// 新增规则
export const addA2ZBillPriceRule= data => {
  return axios.request({
    url: servicePre + '/add',
    data: data,
    method: 'post'
  })
}

// 修改
export const updateA2ZBillPriceRule= data => {
  return axios.request({
    url: servicePre + '/edit?isCover=' + data.isCover,
    data: data,
    method: 'post'
  })
}

// 删除规则
export const delA2ZBillPriceRule = data => {
  return axios.request({
    url: servicePre + '/del',
    params: data,
    method: 'post'
  })
}

/* 审核规则 */
export const approveA2ZRule = data => {
	return axios.request({
		url: servicePre + '/audit',
    params: data,
		method: 'post',
	})
}

/* ----------------------------------- 规则详情 ----------------------------------- */
// 规则分页查询
export const queryRuleDeatil = data => {
  return axios.request({
    url: servicePre + '/queryDetail',
    data,
    method: 'post'
  })
}

// 导出规则详情
export const exportRuleDeatil = data => {
  return axios.request({
    url: servicePre + '/export',
    responseType: 'blob',
    data,
    method: 'post',
  },)
}

// 导入规则
export const uploadRule = data => {
  return axios.request({
    url: servicePre + '/imports',
    data,
    method: 'post',
  })
}

//单个修改、删除、新增规则
export const singleUpdateA2ZBillPriceRule= data => {
  return axios.request({
    url: servicePre + '/edit',
    data: data,
    method: 'post'
  })
}

// 获取国家+运营商
export const getMccOperators = data => {
  return axios.request({
    url: '/oms/api/v1/country/getMccOperators',
    params: data,
    method: 'get',
  },)
}

