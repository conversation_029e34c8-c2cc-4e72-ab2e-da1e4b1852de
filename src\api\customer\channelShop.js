import axios from '@/libs/api.request'

const servicePre = 'pms'
const servicePre_cms = 'cms'


/*渠道商 */

// 分页查询渠道商信息
export const getDistributorsList = data => {
  return axios.request({
    url: servicePre_cms + '/channel/distributors/detail',
    data: data,
    method: 'post'
  })
}

// 渠道商详情查询接口
export const getDistributorsDetail = data => {
  return axios.request({
    url: servicePre_cms + '/channel/distributors/info',
    params: data,
    method: 'get'
  })
}

// 套餐购买记录查询接口（分页）
export const getDistributorsPurchaseRecord = data => {
  return axios.request({
    url: servicePre_cms + '/channel/distributors/purchase/record',
    data: data,
    method: 'post'
  })
}



// 删除渠道商信息
export const deleteDistributors = data => {
  return axios.request({
    url: servicePre_cms + '/channel/distributors/delete',
    data: data,
    method: 'delete'
  })
}

// 套餐购买记录导出接口
export const exportPurchaseRecord = data => {
  return axios.request({
    url: servicePre_cms + '/channel/distributors/purchase/record/export',
    params: data,
    method: 'get',
    responseType: 'blob'
  })
}

// 渠道商充值接口
export const rechargeDistributors = data => {
  return axios.request({
    url: servicePre_cms + '/channel/distributors/recharge',
    data: data,
    method: 'post'
  })
}
// 充值记录查询接口
export const getDistributorsRecord = data => {
  return axios.request({
    url: servicePre_cms + '/channel/distributors/record',
    data: data,
    method: 'post'
  })
}

// 充值记录导出接口
export const exportDistributorsRecord = data => {
  return axios.request({
    url: servicePre_cms + '/channel/distributors/record/export/'+data.corpId,
    method: 'get',
    responseType: 'blob'
  })
}

// 渠道商酬金详情导出接口
export const exportRemunerate = data => {
  return axios.request({
    url: servicePre_cms + '/channel/distributors/remunerate/export',
    params: data,
    method: 'post',
    responseType: 'blob'
  })
}

// 根据id获取厂商
export const getStoreByCorpId = data => {
  return axios.request({
    url: servicePre_cms + '/channel/{corpId}',
    params: data,
    method: 'get'
  })
}
// 查询渠道用户客户信息
export const findCustermInfo = data => {
  return axios.request({
    url: servicePre_cms + '/channel/searchOne',
    params: data,
    method: 'get'
  })
}
// 获取合作商下的套餐
export const getPackages = data => {
  return axios.request({
    url: servicePre + '/packageGroup/purchase',
    params: data,
    method: 'get'
  })
}
// 新增渠道用户客户信息
export const addCustermInfo= data => {
  return axios.request({
    url: servicePre_cms + '/channel/newChannel',
    data: data,
    method: 'post'
  })
}
// 新增渠道用户客户信息
export const UpdateCustermInfo= data => {
  return axios.request({
    url: servicePre_cms + '/channel/updateChannel',
    data: data,
    method: 'put'
  })
}
// 根据id获取厂商
export const approvalChannel = data => {
  return axios.request({
    url: servicePre_cms + '/channel/approvalChannel',
    params: data,
    method: 'put'
  })
}


// mock测试

// export const addDistributors = data => {
//   return axios.request({
//     url: servicePre + '/add',
//     params: data,
//     method: 'post'
//   })
// }
// export const updateDistributors = data => {
//   return axios.request({
//     url: servicePre + '/update',
//     params: data,
//     method: 'put'
//   })
// }
// export const deleteDistributors = data => {
//   return axios.request({
//     url: servicePre + '/delete',
//     params: data,
//     method: 'delete'
//   })
// }



/*套餐组 */

// 获取合作商分页查询接口
// export const queryPackageGroupRelation = data => {
//   return axios.request({
//     url: servicePre + '/packageGroup/queryPackageGroupRelation',
//     params: data,
//     method: 'get'
//   })
// }

// 获取套餐组列表
export const queryPackageGroupRelation = data => {
  return axios.request({
    url: servicePre + '/packageGroup/queryPackageGroupRelation',
    params: data,
    method: 'get'
  })
}

// 套餐组详情
export const queryPackageGroupDetail = data => {
  return axios.request({
    url: servicePre + '/packageGroup/queryPackageGroupDetail',
    params: data,
    method: 'get'
  })
}
// 可购套餐组全量
export const getAllpurchasePackageGroup = data => {
  return axios.request({
    url: servicePre + '/packageGroup/purchase',
    params: data,
    method: 'get'
  })
}

//可购买套餐组 非自建套餐
export const getPurchasePackageGroup = data => {
  return axios.request({
    url: servicePre + '/packageGroup/purchasePart',
    params: data,
    method: 'get'
  })
}

// 可选择套餐
export const getChoosePackageList = data => {
  return axios.request({
    url: servicePre + '/packageGroup/queryPackageList',
    params: data,
    method: 'get'
  })
}
// 更新套餐组
export const updatePackageGroup = data => {
  return axios.request({
    url: servicePre + '/update',
    params: data,
    method: 'put'
  })
}
// 删除套餐组
export const deletePackageGroup = data => {
  return axios.request({
    url: servicePre + '/packageGroup/deleteBatchPackageGroup',
    data: data,
    method: 'delete'
  })
}
// 删除套餐组(单个)
export const deletePackageGroupSingle = data => {
  return axios.request({
    url: servicePre + '/packageGroup/deletePackageGroup',
    params: data,
    method: 'delete'
  })
}
// 新增套餐组
export const addNewPackageGroup = data => {
  return axios.request({
    url: servicePre + '/packageGroup/newPackageGroup',
    data: data,
    method: 'post'
  })
}
// 修改套餐组
export const updateNewPackageGroup = data => {
  return axios.request({
    url: servicePre + '/packageGroup/updatePackageGroup',
    data: data,
    method: 'put'
  })
}
// 审核套餐组
export const approvalPackageGroup = data => {
  return axios.request({
    url: servicePre + '/packageGroup/approvalPackageGroup',
    params: data,
    method: 'put'
  })
}
// 上传套餐组文件
export const createByFile = data => {
  return axios.request({
    url: servicePre + '/packageGroup/create/byFile',
    data,
    method: 'post',
	contentType: 'multipart/form-data'
  })
}
// 套餐组导出
export const exportFile = data => {
  return axios.request({
   url: servicePre + '/packageGroup/packageGroupDetailExport',
   params: data,
   method: 'get',
   responseType: 'blob'
  })
}

// 获取渠道商账单流水详情列表
export const  billflowList = data => {
  return axios.request({
    url: servicePre_cms + '/channel/distributors/channelBill',
    data,
    method: 'post'
  })
}

// 渠道商详情账单流水导出 文件下载
export const billflowData = data => {
  return axios.request({
   url: servicePre_cms + '/channel/distributors/channelBill/export',
   data,
   method: 'post',
  })
}

// 获取渠道商流量明细列表
export const  trafficList = data => {
  return axios.request({
    url: servicePre_cms + '/channel/getCorpFlowDetail',
    params: data,
    method: 'get'
  })
}

// 渠道商详情账单流水导出 文件下载
export const exportTraffic = data => {
  return axios.request({
   url: servicePre_cms + '/channel/corpFlowDetailExport',
   params: data,
   method: 'get',
  })
}

//A2Z停用
export const a2zStop = data => {
  return axios.request({
   url: servicePre_cms + '/channel/distributors/card/suspend',
    params: data,
   method: 'get',
  })
}

//A2Z恢复
export const a2zRecover = data => {
  return axios.request({
   url: servicePre_cms + '/channel/distributors/card/recover',
    params: data,
   method: 'get',
  })
}
