import axios from '@/libs/api.request'

const servicePre = '/cms'

// 获取合作商分页查询接口
export const getPage = data => {
  return axios.request({
    url: servicePre + '/cooperation',
    params: data,
    method: 'get'
  })
}
// 供应商合作商新增接口
export const add = data => {
  return axios.request({
    url: servicePre + '/cooperation',
    data,
    method: 'post'
  })
}
// 供应商合作商编辑接口
export const update = data => {
  return axios.request({
    url: servicePre + '/cooperation/updateCooperation',
    data,
    method: 'post'
  })
}
// 供应商合作商审核接口
export const check = data => {
  return axios.request({
    url: servicePre + '/cooperation',
    params: data,
    method: 'put'
  })
}
// 供应商合作商删除接口
export const del = data => {
  return axios.request({
    url: servicePre + '/cooperation',
    data,
    method: 'delete'
  })
}
// 获取合作商使用明细分页查询接口
export const getMorePage = data => {
  return axios.request({
    url: servicePre + '/cooperation/detail',
    params: data,
    method: 'get'
  })
}
// 合作商使用明细导出接口
export const exportMore = data => {
  return axios.request({
    url: servicePre + '/cooperation/derive',
    params: data,
    responseType: 'blob',
    method: 'get'
  })
}
// 套餐列表查询
export const getPackageList = data => {
  return axios.request({
    url: '/pms/api/v1/package/getList',
    data,
    method: 'post'
  })
}
//资费详情
export const getDetail = data => {
  return axios.request({
    url: servicePre + '/cooperation/getCooperationMoney',
    params: data,
    method: 'get'
  })
}
//查询所有国家
export const getCountryList = data => {
  return axios.request({
    url: '/oms/api/v1/country/queryCounrtyList',
    method: 'get'
  })
}
