import axios from '@/libs/api.request'

const servicePre = '/charging'

/*成本价格管理 */

// 分页查询成本价格管理
export const queryCostPricing = data => {
  return axios.request({
    url: servicePre + '/cost/query',
    data,
    method: 'post'
  })
}

// 新增规则
export const addCostPricing= data => {
  return axios.request({
    url: servicePre + '/cost/add',
    data: data,
    method: 'post'
  })
}

// 修改
export const updateCostPricing= data => {
  return axios.request({
    url: servicePre + '/cost/edit?isCover='+ data.isCover,
    data: data,
    method: 'post'
  })
}

//单个修改、删除、新增规则
export const singleUpdateCostPricing= data => {
  return axios.request({
    url: servicePre + '/cost/edit',
    data: data,
    method: 'post'
  })
}

// 删除规则
export const delCostPricing = data => {
  return axios.request({
    url: servicePre + '/cost/del',
    params: data,
    method: 'post'
  })
}

/* 审核规则 */
export const approveCostPricing = data => {
	return axios.request({
		url: servicePre + '/cost/audit',
    params: data,
		method: 'post',
	})
}

// 全量同步
export const allSync = data => {
	return axios.request({
		url: servicePre + '/cost/allSync',
    data,
		method: 'post',
	})
}

// 全量审批
export const allAudit = data => {
	return axios.request({
		url: servicePre + '/cost/allAudit',
    data,
		method: 'post',
	})
}

//查询国家下的运营商以及tadig接口
export const getCountryInfo = data => {
	return axios.request({
		url: '/oms/api/v1/country/getCountryInfo',
    data,
		method: 'post',
	})
}
/* ----------------------------------- 详情 ----------------------------------- */
// 规则分页查询
export const queryCostPricingDeatil = data => {
  return axios.request({
    url: servicePre + '/cost/queryDetail',
    data,
    method: 'post'
  })
}

// 导出规则详情
export const exportCostPricingDeatil = data => {
  return axios.request({
    url: servicePre + '/cost/export',
    responseType: 'blob',
    data,
    method: 'post',
  },)
}

// 导入规则
export const uploadRule = data => {
  return axios.request({
    url: servicePre + '/cost/imports',
    data,
    method: 'post',
  })
}

//查看是否能修改计费方式的资源供应商
export const modifyBilling = data => {
  return axios.request({
    url: servicePre + '/cost/selectCostType',
    data,
    method: 'post',
  },)
}

// 获取国家+运营商
export const getMccOperators = data => {
  return axios.request({
    url: '/oms/api/v1/country/getMccOperators',
    params: data,
    method: 'get',
  },)
}

