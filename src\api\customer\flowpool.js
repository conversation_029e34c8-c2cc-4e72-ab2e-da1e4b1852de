import axios from '@/libs/api.request'
// 获取列表
const servicePre = '/cms'
// 客户管理-流量池管理分页查询接口
export const  getPage= data => {
  return axios.request({
    url: servicePre + '/flowPool/getCorpList',
    params: data,
    method: 'get'
  })
}
// 客户管理-卡号列表查询分页
export const  Cardlist= data => {
  return axios.request({
    url: servicePre + '/flowPool/getCard',
    params: data,
    method: 'get'
  })
}
// 客户管理-卡号列表导出接口
export const  exportCardlist= data => {
  return axios.request({
    url: servicePre + '/flowPool/outCardList',
    params: data,
    method: 'post'
  })
}
// 客户管理-流量池列表分页查询接口
export const  flowlist= data => {
  return axios.request({
    url: servicePre + '/flowPool/getChannelFlowList',
    data,
    method: 'post'
  })
}
// 客户管理-流量池列表导出接口
export const  exportflow= data => {
  return axios.request({
    url: servicePre + '/flowPool/ChannelFlowListOut',
    data,
    method: 'post'
  })
}
// 客户管理-流量池充值
export const  recharge= data => {
  return axios.request({
    url: servicePre + '/flowPool/rechargeFlow',
    params: data,
    method: 'put'
  })
}
// 客户管理-iccid分页查询接口
export const  channelIccidlist= data => {
  return axios.request({
    url: servicePre + '/flowPool/getICCID',
    params: data,
    method: 'get'
  })
}
// 客户管理-iccid列表导出接口
export const  exporticcid= data => {
  return axios.request({
    url: servicePre + '/flowPool/outICCID',
    params: data,
    method: 'post'
  })
}
// 客户管理-iccid单个导入接口
export const  Singleimport= data => {
  return axios.request({
   url: servicePre + '/channelCard/flowPoolAddCard ',
   data,
   method: 'post'
  })
}
// 客户管理-iccid批量导入接口
export const  Batchimport= data => {
  return axios.request({
    url: servicePre + '/channelCard/flowPoolAddCardBatch',
    data,
    method: 'post',
    contentType: 'multipart/form-data'
  })
}
// 客户管理-iccid删除接口
export const  Deleteiccid= data => {
  return axios.request({
    url: servicePre + '/flowPool/removeCards',
    data,
    method: 'post'
  })
}

// 客户管理-卡号移除接口
export const  DeleteCard= data => {
  return axios.request({
    url: servicePre + '/flowPool/ChannelRemoveCards',
    data,
    method: 'post'
  })
}

// 客户管理-使用记录分页查询接口
export const  UsageList= data => {
  return axios.request({
    url: servicePre + '/flowPool/getFlowpoolUseRecord',
    params: data,
    method: 'get'
  })
}
// 使用记录导出接口
export const  exportUsageList= data => {
  return axios.request({
    url: servicePre + '/flowPool/outFlowpoolUseRecord',
    params: data,
    method: 'post'
  })
}
// 使用记录详情分页查询接口
export const  UsageListdetails= data => {
  return axios.request({
    url: servicePre + '/flowPool/getCardUseDetailRecord',
    params: data,
    method: 'get'
  })
}
// 使用记录详情导出查询接口
export const  exportUsagedetails= data => {
  return axios.request({
    url: servicePre + '/flowPool/outFlowPoolDetailRecord',
    params: data,
    method: 'post'
  })
}

//任务列表查看
export const  getTaskList= data => {
  return axios.request({
    url: servicePre + '/flowPool/getIccidImportTaskList',
	params: data,
    method: 'get'
  })
}
//任务文件下载
export const  exportTaskList= data => {
  return axios.request({
    url: servicePre + '/flowPool/getIccidImportTaskFile',
	params: data,
    method: 'get',
	responseType: 'blob'
  })
}


/* 流量卡暂停 */
export const stopStatusVCard = data => {
  return axios.request({
    url: servicePre + '/flowPool/card/pause',
    params: data,
    method: 'get'
  })
}
/* 流量卡恢复 */
export const recoverStatusVCard =data => {
  return axios.request({
    url: servicePre + '/flowPool/card/resume',
    params: data,
    method: 'get'
  })
}

// 卡管理
export const  cardUpadate = data => {
  return axios.request({
    url: servicePre + '/flowPool/updateICCID',
    data,
    method: 'post'
  })
}