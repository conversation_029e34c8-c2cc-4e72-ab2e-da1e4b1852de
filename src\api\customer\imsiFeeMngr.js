import axios from '@/libs/api.request'

const servicePre = 'pms/imsiAmount'

/*A~Z计费价格管理 */

// 分页查询A~Z价格管理
export const queryImsiFee = data => {
  return axios.request({
    url: servicePre + '/getImsiAmount',
    params: data,
    method: 'get'
  })
}

// 详情规则
export const detailImsiFee= data => {
  return axios.request({
    url: servicePre + '/detail',
    params: data,
    method: 'get'
  })
}

// 新增规则
export const addImsiFee= data => {
  return axios.request({
    url: servicePre + '/new',
    data: data,
    method: 'post'
  })
}

// 修改规则
export const updateImsiFee= data => {
  return axios.request({
    url: servicePre + '/update',
    data: data,
    method: 'put'
  })
}

// 删除规则
export const delImsiFee= data => {
  return axios.request({
    url: servicePre + '/delete',
    params: data,
    method: 'delete'
  })
}
