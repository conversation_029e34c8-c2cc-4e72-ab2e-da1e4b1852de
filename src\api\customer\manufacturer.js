import axios from '@/libs/api.request'

const servicePre = '/cms/api/v1'

// 获取终端厂商分页查询接口
export const getPage = data => {
  return axios.request({
    url: servicePre + '/terminal/pages',
    params: data,
    method: 'get'
  })
}
// 获取终端厂商详情查询接口
// export const getRule = data => {
//   return axios.request({
//     url: servicePre + '/terminal/settleRule/query',
//     params: data,
//     method: 'get'
//   })
// }
//分页查询终端厂商结算表
export const getRule = data => {
  return axios.request({
    url: servicePre + '/terminal/settleRule/queryList',
    params: data,
    method: 'get'
  })
}
// 终端厂商新增接口
export const add = data => {
  return axios.request({
    url: servicePre + '/terminal',
    data,
    method: 'post'
  })
}
// 终端厂商编辑接口
export const update = (id,data) => {
  return axios.request({
    url: servicePre + '/terminal/'+id,
    data,
    method: 'put'
  })
}
// 终端厂商审核接口
export const check = (id,data) => {
  return axios.request({
    url: servicePre + '/terminal/audit/'+id,
    params: data,
    method: 'put'
  })
}
// 终端厂商删除接口
export const del = data => {
  return axios.request({
    url: servicePre + '/terminal',
    data,
    method: 'delete'
  })
}
// 获取终端厂商使用明细分页查询接口
export const getMorePage = data => {
  return axios.request({
    url: servicePre + '/terminal/details',
    params: data,
    method: 'get'
  })
}
// 终端厂商使用明细导出接口
export const exportMore = data => {
  return axios.request({
    url: servicePre + '/terminal/details/export',
    params: data,
    responseType: 'blob',
    method: 'get'
  })
}
// 新增套餐/方向接口
export const addItem = data => {
  return axios.request({
    url: servicePre + '/terminal/settleRule/add',
    data,
    method: 'post'
  })
}
// 套餐/方向编辑接口
export const updateItem = data => {
  return axios.request({
    url: servicePre + '/terminal/settleRule/update',
    data,
    method: 'put'
  })
}
// 查询删除是否有风险
export const checkDelete = data => {
  return axios.request({
    url: '/pms/api/v1/cardPool/checkPackage',
    params: data,
    method: 'get'
  })
}
// 删除套餐/方向接口
export const deleteItem = data => {
  return axios.request({
    url: servicePre + '/terminal/settleRule/delete/'+data,
    method: 'delete'
  })
}

// 多个删除终端厂商结算表
export const deleteBatch = data => {
  return axios.request({
    url: servicePre + '/terminal/settleRule/deleteBatch',
	data,
    method: 'post'
  })
}

// 企业流量账单分页查询接口
export const getflow = data => {
  return axios.request({
    url: '/stat/cdr/flow/get/list',
    params: data,
    method: 'get'
  })
} 
// 企业流量账单导出接口
export const exportflow = data => {
  return axios.request({
    url: '/stat/cdr/flow/export/details',
    params: data,
    method: 'get'
  })
}

//企业流量账单明细导出接口
export const exportdetail = data => {
  return axios.request({
    url: '/stat/cdr/flow/export/info',
    params: data,
    method: 'get'
  })
} 
//企业流量账单明细分页查询接口
export const getflowdetail = data => {
  return axios.request({
    url: '/stat/cdr/flow/get/info',
    params: data,
    method: 'get'
  })
}

// 企业流量导出接口
export const exportlist = data => {
  return axios.request({
    url: '/stat/cdr/flow/export/list',
    params: data,
    method: 'get'
  })
}
//企业流量账单详情分页查询接口
export const flowdetails = data => {
  return axios.request({
    url: '/stat/cdr/flow/get/details',
    params: data,
    method: 'get'
  })
}
//企业流量账单明细导出接口(总明细)
export const exportall = data => {
  return axios.request({
    url: '/stat/cdr/flow/export/info/all',
    params: data,
    method: 'get'
  })
}
// 添加修改MCC
export const updateMccInput = data => {
  return axios.request({
    url: servicePre + '/terminal/plmnlist/update',
    data,
    method: 'post',
	headers: {
	  'Content-Type': 'application/json;charset=UTF-8'
	}
  })
}
// 添加MCC文件
export const addMccFile = data => {
  return axios.request({
    url: servicePre + '/terminal/plmnlist/createByFile',
    data,
    method: 'post',
	contentType: 'multipart/form-data'
  })
}
// 查询plmnlist
export const getPlmnlistDetail = data => {
  return axios.request({
    url: servicePre + '/terminal/plmnlist/get',
    params: data,
    method: 'get'
  })
}