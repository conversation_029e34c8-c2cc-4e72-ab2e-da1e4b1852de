import axios from '@/libs/api.request'

const servicePre = '/cms/api/v2/postpaid'
// 后付费渠道分页查询
export const searchPostpaid = data => {
  return axios.request({
    url: servicePre + '/queryChannel',
    params: data,
    method: 'get'
  })
}
// 后付费渠道新增  
export const PostpaidAdd = data => {
  return axios.request({
    url: servicePre + '/newChannel',
    data,
    method: 'post'
  })
}
// 后付费订单新增
export const  OrderAdd = data => {
  return axios.request({
    url: servicePre + '/order',
    data,
    method: 'post',
	contentType: 'multipart/form-data'
  })
}
// 批量删除
export const  BatchDelete = data => {
  return axios.request({
    url: servicePre + '/batchDeleteChannel',
    data,
    method: 'delete'
  })
}
// 修改
export const  PostpaidUpdate = data => {
  return axios.request({
    url: servicePre + '/updateChannel',
    data,
    method: 'post'
  })
}
// 单个删除
export const  PostpaidDelete = data => {
  return axios.request({
    url: servicePre + '/deleteChannel',
    params: data,
    method: 'delete'
  })
}
// 审批
export const  Approval = data => {
  return axios.request({
    url: servicePre + '/channelCheck',
    params: data,
    method: 'put'
  })
}
// 流量本月账单分页查询
export const  queryFlowBills = data => {
  return axios.request({
    url: servicePre + '/queryFlowBills',
    params: data,
    method: 'get'
  })
  }
// 套餐本月账单分页查询
export const  queryPackageBills = data => {
  return axios.request({
    url: servicePre + '/queryPackageBills',
    params: data,
    method: 'get'
  })
}
// 套餐付费详情分页查询
export const  searchmeal = data => {
  return axios.request({
    url: servicePre + '/queryPackageDetails',
    params: data,
    method: 'get'
  })
}
// 流量付费详情分页查询
export const  queryFlowDetails = data => {
  return axios.request({
    url: servicePre + '/queryFlowDetails',
    params: data,
    method: 'get'
  })
}
//月账单导出
export const  monthExport = data => {
  return axios.request({
    url: servicePre + '/detailDownload',
    params: data,
    method: 'get',
	responseType: 'blob'
  })
}

// 获取可以订购套餐
export const  queryPackageList = data => {
  return axios.request({
    url: servicePre + '/queryPackageListForChannel',
    params: data,
    method: 'get'
  })
}

// 查询订单可用套餐接口
export const  queryOrderlList = data => {
  return axios.request({
    url: servicePre + '/queryPackageListForOrder',
    params: data,
    method: 'get'
  })
}
// 查询订单后付费渠道
export const  queryChannelList = data => {
  return axios.request({
    url: servicePre + '/queryChannelList',
    params: data,
    method: 'get'
  })
}
// 编辑查询详情
export const  queryDetailForUpdate = data => {
  return axios.request({
    url: servicePre + '/queryDetailForUpdate',
    params: data,
    method: 'get'
  })
}
// 运营商管理获取列表
export const opsearch = data => {
  return axios.request({
    url:'/oms/api/v1/country/queryCounrtyList',
    params: data,
    method: 'get'
  })
 }