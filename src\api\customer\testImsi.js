import axios from '@/libs/api.request'

const servicePre = '/cms/channel'

// 获取测试IMSI分页查询接口
export const getPage = data => {
  return axios.request({
    url: servicePre + '/testImsi/query',
    params: data,
    method: 'get'
  })
}
// 测试IMSI新增接口
export const add = data => {
  return axios.request({
    url: servicePre + '/testImsi/add',
    params: data,
    method: 'post'
  })
}
// 终端厂商删除接口
export const del = data => {
  return axios.request({
    url: servicePre + '/testImsi/delete',
    data,
    method: 'delete'
  })
}
