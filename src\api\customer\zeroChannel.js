import axios from '@/libs/api.request'

const servicePre = 'cms'

/*零级渠道商 */

// 分页查询零级渠道商
export const queryZeroChannel = data => {
  return axios.request({
    url: servicePre + '/channel/getTopChannel',
    params: data,
    method: 'get'
  })
}

// 删除零级渠道商
export const deleteZeroChannel = data => {
  return axios.request({
    url: servicePre + '/channel/deleteTopChannel',
    params: data,
    method: 'delete'
  })
}

// 分页查询子渠道商
export const querySubChannel = data => {
  return axios.request({
    url: servicePre + '/channel/getCorpList',
    params: data,
    method: 'get'
  })
}

// 新增子渠道商	
export const subChannel= data => {
  return axios.request({
    url: servicePre + '/channel/newTopChannel',
    data,
    method: 'post',
  })
}
// 修改子渠道商	
export const UpdateSubChannel= data => {
  return axios.request({
    url: servicePre + '/channel/updateTopChannel',
    data,
    method: 'put',
  })
}