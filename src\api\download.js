import axios from '@/libs/api.request'
const servicePre='/sys/api/v1';

// 下载列表查询接口
export const queryTask = data => {
  return axios.request({
    url: '/sys/api/v1/sync/queryTask',
    params: data,
    method: 'get'
  })
}

// 下载列表查询接口
export const getDownloadFileUrl = (taskId, corpId) => {
	return axios.baseUrl + servicePre +`/sync/download/${taskId}/${corpId}`
}

// 下载导出接口
export const download = (id,corpId) => {
  return axios.request({
    url: servicePre +`/getList/download/${id}/${corpId}`,
    method: 'get',
	responseType: 'blob'
  })
}