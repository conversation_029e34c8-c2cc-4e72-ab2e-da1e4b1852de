import axios from '@/libs/api.request'
// 获取列表
const servicePre = '/charging/accountingPeriod'

// 账期管理查询接口
export const getAcountingPeriod = data => {
  return axios.request({
    url: servicePre + '/query',
    data,
    method: 'post'
  })
}

// 账期新增
export const addItem = data => {
  return axios.request({
    url: servicePre + '/add',
    data,
    method: 'post',
  })
 }

// 账期删除
export const delItem = (data) => {
   return axios.request({
     url: servicePre + '/del',
     params: data,
     method: 'post',
   })
}


