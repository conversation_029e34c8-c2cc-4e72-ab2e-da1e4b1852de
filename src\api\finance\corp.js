import axios from '@/libs/api.request'
// 获取列表
const servicePre = '/stat'
// 渠道商收入查询接口
export const getCorpIncomeList = data => {
  return axios.request({
    url: servicePre + '/channelIncome/query',
    data,
    method: 'post'
  })
}

//生成发票编号接口
export const createInvoiceNo = (id,corpid,needNewNo) => {
  return axios.request({
    url: servicePre + `/invoice/create/no/${id}/${corpid}/${needNewNo}`,
    method: 'post',
  })
}

//生成发票接口
export const createInvoice = data => {
  return axios.request({
    url: servicePre + '/invoice/create',
    data,
    method: 'post',
  })
}
// 渠道商收入修改接口
export const updateChannel = data => {
  return axios.request({
    url: servicePre + '/channelIncome/update',
    params: data,
    method: 'put'
  })
}
// 渠道商收入汇总文件导出接口
export const OpexportTotalTask = data => {
  return axios.request({
    url: servicePre + '/channelIncome/createBillTotalTask',
    data,
    method: 'post'
  })
}
// 渠道商收入详情文件导出接口
export const OpexportDetailTask = data => {
  return axios.request({
    url: servicePre + '/channelIncome/createBillDetailTask',
    data,
    method: 'post'
  })
}
//invoice导出
export const exportInvoice = data => {
  return axios.request({
    url: servicePre+'/channelIncome/export/invoice',
    params: data,
    method: 'get'
  })
}
//渠道商审核接口
export const channelAuth = data => {
  return axios.request({
    url: servicePre + '/channelIncome/auth',
    params: data,
    method: 'put'
  })
}
//A~Z Detail导出
export const exportA2Zdeatil = data => {
  return axios.request({
    url: '/cms/channel/channelAtzFlowExport',
    params: data,
    method: 'post'
  })
}
// A~Z 汇总导出
export const exportA2ZSummary = data => {
  return axios.request({
    url: '/cms/channel/channelAtzSummaryExport',
    params: data,
    method: 'post'
  })
}
//IMSI Detail导出
export const exportIMSIdeatil = data => {
  return axios.request({
    url: '/cms/channelSelfServer/exportImsiCost',
    data: data,
    method: 'post'
  })
}

//批量下载Invoice
export const batchExportInvoice = data => {
  return axios.request({
    url: servicePre+'/channelIncome/export/batchInvoice',
    data: data,
    method: 'post'
  })
}

//立即出账接口
export const rapidPaymentOut = data => {
  return axios.request({
    url: servicePre+'/financialReport/billedImmediately',
    data,
    method: 'post'
  })
}

//修改imsi费
export const updateImsi = data => {
  return axios.request({
    url: servicePre + '/channelIncome/updateImsiFee',
    params: data,
    method: 'get'
  })
}
