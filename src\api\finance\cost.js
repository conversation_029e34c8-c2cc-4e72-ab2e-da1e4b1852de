import axios from '@/libs/api.request'
// 获取列表
const servicePre = '/stat'
// 渠道商收入查询接口
export const getCostReportList = data => {
  return axios.request({
    url: servicePre + '/statSupplierCost/query',
    data,
    method: 'post'
  })
}

// 汇总表导出
export const exportCostSummary = data => {
	return axios.request({
		url: servicePre + '/statSupplierCost/exportSummary',
		data,
		method: 'POST',
		responseType: 'blob'
	})
}

//明细表导出
export const exportCostDetail = data => {
	return axios.request({
		url: servicePre + '/statSupplierCost/exportDetail',
		data,
		method: 'POST',
		responseType: 'blob'
	})
}

//新增成本报表
export const addCostItemFunc = data => {
  return axios.request({
    url: servicePre + '/statSupplierCost/add',
    data,
    method: 'post',
  })
}

//修改成本报表
export const updateCostItemFunc = data => {
  return axios.request({
    url: servicePre + '/statSupplierCost/edit',
    data,
    method: 'post',
  })
}

//运营审批确认
export const approvalConfirm = data => {
  return axios.request({
    url: servicePre + '/statSupplierCost/operationConfirm',
    data,
    method: 'post',
  })
}
