import axios from '@/libs/api.request'
// 获取列表
const servicePre = '/stat'
//获取客户账单核对列表
export const StatChannelincomeInfoDay = data => {
  return axios.request({
    url: servicePre + '/channelincome/day/getPage',
    data,
    method: 'post'
  })
}
//线上收入查询
export const getOnlineIncomeList = data => {
  return axios.request({
    url: servicePre + '/onlineIncome/query',
    params: data,
    method: 'get'
  })
}
//单月线上收入账单汇总文件导出接口
export const exportTotalTask = data => {
  return axios.request({
    url: servicePre + '/onlineIncome/createBillTotalTask',
    params: data,
    method: 'post'
  })
}
//单月线上收入账单明细文件导出接口
export const exportDetailTask = data => {
  return axios.request({
    url: servicePre + '/onlineIncome/createBillDetailTask',
    params: data,
    method: 'post'
  })
}
