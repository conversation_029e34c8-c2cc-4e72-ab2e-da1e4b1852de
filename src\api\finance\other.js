import axios from '@/libs/api.request'
const servicePre = '/stat'
const orderServerPre = '/order'

// 分页查询其他客户收入
export const CustomerPage = data => {
  return axios.request({
    url: servicePre+'/otherCustomerIncome/query/page',
    data,
    method: 'post'
  })
}
// 汇总账单实际数/暂估数查询
export const queryCustomer = data => {
  return axios.request({
    url: servicePre+'/otherCustomerIncome/query/num',
    params: data,
    method: 'get'
  })
}
// 编辑其他客户收入
export const UpdateCustomer = data => {
  return axios.request({
    url: servicePre+'/otherCustomerIncome/modified',
    data,
    method: 'post',
	contentType: 'multipart/form-data'
  })
}
//流量结算汇总文件导出
export const exportflowSettle = data => {
  return axios.request({
    url: servicePre+'/otherCustomerIncome/export/flowSettleSummary',
    params: data,
    method: 'get'
  })
}
//流量使用明细文件导出
export const exportflowUsed = data => {
  return axios.request({
    url: servicePre+'/otherCustomerIncome/export/flowUsedDetail',
    params: data,
    method: 'get'
  })
}
//invoice导出
export const exportOtInvoice = data => {
  return axios.request({
    url: servicePre+'/billAdjust/downAdjustFile',
    params: data,
    method: 'post',
    responseType: 'blob'
  })
}
//套餐结算汇总文件导出
export const exportPackage = data => {
  return axios.request({
    url: servicePre+'/otherCustomerIncome/export/packageSettleSummary',
    params: data,
    method: 'get'
  })
}
//套餐使用明细文件导出
export const exportPackageUsed = data => {
  return axios.request({
    url: servicePre+'/otherCustomerIncome/export/packageUsedDetail',
    params: data,
    method: 'get'
  })
}
//其他客户收入汇总文件导出
export const exportSummaryFile = data => {
  return axios.request({
    url: servicePre+'/otherCustomerIncome/export/summaryFile',
    data,
    method: 'post'
  })
}
//taxation导出
export const exportTaxation = data => {
  return axios.request({
    url: servicePre+`/otherCustomerIncome/export/taxation`,
    params: data,
    method: 'get'
  })
}
//生成真实账单接口
export const generateActualBill = data => {
  return axios.request({
    url: servicePre+'/otherCustomerIncome/generateActualBill',
    data,
    method: 'post',
	contentType: 'multipart/form-data'
  })
}
//真实账单查询接口
export const queryHistoryBill = data => {
  return axios.request({
    url: servicePre+'/otherCustomerIncome/queryHistoryBill',
    data,
    method: 'post',
  })
}
//客户账单导出
export const exportCustomer = data => {
  return axios.request({
    url: servicePre+'/otherCustomerIncome/export/bill',
    params: data,
    method: 'get',
  })
}

//生成发票编号接口
export const createOtInvoiceNo = (id,corpid,needNewNo) => {
  return axios.request({
    url: servicePre + `/invoice/createForOther/no/${id}/${corpid}/${needNewNo}`,
    method: 'post',
  })
}

//生成发票接口
export const createOtInvoice = data => {
  return axios.request({
    url: servicePre + '/invoice/createForOther',
    data,
    method: 'post',
  })
}
//审核其他客户收入
export const otherAuth = data => {
  return axios.request({
    url: servicePre + '/otherCustomerIncome/auth',
   params: data,
    method: 'post'
  })
}

// 账期管理查询接口
export const getBillInfoByProps = data => {
  return axios.request({
    url: servicePre + '/billAdjust/searchAdjustInfo',
    data,
    method: 'post'
  })
}

// 账单调整首页导出接口
export const exportAdjustInfo = data => {
  return axios.request({
    url: servicePre + '/billAdjust/exportAdjustInfo',
    data,
    method: 'post'
  })
}

// 账单调整首页批量下载Invoice
export const batchExportInvoice = data => {
  return axios.request({
    url: servicePre+'/billAdjust/batchExportInvoice',
    data: data,
    method: 'post'
  })
}

// 账单调整首页单独下载Invoice
export const singleExportInvoice = data => {
  return axios.request({
    url: servicePre+'/billAdjust/downAdjustFile',
    params: data,
    method: 'post',
    responseType: 'blob'
  })
}

// 账单查询-渠道商收入
export const getChannelBill = data => {
  return axios.request({
    url: servicePre+'/channelincome/month/getChannelBill',
    data,
    method: 'post'
  })
}

// 账单查询-白卡订单收入
export const getBlankCardBill = data => {
  return axios.request({
    url: servicePre+'/channelincome/month/getBlankCardBill',
    data,
    method: 'post'
  })
}

// 新增调账（重新生成/赔付）
export const addAdjustInfo = data => {
  return axios.request({
    url: servicePre+'/billAdjust/addAdjustInfo',
    data,
    method: 'post',
    contentType: 'multipart/form-data'
  })
}

// 新增调账（调整原因）
export const getReasonList = data => {
  return axios.request({
    url: servicePre+'/billAdjust/getPayOutReason',
    data,
    method: 'post',
  })
}

// 批量下载白卡订单Invoice
export const batchDownloadWhiteCardInvoice = (data,corpId) => {
  return axios.request({
    url: orderServerPre+'/blankCardOrder/packInvoice?corpId='+corpId,
    data: data,
    method: 'post',
  })
}
// 单个下载白卡订单Invoice
export const singleDownloadWhiteCardInvoice = data => {
	return axios.request({
		url: orderServerPre+'/blankCardOrder/download',
		data,
		method: 'POST',
		responseType: 'blob'
	})
}

export const getRechargeFreeFeeConsumption = data => {
    return axios.request({
        url: servicePre + '/channelincome/month/getA2zAmountByMonth',
        params:data,
        method: 'get',
    })
}

export const exportA2zAmountByMonth = data => {
    return axios.request({
        url: servicePre + '/channelincome/month/exportA2zAmountByMonth',
        params:data,
        method: 'get',
        responseType: 'blob'
    })
}