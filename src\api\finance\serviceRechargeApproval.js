import axios from '@/libs/api.request'
const servicePre = '/cms'
//查询渠道商名称列表
export const getCoprList = data => {
  return axios.request({
    url: servicePre + '/channel/getChannelByEmail',
    params: data,
    method: 'get'
  })
}
//查询页面
export const getList = data => {
  return axios.request({
    url: servicePre + '/IBoss/getPage',
    data,
    method: 'post'
  })
}
//查询渠道商销售首页列表
export const getCorpList = data => {
  return axios.request({
    url: servicePre + '/channel/getChannelSellData',
    data,
    method: 'post'
  })
}

/* 审核 */
export const examine = data => {
	return axios.request({
		url: servicePre + '/privilege/authPrivilegeReply',
		data,
		method: 'post',
	})
}

// 下载付款证明
export const exportPaymentProofs = data => {
	return axios.request({
		url: servicePre + '/IBoss/downLoad',
		params: data,
		method: 'get',
		responseType: 'blob'
	})
}
