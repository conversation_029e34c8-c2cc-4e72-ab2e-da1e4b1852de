import axios from '@/libs/api.request'
// 获取列表
const servicePre = '/stat'

// 白卡订单收入查询接口
export const getWhiteCardList = data => {
  return axios.request({
    url: servicePre + '/blankOrder/getOrder',
    params: data,
    method: 'get'
  })
}

//白卡订单收入——客户列表
export const getCustomerList = data => {
  return axios.request({
    url: '/cms/channel/getChannelList',
    params: data,
    method: 'get'
  })
}

//白卡订单收入——导出
export const whiteCardFile = data => {
	return axios.request({
		url: servicePre + '/blankOrder/pageList',
		params: data,
		method: 'get',
		responseType: 'blob'
	})
}
