import axios from '@/libs/api.request'

const servicePre = '/api/v1'
// 获取列表
export const search = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}
// 获取列表
export const searchcust = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}
// 新建流量池
export const addpool = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}
// 修改
export const updatepool = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}
// 删除
export const delpool = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}
// 批量删除
export const delBatch = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}