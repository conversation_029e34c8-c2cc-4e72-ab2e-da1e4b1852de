import axios from '@/libs/api.request'
// 获取列表
const servicePre = '/cms'
// 卡号列表查询分页
export const  channelCardlist= data => {
  return axios.request({
    url: servicePre + '/flowPool/getCard',
    params: data,
    method: 'get'
  })
}
// 卡号列表导出接口
export const  exportCardlist= data => {
  return axios.request({
    url: servicePre + '/flowPool/outCardList',
    params: data,
    method: 'post'
  })
}
// 流量池列表分页查询接口
export const  channelflowlist= data => {
  return axios.request({
    url: servicePre + '/flowPool/getChannelFlowList',
    data,
    method: 'post'
  })
}
// 流量池列表导出接口
export const  exportflow= data => {
  return axios.request({
    url: servicePre + '/flowPool/ChannelFlowListOut',
    data,
    method: 'post'
  })
}
// 流量池提醒阈值修改
export const  reminder= data => {
  return axios.request({
    url: servicePre + '/flowPool/updateFlowPoolReminder',
    params: data,
    method: 'post'
  })
}
// iccid分页查询接口
export const  channelIccidlist= data => {
  return axios.request({
    url: servicePre + '/flowPool/getICCID',
    params: data,
    method: 'get'
  })
}
// iccid列表导出接口
export const  exporticcid= data => {
  return axios.request({
   url: servicePre + '/flowPool/outICCID',
   params: data,
   method: 'post'
  })
}
// iccid单个导入接口
export const  Singleimport= data => {
  return axios.request({
    url: servicePre + '/channelCard/flowPoolAddCard ',
    data,
    method: 'post'
  })
}
// iccid批量导入接口
export const  Batchimport= data => {
  return axios.request({
    url: servicePre + '/channelCard/flowPoolAddCardBatch',
    data,
    method: 'post',
	contentType: 'multipart/form-data'
  })
}
// 客户管理-iccid删除接口
export const  Deleteiccid= data => {
  return axios.request({
    url: servicePre + '/flowPool/removeCards',
    data,
    method: 'post'
  })
}

// 客户管理-卡号移除接口
export const  DeleteCard= data => {
  return axios.request({
    url: servicePre + '/flowPool/ChannelRemoveCards',
    data,
    method: 'post'
  })
}

// 使用记录分页查询接口
export const  UsageList= data => {
  return axios.request({
    url: servicePre + '/flowPool/getFlowpoolUseRecord',
    params: data,
    method: 'get'
  })
}
// 使用记录导出接口
export const  exportUsageList= data => {
  return axios.request({
    url: servicePre + '/flowPool/outFlowpoolUseRecord',
    params: data,
    method: 'post'
  })
}
// 使用记录详情分页查询接口
export const  UsageListdetails= data => {
  return axios.request({
    url: servicePre + '/flowPool/getCardUseDetailRecord',
    params: data,
    method: 'get'
  })
}
// 使用记录详情导出查询接口
export const  exportUsagedetails= data => {
  return axios.request({
    url: servicePre + '/flowPool/outFlowPoolDetailRecord',
    params: data,
    method: 'post'
  })
}
// 根据id获取厂商
export const getStoreByCorpId = corpId => {
  return axios.request({
    url: servicePre + `/channel/${corpId}`,
    method: 'get'
  })
}

//任务列表查看
export const  getTaskList= data => {
  return axios.request({
    url: servicePre + '/flowPool/getIccidImportTaskList',
	params: data,
    method: 'get'
  })
}
//任务文件下载
export const  exportTaskList= data => {
  return axios.request({
    url: servicePre + '/flowPool/getIccidImportTaskFile',
	params: data,
    method: 'get',
	responseType: 'blob'
  })
}

// 客户管理/渠道自服务-使用记录流量汇总导出接口
export const  exportTraffic= data => {
  return axios.request({
    url: '/stat/finance/flowpoolBillExport',
    params: data,
    method: 'get',
  })
}

// 卡管理
export const  cardUpadate = data => {
  return axios.request({
    url: servicePre + '/flowPool/updateICCID',
    data,
    method: 'post'
  })
}


/* 流量卡暂停 */
export const stopStatusVCard = data => {
  return axios.request({
    url: servicePre + '/flowPool/card/pause',
    params: data,
    method: 'get'
  })
}
/* 流量卡恢复 */
export const recoverStatusVCard =data => {
  return axios.request({
    url: servicePre + '/flowPool/card/resume',
    params: data,
    method: 'get'
  })
}

// 选择需要的字段进行导出
export const  requiredExport= data => {
  return axios.request({
    url: '/stat/finance/flowpoolBillExport',
	params: data,
    method: 'get',
  })
}