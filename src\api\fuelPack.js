import axios from '@/libs/api.request'

const servicePre = '/cms'
// 加油包列表查询
export const getPage = data => {
  return axios.request({
    url:  servicePre+'/channelSelfServer/packageRefuel/package/pageList',
    data,
    method: 'post'
  })
}
// 购买加油包接口
export const buyPackageRefuel = data => {
  return axios.request({
    url:  servicePre+'/channelSelfServer/packageRefuel/buyPackageRefuel',
    data,
    method: 'post'
  })
}
// 购买加油包接口
export const refuelList = (corpId,data) => {
  return axios.request({
    url:  servicePre+'/channelSelfServer/packageRefuel/refuelList',
	params: corpId,
    data,
    method: 'post'
  })
}

