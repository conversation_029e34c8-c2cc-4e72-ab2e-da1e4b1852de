import axios from '@/libs/api.request'
const servicePre = 'cms'

// 获取渠道商列表
export const getCorpList = data => {
  return axios.request({
    url: servicePre + '/gtpConfig/corpList',
    method: 'get',
    data,
  })
}

// 获取GTP话单地址配置分页列表
export const getGtpConfigList = data => {
  return axios.request({
    url: servicePre + '/gtpConfig/page',
    method: 'post',
    data
  })
}

// 新增GTP话单地址配置
export const addGtpConfig = data => {
  return axios.request({
    url: servicePre + '/gtpConfig/add',
    method: 'post',
    data
  })
}

// 更新GTP话单地址配置
export const updateGtpConfig = data => {
  return axios.request({
    url: servicePre + '/gtpConfig/update',
    method: 'post',
    data
  })
}

// 删除GTP话单地址配置
export const deleteGtpConfig = id => {
  return axios.request({
    url: servicePre + `/gtpConfig/delete?id=${id}`,
    method: 'post'
  })
}
