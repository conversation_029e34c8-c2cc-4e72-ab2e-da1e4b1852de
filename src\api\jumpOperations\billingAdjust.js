import axios from '@/libs/api.request'

/* ————————————————————————————————————— 调账 ————————————————————————————————————— */
// 业务经理审核
export const businessManagerAudits = data => {
  return axios.request({
    url: '/aep/businessManagerAudits',
    data,
    method: 'post'
  })
}

// 财务审核
export const financialAudits = data => {
  return axios.request({
    url: '/aep/financialAudits',
    data,
    method: 'post'
  })
}

// 申请人确认
export const applicantConfirmation = data => {
  return axios.request({
    url: '/aep/applicantConfirmation',
    data,
    method: 'post'
  })
}

// 待办查询调账信息
export const searchAdjustInfo = data => {
  return axios.request({
    url: '/aep/searchAdjustInfo',
    data,
    method: 'post'
  })
}

// 下载证明文件、invoice
export const exportFile = data => {
	return axios.request({
		url: '/aep/downAdjustFile',
		data,
		method: 'POST',
		responseType: 'blob'
	})
}

/* ————————————————————————————————————— 营销活动 ————————————————————————————————————— */

// 营销活动——添加充值记录信息
export const getRechargeRecord = data => {
  return axios.request({
    url: '/aep/campaign/getManualRecharges',
    data,
    method: 'post'
  })
}

// 营销活动——添加充值记录审核
export const examineRechargeRecord = data => {
  return axios.request({
    url: '/aep/settlementReview',
    data,
    method: 'post'
  })
}

// 营销活动——结算审批-信息
export const getCampaign = data => {
  return axios.request({
    url: '/aep/campaign/getCampaign',
    data,
    method: 'post'
  })
}

// 营销活动-导出
export const exportMarketingFile = data => {
	return axios.request({
		url: '/aep/exportMarketingFile',
		data,
		method: 'POST',
		responseType: 'blob'
	})
}

// 营销活动——审批
export const marketingFileApproval = (data, token) => {
  return axios.request({
    url: '/aep/settlement/approvalSettlement',
    data,
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
      'ssoAccessToken': token, // 将 token 放在请求头中
    },
  });
};

// 营销活动——结算分页
export const getMktSettlementPage = data => {
	return axios.request({
		url: '/aep/getMktSettlementPage',
		data,
		method: 'POST',
	})
}

// 营销活动——结算——文件下载、流转意见列表
export const getOpinions = data => {
	return axios.request({
		url: '/aep/settlement/getOpinions',
		data,
		method: 'POST',
	})
}

// 营销活动——结算——文件下载
export const download = (data, token) => {
	return axios.request({
		url: '/aep/settlement/download',
		params: data,
		method: 'POST',
		responseType: 'blob',
    headers: {
      'ssoAccessToken': token, // 将 token 放在请求头中
    },
	})
}
