import axios from '@/libs/api.request'

export const operationAgencyApproval = data => {
	return axios.request({
		url: '/aep/IBoss/getPage',
		data: data,
		method: 'post'
	})
}

export const getWhiteCardOperateObj = data => {
	return axios.request({
		url: '/aep/blankCardOrder/getPayInfo',
		data,
		method: 'post'
	})
}
// 操作白卡订单
export const whiteCardOperate = data => {
	return axios.request({
		url: '/aep/blankCardOrder/payConfirm',
		data,
		method: 'POST'
	})
}
// 下载付款证明
export const exportPaymentProofs = data => {
	return axios.request({
		url: '/aep/blankCardOrder/download',
		data,
		method: 'POST',
		responseType: 'blob'
	})
}
// 获取渠道商销售数据列表——销售人员待办页面
export const getPage = data => {
	return axios.request({
		url: '/aep/channelSellData/getPage',
		data,
		method: 'post'
	})
}

// 渠道商销售数据——财务人员待办页面
export const getDisbursePage = data => {
	return axios.request({
		url: '/aep/channelIncome/queryTodo',
		data,
		method: 'post'
	})
}

// 完成商销售数据代办
export const pushFinish = data => {
	return axios.request({
		url: '/aep/channelSellData/pushFinish',
		data,
		method: 'post'
	})
}

// 催缴列表数据
export const getreminders = data => {
	return axios.request({
		url: '/aep/getreminders/getPage',
		data,
		method: 'post'
	})
}

// 完成催缴待办
export const getremindersPushFinish = data => {
	return axios.request({
		url: '/aep/getreminders/pushFinish',
		data,
		method: 'post'
	})
}

// 获取承诺量完成情况数据
export const getChannelPromiseDTO = data => {
	return axios.request({
		url: '/aep/channelPush/getChannelPromiseDTO',
		data,
		method: 'post'
	})
}
// 完成承诺量完成情况待办
export const channelPushFinish = data => {
	return axios.request({
		url: '/aep/channelPush/finish',
		data,
		method: 'post'
	})
}

// 审核
export const approval = data => {
	return axios.request({
		url: '/aep/IBoss/auth',
		data,
		method: 'post',
	})
}

// 已阅
export const received = data => {
	return axios.request({
		url: '/aep/IBoss/pushFinish',
		data: data,
		method: 'post'
	})
}

// 按钮是否可用
export const getShowButton = data => {
	return axios.request({
		url: '/aep/getTodoStatus/?todoUniqueId=' + data.todoUniqueId,
		data,
		method: 'post'
	})
}


// 获取token请求后端代办数据
export const getToken = data => {
	return axios.request({
		url: '/aep/sso/ssoLogin',
		data,
		method: 'post'
	})
}

// 付款待办——下载付款证明
export const downloadPaymentProofs = data => {
	return axios.request({
		url: '/aep/IBoss/download',
		data,
		method: 'post',
		responseType: 'blob'
	})
}

//下载Invoice
export const downloadInvoice = data => {
	return axios.request({
		url: '/aep/file/download',
		data,
		method: 'post',
		responseType: 'blob'
	})
}

//白卡——运营——已阅
export const receivedOperation = data => {
	return axios.request({
		url: '/aep/blankCardOrder/operatorConfirm',
		data,
		method: 'post'
	})
}

// 押金充值——页面信息
export const getRechargeRecord = data => {
	return axios.request({
		url: '/aep/channelSelfServer/deposit/record',
		data,
		method: 'post',
	})
}

// 销售审批发票申请审批
export const salesApproval = data => {
	return axios.request({
		url: '/aep/channelSelfServer/deposit/salesAppro',
		data,
		method: 'post',
	})
}

// 财务审批发票申请审批
export const financialApproval = data => {
	return axios.request({
		url: '/aep/channelSelfServer/deposit/finanAppro',
		data,
		method: 'post',
	})
}

//下载催缴文件
export const downloadReminderFile = data => {
	return axios.request({
		url: '/aep/getreminders/download',
	  data,
		method: 'post',
		responseType: 'blob'
	})
}
// 查看审阅详情POST    /getreminders/getOpinion（新增）
//下载催缴文件
export const getOpinion = data => {
	return axios.request({
		url: '/aep/getreminders/getOpinion',
	  data,
		method: 'post'
	})
}

export const getInvoiceAuditRecord = data => {
	return axios.request({
		url: '/aep/channelSelfServer/deposit/getInvoiceAuditRecord',
	  data,
		method: 'post'
	})
}

// 下载CN Invoice
export const downloadCnInvoiceFile = data => {
    return axios.request({
      url: '/aep/IBoss/cnInvoiceDownload',
     	data,
			method: 'post',
			responseType: 'blob'
    })
}
