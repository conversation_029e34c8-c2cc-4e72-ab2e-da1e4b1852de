import axios from "@/libs/api.request";

const servicePre = "/mkt";

// 获取列表
const cmsServicePre = "/cms";

/* 新增营销活动 */
export const addCampaign = (data) => {
  return axios.request({
    url: servicePre + "/campaign/add",
    data,
    method: "post",
  });
};
/* 修改营销活动 */
export const updateCampaign = (data) => {
  return axios.request({
    url: servicePre + "/campaign/edit",
    data,
    method: "post",
  });
};

/* 营销活动新增||修改客户公司分页接口*/
export const mktChannelPage = (data) => {
  return axios.request({
    url: cmsServicePre + "/channel/mktChannelPage",
    data,
    method: "post",
  });
};
/* 营销管理客户下拉列表*/
export const getCorpList = (params) => {
  return axios.request({
    url: servicePre + "/campaign/getCorpList",
    params,
    method: "get",
  });
};

/* 营销活动列表 */
export const getCampaignList = (data) => {
  return axios.request({
    url: servicePre + "/campaign/list",
    data,
    method: "post",
  });
};

/* 获取营销活动详情 */
export const getCampaignDetail = (params) => {
  return axios.request({
    url: servicePre + "/campaign/detail",
    params,
    method: "get",
  });
};

/* 获取营销活动规则详情/点击修改时回显数据 */
export const getRuleDetails = (params) => {
  return axios.request({
    url: servicePre + "/campaign/getRuleDetails",
    method: "get",
    params,
  });
};
/* 获取营销活动客户详情 */
export const getCorpDetails = (params) => {
  return axios.request({
    url: servicePre + "/campaign/getCorpDetails",
    method: "get",
    params,
  });
};

/* 营销活动客户详情分页接口 */
export const getCorpDetailsPage = (data) => {
  return axios.request({
    url: servicePre + "/campaign/getCorpDetailsPage",
    data,
    method: "post",
  });
};

/* 营销活动分页列表 */
export const getCampaignPage = (data) => {
  return axios.request({
    url: servicePre + "/campaign/page",
    data,
    method: "post",
  });
};


/* 添加充值记录 */
export const addSettlementLog = (data) => {
  return axios.request({
    url:  servicePre + "/settlement/addSettlementLog",
    data,
    method: "post",
  });
};

//  修改营销活动返回已选择渠道商信息 ：/campaign/getMktCorp
export const getMktCorp = (params) => {
  return axios.request({
    url: servicePre + "/campaign/getMktCorp",
    params,
    method: "get",
  });
};
