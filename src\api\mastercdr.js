import axios from '@/libs/api.request'

const servicePre = '/pms/api/v1'
// 主卡信息分页查询接口
export const Pagesearch = data => {
  return axios.request({
    url: servicePre + '/card/pageList',
    data,
    method: 'post'
  })
}
export const cardPageList = data => {
  return axios.request({
    url: servicePre + '/card/pageListAndBak',
    data,
    method: 'post'
  })
}
// 主卡导入接口
export const cardUp = data => {
  return axios.request({
    url: servicePre + '/card/import',
    data,
    method: 'post'
  })
}
// 上传adm和sdb文件
export const importfile = data => {
  return axios.request({
    url: servicePre + '/card/upload',
    data,
    method: 'post',
    contentType: 'multipart/form-data'
  })
}
// 主卡详情查询接口
export const cardsearch = (id, data) => {
  return axios.request({
    url: servicePre + `/card/${id}`,
    method: 'get',
  })
}
// 主卡信息导出接口
export const cardexport = data => {
  return axios.request({
    url: servicePre + '/card/export',
    data,
    method: 'post',
    // responseType: 'blob'
  })
}
// 查询ota列表
export const getotalist = data => {
  return axios.request({
    url: '/pms/ota/ota',
    params: data,
    method: 'get'
  })
}
// 获取短信模板列表
export const gettemplate = data => {
  return axios.request({
    url: 'sms/notice/list',
    params: data,
    method: 'post'
  })
}
// 查询合作商接口

export const getchannel = data => {
  return axios.request({
    url: '/cms/channel/package',
    params: data,
    method: 'get'
  })
}
// 查询实名制国家
export const getrealName = data => {
  return axios.request({
    url: servicePre + '/realName/list',
    params: data,
    method: 'get'
  })
}

// 查询实名制国家组
export const getrealNameGroups = data => {
  return axios.request({
    url: '/pms/pms-realname/getGroups',
    data,
    method: 'get'
  })
}

// 批量修改
export const BatchUpdate = data => {
  return axios.request({
    url: servicePre + '/card/batchUpdate',
    data,
    method: 'post',
    contentType: 'multipart/form-data'
  })
}

// 单个修改
export const Update = data => {
  return axios.request({
    url: servicePre + '/card/update',
    data,
    method: 'put'
  })
}
// 修改过期时间
export const UpdateTime = (month) => {
  return axios.request({
    url: servicePre + `/card/updateExpireTimeBatch/${month}`,
    method: 'post'
  })
}

// 单个修改查询接口
export const Updatesearch = (imsi) => {
  return axios.request({
    url: servicePre + `/card/extra/${imsi}`,
    method: 'get'
  })
}

// 查询过期时间
export const getExpireTime = data => {
  return axios.request({
    url: servicePre + '/card/getExpireTime',
    data,
    method: 'get'
  })
}
// 修改过期时间
export const updateExpireTimeBatch = data => {
  return axios.request({
    url: servicePre + '/card/updateExpireTimeBatch',
    data,
    method: 'post'
  })
}

// 主卡导入记录分页列表
export const getRecordPage = data => {
  return axios.request({
    url: servicePre + '/card/import/pageList',
    data,
    method: 'post'
  })
}

// 主卡导入记录查看 文件下载
export const exportFile = data => {
  return axios.request({
    url: servicePre + '/card/download',
    params: data,
    method: 'get',
    responseType: 'blob'
  })
}
