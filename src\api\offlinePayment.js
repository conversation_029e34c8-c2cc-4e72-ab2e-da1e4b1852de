import axios from '@/libs/api.request'

const servicePre = '/cms'

//获取渠道商信息
export const goPageInfo = data => {
	return axios.request({
		url: servicePre + '/channelSelfServer/deposit/info',
		params: data,
		method: 'get',
	})
}

// 获取充值记录列表
export const getRechargeRecord = data => {
	return axios.request({
		url: servicePre + '/channelSelfServer/deposit/record',
		data,
		method: 'post',
	})
}

// 导出Invoice
export const exportInvoice = data => {
  return axios.request({
    url: servicePre + '/IBoss/downLoad3',
    params: data,
    method: 'get',
    responseType: 'blob'
  })
}

// 下载付款证明
export const downloadPaymentProof = data => {
  return axios.request({
    url: servicePre + '/IBoss/downLoad/paymentProof',
    params: data,
    method: 'get',
    responseType: 'blob'
  })
}

//付款\重新上传付款证明
export const submitPaymentProof = data => {
	return axios.request({
		url: servicePre + '/IBoss/payBill',
		data,
		method: 'POST',
		contentType: 'multipart/form-data'
	})
}

//申请发票
export const requestInvoice = data => {
	return axios.request({
		url: servicePre + '/channelSelfServer/deposit/applyInvoice',
		data,
		method: 'post',
	})
}

//撤销
export const revoke = chargeId => {
  return axios.request({
    url: servicePre + `/channelSelfServer/deposit/cancelInvoice/${chargeId}/`,
    method: 'put',
  })
}
