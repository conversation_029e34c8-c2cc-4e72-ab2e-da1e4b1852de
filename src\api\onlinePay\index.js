import axios from "@/libs/api.request";

const servicePre = "/order/onlinePayment";
const servicePreAC = "/order/acPayment";
/* 获取参数 */
export const getOrderList = (data) => {
  return axios.request({
    url: servicePre + "/getList",
    params:data,
    method: "get",
  });
};
export const deleteOrder = (data) => {
  return axios.request({
    url: servicePre + "/deleteOrder",
    params:data,
    method: "post",
  });
};
export const closeOrder = (data) => {
  return axios.request({
    url: servicePreAC + "/closeOrder",
    params:data,
    method: "post",
  });
};
