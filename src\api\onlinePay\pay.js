import axios from "@/libs/api.request";

const servicePre = "/order/acPayment";

/* 获取参数 */
export const getAdyenParam = (data) => {
  return axios.request({
    url: servicePre + "/getAdyenParam",
    params:data,
    method: "get",
  });
};

/* 获取方式 */
export const queryAdyPayMethods = (data) => {
  return axios.request({
    url: servicePre + "/queryAdyPayMethods",
    params:data,
    method: "get",
  });
};

/* 创建订单并支付 */
export const createOrderAndPay = (data) => {
  return axios.request({
    url: servicePre + "/createAndPayOrder",
    data,
    method: "post",
  });
};


/* 支付 */
export const payOrder = (data) => {
  return axios.request({
    url: servicePre + "/payOrder",
    data,
    method: "post",
  });
};

/* 关闭 */
export const closeOrder = (data) => {
  return axios.request({
    url: servicePre + "/closeOrder",
    data,
    method: "post",
  });
};
/* 删除 */
export const deleteOrder = (data) => {
  return axios.request({
    url: servicePre + "/deleteOrder",
    data,
    method: "post",
  });
};