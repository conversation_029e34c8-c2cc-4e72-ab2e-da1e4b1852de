import axios from '@/libs/api.request'

const servicePre = '/oms/api/v1'
// 运营商管理获取列表
export const opsearch = data => {
  return axios.request({
    url: servicePre + '/country/queryCounrty',
    params: data,
    method: 'get'
  })
 }
//获取所有国家&地区信息
 export const opsearchAll = () => {
   return axios.request({
     url: servicePre + '/country/queryCounrtyList',
     method: 'get'
   })
  }

// 运营商管理新增国家
export const addop = data => {
  return axios.request({
    url: servicePre + '/country/addCounrty',
    data,
    method: 'post',
	contentType: 'multipart/form-data'
  })
 }
 // 运营商管理修改
 export const updateop = data => {
   return axios.request({
     url: servicePre + '/country/updateCounrty',
     data,
     method: 'post',
	 contentType: 'multipart/form-data'
   })
  }
 // 运营商管理删除
 export const deleteop = (data) => {
   return axios.request({
     url: servicePre + `/country/deleteCounrty`,
     params: data,
     method: 'delete'
   })
  }

  //获取运营商
  export const getOperators = data => {
    return axios.request({
      url: servicePre + '/country/getOperators',
      params: data,
      method: 'get',
    },)
  }

//渠道商配置获取运营商
export const getAllOperators = data => {
  return axios.request({
    url: servicePre + '/operator/list',
    params: data,
    method: 'get',
  },)
}

export const getChoiceOperators = data => {
  return axios.request({
    url: servicePre + '/operator/a2zChannelOperator',
    params: data,
    method: 'get',
  },)
}
