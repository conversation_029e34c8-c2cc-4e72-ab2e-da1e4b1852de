import axios from '@/libs/api.request'

const servicePre = '/pms/api/v1'
const omsServicePre = '/oms/api/v1'
const rmsServicePre = '/rms/api/v1'

// 获取供应商列表 (复用故障处理的接口)
export const getSuppliers = () => {
  return axios.request({
    url: rmsServicePre + '/supplier/query',
    method: 'get'
  })
}

// 获取国家列表 (复用故障处理的接口)
export const getCountries = (data) => {
  return axios.request({
    url: omsServicePre + '/country/getCountryByContinent',
    method: 'get',
    params: data
  })
}

// 获取大洲列表 (复用故障处理的接口)
export const getContinents = () => {
  return axios.request({
    url: omsServicePre + '/country/getContinent',
    method: 'get'
  })
}

// 根据目标国家MCC列表获取供应商 (复用故障处理的接口)
export const getSuppliersByMccList = (data) => {
  return axios.request({
    url: omsServicePre + '/country/getSuppliersByMccList',
    method: 'post',
    data: data
  })
}

// 获取卡池列表 (复用故障处理的接口)
export const getCardPoolsByMcc = (data) => {
  return axios.request({
    url: servicePre + "/cardPool/queryCardPoolByMccList",
    method: 'post',
    data: data // 包含 mccList 和 supplierId
  })
}

// 获取卡池切换任务列表
export const getCardPoolSwitchList = data => {
  return axios.request({
    url: servicePre + '/getList',
    data,
    method: 'POST'
  })
}

// 创建卡池切换任务
export const createCardPoolSwitchTask = data => {
  return axios.request({
    url: servicePre + '/create',
    data,
    method: 'POST'
  })
}

// 获取卡池切换任务详情
export const getCardPoolSwitchDetail = id => {
  return axios.request({
    url: servicePre + `/detail/${id}`,
    method: 'GET'
  })
}

// 执行卡池切换任务
export const executeCardPoolSwitchTask = id => {
  return axios.request({
    url: servicePre + `/execute/${id}`,
    method: 'POST'
  })
}

// 取消卡池切换任务
export const cancelCardPoolSwitchTask = id => {
  return axios.request({
    url: servicePre + `/cancel/${id}`,
    method: 'POST'
  })
}

// 获取剩余未切换套餐列表
export const getRemainingPackages = taskId => {
  return axios.request({
    url: servicePre + `/remaining/${taskId}`,
    method: 'GET'
  })
}

// 继续切换选中套餐
export const continuePackageSwitch = data => {
  return axios.request({
    url: servicePre + '/continueSwitch',
    data,
    method: 'POST'
  })
}

// 完成卡池切换任务
export const finishCardPoolSwitchTask = taskId => {
  return axios.request({
    url: servicePre + `/finish/${taskId}`,
    method: 'POST'
  })
}

// 获取可用的新资源供应商列表（基于目标国家）
export const getAvailableNewSuppliers = data => {
  return axios.request({
    url: servicePre + '/getAvailableSuppliers',
    data,
    method: 'POST'
  })
}

// 根据供应商获取可用卡池列表
export const getAvailableCardPools = data => {
  return axios.request({
    url: servicePre + '/getAvailableCardPools',
    data,
    method: 'POST'
  })
}

// 验证卡池切换规则
export const validateSwitchRule = data => {
  return axios.request({
    url: servicePre + '/validateRule',
    data,
    method: 'POST'
  })
}

// 获取卡池切换历史记录
export const getCardPoolSwitchHistory = data => {
  return axios.request({
    url: servicePre + '/getHistory',
    data,
    method: 'POST'
  })
}

// 导出卡池切换报告
export const exportCardPoolSwitchReport = data => {
  return axios.request({
    url: servicePre + '/exportReport',
    data,
    method: 'POST',
    responseType: 'blob'
  })
}
