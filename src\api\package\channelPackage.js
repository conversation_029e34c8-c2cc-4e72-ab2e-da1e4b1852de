import axios from '@/libs/api.request'

const servicePre = '/pms/api/v1/channelBuiltPackage'

/* 渠道商套餐管理列表 */
export const getList = data => {
	return axios.request({
		url: servicePre + '/getList',
		data,
		method: 'post',
	})
}

//审批
export const examine = data => {
	return axios.request({
		url: servicePre + '/check',
		data,
		method: 'post',
	})
}

//未审批记录导出接口
export const  exportUnapprovedFile= data => {
  return axios.request({
    url: servicePre + '/exportCheckList',
    params: data,
    method: 'post'
  })
}

//导入文件审批接口
export const  fileConfirmApproval= data => {
  return axios.request({
    url: servicePre + '/batchCheck',
    data,
    method: 'post',
	contentType: 'multipart/form-data'
  })
}