import axios from '@/libs/api.request'
// 获取列表
const servicePre = '/cms'
// 套餐管理-流量池管理分页查询接口
export const  getPage= data => {
  return axios.request({
    url: servicePre + '/flowPool/getFlowpoolList',
    data: data,
    method: 'post'
  })
}
// 套餐管理-流量池管理导出接口
export const  exportflow= data => {
  return axios.request({
    url: servicePre + '/flowPool/flowpoolListOut',
    data,
    method: 'post'
  })
}
// 套餐管理-流量池管理审核接口
export const  review= data => {
  return axios.request({
    url: servicePre + '/flowPool/flowpoolAuth',
	params: data,
    method: 'post'
  })
}
// 套餐管理-流量池管理新建接口
export const  addflow= data => {
  return axios.request({
    url: servicePre + '/flowPool/addFlowpool',
    data,
    method: 'post'
  })
}
// 套餐管理-流量池管理修改接口
export const  updateflow= data => {
  return axios.request({
    url: servicePre + '/flowPool/updateFlowPool',
    data,
    method: 'post'
  })
}
// 套餐管理-流量池管理卡池查询接口
export const  getcardpool= data => {
  return axios.request({
    url: servicePre + '/flowPool/getRelateCardPool',
    data,
    method: 'post'
  })
}

