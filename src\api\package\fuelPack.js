import axios from '@/libs/api.request'
// 获取列表
const servicePre = '/pms'
// 加油包管理-加油包管理分页查询接口
export const  getPage= data => {
  return axios.request({
    url: servicePre + '/refuelPackage/select',
    data,
    method: 'post'
  })
}
// 加油包管理-加油包新增接口
export const  addfuelPack= data => {
  return axios.request({
    url: servicePre + '/refuelPackage/add',
    data,
    method: 'post'
  })
}
// 加油包管理-加油包修改接口
export const  updatefuelPack= data => {
  return axios.request({
    url: servicePre + '/refuelPackage/update',
    data,
    method: 'post'
  })
}
// 加油包管理-加油包删除接口
export const deletefuelPack= data => {
  return axios.request({
    url: servicePre + '/refuelPackage/delete',
    params: data,
    method: 'post'
  })
}
// 加油包管理-加油包审核接口
export const  auditfuelPack= data => {
  return axios.request({
    url: servicePre + '/refuelPackage/audit',
    params: data,
    method: 'post'
  })
}
// 根据id查询加油包信息
export const  queryfuelPack= data => {
  return axios.request({
    url: servicePre + '/refuelPackage/query',
    params: data,
    method: 'post'
  })
}
// 加油包关联套餐列表查询
export const  selectRefuekPackage= data => {
  return axios.request({
    url: servicePre + '/refuelPackage/selectRefuekPackage',
    params: data,
    method: 'post'
  })
}
