import axios from '@/libs/api.request'

const servicePre = '/pms/api/v1/package'
const omsServicePre =  '/oms/api/v1'

/* 列表 */
export const getPackageList = data => {
	return axios.request({
		url: servicePre + '/getList',
		data,
		method: 'POST',
	})
}

/* 新增 */
export const addPackage = data => {
	return axios.request({
		url: servicePre + '/add',
		data,
		method: 'POST',
	})
}

// 新增图片
export const addPhoto = data => {
	return axios.request({
		url: servicePre + '/addPhoto',
		data,
		method: 'POST',
		contentType: 'multipart/form-data'
	})
}

/* 更新 */
export const updatePackage = data => {
	return axios.request({
		url: servicePre + '/update',
		data,
		method: 'POST',
		contentType: 'multipart/form-data'
	})
}

/* 批量更新 */
export const batchUpdatePackage = (data, id) => {
	return axios.request({
		url: servicePre + '/batchUpdate',
		data,
		method: 'POST',
	})
}

/* 审核 */
export const checkPackage = (id, auditStatus) => {
	return axios.request({
		url: servicePre + `/check/${id}/${auditStatus}`,
		method: 'PUT',
	})
}


/* 删除 */
export const batchDelete = data => {
	return axios.request({
		url: servicePre + '/batchDelete',
		data: data,
		method: 'delete',
	})
}

/* 套餐列表查询(查询结算规则的表套餐) */
export const getPackageByCorpId = corpId => {
	return axios.request({
		url: '/cms/api/v1/terminal' + `/settleRule/${corpId}`,
		method: 'GET',
	})
}
// 批量删除套餐覆盖国家
export const allBatchDelete = data => {
	return axios.request({
		url: servicePre + '/batchDelete',
		data,
		method: 'post',
		contentType: 'multipart/form-data'
	})
}
// 批量修改套餐覆盖国家 导入文件
export const allBatchUpdate = data => {
	return axios.request({
		url: servicePre + '/batchUpdatePackage',
		data,
		method: 'post',
		contentType: 'multipart/form-data'
	})
}
// 任务列表查看
export const batchQueryTasks = data => {
	return axios.request({
		url: servicePre + '/selectTask',
		params: data,
		method: 'get',
		contentType: 'multipart/form-data'
	})
}
// 批量操作任务下载接口
export const batchQueryDownload = data => {
	return axios.request({
		url: servicePre + '/fileUpload',
		params: data,
		method: 'post',
		responseType: 'blob'
	})
}
//  批量操作套餐覆盖国家 审核
export const allCheckPackage = data => {
	return axios.request({
		url: servicePre + '/batchAuth',
		params: data,
		method: 'post',
	})
}
//  查找可订购加油包列表
export const getRefuelList = data => {
	return axios.request({
		url: servicePre + '/getRefuelList',
		data,
		method: 'post',
	})
}
//  根据套餐id查找可订购加油包列表
export const getDetailsRefuelList = data => {
	return axios.request({
		url: servicePre + '/getDetailsRefuelList',
		data,
		method: 'post',
	})
}

// 套餐管理-套餐管理列表导出接口
export const exportflow = data => {
	return axios.request({
		url: servicePre + '/exportList',
		data,
		method: 'post',
	})
}
// 套餐管理-套餐覆盖国家导出接口
export const exportPackageCountryList = data => {
	return axios.request({
		url: servicePre + '/exportPackageCountryList',
		data,
		method: 'post',
	})
}

// 用量查询接口
export const getConsumption = data => {
	return axios.request({
		url: servicePre + '/getConsumption',
		params:data,
		method: 'post',
	})
}

//速度模板查询接口
export const getTemplate = data => {
	return axios.request({
		url: '/pms/api/v1/upccTemplate/packageGetUpcc',
		params: data,
		method: 'get',
	})
}

//国家卡池关联组查询
export const countryCardPoolGroup = data => {
	return axios.request({
		url: 'pms/api/v1/cardPoolMccGroup/packageGetCardPool',
		params: data,
		method: 'get',
	})
}

//套餐卡池详情查询
export const getCardPoolDetail = data => {
	return axios.request({
		url: servicePre + '/getPackageCardPool',
		params: data,
		method: 'POST',
	})
}

//查询定向应用和模板数据源
export const packageGetDirectional = data => {
	return axios.request({
		url: '/pms/api/v1/directional/packageGetDirectional',
		params: data,
		method: 'get',
	})
}

//套餐详情/编辑/复制 定向应用数据初始化
export const deatilGetDirect = data => {
	return axios.request({
		url: servicePre + '/deatilGetDirect',
		params: data,
		method: 'POST',
	})
}

//判断套餐是否已售卖
export const getPackageSale = data => {
	return axios.request({
		url: '/cms/api/v1/packageCard/IsPackageSale',
		params: data,
		method: 'get',
	})
}

// 套餐 是否支持热点查询国家
export const isSupportGetMccList = data => {
	return axios.request({
		url: servicePre + '/getSelfPackageFlowinfoMcc',
		data: data,
		method: 'post',
	})
}

// 套餐 国家获取用量 (旧)
export const getSelfPackageFlowinfoDTO = data => {
	return axios.request({
		url: servicePre + '/getSelfPackageFlowinfoDTO',
		data: data,
		method: 'post',
	})
}

// 套餐 国家获取用量 (新)
export const getSelfPackageFlowInfoNew = data => {
	return axios.request({
		url: servicePre + '/getSelfPackageFlowInfoNew',
		data: data,
		method: 'post',
	})
}

//获取系统所配置的大洲信息
export const getContinent = data => {
    return axios.request({
        url: omsServicePre + '/country/getContinent',
        data,
        method: 'get',
    })
}


export const getCountry = data => {
    return axios.request({
        url: servicePre+'/getSelfPackageFlowinfoMccNew',
        data: data,
        method: 'post',
    })
}
