import axios from '@/libs/api.request'

const servicePre = 'pms/api/v1/directional'

/* 定向应用管理列表 */
export const getDirectionalApp = data => {
	return axios.request({
		url: servicePre + '/getDirectionalApp',
		params: data,
		method: 'get',
	})
}

// UPCC模板查询
export const getUpccTemplateList = data => {
	return axios.request({
		url: servicePre + '/getUpccTemplateList',
		params:data,
		method: 'post',
	})
}

//新增定向应用
export const newDirectionalApp = data => {
	return axios.request({
		url: servicePre + '/newDirectionalApp',
		data,
		method: 'POST',
	})
}

//修改定向应用
export const updateDirectionalApp = data => {
	return axios.request({
		url: servicePre + '/updateDirectionalApp',
		data,
		method: 'PUT',
	})
}

//删除定向应用
export const delItem = data => {
	return axios.request({
		url: servicePre + '/delDirectionalApp',
		params: data,
		method: 'delete',
	})
}

//可用定向列表
export const getAppInfo = data => {
  return axios.request({
	url: servicePre + '/getAppInfo',
	data,
	method: 'POST',
  })
}