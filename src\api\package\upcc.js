import axios from '@/libs/api.request'

const servicePre = '/pms/api/v1/upccTemplate'

/* upcc列表 */
export const upccList = data => {
	return axios.request({
		url: servicePre + '/getUpccTemplate',
		data,
		method: 'post',
	})
}

/* 删除upcc */
export const del = data => {
	return axios.request({
		url: servicePre + '/delUpccTemplate',
		data,
		method: 'delete',
	})
}

/* 新增upcc */
export const addUpccList = data => {
	return axios.request({
		url: servicePre + '/newUpccTemplate',
		data,
		method: 'post',
	})
}

/* 修改upcc */
export const updateUpccList = data => {
  return axios.request({
    url: servicePre + '/updateUpccTemplate',
    data,
    method: 'PUT',
  })
}