import axios from '@/libs/api.request'
import { param } from 'jquery'

const servicePre = '/cms/whitelist/package'

/* 列表分页查询 */
export const whitePackageList = data => {
	return axios.request({
		url: servicePre + '/page',
		params: data,
		method: 'get',
	})
}

/* 规则详情 */
export const whitePackageDetail = params => {
	return axios.request({
		url: servicePre + '/detail',
		params: params,
		method: 'get',
	})
}

/* 删除规则 */
export const deleteWhitePackage = data => {
	return axios.request({
		url: servicePre + '/delete',
		data,
		method: 'delete',
	})
}

/* 新增号段规则 */
export const addWhitePackageNumber = data => {
	return axios.request({
		url: servicePre + '/add',
		data,
		method: 'post',
	})
}

/* 修改号段规则 */
export const updateWhitePackageNumber = data => {
	return axios.request({
		url: servicePre + '/update',
		data,
		method: 'post',
	})
}

/* 新增文件规则 */
export const addWhitePackageFile = data => {
	return axios.request({
		url: servicePre + '/file/update',
		data,
		method: 'post',
    contentType: 'multipart/form-data'
	})
}

// 模板文件下载接口
export const downloadTemplate = data => {
  return axios.request({
    url: servicePre + '/downloadTemplate',
    params: data,
    method: 'get',
    responseType: 'blob'
  })
}


