import axios from '@/libs/api.request'
const servicePre='/api/v1';
export const search = data => {
  return axios.request({
    url: servicePre+'/limitsmanage/search',
	  data,
    method: 'post'
  })
}

export const add = data => {
  return axios.request({
    url: servicePre+'/limitsmanage/add',
    data,
    method: 'post'
  })
}

export const getAllLimits = data => {
  return axios.request({
    url: servicePre+'/account/privileges',
    method: 'get'
  })
}



export const getOperatorLimits = data => {
  return axios.request({
    url: servicePre+`/account/operator/${data}/privilege`,
    // params: data,
    method: 'get'
  })
}

export const getRoleLimits = data => {
  return axios.request({
    url: servicePre+`/account/role/${data}/privilege`,
    // params: data,
    method: 'get'
  })
}

export const getRoleList = data => {
  return axios.request({
    url: servicePre+'/account/role/getRoleList',
    params: data,
    method: 'get'
  })
}



export const toSetRoleLimits = (data,roleId) => {
  return axios.request({
    url: servicePre+`/account/role/${roleId}/privilege/`,
    data,
    method: 'PUT',
  })
}

export const addRole = (data) => {
  return axios.request({
    url: servicePre+'/account/role/privilege/',
    data,
    method: 'PUT',
  })
}

