import axios from '@/libs/api.request'

const servicePre = '/pms/api/v1/cardPool'
/* 分页列表 */
export const getCardPoolList = data => {
  return axios.request({
    url: servicePre + '/getList',
    data,
    method: 'POST',
  })
}

/* 列表列表 */
export const queryCardPoolList = (data) => {
  return axios.request({
    url: servicePre + '/queryList',
    params: data,
    method: 'GET',
  })
}

/* 新增 */
export const addCardPool = data => {
  return axios.request({
    url: servicePre + '/add',
    data,
    method: 'POST',
  })
}

/* 导出 */
export const exportCardPool = data => {
  return axios.request({
    url: servicePre + `/export/${data}`,
    method: 'POST',
    responseType: 'blob'
  })
}

/* 复制 */
export const copyCardPool = data => {
  return axios.request({
    url: servicePre + `/copy/${data}`,
    method: 'POST',
  })
}

/* 删除 */
export const delCardPool = data => {
  return axios.request({
    url: servicePre + `/${data}`,
    method: 'delete',
  })
}

/* 更新 */
export const updateCardPool = data => {
  return axios.request({
    url: servicePre + '/update',
    data,
    method: 'POST',
  })
}


/* 获取卡池比例信息 */
export const cardPoolRatio = data => {
  return axios.request({
    url: servicePre + '/getRateList',
    data,
    method: 'POST',
  })
}
//根据mcc和supplierId查询卡池信息
export const getCardPoolinfo = data => {
  return axios.request({
    url: servicePre + '/getCardPoolinfoBymcc',
    params:data,
    method: 'get',
  })
}

//根据mcc和poolName查询卡池信息
export const getCardPoolinfoBymccNew = data => {
  return axios.request({
    url: servicePre + '/getCardPoolinfoBymccNew',
    params: data,
    method: 'get',
  })
}
