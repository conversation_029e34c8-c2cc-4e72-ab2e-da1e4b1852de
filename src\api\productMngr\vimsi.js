import axios from '@/libs/api.request'

const servicePre = '/pms/vcard'
const servicePre1 = '/pms/api/v1'

/* 列表 */
export const getVCardList = data => {
  return axios.request({
    url: servicePre + '/getList',
    data,
    method: 'POST',
  })
}

/* 新增 */
export const addVCard = data => {
  return axios.request({
    url: servicePre + '/insertpool',
    data,
    method: 'POST',
  })
}

/* 批量删除 */
// export const batchDelVCard = data => {
//   return axios.request({
//     url: servicePre + '/deleteBatch',
//     data,
//     method: 'DELETE',
//   })
// }

/* 批量删除 */
export const batchDelVCard = (poolId,startImsi,endImsi) => {
  return axios.request({
    url: servicePre + `/deleteBatch?poolId=${poolId}&startImsi=${startImsi}&endImsi=${endImsi}`,
    method: 'DELETE',
  })
}

/* 激活 */
/* 暂停 */
export const statusVCard = (id, status) => {
  return axios.request({
    url: servicePre + `/${id}/${status}`,
    method: 'PUT',
  })
}

/* 删除 */
export const delVCard = data => {
  return axios.request({
    url: servicePre + `/${data}`,
    method: 'delete',
  })
}

// 获取vimsi导入任务列表
export const getRecordPage = data => {
  return axios.request({
    url: servicePre1 + '/card/getVcardImportTaskList',
	params: data,
    method: 'get'
  })
}

// 获取v卡导入结果文件
export const exportFile = data => {
  return axios.request({
	url: servicePre1 + '/card/getResultFile',
	params: data,
	method: 'get',
	responseType: 'blob'
  })
}
