import axios from '@/libs/api.request'
// 获取用户列表
const servicePre = '/cms/packageActive'

//全球卡普通套餐查询
export const searchOrdinaryList = data => {
  return axios.request({
    url: servicePre + '/globalPackage/pageList',
    data,
    method: 'post'
  })
}
//全球卡普通套餐统计导出
export const  exportOrdinary = data => {
  return axios.request({
    url: servicePre + '/globalPackageSearchExport',
    data,
    method: 'post',
	responseType: 'blob'
  })
}
//线下模式套餐查询
export const searchOfflineList = data => {
  return axios.request({
    url: servicePre + '/offlinePackage/pageList',
    data,
    method: 'post'
  })
}
//线下统计导出
export const  exportOffline = data => {
  return axios.request({
    url: servicePre + '/offlinePackageSearchExport',
    data,
    method: 'post',
	responseType: 'blob'
  })
}
//线上模式套餐查询
export const searchOnlineList = data => {
  return axios.request({
    url: servicePre + '/onlinePackage/pageList',
    data,
    method: 'post'
  })
}
//线上导出
export const  exportOnline = data => {
  return axios.request({
    url: servicePre + '/onlinePackageSearchExport',
    data,
    method: 'post',
	responseType: 'blob'
  })
}
//合作运营商套餐查询
export const searchCooperativeList = data => {
  return axios.request({
    url: servicePre + '/cooperationPackage/pageList',
    data,
    method: 'post'
  })
}
//合作运营商导出
export const  exportCooperate = data => {
  return axios.request({
    url: servicePre + '/cooperationPackageSearchExport',
    data,
    method: 'post',
	responseType: 'blob'
  })
}
//基本统计
export const searchActiStatList = data => {
  return axios.request({
    url: servicePre + '/activatedPackageStat',
    data,
    method: 'post'
  })
}
//使用过有/无可用套餐列表导出
export const  downloadByType = data => {
  return axios.request({
    url: servicePre + '/usedPackageStat/export',
    params: data,
    method: 'post',
	responseType: 'blob'
  })
}
//使用过有/无可用套餐列表统计
export const searchUsedStat = data => {
  return axios.request({
    url: servicePre + '/usedPackageStat',
    params: data,
    method: 'post'
  })
}
//未激活套餐统计
export const  searchUnactiveStat = data => {
  return axios.request({
    url: servicePre + '/UnactivatedPackage',
     params: data,
    method: 'post'
  })
}
//未激活套餐导出
export const  exportUnactiveStat = data => {
  return axios.request({
    url: servicePre + '/unactivatedPackageStat/export',
    params: data,
    method: 'post',
	responseType: 'blob'
  })
}
//激活统计导出
export const  exportActiveStat = data => {
  return axios.request({
    url: servicePre + '/activatedPackageStatExport',
    data,
    method: 'post',
	responseType: 'blob'
  })
}

