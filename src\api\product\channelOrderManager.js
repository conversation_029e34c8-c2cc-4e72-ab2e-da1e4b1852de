import axios from '@/libs/api.request'
// 产品运营 渠道商订单管理
const servicePre = '/cms/channelOrder'


//获取渠道商列表
export const getCorpList = data => {
  return axios.request({
    url: '/cms/channel/getChannelList',
    params: data,
    method: 'get'
  })
}

//=============总订单=============
//总订单列表
export const getOrderList = data => {
  return axios.request({
    url: servicePre + '/orderPages',
    params: data,
    method: 'get'
  })
}

//导出订单数据
export const downLoadData = data => {
  return axios.request({
    url: servicePre + '/orderPages/export',
    params: data,
    method: 'GET',
    responseType: 'blob'
  })
}

//全部退订
export const unsubscribeBatch = (id) => {
  return axios.request({
    url: servicePre + `/unsubscribe/${id}`,
    method: 'POST'
  })
}
//退订待审批
export const examineOrderBatch = (data) => {
  return axios.request({
    url: servicePre + '/audit',
    data,
    method: 'post',
  })
}

//=============子订单=============
//子订单列表
export const getDetailsList = data => {
  return axios.request({
    url: servicePre + '/orderDetailsByOrderId',
    params: data,
    method: 'get'
  })
}

//子订单退订
export const unsubscribe = (id) => {
  return axios.request({
    url: servicePre + `/detail/unsubscribe/${id}`,
    method: 'POST'
  })
}

//退订待审批
export const examineOrder = (data) => {
  return axios.request({
    url: servicePre + '/detail/audit',
    data,
    method: 'post',
  })
}
