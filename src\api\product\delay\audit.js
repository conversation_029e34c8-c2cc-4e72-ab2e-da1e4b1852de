import axios from '@/libs/api.request'
// 未激活套餐查询
const servicePre = '/cms/packageDeferred'

//审核
export const doPass = (id,data) => {
  return axios.request({
    url: servicePre + '/packageDelayTask/check/'+id+'/'+data,
    method: 'put'
  })
}

//获取列表
export const getPackageList = data => {
  return axios.request({
    url: servicePre + '/queryPackageDelayTask',
    params: data,
    method: 'get'
  })
}

//套餐延期
export const packageDelay = (data) => {
  return axios.request({
    url: servicePre + '/packageDelayTask/add',
    data,
    method: 'post',
    contentType: 'multipart/form-data'
  })
}