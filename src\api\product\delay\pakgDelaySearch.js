import axios from '@/libs/api.request'
// 未激活套餐查询
const servicePre = '/cms/packageDeferred'

//导出数据
export const  downLoadOverdueFile = (data) => {
  return axios.request({
    url: servicePre + '/inactivePackage/export',
	data,
    method: 'post',
	contentType: 'multipart/form-data',
    responseType: 'blob'
  })
}

//上传查询
export const handlerUpload = (data) => {
  return axios.request({
    url: servicePre + '/getList/inactivePackage',
    data,
    method: 'post',
    contentType: 'multipart/form-data'
  })
}
//查询下载列表
export const  getFileList= (data) => {
  return axios.request({
    url: servicePre + '/getFileList/inactivePackage',
    params: data,
    method: 'get',
  })
}

//导出数据
export const  downloadfile = (id,type) => {
  return axios.request({
    url: servicePre + `/getList/download/${id}/${type}`,
    method: 'get',
	contentType: 'multipart/form-data',
    responseType: 'blob'
  })
}