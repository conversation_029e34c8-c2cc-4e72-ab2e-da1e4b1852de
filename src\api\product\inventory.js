import axios from '@/libs/api.request'
// 产品运营 库存管理
const servicePre = '/pms/api/v3/warehouse'

//获取仓库列表
export const getWarehouseList = data => {
  return axios.request({
    url: servicePre + '/queryWarehouse',
    params: data,
    method: 'get'
  })
}

//导出仓库号码数据
export const  downLoadFile = data => {
  return axios.request({
    url: servicePre + '/cardOut',
	params: data,
    method: 'get'
  })
}

//卡片调拨上传
export const uploadFile = (data) => {
  return axios.request({
    url: servicePre + '/cardAllot',
    data,
    method: 'put',
    contentType: 'multipart/form-data'
  })
}

//新增仓库
export const addInventory = data => {
  return axios.request({
    url: servicePre + '/newWarehouse',
    data,
    method: 'post'
  })
}
//修改仓库
export const editInventory = data => {
  return axios.request({
    url: servicePre + '/updateWarehouse',
    data,
    method: 'post'
  })
}

//查询所有仓库
export const getAllInventory = data => {
  return axios.request({
    url: servicePre + '/queryStoreList',
    method: 'get'
  })
}

//查询所有国家
export const getCountryList = data => {
  return axios.request({
    url: '/oms/api/v1/country/queryCounrtyList',
    method: 'get'
  })
}