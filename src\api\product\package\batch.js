import axios from '@/libs/api.request'
// 产品运营 批量配置
const servicePre = '/cms/package/config'


//获取任务列表
export const getTaskList = data => {
  return axios.request({
    url: servicePre + '/task/pageList',
    data,
    method: 'post'
  })
}

//任务文件下载
export const downloadTask = (taskId,status) => {
  return axios.request({
    url: servicePre + `/task/download/${taskId}?status=` + status,
    method: 'POST',
    responseType: 'blob'
  })
}

//任务回滚
export const doReback = taskId => {
  return axios.request({
    url: servicePre + `/task/rollback/${taskId}`,
    method: 'POST'
  })
}

//新建批量配置
export const addTask = data => {
  return axios.request({
    url: servicePre + '/task',
    data,
    method: 'POST',
    contentType: 'multipart/form-data'
  })
}
//页面配置-新建批量配置
export const addPageTask = data => {
  return axios.request({
    url: servicePre + '/taskPage',
    data,
    method: 'POST',
  })
}


//获取渠道商列表
export const getCorpList = data => {
  return axios.request({
    url: '/cms/channel/searchList',
    data,
    method: 'post'
  })
}

//获取渠道商是否需要备注
export const getIsNeedRemark = data => {
  return axios.request({
    url: '/cms/package/config/getTextChannel',
    data,
    method: 'get'
  })
}
