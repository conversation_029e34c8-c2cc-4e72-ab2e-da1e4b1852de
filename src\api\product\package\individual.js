import axios from '@/libs/api.request'
// 产品运营 配置
const servicePre = '/cms'
const mockApi = ''

//获取主卡查询信息
// export const getindividualList = data => {
//   return axios.request({
//     url:mockApi+ '/pms/api/v1/card/pageList',
//     data,
//     method: 'post'
//   })
// }
export const getindividualList = data => {
  return axios.request({
    url:mockApi+ '/pms/api/v1/card/pageList',
    data,
    method: 'post'
  })
}

//获取已绑定套餐列表
export const getPackageList = data => {
  return axios.request({
    url:mockApi+ servicePre + '/package/config/purchased',
    data,
    method: 'post'
  })
}
//获取新增时的套餐列表
export const getAllPackageList = data => {
  return axios.request({
    url:mockApi+ '/pms/api/v1/package/pageList',
    data,
    method: 'post'
  })
}


//单个删除套餐
export const deletePackage = data => {
  return axios.request({
    url: servicePre + '/package/config/unbind/',
    data,
    method: 'post',
  })
}
//新增套餐
export const addPackage = data => {
  return axios.request({
    url:mockApi+ servicePre + '/package/config/bind',
    params: data,
    method: 'post',
  })
}
