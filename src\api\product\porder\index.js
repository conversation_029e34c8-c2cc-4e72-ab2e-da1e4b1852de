import axios from '@/libs/api.request'
// 产品运营 个人订单管理
const servicePre = '/cms/api/v1/personalOrder'

//=============总订单=============
//总订单列表
export const getOrderList = data => {
  return axios.request({
    url: servicePre + '/pages',
    params: data,
    method: 'get'
  })
}

export const getOrderLists = data => {
  return axios.request({
    url: servicePre + '/getPackagePurchaseRecord',
    params: data,
    method: 'get'
  })
}

//批量发货文件上传
export const deliveryUploadBatch = (data) => {
  return axios.request({
    url: servicePre + '/deliver/batch',
    data,
    method: 'post',
    contentType: 'multipart/form-data'
  })
}

//全部解绑
export const unbundlingBatch = data => {
  return axios.request({
    url: servicePre + `/unbind/${data}`,
    method: 'POST'
  })
}
//全部解绑-大单
export const unbundlingBigOrder = data => {
  return axios.request({
    url: servicePre + `/big/unbind/${data}`,
    method: 'POST'
  })
}

//全部回收
export const recyclingBatch = (data) => {
  return axios.request({
    url: servicePre + `/recover/${data}`,
    method: 'POST'
  })
}

//订单发货
export const deliveryBatch = (data, orderId) => {
  return axios.request({
    url: servicePre + `/deliver/${orderId}`,
    data,
    method: 'post',
  })
}

//全部退订
export const unsubscribeBatch = (id) => {
  return axios.request({
    url: servicePre + `/unsubscribe/${id}`,
    method: 'POST'
  })
}

//全部退订-大额订单
export const unsubscribeBigOrder = data => {
  return axios.request({
    url: servicePre + '/unsubscribeForBigOrder',
    data,
    method: 'POST'
  })
}

//退订待审批
export const examineOrderBatch = (data) => {
  return axios.request({
    url: servicePre + '/audit',
    data,
    method: 'post',
  })
}

//导出订单数据
export const downLoadData = data => {
  return axios.request({
    url: servicePre + '/pages/export',
    params: data,
    method: 'GET',
    responseType: 'blob'
  })
}

//再次订购
export const reorderAgain = data => {
  return axios.request({
    url: 'cms/order/compensation',
    data,
    method: 'POST'
  })
}

//大额订单发货接口
export const deliverBigOrder = data => {
  return axios.request({
    url: servicePre + '/bigOrder/deliver',
    data,
    method: 'POST',
    contentType: 'multipart/form-data'
  })
}

//=============子订单=============
//子订单列表
export const getDetailsList = data => {
  return axios.request({
    url: servicePre + '/orderDetailsByOrderId',
    params: data,
    method: 'get'
  })
}

//子订单解绑
export const unbundling = data => {
  return axios.request({
    url: servicePre + `/unbind/child/${data}`,
    method: 'POST'
  })
}

//子订单回收
export const recycling = (data) => {
  return axios.request({
    url: servicePre + `/recover/child/${data}`,
    method: 'POST'
  })
}


//子订单发货
export const delivery = (data, orderId) => {
  return axios.request({
    url: servicePre + `/deliver/child/${orderId}`,
    data,
    method: 'post',
  })
}

//子订单退订
export const unsubscribe = (id) => {
  return axios.request({
    url: servicePre + `/detail/unsubscribe/${id}`,
    method: 'POST'
  })
}

//退订待审批
export const examineOrder = (data) => {
  return axios.request({
    url: servicePre + '/detail/audit',
    data,
    method: 'post',
  })
}

//H5订单详情
export const getOrderDetail = data => {
  return axios.request({
    url: servicePre + '/detail/orderDetail',
    params: data,
    method: 'get'
  })
}
