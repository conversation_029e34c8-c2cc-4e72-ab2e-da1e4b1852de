import axios from '@/libs/api.request'
import { param } from 'jquery'
// 产品运营 渠道商白卡订单管理
const servicePre = '/order/blankCardOrder'

//产品运营/渠道自服务————获取列表
export const getList = data => {
  return axios.request({
    url: servicePre + '/getOrder',
    params: data,
    method: 'get'
  })
}

//订单确认
export const comfirmOrder = data => {
  return axios.request({
    url: servicePre + `/comfirmOrder/` + data.id,
    data: data,
    method: 'put',
  })
}

//产品运营————订单取消
export const cancelOrder = id => {
  return axios.request({
    url: servicePre + `/cancelOrder/${id}`,
    method: 'put',
  })
}

//1、下载Invoice  2、下载付款证明  3、下载失败文件
export const downloadFile = data => {
	return axios.request({
		url: servicePre + '/download',
		params: data,
		method: 'get',
		responseType: 'blob'
	})
}

//生成发票Invoice
export const createInvoice = data => {
  return axios.request({
    url: servicePre + '/generateInvoice',
    data,
    method: 'post',
  })
}

//重新生成invoice
export const createInvoiceAgain = data => {
  return axios.request({
    url: servicePre + '/regenerateInvoice',
    data,
    method: 'post',
  })
}

//订单发货
export const shippingMethods = data => {
  return axios.request({
    url: servicePre + '/deliver',
	data,
    method: 'put',
	contentType: 'multipart/form-data'
  })
}

//生成发票编号接口
export const createInvoiceNo = data => {
  return axios.request({
    url: servicePre + '/getInvoiceInfo',
	params: data,
    method: 'get',
  })
}

// 白卡订单确认
export const InfoOrder = data => {
  return axios.request({
    url: 'cms/channel/getInfo4Order',
    params: data,
    method: 'get',
  })
}

// 白卡订单回滚
export const rollbackOrder = data => {
  return axios.request({
    url: servicePre + '/rollback',
    params: data,
    method: 'put',
  })
}

// 导出白卡订单
export const blankCardOrderExport = data => {
  return axios.request({
    url: servicePre + '/export',
    data,
    method: 'post',
    responseType: 'blob',
  })
}
