import axios from '@/libs/api.request'

const servicePre = '/cms'
// 人工认证分页查询接口
export const pageSearch = data => {
	return axios.request({
		url: servicePre + '/nameAuth/human',
		params: data,
		method: 'get'
	})
}
// 人工认证审核接口
export const Review = data => {
	return axios.request({
		url: servicePre + '/humanVerify/verify',
		params: data,
		method: 'get'
	})
}
// 人工认证删除接口
export const DeleteHuman = data => {
	return axios.request({
		url: servicePre + '/nameAuth/deleteHuman',
		params: data,
		method: 'delete'
	})
}
// 认证信息分页接口
export const SearchList = data => {
	return axios.request({
		url: servicePre + '/nameAuth/cardInfo',
		params: data,
		method: 'get'
	})
}
//收件人查询接口
export const getemail = data => {
	return axios.request({
		url: 'sys/api/v3/realNameAuth/getEmails',
		params: data,
		method: 'get',
	})
}

// 认证信息导出接口
export const exportFile = (data, exportType, emailAddress) => {
	return axios.request({
		url: servicePre + `/humanVerify/exportInfo/${exportType}/${emailAddress}`,
		data,
		method: 'post',
		contentType: 'multipart/form-data'
	})
}
// 图片下载接口
export const filedownload = data => {
	return axios.request({
		url: 'sys/api/v3/realNameAuth/getImage',
		params: data,
		method: 'get',
		responseType: 'blob'
	})
}
//客服支撑认证信息查询
export const customerService = data => {
	return axios.request({
		url: servicePre + '/nameAuth/customerService',
		params: data,
		method: 'get',
	})
}

//人工修改 用户认证
export const authenUpdate = (data) => {
	return axios.request({
		url: servicePre + `/humanVerify/authenticate/update`,
		params: data,
		method: 'post',
		// contentType: 'multipart/form-data'
	})
}

//认证信息 取消实名信息
export const cancelAuthenticationInfo = data => {
	return axios.request({
		url: servicePre + '/nameAuth/cancelAuthentication',
		params: data,
		method: 'post'
	})
}
