import axios from '@/libs/api.request'

const servicePre = '/pms'
// 规则管理分页查询接口
export const searchRule = (pageNo,pageSize,data) => {
  return axios.request({
    url:  servicePre+`/pms-realname/pages/${pageNo}/${pageSize}`,
    data,
    method: 'post'
  })
}
// 规则管理修改
export const updateRule = (id,data) => {
  return axios.request({
    url:  servicePre+`/pms-realname/update/${id}`,
    data,
    method: 'post'
  })
}
// 规则管理详情分页查询
export const searchDetail = id => {
  return axios.request({
    url:  servicePre+`/pms-realname-attr/rule/${id}`,
    // params: data,
    method: 'get'
  })
}
// 规则管理详情添加
export const addRule = (id,mcc) => {
  return axios.request({
    url: servicePre+ `/pms-realname-attr/add/${id}/${mcc}`,
    // params: data,
    method: 'post'
  })
}
// 规则管理详情删除
export const delRule = (id,mcc) => {
  return axios.request({
    url:  servicePre+`/pms-realname-attr/delete/${id}/${mcc}`,
    // params: data,
    method: 'post'
  })
}