import axios from '@/libs/api.request'
const servicePre = '/stat'
        // 重复使用报表导出
    export const countReuseExport = data => {
    return axios.request({
        url:  '/cms'+'/api/v1/packageCard/countReuseExport',
        data: data,
        responseType: 'blob',
        method: 'post'
    })
    }

        // 重复使用报表分页查询
    export const countReuse = data => {
    return axios.request({
        url:  '/cms'+'/api/v1/packageCard/countReuse',
        data: data,
        method: 'post'
    })
    }
        // 套餐激活报表导出
    export const StatActivereportDetailDownload = data => {
    return axios.request({
        url:  servicePre+'/activereport/detailDownload',
        params: data,
        responseType: 'blob',
        method: 'get'
    })
    }

        // 套餐激活报表分页查询
    export const StatActivereportPageList = data => {
    return axios.request({
        url:  servicePre+'/activereport/pageList',
        data: data,
        method: 'post'
    })
    }

        // 卡销售报表分页查询接口
    export const StatCardReport = data => {
    return axios.request({
        url:  servicePre+'/cardReport',
        params: data,
        method: 'get'
    })
    }

        // 卡销售报表导出接口
    export const StatCardReportExport = data => {
    return axios.request({
        url:  servicePre+'/cardReport/export',
        params: data,
        responseType: 'blob',
        method: 'get'
    })
    }

        // 导出接口
    export const StatOfflineExport = data => {
    return axios.request({
        url:  servicePre+'/offline/export',
        params: data,
        responseType: 'blob',
        method: 'get'
    })
    }

        // 线下导入接口
    export const StatOfflineImport = data => {
    return axios.request({
        url:  servicePre+'/offline/import',
        data: data,
        method: 'post'
    })
    }

        // 线下收入报表分页查询接口
    export const StatOfflinePageList = data => {
    return axios.request({
        url:  servicePre+'/offline/pageList',
        data: data,
        method: 'post'
    })
    }

        // 运营商结算报表导出
    export const StatOperatorsettleDetailDownload = data => {
    return axios.request({
        url:  servicePre+'/operatorsettle/detailDownload',
        params: data,
        responseType: 'blob',
        method: 'get'
    })
    }

        // 运营商结算报表分页查询
    export const StatOperatorsettlePageList = data => {
    return axios.request({
        url:  servicePre+'/operatorsettle/pageList',
        data: data,
        method: 'post'
    })
    }

        // 后付费结算报表导出
    export const StatPostpaidsettleDetailDownload = data => {
    return axios.request({
        url:  servicePre+'/postpaidsettle/detailDownload',
        params: data,
        responseType: 'blob',
        method: 'get'
    })
    }

        // 后付费结算报表分页查询
    export const StatPostpaidsettlePageList = data => {
    return axios.request({
        url:  servicePre+'/postpaidsettle/pageList',
        data: data,
        method: 'post'
    })
    }

        // 汇率分页查询接口
    export const StatRate = data => {
    return axios.request({
        url:  servicePre+'/rate',
        params: data,
        method: 'get'
    })
    }
        // 汇率分页查询接口
    export const importRate = data => {
    return axios.request({
        url:  servicePre+'/rate',
        data: data,
        method: 'post'
    })
    }

        // 汇率导出接口
    export const StatRateExport = data => {
    return axios.request({
        url:  servicePre+'/rate/export',
        params: data,
        responseType: 'blob',
        method: 'get'
    })
    }

        // 套餐分析报表导出接口
    export const StatReportPackageAnalysisExport = data => {
    return axios.request({
        url:  servicePre+'/report/package/analysis/export',
        params: data,
        responseType: 'blob',
        method: 'get'
    })
    }

        // 套餐分析报表查询接口
    export const StatReportPackageAnalysisSearch = data => {
    return axios.request({
        url:  servicePre+'/report/package/analysis/search',
        data: data,
        method: 'post'
    })
    }

        // 终端结算报表导出
    export const StatTerminalsettleDetailDownload = data => {
    return axios.request({
        url:  servicePre+'/terminalsettle/detailDownload',
        params: data,
        responseType: 'blob',
        method: 'get'
    })
    }

        // 终端结算报表分页查询
    export const StatTerminalsettlePageList = data => {
    return axios.request({
        url:  servicePre+'/terminalsettle/pageList',
        data: data,
        method: 'post'
    })
    }

    // 成本报表分页查询
    export const getCostReport = data => {
    return axios.request({
        url: '/charging/cost/supplierCostQuery',
        data: data,
        method: 'post'
    })
    }

    // 成本报表导出
    export const costReportDownload = data => {
    return axios.request({
        url: '/charging/cost/supplierCostExport',
        data: data,
        responseType: 'blob',
        method: 'post'
    })
    }

    // ESIM DOWNLOAD报表分页查询
    export const getEsimReport = data => {
    return axios.request({
        url: '/cms/esim/getEsimcardStats',
        params: data,
        method: 'get'
    })
    }

    // ESIM DOWNLOAD报表导出
    export const esimReportDownload = data => {
    return axios.request({
        url: '/cms/esim/exportEsimcardStats',
        params: data,
        method: 'get'
    })
    }
