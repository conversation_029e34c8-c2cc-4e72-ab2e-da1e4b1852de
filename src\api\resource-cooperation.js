import { data, param } from 'jquery'
import axios from '@/libs/api.request'
import store from '@/store'

const servicePre = '/rms/api/v1/assigned'
const servicePre_stat= '/stat/imsiFlow'
const servicePre_cms= '/cms/channel'

/**——————————————————————————————共用——————————————————————————————————————*/
//获取分配资源信息
export const getResourceInfo = data => {
  return axios.request({
    url: servicePre + '/Imsi',
    data,
    method: 'post'
  })
}

// 获取imsi费、流量计费规则下拉框
export const InfoOrder = data => {
  return axios.request({
    url: servicePre_cms + '/getInfo4Order2',
    params: data,
    method: 'get',
  })
}

//导出公用方法
export const commonExportFile = (param) => {
  let corpId = sessionStorage.getItem("corpId")
  return axios.request({
    url: param.url,
    params: {
      ...param.data,
      userId: (corpId && corpId != 'null' && corpId != 'undefined' && corpId != "") ? corpId : store.state.user.userId, // 将 userId 作为参数传递
    },
    method: 'post',
  });
};

/**——————————————————————————————资源管理首页——————————————————————————————————————*/
// 资源管理分页查询接口
export const getList = data => {
  return axios.request({
    url: servicePre_cms + '/distributors/getPage',
    params: data,
    method: 'get'
  })
}

//分配资源提交
export const resourceAllocation = data => {
  return axios.request({
    url: servicePre + '/Imsi',
    data,
    method: 'POST',
  })
}

/**——————————————————————————————资源查看——————————————————————————————————————*/

//资源查看分页接口
export const getResourceList = data => {
  return axios.request({
    url: servicePre + '/getImsiPage',
    params: data,
    method: 'get'
  })
}

//查看信息
export const checkImsi = data => {
  return axios.request({
    url: servicePre + '/checkImsi',
    params: data,
    method: 'get',
  })
}

//删除
export const deleteItem = data => {
  return axios.request({
    url: servicePre + '/deleteImsi',
    params: data,
    method: 'get',
  })
}

//冻结
export const freezeItem = data => {
  return axios.request({
    url: servicePre + '/freezeImsi',
    params: data,
    method: 'get'
  })
}

//恢复
export const recoverItem =data => {
  return axios.request({
    url: servicePre + '/recoverImsi',
    params: data,
    method: 'get'
  })
}

//单个修改资源 提交
export const singleResource = data => {
  return axios.request({
    url: servicePre + '/updateImsi',
    params: data,
    method: 'get'
  })
}

// 批量删除
export const batchDelete = data => {
  return axios.request({
    url: servicePre + '/deleteImsiList',
    data,
    method: 'post',
  })
}

//批量冻结
export const batchFreeze = data => {
  return axios.request({
    url: servicePre + '/freezeImsiList',
    data,
    method: 'post'
  })
}

//批量恢复
export const batchrRecover =data => {
  return axios.request({
    url: servicePre + '/recoverImsiList',
    data,
    method: 'post'
  })
}

//批量修改
export const batchResource = data => {
  return axios.request({
    url: servicePre + '/updateImsiList',
    data,
    method: 'post'
  })
}

//分配资源导出
export const exportFile = data => {
	return axios.request({
		url: servicePre + '/exportImsi',
		params: data,
		method: 'get',
	})
}

/**——————————————————————————————话单明细——————————————————————————————————————*/
//话单明细分页查询
export const getCallorderList = data => {
  return axios.request({
    url: servicePre_stat + '/getImsiFlow',
    params: data,
    method: 'get'
  })
}

//话单导出
export const exportCallorderFile = (data) => {
  return commonExportFile({
    url: servicePre_stat + '/exportResourceFlowDetail',
    data,
  });
};

/**——————————————————————————————账单统计——————————————————————————————————————*/
//账单统计分页查询
export const getBillingList = data => {
  return axios.request({
    url: servicePre_cms + '/getResourceFlowDetail',
    params: data,
    method: 'get'
  })
}

//账单统计导出
export const exportBillingFile = (data) => {
  return commonExportFile({
    url: servicePre_cms + '/exportResourceFlowDetail',
    data,
  });
};

