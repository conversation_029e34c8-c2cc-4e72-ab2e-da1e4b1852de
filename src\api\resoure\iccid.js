import axios from '@/libs/api.request'

const servicePre = '/rms/api/v1'

// 获取ICCID分页查询接口
export const getPage = data => {
  return axios.request({
    url: servicePre + '/ICCID/query',
    params: data,
    method: 'get'
  })
}
// 供应商ICCID批量入库接口
export const add = data => {
  return axios.request({
    url: servicePre + '/ICCID/add',
    data,
    method: 'post'
  })
}
// 供应商ICCID单个修改状态接口
export const updateStatus = data => {
  return axios.request({
    url: servicePre + '/ICCID/updateSingleStatus',
    params: data,
    method: 'put'
  })
}
// 供应商ICCID单个删除接口
export const del = data => {
  return axios.request({
    url: servicePre + '/ICCID/deleteSingle',
    params: data,
    method: 'delete'
  })
}
// 供应商ICCID批量修改接口
export const updateBatch = data => {
  return axios.request({
    url: servicePre + '/ICCID/updateStatus',
    data,
    method: 'put'
  })
}
// 供应商ICCID批量删除接口
export const delBatch = data => {
  return axios.request({
    url: servicePre + '/ICCID/delete',
    data,
    method: 'delete'
  })
}
