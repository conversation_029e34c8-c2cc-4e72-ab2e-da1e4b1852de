import axios from '@/libs/api.request'

const servicePre = '/rms/api/v1'

// 制卡管理分页查询接口
export const getPage = data => {
  return axios.request({
    url: servicePre + '/cardfile/queryList',
    params: data,
    method: 'get'
  })
}

//查看文件详情
export const cardFileView = id => {
  return axios.request({
    url: servicePre + `/cardfile/view/${id}`,
    method: 'get'
  })
}

// 制卡文件下载接口
export const downloadFile = (id, type) => {
  return axios.request({
    url: servicePre + `/cardfile/download/${id}/${type}`,
    method: 'get',
    responseType: 'blob'
  })
}

//回滚接口
export const doReback = id => {
  return axios.request({
    url: servicePre + `/cardfile/rollback/${id}`,
    method: 'get'
  })
}

// 欧洲卡时提前返回制卡数据
export const defaultContent = data => {
  return axios.request({
    url: servicePre + '/cardfile/getDefaultContent',
    params: data,
    method: 'get'
  })
}

// 新建制卡任务接口
export const addTask = data => {
  return axios.request({
    url: servicePre + '/cardfile/add',
    data,
    method: 'post'
  })
}
