import axios from '@/libs/api.request'

const servicePre = '/rms/api/v1'

// 获取MSISDN分页查询接口
export const getPage = data => {
  return axios.request({
    url: servicePre + '/MSISDN/query',
    params: data,
    method: 'get'
  })
}
// 供应商MSISDN批量入库接口
export const add = data => {
  return axios.request({
    url: servicePre + '/MSISDN/add',
    data,
    method: 'post'
  })
}
// 供应商MSISDN单个修改状态接口
export const updateStatus = data => {
  return axios.request({
    url: servicePre + '/MSISDN/updateSingleStatus',
    params: data,
    method: 'put'
  })
}
// 供应商MSISDN单个删除接口
export const del = data => {
  return axios.request({
    url: servicePre + '/MSISDN/deleteSingle',
    params: data,
    method: 'delete'
  })
}
// 供应商MSISDN批量修改接口
export const updateBatch = data => {
  return axios.request({
    url: servicePre + '/MSISDN/updateStatus',
    data,
    method: 'put'
  })
}
// 供应商MSISDN批量删除接口
export const delBatch = data => {
  return axios.request({
    url: servicePre + '/MSISDN/delete',
    data,
    method: 'delete'
  })
}

// 任务分页列表
export const getRecordPage = data => {
  return axios.request({
    url: servicePre + '/CMHKIMSI/selectTask',
		params: data,
    method: 'get'
  })
}

// 导入任务下载
export const exportFile = data => {
  return axios.request({
   url: servicePre + '/CMHKIMSI/fileDownLoad',
   params: data,
   method: 'get',
   responseType: 'blob'
  })
}