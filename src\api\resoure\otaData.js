import axios from '@/libs/api.request'

const servicePre = '/rms/api/v1'

// ota数据管理分页查询接口
export const getOtaDataPage = data => {
  return axios.request({
    url: servicePre + '/ota2/list',
    params: data,
    method: 'get'
  })
}

// 新增ota任务接口
export const addTask = data => {
  return axios.request({
    url: servicePre + '/ota2/add',
    data,
    method: 'post'
  })
}

// 删除ota任务接口
export const deleteTask = data => {
  return axios.request({
    url: servicePre + '/ota2/delete',
    data,
    method: 'post'
  })
}

// 文件下载接口
export const downloadFile = data => {
  return axios.request({
    url: servicePre + '/ota2/download',
    params: data,
    method: 'get',
  })
}

// adm模板文件下载接口
export const downloadTemplate = data => {
  return axios.request({
    url: servicePre + '/ota2/downloadTemplate',
    params: data,
    method: 'get',
    responseType: 'blob'
  })
}
