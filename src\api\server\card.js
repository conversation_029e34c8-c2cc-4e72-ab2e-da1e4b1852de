import axios from '@/libs/api.request'
// 获取用户列表
const servicePre = '/cms/api/v1/customerService'
export const searchByLocal = data => {
  return axios.request({
    url: servicePre + '/logs/searchByLocal',
    params: data,
    method: 'get'
  })
}

export const searchCardFlow = data => {
  return axios.request({
    url: servicePre + '/queryPackageFlow',
    params: data,
    method: 'get'
  })
}


export const downloadByLocal = data => {
  return axios.request({
    url: '/user/downloadByLocal',
    method: 'get',
    responseType: 'blob'
  })
}

//位置更新记录导出
export const downlocalUploadFile = data => {
  return axios.request({
    url: '/user/downlocalUploadFile',
    method: 'get',
    responseType: 'blob'
  })
}
//流量详情导出
export const downLoadTrafficFile = data => {
  return axios.request({
    url: '/user/downLoadTrafficFile',
    method: 'get',
    responseType: 'blob'
  })
}



// -----------------------------


// -----------------------------




//当前位置套餐查询
export const getLocalMeals = data => {
  return axios.request({
    url: servicePre + '/package/current',
    params: data,
    method: 'get'
  })
}

//当前位置套餐激活
export const doActivation = data => {
  return axios.request({
    url: servicePre + '/package/active',
    data: data,
    method: 'post'
  })
}

// 已购买套餐列表
export const searchPackageList = data => {
  return axios.request({
    url: servicePre + '/package/purchased',
    params: data,
    method: 'get'
  })
}

//已购买套餐列表——详情
export const getPurchasedDetail = data => {
  return axios.request({
    url: servicePre + '/package/purchasedDetail',
    params: data,
    method: 'get'
  })
}

//查询位置更新记录-主卡
export const getUpdateRecordsH = data => {
  return axios.request({
    // url: servicePre + '/luDetails/h',
    url: servicePre + '/luDetails/hOnly',
    params: data,
    method: 'get'
  })
}

//查询位置更新记录-V卡
export const getUpdateRecordsV = data => {
  return axios.request({
    url: servicePre + '/luDetails/v',
    params: data,
    method: 'get'
  })
}


//查询V卡分配信息-V卡
export const getAllocationV = data => {
  return axios.request({
    url: servicePre + '/surf/getMcc/v',
    params: data,
    method: 'get'
  })
}

// 查询定向应用上网详情
export const getAppInfo = data => {
  return axios.request({
    url: '/cms/channelSelfServer/directionalAppSurfDetail',
    params: data,
    method: 'get'
  })
}

// 提前回收
export const recoveryPackage = data => {
  return axios.request({
    url: servicePre + '/recoveryPackage',
    data: data,
    method: 'post'
  })
}

// 更换VIMSI
export const changeVIMSI = data => {
  return axios.request({
    url: servicePre + '/replaceVImsi',
    data: data,
    method: 'post'
  })
}

//查询短信模板列表
export const getSmsTempList = data => {
  return axios.request({
    url: '/sms/customer/list',
    data: data,
    method: 'post'
  })
}

//发送短信或者验证码
export const sendSms = data => {
  return axios.request({
    url: '/sms/customer/send',
    data: data,
    method: 'post'
  })
}

//卡片信息-流量详情
export const getflowinfo = data => {
  return axios.request({
    url: '/stat/finance/card/flow/info',
    params: data,
    method: 'post'
  })
}

// 已购买套餐——流量使用详情
export const getTrafficDetail = (packageUniqueId,data) => {
  return axios.request({
    url: `/stat/finance/get/flow/${packageUniqueId}`,
	data,
    method: 'post'
  })
}
// 已购买套餐——流量使用详情——流量详情
export const flowDetail = (packageUniqueId) => {
  return axios.request({
    url: `/stat/finance/get/flow/detail/${packageUniqueId}`,
    method: 'post'
  })
}
// 已购买套餐——流量使用详情——流量详情(导出)
export const exportFlow = (data) => {
  return axios.request({
    url: `/stat/finance/get/flow/detail/export/`,
    method: 'post',
	params: data,
	// responseType: 'blob'
  })
}
//卡片信息-查看实名制详情
export const getRealinfo = data => {
  return axios.request({
    url: '/cms/nameAuth/customerService',
    params: data,
    method: 'post'
  })
}
//已购买套餐-套餐生效日期修改
export const updateTime = data => {
  return axios.request({
    url: '/cms/package/updatePackageActiveTime',
    params: data,
    method: 'post'
  })
}

// 替换HIMSI
export const changeHIMSI = data => {
  return axios.request({
    url: servicePre + '/package/replaceIccid',
    data: data,
    method: 'post'
  })
}
// 已购买套餐——流量使用详情——CDR明细
export const getCDR = data => {
  return axios.request({
    url: servicePre +`/package/getCDR`,
    method: 'post',
    data
  })
}
// 已购买套餐——流量使用详情——CDR明细-覆盖时间
export const getCDRCoverageTime = data => {
  return axios.request({
    url: servicePre +`/package/getCoverHours`,
    method: 'post',
    data
  })
}
// 已购买套餐——详情-套餐ID-信息查询
export const getConsumption = data => {
  return axios.request({
    url: servicePre +`/package/getConsumption`,
    method: 'post',
    params: data
  })
}
