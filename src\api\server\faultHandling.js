import axios from "@/libs/api.request";
// import { param } from "jquery"; // 移除未使用的导入
const servicePre = "/rms/api/v1"; // 假设故障处理相关接口前缀
const omsServicePre ="/oms/api/v1";
const pmsServicePre = "/pms/api/v1"; // 添加PMS服务前缀

// 获取供应商列表
export const getSuppliers = () => {
  return axios.request({
    url: servicePre + "/supplier/query",
    method: "get",
    // 如果需要参数，可以添加 params: data
  });
};

// 获取国家列表 (包含大洲信息)
export const getCountries = (data) => {
  return axios.request({
    url: omsServicePre + "/country/getCountryByContinent", // 使用用户提供的国家查询接口地址
    method: "get",
    params: data,
  });
};
//获取大洲列表
export const getContinents = () => {
  return axios.request({
    url: omsServicePre + "/country/getContinent",
    method: "get",
  });
};

// 新增：保存供应商故障规则
export const saveSupplierFaultRule = (data) => {
  return axios.request({
    url: servicePre + "/supplierFaultRule/save",
    method: "post",
    data: data, // 使用 post 方法，数据放在 body 里
  });
};

// 新增：获取备用卡池列表
export const getCardPoolsByMcc = (data) => {
  return axios.request({
    url: pmsServicePre + "/cardPool/queryCardPoolByMccList",
    method: "post",
    data: data, // 包含 mccList 和 supplierId
  });
};

// 新增：查询单个卡池详情
export const getCardPoolById = (data) => {
  return axios.request({
    url: pmsServicePre + "/cardPool/getList",
    method: "post",
    data: data
  });
};

// 新增：查询故障国家列表
export const getTargetCountries = () => {
  return axios.request({
    url: servicePre + "/supplierFaultRule/getTargetCountry",
    method: "get"
  });
};

// 新增：故障修复接口
export const updateFaultStatus = (id) => {
  return axios.request({
    url: servicePre + "/supplierFaultRule/updateFaultStatus",
    method: "post",
    params: { id }
  });
};

// 新增：获取故障规则列表（分页查询）
export const getSupplierFaultRuleList = (params) => {
  return axios.request({
    url: servicePre + "/supplierFaultRule/getPageList",
    method: "post",
    data:params
  });
};

// 添加新的接口函数，用于根据目标国家MCC列表获取供应商
export function getSuppliersByMccList(data) {
  return axios.request({
    url: omsServicePre + '/country/getSuppliersByMccList',
    method: 'post',
    data:data
  });
}


// 新增：获取卡池套餐列表
export const getCardPoolPackageList = (data) => {
  return axios.request({
    url: pmsServicePre + "/cardPool/getPackageListByPoolId",
    method: "get",
    params: data
  });
};