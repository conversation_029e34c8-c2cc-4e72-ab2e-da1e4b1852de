import axios from '@/libs/api.request'
const servicePre = '/api/v1'

// 已购买套餐列表
export const searchPackageList = data => {
  return axios.request({
    url: servicePre + '/logs/searchPackageList',
    params: data,
    method: 'get'
  })
}

// export const getUsedPackages = data => {
//   return axios.request({
//     url: servicePre + '/logs/getUsedPackages',
//     params: data,
//     method: 'get'
//   })
// }

// 提前回收
export const recoveryPackage = data => {
  return axios.request({
    url: servicePre + '/logs/getUsedPackages',
    params: data,
    method: 'post'
  })
}
// 更换VIMSI
export const changeVIMSI = data => {
  return axios.request({
    url: servicePre + '/logs/getUsedPackages',
    params: data,
    method: 'post'
  })
}
