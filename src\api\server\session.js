import axios from '@/libs/api.request'
const servicePre = '/cms'

// 获取实时会话信息
export const getRealtimeSession = data => {
  return axios.request({
    url: servicePre + '/session/getCurrent',
    method: 'get',
    params: data,
  })
}

// 获取历史会话信息
export const getHistorySession = data => {
  return axios.request({
    url: servicePre + '/session/getHistory',
    method: 'post',
    data,
  })
}

// 导出历史会话信息
export const exportHistorySession = data => {
  return axios.request({
    url: servicePre + '/session/exportHistory',
    method: 'post',
    responseType: 'blob',
    data,
  })
}
