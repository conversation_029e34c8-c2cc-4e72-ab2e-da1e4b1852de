import axios from '@/libs/api.request'

const servicePre = '/sms'
/* 首页介绍 */
export const getIntroduce = data => {
  return axios.request({
    url: servicePre + `/regionalWelcome/introduce`,
    method: 'GET',
  })
}
/* 短信列表 */
export const getAreaWelcomeList = data => {
  return axios.request({
    url: servicePre + '/regionalWelcome/page',
    data,
    method: 'POST',
  })
}
//查询所有国家
export const getCountryList = data => {
  return axios.request({
    url: '/oms/api/v1/country/queryCounrtyList',
    method: 'get'
  })
}

/* 删除 */
export const delAreaWelcome = data => {
  return axios.request({
    url: servicePre + `/regionalWelcome/del`,
    method: 'post',
    params:data
  })
}

/* 根据模板获取地区/国家列表 */
export const getAreaForTemplate = data => {
  return axios.request({
    url: servicePre + `/regionalWelcome/getRegional`,
    method: 'post',
    data
  })
}

/* 根据模板获取套餐 */
export const getPackagesForTemplate = data => {
  return axios.request({
    url: servicePre + `/regionalWelcome/getPackage`,
    method: 'post',
    data
  })
}

/* 根据模板获取全量套餐 */
export const getAllPackagesForTemplate = data => {
  return axios.request({
    url: servicePre + `/regionalWelcome/getAllPackageFile`,
    method: 'post',
    data,
    responseType: 'blob'
    
  })
}
/* 获取大洲-国家 */
export const getContinentMcc = data => {
  return axios.request({
    url: 'oms/api/v1/country/queryCounrtyByContinent',
    method: 'get',
  })
}

/* 获取适用套餐列表 */
export const getPackageList = data => {
  return axios.request({
    url: 'pms/api/v1/package/smsGetPackage',
    data,
    method: 'post',
    responseType: 'blob',
  })
}

//上传适用套餐-套餐ID文件
export const uploadPackageFile = data => {
  return axios.request({
    url: 'pms/api/v1/package/importPackageId',
    data,
    method: 'post',
    responseType: 'blob',
  })
}

//新建欢饮短信
export const addWelcome = data => {
  return axios.request({
    url: 'sms/regionalWelcome/add',
    data,
    method: 'post'
  })
}

//编辑欢饮短信
export const editWelcome = data => {
  return axios.request({
    url: 'sms/regionalWelcome/edit',
    data,
    method: 'post'
  })
}

//下载套餐id模板文件
export const getPackageIdDwonload = data => {
  return axios.request({
    url: '/pms/api/v1/package/downSmsPackageFile',
    params: data,
    method: 'get',
    responseType: 'blob'
  })
}
