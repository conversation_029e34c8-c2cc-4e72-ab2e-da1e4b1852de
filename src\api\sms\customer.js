import axios from '@/libs/api.request'

const servicePre = '/sms'
/* 列表 */
export const getCustomerList = data => {
  return axios.request({
    url: servicePre + '/customer/pageList',
    data,
    method: 'POST',
  })
}


/* 新增 */
export const addCustomer = data => {
  return axios.request({
    url: servicePre + '/customer',
    data,
    method: 'POST',
  })
}

/* 更新 */
export const updateCustomer = (data) => {
  return axios.request({
    url: servicePre + '/customer',
    data,
    method: 'PUT',
  })
}

/* 删除 */
export const delCustomer = data => {
  return axios.request({
    url: servicePre + `/customer/delete`,
    method: 'post',
    data
  })
}


