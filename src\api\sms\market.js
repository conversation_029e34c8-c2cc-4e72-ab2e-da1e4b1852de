import axios from '@/libs/api.request'

const servicePre = '/sms'
/* 列表 */
export const getTaskList = data => {
  return axios.request({
    url: servicePre + '/task/pageList',
    data,
    method: 'POST',
  })
}

/* 群发任务详情 */
export const getTaskDetails = data => {
  return axios.request({
    url: servicePre + `/task/${data}`,
    method: 'GET',
  })
}


/* 新增 */
export const addTask = data => {
  return axios.request({
    url: servicePre + '/task',
    data,
    method: 'POST',
  })
}

/* 更新 */
export const updateTask = (data) => {
  return axios.request({
    url: servicePre + '/task',
    data,
    method: 'PUT',
  })
}

/* 取消 */
export const delTask = data => {
  return axios.request({
    url: servicePre + `/task/${data}`,
    method: 'put',
  })
}
/* 下载任务执行情况文件文件 */
export const getTaskDwonload = data => {
  return axios.request({
    url: servicePre + `/task/download/${data.taskId}?status=${data.status}`,
    method: 'post',
    responseType: 'blob'
  })
}
/* 上传文件号码文件新建任务 */
export const uploadTask = data => {
  return axios.request({
    url: servicePre + `/task/upload`,
    method: 'post',
    data
  })
}
/* 查看执行情况 */
export const sendResult = data => {
  return axios.request({
    url: servicePre + `/task/sendResult/${data.taskId}?status=${data.status}`,
    method: 'post',
  })
}



