import axios from '@/libs/api.request'

const servicePre = '/sms'
/* 列表 */
export const getNoticeList = data => {
  return axios.request({
    url: servicePre + '/notice/pageList',
    data,
    method: 'POST',
  })
}

/* 详情 */
export const getNoticeDetails = data => {
  return axios.request({
    url: servicePre+ `/notice/detail/${data}`,
    method: 'GET',
  })
}
/* 修改短信模板使用状态 */
export const changeDetailsStatus = data => {
  return axios.request({
    url:  servicePre+`/notice/detail/${data.id}?status=${data.status}`,
    method: 'PUT',
    
  })
}

/* 新增 */
export const addNotice = data => {
  return axios.request({
    url: servicePre + '/notice',
    data,
    method: 'POST',
  })
}

/* 更新 */
export const updateNotice = (data, id) => {
  return axios.request({
    url: servicePre + '/notice',
    data,
    method: 'PUT',
  })
}

/* 删除 */
export const delNotice = data => {
  return axios.request({
    url: servicePre + `/notice/delete`,
    method: 'post',
    data
  })
}
/* 获取场景列表 */
export const getSceneList = data => {
  return axios.request({
    url: servicePre + `/notice/scene`,
    method: 'get',
  })
}



