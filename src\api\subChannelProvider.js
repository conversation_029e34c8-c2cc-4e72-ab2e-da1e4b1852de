import axios from '@/libs/api.request'
const servicePre = '/cms'

// 子渠道商列表查询
export const subChannelProviderList = data => {
  return axios.request({
    url:  servicePre +'/subChannel/queryPage',
    data,
    method: 'post'
  })
}

// 子渠道商删除接口
export const delItem= (corpId) => {
  return axios.request({
    url: servicePre + `/subChannel/del/${corpId}`,
    method: 'delete'
  })
}

//账号权限列表
export const getAccountPermissionsList = data => {
  return axios.request({
    url: servicePre +'getAccountPermissionsList',
    data: data,
    method: 'post'
  })
}

// 新增子渠道商
export const addSubChannel = data => {
  return axios.request({
    url: servicePre + '/subChannel/add',
    data,
    method: 'post',
	contentType: 'multipart/form-data'
  })
}
// 修改子渠道商
export const editSubChannel = data => {
  return axios.request({
    url: servicePre + '/subChannel/edit',
    data,
    method: 'post',
	contentType: 'multipart/form-data'
  })
}