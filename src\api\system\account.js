import axios from '@/libs/api.request'
// 获取用户列表
const servicePre = '/sys/api/v1'
export const getAccountList = data => {
  return axios.request({
    url: servicePre + '/account/list',
    data,
    method: 'post'
  })
}
//删除账户
export const delOperator = data => {
  return axios.request({
    url: servicePre + '/user/deleteUser',
    params: data,
    method: 'PUT',
  })
}
//新增账户
export const addOperator = data => {
  return axios.request({
    url: servicePre + '/user/signIn',
    data,
    method: 'post',
  })
}
//编辑账户信息
export const editOperator = data => {
  return axios.request({
    url: servicePre + '/user/update',
    data,
    method: 'PUT',
  })
}

//修改密码
export const  rePassword= data => {
  return axios.request({
    url: servicePre + '/user/resetPasswd',
    data,
    method: 'PUT',
  })
}

// 发送验证码
export const  sendSms= data => {
  return axios.request({
    url: servicePre + '/user/phone',
    params: data,
    method: 'get',
  })
}

// 获取归属大区
export const getRegionList = data => {
  return axios.request({
    url: servicePre + '/region/list',
    params: data,
    method: 'get'
  })
}
