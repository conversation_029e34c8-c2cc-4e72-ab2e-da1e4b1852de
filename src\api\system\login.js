import axios from '@/libs/api.request'
const servicePre = ''

export const login = data => {
  return axios.request({
    url: servicePre + '/auth/login',
    data,
    method: 'post'
  })
}

export const getUserInfo = (token) => {
  return axios.request({
    url: 'get_info',
    params: {
      token
    },
    method: 'get'
  })
}

export const getSmsMsg = data => {
  return axios.request({
    url: servicePre + '/passport/smscode',
    params: data,
    method: 'get'
  })
}

export const getVerCode = data => {
  return axios.request({
    url: servicePre + '/passport/captcha',
    params: data,
    method: 'get'
  })
}

// export const resetPwdByPhone = data => {
//   return axios.request({
//     url: servicePre + '/passport/resetpass',
//     data,
//     method: 'PUT'
//   })
// }

export const logoutClearRedis = (name) => {
  return axios.request({
    url: '/auth/logout?userName='+name,
    method: 'delete'
  })
}
// 忘记密码重置密码
export const resetPwdByPhone = data => {
  return axios.request({
    url: '/sys/api/v1/user/userForgetpasswd/resetPasswd',
    params: data,
    method: 'PUT'
  })
}

//SSO单点登录
export const ssoLoginGDS = data => {
	return axios.request({
		url: '/aep/sso/ssoLoginGDS',
		data,
		method: 'post'
	})
}

//获取SSO登录开关配置
export const getSSOSwitchConfig = data => {
	return axios.request({
		url: '/aep/sso/getSSOIsOpen',
		data,
		method: 'get'
	})
}
