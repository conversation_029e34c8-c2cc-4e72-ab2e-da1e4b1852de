import axios from '@/libs/api.request'
// 获取用户列表
const servicePre = '/sys/api/v1/log'
export const exportLogList = data => {
  return axios.request({
    url: servicePre + '/login/export',
    responseType: 'blob',
    params: data,
    method: 'get'
  })
}
export const exportOperateLogList = data => {
  return axios.request({
    url: servicePre + '/operate/export',
    responseType: 'blob',
    params: data,
    method: 'get',
  },)
}
export const getLogList = data => {
  return axios.request({
    url: servicePre + '/login/list',
    data: data,
    method: 'post'
  })
}
export const getOperateList = data => {
  return axios.request({
    url: servicePre + '/operate/list',
    data: data,
    method: 'post'
  })
}
