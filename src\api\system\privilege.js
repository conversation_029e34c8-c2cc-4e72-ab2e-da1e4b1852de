import axios from '@/libs/api.request'
const servicePre='/sys/api/v1';

export const getRoleLimits = data => {
  return axios.request({
    url: servicePre+'/user/pagePrivilegesByRole/tree',
    params: data,
    method: 'get'
  })
}
export const getUserLimits = data => {
  return axios.request({
    url: servicePre+'/user/pagePrivilegesByUser/tree',
    params: data,
    method: 'get'
  })
}
export const getRoleList = (data) => {
  return axios.request({
    url: servicePre+'/role/roles',
    data,
    method: 'post'
  })
}
export const setRoleLimits = data => {
  return axios.request({
    url: servicePre+'/role/privileges/set',
    data,
    method: 'post',
  })
}

export const addRole = (data) => {
  return axios.request({
    url: servicePre+'/role/add',
    params: data,
    method: 'post',
  })
}
