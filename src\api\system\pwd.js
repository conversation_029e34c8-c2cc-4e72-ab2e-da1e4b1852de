import axios from '@/libs/api.request'
const servicePre = '/sys/api/v1'
//获取用户信息
export const  getUserInfo= data => {
  return axios.request({
    url: '/rcs/user/getUserInfo',
    data,
    method: 'post',
  })
}

//修改密码
export const  rePassword= (data,operatorId) => {
  return axios.request({
    url: servicePre + `/account/operator/${operatorId}/password`,
    data,
    method: 'PUT',
  })
}

//修改用户基本信息
export const  setUserInfo= (data,operatorId) => {
  return axios.request({
    url: servicePre + `/account/operator/${operatorId}`,
    data,
    method: 'PUT',
  })
}
