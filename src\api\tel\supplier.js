import axios from '@/libs/api.request'

const servicePre = '/rms/api/v1'
// 获取IMSI分页查询接口
export const Pagesearch = data => {
  return axios.request({
    url: servicePre + '/IMSITRIAD/query',
    params: data,
    method: 'get'
  })
}
// 供应商IMSI批量入库接口
export const batchUp = data => {
  return axios.request({
    url: servicePre + '/IMSITRIAD/add',
    data,
    method: 'post'
  })
}
// 供应商IMSI文件入库接口
export const fileUp = data => {
  return axios.request({
    url: servicePre + '/IMSITRIAD/excelAdd',
    data,
    method: 'post',
		contentType: 'multipart/form-data'
  })
}
// 供应商IMSI批量修改接口
export const updatebatch = data => {
  return axios.request({
    url: servicePre + '/IMSITRIAD/update',
    data,
    method: 'put'
  })
}
// 供应商IMSI批量删除接口
export const deletebatch = data => {
  return axios.request({
    url: servicePre + '/IMSITRIAD/delete',
    data,
    method: 'DELETE'
  })
}
// 供应商IMSI单个修改接口
export const updatesingle = data => {
  return axios.request({
    url: servicePre + '/IMSITRIAD/updateSingleStatus',
    params: data,
    method: 'put'
  })
}
// 供应商IMSI单个删除接口  
export const deletesingle = data => {
  return axios.request({
    url: servicePre + '/IMSITRIAD/deleteSingle',
    params: data,
    method: 'DELETE'
  })
}
// 获取所有供应商id和名称
export const supplierList = data => {
  return axios.request({
    url: servicePre + '/supplier/query',
    params: data,
    method: 'get'
  })
}

// 模板文件下载
export const download = data => {
  return axios.request({
    url:  '/rms/IMSITRIAD/template.csv',
    params: data,
    method: 'get',
	responseType: 'blob'
  })
}

// 任务分页列表
export const getRecordPage = data => {
  return axios.request({
    url: servicePre + '/CMHKIMSI/selectTask',
	params: data,
    method: 'get'
  })
}

// 导入任务下载
export const exportFile = data => {
  return axios.request({
   url: servicePre + '/CMHKIMSI/fileDownLoad',
   params: data,
   method: 'get',
   responseType: 'blob'
  })
}
