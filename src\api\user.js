import axios from '@/libs/api.request'
const servicePre = '/rcs/api/v1'
// export const login = ({ userName, password }) => {
//   const data = {
//     userName,
//     password
//   }
//   return axios.request({
//     url: 'login',
//     data,
//     method: 'post'
//   })
// }

export const login = data => {
  return axios.request({
    url: servicePre + '/passport/login',
    data,
    method: 'post'
  })
}

export const getUserInfo = (token) => {
  return axios.request({
    url: 'get_info',
    params: {
      token
    },
    method: 'get'
  })
}

export const logout = (token) => {
  return axios.request({
    url: 'logout',
    method: 'post'
  })
}

export const getVerCode = () => {
  return axios.request({
    url: servicePre + '/passport/captcha',
    method: 'get'
  })
}

//获取开关配置
export const getConfigure = data => {
  return axios.request({
    url: '/auth/code/getIsOpen',
    method: 'post',
		data,
  })
}

//获取用户是否需要验证码开关配置
export const getNeedCode = (data) => {
  return axios.request({
    url: '/sys/api/v1/user/getUserInformation',
    method: 'get',
		params:data,
  })
}

//下发短信验证码
export const sendMsgForReg = (data) => {
  return axios.request({
    url: '/sys/api/v1/user/getVerifyCode',
	params:data,
    method: 'get'
  })
}
// 忘记密码下发验证码
export const sendVerifyCode = data => {
  return axios.request({
    url: '/sys/api/v1/user/userForgetpasswd/sendVerifyCode',
    params:data,
    method: 'put'
  })
}
