<template>
  <div class="multi-column-selector">
    <!-- 使用v-model绑定value，确保双向绑定 -->
    <Select v-model="localValue" multiple filterable style="width:100%" :placeholder="placeholder"
       class="target-country-main-select" @on-change="handleTopSelectChange">
      <!-- Option value is now country.mcc -->
      <Option v-for="item in sortedCountriesForSelect" :value="item.mcc" :key="item.mcc">{{ item.countryEn }}</Option>
    </Select>
    
    <!-- 添加验证错误信息显示 -->
    <div class="validation-error" v-if="showError">{{ errorMessage }}</div>

    <!-- Continent Filter -->
    <div class="continent-buttons">
      <Button :type="currentContinent === 'all' ? 'primary' : 'default'" @click="selectContinent('all')" size="small">
        全部
      </Button>
      <Button v-for="continent in continentList" :key="continent.continentEn"
        :type="currentContinent === continent.continentEn ? 'primary' : 'default'" @click="selectContinent(continent.continentEn)"
        size="small">
        {{ continent.continentCn }}
      </Button>
    </div>

    <!-- Check All for Current View -->
    <div class="check-all-container">
      <Checkbox :indeterminate="indeterminate" :value="checkAll" @click.prevent.native="handleCheckAll">全选</Checkbox>
    </div>

    <!-- Multi-column Layout with Vertical Scroll -->
    <div class="content-wrapper vertical-scroll">
       <!-- CheckboxGroup直接使用value -->
      <CheckboxGroup v-model="localValue" @on-change="handleCheckboxGroupChange">
        <Row :gutter="16">
          <Col v-for="(column, colIndex) in displayCountryData" :key="colIndex" :xs="24" :sm="12" :md="8" :lg="6">
          <div class="column-card">
            <div class="column-header">
              <span class="header-check"></span>
              <span class="header-en">国家 (英文)</span>
              <span class="header-cn">国家 (中文)</span>
            </div>
            <div v-for="country in column.countries" :key="country.mcc" class="country-item">
              <Checkbox :label="country.mcc" class="item-check"><span></span></Checkbox>
              <span class="item-en" :title="country.countryEn">{{ country.countryEn }}</span>
              <span class="item-cn" :title="country.countryCn">{{ country.countryCn }}</span>
            </div>
          </div>
          </Col>
          <!-- Placeholder if no data -->
          <div v-if="!allCountries || allCountries.length === 0" class="no-data-placeholder">
            无数据
          </div>
        </Row>
      </CheckboxGroup>
    </div>
  </div>
</template>

<script>
import { throttle } from 'lodash-es';

export default {
  name: 'TargetCountrySelector',
  props: {
    // v-model integration - now expects and emits an array of MCCs
    value: {
      type: Array,
      default: () => []
    },
    // Full list of countries passed from parent
    allCountries: {
      type: Array,
      required: true,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择目标国家'
    },
    // Continent list passed from parent
    continentList: {
      type: Array,
      required: true,
      default: () => []
    },
    // 当前选中的大洲由父组件控制
    currentContinent: {
      type: String,
      default: 'all'
    },
    // 全选状态由父组件传入
    checkAll: {
      type: Boolean,
      default: false
    },
    // 不确定状态由父组件传入
    indeterminate: {
      type: Boolean,
      default: false
    },
    // 验证错误信息由父组件传入
    showError: {
      type: Boolean,
      default: false
    },
    // 错误信息由父组件传入
    errorMessage: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeColumns: 4,
      handleResizeThrottled: null,
      localValue: [...this.value],
      sortedCountriesForSelect: []
    }
  },
  computed: {
    // 当前视图中的MCC列表
    currentViewCountryMccs() {
      return this.allCountries.map(country => country.mcc).filter(mcc => mcc != null);
    },
    // 分列展示的数据
    displayCountryData() {
      const countriesToDisplay = this.allCountries.filter(c => c.mcc != null);
      const totalItems = countriesToDisplay.length;
      if (totalItems === 0) return [];

      const numCols = this.activeColumns;
      const grouped = Array.from({ length: numCols }, () => ({ countries: [] }));

      countriesToDisplay.forEach((country, index) => {
        if (numCols > 0) {
          const colIndex = index % numCols;
          if (grouped[colIndex]) {
             grouped[colIndex].countries.push(country);
          } else {
             console.error(`Error: Trying to push to non-existent column index ${colIndex}`);
          }
        }
      });
      return grouped;
    }
  },
  methods: {
    // 初始化所有国家列表的排序数据
    initSortedCountries() {
      // 确保这是所有国家的数据，获取一次后就不需要再更新了
      if (this.sortedCountriesForSelect.length === 0) {
        console.log('初始化全量国家数据，总数:', this.allCountries.length);
        this.sortedCountriesForSelect = [...this.allCountries].sort((a, b) => {
          const nameA = a.countryEn || '';
          const nameB = b.countryEn || '';
          if (!a.mcc || !b.mcc) {
            console.warn("国家数据缺少 MCC:", a, b);
          }
          return nameA.localeCompare(nameB);
        });
      }
    },
    // 切换大洲
    selectContinent(continent) {
      if (this.currentContinent !== continent) {
        this.$emit('continent-change', continent);
      }
    },
    // 顶部选择框变更
    handleTopSelectChange(selectedMccs) {
      // 不要立即更新本地值，等selection-change事件之后由父组件统一更新
      // this.localValue = selectedMccs;
      
      // 只发送selection-change事件，避免重复触发
      this.$emit('selection-change', selectedMccs, this.currentViewCountryMccs);
    },
    // 处理全选
    handleCheckAll() {
      const currentMccs = this.currentViewCountryMccs;
      const isCurrentlyAllSelected = this.checkAll;
      
      this.$emit('check-all', {
        currentMccs,
        isCurrentlyAllSelected
      });
    },
    // 处理复选框组变更
    handleCheckboxGroupChange(selectedMccs) {
      // 不要立即更新本地值，等selection-change事件之后由父组件统一更新
      
      // 只发送selection-change事件，避免重复触发
      this.$emit('selection-change', selectedMccs, this.currentViewCountryMccs);
    },
    // 更新列数
    updateActiveColumns() {
      const width = window.innerWidth;
      if (width >= 1400) {
        this.activeColumns = 4;
      } else if (width >= 1100) {
        this.activeColumns = 3;
      } else if (width >= 800) {
        this.activeColumns = 2;
      } else {
        this.activeColumns = 1;
      }
    },
    handleResize() {
      this.updateActiveColumns();
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (JSON.stringify(this.localValue) !== JSON.stringify(newVal)) {
          this.localValue = [...newVal];
        }
      },
      deep: true
    },
    localValue: {
      handler(newVal) {
        if (JSON.stringify(this.value) !== JSON.stringify(newVal)) {
          this.$emit('input', newVal);
        }
      },
      deep: true
    },
    allCountries: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          // 当allCountries变化且有数据时初始化排序列表
          this.initSortedCountries();
        }
      },
      immediate: true, // 立即执行
      deep: true
    }
  },
  created() {
    this.handleResizeThrottled = throttle(this.handleResize, 200);
  },
  mounted() {
    this.updateActiveColumns();
    window.addEventListener('resize', this.handleResizeThrottled);
    
    // 确保组件挂载后初始化排序国家列表
    this.$nextTick(() => {
      this.initSortedCountries();
    });
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResizeThrottled);
    if (this.handleResizeThrottled && this.handleResizeThrottled.cancel) {
        this.handleResizeThrottled.cancel();
    }
  }
}
</script>

<style scoped>
/* 样式保持不变 */
.multi-column-selector {
  /* padding: 15px; */
}

.validation-error {
  color: #ed4014;
  /* padding: 5px 0; */
  font-size: 12px;
}

.continent-buttons {
  margin-bottom: 15px;
  margin-top: 20px;
}

.continent-buttons .ivu-btn {
margin: 0 15px 0 0;
padding: 3px 10px;
}

.check-all-container {
  margin-bottom: 15px;
}

.content-wrapper {
  overflow-y: auto;
  max-height: 400px;
  min-height: 200px;
  padding-top: 0px !important;
  padding-left:0px !important;
  margin-bottom: 3px;
}

.column-card {
  border-bottom: 1px solid #e8eaec;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.column-header {
  display: flex;
  padding: 8px 12px;
  border: 1px solid #e8eaec;
  background-color: #f8f8f9;
  font-weight: bold;
  flex-shrink: 0;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1;
  justify-content: flex-start;
}

.column-header .header-check {
  flex-basis: 20px;
  flex-shrink: 0;
  margin-right:8px;
}

.column-header .header-cn,
.column-header .header-en,
.column-header .header-mcc {
  flex: 1;
  min-width: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

.country-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-bottom: 1px solid #e8eaec;
  border-left: 1px solid #e8eaec;
  border-right: 1px solid #e8eaec;
  min-height: 36px;
  flex-shrink: 0;
  justify-content: flex-start;
}

.column-card>div:last-of-type.country-item {
  border-bottom: none;
}

.country-item .item-check {
  flex-basis: 20px;
  flex-shrink: 0;
  margin-right: 8px;
}

.country-item .item-cn,
.country-item .item-en,
.country-item .item-mcc {
  flex: 1;
  min-width: 0;
  padding: 0 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}
.country-item .item-en {
  color: #808695;
}
.country-item .item-mcc {
  color: #515a6e;
  font-family: monospace;
}

.content-wrapper::-webkit-scrollbar {
  width: 6px;
}

.content-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-wrapper::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.content-wrapper::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

.no-data-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  width: 100%;
}
</style>
<style>
.target-country-main-select .ivu-select-dropdown{
  min-width: 300px !important;
}
</style>