<template>
	<div>
		<Form ref="formObj" :model="formObj" :label-width="90" :rules="ruleAddValidate">
			<FormItem label="申请原因" prop="replyReason">
				<Input v-model="formObj.replyReason" type="textarea" :rows="3" :maxlength="300"
					clearable placeholder="请输入原因" style="width: 350px;" />
			</FormItem>
			<FormItem label="浏览时间" prop="replyTime">
				<Select v-model="formObj.replyTime" style="width: 350px;">
					<Option v-for="(item,index) in timeList" :value="item" :key="item">{{item + "小时"}}</Option>
				</Select>
			</FormItem>
			<FormItem label="申请页面" prop="pageNumber">
				<Select v-model="formObj.pageNumber" style="width: 350px;">
					<Option v-if="page == 1" value="1">个人订单管理</Option>
					<Option v-else value="2">认证信息</Option>
				</Select>
			</FormItem>
		</Form>
		<div slot="footer" style="text-align: center;">
		  <Button @click="cancelModal">取消</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		  <Button v-has="'submit'" type="primary" @click="submit" v-preventReClick :loading="submitLoading">确定</Button>
		</div>
	</div>
</template>

<script>
	import {lockApplicationMethods, getTime} from '@/api/lockApplication.js'
	import lockApplicationVue from './lockApplication.vue'
	export default{
		props: {
		  page: {
		    type: Number,
		    default: "",
		  },
		  type: {
		    type: Number,
		    default: "",
		  },
		},
		data(){
			return{
				submitLoading: false,
				timeList: [],
				formObj: {
					replyReason: "",
					replyTime: "",
					pageNumber: ""
				},
				ruleAddValidate: {
					replyReason: [{
					  required: true,
					  type: 'string',
					  message: '原因不能为空',
					}],
					replyTime: [{
					  required: true,
					  message: '时间不能为空',
					}],
					pageNumber: [{
					  required: true,
					  type: 'string',
					  message: '页面不能为空',
					}],
				}
			}
		},
		mounted() {
			this.getTime()
			this.page == 1 ? this.formObj.pageNumber = "1" : this.formObj.pageNumber = "2"
		},
		methods: {
			cancelModal() {
				this.$emit('lockApplicationCancel')
			},
			submit() {
				this.$refs['formObj'].validate((valid) => {
				  if (valid) {
				    this.submitLoading = true;
				    lockApplicationMethods({
						replyReason: this.formObj.replyReason,
						replyTime: this.formObj.replyTime,
						pageNumber: this.formObj.pageNumber,
					}).then(res => {
				      if (res.code === '0000') {
				        var data = res.data;
				        this.$Notice.success({
				          title: '操作提示',
				          desc: '操作成功'
				        })
				        this.submitLoading = false;
						this.$emit('lockApplicationCancel')
						this.$emit('goPageFirst')
				      } else {
				        throw res
				      }
				    }).catch((err) => {
				      console.log(err)
				    }).finally(() => {
				      this.submitLoading = false;
				    })
				  }
				})
			},
			/*——————————————————————— 初始化信息 ————————————————————————*/
			// 时间
			getTime() {
				getTime().then(res => {
					if (res.code === '0000') {
						this.timeList = res.data;
					}
				}).catch((err) => {
					console.log(err)
				})
			},
		}
	}
</script>

<style>
</style>