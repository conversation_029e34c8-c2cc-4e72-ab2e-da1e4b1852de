<template>
	<div ref="dom" class="charts chart-bar">
	</div>
</template>

<script>
	import echarts from 'echarts'
	import tdTheme from './theme.json'
	import {
		on,
		off
	} from '@/libs/tools'
	echarts.registerTheme('tdTheme', tdTheme)
	export default {
		name: 'ChartBar',
		props: {
			value: Array
		},
		data() {
			return {
				dom: null
			}
		},
		methods: {
			resize() {
				this.dom.resize()
			}
		},
		mounted() {
			this.$nextTick(() => {
				let barData = Object.values(this.value)
				let seriesData = barData.map((item) => {
					return item.statCount
				})
				let xAxisData = barData.map((item) => {
					return item.statTime
				})
				
				let option = {
					title: [{
							text: this.$t('packageOrdered'),
							top: '0',
							left: '0',
							textStyle: {
								width: '70',
								height: '20',
								fontSize: '14',
								// fontFamily: 'PingFang SC-Semibold, PingFang SC',
								fontWeight: '600',
								color: '#323232',
								lineheight: '20',
							}
						},
						{
							text: this.$t('months12'),
							top: '0',
							right: '0',
							textStyle: {
								width: '85',
								height: '20',
								fontSize: '14',
								// fontFamily: 'PingFang SC-Regular, PingFang SC',
								fontWeight: '400',
								color: ' #666666',
								lineHeight: '20',
							}
						}
					],
					tooltip: {
						trigger: 'item',
						showContent: true,
						confine: false,
						borderColor: "#fff",
						position: 'top',
						axisPointer: {
							type: 'shadow'
						},
						extraCssText: "padding: 8px 10px;text-align: center;box-shadow: 0px 6px 17px 2px rgba(0,0,0,0.1);border-radius: 8px;",
						textStyle: {
							// fontFamily: 'PingFang SC-Semibold, PingFang SC',
							lineHeight: '20',
						},
						backgroundColor: '#FFF',
						formatter: function(params, ticket, callback) {
							var xName= params.name;
							var yValue= params.value;
							var tip =
							`<div>
								<p style="height:16px;font-size:12px;font-weight:400;line-height:16px;color:#666666;">
								${xName}</p>
								<p style="height:20px;font-size:14x;font-weight:600;line-height:20px; 
								color: #323232;">${yValue}</p>
							</div>`
							return tip;
						},

					},
					grid: {
						left: '0',
						right: '0',
						bottom: '0',
						top: '42',
						containLabel: true
					},
					xAxis: [{
						type: 'category',
						data: xAxisData,
						axisTick: {
							alignWithLabel: true
						},
						splitLine: {
							show: false
						}
					}],
					yAxis: [{
						type: 'value',
						axisLabel: {
							formatter: "{value}",
						},
						axisLine: {
							show: false
						}
						
					}],
					series: [{
							name: 'Direct',
							type: 'bar',
							barWidth: '12',
							barGap:'85%',
							barCategoryGap: '85%',
							data: seriesData,
							itemStyle: {
								borderRadius: [2, 2, 0, 0],
								color: '#4289F4',
							},
							// emphasis: {
							// 	itemStyle: {
							// 		color: '#ECF3FE',
							// 	}
							// }
						},

					]
				}
				this.dom = echarts.init(this.$refs.dom, 'tdTheme')
				this.dom.setOption(option)
				on(window, 'resize', this.resize)
			})
		},
		beforeDestroy() {
			off(window, 'resize', this.resize)
		}
	}
</script>