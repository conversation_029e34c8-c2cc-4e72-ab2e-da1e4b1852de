<template>
	<div ref="dom" class="charts chart-pie"></div>
</template>

<script>
	import echarts from 'echarts'
	import tdTheme from './theme.json'
	import {
		on,
		off
	} from '@/libs/tools'
	echarts.registerTheme('tdTheme', tdTheme)
	export default {
		name: 'Chart<PERSON>ie',
		props: {
			value: Array,
			title: String,
			text: String,
			subtext: String,
			inText: String,
			number: String,
			minAngle: Number,
		},
		data() {
			return {
				dom: null
			}
		},
		methods: {
			resize() {
				this.dom.resize()
			}
		},
		mounted() {
			this.$nextTick(() => {
				let legend = this.value.map(_ => _.name)
				let option = {
					title: {
						text: this.text,
						top: '0',
						left: '0',
						textStyle: {
							color: '#323232',
							height: '20',
							fontSize: '14',
							fontWeight: '600',
							lineHeight: '20',
							// fontFamily: 'PingFang SC-Regular, PingFang SC',
						}
					},
					tooltip: {
						trigger: 'item',
						extraCssText: 'box-shadow: 0px 6px 17px 2px rgba(0,0,0,0.1);border-radius: 8px;padding: 8px;background-color: #fff;',
						textStyle: {
							color: '#000',
							fontSize: '14',
							// fontFamily: 'PingFang SC-Semibold, PingFang SC',
							lineHeight: '20',
						},
						axisPointer: {
							type: 'shadow'
						},
						formatter: function(parms) {
							var tip =
								`<div>
								<div style="display: flex;justify-content: start;align-items: center;">
									${parms.marker}
									<p style="font-size: 12px;height: 16px;
									font-weight: 400;color: #7C7C7C;line-height: 16px;">
										${parms.name}&nbsp;&nbsp;${parms.value}</br>
									</p>
								</div>
								<p style="text-indent: 18px;height: 20px;font-size: 14px;
								font-weight: 600;color: #000000;line-height: 20px;">${parms.percent}%</p>
							</div>`
							return tip
						}
					},
					legend: {
						bottom: '0',
						left: 'center',
						icon: 'circle',
						itemHeight: 6,
						textStyle: {
							height: '16',
							fontSize: '12',
							// fontFamily: 'PingFang SC-Regular, PingFang SC',
							fontWeight: '400',
							color: '#949698',
							lineHeight: '16'
						},
						selectedMode: false,
						selectedOffset: 1,
					},
					// color: ['#4289F4','#cde1fe'],
					color: [{
						type: "linear",
						colorStops: [{
							offset: 0, color: '#4289F4'
						},{
							offset: 1, color: '#4289F4'
						}],
						global: false
					},{
						type: "linear",
						colorStops: [{
							offset: 0, color: '#dbebfe'
						},{
							offset: 1, color: '#dbebfe'
						}],
						global: false
					}],
					series: [{
						name: this.inText,
						type: 'pie',
						radius: ['50%', '65%'],
						data: this.value,
						label: {
							show: false,
							position: 'center'
						},
						labelLine: {
							show: false
						},
						minAngle: this.minAngle,
						// itemStyle: {
						// 	normal: {
						// 		borderColor: '#fff',
						// 		borderWidth: 4,
						// 	},
						// 	emphasis: {
						// 		borderWidth: 0,
						// 	}
						// },
						avoidLabelOverlap: false,
					}],

					graphic: {
						elements: [{
								type: 'text',
								left: 'center',
								top: '42%',
								style: {
									text: this.number,
									fontSize: 24,
									fontWeight: '600',
									textAlign: 'center',
									height: 28,
									color: "#000",
									lineHeight: 28,
								}
							},
							{
								type: 'text',
								left: 'center',
								top: '55%',
								style: {
									text: this.inText.length > 15 ? this.inText.slice(0,16) +
									 '\n' + this.inText.slice(16) : this.inText,
									fontSize: 12,
									fontWeight: '400',
									textAlign: 'center',
									height: 16,
									color: '#737373',
									lineHeight: 16,
								}
							}
						]
					}
				} 
				this.dom = echarts.init(this.$refs.dom, 'tdTheme')
				this.dom.setOption(option)
				on(window, 'resize', this.resize)
			})
		},
		
		beforeDestroy() {
			off(window, 'resize', this.resize)
		}
	}
</script>