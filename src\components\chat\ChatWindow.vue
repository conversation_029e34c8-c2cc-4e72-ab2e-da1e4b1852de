<template>
  <div class="chat-window">
    <!-- 收起状态的侧边栏 -->
    <div class="collapsed-sidebar" v-if="isCollapsed">
      <div class="logo">
        <img src="/img/logo-min.1703d006.jpg" alt="CMI Logo" />
      </div>
      <Tooltip content="打开边栏" placement="right">
        <div class="sidebar-toggle" @click="toggleCollapse">
          <Icon type="md-menu" />
        </div>
      </Tooltip>
      <Tooltip content="开启新会话" placement="right">
        <div class="new-chat" @click="createNewSession">
          <Icon type="md-add" />
        </div>
      </Tooltip>
    </div>

    <!-- 展开状态的侧边栏 -->
    <div class="chat-sidebar" v-else>
      <div class="sidebar-header">
        <div class="logo-title">AI助手</div>
        <Tooltip content="收起边栏" placement="right">
          <div class="sidebar-toggle" @click="toggleCollapse">
            <Icon type="md-menu" />
          </div>
        </Tooltip>
      </div>

      <div class="new-chat-btn" @click="createNewSession">
        <Icon type="md-add" />
        <span>开启新会话</span>
      </div>

      <div class="session-list">
        <div
          v-for="(group, groupIndex) in groupedSessions"
          :key="'group-' + groupIndex"
          class="session-group"
        >
          <div class="group-title">{{ group.title }}</div>
          <div
            v-for="item in group.sessions"
            :key="item.id"
            class="session-item"
            :class="{ active: currentSessionId === item.id }"
            @click="selectSession(item)"
          >
            <div class="session-info">
              <span class="session-title">{{ item.title || '新会话' }}</span>
              <span class="session-time">{{ formatTime(item.updateTime) }}</span>
            </div>
            <Button
              type="text"
              icon="md-trash"
              @click.stop="deleteSession(item.id)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="chat-main" :class="{ 'expanded': isCollapsed }">
      <div class="chat-header">
        <span class="chat-title">{{ (currentSession && currentSession.title) || '新会话' }}</span>
        <div class="header-actions">
          <Button type="text" icon="md-exit" @click="exitChat">退出</Button>
        </div>
      </div>

      <div class="message-list" ref="messageList">
        <div class="message-item-wrapper">
          <div
            v-for="(item, index) in currentMessages"
            :key="index"
            class="message-item"
            :class="item.role"
          >
            <template v-if="item.role === 'user'">
              <Icon type="md-contact" size="32" style="margin: 0 8px;" />
            </template>
            <template v-else>
              <Avatar :src="item.avatar" />
            </template>
            <div class="message-content">
              <div v-if="item.thinking" class="thinking">
                <Icon type="ios-loading" class="loading-icon" />
                <span>思考中...</span>
              </div>
              <div v-else-if="item.streaming" class="streaming">
                <!-- 添加一个固定大小的容器 -->
                <div class="typewriter-container">
                  <!-- 完整内容区域（隐藏，用于占位） -->
                  <div class="full-content markdown-content" v-html="renderMarkdown(item.content)" ref="fullContent"></div>
                  <!-- 打字区域（显示，逐渐显示的内容） -->
                  <div :id="`typedText-${index}`" class="typed-content markdown-content"></div>
                </div>
              </div>
              <div v-else class="content">
                <div v-if="item.hasImage" class="image-content">
                  <img :src="item.imageUrl" />
                  <div class="markdown-content" v-html="renderMarkdown(item.content)"></div>
                </div>
                <div v-else class="markdown-content" v-html="renderMarkdown(item.content)"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="input-area">
        <div class="toolbar">
          <Upload
            action="/api/upload"
            :before-upload="handleBeforeUpload"
            :on-success="handleUploadSuccess"
          >
            <Button type="text">
              <Icon type="md-image" />
            </Button>
          </Upload>
        </div>
        <Input
          v-model="inputMessage"
          type="textarea"
          :rows="4"
          placeholder="请输入您的问题..."
          @keyup.enter.native="sendMessage"
        />
        <div class="action-buttons">
          <Button type="primary" :loading="sending" @click="sendMessage">
            发送
          </Button>
          <Button @click="clearChat">清空对话</Button>
          <Button @click="stopChat" :disabled="!sending">停止回答</Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VirtualList from 'vue-virtual-scroll-list'
import { formatTime } from '@/libs/util'
import { sendChatMessage } from '@/api/ai'

export default {
  name: 'ChatWindow',
  components: {
    VirtualList
  },
  data() {
    return {
      isCollapsed: true,
      sidebarWidth: 300,
      mainWidth: 500,
      isResizing: false,
      startX: 0,
      startWidth: 0,
      sessions: [
        {
          id: '1',
          title: '示例会话',
          createTime: new Date(),
          updateTime: new Date(),
          messages: []
        }
      ],
      currentSessionId: '1',
      currentMessages: [],
      inputMessage: '',
      sending: false,
      abortController: null,
      defaultAvatars: {
        user: '', // 不再用图片
        assistant: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      }
    }
  },
  computed: {
    currentSession() {
      return this.sessions.find(s => s.id === this.currentSessionId)
    },
    groupedSessions() {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)

      const sevenDaysAgo = new Date(today)
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

      const thirtyDaysAgo = new Date(today)
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const groups = [
        { title: '今天', sessions: [] },
        { title: '昨天', sessions: [] },
        { title: '7天内', sessions: [] },
        { title: '30天内', sessions: [] },
        { title: '更早', sessions: [] }
      ]

      this.sessions.forEach(session => {
        const updateTime = new Date(session.updateTime)
        updateTime.setHours(0, 0, 0, 0)

        if (updateTime.getTime() === today.getTime()) {
          groups[0].sessions.push(session)
        } else if (updateTime.getTime() === yesterday.getTime()) {
          groups[1].sessions.push(session)
        } else if (updateTime >= sevenDaysAgo) {
          groups[2].sessions.push(session)
        } else if (updateTime >= thirtyDaysAgo) {
          groups[3].sessions.push(session)
        } else {
          groups[4].sessions.push(session)
        }
      })

      // 只返回有会话的分组
      return groups.filter(group => group.sessions.length > 0)
    }
  },
  methods: {
    formatTime(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hour = String(d.getHours()).padStart(2, '0')
      const minute = String(d.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hour}:${minute}`
    },
    startResize(e) {
      this.isResizing = true
      this.startX = e.clientX
      this.startWidth = this.sidebarWidth

      document.addEventListener('mousemove', this.onResize)
      document.addEventListener('mouseup', this.stopResize)
    },
    onResize(e) {
      if (!this.isResizing) return
      const delta = e.clientX - this.startX
      const newWidth = this.startWidth + delta
      if (newWidth >= 200 && newWidth <= 500) {
        this.sidebarWidth = newWidth
      }
    },
    stopResize() {
      this.isResizing = false
      document.removeEventListener('mousemove', this.onResize)
      document.removeEventListener('mouseup', this.stopResize)
    },
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
    },
    createNewSession() {
      const sessionCount = this.sessions.filter(s => s.title.startsWith('新会话')).length
      const newTitle = sessionCount > 0 ? `新会话${sessionCount + 1}` : '新会话'

      const newSession = {
        id: Date.now().toString(),
        title: newTitle,
        createTime: new Date(),
        updateTime: new Date(),
        messages: [
          {
            role: 'assistant',
            content: '你好！我是AI助手。我可以帮您：\n\n1. 回答问题和解决问题\n2. 提供专业建议\n3. 协助编写代码\n4. 分析数据和文档\n\n请告诉我您需要什么帮助？',
            timestamp: new Date(),
            avatar: this.defaultAvatars.assistant
          }
        ]
      }
      this.sessions.unshift(newSession)
      this.selectSession(newSession)
    },
    selectSession(session) {
      this.currentSessionId = session.id
      this.currentMessages = session.messages || []
      // 切换会话后滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    deleteSession(sessionId) {
      const index = this.sessions.findIndex(s => s.id === sessionId)
      if (index > -1) {
        this.sessions.splice(index, 1)
        if (this.currentSessionId === sessionId) {
          this.currentSessionId = null
          this.currentMessages = []
        }
      }
    },
    exitChat() {
      this.$emit('close')
    },
    updateSessionTitle(sessionId, title) {
      const session = this.sessions.find(s => s.id === sessionId)
      if (session) {
        session.title = title
        session.updateTime = new Date()
      }
    },
    loadSessionMessages(sessionId) {
      // 添加初始欢迎消息
      this.currentMessages = [
        {
          role: 'assistant',
          content: '你好！我是AI助手。我可以帮您：\n\n1. 回答问题和解决问题\n2. 提供专业建议\n3. 协助编写代码\n4. 分析数据和文档\n\n请告诉我您需要什么帮助？',
          timestamp: new Date(),
          avatar: this.defaultAvatars.assistant
        }
      ]
    },
    async sendMessage() {
      if (!this.inputMessage.trim() || !this.currentSessionId) return

      const currentInput = this.inputMessage
      this.inputMessage = '' // 立即清空输入框

      const userMessage = {
        role: 'user',
        content: currentInput,
        timestamp: new Date(),
        avatar: this.defaultAvatars.user
      }

      this.currentMessages.push(userMessage)
      this.scrollToBottom()

      // 更新会话标题
      const currentSession = this.sessions.find(s => s.id === this.currentSessionId)
      if (currentSession) {
        if (currentSession.title.startsWith('新会话')) {
          const question = currentInput.length > 20
            ? currentInput.substring(0, 20) + '...'
            : currentInput
          this.updateSessionTitle(this.currentSessionId, question)
        }
      }

      // 添加AI思考中的状态
      this.currentMessages.push({
        role: 'assistant',
        thinking: true,
        content: '',
        avatar: this.defaultAvatars.assistant
      })
      this.scrollToBottom()

      try {
        this.sending = true

        // 创建AbortController用于取消请求
        this.abortController = new AbortController()

        // 调用API
        const body = {
          message: {
            content: currentInput
          }
        }

        // 使用封装的sendChatMessage方法发送请求
        const response = await sendChatMessage(body, this.abortController)
        console.log(response,"response-----")

        // 处理流式响应
        const reader = response.body.getReader()

        // 移除思考中的状态
        const lastMessage = this.currentMessages[this.currentMessages.length - 1]
        if (lastMessage.thinking) {
          this.currentMessages.pop()
        }

        // 创建新的助手消息
        const messageIndex = this.currentMessages.length
        this.currentMessages.push({
          role: 'assistant',
          streaming: true,
          content: '',
          avatar: this.defaultAvatars.assistant
        })

        // 流式处理变量
        let accumulatedContent = ''  // 累积的完整响应内容
        let displayedContent = ''    // 已经显示的内容
        let previousChunks = []      // 存储所有数据块用于恢复
        const decoder = new TextDecoder()

        // 修改updateTypedContent方法
        const updateTypedContent = (newContent) => {
          if (newContent.length > displayedContent.length) {
            // 计算需要新增的内容
            const newTextPart = newContent.substring(displayedContent.length)
            displayedContent = newContent

            // 获取要更新的元素
            this.$nextTick(() => {
              const element = document.getElementById(`typedText-${messageIndex}`)
              if (element) {
                // 先将完整内容渲染到隐藏元素中，确保布局稳定
                const fullContentEls = this.$refs.fullContent
                if (fullContentEls && fullContentEls.length > 0) {
                  const fullContentEl = fullContentEls[fullContentEls.length - 1]
                  fullContentEl.innerHTML = this.renderMarkdown(newContent)
                }

                // 创建一个函数来逐字符添加文本
                let index = 0

                const typeNextChar = () => {
                  if (index < newTextPart.length) {
                    // 只更新显示的部分，不影响整体布局
                    const partialContent = displayedContent.substring(0, displayedContent.length - newTextPart.length + index + 1)
                    element.innerHTML = this.renderMarkdown(partialContent)
                    index++
                    setTimeout(typeNextChar, 10) // 调整打字速度
                  }
                }

                // 开始打字效果
                typeNextChar()

                // 更新消息内容（不会影响显示效果，仅用于保存）
                this.currentMessages[this.currentMessages.length - 1].content = newContent
                this.scrollToBottom()
              }
            })
          }
        }

        // 初始化打字区域
        this.$nextTick(() => {
          const element = document.getElementById(`typedText-${messageIndex}`)
          if (element) {
            element.innerHTML = ''
          }
        })

        // 更健壮的JSON解析方法
        const safeJsonParse = (text) => {
          try {
            return JSON.parse(text)
          } catch (e) {
            // 如果解析失败，返回null而不是抛出错误
            return null
          }
        }

        // 流式读取循环
        while (true) {
          try {
            const { done, value } = await reader.read()

            if (done) {
              break
            }

            // 解码接收到的数据
            const chunk = decoder.decode(value, { stream: true })
            previousChunks.push(chunk)

            // 处理SSE格式的响应数据
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data:')) {
                const jsonString = line.substring(5) // 去除 "data:" 前缀
                const data = safeJsonParse(jsonString)

                // 明确提取message.content字段
                if (data && data.message && typeof data.message.content === 'string') {
                  // 直接使用message.content作为新内容
                  accumulatedContent = data.message.content
                  updateTypedContent(accumulatedContent)
                }
              }
            }
          } catch (readError) {
            // 捕获读取过程中的错误，可能是由于请求被中断
            if (readError.name === 'AbortError') {
              console.log('请求已被用户取消')
              // 标记消息为被中断
              this.currentMessages[this.currentMessages.length - 1].content += '\n\n[回答已中断]'
              break
            } else {
              throw readError
            }
          }
        }

        // 恢复机制: 如果没有获取到内容，尝试从所有块中提取
        if (!accumulatedContent && previousChunks.length > 0) {
          try {
            console.log('尝试从累积响应中恢复内容...')
            const fullResponse = previousChunks.join('')

            // 查找所有data:开头的行，提取最后一个的内容
            const dataLines = fullResponse.split('\n').filter(line => line.startsWith('data:'))
            if (dataLines.length > 0) {
              const lastDataLine = dataLines[dataLines.length - 1]
              const jsonString = lastDataLine.substring(5)
              const data = safeJsonParse(jsonString)
              
              if (data && data.message && typeof data.message.content === 'string') {
                accumulatedContent = data.message.content
                updateTypedContent(accumulatedContent)
                console.log('内容恢复成功')
              }
            }
          } catch (finalError) {
            console.error('最终内容提取失败:', finalError)
          }
        }

        // 更新为最终消息 (移除streaming标记)
        this.currentMessages[this.currentMessages.length - 1] = {
          role: 'assistant',
          content: accumulatedContent || '获取响应失败，请重试',
          avatar: this.defaultAvatars.assistant
        }

        // 保存消息到会话历史
        const session = this.sessions.find(s => s.id === this.currentSessionId)
        if (session) {
          session.messages = [...this.currentMessages]
        }

        this.scrollToBottom()
      } catch (error) {
        if (error.name !== 'AbortError') {
          // 非中断导致的错误才显示错误信息
          this.$Message.error('发送失败')
          console.error('发送消息失败:', error)

          // 移除思考中的状态
          const lastMessage = this.currentMessages[this.currentMessages.length - 1]
          if (lastMessage.thinking) {
            this.currentMessages.pop()
          }

          // 添加错误消息
          this.currentMessages.push({
            role: 'assistant',
            content: '抱歉，发生了错误，请稍后再试。',
            avatar: this.defaultAvatars.assistant
          })
        }
      } finally {
        this.sending = false
        this.abortController = null
      }
    },
    stopChat() {
      // 中断请求
      if (this.abortController) {
        console.log('停止回答')
        this.abortController.abort()
        this.abortController = null
        this.sending = false
      }

      // 检查是否有"思考中"状态的消息
      const lastMessage = this.currentMessages[this.currentMessages.length - 1]
      if (lastMessage && lastMessage.thinking) {
        // 替换思考中的消息为停止回答
        this.currentMessages.pop()
        this.currentMessages.push({
          role: 'assistant',
          content: '已停止回答',
          avatar: this.defaultAvatars.assistant
        })
      }
    },
    clearChat() {
      // 如果有正在进行的请求，先中断
      if (this.sending && this.abortController) {
        this.abortController.abort()
        this.abortController = null
        this.sending = false
      }

      if (this.currentSessionId) {
        // 重置会话标题
        const session = this.sessions.find(s => s.id === this.currentSessionId)
        if (session) {
          const sessionCount = this.sessions.filter(s => s.title.startsWith('新会话')).length
          session.title = sessionCount > 0 ? `新会话${sessionCount + 1}` : '新会话'
        }

        // 清空消息
        this.currentMessages = []
        const currentSession = this.sessions.find(s => s.id === this.currentSessionId)
        if (currentSession) {
          currentSession.messages = []
        }
      }
    },
    handleBeforeUpload(file) {
      // 验证文件类型和大小
      return true
    },
    handleUploadSuccess(response) {
      // 处理上传成功后的逻辑
    },
    renderMarkdown(text) {
      if (!text) return '';

      // 处理代码块，自动检测并格式化JSON
      text = text.replace(/```(?:json)?\s*([\s\S]*?)```/g, (match, code) => {
        // 尝试格式化JSON
        try {
          if (code.trim().startsWith('{') || code.trim().startsWith('[')) {
            const jsonObj = JSON.parse(code.trim());
            const formattedJson = JSON.stringify(jsonObj, null, 2);
            return `<pre><code style="white-space: pre-wrap; word-break: break-word;">${formattedJson.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>`;
          }
        } catch (e) {
          // 如果不是有效的JSON，就原样返回
        }
        return `<pre><code style="white-space: pre-wrap; word-break: break-word;">${code.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>`;
      });

      // 处理行内代码
      text = text.replace(/`([^`]+)`/g, '<code>$1</code>');

      // 处理粗体
      text = text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

      // 处理斜体
      text = text.replace(/\*([^*]+)\*/g, '<em>$1</em>');

      // 处理链接
      text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');

      // 处理换行
      text = text.replace(/\n/g, '<br>');

      return text;
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const messageList = this.$refs.messageList
        if (messageList) {
          messageList.scrollTop = messageList.scrollHeight
        }
      })
    }
  },
  mounted() {
    // 加载初始消息
    this.loadSessionMessages(this.currentSessionId)
  },
  beforeDestroy() {
    // 清理资源，确保离开页面时中断任何未完成的请求
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
  }
}
</script>

<style lang="less" scoped>
.chat-window {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  width: 800px;
  display: flex;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  .collapsed-sidebar {
    width: 48px;
    background: #f7f7f8;
    border-right: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 0;

    .logo {
      width: 32px;
      height: 32px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      img {
        width: 24px;
        height: 24px;
        object-fit: contain;
        border-radius: 4px;
      }
    }

    .sidebar-toggle,
    .new-chat {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 4px;
      margin-bottom: 8px;

      &:hover {
        background: #e6f7ff;
        color: #1890ff;
        cursor: pointer;
      }
    }
  }

  .chat-sidebar {
    width: 300px;
    background: #f7f7f8;
    border-right: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;

    .sidebar-header {
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #e8e8e8;

      .logo-title {
        font-size: 16px;
        font-weight: bold;
      }

      .sidebar-toggle {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 4px;

        &:hover {
          background: #e6f7ff;
          color: #1890ff;
        }
      }
    }

    .new-chat-btn {
      margin: 16px;
      padding: 8px 16px;
      background: #1890ff;
      color: white;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      .ivu-icon {
        margin-right: 8px;
      }

      &:hover {
        background: #40a9ff;
      }
    }

    .session-list {
      flex: 1;
      overflow: auto;
      padding: 0 16px;

      .session-group {
        margin-bottom: 16px;

        .group-title {
          font-size: 12px;
          color: #999;
          padding: 8px 0;
        }

        .session-item {
          padding: 12px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;
          border-radius: 4px;
          margin-bottom: 4px;

          &:hover {
            background: #f5f5f5;
          }

          &.active {
            background: #e6f7ff;
          }

          .session-info {
            flex: 1;
            display: flex;
            flex-direction: column;

            .session-title {
              font-size: 14px;
              margin-bottom: 4px;
            }

            .session-time {
              font-size: 12px;
              color: #999;
            }
          }

          .ivu-btn {
            color: #ff4d4f;

            &:hover {
              color: #ff7875;
            }
          }
        }
      }
    }
  }

  .chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 500px;
    transition: all 0.3s;

    &.expanded {
      margin-left: 0;
    }

    .chat-header {
      padding: 16px;
      border-bottom: 1px solid #e8e8e8;
      display: flex;
      align-items: center;

      .chat-title {
        margin-left: 8px;
        font-size: 16px;
        font-weight: bold;
        flex: 1;
      }

      .header-actions {
        margin-left: auto;

        .ivu-btn {
          color: #1890ff;

          &:hover {
            color: #40a9ff;
          }
        }
      }
    }

    .message-list {
      flex: 1;
      overflow: auto;
      padding: 16px;

      .message-item-wrapper {
        display: flex;
        flex-direction: column;

        .message-item {
          display: flex;
          margin-bottom: 16px;

          &.user {
            flex-direction: row-reverse;

            .message-content {
              margin-right: 8px;
              margin-left: 0;
            }
          }

          &.assistant {
            .message-content {
              margin-left: 8px;
              margin-right: 0;
            }
          }

          .message-content {
            max-width: 70%;

            .thinking {
              display: flex;
              align-items: center;

              .loading-icon {
                animation: spin 1s linear infinite;
                margin-right: 8px;
              }
            }

            .streaming {
              background: #f5f5f5;
              padding: 12px;
              border-radius: 4px;

              .typewriter-container {
                position: relative;

                .full-content {
                  visibility: hidden;
                  position: relative;
                  z-index: 1;
                }

                .typed-content {
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  z-index: 2;
                }
              }
            }

            .content {
              background: #f5f5f5;
              padding: 12px;
              border-radius: 4px;
              overflow: hidden;
              transition: height 0.1s ease-in-out, min-height 0.1s ease-in-out;

              .markdown-content {
                line-height: 1.6;
                word-break: break-word;
                transition: all 0.1s ease-in-out;

                pre {
                  background-color: #282c34;
                  padding: 12px;
                  border-radius: 4px;
                  overflow-x: auto;
                  margin: 10px 0;

                  code {
                    color: #abb2bf;
                    font-family: Consolas, Monaco, 'Andale Mono', monospace;
                    line-height: 1.5;
                    white-space: pre;
                  }
                }

                code {
                  background-color: rgba(0, 0, 0, 0.05);
                  padding: 2px 4px;
                  border-radius: 3px;
                  font-family: monospace;
                }

                strong {
                  font-weight: bold;
                }

                em {
                  font-style: italic;
                }

                a {
                  color: #1890ff;
                  text-decoration: none;

                  &:hover {
                    text-decoration: underline;
                  }
                }
              }

              .image-content {
                img {
                  max-width: 100%;
                  margin-bottom: 8px;
                }
              }
            }
          }
        }
      }
    }

    .input-area {
      border-top: 1px solid #e8e8e8;
      padding: 16px;

      .toolbar {
        margin-bottom: 8px;
      }

      .action-buttons {
        margin-top: 8px;
        display: flex;
        justify-content: flex-end;

        .ivu-btn + .ivu-btn {
          margin-left: 8px;
        }
      }
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
