<template>
  <div 
    class="floating-button"
    :style="{ left: position.x + 'px', top: position.y + 'px' }"
    @mousedown="startDrag"
    @touchstart="startDrag"
    @click="handleClick"
  >
    <div class="button-content">
      <Icon type="ios-chatbubbles" size="24" />
      <span class="button-text">AI助手</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FloatingButton',
  data() {
    return {
      position: {
        x: window.innerWidth - 100,
        y: window.innerHeight - 100
      },
      isDragging: false,
      startX: 0,
      startY: 0
    }
  },
  methods: {
    handleClick(e) {
      // 如果是拖拽结束，不触发点击事件
      if (this.isDragging) {
        this.isDragging = false
        return
      }
      this.$emit('click', e)
    },
    startDrag(e) {
      this.isDragging = false
      const event = e.type === 'touchstart' ? e.touches[0] : e
      this.startX = event.clientX - this.position.x
      this.startY = event.clientY - this.position.y
      
      document.addEventListener('mousemove', this.onDrag)
      document.addEventListener('touchmove', this.onDrag)
      document.addEventListener('mouseup', this.stopDrag)
      document.addEventListener('touchend', this.stopDrag)
    },
    onDrag(e) {
      this.isDragging = true
      const event = e.type === 'touchmove' ? e.touches[0] : e
      this.position.x = event.clientX - this.startX
      this.position.y = event.clientY - this.startY
    },
    stopDrag() {
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('touchmove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
      document.removeEventListener('touchend', this.stopDrag)
    }
  }
}
</script>

<style lang="less">
.floating-button {
  position: fixed;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #1890ff, #36cfc9);
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: move;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: all 0.3s;
  overflow: hidden;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    
    .button-content {
      transform: translateY(-100%);
    }
  }
  
  .button-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.3s;
    
    .button-text {
      font-size: 12px;
      margin-top: 4px;
    }
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #36cfc9, #1890ff);
    opacity: 0;
    transition: opacity 0.3s;
  }
  
  &:hover::before {
    opacity: 1;
  }
}
</style> 