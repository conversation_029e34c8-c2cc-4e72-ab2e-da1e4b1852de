<template>
  <!-- <div>
    <floating-button @click="toggleChatWindow" />
    <chat-window
      v-if="showWindow"
      ref="chatWindow"
      @close="showWindow = false"
    />
  </div> -->
</template>

<script>
import FloatingButton from './FloatingButton.vue'
import ChatWindow from './ChatWindow.vue'

export default {
  name: 'Chat',
  components: {
    FloatingButton,
    ChatWindow
  },
  data() {
    return {
      showWindow: false
    }
  },
  methods: {
    toggleChatWindow() {
      this.showWindow = !this.showWindow
    }
  }
}
</script>

<style lang="less">
.chat-window {
  z-index: 1001;
}
</style>
