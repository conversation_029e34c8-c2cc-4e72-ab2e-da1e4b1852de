<template>
  <div style="height: 705px; overflow-y: auto;">
    <DualTableSelect ref="dualTable" v-model="selectedValues" :source-columns="sourceColumns" :source-data="sourceData"
      :selected-columns="selectedColumns" :selected-data="selectedData" :total="total" :current="currentPage"
      :page-size="pageSize" :loading="loading" :check-all="checkAll" :indeterminate="indeterminate"
      @on-check-all="handleCheckAll" @on-page-change="handlePageChange" @on-select="handleSelect"
      @on-select-cancel="handleSelectCancel" @on-select-all="handleSelectAll"
      @on-select-all-cancel="handleSelectAllCancel" @on-remove="handleRemove">
 
      <template slot="source-header">
        <div style="width:88%;height: 130px;">
          <div class="custom-selected-header">
            <h3>{{ $t('country.select') }}</h3>
          </div>
          <div class="country-select-wrapper">
              <Select
                ref="countrySelect"
                v-model="selectedCountrySearch"
                filterable
                clearable
                multiple
                :max-tag-count="4"
                :max-tag-placeholder="maxTagPlaceholder"
                :placeholder="$t('country.select')"
                style="width: 100%;"
                @on-change="handleCountrySelectChange"
                @on-open-change="handleCountrySelectBlur"
              >
                <Option
                  v-for="item in countrySearchOptions"
                  :value="item.mccId"
                  :key="item.mccId"
                  :disabled="isCountryOptionDisabled(item)"
                  :label="getCountryOptionLabel(item)"
                >
                  {{ language === 'zh-CN' ? item.countryCn : item.countryEn }}
                  <span v-if="(language === 'zh-CN' ? item.remarkCn : item.remarkEn)" style="color: #999; margin-left: 5px;">
                    ({{ language === 'zh-CN' ? item.remarkCn : item.remarkEn }})
                  </span>
                </Option>
              </Select>
          </div>
          <div class="continent-buttons">
            <div class="continent-buttons-wrapper">
              <Button class="continentButton" :type="currentContinent === 'all' ? 'primary' : 'default'"
                @click="selectCountry('all')">
                {{ $t('country.all') }}
              </Button>
              <Button class="continentButton" v-for="(continent, index) in continentList" :key="continent._id || index"
                :type="currentContinent === continent.continentEn ? 'primary' : 'default'"
                @click="selectCountry(continent.continentEn)">
                {{ language == 'zh-CN' ? continent.continentCn : continent.continentEn }}
              </Button>
            </div>
          </div>
        </div>
      </template>
      <template slot="all-check-box">
        <div class="check-all-container" style="text-align: right;margin-top: 15px;">
          <Checkbox :indeterminate="computedIndeterminate" :value="computedCheckAll" @on-change="handleCheckAll" :disabled ="sourceData.length === 0">{{$t('country.selectAll')}}</Checkbox>
        </div>
      </template>
    
      <template slot="empty">
        <div class="empty-state">
          <Icon type="ios-alert-outline" size="48" />
          <p>{{ $t('country.noData') }}</p>
          <Button v-if="currentContinent !== 'all'" @click="selectCountry('all')">
            {{ $t('country.viewAll') }}
          </Button>
        </div>
      </template>

      <template slot="selected-header">
        <div style="height: 130px;">
          <div class="custom-selected-header">
            <h3>{{ $t('country.selected') }}</h3>
          </div>
          <div class="continent-buttons" style="height: 73px;"></div>
        </div>
      </template>
    </DualTableSelect>
    
    <Spin size="large" fix v-if="checkAllLoading">
      <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
      <div>{{ loadingMessage }}</div>
    </Spin>
  </div>
</template>

<script>
import DualTableSelect from '@/components/dual-table-select/DualTableSelect.vue'
import {
  getCountry,
  getContinent,
} from '@/api/package/package.js'
export default {
  continent: "",
  name: "AddCountry",
  components: {
    DualTableSelect
  },
  props: {
    corpId: {
      type: String,
      default: "",
    },
    // 控制是否可编辑
    typeFlag: {
      type: String,
      default: "",
    },
    supportedHotspots: {
      type: [String, Number],
      default: "",
    }
  },
  data () {
    return {
      language: localStorage.getItem("local"),
      continentList: [],
      currentContinent: 'all', // 当前选中的大洲，默认为全部
      checkAll: false,
      indeterminate: false,
      loading: false,
      checkAllLoading: false, // 专门用于全选操作的loading状态
      loadingMessage: '', // loading状态显示的消息
      isAllSelected: false,
      continent: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      //存放选择数据的id唯一标识
      selectedValues: [],
      //查询源数据
      sourceData: [],
      //查询数据源表头信息
      // sourceColumns: [{
      //   type: 'selection',
      //   width: 60,
      //   align: 'center'
      // }, {
      //   title: this.$t('country.nameCn'),
      //   key: 'countryCn',
      //   minWidth: 120,
      //   align: 'center',
      //   tooltip: true,
      // }, {
      //   title: this.$t('country.nameEn'),
      //   key: 'countryEn',
      //   minWidth: 120,
      //   align: 'center',
      //   tooltip: true,
      // }, {
      //   title: this.$t('country.continentCn'),
      //   key: 'continentCn',
      //   minWidth: 120,
      //   align: 'center',
      //   tooltip: true,
      // }, {
      //   title: this.$t('country.continentEn'),
      //   key: 'continentEn',
      //   minWidth: 120,
      //   align: 'center',
      //   tooltip: true,
      // }, {
      //   title: this.$t('flow.remark'),
      //   key: 'remark',
      //   minWidth: 120,
      //   align: 'center',
      //   tooltip: true,
      // }],

      //表格选择的数据及表头信息
      selectedData: [],
      // selectedColumns: [
      //   {
      //     title: this.$t('country.nameCn'),
      //     key: 'countryCn',
      //     minWidth: 120,
      //     align: 'center',
      //     tooltip: true,
      //   }, {
      //     title: this.$t('country.nameEn'),
      //     key: 'countryEn',
      //     minWidth: 120,
      //     align: 'center',
      //     tooltip: true,
      //   }, {
      //     title: this.$t('country.continentCn'),
      //     key: 'continentCn',
      //     minWidth: 120,
      //     align: 'center',
      //     tooltip: true,
      //   }, {
      //     title: this.$t('country.continentEn'),
      //     key: 'continentEn',
      //     minWidth: 120,
      //     align: 'center',
      //     tooltip: true,
      //   },
      //   {
      //     title: this.$t('flow.remark'),
      //     key: 'remark',
      //     minWidth: 120,
      //     align: 'center',
      //     tooltip: true,
      //   },
      //   {
      //     title: this.$t('order.action'),
      //     slot: 'action',
      //     width: 110,
      //     align: 'center'
      //   }
      // ],
      //存放列表全部的数据
      allCountryData: null,
      selectedCountrySearch: [],
      countrySearchOptions: [],
    }
  },
  watch: {
    // 监听selectedValues变化，若为空（新建或重置），清空selectedCountrySearch
    selectedValues(val) {
      if (!val || val.length === 0) {
        this.selectedCountrySearch = [];
      }
    }
  },
  mounted () {
    this.currentPage = 1
    this.currentContinent = 'all'
    this.continentEn = ''
    // 初始化时获取大洲列表
    this.getContinentList()
    // 组件加载后获取初始数据
    this.loadData()
  },
  methods: {
    // 从mccId中提取mcc部分（连字符前的部分）
    extractMccFromMccId(mccId) {
      if (!mccId) return '';
      const matches = mccId.match(/^(\d+)(?:-|$)/);
      return matches ? matches[1] : mccId;
    },
    
    // 统一的错误处理方法
    handleError(error, message, isRetryable = false, retryFn = null) {
      this.$Message.error({
          content: message,
          duration: 5,
          closable: true
        });
    },
    
    // 获取大洲列表
    async getContinentList () {
      try {
        const res = await getContinent()
        if (res && res.code === '0000') {
          // 处理大洲数据并添加"全部"选项
          this.continentList = res.data.map((item, index) => ({
            continentCn: item.continentCn,
            continentEn: item.continentEn,
            _id: index // 添加内部ID确保唯一性
          }))
        } else {
          this.handleError(null, this.$t('country.getContinentFail'), true, this.getContinentList);
        }
      } catch (error) {
        this.handleError(error, '获取大洲列表失败', true, this.getContinentList);
      }
    },

    // 获取国家数据
    async getCountryData() {
      try {
        const params = {
          continentEn: this.continent && this.continent.continentEn ? this.continent.continentEn : '',
          pageNo: this.currentPage,
          pageSize: this.pageSize,
          corpId: this.corpId,
          supportedHotspots: this.supportedHotspots
        }

        const res = await getCountry(params)

        if (res && res.code == "0000") {
          return res
        } else {
          this.handleError(null, this.$t('country.getCountryFail'));
          return null;
        }
      } catch (error) {
        this.handleError(error, '获取国家列表失败');
        return null;
      }
    },

    // 加载数据
    async loadData(isFirstLoad) {
      if (this.loading) return; // 防止重复加载

      this.loading = true;
      try {
        // 如果是首次加载或allCountryData为空，则获取全量数据
        if (isFirstLoad || !this.allCountryData) {
          try {
            await this.getAllCountryData();
            
            // 只在组件初始化时(而非切换大洲时)从全量数据中构建selectedData
            // 使用一个额外的标志来判断是否是组件的首次加载
            if (this.selectedValues.length > 0 && this.allCountryData && 
                this.allCountryData.data && this.allCountryData.data.records && 
                this.selectedData.length === 0) { // 只在selectedData为空时初始化
              const matchedRecords = this.allCountryData.data.records.filter(item => 
                this.selectedValues.includes(item.mccId)
              );
              if (matchedRecords.length > 0) {
                this.selectedData = matchedRecords.map(item => ({
                  id: item.mccId,
                  countryCn: item.countryCn,
                  countryEn: item.countryEn,
                  continentCn: item.continentCn,
                  continentEn: item.continentEn,
                  mcc: this.extractMccFromMccId(item.mccId),
                  mccId: item.mccId,
                  remarkCn: item.remarkCn || '',
                  remarkEn: item.remarkEn || '',
                  remark: this.language === 'zh-CN' ? (item.remarkCn || '') : (item.remarkEn || '')
                }));
                
                // 初始化搜索下拉框的选中状态
                this.selectedCountrySearch = [...this.selectedValues];
              }
            }
          } catch (error) {
            console.error('获取全量数据错误:', error);
            this.$Message.error(this.$t('country.getAllCountryFail'));
          }
        }
        
        const res = await this.getCountryData();

        if (res && res.code == "0000") {
          // 转换数据结构以匹配现有组件
          this.sourceData = res.data.records.map(item => {
            // 从mccId提取基础mcc
            const mcc = this.extractMccFromMccId(item.mccId);
            
            // 检查是否已在选中列表
            const isSelected = this.selectedData.some(selected => 
              selected.mccId === item.mccId
            );
            
            // 检查是否有相同mcc但不同mccId的项已被选中
            const hasSameMccSelected = this.selectedData.some(selected => 
              this.extractMccFromMccId(selected.mccId) === mcc && 
              selected.mccId !== item.mccId
            );

            return {
              id: item.mccId, // 使用mccId作为唯一标识
              countryCn: item.countryCn,
              countryEn: item.countryEn,
              continentCn: item.continentCn,
              continentEn: item.continentEn,
              mcc: mcc, // 从mccId中提取mcc部分
              mccId: item.mccId,
              remarkCn: item.remarkCn || '',
              remarkEn: item.remarkEn || '',
              //remark需要根据语言来显示
              remark: this.language === 'zh-CN' ? (item.remarkCn || '') : (item.remarkEn || ''),
              _checked: isSelected,
              _disabled: hasSameMccSelected || (this.typeFlag === 'info')
            }
          });

          this.total = res.data.total || 0;

          // 更新当前页的选中状态
          this.updateCheckAllStatus();
          
          // 重置全选相关状态
          this.adjustAllCheckStatus();
        } else {
          this.handleError(null, res.msg || '获取国家列表失败');
        }
      } catch (error) {
        this.handleError(error, '加载数据错误');
      } finally {
        this.loading = false;
      }
    },

    // 分页处理
    handlePageChange (page) {
      this.currentPage = page
      this.loadData()
    },

    // 选择处理 - 重写单选方法
    handleSelect(selection, row) {
      // 只读模式下不允许选择
      if (this.typeFlag === 'info') {
        // 如果是只读模式，阻止选择并返回当前状态
        this.refreshUIState();
        return;
      }

      // 如果行被禁用，则不允许选择
      if (row._disabled) {
        // 找到当前行在sourceData中的索引
        const rowIndex = this.sourceData.findIndex(item => item.id === row.id);
        if (rowIndex > -1) {
          // 直接设置为未选中状态
          this.sourceData[rowIndex]._checked = false;
          if (this.$refs.dualTable) {
            this.$refs.dualTable.cancleOneSelect(row);
          }
        }

        // 查找是否有相同mcc的项已被选中
        const mcc = this.extractMccFromMccId(row.mccId);
        const existingItem = this.selectedData.find(item => 
          this.extractMccFromMccId(item.mccId) === mcc && 
          item.mccId !== row.mccId
        );
        
        if (existingItem) {
          this.$Message.warning(this.$t('country.alreadySelected') + existingItem.countryCn);
        }
        
        // 刷新UI
        this.$nextTick(() => {
          this.refreshUIState();
        });
        return;
      }

      // 检查相同mcc的国家是否已被选择
      const mcc = this.extractMccFromMccId(row.mccId);
      const existingItem = this.selectedData.find(item => 
        this.extractMccFromMccId(item.mccId) === mcc && 
        item.mccId !== row.mccId
      );
      
      if (existingItem) {
        // 取消选中当前行
        const rowIndex = this.sourceData.findIndex(item => item.id === row.id);
        if (rowIndex > -1) {
          this.sourceData[rowIndex]._checked = false;
          if (this.$refs.dualTable) {
            this.$refs.dualTable.cancleOneSelect(row);
          }
        }

        this.$Message.warning(this.$t('country.alreadySelected') + existingItem.countryCn);
        
        // 刷新UI
        this.$nextTick(() => {
          this.refreshUIState();
        });
        return;
      }

      // 正常选择处理
      // 检查selection中是否包含当前行
      const isRowSelected = selection.some(item => item.id === row.id);
      
      if (isRowSelected) {
        // 选择：将当前行加入选择
        // 确保不重复添加
        if (!this.selectedData.some(item => item.id === row.id)) {
          // 确保备注信息也被传递
          const rowToAdd = {
            ...row,
            remark: this.language === 'zh-CN' ? (row.remarkCn || '') : (row.remarkEn || '')
          };
          this.selectedData.push(rowToAdd);
        }
        
        // 同步更新下拉选择框的选中状态
        if (!this.selectedCountrySearch.includes(row.id)) {
          this.selectedCountrySearch.push(row.id);
        }
      } else {
        // 取消选择：移除当前行
        const index = this.selectedData.findIndex(item => item.id === row.id);
        if (index > -1) {
          this.selectedData.splice(index, 1);
        }
        
        // 同步更新下拉选择框的选中状态
        const searchIndex = this.selectedCountrySearch.indexOf(row.id);
        if (searchIndex > -1) {
          this.selectedCountrySearch.splice(searchIndex, 1);
        }
      }
      
      // 更新ID数组
      this.selectedValues = this.selectedData.map(item => item.id);
      
      // 更新UI状态
      this.refreshUIState();
      
      // 触发事件
      this.$emit('input', this.selectedValues);
      this.$emit('on-change', this.selectedData);
      
      // 更新全选状态
      this.updateCheckAllStatus();
    },

    handleSelectCancel(selection, row) {
      if (!row) {
        console.warn('handleSelectCancel: row参数为空');
        return;
      }
      
      // 从已选数据中找到并移除当前行
      const index = this.selectedData.findIndex(item => item && item.id === row.id);
      if (index > -1) {
        this.selectedData.splice(index, 1);
        // 更新ID数组
        this.selectedValues = this.selectedData.map(item => item.id);
        
        // 同步更新下拉选择框的选中状态
        const searchIndex = this.selectedCountrySearch.indexOf(row.id);
        if (searchIndex > -1) {
          this.selectedCountrySearch.splice(searchIndex, 1);
        }
        
        // 获取当前行的mcc，检查是否有相同mcc的其他行需要恢复可选状态
        const mcc = this.extractMccFromMccId(row.mccId);
        
        // 更新sourceData中相同mcc的行的禁用状态
        if (this.sourceData && this.sourceData.length) {
          this.sourceData.forEach(item => {
            if (this.extractMccFromMccId(item.mccId) === mcc) {
              // 检查是否还有其他相同mcc的行被选中
              const hasSameMccSelected = this.selectedData.some(selected => 
                selected && this.extractMccFromMccId(selected.mccId) === mcc
              );
              
              // 如果没有其他相同mcc的行被选中，取消禁用状态
              if (!hasSameMccSelected) {
                item._disabled = this.typeFlag === 'info';
              }
            }
          });
        }
      }
      
      // 更新UI
      this.refreshUIState();
      
      // 通知父组件
      this.$emit('input', this.selectedValues);
      this.$emit('on-change', this.selectedData);
      
      // 更新全选状态
      this.updateCheckAllStatus();
    },

    // 确定当前行是否可以被选择（用于禁止选择相同MCC的不同行）
    isRowSelectable (row) {
      // 如果是只读模式，所有行都不可选
      if (this.typeFlag === 'info') {
        return false;
      }

      // 检查是否已经选择了相同MCC的其他国家
      const existingItem = this.selectedData.find(item => item.mcc === row.mcc && item.id !== row.id);
      return !existingItem;
    },

    // 自定义行选择函数，用于拦截iview表格的默认选择行为
    customRowSelection (row, index) {
      return {
        on: {
          click: (e) => {
            // 如果不可选择，阻止默认行为
            if (row._disabled) {
              // 防止事件冒泡和默认行为
              e.stopPropagation();
              e.preventDefault();

              // 如果有相同MCC的行已被选中，显示提示
              const existingItem = this.selectedData.find(item => item.mcc === row.mcc && item.id !== row.id);
              if (existingItem) {
                this.$Message.warning(this.$t('country.alreadySelected') + existingItem.countryCn);
              }

              // 确保UI状态同步
              this.refreshUIState();
              return false;
            }
          }
        }
      };
    },
    //获取列表全部的数据并存放，后续全选也可直接用
    async getAllCountryData() {
      try {
        const params = {
          continentEn: this.continent.continentEn, // 保留筛选条件
          pageNo: -1,
          pageSize: -1, // -1表示不分页，获取所有数据
          corpId: this.corpId,
          supportedHotspots: this.supportedHotspots
        };
  
        const res = await getCountry(params);
        if (res && res.code === '0000') {
          this.allCountryData = res;
          // 获取数据后初始化搜索下拉框
          if(!this.continent.continentEn||this.continent.continentEn==''){
            this.initCountrySearchOptions();
          }
          return res;
        } else {
          this.$Message.error(this.$t('country.getAllCountryFail'));
          return null;
        }
      } catch (error) {
        console.error('获取全量数据错误:', error);
        this.$Message.error(this.$t('country.getAllCountryFail'));
        return null;
      }
    },

    // 全选处理
    async handleCheckAll(checked) {
      // 只读模式下不允许选择
      if (this.typeFlag === 'info') return;

      try {
        this.loading = true;
        this.checkAllLoading = true;
        this.loadingMessage = checked ? this.$t('country.selectingContinent') : this.$t('country.deselectingContinent');
        this.$Message.loading({
          content: this.loadingMessage,
          duration: 0
        });

        if (checked) {
          // 全选处理 - 获取当前大洲数据
          let dataToAdd = [];

          // 确保allCountryData存在
          if (!this.allCountryData) {
            await this.getAllCountryData();
          }
          
          if (this.allCountryData && this.allCountryData.code === '0000' && this.allCountryData.data && this.allCountryData.data.records) {
            // 获取当前大洲数据
            const currentContinentData = this.allCountryData.data.records.filter(item => {
              if (this.currentContinent === 'all') {
                return true; // 全部数据
              }
              return item.continentEn === this.currentContinent; // 当前大洲数据
            });
            
            // 转换为选中数据格式
            dataToAdd = currentContinentData.map(item => ({
              id: item.mccId,
              countryCn: item.countryCn,
              countryEn: item.countryEn,
              continentCn: item.continentCn,
              continentEn: item.continentEn,
              mcc: this.extractMccFromMccId(item.mccId),
              mccId: item.mccId,
              remarkCn: item.remarkCn || '',
              remarkEn: item.remarkEn || '',
              remark: this.language === 'zh-CN' ? (item.remarkCn || '') : (item.remarkEn || '')
            }));
            
            // 检查MCC码互斥，优先保留已选择的相同MCC码国家
            const newSelectedData = [...this.selectedData];
            
            // 创建已选MCC码的映射，记录哪些MCC码已被选中以及选中的是哪个国家ID
            const existingMccMap = new Map();
            this.selectedData.forEach(item => {
              const mcc = this.extractMccFromMccId(item.mccId);
              existingMccMap.set(mcc, item.id);
            });
            
            // 处理待添加的数据
            dataToAdd.forEach(item => {
              const mcc = this.extractMccFromMccId(item.mccId);
              const existingId = existingMccMap.get(mcc);
              
              // 如果MCC码还未被选择或者当前ID就是已选择的ID，则可以添加
              if (!existingId || existingId === item.id) {
                // 如果该ID未被选择，则添加
                if (!this.selectedValues.includes(item.id)) {
                  newSelectedData.push(item);
                  existingMccMap.set(mcc, item.id); // 更新已选的MCC码映射
                }
              }
              // 否则，跳过该数据，保留已选择的MCC码对应的国家
            });
            
            // 更新数据 - 追加而非替换
            this.selectedData = newSelectedData;
            this.selectedValues = newSelectedData.map(item => item.id);
            
            // 同步更新下拉选择框的选中状态
            this.selectedCountrySearch = [...this.selectedValues];
          } else {
            this.$Message.error('获取大洲国家数据失败');
            this.loading = false;
            this.checkAllLoading = false;
            this.$Message.destroy();
            return;
          }

          // 更新全选状态
          this.updateCheckAllStatus();
        } else {
          // 取消当前大洲的选择
          if (this.currentContinent === 'all') {
            // 取消全部选择
            this.selectedData = [];
            this.selectedValues = [];
            
            // 同步更新下拉选择框的选中状态
            this.selectedCountrySearch = [];
          } else {
            // 只取消当前大洲的选择
            this.selectedData = this.selectedData.filter(item => 
              item.continentEn !== this.currentContinent
            );
            this.selectedValues = this.selectedData.map(item => item.id);
            
            // 同步更新下拉选择框的选中状态
            this.selectedCountrySearch = [...this.selectedValues];
          }
          
          // 更新全选状态
          this.checkAll = false;
          this.indeterminate = this.selectedData.length > 0;
        }

        // 更新UI
        this.refreshUIState();

        // 通知父组件
        this.$emit('input', this.selectedValues);
        this.$emit('on-change', this.selectedData);
      } catch (error) {
        console.error('全选处理错误:', error);
        this.$Message.error(this.$t('country.operationFail') + (error.message || this.$t('country.getCountryFail')));
      } finally {
        this.loading = false;
        this.checkAllLoading = false;
        this.$Message.destroy();
      }
    },

    // 刷新UI状态
    refreshUIState() {
      // 检查sourceData是否存在
      if (!this.sourceData || this.sourceData.length === 0) {
        console.warn('sourceData为空或者长度为0，跳过UI刷新');
        return;
      }

      // 创建一个集合，存储已选中数据的mcc
      const selectedMccSet = new Set();
      if (this.selectedData && this.selectedData.length) {
        this.selectedData.forEach(selected => {
          if (selected && selected.mccId) {
            selectedMccSet.add(this.extractMccFromMccId(selected.mccId));
          }
        });
      }
      
      // 更新sourceData中的选中和禁用状态
      this.sourceData.forEach(item => {
        // 检查是否已在选中列表
        const isSelected = this.selectedData.some(selected => selected && selected.id === item.id);
        
        // 检查是否有相同mcc的其他记录已被选中
        const mcc = this.extractMccFromMccId(item.mccId);
        const hasSameMccSelected = selectedMccSet.has(mcc) && !this.selectedData.some(selected => selected && selected.id === item.id);

        // 更新状态
        item._checked = isSelected;
        item._disabled = hasSameMccSelected || (this.typeFlag === 'info');
      });

      // 强制刷新视图
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },

    // 调整全选状态
    adjustAllCheckStatus() {
      // 如果当前页面没有数据，则禁用全选
      if (!this.sourceData || this.sourceData.length === 0) {
        this.checkAll = false;
        this.indeterminate = false;
        return;
      }
      
      // 获取当前页面可选的项目数量
      const availableItems = this.sourceData.filter(item => !item._disabled).length;
      
      // 获取当前页面已选的项目数量
      const selectedItems = this.sourceData.filter(item => item._checked).length;
      
      // 根据选择情况设置全选状态
      if (selectedItems === 0) {
        this.checkAll = false;
        this.indeterminate = false;
      } else if (selectedItems === availableItems && availableItems > 0) {
        this.checkAll = true;
        this.indeterminate = false;
      } else {
        this.checkAll = false;
        this.indeterminate = true;
      }
    },
    
    // 更新全选状态
    updateCheckAllStatus() {
      // 如果没有allCountryData或者allCountryData.data不存在，直接返回
      if (!this.allCountryData || !this.allCountryData.data) {
        this.checkAll = false;
        this.indeterminate = this.selectedData.length > 0;
        return;
      }
      // 过滤出当前大洲的所有国家
      let filteredRecords = [];
      if (this.allCountryData.data.records) {
        filteredRecords = this.allCountryData.data.records.filter(item => {
          if (this.currentContinent === 'all') return true;
          return item.continentEn === this.currentContinent;
        });
      }
      // 只统计未被MCC互斥禁用的项
      const availableItems = filteredRecords.filter(item => {
        const mcc = this.extractMccFromMccId(item.mccId);
        return !this.selectedData.some(selected =>
          this.extractMccFromMccId(selected.mccId) === mcc && selected.mccId !== item.mccId
        );
      });
      // 这些可选项中有多少被选中
      const selectedCount = availableItems.filter(item =>
        this.selectedValues.includes(item.mccId)
      ).length;
      // 设置全选/半选/未选
      if (availableItems.length === 0) {
        this.checkAll = false;
        this.indeterminate = false;
      } else if (selectedCount === 0) {
        this.checkAll = false;
        this.indeterminate = false;
      } else if (selectedCount === availableItems.length) {
        this.checkAll = true;
        this.indeterminate = false;
      } else {
        this.checkAll = false;
        this.indeterminate = true;
      }
    },

    // 页面全选按钮状态
    // 处理取消全选
    handleSelectAllCancel (selection) {
      // 只读模式下不允许操作
      if (this.typeFlag === 'info') {
        return;
      }

      // 获取当前页的数据
      const currentPageData = [...this.sourceData];

      // 保存已有的选择（其他页面的选择）
      const existingSelected = this.selectedData.filter(item =>
        !currentPageData.some(cpItem => cpItem.id === item.id)
      );

      // 获取当前页面被取消选择的数据mcc集合
      const canceledMccSet = new Set();
      currentPageData.forEach(item => {
        const mcc = this.extractMccFromMccId(item.mccId);
        // 检查是否被取消选择
        if (this.selectedData.some(selected => selected.id === item.id) && 
            !existingSelected.some(existing => existing.id === item.id)) {
          canceledMccSet.add(mcc);
        }
      });

      // 更新数据
      this.selectedData = existingSelected;
      this.selectedValues = existingSelected.map(item => item.id);
      // 同步更新下拉选择框的选中状态
      this.selectedCountrySearch = [...this.selectedValues];
      
      // 更新sourceData中被取消选择的mcc相关行的禁用状态
      this.sourceData.forEach(item => {
        const mcc = this.extractMccFromMccId(item.mccId);
        if (canceledMccSet.has(mcc)) {
          // 检查是否还有其他相同mcc的行被选中
          const hasSameMccSelected = this.selectedData.some(selected => 
            this.extractMccFromMccId(selected.mccId) === mcc
          );
          
          // 如果没有其他相同mcc的行被选中，取消禁用状态
          if (!hasSameMccSelected) {
            item._disabled = this.typeFlag === 'info';
          }
        }
      });

      // 更新UI状态
      this.refreshUIState();

      // 手动通知父组件值已更改
      this.$emit('input', this.selectedValues);
      this.$emit('on-change', this.selectedData);

      // 更新全选状态
      this.updateCheckAllStatus();
    },
    
    // 处理全选 - 拦截表格默认全选行为
    handleSelectAll(selection) {
      // 只读模式下不允许选择
      if (this.typeFlag === 'info') {
        this.refreshUIState();
        return;
      }

      // 获取当前页的数据
      const currentPageData = [...this.sourceData];

      // 保存已有的选择（其他页面的选择）
      const existingSelected = this.selectedData.filter(item =>
        !currentPageData.some(cpItem => cpItem.id === item.id)
      );

      if (selection.length > 0) {
        // 创建已选MCC码的映射，记录哪些MCC码已被选中以及选中的是哪个国家ID
        const existingMccMap = new Map();
        this.selectedData.forEach(item => {
          const mcc = this.extractMccFromMccId(item.mccId);
          existingMccMap.set(mcc, item.id);
        });
        
        // 选择当前页面的数据，但要尊重用户已选的相同MCC码国家
        const pageSelection = [];
        
        // 处理当前页面数据
        for (const item of currentPageData) {
          const mcc = this.extractMccFromMccId(item.mccId);
          const existingId = existingMccMap.get(mcc);
          
          // 如果MCC码还未被选择，或者当前ID就是已选择的ID，则可以添加
          if (!existingId || existingId === item.id) {
            // 如果该ID未被选择，则添加
            if (!this.selectedValues.includes(item.id)) {
              pageSelection.push(item);
              existingMccMap.set(mcc, item.id); // 更新已选的MCC码映射
            } else if (existingId === item.id) {
              // 如果该ID已被选择且是当前MCC对应的已选ID，确保添加
              pageSelection.push(item);
            }
          }
          // 否则，跳过该数据，保留用户已选择的相同MCC码国家
        }

        // 合并已有选择和当前页选择
        // 先移除当前页中已存在于selectedData的项，避免重复
        const currentPageIds = new Set(currentPageData.map(item => item.id));
        const filteredSelected = this.selectedData.filter(item => !currentPageIds.has(item.id));
        
        // 合并不在当前页的选择和当前页新选择
        const combinedSelection = [...filteredSelected, ...pageSelection];

        // 更新数据
        this.selectedData = combinedSelection;
        this.selectedValues = combinedSelection.map(item => item.id);
        
        // 同步更新下拉选择框的选中状态
        this.selectedCountrySearch = [...this.selectedValues];
      } else {
        // 取消选择当前页面的数据
        const currentPageIds = new Set(currentPageData.map(item => item.id));
        this.selectedData = this.selectedData.filter(item => !currentPageIds.has(item.id));
        this.selectedValues = this.selectedData.map(item => item.id);
        
        // 同步更新下拉选择框的选中状态
        this.selectedCountrySearch = [...this.selectedValues];
        
        // 更新sourceData中的选中状态
        this.sourceData.forEach(item => {
          item._checked = false;
        });
      }

      // 更新UI
      this.refreshUIState();
      
      // 通知父组件
      this.$emit('input', this.selectedValues);
      this.$emit('on-change', this.selectedData);

      // 更新全选状态
      this.updateCheckAllStatus();
    },

    // 移除已选项
    handleRemove(row) {
      // 只读模式下不允许移除
      if (this.typeFlag === 'info') return;

      // 从选中数据中移除
      const index = this.selectedData.findIndex(item => item.id === row.id);
      if (index > -1) {
        this.selectedData.splice(index, 1);
        this.selectedValues = this.selectedData.map(item => item.id);
        
        // 同步更新下拉选择框的选中状态
        const searchIndex = this.selectedCountrySearch.indexOf(row.id);
        if (searchIndex > -1) {
          this.selectedCountrySearch.splice(searchIndex, 1);
        }

        // 如果之前是全选状态，现在不再是了
        if (this.isAllSelected) {
          this.isAllSelected = false;
          this.checkAll = false;
          this.indeterminate = this.selectedData.length > 0;
        } else {
          // 更新全选状态
          this.updateCheckAllStatus();
        }

        // 更新UI
        this.refreshUIState();
        
        // 通知父组件
        this.$emit('input', this.selectedValues);
        this.$emit('on-change', this.selectedData);
      }
    },

    // 按大洲筛选国家
    selectCountry(continentType) {
      this.currentContinent = continentType;
      this.continent = continentType === 'all' ? '' : { continentEn: continentType };
      this.currentPage = 1;
      this.isAllSelected = false; // 重置全选状态
      
      // 更改大洲筛选时，清空全量数据缓存，确保后续会重新获取
      // 注意：这里不应该清空已选择的数据
      this.allCountryData = null;
      
      // 先将checkAll和indeterminate重置为合适的状态
      this.checkAll = false;
      this.indeterminate = this.selectedData.length > 0;
      
      // 加载新数据，设置isFirstLoad为true确保获取全量数据
      this.loadData(true);
    },
    
    // 更新下拉框的选项文本显示
    getCountryOptionLabel(item) {
      const countryName = this.language === 'zh-CN' ? item.countryCn : item.countryEn;
      const remark = this.language === 'zh-CN' ? item.remarkCn : item.remarkEn;
      
      if (remark) {
        return `${countryName} (${remark})`;
      } else {
        return countryName;
      }
    },
    
    // 在初始化完成后处理批量选择的反显
    processBatchSelection() {
      // 如果有批量选择的值但selectedData为空，需要从allCountryData中构建
      if (this.selectedValues && this.selectedValues.length > 0 && 
          this.selectedData.length === 0 && this.allCountryData &&
          this.allCountryData.data && this.allCountryData.data.records) {
        
        // 找到匹配的记录
        const matchedRecords = this.allCountryData.data.records.filter(item => 
          this.selectedValues.includes(item.mccId)
        );
        
        if (matchedRecords.length > 0) {
          // 构建selectedData
          this.selectedData = matchedRecords.map(item => ({
            id: item.mccId,
            countryCn: item.countryCn,
            countryEn: item.countryEn,
            continentCn: item.continentCn,
            continentEn: item.continentEn,
            mcc: this.extractMccFromMccId(item.mccId),
            mccId: item.mccId,
            remarkCn: item.remarkCn || '',
            remarkEn: item.remarkEn || '',
            remark: this.language === 'zh-CN' ? (item.remarkCn || '') : (item.remarkEn || '')
          }));
          
          // 同步更新下拉选择框的值
          this.selectedCountrySearch = [...this.selectedValues];
        }
      }
    },
    
    // 初始化搜索下拉框的数据
    initCountrySearchOptions() {
      // 如果allCountryData已存在，使用它来填充搜索选项
      if (this.allCountryData && this.allCountryData.data && this.allCountryData.data.records) {
        // 始终使用全部数据，不根据当前大洲筛选
        let optionsData = this.allCountryData.data.records;
        
        this.countrySearchOptions = optionsData.map(item => ({
          mccId: item.mccId,
          countryCn: item.countryCn,
          countryEn: item.countryEn,
          continentCn: item.continentCn,
          continentEn: item.continentEn,
          mcc: this.extractMccFromMccId(item.mccId),
          remarkCn: item.remarkCn || '',
          remarkEn: item.remarkEn || ''
        }));
        
        // 如果已有选中值，初始化搜索下拉框的选中状态
        if (this.selectedValues && this.selectedValues.length > 0) {
          this.selectedCountrySearch = [...this.selectedValues];
        }
        
        // 处理批量选择的反显
        this.processBatchSelection();
      }
    },
    
    // 处理搜索下拉框选中变化
    handleCountrySelectChange(value) {
      if (!value || value.length === 0) {
        // 如果清空了选择，需要更新表格中的选中状态
        this.selectedValues = [];
        this.selectedData = [];
        // 更新表格UI
        this.refreshUIState();
        // 通知父组件
        this.$emit('input', this.selectedValues);
        this.$emit('on-change', this.selectedData);
        return;
      }
      
      // 获取新增的选项（与之前的selectedValues比较）
      const newSelected = value.filter(id => !this.selectedValues.includes(id));
      // 获取被移除的选项
      const removed = this.selectedValues.filter(id => !value.includes(id));
      
      // 处理新增选项
      if (newSelected.length > 0) {
        // 从countrySearchOptions中找到对应的数据
        for (const id of newSelected) {
          const item = this.countrySearchOptions.find(option => option.mccId === id);
          if (item) {
            // 检查是否有相同MCC的国家已被选中
            const mcc = this.extractMccFromMccId(item.mccId);
            const existingItem = this.selectedData.find(selected => 
              this.extractMccFromMccId(selected.mccId) === mcc && 
              selected.mccId !== item.mccId
            );
            
            if (existingItem) {
              // 有冲突，提示用户并移除该选项
              this.$Message.warning(this.$t('country.alreadySelected') + existingItem.countryCn);
              // 从selectedCountrySearch中移除该选项
              const index = this.selectedCountrySearch.indexOf(id);
              if (index !== -1) {
                this.selectedCountrySearch.splice(index, 1);
              }
              continue;
            }
            
            // 没有冲突，添加到已选列表
            const newItem = {
              id: item.mccId,
              countryCn: item.countryCn,
              countryEn: item.countryEn,
              continentCn: item.continentCn,
              continentEn: item.continentEn,
              mcc: mcc,
              mccId: item.mccId,
              remarkCn: item.remarkCn || '',
              remarkEn: item.remarkEn || '',
              remark: this.language === 'zh-CN' ? (item.remarkCn || '') : (item.remarkEn || ''),
              _checked: true
            };
            
            this.selectedData.push(newItem);
          }
        }
      }
      
      // 处理被移除的选项
      if (removed.length > 0) {
        for (const id of removed) {
          const index = this.selectedData.findIndex(item => item.id === id);
          if (index !== -1) {
            this.selectedData.splice(index, 1);
          }
        }
      }
      
      // 更新选中值
      this.selectedValues = this.selectedData.map(item => item.id);
      
      // 更新UI状态
      this.refreshUIState();
      
      // 通知父组件
      this.$emit('input', this.selectedValues);
      this.$emit('on-change', this.selectedData);
      
      // 更新全选状态
      this.updateCheckAllStatus();
    },
    
    // 判断国家选项是否禁用
    isCountryOptionDisabled(item) {
      // 只读模式下所有选项都禁用
      if (this.typeFlag === 'info') {
        return true;
      }
      
      // 检查是否有相同MCC的国家已被选中
      const mcc = this.extractMccFromMccId(item.mccId);
      const existingItem = this.selectedData.find(selected => 
        selected && this.extractMccFromMccId(selected.mccId) === mcc && 
        selected.mccId !== item.mccId
      );
      
      return !!existingItem;
    },

    // 在表格渲染时处理行类名，用于替代_disabled属性的视觉反馈
    getRowClass (record) {
      if (this.typeFlag === 'info') {
        return 'readonly-row';
      }

      const selectedWithSameMcc = this.selectedData.find(item =>
        item.mcc === record.mcc && item.id !== record.id
      );

      if (selectedWithSameMcc) {
        return 'disabled-row';
      }

      return '';
    },
    
    // 检查项目是否可选
    isItemSelectable (record) {
      // 只读模式下所有项目都不可选
      if (this.typeFlag === 'info') {
        return false;
      }

      // 检查是否有相同MCC但不同ID的项目已经被选中
      const selectedWithSameMcc = this.selectedData.find(item =>
        item.mcc === record.mcc && item.id !== record.id
      );

      // 如果有相同MCC的其他项目被选中，则此项不可选
      return !selectedWithSameMcc;
    },
    maxTagPlaceholder (num) {
      return '...';
    },
    handleCountrySelectBlur(open) {
      if (open === false && this.$refs.countrySelect) {
        // 清空输入框内容
        this.$refs.countrySelect.query = '';
     }
    },
  },
  computed: {
    computedCheckAll() {
      // 当前大洲所有可选项
      if (!this.allCountryData || !this.allCountryData.data) return false;
      let filteredRecords = [];
      if (this.allCountryData.data.records) {
        filteredRecords = this.allCountryData.data.records.filter(item => {
          if (this.currentContinent === 'all') return true;
          return item.continentEn === this.currentContinent;
        });
      }
      const availableItems = filteredRecords.filter(item => {
        const mcc = this.extractMccFromMccId(item.mccId);
        return !this.selectedData.some(selected =>
          this.extractMccFromMccId(selected.mccId) === mcc && selected.mccId !== item.mccId
        );
      });
      if (availableItems.length === 0) return false;
      return availableItems.every(item => this.selectedValues.includes(item.mccId));
    },
    computedIndeterminate() {
      if (!this.allCountryData || !this.allCountryData.data) return false;
      let filteredRecords = [];
      if (this.allCountryData.data.records) {
        filteredRecords = this.allCountryData.data.records.filter(item => {
          if (this.currentContinent === 'all') return true;
          return item.continentEn === this.currentContinent;
        });
      }
      const availableItems = filteredRecords.filter(item => {
        const mcc = this.extractMccFromMccId(item.mccId);
        return !this.selectedData.some(selected =>
          this.extractMccFromMccId(selected.mccId) === mcc && selected.mccId !== item.mccId
        );
      });
      const selectedCount = availableItems.filter(item =>
        this.selectedValues.includes(item.mccId)
      ).length;
      return selectedCount > 0 && selectedCount < availableItems.length;
    },
    sourceColumns() {
      if (this.language === 'zh-CN') {
        return [
          { type: 'selection', width: 60, align: 'center' },
          { title: this.$t('country.nameCn'), key: 'countryCn', minWidth: 120, align: 'center', tooltip: true },
          { title: this.$t('country.continentCn'), key: 'continentCn', minWidth: 120, align: 'center', tooltip: true },
          { title: this.$t('flow.remark'), key: 'remark', minWidth: 120, align: 'center', tooltip: true },
        ];
      } else {
        return [
          { type: 'selection', width: 60, align: 'center' },
          { title: this.$t('country.nameEn'), key: 'countryEn', minWidth: 120, align: 'center', tooltip: true },
          { title: this.$t('country.continentEn'), key: 'continentEn', minWidth: 120, align: 'center', tooltip: true },
          { title: this.$t('flow.remark'), key: 'remark', minWidth: 120, align: 'center', tooltip: true },
        ];
      }
    },
    selectedColumns() {
      // 操作列（action）只在原有selectedColumns有时保留
      const actionCol = { title: this.$t('order.action'), slot: 'action', width: 110, align: 'center' };
      if (this.language === 'zh-CN') {
        return [
          { title: this.$t('country.nameCn'), key: 'countryCn', minWidth: 120, align: 'center', tooltip: true },
          { title: this.$t('country.continentCn'), key: 'continentCn', minWidth: 120, align: 'center', tooltip: true },
          { title: this.$t('flow.remark'), key: 'remark', minWidth: 120, align: 'center', tooltip: true },
          actionCol
        ];
      } else {
        return [
          { title: this.$t('country.nameEn'), key: 'countryEn', minWidth: 120, align: 'center', tooltip: true },
          { title: this.$t('country.continentEn'), key: 'continentEn', minWidth: 120, align: 'center', tooltip: true },
          { title: this.$t('flow.remark'), key: 'remark', minWidth: 120, align: 'center', tooltip: true },
          actionCol
        ];
      }
    },
  },
}
</script>

<style scoped>
/* 移除之前的Select自定义样式 */
::v-deep .ivu-select-selection {
  min-height: 32px;
  height: auto;
  max-width: 100%;
}

::v-deep .ivu-select-multiple .ivu-select-selection {
  padding: 4px 24px 4px 4px;
  width: 100%;
}

::v-deep .ivu-select-multiple .ivu-tag {
  height: 24px;
  line-height: 22px;
  margin: 2px 4px 2px 0;
  max-width: 120px; /* 限制每个标签的最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}

/* 限制选择框的宽度 */
.country-select-wrapper {
  margin-bottom: 15px;
  margin-top: 5px;
  width: 100%;
}

/* Tooltip样式调整 */
::v-deep .ivu-tooltip {
  display: block;
  width: 100%;
}

::v-deep .ivu-tooltip-inner {
  max-width: 600px; /* 增加tooltip最大宽度 */
  min-width: 300px; /* 设置最小宽度 */
  white-space: pre-line;
  word-break: break-all;
  padding: 8px 12px;
}

::v-deep .ivu-tooltip-popper {
  margin-top: -10px !important; /* 调整tooltip与Select的间距 */
}

::v-deep .ivu-select-dropdown {
  z-index: 1001; /* 确保下拉框在tooltip之上 */
}

.continentButton {
  margin-right: 10px;
  margin-bottom: 10px;
  flex-shrink: 0;
  white-space: nowrap;
}


.custom-selected-header h3 {
  font-size: 16px;
  font-weight: 500;
}

.continent-buttons {
  position: relative;
  width: 100%;
  overflow-x: auto;
  margin-top: 15px;
  display: block;
}

.continent-buttons-wrapper {
  display: flex;
  flex-wrap: nowrap;
  width: max-content;
  min-width: 100%;
}

.check-all-container {
  text-align: right;
  margin-top: 10px;
  margin-bottom: 10px;
  position: sticky;
  right: 0;
  white-space: nowrap;
}

/* 添加滚动条样式 */
.continent-buttons::-webkit-scrollbar {
  height: 6px;
}

.continent-buttons::-webkit-scrollbar-thumb {
  background-color: #e8e8e8;
  border-radius: 3px;
}

.continent-buttons::-webkit-scrollbar-track {
  background-color: #f8f8f9;
}
</style>