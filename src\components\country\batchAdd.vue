<template>
  <!-- 批量添加国家 -->
  <Modal title="批量添加国家/地区" v-model="modal" :footer-hide="true" :mask-closable="false" @on-cancel="closeModal"
    width="90%" :styles="{top: '0px'}">
    <div style="height: 750px; overflow: auto;">
      <Tabs type='card'>
        <TabPane v-for="(countries, continent) in continentsData" :key="continent" :label="continent">
          <div style="min-height: 100px;">
            <Checkbox :indeterminate="isIndeterminate(continent)" :value="isAllSelected(continent)"
              @on-change="toggleSelectAll(continent)" style="margin: 30px 0;">
              <span style="color: brown;">全选{{continent}}</span>
            </Checkbox>

            <Row :gutter="16">
              <Col v-for="country in countries" :key="country.mcc" :xs="24" :sm="24" :md="12" :lg="8"
                style="margin-bottom: 20px;">
              <Checkbox
                :value="selectedCountriesMap[country.mcc]"
                :disabled="isCountryDisabled(country.mcc)"
                @on-change="checked => toggleCountry(checked, country)">
                {{ country.countryEn }}（{{ country.countryCn }}）
              </Checkbox>
              </Col>
            </Row>
          </div>
        </TabPane>
      </Tabs>
      <div style="margin-top: 30px; padding: 0 20px;">
        <p style="font-weight: bold; color: brown;">已勾选国家：</p>
        <div class="selected-countries-container">
          <div v-for="country in localSelectedCountries" :key="country.mcc" class="selected-country-item">
            {{ country.countryEn }}（{{ country.countryCn }}）
          </div>
        </div>
      </div>
    </div>
    <div class="footer-textarea" style="margin: 30px;">
      <Button type="primary" @click="handleSumbitCountry" icon="md-checkmark">确定</Button>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
      <Button @click="closeModal" icon="md-arrow-back">返回</Button>
    </div>
  </Modal>
</template>

<script>
  export default {
    props: {
      continentsData: {
        type: Object,
        required: true
      },
      selectedCountries: {
        type: Array,
        default: () => []
      },
      disabledMccs: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        modal: false,
        selectedCountriesMap: {}, // 存储选中国家 {mcc: countryObject}
        localSelectedCountries: [], // 本地存储选中的国家
      }
    },
    watch: {
      selectedCountries: {
        immediate: true,
        handler(newVal) {
          // 初始化选中的国家
          this.localSelectedCountries = Array.isArray(newVal) ? [...newVal] : [];
          this.selectedCountriesMap = {}
          this.localSelectedCountries.forEach(country => {
            if (country && country.mcc) {
              this.$set(this.selectedCountriesMap, country.mcc, true)
            }
          })
        }
      }
    },
    methods: {
      closeModal() {
        this.$emit('on-close')
      },
      handleSumbitCountry() {
        this.$emit('on-confirm', this.localSelectedCountries)
      },
      // 判断是否为部分选中状态
      isIndeterminate(continent) {
        const countries = this.continentsData[continent] || [];
        const selectedCount = countries.filter(c => this.selectedCountriesMap[c.mcc]).length;
        return selectedCount > 0 && selectedCount < countries.length;
      },
      // 判断是否为全部选中状态
      isAllSelected(continent) {
        const countries = this.continentsData[continent];
        return countries.every(country => this.selectedCountriesMap[country.mcc] === true);
      },
      // 勾选框-单个勾选国家
      toggleCountry(checked, country) {
        this.$set(this.selectedCountriesMap, country.mcc, checked);
        if (checked) {
          this.localSelectedCountries.unshift({...country}); // 新添加的放在最前面
        } else {
          const index = this.localSelectedCountries.findIndex(c => c.mcc === country.mcc);
          if (index !== -1) {
            this.localSelectedCountries.splice(index, 1);
          }
        }
      },
      // 该大洲-全部勾选
      toggleSelectAll(continent) {
        const countries = this.continentsData[continent];
        const allSelected = this.isAllSelected(continent);
        const newSelection = !allSelected;

        // 重置 selectedCountries 和 selectedCountriesMap 中的相关状态
        this.localSelectedCountries = this.localSelectedCountries.filter(country => !countries.some(c => c.mcc === country.mcc));
        countries.forEach(country => {
          this.$set(this.selectedCountriesMap, country.mcc, false);
        });

        if (newSelection) {
          // 只选择未禁用的国家
          const newCountries = countries
            .filter(country => !this.isCountryDisabled(country.mcc))
            .map(country => ({...country}));

          this.localSelectedCountries = [...newCountries, ...this.localSelectedCountries];
          newCountries.forEach(country => {
            this.$set(this.selectedCountriesMap, country.mcc, true);
          });
        }
      },
      isCountryDisabled(mcc) {
        return this.disabledMccs.includes(mcc);
      },
    }
  }
</script>

<style lang="less" scoped>
  .selected-countries-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    padding: 10px;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    min-height: 50px;
  }

  .selected-country-item {
    padding: 5px 10px;
    background-color: #f8f8f9;
    border-radius: 4px;
    white-space: nowrap;
  }

  .footer-textarea {
    display: flex;
    justify-content: center;
    margin: 30px 0;
  }
</style>
