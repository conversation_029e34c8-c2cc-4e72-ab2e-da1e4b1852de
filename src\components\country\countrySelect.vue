<template>
  <div>
    <batchAdd ref="batchAdd" :continents-data="continentsData" :selected-countries="selectedCountries"
      :disabled-mccs="selectedMccs" @on-selected="handleBatchAddSelected"></batchAdd>
  </div>
</template>

<script>
export default {
  computed: {
    selectedMccs() {
      return this.selectedCountries.map(country => country.mcc);
    }
  },
  methods: {
    handleBatchAddSelected(selectedCountries) {
      // Handle the selected countries
    }
  }
};
</script> 