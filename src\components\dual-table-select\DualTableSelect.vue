<template>
  <div class="dual-table-container" :style="{ height: containerHeight }">
    <div class="source-table">
      <div class="table-header">
        <slot name="source-header">
          <!-- 默认搜索框 -->
          <div style="height: 32px;line-height: 32px; margin-right: 10px;">渠道商简称</div>
          <Input
            v-if="showDefaultSearch"
            v-model="searchKeyword"
            :placeholder="searchPlaceholder"
            style="width: 200px; margin-right: 8px"
            clearable
          />
          <Button v-if="showDefaultSearch" type="primary" @click="handleSearch">搜索</Button>
          <Checkbox
            v-if="showCheckAll"
            :indeterminate="indeterminate"
            :value="checkAll"
            @click.prevent.native="handleCheckAll"
            style="margin-left: auto"
          >全选</Checkbox>
        </slot>
        <slot name="all-check-box"></slot>
      </div>
      <div class="table-content">
        <Table 
          ref="sourceTable"
          :columns="sourceColumns" 
          :data="sourceData" 
          :loading="loading"
          :height="tableHeight"
          :ellipsis="true"
          border
          @on-select="handleSelect"
          @on-select-cancel="handleSelectCancel"
          @on-select-all="handleSelectAll"
          @on-select-all-cancel="handleSelectAllCancel"
        >
          <template slot="noDataText">
            <span>{{ loading ? '加载中...' : '暂无数据' }}</span>
          </template>
          <template v-for="slot in Object.keys($slots)" :slot="slot" slot-scope="scope">
            <slot :name="slot" v-bind="scope"/>
          </template>
        </Table>
        <Page
          v-if="showPagination"
          :total="total"
          :current="current"
          :page-size="pageSize"
          @on-change="handlePageChange"
          size="small"
          show-total
          show-elevator
          style="margin-top: 10px; text-align: right;"
        />
      </div>
    </div>
    <div class="selected-table">
      <div class="table-header">
        <slot name="selected-header">
          <div class="selected-title">已选数据</div>
        </slot>
      </div>
      <div class="table-content">
        <Table 
          :columns="selectedColumns" 
          :data="selectedData" 
          :height="tableHeight"
          :ellipsis="true"
          border
        >
          <template v-for="slot in Object.keys($slots)" :slot="slot" slot-scope="scope">
            <slot :name="slot" v-bind="scope"/>
          </template>
          <template slot-scope="{ row }" slot="action">
            <Button type="error" size="small" @click="handleRemove(row)">
              <Icon type="md-close" />
            </Button>
          </template>
        </Table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DualTableSelect',
  props: {
    // 源数据表格配置
    sourceColumns: {
      type: Array,
      required: true
    },
    sourceData: {
      type: Array,
      default: () => []
    },
    // 已选表格配置
    selectedColumns: {
      type: Array,
      required: true
    },
    // 容器高度
    containerHeight: {
      type: String,
      default: ''
    },
    // 表格高度
    tableHeight: {
      type: [Number, String],
      default: 520
    },
    // 是否显示默认搜索框
    showDefaultSearch: {
      type: Boolean,
      default: true
    },
    // 搜索框占位符
    searchPlaceholder: {
      type: String,
      default: '请输入关键词搜索'
    },
    // 是否显示全选框
    showCheckAll: {
      type: Boolean,
      default: true
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true
    },
    // 分页配置
    total: {
      type: Number,
      default: 0
    },
    current: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 选中的值
    value: {
      type: Array,
      default: () => []
    },
    // 选中的完整数据
    selectedData: {
      type: Array,
      default: () => []
    },
    // 全选状态
    indeterminate: {
      type: Boolean,
      default: false
    },
    // 全选状态
    checkAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchKeyword: ''
    }
  },
  methods: {
    // 搜索处理
    handleSearch() {
      this.$emit('on-search', this.searchKeyword)
    },
    // 全选处理
    handleCheckAll() {
      //
      this.$emit('on-check-all', !this.checkAll)
    },
    // 分页处理
    handlePageChange(page) {
      this.$emit('on-page-change', page)
    },
    // 选择处理
    handleSelect(selection, row) {
      this.$emit('on-select', selection, row)
    },
    handleSelectCancel(selection, row) {
      this.$emit('on-select-cancel', selection, row)
    },
    handleSelectAll(selection) {
      this.$emit('on-select-all', selection)
    },
    handleSelectAllCancel(selection) {
      this.$emit('on-select-all-cancel', selection)
    },
    // 移除已选项
    handleRemove(row) {
      this.$emit('on-remove', row)
    },
    // 重置组件状态
    reset() {
      this.searchKeyword = ''
    }
  }
}
</script>

<style lang="less" scoped>
.dual-table-container {
  display: flex;
  gap: 20px;
  width: 100%;
  
  .source-table,
  .selected-table {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    
    .table-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      justify-content: space-between;
      
      .selected-title {
        font-size: 14px;
        color: #17233d;
        font-weight: 500;
      }
    }
    
    .table-content {
      display: flex;
      flex-direction: column;
      
      .ivu-table-wrapper {
        /deep/ .ivu-table {
          &:before {
            background-color: #e8eaec;
          }
        }
      }
      
      /deep/ .ivu-page {
        margin-top: 16px;
        text-align: right;
      }
    }
  }
}
</style> 