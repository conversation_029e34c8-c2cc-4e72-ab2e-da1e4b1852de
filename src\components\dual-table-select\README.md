# DualTableSelect 双表格选择器

这是一个通用的 Vue 组件，用于实现双表格选择功能，常见于需要从一个大数据列表中选择若干项的场景。左侧为源数据表格，支持搜索、分页和选择；右侧为已选数据表格，展示已选项并支持移除。

## 何时使用

- 需要从大量数据中进行选择，并清晰展示已选项。
- 需要支持搜索、分页、单选、多选、全选和移除功能。
- 需要自定义表格头部或全选区域。

## API

### Props

| 属性名             | 说明                                                                 | 类型             | 默认值            |
|--------------------|----------------------------------------------------------------------|------------------|-------------------|
| `sourceColumns`    | **必需** 源数据表格的列配置 (iview Table columns)                      | `Array`          | -                 |
| `sourceData`       | 源数据表格的数据数组                                                     | `Array`          | `[]`              |
| `selectedColumns`  | **必需** 已选数据表格的列配置 (iview Table columns)                    | `Array`          | -                 |
| `selectedData`     | **必需** 已选数据表格的数据数组 (完整的对象数组)                       | `Array`          | `[]`              |
| `value`            | **必需** 已选数据的唯一标识符数组 (通常是 ID 数组), 可配合 `v-model` 使用 | `Array`          | `[]`              |
| `loading`          | 源数据表格是否处于加载状态                                               | `Boolean`        | `false`           |
| `total`            | 源数据总条数 (用于分页)                                               | `Number`         | `0`               |
| `current`          | 源数据当前页码 (用于分页)                                               | `Number`         | `1`               |
| `pageSize`         | 源数据每页条数 (用于分页)                                               | `Number`         | `10`              |
| `checkAll`         | 全选框的选中状态 (由父组件控制)                                          | `Boolean`        | `false`           |
| `indeterminate`    | 全选框的半选状态 (由父组件控制)                                          | `Boolean`        | `false`           |
| `showDefaultSearch`| 是否显示默认的搜索框和按钮                                               | `Boolean`        | `true`            |
| `searchPlaceholder`| 默认搜索框的占位文本                                                   | `String`         | `'请输入关键词搜索'` |
| `showCheckAll`     | 是否显示默认的全选 Checkbox (在源表格头右侧)                             | `Boolean`        | `true`            |
| `showPagination`   | 是否显示分页组件                                                       | `Boolean`        | `true`            |
| `containerHeight`  | 整个组件容器的高度 (CSS 值)                                            | `String`         | `''`              |
| `tableHeight`      | 表格内容区域的高度 (CSS 值或数字)                                        | `[Number, String]`| `520`             |

**注意:**
*   `value` 和 `selectedData` 需要父组件同步维护。`value` 通常用于 `v-model` 绑定和提交，`selectedData` 用于渲染右侧表格。
*   源数据表格行的选中状态 (`_checked`) 和禁用状态 (`_disabled`) 需要在父组件传入的 `sourceData` 中进行设置。

### Events

| 事件名                | 说明                                               | 回调参数                                  |
|-----------------------|----------------------------------------------------|-------------------------------------------|
| `on-search`           | 点击默认搜索按钮时触发                               | `keyword` (String): 搜索关键词            |
| `on-check-all`        | 点击自定义全选框 (通过 `all-check-box` 插槽或默认 `showCheckAll` 为 true 时) 时触发 | `checked` (Boolean): 是否选中           |
| `on-page-change`      | 页码改变时触发                                     | `page` (Number): 新的页码                 |
| `on-select`           | 在源表格中选中某一项时触发                         | `selection` (Array): 当前页已选项, `row` (Object): 当前操作行 |
| `on-select-cancel`    | 在源表格中取消选中某一项时触发                       | `selection` (Array): 当前页已选项, `row` (Object): 当前操作行 |
| `on-select-all`       | 在源表格中点击**表头全选框**选中全部时触发           | `selection` (Array): 当前页已选项             |
| `on-select-all-cancel`| 在源表格中点击**表头全选框**取消选中全部时触发       | `selection` (Array): 当前页已选项 (通常为空) |
| `on-remove`           | 在已选表格中点击移除按钮时触发                     | `row` (Object): 被移除的行                |

### Slots

| 名称              | 说明                                                   |
|-------------------|--------------------------------------------------------|
| `source-header`   | 自定义源数据表格的头部区域 (会替换默认搜索框和全选框)      |
| `selected-header` | 自定义已选数据表格的头部区域 (会替换默认的 "已选数据" 标题) |
| `all-check-box`   | 自定义全选 Checkbox 的位置和样式 (会替换默认的全选框)    |
| `(其他)`          | 支持透传 iview Table 的 slot，用于自定义单元格渲染       |

### Methods

| 名称    | 说明         | 参数 | 返回值 |
|---------|--------------|------|--------|
| `reset` | 重置搜索关键词 | -    | -      |

## 使用示例与重点说明

### 1. 基本集成

```vue
<template>
  <DualTableSelect
    ref="dualTable"
    v-model="selectedIds"
    :source-columns="sourceCols"
    :source-data="sourceList"
    :selected-columns="selectedCols"
    :selected-data="selectedList"
    :total="totalCount"
    :current="currentPage"
    :page-size="pageSize"
    :loading="isLoading"
    :check-all="isCheckAll"
    :indeterminate="isIndeterminate"
    @on-search="handleSearch"
    @on-page-change="handlePageChange"
    @on-select="handleSelect"
    @on-select-cancel="handleSelectCancel"
    @on-select-all="handleSelectAll"
    @on-select-all-cancel="handleSelectAllCancel"
    @on-remove="handleRemove"
    @on-check-all="handleGlobalCheckAll"
  />
</template>

<script>
import DualTableSelect from '@/components/dual-table-select/DualTableSelect.vue';

export default {
  components: { DualTableSelect },
  data() {
    return {
      selectedIds: [], // v-model 绑定的已选 ID 数组
      sourceCols: [/* ... */],
      sourceList: [], // 源数据 (从 API 获取)
      selectedCols: [/* ... */ { title: '操作', slot: 'action', width: 80, align: 'center' }],
      selectedList: [], // 已选数据 (完整的对象数组)
      totalCount: 0,
      currentPage: 1,
      pageSize: 10,
      isLoading: false,
      isCheckAll: false, // 全选状态
      isIndeterminate: false, // 半选状态
      searchKeyword: '',
      // ...
    };
  },
  methods: {
    async fetchData() {
      this.isLoading = true;
      try {
        // 调用 API 获取源数据
        const res = await yourApi.getSourceData({
          page: this.currentPage,
          size: this.pageSize,
          keyword: this.searchKeyword,
          // ...其他参数
        });
        this.sourceList = res.data.records.map(item => ({
          ...item,
          // !!! 关键: 根据 selectedIds 初始化 _checked 状态
          _checked: this.selectedIds.includes(item.id),
          // !!! 关键: 根据业务逻辑设置 _disabled 状态
          _disabled: this.checkIfDisabled(item),
        }));
        this.totalCount = res.data.total;
        this.updateCheckAllStatus(); // 获取数据后更新全选状态
      } catch (error) {
        // ...错误处理
      } finally {
        this.isLoading = false;
      }
    },

    // 检查是否应禁用某行 (示例)
    checkIfDisabled(item) {
      // 例如：如果已选列表中存在与 item 互斥的项
      // const mcc = this.extractMccFromMccId(item.mccId);
      // return this.selectedList.some(selected => this.extractMccFromMccId(selected.mccId) === mcc && selected.id !== item.id);
      return false; // 默认不禁用
    },

    // --- 事件处理 ---
    handleSearch(keyword) {
      this.searchKeyword = keyword;
      this.currentPage = 1;
      this.fetchData();
    },
    handlePageChange(page) {
      this.currentPage = page;
      this.fetchData();
    },

    // --- 核心：选择与状态切换 ---
    handleSelect(selection, row) {
      // 添加到已选列表 (确保不重复)
      if (!this.selectedIds.includes(row.id)) {
        this.selectedIds.push(row.id);
        this.selectedList.push(row);
      }
      // 可选：更新源数据中其他可能受影响行的 _disabled 状态
      this.updateRelatedDisabledStatus(row, true);
      // 更新全选状态
      this.updateCheckAllStatus();
    },
    handleSelectCancel(selection, row) {
      // 从已选列表移除
      const idIndex = this.selectedIds.indexOf(row.id);
      if (idIndex > -1) {
        this.selectedIds.splice(idIndex, 1);
      }
      const listIndex = this.selectedList.findIndex(item => item.id === row.id);
      if (listIndex > -1) {
        this.selectedList.splice(listIndex, 1);
      }
      // 可选：更新源数据中其他可能受影响行的 _disabled 状态
      this.updateRelatedDisabledStatus(row, false);
      // 更新全选状态
      this.updateCheckAllStatus();
    },

    // --- 核心：全选处理 ---
    // 表头全选/取消全选 (处理当前页)
    handleSelectAll(selection) {
      selection.forEach(row => {
        if (!this.selectedIds.includes(row.id)) {
          this.selectedIds.push(row.id);
          this.selectedList.push(row);
          this.updateRelatedDisabledStatus(row, true);
        }
      });
      this.updateCheckAllStatus();
    },
    handleSelectAllCancel(selection) { // selection 通常为空
      this.sourceList.forEach(row => {
        const idIndex = this.selectedIds.indexOf(row.id);
        if (idIndex > -1) {
          this.selectedIds.splice(idIndex, 1);
          const listIndex = this.selectedList.findIndex(item => item.id === row.id);
          if (listIndex > -1) {
            this.selectedList.splice(listIndex, 1);
          }
          this.updateRelatedDisabledStatus(row, false);
        }
      });
      this.updateCheckAllStatus();
    },
    // 自定义全选框触发 (处理所有页)
    async handleGlobalCheckAll(checked) {
      if (checked) {
        // --- 跨页全选逻辑 ---
        this.isLoading = true;
        try {
          // 1. 获取所有符合条件的数据 ID (可能需要新的 API)
          const allIds = await yourApi.getAllIds({ keyword: this.searchKeyword /*...*/ });
          // 2. 获取这些 ID 对应的完整数据 (如果需要显示在右侧)
          //    或者只更新 selectedIds，右侧数据按需加载
          // 3. (示例) 假设只更新 ID
          this.selectedIds = [...new Set([...this.selectedIds, ...allIds])];
          // 4. (可选) 如果需要更新 selectedList，需要获取完整数据并合并
          // 5. (可选) 更新所有源数据的 _disabled 状态 (如果全选影响禁用)
        } catch (error) { /* ... */ }
        finally { this.isLoading = false; }
      } else {
        // --- 跨页取消全选逻辑 ---
        // 通常是清空所有选择，或根据筛选条件移除
        // 1. 获取所有符合当前筛选条件的数据 ID
        // 2. 从 selectedIds 和 selectedList 中移除这些 ID
        this.selectedIds = []; // 简单示例：清空
        this.selectedList = []; // 简单示例：清空
        // 3. 更新所有源数据的 _disabled 状态
      }
      // 更新本地 sourceList 的 _checked 状态
      this.sourceList.forEach(item => item._checked = this.selectedIds.includes(item.id));
      this.updateCheckAllStatus();
    },

    // --- 核心：移除处理 ---
    handleRemove(row) {
      this.handleSelectCancel([], row); // 复用取消选择的逻辑
    },

    // --- 核心：状态更新 ---
    // 更新源表格行的 _checked 和 _disabled 状态
    // 通常在 fetchData 或选择/移除操作后调用
    refreshSourceDataStatus() {
      this.sourceList.forEach(item => {
        item._checked = this.selectedIds.includes(item.id);
        item._disabled = this.checkIfDisabled(item);
      });
      // 强制更新视图，确保 iview Table 能检测到 _checked 和 _disabled 的变化
      this.$forceUpdate();
      // 如果直接操作 sourceList 后表格状态未更新，可能需要下面这行
      // if (this.$refs.dualTable && this.$refs.dualTable.$refs.sourceTable) {
      //   this.$refs.dualTable.$refs.sourceTable.clearCurrentRow(); // 重新渲染状态
      // }
    },
    // 更新全选/半选状态
    updateCheckAllStatus() {
      if (this.sourceList.length === 0) {
        this.isCheckAll = false;
        this.isIndeterminate = false;
        return;
      }

      // 获取当前页可选的项的数量
      const availableItems = this.sourceList.filter(item => !item._disabled);
      const availableCount = availableItems.length;

      // 获取当前页已选的项的数量 (在可选中的项里算)
      const selectedCount = availableItems.filter(item => item._checked).length;

      if (availableCount === 0) { // 当前页没有可选的
        this.isCheckAll = false;
        this.isIndeterminate = false;
      } else if (selectedCount === 0) { // 当前页一个都没选
        this.isCheckAll = false;
        // 判断是否需要半选：如果总的 selectedIds 不为空，则可能需要半选
        this.isIndeterminate = this.selectedIds.length > 0;
      } else if (selectedCount === availableCount) { // 当前页已全选
        this.isCheckAll = true;
        this.isIndeterminate = false;
        // 注意：这里的 true 只代表当前页全选，不代表跨页全选
      } else { // 当前页部分选中
        this.isCheckAll = false;
        this.isIndeterminate = true;
      }

      // --- 可选：处理跨页全选状态 ---
      // 如果需要精确的跨页全选状态，需要知道所有符合条件的总数
      // const totalAvailableCount = await yourApi.getTotalAvailableCount({...});
      // if (this.selectedIds.length === totalAvailableCount && totalAvailableCount > 0) {
      //   this.isCheckAll = true;
      //   this.isIndeterminate = false;
      // } else if (this.selectedIds.length > 0) {
      //   this.isCheckAll = false;
      //   this.isIndeterminate = true;
      // } else {
      //   this.isCheckAll = false;
      //   this.isIndeterminate = false;
      // }
    },

    // 更新相关行的禁用状态 (示例，用于互斥选择)
    updateRelatedDisabledStatus(currentRow, isSelecting) {
      // const mcc = this.extractMccFromMccId(currentRow.mccId);
      // this.sourceList.forEach(item => {
      //   if (this.extractMccFromMccId(item.mccId) === mcc && item.id !== currentRow.id) {
      //     // 如果是选择操作，则禁用其他相同 MCC 的；如果是取消选择，则解除禁用
      //     item._disabled = isSelecting;
      //   }
      // });
      // this.refreshSourceDataStatus(); // 需要刷新状态
    }
  },
  mounted() {
    this.fetchData();
  }
}
</script>
```

### 2. 状态切换与禁用

**关键点:** `DualTableSelect` 组件本身不直接管理行的选中 (`_checked`) 和禁用 (`_disabled`) 状态。这需要父组件在准备 `sourceData` 时进行设置。

*   **选中状态 (`_checked`):**
    *   在每次获取 `sourceData` (例如 `fetchData` 方法中) 或选择状态改变后，遍历 `sourceData`。
    *   检查每一项的 `id` 是否存在于父组件维护的 `selectedIds` (或 `selectedList`) 数组中。
    *   根据检查结果设置该项的 `_checked` 属性为 `true` 或 `false`。
    *   使用 `$forceUpdate()` 或确保 `sourceList` 被重新赋值来触发视图更新。

*   **禁用状态 (`_disabled`):**
    *   同样在准备 `sourceData` 时设置。
    *   根据业务逻辑判断某行是否应该被禁用 (例如，互斥选择、权限控制等)。
    *   将判断结果赋值给该项的 `_disabled` 属性。
    *   当选择状态改变可能影响其他行的禁用状态时 (如 `addCountry.vue` 中的 MCC 码互斥)，需要显式更新受影响行的 `_disabled` 属性，并刷新 `sourceData` 的状态。

*   **示例 (`refreshSourceDataStatus`):**

    ```javascript
    refreshSourceDataStatus() {
      this.sourceList.forEach(item => {
        item._checked = this.selectedIds.includes(item.id);
        item._disabled = this.checkIfDisabled(item); // checkIfDisabled 是你的业务逻辑
      });
      this.$forceUpdate(); // 确保 iview Table 更新状态
    }
    ```

### 3. 全选与半选状态管理

`checkAll` 和 `indeterminate` 状态也由父组件完全控制，并通过 Props 传递给 `DualTableSelect`。

*   **计算状态:** 在每次选择状态改变 (包括单选、多选、移除、全选/取消全选) 后，父组件需要重新计算这两个状态。
*   **计算逻辑 (`updateCheckAllStatus`):**
    1.  获取当前页 `sourceData` 中**未被禁用**的项 (`availableItems`)。
    2.  获取这些 `availableItems` 中**已被选中** (`_checked` 为 `true`) 的项的数量 (`selectedCount`)。
    3.  比较 `selectedCount` 和 `availableItems.length`：
        *   `selectedCount === 0`: `checkAll = false`, `indeterminate` 取决于**总的** `selectedIds` 是否为空。
        *   `selectedCount === availableItems.length` (且 `availableItems.length > 0`): `checkAll = true`, `indeterminate = false` (表示当前页全选)。
        *   `0 < selectedCount < availableItems.length`: `checkAll = false`, `indeterminate = true` (表示当前页部分选中)。
*   **跨页全选:** 如果需要精确反映跨页全选状态，则需要知道所有符合条件的数据总数，并与 `selectedIds.length` 进行比较。

### 4. 跨页全选实现

`@on-select-all` 和 `@on-select-all-cancel` 事件只处理当前页的数据。真正的跨页全选/取消全选需要通过监听 `@on-check-all` 事件 (配合自定义全选框插槽或 `showCheckAll` prop) 来实现。

*   **逻辑:**
    1.  在 `@on-check-all` 事件处理器中判断是全选还是取消全选。
    2.  **全选:** 通常需要调用一个额外的 API 来获取所有符合当前筛选条件的数据 ID，然后将这些 ID 合并到父组件的 `selectedIds` 和 `selectedList` 中。
    3.  **取消全选:** 可能需要调用 API 获取所有符合条件的 ID，然后从 `selectedIds` 和 `selectedList` 中移除它们；或者直接清空选择。
    4.  更新完 `selectedIds` 和 `selectedList` 后，刷新当前页 `sourceList` 的 `_checked` 和 `_disabled` 状态。
    5.  最后调用 `updateCheckAllStatus` 更新全选/半选状态。

## 最佳实践

*   **状态管理:** 将 `selectedIds`, `selectedList`, `checkAll`, `indeterminate` 等状态放在父组件中管理。
*   **数据驱动:** 通过修改 `sourceData` 中的 `_checked` 和 `_disabled` 属性来控制表格行的状态。
*   **事件驱动:** 监听 `DualTableSelect` 发出的事件，在父组件中处理数据变更和 API 调用。
*   **性能:** 对于跨页全选，考虑只获取 ID 列表以提高性能，右侧的 `selectedList` 可以按需加载或只显示部分。
*   **清晰性:** 将状态更新逻辑（如 `refreshSourceDataStatus`, `updateCheckAllStatus`）封装成独立的方法。 