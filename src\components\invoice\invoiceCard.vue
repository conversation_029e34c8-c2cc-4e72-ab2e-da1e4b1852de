<template>
	<div>
		<div class="_img_view">
			<img class="img" :src='heard_src' width="100%" height="100%"></img>
		</div>
		<div style=" width: 100%; margin:20px 0 ;">
			<div style="display: flex;flex-wrap: wrap;align-items: flex-start;justify-content: space-between;">
				<div class="color_front">{{customerName}}</div>
				<div class="color_fronts"><span class="fontBlod">Invoice No:</span> {{InvoiceNo}}</div>
				<div class="color_front">{{address}}</div>
				<div class="color_fronts"><span class="fontBlod">Invoice Date:</span> {{InvoiceDate}}</div>
				<div class="color_fronts" style="margin-left: 60%;"><span class="fontBlod">Page </span>1<span> of </span>1</div>
				<div style="width: 100%;padding: 25px; text-align: center;font-size: 25px;font-weight: 600;color: #000000;">INVOICE</div>
				<div style="width: 100%; padding: 5px; text-align: right; font-size: 17px;font-weight: 600;color: #000000;">
				  Payment Currency:{{currency}}
				</div>
				<div style="width: 100%;">
					<Form ref="invoiceForm" :model="invoiceForm" :rules="rule">
						<Table border :columns="columns" :data="data">
							<template slot-scope="{ row, index }" slot="description">
								<FormItem prop="productName">
									<Input v-model="invoiceForm.productName" placeholder="请输入productName"
										:clearable="true" style="width: 200px;margin-top: 15px;" />
								</FormItem>
							</template>
							<template slot-scope="{ row, index }" slot="listedPrice">
								<FormItem prop="listedPrice">
									<Input v-model="invoiceForm.listedPrice" placeholder="请输入listedPrice"
										:clearable="true" style="width: 200px;margin-top: 15px;" @on-blur= "getAmount"/>
								</FormItem>
							</template>
							<template slot-scope="{ row, index }" slot="amount">
								<FormItem prop="amount">
									<Input :disabled="true" v-model="InvoiceAmount" placeholder="" style="width: 100px;margin-top: 15px;" />
								</FormItem>
							</template>
						</Table>
					</Form>
				</div>
				<div style="width: 100%;padding: 10px;display: flex;justify-content: space-between; font-weight: 600; color: #000;">
					<p>Total Amount(Tax Exclusive)</p>
					<p>{{InvoiceAmount}}</p>
				</div>
				<Input style="margin-top: 50px;" class="input-call" v-model="InvoiceDescValue" type="textarea"
					:autosize="true" placeholder="发票说明" ></Input>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				//移动logo
				heard_src: require("@/assets/images/cmLink.png"),
				InvoiceAmount: "",
				rule: {
					listedPrice: [{
						required: true,
						message: '请输入Listed Price',
						trigger: 'blur',
					}, {
						validator: (rule, value, cb) => {
							var str = /^(?:(?:[1-9]\d{0,7})(?:\.\d{1,2})?|0\.(?:[1-9]\d?|0[1-9]))$/;
							return str.test(value);
						},
						message: '最高支持8位整数和2位小数正数',
					}],
				}
			}
		},
		props: {
			customerName: {
			  type: String,
			  default: ''
			},
			AccountNo: {
			  type: String,
			  default: ''
			},
			address: {
			  type: String,
			  default: ''
			},
			InvoiceNo: {
			  type: String,
			  default: ''
			},
			InvoiceDate: {
			  type: String,
			  default: ''
			},
			currency: {
			  type: String,
			  default: ''
			},
			InvoiceDescValue: {
				type: String,
				default: ''
			},
			quantityValue: {
				type: Number,
				default: ''
			},
			invoiceForm:{},
			columns: {
				type: Array,
				default: () => []
			},
			data: {
				type: Array,
				default: () => []
			}
		},
		watch: {
			InvoiceDescValue(val, oldVal){
				this.$emit('getdesc',val)
			}
		},
		computed: {
			getAmount() {
				if (this.invoiceForm.listedPrice) {
					const rawAmount = (this.invoiceForm.listedPrice * this.quantityValue).toFixed(2);
					// 使用 Intl.NumberFormat 来格式化数字为千分位格式
					const formatter = new Intl.NumberFormat('en-US', {
					    style: 'decimal',
					    minimumFractionDigits: 2, // 保留两位小数
					    maximumFractionDigits: 2 // 最多两位小数
					});
					this.InvoiceAmount = formatter.format(parseFloat(rawAmount))
					return this.InvoiceAmount;
				} else {
					this.InvoiceAmount = ""
					return this.InvoiceAmount
				}
			}

		},
	}
</script>

<style>
	._img_view {
		margin: 10px 0px;
		width: 55%;
		height: 80px;
	}

	.color_front {
		width: 60%;
		margin-bottom: 5px;
	}

	.color_fronts {
		width: 40%;
		margin-bottom: 5px;
	}

	.fontBlod {
		font-weight: 700;
		color: #000;
	}

	.input-call :nth-last-child(1) {
	  border: 0px;
	  outline: none;
	  background-color: #ffffff;
	  margin: 4px 0;
	}
</style>
