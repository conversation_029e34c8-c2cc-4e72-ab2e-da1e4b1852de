<template>
  <div>
    <div class="_img_view">
      <img class="img" :src='heard_src' width="100%" height="100%"></img>
    </div>
    <div style=" width: 100%; margin:20px 0 ;">
      <div style="display: flex;flex-wrap: wrap;align-items: flex-start;justify-content: space-between;">
        <div style="width: 50%;">
          <div class="color_front" style="width: 250px;">{{AccountNo}}</div>
          <div style="width: 250px">{{address}}</div>
        </div>
        <div style="width: 50%; display: flex;flex-wrap: wrap;align-items: center;justify-content: space-between;">
          <p style="width: 100%; display: flex;justify-content: flex-start;"><span style="width: 25%;">Amount Due
              :</span> {{AmountDue}}</p>
          <p style="width: 100%;display: flex;justify-content: flex-start;"><span style="width: 25%;">Invoice No.
              :</span>{{InvoiceNo}}</p>
          <p style="width: 100%;display: flex;justify-content: flex-start;"><span style="width: 25%;">Account No.
              :</span>{{AccountNo}}</p>
          <p style="width: 100%;display: flex;justify-content: flex-start;"><span style="width: 25%;">Invoice Date
              :</span>{{InvoiceDate}}</p>
        </div>
        <div style="width: 100%;padding: 30px; text-align: center;font-size: 25px;color: #000000;">
          {{FileTitle}}
        </div>
        <div style="width: 100%;">
			<Form ref="invoiceForm" :model="invoiceForm" :rules="rule">
				<Table border  :columns="columns" :data="data" >
					  <template slot-scope="{ row, index }" slot="description">
						<FormItem  prop="invoiceNameDesc" >
							<Input   v-model="invoiceForm.invoiceNameDesc" placeholder="请输入description" :clearable="true" style="width: 200px;margin-top: 15px;" />
						</FormItem>
					  </template>
					  <template slot-scope="{ row, index }" slot="billingPeriod">
						<FormItem prop="billingPeriod" >
							<Input  v-model="invoiceForm.billingPeriod" placeholder="请输入billingPeriod" :clearable="true" style="width: 200px;margin-top: 15px;" />
						</FormItem>
					  </template>
				</Table>
			</Form>
        </div>
		<div style="width: 100%;padding: 10px;">
		  <span >Amount before Tax:     </span>
		  <span style="color: red;">{{AmountTax}}</span>
		</div>
		<div style="width: 100%;padding: 10px;">
		  <span >TAX:     </span>
		  <span style="color: red;">{{Tax}}</span>
		</div>
		<div style="width: 100%;padding: 10px;">
		  <span >Total Amount Due:</span>
		  <span style="color: red;">{{TotalAmount}}</span>
		</div>
		  <Input  style="margin-top: 50px;" class="input-call" v-model="InvoiceDesc" type="textarea" :autosize="true" placeholder="发票说明" />
      </div>
    </div>
  </div>
</template>

<script>
  import config from '@/config'
  export default {
    data() {
      return {
        //移动logo
        heard_src: require("@/assets/images/china_mobail.png"),
		// invoiceForm:{
		// 	description:'',
		// 	billingPeriod:''
		// },
		rule:{
			invoiceNameDesc: [{
				required: true,
				message: '请输入description',
				trigger: 'blur',
				}
			],
			billingPeriod: [{
				required: true,
				message: '请输入billingPeriod',
				trigger: 'blur',
				}
			],
		}
      }
    },
    props: {
      AccountNo: {
        type: String,
        default: ''
      },
      address: {
        type: String,
        default: ''
      },
      AmountDue: {
        type: String,
        default: ''
      },
      InvoiceNo: {
        type: String,
        default: ''
      },
      InvoiceDate: {
        type: String,
        default: ''
      },
      FileTitle: {
        type: String,
        default: 'INVOICE'
      },
	  AmountTax: {
	    type: String,
	    default: ''
	  },
	  Tax: {
	    type: String,
	    default: ''
	  },
	  TotalAmount: {
	    type: String,
	    default: ''
	  },
      InvoiceDesc: {
        type: String,
		default: ''
      },
      columns: {
        type: Array,
        default: () => []
      },
      data: {
        type: Array,
        default: () => []
      },
	  invoiceForm:{}
    },
    methods: {

    },
    mounted() {

    },
	watch:{
	        InvoiceDesc:{//深度监听，可监听到对象、数组的变化
	            handler(val, oldVal){
					this.$emit('InvoiceDesc',val)
	            },
	            deep:true
	        }
	    }
  }
</script>

<style>
  .img {
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px 8px 0 0;
  }

  ._img_view {
    margin: 10px 0px;
    width: 200px;
    height: 60px;
  }

   .input-call :nth-last-child(1) {
    border: 0px;
    outline: none;
    background-color: #ffffff;
    margin: 4px 0;
  }

  .color_front {
    color: black !important;
    margin: 4px 0;
    font-family: "Arial Unicode MS", serif !important;
  }


    .ivu-table .demo-table-info-row td{
        background-color: #2db7f5;
        color: #fff;
    }
    .ivu-table .demo-table-error-row td{
        background-color: #ff6600;
        color: #fff;
    }
    .ivu-table td.demo-table-info-column{
        background-color: #2db7f5;
        color: #fff;
    }
    .ivu-table .demo-table-info-cell-name {
        background-color: #2db7f5;
        color: #fff;
    }
    .ivu-table .demo-table-info-cell-age {
        background-color: #ff6600;
        color: #fff;
    }
    .ivu-table .demo-table-info-cell-address {
        background-color: #187;
        color: #fff;
    }
	.form_input::before{
		content: '* ';
		color: #F5222D;
		display: flex;
	}
</style>
