<template>
  <div>
    <div class="_img_view">
      <img class="img" :src='heard_src' width="100%" height="100%"></img>
    </div>
    <div style=" width: 100%; margin:20px 0 ;">
      <div style="display: flex;flex-wrap: wrap;align-items: flex-start;justify-content: space-between;">
        <div style="width: 50%;">
          <div class="color_front" style="width: 250px;">{{AccountNo}}</div>
          <div style="width: 250px">{{address}}</div>
        </div>
        <div style="width: 50%; display: flex;flex-wrap: wrap;align-items: center;justify-content: space-between;">
          <p style="width: 100%;display: flex;justify-content: flex-start;"><span style="width: 25%;">Amount Due
              :</span> {{AmountDue}}</p>
          <p style="width: 100%;display: flex;justify-content: flex-start;"><span style="width: 25%;">Invoice No.
              :</span>{{InvoiceNo}}</p>
          <p style="width: 100%;display: flex;justify-content: flex-start;"><span style="width: 25%;">Account No.
              :</span>{{AccountNo}}</p>
          <p style="width: 100%;display: flex;justify-content: flex-start;"><span style="width: 25%;">Invoice Date
              :</span>{{InvoiceDate}}</p>
        </div>
        <div style="width: 100%;padding: 30px; text-align: center;font-size: 25px;color: #000000;">
          {{FileTitle}}
        </div>
        <div style="width: 100%;">
			<Form ref="invoiceForm" :model="invoiceForm" :rules="rule" v-if="invoiceForm.length == 3">
				<Table border :columns="columns" :data="data" >
					  <template slot-scope="{ row, index }" slot="description">
						<FormItem prop="invoiceNameDesc">
							<Input v-model="invoiceForm[index].invoiceNameDesc" placeholder="请输入description" :clearable="true" style="width: 200px;margin-top: 15px;" />
						</FormItem>
					  </template>
					  <template slot-scope="{ row, index }" slot="billingPeriod">
						<FormItem prop="billingPeriod">
							<Input v-model="invoiceForm[index].billingPeriod" placeholder="请输入billingPeriod" :clearable="true" style="width: 200px;margin-top: 15px;" />
						</FormItem>
					  </template>
				</Table>
			</Form>
        </div>
		<div class="description_box">
		  <span class="textual_box">Amount before Tax:</span>
		  <span class="currencyCode_box">{{currencyCode}}</span>
      <span class="currencyCode_box">{{TotalAmount}}</span>
    </div>
		<div class="description_box">
		  <span class="textual_box">TAX:</span>
      <span class="currencyCode_box">{{currencyCode}}</span>
		  <span class="currencyCode_box">{{Tax}}</span>
		</div>
		<div class="description_box">
		  <span class="textual_box">Total Amount Due:</span>
      <span class="currencyCode_box">{{currencyCode}}</span>
      <span class="currencyCode_box">{{TotalAmount}}</span>
		</div>
		  <Input  style="margin-top: 50px;" class="input-call" v-model="InvoiceDesc" type="textarea" :autosize="true" placeholder="发票说明" />
      </div>
    </div>
  </div>
</template>

<script>
  import config from '@/config'
  export default {
    data() {
      return {
        //移动logo
        heard_src: require("@/assets/images/china_mobail.png"),
        rule:{
          // invoiceNameDesc: [{
          //   required: true,
          //   message: '请输入invoiceNameDesc',
          //   trigger: 'blur',
          //   }
          // ],
          // billingPeriod: [{
          //   required: true,
          //   message: '请输入billingPeriod',
          //   trigger: 'blur',
          //   }
          // ],
        }
      }
    },
    props: {
      AccountNo: {
        type: String,
        default: ''
      },
      address: {
        type: String,
        default: ''
      },
      AmountDue: {
        type: String,
        default: ''
      },
      InvoiceNo: {
        type: String,
        default: ''
      },
      InvoiceDate: {
        type: String,
        default: ''
      },
      FileTitle: {
        type: String,
        default: 'INVOICE'
      },
      AmountTax: {
        type: String,
        default: ''
      },
      Tax: {
        type: String,
        default: ''
      },
      TotalAmount: {
        type: String,
        default: ''
      },
      currencyCode: {
        type: String,
        default: ''
      },
      InvoiceDesc: {
        type: String,
        default: ''
      },
      columns: {
        type: Array,
        default: () => []
      },
      data: {
        type: Array,
        default: () => []
      },
      invoiceForm: {
        type: Array,//既可以是obejct，也可以是array
        default: () => []
      },
    },
    methods: {
      // 自定义校验
      validateInvoiceForm() {
        const errors = [];

        this.invoiceForm.forEach((item, index) => {
          if (!item.invoiceNameDesc) {
            errors.push(`第 ${index + 1} 行的 description 是必填项`);
          }
          if (!item.billingPeriod) {
            errors.push(`第 ${index + 1} 行的 billingPeriod 是必填项`);
          }
        });

        if (errors.length > 0) {
          this.$Message['warning']({
            content: errors.join('; '),
            background: true,
            duration: 5
          });
          return false;
        }

        // 如果通过验证，则继续提交表单或执行其他操作
        return true;
      },
    },
    mounted() {
    },
    watch:{
	    InvoiceDesc:{//深度监听，可监听到对象、数组的变化
	      handler(val, oldVal){
					this.$emit('InvoiceDesc',val)
	      },
	      deep:true
	    }
	  }
  }
</script>

<style>
  .img {
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px 8px 0 0;
  }

  ._img_view {
    margin: 10px 0px;
    width: 200px;
    height: 60px;
  }

   .input-call :nth-last-child(1) {
    border: 0px;
    outline: none;
    background-color: #ffffff;
    margin: 4px 0;
  }

  .color_front {
    color: black !important;
    margin: 4px 0;
    font-family: "Arial Unicode MS", serif !important;
  }


    .ivu-table .demo-table-info-row td{
        background-color: #2db7f5;
        color: #fff;
    }
    .ivu-table .demo-table-error-row td{
        background-color: #ff6600;
        color: #fff;
    }
    .ivu-table td.demo-table-info-column{
        background-color: #2db7f5;
        color: #fff;
    }
    .ivu-table .demo-table-info-cell-name {
        background-color: #2db7f5;
        color: #fff;
    }
    .ivu-table .demo-table-info-cell-age {
        background-color: #ff6600;
        color: #fff;
    }
    .ivu-table .demo-table-info-cell-address {
        background-color: #187;
        color: #fff;
    }
	.form_input::before{
		content: '* ';
		color: #F5222D;
		display: flex;
	}
  .description_box {
    width: 100%;
    padding: 10px 0 10px 10px;
    display: flex;
    flex-wrap: nowrap;
  }
  .textual_box {
    display: inline-block;
    width: 500px;
  }
  .currencyCode_box {
    color: red;
    display: inline-block;
    width: 120px;
    text-align: center;
  }
</style>
