<template>
  <!-- 验证码校验 -->
	<Form ref="loginCood" :model="form" :rules="rules" @keydown.enter.native.prevent class="loginCood-box">
		<div v-if="this.$i18n.locale === 'en-US'">
			<div class="flex-box">
        <FormItem class="h3-boxOne" v-if="showTip">
            <h3>We have sent the verification code to your email or mobile phone. Please check and input.</h3>
        </FormItem>
      </div>
			<div class="smsCode-box">
        <FormItem prop="smsCode" class="smsCode-input"
          :rules="[{required: true,len: 6,message: $t('sys.smsCode'),trigger: 'blur'},{
        		pattern: /^[0-9]\d*$/,
        		message: 'Please Enter A 6-digit Verification Code',
        		trigger: 'blur'
        	},]">
            <Input v-model="form.smsCode" placeholder="Verification Code" :maxlength="6" clearable>
              <span slot="prepend">
                <Icon :size="20" type="md-checkmark-circle"></Icon>
              </span>
            </Input>
        </FormItem>
        <FormItem class="smscodess">
          <Button :disabled="disabled" shape="circle" @click="getCode" style="float:left;">
            <span>{{valiBtn}}</span>
          </Button>
        </FormItem>
      </div>
		</div>
		<div v-else>
      <div class="flex-box" v-if="showTip">
        <FormItem class="h3-boxOne">
          <h3>我们已将登录验证码发送至您的邮箱或手机号，请您查收并输入。</h3>
        </FormItem>
      </div>
      <div class="smsCode-box">
        <FormItem prop="smsCode" class="smsCode-input2"
          :rules="[{required: true,len: 6,message: $t('sys.smsCode'),trigger: 'blur'},{
        		pattern: /^[0-9]\d*$/,
        		message: '请输入6位数字验证码',
        		trigger: 'blur'
        	},]">
            <Input v-model="form.smsCode" placeholder="请输入验证码" :maxlength="6" clearable>
              <span slot="prepend">
                <Icon :size="20" type="md-checkmark-circle"></Icon>
              </span>
            </Input>
        </FormItem>
        <FormItem class="smscodess">
          <Button :disabled="disabled" shape="circle" @click="getCode" style="float:left;">
            <span>{{valiBtn}}</span>
          </Button>
        </FormItem>
      </div>
		</div>
		<!--  <FormItem prop="captcha">
      <div style="display: flex; flex-wrap: nowrap;">
        <Input v-model="form.captcha" placeholder="请输入验证码" ref="captcha" :maxlength="4" clearable>
        <span slot="prepend">
          <Icon :size="20" type="ios-image"></Icon>
        </span>
        </Input>
        <input ref="sessionId" name="sessionId" type="hidden" />
        <img ref="codeImg" alt="验证码" @click="refreshCode" title="点击换一张" style="width: 30%; height: 32px; margin: 0 10px;">
      </div>
    </FormItem> -->
		<div class="flex-box2">
      <div class="h3-boxthree">
        <Button @click="back" v-if="this.$i18n.locale === 'en-US'" shape="circle" long>Return</Button>
        <Button @click="back" v-else shape="circle" long>返回</Button>
      </div>
      <div class="h3-boxfour">
        <Button @click="handleSubmitCood" :disabled="isSubmitting" type="primary" v-if="this.$i18n.locale === 'en-US'" shape="circle" long>Log In</Button>
        <Button @click="handleSubmitCood" :disabled="isSubmitting" type="primary" v-else shape="circle" long>登录</Button>
      </div>
    </div>
	</Form>
</template>
<script>
	import {
		getVerCode,
		sendMsgForReg
	} from '@/api/user'
	import {
		mapActions
	} from 'vuex'
	export default {
		name: 'loginCood',
		props: {
			refresh: {
				type: Boolean,
				default: false
			},
			language: '',
      userInfo: {
				type: Object,
				default: {}
			},
		},
		data() {
			return {
				lock_src: require("@/assets/images/locked_icon.png"),
				form: {
					smsCode: '',
				},
        rules: {},
        valiBtn: '发送验证码',
				disabled: false,
        showTip: false,
        isSubmitting: false, // 跟踪表单是否正在提交
        isSendingCode: false, // 是否正在发送验证码
        codeTimer: null, // 验证码倒计时器
			}
		},
		computed: {},
		activated: function() {},
		created() {
			let home = false
			this.iistoken(home)
      this.getCode()
		},
    mounted() {
			let lang = this.$i18n.locale
			if (lang === 'en-US') {
				this.valiBtn = 'Send Verification Code'
			}
		},
		watch: {
			language:{
				handler(newVal, oldVal){
					this.$i18n.locale = newVal
					if (newVal === 'en-US') {
						this.valiBtn = 'Send Verification Code'
					} else {
						this.valiBtn = '发送验证码'
					}
				},
			},
		},
		methods: {
			...mapActions([
				"iistoken"
			]),
			// 登录提交
      async handleSubmitCood() {
        if (this.isSubmitting || this.isSendingCode) return; // 防止重复提交或发送验证码

        try {
          this.isSubmitting = true; // 设置提交状态为正在提交
          await this.validateAndSubmit();
        } catch (err) {
          console.error("表单验证或提交失败:", err);
        } finally {
          this.clearCodeTimer();
          this.isSubmitting = false; // 重置提交状态
        }
      },

      async validateAndSubmit() {
        try {
          await this.$refs.loginCood.validate(async (valid) => {
            if (valid) {
              // 触发自定义事件，传递用户名、密码和验证码
              this.$emit('on-success-valid', {
                username: this.userInfo.username,
                password: this.userInfo.password,
                code: this.form.smsCode,
              });
            }
          });
        } catch (validationErr) {
          throw new Error("表单验证失败: " + validationErr.message); // 重新抛出验证错误
        }
      },

      // 发送验证码
      async getCode() {
        if (this.isSendingCode) return; // 防止重复发送
        this.clearCodeTimer()
        this.showTip = true;
        this.isSendingCode = true; // 设置发送状态为正在发送
        this.valiBtn = this.$i18n.locale === 'en-US' ? 'Sending...' : '发送中...';

        try {
          const res = await sendMsgForReg({
            "username": this.userInfo.username,
            "language": this.$i18n.locale,
          });

          if (res.code === '0000') {
            this.$Notice.success({
              title: this.$t("stock.Code"),
              desc: res.data,
            });
            this.startCodeTimer(60); // 启动倒计时
          } else {
            this.handleError();
          }
        } catch (err) {
          this.handleError();
        } finally {
          this.isSendingCode = false; // 重置发送状态，但showTip保持为true直到倒计时结束
        }
      },

      // 处理错误并重置按钮状态
      handleError() {
        this.valiBtn = '发送验证码';
        if (this.$i18n.locale === 'en-US') {
          this.valiBtn = 'Send Verification Code';
        }
      },

      // 验证码倒计时
      startCodeTimer(seconds) {
        this.disabled = true;
        this.clearCodeTimer();
        let countdown = seconds;

        // 使用箭头函数保持this指向
        this.codeTimer = setInterval(() => {
          countdown--;
          // 添加null检查
          if (this.$i18n) {
            this.valiBtn = this.$i18n.locale === 'en-US'
              ? `Retry after ${countdown} seconds`
              : `${countdown}秒后重试`;
          } else {
            console.warn('$i18n is not available');
            this.valiBtn = `${countdown}秒后重试`; // 默认值
          }

          if (countdown <= 0) {
            this.clearCodeTimer();
            this.updateButtonText();
            this.disabled = false;
            this.showTip = false;
          }
        }, 1000);
      },

      // 更新按钮文本
      updateButtonText() {
        if (this.$i18n.locale === 'en-US') {
          this.valiBtn = 'Send Verification Code';
        } else {
          this.valiBtn = '发送验证码';
        }
      },

      // 清除倒计时器
      clearCodeTimer() {
        if (this.codeTimer) {
          clearInterval(this.codeTimer);
          this.codeTimer = null;
        }
      },

			refreshCode() {
				let codeImg = this.$refs.codeImg
				getVerCode().then(resp => {
					codeImg.src = 'data:image/png;base64,' + resp.data.image
					this.form.sessionId = resp.data.sessionId
					this.$refs['sessionId'].value = resp.data.sessionId
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
				})
			},
      back() {
        this.$emit('parent-method');
      },
		},
    beforeDestroy() {
      this.clearCodeTimer();
    },
	}
</script>
<style scoped="scoped">
  .loginCood-box {
    width: 100%;
    margin-top: 10px;
  }
  .flex-box {
    margin-top: 30px;
    display: flex;
  }
  .h3-boxOne {
    margin-right: 8%;
  }
  .h3-boxTwo {
    width: 35%;
    margin-right: 27%;
  }
  .flex-box2 {
    margin-top: 50px;
    display: flex;
  }
  .h3-boxthree {
    width: 35%;
    margin-right: 15%;
  }
  .h3-boxfour {
    width: 35%;
  }
  .smsCode-box {
    display: flex;
    justify-content: flex-start;
  }
  .smsCode-input {
    width: 50%;
    margin-right: 4%;
  }
  .smsCode-input2  {
    width: 50%;
    margin-right: 15%;
  }
  /* 表单之前的上下间距样式 */
	/deep/.ivu-form-item {
	    margin-bottom: 30px;
	    vertical-align: top;
	    zoom: 1;
	}
	/* 发送验证码样式 */
	/deep/.smscodess {
		/* text-align: center; */
		/* margin-left: 25px; */
		/* height: 50px; */
	}
	/* 输入框样式 */
	/deep/.ivu-input-group {
	    display: table;
	    width: 100%;
	    border-collapse: separate;
	    position: relative;
	    font-size: 12px;
	    top: 1px;
	    height: 35px;
	}
	/* 输入框前置图标样式 */
	/deep/.ivu-input-group-prepend, .ivu-input-group-append {
	    padding: 4px 7px;
	    font-size: inherit;
	    font-weight: normal;
	    line-height: 1;
	    color: rgb(115,115,115);
	    text-align: center;
	    background-color: rgb(239,250,255);
	    border-radius: 12px;
		border: 0px;
	}
	/* 输入框样式 */
	/deep/.ivu-input-group .ivu-input, .ivu-input-group .ivu-input-inner-container {
	    width: 100%;
	    float: left;
	    margin-bottom: 0;
	    position: relative;
	    z-index: 2;
	    height: 35px;
	    border-radius: 12px;
	}
	/* 输入框报错前置样式 */
	/deep/.ivu-form-item-error .ivu-input-group-prepend, .ivu-form-item-error .ivu-input-group-append {
	    /* background-color: #fff; */
	    border: 0px solid #ed4014;
	}
	/* 输入框 */
	/deep/.ivu-input {
	    display: inline-block;
	    width: 100%;
	    height: 32px;
	    line-height: 1.5;
	    padding: 4px 7px;
	    font-size: 12px;
	    border: 0px solid #dcdee2;
	    color: rgb(118, 118, 118);
	    background-color: rgb(239,250,255);
	    background-image: none;
	    position: relative;
	    cursor: text;
	    -webkit-transition: border 0.2s ease-in-out, background 0.2s ease-in-out, -webkit-box-shadow 0.2s ease-in-out;
	    transition: border 0.2s ease-in-out, background 0.2s ease-in-out, -webkit-box-shadow 0.2s ease-in-out;
	    transition: border 0.2s ease-in-out, background 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
	    transition: border 0.2s ease-in-out, background 0.2s ease-in-out, box-shadow 0.2s ease-in-out, -webkit-box-shadow 0.2s ease-in-out;
	}
	/* 登录按钮 */
	/deep/.ivu-btn-primary {
		font-weight: 800;
	}
	/deep/.ivu-btn-primary:hover {
		background-color: #57a3f3;
		border-color: #57a3f3;
	}
</style>
