<template>
  <!-- @keydown.enter.native="handleSubmit"  -->
	<Form ref="loginForm" :model="form" :rules="rules" @keydown.enter.native.prevent style="margin-top: 10px;">
		<div v-if="this.$i18n.locale === 'en-US'">
			<FormItem prop="username" :rules="[{
						required: true,
						message: 'Username Cannot Be Empty',
						trigger: 'blur'
					},]" style="width: 82%;">
				<Input v-model="form.username" placeholder="Username" clearable>
				<span slot="prepend">
					<Icon :size="20" type="md-person"></Icon>
				</span>
				</Input>
			</FormItem>
			<FormItem prop="password" :rules="[{
						required: true,
						message: 'Password Cannot Be Empty',
						trigger: 'blur'
					},]" style="width: 82%;">
				<Input type="password" v-model="form.password" placeholder="Password" clearable>
				<span slot="prepend">
					<Icon :size="20" type="md-lock"></Icon>
				</span>
				</Input>
			</FormItem>
		</div>
		<div v-else>
			<FormItem prop="username" :rules="[{
						required: true,
						message: '账号不能为空',
						trigger: 'blur'
					},]" style="width: 82%;">
				<Input v-model="form.username" placeholder="用户名" clearable>
				<span slot="prepend">
					<Icon :size="20" type="md-person"></Icon>
				</span>
				</Input>
			</FormItem>
			<FormItem prop="password" :rules="[{
						required: true,
						message: '密码不能为空',
						trigger: 'blur'
					},]" style="width: 82%;">
				<Input type="password" v-model="form.password" placeholder="请输入密码" clearable>
				<span slot="prepend">
					<Icon :size="20" type="md-lock"></Icon>
					<!-- <img :src="lock_src" style="width: 20px; height: 20px; color: #737373;" alt="111" width="100%" height="100%" /> -->
				</span>
				</Input>
			</FormItem>
		</div>
		<FormItem>
			<Button @click="handleSubmit" :disabled="isSubmitting" type="primary" style="width: 82%;" v-if="this.$i18n.locale === 'en-US'">Log In</Button>
			<Button @click="handleSubmit" :disabled="isSubmitting" type="primary" style="width: 82%;" v-else>登录</Button>
		</FormItem>
	</Form>
</template>
<script>
	import {
		mapActions
	} from 'vuex'
	export default {
		name: 'LoginForm',
		props: {
			refresh: {
				type: Boolean,
				default: false
			},
			language: '',
      rules: {},
		},
		data() {
			return {
				lock_src: require("@/assets/images/locked_icon.png"),
				form: {
					username: '',
					password: '',
					type: 1,
				},
        isSubmitting: false, // 跟踪表单是否正在提交
			}
		},
		computed: {
    },
		activated: function() {},
		mounted() {
			let lang = this.$i18n.locale
		},
		watch: {
		},
		methods: {
			...mapActions([
				"iistoken"
			]),
			//登录提交
      async handleSubmit() {
			  if (this.isSubmitting) return; // 防止重复提交
			  this.isSubmitting = true; // 设置提交状态为正在提交
        await this.handleLogin();
			  this.isSubmitting = false; // 重置提交状态
			},
      async handleLogin() {
        try {
          await this.$refs.loginForm.validate(async (valid) => {
            if (valid) {
            	this.$emit('on-success-valid', {
            		username: this.form.username,
            		password: this.form.password,
            	})
            }
          });
        } catch (err) {
          console.error("表单验证失败:", err);
        } finally {
          this.isSubmitting = false; // 重置提交状态
        }
      },
		},
		created() {
			let home = false
			this.iistoken(home)
		}
	}
</script>
<style scoped="scoped">
	/* 表单之前的上下间距样式 */
	/deep/.ivu-form-item {
	    margin-bottom: 30px;
	    vertical-align: top;
	    zoom: 1;
	}
	/* 发送验证码样式 */
	/deep/.smscodess {
		text-align: center;
		margin-left: 25px;
		height: 35px;
	}
	/* 输入框样式 */
	/deep/.ivu-input-group {
	    display: table;
	    width: 100%;
	    border-collapse: separate;
	    position: relative;
	    font-size: 12px;
	    top: 1px;
	    height: 35px;
	}
	/* 输入框前置图标样式 */
	/deep/.ivu-input-group-prepend, .ivu-input-group-append {
	    padding: 4px 7px;
	    font-size: inherit;
	    font-weight: normal;
	    line-height: 1;
	    color: rgb(115,115,115);
	    text-align: center;
	    background-color: rgb(239,250,255);
	    border-radius: 12px;
		border: 0px;
	}
	/* 输入框样式 */
	/deep/.ivu-input-group .ivu-input, .ivu-input-group .ivu-input-inner-container {
	    width: 100%;
	    float: left;
	    margin-bottom: 0;
	    position: relative;
	    z-index: 2;
	    height: 35px;
	    border-radius: 12px;
	}
	/* 输入框报错前置样式 */
	/deep/.ivu-form-item-error .ivu-input-group-prepend, .ivu-form-item-error .ivu-input-group-append {
	    /* background-color: #fff; */
	    border: 0px solid #ed4014;
	}
	/* 输入框 */
	/deep/.ivu-input {
	    display: inline-block;
	    width: 100%;
	    height: 32px;
	    line-height: 1.5;
	    padding: 4px 7px;
	    font-size: 12px;
	    border: 0px solid #dcdee2;
	    color: rgb(118, 118, 118);
	    background-color: rgb(239,250,255);
	    background-image: none;
	    position: relative;
	    cursor: text;
	    -webkit-transition: border 0.2s ease-in-out, background 0.2s ease-in-out, -webkit-box-shadow 0.2s ease-in-out;
	    transition: border 0.2s ease-in-out, background 0.2s ease-in-out, -webkit-box-shadow 0.2s ease-in-out;
	    transition: border 0.2s ease-in-out, background 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
	    transition: border 0.2s ease-in-out, background 0.2s ease-in-out, box-shadow 0.2s ease-in-out, -webkit-box-shadow 0.2s ease-in-out;
	}
	/* 登录按钮 */
	/deep/.ivu-btn-primary {
		color: #fff;
		background-color: #2d8cf0;
		border-color: #2d8cf0;
		border-radius: 20px;
		font-size: 18px;
		font-weight: 800;
	}
	/deep/.ivu-btn-primary:hover {
		background-color: #57a3f3;
		border-color: #57a3f3;
	}
</style>
