<template>
  <Form ref="loginForm" :model="form" :rules="rules" @keydown.enter.native="handleSubmit">
    <FormItem prop="username">
      <Input v-model="form.username" placeholder="请输入手机号码" clearable>
      <span slot="prepend">
        <Icon :size="20" type="md-call"></Icon>
      </span>
      </Input>
    </FormItem>
    <!--    <FormItem prop="phoneNum">
      <Input v-model="form.phoneNum" placeholder="请输入手机号" :maxlength="16" clearable>
      <span slot="prepend">
        <Icon :size="20" type="md-lock"></Icon>
      </span>
      </Input>
    </FormItem> -->
    <FormItem prop="smsCode">
      <div style="display: flex; flex-wrap: nowrap;">
        <Input v-model="form.smsCode" placeholder="请输入短信验证码" ref="smsCode" :maxlength="6" clearable>
        <span slot="prepend">
          <Icon :size="20" type="ios-mail"></Icon>
        </span>
        </Input>
        <input ref="sessionId" name="sessionId" type="hidden" />
        <Button id="msgo" @click="handlePhone" type="success" :disabled="disabled" long style="width: 30%;margin: 0 0 0 10px;">发送验证码</Button>
      </div>
    </FormItem>
    <!--   <FormItem prop="captcha">
      <div style="display: flex; flex-wrap: nowrap;">
        <Input v-model="form.captcha" placeholder="请输入图形验证码" ref="captcha" :maxlength="4" clearable>
        <span slot="prepend">
          <Icon :size="20" type="ios-image"></Icon>
        </span>
        </Input>
        <input ref="sessionId" name="sessionId" type="hidden" />
        <img ref="codeImg" alt="验证码" @click="refreshCode" title="点击换一张" style="width: 30%; height: 32px; margin: 0 10px;">
      </div>
    </FormItem> -->
   <FormItem>
        <Button @click="handleSubmit" type="primary" long>登录</Button>
    </FormItem>
  </Form>
</template>

<script>
  // import router from '@/router'
  import config from '@/config'
  export default {
    name: 'LoginMsg',
    props: {
      // usernameRules: {
      //   type: Array,
      //   default: () => {
      //     return [{
      //       required: true,
      //       message: '用户名不能为空',
      //       trigger: 'blur'
      //     }]
      //   }
      // },
      usernameRules: {
        type: Array,
        default: () => {
          return [{
            required: true,
            message: '手机号不能为空',
            trigger: 'blur'
          }]
        }
      },
      // verifyCodeRules: {
      //   type: Array,
      //   default: () => {
      //     return [{
      //       required: true,
      //       message: '请输入四位验证码',
      //       trigger: 'blur'
      //     },
      //     {
      //       len: 4,
      //       message: '请输入四位验证码',
      //       trigger: 'blur'
      //     }
      //     ]
      //   }
      // },
      smsCodeRules: {
        type: Array,
        default: () => {
          return [{
              required: true,
              message: '请输入短信验证码',
              trigger: 'blur'
            },
            {
              max: 6,
              message: '请输入短信验证码',
              trigger: 'blur'
            }
          ]
        }
      },
      refresh: {
        type: Boolean,
        default: false
      },
      time: {
        type: Number,
        default: false
      }
    },
    data() {
      return {
        form: {
          username: '',
          password: '',
          type: 2,
          smsCode: ''
        },
        disabled: false
      }
    },
    computed: {
      rules() {
        return {
          username: this.usernameRules,
          // phoneNum: this.phoneNumRules,
          smsCode: this.smsCodeRules,
          // captcha: this.verifyCodeRules
        }
      }
    },
    watch: {
      time(newVal, old) {
        if (newVal !== 0) {
          this.disabled = true
        }
        this.showtime(newVal)
      }
      // refresh (newVal, oldVal) {
      //   this.refreshCode()
      // }
    },
    activated: function() {
      // this.refreshCode()
    },
    mounted: function() {},
    methods: {
      returnBak() {
        this.$emit('on-cancel-valid', false)
      },
      handleSubmit() {
        this.$refs.loginForm.validate((valid) => {
          if (valid) {
            this.$emit('on-success-valid', {
              username: this.form.username,
              password: this.form.password,
              type: this.form.type,
              smsCode: this.form.smsCode
            })
          }
        })
      },
      handlePhone: function() {
        if (!/^[0-9]*$/.test(this.form.username) || !/^\d{4,16}$/.test(this.form.username)) {
          this.$Notice.warning({
            title: '号码格式错误',
            desc: '号码格式错误，请输入正确的手机号码'
          })
          return
        }
        this.$emit('on-send-code', {
          phoneNumber: this.form.username,
          type: 1
        })
        this.disabled = true
        try {
          document.getElementById('msgo').innerHTML = '正在发送'
        } catch (e) {}
      },
      showtime: function(t) {
        let _this = this
        if (t !== 0) {
          setTimeout(function() {
            try {
              let tim = t - 1
              document.getElementById('msgo').innerHTML = ' (' + tim + ')秒'
              _this.$emit('timeChange', t - 1)
            } catch (e) {}
          }, 1000)
        } else {
          this.disabled = false
          try {
            document.getElementById('msgo').innerHTML = '发送验证码'
          } catch (e) {}
        }
      },
      // refreshCode() {
      //   console.log('获取验证码')
      //   let codeImg = this.$refs.codeImg
      //   getVerCode().then(resp => {
      //     console.log(resp)
      //     codeImg.src = 'data:image/png;base64,' + resp.data.image
      //     this.form.sessionId = resp.data.sessionId
      //     this.$refs['sessionId'].value = resp.data.sessionId
      //   }).catch((err) => {
      //     console.log(err)
      //     this.$Notice.error({
      //       title: '服务器内部异常',
      //       desc: '获取验证码失败'
      //     })
      //   }).finally(() => {
      //     this.loading = false
      //   })
      // }
    }

  }
</script>
