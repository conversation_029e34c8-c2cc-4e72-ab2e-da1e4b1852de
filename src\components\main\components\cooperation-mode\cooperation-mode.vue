<template>
	<div>
		<Dropdown trigger="click" @on-click="selectMode">
			<a href="javascript:void(0)">
				{{$t('support.cooperationModel')}}
				<Icon :size="18" type="md-arrow-dropdown" />
			</a>
			<DropdownMenu slot="list">
				<DropdownItem v-for="(value, key) in modeList" :name="key" :key="key">{{ value }}</DropdownItem>
			</DropdownMenu>
		</Dropdown>
	</div>
</template>

<script>
	import {
    getChannelCooperationModeForLogin
	} from '@/api/channel'
	import { typeOf } from 'mathjs'
	export default {
		name: 'CooperationMode',
		inject: ['reload'], //接受App那边提供的方法
		data() {
			return {
				modeList: {},
				modeFlag: '',
				cooperationMode: ''
			}
		},
		watch: {
			modeFlag(newVal, oldVal) {
				this.$emit('on-mode-change', newVal)
				this.reload() //在触发切换合作模式后调用，相当于直接调用app里写的reload方法
			},

		},
		methods: {
			selectMode(name) {
				this.modeFlag = name
			},
			modeShow() {
				getChannelCooperationModeForLogin({
					corpId: sessionStorage.getItem('corpId')
				}).then(res => {
					if (res.code == '0000') {
						if (res.data.length > 1) {
							if ( res.data.includes("1") && res.data.includes("2") && !res.data.includes("3")) {
								if (this.cooperationMode == '1') {
									this.modeList = {
										2: this.$t('A2Zmode'),
									}
								} else {
									this.modeList = {
										1: this.$t('consignmentSalesModel'),
									}
								}
							} else if (res.data.includes("1") && res.data.includes("3") && !res.data.includes("2")) {
								if (this.cooperationMode == '1') {
									this.modeList = {
										3: this.$t('resourceMode')
									}
								} else {
									this.modeList = {
										1: this.$t('consignmentSalesModel')
									}
								}
							} else if (res.data.includes("2") && res.data.includes("3") && !res.data.includes("1")){
								if (this.cooperationMode == '2') {
									this.modeList = {
										3: this.$t('resourceMode')
									}
								} else {
									this.modeList = {
										2: this.$t('A2Zmode')
									}
								}
							} else {
								if (this.cooperationMode == '1') {
									this.modeList = {
										2: this.$t('A2Zmode'),
										3: this.$t('resourceMode')
									}
								} else if (this.cooperationMode == '2'){
									this.modeList = {
										1: this.$t('consignmentSalesModel'),
										3: this.$t('resourceMode')
									}
								} else {
									this.modeList = {
										1: this.$t('consignmentSalesModel'),
										2: this.$t('A2Zmode'),
									}
								}
							}
						}
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				})
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			this.modeShow()
		}
	}
</script>
