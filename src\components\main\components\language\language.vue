<template>
  <div>
    <Dropdown trigger="click" @on-click="selectLang">
      <a href="javascript:void(0)">
        {{ title }}
        <Icon :size="18" type="md-arrow-dropdown" />
      </a>
      <DropdownMenu slot="list">
        <DropdownItem v-for="(value, key) in localList" :name="key" :key="`lang-${key}`">{{ value }}</DropdownItem>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
  export default {
    name: 'Language',
    inject: ['reload'], //接受App那边提供的方法
    props: {
      lang: String
    },
    data() {
      return {
        langList: {
          'zh-CN': '语言',
          'en-US': 'Lang'
        },
        localList: {
          'zh-CN': '中文简体',
          'en-US': 'English'
        }
      }
    },
    watch: {
      lang(lang) {
        this.$i18n.locale = lang
        this.reload() //在触发切换语言后调用，相当于直接调用app里写的reload方法
      }
    },
    computed: {
      title() {
        return this.langList[this.lang]
      }
    },
    methods: {
      selectLang(name) {
        this.$emit('on-lang-change', name)
      }
    }
  }
</script>
