<template>
  <div class="user-avatar-dropdown">
    <Dropdown @on-click="handleClick">
      <Badge :dot="!!messageUnreadCount">
        <Avatar :src="userAvatar" />
      </Badge>
      <Icon :size="18" type="md-arrow-dropdown"></Icon>
      <DropdownMenu slot="list">
        <!-- <DropdownItem name="repwd">{{$t('common.rePwd')}}</DropdownItem> -->
        <DropdownItem name="logout">{{$t('common.logOut')}}</DropdownItem>

      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
  import './user.less'
  import {
    mapActions
  } from 'vuex'
  import { logoutClearRedis} from '@/api/system/login'
  export default {
    name: 'User',
    props: {
      userAvatar: {
        type: String,
        default: ''
      },
      messageUnreadCount: {
        type: Number,
        default: 0
      }
    },
    methods: {
      ...mapActions([
        'handleLogOut'
      ]),
      logout() {
        this.handleLogOut().then(() => {
          this.$router.push({
            name: 'login'
          })
        })
      },
      repwd() {
        this.$router.push({
          name: 'pwd_mngr'
        })
      },
      handleClick(name) {
        switch (name) {
          case 'logout':
            //TODO 退出登录请求接口
        logoutClearRedis(this.$store.state.user.userName).then((res) => {

          if(res.code === '0000'){
            this.logout()
          }

        })

            break

          case 'repwd':
            this.repwd()
            break
        }
      }
    }
  }
</script>
