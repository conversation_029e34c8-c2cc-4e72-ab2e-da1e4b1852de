<template>
	<Layout style="height: 100%" class="main">
		<Sider hide-trigger collapsible :width="256" :collapsed-width="64" v-model="collapsed" class="left-sider"
			:style="{ overflow: 'hidden' }">
			<side-menu accordion ref="sideMenu" :active-name="$route.name" :collapsed="collapsed"
				@on-select="turnToPage" :menu-list="menuList">
				<!-- 需要放在菜单上面的内容，如Logo，写在side-menu标签内部，如下 -->
				<div class="logo-con">
					<img v-show="!collapsed" :src="maxLogo" key="max-logo" style="width:240px; height: 60px;" />
					<img v-show="collapsed" :src="minLogo" key="min-logo" />
				</div>
			</side-menu>
		</Sider>
		<Layout>
			<Header class="header-con">
				<header-bar :collapsed="collapsed" @on-coll-change="handleCollapsedChange">
					<user :message-unread-count="unreadCount" :user-avatar="userAvatar" />
					<language v-if="$config.useI18n" @on-lang-change="setLocal" style="margin-right: 10px;"
						:lang="local" />
					<cooperation-mode v-if="modeLength > 1" @on-mode-change="setCooperationMode" style="margin-right: 10px;"/>
					<!-- <error-store v-if="$config.plugin['error-store'] && $config.plugin['error-store'].showInHeader" :has-read="hasReadErrorPage" :count="errorCount"></error-store> -->
					<fullscreen v-model="isFullscreen" style="margin-right: 20px;" />
					<strong style="color: rgb(81, 90, 110); margin:0 20px 0 20px; font-size: 15px;">
						{{ $t("common.welcome") }}{{ $store.state.user.userName }}
					</strong>
				</header-bar>
			</Header>
			<Content class="main-content-con">
				<Layout class="main-layout-con">
					<div class="tag-nav-wrapper">
						<tags-nav :value="$route" @input="handleClick" :list="tagNavList" @on-close="handleCloseTag" />
					</div>
					<Content class="content-wrapper">
						<keep-alive :include="cacheList">
							<router-view />
						</keep-alive>
						<ABackTop :height="100" :bottom="80" :right="50" container=".content-wrapper"></ABackTop>
					</Content>
				</Layout>
			</Content>
		</Layout>
	</Layout>
</template>
<script>
	import SideMenu from "./components/side-menu";
	import HeaderBar from "./components/header-bar";
	import TagsNav from "./components/tags-nav";
	import User from "./components/user";
	import ABackTop from "./components/a-back-top";
	import Fullscreen from "./components/fullscreen";
	import Language from "./components/language";
	import ErrorStore from "./components/error-store";
	import CooperationMode from "./components/cooperation-mode";
	import {
		mapMutations,
		mapActions,
		mapGetters
	} from "vuex";
	import {
		getNewTagList,
		routeEqual
	} from "@/libs/util";
	import routers from "@/router/routers";
	import minLogo from "@/assets/images/logo-min.jpg";
	import maxLogoCN from "@/assets/images/logo-cn.jpg";
	import maxLogoUS from "@/assets/images/logo-us.jpg";
	import "./main.less";
	export default {
		name: "Main",
		components: {
			SideMenu,
			HeaderBar,
			Language,
			TagsNav,
			Fullscreen,
			ErrorStore,
			User,
			ABackTop,
			CooperationMode
		},
		data() {
			return {
				collapsed: false,
				minLogo,
				maxLogo: maxLogoCN,
				userAvatar: require("@/assets/images/header.jpg"),
				isFullscreen: false,
				modeLength: '',
			};
		},
		computed: {
			...mapGetters(["errorCount"]),
			tagNavList() {
				return this.$store.state.app.tagNavList;
			},
			tagRouter() {
				return this.$store.state.app.tagRouter;
			},
			// userAvatar () {
			//   return this.$store.state.user.avatarImgPath
			// },
			cacheList() {
				const list = [
					"ParentView",
					...(this.tagNavList.length ?
						this.tagNavList
						.filter(item => !(item.meta && item.meta.notCache))
						.map(item => item.name) : [])
				];
				return list;
			},
			menuList() {
				return this.$store.getters.menuList;
			},
			local() {
				return this.$store.state.app.local;
			},
			hasReadErrorPage() {
				return this.$store.state.app.hasReadErrorPage;
			},
			unreadCount() {
				return this.$store.state.user.unreadCount;
			},
		},
		methods: {
			...mapMutations([
				"setBreadCrumb",
				"setTagNavList",
				"addTag",
				"setLocal",
				"setHomeRoute",
				"closeTag",
				"setCooperationMode"
			]),
			...mapActions([
        "handleLogin",
        "handleSSOLogin"
      ]),
			turnToPage(route) {
				let {
					name,
					params,
					query
				} = {};

				//密码过期或者首次登录强制修改密码
				if (this.$store.state.user.isUpdatePassword === 1) {
					this.$router.push({
						name: "pwd_mngr"
					});
					return;
				}

				if (typeof route === "string") name = route;
				else {
					name = route.name;
					params = route.params;
					query = route.query;
				}
				if (name.indexOf("isTurnByHref_") > -1) {
					window.open(name.split("_")[1]);
					return;
				}
				this.$router.push({
					name,
					params,
					query
				});
			},
			handleCollapsedChange(state) {
				this.collapsed = state;
			},
			handleCloseTag(res, type, route) {
				if (type !== "others") {
					if (type === "all") {
						this.turnToPage(this.$config.homeName);
					} else {
						if (routeEqual(this.$route, route)) {
							this.closeTag(route);
						}
					}
				}
				this.setTagNavList(res);
			},
			handleClick(item) {
				this.turnToPage(item);
			},
			setCooperationMode(mode) {
				sessionStorage.setItem("cooperationMode" , mode)
			},
		},
		watch: {
			$route(newRoute) {
				const {
					name,
					query,
					params,
					meta
				} = newRoute;
				this.addTag({
					route: {
						name,
						query,
						params,
						meta
					},
					type: "push"
				});
				this.setBreadCrumb(newRoute);
				this.setTagNavList(getNewTagList(this.tagNavList, newRoute));
				this.$refs.sideMenu.updateOpenName(newRoute.name);
			},
		},
		mounted() {
			/**
			 * @description 初始化设置面包屑导航和标签导航
			 */
			this.setTagNavList();
			this.setHomeRoute(routers);
			const {
				name,
				params,
				query,
				meta
			} = this.$route;
			this.addTag({
				route: {
					name,
					params,
					query,
					meta
				}
			});
			this.setBreadCrumb(this.$route);
			this.modeLength = sessionStorage.getItem("modeLength")
			// 设置初始语言
			let lang = this.$i18n.locale
			this.setLocal(lang);
			// 如果当前打开页面不在标签栏中，跳到homeName页
			if (!this.tagNavList.find(item => item.name === this.$route.name)) {
				this.$router.push({
					name: this.$config.homeName
				});
			}
			if (lang === 'en-US') {
				this.maxLogo = maxLogoUS
			}
		}
	};
</script>
