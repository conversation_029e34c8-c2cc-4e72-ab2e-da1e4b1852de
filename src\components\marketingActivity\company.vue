<template>
  <div>
    <Modal :value="companyVisible" title="参与公司详情" width="900" @on-cancel="companyClose" @keydown.enter.native.prevent
      @input="val => $emit('update:companyVisible', val)" :mask-closable="false" :label-width="100">
      <Form label-position="right" :label-width="100">
        <Row>
          <Col span="12">
          <FormItem label="活动名称:" >
            {{ selectedRow.campaignName }}
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="适用合作模式:" >
            {{ selectedRow.cooperationMode === '1' ? '代销' : 'A2Z' }}
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="活动开始时间:" >
            {{ formatDateToClient(selectedRow.startTime) }}
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="活动结束时间:" >
            {{ formatDateToClient(selectedRow.endTime) }}
          </FormItem>
          </Col>
          <template v-if="selectedRow.cooperationMode === '2'">
            <Col span="12">
            <FormItem label="环比开始时间:" >
              {{ formatDateToClient(selectedRow.sequentialStartTime) }}
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem label="环比结束时间:" >
              {{ formatDateToClient(selectedRow.sequentialEndTime) }}
            </FormItem>
            </Col>
          </template>
          <Col span="12" v-if="selectedRow.cooperationMode === '1'">
          <FormItem label="总计返利:" >
            {{ selectedRow.expectAmountRefund }} / {{ selectedRow.totalAmountRefund }}
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="总预计返利:"  v-if="selectedRow.cooperationMode === '2'">
            {{ selectedRow.expectAmountRefund }}
          </FormItem>
          </Col>

          <Col span="12" v-if="selectedRow.campaignStatus === '1' && selectedRow.cooperationMode === '1'">
          <!-- 添加充值记录按钮 -->
          <Button type="primary" icon="md-add" class="add-recharge-btn" @click="addRechargeRecord" v-has="'addRechargeItem'">
            添加充值记录
          </Button>
          </Col>
        </Row>

        <!-- 表格展示区域 -->
        <div class="table-container">
          <Table :columns="getColumns" :data="localCompanyList" :ellipsis="true">
          </Table>
        </div>

        <div class="page-info">
          <Page :total="total" show-total show-elevator :page-size="pageSize" :current="pageNum" @on-change="handlePageChange" />
        </div>
      </Form>
      <div slot="footer" class="dialog-footer">
        <Button @click="companyClose">返回</Button>
      </div>
    </Modal>

    <!-- 添加充值记录弹窗 -->
    <Modal v-model="rechargeVisible" title="添加充值记录" width="800" :mask-closable="false"
      @on-cancel="handleRechargeCancel">
      <Form ref="rechargeForm" :model="rechargeForm" label-position="right" :rules="rechargeRules" :label-width="80">
        <Row :gutter="24">
          <Col span="11">
          <FormItem label="公司名称" prop="companyId" required>
            <Select v-model="rechargeForm.companyId" placeholder="请选择公司" filterable clearable @on-change="handleCompanyChange">
              <Option v-for="item in companyOptions" :value="item.corpId" :key="item.corpId" class="company-option">{{ item.companyName }}</Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="11">
          <FormItem label="公司币种">
            <span>{{ companyCurrency || '--' }}</span>
          </FormItem>
          </Col>
        </Row>
        <div v-for="(item, index) in rechargeForm.rechargeList" :key="index" class="recharge-item">
          <div class="recharge-content">
            <Row :gutter="24" align="middle">
              <Col span="11">
              <FormItem :label="'充值金额'" :prop="'rechargeList.' + index + '.amount'" :rules="{
                required: true,
                trigger: 'blur',
                validator: validateAmount
              }" required :label-width="80">
              <!-- 只能输入正整数 -->
              <InputNumber v-model="item.amount"  :min="1"  :max="99999999" :step="1" style="width: 100%"
                  :placeholder="'请输入充值金额'" />
              </FormItem>
              </Col>
              <Col span="11">
              <FormItem :label="'充值时间'" :prop="'rechargeList.' + index + '.rechargeTime'" :rules="{
                required: true,
                type: 'date',
                message: '请选择充值时间',
                trigger: 'change',
                validator: (rule, value, callback) => {
                  if (!value) {
                    callback(new Error('请选择充值时间'));
                  } else {
                    const rechargeTime = new Date(value);
                    const startTime = new Date(selectedRow.startTime);
                    const endTime = new Date(selectedRow.endTime);

                    if (rechargeTime < startTime || rechargeTime > endTime) {
                      callback(new Error('充值时间必须在活动时间范围内'));
                    } else {
                      callback();
                    }
                  }
                }
              }" required :label-width="80">
                <DatePicker v-model="item.rechargeTime" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                  :options="{
                    disabledDate(date) {
                      const startTime = new Date(selectedRow.startTime);
                      const endTime = new Date(selectedRow.endTime);
                      return date < startTime || date > endTime;
                    }
                  }"
                  placeholder="请选择充值时间" style="width: 100%" />
              </FormItem>
              </Col>
              <Col span="2" v-if="rechargeForm.rechargeList.length > 1">
                <Button type="error" size="small" class="delete-btn" @click="removeRechargeItem(index)">删除</Button>
              </Col>
            </Row>
          </div>
        </div>
        <div class="add-button-wrapper">
          <Button type="success" size="small" @click="addRechargeItem" icon="md-add">添加充值记录</Button>
        </div>
      </Form>
      <div slot="footer">
        <Button @click="handleRechargeCancel">取消</Button>
        <Button type="primary" @click="handleRechargeSubmit" :loading="submitLoading">确认</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { getCorpDetailsPage, getCorpList, addSettlementLog } from '@/api/marketingActivity/index';
import { formatTime ,formatDate } from "@/libs/tools";

export default {
  props: {
    companyVisible: {
      type: Boolean,
      default: false
    },
    selectedRow: {
      type: Object,
      default: () => ({})
    },
    companyDetails: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    // 验证充值金额
    const validateAmount = (rule, value, callback) => {
      if (!value && value !== 0) {
        return callback(new Error('请输入充值金额'));
      }
      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        return callback(new Error('请输入正确的金额'));
      }

      // 添加正整数校验
      if (!/^\d+$/.test(value)) {
        return callback(new Error('请输入正整数'));
      }

      if (numValue <= 0) {
        return callback(new Error('充值金额必须大于0'));
      }
      callback();
    };

    return {
      rechargeVisible: false,
      companyLoading: false,
      submitLoading: false,
      companyOptions: [],
      companyCurrency: '', // 显示用的币种名称
      currencyCode: '', // 接口传参用的币种代码
      rechargeForm: {
        companyId: '',
        rechargeList: [
          {
            amount: null,
            rechargeTime: new Date()
          }
        ]
      },
      rechargeRules: {
        companyId: [
          { required: true, message: '请选择公司', trigger: 'change' }
        ]
      },
      validateAmount,
      pageNum: 1,
      pageSize: 5,
      total: 0,
      localCompanyList: [],
      activityStartTime: new Date(),
      activityEndTime: new Date()
    };
  },
  computed: {
    // 监听弹窗打开并且有数据，则请求table数据
    getColumns () {
      // 根据合作模式返回不同的列配置
      if (this.selectedRow.cooperationMode === '1') {
        return [
          { title: '公司名称', key: 'companyName', align: 'center', tooltip: true },
          { title: '已充值金额', key: 'rechargeAmount', align: 'center', tooltip: true },
          { title: '预计返利', key: 'expectedReturn', align: 'center', tooltip: true },
          { title: '实际返利', key: 'actualReturn', align: 'center', tooltip: true },
          { title: '返利类型', key: 'returnType', align: 'center', tooltip: true ,
          render: (h, params) => {
            const returnType = params.row.returnType;
            if (returnType == "2") {
              return h('span', '单笔充值');
            } else {
              return h('span', '累计充值');
            }
          }},
        ];
      } else {
        return [
          { title: '公司名称', key: 'companyName', align: 'center', tooltip: true },
          { title: '上期金额', key: 'preAmount', align: 'center', tooltip: true },
          { title: '本期金额', key: 'currAmount', align: 'center', tooltip: true },
          { title: '预计返利', key: 'expectedReturn', align: 'center', tooltip: true },
          { title: '实际返利', key: 'actualReturn', align: 'center', tooltip: true },
        ];
      }
    }
  },
  watch: {
    // 监听弹窗显示状态
    companyVisible: {
      handler (newVal) {
        if (newVal && this.selectedRow.id) {
          this.pageNum = 1; // 重置页码
          this.getTableData();
        }
      },
      immediate: true
    }
  },
  methods: {
    formatDateToClient (dateString) {
      return formatDate(dateString);
    },
    // 获取表格数据
    async getTableData () {
      try {
        const params = {
          mcId: this.selectedRow.id,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        };

        const res = await getCorpDetailsPage(params);
        if (res.code === '0000') {
          let data = res.data;
          //处理返回的数据，若金额返回为null,则显示为0；包含字段：
          if(this.selectedRow.cooperationMode === '1'){
            data.forEach(item => {
              item.rechargeAmount = item.rechargeAmount === null ? 0 : item.rechargeAmount;
              item.expectedReturn = item.expectedReturn === null ? 0 : item.expectedReturn;
            });
          }else{
            data.forEach(item => {
              item.preAmount = item.preAmount === null ? 0 : item.preAmount;
              item.currAmount = item.currAmount === null ? 0 : item.currAmount;
              item.expectedReturn = item.expectedReturn === null ? 0 : item.expectedReturn;
            });
          }

          this.localCompanyList = data || [];
          this.total = parseInt(res.count) || 0;
        } else {
          this.$Message.error(res.message || '获取数据失败');
        }
      } catch (error) {
        console.error('获取表格数据失败:', error);
        this.$Message.error('获取数据失败');
      }
    },

    // 处理分页变化
    handlePageChange (page) {
      this.pageNum = page;
      this.getTableData();
    },

    companyClose () {
      this.$emit('companyClose', false);
      this.pageNum = 1;
    },
    // 修改公司选择变化的处理方法
    handleCompanyChange (value) {
      if (value) {
        const selectedCompany = this.companyOptions.find(item => item.corpId === value);
        if (selectedCompany) {
          // 保存币种代码用于接口传参
          this.currencyCode = selectedCompany.currencyCode;

          // 转换币种显示名称
          switch (selectedCompany.currencyCode) {
            case '156':
              this.companyCurrency = '人民币';
              break;
            case '840':
              this.companyCurrency = '美元';
              break;
            case '344':
              this.companyCurrency = '港币';
              break;
            default:
              this.companyCurrency = '--';
          }
        }
      } else {
        this.companyCurrency = '';
        this.currencyCode = '';
      }
    },
    // 添加获取公司列表的方法
    async getCompanyList() {
      try {
        let data = {
          mcId: this.selectedRow.id,
        }
        const res = await getCorpList(data);
        if (res.code === '0000') {
          this.companyOptions = res.data || [];
        } else {
          this.$Message.error(res.message || '获取公司列表失败');
        }
      } catch (error) {
        console.error('获取公司列表失败:', error);
        this.$Message.error('获取公司列表失败');
      }
    },
    // 修改添加充值记录方法
    addRechargeRecord (row) {
      this.rechargeForm.companyId = row.corpId;
      this.rechargeVisible = true;
      // 打开弹窗时获取公司列表
      this.getCompanyList();
    },
    // 添加充值记录项
    addRechargeItem () {
      this.rechargeForm.rechargeList.push({
        amount: null,
        rechargeTime: new Date()
      });
    },
    // 删除充值记录项
    removeRechargeItem (index) {
      this.rechargeForm.rechargeList.splice(index, 1);
    },
    // 取消充值记录
    handleRechargeCancel () {
      this.rechargeVisible = false;
      this.resetRechargeForm();
    },
    // 提交充值记录
    handleRechargeSubmit () {
      this.$refs.rechargeForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          try {
            // 构造接口参数
            const params = {
              corpId: this.rechargeForm.companyId,
              cooperationMode: this.selectedRow.cooperationMode,
              currencyCode: this.currencyCode,
              campaignId: this.selectedRow.id,
              paymentHistorys: this.rechargeForm.rechargeList.map(item => ({
                rechargeAmount: item.amount,
                settlementTime: formatTime(item.rechargeTime, "Y-M-D h:m:s")
              }))
            };

            const response = await addSettlementLog(params);
            if (response.code === '0000') {
              this.$Message.success('添加充值记录成功');
              this.rechargeVisible = false;
              this.$emit('refresh-table');
              this.resetRechargeForm();
            } else {
              this.$Message.error(response.message || '添加充值记录失败');
            }
          } catch (error) {
            this.$Message.error('添加充值记录失败');
          } finally {
            this.submitLoading = false;
          }
        }
      });
    },
    resetRechargeForm () {
      if (this.$refs.rechargeForm) {
        this.$refs.rechargeForm.resetFields();
      }
      this.rechargeForm = {
        companyId: '',
        rechargeList: [
          {
            amount: null,
            rechargeTime: new Date()
          }
        ]
      };
      this.companyCurrency = '';
      this.currencyCode = '';
    }
  }
};
</script>

<style scoped lang="less">
.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.recharge-item {
  position: relative;
  // margin-bottom: 16px;
}

.recharge-content {
  width: 100%;
}

:deep(.ivu-form-item-required .ivu-form-item-label:before) {
  content: '*';
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 12px;
  color: #ed4014;
}


.page-info {
  margin-top: 16px;
  text-align: center;
  color: #666;
}

.operation-area {
  margin: 16px 0;
  display: flex;
  justify-content: flex-end;
}

.add-recharge-btn {
  padding: 3px 10px;
  font-size: 14px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  margin-bottom: 10px;
}

.add-recharge-btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.add-recharge-btn i {
  margin-right: 4px;
}

:deep(.company-option) {
  width: 340px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.add-button-wrapper{
  text-align: right;
}


</style>
