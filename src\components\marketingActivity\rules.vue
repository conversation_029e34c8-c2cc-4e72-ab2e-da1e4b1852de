<template>
  <Modal :value="rulesVisible" title="详细规则" width="800" @on-cancel="ruleClose" :mask-closable="false" @keydown.enter.native.prevent>
    <Form label-position="right" v-if="loading" :label-width="100">
      <Row>
        <Col span="12">
        <FormItem label="活动名称：" >
          {{ selectedRow.campaignName }}
        </FormItem>
        </Col>
        <Col span="12">
        <FormItem label="适用合作模式：" >
          {{ selectedRow.cooperationMode === '1' ? '代销' : 'A2Z' }}
        </FormItem>
        </Col>
        <Col span="12">
        <FormItem label="活动开始时间：" >
          {{ formatDate(selectedRow.startTime) }}
        </FormItem>
        </Col>
        <Col span="12">
        <FormItem label="活动结束时间：" >
          {{ formatDate(selectedRow.endTime) }}
        </FormItem>
        </Col>
        <template v-if="selectedRow.cooperationMode === '2'">
          <Col span="12">
          <FormItem label="环比开始时间：" >
            {{ formatDate(selectedRow.sequentialStartTime) }}
          </FormItem>
          </Col>
          <Col span="12">
          <FormItem label="环比结束时间：" >
            {{ formatDate(selectedRow.sequentialEndTime) }}
          </FormItem>
          </Col>
        </template>
      </Row>
      <Row>
        <Col span="24">
        <FormItem label="详细规则：" :label-width="100">
          <div class="detail-rule">
            <!-- 代销模式规则显示 -->
            <template v-if="selectedRow.cooperationMode === '1'">
              <div class="rule-content">
                <div class="rule-header">本次活动设定总返利金额为： {{ ruleDetails.totalAmountRefund }}港币</div>
                <div class="rule-list">
                  <div style="font-weight: bold; margin: 10px 0;">具体累计返还策略如下：</div>
                  <div v-for="(rule, index) in cumulativeRules" :key="rule.id" class="rule-row">
                    <div class="rule-left">预存款: {{ rule.amountDeposited }}</div>
                    <div class="rule-right">返还比例: {{ rule.returnRatio }}%</div>
                  </div>
                </div>

                <div v-if="immediateRules.length > 0" class="rule-list" style="margin-top: 20px;">
                  <div style="font-weight: bold; margin: 10px 0;">具体立即返还策略如下：</div>
                  <div class="rule-row">
                    <div class="rule-left">返还金生效期: {{ formatDate(immediateRules[0].effectiveTime) }}</div>
                  </div>
                  <div v-for="(rule, index) in immediateRules" :key="rule.id" class="rule-row">
                    <div class="rule-left">预存款: {{ rule.amountDeposited }}</div>
                    <div class="rule-right">返还比例: {{ rule.returnRatio }}%</div>
                  </div>
                </div>
              </div>
            </template>

            <!-- A2Z模式规则显示 -->
            <template v-else>
              <!-- 样式按照代销模式规则显示 -->
              <div class="rule-content">
                <div class="rule-list">
                  <div>具体返还策略如下：</div>
                  <div v-for="(rule, index) in ruleDetails.campaignRuleList" :key="rule.id" class="rule-row">
                    <div class="rule-left">环比增幅: {{ rule.sequentialGrowthRate }}%</div>
                    <div class="rule-right">返还比例: {{ rule.returnRatio }}%</div>
                  </div>
                </div>
              </div>

            </template>
          </div>
        </FormItem>
        </Col>
      </Row>
    </Form>
    <!-- 增加一个loading -->
    <div v-else class="loading">
      <Spin size="large" fix></Spin>
    </div>
    <div slot="footer" class="dialog-footer">
      <Button @click="ruleClose()">返回</Button>
    </div>
  </Modal>
</template>

<script>
import { formatDate } from "@/libs/tools";

export default {
  props: {
    rulesVisible: {
      type: Boolean,
      default: false
    },
    selectedRow: {
      type: Object,
      default: () => ({})
    },
    ruleDetails: {
      type: Object,
      default: () => ({})
    }
  },
  // 监听ruleDetails
  watch: {
    ruleDetails (newVal) {
      console.log(newVal);
      this.loading = true;
      if (newVal && newVal.campaignRuleList) {
        this.cumulativeRules = newVal.campaignRuleList.filter(
          (rule) => rule.returnType == null || rule.returnType == '1'
        );
        this.immediateRules = newVal.campaignRuleList.filter(
          (rule) => rule.returnType == '2'
        );
      }
    }
  },
  data () {
    return {
      loading: false,
      cumulativeRules: [],
      immediateRules: []
    }
  },
  methods: {
    formatDate (dateString) {
      return formatDate(dateString);
    },
    ruleClose () {
      this.$emit('ruleClose')
    }
  }
};
</script>

<style scoped lang="less">
.detail-rule {
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f8f8f8;
  width: 85%;
}

.rule-content {
  .rule-header {
    margin-bottom: 10px;
    font-weight: bold;
  }

  .rule-list {
    .rule-row {
      display: flex;
      margin-bottom: 8px;

      .rule-left {
        flex: 1;
      }

      .rule-right {
        flex: 1;
      }
    }
  }
}

.rule-item {
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }

  div {
    line-height: 24px;
    color: #333;
    font-size: 14px;
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
