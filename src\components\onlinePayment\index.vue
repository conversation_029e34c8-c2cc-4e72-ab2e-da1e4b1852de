<template>
  <div> <!-- 实付金额 -->
    <div class="total-amount">
      <span>{{ $t('onlineOrder.paidAmount') }}</span>
      <span>{{ formattedTotalAmount }}</span>
    </div>

    <div class="payment-container">
      <div class="payment-component">
        <div class="payment-group payment-group-between" v-if="currencyCode == '156'">
          <div class="payment-option payment-group-between border-box payment-option-left"
            :class="{ activited: selectedPaymentMethod === 'wechat' }" @click="selectPaymentMethod('wechat')">
            <img src="@/assets/images/wechat-pay.png" :alt="$t('onlineOrder.weChatPayChina')" />
            <div class="payment-text">{{ $t('onlineOrder.weChatPayChina') }}</div>
          </div>
          <div class="payment-option payment-group-between border-box"
            :class="{ activited: selectedPaymentMethod === 'alipay' }" @click="selectPaymentMethod('alipay')">
            <img src="@/assets/images/alipay.png" :alt="$t('onlineOrder.alipayChina')" />
            <div class="payment-text">{{ $t('onlineOrder.alipayChina') }}</div>
          </div>
        </div>
        <div class="payment-group payment-option border-box" @click="selectPaymentMethod('card')"
          :class="{ activited: selectedPaymentMethod === 'card' }">
          <div class="payment-flex">
            <div class="payment-option visa-option">
              <img src="@/assets/images/visa.png" alt="VISA" />
            </div>
            <div class="payment-option">
              <img src="@/assets/images/and.png" />
            </div>
            <div class="payment-option card-option">
              <img src="@/assets/images/card.png" :alt="$t('onlineOrder.debitCreditCard')" />
            </div>
          </div>
          <div class="payment-text">{{ $t('onlineOrder.debitCreditCard') }}</div>
        </div>
      </div>
    </div>
    <div id="dropin-container" ref="paymentRef" v-show="selectedPaymentMethod == 'card'"></div>
    <div class="buttons-container">
      <Button type="primary" :loading="payLoading" @click="proceedToNextStep">
        <span v-if="!payLoading">{{ $t('onlineOrder.payBtn') }}</span>
        <span v-else>{{ $t('onlineOrder.loadingStatus') }}</span>
      </Button>
    </div>
  </div>
</template>

<script>
import AdyenCheckout from "@adyen/adyen-web";
import "@adyen/adyen-web/dist/adyen.css";
import { getAdyenParam, queryAdyPayMethods } from "@/api/onlinePay/pay";

export default {
  data () {
    return {
      selectedPaymentMethod: null,
      totalAmount: 0, // 实付金额，单位：分（例如：190表示1.9元）  
      adyenStateData: {
        storePaymentMethod: false, // adyen绑卡
        isValid: false, // 提供信息是否有效
        brand: '', // 卡的类型
        // 下面是method中的一些信息
        encryptedCardNumber: '',
        encryptedExpiryMonth: '',
        encryptedExpiryYear: '',
        encryptedSecurityCode: '',
        holderName: '',
        type: '',
        dropinContainer: null,
        browserInfo: null,
        agreeBrands: null
      },
      payValid: false,
      formObj: {
        depositNumber: null,
      },
      errorMessage: "",
      ruleAddValidate: {
        depositNumber: [
          {
            required: true,
            message: this.$t('onlineOrder.depositAmountGreaterThanZero'),
            type: 'string',
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              const positiveNumberRegex = /^\d+(\.\d{1,2})?$/; // 最多两位小数

              if (!value) {
                callback(new Error(this.$t('onlineOrder.correctDepositAmount')));
              } else if (!positiveNumberRegex.test(value)) {
                callback(new Error(this.$t('onlineOrder.validPositiveNumber')));
              } else {
                const isPositive = parseFloat(value) > 0;
                if (!isPositive) {
                  callback(new Error(this.$t('onlineOrder.positiveNumberGreaterThanZero')));
                } else {
                  callback(); // 验证通过  
                }
              }
            },
            trigger: 'blur'
          }
        ],

      },
    };
  },
  props: {
    currencyCode: {
      type: String,
      default: '',
    },
    amount: {
      type: Number,
      default: 0,
    },
    paySuccessInfo: {
      type: Object,
      default: {},
    },
    payLoading: {
      type: Boolean,
      default: false,
    },
    billId: {
      type: String,
      default: "",
    },
    corpId: {
      type: String,
      default: "",
    },
    orderType: {
      type: String,
      default: "",
    },
  },
  watch: {
    currencyCode (val, oldVal) {
      console.log(val, "---")
      if (val) {
        if (val != '156') {
          this.selectedPaymentMethod = 'card'
          this.pay();
        }
      }
    },

    paySuccessInfo (val, oldVal) {
      if (val.code == "0000") {
        //支付进行中，则初始化页面
      }
      this.payLoading = false
    },
  },
  computed: {
    formattedTotalAmount () {
      const currencySymbolMap = {
        '156': '¥', // CNY
        '344': '$', // HKD
        '840': '$'  // USD 
      };
      const currencyCodeMap = {
        '156': 'CNY',
        '344': 'HKD',
        '840': 'USD'
      };

      const currencySymbol = currencySymbolMap[this.currencyCode] || '$'; // 默认使用$作为货币符号
      const currencyCode = currencyCodeMap[this.currencyCode] || 'USD'; // 默认使用USD作为货币代码
      const formattedAmount = this.formatNumber(this.amount, 2); // 假设你有一个formatNumber函数来格式化数字为两位小数

      return `${currencySymbol}${formattedAmount} ${currencyCode}`;
    },

  },
  methods: {
     // 假设的formatNumber函数，用于将数字格式化为两位小数
     formatNumber (number, decimals) {
      return number.toFixed(decimals).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
    },
    async pay () {
      console.log()
      try {
        let data = {
          billId: this.billId,
          corpId: this.corpId,
          orderType:this.orderType,
          paymentMethod: this.selectedPaymentMethod,
          language: localStorage.getItem("local")
        }
        console.log(data)
        const param = await getAdyenParam(data);
        const { environment, clientKey } = param.data;
        const methods = await queryAdyPayMethods(data);
        const resultMethods = methods.data.result.paymentMethods[0];
        const checkout = await this.createAdyenCheckout(environment, clientKey, resultMethods, methods.data.result);
        console.log('checkout', checkout)
        checkout.create("card").mount('#dropin-container');
      } catch (error) {
        console.error(error);
      }
    },
    async createAdyenCheckout (environment, clientKey, methods, result) {
      let that = this
      const configuration = {
        paymentMethodsResponse: result,
        //clientKey: "test_PRP4NHDGRNDPXMM6IVXJ7JLVF4UYLLDS",
        clientKey: clientKey,
        locale: localStorage.getItem("local"), // 根据需要设置语言
        environment: environment, // Change to 'live' for production
        // showPayButton: false,
        onBrand: (state, dropin) => { // brand 变换时会调用
          var newBrand = state.brand
          that.adyenStateData.brand = newBrand
          console.log("brand status:", state)
        },
        paymentMethodsConfiguration: {
          card: {
            // ...methods,
            showPayButton: false,
            hasHolderName: true,
            // name: methods.name,
            hideCVC: false,
            exposeExpiryDate: false,
            styles: {
              base: {
                color: '#001b2b', // 内容变更时字体颜色
                // color: "#33ffcc",
                fontSize: '16px',
                fontWeight: '400'
              },
              placeholder: {
                color: '#90a2bd', // 默认背景案例字体颜色
                fontWeight: '200'
              },
              error: {
                color: '#001b2b'
              },
              validated: {
                color: '#000'// 校验通过时字体颜色
              }
            },
            onBrand: (state, dropin) => { // brand 变换时会调用
              var newBrand = state.brand
              that.adyenStateData.brand = newBrand
              console.log(state)
            },
          },
        },
        onChange: (state, component) => {
          console.log("state.isValid:", state.isValid)
          console.log("state.data.paymentMethod:", state.data.paymentMethod)
          this.adyenStateData.isValid = state.isValid
          that.adyenStateData.type = state.data.paymentMethod.type
          that.adyenStateData.encryptedCardNumber = state.data.paymentMethod.encryptedCardNumber
          that.adyenStateData.encryptedExpiryMonth = state.data.paymentMethod.encryptedExpiryMonth
          that.adyenStateData.encryptedExpiryYear = state.data.paymentMethod.encryptedExpiryYear
          that.adyenStateData.encryptedSecurityCode = state.data.paymentMethod.encryptedSecurityCode
          that.adyenStateData.holderName = state.data.paymentMethod.holderName
          that.adyenStateData.storePaymentMethod = state.data.storePaymentMethod
          that.adyenStateData.browserInfo = state.data.browserInfo
          if (this.isCardRequiringExpiryAndCvc(this.adyenStateData.type)) {
            console.log("需要校验日期和安全码")
          } else {
            // 如果不需要，则默认通过校验  
            state.isValid = true; // 或者根据其他条件来判断  
          }
          component
        },
        onError: (error, component) => {
          console.error(error.name, error.message, error.stack, component);
        },
      };

      return new AdyenCheckout(configuration);
    },
    selectPaymentMethod (method) {
      // 选择相同方式则返回
      if (this.selectedPaymentMethod === method) {
        return;
      }
      this.selectedPaymentMethod = method;
      if (this.selectedPaymentMethod === 'card') {
        this.pay();
      } else {
        // 处理其他支付方式的逻辑，例如跳转到第三方支付页面  
        console.log('Selected payment method:', this.selectedPaymentMethod);
      }
    },
    goBack () {
      this.selectedPaymentMethod = "";
    },
    proceedToNextStep () {
      // 校验支付信息  
      this.validatePayment();

      if (this.payValid) {
        let payload = {};
        if (this.selectedPaymentMethod === 'card') {
          payload.adyenStateData = this.adyenStateData;
        }
        payload.paymentMethod = this.selectedPaymentMethod;
        // this.payLoading = true
        this.$emit('onlinePay', payload);
      }
    },
    // 校验支付信息  
    validatePayment () {
      let isValid = true;

      // 校验金额  
      // if (this.showDeposit) {
      //   this.$refs.formObj.validate((valid) => {
      //     if (!valid) {
      //       isValid = false;
      //       this.$Notice.error({ title: this.$t('onlineOrder.correctDepositAmount') });
      //     } else if (this.formObj.depositNumber <= 0) {
      //       isValid = false;
      //       this.$Notice.error({ title: this.$t('onlineOrder.depositAmountGreaterThanZero') });
      //     }
      //   });

      //   // 如果金额校验失败，则不需要继续校验其他信息  
      //   if (!isValid) {
      //     this.payValid = false;
      //     return;
      //   }
      // }

      // 校验支付方式  
      if (!this.selectedPaymentMethod) {
        isValid = false;
        this.$Notice.error({ title: this.$t('onlineOrder.selectPaymentMethod') });
      }

      // 校验银行卡信息（如果选择了银行卡支付）  
      if (this.selectedPaymentMethod === 'card') {
        // console.log(this.adyenStateData.isValid)
        if (!this.adyenStateData.isValid) {
          isValid = false;
          // 这里可以根据需要显示更具体的错误信息，例如哪个字段填写不正确  
          this.$Notice.error({ title: this.$t('onlineOrder.correctBankCardInfo') });
        }
      }

      // 最终设置 payValid  
      this.payValid = isValid;
    },
    // 模拟判断银行卡是否需要到期日和安全码的函数  
    isCardRequiringExpiryAndCvc (cardType) {
      // 这里应该根据实际的Adyen支付方法配置来判断  
      // 例如，某些存储卡可能不需要到期日和安全码  
      // 这里只是返回一个模拟值  
      return cardType !== "storedCardType"; // 假设storedCardType是不需要到期日和安全码的存储卡类型  
    },
  },
  mounted () {
  },
};
</script>

<style scoped>
/* 添加必要的样式 */
.total-amount {
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
}

#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}

.payment-container {
  /* max-width: 400px;   */
  margin: 0 auto;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background-color: #fff;
}

.payment-component {
  text-align: center;
}

.payment-flex {
  display: flex;
  justify-content: center;
}

.payment-group {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  width: 100%;
  /* 或者其他具体的宽度值 */
}

.payment-group-between {
  justify-content: space-between;
}

.payment-option-left {
  margin-right: 10px;
}

.payment-option {
  display: flex;
  /* flex-direction: column;   */
  align-items: center;
  cursor: pointer;
  padding: 10px 10px 10px 10px;
  /* border-radius: 8px; */
  transition: background-color 0.3s;
  box-sizing: border-box;
  flex: 1;
  /* 让子元素平分父元素的宽度 */
}

.payment-option:hover {
  background-color: #f0f0f0;
}

.payment-option img {
  width: 50px;
  height: 50px;
}

.card-option img {
  width: 60px;
}

.payment-text {
  font-size: 14px;
  color: #333;
}

.visa-option {
  justify-content: center;
}

.card-option {
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

.buttons-container {
  display: flex;
  justify-content: center;
  gap: 10px;
}

button {
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: #fff;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #0056b3;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.border-box {
  border: 1px solid #ccc;
}

.activited {
  background-color: #f0f0f0;
}

#dropin-container {
  margin-bottom: 20px;
}

.payment-container .el-form-item {
  margin-bottom: 20px;
}

.payment-container .el-input__inner {
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  padding: 10px;
  font-size: 14px;
  box-sizing: border-box;
}

.payment-container .el-input__inner:focus {
  border-color: #409EFF;
  outline: 0;
  box-shadow: 0 0 2px rgba(64, 158, 255, 0.2);
}
</style>
