<template>
	<!-- 新建/编辑套餐-->
	<div>
		<Modal v-model="modal" :title="title"  @on-cancel="cancelModal" :mask-closable="false" width="750px">
			<Form ref="formObj" :model="formObj" :label-width="150" :rules="ruleAddValidate">
				<FormItem :label="$t('deposit.mealname')" prop="nameCn">
					  <Input v-model="formObj.nameCn"  type="textarea" :rows="5"
					  :maxlength="500" :readonly="typeFlag=='info'"
					  :clearable="true" :placeholder="$t('support.fiveCharacters')" style="width: 350px;"/>
				  </FormItem>
				<FormItem :label="$t('support.packageDescription') + ':'" prop="descCn">
					<Input v-model="formObj.descCn"  type="textarea"
					   :rows="5" :maxlength="4000" :readonly="typeFlag=='info'"
					 :clearable="true" :placeholder="$t('support.fourtCharacters')" style="width: 350px;"/>
				 </FormItem>
				<FormItem :label="$t('support.Periodtype') + ':'" prop="periodUnit">
					<Select v-model="formObj.periodUnit" filterable
					    :disabled="typeFlag=='info'"
					    style="width: 350px;"
						:placeholder="$t('support.selectType')"
						:clearable="true">
						<Option v-for="item in periodUnitList" :value="item.value" :key="item.value">{{ item.label }}</Option>
					</Select>
				</FormItem>
				<FormItem :label="$t('support.Continuouscycle') + ':'" prop="keepPeriod">
						<Input v-model="formObj.keepPeriod" :maxlength="11" :readonly="typeFlag=='info'"
						 :placeholder="$t('support.selectDuration')" style="width: 350px;"/>
				 </FormItem>
				<FormItem :label="$t('support.packageValidity') + ':'" prop="effectiveDay">
						<Input v-model="formObj.effectiveDay" :maxlength="11" :readonly="typeFlag=='info'"
						 :placeholder="$t('support.inputPackageValidity')" style="width: 350px;">
						  <span slot="append">{{$t('support.day')}}</span>
						</Input>
				 </FormItem>
				<FormItem :label="$t('support.DataRestrictionType') + ':'" prop="flowLimitType">
						<Select v-model="formObj.flowLimitType" filterable
						    :disabled="typeFlag=='info' || typeFlag=='update'"
						    style="width: 350px;"
							:placeholder="$t('support.selectDataResetType')"
							@on-change="getcontrolLogic"
							:clearable="true">
							<Option :value="1">{{$t('support.DataRestrictionCycle')}}</Option>
							<Option :value="2">{{$t('support.DataRestrictionSingle')}}</Option>
						</Select>
				 </FormItem>
				<FormItem :label="$t('flow.Controllogic') + ':'" prop="controlLogic">
						<Select v-model="formObj.controlLogic" filterable
						    :disabled="typeFlag=='info' || typeFlag=='update'"
						    style="width: 350px;"
							:placeholder="$t('support.selectRestrictionLogic')"
							>
							<Option v-if="formObj.flowLimitType===1||formObj.flowLimitType===2 ||!formObj.flowLimitType" :value="1">{{$t('support.RestrictedSpeedLimit')}}</Option>
							<Option v-if="formObj.flowLimitType===1|| !formObj.flowLimitType" :value="2">{{$t('support.ReleaseAfterLimit')}}</Option>
						</Select>
				 </FormItem>
				<FormItem :label="$t('support.aupportHotspot') + ':'" prop="isSupportedHotspots">
						<Select v-model="formObj.isSupportedHotspots" filterable
						    :disabled="typeFlag=='info'|| typeFlag=='update'"
						    style="width: 350px;"
							:placeholder="$t('support.isAupportHotspot')"
							@on-change="getcountryList(formObj.isSupportedHotspots)"
							>
							<Option :value="1">{{$t('order.yes')}}</Option>
							<Option :value="2">{{$t('order.no')}}</Option>
						</Select>
				 </FormItem>
				 <FormItem :label="$t('flow.SelectDestination') + ':'" prop="mccList" >
						<Select v-model="formObj.mccList" filterable multiple
						    :disabled="true"
						    style="width: 350px;"
								 v-defaultSelect="[formObj.mccList]"
							:placeholder="$t('buymeal.selectCountry')"
							:clearable="true"
							>
							<Option v-for="value in countryList" :value="value.countryId" :key="value.countryId">{{ value.countryName }}</Option>
						</Select>
						<Button v-show="typeFlag!='info'" style="margin-left: 10px" type="primary"  @click="showCountry">{{$t('country.editCountry')}}</Button>
				 </FormItem>
				<div v-for="(item,index) in formObj.combinationList"  :key="index">
					<FormItem :label="$t('support.usage2') + ':'"
					    :prop="'combinationList.' + index+ '.consumption'"
					    :rules="ruleAddValidate.consumption" style="margin-bottom: 30px;">
							<Input v-model="item.consumption" v-show="typeFlag!='info'"
							   :maxlength="18" :readonly="typeFlag=='info'"
							   :clearable="true" :placeholder="$t('support.inputUsage')" style="width: 350px;">
								<Select v-model="item.unit" slot="append" style="width: 70px" :disabled="typeFlag=='info'">
									<Option value="MB">MB</Option>
									<Option value="GB">GB</Option>
									<Option value="TB">TB</Option>
								</Select>
							</Input>
							<Input v-model="item.displayConsumption"
							v-show="typeFlag=='info'"
							:disabled="typeFlag=='info'"
							style="width: 350px;"/>
					</FormItem>
					<FormItem :label="$t('support.speedTemplate') + ':'"
					   :prop="'combinationList.' + index+ '.upccTemplateId'"
					   :rules="[{required: true, message: $t('support.selectSpeedTemplate')}]"
					>
							<Select v-model="item.upccTemplateId"
							    v-show="typeFlag!='info'"
							    filterable
							    :disabled="typeFlag=='info'"
							    style="width: 350px;"
								:placeholder="$t('support.selectSpeedTemplate')"
								:clearable="true">
								<Option v-for="(item,index) in upccTemplateList" :value="item.templateId" :key="index" :title="item.templateDesc">{{item.templateName.length > 35 ? item.templateName.substring(0,35) + "…" : item.templateName}}
								</Option>
							</Select>
							<Input v-model="item.templateName"
							v-show="typeFlag=='info'"
							:disabled="typeFlag=='info'"
							style="width: 350px;"/>

							<Button v-show="typeFlag!='info' && ((formObj.combinationList.length===1 && index===0)
							||(formObj.combinationList.length>1 && index===(formObj.combinationList.length-1)))"
							type="success" ghost style="margin-left: 10px;" @click="addItem()">{{$t('support.add')}}</Button>
							<Button  v-show="typeFlag!='info' && formObj.combinationList.length>1"
							type="error"   ghost style="margin-left: 10px;" @click="delItem(index)">{{$t('address.Delete')}}</Button>
					</FormItem>
				</div>
				<FormItem :label="$t('support.unlimitedUsageTemplate') + ':'" prop="templateName">
						<Select v-model="formObj.templateName" filterable
						    :disabled="typeFlag=='info'"
						    style="width: 350px;"
							:placeholder="$t('support.selectUnlimitedUsageTemplate')"
							:clearable="true">
							<Option v-for="(item,index) in upccTemplateList" :value="item.templateId" :key="index" :title="item.templateDesc">{{item.templateName.length > 35 ? item.templateName.substring(0,35) + "…" : item.templateName}}
							</Option>
						</Select>
				 </FormItem>
				<FormItem :label="$t('support.supportAddon') + ':'" prop="hasRefuelPackage">
				 	<i-switch v-model="formObj.hasRefuelPackage" size="large" :disabled="typeFlag=='info'" @on-change="fuelPackaChange">
				 		<span slot="open">{{$t('order.yes')}}</span>
				 		<span slot="close">{{$t('order.no')}}</span>
				 	</i-switch>
				</FormItem>
				<FormItem :label="$t('support.bindAddon')" prop="selectionTypes" v-if="formObj.hasRefuelPackage">
				 	<Button type="dashed" class="inputSty" style="width: 350px;" @click="RefuelPackageList">{{$t('support.AddonList')}}</Button>
				</FormItem>
				<Row >
					<Col v-if="appInfos.length != 0" span="24">
					<FormItem :label="$t('directionalApp.supportUsage') + ':'" prop="isSupportDirect">
						<Select v-model="formObj.isSupportDirect" class="inputSty" :placeholder="$t('directionalApp.selectSupportUsage')" :disabled="typeFlag=='info' || notClick == true"
						 :clearable="typeFlag!='info'" @on-change="changeDirect($event)" style="width: 350px;">
							<Option value="1">{{$t('order.yes')}}</Option>
							<Option value="2">{{$t('order.no')}}</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<div v-for="(fitem, findex) in formObj.directAppInfos" :key="findex">
					<Row v-if="formObj.isSupportDirect == '1'" type="flex" justify="start" align="middle">
						<Col span="24">
						<FormItem :label="$t('directionalApp.selectAPP') + ':'" :prop="'directAppInfos.' + findex + '.appId'" :rules="ruleAddValidate.appId">
							<Select v-model="fitem.appId" multiple filterable :placeholder="$t('directionalApp.pleaseSelectAPP')" :disabled="typeFlag=='info' || notClick == true"
								clearable @on-change="changeAppId($event,findex)" style="width: 350px;">
								<Option v-for="(item2,index2) in choseAppInfos(fitem.appId)" :value="item2.id" :key="item2.id" :disabled="!fitem.appId.includes(item2.id) && appTotal == true">{{ item2.appName }}</Option>
							</Select>
							<Button :disabled="typeFlag=='info' || notClick == true" v-if="findex != 0" size="small" type="error" style="margin-left: 20px; width: 80px;" @click="deleteApp(findex)">{{$t('directionalApp.deleteAPP')}}</Button>
							<Button :disabled="typeFlag=='info' || notClick == true || appTotal == true" v-if="findex == (formObj.directAppInfos.length-1)" size="small" type="primary" style="margin-left: 20px; width: 80px;" @click="addApp()">{{$t('directionalApp.addAPP')}}</Button>
						</FormItem>
						</Col>
					</Row>
					<Row v-if="formObj.isSupportDirect == '1'">
						<Col span="24">
						<FormItem :label="$t('directionalApp.specificAPPLogic') + ':'" :prop="'directAppInfos.' + findex + '.directType'" :rules="ruleAddValidate.directType">
							<RadioGroup v-model="fitem.directType" @on-change="changeLogic(findex)">
								<Radio label="1" :disabled="typeFlag=='info' || notClick == true">{{$t('flow.Restricted')}}</Radio>
								<Radio label="2" :disabled="typeFlag=='info' || notClick == true">{{$t('directionalApp.freeFlow')}}</Radio>
							</RadioGroup>
						</FormItem>
						</Col>
					</Row>
					<div v-if="formObj.isSupportDirect == '1' && fitem.directType == '2'" v-for="(item1, index1) in fitem.appConsumption" :key="index1">
							<Row style="margin-bottom: 10px;">
								<Col span="16">
								<FormItem :label="$t('directionalApp.dataValue') + ':'" :prop="'directAppInfos.' + findex + '.appConsumption.' + index1 + '.consumption'" :rules="ruleAddValidate.consumption1">
									<Input v-model.trim="item1.consumption" type="number" :readonly="typeFlag=='info'" :clearable="typeFlag!='info'" :disabled="notClick == true"
									:placeholder="$t('directionalApp.inputDataValue')" style="width: 320px;"><span slot="append">MB</span></Input>
								</FormItem>
								</Col>
								<Col span="4" style="padding: 6px 0 0 10px;" v-if="index1 != 0">
									<Button size="small" type="error" ghost @click="delFlowValue(findex,index1)" :disabled="typeFlag=='info' || notClick == true">{{$t('directionalApp.deleteDataValue')}}</Button>
								</Col>
								<Col span="4" style="padding: 6px 0 0 10px;" v-if="index1 == (fitem.appConsumption.length - 1)">
									<Button size="small" type="info" ghost @click="addFlowValue(findex)" :disabled="typeFlag=='info' || notClick == true">{{$t('directionalApp.addDataValue')}}</Button>
								</Col>
							</Row>
							<Row type="flex" justify="start" align="middle">
								<Col span="24">
								<FormItem :label="$t('directionalApp.selectUPCCTemplate')" :prop="'directAppInfos.' + findex + '.appConsumption.' + index1 + '.upccTemplateId'"
								:rules="ruleAddValidate.upccTemplateId1">
								<div v-for="(uitem, uindex) in fitem.appId"  style="display: flex; justify-content: flex-statrt; align-items: flex-start;">
										<Select v-model="item1.upccTemplateId[uindex]" filterable :placeholder="$t('directionalApp.pleaseSelectUPCCTemplate')" style="margin-bottom: 20px;width: 350px;"
									:clearable="typeFlag!='info'" :key="uitem" :disabled="typeFlag=='info' || notClick == true">
											<Option v-for="bitem in directTemplateList[uitem]" :value="bitem.upccTemplateId" :key="bitem.upccTemplateId" :title="bitem.templateName">{{bitem.templateName}}</Option>
										</Select>
									</div>
								</FormItem>
								</Col>
							</Row>
						</div>
					<Row v-if="formObj.isSupportDirect == '1' && fitem.directType == '2'">
						<Col span="12">
						<FormItem :label="$t('directionalApp.continueDataUsage') + ':'" :prop="'directAppInfos.' + findex + '.isUsePackage'" :rules="ruleAddValidate.isUsePackage"
						  >
							<Select v-model="fitem.isUsePackage" :placeholder="$t('directionalApp.PleaseDataUsage')" :disabled="typeFlag=='info' || notClick == true"
							:clearable="typeFlag!='info'" @on-change="changeUsePackage($event)" style="width: 350px;">
								<Option value="1">{{$t('order.yes')}}</Option>
								<Option value="2">{{$t('order.no')}}</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
					<Row v-if="formObj.isSupportDirect == '1'">
						<Col span="12" v-if="fitem.directType == '1'">
						<FormItem :label="$t('directionalApp.restrictedTemplate') + ':'" :prop="'directAppInfos.' + findex + '.noLimitTemplateId'" :rules="ruleAddValidate.noLimitTemplateId">
							<div v-for="(ditem, dindex) in fitem.appId">
								<Select v-model="fitem.noLimitTemplateId[dindex]" filterable :placeholder="$t('directionalApp.pleasepRrestrictedTemplate')" :disabled="typeFlag=='info' || notClick == true"
									:clearable="typeFlag!='info'" style="margin-bottom: 20px; width: 350px;">
									<Option v-for="item3 in directTemplateList[ditem]" :value="item3.upccTemplateId" :key="item3.upccTemplateId">{{ item3.templateName }}</Option>
								</Select>
							</div>
						</FormItem>
						</Col>
						<Col span="12" v-if="fitem.isUsePackage && fitem.directType == '2'">
							<FormItem :label="fitem.isUsePackage == '2' ? $t('directionalApp.FreeTemplate') : $t('directionalApp.FreeContinueTemplate')" :prop="'directAppInfos.' + findex + '.noLimitTemplateId'"
							 :rules="ruleAddValidate.noLimitTemplateId">
								<div v-for="(ditem, dindex) in fitem.appId">
									<Select v-model="fitem.noLimitTemplateId[dindex]" :placeholder="fitem.isUsePackage == '2' ? $t('directionalApp.pleaseFree') : $t('directionalApp.pleaseFreeContinue')"
									 :disabled="typeFlag=='info' || notClick == true" :clearable="typeFlag!='info'" style="margin-bottom: 20px; width: 350px;">
										<Option v-for="item5 in directTemplateList[ditem]" :value="item5.upccTemplateId" :key="item5.upccTemplateId">{{ item5.templateName }}</Option>
									</Select>
								</div>
							</FormItem>
						</Col>
					</Row>
					<div style="margin-bottom: 30px;"></div>
				</div>

			</Form>
			<div slot="footer" class="footer_wrap">
			  <Button @click="cancelModal">{{$t('common.cancel')}}</Button>
			  <Button v-show="typeFlag!='info'" type="primary" @click="submit" :loading="submitloading">{{$t('common.determine')}}</Button>
			</div>
		</Modal>
		<!-- 加油包列表弹窗 -->
		<Modal :title="$t('support.newAddon')"  v-model="addRefuelModel"  @on-cancel="cancelBagModal" :mask-closable="false"
		 width="700px">
			<Form v-if="this.typeFlag!=='info'" ref="searchObj" :model="searchObj" :label-width="80" inline style="font-weight:bold;display: flex;justify-content: flex-start; flex-wrap: wrap;">
				<FormItem :label="$t('support.AddonName')">
					<Input type="text" v-model.trim="searchObj.gaspackname" clearable :placeholder="$t('support.inputAddonName')" style="width: 205px;"></Input>
				</FormItem>
				<FormItem :label="$t('support.AddonID')">
					<Input type="text" v-model.trim="searchObj.gaspacknameid" clearable :placeholder="$t('support.inputAddonId')" style="width: 205px;"></Input>
				</FormItem>
				<div>
					<Button type="primary" icon="md-search" :loading="searchObjloading" @click="search">{{$t('address.search')}}</Button>
				</div>
			</Form>
			<Table :columns="Unitedcolumns" :data="Uniteddata" style="width:100%;margin-top: 20px;"
			 @on-selection-change="handleRowChange"
			 @on-select-cancel="cancelPackage"
			 @on-select-all-cancel="cancelPackageAll"
			 :loading="Unitedloading">
			</Table>
			<div style="margin-top:15px">
				<Page :total="Unitedtotal" :current.sync="UnitedcurrentPage" show-total show-elevator @on-change="UnitedgoPage" />
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button style="margin-left: 8px" @click="cancelBagModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" v-if="this.typeFlag!=='info'" @click="Confirm">{{$t('common.determine')}}</Button>
			</div>
		</Modal>

    <Modal :title="$t('flow.SelectDestination')"  v-model="countryShowType"  @on-cancel="canceCountry" :mask-closable="false"
           width="1700px" :styles="{top: '45px'}">
      <AddCountry ref="addCountry" :title="title"
                  :typeFlag="typeFlag" :corpId="corpId" :supportedHotspots="formObj.isSupportedHotspots"></AddCountry>
      <div slot="footer" style="width: 100%; display: flex;align-items: center; justify-content: center;">
        <Button style="margin-left: 8px" @click="canceCountry">{{$t('common.cancel')}}</Button>
        <Button type="primary" v-if="this.typeFlag!='info'" @click="ConfirmCountry">{{$t('common.determine')}}</Button>
      </div>
    </Modal>
	</div>
</template>

<script>
  import AddCountry from'../country/addCountry.vue'
	import {opsearchAll} from '@/api/operators';
	import{
    getRefuelList,
    getDetailsRefuelList,
    packageGetDirectional,
    deatilGetDirect,
    getPackageSale,
		getSelfPackageFlowInfoNew,
    isSupportGetMccList,
  } from '@/api/package/package.js'
	import {
		addItem,
		updateItem,
		getUpccList,
	} from '@/api/channel/package.js'
	import{
    getCardPoolMccList,
  } from'@/api/associationGroup/cardPoolMccGroup.js'
	export default{
    components:{
      AddCountry
    },
			props: {
		  title: {
		    type: String,
		    default: "",
		  },
		  typeFlag:{
			type: String,
			default: "",
		  },
		  corpId:{
			type: String,
			default: "",
		  },
		},
		data(){
			const integerNum = (rule, value, callback) => {
				var str = /^[0-9]\d*$/;
				return str.test(value);
			};
			// upcc模板
			const validateUpcc = (rule, value, callback) => {
				let matches = rule.field.match(/\d+/g); //使用正则表达式匹配所有数字
				let directIndex = matches[0]; //得到对应的应用组
				let appIndex = matches[1]; //得到对应的应用组
				let upcc = this.formObj.directAppInfos[directIndex].appConsumption[appIndex].upccTemplateId;
				let appIds = this.formObj.directAppInfos[directIndex].appId;
				//判断用户点击upcc后删除产生的undefined或者null
				let filteredArr = upcc.filter(item => item !== undefined && item !== null && item !== '');
				let isValid = upcc.every(item => item !== null && item !== '' && item !== undefined);
				if (upcc.length == 0) {
					callback(new Error(rule.message));
				} else if (upcc.length < appIds.length) {
					callback(new Error(rule.message));
				} else if (!isValid) {
					callback(new Error(rule.message));
				} else if (upcc.length != filteredArr.length) {
					callback(new Error(rule.message));
				} else {
					callback();
				}
			};
			// 定向限速模板/定向免流限速模板/定向免流继续使用模板
			const validateSpeedLimit = (rule, value, callback) => {
				let matches = rule.field.match(/\d+/g); //使用正则表达式匹配所有数字
				let directIndex = matches[0]; //得到对应的应用组的定向限速模板
				let speedLimit = this.formObj.directAppInfos[directIndex].noLimitTemplateId;
				let appIds = this.formObj.directAppInfos[directIndex].appId;
				//判断用户点击定向限速模板后删除产生的undefined或者null
				let filteredArr = speedLimit.filter(item => item !== undefined && item !== null && item !== '');
				let isValid = filteredArr.every(item => item !== null && item !== '' && item !== undefined);

				let length = appIds.length
				let noLimitTemplateIds = speedLimit.slice(0, length)
				let filteredArrs = noLimitTemplateIds.filter(item => item !== undefined && item !== null && item !== '');
				//判断定向限速模板/定向免流限速模板/定向免流继续使用模板
				let templateType = this.formObj.directAppInfos[directIndex].directType
				let isUsePackage = this.formObj.directAppInfos[directIndex].isUsePackage
				let message;
				if (templateType == '1') {
					message = this.$t('directionalApp.dingTemMandatory')
				} else {
					if (isUsePackage == '1') {
						message = this.$t('directionalApp.useTemMandatory')
					} else {
						message = this.$t('directionalApp.freeTemMandatory')
					}
				}
				if (speedLimit.length == 0) {
					callback(new Error(message));
				} else if (speedLimit.length < appIds.length) {
					callback(new Error(message));
				} else if (!isValid) {
					callback(new Error(message));
				} else if (noLimitTemplateIds.length != filteredArrs.length) {
					callback(new Error(message));
				} else {
					callback();
				}
			};
			// 流量值不可以重复
			const validateRepeat = (rule, value, callback) => {
				let matches = rule.field.match(/\d+/g); //使用正则表达式匹配所有数字
				let directIndex = matches[0]; //得到对应的应用组
				let consumptionList = [];
				if (this.formObj.directAppInfos[directIndex].appConsumption.length > 1) {
					this.formObj.directAppInfos[directIndex].appConsumption.forEach((item,index) => {
						consumptionList.push(item.consumption)
					})
					consumptionList = consumptionList.map(String);
					let set = new Set();
					let duplicates = [];
					for (let i = 0; i < consumptionList.length; i++) {
						if (!set.has(consumptionList[i])) {
							set.add(consumptionList[i]);
						} else {
							duplicates.push(consumptionList[i]);
						}
					}
					if (duplicates.includes(value)) {
						callback(new Error(rule.message));
					} else {
						callback();
					}
				} else {
					callback();
				}
			};
			// 流量值要比前面的流量值大
			const validateLarger = (rule, value, callback) => {
				let matches = rule.field.match(/\d+/g); //使用正则表达式匹配所有数字
				let directIndex = matches[0]; //得到对应的应用组
				let cIndex = matches[1]; //得到对应的流量值下标
				let arrList = this.formObj.directAppInfos[directIndex].appConsumption[cIndex].consumption
				if (this.formObj.directAppInfos[directIndex].appConsumption.length > 1) {
					let arrList1 =  (cIndex > 0) ? this.formObj.directAppInfos[directIndex].appConsumption[cIndex-1].consumption : null
					if (arrList1 && (Number(arrList) < Number(arrList1))) {
						callback(new Error(rule.message));
					} else {
						callback();
					}
				} else {
					callback();
				}
			};
			// 用量值要比前面的用量值大 并且唯一
			const validateConsumption = (rule, value, callback) => {
				// 自动去除前后空格
				if (typeof value === 'string') {
					value = value.trim();
				}
				// 1. 必填校验 (虽然ruleAddValidate有required，但这里也作为安全检查)
				if (!value) {
					return callback(new Error(this.$t('support.inputUsage')));
				}

				// 2. 整数格式校验 (1-10位正整数，不能以0开头)
				const reg = /^[1-9]\d{0,9}$/;
				if (!reg.test(value)) {
					return callback(new Error(this.$t('support.wrongUsageFormat')));
				}

				// 3. 单位换算成MB后的最大值校验（不能超过10位数）
				const matches = rule.field.match(/combinationList\.(\d+)\.consumption/); // 只提取数字索引
				const index = parseInt(matches[1], 10);
				const combinationList = this.formObj.combinationList;
				const currentItem = combinationList[index];
				const currentUnit = currentItem.unit;
				// 用BigInt防止大数精度丢失
				let mbValue;
				if (currentUnit === 'MB') {
					mbValue = BigInt(value);
				} else if (currentUnit === 'GB') {
					mbValue = BigInt(value) * 1024n;
				} else if (currentUnit === 'TB') {
					mbValue = BigInt(value) * 1024n * 1024n;
				} else {
					mbValue = BigInt(value);
				}
				const maxMB = 9999999999n;
				let maxValueStr = '';
				if (currentUnit === 'MB') {
					maxValueStr = '9999999999MB';
				} else if (currentUnit === 'GB') {
					maxValueStr = Math.floor(Number(maxMB / 1024n)) + 'GB';
				} else if (currentUnit === 'TB') {
					maxValueStr = Math.floor(Number(maxMB / 1024n / 1024n)) + 'TB';
				} else {
					maxValueStr = '9999999999MB';
				}
				if (mbValue > maxMB) {
					return callback(new Error(this.$t('sessionInfo.UsageMax') + maxValueStr));
				}
				if (mbValue < 10n) {
					return callback(new Error(this.$t('sessionInfo.UsageMin')));
				}

				// 4. 检查递增性
				const preIndex = index - 1;
				if (preIndex >= 0) {
					const prevItem = combinationList[preIndex];
					const prevConsumptionValue = Number(prevItem.consumption);
					const prevUnit = prevItem.unit;
					const prevConvertedValue = this.convertToMB(prevConsumptionValue, prevUnit);

					if (mbValue <= prevConvertedValue) {
						callback(new Error(this.$t('directionalApp.usageRule'))); // 使用原始的"usageRule"消息
						return;
					}
				}
				callback(); // 所有校验通过
			};
			return{
        language:localStorage.getItem("local"),
        selectCountryData:[],
        countryShowType:false,
				Unitedtotal: 0,
				UnitedcurrentPage: 1,
				Unitedpage: 0,
				modal:false,
				submitloading:false,//提交加载
				searchObjloading: false,
				Unitedloading: false,
				addRefuelModel: false, //加油包弹窗标识
				notClick: false,
				appTotal: false,
				initialized: true, // 添加一个标志位
				selectedValues: [], // 当前选中的upcc值
				oldValue: [], // 上一次选中的upcc值
				countryList:[],//国家地区列表
				upccTemplateList:[],//模板列表
				Uniteddata: [], //加油包列表
				refuelIDList: [],//加油包初始数据
				refuelIDLists: [],//加油包列表确定后的数据
				formObj:{
					nameCn:'',//套餐名称
					descCn:'',//套餐描述
					periodUnit:'',//周期类型
					keepPeriod:'',//持续周期
					effectiveDay:'',//套餐有效期
					flowLimitType:'',//流量限制类型
					controlLogic:'',//控制逻辑
					isSupportedHotspots:'',//是否支持热点
					mccList:'',//国家/地区
					templateName:'',//无上限模板
					combinationList:[{
						consumption:'',//用量值
						unit: 'MB', // 新增单位字段，默认MB
						upccTemplateId:'',//速度模板
					}],//用量值与速度模板组合列表
					hasRefuelPackage: false,//是否支持加油包
					selectionTypes: [], //多选类型
					isSupportDirect: '',//是否支持定向应用
					directAppInfos:[
						{
							index: 1,
							appId: [],//应用多选框选中的值
							directType: '',//定向使用逻辑
							appConsumption: [{
								index1: 1,
								consumption: '',//流量值
								upccTemplateId: [],//选择UPCC模板值
							}],
							isUsePackage: '',//是否继续使用通用模板
							noLimitTemplateId: [],//定向限速板/定向免流限速模版/定向免流继续使用模板
						}
					],
				},
				index: 1,
				index1: 1,
				upccIndex: '',
				upccChange: '',
				appInfos: [],//应用列表
				directTemplateList: {}, //定向模板列表
				selectedOptions: [],
				submitList: [],
				searchObj: {
					gaspackname: '',
					gaspacknameid: '',
				},
				periodUnitList: [{
						value: '1',
						label: this.$t('buymeal.hour'),
					},
          {
          	value: '2',
          	label: this.$t('buymeal.day'),
          },
          {
          	value: '3',
          	label: this.$t('buymeal.month'),
          },
          {
          	value: '4',
          	label: this.$t('buymeal.year'),
          }
				],
				unitList: [{
						value: 'MB',
						label: 'MB',
					},
					{
						value: 'GB',
						label: 'GB',
					},
					{
						value: 'TB',
						label: 'TB',
					}
				],
				Unitedcolumns: [{
					type: 'selection',
					width: 60,
					align: 'center'
				}, {
					title: this.$t('support.AddonID'),
					key: 'id',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				},
				{
					title: this.$t('support.AddonName'),
					key: 'nameCn',
					minWidth: 180,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row;
						const text = this.$i18n.locale==='zh-CN' ? row.nameCn : this.$i18n.locale==='en-US' ? row.nameEn: ''
						return h('label', text)
					}
				}, {
					title: this.$t('support.AddonAmount') + '(MB)',
					key: 'flowValue',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}],
				ruleAddValidate:{
					nameCn: [{required: true,message: this.$t('support.packageNameNotNull') }],
					descCn:[{required: true,message: this.$t('support.packageDescriptionNotNull') }],
					periodUnit:[{required: true,message: this.$t('support.selectType'),}],
					keepPeriod:[{required: true,message: this.$t('support.selectDuration')},
					          {validator: integerNum,message: this.$t('support.prongDurationFormat')},
						      {validator: (rule, value, cb) => {
								return Number(2147483647) >= Number(value);
							  },message: this.$t('support.DurationValueLarge'),}],
					effectiveDay:[
					            {validator: (rule, value, cb) => {
							      return /^[0-9]\d*$/.test(value) || value == '';
						        },message:  this.$t('support.wrongPackageValidity')},
					            {validator: (rule, value, cb) => {
							      return (Number(2147483647) >= Number(value)) || value == '';
						        },message: this.$t('support.packageValidityLarge'),}],
					flowLimitType:[{required: true,message: this.$t('support.selectDataResetType'),}],
					controlLogic:[{required: true,message: this.$t('support.selectRestrictionLogic'),}],
					isSupportedHotspots:[{required: true,message: this.$t('support.isAupportHotspot'),}],
					mccList:[{required: true,message: this.$t('buymeal.selectCountry'),}],
					consumption:[
						{required: true,message: this.$t('support.inputUsage'),},
						{
							validator: validateConsumption,
						}
					],
					upccTemplateId:[{required: true,message: this.$t('support.selectSpeedTemplate')}],
					templateName:[{required: true,message: this.$t('support.selectUnlimitedUsageTemplate'),}],
					selectionTypes: [
						{
							validator: (rule, value, cb) => {
								var len = this.formObj.selectionTypes.length;
								return len > 0 || !this.formObj.hasRefuelPackage;
							},
							message: this.$t("support.AddonListMandatory"),
						}
					],
					isSupportDirect: [{required: true,message: this.$t('directionalApp.SupportDataMandatory'),}],
					directType: [{
						required: true,
						message: this.$t('directionalApp.LogicMandatory'),
					}],
					appId: [{
						required: true,
						type: 'array',
						message: this.$t('directionalApp.APPMandatory'),
					}],
					isUsePackage: [{
						required: true,
						message: this.$t('directionalApp.ContinueMandatory'),
					}],
					consumption1: [{
						required: true,
						message: this.$t('directionalApp.valueMandatory'),
					}, {
						validator: (rule, value, cb) => {
							var str = /^[1-9]\d*$/;
							return str.test(value);
						},
						message: this.$t('flow.Pleaseinteger'),
					}, {
						validator: validateRepeat,
						message: this.$t('directionalApp.valueRepeat'),
					}, {
						validator: validateLarger,
						message: this.$t('directionalApp.gearRule'),
					}],
					upccTemplateId1: [{
						required: true,
						validator: validateUpcc,
						message: this.$t('directionalApp.upccMandatory'),
						// trigger: 'blur',
					}],
					noLimitTemplateId: [{
						required: true,
						validator: validateSpeedLimit,
						// trigger: 'blur',
					}]
				},
				editRowData: {},
				allCountryData: [],
			}
		},
		mounted() {
			this.packageGetDirectional()
			this.language = localStorage.getItem("local")
		},
		computed: {
			// 应用不能重复选择，去掉已经选择的选项
			choseAppInfos() {
				// 把已经选中的选项过滤掉
				return (val) => {
					let newList = JSON.parse(JSON.stringify(this.appInfos));
					//处理appId数据，返回一个新数组arr
					//arr数组就相当于所有Select选中的数据集合（没有选中的为''，不影响判断），只要在这个集合里面，其他的下拉框就不应该有这个选项
					const arr = [];
					this.formObj.directAppInfos.forEach(item => {
						item.appId.map(i => {
							arr.push(i)
							return arr
						})
					});
					//过滤出newList里面需要显示的数据
					newList = newList.filter(item => {
						//当前下拉框的选中的数据需要显示
						//val就是当前下拉框选中的值
						if (val.includes(item.id)) {
							return item;
						} else {
							//再判断在arr这个数组中是不是有这个数据，如果不在，说明是需要显示的
							if (arr.indexOf(item.id) == -1) {
								return item;
							}
						}
					});
					return newList;
				}
			},
		},
		methods:{
			convertToMB(value, unit) {
				// 用字符串处理，避免精度丢失
				if (unit === 'MB') return value.toString();
				if (unit === 'GB') return (BigInt(value) * 1024n).toString();
				if (unit === 'TB') return (BigInt(value) * 1024n * 1024n).toString();
				return value.toString();
			},
			// 编辑/相亲/复制显示
			async show(row,type){ // 改为 async
				this.countryList = []
				this.allCountryData = []
				this.formObj.mccList = []
				this.editRowData = {}
				if(row){
					this.editRowData = JSON.parse(JSON.stringify(row))
					this.refuelIDList = JSON.parse(JSON.stringify(row.refuelIDList)) //获取初始加油包列表数据
          this.formObj.id=row.id
					this.packageGetDirectional()
					this.formObj.isSupportDirect = row.isSupportDirect
					if (row.isSupportDirect == '1') {
						this.setData()
					}
					if (type == 'true' && row.isSupportDirect == '1') {
						this.getPackageSale(row.id)
					} else {
						this.notClick = false
					}
          this.formObj.nameCn=row.nameCn
          this.formObj.descCn=row.descCn
          this.formObj.periodUnit=row.periodUnit
          this.formObj.effectiveDay=row.effectiveDay
          this.formObj.keepPeriod=row.keepPeriod
          this.formObj.flowLimitType=Number(row.flowLimitType)
          this.formObj.controlLogic=Number(row.controlLogic)
					this.formObj.isSupportedHotspots=Number(row.isSupportedHotspots)
					// 获取国家全量数据
					// 查询条件this.$refs.addCountry.currentContinent = 'all';
					this.$refs.addCountry.currentContinent = 'all';
					this.$refs.addCountry.continent = '';
					let tempallCountryData = await this.$refs.addCountry.getAllCountryData();
					this.allCountryData = tempallCountryData.data.records
					// 3. 异步获取依赖数据（国家列表、UPCC模板）
					await this.getcountryList(this.formObj.isSupportedHotspots); // 等待国家列表获取完成

          this.formObj.templateName=row.noLimitTemplateId
					this.formObj.mccList=row.mccList
					this.formObj.combinationList=[]
          this.formObj.mccMap=row.mccMap
					this.formObj.hasRefuelPackage = row.refuelIDList.length > 0 ? true : false;
					this.formObj.selectionTypes = row.refuelIDList
					row.packageConsumptions.map((item)=>{
            const [consumptionValue, unitValue] = item.displayConsumption.split(' ');
            const unit = unitValue || "MB"; // 默认MB
						this.formObj.combinationList.push({
								consumption:consumptionValue,//用量值
								displayConsumption:item.displayConsumption,
								unit: unit, // 用量单位
								upccTemplateId:item.upccTemplateId,//速度模板
								templateName:item.templateName,//速度模板
						})
					})
				}
				this.modal=true
			},
			// 添加用量值与速度模板组
			addItem(){
				this.formObj.combinationList.push({
							consumption:'',
							unit: 'MB',
							upccTemplateId:'',
						})
			},
			// 删除用量值与速度模板组
			delItem(index){
				this.formObj.combinationList.splice(index,1)
			},
			//单日周期时默认选择达量控制逻辑
			getcontrolLogic() {
				this.formObj.controlLogic = this.formObj.flowLimitType === 2 ? 1 :null
			},
			// 根据是否支持热点值获取国家列表
			async getcountryList(isSupportedHotspots){
        this.formObj.combinationList=[{
					consumption:'',
					unit: 'MB',
				}]
				if(isSupportedHotspots){
					this.isSupportGetMccList(isSupportedHotspots)
					this.getUpccList(isSupportedHotspots)
				}

			},
			showCountry() {
				//将已选国家回显到已选国家列表，按照selectedColumns的数据结构展示
				this.$refs.addCountry.selectedData = [] // 清空已选国家数据
				// 先重置addCountry组件的状态
				this.$refs.addCountry.selectedValues = [...this.formObj.mccList];

				this.$refs.addCountry.currentContinent = 'all';
				this.$refs.addCountry.continent = "";
				// 先获取全量数据，再加载分页数据
				this.$refs.addCountry.getAllCountryData().then(() => {
					this.$refs.addCountry.loadData(true);
					this.countryShowType = true;  // 数据加载后再显示弹窗
				});
			},
			//是否支持加油包切换清空加油包选项
			fuelPackaChange(){
				this.formObj.selectionTypes=[]
			},
			RefuelPackageList() {
				if (this.typeFlag==="info") {
					this.getDetailsRefuelList(1)
				} else {
					this.getRefuelList(1)
				}
			},
			//获取可订购加油包列表 详情
			getDetailsRefuelList(page) {
				this.Unitedloading = true
				getDetailsRefuelList({
					pageNum: page,
					pageSize: 10,
					refuelID: this.searchObj.gaspacknameid,
					refuelName: this.searchObj.gaspackname,
					packageID: this.formObj.id,
				}).then(res => {
					if (res && res.code == '0000') {
						this.Uniteddata = res.data
						this.Unitedtotal = res.count
						this.UnitedcurrentPage = page
						//回显
						this.formObj.selectionTypes.forEach(item => {
							res.data.forEach(element => {
								if (element.id == item.id) {
									this.$set(element, '_checked', true)
									this.$set(element, '_disabled', true)
								}
							})
						})
						this.addRefuelModel = true
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
					this.Unitedloading = false
					this.searchObjloading = false
				})
			},
			//获取可订购加油包列表 新增、编辑、复制
			getRefuelList(page){
				this.Unitedloading=true
				getRefuelList({
					pageNum:page,
					pageSize:10,
					refuelID:this.searchObj.gaspacknameid,
					refuelName:this.searchObj.gaspackname,
					corpId: this.corpId
				}).then(res => {
					if (res && res.code == '0000') {
						this.Uniteddata=res.data
						this.Unitedtotal=res.count
						this.UnitedcurrentPage = page
						//回显
						this.formObj.selectionTypes.forEach(item=>{
						   res.data.forEach(element=>{
								if(element.id==item.id){
									this.$set(element,'_checked',true)
								}
							})
						})
						this.addRefuelModel = true
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
					this.Unitedloading=false
				   this.searchObjloading=false
				})
			},
			search(){
				this.searchObjloading=true
				if (this.typeFlag==="info") {
					this.getDetailsRefuelList(1)
				} else {
					this.getRefuelList(1)
				}
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					let flag = true;
					this.formObj.selectionTypes.map((item, index) => {
					   if(value.id===item.id){
						   flag=false
					   }
					});
					//判断重复
					if(flag){
						this.formObj.selectionTypes.push(value);
					}
				});
			},
			// 取消选择套餐包
			cancelPackage(selection, row) {
				this.formObj.selectionTypes.forEach((value,index)=>{
					if(value.id===row.id){
						 this.formObj.selectionTypes.splice(index,1);
					}
				})
			},
			// 取消全选选择套餐包
			cancelPackageAll(selection, row) {
				this.formObj.selectionTypes=[]
			},
			//加油包列表确认
			Confirm() {
				this.addRefuelModel = false
				this.searchObj.gaspackname = ""
				this.searchObj.gaspacknameid = ""
				this.$refs["formObj"].validateField('selectionTypes', valid => {
				})
				//获取确定后的数据
				this.refuelIDLists = JSON.parse(JSON.stringify(this.formObj.selectionTypes))
			},
			//国家确认
			ConfirmCountry() {
				this.countryShowType = false

				//已选国家
				this.selectCountryData = this.$refs.addCountry.selectedData

				// 更新formObj.mccList和countryList
				this.formObj.mccList = this.selectCountryData.map((item) => item.mccId)
				this.countryList = this.selectCountryData.map((item) => {
					return {
						countryId: item.mccId,
						countryName: item.mccId.includes('-') ?   item.countryEn + '(' + item.remark + ')':item.countryEn
					}
				})
				//弹窗内容初始化：默认打开分类【全部】，默认选中【全部】，默认分页为1
				this.$refs.addCountry.currentContinent = 'all';
				this.$refs.addCountry.currentPage = 1;

			},
			//加油包取消
			cancelBagModal() {
				this.addRefuelModel = false
				this.searchObj.gaspackname = ""
				this.searchObj.gaspacknameid = ""
				if (this.typeFlag!=="info") {
					//如果一开始取消，加油包数据则等于初始数据，如果有确定后改过数据，那么再点取消，展示的应该是确定后的数据
					if (this.refuelIDLists.length == 0) {
						this.formObj.selectionTypes = JSON.parse(JSON.stringify(this.refuelIDList))
					} else {
						this.formObj.selectionTypes = JSON.parse(JSON.stringify(this.refuelIDLists))
					}
				}
			},
			canceCountry() {
				this.countryShowType = false
				//弹窗内容初始化：默认打开分类【全部】，默认选中【全部】，默认分页为1
				this.$refs.addCountry.currentContinent = 'all';
				this.$refs.addCountry.currentPage = 1;
				this.countryList = [];
			},
			UnitedgoPage(page){
				if (this.typeFlag==="info") {
					this.getDetailsRefuelList(page)
				} else {
					this.getRefuelList(page)
				}
			},
			// 取消
			cancelModal(){
				this.modal=false
				this.formObj.controlLogic=""
				this.formObj.combinationList=[{
					consumption:'',
					unit: 'MB',
					upccTemplateId:'',
				}]
				this.selectCountryData = [] // 清空已选国家数据
				this.$nextTick(()=>{
				  this.$refs['formObj'].resetFields()
				})
				this.formObj.directAppInfos=[{
					index: 1,
					appId: [],//应用多选框选中的值
					directType: '',//定向使用逻辑
					appConsumption: [{
						index1: 1,
						consumption: '',//流量值
						upccTemplateId: [],//选择UPCC模板值
					}],
					isUsePackage: '',//是否继续使用通用模板
					noLimitTemplateId: [],//定向限速板/定向免流限速模版/定向免流继续使用模板
				}]
				this.formObj.mccList=[]
				this.refuelIDLists = []
				this.notClick = false
				this.editRowData = {}
			},
			transferData(data){
				//转换定向流量数据格式传给后端
				const directAppInfos = [];

				for (const directAppInfo of data) {
				    const appDetailInfos = [];
				    directAppInfo.appId.forEach((appId, i) => {
						if (directAppInfo.appConsumption) {
							const appConsumption = directAppInfo.appConsumption.map((consumptionData) => {
								return {
									consumption: parseInt(consumptionData.consumption),
									upccTemplateId: consumptionData.upccTemplateId[i]
								};
							});
							const noLimitTemplateIdList = directAppInfo.noLimitTemplateId;
							const appDetailInfo = {
								appConsumption : directAppInfo.directType == '2' ? appConsumption : [],
								appId,
								noLimitTemplateId: noLimitTemplateIdList[i]
							};
							appDetailInfos.push(appDetailInfo);
						}
				    });
				    const directAppInfoData = {
						appDetailInfos,
						directType: directAppInfo.directType,
						isUsePackage: directAppInfo.isUsePackage
				    };

					directAppInfos.push(directAppInfoData);
				}
				this.submitList = directAppInfos
			},
			// 提交
			submit(){
				if (this.formObj.isSupportDirect == '1') {
					//转换定向应用数据给后端
					this.transferData(this.formObj.directAppInfos)
				}
				this.$refs["formObj"].validate((valid)=>{
					if(valid){
						let list2 = JSON.parse(JSON.stringify(this.formObj.combinationList)); // list2 深拷贝原始数据
						list2.forEach((e)=>{ // 用量转MB处理，MB为字符串，GB/TB为字符串
							e.consumption = this.convertToMB(e.consumption, e.unit);
						})
						let list1 = JSON.parse(JSON.stringify(list2)); // 从 list2 深拷贝到 list1
						list1.forEach((e)=>{
						    delete e.displayConsumption // 在 list1 上删除前端显示字段
						    delete e.templateName     // 在 list1 上删除前端显示字段
						})
						//加油包列表
						var refuelList = [];
						if (this.formObj.hasRefuelPackage) {
							this.formObj.selectionTypes.forEach((value, index) => {
								refuelList.push(value.id)
							})
						}
						this.submitloading=true
						if(this.typeFlag==="info" || this.typeFlag==="add"){//新增/复制
							addItem({
								nameCn:this.formObj.nameCn,//套餐名称
								descCn:this.formObj.descCn,//套餐描述
								periodUnit:this.formObj.periodUnit,//套餐周期
								keepPeriod:this.formObj.keepPeriod,//套餐持续周期
								effectiveDay:this.formObj.effectiveDay,//套餐有效期
								flowLimitType:this.formObj.flowLimitType,//流量限制类型
								controlLogic:this.formObj.controlLogic,//控制逻辑
								isSupportedHotspots:this.formObj.isSupportedHotspots,//是否支持热点
								packageConsumptions:this.typeFlag==="update" ? list1 : list2,//用量和速度模板组
								noLimitTemplateId:this.formObj.templateName,//无上限模板
								mccList:this.formObj.mccList,//支持国家/地区
								corpId:this.corpId,//渠道商ID
								groupId:this.formObj.groupId,//卡池组id
								supportRefuel: this.formObj.hasRefuelPackage ? 1 : 2, //是否支持加油包
								refuelList: refuelList, //加油包
								isSupportDirect: this.appInfos.length == 0 ? 2 : this.formObj.isSupportDirect,//是否支持定向用量
								directAppInfos: this.formObj.isSupportDirect == "1" ? this.submitList : [],//应用信息
							}).then(res => {
								if (res && res.code == '0000') {
									this.$emit('goPageFirst',1)
									this.$Notice.success({
										title: this.$t("address.Operationreminder"),
										desc: this.$t("common.Successful")
									})
									 this.cancelModal()
								} else {
									throw res
								}
							}).catch((err) => {

							}).finally(() => {
							   this.submitloading=false
				            })
						}else if(this.typeFlag==="update"){//修改
							updateItem({
								id: this.formObj.id,
								nameCn:this.formObj.nameCn,//套餐名称
								descCn:this.formObj.descCn,//套餐描述
								periodUnit:this.formObj.periodUnit,//套餐周期
								keepPeriod:this.formObj.keepPeriod,//套餐持续周期
								effectiveDay:this.formObj.effectiveDay,//套餐有效期
								flowLimitType:this.formObj.flowLimitType,//流量限制类型
								controlLogic:this.formObj.controlLogic,//控制逻辑
								isSupportedHotspots:this.formObj.isSupportedHotspots,//是否支持热点
								packageConsumptions:this.typeFlag==="update" ? list1 : list2,//用量和速度模板组
								noLimitTemplateId:this.formObj.templateName,//无上限模板
								mccList:this.formObj.mccList,//支持国家/地区
								corpId:this.corpId,//渠道商ID
								groupId:this.formObj.groupId,//卡池组id
								supportRefuel: this.formObj.hasRefuelPackage ? 1 : 2, //是否支持加油包
								refuelList: refuelList, //加油包
								isSupportDirect: this.formObj.isSupportDirect,//是否支持定向用量
								directAppInfos: this.formObj.isSupportDirect == "1" ? this.submitList : [],//应用信息
							}).then(res => {
								if (res && res.code == '0000') {
									this.$emit('goPageFirst',1)
									this.$Notice.success({
										title: this.$t("address.Operationreminder"),
										desc: this.$t("common.Successful")
									})
									 this.cancelModal()
								} else {
									throw res
								}
							}).catch((err) => {

							}).finally(() => {
								this.submitloading=false
				            })
						}
					}
				})
			},
			//添加应用
			addApp() {
				this.index++;
				this.formObj.directAppInfos.push({
				    index: this.index,
				    appId: [],//应用列表
					directType: '',//定向使用逻辑
					appConsumption: [{
						index1: 1,
						consumption: '',//流量值
						upccTemplateId: [],//选择UPCC模板值
					}],
					isUsePackage: '',//是否继续使用通用模板
					noLimitTemplateId: [],//定向限速板/定向免流限速模版/定向免流继续使用模板
				});
			},
			//刪除应用
			deleteApp(index) {
				this.formObj.directAppInfos.splice(index, 1)
				this.index--;

				//删除应用后重新计算应用总数
				let arr = [];
				this.formObj.directAppInfos.forEach(item => {
					item.appId.map(i => {
						arr.push(i)
						return arr
					})
				});
				if (arr.length >= '9') {
					this.appTotal = true
				} else {
					this.appTotal = false
				}

			},
			//删除流量值
			delFlowValue(findex,index) {
				let directAppInfos = this.formObj.directAppInfos[findex]
				directAppInfos.appConsumption.splice(index, 1);
				this.index1--;
			},
			//添加流量值
			addFlowValue(findex) {
				let directAppInfos = this.formObj.directAppInfos[findex]
				this.index1++;
				directAppInfos.appConsumption.push({
					index1: this.index1,
					consumption: '',//流量值
					upccTemplateId:[],//选择UPCC模板值
				})
			},
			//改变是否支持定向流量值时清空数据
			changeDirect(data) {
				// if (data == "2") {
				// 	this.formObj.directAppInfos = [{
				// 		index: 1,
				// 		appId: [],//应用列表
				// 		directType: '',//定向使用逻辑
				// 		appConsumption: [{
				// 			index1: 1,
				// 			consumption: '',//流量值
				// 			upccTemplateId:[],//选择UPCC模板值
				// 		}],
				// 		isUsePackage: '',//是否继续使用通用模板
				// 		noLimitTemplateId: [],//定向限速板/定向免流限速模版/定向免流继续使用模板
				// 	}]
				// }
			},
			//改变定向流量逻辑时清空数据
			changeLogic(findex) {
				let directAppInfos = this.formObj.directAppInfos[findex]
				directAppInfos.appConsumption= [{
					index1: this.index1,
					consumption: '',//流量值
					upccTemplateId:[],//选择UPCC模板值
				}]
				directAppInfos.isUsePackage =''
				directAppInfos.noLimitTemplateId = []
			},
			//改变是否继续使用通用模版时清除数据
			changeUsePackage(e) {
				// this.formObj.directAppInfos.forEach((i) => {
				// 	i.noLimitTemplateId = []
				// })
			},
			//计算应用总数
			changeAppId(e,findex) {
				let arr = [];
				this.formObj.directAppInfos.forEach(item => {
					item.appId.map(i => {
						arr.push(i)
						return arr
					})
				});
				if (arr.length >= '9') {
					this.appTotal = true
				} else {
					this.appTotal = false
				}
				this.upccChange = e
				this.upccIndex = findex

				// 如果appId一样 查看upcc
				if (this.initialized) {
					console.error(this.initialized,"处理数据，初始化已经完成")
					if(this.formObj.directAppInfos[findex].appId == e) {
						const oldValue = this.oldValue
						this.oldValue = e //更新旧值为当前值，为下一次变化做准备
						if (e.length < oldValue.length) {
							function findMissingIndex(arr1, arr2) {
								// 遍历arr1数组
								for (let i = 0; i < arr1.length; i++) {
									// 检查arr1中的当前元素是否存在于arr2中
									if (arr2.indexOf(arr1[i]) === -1) {
									// 如果不存在，返回当前元素在arr1中的下标
									return i;
									}
								}
								// 如果没有找到缺失的元素，则返回-1表示没有缺失
								return -1;
							}
							let missingIndex = ''
							missingIndex = findMissingIndex(oldValue,e)
							let arrList = this.formObj.directAppInfos[findex].appConsumption
							// 每删除一个应用,就删除对应的upcc模板
							arrList.forEach((i) => {
								i.upccTemplateId.splice(missingIndex, 1)
							})
						}
					}

				} else {
					console.error(this.initialized,"不处理")
					return
				}
			},
			//获取定向应用和模板数据源接口
			packageGetDirectional() {
				packageGetDirectional({
					corpId: sessionStorage.getItem("corpId")
				}).then(res => {
					if (res && res.code == '0000') {
						this.appInfos = res.data
						this.appInfos.map((item,index)=>{
							this.directTemplateList[item.id] = item.appUpccInfo
						})
						this.initialized = false
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
					this.initialized = true
				})
			},
			//已售卖套餐不能修改
			getPackageSale(id) {
				getPackageSale({
					packageId: id
				}).then(res => {
					if (res && res.code == '0000') {
						this.notClick = res.data
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			/**
			*
			* ----------------------------------初始化信息-----------------------------------
			*
			*/
			// 获取模板
			getUpccList(isSupportedHotspots){
				getUpccList({
					corpId:this.corpId,
					isSupportedHotspots :isSupportedHotspots,
				}).then(res => {
					if (res.code == '0000') {
						this.upccTemplateList = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				})
			},
      // 查询国家
      isSupportGetMccList(isSupportedHotspots){
				let that = this
				isSupportGetMccList({
					corpId:this.corpId,
					supportedHotspots:isSupportedHotspots,
				}).then(res => {
					if (res.code == '0000') {
            this.formObj.groupId = res.data.groupIds

						// 确保 allCountryData 和 mccList 都存在且为数组
						if (Array.isArray(that.allCountryData) && Array.isArray(this.editRowData.mccList)) {
							let tempCountryList = that.allCountryData.filter((item) => {
								// 使用 item.mccId 与 editRowData.mccList 中的 ID 进行匹配
								return this.editRowData.mccList.includes(item.mccId);
							});
							//将tempCountryList数据从新构建成countryList
							this.countryList = tempCountryList.map(item => {
								let remark = this.language=='zh-CN' ? '('+item.remarkCn +')' : '('+item.remarkEn +')'
								let countryName = item.mccId.includes('-') ?   item.countryEn + ''+remark : item.countryEn
								return {
									countryId: item.mccId,
									countryName: countryName
								}
							});
						} else {
							// 如果数据无效，则将 countryList 设为空数组
							this.countryList = [];
						}
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				})
			},
			// 定向应用信息初始化
			setData() {
				deatilGetDirect({
					packageId: this.formObj.id
				}).then(res => {
					if (res && res.code == '0000') {
						let transformedData = [];

						res.data.forEach((item, index) => {
						    let transformedItem = {
						        index: index + 1,
						        appId: [],
						        directType: item.directType,
						        noLimitTemplateId: item.appDetailInfos.map(detail => detail.noLimitTemplateId),
								isUsePackage: item.isUsePackage
						    };

						    item.appDetailInfos.forEach(appDetail => {
						        transformedItem.appId.push(appDetail.appId);

						        if (appDetail.appConsumption.length > 0) {
						            appDetail.appConsumption.forEach(consumption => {
						                if (!transformedItem.appConsumption) {
						                    transformedItem.appConsumption = [];
						                }

						                let existingConsumption = transformedItem.appConsumption.find(c => c.consumption === consumption.consumption);
						                if (existingConsumption) {
						                    existingConsumption.upccTemplateId.push(consumption.upccTemplateId);
						                } else {
						                    transformedItem.appConsumption.push({
						                        index1: transformedItem.index,
						                        consumption: consumption.consumption,
						                        upccTemplateId: [consumption.upccTemplateId],
												// isUsePackage: transformedItem.isUsePackage,
												// noLimitTemplateId: transformedItem.noLimitTemplateId,
						                    });
						                }
						            });
						        } else {
									transformedItem.appConsumption= [{
										index1: this.index1,
										consumption: '',
										upccTemplateId: [],
									}]
								}
						    });

						    transformedData.push(transformedItem);
						});

						this.formObj.directAppInfos = transformedData
						this.initialized = false
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
					this.initialized = true
				})
			},
      		}
	}
</script>

<style scoped="scoped">
	.footer_wrap{
		width: 100%;
		display: flex;
		align-items: center;
		justify-content:center;
	}

	:deep(.ivu-select) {
		position: relative;
	}

	:deep(.ivu-select .ivu-select-dropdown) {
		width: 100% !important;
		min-width: auto !important;
	}
</style>
