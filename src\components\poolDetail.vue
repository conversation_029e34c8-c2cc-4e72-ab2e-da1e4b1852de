<template>
  <!-- 4. 卡池详情弹窗 -->
  <Modal :value="visible" title="卡池详情" :footer-hide="true" :mask-closable="false" width="900px" @on-cancel="$emit('close')">
    <Spin size="large" fix v-if="loading"></Spin>
    <div v-if="!loading && currentPoolDetails" class="modal-content">
      <div class="box">
        <span style="">卡池类型：&nbsp;&nbsp;{{ currentPoolDetails.usageType == "1" ? '全球卡普通卡池' :
          currentPoolDetails.usageType
            == "2" ? '终端线下卡池' : currentPoolDetails.usageType == "3" ? '终端线上卡池' : "" }}</span>
      </div>
      <div class="box">
        <span style="">卡池名称：&nbsp;&nbsp;{{ currentPoolDetails.poolName }}</span>
      </div>
      <div class="box">
        <span style="">支持国家/地区：&nbsp;&nbsp;{{ currentPoolDetails.mccsCn ? currentPoolDetails.mccsCn.join(',') :
          '' }}</span>
      </div>
      <div class="box">
        <span style="">供应商：&nbsp;&nbsp;{{ currentPoolDetails.supplierName }}</span>
      </div>
      <div class="box">
        <span style="">TPLID：&nbsp;&nbsp;{{ currentPoolDetails.tplId }}</span>
      </div>
      <div class="box" v-if="currentPoolDetails.usageType != '2'">
        <span style="">是否动态签约：&nbsp;&nbsp;{{ currentPoolDetails.isSignUpcc == "1" ? '是' : '否' }}</span>
      </div>
      <div class="box" v-if="currentPoolDetails.usageType != '1'">
        <span style="">是否动态开户：&nbsp;&nbsp;{{ currentPoolDetails.isOpenAccount == "1" ? '是' : '否' }}</span>
      </div>
      <!-- 全球卡普通卡池 -->
      <div class="box" v-if="currentPoolDetails.usageType == '1'">
        <span style="">支持HIMSI-4G上网：&nbsp;&nbsp;{{ currentPoolDetails.isSupportHimsi == "1" ? '是' : '否' }}</span>
      </div>
      <!-- 终端线下卡池 -->
      <div class="box" v-if="currentPoolDetails.usageType == '2'">
        <span style="">厂商：&nbsp;&nbsp;{{ detilCorpname }}</span>
      </div>
      <div class="box" v-if="currentPoolDetails.usageType == '2'">
        <span style="">套餐名称：&nbsp;&nbsp;{{ currentPoolDetails.packageName || '暂无套餐名称' }}</span>
      </div>
      <div class="box" v-if="currentPoolDetails.usageType == '2'">
        <span style="">套餐计算周期类型：&nbsp;&nbsp;{{ currentPoolDetails.periodUnit == '1' ? '24小时' :
          currentPoolDetails.periodUnit == '2' ?
            '自然日' : currentPoolDetails.periodUnit == '3' ? '自然月' : currentPoolDetails.periodUnit == '4' ? '自然年' :
              '' }}</span>
      </div>
      <div class="box" v-if="currentPoolDetails.usageType == '2'">
        <span style="">持续周期数：&nbsp;&nbsp;{{ currentPoolDetails.keepPeriod }}</span>
      </div>
      <div class="box" v-if="currentPoolDetails.usageType == '2'">
        <span style="">到期后是否重置：&nbsp;&nbsp;{{ currentPoolDetails.isExpireReset == '1' ? '是' : '否' }}</span>
      </div>
      <!-- 全球卡普通卡时，支持HIMSI-4g上网时不展示 -->
      <div class="box" v-if="currentPoolDetails.isSupportHimsi != '1'">
        <span style="">VIMSI冻结周期：&nbsp;&nbsp;{{ currentPoolDetails.vimsiFreezeDay }}</span>
      </div>
      <div class="box" v-if="currentPoolDetails.isSupportHimsi != '1'">
        <span style="">告警阈值：&nbsp;&nbsp;{{ currentPoolDetails.alarmThreshold }}</span>
      </div>
      <div style="color:#878787;line-height: 30px;background-color: #f7f7f7;">
        <Collapse v-model="activeCollapsePanel" @on-change="handleCollapseChange">
          <Panel name="1">
            已绑定套餐
            <div slot="content"
              :style="{ 'height': currentPoolDetails.packageList && currentPoolDetails.packageList.length > 5 ? '150px' : '100%', 'overflowY': currentPoolDetails.packageList && currentPoolDetails.packageList.length > 5 ? 'auto' : 'visible' }">
              <div v-if="!currentPoolDetails.packageList || currentPoolDetails.packageList.length === 0">
                <div v-if="!currentPoolDetails.packageList">
                  <div style="display: flex;justify-content: center;">
                    <Icon type="ios-loading" size="large" style="margin-top: 100px;"></Icon>
                  </div>
                </div>
                <div v-else style="display: flex;justify-content: center;">
                  <div style="color: #999;">暂未绑定套餐</div>
                </div>
              </div>
              <div v-for="(item, index) in currentPoolDetails.packageList" :key="index" v-else>
                <Row>
                  <Col span="10">
                  <div style="overflow: hidden; text-overflow:ellipsis; white-space: nowrap;text-align: left;">
                    套餐ID：&nbsp;&nbsp;{{ item.packageId }}
                  </div>
                  </Col>
                  <Col span="8">
                  <div style="overflow: hidden; text-overflow:ellipsis; white-space: nowrap;text-align: left;">
                    套餐名称：&nbsp;&nbsp;{{ item.nameCn || '暂无名称' }}
                  </div>
                  </Col>
                  <Col span="6">
                  <div style="overflow: hidden; text-overflow:ellipsis; white-space: nowrap;text-align: left;">
                    绑定时间：&nbsp;&nbsp;{{ item.createTime | formatDateTime }}
                  </div>
                  </Col>
                </Row>
              </div>
            </div>
          </Panel>
        </Collapse>
      </div>
    </div>
    <div v-if="!loading && !currentPoolDetails" style="text-align: center; padding: 20px;">
      无法加载卡池详情
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'PoolDetail',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    currentPoolDetails: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    },
    detilCorpname: {
      type: String,
      default: ''
    },
    // 支持 String 或 Array，兼容 Collapse 单开/多开
    activeCollapsePanel: {
      type: [String, Array],
      default: ''
    }
  },
  methods: {
    /**
     * Collapse 面板切换时触发，通知父组件更新 activeCollapsePanel
     * @param {String|Array} e 当前激活的面板名
     */
    handleCollapseChange (e) {
      this.$emit('update:activeCollapsePanel', e)
      this.$emit('collapse-change', e)
    }
  },
  filters: {
    formatDateTime (value) {
      if (!value) return '';
      try {
        const date = new Date(value);
        return date.toLocaleString();
      } catch (e) {
        return value;
      }
    }
  }
}
</script>

<style scoped>
	.box {
		padding: 0 10px;
		color: #878787;
		line-height: 38px;
		background-color: #f7f7f7;
		border: 1px solid #dcdee2;
		border-bottom: none;
	}</style>