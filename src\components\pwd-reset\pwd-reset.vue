<template>
	<!-- 忘记密码 -->
	<div class="pwd-reset">
		<div v-if="showRules" class="view_right">
			<Alert type="warning" closable @on-close="showRules = false">
				<div v-if="this.$i18n.locale==='zh-CN'">
					<text-view></text-view>
				</div>
				<div v-if="this.$i18n.locale==='en-US'">
					<text-viewEn></text-viewEn>
				</div>
			</Alert>
		</div>
		<div class="view_out" v-if="this.$i18n.locale === 'en-US'">
			<Form ref="forgetPwForm" :model="form" :rules="rules" @keydown.enter.native="handleSubmit">
				<FormItem prop="username">
					<div style="display: flex; flex-wrap: nowrap;">
						<Input v-model="form.username" placeholder="Please Enter User Name" clearable>
						<span slot="prepend">
							<Icon :size="20" type="ios-person"></Icon>
						</span>
						</Input>
						<input ref="captchaSessionId" name="captchaSessionId" type="hidden" />
						<Button id="msg" @click="handlePhone" type="success" :disabled="disabled" long
							style="width: 50%;margin: 0 0 0 10px;">{{valiBtn}}</Button>
					</div>
				</FormItem>
				<FormItem prop="smsCode">
					<div style="display: flex; flex-wrap: nowrap;">
						<Input v-model="form.smsCode" placeholder="Please enter verification code" ref="smsCode"
							:maxlength="6" clearable>
						<span slot="prepend">
							<Icon :size="20" type="ios-mail"></Icon>
						</span>
						</Input>
					</div>
				</FormItem>
				<FormItem prop="password">
					<Input type="password" v-model="form.password" placeholder="Please Enter A New Password" clearable>
					<span slot="prepend">
						<Icon :size="20" type="md-lock"></Icon>
					</span>
					</Input>
				</FormItem>
				<FormItem prop="repassword">
					<Input type="password" v-model="form.repassword" placeholder="Please enter password again" clearable
						@on-blur="checkPwd">
					<span slot="prepend">
						<Icon :size="20" type="md-lock"></Icon>
					</span>
					</Input>
				</FormItem>
				<!-- 密码规则 -->
				<Alert type="warning" show-icon>
					When Setting The Password, You Need To Meet The Given Rules, Click
					<a href="#" @click="showRules = true">View </a>
					Learn More
				</Alert>
				<FormItem>
					<div style="display: flex; flex-wrap: nowrap;justify-content: space-between;">
						<Button @click="returnBak" type="error" long style="width: 40%;">Return</Button>
						<Button @click="handleSubmit" type="primary" long style="width: 40%;">Reset password</Button>
					</div>
				</FormItem>
			</Form>
		</div>

		<div class="view_out" v-else>
			<Form ref="forgetPwForm" :model="form" :rules="rules" @keydown.enter.native="handleSubmit">
				<FormItem prop="username">
					<div style="display: flex; flex-wrap: nowrap;">
						<Input v-model="form.username" placeholder="请输入用户名" clearable>
						<span slot="prepend">
							<Icon :size="20" type="ios-person"></Icon>
						</span>
						</Input>
						<input ref="captchaSessionId" name="captchaSessionId" type="hidden" />
						<Button id="msg" @click="handlePhone" type="success" :disabled="disabled" long
							style="width: 50%;margin: 0 0 0 10px;">{{valiBtn}}</Button>
					</div>
				</FormItem>
				<!-- <FormItem prop="phoneNumber">
						<Input v-model="form.phoneNumber" placeholder="请输入手机号" :maxlength="16" clearable>
						<span slot="prepend">
							<Icon :size="20" type="md-call"></Icon>
						</span>
						</Input>
					</FormItem> -->
				<FormItem prop="smsCode">
					<div style="display: flex; flex-wrap: nowrap;">
						<Input v-model="form.smsCode" placeholder="请输入验证码" ref="smsCode" :maxlength="6" clearable>
						<span slot="prepend">
							<Icon :size="20" type="ios-mail"></Icon>
						</span>
						</Input>
					</div>
				</FormItem>
				<FormItem prop="password">
					<Input type="password" v-model="form.password" placeholder="请输入新密码" clearable>
					<span slot="prepend">
						<Icon :size="20" type="md-lock"></Icon>
					</span>
					</Input>
				</FormItem>
				<FormItem prop="repassword">
					<Input type="password" v-model="form.repassword" placeholder="请再次输入新密码" clearable
						@on-blur="checkPwd">
					<span slot="prepend">
						<Icon :size="20" type="md-lock"></Icon>
					</span>
					</Input>
				</FormItem>
				<!-- <FormItem prop="captcha">
						<div style="display: flex; flex-wrap: nowrap;">
							<Input v-model="form.captcha" placeholder="请输入图形验证码" ref="captcha" :maxlength="4" clearable>
							<span slot="prepend">
								<Icon :size="20" type="ios-image"></Icon>
							</span>
							</Input>
							<input ref="captchaSessionId" name="captchaSessionId" type="hidden" />
							<img ref="codeImg" alt="验证码" @click="refreshCode" title="点击换一张"
								style="width: 30%; height: 32px; margin: 0 10px;">
						</div>
					</FormItem>
					<FormItem>
						<div style="display: flex; flex-wrap: nowrap;justify-content: space-between;">
							<Button @click="returnBak" type="error" long style="width: 40%;">取消</Button>
							<Button @click="handleSubmit" type="primary" long style="width: 40%;">确认</Button>
						</div>
					</FormItem> -->
				<!-- 密码规则 -->
				<Alert type="warning" show-icon>
					密码设置时需要满足给定规则，点击
					<a href="#" @click="showRules = true">查看</a>
					了解详情
				</Alert>
				<FormItem>
					<div style="display: flex; flex-wrap: nowrap;justify-content: space-between;">
						<Button @click="returnBak" type="error" long style="width: 40%;">返回</Button>
						<Button @click="handleSubmit" type="primary" long style="width: 40%;">重置密码</Button>
					</div>
				</FormItem>
			</Form>
		</div>
	</div>
</template>
<script>
	import {
		getVerCode
	} from '@/api/system/login'
	import config from '@/config'
	import TextView from '../../view/system/account/text.vue'
	import TextViewEn from '../../view/system/account/textEn.vue'
	import {
		sendVerifyCode
	} from '@/api/user'
	export default {
		name: 'PwdReset',
		components: {
			TextView,
			TextViewEn
		},
		props: {
			usernameRules: {
				type: Array,
				default: () => {
					return [{
						required: true,
						message: "Login name can't be blank",
						trigger: 'blur'
					}]
				}
			},
			smsCodeRules: {
				type: Array,
				default: () => {
					return [{
							required: true,
							message: 'Please enter verification code',
							trigger: 'blur'
						},
						{
							max: 6,
							message: 'Verification code format incorrect',
							trigger: 'blur'
						}
					]
				}
			},
			language: '',
			// pwdRules: {
			// 	type: Array,
			// 	default: () => {
			// 		return [{
			// 			required: true,
			// 			trigger: 'blur',
			// 			message: "请输入密码"
			// 		}]
			// 	}
			// },
			// refresh: {
			// 	type: Boolean,
			// 	default: false
			// },
			// time: {
			// 	type: Number,
			// 	default: false
			// }
		},
		data() {
			return {
				form: {
					username: '',
					phoneNumber: '',
					smsCode: '',
					password: '',
					repassword: '',
					captchaSessionId: '',
					captcha: ''
				},
				disabled: false,
				checkPass: false,
				showRules: false,
				valiBtn: '下发验证码',
			}
		},
		computed: {
			rules() {
				const validatePassword = (rule, value, callback) => {
					if (this.form.repassword !== '') {
						// 对第二个密码框单独验证
						this.$refs.forgetPwForm.validateField('repassword')
					}
					callback()
				}

				const validateRepeatPassword = (rule, value, callback) => {
					if (value !== this.form.password) {
						callback(new Error(this.$t("sys.inputTwoPw")))
					} else {
						callback()
					}
				}
				const validatePwd = (rule, value, callback) => {
					var pwpattent =
						/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/;
					if (pwpattent.test(value) == false) {
						callback(new Error(this.$t("address.reset")));
						return;
					}
					if (/(.)\1{2}/i.test(value)) {
						callback(new Error(this.$t("address.appear")));
						return;
					}
					var alphnumon =
						/((?:0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)|9(?=0)){2}\d)/;
					if (alphnumon.test(value)) {
						callback(new Error(this.$t("address.allowed"))); //字母或
						return;
					} else {
						callback();
					}
				};
				return {
					username: this.usernameRules,
					smsCode: this.smsCodeRules,
					// phoneNumber: this.phoneNumberRules,					
					// captcha: this.verifyCodeRules,

					password: [{
							required: true,
							message: "The new password cannot be empty",
							trigger: 'blur'
						},
						// {
						//   pattern: /^[0-9a-zA-Z_]+$/,
						//   message: '只能包含英文字母、数字、下划线'
						// },
						{
							min: 6,
							message: this.$t("sys.pwdLengthMsh")
						},
						{
							validator: validatePwd,
							trigger: "blur",
						},
						{
							validator: validatePassword,
							trigger: 'blur'
						},
					],
					repassword: [{
							required: true,
							message: "The new password cannot be empty",
							trigger: 'blur'
						},
						{
							validator: validatePwd,
							trigger: 'blur'
						},
						{
							validator: validateRepeatPassword,
							trigger: 'blur'
						},
					]
				}
			}
		},
		activated: function() {
			// this.refreshCode()
		},
		mounted: function() {
			// this.refreshCode()
			let lang = this.$i18n.locale
			if (lang === 'en-US') {
				this.valiBtn = 'verification code sent'
			}
		},
		watch: {
			// refresh(newVal, oldVal) {
			// 	this.refreshCode()
			// },
			time(newVal, old) {
				if (newVal !== 0) {
					this.disabled = true
				}
				this.showtime(newVal)
			},
			language:{
				handler(newVal, oldVal){
					this.$i18n.locale = newVal
					if (newVal === 'en-US') {
						this.valiBtn = 'verification code sent'
					} else {
						this.valiBtn = '下发验证码'
					}
				},
			},
		},
		methods: {
			returnBak() {
				this.$emit('on-cancel-valid', false)
			},
			checkPwd() {
				if (this.form.password !== this.form.repassword) {

					this.$Notice.warning({
						title: this.$t("address.Operationreminder"),
						desc: this.$t("sys.pwNotMatch")
					})
				} else {
					this.checkPass = true
				}
			},
			handleSubmit() {
				this.$refs.forgetPwForm.validate((valid) => {
					if (valid) {
						if (this.checkPass) {
							this.$emit('on-success-valid', {
								username: this.form.username,
								verifyCode: this.form.smsCode,
								passwd: this.form.password,
								confirmPasswd: this.form.repassword
								// phoneNumber: this.form.phoneNumber,							
								// captchaSessionId: this.form.captchaSessionId,
								// captcha: this.form.captcha,						
							})
						} else {
							this.$Notice.warning({
								title: this.$t("address.Operationreminder"),
								desc: this.$t("sys.pwNotMatch")
							})
						}
					}
				})
			},
			handlePhone: function() {
				if (!this.form.username) {
					this.$Notice.warning({
						title: this.$t("address.Operationreminder"),
						desc: this.$t("sys.inputName")
					})
					return
				}
				// this.$emit('on-send-code', {
				// 	username : this.form.username
				// 	// phoneNumber: this.form.phoneNumber,
				// 	// type: 2
				// })	
				// 发送短信验证码
				sendVerifyCode({
					username: this.form.username
				}).then(res => {
					if (res.code === '0000') {
						let desc = res.data
						this.$Notice.success({
							title: this.$t("address.Operationreminder"),
							desc: desc
						})
						// this.time = this.$config.sendsmsCodeTimeLimit
						this.tackBtn() //验证码倒数60秒
					} else {
						throw res
					}
				}).catch((err) => {
					// this.time = 0
					// this.refresh = !this.refresh
					console.log(err)
					this.disabled = false
				}).finally(() => {})
				// this.showtime(config.sendsmsCodeTimeLimit)				
				this.disabled = true
				try {
					// document.getElementById('msg').innerHTML = '正在发送'
				} catch (e) {
					console.log(e)
				}
			},
			tackBtn() { //验证码倒数60秒
				let time = 60
				let lang = this.$i18n.locale
				let timer = setInterval(() => {
					if (time == 0) {
						clearInterval(timer);
						this.valiBtn = "下发验证码";
						if (lang === 'en-US') {
							this.valiBtn = 'verification code sent'
						}
						this.disabled = false;
					} else {
						this.disabled = true;
						this.valiBtn = time + '秒后重试';
						if (lang === 'en-US') {
							this.valiBtn = 'Retry after ' + time + ' seconds';
						}
						time--;
					}
				}, 1000);
			},
			showtime: function(t) {
				let _this = this
				if (t !== 0) {
					setTimeout(function() {
						try {
							let tim = t - 1
							console.log(tim)
							document.getElementById('msg').innerHTML = ' (' + tim + ')this.$t("sys.second")'
							_this.$emit('timeChangePwd', t - 1)
						} catch (e) {}
					}, 1000)
				} else {
					this.disabled = false
					try {
						document.getElementById('msg').innerHTML = this.$t("support.Verification_Code")
					} catch (e) {}
				}
			},
			refreshCode() {
				let codeImg = this.$refs.codeImg
				getVerCode({
					type: 2
				}).then(resp => {
					codeImg.src = 'data:image/png;base64,' + resp.data.image
					this.form.captchaSessionId = resp.data.sessionId
					this.$refs['captchaSessionId'].value = resp.data.sessionId
				}).catch((err) => {
					console.log(err)
					// this.$Notice.error({
					//   title: '服务器内部异常',
					//   desc: '获取验证码失败'
					// })
				}).finally(() => {
					this.loading = false
				})
			},
			// resetFields() {
			// 	this.$refs['forgetPwForm'].resetFields()
			// },
		}
	}
</script>

<style>
	.pwd-reset{
		width: 100%;
		display: flex;
		justify-content: flex-end;
		align-items: flex-start;
		flex-wrap: wrap;
	}
	
	.view_out {
		width: 360px;
		min-width: 300px;
		margin-right: 20px;
		margin-top: 20px;
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		align-items: center;
	}

	.view_right {
		margin: 20px;
		width: 50%;
		min-width: 30%;
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
	}
</style>