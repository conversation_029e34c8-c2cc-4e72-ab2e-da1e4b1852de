<template>
	<Card style="width: 100%;padding: 16px;">
		<div class="search_box">
			<span class="search_box_label">{{$t('resourceManage.channelName')}}：&nbsp;&nbsp;</span>
			<p><strong> {{corpName}} </strong></p>
		</div>
		<div style="display: flex; width: 100%">
			<Form ref="form" :model="form" :rules="ruleValidate"
				style="display: flex; justify-content: flex-start; flex-wrap: wrap;">
				<FormItem :label="$t('buymeal.Country')" prop="mccList" class="formBox">
					<Select v-model="form.mccList" :placeholder="$t('buymeal.selectCountry')" clearable
						:filterable="true" class="inputSty">
						<Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}
						</Option>
					</Select>
				</FormItem>
				<FormItem :label="$t('resourceManage.Dimension')" prop="dimension" class="formBox">
					<Select v-model="form.dimension" :clearable="true"
						:placeholder="$t('resourceManage.selectDimension')" class="inputSty" 
						@on-change="clearDate">
						<Option :value="1">{{$t('flow.dday')}}</Option>
						<Option :value="2">{{$t('flow.month')}}</Option>
					</Select>
				</FormItem>
				<FormItem v-if="form.dimension == '1' || form.dimension == ''" :label="$t('fuelPack.SelectDate')"
					prop="startTime" class="formBox"
					:rules="form.dimension == '1' ? ruleValidate.date : [{required: false}]">
					<DatePicker v-model="form.date" format="yyyy-MM-dd" type="daterange"
						:placeholder="$t('fuelPack.PleaseSelectDate')" :editable="false" class="inputSty"
						@on-change="checkDatePicker"></DatePicker>
				</FormItem>
				<FormItem v-else :label="$t('flow.Choosemonth')" prop="month" class="formBox"
					:rules="form.dimension == '2' ? ruleValidate.month : [{required: false}]">
					<DatePicker v-model="form.month" format="yyyy-MM" type="month" :placeholder="$t('flow.Pleasemonth')"
						class="inputSty" @on-change="handleDateChange"></DatePicker>
				</FormItem>&nbsp;&nbsp;&nbsp;
				<Button style="margin: 0 10px 30px 0" type="info" v-preventReClick @click="search"
					:loading="searchLoading" v-has="'search'" :disabled="showButton == false && (cooperationMode == '1' || cooperationMode == '2')">
					<Icon type="ios-search" />&nbsp;{{$t('buymeal.search')}}
				</Button>&nbsp;&nbsp;&nbsp;
				<Button style="margin: 0 10px 30px 0" type="primary" @click="exportFile" :loading="downloading"
					v-has="'export'" :disabled="showButton == false && (cooperationMode == '1' || cooperationMode == '2')">
					<Icon type="ios-cloud-download-outline" />&nbsp;{{$t('order.exporttb')}}
				</Button>
			</Form>
		</div>
		<div style="margin-top: 30px;">
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading">
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0;" />
		</div>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
					<FormItem :label="$t('exportID')">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem :label="$t('exportFlie')">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">{{$t('downloadResult')}}</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		getBillingList,
		exportBillingFile
	} from '@/api/resource-cooperation.js';
	import {
		supplier,
	} from '@/api/ResourceSupplier'
	import {
		opsearchAll,
	} from '@/api/operators';
	export default {
		props: ['showButton'],
		components: {},
		data() {
			return {
				total: 0,
				pageSize: 10,
				page: 1,
				cooperationMode: '',
				corpId: '',
				corpName: '',
				taskId: '',
				taskName: '',
				searchLoading: false, //
				downloading: false, //导出
				tableLoading: false, //列表
				exportModal: false,
				form: {
					mccList: '',
					dimension: '',
					date: '',
					startTime: '',
					endTime: '',
					month: '',
				},
				columns: [{
						title: this.$t('buymeal.Country'),
						key: 'countryOrRegion',
						align: 'center',
						tooltip: true,
						minWidth: 120,
						render: (h, params) => {
							const row = params.row
							var text = this.$i18n.locale === 'zh-CN' ? row.countryOrRegion : this.$i18n.locale === 'en-US' ?
								row.mccEn : ''
							return h('label', text)
						}
					},
					{
						title: this.$t('resourceManage.dateOrMonth'),
						key: 'dateStr',
						align: 'center',
						tooltip: true,
						minWidth: 120,
					},
					{
						title: this.$t('flow.usageMB'),
						key: 'usedTraffic',
						align: 'center',
						tooltip: true,
						minWidth: 120,
					},
					{
						title: this.$t('resourceManage.fee'),
						key: 'amount',
						align: 'center',
						tooltip: true,
						minWidth: 120,
					}
				],
				tableData: [],
				continentList: [],
				ruleValidate: {
					date: [{
						required: true,
						message: this.$t('stock.chose_time'),
					}],
					month: [{
						required: true,
						message: this.$t('stock.chose_time'),
					}],
					dimension: [{
						required: true,
						message: this.$t('resourceManage.selectDimension'),
					}],
				},
			}
		},
		methods: {
			//搜索 表格数据加载
			getPageFirst(page) {
				this.page = page;
				this.tableLoading = true
				var searchCondition = {
					pageSize: 10,
					pageNum: page,
					corpId: this.corpId,
					dimension: this.form.dimension,
					beginDate: this.form.startTime,
					endDate: this.form.endTime,
					month: this.form.month,
					country: this.form.mccList
				};
				getBillingList(searchCondition).then(res => {
					if (res && res.code == '0000') {
						this.total = res.count;
						this.tableData = res.data;
						this.tableLoading = false;
						this.searchLoading = false
					} else {
						throw res
					}
				}).catch((err) => {
					this.tableLoading = false;
					this.searchLoading = false
				}).finally(() => {})
			},
			loadByPage(page) {
				this.getPageFirst(page);
			},
			//搜索
			search() {
				this.$refs["form"].validate((valid) => {
					if (valid) {
						this.searchLoading = true
						this.getPageFirst(1);
					}
				})
			},
			//导出
			exportFile() {
				this.$refs["form"].validate((valid) => {
					if (valid) {
						this.downloading = true
						exportBillingFile({
							corpId: this.corpId,
							dimension: this.form.dimension,
							beginDate: this.form.startTime,
							endDate: this.form.endTime,
							month: this.form.month,
							country: this.form.mccList,
							en: this.$i18n.locale === 'zh-CN' ? false : this.$i18n.locale === 'en-US' ? true : ''
						}).then((res) => {
							if (res && res.code == '0000') {
								this.exportModal = true
								this.taskId = res.data.taskId
								this.taskName = res.data.taskName
								this.downloading = false
							} else {
								this.downloading = false
								throw error
							}
						}).catch((err) => {
							this.downloading = false
						}).finally(() => {

						})
					}
				})
			},

			cancelModal() {
				this.exportModal = false
			},

			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportModal = false
			},

			checkDatePicker(date) {
				if (Array.isArray(date)) {
					this.form.startTime = date[0];
					this.form.endTime = date[1];
				}
			},

			handleDateChange(dateArr) {
				this.form.month = dateArr
			},

			//清除时间
			clearDate(value) {
				if (value == '1') {
					this.form.month = ''
				} else if (value == '2') {
					this.form.date = ''
					this.form.startTime = ''
					this.form.endTime = ''
				} else {
					this.form.month = ''
					this.form.date = ''
					this.form.startTime = ''
					this.form.endTime = ''
				}
			},

			//国家/地区
			getLocalList() {
				opsearchAll().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.continentList = list;
						this.continentList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
						var localMap = new Map();
						list.map((local, index) => {
							localMap.set(local.mcc, local.countryEn);
						});
						this.localMap = localMap;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},

		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			this.corpId = this.$route.query.corpId
			this.corpName = this.$route.query.corpName
			if(this.cooperationMode) {
				//渠道自服务且合作模式为3
				if(this.cooperationMode == '3') {
					this.getLocalList()
				}
			} else { 
				//客户管理
				this.getLocalList()
			}
		}
	}
</script>

<style>
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box_label {
		font-weight: bold;
	}

	.search_box {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin: 0 20px 30px 0;
		flex-direction: row;
	}

	.formBox {
		display: flex;
		font-weight: bold;
		margin-right: 20px;
	}

	.inputSty {
		width: 250px;
	}

	/* 去掉input为number的上下箭头 */
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}

	input[type="number"] {
		-moz-appearance: textfield;
	}
</style>