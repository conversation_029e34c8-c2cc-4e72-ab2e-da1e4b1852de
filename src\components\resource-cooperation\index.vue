<template>
	<Card style="width: 100%; padiing: 16px">
		<Form ref="searchForm" :model="searchObj" inline @submit.native.prevent :label-width="100"
			style="margin: 30px 0">
			<FormItem :label="$t('resourceManage.channelName')" style="font-weight: bold;">
				<Input v-model.trim="searchObj.corpName" :placeholder="$t('resourceManage.enterChannelName')" :clearable="showAllocate == true && cooperationMode != '3'" :disabled="showAllocate == false"
				style="width: 200px" />
			</FormItem>
			<FormItem>
				<Button style="margin: 0 2px" type="primary" @click="search" :loading="searchloading" v-has="'search'" :disabled="showAllocate == false && cooperationMode != '3'">
					<Icon type="ios-search" />&nbsp;{{$t('common.search')}}
				</Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
				<template slot-scope="{ row, index }" slot="action">
					<Button type="info" size="small" style="margin-right: 20px" v-has="'allocateResources'"
						@click="allocateResources(row)">{{$t('resourceManage.allocateResources')}}</Button>
					<Button type="success" size="small" style="margin-right: 20px" v-has="'resourceView'" @click="resourceView(row)">{{$t('resourceManage.resourceView')}}</Button>
					<Button type="warning" size="small" style="margin-right: 20px" v-has="'callOrderDetails'"
						@click="callOrderDetails(row)">{{$t('callOrderDetails')}}</Button>
					<Button type="error" size="small" v-has="'billingStatistics'" @click="billingStatistics(row)">{{$t('channelBillingStatistics')}}</Button>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="currentPage" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0" />
		</div>
		<!-- 分配资源弹窗-->
		<Modal title="分配资源" v-model="allocateModal" :footer-hide="true" :mask-closable="false" width="600px"
			@on-cancel="cancelModal">
			<div style="padding: 0 16px">
				<Form ref="allocateObj" :model="allocateObj" :label-width="190" :rules="ruleValidate">
					<FormItem label="渠道商名称:">
						<p>{{corpName}}</p>
					</FormItem>
					<FormItem label="是否使用供应商IMSI" prop="supportImsi">
						<Select v-model="allocateObj.supportImsi" placeholder="请选择是否支持供应商IMSI" :clearable="true"
							class="inputSty">
							<Option value='true'>是</Option>
							<Option value='false'>否</Option>
						</Select>
					</FormItem>
					<FormItem label="资源供应商" prop="supplierId">
						<Select v-if="allocateObj.supportImsi == 'true'" v-model="allocateObj.supplierId" filterable placeholder="请选择资源供应商" :clearable="true"
							class="inputSty" :disabled="!allocateObj.supportImsi">
							<Option v-for="(item,index) in supplierListYes" :value="item.supplierId"
								:key="item.supplierId">{{ item.supplierName }}</Option>
						</Select>
						<Select v-else v-model="allocateObj.supplierId" filterable placeholder="请选择资源供应商" :clearable="true"
							class="inputSty" :disabled="!allocateObj.supportImsi">
							<Option v-for="(item2,index2) in supplierListNo" :value="item2.supplierId"
								:key="item2.supplierId">{{ item2.supplierName }}</Option>
						</Select>
					</FormItem>
          <FormItem label="流量计费规则" prop="a2zRuleId">
            <Select filterable clearable v-model="allocateObj.a2zRuleId" placeholder="请选择流量计费规则" class="inputSty">
            	<Option v-for="item3 in a2zRuleList" :value="item3.id" :key="item3.id">{{ item3.name }}</Option>
            </Select>
          </FormItem>
          <FormItem label="IMSI费规则" prop="imsiFee">
          	<Select v-model="allocateObj.imsiFee" filterable clearable placeholder="请选择IMSI费规则" class="inputSty">
          		<Option v-for="item2 in imsiFeeList" :value="item2.imsi" :key="item2.imsi">{{ item2.imsiName}}</Option>
          	</Select>
          </FormItem>
          <FormItem label="起始IMSI号" prop="startImsiNumber">
          	<Input placeholder="请输入起始IMSI号" type="number" v-model="allocateObj.startImsiNumber" clearable class="inputSty" />
          </FormItem>
					<FormItem label="IMSI数量" prop="imsiNumber">
						<Input v-model="allocateObj.imsiNumber" :active-change="false" :clearable="true"
							placeholder="请输入IMSI数量" type="number" class="inputSty"></Input>
					</FormItem>
          <FormItem label="是否需要GTP PROXY新增路由" prop="needGtpRouter">
          	<Select v-model="allocateObj.needGtpRouter" filterable clearable placeholder="请选择是否需要GTP PROXY新增路由" class="inputSty">
          		<Option value='true'>是</Option>
          		<Option value='false'>否</Option>
          	</Select>
          </FormItem>
					<FormItem label="路由ID" prop="routingID">
						<Input v-model="allocateObj.routingID" type="number" :clearable="true" placeholder="请输入路由ID"
							class="inputSty"></Input>
					</FormItem>
				</Form>
				<div style="text-align: center">
					<Button @click="cancelModal">返回</Button>
					<Button style="margin-left: 20px" :loading="submitFlag" type="primary" v-has="'submit'" @click="submit">提交</Button>
				</div>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		getList,
		resourceAllocation,
    InfoOrder
	} from "@/api/resource-cooperation.js";
	import {
		supplier,
	} from '@/api/ResourceSupplier'
	import {
		getAttributableChannelList
	} from '@/api/channel.js'
	export default {
		props: ['showAllocate'],
		components: {},
		data() {
			const checkNumber = (rule, value, callback) => {
				if (value > 100000) {
					callback(new Error("超过了最大限制范围100000"));
				} else {
					callback();
				}
			};
      const checkStartImsi = (rule, value, callback) => {
      	let reg = /^[0-9]\d*$/;
        if (value) {
          if (reg.test(value)) {
          	if (value.length > 30) {
          		callback(new Error("请输入1-30位的纯数字"));
          	} else {
          		callback();
          	}
          } else {
          	callback(new Error("请输入1-30位的纯数字"));
          }
        } else {
          callback();
        }
      };
			const checkroutingID = (rule, value, callback) => {
				if (value > **********) {
					callback(new Error("超过了最大限制范围**********"));
				} else {
					callback();
				}
			};

			return {
				cooperationMode: '',
				searchObj: {
					corpName: "", //渠道商名称
				},
				allocateObj: {
					supportImsi: '',
					supplierId: '',
					imsiNumber: '',
          a2zRuleId: '',
          startImsiNumber: '',
          imsiFee: '',
          needGtpRouter: '',
          routingID: '',
				},
				total: 0,
				pageSize: 10,
				page: 1,
				currentPage: 1,
				corpId: "",
				corpName: "",
				loading: false,
				searchloading: false,
				submitFlag: false,
				allocateModal: false, //分配资源弹窗
				tableData: [],
				supplierListYes: [], //资源供应商列表
				supplierListNo: [], //资源供应商列表
        a2zRuleList: [], //流量计费规则列表
        imsiFeeList: [], //imsi费规则列表
				columns: [{
						title: this.$t('resourceManage.channelName'),
						key: "corpName",
						align: "center",
						minWidth: 120,
						tooltip: true
					},
					{
						title: this.$t('support.action'),
						slot: "action",
						minWidth: 500,
						align: "center"
					}
				],
				ruleValidate: {
					supportImsi: [{
						required: true,
						message: "是否使用供应商IMSI不能为空",
						trigger: "blur",
					}],
					supplierId: [{
						required: true,
						message: "资源供应商不能为空",
						trigger: "change",
					}, ],
          a2zRuleId: [{
						required: true,
						message: "流量计费规则不能为空",
						trigger: "change",
					}, ],
          imsiFee: [{
						required: true,
						message: "IMSI费规则不能为空",
						trigger: "change",
					}, ],
          startImsiNumber: [{
          		validator: checkStartImsi,
          		trigger: "blur"
          	}
          ],
					imsiNumber: [{
							required: true,
							message: "IMSI数量不能为空",
							trigger: "blur",
						},
						{
							validator: (rule, value, cb) => {
								var str = /^[1-9]\d*$/;
								return str.test(value);
							},
							message: "请输入正整数",
						},
						{
							validator: checkNumber,
							trigger: "blur"
						}
					],
          needGtpRouter: [{
          	required: true,
          	message: "是否需要GTP PROXY新增路由不能为空",
          }, ],
					routingID: [{
						required: true,
						message: "路由ID不能为空",
						trigger: "blur",
					},
					{
						pattern: /^[1-9]\d*$/,
						message: "请输入正整数",
						trigger: 'blur',
					},
					{
						validator: checkroutingID,
						trigger: "blur"
					}],
				},
			};
		},

		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			if (this.showAllocate == false) {//渠道自服务
				if (this.cooperationMode == '3') {
					this.getChannel()
					this.total = 1
				}
			} else {//客户管理
				this.goPageFirst(1);
			}
		},

		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				getList({
					pageSize: 10,
					pageNumber: page,
					corpName: this.searchObj.corpName,
					corpId: sessionStorage.getItem("corpId")
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},

			//表格数据加载
			loadByPage(page) {
				this.goPageFirst(page);
			},

			//搜索
			search() {
				this.searchloading = true
				this.goPageFirst(1);
			},

			//分配资源弹窗
			allocateResources(row) {
        this.getInfoOrder(row.corpId, row.cooperationMode)
        this.getsupplier()
				this.allocateModal = true
				this.corpName = row.corpName
				this.corpId = row.corpId
			},

			cancelModal() {
				this.allocateModal = false
				this.$refs["allocateObj"].resetFields();
			},

			submit() {
				this.$refs["allocateObj"].validate((valid) => {
					if (valid) {
						var data = this.allocateObj;
						var obj = {
							corpId: this.corpId,
							supplierId: data.supplierId,
							imsiNumber: data.imsiNumber,
							routerId: data.routingID,
							useSupplierImsi: data.supportImsi,
              flowRuleId: data.a2zRuleId,
              imsiRuleId: data.imsiFee,
              startImsiNumber: data.startImsiNumber,
              needGtpRouter: data.needGtpRouter,
						};
						this.submitFlag = true;
						resourceAllocation(obj).then(res => {
							if (res && res.code == '0000') {
								setTimeout(() => {
									this.$Notice.success({
										title: "操作提醒：",
										desc: "操作成功！"
									});
									this.submitFlag = false;
									this.allocateModal = false;
									this.goPageFirst(this.currentPage);
									this.cancelModal()
								}, 1500);
							} else {
								this.submitFlag = false;
								throw res
							}
						}).catch((err) => {
							this.submitFlag = false;
						}).finally(() => {

						})
					}
				})
			},

			//资源查看
			resourceView(row) {
				//判断是客户管理还是渠道自服务
				let pathName = this.showAllocate == true ? "viewResources" : "channelViewResources"
				this.$router.push({
					name: pathName,
					query: {
						corpId: row.corpId,
						corpName: row.corpName,
					},
				});
			},

			//话单明细
			callOrderDetails(row) {
				//判断是客户管理还是渠道自服务
				let pathName = this.showAllocate == true ? "callOrderDetails" : "channelCallOrderDetails"
				this.$router.push({
					name: pathName,
					query: {
						corpId: row.corpId,
						corpName: row.corpName,
					},
				});
			},

			//账单统计
			billingStatistics(row) {
				//判断是客户管理还是渠道自服务
				let pathName = this.showAllocate == true ? "billingStatistics" : "channelBillingStatistics"
				this.$router.push({
					name: pathName,
					query: {
						corpId: row.corpId,
						corpName: row.corpName,
					},
				});
			},


			/** -------------------------------------------------------------*/
			// 获取资源供应商
			getsupplier() {
				supplier({
					pageNum: -1,
					pageSize: -1,
				}).then(res => {
					if (res.code == '0000') {
						var List = []
						res.data.forEach((value, index) => {
							if (value.supplierName == "CMHK") {
								List.push(value)
							}
						})
						this.supplierListNo = List
						this.supplierListYes = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {})
			},

			//获取渠道
			getChannel() {
				getAttributableChannelList({
					corpId: sessionStorage.getItem("corpId"),
					selfContain: true,
				}).then(res => {
					if (res.code === '0000') {
						this.searchObj.corpName = res.data[0].corpName
						this.tableData = [{
							corpName: res.data[0].corpName,
							corpId: res.data[0].corpId
						}]
					}
				}).catch((err) => {
					console.log(err)
				})
			},

      // 获取关联信息
      getInfoOrder(corpId,cooperationMode) {
      	InfoOrder({
          corpId: corpId,
          cooperationMode: '3'
        }).then(res => {
      		if (res.code === '0000') {
            // 直接将对象转换为数组
            this.a2zRuleList = res.data.atzChargings

            this.imsiFeeList = Object.entries(res.data.freeImsiList).map(([imsi, imsiName]) => ({
              imsi,
              imsiName
            }));
      		}
      	}).catch((err) => {
      		console.log(err)
      	})
      },
    },
	};
</script>

<style>
	.inputSty {
		width: 300px;
	}

	/* 去掉input为number的上下箭头 */
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}

	input[type="number"] {
		-moz-appearance: textfield;
	}
</style>
