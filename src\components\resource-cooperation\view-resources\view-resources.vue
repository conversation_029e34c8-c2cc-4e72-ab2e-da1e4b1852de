<template>
	<Card style="width: 100%;padding: 16px;">
		<div class="search_box">
			<span class="search_box_label">{{$t('resourceManage.channelName')}}：&nbsp;&nbsp;</span>
			<p><strong> {{corpName}} </strong></p>
		</div>
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">{{$t('resourceManage.imsiPhone')}}&nbsp;&nbsp;</span>
				<Input v-model.trim='searchObj.imsiNumber' :placeholder="$t('resourceManage.selectVIMSIphone')"
					clearable style="width: 300px;" />
			</div>
			<div class="search_box" v-if="showButton == true">
				<span class="search_box_label">{{$t('resourceManage.resourceSupplier')}}&nbsp;&nbsp;</span>
				<Select v-model="searchObj.supplierId" filterable
					:placeholder="$t('resourceManage.selectResourceSupplier')" :clearable="true" style="width: 300px;">
					<Option v-for="(item,index) in supplierList" :value="item.supplierId" :key="item.supplierId">{{ item.supplierName }}</Option>
				</Select>
			</div>
			<Button class="search_box" type="info" v-preventReClick @click="search" :loading="searchLoading"
				v-has="'search'" :disabled="showButton == false && (cooperationMode == '1' || cooperationMode == '2')">
				<Icon type="ios-search" />&nbsp;{{$t('common.search')}}
			</Button>
			<Button class="search_box" type="error" @click="batchoOperation(1)" v-has="'Batchdelete'">
				<div style="display: flex;align-items: center;">
					<Icon type="ios-trash" />&nbsp;{{$t('flow.Batchdelete')}}
				</div>
			</Button>
			<Button class="search_box" type="primary" @click="batchEdit" v-has="'Batchupdate'" :disabled="showButton == false && (cooperationMode == '1' || cooperationMode == '2')">
				<div style="display: flex;align-items: center;">
					<Icon type="md-create" />&nbsp;{{$t('flow.Batchupdate')}}
				</div>
			</Button>
			<Button class="search_box" type="warning" @click="batchoOperation(2)" v-has="'BatchFreeze'" :disabled="showButton == false && (cooperationMode == '1' || cooperationMode == '2')">
				<div style="display: flex;align-items: center;">
					<Icon type="ios-pause" />&nbsp;{{$t('resourceManage.batchFreeze')}}
				</div>
			</Button>
			<Button class="search_box" type="success" @click="batchoOperation(3)" v-has="'BatchRecovery'" :disabled="showButton == false && (cooperationMode == '1' || cooperationMode == '2')">
				<div style="display: flex;align-items: center;">
					<Icon type="ios-play" />&nbsp;{{$t('resourceManage.BatchRecovery')}}
				</div>
			</Button>
			<Button class="search_box" type="primary" @click="exportFile" :loading="downloading"
				v-has="'export'" :disabled="showButton == false && (cooperationMode == '1' || cooperationMode == '2')">
				<Icon type="ios-cloud-download-outline" />&nbsp;{{$t('stock.exporttb')}}
			</Button>
		</div>
		<div style="margin-top: 20px;">
			<Table ref="selection" :columns="showButton == true ? columns : channelColumns" :data="tableData" :ellipsis="true" :loading="tableLoading"
				@on-selection-change="handleRowChange" @on-select-cancel="cancelPackage"
				@on-select-all-cancel="cancelPackageAll">
				<template slot-scope="{ row, index }" slot="action">
					<Button type="info" ghost style="margin-right: 10px" @click="viewInfo(row)" v-has="'info'"
						:disabled="row.status == '5'">{{$t('resourceManage.seeInformation')}}</Button>
					<Button type="error" ghost style="margin-right: 10px" @click="operation(row,1)" v-has="'del'"
						:disabled="row.status != '5'">{{$t('common.del')}}</Button>
					<Button type="primary" ghost style="margin-right: 10px" @click="updateItem(row)" v-has="'edit'"
						:disabled="row.status == '5'">{{$t('common.edit')}}</Button>
					<Button type="warning" ghost style="margin-right: 10px" @click="operation(row,2)" v-has="'frozen'"
						:disabled="row.status == '5'">{{$t('common.frozen')}}</Button>
					<Button type="success" ghost @click="operation(row,3)" v-has="'recover'"
						:disabled="row.status == '4'">{{$t('flow.recover')}}</Button>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0;" />
		</div>
		<!-- 查看信息弹出框-->
		<Modal :title="$t('resourceManage.seeInformation')" v-model="viewInfoModal" :footer-hide="true"
			:mask-closable="false" width="500px" @on-cancel="cancelModal">
			<div style="padding: 10px 16px;">
				<Form ref="viewInfoObj" :model="viewInfoObj" :label-width="100">
					<FormItem label="imsi">
						<Input v-model="viewInfoObj.imsi" readonly class="inputSty"></Input>
					</FormItem>
					<FormItem label="imsi-mapping">
						<Input v-model="viewInfoObj.imsiMapping" readonly class="inputSty"></Input>
					</FormItem>
					<FormItem label="apn">
						<Input v-model="viewInfoObj.apn" readonly class="inputSty"></Input>
					</FormItem>
					<FormItem label="plmn">
						<Input v-model="viewInfoObj.plmn" readonly class="inputSty"></Input>
					</FormItem>
					<FormItem label="rat-type">
						<Input v-model="viewInfoObj.ratType" readonly class="inputSty"></Input>
					</FormItem>
					<FormItem label="create-time">
						<Input v-model="viewInfoObj.createTime" readonly class="inputSty"></Input>
					</FormItem>
					<FormItem label="sgw-ip-c">
						<Input v-model="viewInfoObj.sgwIpC" readonly class="inputSty"></Input>
					</FormItem>
					<FormItem label="pgw-ip-c">
						<Input v-model="viewInfoObj.pgwIpC" readonly class="inputSty"></Input>
					</FormItem>
					<FormItem label="ue-ip">
						<Input v-model="viewInfoObj.ueIp" readonly class="inputSty"></Input>
					</FormItem>
					<FormItem label="sgw-ip">
						<Input v-model="viewInfoObj.sgwIp" readonly class="inputSty"></Input>
					</FormItem>
					<FormItem label="pgw-ip">
						<Input v-model="viewInfoObj.pgwIp" readonly class="inputSty"></Input>
					</FormItem>
				</Form>
				<div style="text-align: center;margin-top: 30px;">
					<Button type="primary" ghost icon="ios-arrow-back" @click="cancelModal"
						v-preventReClick>{{$t('support.back')}}</Button>
				</div>
			</div>
		</Modal>
		<!-- 修改资源弹窗-->
		<Modal :title="$t('resourceManage.modifyingResources')" v-model="allocateModal" :footer-hide="true"
			:mask-closable="false" width="600px" @on-cancel="cancelModal">
			<div style="padding: 0 16px">
				<Form ref="allocateObj" :model="allocateObj" :label-width="140" :rules="ruleValidate">
					<FormItem :label="$t('resourceManage.channelName')+ ':'">
						<p>{{corpName}}</p>
					</FormItem>
					<!-- <FormItem :label="$t('resourceManage.supportImsi')">
						<Select v-model="allocateObj.supportImsi" :placeholder="$t('resourceManage.selectSupportImsi')" disabled
							class="inputSty">
							<Option value="1">{{$t('order.yes')}}</Option>
							<Option value="2">{{$t('order.no')}}</Option>
						</Select>
					</FormItem>
					<FormItem :label="$t('resourceManage.resourceSupplier')">
						<Select v-model="allocateObj.supplierId" filterable :placeholder="$t('resourceManage.selectResourceSupplier')" disabled
							class="inputSty">
							<Option v-for="(item,index) in supplierList" :value="item.supplierId"
								:key="item.supplierId">{{ item.supplierName }}
							</Option>
						</Select>
					</FormItem>
					<FormItem :label="$t('resourceManage.imsiNumber')">
						<Input v-model="allocateObj.imsiNumber" :active-change="false" readonly :placeholder="$t('resourceManage.selecyImsiNumber')"
							class="inputSty"></Input>
					</FormItem> -->
					<FormItem :label="$t('resourceManage.routingID')" prop="routingID">
						<Input v-model="allocateObj.routingID" type="number" clearable
							:placeholder="$t('resourceManage.selectRoutingID')" class="inputSty"></Input>
					</FormItem>
				</Form>
				<div style="text-align: center">
					<Button @click="cancelModal">{{$t('support.back')}}</Button>
					<Button style="margin-left: 20px" :loading="submitFlag" type="primary" @click="submit"
						v-has="'submit'">{{$t('support.submit')}}</Button>
				</div>
			</div>
		</Modal>
		<!-- 批量修改资源弹窗-->
		<Modal :title="$t('resourceManage.BatchModifyingResources')" v-model="batchAllocateModal" :footer-hide="true"
			:mask-closable="false" width="600px" @on-cancel="cancelModal">
			<div style="padding: 0 16px">
				<Form ref="batchAllocateObj" :model="batchAllocateObj" :label-width="140" :rules="ruleBatchValidate">
					<FormItem :label="$t('resourceManage.routingID')" prop="routingID">
						<Input v-model="batchAllocateObj.routingID" type="number" clearable
							:placeholder="$t('resourceManage.selectRoutingID')" class="inputSty"></Input>
					</FormItem>
				</Form>
				<div style="text-align: center">
					<Button @click="cancelModal">{{$t('support.back')}}</Button>
					<Button style="margin-left: 20px" :loading="batchSubmitLoading" type="primary" @click="batchSubmit"
						v-has="'batchSubmit'">{{$t('support.submit')}}</Button>
				</div>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
					<FormItem :label="$t('exportID')">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem :label="$t('exportFlie')">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">{{$t('downloadResult')}}</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		getResourceList,
		deleteItem,
		freezeItem,
		recoverItem,
		getResourceInfo,
		singleResource,
		batchDelete,
		batchFreeze,
		batchrRecover,
		batchResource,
		exportFile,
		checkImsi
	} from '@/api/resource-cooperation.js';
	import {
		supplier,
	} from '@/api/ResourceSupplier'
	export default {
		props: ['showButton'],
		components: {},
		data() {
			const checkroutingID = (rule, value, callback) => {
				if (value > **********) {
					callback(new Error(this.$t('resourceManage.routingExceeds')));
				} else {
					callback();
				}
			};
			return {
				total: 0,
				pageSize: 10,
				page: 1,
				cooperationMode: '',
				id: '',
				taskId: '',
				taskName: '',
				corpId: '',
				corpName: '',
				searchLoading: false, //删除
				downloading: false, //导出
				tableLoading: false, //列表
				submitFlag: false, //单个修改
				batchSubmitLoading: false, //批量修改
				viewInfoModal: false, //查看信息弹窗
				allocateModal: false, //修改资源
				batchAllocateModal: false, //批量修改资源
				exportModal: false, //导出弹框标识
				searchObj: {
					imsiNumber: '', //IMSI号码
					supplierId: '', //供应商ID
				},
				viewInfoObj: {
					imsi: '',
					imsiMapping: '',
					apn: '',
					plmn: '',
					ratType: '',
					createTime: '',
					sgwIpC: '',
					pgwIpC: '',
					ueIp: '',
					sgwIp: '',
					pgwIp: '',
				},
				allocateObj: {
					supportImsi: '',
					supplierId: '',
					imsiNumber: '',
					routingID: ''
				},
				batchAllocateObj: {
					routingID: ''
				},
				columns: [{
						type: 'selection',
						width: 60,
						align: 'center'
					},
					{
						title: this.$t('resourceManage.imsiPhone'),
						key: 'imsi',
						align: 'center',
						tooltip: true,
						minWidth: 150,
					},
					{
						title: this.$t('resourceManage.resourceSupplier'),
						key: 'supplierName',
						align: 'center',
						tooltip: true,
						minWidth: 120,
					},
					{
						title: this.$t('sys.opt'),
						slot: "action",
						minWidth: 500,
						align: "center"
					}
				],
				channelColumns: [{
						type: 'selection',
						width: 60,
						align: 'center'
					},
					{
						title: this.$t('resourceManage.imsiPhone'),
						key: 'imsi',
						align: 'center',
						tooltip: true,
						minWidth: 150,
					},
					{
						title: this.$t('sys.opt'),
						slot: "action",
						minWidth: 500,
						align: "center"
					}
				],
				tableData: [],
				ruleValidate: {
					routingID: [{
						required: true,
						message: this.$t('resourceManage.routingIDMandatory'),
					},
					{
						pattern: /^[1-9]\d*$/,
						message: this.$t('flow.Pleaseinteger'),
						trigger: 'blur',
					},
					{
						validator: checkroutingID,
						trigger: "blur"
					} ],
				},
				ruleBatchValidate: {
					routingID: [{
						required: true,
						message: this.$t('resourceManage.routingIDMandatory'),
						trigger: "blur",
					},
					{
						pattern: /^[1-9]\d*$/,
						message: this.$t('flow.Pleaseinteger'),
						trigger: 'blur',
					},
					{
						validator: checkroutingID,
						trigger: "blur"
					} ],
				},
				supplierList: [], //资源供应商列表
				selection: [], //选中的数组
				selectionList: [], //翻页勾选List
				imsiList: [],
				idList: [],
			}
		},
		methods: {
			//搜索 表格数据加载
			getPageFirst(page) {
				this.page = page;
				this.tableLoading = true
				var searchCondition = {
					pageSize: 10,
					pageNum: page,
					corpId: this.corpId,
					imsi: this.searchObj.imsiNumber,
					supplierId: this.searchObj.supplierId
				};
				getResourceList(searchCondition).then(res => {
					if (res && res.code == '0000') {
						var data = res.data.records
						var List = []
						data.map((value, index) => {
							List.push(value)
						})
						//回显
						this.selectionList.forEach(item => {
							List.forEach(element => {
								if (element.imsi == item.imsi) {
									this.$set(element, '_checked', true)
								}
							})
						})
						this.total = res.data.total;
						this.tableData = List;
						this.tableLoading = false;
						this.searchLoading = false;
					} else {
						throw res
					}
				}).catch((err) => {
					this.tableLoading = false;
					this.searchLoading = false;
				}).finally(() => {})
			},
			loadByPage(page) {
				this.getPageFirst(page);
			},
			//搜索
			search() {
				this.searchLoading = true
				this.getPageFirst(1);
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					let flag = true;
					this.selectionList.map((item, index1) => {
						if (value.imsi === item.imsi) {
							flag = false
						}
					});
					//判断重复
					if (flag) {
						this.selectionList.push(value);
					}
				});
			},
			//取消选择
			cancelPackage(selection, row) {
				this.selectionList.forEach((value, index) => {
					if (value.imsi === row.imsi) {
						this.selectionList.splice(index, 1);
					}
				})
			},
			//取消全选选择
			cancelPackageAll(selection, row) {
				this.selectionList = []
			},
			//查看信息
			viewInfo(row) {
				this.checkImsi(row.imsi)
				this.viewInfoModal = true
			},
			checkImsi(data) {
				checkImsi({
					imsi: data
				}).then(res => {
					if (res && res.code == '0000') {
						let data = res.data
						this.viewInfoObj = data
						this.viewInfoObj.imsiMapping = data['imsi-mapping']
						this.viewInfoObj.ratType = data['rat-type']
						this.viewInfoObj.createTime = data['create-time']
						this.viewInfoObj.sgwIpC = data['sgw-ip-c']
						this.viewInfoObj.pgwIpC = data['pgw-ip-c']
						this.viewInfoObj.ueIp = data['ue-ip']
						this.viewInfoObj.sgwIp = data['gtp-u']['sgw-ip']
						this.viewInfoObj.pgwIp = data['gtp-u']['pgw-ip']
					} else {
						throw res
					}
				}).catch((err) => {
					console.error()
				}).finally(() => {})
			},
			cancelModal() {
				this.viewInfoObj = {}
				this.viewInfoModal = false
				this.$refs["allocateObj"].resetFields()
				this.allocateModal = false
				this.$refs["batchAllocateObj"].resetFields()
				this.batchAllocateModal = false
				this.exportModal = false
				this.imsiList = []
				this.idList = []
			},
			//type： 1删除  2冻结  3恢复
			operation(row, type) {
				let titleDesc = type == 1 ? this.$t('address.deleteitem') : type == 2 ? this.$t(
					'resourceManage.FreezeItem') : this.$t('resourceManage.RecoverItem')
				let func = type == 1 ? deleteItem : type == 2 ? freezeItem : recoverItem
				this.$Modal.confirm({
					title: titleDesc,
					onOk: () => {
						func({
							imsi: row.imsi
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t('address.Operationreminder'),
									desc: this.$t('common.Successful')
								})
								this.getPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {})
					}
				});
			},
			//批量操作 type： 1删除  2冻结  3恢复
			batchoOperation(type) {
				var len = this.selection.length;
				// let statusList = []
				// var typeFlag;
				//4：使用中  5：已冻结
				// this.selectionList.map((value, index) => {
				// statusList.push(value.status)
				// if (statusList.includes('4')) {
				// 	typeFlag = true
				// } else {
				// 	typeFlag = false
				// }
				// })
				if (len < 1) {
					this.$Message.warning(this.$t('flow.chooserecord'));
					return;
				}
				this.selectionList.forEach((value, index) => {
					this.imsiList.push(value.imsi)
				})
				// else if (typeFlag == false && type == 2) {
				// 	this.$Message.warning('勾选的记录包含已冻结状态！');
				// }  else if (typeFlag == true && type == 3){
				// 	this.$Message.warning('勾选的记录包含使用中状态！');
				// } else {
				let titleDesc = type == 1 ? this.$t('flow.Confirmdelete') + '?' : type == 2 ? this.$t(
					'resourceManage.confirmFreeze') : this.$t('resourceManage.confirmRecover')
				let method = type == 1 ? batchDelete : type == 2 ? batchFreeze : batchrRecover
				this.$Modal.confirm({
					title: titleDesc,
					onOk: () => {
						method({
								imsiList: this.imsiList
							}).then((res) => {
								if (res.code === "0000") {
									this.$Notice.success({
										title: this.$t('address.Operationreminder'),
										desc: this.$t('common.Successful'),
									});
									this.selection = [];
									this.imsiList = [];
									this.selectionList = []
									// statusList = [];
									this.getPageFirst(1);
								}
							})
							.catch((err) => {
								this.$Notice.error({
									title: this.$t('address.Operationreminder'),
									desc: this.$t('resourceManage.operationFail'),
								});
								
							}).finally(() => {
							})
					},
				});
				// }
			},
			//修改
			updateItem(row) {
				this.id = row.id
				this.allocateObj.routingID = row.routeId
				// this.resourceInfo(row)
				this.allocateModal = true
			},
			//获取分配资源信息
			resourceInfo(row) {
				getResourceInfo({
					"corpId": row.corpId,
					"imsiNumber": row.imsi,
					"routerId": row.routeId,
					"supplierId": row.supplierId,
					"useSupplierImsi": true
				}).then(res => {
					if (res && res.code == '0000') {
						this.allocateObj = res.data
					} else {
						throw res
					}
				}).catch((err) => {
					console.error()
				}).finally(() => {})
			},
			submit() {
				this.$refs["allocateObj"].validate((valid) => {
					if (valid) {
						var data = this.allocateObj;
						var obj = {
							id: this.id,
							routerId: data.routingID
						};
						this.submitFlag = true;
						singleResource(obj).then(res => {
							if (res && res.code == '0000') {
								setTimeout(() => {
									this.$Notice.success({
										title: this.$t('address.Operationreminder'),
										desc: this.$t('common.Successful'),
									});
									this.submitFlag = false;
									this.allocateModal = false;
									this.getPageFirst(1);
								}, 1500);
							} else {
								this.submitFlag = false;
								throw res
							}
						}).catch((err) => {
							this.submitFlag = false;
						}).finally(() => {

						})
					}
				})
			},
			//批量修改
			batchEdit() {
				var len = this.selection.length;
				if (len < 1) {
					this.$Message.warning(this.$t('flow.chooserecord'));
					return;
				}
				this.selectionList.forEach((value, index) => {
					this.idList.push(value.id)
				})
				this.batchAllocateModal = true
			},
			batchSubmit() {
				this.$refs["batchAllocateObj"].validate((valid) => {
					if (valid) {
						var data = this.batchAllocateObj;
						var obj = {
							list: this.idList,
							routerId: data.routingID
						};
						this.batchSubmitLoading = true;
						batchResource(obj).then(res => {
							if (res && res.code == '0000') {
								setTimeout(() => {
									this.$Notice.success({
										title: this.$t('address.Operationreminder'),
										desc: this.$t('common.Successful'),
									});
									this.selection = [];
									this.idList = [];
									this.selectionList = [];
									this.batchAllocateObj.routingID = '';
									this.batchSubmitLoading = false;
									this.batchAllocateModal = false;
									this.getPageFirst(1);
								}, 1500);
							} else {
								this.batchSubmitLoading = false;
								throw res
							}
						}).catch((err) => {
							this.batchSubmitLoading = false;
						}).finally(() => {

						})
					}
				})
			},
			//导出
			exportFile() {
				this.downloading = true
				exportFile({
					corpId: this.corpId,
					corpName: this.corpName,
					imsi: this.searchObj.imsiNumber,
					supplierId: this.searchObj.supplierId,
					userId: this.showButton == true ? this.$store.state.user.userId : this.corpId,
					pageNum: this.showButton == true ? '1' : '2'
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.id
					this.taskName = res.data.fileName
					this.downloading = false
				}).catch(() => this.downloading = false)
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportModal = false
			},
			/** -------------------------------------------------------------*/
			// 获取资源供应商
			getsupplier() {
				supplier({
					pageNum: -1,
					pageSize: -1,
				}).then(res => {
					if (res.code == '0000') {
						this.supplierList = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {})
			},
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			this.corpId = this.$route.query.corpId
			this.corpName = this.$route.query.corpName
			if(this.cooperationMode) {
				//渠道自服务且合作模式为3
				if(this.cooperationMode == '3') {
					this.getPageFirst(1)
				}
			} else { 
				//客户管理
				this.getsupplier()
				this.getPageFirst(1)
			}
		}
	}
</script>

<style>
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box_label {
		font-weight: bold;
	}

	.search_box {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin: 0 20px 20px 0;
		flex-direction: row;
	}

	.inputSty {
		width: 300px;
	}

	/* 去掉input为number的上下箭头 */
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}

	input[type="number"] {
		-moz-appearance: textfield;
	}
</style>