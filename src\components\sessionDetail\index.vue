<template>
  <div class="session-detail">
    <div class="session-container">
      <!-- 左侧实时会话信息 -->
      <div class="session-section left-section">
        <div class="section-header">
          <div class="section-title">{{ $t('sessionInfo.realtimeSession') }}</div>
        </div>
        <div class="table-container">
          <Table :columns="realtimeColumns" :data="realtimeData" :loading="realtimeLoading" max-height="500"></Table>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="divider"></div>

      <!-- 右侧历史会话信息 -->
      <div class="session-section right-section">
        <div class="section-header">
          <div class="section-title">{{ $t('sessionInfo.historySession') }}</div>
          <Button type="primary" @click="exportHistory" :loading="exportLoading">
            {{ $t('stock.exporttb') }}
          </Button>
        </div>
        <div class="table-container">
          <Table :columns="historyColumns" :data="historyData" :loading="historyLoading" max-height="500"></Table>
        </div>
      </div>
    </div>
    <div class="footer">
      <Button @click="handleClose">{{ $t('support.back') }}</Button>
    </div>
    <a ref="downloadLink" style="display: none"></a>
  </div>
</template>

<script>
import { getRealtimeSession, getHistorySession, exportHistorySession } from '@/api/server/session'

export default {
  name: 'SessionDetail',
  props: {
    imsi: {
      type: String,
      required: true
    },
    iccid: {
      type: String,
      required: true
    },
    corpId: {
      type: String,
      required: false
    },
  },
  data() {
    return {
      realtimeLoading: false,
      historyLoading: false,
      exportLoading: false,
      realtimeData: [],
      historyData: [],
      remarks: {
        online: this.$t('sessionInfo.online'),
        startTime: this.$t('sessionInfo.sessionStartTime'),
        usedFlow: this.$t('sessionInfo.dataUsageDuringSession'),
        operatorName: this.$t('sessionInfo.serviceProviderUsed'),
        ip: this.$t('sessionInfo.userIPAddress'),
        apn: 'APN',
        ratType: this.$t('support.network'),
        id: this.$t('sessionInfo.PGWSiteAccessedSession'),
      },
      realtimeColumns: [
        {
          title: this.$t('sessionInfo.field'),
          key: 'field',
          tooltip: true,
          minWidth: 110
        },
        {
          title: this.$t('flow.remark'),
          key: 'remark',
          tooltip: true,
          minWidth: 140
        },
        {
          title: this.$t('sessionInfo.currentVlaue'),
          key: 'value',
          tooltip: true,
          minWidth: 120
        },
      ],
      historyColumns: [
        {
          title: "Start Time",
          key: 'startTime',
          tooltip: true,
          minWidth: 160
        },
        {
          title: "End Time",
          key: 'endTime',
          tooltip: true,
          minWidth: 160
        },
        {
          title: "Flow Uplink",
          key: 'flowUplink',
          tooltip: true,
          minWidth: 120
        },
        {
          title: "Flow Downlink",
          key: 'flowDownlink',
          tooltip: true,
          minWidth: 120
        },
        {
          title: "Flow Total",
          key: 'flowTotal',
          tooltip: true,
          minWidth: 120
        },
        {
          title: "APN",
          key: 'apn',
          tooltip: true,
          minWidth: 100
        },
        {
          title: "MCC",
          key: 'mcc',
          tooltip: true,
          minWidth: 100
        },
        {
          title: this.$t('sessionInfo.accessSite'),
          key: 'id',
          tooltip: true,
          minWidth: 120
        }
      ]
    }
  },
  methods: {
    async fetchRealtimeData() {
      this.realtimeLoading = true
      try {
        const res = await getRealtimeSession(
          {
            imsi: this.imsi,
          }
        )
        if (res && res.code === '0000') {
          if (res.data && typeof res.data === 'object' && Object.keys(res.data).length > 0) {

            let online = res.data.online == true ? this.$t('order.yes') : res.data.online == false ? this.$t('order.no') : ''
            let ratType = res.data.ratType == "1" ? '2G' : res.data.ratType == "2" ? '3G' : res.data.ratType == "3" ? '4G' : res.data.ratType == "4" ? 'Wifi' : ''

            this.realtimeData = [
              { field: 'Online', remark: this.remarks.online, value: online },
              { field: 'Start Time', remark: this.remarks.startTime, value: res.data.startTime },
              { field: 'Used Flow', remark: this.remarks.usedFlow, value: res.data.usedFlow },
              { field: 'Operator Name', remark: this.remarks.operatorName, value: res.data.operatorName },
              { field: 'IP', remark: this.remarks.ip, value: res.data.ip },
              { field: 'APN', remark: this.remarks.apn, value: res.data.apn },
              { field: 'Rat Type', remark: this.remarks.ratType, value: ratType },
              { field: this.$t('sessionInfo.accessSite'), remark: this.remarks.id, value: res.data.id }
            ];

          } else {
            this.realtimeData = [];
          }
        } else {
          this.realtimeData = []
        }
      } catch (error) {
        console.error(error)
        this.realtimeData = []
      } finally {
        this.realtimeLoading = false
      }
    },
    async fetchHistoryData() {
      this.historyLoading = true
      try {
        const res = await getHistorySession({ iccid: this.iccid })
        if (res && res.code === '0000') {
          this.historyData = res.data || []
        }
      } catch (error) {
        console.error(error)
      } finally {
        this.historyLoading = false
      }
    },
    async exportHistory() {
      this.exportLoading = true
      exportHistorySession({
        iccid: this.iccid
      })
      .then((res) => {
        const content = res.data;
        let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[1]);
        if ('download' in document.createElement('a')) {
          const link = this.$refs.downloadLink;
          let url = URL.createObjectURL(content);
          link.download = fileName;
          link.href = url;
          link.click();
          URL.revokeObjectURL(url);
        } else {
          navigator.msSaveBlob(content, fileName);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        this.exportLoading = false;
      });
    },
    handleClose() {
      this.$emit('close')
    }
  },
  created() {
    this.fetchRealtimeData()
    this.fetchHistoryData()
  }
}
</script>

<style scoped>
.session-detail {
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.session-container {
  display: flex;
  flex: 1;
  min-height: 0;
}

.session-section {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.left-section {
  width: 30%;
  min-width: 300px;
}

.right-section {
  width: 70%;
  min-width: 700px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  height: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
}

.table-container {
  flex: 1;
  overflow: auto;
  height: 600px;
  min-width: 0;
}

.divider {
  width: 1px;
  background-color: #e8e8e8;
  margin: 0 20px;
  height: 100%;
}

.footer {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 自定义滚动条样式 */
.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
</style>
