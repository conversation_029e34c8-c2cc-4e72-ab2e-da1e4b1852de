<template>
	<Table
	  border 
	  :columns="packageSelectColumns"
	  :data="packageSelectTableData"
	  :ellipsis="true"
	  :loading="loading"
	  @on-select="selectPackage"
	  @on-select-cancel="cancelPackage"
	  @on-select-all="selectPackage"
	  @on-select-all-cancel="cancelPackageAll"
	></Table>
</template>

<script>
	export default{
		// props:{
		// 	packageSelectTableData:[],
		// 	editObj:{}
		// },
		props: {
			row: Object,
			editObj:Object,
			tempSelectDataArrzj:[]
		},
		data(){
			return{
				loading: false,
				tempSelectDataArr: [],
				packageSelectColumns: [
				  {
				    type: "selection",
				    width: 60,
				    align: "center",
				  },
				  {
				    title: "加油包名称",
				    key: "nameCn",
				    align: "center",
				  },
				 {
				   title: "人民币价格",
				   key: "cny",
				   width: 150,
				   align: "center",
				   render: (h, { row, column, index }) => {
				     if (this.editObj.groupType === "1") {
				       let cancelSection = () => {
				         if (this.tempSelectDataArrzj.length &&this.tempSelectDataArrzj[0].id===this.row.id) {
							   let flag = false;
							   this.tempSelectDataArrzj.forEach((ele) => {
								 if (ele.id === this.packageSelectTableData[index].id) {
								   flag = true;
								   ele.cny = this.packageSelectTableData[index].cny;
								   ele.hkd = this.packageSelectTableData[index].hkd;
								   ele.usd = this.packageSelectTableData[index].usd;
								 }
							   });
							   if (!flag) {
								 this.tempSelectDataArrzj.push(
								   this.packageSelectTableData[index]
								 );
							   }
							 } else {
							   this.tempSelectDataArrzj.push(
								 this.packageSelectTableData[index]
							   );
							 }
							 this.editObj.packages.forEach((ele) => {
								if (ele.packageId === this.row.id) {
								  ele.refuelDetailList.forEach((item)=>{
									if(item.refuelId===this.packageSelectTableData[index].id){
										item.cny = this.packageSelectTableData[index].cny
										this.$nextTick(() => {
										  if (document.querySelector(".errorPrice")) {
											this.twicePriceFormat = true;
										  } else {
											this.twicePriceFormat = false;
										  }
										});
									}
								  })
								}
							 });
							 this.$set(this.tempSelectDataArrzj, "tempSelectDataArrzj", this.tempSelectDataArrzj);
				       };
				   				 				
				       this.editObj.packages.forEach((ele) => {
				         if (ele.packageId === this.packageSelectTableData[index].id) {
				           this.packageSelectTableData[index]._checked = true;
				         }
				       });
				   				 				
				       return (
				         <div>
				           <Input
				             maxlength="13"
				             v-model={this.packageSelectTableData[index].cny}
				             v-on:on-change={cancelSection}
				             placeholder="请输入二次定价价格"/>
				           {/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(this.packageSelectTableData[index].cny ) ? null : (
				             <p class="errorPrice" style="text-align: left;color:red">二次定价格最高支持10位整数和2位小数正数或零</p>
				           )}
						   </div>
				       );
				     }else {
				       return <span>{row.cny}</span>;
				     }
				   },
				 },
				 {
				   title: "港币价格",
				   key: "hkd",
				   width: 150,
				   align: "center",
				   render: (h, { row, column, index }) => {
				     if (this.editObj.groupType === "1") {
				       let cancelSection = () => {
				         if (this.tempSelectDataArrzj.length && this.tempSelectDataArrzj[0].id===this.row.id) {
				           let flag = false;
				           this.tempSelectDataArrzj.forEach((ele) => {
				             if (ele.id === this.packageSelectTableData[index].id) {
				               flag = true;
				               ele.cny = this.packageSelectTableData[index].cny;
				               ele.hkd = this.packageSelectTableData[index].hkd;
				               ele.usd = this.packageSelectTableData[index].usd;
				             }
				           });
				 
				           if (!flag) {
				             this.tempSelectDataArrzj.push(
				               this.packageSelectTableData[index]
				             );
				           }
				         } else {
				           this.tempSelectDataArrzj.push(
				             this.packageSelectTableData[index]
				           );
				         }
				         this.editObj.packages.forEach((ele) => {
				           if (ele.packageId === this.row.id) {
							 ele.refuelDetailList.forEach((item)=>{
							 	if(item.refuelId===this.packageSelectTableData[index].id){
							 		item.hkd = this.packageSelectTableData[index].hkd
							 	}
							 })
				           }
				         });
				         this.$nextTick(() => {
				           if (document.querySelector(".errorPrice")) {
				             this.twicePriceFormat = true;
				           } else {
				             this.twicePriceFormat = false;
				           }
				         });
						 this.$set(this.tempSelectDataArrzj, "tempSelectDataArrzj", this.tempSelectDataArrzj);
				       };
				 
				       this.editObj.packages.forEach((ele) => {
				         if (ele.packageId === this.packageSelectTableData[index].id) {
				           this.packageSelectTableData[index]._checked = true;
				         }
				       });
				       return (
				         <div>
				           <Input
				             v-model={this.packageSelectTableData[index].hkd}
				             v-on:on-change={cancelSection}
				             placeholder="请输入二次定价价格"
				           />
				 
				           {/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(
				             this.packageSelectTableData[index].hkd
				           ) ? null : (
				             <p class="errorPrice" style="text-align: left;color:red">
				               二次定价格最高支持10位整数和2位小数正数或零
				             </p>
				           )}
				         </div>
				       );
				     } else {
				       return <span>{row.hkd}</span>;
				     }
				   },
				 },
				 {
				   title: "美元价格",
				   key: "usd",
				   width: 150,
				   align: "center",
				   render: (h, { row, column, index }) => {
				     if (this.editObj.groupType === "1") {
				       let cancelSection = () => {
				         if (this.tempSelectDataArrzj.length && this.tempSelectDataArrzj[0].id===this.row.id) {
				           let flag = false;
				           this.tempSelectDataArrzj.forEach((ele) => {
				             if (ele.id === this.packageSelectTableData[index].id) {
				               flag = true;
				               ele.cny = this.packageSelectTableData[index].cny;
				               ele.hkd = this.packageSelectTableData[index].hkd;
				               ele.usd = this.packageSelectTableData[index].usd;
				             }
				           });
				           if (!flag) {
				             this.tempSelectDataArrzj.push(
				               this.packageSelectTableData[index]
				             );
				           }
				         } else {
				           this.tempSelectDataArrzj.push(
				             this.packageSelectTableData[index]
				           );
				         }
				         this.editObj.packages.forEach((ele) => {
				           if (ele.packageId === this.row.id) {
							 ele.refuelDetailList.forEach((item)=>{
							 	if(item.refuelId===this.packageSelectTableData[index].id){
							 		item.usd = this.packageSelectTableData[index].usd
							 		this.$nextTick(() => {
							 		  if (document.querySelector(".errorPrice")) {
							 		    this.twicePriceFormat = true;
							 		  } else {
							 		    this.twicePriceFormat = false;
							 		  }
							 		});
							 	}
							 })
				           }
				         });
						 this.$set(this.tempSelectDataArrzj, "tempSelectDataArrzj", this.tempSelectDataArrzj);
				       };
				 
				       this.editObj.packages.forEach((ele) => {
				         if (ele.packageId === this.packageSelectTableData[index].id) {
				           this.packageSelectTableData[index]._checked = true;
				         }
				       });
				       return (
				         <div>
				           <Input
				             v-model={this.packageSelectTableData[index].usd}
				             v-on:on-change={cancelSection}
				             placeholder="请输入二次定价价格"
				           />
				           {/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(
				             this.packageSelectTableData[index].usd
				           ) ? null : (
				             <p class="errorPrice" style="text-align: left;color:red">
				               二次定价格最高支持10位整数和2位小数正数或零
				             </p>
				           )}
				         </div>
				       );
				     } else {
				       return <span>{row.usd}</span>;
				     }
				   },
				 },
				],
				packageSelectTableData:[]
			}
		},
		mounted(){
			    this.packageSelectTableData=this.row.refuelList
			    this.packageSelectTableData.forEach(ele=>{
			   	  ele.packageId=this.row.id
			    })
				console.log(this.tempSelectDataArrzj);
			   if (this.tempSelectDataArrzj.length>0){
			     this.tempSelectDataArrzj.forEach((ele) => {
			       this.packageSelectTableData.forEach((element1) => {
			         if (element1.id === ele.id && ele.packageId===this.row.id) {
			           element1.cny = ele.cny;
			           element1.hkd = ele.hkd;
			           element1.usd = ele.usd;
			         }
			       });
			     });
			   }
				 this.packageSelectTableData.forEach((element1)=>{
				 	element1._disabled = true;
				 })
			    //回显
				if (this.editObj.packages && this.editObj.packages.length) {
				  this.editObj.packages.forEach((element, index) => {
					if(this.row.id===element.packageId){
						this.packageSelectTableData.forEach((element1)=>{
							element1["_checked"] = true;
						})
					}  
				  });
				}
				
		},
		methods:{
			// 新增选择套餐包
			    selectPackage(selection, row) {
					selection.forEach((select,index)=>{
					  // 获取对应的索引
					  let _index = this.packageSelectTableData.findIndex(ele=>{return ele.id ==select.id})
						selection[index]._index = _index;
					})
					// 选中并传值
					let arr = selection.map((ele) => {
						this.packageSelectTableData[ele._index]._checked = true;
						return {
						  refuelId: ele.id,
						  cny: ele.cny, 
						  hkd: ele.hkd,
						  usd: ele.usd,
						};
					});
				    if (this.editObj.refuelDetailList && this.editObj.refuelDetailList.length) {
					  let tempArr = [];
			
					  arr.forEach((item) => {
					    let flag = true;
					    this.editObj.refuelDetailList.forEach((ele) => {
						  if (ele.packageId === item.packageId) {
						  flag = false;
						}
					  });
					  if (flag) {
						//判断是否重复
						tempArr.push(item);
					  }
					});
					this.editObj.refuelDetailList = this.editObj.refuelDetailList.concat(tempArr);
				  } else {  
					this.$set(this.editObj.packages, "refuelDetailList", arr);
					this.$set(this.editObj.packages, "packages", arr);
				  }
			    },
				// 取消选择套餐包
				cancelPackage(selection, row) {
				  if (this.editObj.refuelDetailList && this.editObj.refuelDetailList.length) {
				    this.editObj.refuelDetailList = this.editObj.refuelDetailList.filter((ele) => {
				      return ele.packageId !== row.id;
				    });
				  }
				
				  this.packageSelectTableData.forEach((ele) => {
				    if (ele.id === row.id) {
				      ele._checked = false;
				    }
				  });
				},
				// 取消选择套餐包
				cancelPackageAll(selection, row) {
				  if (this.editObj.refuelDetailList && this.editObj.refuelDetailList.length) {
				    this.editObj.refuelDetailList = [];
				  }
				
				  this.packageSelectTableData.forEach((ele) => {
				    ele._checked = false;
				  });
				},
		},
		created(){
			
		}
	}
</script>

<style>
</style>
