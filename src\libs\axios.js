import axios from 'axios'
import store from '@/store'
import {
  Notice
} from 'iview'
import router from '@/router'
import i18n from '@/locale/index'
// const addErrorLog = errorInfo => {
//   const {
//     statusText,
//     status,
//     request: {
//       responseURL
//     }
//   } = errorInfo
//   let info = {
//     type: 'ajax',
//     code: status,
//     mes: statusText,
//     url: responseURL
//   }
//   if (!responseURL.includes('save_error_logger')) store.dispatch('addErrorLog', info)
// }
const handleErrorCode = (result) => {
  if (result.code === '4000') {
    router.push({
      name: 'authenError'
    })
    return
  }
  if (result.code === '4001' || result.code === '4002') {
    //token校验失败，跳转到登录页面
    store.dispatch('handleLogOut', false).then(() => {
      router.push({
        name: 'login'
      })
    })
    setTimeout(() => {
      Notice.error({
        title: i18n.t("common.Error"),
        desc: i18n.t("common.token")
      })
    }, 60000)
  } else {
    // 全局错误提示
    Notice.error({
      title: i18n.t("common.Error"),
      desc: result.msg
    })
  }
}
class HttpRequest {
  constructor(baseUrl = baseURL) {
    this.baseUrl = baseUrl
    this.queue = {}
  }

  getInsideConfig() {
    const config = {
      baseURL: this.baseUrl,
      headers: {
        //
      }
    }
    return config
  }

  destroy(url) {
    delete this.queue[url]
    if (!Object.keys(this.queue).length) {
      // Spin.hide()
    }
  }

  interceptors(instance, url) {
    // 请求拦截
    instance.interceptors.request.use(config => {	
      if (store.state.user.token) { // 判断是否存在token，如果存在的话，则每个http header都加上token
			var token = store.state.user.token;
			var userId = store.state.user.userId
			//TODO
			// config.headers.Authorization = `${token}`;
			// config.headers.userId = `${userId}`;	
			if(config.params != false){ 
				//如果istoken是false，Authorization为空；不为false，则带上token
				config.headers.Authorization = `bearer ${token}`;
			}else{
				// 清空下发短信接口的token
				store.state.user.token=""
			}							
      }
      config.headers.userName =encodeURIComponent(store.state.user.userName,'utf-8') ;
      this.queue[url] = true
      return config
    }, error => {
      Notice.error({
        title: i18n.t("common.Error"),
        desc: i18n.t("common.Request")
      })
      return Promise.reject(error)
    })
    // 响应拦截
    instance.interceptors.response.use(res => {
      this.destroy(url)
      if (res.request.responseType === 'blob') {
        if (res.headers['content-type'] === 'application/json;charset=UTF-8') {
          // 处理文件下载失败，返回错误信息的情况
          let reader = new FileReader()
          reader.onload = e => {
            if (e.target.readyState === FileReader.DONE) {
              let result = JSON.parse(e.target.result)
              if (result.code !== '0000') {
                handleErrorCode(result)
              }
            }
          }
          reader.readAsText(res.data)
          return
        }
        return res
      }
      const resData = res.data
      if (resData.code === '0000') {
        return resData
      }
      handleErrorCode(resData)
      return Promise.reject(resData)
    }, error => {
      let desc
      if (error && error.response) {
        switch (error.response.status) {
          case 401:
            store.dispatch('handleLogOut', false).then(() => {
              router.push({
                name: 'login'
              })
            })
            desc = i18n.t("common.token")
            break
          case 402:
            store.dispatch('handleLogOut', false).then(() => {
              router.push({
                name: 'login'
              })
            })
            desc =i18n.t("common.token")
            break
          case 400:
            desc = error.response.data.msg
            break
          case 404:
            desc =i18n.t("common.errorcode")
            break
          case 408:
            desc = i18n.t("common.timedout")
            break
          case 500:
            desc = i18n.t("sys.Serverwrong")
            break
          case 503:
            desc = i18n.t("common.feature")
            break
          default:
            desc = i18n.t("common.Servererror")
        }
      }
      if (error.code == 'ECONNABORTED' && error.message.indexOf('timeout') != -1) {
        desc =  i18n.t("common.Requestout")
      }
      Notice.error({
        title: i18n.t("common.Error"),
        desc: desc || i18n.t("common.Servererror")
      })
      this.destroy(url)
      return Promise.reject(error)
    })
  }

  request(options) {
    const instance = axios.create()
    options = Object.assign(this.getInsideConfig(), options)
    this.interceptors(instance, options.url)
    return instance(options)
  }
}
export default HttpRequest
