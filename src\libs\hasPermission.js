const hasPermission = {
  install(Vue, options) {
    Vue.directive('has', {
      inserted(el, binding, vnode) {

        let permTypes = vnode.context.$route.meta.permTypes
        //包含A或者包含B
        if (Object.prototype.toString.call(binding.value) == '[object Array]') {
          var k = 0
          for (var n = 0; n < binding.value.length; n++) {
            if (permTypes.includes(binding.value[n])) {
              return
            }
          }
          el.parentNode.removeChild(el)
        }
        //包含A但是不包含B,或者包含A中所有选项
        if (Object.prototype.toString.call(binding.value) == '[object Object]') {
          var haveArr = binding.value.have
          var noArr = binding.value.haveNot
          // v-has="{'have':['A1','A2']}"
          if (haveArr && haveArr !== undefined) {
            //不包括必含权限，直接删除标签节点
            for (var m = 0; m < haveArr.length; m++) {
              if (!permTypes.includes(haveArr[m])) {
                el.parentNode.removeChild(el)
                return
              }
            }
          }
          // v-has="{'have':['A'],'haveNot':['B']}"
          if (noArr && noArr !== undefined) {
            //不包括必含权限，直接删除标签节点
            for (var m = 0; m < noArr.length; m++) {
              if (permTypes.includes(noArr[m])) {
                el.parentNode.removeChild(el)
                return
              }
            }
          }
        }
        //包含A
        // v-has="'A'"
        if (Object.prototype.toString.call(binding.value) == '[object String]') {
          if (!permTypes.includes(binding.value)) {
            el.parentNode.removeChild(el)
          }
        }
      }
    })
  }
}

export default hasPermission
