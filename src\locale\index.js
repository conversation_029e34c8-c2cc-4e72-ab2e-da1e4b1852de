import Vue from 'vue'
import VueI18n from 'vue-i18n'
import { localRead } from '@/libs/util'
import customZhCn from './lang/zh-CN'
import customEnUs from './lang/en-US'
import zhCnLocale from 'iview/src/locale/lang/zh-CN'
import enUsLocale from 'iview/src/locale/lang/en-US'
import zhTwLocale from 'iview/src/locale/lang/zh-TW'

Vue.use(VueI18n)

const sysLang = navigator.language.indexOf('en') === 0 ? 'en-US': navigator.language.indexOf('zh') === 0 ? 'zh-CN' : false
const navLang = localStorage.getItem('local')
const localLang = (navLang === 'zh-CN' || navLang === 'en-US') ? navLang : false
//优先localstorage，其次浏览器语言，最后默认
// let lang = localLang || sysLang || 'zh-CN'
//优先localstorage，其次默认英文
let lang = localLang || 'en-US'

Vue.config.lang = lang

// vue-i18n 6.x+写法
Vue.locale = () => {}
const messages = {
  'zh-CN': Object.assign(zhCnLocale, customZhCn),
  'en-US': Object.assign(enUsLocale, customEnUs)
}

const i18n = new VueI18n({
  locale: lang,
  messages,
  silentTranslationWarn: true
})
export default i18n

// vue-i18n 5.x写法
// Vue.locale('zh-CN', Object.assign(zhCnLocale, customZhCn))
// Vue.locale('en-US', Object.assign(zhTwLocale, customZhTw))
// Vue.locale('zh-TW', Object.assign(enUsLocale, customEnUs))
