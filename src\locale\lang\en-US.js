export default {
	home: 'Home',
	login: 'Login',
  spinLoading: "Loading",
	//系统管理
	system_mngr: 'System Management',
	account_list: 'Account Management',
	log_mngr: 'Operation Log',
	Announcement_mngr: 'Announcement Management',
	lockModeApproval_mngr: 'Golden Lock Mode Management',
	login_mngr: 'Log In Log',
	pwd_mngr: 'Password Management',
	pri_mngr: 'Role Management',
	sys: {
		wholesalerName: 'Please Enter Account Number...',
		roleType: 'Please Select Role...',
		add: 'New Account',
		account: 'User Name:',
		paccount: 'Please Enter User Name...',
		oldPwd: 'Original Password:',
		poldPwd: 'Please Enter The Original Password...',
		newPwd: 'New Password:',
		pnewPwd: 'Please Enter A New Password...',
		rePwd: 'Confirm Password:',
		prePwd: 'Please Confirm The Password...',
		pwdNullMsg: 'Password Cannot Be Empty',
		pwdLengthMsh: 'Length Cannot Be Less Than 6 Bits',
		accountNummMsg: 'Account Name Cannot Be Empty',
		logInto: 'Login',
		logOut: 'Log Out',
		optAccoount: 'Operation Account',
		optType: 'Operation Type',
		serviceUrl: 'Server Address',
		optTime: 'Operation Time',
		accountT: 'Account',
		role: 'User Role',
		status: 'User State',
		opt: 'Operation',
		accountempty:'The Account Name Cannot Be Empty',
		newpassword:'Enter A New Password',
		oldpassword:'Enter The Original Password',
		Enteraccount:'Enter Account',
		successfully:'The Users Basic Information Has Been Successfully Modified, Please Log In Again.',
		wrong:'Something Went Wrong',
		Serverwrong:'Server Internal Error',
		smsCode:'Please Enter A 6-digit Verification Code',
		sendSmsCode:'Verification Code Sent Successfully',
		enterEmail: 'Enter email:',
		inputEmail: 'Please input your email',
		setPassword: 'set password:',
		enterPassword: 'Please enter password...',
		phone: 'cellphone number',
		enterPhone: 'Please enter the phone number...',
		pwNotMatch: 'The two passwords entered do not match. Please check carefully.',
		inputName: 'Please enter login name first!',
		codeSendPhone: 'Verification code has already sent to your phone/email, please check.',
		second: 'Second',
		inputTwoPw: 'The two passwords entered do not match.',
		sendVercode: 'Verification code sent',
		persona: 'Role',
		exitModifyPassword: 'Exit to change password',
		userNameEmpty: 'User name is mandatory',
		pleaseSetRoles: 'Please select the user role',
		phoneNoEmpty: 'Mobile number is mandatory',
		onlyNumbers: 'Only support numbers',
		userNameSpaces: 'Users name contains space',
    userNameAmpersand: "Login name can't include symbol '&'",
		twentyDigits: 'Maximum 20 characters supported',
		passwordsNotMatch: 'The passwords entered are inconsistent, please re-enter',
		successAddedUser: 'Added users successfully!',
		userInforupdated: 'User information has been updated',
		successDeleted: 'User deleted successfully',
		operationWill: 'Current operation',
		user: 'Users',
		continueExecution: ',Continue process?',
		userStatusUpdated: 'User status updated!',
		confirmDeleteUser: 'Please confirm delete the selected user',
		obtainPermissionData: 'Extracting permission data, please wait',
		Saving: 'Saving, please wait',
		roleName: 'Role name',
		newRole: 'Add role',
		enterName: 'Enter role name',
		viewPermissions: 'View permissions',
		rolePermissionManagement: 'Role permission management',
		editAccount: 'Edit',
		characterSpaces: 'Role name contains space',
		purview: 'Permission',
		successAddedRole: 'Role added successfully',
		failedObtainRole: 'Failed to obtain user permission, please try again!',
		rolePpermissionsUpdated: 'Role permission updated',
	},


	common: {
		welcome: 'Hello ',
		edit: 'Modify',
		frozen: 'Frozen',
		thaw: 'Thaw',
		del: 'Delete',
		search: 'Search',
		timeSection: 'Select Time Period',
		beginTime: 'Start Time',
		endTime: 'End Time',
		optResult: 'Results',
		optType: 'please Select An Operation Type',
		success: 'Success',
		failure: 'Failure',
		logOut: 'Log Out',
		rePwd: 'Confirm Password',
		determine: 'Confirm',
		cancel: 'Cancel',
		Error:'Error Message',
		token:'Token Verification Failed, Please Log In Again',
		Request:'Request Error',
		errorcode:'Request Interface Exception, Error Code [404]',
		timedout:'Request Timed Out',
		feature:'This Feature Is Temporarily Unavailable, Please Try Again Later',
		Servererror:'Server Error, Please Contact The Administrator',
		Requestout:'Request Timed Out, Please Try Again Later',
		Successful:'Successful operation',
		Closeall:'Close All',
		Closeother:'Close Others',
		Fullscreen:'Full Screen',
		Exitscreen:'Exit Full Screen',
		manageOperate: 'Manage',
		pleaseChoose: "Please Select",
		PhysicalSIM: "CMLink SIM",
		cardType: "Card Type",
		quantity: "Quantity",
    simplifiedChinese: "Simplified Chinese",
    traditionalChinese: "Traditional Chinese",
    english: "English",
    tips: "Tips"
	},

	//CDR话单管理
	cdr_mngr: 'CDR Bill Management',
	call_query: 'Bill Query',
	country_operators: 'Search By Country/Operator',
	search_number: 'Query By Number',
	flowtotal: 'Flow Total',
	search_no: 'Query By sectionNo',
	price_rules: 'Rating Rules',
	search_enterprise: 'Query By Enterprise',
	search_setMeal: 'Query By Package',
	company_price: 'Enterprise/Carrier Billing Statistics',
	product: 'Product Manager',
	//下载管理
	download_mngr:'Downloads',
	taskListinfo:'Downloads',
	Period:'Period',
	TaskID:'Task ID',
	FileName:'File Name',
	Tasks:'Task ID',
	Description:'Description',
	TasksStatus:'Status',
	File:'File Name',
	CreationTime:'Creation Time',
	FinishedTime:'Finished Time',
	Operation:'Operation',
	DownloadFlie:'Download',
	FailedFlie:'Failed To Download',
	Notexist:'File Does Not Exist',
	exportMS:'Message',
	exportID:'The Task ID:',
	exportFlie:'The File Name:',
	downloadResult:'Please Go To The Page Of "Downloads" To Check And Download The Result',
	Goto:'Go To "Downloads"',


	//产品运营
	product_operation: 'Product Operation',
	package_config: 'Package Configuration',
	individual_config: 'Individual Configuration',
	batch_config: 'Batch Configuration',
	inventory_mngr: 'Inventory Management',
	task_query: 'Task Query',
	porder_mngr: 'Personal Order Management',
	package_delay: 'Package Delay',
	package_search: 'Inactive Package Query',
	pakg_delay: 'Unactivated Packages Delay',
	delayTask_audit: 'Deferred Task Review',
	activation_record: ' Activate Record Query',
	ordinary_package: ' Global Card Ordinary Package',
	offline_mode: ' Terminal Vendor Offline Mode',
	online_mode: ' Terminal Vendor Online Mode',
	cooperative_model: ' Partner Operator Mode',
	SupplierOrder_management: 'Channel supplier order management',
	ChannelCardOrders_management: 'Channel Blank SIM Management',

	activateStat_search: 'Activate Record Query',
	activate_stat: 'Sales Statistical Query',
	availability_stat: 'Available Package Statistics',
	statEnd_month: 'Statistics Of Inactive Packages',
	// 产品管理
	masterCard: 'Master Card Management',
	cardPool: 'Card Pool Management',
	associationGroup:'National Card Pool Association Group',
  addCardPool: "Add Association Group",
  updateCardPool: "Edit Association Group",
  copyCardPool: "Copy Association Group",
	cardPooldetails:'Card Pool Details',
  specialCountryRule: "Special Destination Rules Management",
  specialCountryRuleAdd: "Add Special Destination Rules",
  specialCountryRuleEdit: "Edit Special Destination Rules",
	//套餐管理
	packageManage: 'Package Management',
	packageIndex: 'Package',
	packageBatch: 'Package Batch Edit',
	packageAdd: 'Package Add',
	packageUpdate: 'Package Edit',
	packageInfo: 'Package Details',
	cardPoolDetail: 'Package Card Pool Details',
	packageCopy: 'Package Copy',
	resource: 'Number Resource Management',
	msisdn: 'MSISDN Management',
	iccid: 'ICCID Management',
	imsi: 'IMSI Management',
	supplyImsi: 'Supplier IMSI Management',
  makeCardFile_mngr: 'Card file Management',
  addCard_mngr: 'Create card tasks',
  otaData_mngr: "OTA Data Management",
	imsi_list: 'IMSI List',
	vimsi: 'VIMSI Management',
	trafficPool_mngr:'Traffic Pool Management',
	addPool_mngr:'Create A New Traffic Pool',
	editPool_mngr:'Modify The Traffic Pool',
	copyPool_mngr:'Copy Traffic Pool',
	detailsPool_mngr:'Flow Pool Details',
	channelPool_mngr:'Traffic Pool Management',
	resourceCooperation:'Resource Management',
	fuelPack_mngr:'Add-on Package Management',
	upccTemplate_mngr: 'UPCC speed template Management',
	channelProvider_mngr: 'Channel provider package Management',
	targetedApplication_mngr: 'Specific APP Management',
  whiteListPackage_mngr: "White list package management",
	viewResources: 'Resource',
	callOrderDetails: 'CDR details',
	billingStatistics: 'Invoice statistics',

  //财务系统管理
  finance_mngr: 'Financial System Management',
  billing_mngr: 'Billing Management',
  history_mngr:'Historical Real Bill',
  acounting_period: 'Account Period Management',
  serviceRecharge_approval: 'Self-service recharge approval',
	billing_adjust: 'Billing adjustments',
  addBilling_adjust: 'Added account adjustments',
	aprv_details: 'Approval details',

  //实名制管理
  realname_mngr:'Real Name Management',
  rule_mngr:'Rule Management',
  ruleDetail_mngr:'Rule Details',
  certification_mngr:'Manual Authentication',
  certificationinfo_mngr:'Certification Information',
  //营销管理
  marketing_mngr: "Marketing management",
  marketingActivityIndex: "Marketing management",
  marketingActivityUpdate: "Marketing activity Edit",
  marketingActivityAdd: "Marketing activity Add",
	//短信管理
	smsManage: 'SMS Management',
	notificationSMS: 'Notification SMS Management',
	notificationIndex: 'Notification SMS',
	notificationAdd: 'Notification SMS Add',
	notificationUpdate: 'Notification SMS Edit',
	notificationInfo: 'Notification SMS Details',
	areaWelcomeSMSIndex: "Regional Welcome SMS Management",
  areaWelcomeInfo: "Regional Welcome SMS Details",
  areaWelcomeEdit: "Regional Welcome SMS Editor",
  areaWelcomeEditAdd: "Regional Welcome SMS Add",
	customerSMS: 'Customer service SMS Management',
	customerIndex: 'Customer Service SMS',
	marketingSMS: 'Marketing SMS Management',
	marketingIndex: 'Marketing SMS',
	marketingInfo: 'Marketing SMS Details',

	//客户管理
	customerManage: 'Customer Management',
	channelManage: 'Channel Management',
	channelIndex: 'Channel',
	billCheck:'Bill Check',
	channelAdd: 'Channel Business Add',
	channelInfo: 'Channel Business Details',
	channelUpdate: 'Channel Dealer Editor',
	packageGroup: 'Package Group Management',
	zeroLevelChannel: 'Zero level channel provider',
  a2zBillPriceMngr: 'Traffic Billing and Pricing Management',
  costPricingMngr: 'Cost Pricing Management',
  imsiFeeMngr: 'IMSI Fee Management',
	zeroChannelAdd: 'New zero level channel providers',
	zeroChannelUpdate: 'Modify zero level channel supplier',
  a2zBillPriceDetail: 'Traffic Billing and Pricing Management - Rule Details',
  costPricingDetails: 'Cost Pricing Management - Details',
	cooperativeManage: 'Cooperative Operator Management',
	cooperativeIndex: 'Cooperative Operator',
	cooperativeInfo: 'Details Of Cooperative Operators',
	postPaymentChannel: 'Post-paid Channel Management',
	paymentChannelIndex: 'Post-paid Channel',
	paymentChannelInfo: 'Post-paid Channel Details',
	manufacturerManage: 'Terminal Manufacturer Management',
	manufacturerIndex: 'Terminal Manufacturer',
	manufacturerInfo: 'Terminal Manufacturer Details',
	manufacturer_update: 'Terminal Manufactureredit',
  test_imsi: 'Test IMSI Management',

	//客服支撑
	service_brace: 'Customer Service',
	service_index: 'SIM Details',
	local_search: 'Location Query ',
	purchased_package: 'Purchased Package',
	location_package: 'Current Activated Package',
	sppurchased_package: 'Purchased Package',
	splocation_package: 'Current Activated Package',
	//故障处理
	fault_mngr: "Fault Handling",
	fault_add: "New Fault Handling",
  // GTP话单地址配置
  GTPAddressConfig: "GTP Call Ticket Address Configuration",

	// 流量池管理
	flowPool: 'Flow Pool Management',
	poolList: 'Flow Pool Details',

	// 渠道自服务
	channel_mngr: 'Channel Portal',
	deposit_mngr: 'Account Management',
	mealList_mngr: 'Package Details',
	streamList_mngr: 'Flow Details',
  offlinePayment: 'Top up page',
  marketingAccount :"Marketing Activity Details",
	adminMarketingAccount :"Marketing Activity Details",
	stock_mngr: 'Inventory Management',
	cardList_mngr: 'SIM Details',
	buymeal_mngr: 'Purchase Package',
	order_mngr: 'Order Management',
	bill_mngr: 'Monthly Bill',
	support_mngr: 'Customer Service',
	address_mngr: 'Address Management',
	detailsList_mngr: 'Service And Support Package Details',
	useList_mngr: 'Usage Details',
	showiccid_mngr:'ICCID List',
	flowpool_mngr:'Data Pool',
	cardlist_mngr:'ICCID List',
	flowlist_mngr:'Data Pool List',
	userecord_mngr:'Usage Record',
	iccidlist_mngr:'ICCID List',
	channelcardlist_mngr:'ICCID List',
	channelflowlist_mngr:'Data Pool List',
	channeluserecord_mngr:'Usage Record',
	channeliccidlist_mngr:'ICCID List',
	channelfuelPack_mngr:'Add-on Package Management',
	channelpackage:'Package Management',
	fuelPackManagement: 'Fuel Pack Management',
	aqCode_mngr:'ESIM QR code Management',
	subChannelProvider_mngr: 'Sub-channel Management',
	whiteCardOrders_management: 'Blank SIM Management',
	channelResourceCooperation: 'Resource Management',
	channelViewResources: 'Resource',
	channelCallOrderDetails: 'CDR details',
	channelBillingStatistics: 'Invoice statistics',
  channelBillingQuery: 'Partner invoice',
  paymentOrderManagement: 'Payment order management',

	// 运营商管理
	operators_mngr: 'Operator Management',
	ResourceSupplier_mngr: 'Resource supplier management',
	operatorsindex_mngr: 'Operator Management',
	channelSellindex_mngr: 'ChannelSell Management',
	channelSellHistory_mngr:'channelSellHistory Management',
	channelSell_mngr:'ChannelSell Management',
  topUp_records: "Top-Tp Records",

	// 报表
	report_mngr: 'Report Function',
	exchange_mngr: 'Exchange Rate Management',
	cardsell_mngr: 'Card Sales Report',
	cardactive_mngr: 'Card Activation Report',
	subscription_mngr: 'Package Subscription Report',
	activation_mngr: 'Package Activation Report',
	reuse_mngr: 'Reuse Query',
	analysis_mngr: 'Package Analysis Report',
	terminal_mngr: 'Terminal Settlement Report',
	channelReport_mngr: 'Operator Settlement Report',
	income_mngr: 'Offline Revenue Report',
	terminal_after_pay: 'After Payment Report',
  costReport_mngr: 'Cost Report',
  esimDownloadReport_mngr: "ESIM Download Report",

	// 押金管理

	deposit: {
		deposit_money: 'Current Balance',
		mealList: 'Package Details',
		streamList: 'Detail',
		mealname: 'Package Name',
		search: 'Search',
		charge_time: 'Period',
		canmeal: 'Product List',
		flow: 'Records of Increased Deposit Balance',
		mealId: 'Package ID',
		mealprice: 'Charges',
		charge_price: 'Recharge Amount',
		currency: 'Currency',
		chosetime: 'Please Select Recharge Time',
		ProductList:'Product List Export',
		charge_type:'Type',
		Amount: 'Transaction amount',
		accountdeposit: 'Account balance',
		startTime:'Please select a start date',
		endTime: 'Please select an end time',
		a2zDepositLimit: 'Deposit/Prepayment',
    channelMode: "Business Model",
    depositAccount: "Deposit Account",
    preDepositAccount: "Prepayment Account",
    marketingAccount: "Marketing Account",
    creditAccount: "Credit Account",
    marketingAccountFlow: "Marketing Account Transaction Details",
    marketingActivities: "Marketing Activity",
    eventStartTime: "Activity Start Time",
    eventEndTime: "Activity End Time",
    campaignStatus: "Activity Status",
    rebateAmount: "Rebate Amount",
    usedAmount: "Amount Used",
    rebateBalance: "Rebate Balance",
    arrivalTime: "Arrival Time",
    expirationTime: "Expiration Time",
    spendTime: "Consumption Date",
    effectiveTime: "Effective Time",
    connectedActivities: "Related Activities",
    consumptionAmount: "Consumption Amount",
    accountTotalBalance: "Total Balance of Marketing Account",
    settlementDate: "Settlement Date",
    pricingDate: "Price Approval Date",
    singleActivityBalance: "Single Marketing Activity Balance",
    marketingActivityDetails: "Marketing Activity Rebate Details",
    marketingAccountDetails: "Marketing Account Details",
    totalOrderId: "Order ID",
    toBeStarted: "To Be Started",
    started: "Started",
    ended: "Ended",
    obsolete: "Obsolete",
    earlyTerminate: "Early Terminate",
    increaseMarketinRebates: "Increase Marketing Rebates",
    distributionPackageOrdering: "Distribution Package Ordering",
    startEndDate: "Please Select The Start And End Date!",
    totalOrderNumber: "Order Number",
    SubOrderNumber: "Order Number(Sub-Order)",
    ResetMarketingBudget:　"Reset The Expired Marketing Budget",
    dataUsageSettlemtn: "Data Usage Settlement",
    OffsetMarketingRebateAmount: "Offset Marketing Rebate Amount",
	},
	// 库存管理
	stock: {
		order_number: 'Task',
		input_number: 'Please Fill In Task Name',
		timeslot: 'Period',
		chose_time: 'Please Select',
		search: 'Search',
		details: 'Details',
		Card_status: 'Card Status',
		chose_status: 'Please Select Card Status',
		exporttb: 'Export',
		card_number: 'QTY',
		addtime: 'Import Time',
		action: 'Operation',
		usedstate: 'Status',
		cardtype: 'Card Type',
		Code: 'Verification Code:',
		PhysicalSIM:'Physical SIM',
		eSIM:'ESIM',
		TSIM:'TSIM',
		showiccid:'ICCID List',
		Storagetime:'Date',
		attributableChannel: 'Channel',
		transfer: 'Transfer',
		subChannel: 'Sub-channel',
		subChannelProviderEmpty: 'Sub-channel is mandatory',
		availablePackages: 'Are there any packages available',
		whitelistPackage: 'Whitelist Package',
    WhitelistPackageID: "Whitelist package ID",
	},
	// 套餐购买
	buymeal: {
		manual_batch: 'Single Purchase',
		file_batch: ' Batch Upload',
		chose_meal: 'Selected Package',
		input_number: 'ICCID',
		chose_mealtext: 'Please Choose A Set Meal',
		chose_number: 'ICCID',
		confirm: 'Submit Order',
		Reset: 'Reset',
		HK_dollar: 'Hong Kong Dollar',
		payment: 'Order Amount',
		mealname: 'Package Name',
		search: 'Search',
		input_mealname: 'Fill In Package Name',
		Selectall: 'Set All',
		Deselectall: 'Deselect All',
		upload: 'Upload',
		Country: 'Country/Region',
		amount: 'Original Package Amount',
		selectCountry: 'Select Country/Region',
		Download: 'Download File Template',
		Downloadmsg: 'Only Support Csv, And FIle Size Cannot Over 5M',
		Choosemeal: 'Please Select The Package!',
		cycletype: 'Package Type',
		cycleNumber: 'Package Cycle',
		filename:'Export Uploaded File',
		time:'Uploaded Time',
		chooseprice:'Please Choose A Package To Get A Discounted Price!',
		choose:'Select',
		hour:'24hours',
		day:'By day',
		month:'By month',
		year:'By year',
		cday: 'By day',
		cmonth: 'By month',
		cyear: 'By year',
		Failedfile:'Export Failed Result',
		clickdownload:'Click To Download',
		tasksTotal:'Total Uploaded Record',
		successes:'Succeed',
		failed:'Failed',
		Nofailed:'No Failed Files',
		taskview:'Historical Task View',
		Taskstatus:'Task Status',
		Processing:'Loading',
		completed:'Completed',
		templatename:'File Template',
		Insufficient:'Insufficient Deposit, Please Recharge Before Buying',
		purchase:'Successful Purchase',
		toupload:'Please Select The File To Upload!',
		fileformat:'Incorrect File Format',
		incorrect:'The Format Is Incorrect, Please Upload A .Csv File',
		Filesize:'File Size Exceeds Limit'	,
		Exceeds:'Exceeds The Maximum Limit Of 5MB',
		fileresult:'Failed Result',
		Uploadfile:'Upload'
	},
	// 订单管理
	order: {
		mealname: 'Package Name',
		input_mealname: 'Fill In Package Name',
		chose_number: 'ICCID',
		input_number: ' ICCID',
		timeslot: 'Period',
		chose_time: 'Please Select',
		search: 'Search',
		monthly_bill: 'View Monthly Bill',
		exporttb: 'Export',
		month: 'Month',
		chose_month: 'Please Select Month',
		expenditure: 'Total Expenditure',
		order_number: 'Order Number',
		order_state: 'Order Status',
		count: 'QTY',
		order_money: 'Order Amount',
		addtime: 'Creation Time',
		isused: 'Is The Package Used',
		action: 'Operation',
		unsubscribe: 'Unsubscribe',
		ifunsubscribe: 'Confirm To Unsubscribe This Item',
		channels: 'Method',
		Website:'Website',
		BulkOrder:'Bulk Order',
		Trial:'Trial',
		Testing:'Testing',
		Datapool:'Data Pool',
		ActivationTime:'Activation Time',
		LocationUpdate:'Latest Location Update',
		BeijingMobile:'Beijing Mobile',
		issuance:'Cooperative Card Issuance',
		Postpaid:'Post-paid Card Issuance',
		Normal:'Normal',
		Suspend:'Terminated',
		Expired:'Expire',
		Terminated:'Suspend',
		WeChat:'WeChat Public Account',
		yes:'Yes',
		no:'No',
		delivered:'To Be Delivered',
		Completed:'Completed',
		Cancelled:'Cancelled',
		approval:'Activate Unsubscribe Pending Approval',
		Recycled:'Recycled',
		Numberform:'Number Registration Form.txt',
		Unsubscribe:'Unsubscribe Successfully',
		channelOrderMoney: 'Channel Order Amount',
	},
	// 服务与支持
	support: {
		cardtype: 'Main Card Type',
		chose_type: 'Please Select Card Type',
		cardstate: 'SIM status',
		chose_state: 'Please choose SIM status',
		pause: 'Pause',
		input: 'Please Input',
		search: 'Search',
		mealList: 'Package Details',
		mealname: 'Package Name',
		input_mealname: 'Fill In Package Name',
		timeslot: 'Period',
		chose_time: 'Please Select',
		used_details: 'Usage Details',
		activation: 'Activation',
		frozen: 'Frozen',
		action: 'Operation',
		time: 'Package End Date',
		Verification_Code: 'Send Verification Code',
		Activation: 'H IMSI Activation Method',
		isused: 'Is It Used',
		meal_time: 'Package Valid Date',
		cmeal_time: 'Package Valid Date',
		Activation_state: 'Package Status',
		used_flow: 'Usage Flow',
		used_cycle: 'Life Cycle',
		Report_time: 'Latest Location Time',
		Report_address: 'Reporting Location',
		Targeting:'H/V IMSI',
		template:'SMS',
		position:'Current Location',
		Locationrecord:'Location Update',
		SendSMS:'SMS',
		Flowdetails:'Data Usage',
		Packagestatus:'Package Status',
		Activationtype:'Activation Type',
		Ordernumber:'Order Number',
		Ordertime:'Order Date',
		Orderchannel:'Channel',
		Activationmethod:'Activation Type',
		sendingmethod:'Sending Method',
		chosesend:'Please Select The Sending Method',
		phone:'MSISDN',
		phoneprompt:'Please Enter Phone Number',
		chosetemplate:'Please Select SMS Template',
		sending:'Send SMS',
		usedetails:'Historical Usage Details',
		date:'Date',
		useflow:'Data Usage(G)',
		close:'Shut Down',
		Periodtype:'Package Type',
		Continuouscycle:'Package Cycle',
		Activatepackage:'Activate',
		MSISDNenter:'Please Enter MSISDN',
		ICCIDenter:'Please Enter ICCID',
		IMSIenter:'Please Enter IMSI',
		Recycle:'Early Termination',
		ReplaceVIMSI:'Replace VIMSI',
		Automatic:'Auto',
		Manual:'Manual',
		Sendingempty:'Sending Method Cannot Be Empty',
		SMSempty:'SMS Template Cannot Be Empty',
		Phoneempty:'Phone Number Can Not Be Blank',
		PhoneWrong:'Wrong Format Of Phone Number',
		Unuse:'Unuse',
		InUse:'In Use',
		Expired:'Expired',
		Activatedpending:'Activated Pending',
		Activating:'Activating',
		activated:'Activated',
		Used:'Used',
		CNY:'CNY',
		USD:'USD',
		HKD:'HKD',
		VIMSILocation:'VIMSI Location Update Details',
		Cardtype:'Card Type',
		Flowg:'Data Usage(G)',
		VIMSIphone:'VIMSI Number',
		TimeLocation:'Time',
		Location:'Location',
		Termination:'Confirm Early Termination?',
		replacement:'Confirm Replacement Of VIMSI?',
		VIMSIdetails:'VIMSI Allocation Details',
		IMSIdetails:'IMSI online details',
		targetedAppDetails: 'Specific APP online details',
		cardtraffic:'Query V Card Traffic',
		QueryH:'Query H Card Traffic',
		cardflow:'H Card/V Card Traffic',
		packageflow:  'Total Package Traffic/ High Speed Traffic(MB)：',
		remainingflow:'Current Remainging Traffic(MB)：',
		Usedtraffic:  'Used Traffic(MB)：',
		Usedflow:'Used Traffic',
		countryregion:'Country/Region',
		flowquery:'Usage Query',
		back:'Return',
		Vcard:'VIMSI ',
		Hcard:'HIMSI ',
		searchcondition:'Please Fill In At Least One Search Condition',
		obtained:'The List Failed To Load Because The Current Card Position Was Not Obtained',
		advance:'The Package Has Expired And Cannot Be Recycled In Advance',
		Sentsuccessfully:'Sent Successfully',
		SIMDate:'SIM Expiry Date',
		Locationexport:'Location Update Export',
		recordexport:'Data Usage Details Export',
		operationFailed:'The Operation Failed, Only The In-use State Package Is Supported',
		VoperationFailed:'The VIMSI Cannot Be Replaced Because The Current Card Position Has Not Been Obtained',
	  usageTotal:'Total data usage',
		operatorName:'Operator',
		Complete:'Complete ID Registration',
		registration:'ID Registration Details',
		IDnumber:'ID Number:',
		IDtype:'ID Type:',
		Issuingcountry:'Issuing Country:',
		approval:'Pending Approval',
		process:'Approval In Process',
		Approved:'Approved',
		Rejected:'Rejected',
		IDexpired:'ID Expired',
		registrationrules:'ID Registration Rules',
		registrationcountry:'ID Registration Country',
		Passport:'Passport',
		Permit:'Exit/Entry Permit For Travelling To And From Hong Kong And Macau',
		HKIdentityCard:'Hong Kong Identity Card',
		MacauIdentityCard:'Macau Identity Card',
		view:'View',
		DataUsedDay:'Data Used For The Day',
		DataRestrictionType:'Data Restriction Type',
		DataRestrictionCycle:'Data Restriction In Cycle',
		DataRestrictionSingle:'Reset by cycle type',
		ControlLogicLimit:'Control Logic After Data Limit',
		RestrictedSpeedLimit:'Restricted Speed After Data Limit',
		ReleaseAfterLimit:'Release After Data Limit',
		InternetStatus:'Internet Status',
		Normal:'Normal',
		RestrictedSpeed:'Restricted Speed',
		DataCap:'Data Cap',
		NameChinese:'Name(Chinese):',
		NameEnglish:'Name(English):',
		hightDataCap:'High Speed Data Limit',
		payBills:'Pay Bill',
		remunerationReturn:'Commission Return',
		increaseDeposit:'Deposit Topup',
		PreDeposit:'Prepayment Topup',
		packageOrder: 'Package purchase',
		fuelPackpackageOrder: 'Add-on Pack Purchase',
		packageCancellation: 'Package Refund',
		fuelPackUnsubscribe:'Add-on Pack Refund',
		UsedAddonPack:'Data Used For Add-on Pack',
		ToppedAddonPack:'Data Topped Up For Add-on Pack',
		hightDataCap:'High Speed Data Limit',
		WaitingUser:'Waiting For User To Submit Authentication Information',
		AuthFailed:'[Authentication Failed], Wait For The User To Re-submit The Authentication Information',
		NotCertified:'Not Certified',
		flowpoolApi:'Data Pool API',
		NoCertification:'No Certification Required',
		querycontent:'Card number does not exist',
		imsiType: 'IMSI type',
		useCountry: 'Used destination',
		imsiResource:'IMSI provider',
		activeTime: 'Activation time',
		latestActivationDate: 'Latest activation time',
		Refunded:'Refunded',
		Available:'Available operator',
		channelIncomeAdjustment: 'Channel income adjustment',
    marketingRebate: "Marketing rebate",
    imsiFeeStatistics: "IMSI fee statistics",
    indemnity: "Indemnity",
		errorDesc: 'Failure reason:',
		nameIsInconsistent: 'Name mismatch',
		certificateHasExpired: 'Document expired',
		IDIsInconsistent: 'Document ID mismatch',
		sixteenyYearsOld: 'Under 16 years old',
		picturesAreNotSatisfied: 'Image uploaded unsatisfactory',
		EsimDetails: 'ESIM Information Enquiry',
		smDpAddress: 'SM-DP+ address',
		activationCode: 'Activation code',
		esimStatus: 'ESIM status',
		eid: 'EID',
		installationTime: 'Installation time',
		installationEquipment: 'Installation device',
		instalAmount: 'Number of installation',
		updateTime: 'Update time',
		generateQRCode: 'Generate QR code',
		msisdnImsiIccid: 'Please select msisdn, imsi or iccid！',
		timeFrame: 'Time range',
		orderBatch: 'Order batch',
		inputOrderBatch: 'Please input order batch',
		segmentSearch: 'Number range search',
		startNumber: 'Starting number',
		endNumber: 'Ending number',
		codeExport: 'Export QR code',
		fileExport: 'Export file',
		xiazai: 'Download',
		operate: 'Operate',
		inputPackageID: 'Please input package ID',
		edit2: 'Edit',
		copy: 'Copy',
		approvalStatus: 'Approval status',
		newApproval: 'Waiting approval for creation',
		approve: 'Approve',
		notApprove: 'Reject',
		modificationApproval: 'Waiting approval for modification',
		deleteApproval: 'Waiting approval for deletion',
		fiveCharacters: 'Maximum 500 characters',
		fourtCharacters: 'Maximum 4000 characters',
		packageDescription: 'Package description',
		selectType: 'Please select cycle type',
		selectDuration: 'Please select duration',
		packageValidity: 'Package validity',
		inputPackageValidity: 'Please input package validity',
		selectDataResetType: 'Please select data reset type',
		selectRestrictionLogic: 'Please select restriction logic',
		aupportHotspot: 'Support hotspot sharing',
		isAupportHotspot : 'Hotspot sharing ON/OFF?',
		usage2: 'Usage',
		selectUsage: 'Please select IMSI provider',
    inputUsage: "Please enter IMSI provider",
    wrongUsageFormat: "Please enter an integer between 1 and 10 digits",
		speedTemplate: 'Speed template',
		selectSpeedTemplate: 'Please select speed template',
		unlimitedUsageTemplate: 'Unlimited usage template',
		selectUnlimitedUsageTemplate: 'Please select unlimited usage template',
		packageNameNotNull: 'Package name is mandatory',
		packageDescriptionNotNull: 'Package description is mandatory',
		prongDurationFormat: 'Wrong duration format',
		DurationValueLarge: 'Duration value is too large',
		wrongPackageValidity: 'Wrong package validity',
		packageValidityLarge: 'Package validity is too large',
		qrPicture: "QR code",
		pakageId: 'Package ID',
		day: 'Day',
		add: 'Add',
		create: 'Create',
		createPackage: 'Create package',
		operator: 'Operator',
		network: 'Network Type',
		subChannelName: 'Sub-channel Name',
		contactEmail: 'Contact email',
		purchasePackage: 'Package available for purchase',
		profitMargin: 'Profit rate',
		totalAmount: 'Credit Limit',
		accountPermissions: 'Account permissions',
		channelAppSecret: 'channel App Secret',
		channelAppKey: 'channel App Key',
		selectCharacter: 'Select role',
		submit: 'Submit',
		clickToUpload: 'Upload',
		downloadfile: 'Download template',
		pleaseUploadFile: 'Please upload',
		subChannelEmpty: 'Sub-channel name is mandatory',
    subChannelAmpersand: "Sub-channel can't include symbol '&'",
		contactEmailEmpty: 'Contact email address is mandatory',
		EmailFormatError: 'Contact email address wrong format',
		fuelPackProfitMarginEmpty: 'Add-on pack margin is mandatory',
		totalAmountEmpty: 'Total quota is mandatory',
		accountPermissionsEmpty : "Account permission is mandatory",
		subChannelDetails: 'Sub-channel details',
		editSubChannel: 'Edit sub-channel',
		addSubChannel: 'Create sub-channel',
		uploadFailed: 'Upload failed!',
		files: 'Document',
		fileUploadedAndProgressDisappears: 'Document is uploading, please wait until the progress bar disappears',
		downTemplateFilelAndUpload: 'Please download the template first, then upload after fill in',
		imsi: "IMSI",
		packageProfitMargin: 'Package profit rate',
		fuelPackProfitMargin: 'Add-on pack profit rate',
		failureReason: 'Reason for failure',
		supportAddon: "Support add on",
		bindAddon: "Bind add on",
		AddonList: "Add on list",
		newAddon: "Create add-on pack",
		AddonName: "Add on pack name",
		AddonID: "Add on pack ID",
		AddonAmount: "Add on data amount",
		AddonListMandatory: "Add on list is mandatory",
		createOrder: "Create order",
		revoke: "Revoke",
		downloadInvoice: "Download Invoice",
		downloadList: "Download list",
		uploadPayslip: "Upload payslip",
		customizedSIM: "Customized SIM",
		PhysicalSIM: "Physical SIM",
		receiveeAddress: "Delivery Address",
		receiverName: "Contact Name",
		contactNumber: "Contact Number",
		distribution: "Distribution",
		atoz: "A~Z",
		cooperationModel: "Cooperation Model",
		payslip: "Payslip",
		uploadPicture: "Click or drag to upload file",
		pictureSize: "Exceeds The Maximum Limit Of 10MB",
		paymentMethod: "Payment method",
		deliveryCompany: "Delivery company",
		trackingNumber: "Tracking number",
		ordered: "Ordered",
		cancelled: "Cancelled",
		pendingPayment: "Pending for payment",
		paid: "Paid",
		deliveryProgress: "Delivery in progress",
		delivered: "Delivered",
		deliveryFailed: "Delivery failed",
		PaymentMandatory: "CMLink/Customized SIM is mandatory",
		cardTypeMandatory: "Card type is mandatory",
		cardMumberMandatory: "Card quantity is mandatory",
		wrongFormat: "Wrong format",
		addressMandatory: "Delivery address is mandatory",
		receiverMandatory: "Contact name is mandatory",
		contactNumberMandatory: "Contact number is mandatory",
		cooperationMandatory: "Cooperation model is mandatory",
		picetureMandatory: "File is mandatory",
		confirmRevocation: "Confirm revert?",
		paymentMethod: "Payment method",
		description: "Description!",
		newAddonPack: "Add-on Pack",
		determine: "Confirm",
		flowValue: "Add on data amount",
		addAmountMandatory: "Add on data amount is mandatory",
		cannotExceed: "Exceeds The Maximum Limit Of 104857600MB",
		generateInvoice: 'Generate Invoice Time',
		uploadPayslipTime: "Upload payslip time",
		orderConfirmTime: "Order confirmation time",
		deliveryTime: "Delivery Time",
		paymentConfirmed: "Pending payment confirmed",
    PaymentNotConfirmed: "Payment not yet receive",
		physicalOrCustomized: "CMLink/Customized SIM",
		inputFlowValue: "Please Enter Add on data amount",
		inputAddonName: "Please Enter Add on pack name",
		inputAddonId: "Please Enter Add on pack ID",
		inputCardNumber: "Please enter card quantity",
		inputRecrver: "Please enter contact name",
		inputAddress: "Please enter delivery address",
		selectCardtype: "Please Select Card Type",
    productPackaging: "Packaging",
    selectProductPackaging: "Please select packaging",
    productPackagingEmpty: "Packaging is mandatory",
    packagingCard: "SIM with package",
    nakedCard: "SIM without package",
    receivingCountry: "Receiver country",
    selectReceivingCountry: "Please select receiver country",
    receivingCountryEmpty: "Receiver country is mandatory",
    postalCode: "Postal code",
    selectPostalCode: "Please input postal code",
    postalCodeEmpty: "Postal code is mandatory",
    language: "Language",
    selectLanguage: "Please select language",
    languageEmpty: "Language is mandatory",
    agreement: "Please agree the agreement first",
    agree: "I agree this ",
    Agreements: "agreement",
    orderId: "Order ID",
    replaceHIMSI: "Swap HIMSI",
    isSupportHot: "Current internet access supports hotspot",
    internetTemplateSpeed: "Current speed template",
    AgreementContent: "Agreement",
		resourceCooperation: "Resource Cooperation",
		//覆盖时间
		coverageTime: "Coverage time",
		informationQuery: "Information query",
    cdrDetails: "CDR details",
    moreInformation: "More information",
    dataInformation: "Data information",
    rollbackInProgress: "Rollback in progress",
    rollbackSuccess: "Rollback success",
    rollbackFailure: "Rollback failure",
		emptyData:"Package information not found",
    validityPeriod: "Real Name Registration Validity",
    longTerm: "Long-term Validity",
    singlePackageEffective: "Single Package Effective",
	},
	//定向应用
	directionalApp: {
		application: "APP",
		freeFlow: "Exception",
		supportUsage: "Support specific APP data usage",
		selectSupportUsage: "Please select support specific APP data usage or not",
		selectAPP: "Select APP",
		pleaseSelectAPP: "Please select APP",
		deleteAPP: "Delete APP",
		addAPP: "Add APP",
		specificAPPLogic: "Specific APP usage logic",
		inputDataValue: "Please input data value",
		deleteDataValue: "Delete data value",
		addDataValue: "Add data value",
		dataValue: "data value",
		selectUPCCTemplate: "Select UPCC template",
		pleaseSelectUPCCTemplate: "Please select UPCC template",
		continueDataUsage: "Continue to use normal data usage",
		PleaseDataUsage: "Please select whether continuing using normal data usage",
		restrictedTemplate:'Specific APP restricted speed template',
		pleasepRrestrictedTemplate: "Please select specific APP restricted speed template",
		FreeTemplate: "Specific APP data exception restricted speed template",
		pleaseFree: 'Please select specific APP data exception restricted speed template',
		FreeContinueTemplate: 'Specific APP data exception continue using data speed template',
		pleaseFreeContinue: "Please select specific APP data exception continue using data speed template",
		SupportDataMandatory: 'Support specific APP data usage is mandatory',
		number9:"The number of selected APP can't exceed 9",
		APPMandatory: 'Select APP is mandatory',
		LogicMandatory: 'Specific APP usage logic is mandatory',
		folwUpcc: 'Data value and 和 UPCC template',
		valueMandatory: 'Data value is mandatory',
		twoTemplate: 'Specific APP data exception restricted speed template or Specific APP data exception continue using data speed template  is mandatory',
		ContinueMandatory: 'Continue to use normal data usage is mandatory',
		upccMandatory: 'Select UPCC template is mandatory',
		valueRepeat: "Data value can't repeat",
		appRepeat: "APP can't repeat",
		dingTemMandatory: 'Specific APP restricted speed template is mandatory',
		useTemMandatory: 'Specific APP data exception continue using data speed template is mandatory',
		freeTemMandatory: 'Specific APP data exception restricted speed template is mandatory',
		gearRule: 'Data value is incorrect. The data value of each level must be greater than the data value of the previous level.',
		usageRule: 'Usage logic is incorrect. The usage value of each level must be greater than the usage value of the previous level.',
	},
	// 地址管理
	address: {
		deleteitem:'Confirm To Delete This Item?',
		fullname: 'Full Name',
		input_fullname: 'Please Enter Your Name',
		mailbox: 'Mailbox',
		input_mailbox: 'Please Fill In Email Address',
		Newaddress: 'New Address',
		modify: 'Modify',
		modifyaddress: 'Reset Mailbox',
		Delete: 'Delete',
		Forgetpwd: 'Reset Password',
		setdefault: 'Set Default',
		search: 'Search',
		password: 'Password',
		password_ok: 'Confirm Password',
		input_pwd: 'Please Input A Password',
		newpwd: 'New Password',
		account: 'Account Name',
		action: 'Operation',
		oldPwd: 'Original Password',
		PwdRules: 'When Setting The Password, You Need To Meet The Given Rules, Click  ',
		watch: 'View  ',
		more: 'Learn More',
		Rules1: 'Password Character Policy:',
		Rules2: '1. The password must be 8 characters or more and contain at least one uppercase character, at least one lowercase character, at least one number and at least one special symbol;',
		Rules3: '2. The password shall not contain any three identical consecutive (ABC, Abc, 123, !@# etc) and repetitive characters (AAA, Aaa, 111, ### etc)',
		Rules4: '3. The factory default password of the system or device should be replaced, such as huawei:huawei@123, SYS:CHANGE_ON_INSTALL in the oracle database, and the default account of a mobile customized version of optical modem CMCCAdmin:aDm8H%MdA, etc.;',
		Rules5: '4. The password setting should avoid keyboard sorting passwords of more than 3 digits (including 3 digits), such as qwe (the first three letters of the first row of the keyboard), asd (the first three letters of the second row of the keyboard), qaz (the first three letters of the keyboard) Letters), 1qaz (the number in the first column of the keyboard plus the first three letters),! QAZ (Special characters in the first column of the keyboard plus the first three letters), etc.;',
		Rules6: '5. No more than 3 (including 3) consecutive letters, numbers, and special characters, such as ABC, Abc, 123,! @#Wait;',
		Rules7: '6. No more than 3 (including 3) repeated letters, numbers, and special characters, such as AAA, Aaa, 111, ###, etc., can appear in the password.',
		Rules8: 'Avoid the following easy-to-guess password rules',
		Rules9: '1. Province, city name, email address, telephone area code, postal code and abbreviation, and simple numbers or shift key + simple numbers, such as BJYD123, HBYD!@#, etc.;',
		Rules10: '2. Unit name, professional name, system name, manufacturer name (including abbreviations) and simple numbers, such as HBnmc123, HBsmc_123, etc.;',
		Rules11: '3. The name of the maintenance personnel is spelled out, uppercase and lowercase abbreviations, etc. + device IP address (one or two) or date of birth, etc., such as maintenance personnel Zhang San, maintenance equipment address ************ and ************, date of birth If it is 19951015, the possible weak passwords are zhangsan100, zhangsan101, zhangsan10100, zhangsan10101, zhangsan19951015, ZS19951015, etc.;',
		Operationreminder:'Operation Reminder',
		deleted:'Successfully Deleted!',
		inconsistent:'The Two Passwords Are Inconsistent',
		emailaddress:'Please Enter A Valid Email Address',
		reset:'Please Reset',
		appear:'3 Consecutive And Identical Characters Cannot Appear',
		allowed:'3 Consecutive Digits Are Not Allowed',
		determine:'Submit'

	},
	//流量池管理
	flow:{
		inputICCID:"Please Enter ICCID",
		Channel:'Channel',
		Status:'Status',
		chooseStatus:'Please Choose Status',
		toassigned:'To Be Assigned',
		Assigned:'Assigned',
		typelimit:'Monocycle Type Limit',
		Totallimits:'Total Limit',
		Controllogic:'Control Logic',
		Stoplimit:'Stop After Data Limit',
		speedlimit:'Restricted Speed After Data Limit',
		Continuelimit:'Continue After Data Limit',
		Originlimit:'Origin Data Pool',
		inputPoolname:'Please Enter Data Pool Name',
		poolName:'Data Pool Name',
		Usagestatus:'Usage Status',
		chooseStatus:'Please Choose Usage Status',
		Normal:'Normal',
		Restricted:'Restricted Speed',
		Stop:'Stop',
		Pleasechoose:'Please Choose',
		upStatus:'Status',
		Online:'Online',
		Offline:'Offline',
		Useddata:'Data used',
		Numbericid:'Number Of ICCID',
		threshold:'Alert Threshold',
		ImportICCID:'Import ICCID',
		Batchdelete:'Batch Delete',
		Batchupdate: 'Batch Edit',
		ImportTime:'Import Time',
		Singleimport:'Single Import',
		Batchimport:'Batch Import',
		Choosepool:'Choose Data Pool',
		plesepool:'Please Choose Data Pool',
		ICCIDempty:'ICCID Cannot Be Empty',
		Monocyleempty:'Monocyle Type Limit Cannot Be Empty',
		Totalempty:'Total Limit Cannot Be Empty',
		choosecontrollogic:'Please Choose Control Logic',
		Unit:'Unit',
		units:'Unit',
		UploadICCID:'Upload ICCID list',
		iccidFileEmpty:'ICCID List Cannot Be Empty',
		Batchtime:'Batch Import Time',
		Importtotal:'Import Total',
		Numbersuccess:'Number Of Success',
		Numberfailure:'Number Of Failure',
		Successfile:'Success File',
		Failurefile:'Failure File',
		Usagethreshold:'Usage Alert Threshold',
		Percentage:'Percentage',
		Usageempty:'Usage Alert Threshold Cannot Be empty',
		Usagerecord:'Usage Record',
		Choosemonth:'Choose Month',
		Choosedate:'Invoice period',
		PleaseChoosedate: 'Select Period',
		Pleasemonth:'Please Choose Month',
		Billmonth:'Bill Month',
		UsagestartTime:'Usage Start Time',
		UsageendTime:'Usage End Time',
		Ratedtotalusage:'Rated Total Usage',
		Actualtotalusage:'Actual Total Usage',
		Excessusage:'Excess Usage',
		Ratedcharge:'Rated Charge ($)',
		Excesscharge:'Excess Charge ($)',
		Totalcharge:'Total Charge ($)',
		Details:'Details',
		Clickview:'Click To View',
		IMSI:'HIMSI',
		VIMSI:'VIMSI',
		Numbertype:'Number Type',
		StartTime:'Start Time',
		EndTime:'End Time',
		Usage:'Usage',
		totalusage:'Total Usage',
		year:' Year ',
		month:' Month ',
		dday: ' Day ',
		flowyear:'Year',
		flowmonth:'Month',
		Totallimit:'Total Limit',
		Usagedetails:'Usage Record Details',
		Pleasethreshold:'Please Enter Usage Alert Threshold',
		Pleaseinteger:'Please Enter A Positive Integer',
		Validdate:'Valid Date',
		Resetnumber:'Reset Cycle Number',
		Resettype:'Reset Cycle Type',
		Monocyletype:'Monocyle Type Limit',
		Cardcycle:'Card Cycle (Restricted Speed After Data Limit)',
		Stopdatalimit:'Card Cycle (Stop After Data Limit)',
		Restrictedspeed:'Total No Of Cards (Restricted Speed After Data Limit)',
		Totallimitcard:'Total No Of Cards (Stop After Data Limit)',
		Datapoollimit:'Total no of Data pool (Restricted speed after data limit)',
		stoppoollimit:'Total no of Data pool (Stop after data limit)',
		chooserecord:'Please Choose At Least 1 Record',
		Confirmdelete:'Confirm Delete',
		Dataused:'Data Used',
		Originenterprise:'Origin Enterprise',
		Positivenumber:'Positive Number With Maximum 8 Digits And 2 Decimal Places',
		// yearandmonth:'Bill  Month',
		yearandmonthdate:'Invoice date',
		day:'Day',
		Country:'Supporting Country',
		deleteNumber:'Delete',
		poolAvailableTime: 'Available day to enter pool',
		fillNumber: 'Enter available day',
		exportflowsum: 'Export data usage summary',
		remark: 'Remark',
		inputRemark: 'Please enter remark',
		enterRemark: 'Enter remark',
		internetStatus: 'Online status',
		inputinternetStatus: 'Please choose online status',
		recover: 'Resume',
		cardManager: 'SIM management',
		mb: 'MB',
		totallimit: 'Total data limit',
		availableday: 'Available day',
		none: 'None',
		confirmPause: 'Confirm Pause',
		confirmResume: 'Confirm Resume',
		kongge: 'Spaces are not allowed',
		expirationDate: 'Expiry date',
		SelectDestination: 'Select destination',
		select: 'query',
		usageMB: 'Usage (MB)',
		requiredDataExport: 'Please select the required data to export',
		chooseOne: 'Please select at least one',
		negative: "Can't be negative number"
	},
	//加油包管理
	fuelPack:{
		startDate:'Package Start Date',
		endDate:'Package End Date',
		packagedata:'Current Package Data (MB)',
		adddata:'Purchased Add-on Data (MB)',
		usedhigh:'Used High Speed Data (MB)',
		Purchase:'Purchase Add-on',
		SelectfuelPack:'Select Add-on',
		PleaseSelectfuelPack:'Please Select Add-on',
		quantity:'Select Add-on Quantity',
		Currentday:'Current Day Add-on',
		Daily:'Daily Add-on',
		//当月加油包
		CurrentMonthly: "Current month add on pack",
		//每月加油包
		Monthly: "Monthly add on pack",
		//当年加油包
		CurrentYear: "Current year add on pack",
		//每年加油包
		Yearly: "Yearly add on pack",
		Amount:'Amount',
		onlinestatus:'Current Online Status',
		purchasequantity:'Please Input Purchase Quantity',
		Specificdate:'Specific Activation Date',
		SelectDate:'Select Date',
		PleaseSelectDate:'Please Select Date',
		activationType:'Package Activation Type',
		Activatelimit:'Activate After Data Limit',
		ActivateSpecific:'Activate After Specific Date',
		ActivateLU:'Activate After LU',
		packageTtart:'Select Package Start Date',
		activationdate:'Specific Activation Date',
		buyfuelPack:'Please double confirm before submit - It is effective immediately and non-refundable once Add-on pack purchased?',
		price:'Price',
		Remainingdays:'Remaining Days:'
	},

	//渠道自服务 合作模式
	selectCooperationMode: 'Please select cooperation model',
	consignmentSalesModel: 'Distribution Model',
	A2Zmode: 'A~Z Model',
	resourceMode: 'Resource cooperation Model',
	welcomeWebsite: 'Welcome to Global Data SIM Portal',
	totalCards: 'Total cards',
	announcement: 'Notice',
	esimTotal: 'Total number of eSIM',
	tsimTotal: 'Total number of TSIM',
	imsiTotal: 'Total number of IMSI',
	imsika: "IMSI",
	shitika: "Physical SIM",
	simTotal: 'Total number of physical SIM',
	quotaUsedMonth: 'Credit Used in Bill Cycle',
	AtoZTotal: 'Credit Limit',
	creditsUsed: 'Credit Used in Bill Cycle',
	unused: 'Unused',
	usedLimit: 'Credit Used',
	yuan: 'Dollars',
	packageOrdered: 'Number of package ordered',
	months12: "Data for last 12 months",
	twofivebefore: 'Before the 25th: from the 25th of the previous month to present',
	twofiveafter: 'After the 25th: from the 25th of this month to present',
	esimkazhang: 'ESIM Card(s)',
	imsikazhang: 'IMSI(s)',
	simkazhang: 'Physical SIM Card(s)',
	cardkazhang: 'Total Card(s)',

	//资源管理
	resourceManage: {
		channelName: 'Channel Name',
		enterChannelName: 'Please enter channel name',
		allocateResources: 'Resource allocation',
		resourceView: 'Resource',
		resourceSupplier: 'Resource Provider',
		selectResourceSupplier: 'Please select resource provider',
		imsiNumber: 'Number of IMSI',
		selecyImsiNumber: 'Please enter IMSI quantity',
		routingID: 'Route ID',
		selectRoutingID: 'Please enter route ID',
		resourceSupplierMandatory: "Resource provider is mandatory",
		imsiNumberMandatory: "IMSI quantity is mandatory",
		routingIDMandatory: "Route ID is mandatory",
		selectVIMSIphone: 'Please enter IMSI',
		batchFreeze: "Batch Freeze",
		BatchRecovery: 'Batch Resume',
		seeInformation: 'View information',
		modifyingResources: 'Modify Resources',
		BatchModifyingResources: 'Batch Edit Resources',
		FreezeItem: 'Confirm to freeze this item?',
		RecoverItem: 'Confirm to resume this item?',
		confirmFreeze: 'Confirm Freeze?',
		confirmRecover: 'Confirm Resume?',
		operationFail: 'Failed operation',
		Dimension: 'Select Statistics dimension',
		selectDimension: 'Please select statistics dimension',
		dateOrMonth: 'Date/Month',
		fee: 'Fee',
		imsiPhone: 'IMSI',
		selectImsiPhone: 'Please enter IMSI',
		routingExceeds: 'Exceeds The Maximum Limit Of **********',
		numberExceeds: 'Exceeds The Maximum Limit Of 100000',
	},
  // 渠道商账单查询
  channelBill: {
    startMonth: 'Start Month',
    endMonth: 'End Month',
    selectStart: 'Please select start month',
    selectEnd: 'Please select end month',
    dataUsage: 'Data usage charges',
    inputChargesName: 'Please input charges name',
    imsiFee: 'IMSI fee',
    detailed: 'Details',
    billFileDownload: 'Invoice details download',
    invoiceDownload: 'Invoice download',
    payslip: 'Payment',
    reUpload: 'Upload again',
    dataFee: 'Data usage fee',
    cardFee: 'Card fee',
    packageFee: 'Package fee',
    paymentPage: 'Payment page',
    inputAmount: 'Please input amount',
    checkNumber: 'Positive Number With Maximum 8 Digits And 2 Decimal Places',
    imsiFeeType: 'IMSI fee type',
    imsiFeeAmount: "IMSI fee amount",
    quantityRange: "Quantity range",
    chargesName: "Charges name",
    country: "Country",
    cny: "CNY",
    hkd: "HKD",
    usd: "USD",
    billId: "Invoice ID",
    billType: "Invoice type",
    paymentMonth: "Bill month",
    totalBillAmount: "Invoice amount",
    accountsPayableAmount: "Invoice balance",
    paymentStatus: "Payment status",
    verified: 'Written off',
    unpaidPayment: "Unpaid",
    confirmationReceipt: "Pending payment confirmed",
    Arrived: "Payment successful",
    NotCredited: "Payment failed",
    merge: "Combine",
    endGreaterStart: 'End month should be the same/larger than start month!',
    lessThan0: 'Cannot Be Less Than 0',
    onlinePayment: 'Online payment',
    offlinePayment: 'Offline payment',
		OnlinePaymentInProgress:"Online payment in progress",
		cnInvoice: 'CN Invoice',
		uploadCnInvoice: 'Upload CN Invoice',
		cnInvoiceDownload: 'CN Invoice Download',
		pleaseUploadCnInvoice: 'Please upload CN Invoice file'
  },
	onlineOrder:{
		orderUniqueId : 'Order ID',
    orderName : 'orderName',
    orderType : 'Order type',
    corpId : 'Channel ID',
    orderUserName : 'Purchaser name',
    productId : 'Product ID or Invoice ID',
    thirdOrderNo : 'Payment GW order identification',
    thirdMchorderNo : 'Payment GW order number',
    thirdTransactionNo : 'Payment GW transaction number',
    currencyCode : 'Currency',
    amount : 'Amount',
    orderStatus : 'Order Status',
    paymentMethod : 'Payment method',
    paymentStatus : 'Payment status',
    paymentTime : 'Payment time',
    paymentIp : 'User IP',
    paymentReference : 'paymentReference',
    exprieTime : 'Payment deadline',
    asyncNotifyType : 'Asynchronous result notification type',
    sendLang : 'Language',
    isDeleted : 'Logic remove marker',
    createTime : 'Creation Time',
    updateTime : 'Update time',
    email : 'Email',
		choosePaymentStatusPlaceholder:"Please select order status",
    thirdOrderNoPlaceholder:"Please input payment GW order identification",
    thirdTransactionNoPlaceholder:"Please input payment GW transaction number",
    thirdMchorderNoPlaceholder:"Please input payment GW order number",
    chooseCreateDate:"Please select create period",
    onlineModalTitle:"Online payment",
    payTxt01:"Transaction will be closed in ",
    payTxt02:"",
    payBtn:"Pay",
    orderTypeBill:"Bill payment",
    orderTypedeposit:"Deposit topup",
    wechat:"Wechat",
    alipay:"Alipay",
    card:"Bank card",
    paying:"Pending payment",
    paySuccess:"Payment success",
    payExpired:"Order expired",
    payclosed:"Order closed",
    closeOrderContent:"Confirm to close the order?",
    deleteOrderContent:"Confirm to delete the order?",
    deleteSuccess:"Successfully Deleted!",
    closeSuccess:"Closed successfully",
    paidAmount: "Actual paid amount：",
    depositAmount: "Deposit amount：",
    weChatPayChina: "WeChat Pay (Mainland China version)",
    alipayChina: "Alipay (Mainland China version)",
    debitCreditCard: "Debit card/Credit card",
    depositAmountPlaceholder:"Please input deposit amount",
    loadingStatus: "Loading",
    correctDepositAmount: "Please input correct amount",
    depositAmountGreaterThanZero: "Amount should be larger than 0",
    selectPaymentMethod: "Please select payment method",
    correctBankCardInfo: "Please input correct bank information"  ,
    amountCannotBeEmpty: "Amount is mandatory",
    validPositiveNumber: "Please input valid number (2 decimal places)",
    positiveNumberGreaterThanZero: "Please input positive number larger than 0",
    stateIsValid: "state.isValid:",
    paymentMethodType: "state.data.paymentMethod.type:"

	},
  paymentResultpageTexts: {
		paymentFailed: "Warning",
		paymentSuccessful: "Warning",
		errorDetails: "Payment has been submitted, please click the button to view payment details.",
		successDetails: "Payment has been submitted, please click the button to view payment details.",
		errorReason: "Failure reason",
		thanksMessage: "Thank you for your support",
		viewOrder: "Check orders",
		goToHome: "Back to main page",

	},
  //线下支付
  offlinePay: {
    applyInvoice: 'Apply invoice',
    pay: 'Pay',
    reApplyInvoice: 'Re-apply invoice',
    rePay: 'Re-upload payment advice',
    invoiceType: 'Invoice type',
    topupID: 'Top up ID',
    invoiceNumber: 'Invoice number',
    deposit: "Deposit",
    Prepayment: "Prepayment",
    applyTime: 'Apply time',
    unpaid: "Unpaid",
    invoicePendingApro: "Invoice pending approval",
    invoiceAproReject: "Invoice approval rejected",
    payable: "Payable",
    payPendingApro: "Payment pending approval",
    paymentAproReject: "Payment approval rejected",
    paid: "Paid",
    attachment: 'Attachment',
    onlineTopup: 'Online top up',
    offlineTopup: 'Offline top up',
    topup: 'Top Up',
    topupAmount: "Top up amount",
  },
  country: {
    select: "Select destinations",
    selectAll: "Select all",
    selected: "Selected destinations",
    nameCn: "Destination name (Chinese)",
    nameEn: "Destination name (English)",
    continentCn: "Continent (Chinese)",
    continentEn: "Continent (English)",
    specialRuleMngr: "Special destination rules management",
    all: "All",
    noData: "No Data",
    viewAll: "View All Countries",
    selectingContinent: "Selecting current continent destinations...",
    deselectingContinent: "Deselecting current continent destinations...",
    getContinentFail: "Failed to get continent list",
    getCountryFail: "Failed to get country list",
    getAllCountryFail: "Failed to get all country data",
    alreadySelected: "Already selected destination: ",
		operationFail: "Operation failed: ",
		editCountry: "Edit Country"
  },

  sessionInfo: {
    sessionDetail: "Session Details",
    realtimeSession: "Real time Session Information",
    historySession: "Historical Session Information",
    field: "Fields",
    currentVlaue: "Current Value",
    accessSite: "Access Site" ,
    online: 'Online',
    sessionStartTime: "Session Start Time",
    dataUsageDuringSession: "Traffic generated within a session",
    serviceProviderUsed: "Operators used",
    userIPAddress: "User's IP address",
    PGWSiteAccessedSession: "PGW site accessed by the session",
    sessionQuery: "Session Query",
    UsageMax: "Usage Exceeds The Maximum Limit Of ",
    UsageMin: "Usage Cannot Be Less Than 10MB",
  }
}
