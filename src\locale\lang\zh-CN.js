export default {
  home: "首页",
  login: "登录",
  spinLoading: "加载页面",
  account_list: "账号管理",
  system_mngr: "系统管理",
  log_mngr: "操作日志",
  Announcement_mngr: "公告管理",
  lockModeApproval_mngr: "金锁模式管理",
  login_mngr: "登录日志",
  pwd_mngr: "账户密码管理",
  pri_mngr: "角色管理",
  sys: {
    wholesalerName: "请输入账号...",
    roleType: "请选择角色...",
    add: "新增账户",
    account: "用户名：",
    paccount: "请输入用户名...",
    oldPwd: "原密码：",
    poldPwd: "请输入原始密码...",
    newPwd: "新密码：",
    pnewPwd: "请输入新密码...",
    rePwd: "确认密码：",
    prePwd: "请确认密码...",
    pwdNullMsg: "密码不能为空",
    pwdLengthMsh: "长度不能小于6位",
    accountNummMsg: "账户名不能为空",
    logInto: "登入",
    logOut: "登出",
    optAccoount: "登录账号",
    optType: "登录类型",
    serviceUrl: "IP地址",
    optTime: "登录时间",
    accountT: "账号",
    role: "用户角色",
    status: "用户状态",
    opt: "操作",
    accountempty: "账户名不允许为空",
    newpassword: "输入新密码",
    oldpassword: "输入原密码",
    Enteraccount: "输入账户",
    successfully: "用户基本信息修改成功，请重新登录。",
    wrong: "出错啦",
    Serverwrong: "服务器内部错误",
    smsCode: "请输入6位数字验证码",
    sendSmsCode: "验证码下发成功",
    enterEmail: "输入邮箱：",
    inputEmail: "输入邮箱...",
    setPassword: "设置密码：",
    enterPassword: "输入密码...",
    phone: "手机号码",
    enterPhone: "输入手机号码...",
    pwNotMatch: "两次输入的密码不一致，请仔细核对！",
    inputName: "请先输入用户名!",
    codeSendPhone: "验证码已发送至你的手机/邮箱，请注意查收！",
    second: "秒",
    inputTwoPw: "两次密码输入不一致",
    sendVercode: "下发验证码",
    persona: "角色",
    exitModifyPassword: "退出修改密码",
    userNameEmpty: "用户名不能为空",
    pleaseSetRoles: "请设置用户角色",
    phoneNoEmpty: "手机号不能为空",
    onlyNumbers: "只支持数字",
    userNameSpaces: "用户名有空格",
    userNameAmpersand: "用户名不能包含&符号",
    twentyDigits: "最长支持20位",
    passwordsNotMatch: "两次输入的密码不一致，请重新输入",
    successAddedUser: "新增用户成功!",
    userInforupdated: "用户信息已完成更新",
    successDeleted: "已成功删除用户",
    operationWill: "当前操作将",
    user: "用户",
    continueExecution: "，是否继续执行？",
    userStatusUpdated: "用户状态已更新！",
    confirmDeleteUser: "请确定删除已选中用户",
    obtainPermissionData: "正在获取权限数据，请稍候...",
    Saving: "正在保存，请稍候...",
    roleName: "角色名称",
    newRole: "新增角色",
    enterName: "输入角色名称...",
    viewPermissions: "查看权限",
    rolePermissionManagement: "角色权限管理",
    editAccount: "编辑",
    characterSpaces: "角色名称有空格",
    purview: "权限",
    successAddedRole: "角色新增成功",
    failedObtainRole: "获取角色权限失败，请稍后再试!",
    rolePpermissionsUpdated: "角色权限已更新",
  },
  common: {
    welcome: "欢迎你：",
    edit: "修改",
    frozen: "冻结",
    thaw: "解冻",
    del: "删除",
    search: "搜索",
    reSet: "重置",
    timeSection: "选择时间段",
    beginTime: "开始时间",
    endTime: "结束时间",
    optResult: "结果",
    optType: "请选择登录类型",
    success: "成功",
    failure: "失败",
    logOut: "退出登录",
    rePwd: "修改密码",
    determine: "确定",
    cancel: "取消",
    Error: "错误提示",
    token: "token校验失败，请重新登录",
    Request: "请求错误",
    errorcode: "请求接口异常，错误码[404]",
    timedout: "请求超时",
    feature: "该功能暂不可用，请稍候再试",
    Servererror: "服务器错误，请联系管理员",
    Requestout: "请求超时,请稍候再试",
    Successful: "操作成功",
    Closeall: "关闭所有",
    Closeother: "关闭其他",
    Fullscreen: "全屏",
    Exitscreen: "退出全屏",
    manageOperate: "操作",
    pleaseChoose: "请选择",
    PhysicalSIM: "CMLINK卡",
    cardType: "主卡形态",
    quantity: "卡片数量",
    simplifiedChinese: "简体中文",
    traditionalChinese: "繁体中文",
    english: "英文",
    tips: "提示"
  },
  //CDR
  cdr_mngr: "CDR话单管理",
  call_query: "话单查询",
  country_operators: "按国家/运营商查询",
  search_number: "按号码查询",
  flowtotal: "流量总量",
  search_no: "按号段查询",
  search_enterprise: "按企业查询",
  search_setMeal: "按套餐查询",
  price_rules: "批价规则管理",
  company_price: "企业/运营商账单统计",
  callListinfo: "话单详情",

  //下载管理
  download_mngr: "下载管理",
  taskListinfo: "下载列表",
  Period: "选择时间段",
  TaskID: "请输入任务ID",
  FileName: "请输入文件名称",
  Tasks: "任务ID",
  Description: "任务描述",
  TasksStatus: "任务状态",
  File: "文件名称",
  CreationTime: "任务创建时间",
  FinishedTime: "任务结束时间",
  Operation: "文件下载",
  DownloadFlie: "点击下载",
  FailedFlie: "下载失败",
  Notexist: "文件不存在",
  exportMS: "导出提示",
  exportID: "你本次导出任务ID为:",
  exportFlie: "你本次导出的文件名为:",
  downloadResult: "请前往下载管理-下载列表查看及下载",
  Goto: "立即前往",

  //产品运营
  product_operation: "产品运营",
  package_config: "套餐配置",
  individual_config: "单独配置",
  batch_config: "批量配置",
  inventory_mngr: "库存管理",
  task_query: "任务查询",
  porder_mngr: "个人订单管理",
  package_delay: "套餐延期",
  package_search: "未激活套餐查询",
  pakg_delay: "未激活套餐延期",
  delayTask_audit: "延期任务审核",
  activation_record: "激活记录查询",
  ordinary_package: "全球卡普通套餐",
  offline_mode: "终端厂商线下模式",
  online_mode: "终端厂商线上模式",
  cooperative_model: "合作运营商模式",
  SupplierOrder_management: "渠道商订单管理",
  ChannelCardOrders_management: "渠道商白卡订单管理",

  activateStat_search: "激活统计",
  activate_stat: "销量统计查询",
  availability_stat: "可用套餐统计",
  statEnd_month: "未激活套餐统计",

  //套餐

  packageManage: "套餐管理",
  packageIndex: "套餐管理",
  packageBatch: "批量编辑",
  packageAdd: "套餐添加",
  packageUpdate: "套餐编辑",
  packageInfo: "套餐详情",
  cardPoolDetail: "套餐卡池详情",
  packageCopy: "套餐复制",
  trafficPool_mngr: "流量池管理",
  addPool_mngr: "新建流量池",
  editPool_mngr: "修改流量池",
  copyPool_mngr: "复制流量池",
  detailsPool_mngr: "流量池详情",
  channelPool_mngr: "流量池管理",
  resourceCooperation: "资源管理",
  fuelPack_mngr: "加油包管理",
  upccTemplate_mngr: "UPCC速度模板管理",
  channelProvider_mngr: "渠道商套餐管理",
  targetedApplication_mngr: "定向应用管理",
  whiteListPackage_mngr: "白名单套餐管理",
  viewResources: "查看资源",
  callOrderDetails: "话单明细",
  billingStatistics: "账单统计",

  //码号资源
  resource: "码号资源管理",
  msisdn: "MSISDN管理",
  iccid: "ICCID管理",
  imsi: "IMSI管理",
  supplyImsi: "供应商IMSI管理",
  makeCardFile_mngr: "制卡文件管理",
  addCard_mngr: "新建制卡任务",
  otaData_mngr: "OTA数据管理",

  //短信管理
  smsManage: "短信管理",
  notificationSMS: "通知短信管理",
  notificationIndex: "通知短信",
  notificationAdd: "通知短信新增",
  notificationUpdate: "通知短信编辑",
  notificationInfo: "通知短信详情",
  areaWelcomeSMSIndex: "地区欢迎短信管理",
  areaWelcomeInfo: "地区欢迎短信详情",
  areaWelcomeEdit: "地区欢迎短信编辑",
  areaWelcomeEditAdd: "地区欢迎短信新增",
  customerSMS: "客服短信管理",
  customerIndex: "客服短信",
  marketingSMS: "营销短信管理",
  marketingIndex: "营销短信",
  marketingInfo: "营销短信详情",

  //客户管理
  customerManage: "客户管理",
  channelManage: "渠道商管理",
  channelIndex: "渠道商",
  billCheck: "账单核对",
  channelAdd: "渠道商新增",
  channelInfo: "渠道商详情",
  channelUpdate: "渠道商编辑",
  packageGroup: "套餐组",
  zeroLevelChannel: "零级渠道商",
  a2zBillPriceMngr: "流量计费价格管理",
  costPricingMngr: "成本价格管理",
  imsiFeeMngr: "IMSI费管理",
  zeroChannelAdd: "新建零级渠道商",
  zeroChannelUpdate: "修改零级渠道商",
  a2zBillPriceDetail: "流量计费价格管理—规则详情",
  costPricingDetails: "成本价格管理—详情",
  cooperativeManage: "合作运营商管理",
  cooperativeIndex: "合作运营商",
  cooperativeInfo: "合作运营商详情",
  postPaymentChannel: "后付费渠道管理",
  paymentChannelIndex: "后付费渠道",
  paymentChannelInfo: "后付费渠道详情",
  manufacturerManage: "终端厂商管理",
  manufacturerIndex: "终端厂商",
  manufacturerInfo: "终端厂商详情",
  manufacturer_update: "终端厂商编辑",
  billInfo: "账单详情",
  flowInfo: "流量明细",
  test_imsi: "测试IMSI管理",

  channel_mngr: "渠道自服务",
  deposit_mngr: "账户管理",
  mealList_mngr: "套餐详情",
  streamList_mngr: "流水详情",
  offlinePayment: "充值页面",
  marketingAccount :"营销活动详情",
  adminMarketingAccount: "营销活动详情",
  stock_mngr: "库存管理",
  cardList_mngr: "卡片详情",
  buymeal_mngr: "套餐购买",
  order_mngr: "订单管理",
  bill_mngr: "月账单",
  support_mngr: "服务与支持",
  address_mngr: "地址管理",
  detailsList_mngr: "服务与支持套餐详情",
  useList_mngr: "使用详情",
  operators_mngr: "运营商管理",
  ResourceSupplier_mngr: "资源供应商管理",
  operatorsindex_mngr: "运营商管理",
  report_mngr: "报表功能",
  exchange_mngr: "汇率管理",
  cardsell_mngr: "卡销售报表",
  cardactive_mngr: "卡激活报表",
  terminal_after_pay: "后付费结算报表",
  subscription_mngr: "套餐订购报表",
  activation_mngr: "套餐激活报表",
  reuse_mngr: "重复使用查询",
  analysis_mngr: "套餐分析报表",
  terminal_mngr: "终端结算报表",
  channelReport_mngr: "运营商结算报表",
  income_mngr: "线下收入报表",
  costReport_mngr: "成本报表",
  esimDownloadReport_mngr: "ESIM Download报表",
  flowPool: "流量池管理",
  poolList: "流量池详情",
  showiccid_mngr: "ICCID列表",
  flowpool_mngr: "流量池",
  cardlist_mngr: "卡号列表",
  flowlist_mngr: "流量池列表",
  userecord_mngr: "使用记录",
  iccidlist_mngr: "ICCID列表",
  channelcardlist_mngr: "卡号列表",
  channelflowlist_mngr: "流量池列表",
  channeluserecord_mngr: "使用记录",
  channeliccidlist_mngr: "ICCID列表",
  channelfuelPack_mngr: "加油包管理",
  channelpackage: "套餐管理",
  fuelPackManagement: "加油包管理",
  aqCode_mngr: "ESIM二维码管理",
  subChannelProvider_mngr: "子渠道商管理",
  whiteCardOrders_management: "白卡订单管理",
  channelResourceCooperation: "资源管理",
  channelViewResources: "查看资源",
  channelCallOrderDetails: "话单明细",
  channelBillingStatistics: "账单统计",
  channelBillingQuery: "渠道商账单查询",
  paymentOrderManagement: "支付记录查询",

  //产品管理
  product: "产品管理",
  makeCard: "制卡管理",
  masterCard: "主卡管理",
  cardPool: "卡池管理",
  vimsi: "VIMSI管理",
  associationGroup: "国家卡池关联组",
  addCardPool: "新增关联组",
  updateCardPool: "编辑关联组",
  copyCardPool: "复制关联组",
  cardPooldetails: "卡池详情",
  specialCountryRule: "特殊国家规则管理",
  specialCountryRuleAdd: "新增特殊国家规则",
  specialCountryRuleEdit: "编辑特殊国家规则",

  //客服支撑
  service_brace: "客服支撑",
  service_index: "卡片信息",
  local_search: "位置信息",
  purchased_package: "已购买套餐",
  location_package: "当前位置套餐",
  sppurchased_package: "已购买套餐",
  splocation_package: "当前位置套餐",
  //故障处理
  //新建故障处理
  fault_mngr: "故障处理",
  fault_add: "新建故障处理",
  // GTP话单地址配置
  GTPAddressConfig: "GTP话单地址配置",


  //财务系统管理
  finance_mngr: "财务系统管理",
  billing_mngr: "出账管理",
  history_mngr: "历史真实账单",
  acounting_period: "账期管理",
  serviceRecharge_approval: "自服务充值审批",
  finance_mngr: '财务系统管理',
  billing_mngr: '出账管理',
  history_mngr:'历史真实账单',
  acounting_period: '账期管理',
  serviceRecharge_approval: '自服务充值审批',
  billing_adjust: '账单调整',
  addBilling_adjust: '新增调账',
  aprv_details: '审批详情',


  // 渠道商销售数据
  channelSellindex_mngr: "首页",
  channelSellHistory_mngr: "历史账单",
  channelSell_mngr: "渠道商销售数据",
  topUp_records: "充值记录",

  //实名制管理
  realname_mngr: "实名制管理",
  rule_mngr: "规则管理",
  ruleDetail_mngr: "规则详情",
  certification_mngr: "人工认证",
  certificationinfo_mngr: "认证信息",

  //营销管理
  marketing_mngr: "营销管理",
  marketingActivityIndex: "营销管理",
  marketingActivityUpdate: "修改营销活动",
  marketingActivityAdd: "新增营销活动",
  // 押金管理/账户管理

  deposit: {
    deposit_money: "可用额度",
    mealList: "套餐详情",
    streamList: "流水详情",
    mealname: "套餐名称",
    search: "搜索",
    charge_time: "时间",
    canmeal: "可订购套餐",
    flow: "流水",
    mealId: "套餐id",
    mealprice: "套餐价格",
    charge_price: "充值金额",
    currency: "币种",
    chosetime: "请选择充值时间",
    ProductList: "可购买套餐导出",
    charge_type: "类型",
    Amount: "交易金额",
    accountdeposit: "账户金额",
    startTime: "请选择开始时间",
    endTime: "请选择结束时间",
    a2zDepositLimit: "A2Z预存款额度",
    channelMode: "渠道商模式",
    depositAccount: "押金账户",
    preDepositAccount: "预存账户",
    marketingAccount: "营销账户",
    creditAccount: "信用账户",
    marketingAccountFlow: "营销账户流水",
    marketingActivities: "营销活动",
    eventStartTime: "活动开始时间",
    eventEndTime: "活动结束时间",
    campaignStatus: "活动状态",
    rebateAmount: "返利金额",
    usedAmount: "已用金额",
    rebateBalance: "返利余额",
    arrivalTime: "到账时间",
    expirationTime: "到期时间",
    spendTime: "消费时间",
    effectiveTime: "生效时间",
    connectedActivities: "关联活动",
    consumptionAmount: "消费金额",
    accountTotalBalance: "营销账户总余额",
    settlementDate: "结算日期",
    pricingDate: "批价日期",
    singleActivityBalance: "单笔营销活动余额",
    marketingActivityDetails: "营销活动返利流水详情",
    marketingAccountDetails: "营销账户详情",
    totalOrderId: "总单ID",
    toBeStarted: "待开始",
    started: "已开始",
    ended: "已结束",
    obsolete: "已作废",
    earlyTerminate: "提前结束",
    increaseMarketinRebates: "增加营销返利款",
    distributionPackageOrdering: "代销套餐订购",
    startEndDate: "请选择起止日期！",
    totalOrderNumber: "订单编号",
    SubOrderNumber: "订单编号（子单）",
    ResetMarketingBudget:　"营销额过期清零",
    dataUsageSettlemtn: "流量结算",
    OffsetMarketingRebateAmount: "扣减营销返利款",
  },

  // 库存管理
  stock: {
    order_number: "任务名称",
    input_number: "请输入任务名称",
    timeslot: "时间段",
    chose_time: "请选择时间段",
    search: "搜索",
    details: "详情",
    Card_status: "卡状态",
    chose_status: "请选择卡状态",
    exporttb: "导出",
    card_number: "卡片数量",
    addtime: "创建时间",
    action: "操作",
    usedstate: "卡使用状态",
    cardtype: "卡类别",
    Code: "验证码:",
    PhysicalSIM: "普通卡（实体卡)",
    eSIM: "Esim卡",
    TSIM: "贴片卡",
    showiccid: "ICCID列表",
    Storagetime: "入库时间",
    attributableChannel: "归属渠道商",
    transfer: "划拨",
    subChannel: "子渠道商",
    subChannelProviderEmpty: "子渠道商不能为空",
    availablePackages: "是否有可用套餐",
    whitelistPackage: "白名单套餐",
    WhitelistPackageID: "白名单套餐ID",
  },
  // 套餐购买
  buymeal: {
    manual_batch: "手动输入",
    file_batch: "文件批量",
    chose_meal: "已选套餐",
    chose_mealtext: "请选择套餐",
    chose_number: "请输入卡号",
    input_number: "输入卡号",
    confirm: "确定",
    Reset: "重置",
    HK_dollar: "港元",
    payment: "共需支付",
    mealname: "套餐名称",
    search: "搜索",
    input_mealname: "请输入套餐名称",
    Selectall: "设置全选",
    Deselectall: "取消全选",
    upload: "点击或拖拽文件上传",
    Country: "国家/地区",
    amount: "套餐金额/元",
    selectCountry: "请选择国家/地区",
    Download: "下载模板文件",
    Downloadmsg: "文件仅支持csv格式文件,大小不能超过5MB",
    Choosemeal: "请选择套餐!",
    cycletype: "套餐周期类型",
    cycleNumber: "套餐周期数",
    filename: "文件名",
    time: "批量购买时间",
    chooseprice: "请选择套餐,获取折扣价!",
    choose: "选择",
    hour: "24小时",
    day: "自然日",
    month: "自然月",
    year: "自然年",
    cday: "每日",
    cmonth: "每月",
    cyear: "每年",
    Failedfile: "失败文件",
    clickdownload: "点击下载",
    tasksTotal: "任务总条数",
    successes: "成功条数",
    failed: "失败条数",
    Nofailed: "无失败文件",
    taskview: "历史任务查看",
    Taskstatus: "任务状态",
    Processing: "处理中",
    completed: "已完成",
    templatename: "套餐购买批量文件模板",
    Insufficient: "押金不足，请充值后再购买",
    purchase: "购买成功",
    toupload: "请选择需要上传的文件!",
    fileformat: "文件格式不正确",
    incorrect: "格式不正确，请上传.csv格式文件",
    Filesize: "文件大小超过限制",
    Exceeds: "超过了最大限制范围5MB",
    fileresult: "套餐购买批量文件_失败",
    Uploadfile: "确定",
  },
  // 订单管理
  order: {
    mealname: "套餐名称",
    input_mealname: "请输入套餐名称",
    chose_number: "请输入卡号",
    input_number: "卡号",
    timeslot: "时间段",
    chose_time: "请选择时间段",
    search: "搜索",
    monthly_bill: "查看月账单",
    exporttb: "导出",
    month: "月份",
    chose_month: "请选择月份",
    expenditure: "总支出",
    order_number: "订单号",
    order_state: "订单状态",
    count: "数量",
    order_money: "订单金额",
    addtime: "创建时间",
    isused: "套餐是否已使用",
    action: "操作",
    unsubscribe: "退订",
    ifunsubscribe: "确认退订该项",
    channels: "购买渠道",
    Website: "官网（H5)",
    BulkOrder: "批量售卖",
    Trial: "推广活动",
    Testing: "测试渠道",
    Datapool: "流量池web",
    ActivationTime: "使用时间",
    LocationUpdate: "最新位置",
    BeijingMobile: "北京移动",
    issuance: "合作发卡",
    Postpaid: "后付费发卡",
    Normal: "正常",
    Suspend: "注销",
    Expired: "过期",
    Terminated: "暂停",
    WeChat: "微信公众号",
    yes: "是",
    no: "否",
    delivered: "待发货",
    Completed: "完成",
    Cancelled: "已退订",
    approval: "激活退订待审批",
    Recycled: "已回收",
    Numberform: "号码登记表格.txt",
    Unsubscribe: "退订成功",
    channelOrderMoney: "渠道商订单金额",
  },
  // 服务与支持
  support: {
    cardtype: "主卡形态",
    chose_type: "请选择卡类型",
    cardstate: "卡片状态",
    chose_state: "请选择卡状态",
    pause: "暂停",
    input: "请输入",
    search: "搜索",
    mealList: "套餐详情",
    mealname: "套餐名称",
    input_mealname: "请输入套餐名称",
    timeslot: "时间段",
    chose_time: "请选择时间段",
    used_details: "使用详情",
    activation: "激活",
    frozen: "冻结",
    action: "操作",
    time: "结束时间",
    Verification_Code: "发送验证码",
    Activation: "H卡激活方式",
    isused: "是否已使用",
    meal_time: "套餐失效时间",
    cmeal_time: "套餐过期时间",
    Activation_state: "激活状态",
    used_flow: "使用流量",
    used_cycle: "使用周期",
    Report_time: "最新上报时间",
    Report_address: "上报位置",
    Targeting: "定位方式",
    template: "短信模板",
    position: "当前位置",
    Locationrecord: "位置更新记录",
    SendSMS: "下发客服短信",
    Flowdetails: "流量详情",
    Packagestatus: "套餐状态",
    Activationtype: "激活类型",
    Ordernumber: "订单编号",
    Ordertime: "订单时间",
    Orderchannel: "订单渠道",
    Activationmethod: "激活方式",
    sendingmethod: "发送方式",
    chosesend: "请选择发送方式",
    phone: "手机号码",
    phoneprompt: "请输入手机号",
    chosetemplate: "请选择短信模板",
    sending: "发送短信",
    usedetails: "流量使用详情",
    date: "日期",
    useflow: "使用流量(G)",
    close: "关闭",
    Periodtype: "周期类型",
    Continuouscycle: "持续周期",
    Activatepackage: "激活套餐",
    MSISDNenter: "请输入MSISDN",
    ICCIDenter: "请输入ICCID",
    IMSIenter: "请输入IMSI",
    Recycle: "提前回收",
    ReplaceVIMSI: "更换VIMSI",
    Automatic: "自动激活",
    Manual: "手动激活",
    Sendingempty: "发送方式不能为空",
    SMSempty: "短信模板不能为空",
    Phoneempty: "手机号码不能为空",
    PhoneWrong: "手机号码格式错误",
    Unuse: "待激活",
    InUse: "使用中",
    Expired: "已过期",
    Activatedpending: "已激活待计费",
    Activating: "激活中",
    activated: "已激活",
    Used: "已使用",
    CNY: "人民币",
    USD: "美元",
    HKD: "港币",
    VIMSILocation: "VIMSI位置更新详情",
    Cardtype: "卡片类型",
    Flowg: "流量(G)",
    VIMSIphone: "IMSI号码",
    TimeLocation: "位置上报时间",
    Location: "位置上报地点",
    Termination: "确认提前回收?",
    replacement: "确认更换VIMSI?",
    VIMSIdetails: "VIMSI分配详情",
    IMSIdetails: "IMSI上网详情",
    targetedAppDetails: "定向应用上网详情",
    cardtraffic: "查询V卡流量",
    QueryH: "查询H卡流量",
    cardflow: "H卡/V卡流量",
    packageflow: "套餐总流量(MB)：",
    remainingflow: "当前剩余流量(MB)：",
    Usedtraffic: "已使用流量(MB)：",
    Usedflow: "已使用流量",
    countryregion: "使用国家/地区",
    flowquery: "套餐流量查询",
    back: "返回",
    Vcard: "V卡",
    Hcard: "H卡",
    searchcondition: "请至少填写一项搜索条件",
    obtained: "因未获取到当前卡位置,列表加载失败",
    advance: "套餐已过期,不能提前回收",
    Sentsuccessfully: "发送成功",
    SIMDate: "过期时间",
    Locationexport: "位置更新记录导出",
    recordexport: "流量详情记录导出",
    operationFailed: "操作失败,仅支持使用中状态套餐",
    VoperationFailed: "因未获取到当前卡位置,无法更换VIMSI",
    usageTotal: "流量总量",
    operatorName: "运营商名称",
    Complete: "实名认证状态",
    registration: "实名认证详情",
    IDnumber: "证件ID:",
    IDtype: "证件类型:",
    Issuingcountry: "护照国家:",
    approval: "待认证",
    process: "认证中",
    Approved: "认证通过",
    Rejected: "认证失败",
    IDexpired: "证件过期",
    registrationrules: "实名制认证规则",
    registrationcountry: "规则覆盖国家",
    Passport: "护照",
    Permit: "港澳通行证",
    HKIdentityCard: "香港身份证",
    MacauIdentityCard: "澳门身份证",
    view: "点击查看",
    DataUsedDay: "当日已用流量",
    DataRestrictionType: "流量限制类型",
    DataRestrictionCycle: "周期内限量",
    DataRestrictionSingle: "按周期类型重置",
    ControlLogicLimit: "达量后控制逻辑",
    RestrictedSpeedLimit: "达量限速",
    ReleaseAfterLimit: "达量释放",
    InternetStatus: "上网状态",
    Normal: "正常",
    RestrictedSpeed: "限速",
    DataCap: "流量上限",
    NameChinese: "姓名(中文):",
    NameEnglish: "姓名(英文):",
    hightDataCap: "高速流量上限",
    payBills: "缴付账单",
    remunerationReturn: "酬金返还",
    increaseDeposit: "增加押金",
    PreDeposit: "增加预存款",
    packageOrder: "套餐订购",
    fuelPackpackageOrder: "加油包订购",
    packageCancellation: "套餐退订",
    fuelPackUnsubscribe: "加油包退订",
    hightDataCap: "套餐高速流量上限",
    UsedAddonPack: "加油包已用流量",
    ToppedAddonPack: "加油包充值流量",
    WaitingUser: "待用户提交认证信息",
    AuthFailed: "【认证失败】，待用户重新提交认证信息",
    NotCertified: "未认证",
    flowpoolApi: "流量池API",
    NoCertification: "无需认证",
    querycontent: "操作失败,查询内容不存在",
    imsiType: "IMSI类型",
    useCountry: "使用国家/地区",
    imsiResource: "IMSI资源供应商",
    activeTime: "激活时间",
    latestActivationDate: "最晚激活时间",
    Refunded: "已退款",
    Available: "可选运营商",
    channelIncomeAdjustment: "渠道商收入调账",
    marketingRebate: "营销返利",
    imsiFeeStatistics: "imsi费统计",
    indemnity: "赔付",
    errorDesc: "认证失败原因:",
    nameIsInconsistent: "姓名校验不一致",
    certificateHasExpired: "证件已过期",
    IDIsInconsistent: "证件ID校验不一致",
    sixteenyYearsOld: "未满16周岁",
    picturesAreNotSatisfied: "上传图片不满足",
    EsimDetails: "ESIM信息查询",
    smDpAddress: "SM-DP+地址",
    activationCode: "激活码",
    esimStatus: "ESIM-状态",
    eid: "EID",
    installationTime: "安装时间",
    installationEquipment: "安装设备",
    instalAmount: "安装次数",
    updateTime: "更新时间",
    generateQRCode: "生成二维码",
    msisdnImsiIccid: "请选择msisdn、imsi、iccid其中一项！",
    timeFrame: "时间范围",
    orderBatch: "订单批次",
    inputOrderBatch: "请输入订单批次",
    segmentSearch: "号段搜索",
    startNumber: "起始号码",
    endNumber: "结束号码",
    codeExport: "二维码导出",
    fileExport: "文件导出",
    xiazai: "下载",
    operate: "操作",
    inputPackageID: "请输入套餐ID",
    edit2: "编辑",
    copy: "复制",
    approvalStatus: "审批状态",
    newApproval: "新建待审核",
    approve: "通过",
    notApprove: "不通过",
    modificationApproval: "修改待审批",
    deleteApproval: "删除待审批",
    fiveCharacters: "最多支持500字符",
    fourtCharacters: "最多可支持4000字符",
    packageDescription: "套餐描述",
    selectType: "请选择周期类型",
    selectDuration: "请选择持续周期",
    packageValidity: "套餐购买有效期",
    inputPackageValidity: "请输入套餐购买有效期",
    selectDataResetType: "请选择流量限制类型",
    selectRestrictionLogic: "请选择控制逻辑",
    aupportHotspot: "是否支持热点",
    isAupportHotspot: "请选择是否支持热点",
    usage2: "用量值",
    selectUsage: "请选择用量值",
    inputUsage: "请输入用量值",
    wrongUsageFormat: "请输入1-10位整数数字",
    speedTemplate: "速度模板",
    selectSpeedTemplate: "请选择速度模板",
    unlimitedUsageTemplate: "无上限模板",
    selectUnlimitedUsageTemplate: "请选择无上限模板",
    packageNameNotNull: "套餐名称不能为空",
    packageDescriptionNotNull: "套餐描述不能为空",
    prongDurationFormat: "持续周期格式错误",
    DurationValueLarge: "持续周期数值过大",
    wrongPackageValidity: "购买有效期(天)格式错误",
    packageValidityLarge: "购买有效期(天)数值过大",
    qrPicture: "二维码图片",
    pakageId: "套餐ID",
    day: "天",
    add: "添加",
    create: "新增",
    createPackage: "新增套餐",
    operator: "运营商",
    network: "网络类型",
    subChannelName: "子渠道商名称",
    contactEmail: "联系人邮箱",
    purchasePackage: "可购买套餐",
    profitMargin: "利润率",
    totalAmount: "总额度",
    accountPermissions: "账号权限",
    channelAppSecret: "渠道商App Secret",
    channelAppKey: "渠道商App Key",
    selectCharacter: "选择角色",
    submit: "提交",
    clickToUpload: "点击上传",
    downloadfile: "下载模版",
    pleaseUploadFile: "请上传文件",
    subChannelEmpty: "子渠道商名称不能为空",
    subChannelAmpersand: "子渠道商名称不能包含&符号",
    contactEmailEmpty: "联系人邮箱不能为空",
    EmailFormatError: "联系人邮箱格式错误",
    fuelPackProfitMarginEmpty: "加油包利润率不能为空",
    totalAmountEmpty: "总额度不能为空",
    accountPermissionsEmpty: "账号权限不能为空",
    subChannelDetails: "子渠道商详情",
    editSubChannel: "编辑子渠道商",
    addSubChannel: "新增子渠道商",
    uploadFailed: "上传失败!",
    files: "文件",
    fileUploadedAndProgressDisappears: "文件上传中、待进度条消失后再操作",
    downTemplateFilelAndUpload: "请先下载模板文件，并按格式填写后上传",
    imsi: "IMSI号",
    packageProfitMargin: "套餐利润率",
    fuelPackProfitMargin: "加油包利润率",
    failureReason: "不通过原因",
    supportAddon: "是否支持加油包",
    bindAddon: "绑定加油包",
    AddonList: "加油包列表",
    newAddon: "添加加油包",
    AddonName: "加油包名称",
    AddonID: "加油包ID",
    AddonAmount: "加油包流量值",
    AddonListMandatory: "加油包列表不能空",
    createOrder: "新增订单",
    revoke: "撤销",
    downloadInvoice: "下载Invoice",
    downloadList: "下载号码列表",
    uploadPayslip: "上传付款证明",
    customizedSIM: "定制卡",
    PhysicalSIM: "普通卡",
    receiveeAddress: "收货地址",
    receiverName: "收件人",
    contactNumber: "联系电话",
    distribution: "代销",
    atoz: "A2Z",
    cooperationModel: "合作模式",
    payslip: "付款证明",
    uploadPicture: "点击或拖拽文件上传到此处",
    pictureSize: "超过了最大限制范围10MB",
    paymentMethod: "收费模式",
    deliveryCompany: "物流公司",
    trackingNumber: "物流编号",
    ordered: "已下单",
    cancelled: "已取消",
    pendingPayment: "待付款",
    paid: "已付款",
    deliveryProgress: "发货中",
    delivered: "已发货",
    deliveryFailed: "发货失败",
    PaymentConfirmed: "付款已确认",
    PaymentNotConfirmed: "付款未到账",
    PaymentMandatory: "普通/定制卡不能为空",
    cardTypeMandatory: "主卡形态不能为空",
    cardMumberMandatory: "卡片数量不能为空",
    wrongFormat: "格式不正确",
    addressMandatory: "收货地址不能为空",
    receiverMandatory: "收件人不能为空",
    contactNumberMandatory: "联系电话不能为空",
    cooperationMandatory: "合作模式不能为空",
    picetureMandatory: "文件不能为空",
    confirmRevocation: "确认撤销？",
    paymentMethod: "收费模式",
    description: "说明！",
    newAddonPack: "新增加油包",
    determine: "确认",
    flowValue: "流量值",
    addAmountMandatory: "加油包流量值不能为空",
    cannotExceed: "超过了最大限制范围104857600MB",
    generateInvoice: "生成Invoice时间",
    uploadPayslipTime: "上传付款证明时间",
    orderConfirmTime: "订单确认时间",
    deliveryTime: "发货时间",
    paymentConfirmed: "付款待确认",
    physicalOrCustomized: "CMLINK/定制卡",
    inputFlowValue: "请输入加油包流量值",
    inputAddonName: "请输入加油包名称",
    inputAddonId: "请输入加油包ID",
    inputCardNumber: "请输入卡片数量",
    inputRecrver: "请输入收件人",
    inputAddress: "请输入收货地址",
    selectCardtype: "请选择主卡形态",
    productPackaging: "产品包装",
    selectProductPackaging: "请选择产品包装",
    productPackagingEmpty: "产品包装不能为空",
    packagingCard: "包装卡",
    nakedCard: "裸卡",
    receivingCountry: "收货国家",
    selectReceivingCountry: "请输入收货国家",
    receivingCountryEmpty: "收货国家不能为空",
    postalCode: "邮编",
    selectPostalCode: "请输入邮编",
    postalCodeEmpty: "邮编不能为空",
    language: "语言",
    selectLanguage: "请选择语言",
    languageEmpty: "语言不能为空",
    agreement: "请勾选协议",
    agree: "我同意此",
    Agreements: "协议",
    orderId: "订单ID",
    replaceHIMSI: "替换HIMSI",
    isSupportHot: "当前上网是否支持热点",
    internetTemplateSpeed: "当前模板上网速度",
    AgreementContent: "协议内容",
    resourceCooperation: "资源合作",
    // 覆盖时间
    coverageTime: "覆盖时间",
    //信息查询 Information query CDR明细 CDR details 更多信息 More information 流量信息 Data information 回滚中 Rollback in progress 回滚成功 Rollback success 存在回滚失败 Rollback failure
    informationQuery: "信息查询",
    cdrDetails: "CDR明细",
    moreInformation: "更多信息",
    dataInformation: "流量信息",
    rollbackInProgress: "回滚中",
    rollbackSuccess: "回滚成功",
    rollbackFailure: "存在回滚失败",
    //数据为空
    emptyData:"未查询到套餐信息",
    validityPeriod: "实名制有效期",
    longTerm: "长期有效",
    singlePackageEffective: "单次套餐生效",
  } ,
  //定向应用
  directionalApp: {
    application: "应用",
    freeFlow: "免流",
    supportUsage: "是否支持定向流量",
    selectSupportUsage: "请选择是否支持定向流量",
    selectAPP: "选择应用",
    pleaseSelectAPP: "请选择应用",
    deleteAPP: "删除应用",
    addAPP: "添加应用",
    specificAPPLogic: "定向使用逻辑",
    inputDataValue: "请输入流量值",
    deleteDataValue: "删除流量值",
    addDataValue: "添加流量值",
    dataValue: "流量值",
    selectUPCCTemplate: "选择UPCC模板",
    pleaseSelectUPCCTemplate: "请选择UPCC模板",
    continueDataUsage: "是否继续使用通用流量",
    PleaseDataUsage: "请选择是否继续使用通用流量",
    restrictedTemplate: "定向限速模板",
    pleasepRrestrictedTemplate: "请选择定向限速模板",
    FreeTemplate: "定向免流限速模板",
    pleaseFree: "请选择定向免流限速模板",
    FreeContinueTemplate: "定向免流继续使用模板",
    pleaseFreeContinue: "请选择定向免流继续使用模板",
    SupportDataMandatory: "是否支持定向流量不能为空",
    number9: "选择应用总数不能超过9个",
    APPMandatory: "选择应用不能为空",
    LogicMandatory: "定向使用逻辑不能为空",
    valueMandatory: "流量值不能为空",
    twoTemplate: "定向免流继续使用模板/定向免流限速模板不能为空",
    ContinueMandatory: "是否继续使用通用流量不能为空",
    upccMandatory: "选择UPCC模板不能为空",
    valueRepeat: "流量值不能重复",
    appRepeat: "应用不能重复",
    dingTemMandatory: "定向限速模板不能为空",
    useTemMandatory: "定向免流继续使用模板不能为空",
    freeTemMandatory: "定向免流限速模板不能为空",
    gearRule: "流量值逻辑不正确，每档的流量值需大于上一档次的流量值",
    usageRule: "用量值逻辑不正确，每档的用量值需大于上一档次的用量值",
  },
  // 地址管理
  address: {
    deleteitem: "确认删除该项?",
    fullname: "姓名",
    input_fullname: "请输入姓名",
    mailbox: "邮箱",
    input_mailbox: "请输入邮箱地址",
    Newaddress: "新建地址",
    modify: "修改",
    modifyaddress: "修改邮箱",
    Delete: "删除",
    Forgetpwd: "修改密码",
    setdefault: "设置默认",
    search: "搜索",
    password: "密码",
    password_ok: "确认密码",
    input_pwd: "请输入密码",
    newpwd: "新密码",
    account: "账号",
    action: "操作",
    oldPwd: "原密码",
    PwdRules: "密码设置时需要满足给定规则，点击",
    watch: "查看",
    more: "了解详情",
    Rules1: "确保口令满足以下通用原则",
    Rules2:
      "1、口令至少由8位及以上大写字母、小写字母、数字与特殊符号等4类中3类混合、随机组成，尽量不要以姓名、电话号码以及出生日期等作为口令或者口令的组成部分；",
    Rules3:
      "2、口令应与用户名无相关性，口令中不得包含用户名的完整字符串、大小写变位或形似变换的字符串，如teleadmin:teleadmin、teleadmin:teleadmin2017、teleadmin:TeleAdmin、teleadmin:te!e@dmin等；",
    Rules4:
      "3、应更换系统或设备的出厂默认口令，如huawei:huawei@123，oracle数据库中SYS:CHANGE_ON_INSTALL,某移动定制版光猫默认帐号CMCCAdmin:aDm8H%MdA等；",
    Rules5:
      "4、口令设置应避免3位以上（含3位）键盘排序密码，如qwe（键盘第1行前三个字母）、asd（键盘第2行前三个字母）、qaz（键盘第1列三个字母）、1qaz（键盘第1列数字加前三个字母）、！QAZ（键盘第1列特殊字符加前三个字母）等；",
    Rules6:
      "5、口令中不能出现3位以上（含三位）连续字母、数字、特殊字符，如ABC、Abc、123、！@#等；",
    Rules7:
      "6、口令中不能出现3位以上（含三位）重复字母、数字、特殊字符，如AAA、Aaa、111、###等。",
    Rules8: "避免以下易猜解口令规则",
    Rules9:
      "1、省份、地市名称、邮箱、电话区号、邮政编码及缩写和简单数字或shift键+简单数字，如BJYD123、HBYD!@#等；",
    Rules10:
      "2、单位名称、专业名称、系统名称、厂家名称（含缩写）和简单数字，如HBnmc123、HBsmc_123等；",
    Rules11:
      "3、维护人员名字全拼大小写缩写等变形+设备IP地址（一位或两位）或出生年月日等，如维护人员张三，维护设备地址为************和************,出生日期为19951015，则其可能的弱口令为zhangsan100、zhangsan101，zhangsan10100，zhangsan10101，zhangsan19951015，ZS19951015等；",
    Operationreminder: "操作提醒",
    deleted: "删除成功!",
    inconsistent: "两次密码，输入不一致",
    emailaddress: "请输入有效的邮箱地址",
    reset: "密码格式不规范，请重新填写",
    appear: "不可出现3个连续且相同的字符",
    allowed: "不可出现3位连续的数字",
    determine: "确定",
  },
  //流量池管理
  flow: {
    inputICCID: "请输入ICCID",
    Channel: "渠道商",
    Status: "状态",
    chooseStatus: "请选择状态",
    toassigned: "待分配",
    Assigned: "已分配",
    typelimit: "单周期类型限量",
    Totallimits: "总限量",
    Controllogic: "控制逻辑",
    Stoplimit: "达量停用",
    speedlimit: "达量限速",
    Continuelimit: "达量继续使用",
    Originlimit: "归属流量池",
    inputPoolname: "请输入流量池名称",
    poolName: "流量池名称",
    Usagestatus: "使用状态",
    chooseStatus: "请选择使用状态",
    Normal: "正常",
    Restricted: "限速",
    Stop: "停用",
    Pleasechoose: "请选择上架状态",
    upStatus: "上架状态",
    Online: "上架",
    Offline: "下架",
    Useddata: "已用流量",
    Numbericid: "卡号数量",
    threshold: "提醒阈值",
    ImportICCID: "ICCID导入",
    Batchdelete: "批量删除",
    Batchupdate: "批量修改",
    ImportTime: "入池时间",
    Singleimport: "单个导入",
    Batchimport: "批量导入",
    Choosepool: "选择流量池",
    plesepool: "请选择流量池",
    ICCIDempty: "ICCID不能为空",
    Monocyleempty: "单周期类型上限不能为空",
    Totalempty: "总上限不能为空",
    choosecontrollogic: "请选择控制逻辑",
    Unit: "单位",
    units: "单位",
    UploadICCID: "上传ICCID列表",
    iccidFileEmpty: "ICCID列表文件不能为空",
    Batchtime: "批量导入时间",
    Importtotal: "导入总数",
    Numbersuccess: "成功数量",
    Numberfailure: "失败数量",
    Successfile: "成功文件",
    Failurefile: "失败文件",
    Usagethreshold: "用量提醒阈值",
    Percentage: "百分比",
    Usageempty: "用量提醒阈值不能为空",
    Usagerecord: "使用记录",
    Choosemonth: "选择月份",
    Choosedate: "选择起止日期",
    PleaseChoosedate: "请选择日期范围",
    Pleasemonth: "请选择月份",
    Billmonth: "账单月份",
    UsagestartTime: "使用开始时间",
    UsageendTime: "使用结束时间",
    Ratedtotalusage: "额定总流量",
    Actualtotalusage: "实际使用总流量",
    Excessusage: "超额流量",
    Ratedcharge: "额定费用(元)",
    Excesscharge: "超额费用(元)",
    Totalcharge: "总费用(元)",
    Details: "详情",
    Clickview: "点击查看",
    IMSI: "HIMSI号码",
    VIMSI: "VIMSI号码",
    Numbertype: "号码类型",
    StartTime: "开始时间",
    EndTime: "结束时间",
    Usage: "用量",
    totalusage: "总流量",
    year: " 年 ",
    month: " 月 ",
    dday: " 日 ",
    flowyear: "自然年",
    flowmonth: "自然月",
    Totallimit: "总上限",
    Usagedetails: "使用记录详情",
    Pleasethreshold: "请输入用量提醒阈值",
    Pleaseinteger: "请输入正整数",
    Validdate: "有效日期",
    Resetnumber: "重置周期数",
    Resettype: "重置周期类型",
    Monocyletype: "单周期类型上限",
    Cardcycle: "单卡周期达量限速",
    Stopdatalimit: "单卡周期达量停用",
    Restrictedspeed: "单卡总量达量限速",
    Totallimitcard: "单卡总量达量停用",
    Datapoollimit: "流量池总量达量限速",
    stoppoollimit: "流量池总量达量停用",
    chooserecord: "请至少选择一条记录",
    Confirmdelete: "确认删除",
    Dataused: "已使用流量",
    Originenterprise: "归属企业",
    Positivenumber: "最高支持8位整数和2位小数的正数或零",
    // yearandmonth: '账单年月',
    yearandmonthdate: "账单日期",
    day: "自然日",
    Country: "支持国家",
    deleteNumber: "移除",
    poolAvailableTime: "入池可用时长",
    fillNumber: "填写可用的单周期数量",
    exportflowsum: "流量汇总导出",
    remark: "备注",
    inputRemark: "请输入备注",
    enterRemark: "填写备注信息",
    internetStatus: "上网状态",
    inputinternetStatus: "请输入上网状态",
    recover: "恢复",
    cardManager: "单卡管理",
    mb: "单位MB",
    totallimit: "总上限",
    availableday: "可用时长",
    none: "无",
    confirmPause: "确认暂停该项？",
    confirmResume: "确认恢复该项？",
    kongge: "不允许输入空格",
    expirationDate: "到期时间",
    SelectDestination: "选择国家/地区",
    select: "查询",
    usageMB: "用量(MB)",
    requiredDataExport: "请选择以下需要的导出数据",
    chooseOne: "请至少选择一个",
    negative: "不能为负数",
  },
  //加油包管理
  fuelPack: {
    startDate: "套餐开始时间",
    endDate: "套餐结束时间",
    packagedata: "当前套餐流量上限(MB)",
    adddata: "已购买加油包流量(MB)",
    usedhigh: "已使用高速流量(MB)",
    Purchase: "购买加油包",
    SelectfuelPack: "选择加油包",
    PleaseSelectfuelPack: "请选择加油包",
    quantity: "选择加油包数量",
    Currentday: "当日加油包",
    Daily: "每日加油包",
    //当月加油包
    CurrentMonthly: "当月加油包",
    //每月加油包
    Monthly: "每月加油包",
    //当年加油包
    CurrentYear: "当年加油包",
    //每年加油包
    Yearly: "每年加油包",
    Amount: "金额",
    onlinestatus: "当前上网状态",
    purchasequantity: "请输入购买数量",
    Specificdate: "指定激活日期",
    SelectDate: "选择日期",
    PleaseSelectDate: "请选择日期",
    activationType: "套餐确认激活方式",
    Activatelimit: "达量激活",
    ActivateSpecific: "指定日期激活",
    ActivateLU: "LU激活",
    packageTtart: "选择套餐生效日期",
    activationdate: "限定激活日期",
    buyfuelPack: "加油包购买后，立即生效，不支持退订，是否确认购买？",
    price: "价格",
    Remainingdays: "剩余天数:",
  },

  //渠道自服务 合作模式
  selectCooperationMode: "请选择渠道商合作模式",
  consignmentSalesModel: "代销模式",
  A2Zmode: "A2Z模式",
  resourceMode: "资源合作模式",
  welcomeWebsite: "欢迎使用CMI全球卡业务网站",
  totalCards: "卡总数",
  announcement: "公告栏",
  esimTotal: "ESIM卡总数",
  tsimTotal: "贴片卡总数",
  imsiTotal: "IMSI卡总数",
  imsika: "IMSI卡",
  shitika: "实体卡",
  simTotal: "实体卡总数",
  quotaUsedMonth: "当月已用额度",
  AtoZTotal: "A2Z总信用额度",
  creditsUsed: "已用信用额度",
  unused: "未使用",
  usedLimit: "已用额度",
  yuan: "元",
  packageOrdered: "套餐订购数",
  months12: "近12个月数据",
  twofivebefore: "25日之前: 上月25日至今",
  twofiveafter: "25日之后: 本月25日至今",
  esimkazhang: "ESIM卡(张)",
  imsikazhang: "IMSI卡(张)",
  simkazhang: "实体卡(张)",
  cardkazhang: "卡总数(张)",

  //资源管理
  resourceManage: {
    channelName: "渠道商名称",
    enterChannelName: "请输入渠道商名称",
    allocateResources: "分配资源",
    resourceView: "资源查看",
    resourceSupplier: "资源供应商",
    selectResourceSupplier: "请选择资源供应商",
    imsiNumber: "IMSI数量",
    selecyImsiNumber: "请输入IMSI数量",
    routingID: "路由ID",
    selectRoutingID: "请输入路由ID",
    resourceSupplierMandatory: "资源供应商不能为空",
    imsiNumberMandatory: "IMSI数量不能为空",
    routingIDMandatory: "路由ID不能为空",
    selectVIMSIphone: "请输入IMSI号码",
    batchFreeze: "批量冻结",
    BatchRecovery: "批量恢复",
    seeInformation: "查看信息",
    modifyingResources: "修改资源",
    BatchModifyingResources: "批量修改资源",
    FreezeItem: "确认冻结该项?",
    RecoverItem: "确认恢复该项",
    confirmFreeze: "确认冻结?",
    confirmRecover: "确认恢复?",
    operationFail: "操作失败",
    Dimension: "选择统计维度",
    selectDimension: "请选择统计维度",
    dateOrMonth: "日期/月份",
    fee: "费用",
    imsiPhone: "IMSI号码",
    selectImsiPhone: "请输入IMSI号码",
    routingExceeds: "超过了最大限制范围**********",
    numberExceeds: "超过了最大限制范围100000",
  },
  // 渠道商账单查询
  channelBill: {
    startMonth: "开始月份",
    endMonth: "结束月份",
    selectStart: "请选择开始月份",
    selectEnd: "请选择结束月份",
    dataUsage: "流量计费查询",
    inputChargesName: "请输入计费名称",
    imsiFee: "IMSI费查询",
    detailed: "明细",
    billFileDownload: "账单明细下载",
    invoiceDownload: "Invoice下载",
    payslip: "付款",
    reUpload: "重新上传",
    dataFee: "流量费",
    cardFee: "卡费",
    packageFee: "套餐费",
    paymentPage: "缴付页面",
    inputAmount: "请输入金额",
    checkNumber: "最高支持8位整数和2位小数的正数",
    imsiFeeType: "IMSI费规则",
    imsiFeeAmount: "IMSI费金额",
    quantityRange: "数量区间",
    chargesName: "计费名称",
    country: "国家",
    cny: "人民币单价",
    hkd: "港币单价",
    usd: "美元单价",
    billId: "账单ID",
    billType: "账单类型",
    paymentMonth: "出账月份",
    totalBillAmount: "账单总金额",
    accountsPayableAmount: "应缴账单金额",
    paymentStatus: "缴费状态",
    verified: "已核销",
    unpaidPayment: "未付款",
    confirmationReceipt: "待确认到账",
    Arrived: "付款成功",
    NotCredited: "付款失败",
    merge: "合并",
    endGreaterStart: "结束月份需大于等于开始月份！",
    lessThan0: "不能小于0",
    onlinePayment: "线上支付",
    offlinePayment: "线下支付",
    OnlinePaymentInProgress:"线上缴费中",
    cnInvoice: 'CN Invoice',
    uploadCnInvoice: '上传CN Invoice',
    cnInvoiceDownload: 'CN Invoice下载',
    pleaseUploadCnInvoice: '请上传CN Invoice文件'
  },
  // 线上订单管理
  onlineOrder: {
    orderUniqueId: "订单ID",
    orderName: "产品名称",
    orderType: "订购类型",
    corpId: "渠道商ID",
    orderUserName: "购买用户名称",
    productId: "产品ID或账单ID",
    thirdOrderNo: "第三方支付订单标识",
    thirdMchorderNo: "第三方支付订单号",
    thirdTransactionNo: "第三方支付交易流水号",
    currencyCode: "货币类型",
    amount: "金额",
    orderStatus: "订单状态",
    paymentMethod: "支付方式",
    paymentStatus: "支付状态",
    paymentTime: "支付时间",
    paymentIp: "用户IP",
    paymentReference: "支付凭证ID或脱敏后的卡号",
    exprieTime: "支付截止时间",
    asyncNotifyType: "异步结果通知类型",
    sendLang: "语言",
    isDeleted: "逻辑删除标记",
    createTime: "创建时间",
    updateTime: "更新时间",
    email: "邮箱",
    choosePaymentStatusPlaceholder: "请选择订单状态",
    thirdOrderNoPlaceholder: "请输入第三方支付订单标识",
    thirdTransactionNoPlaceholder: "请输入第三方支付交易流水号",
    thirdMchorderNoPlaceholder: "请输入第三方支付订单号",
    chooseCreateDate: "请选择创建时间段",
    onlineModalTitle: "线上支付",
    payTxt01: "交易将在",
    payTxt02: "后关闭",
    payBtn: "支付",
    orderTypeBill: "账单缴费",
    orderTypedeposit: "押金充值",
    wechat: "微信",
    alipay: "支付宝",
    card: "银行卡",
    paying: "待支付",
    paySuccess: "支付成功",
    payExpired: "订单已过期",
    payclosed: "订单已关闭",
    closeOrderContent: "确定要关闭此订单吗？",
    deleteOrderContent: "确定要删除此订单吗？",
    deleteSuccess: "删除成功",
    closeSuccess: "关闭成功",
    paidAmount: "实付金额：",
    depositAmount: "押金金额：",
    weChatPayChina: "微信支付（中国内地版）",
    alipayChina: "支付宝（中国内地版）",
    debitCreditCard: "储蓄卡/信用卡",
    depositAmountPlaceholder: "请输入押金金额",
    loadingStatus: "加载中",
    correctDepositAmount: "请输入正确的金额",
    depositAmountGreaterThanZero: "金额必须大于0",
    selectPaymentMethod: "请选择支付方式",
    correctBankCardInfo: "请输入正确的银行卡信息",
    amountCannotBeEmpty: "金额不能为空",
    validPositiveNumber: "请输入有效的正数，最多两位小数",
    positiveNumberGreaterThanZero: "请输入大于0的正数",
    stateIsValid: "state.isValid:",
    paymentMethodType: "state.data.paymentMethod.type:",
  },
  paymentResultpageTexts: {
    paymentFailed: "支付提醒",
    paymentSuccessful: "支付提醒",
    errorDetails: "支付已提交，请点击下方按钮查看支付详细信息。",
    successDetails: "支付已提交，请点击下方按钮查看支付详细信息。",
    errorReason: "失败原因：",
    thanksMessage: "感谢您的支持",
    viewOrder: "查看订单",
    goToHome: "去首页",
  },
  //线下支付
  offlinePay: {
    applyInvoice: '申请发票',
    pay: '缴付',
    reApplyInvoice: '重新申请发票',
    rePay: '重新上传付款证明',
    invoiceType: '发票类型',
    topupID: '充值ID',
    invoiceNumber: '发票号',
    deposit: "押金",
    Prepayment: "预存",
    applyTime: '申请时间',
    unpaid: "未缴费",
    invoicePendingApro: "发票审批中",
    invoiceAproReject: "发票审批拒绝",
    payable: "可缴费",
    payPendingApro: "缴费审批中",
    paymentAproReject: "缴费审批拒绝",
    paid: "已缴费",
    attachment: '附件',
    onlineTopup: '线上充值',
    offlineTopup: '线下充值',
    topup: '充值',
    topupAmount: "充值金额",
  },

  country: {
    select: "选择国家",
    selectAll: "全选",
    selected: "已选国家",
    nameCn: "国家名称(中文)",
    nameEn: "国家名称(英文)",
    continentCn: "所属大洲(中文)",
    continentEn: "所属大洲(英文)",
    specialRuleMngr: "特殊国家规则管理",
    all: "全部",
    noData: "暂无数据",
    viewAll: "查看全部国家",
    selectingContinent: "正在选择当前大洲国家...",
    deselectingContinent: "正在取消选择当前大洲国家...",
    getContinentFail: "获取大洲列表失败",
    getCountryFail: "获取国家列表失败",
    getAllCountryFail: "获取全部国家数据失败",
    alreadySelected: "已经选择了国家: ",
    operationFail: "操作失败：",
    editCountry:"编辑国家"
  },

  sessionInfo: {
    sessionDetail: "会话详情",
    realtimeSession: "实时会话信息",
    historySession: "历史会话信息",
    field: "字段",
    currentVlaue: "当前值",
    accessSite: "接入站点" ,
    online: '是否在线',
    sessionStartTime: "会话开始时间",
    dataUsageDuringSession: "会话内产生的流量",
    serviceProviderUsed: "使用的运营商",
    userIPAddress: "用户IP地址",
    PGWSiteAccessedSession: "会话接入的PGW站点",
    sessionQuery: "Session查询",
    UsageMax: "用量值超过最大限制 ",
    UsageMin: "用量值不能小于10MB",
  }
};

