export default {

  //系统
  home: '首頁',
  login: '登錄',
  system_mngr: '系統管理',
  account_list: '賬號管理',
  log_mngr: '操作日誌',
  Announcement_mngr: '公告管理',
  login_mngr: '登錄日誌',
  pwd_mngr: '賬戶密碼管理',
  pri_mngr: '角色管理',
  //按钮
  sys: {
    wholesalerName: '請輸入賬號...',
    roleType: '請選擇角色...',
    add: '新增賬戶',
    account: '用戶名：',
    paccount: '請輸入用戶名...',
    oldPwd: '原密碼：',
    poldPwd: '請輸入原始密碼...',
    newPwd: '新密碼：',
    pnewPwd: '請輸入新密碼...',
    rePwd: '確認密碼：',
    prePwd: '請確認密碼...',
    pwdNullMsg: '密碼不能為空',
    pwdLengthMsh: '長度不能小於6位',
    accountNummMsg: '賬戶名不能為空',
    logInto: '登入',
    logOut: '登出',
    optAccoount: '登錄賬號',
    optType: '登錄類型',
    serviceUrl: '服務器地址',
    optTime: '登錄時間',
    accountT: '賬號',
    role: '用戶角色',
    status: '用戶狀態',
    opt: '操作',
    determine: '確定',
    cancel: '取消',
	accountempty:'賬戶名不允許為空',
	newpassword:'輸入新密碼',
	oldpassword:'輸入原密碼',
	Enteraccount:'輸入賬戶',
	successfully:'用戶基本信息修改成功，請重新登錄。',
	wrong:'出錯啦',
	Serverwrong:'服務器內部錯誤'
  },
  common: {
    welcome: '歡迎你：',
    edit: '修改',
    frozen: '凍結',
    thaw: '解凍',
    del: '删除',
    search: '搜索',
    timeSection: '選擇時間段',
    beginTime: '開始時間',
    endTime: '結束時間',
    optResult: '结果',
    optType: '請選擇登錄類型',
    success: '成功',
    failure: '失敗',
    logOut: '退出登錄',
    rePwd: '修改密碼',
	cancel: '取消',
  },



  //CDR
  cdr_mngr: 'CDR話單管理',
  call_query: '話單查詢',
  country_operators: '按國家/運營商查詢',
  search_number: '按號碼查詢',
  flowtotal: '流量總量',
  search_no: '按號段查詢',
  search_enterprise: '按企業查詢',
  search_setMeal: '按套餐查詢',
  price_rules: '批價規則管理',
  company_price: '企業/運營商賬單統計',


  product: '產品管理',
  makeCard: '製卡管理',
  masterCard: '主卡管理',
  cardPool: '卡池管理',
  vimsi: 'VIMSI管理',

  //产品运营
  product_operation: '產品運營',
  package_config: '套餐配置',
  individual_config: '單獨配置',
  batch_config: '批量配置',
  inventory_mngr: '庫存管理',
  task_query: '任務查詢',
  porder_mngr: '個人訂單管理',
  package_delay: '套餐延期',
  package_search: '未激活套餐查詢',
  pakg_delay: '未激活套餐延期',
  delayTask_audit: '延期任務審核',
  activation_record: '激活記錄查詢',
  ordinary_package: '全球卡普通套餐',
  offline_mode: '終端廠商線下模式',
  online_mode: '終端廠商線上模式',
  cooperative_model: '合作運營商模式',
  activateStat_search: '激活統計',
  activate_stat: '銷量統計查詢',
  availability_stat: '可用套餐統計',
  statEnd_month: '未激活套餐統計',

  //套餐
  packageManage: '套餐管理',
  packageIndex: '套餐管理',
  packageBatch: '批量編輯',
  packageAdd: '套餐添加',
  packageUpdate: '套餐編輯',
  packageInfo: '套餐詳情',
  packageCopy: '套餐複製',
  resource: '碼號資源管理',
  msisdn: 'MSISDN管理',
  iccid: 'ICCID管理',
  imsi: 'IMSI管理',
  supplyImsi:'供應商IMSI管理',
  imsi_list: 'IMSI列表',
  vimsi: 'VIMSI管理',

  // 客服支撐
  service_brace: '客服支撐',
  service_index: '卡片資訊',
  local_search: '位置資訊',
  purchased_package: '已購買套餐',
  location_package: '當前位置套餐',
  sppurchased_package: '已購買套餐',
  splocation_package: '當前位置套餐',

  //簡訊管理
  smsManage: '簡訊管理',
  notificationSMS: '通知簡訊管理',
  notificationIndex: '通知簡訊',
  notificationAdd: '通知簡訊新增',
  notificationUpdate: '通知簡訊編輯',
  notificationInfo: '通知簡訊詳情',
  customerSMS: '客服簡訊管理',
  customerIndex: '客服簡訊',
  marketingSMS: '行銷簡訊管理',
  marketingIndex: '行銷簡訊',
  marketingInfo: '行銷簡訊詳情',

  //客戶管理
  customerManage: '客戶管理',
  channelManage: '管道商管理',
  channelIndex: '管道商',
  channelAdd: '管道商新增',
  channelInfo: '管道商詳情',
  channelUpdate: '管道商編輯',
  packageGroup: '套餐組',
  cooperativeManage: '合作運營商管理',
  cooperativeIndex: '合作運營商',
  cooperativeInfo: '合作運營商詳情',
  postPaymentChannel: '後付費管道管理',
  paymentChannelIndex: '後付費管道',
  paymentChannelInfo: '後付費通路詳情',
  manufacturerManage: '終端廠商管理',
  manufacturerIndex: '終端廠商',
  manufacturerInfo: '終端廠商詳情',
  manufacturer_update: '終端廠商編輯',
  test_imsi: '測試IMSI管理',

  // 流量池管理
  flowPool: '流量池管理',
  poolList: '流量池詳情',

  // 渠道自服务
  channel_mngr: '渠道自服務',
  deposit_mngr: '押金管理',
  mealList_mngr: '套餐詳情',
  streamList_mngr: '流水詳情',
  stock_mngr: '庫存管理',
  cardList_mngr: '卡片詳情',
  buymeal_mngr: '套餐購買',
  order_mngr: '訂單管理',
  bill_mngr: '月帳單',
  support_mngr: '服務與支持',
  address_mngr: '地址管理',
  detailsList_mngr: '服務與支持套餐詳情',
  useList_mngr: '使用詳情',
  showiccid_mngr:'ICCID列表',

  // 运营商管理
  operators_mngr: '運營商管理',

  channelSell_mngr:'渠道商销售数据',

  // 报表
  report_mngr: '報表功能',
  exchange_mngr: '匯率管理',
  cardsell_mngr: '卡銷售報表',
  cardactive_mngr: '卡啟動報表',
  subscription_mngr: '套餐訂購報表',
  activation_mngr: '套餐啟動報表',
  reuse_mngr: '重複使用査詢',
  analysis_mngr: '套餐分析報表',
  terminal_mngr: '終端結算報表',
  channelReport_mngr: '運營商結算報表',
  income_mngr: '線下收入報表',
  terminal_after_pay: '後付費結算報表',

  //财务系统管理
  finance_mngr: '財務系統管理',
  billing_mngr: '出賬管理',

  // 押金管理

  deposit: {
    deposit_money: '押金金額',
    mealList: '套餐詳情',
    streamList: '流水詳情',
    mealname: '套餐名稱',
    search: '搜索',
    charge_time: '充值時間',
    canmeal: '可訂購套餐',
    flow: '流水',
    mealId: '套餐id',
    mealprice: '套餐價格',
    charge_price: '充值金額',
    currency: '幣種',
    chosetime: '請選擇充值時間'
  },
  // 库存管理
  stock: {
    order_number: '任務名稱',
    input_number: '請輸入任務名稱',
    timeslot: '時間段',
    chose_time: '請選擇時間段',
    search: '搜索',
    details: '詳情',
    Card_status: '卡狀態',
    chose_status: '請選擇卡狀態',
    exporttb: '匯出',
    card_number: '卡片數量',
    addtime: '創建時間',
    action: '操作',
    usedstate: '卡使用狀態',
    cardtype: '卡類別',
	Code:'驗證碼:',
	PhysicalSIM:'普通卡（實體卡)',
	eSIM:'Esim卡',
	TSIM:'貼片卡',
	showiccid:'ICCID列表'
  },
  // 套餐购买
  buymeal: {
    manual_batch: '手動輸入',
    file_batch: '文件批量',
    chose_meal: '已選套餐',
    input_number: '輸入卡號',
    chose_mealtext: '請選擇套餐',
    chose_number: '請輸入卡號',
    confirm: '確定',
    Reset: '重置',
    HK_dollar: '港元',
    payment: '共需支付',
    mealname: '套餐名稱',
    search: '搜索',
    input_mealname: '請輸入套餐名稱',
    Selectall: '設定全選',
    Deselectall: '取消全選',
    upload: '點擊或拖拽文件上傳',
	Country:'國家/地區',
	amount:'套餐金額/元',
	selectCountry:'請選擇國家/地區',
	Download:'下載模板文件',
	Downloadmsg:'文件僅支持csv格式文件,大小不能超過5MB',
	Choosemeal:'請選擇套餐!',
	cycletype:'套餐週期類型',
	cycleNumber:'套餐週期數',
	filename:'文件名',
	time:'批量購買時間',
	chooseprice:'請選擇套餐,獲取折扣價!',
	choose:'選擇',
	hour:'24小時',
	day:'自然日',
	month:'自然月',
	year:'自然年',
	Failedfile:'失敗文件',
	clickdownload:'點擊下載',
	tasksTotal:'任務總條數',
	successes:'成功條數',
	failed:'失敗條數',
	Nofailed:'無失敗文件',
	taskview:'歷史任務查看',
	Taskstatus:'任務狀態',
	Processing:'處理中',
	completed:'已完成'
  },
  // 订单管理
  order: {
    mealname: '套餐名稱',
    input_mealname: '請輸入套餐名稱',
    chose_number: '請輸入卡號',
    input_number: '卡號',
    timeslot: '時間段',
    chose_time: '請選擇時間段',
    search: '搜索',
    monthly_bill: '查看月帳單',
    exporttb: '匯出',
    month: '月份',
    chose_month: '請選擇月份',
    expenditure: '總支出',
    order_number: '訂單號',
    order_state: '訂單狀態',
    count: '數量',
    order_money: '訂單金額',
    addtime: '創建時間',
	isused:'套餐是否已使用',
	action: '操作',
	unsubscribe:'退訂',
	ifunsubscribe:'確認退訂該項',
	channels:'購買渠道',
	Website:'官網（H5)',
	BulkOrder:'批量售賣',
	Trial:'推廣活動',
	Testing:'測試渠道',
	Datapool:'流量池web',
	ActivationTime:'使用時間',
	LocationUpdate:'最新位置',
	BeijingMobile:'北京移動',
	issuance:'合作發卡',
	Postpaid:'後付費發卡',
	Normal:'正常',
	Suspend:'註銷',
	Expired:'過期',
	Terminated:'暫停',
	WeChat:'微信公眾號',
	yes:'是',
	no:'否',
	delivered:'待發貨',
	Completed:'完成',
	Cancelled:'已退訂',
	approval:'激活退訂待審批',
	Recycled:'已回收'
  },
  // 服务与支持
  support: {
    cardtype: '主卡形態',
    chose_type: '請選擇卡類型',
    cardstate: '卡片狀態',
    chose_state: '請選擇卡狀態',
    input: '請輸入',
    search: '搜索',
    mealList: '套餐詳情',
    mealname: '套餐名稱',
    input_mealname: '請輸入套餐名稱',
    timeslot: '時間段',
    chose_time: '請選擇時間段',
    used_details: '使用詳情',
    activation: '啟動',
    frozen: '凍結',
    action: '操作',
    time: '過期時間',
    Verification_Code: '發送驗證碼',
    Activation: 'H卡激活方式',
    isused: '是否已使用',
    meal_time: '套餐過期時間',
    Activation_state: '啟動狀態',
    used_flow: '使用流量',
    used_cycle: '使用週期',
    Report_time: '上報時間',
    Report_address: '上報位置',
	Targeting:'定位方式',
	template:'短信模板',
	position:'當前位置',
	Locationrecord:'位置更新記錄',
	SendSMS:'下發客服短信',
	Flowdetails:'流量詳情',
	Packagestatus:'套餐狀態',
	Activationtype:'激活類型',
	Ordernumber:'訂單編號',
	Ordertime:'訂單時間',
	Orderchannel:'訂單渠道',
	Activationmethod:'激活方式',
	sendingmethod:'發送方式',
	chosesend:'請選擇發送方式',
	phone:'手機號碼',
	phoneprompt:'請輸入手機號',
	chosetemplate:'請選擇短信模板',
	sending:'發送短信',
	usedetails:'流量使用詳情',
	date:'日期',
	useflow:'使用流量(G)',
	close:'關閉',
	Periodtype:'週期類型',
	Continuouscycle:'持續週期',
	Activatepackage:'激活套餐',
	MSISDNenter:'請輸入MSISDN',
	ICCIDenter:'請輸入ICCID',
	IMSIenter:'請輸入IMSI',
	Recycle:'提前回收',
	ReplaceVIMSI:'更換VIMSI',
	Automatic:'自動激活',
	Manual:'手動激活',
	Sendingempty:'發送方式不能為空',
	SMSempty:'短信模板不能為空',
	Phoneempty:'手機號碼不能為空',
	PhoneWrong:'手機號碼格式錯誤',
	Unuse:'待激活',
	InUse:'使用中',
	Expired:'已過期',
	Activatedpending:'已激活待計費',
	Activating:'激活中',
	activated:'已激活',
	Used:'已使用',
	CNY:'人民幣',
	USD:'美元',
	HKD:'港幣',
	VIMSILocation:'VIMSI位置更新詳情',
	Cardtype:'卡片類型',
	Flowg:'流量(G)',
	VIMSIphone:'VIMSI號碼',
	TimeLocation:'位置上報時間',
	Location:'位置上報地點',
	Termination:'確認提前回收?',
	replacement:'確認更換VIMSI?',
	VIMSIdetails:'VIMSI分配詳情',
	IMSIdetails:'IMSI上網詳情',
	cardtraffic:'查詢V卡流量',
	QueryH:'查詢H卡流量',
	cardflow:'H卡/V卡流量',
	packageflow:'套餐總流量(MB)：',
	remainingflow:'當前剩餘流量(MB)：',
	Usedtraffic:'已使用流量(MB)：',
	Usedflow:'已使用流量',
	countryregion:'分配國家/地區',
	flowquery:'套餐流量查詢',
	back:'返回',
	Vcard:'V 卡 ',
	Hcard:'H 卡 '
  },
  // 地址管理
  address: {
	deleteitem:'確認刪除該項?',
    fullname: '姓名',
    input_fullname: '請輸入姓名',
    mailbox: '郵箱',
    input_mailbox: '請輸入郵箱地址',
    Newaddress: '新建地址',
    modify: '修改',
    modifyaddress: '修改郵箱',
    Delete: '删除',
    Forgetpwd: '修改密碼',
    setdefault: '設定默認',
    search: '搜索',
    password: '密碼',
    password_ok: '確認密碼',
    input_pwd: '請輸入密碼',
    newpwd: '新密碼',
    account: '帳號',
    action: '操作',
	oldPwd:'原密碼',
	PwdRules:'密碼設置時需要滿足給定規則，點擊',
	watch:'查看',
	more:'了解詳情',
	Rules1:'確保口令滿足以下通用原則',
	Rules2:'1、口令至少由8位及以上大寫字母、小寫字母、數字與特殊符號等4類中3類混合、隨機組成，盡量不要以姓名、電話號碼以及出生日期等作為口令或者口令的組成部分；',
	Rules3:'2、口令應與用戶名無相關性，口令中不得包含用戶名的完整字符串、大小寫變位或形似變換的字符串，如teleadmin:teleadmin、teleadmin:teleadmin2017、teleadmin:TeleAdmin、teleadmin:te!e@ dmin等；',
	Rules4:'3、應更換系統或設備的出廠默認口令，如huawei:huawei@123，oracle數據庫中SYS:CHANGE_ON_INSTALL,某移動定製版光貓默認帳號CMCCAdmin:aDm8H%MdA等；',
	Rules5:'4、口令設置應避免3位以上（含3位）鍵盤排序密碼，如qwe（鍵盤第1行前三個字母）、asd（鍵盤第2行前三個字母）、qaz（鍵盤第1列三個字母）、1qaz（鍵盤第1列數字加前三個字母）、！ QAZ（鍵盤第1列特殊字符加前三個字母）等；',
	Rules6:'5、口令中不能出現3位以上（含三位）連續字母、數字、特殊字符，如ABC、Abc、123、！ @#等；',
	Rules7:'6、口令中不能出現3位以上（含三位）重複字母、數字、特殊字符，如AAA、Aaa、111、###等。',
	Rules8:'避免以下易猜解口令規則',
	Rules9:'1、省份、地市名稱、郵箱、電話區號、郵政編碼及縮寫和簡單數字或shift鍵+簡單數字，如BJYD123、HBYD!@#等；',
	Rules10:'2、單位名稱、專業名稱、系統名稱、廠家名稱（含縮寫）和簡單數字，如HBnmc123、HBsmc_123等；',
	Rules11:'3、維護人員名字全拼大小寫縮寫等變形+設備IP地址（一位或兩位）或出生年月日等，如維護人員張三，維護設備地址為************和************,出生日期為19951015，則其可能的弱口令為zhangsan100、zhangsan101，zhangsan10100，zhangsan10101，zhangsan19951015，ZS19951015等；',
    Operationreminder:'操作提醒',
	deleted:'删除成功!',
	inconsistent:'兩次密碼，輸入不一致',
	emailaddress:'請輸入有效的郵箱地址',
	reset:'密碼格式不規範，請重新填寫',
	appear:'不可出現3個連續且相同的字符',
	allowed:'不可出現3位連續的數字',
	determine:'確定'
  }
}
