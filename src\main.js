// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import Routers from 'vue-router'
import store from './store'
import iView from 'iview'
import dayjs from 'dayjs'
import ViewUI from 'view-design'
import i18n from '@/locale'
import config from '@/config'
import importDirective from '@/directive'
import {
  directive as clickOutside
} from 'v-click-outside-x'
import installPlugin from '@/plugin'
import './index.less'
import '@/assets/icons/iconfont.css'
import 'v-org-tree/dist/v-org-tree.css'
import '@/assets/styles/global.css'
import hasPermission from '@/libs/hasPermission'
import preventReClick from '@/libs/plugins'
import redirectPlugin from './plugin/redirectPlugin.js'
const mathjs = require('mathjs');


import axios from 'axios'
axios.defaults.withCredentials = true; //开启ajax携带cookie发出请求
axios.defaults.timeout = 60000; //设置请求超时时间  15秒

// 实际打包时应该不引入mock
/* eslint-disable */
// if (process.env.NODE_ENV !== 'production') require('@/mock')
Vue.use(hasPermission);
Vue.use(preventReClick);
Vue.use(ViewUI, {
  i18n: (key, value) => i18n.t(key, value)
})
// 代办、sso重定向
Vue.use(redirectPlugin)

/**
 * @description 注册admin内置插件
 */
installPlugin(Vue)
/**
 * @description 生产环境关掉提示
 */
Vue.config.productionTip = false
/**
 * @description 全局注册应用配置
 */
Vue.prototype.$config = config
/**
 * 注册指令
 */
importDirective(Vue)
Vue.directive('clickOutside', clickOutside)


// mock格式化数据公用方法
Vue.prototype.$mockFormData = (data) => {
  // 下划线转换驼峰
  function toHump(name) {
    return name.replace(/\_(\w)/g, function(all, letter) {
      return letter.toUpperCase();
    });
  }

  const arr = [];

  data.forEach((element) => {
    let obj = {};

    Object.keys(element).forEach((key) => {
      obj[toHump(key)] = element[key].value;
    });

    arr.push(obj);
  });

  console.log(arr[0]);

  return arr;
};

Vue.prototype.$moneyCover = (value, times) => {

  if (value) {
    return mathjs.multiply(mathjs.bignumber(value), times).toNumber();
  } else {
    return value;
  }


}

// 日期比较大小 type取值 > <
Vue.prototype.$time = (date1, type, date2) => {

  let formatDate1 = ''
  let formatDate2 = ''
  if (date1.indexOf('-') > -1) {
    formatDate1 = dayjs(date1, 'YYYY-MM-DD');
    formatDate2 = dayjs(date2, 'YYYY-MM-DD');
  } else {
    formatDate1 = dayjs(date1, 'YYYYMMDD');
    formatDate2 = dayjs(date2, 'YYYYMMDD');
  }

  if (type === '>') {
    return formatDate1.isAfter(formatDate2)

  } else {
    return formatDate1.isBefore(formatDate2)
  }
}
Vue.directive("defaultSelect", {
  componentUpdated (el, bindings) {
    // defaultValues 应该是传入的不可删除的值的数组
    const [defaultValues] = bindings.value || [[]]; // 添加默认值防止出错
    if (!Array.isArray(defaultValues)) return; // 确保是数组

    const dealStyle = function (closeIcons) {
      // 将前 defaultValues.length 个标签的关闭按钮隐藏
      closeIcons.forEach((icon, index) => {
        if (icon && index < defaultValues.length) {
           // 直接设置样式，而不是添加类
           icon.style.display = 'none';
        } else if (icon) {
           // 确保后续添加的可删除项显示关闭按钮
           icon.style.display = '';
        }
      });
    }

    // 设置样式 隐藏close icon
    // View UI (iView) 的选择器通常是 .ivu-tag .ivu-icon-ios-close
    // 需要根据实际使用的 Select 组件版本确认准确的选择器
    const closeIcons = el.querySelectorAll('.ivu-tag .ivu-icon-ios-close');

    if (closeIcons.length === 0) {
      // 初始化时 tags 可能还未渲染，延迟处理
      setTimeout(() => {
        const iconsTemp = el.querySelectorAll('.ivu-tag .ivu-icon-ios-close');
        dealStyle(iconsTemp);
      }, 0); // 使用 0ms timeout 推入下一个事件循环
    } else {
      dealStyle(closeIcons);
    }
  }
});
const originalPush = Routers.prototype.push
Routers.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}


/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  i18n,
  store,
  render: h => h(App)
})
