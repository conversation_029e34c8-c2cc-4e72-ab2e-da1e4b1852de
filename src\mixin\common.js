export default {
  methods: {
    validateDate(rule, value, callback) {
       let  endDate = this.form.endDate || this.form.endTime;
       let  startDate = this.form.startDate || this.form.startTime;
      if (!endDate || !startDate) {
        callback();
      } else {
        if ((rule.field === "startDate") || rule.field === "startTime") {
          if (this.$time(value, ">", endDate)) {

            callback(new Error("开始时间不能大于结束时间"));
          } else {
            callback();
          }
        } else {
          if (this.$time(value, "<", endDate)) {
            callback(new Error("结束时间不能小于开始时间"));
          } else {
            callback();
          }
        }
      }
    }
  }
};