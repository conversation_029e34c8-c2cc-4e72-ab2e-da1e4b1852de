export default {
  methods: {
    validateDate(rule, value, callback) {
       let  endDate = this.form.endDate || this.form.endTime || this.searchEndTime.toString();
       let  startDate = this.form.startDate || this.form.startTime||this.searchBeginTime.toString();
      if (!endDate || !startDate) {
        callback();
      } else {
        if ((rule.field === "startDate") || rule.field === "startTime"|| rule.field === "beginMonth") {
		  if (this.$time(startDate, ">", endDate)) {
            callback(new Error("开始时间不能大于结束时间"));
          } else {
            callback();
          }
        } else {
          if (endDate<startDate) {
            callback(new Error("结束时间不能小于开始时间"));
          } else {
            callback();
          }
        }
      }
    }
  }
};

