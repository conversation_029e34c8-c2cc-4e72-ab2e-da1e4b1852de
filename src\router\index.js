import Vue from 'vue'
import Router from 'vue-router'
import routes from './routers'
import store from '@/store'
import iView from 'iview'
import {
  setToken,
  getToken,
  canTurnTo,
  setUserId,
  getUserId,
  setTitle,
  initRouterNode,
  getMode
} from '@/libs/util'
import config from '@/config'
const {
  homeName
} = config

Vue.use(Router)

const router = new Router({
  mode: 'history',
  routes,
  base: '/cmifront',
})
const LOGIN_PAGE_NAME = 'login'

const turnTo = (to, access, next) => {
  if (canTurnTo(to.name, access, routes)) {
    next()
  } // 有权限，可访问
  else {
    next({
      replace: true,
      name: 'error_401'
    })
  } // 无权限，重定向到401页面
}

router.beforeEach((to, from, next) => {
  iView.LoadingBar.start()
  // 使用getUserId会导致另一个页面登错时清除cookie，其他页面的cookie也跟着清除导致退出
  // const userId = getUserId()
  // const token = getToken()
  const token = store.state.user.token
  const userId = store.state.user.userId
  const corpId = sessionStorage.getItem('corpId')
  const cooperationMode = sessionStorage.getItem('cooperationMode')

  if ((!token || !userId) && to.name !== LOGIN_PAGE_NAME) {
    if (to.name == 'operationAgencyApproval' || to.name == 'proxyReviewed' || to.name == 'costToDo' || to.name == 'paymentOrderPaySuccess' || to.name == 'paymentOrderPayFailed' || to.name == 'aprvDetails') {
      next()
    } else {
      // 未登录且要跳转的页面不是登录页
      next({
        name: LOGIN_PAGE_NAME // 跳转到登录页
      })
    }
  } else if ((!token || !userId) && to.name === LOGIN_PAGE_NAME) {
    // 未登录且要跳转的页面是登录页
    next() // 跳转
  } else if (token && userId && corpId && !cooperationMode && to.name === LOGIN_PAGE_NAME) {
    // 已登录在选择合作模式页面没选择合作模式且要跳转的页面是登录页
    next() // 跳转
  } else if (token && userId && to.name === LOGIN_PAGE_NAME) {
    // 已登录且要跳转的页面是登录页
    next({
      name: homeName // 跳转到homeName页
    })
  } else {
    // 检查用户信息是否已获取
    if (!store.state.user.hasGetInfo) {
      store.dispatch('getUserInfo').then(user => {
        turnTo(to, user.access, next)
      }).catch(() => {
        // 获取用户信息失败时，只清除当前窗口的状态
        store.commit('setToken', '')
        store.commit('setUserId', '')
        store.commit('setUserPriv', [])
        store.commit('setAccess', [])
        store.commit('setHasGetInfo', false)
        next({
          name: LOGIN_PAGE_NAME
        })
      })
    } else {
      // 用户信息已获取，检查权限
      let btnPriv = []
      try {
        // 检查 userBtnPriv 是否存在且不为空
        if (store.state.user.userBtnPriv && store.state.user.userBtnPriv.length > 0) {
          const matchedPriv = store.state.user.userBtnPriv.find(item => item.url === to.path)
          if (matchedPriv) {
            btnPriv = matchedPriv.priv
          } else {
            // 如果没有找到匹配的权限，使用默认权限
            btnPriv = ['view']
          }
        } else {
          // 如果没有权限配置，使用默认权限
          btnPriv = ['view']
        }

        initRouterNode(to, btnPriv)
        turnTo(to, store.state.user.access, next)
      } catch (e) {
        // 如果获取权限失败，只清除当前窗口的状态
        store.commit('setToken', '')
        store.commit('setUserId', '')
        store.commit('setUserPriv', [])
        store.commit('setAccess', [])
        store.commit('setHasGetInfo', false)
        next({
          name: LOGIN_PAGE_NAME
        })
      }
    }
  }
})

router.afterEach(to => {
  setTitle(to, router.app)
  iView.LoadingBar.finish()
  window.scrollTo(0, 0)
})

export default router
