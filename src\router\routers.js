import Main from '@/components/main'
import parentView from '@/components/parent-view'

/**
 * iview-admin中meta除了原生参数外可配置的参数:
 * meta: {
 *  title: { String|Number|Function }
 *         显示在侧边栏、面包屑和标签栏的文字
 *         使用'{{ 多语言字段 }}'形式结合多语言使用，例子看多语言的路由配置;
 *         可以传入一个回调函数，参数是当前路由对象，例子看动态路由和带参路由
 *  hideInBread: (false) 设为true后此级路由将不会出现在面包屑中，示例看QQ群路由配置
 *  hideInMenu: (false) 设为true后在左侧菜单不会显示该页面选项
 *  notCache: (false) 设为true后页面在切换标签后不会缓存，如果需要缓存，无需设置这个字段，而且需要设置页面组件name属性和路由配置的name一致
 *  access: (null) 可访问该页面的权限数组，当前路由设置的权限会影响子路由
 *  icon: (-) 该页面在左侧菜单、面包屑和标签导航处显示的图标，如果是自定义图标，需要在图标名称前加下划线'_'
 *  beforeCloseName: (-) 设置该字段，则在关闭当前tab页时会去'@/router/before-close.js'里寻找该字段名对应的方法，作为关闭前的钩子函数
 * }
 */

export default [{
    path: "/login",
    name: "login",
    meta: {
      title: "Login - 登录",
      hideInMenu: true,
    },
    component: () => import("@/view/login/login.vue"),
  },
  {
    path: "/channelCooperationMode",
    name: "channelCooperationMode",
    meta: {
      title: "渠道商合作模式",
      hideInMenu: true,
    },
    component: () => import("@/view/channelCooperationMode/index.vue"),
  },
  {
    path: "/spinLoading",
    name: "spinLoading",
    meta: {
      title: "加载中",
      hideInMenu: true,
    },
    component: () => import("@/view/spin/index.vue"),
  },
  {
    path: "/paymentOrder/successed",
    name: "paymentOrderPaySuccess",
    meta: {
      hideInMenu: true,
      title: "支付成功",
    },
    component: () => import("@/view/channel/paymentOrder/paySuccess.vue"),
  },
  {
    path: "/paymentOrder/failed",
    name: "paymentOrderPayFailed",
    meta: {
      hideInMenu: true,
      title: "支付失败",
    },
    component: () => import("@/view/channel/paymentOrder/payFailed.vue"),
  },
  {
    path: "/",
    name: "_home",
    redirect: "/home",
    component: Main,
    meta: {
      hideInMenu: true,
      notCache: true,
    },
    children: [{
      path: "/home",
      name: "home",
      meta: {
        hideInMenu: true,
        title: "首页",
        notCache: true,
        icon: "md-home",
      },
      component: () => import("@/view/home/<USER>"),
    }, ],
  },
  {
    path: "/sys",
    name: "system_mngr",
    meta: {
      title: "系统管理",
      icon: "logo-buffer",
      access: ["system_mngr"],
    },
    component: Main,
    children: [{
        path: "/accountList",
        name: "account_list",
        meta: {
          icon: "md-people",
          title: "账户管理",
          access: ["account_list"],
        },
        component: () => import("@/view/system/account/index.vue"),
      },
      {
        path: "/pwdmngr",
        name: "pwd_mngr",
        meta: {
          icon: "md-person",
          title: "账户密码管理",
          access: ["pwd_mngr"],
        },
        component: () => import("@/view/system/account/pwdmngr.vue"),
      },
      {
        path: "/roleMngr",
        name: "pri_mngr",
        meta: {
          icon: "md-contacts",
          title: "角色管理",
          access: ["pri_mngr"],
        },
        component: () => import("@/view/system/privilege/index.vue"),
      },
      {
        path: "/loginLog",
        name: "login_mngr",
        meta: {
          icon: "md-document",
          title: "登录日志",
          access: ["login_mngr"],
        },
        component: () => import("@/view/system/logs/loginLog.vue"),
      },
      {
        path: "/operatorLog",
        name: "log_mngr",
        meta: {
          icon: "ios-paper",
          title: "操作日志",
          access: ["log_mngr"],
        },
        component: () => import("@/view/system/logs/operatorLog.vue"),
      },
      {
        path: "/Announcement",
        name: "Announcement_mngr",
        meta: {
          icon: "ios-book",
          title: "公告管理",
          access: ["Announcement_mngr"],
        },
        component: () => import("@/view/system/Announcement.vue"),
      },
      {
        path: "/lockModeApproval",
        name: "lockModeApproval_mngr",
        meta: {
          icon: "ios-lock",
          title: "金锁模式管理",
          access: ["lockModeApproval_mngr"],
        },
        component: () => import("@/view/system/lockModeApproval.vue"),
      },
    ],
  },
  {
    path: "/resource",
    name: "resource",
    meta: {
      icon: "md-cart",
      title: "码号资源管理",
      access: ["resource"],
    },
    component: Main,
    children: [{
        path: "/msisdn",
        name: "msisdn",
        meta: {
          access: ["msisdn"],
          icon: "md-bowtie",
          title: "MSISDN管理",
        },
        component: () => import("@/view/resource/msisdn.vue"),
      },
      {
        path: "/iccid",
        name: "iccid",
        meta: {
          access: ["iccid"],
          icon: "md-bug",
          title: "ICCID管理",
        },
        component: () => import("@/view/resource/iccid.vue"),
      },
      {
        path: "/imsi",
        name: "imsi",
        meta: {
          access: ["imsi"],
          icon: "md-funnel",
          title: "IMSI管理",
        },
        component: () => import("@/view/resource/imsiList.vue"),
      },
      {
        path: "/supplyImsi",
        name: "supplyImsi",
        meta: {
          access: ["supplyImsi"],
          icon: "md-funnel",
          title: "消息上报IMSI管理",
        },
        component: () => import("@/view/resource/vimsi.vue"),
      },
      {
        path: "/makeCardFile",
        name: "makeCardFile_mngr",
        meta: {
          access: ["makeCardFile_mngr"],
          icon: "md-card",
          title: "制卡文件管理",
        },
        component: () => import("@/view/resource/makeCardFile/index.vue"),
      },
      {
        path: "/addCard",
        name: "addCard_mngr",
        meta: {
          // access: ['makeCardFile'],
          icon: "md-card",
          title: "新建制卡任务",
          hideInMenu: true,
        },
        component: () => import("@/view/resource/makeCardFile/add.vue"),
      },
      {
        path: "/otaData_mngr",
        name: "otaData_mngr",
        meta: {
          access: ['otaData_mngr'],
          icon: "md-analytics",
          title: "OTA数据管理",
        },
        component: () => import("@/view/resource/otaData.vue"),
      },
    ],
  },
  {
    path: "/product",
    name: "product",
    meta: {
      access: ["product"],
      icon: "md-menu",
      title: "产品管理",
    },
    component: Main,
    children: [{
        path: "/makeCard",
        name: "makeCard",
        meta: {
          access: ["makeCard"],
          icon: "md-funnel",
          title: "制卡管理",
        },
        component: () => import("@/view/productMngr/makeCard.vue"),
      },
      {
        path: "/masterCard",
        name: "masterCard",
        meta: {
          access: ["masterCard"],
          icon: "md-filing",
          title: "主卡管理",
        },
        component: () => import("@/view/productMngr/masterCard.vue"),
      },
      {
        path: "/cardPool",
        name: "cardPool",
        meta: {
          icon: "md-medical",
          access: ["cardPool"],
          title: "卡池管理",
        },
        component: () => import("@/view/productMngr/cardPool.vue"),
      },
      {
        path: "/vimsi",
        name: "vimsi",
        meta: {
          icon: "md-send",
          access: ["vimsi"],
          title: "VIMSI管理",
        },
        component: () => import("@/view/productMngr/vimsiManage.vue"),
      },
      {
        path: "/associationGroup",
        name: "associationGroup",
        meta: {
          icon: "ios-beaker",
          access: ["associationGroup"],
          title: "国家卡池关联组",
        },
        component: () =>
          import("@/view/productMngr/associationGroup/index.vue"),
      },
      {
        path: "/addCardPool",
        name: "addCardPool",
        meta: {
          icon: "ios-beaker",
          title: "新增关联组",
          access: ["addCardPool"],
          hideInMenu: true,
        },
        component: () =>
          import("@/view/productMngr/associationGroup/countrySelect.vue"),
      },
      {
        path: "/updateCardPool",
        name: "updateCardPool",
        meta: {
          icon: "ios-beaker",
          title: "编辑关联组",
          access: ["updateCardPool"],
          hideInMenu: true,
        },
        component: () =>
          import("@/view/productMngr/associationGroup/countrySelect.vue"),
      },
      {
        path: "/copyCardPool",
        name: "copyCardPool",
        meta: {
          icon: "ios-beaker",
          title: "复制关联组",
          access: ["copyCardPool"],
          hideInMenu: true,
        },
        component: () =>
          import("@/view/productMngr/associationGroup/countrySelect.vue"),
      },
      {
        path: "/cardPooldetails",
        name: "cardPooldetails",
        meta: {
          icon: "ios-beaker",
          access: ["cardPooldetails"],
          title: "关联组详情",
          hideInMenu: true,
        },
        component: () =>
          import("@/view/productMngr/associationGroup/countrySelect.vue"),
      },
      {
        path: "/specialCountryRule",
        name: "specialCountryRule",
        meta: {
          icon: "md-globe",
          access: ["specialCountryRule"],
          title: "特殊国家规则管理",
        },
        component: () => import("@/view/productMngr/specialCountryRule/index.vue"),
      },
      {
        path: "/specialCountryRuleAdd",
        name: "specialCountryRuleAdd",
        meta: {
          icon: "md-add",
          access: ["specialCountryRuleAdd"],
          title: "新增特殊国家规则",
          hideInMenu: true,
        },
        component: () => import("@/view/productMngr/specialCountryRule/update.vue"),
      },
      {
        path: "/specialCountryRuleEdit",
        name: "specialCountryRuleEdit",
        meta: {
          icon: "md-create",
          access: ["specialCountryRuleEdit"],
          title: "编辑特殊国家规则",
          hideInMenu: true,
        },
        component: () => import("@/view/productMngr/specialCountryRule/update.vue"),
      }
    ],
  },
  {
    path: "/packageMngr",
    name: "packageManage",
    meta: {
      access: ["packageManage"],
      icon: "md-pricetags",
      title: "套餐管理",
      showAlways: true,
    },
    component: Main,
    children: [{
        path: "/package",
        name: "packageIndex",
        meta: {
          icon: "md-pricetag",
          title: "套餐管理",
          access: ["packageIndex"],
          // hideInMenu: true
        },
        component: () => import("@/view/package/index.vue"),
      },
      {
        path: "/packageAdd",
        name: "packageAdd",
        meta: {
          icon: "ios-list",
          title: "套餐新增",
          // access: ['packageAdd'],
          hideInMenu: true,
        },
        component: () => import("@/view/package/add.vue"),
      },
      {
        path: "/packageEdit",
        name: "packageUpdate",
        meta: {
          icon: "ios-list",
          title: "套餐编辑",
          // access: ['packageUpdate'],
          hideInMenu: true,
        },
        component: () => import("@/view/package/edit.vue"),
      },
      {
        path: "/packageDetails",
        name: "packageInfo",
        meta: {
          icon: "ios-list",
          title: "套餐详情",
          // access: ['packageInfo'],
          hideInMenu: true,
        },
        component: () => import("@/view/package/details.vue"),
      },
      {
        path: "/cardPoolSwitchList",
        name: "cardPoolSwitchList",
        meta: {
          icon: "md-swap",
          title: "卡池切换任务",
          // access: ["cardpool_switch"],
          hideInMenu: true,
        },
        component: () => import("@/view/package/cardPoolSwitch/index.vue"),
      },
      {
        path: "/cardPoolSwitchAdd",
        name: "cardPoolSwitchAdd",
        meta: {
          icon: "md-add",
          title: "新建卡池切换",
          // access: ["cardpool_switch"],
          hideInMenu: true,
        },
        component: () => import("@/view/package/cardPoolSwitch/CardPoolSwitchWizard.vue"),
      },
      {
        path: "/cardPoolDetail",
        name: "cardPoolDetail",
        meta: {
          icon: "ios-list",
          title: "套餐卡池详情",
          // access: ['packageInfo'],
          hideInMenu: true,
        },
        component: () => import("@/view/package/model/cardPoolDetail.vue"),
      },
      {
        path: "/packageCopy",
        name: "packageCopy",
        meta: {
          icon: "ios-list",
          title: "套餐复制",
          // access: ['packageCopy'],
          hideInMenu: true,
        },
        component: () => import("@/view/package/copy.vue"),
      },
      {
        path: "/trafficPool",
        name: "trafficPool_mngr",
        meta: {
          icon: "md-wifi",
          title: "流量池管理",
          access: ["trafficPool_mngr"],
        },
        component: () => import("@/view/package/flowpool/index.vue"),
      },
      {
        path: "/addPool",
        name: "addPool_mngr",
        meta: {
          icon: "md-wifi",
          title: "新建流量池",
          hideInMenu: true,
          // access: ['addPool_mngr'],
        },
        component: () => import("@/view/package/flowpool/add.vue"),
      },
      {
        path: "/editPool",
        name: "editPool_mngr",
        meta: {
          icon: "md-wifi",
          title: "修改流量池",
          hideInMenu: true,
          // access: ['editPool_mngr'],
        },
        component: () => import("@/view/package/flowpool/edit.vue"),
      },
      {
        path: "/copyPool",
        name: "copyPool_mngr",
        meta: {
          icon: "md-wifi",
          title: "复制流量池",
          hideInMenu: true,
          // access: ['copyPool_mngr'],
        },
        component: () => import("@/view/package/flowpool/copy.vue"),
      },
      {
        path: "/detailsPool",
        name: "detailsPool_mngr",
        meta: {
          icon: "md-wifi",
          title: "流量池详情",
          hideInMenu: true,
          // access: ['detailsPool_mngr'],
        },
        component: () => import("@/view/package/flowpool/details.vue"),
      },
      {
        path: "/fuelPack",
        name: "fuelPack_mngr",
        meta: {
          icon: "md-briefcase",
          title: "加油包管理",
          access: ["fuelPack_mngr"],
        },
        component: () => import("@/view/package/fuelPack/index.vue"),
      },
      {
        path: "/upccTemplate",
        name: "upccTemplate_mngr",
        meta: {
          icon: "md-list-box",
          title: "UPCC速度模板管理",
          access: ["upccTemplate_mngr"],
        },
        component: () => import("@/view/package/upccTemplate/index.vue"),
      },
      {
        path: "/channelProviderPackage",
        name: "channelProvider_mngr",
        meta: {
          icon: "md-contact",
          title: "渠道商套餐管理",
          access: ["channelProvider_mngr"],
        },
        component: () =>
          import("@/view/package/channelProviderPackage/index.vue"),
      },
      {
        path: "/targetedApplicationManagement",
        name: "targetedApplication_mngr",
        meta: {
          icon: "md-ionic",
          title: "定向应用管理",
          access: ["targetedApplication_mngr"],
        },
        component: () =>
          import("@/view/package/targetedApplications/index.vue"),
      },
      {
        path: "/whiteListPackageMngr",
        name: "whiteListPackage_mngr",
        meta: {
          icon: "md-ionic",
          title: "白名单套餐管理",
          access: ["whiteListPackage_mngr"],
        },
        component: () =>
          import("@/view/package/whiteListPackage/index.vue"),
      },
    ],
  },
  {
    path: "/cdr",
    name: "cdr_mngr",
    meta: {
      title: "CDR话单管理",
      icon: "md-list-box",
      access: ["cdr_mngr"],
    },
    component: Main,
    children: [{
        path: "/callQuery",
        name: "call_query",
        meta: {
          icon: "md-search",
          title: "话单查询",
          access: ["call_query"],
        },
        component: () => import("@/view/cdr/search/cdrSearch.vue"),
      },
      {
        path: "/priceRules",
        name: "price_rules",
        meta: {
          icon: "md-settings",
          title: "批价规则管理",
          access: ["price_rules"],
        },
        component: () => import("@/view/cdr/price/rules.vue"),
      },
      {
        path: "/companyPrice",
        name: "company_price",
        meta: {
          icon: "md-podium",
          title: "企业运营商账单统计",
          access: ["company_price"],
        },
        component: () => import("@/view/cdr/price/companyRules.vue"),
      },
      {
        path: "/callListDetail",
        name: "callListinfo",
        meta: {
          icon: "md-podium",
          title: "话单详情",
          hideInMenu: true,
          // access: ['callListinfo']
        },
        component: () => import("@/view/cdr/search/callListDetail.vue"),
      },
    ],
  },
  {
    path: "/download",
    name: "download_mngr",
    meta: {
      title: "下载管理",
      icon: "md-arrow-round-down",
      access: ["download_mngr"],
    },
    component: Main,
    children: [{
      path: "/taskList",
      name: "taskListinfo",
      meta: {
        icon: "ios-cloud-download",
        title: "下载列表",
        access: ["taskListinfo"],
      },
      component: () => import("@/view/download/taskList.vue"),
    }, ],
  },

  // {
  // 	path: '/flowPool',
  // 	name: 'flowPool',
  // 	component: Main,
  // 	meta: {
  // 		icon: 'md-wifi',
  // 		title: '流量池管理',
  // 		access: ['flowPool']
  // 	},
  // 	children: [{
  // 			path: '/flowPool',
  // 			name: 'flowPool',
  // 			meta: {
  // 				title: '流量池管理',
  // 				icon: 'md-wifi',
  // 				access: ['flowPool']
  // 			},
  // 			component: () => import('@/view/flowPool/index.vue')
  // 		},
  // 		{
  // 			path: '/poolList',
  // 			name: 'poolList',
  // 			meta: {
  // 				title: '流量池详情',
  // 				icon: 'ios-wifi',
  // 				hideInMenu: true,
  // 				access: ['poolList']
  // 			},
  // 			component: () => import('@/view/flowPool/poolList.vue')
  // 		},
  // 	]
  // },
  {
    path: "/productOperation",
    name: "product_operation",
    meta: {
      title: "产品运营",
      icon: "md-ionic",
      access: ["product_operation"],
    },
    component: Main,
    children: [{
        path: "/packageConfig",
        name: "package_config",
        meta: {
          access: ["package_config"],
          showAlways: true,
          icon: "md-list",
          title: "套餐配置",
        },
        component: parentView,
        children: [{
            path: "/individualConfig",
            name: "individual_config",
            meta: {
              icon: "md-build",
              access: ["individual_config"],
              title: "单独配置",
            },
            component: () => import("@/view/product/package/individual.vue"),
          },
          {
            path: "/batchConfig",
            name: "batch_config",
            meta: {
              access: ["batch_config"],
              icon: "md-construct",
              title: "批量配置",
            },
            component: () => import("@/view/product/package/batch.vue"),
          },
        ],
      },
      {
        path: "/inventoryMngr",
        name: "inventory_mngr",
        meta: {
          icon: "md-home",
          title: "库存管理",
          access: ["inventory_mngr"],
        },
        component: () => import("@/view/product/inventory/index.vue"),
      },
      {
        path: "/porderMngr",
        name: "porder_mngr",
        meta: {
          icon: "md-ribbon",
          title: "个人订单管理",
          access: ["porder_mngr"],
        },
        component: () => import("@/view/product/porder/index.vue"),
      },
      {
        path: "/packageDelay",
        name: "package_delay",
        meta: {
          access: ["package_delay"],
          icon: "md-calendar",
          // showAlways: true,
          title: "套餐延期",
        },
        component: parentView,
        children: [{
            path: "/packageSearch",
            name: "package_search",
            meta: {
              access: ["package_search"],
              icon: "md-search",
              title: "未激活套餐查询",
            },
            component: () => import("@/view/product/packageDelay/search.vue"),
          },
          {
            path: "/delayTaskAudit",
            name: "delayTask_audit",
            meta: {
              access: ["delayTask_audit"],
              icon: "md-finger-print",
              title: "延期任务审核",
            },
            component: () => import("@/view/product/packageDelay/audit.vue"),
          },
        ],
      },
      {
        path: "/activationRcord",
        name: "activation_record",
        meta: {
          access: ["activation_record"],
          icon: "md-list",
          // showAlways: true,
          title: "激活记录查询",
        },
        component: parentView,
        children: [{
            path: "/ordinaryPackage",
            name: "ordinary_package",
            meta: {
              access: ["ordinary_package"],
              icon: "md-search",
              title: "全球卡普通套餐",
            },
            component: () =>
              import("@/view/product/activationRecord/ordinaryPakg.vue"),
          },
          {
            path: "/offlineMode",
            name: "offline_mode",
            meta: {
              access: ["offline_mode"],
              icon: "md-search",
              title: "终端厂商线下模式",
            },
            component: () =>
              import("@/view/product/activationRecord/offlineMode.vue"),
          },
          {
            path: "/onlineMode",
            name: "online_mode",
            meta: {
              access: ["online_mode"],
              icon: "md-search",
              title: "终端厂商线上模式",
            },
            component: () =>
              import("@/view/product/activationRecord/onlineMode.vue"),
          },
          {
            path: "/cooperativeModel",
            name: "cooperative_model",
            meta: {
              access: ["cooperative_model"],
              icon: "md-search",
              title: "合作运营商模式",
            },
            component: () =>
              import("@/view/product/activationRecord/cooperativeModel.vue"),
          },
          {
            path: "/activateStatAndSearch",
            name: "activateStat_search",
            meta: {
              access: ["activateStat_search"],
              icon: "md-list",
              showAlways: true,
              title: "激活统计查询",
            },
            component: parentView,
            children: [{
                path: "/activateStat",
                name: "activate_stat",
                meta: {
                  access: ["activate_stat"],
                  icon: "md-search",
                  title: "激活统计查询",
                },
                component: () =>
                  import(
                    "@/view/product/activationRecord/stat/activateStat.vue"
                  ),
              },
              {
                path: "/availabilityStat",
                name: "availability_stat",
                meta: {
                  access: ["availability_stat"],
                  icon: "md-search",
                  title: "激活统计分类",
                },
                component: () =>
                  import("@/view/product/activationRecord/stat/byTypeStat.vue"),
              },
              {
                path: "/statEndMonth",
                name: "statEnd_month",
                meta: {
                  access: ["statEnd_month"],
                  icon: "md-search",
                  title: "月底未激活统计",
                },
                component: () =>
                  import(
                    "@/view/product/activationRecord/stat/unActivatedEndMonth.vue"
                  ),
              },
            ],
          },
        ],
      },
      {
        path: "/SupplierOrder",
        name: "SupplierOrder_management",
        meta: {
          icon: "ios-contacts",
          title: "渠道商订单管理",
          access: ["SupplierOrder_management"],
        },
        component: () => import("@/view/product/SupplierOrder/index.vue"),
      },
      {
        path: "/ChannelCardOrders",
        name: "ChannelCardOrders_management",
        meta: {
          icon: "ios-card-outline",
          title: "渠道商白卡订单管理",
          access: ["ChannelCardOrders_management"],
        },
        component: () => import("@/view/product/ChannelCardOrders/index.vue"),
      },
    ],
  },
  {
    path: "/channel",
    name: "channel_mngr",
    meta: {
      icon: "ios-people",
      access: ["channel_mngr"],
      title: "渠道自服务",
      showAlways: true,
    },
    component: Main,
    children: [{
        path: "/deposit",
        name: "deposit_mngr",
        meta: {
          icon: "logo-usd",
          title: "账户管理",
          access: ["deposit_mngr"],
        },
        component: () => import("@/view/channel/deposit/index.vue"),
      },
      {
        path: "/mealList",
        name: "mealList_mngr",
        meta: {
          icon: "md-funnel",
          hideInMenu: true,
          title: "套餐详情",
          access: ["mealList_mngr"],
        },
        component: () => import("@/view/channel/deposit/mealList.vue"),
      },
      {
        path: "/streamList",
        name: "streamList_mngr",
        meta: {
          icon: "md-funnel",
          hideInMenu: true,
          title: "流水详情",
          access: ["streamList_mngr"],
        },
        component: () => import("@/view/channel/deposit/streamList.vue"),
      },
      {
        path: "/offlinePayment",
        name: "offlinePayment",
        meta: {
          icon: "md-funnel",
          hideInMenu: true,
          title: "充值页面",
          access: ['offlinePayment']
        },
        component: () => import("@/view/channel/deposit/offlinePayment.vue"),
      },
      {
        path: "/marketingAccount",
        name: "marketingAccount",
        meta: {
          icon: "md-funnel",
          hideInMenu: true,
          title: "营销活动详情",
          access: ['marketingAccount']
        },
        component: () => import("@/view/channel/deposit/marketingAccount.vue"),
      },
      {
        path: "/stock",
        name: "stock_mngr",
        meta: {
          icon: "ios-home",
          title: "库存管理",
          access: ["stock_mngr"],
        },
        component: () => import("@/view/channel/stock/showiccid.vue"),
      },
      {
        path: "/cardList",
        name: "cardList_mngr",
        meta: {
          icon: "md-funnel",
          hideInMenu: true,
          title: "卡片详情",
          access: ["cardList_mngr"],
        },
        component: () => import("@/view/channel/stock/cardList.vue"),
      },
      {
        path: "/showiccid",
        name: "showiccid_mngr",
        meta: {
          icon: "md-funnel",
          hideInMenu: true,
          title: "iccid列表",
          access: ["showiccid_mngr"],
        },
        component: () => import("@/view/channel/stock/showiccid.vue"),
      },
      {
        path: "/aqCode",
        name: "aqCode_mngr",
        meta: {
          icon: "md-apps",
          title: "二维码管理",
          access: ["aqCode_mngr"],
        },
        component: () => import("@/view/channel/aqCode/index.vue"),
      },
      {
        path: "/channelfuelPack",
        name: "channelfuelPack_mngr",
        meta: {
          icon: "md-briefcase",
          title: "加油包管理",
          access: ["channelfuelPack_mngr"],
        },
        component: () => import("@/view/channel/fuelPack/index.vue"),
      },
      {
        path: "/channelpackage",
        name: "channelpackage",
        meta: {
          icon: "md-pricetag",
          title: "套餐管理",
          access: ["channelpackage_mngr"],
        },
        component: () => import("@/view/channel/package/index.vue"),
      },
      {
        path: "/fuelPackManagement",
        name: "fuelPackManagement",
        meta: {
          icon: "md-pricetag",
          title: "加油包管理",
          hideInMenu: true,
          // access: ['fuelPackManagement_mngr'],
        },
        component: () =>
          import("@/view/channel/package/modal/fuelPackManagement.vue"),
      },
      {
        path: "/buymeal",
        name: "buymeal_mngr",
        meta: {
          icon: "md-cart",
          title: "套餐购买",
          access: ["buymeal_mngr"],
        },
        component: () => import("@/view/channel/buymeal/index.vue"),
      },
      {
        path: "/order",
        name: "order_mngr",
        meta: {
          icon: "ios-clipboard",
          title: "订单管理",
          access: ["order_mngr"],
        },
        component: () => import("@/view/channel/order/index.vue"),
      },
      {
        path: "/bill",
        name: "bill_mngr",
        meta: {
          icon: "md-funnel",
          hideInMenu: true,
          title: "月账单",
          access: ["bill_mngr"],
        },
        component: () => import("@/view/channel/order/bill.vue"),
      },
      {
        path: "/support",
        name: "support_mngr",
        meta: {
          icon: "md-contacts",
          title: "服务与支持",
          access: ["support_mngr"],
        },
        component: () => import("@/view/channel/support/index.vue"),
      },
      {
        path: "/detailsList",
        name: "detailsList_mngr",
        meta: {
          icon: "md-funnel",
          title: "服务与支持套餐详情",
          hideInMenu: true,
          access: ["detailsList_mngr"],
        },
        component: () => import("@/view/channel/support/detailsList.vue"),
      },
      {
        path: "/useList",
        name: "useList_mngr",
        meta: {
          icon: "md-funnel",
          title: "使用详情",
          hideInMenu: true,
          access: ["useList_mngr"],
        },
        component: () => import("@/view/channel/support/useList.vue"),
      },
      {
        path: "/splocationPackage",
        name: "splocation_package",
        meta: {
          icon: "md-pin",
          hideInMenu: true,
          access: ["splocation_package"],
          title: "当前位置套餐",
        },
        component: () => import("@/view/channel/support/locationPackage.vue"),
      },
      {
        path: "/sppurchasedPackage",
        name: "sppurchased_package",
        meta: {
          icon: "md-cart",
          hideInMenu: true,
          access: ["sppurchased_package"],
          title: "已购买套餐",
        },
        component: () => import("@/view/channel/support/purchasedPackage.vue"),
      },
      {
        path: "/address",
        name: "address_mngr",
        meta: {
          icon: "md-pin",
          title: "账户管理",
          permTypes: [],
          access: ["address_mngr"],
        },
        component: () => import("@/view/channel/address/index.vue"),
      },
      {
        path: "/flowpool",
        name: "flowpool_mngr",
        meta: {
          access: ["flowpool_mngr"],
          icon: "md-wifi",
          showAlways: false,
          title: "流量池",
        },
        component: parentView,
        children: [{
            path: "/cardnumlist",
            name: "cardlist_mngr",
            meta: {
              icon: "md-list-box",
              access: ["cardlist_mngr"],
              title: "卡号列表",
            },
            component: () =>
              import("@/view/channel/flowpool/cardlist/index.vue"),
          },
          {
            path: "/flowlist",
            name: "flowlist_mngr",
            meta: {
              icon: "md-list",
              access: ["flowlist_mngr"],
              title: "流量池列表",
            },
            component: () =>
              import("@/view/channel/flowpool/flowlist/index.vue"),
          },
          {
            path: "/iccidlist",
            name: "iccidlist_mngr",
            meta: {
              icon: "md-list",
              hideInMenu: true,
              access: ["iccidlist_mngr"],
              title: "ICCID列表",
            },
            component: () =>
              import("@/view/channel/flowpool/flowlist/iccidlist.vue"),
          },
          {
            path: "/userecord",
            name: "userecord_mngr",
            meta: {
              icon: "ios-create-outline",
              access: ["userecord_mngr"],
              title: "使用记录",
            },
            component: () =>
              import("@/view/channel/flowpool/userecord/index.vue"),
          },
        ],
      },
      {
        path: "/subChannelProvider",
        name: "subChannelProvider_mngr",
        meta: {
          icon: "md-body",
          title: "子渠道商管理",
          access: ["subChannelProvider_mngr"],
        },
        component: () => import("@/view/channel/subChannelProvider/index.vue"),
      },
      {
        path: "/whiteCardOrders",
        name: "whiteCardOrders_management",
        meta: {
          icon: "ios-card-outline",
          title: "白卡订单管理",
          access: ["whiteCardOrders_management"],
        },
        component: () => import("@/view/channel/whiteCardOrders/index.vue"),
      },
      {
        path: "/channelResourceCooperation",
        name: "channelResourceCooperation",
        meta: {
          icon: "md-infinite",
          access: ["channelResourceCooperation"],
          title: "资源管理",
        },
        component: () => import("@/view/channel/resourceCooperation/index.vue"),
      },
      {
        path: "/channelViewResources",
        name: "channelViewResources",
        meta: {
          hideInMenu: true,
          access: ["channelViewResources"],
          title: "查看资源",
        },
        component: () =>
          import("@/view/channel/resourceCooperation/viewResources/index.vue"),
      },
      {
        path: "/channelCallOrderDetails",
        name: "channelCallOrderDetails",
        meta: {
          hideInMenu: true,
          access: ["channelCallOrderDetails"],
          title: "话单明细",
        },
        component: () =>
          import(
            "@/view/channel/resourceCooperation/callOrderDetails/index.vue"
          ),
      },
      {
        path: "/channelBillingStatistics",
        name: "channelBillingStatistics",
        meta: {
          hideInMenu: true,
          access: ["channelBillingStatistics"],
          title: "账单统计",
        },
        component: () =>
          import(
            "@/view/channel/resourceCooperation/billingStatistics/index.vue"
          ),
      },
      {
        path: "/channelBillingQuery",
        name: "channelBillingQuery",
        meta: {
          icon: "md-football",
          access: ["channelBillingQuery"],
          title: "渠道商账单查询",
        },
        component: () => import("@/view/channel/channelBillingQuery/index.vue"),
      },
      {
        path: "/paymentOrder/management",
        name: "paymentOrderManagement",
        meta: {
          icon: "md-list",
          access: ["paymentOrderManagement"],
          title: "支付记录查询",
        },
        component: () => import("@/view/channel/paymentOrder/index.vue"),
      },
    ],
  },
  {
    path: "/operators",
    name: "operators_mngr",
    meta: {
      icon: "md-person",
      title: "运营商管理",
      access: ["operators_mngr"],
    },
    component: Main,
    children: [{
        path: "/operatorsindex",
        name: "operatorsindex_mngr",
        meta: {
          title: "运营商管理",
          icon: "md-person",
          access: ["operators_mngr"],
        },
        component: () => import("@/view/operators/index.vue"),
      },
      {
        path: "/resourceSupplier",
        name: "ResourceSupplier_mngr",
        meta: {
          title: "资源供应商管理",
          icon: "md-person",
          access: ["ResourceSupplier_mngr"],
        },
        component: () => import("@/view/operators/ResourceSupplier.vue"),
      },
    ],
  },
  {
    path: "/channelSell",
    name: "channelSell_mngr",
    meta: {
      icon: "md-person",
      title: "渠道商销售数据",
      access: ["channelSell_mngr"],
    },
    component: Main,
    children: [{
        path: "/channelSellindex",
        name: "channelSellindex_mngr",
        meta: {
          title: "首页",
          icon: "md-git-compare",
          access: ["channelSellindex_mngr"],
        },
        component: () => import("@/view/channelSell/index.vue"),
      },
      {
        path: "/channelSellHistory",
        name: "channelSellHistory_mngr",
        meta: {
          title: "历史账单",
          icon: "md-analytics",
          access: ["channelSellHistory_mngr"],
        },
        component: () => import("@/view/channelSell/history.vue"),
      },
      {
        path: "/topUpRecords",
        name: "topUp_records",
        meta: {
          title: "充值记录",
          icon: "md-recording",
          access: ["topUp_records"],
        },
        component: () => import("@/view/channelSell/topUpRecords.vue"),
      },
    ],
  },
  {
    path: "/report",
    name: "report_mngr",
    meta: {
      icon: "md-print",
      title: "报表功能",
      access: ["report_mngr"],
    },
    component: Main,
    children: [{
        path: "/exchange",
        name: "exchange_mngr",
        meta: {
          icon: "logo-yen",
          title: "汇率管理",
          access: ["exchange_mngr"],
        },
        component: () => import("@/view/report/exchange/index.vue"),
      },
      {
        path: "/cardsell",
        name: "cardsell_mngr",
        meta: {
          icon: "ios-card",
          title: "卡销售报表",
          access: ["cardsell_mngr"],
        },
        component: () => import("@/view/report/cardsell/index.vue"),
      },
      {
        path: "/cardactive",
        name: "cardactive_mngr",
        meta: {
          icon: "md-card",
          title: "卡激活报表",
          access: ["cardactive_mngr"],
        },
        component: () => import("@/view/report/cardactive/index.vue"),
      },
      {
        path: "/subscription",
        name: "subscription_mngr",
        meta: {
          icon: "md-cart",
          title: "套餐订购报表",
          access: ["subscription_mngr"],
        },
        component: () => import("@/view/report/subscription/index.vue"),
      },
      {
        path: "/activation",
        name: "activation_mngr",
        meta: {
          icon: "md-pulse",
          title: "套餐激活报表",
          access: ["activation_mngr"],
        },
        component: () => import("@/view/report/activation/index.vue"),
      },
      {
        path: "/reuse",
        name: "reuse_mngr",
        meta: {
          icon: "md-refresh",
          title: "重复使用查询",
          access: ["reuse_mngr"],
        },
        component: () => import("@/view/report/reuse/index.vue"),
      },
      {
        path: "/analysis",
        name: "analysis_mngr",
        meta: {
          icon: "md-stats",
          title: "套餐分析报表",
          access: ["analysis_mngr"],
        },
        component: () => import("@/view/report/analysis/index.vue"),
      },
      {
        path: "/terminal",
        name: "terminal_mngr",
        meta: {
          icon: "ios-phone-portrait",
          title: "终端结算报表",
          access: ["terminal_mngr"],
        },
        component: () => import("@/view/report/terminal/index.vue"),
      },
      {
        path: "/afterPay",
        name: "terminal_after_pay",
        meta: {
          icon: "logo-bitcoin",
          title: "后付费结算报表",
          access: ["terminal_after_pay"],
        },
        component: () => import("@/view/report/afterPay/index.vue"),
      },
      {
        path: "/channelReport",
        name: "channelReport_mngr",
        meta: {
          icon: "ios-contact",
          title: "运营商结算报表",
          access: ["channelReport_mngr"],
        },
        component: () => import("@/view/report/channelReport/index.vue"),
      },
      {
        path: "/income",
        name: "income_mngr",
        meta: {
          icon: "ios-home",
          title: "线下收入报表",
          access: ["income_mngr"],
        },
        component: () => import("@/view/report/income/index.vue"),
      },
      {
        path: "/costReport",
        name: "costReport_mngr",
        meta: {
          icon: "logo-yen",
          title: "成本报表",
          access: ["costReport_mngr"],
        },
        component: () => import("@/view/report/costReport/index.vue"),
      },
      {
        path: "/esimDownloadReport",
        name: "esimDownloadReport_mngr",
        meta: {
          icon: "logo-euro",
          title: "ESIM Download报表",
          access: ["esimDownloadReport"],
        },
        component: () => import("@/view/report/esimDownloadReport/index.vue"),
      },
    ],
  },
  {
    path: "/sms",
    name: "smsManage",
    meta: {
      icon: "md-mail",
      access: ["smsManage"],
      title: "短信管理",
      showAlways: true,
    },
    component: Main,
    children: [{
        path: "/noticeSMSMngr",
        name: "notificationSMS",
        meta: {
          icon: "ios-mail",
          access: ["notificationSMS"],
          showAlways: false,
          title: "通知短信",
        },
        component: parentView,
        children: [{
            path: "/noticeSMS",
            name: "notificationIndex",
            meta: {
              icon: "ios-mail",
              access: ["notificationIndex"],
              title: "通知短信",
            },
            component: () => import("@/view/SMS/notification/index.vue"),
          },

          {
            path: "/areaWelcomeSMSIndex",
            name: "areaWelcomeSMSIndex",
            meta: {
              icon: "ios-mail",
              access: ["areaWelcomeSMSIndex"],
              title: "地区欢迎短信",
            },
            component: () => import("@/view/SMS/areaWelcome/index.vue"),
          },
          {
            path: "/areaWelcomeSMSDetails",
            name: "areaWelcomeInfo",
            meta: {
              icon: "md-list",
              access: ["marketingInfo"],
              title: "地区欢迎短信详情",
              hideInMenu: true,
            },
            component: () => import("@/view/SMS/areaWelcome/detail.vue"),
          },
          {
            path: "/areaWelcomeEdit",
            name: "areaWelcomeEdit",
            meta: {
              icon: "ios-mail",
              access: ["areaWelcomeEdit"],
              title: "地区欢迎短信编辑",
              hideInMenu: true,
            },
            component: () => import("@/view/SMS/areaWelcome/update.vue"),
          },
          {
            path: "/areaWelcomeEditAdd",
            name: "areaWelcomeEditAdd",
            meta: {
              icon: "ios-mail",
              access: ["areaWelcomeEditAdd"],
              title: "地区欢迎短信新增",
              hideInMenu: true,
            },
            component: () => import("@/view/SMS/areaWelcome/update.vue"),
          },
        ],
      },
      {
        path: "/customerSMSMngr",
        name: "customerSMS",
        meta: {
          icon: "ios-mail",

          access: ["customerSMS"],
          showAlways: false,
          title: "客服短信",
        },
        component: parentView,
        children: [{
          path: "/customerSMS",
          name: "customerIndex",
          meta: {
            access: ["customerIndex"],
            icon: "ios-mail",

            title: "客服短信",
          },
          component: () => import("@/view/SMS/customer/index.vue"),
        }, ],
      },
      {
        path: "/marketingSMSMngr",
        name: "marketingSMS",
        meta: {
          icon: "ios-mail",
          access: ["marketingSMS"],
          showAlways: false,
          title: "营销短信",
        },
        component: parentView,
        children: [{
            path: "/marketingSMS",
            name: "marketingIndex",
            meta: {
              icon: "ios-mail",
              access: ["marketingIndex"],
              title: "营销短信",
            },
            component: () => import("@/view/SMS/marketing/index.vue"),
          },
          {
            path: "/marketingSMSDetails",
            name: "marketingInfo",
            meta: {
              icon: "md-list",
              access: ["marketingInfo"],
              title: "营销短信详情",
              hideInMenu: true,
            },
            component: () => import("@/view/SMS/marketing/marketing.vue"),
          },
        ],
      },
    ],
  },
  {
    path: "/customer",
    name: "customerManage",
    meta: {
      access: ["customerManage"],
      icon: "ios-people",
      title: "客户管理",
      showAlways: true,
    },
    component: Main,
    children: [{
        path: "/channelMngr",
        name: "channelManage",
        meta: {
          access: ["channelManage"],
          icon: "md-people",
          showAlways: false,
          title: "渠道商",
        },
        component: parentView,
        children: [{
            path: "/channelInfo",
            name: "channelIndex",
            meta: {
              icon: "md-people",
              access: ["channelIndex"],
              title: "渠道商",
            },
            component: () => import("@/view/customer/channel/index.vue"),
          },
          {
            path: "/billCheck",
            name: "billCheck",
            meta: {
              icon: "md-people",
              access: ["billCheck"],
              title: "账单核对",
            },
            component: () => import("@/view/customer/channel/billCheck.vue"),
          },
          {
            path: "/packageGroup",
            name: "packageGroup",
            meta: {
              icon: "md-people",
              access: ["packageGroup"],
              title: "套餐组",
            },
            component: () => import("@/view/customer/packageGroup/index.vue"),
          },
          {
            path: "/zeroLevelChannel",
            name: "zeroLevelChannel",
            meta: {
              icon: "md-people",
              access: ["zeroLevelChannel"],
              title: "零级渠道商",
            },
            component: () =>
              import("@/view/customer/zeroLevelChannel/index.vue"),
          },
          {
            path: "/a2zBillPriceMngr",
            name: "a2zBillPriceMngr",
            meta: {
              icon: "md-people",
              access: ["a2zBillPriceMngr"],
              title: "流量计费价格管理",
            },
            component: () =>
              import("@/view/customer/a2zBillPriceMngr/index.vue"),
          },
          {
            path: "/costPricingMngr",
            name: "costPricingMngr",
            meta: {
              icon: "md-people",
              access: ["costPricingMngr"],
              title: "成本价格管理",
            },
            component: () =>
              import("@/view/customer/costPricingMngr/index.vue"),
          },
          {
            path: "/imsiFeeMngr",
            name: "imsiFeeMngr",
            meta: {
              icon: "md-people",
              access: ["imsiFeeMngr"],
              title: "IMSI费管理",
            },
            component: () => import("@/view/customer/imsiFeeMngr/index.vue"),
          },
          {
            path: "/channelAdd",
            name: "channelAdd",
            meta: {
              icon: "md-list",
              access: ["channelAdd"],
              title: "渠道商新增",
              hideInMenu: true,
            },
            component: () => import("@/view/customer/channel/channel.vue"),
          },
          {
            path: "/channelDetails",
            name: "channelInfo",
            meta: {
              icon: "md-list",
              access: ["channelInfo"],
              title: "渠道商详情",
              hideInMenu: true,
            },
            component: () => import("@/view/customer/channel/channel.vue"),
          },
          {
            path: "/channelEdit",
            name: "channelUpdate",
            meta: {
              icon: "md-list",
              access: ["channelUpdate"],
              title: "渠道商编辑",
              hideInMenu: true,
            },
            component: () => import("@/view/customer/channel/channel.vue"),
          },
          {
            path: "/zeroChannelAdd",
            name: "zeroChannelAdd",
            meta: {
              icon: "md-list",
              access: ["zeroChannelAdd"],
              title: "新建零级渠道商",
              hideInMenu: true,
            },
            component: () =>
              import("@/view/customer/zeroLevelChannel/zeroChannel.vue"),
          },
          {
            path: "/zeroChannelUpdate",
            name: "zeroChannelUpdate",
            meta: {
              icon: "md-list",
              access: ["zeroChannelUpdate"],
              title: "修改零级渠道商",
              hideInMenu: true,
            },
            component: () =>
              import("@/view/customer/zeroLevelChannel/zeroChannelUpdate.vue"),
          },
          {
            path: "/a2zBillPriceDetail",
            name: "a2zBillPriceDetail",
            meta: {
              icon: "md-list",
              access: ["a2zBillPriceDetail"],
              title: "流量计费价格管理—规则详情",
              hideInMenu: true,
            },
            component: () =>
              import("@/view/customer/a2zBillPriceMngr/detail.vue"),
          },
          {
            path: "/costPricingDetails",
            name: "costPricingDetails",
            meta: {
              icon: "md-list",
              access: ["costPricingDetails"],
              title: "成本价格管理—详情",
              hideInMenu: true,
            },
            component: () =>
              import("@/view/customer/costPricingMngr/detail.vue"),
          },
          {
            path: "/adminMarketingAccount",
            name: "adminMarketingAccount",
            meta: {
              icon: "md-people",
              access: ["adminMarketingAccount"],
              title: "营销活动详情",
              hideInMenu: true
            },
            component: () => import("@/view/channel/deposit/marketingAccount.vue"),
          },
        ],
      },
      {
        path: "/cooperativeMngr",
        name: "cooperativeManage",
        meta: {
          icon: "md-people",
          showAlways: false,
          access: ["cooperativeManage"],
          title: "合作运营商",
        },
        component: parentView,
        children: [{
            path: "/cooperative",
            name: "cooperativeIndex",
            meta: {
              icon: "md-people",
              access: ["cooperativeIndex"],
              title: "合作运营商",
            },
            component: () => import("@/view/customer/cooperative/index.vue"),
          },
          {
            path: "/cooperativeDetails",
            name: "cooperativeInfo",
            meta: {
              icon: "md-list",
              access: ["cooperativeInfo"],
              title: "合作运营商详情",
              hideInMenu: true,
            },
            component: () =>
              import("@/view/customer/cooperative/cooperative.vue"),
          },
        ],
      },
      {
        path: "/postPaidMngr",
        name: "postPaymentChannel",
        meta: {
          icon: "md-people",
          access: ["postPaymentChannel"],
          showAlways: false,
          title: "后付费渠道",
        },
        component: parentView,
        children: [{
            path: "/postPaid",
            name: "paymentChannelIndex",
            meta: {
              icon: "md-people",
              access: ["paymentChannelIndex"],
              title: "后付费渠道",
            },
            component: () =>
              import("@/view/customer/postPaymentChannel/index.vue"),
          },
          {
            path: "/postPaidDetails",
            name: "paymentChannelInfo",
            meta: {
              icon: "md-list",
              access: ["paymentChannelInfo"],
              title: "后付费渠道详情",
              hideInMenu: true,
            },
            component: () =>
              import("@/view/customer/postPaymentChannel/paymentChannel.vue"),
          },
        ],
      },
      {
        path: "/manufacturerMngr",
        name: "manufacturerManage",
        meta: {
          icon: "md-people",
          showAlways: false,
          access: ["manufacturerManage"],
          title: "终端厂商",
        },
        component: parentView,
        children: [{
            path: "/manufacturer",
            name: "manufacturerIndex",
            meta: {
              icon: "md-people",
              access: ["manufacturerIndex"],
              title: "终端厂商",
            },
            component: () => import("@/view/customer/manufacturer/index.vue"),
          },
          {
            path: "/testImsi",
            name: "test_imsi",
            meta: {
              icon: "md-people",
              access: ["test_imsi"],
              title: "测试IMSI管理",
            },
            component: () =>
              import("@/view/customer/manufacturer/testImsi.vue"),
          },
          {
            path: "/manufacturerUpdate",
            name: "manufacturer_update",
            meta: {
              icon: "md-list",
              access: ["manufacturer_update"],
              title: "终端厂商编辑",
              hideInMenu: true,
            },
            component: () => import("@/view/customer/manufacturer/update.vue"),
          },
          {
            path: "/manufacturerDetails",
            name: "manufacturerInfo",
            meta: {
              icon: "md-list",
              access: ["manufacturerInfo"],
              title: "终端厂商详情",
              hideInMenu: true,
            },
            component: () =>
              import("@/view/customer/manufacturer/manufacturer.vue"),
          },
          {
            path: "/billDetails",
            name: "billInfo",
            meta: {
              icon: "md-list",
              access: ["billInfo"],
              title: "账单详情",
              hideInMenu: true,
            },
            component: () =>
              import("@/view/customer/manufacturer/billDetails.vue"),
          },
          {
            path: "/flowDetails",
            name: "flowInfo",
            meta: {
              icon: "md-list",
              access: ["flowInfo"],
              title: "流量明细",
              hideInMenu: true,
            },
            component: () =>
              import("@/view/customer/manufacturer/flowDetails.vue"),
          },
        ],
      },
      {
        path: "/channelPool",
        name: "channelPool_mngr",
        meta: {
          icon: "md-wifi",
          access: ["channelPool_mngr"],
          title: "流量池管理",
        },
        component: () => import("@/view/customer/flowpool/index.vue"),
      },
      {
        path: "/channelcardlist",
        name: "channelcardlist_mngr",
        meta: {
          icon: "md-list-box",
          access: ["cardlist_mngr"],
          title: "卡号列表",
          hideInMenu: true,
        },
        component: () => import("@/view/customer/flowpool/cardlist/index.vue"),
      },
      {
        path: "/channelflowlist",
        name: "channelflowlist_mngr",
        meta: {
          icon: "md-list",
          access: ["flowlist_mngr"],
          title: "流量池列表",
          hideInMenu: true,
        },
        component: () => import("@/view/customer/flowpool/flowlist/index.vue"),
      },
      {
        path: "/channeliccidlist",
        name: "channeliccidlist_mngr",
        meta: {
          icon: "md-list",
          hideInMenu: true,
          access: ["iccidlist_mngr"],
          title: "ICCID列表",
        },
        component: () =>
          import("@/view/customer/flowpool/flowlist/iccidlist.vue"),
      },
      {
        path: "/channeluserecord",
        name: "channeluserecord_mngr",
        meta: {
          icon: "ios-create-outline",
          access: ["userecord_mngr"],
          title: "使用记录",
          hideInMenu: true,
        },
        component: () => import("@/view/customer/flowpool/userecord/index.vue"),
      },
      {
        path: "/resourceCooperation",
        name: "resourceCooperation",
        meta: {
          icon: "md-infinite",
          access: ["resourceCooperation"],
          title: "资源管理",
        },
        component: () =>
          import("@/view/customer/resourceCooperation/index.vue"),
      },
      {
        path: "/viewResources",
        name: "viewResources",
        meta: {
          hideInMenu: true,
          access: ["viewResources"],
          title: "查看资源",
        },
        component: () =>
          import("@/view/customer/resourceCooperation/viewResources/index.vue"),
      },
      {
        path: "/callOrderDetails",
        name: "callOrderDetails",
        meta: {
          hideInMenu: true,
          access: ["callOrderDetails"],
          title: "话单明细",
        },
        component: () =>
          import(
            "@/view/customer/resourceCooperation/callOrderDetails/index.vue"
          ),
      },
      {
        path: "/billingStatistics",
        name: "billingStatistics",
        meta: {
          hideInMenu: true,
          access: ["billingStatistics"],
          title: "账单统计",
        },
        component: () =>
          import(
            "@/view/customer/resourceCooperation/billingStatistics/index.vue"
          ),
      },
    ],
  },

  {
    path: "/service",
    name: "service_brace",
    meta: {
      title: "客服支撑管理",
      showAlways: true,
      icon: "logo-octocat",
      access: ["service_brace"],
    },
    component: Main,
    children: [{
        path: "/serviceIndex",
        name: "service_index",
        meta: {
          icon: "md-card",
          access: ["service_index"],
          title: "卡片信息",
        },
        component: () => import("@/view/service/index.vue"),
      },
      {
        path: "/faultHandling",
        name: "fault_mngr",
        meta: {
          icon: "md-alert",
          title: "故障处理",
          access: ["fault_mngr"]
        },
        component: () => import("@/view/service/faultHandling/index.vue")
      },
      {
        path: "/faultHandlingAdd",
        name: "fault_add",
        meta: {
          icon: "md-add",
          title: "新建故障处理",
          hideInMenu: true,
          access: ["fault_add"]
        },
        component: () => import("@/view/service/faultHandling/add.vue")
      },
      {
        path: "/locationPackage",
        name: "location_package",
        meta: {
          icon: "md-pin",
          hideInMenu: true,
          access: ["location_package"],
          title: "当前位置套餐",
        },
        component: () => import("@/view/service/locationPackage.vue"),
      },
      {
        path: "/purchasedPackage",
        name: "purchased_package",
        meta: {
          icon: "md-cart",
          hideInMenu: true,
          access: ["purchased_package"],
          title: "已购买套餐",
        },
        component: () => import("@/view/service/purchasedPackage.vue"),
      },
      {
        path: "/GTPAddressConfig",
        name: "GTPAddressConfig",
        meta: {
          icon: "md-build",
          title: "GTP话单地址配置",
          access: ["GTPAddressConfig_mngr"]
        },
        component: () => import("@/view/service/GTPAddressConfig/index.vue")
      },
    ],
  },
  {
    path: "/finance",
    name: "finance_mngr",
    meta: {
      icon: "logo-yen",
      title: "财务系统管理",
      access: ["finance_mngr"],
    },
    component: Main,
    children: [{
        path: "/billing",
        name: "billing_mngr",
        meta: {
          icon: "md-print",
          title: "出账管理",
          access: ["billing_mngr"],
        },
        component: () => import("@/view/finance/billing/index.vue"),
      },
      {
        path: "/history",
        name: "history_mngr",
        meta: {
          icon: "md-print",
          hideInMenu: true,
          title: "历史真实账单",
          // access: ['history_mngr']
        },
        component: () => import("@/view/finance/billing/history.vue"),
      },
      {
        path: "/acountingPeriod",
        name: "acounting_period",
        meta: {
          icon: "md-menu",
          title: "账期管理",
          access: ["acounting_period"],
        },
        component: () => import("@/view/finance/acountingPeriod/index.vue"),
      },
      {
        path: "/serviceRechargeApproval",
        name: "serviceRecharge_approval",
        meta: {
          icon: "md-menu",
          title: "自服务充值审批",
          access: ['serviceRecharge_approval']
        },
        component: () =>
          import("@/view/finance/serviceRechargeApproval/index.vue"),
      },
      {
        path: '/billingAdjust',
        name: 'billing_adjust',
        meta: {
          icon: 'md-menu',
          title: '账单调整',
          access: ['billing_adjust']
        },
        component: () => import('@/view/finance/billingAdjust/index.vue')
      },
      {
        path: '/addBillingAdjust',
        name: 'addBilling_adjust',
        meta: {
          icon: 'md-menu',
          title: '新增调账',
          hideInMenu: true,
          access: ['addBilling_adjust']
        },
        component: () => import('@/view/finance/billingAdjust/add.vue')
      },
    ],
  },
  {
    path: "/realname",
    name: "realname_mngr",
    meta: {
      icon: "ios-create",
      title: "实名制管理",
      access: ["realname_mngr"],
    },
    component: Main,
    children: [{
        path: "/rule",
        name: "rule_mngr",
        meta: {
          icon: "md-settings",
          title: "规则管理",
          access: ["rule_mngr"],
        },
        component: () => import("@/view/realname/rule/index.vue"),
      },
      {
        path: "/ruleDetail",
        name: "ruleDetail_mngr",
        meta: {
          icon: "md-print",
          hideInMenu: true,
          title: "规则详情",
          access: ["rule_mngr"],
        },
        component: () => import("@/view/realname/rule/ruleDetail.vue"),
      },
      {
        path: "/certification",
        name: "certification_mngr",
        meta: {
          icon: "md-contacts",
          title: "人工认证",
          access: ["certification_mngr"],
        },
        component: () => import("@/view/realname/certification/index.vue"),
      },
      {
        path: "/certificationinfo",
        name: "certificationinfo_mngr",
        meta: {
          icon: "ios-chatbubbles",
          title: "认证信息",
          access: ["certificationinfo_mngr"],
        },
        component: () => import("@/view/realname/certificationinfo/index.vue"),
      },
    ],
  },
  {
    path: "/marketingActivity",
    name: "marketing_mngr",
    meta: {
      icon: "ios-pie", // 可以选择一个合适的图标
      title: "营销管理",
      access: ["marketing_mngr"], // 确保用户有访问权限
    },
    component: Main,
    children: [{
        path: "/marketingActivityIndex", // 营销活动管理列表页面
        name: "marketingActivityIndex",
        meta: {
          icon: "md-list",
          title: "营销活动管理",
          access: ["marketingActivityIndex"],
        },
        component: () => import("@/view/marketingActivity/index.vue"), // 对应的Vue组件
      },
      {
        path: "/marketingActivityUpdate", // 营销活动管理列表页面
        name: "marketingActivityUpdate",
        meta: {
          icon: "md-list",
          title: "修改营销活动",
          hideInMenu: true,
          access: ["marketingActivityUpdate"],
        },
        component: () => import("@/view/marketingActivity/update.vue"), // 对应的Vue组件
      },
      //新增
      {
        path: "/marketingActivityAdd", // 营销活动管理列表页面
        name: "marketingActivityAdd",
        meta: {
          icon: "md-list",
          title: "新增营销活动",
          hideInMenu: true,
          access: ["marketingActivityAdd"],
        },
        component: () => import("@/view/marketingActivity/update.vue"), // 对应的Vue组件
      }
      // 其他可能的子页面，如编辑页面等，可以按需添加
    ],
  },
  {
    path: "/operationAgencyApproval",
    name: "operationAgencyApproval",
    meta: {
      title: "代办审批详情页",
      hideInMenu: true,
    },
    component: () =>
      import("@/view/jumpOperations/operationAgencyApproval.vue"),
  },
  {
    path: "/proxyReviewed",
    name: "proxyReviewed",
    meta: {
      title: "代办审批详情页",
      hideInMenu: true,
    },
    component: () => import("@/view/jumpOperations/proxyReviewed.vue"),
  },
  {
    path: "/costToDo",
    name: "costToDo",
    meta: {
      title: "成本报表待办审批页",
      hideInMenu: true,
    },
    component: () =>
      import("@/view/jumpOperations/costToDo.vue"),
  },
  {
    path: '/aprvDetails',
    name: 'aprvDetails',
    meta: {
      title: '代办审批详情页',
      hideInMenu: true
    },
    component: () => import('@/view/jumpOperations/billingAdjust.vue')
  },
  {
    path: "/401",
    name: "error_401",
    meta: {
      hideInMenu: true,
    },
    component: () => import("@/view/error-page/401.vue"),
  },
  {
    path: "/500",
    name: "error_500",
    meta: {
      hideInMenu: true,
    },
    component: () => import("@/view/error-page/500.vue"),
  },
  {
    path: "*",
    name: "error_404",
    meta: {
      hideInMenu: true,
    },
    component: () => import("@/view/error-page/404.vue"),
  },
];
