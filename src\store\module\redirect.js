import { getRedirectUrl } from '@/api/system/redirect'

const state = {
  url: null,
  loading: false,
  error: null
}

const mutations = {
  SET_URL(state, url) {
    state.url = url
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  SET_ERROR(state, error) {
    state.error = error
  }
}

const actions = {
  async fetchRedirectUrl({ commit, state }, key) {  // 接收 key 参数
    if (state.url && !key) return state.url  // 如果没有 key，返回缓存的 URL（可选）

    commit('SET_LOADING', true)
    try {
      const res = await getRedirectUrl(key)  // 传递 key 给 API
      if (res.code === '0000') {
        commit('SET_URL', res.data)
        return res.data
      }
      throw new Error(res.msg || 'Failed to get redirect URL')
    } catch (error) {
      commit('SET_ERROR', error.msg)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

const getters = {
  redirectUrl: state => state.url,
  isLoading: state => state.loading,
  error: state => state.error
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
