// import Vue from 'vue'

// // 营销活动结算待办
// export default {
//   state: {
//     editedRebates: {},
//     selectedRows: [] // 改为数组存储
//   },
//   mutations: {
//     setEditedRebate(state, {
//       id,
//       value
//     }) {
//       Vue.set(state.editedRebates, id, value)
//     },
//     // clearEditedRebates(state) {
//     //   state.editedRebates = {}; // 清除所有编辑过的返利数据
//     // },
//     toggleSelection(state, id) {
//       const index = state.selectedRows.indexOf(id)
//       if (index > -1) {
//         state.selectedRows.splice(index, 1)
//       } else {
//         state.selectedRows.push(id)
//       }
//     },
//     // 添加设置选中数组的方法
//     setSelectedRows(state, ids) {
//       state.selectedRows = ids
//     },
//   },
// }
