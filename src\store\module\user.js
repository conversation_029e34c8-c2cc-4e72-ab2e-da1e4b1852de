import {
  login,
  ssoLoginGDS
} from '@/api/system/login'

import {
  setToken,
  setUserId,
  getUserId,
  getToken,
} from '@/libs/util'

export default {
  state: {
	istoken:true,
    userName: '',
    userId: getUserId(),
    roleId: '',
    avatarImgPath: '',
    token: getToken(),
    access: '',
    hasGetInfo: false,
    unreadCount: 0,
    isUpdatePassword:2,
    userBtnPriv: [],
    messageUnreadList: [],
    messageReadedList: [],
    messageTrashList: [],
    messageContentStore: {}
  },
  mutations: {
    setAvatar(state, avatarPath) {
      state.avatarImgPath = avatarPath
    },
    setUserId(state, id) {
      state.userId = id
      setUserId(id)
    },
    setRoleId(state, id) {
      state.roleId = id
    },
    setUserName(state, name) {
      state.userName = name
    },
    setIsUpdatePassword(state, flag) {
      state.isUpdatePassword = flag
    },
    setAccess(state, access) {
      state.access = access
    },
    setToken(state, token) {
      state.token = token
      setToken(token)
    },
    setHasGetInfo(state, status) {
      state.hasGetInfo = status
    },
    setMessageCount(state, count) {
      state.unreadCount = count
    },
    setMessageUnreadList(state, list) {
      state.messageUnreadList = list
    },
    setUserPriv(state, list) {
      state.userBtnPriv = list
    },
    setMessageReadedList(state, list) {
      state.messageReadedList = list
    },
    setMessageTrashList(state, list) {
      state.messageTrashList = list
    },
    updateMessageContentStore(state, {
      msg_id,
      content
    }) {
      state.messageContentStore[msg_id] = content
    },
    moveMsg(state, {
      from,
      to,
      msg_id
    }) {
      const index = state[from].findIndex(_ => _.msg_id === msg_id)
      const msgItem = state[from].splice(index, 1)[0]
      msgItem.loading = false
      state[to].unshift(msgItem)
    }
  },
  getters: {
    messageUnreadCount: state => state.messageUnreadList.length,
    messageReadedCount: state => state.messageReadedList.length,
    messageTrashCount: state => state.messageTrashList.length
  },
  actions: {
    iistoken(store,home){
      store.state.istoken=home
    },
    // 登录
    handleLogin({
      commit
    }, e) {
      return new Promise((resolve, reject) => {
        // ------------------------------------------------------------------------------------
        // --------------------------------------版本二----------------------------------------
        // ------------------------------------------------------------------------------------
        let btnPriv_version2 = [
          //码号资源
          {
          'url': '/msisdn',
          'priv': ['search', 'add', 'delete', 'update', 'view', 'export', 'batchUpdate', 'batchDelete']
          },
          {
          'url': '/iccid',
          'priv': ['search', 'add', 'delete', 'update', 'view', 'export', 'batchUpdate', 'batchDelete']
          },
          {
          'url': '/imsi',
          'priv': ['search', 'add', 'delete', 'update', 'view', 'export', 'batchUpdate', 'batchDelete']
          },
          {
          'url': '/supplyImsi',
          'priv': ['search', 'add', 'delete', 'update', 'view', 'import', 'export', 'batchUpdate',
            'batchDelete'
          ]
          },
          //主卡管理
          {
          'url': '/masterCard',
          'priv': ['search', 'add', 'view','update','batchUpdate', 'import', 'export','updateExpire']
          },
          //========================'update','batchUpdate'
          //卡池管理
          {
          'url': '/cardPool',
          'priv': ['search', 'add', 'view', 'vimsiManage','update', 'delete', 'copy', 'export']
          },
          //VIMSI管理
          {
          'url': '/vimsi',
          'priv': ['search', 'add', 'delete', 'batchDelete']
          },
          // 运营商管理
          {
          'url': '/operators',
          'priv': ['search', 'add', 'update', 'delete']
          },
          //产品运营——套餐配置——单个
          {
          'url': '/individualConfig',
          'priv': ['search','add', 'delete', 'viewPackage', 'unbundling']
          },
          //===================产品运营——套餐配置——批量
          {
          'url': '/batchConfig',
          'priv': ['search','addBatch', 'download', 'rollBack']
          },
          //系统管理——账户列表
          {
          'url': '/accountList',
          'priv': ['search','add', 'delete', 'edit', 'freeze', 'unfreeze']
          },
          //系统管理——角色管理
          {
          'url': '/roleMngr',
          'priv': ['search','add', 'view', 'setPriv']
          },
          //系统管理——修改密码
          {
          'url': '/pwdmngr',
          'priv': ['search','edit']
          },
          //套餐管理
          {
          'url': '/packageMngr',
          'priv': ['search', 'add','view','batchUpdate','update','copy','batchDelete','delete','check'],
          //========================batchUpdate update copy
          //========================batchDelete delete check
          },
          //========================客服支撑-卡片信息
          {
          'url': '/serviceIndex',
          'priv': ['search', 'currentPackage', 'purchasedPackage', 'locationRecord', 'sendSMS', 'view',
            'export']
          },
          //========================当前位置套餐
          {
          'url': '/locationPackage',
          'priv': ['search','active']
          },
          //========================已购买套餐
          {
          'url': '/purchasedPackage',
          //提前回收：recovery
          //更换(VIMSI)：change
          'priv': ['search','view', 'recovery', 'change']
          },

          //短信管理
          //========================通知短信
          {
          'url': '/noticeSMS',
          //add、update包含模态框按钮和提交按钮
          'priv': ['search', 'add', 'batchDelete', 'view', 'update', 'delete']
          },
          //========================/客服短信
          {
          'url': '/customerSMS',
          //add、update包含模态框按钮和提交按钮
          'priv': ['search','add', 'batchDelete', 'update', 'delete']
          },
          //========================/营销短信
          {
            'url': '/marketingSMS',
            //add、update包含模态框按钮和提交按钮
            'priv': ['search', 'import', 'add', 'view', 'update', 'cancel']
          },
          //========================/营销短信详情
          {
            'url': '/marketingSMSDetails',
            'priv': ['export']
          },
          //========================合作运营商管理
          {
          'url': '/cooperative',
          //view为页面跳转
          //add、update包含模态框按钮和提交按钮
          'priv': ['search', 'add', 'batchDelete', 'view', 'update', 'delete', 'check']
          },
         //========================合作运营商详情
          {
          'url': '/cooperativeDetails',
          'priv': ['export']
          },
         //========================后付费渠道管理
          {
          'url': '/postPaid',
          //view为页面跳转
          //add、update、orderAdd包含模态框按钮和提交按钮
          'priv': ['search', 'add', 'orderAdd', 'batchDelete', 'view', 'update', 'delete', 'check']
          },
         //========================后付费渠道详情
          {
          'url': '/postPaidDetails',
          'priv': ['search', 'export']
          },
          //终端厂商管理
          {
          'url': '/terminal',
          //view为页面跳转
          //add、update包含模态框按钮和提交按钮
          'priv': ['search', 'add', 'batchDelete', 'view', 'update', 'delete', 'check']
          },
          //终端厂商详情
          {
          'url': '/terminalDetails',
          'priv': ['search', 'export']
          },
        ]

        //测试用
        // let res = {
        //   "code": "0000",
        //   "msg": "成功",
        //   "data": {
        //     "access": ['system_mngr', 'account_list', 'pwd_mngr', 'login_mngr', 'pri_mngr','log_mngr', 'product_operation',
        //       'package_config', 'individual_config', 'batch_config'
        //     ],
        //     "token": "eyJ0eXAiOiJKV1Q"
        //   }
        // }
        // commit('setUserPriv', btnPriv_version1)
        // // commit('setUserPriv', btnPriv)
        // commit('setUserName', 'zzkk')
        // commit('setUserId', '*********')
        // commit('setAccess', res.data.access)
        // commit('setHasGetInfo', true)
        // commit('setToken', res.data.token)
        // resolve(res)

        //调用登录接口
        login(e).then(res => {
          if (res.code === '0000') {
            var privList = res.data.userDetails.pagePrivileges
            var access = []
            var btnPrivs = []
            for (var n = 0; n < privList.length; n++) {
              access.push(privList[n].access) //菜单权限
              btnPrivs.push({ //按钮权限
                url: privList[n].url,
                priv: privList[n].buttons
              })
            }
            commit('setUserPriv', btnPrivs) //页面权限
            commit('setUserName', res.data.userDetails.username)
            commit('setIsUpdatePassword', res.data.userDetails.rePassword) //密码是否失效
            // commit('setIsUpdatePassword', '2')//自测
            commit('setUserId', res.data.userDetails.id)
            commit('setRoleId', res.data.userDetails.roleId) //角色ID
            // commit('setRoleId', '3') //角色ID
            commit('setAccess', access) //菜单权限
            commit('setHasGetInfo', true)
            commit('setToken', res.data.oauth2AccessToken.access_token)
          }
          resolve(res)
        }).catch(err => {
          console.log(err)
          reject(err)
        })
        // 环境用

      })
    },
    //SSO单点登录
    handleSSOLogin({
      commit
    }, e) {
      return new Promise((resolve, reject) => {
        //调用登录接口
          ssoLoginGDS(e).then(res => {
            if (res.code === '0000') {
              var privList = res.data.userDetails.pagePrivileges
              var access = []
              var btnPrivs = []
              for (var n = 0; n < privList.length; n++) {
                access.push(privList[n].access) //菜单权限
                btnPrivs.push({ //按钮权限
                  url: privList[n].url,
                  priv: privList[n].buttons
                })
              }
              commit('setUserPriv', btnPrivs) //页面权限
              commit('setUserName', res.data.userDetails.username)
              commit('setIsUpdatePassword', res.data.userDetails.rePassword) //密码是否失效
        // commit('setIsUpdatePassword', '2')//自测
              commit('setUserId', res.data.userDetails.id)
              commit('setRoleId', res.data.userDetails.roleId) //角色ID
              // commit('setRoleId', '3') //角色ID
              commit('setAccess', access) //菜单权限
              commit('setHasGetInfo', true)
              commit('setToken', res.data.oauth2AccessToken.access_token)
            }
            resolve(res)
          }).catch(err => {
            console.log(err)
            reject(err)
          })
      })
    },
    // 退出登录
    handleLogOut({
      state,
      commit
    }) {
      return new Promise((resolve, reject) => {
        commit('setToken', '', -1)
        commit('setUserPriv', '', -1)
        commit('setUserId', '', -1)
        commit('setRoleId', '', -1)
        commit('setUserName', '')
        commit('setAccess', [])
        commit('setHasGetInfo', false)
        sessionStorage.clear()
		var navLang = localStorage.getItem('local')
        localStorage.clear()
		localStorage.setItem('local',navLang)
        resolve()
      })
    },
    // 获取用户相关信息
    getUserInfo({
      state,
      commit
    }) {
      return new Promise((resolve, reject) => {
        try {
          getUserInfo(state.token).then(res => {
            commit('setAvatar', res.avatar)
            commit('setUserName', res.name)
            commit('setUserId', res.user_id)
            commit('setAccess', res.access)
            commit('setHasGetInfo', true)
            resolve(res)
          }).catch(err => {
            reject(err)
          })
        } catch (error) {
          reject(error)
        }
      })
    }

  }
}
