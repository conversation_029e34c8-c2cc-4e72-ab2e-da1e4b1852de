<template>
	<Card style="width: 100%; padding: 16px">
		<div class="package-info">
			<div style="display: flex;">
				<div class="package-name" style="flex-shrink: 0;margin-right: 8px;">模板名称: </div>
				<div v-if="tableData.length" :title="tableData[0].templateName" class="package-name package-name-txt">{{ tableData.length ?
					tableData[0].templateName : "" }}</div>
			</div>

			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading">
			</Table>

		</div>

		<Tabs v-model="activeTab" @on-click="handleTabClick">
			<TabPane label="适用国家/地区" name="area">
				<Form ref="searchForm" :model="searchObj" inline>
					<Form-Item label="国家/地区" :label-width="70">
						<Select filterable v-model="searchObj.area" placeholder="请选择适用国家/地区" :clearable="true" class="inputSty">
							<Option v-for="item in areaList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
						</Select>
					</Form-Item>
					<Form-Item>
						<Button type="primary" @click="searchPackage" icon="md-search" :loading="searchAreaLoading">搜索</Button>
					</Form-Item>
				</Form>
				<Table :columns="areasColumns" :data="areaTableData" :loading="areaTableLoading">
				</Table>
				<Page style="margin-top: 20px;" :total="areaTotal" :page-size="areaPageSize" :current.sync="areaPage" show-total
					show-elevator @on-change="loadByPageArea" />
			</TabPane>
			<TabPane label="适用套餐" name="package">
				<div v-if="applyPackageType == 4">
					<Form ref="searchForm" :model="searchObj" inline>
						<Form-Item label="套餐ID/流量池ID" :label-width="120">
							<Input v-model.trim="searchObj.packageId" placeholder="请输入套餐ID" clearable style="width: 200px" />
						</Form-Item>
						<Form-Item label="套餐名称" :label-width="90">
							<Input v-model.trim="searchObj.packageName" placeholder="请输入套餐名称" clearable style="width: 200px" />
						</Form-Item>
            <Form-Item label="流量池名称" :label-width="90">
            	<Input v-model.trim="searchObj.flowPoolName" placeholder="请输入流量池名称" clearable style="width: 200px" />
            </Form-Item>
						<Form-Item>
							<Button type="primary" @click="searchPackage" icon="md-search" :loading="searchPackageLoading">搜索</Button>
						</Form-Item>
					</Form>
					<!-- 套餐表格 -->
					<Table :columns="packageColumns" :data="packageData" :loading="packageLoading" />
					<Page style="margin-top: 20px;" :total="packageTotal" :page-size="packageSize" :current.sync="packagePage"
						show-total show-elevator @on-change="loadByPagePackage" />
				</div>
				<div class="package-txt">
					<!-- applyPackageType:0,//适用套餐类型  1-全部套餐,2-全部cmi套餐,3-全部渠道自建套餐,4-自选套餐 -->
					{{ applyPackageType == 1 ? "全部套餐" : applyPackageType == 2 ? "全部CMI套餐" : applyPackageType == 3 ? "全部渠道自建套餐" :
						"" }}
				</div>

			</TabPane>
		</Tabs>
		<div class="footer-textarea" style="margin: 30px;">
			<Button @click="back" icon="md-arrow-back">返回</Button>
		</div>
	</Card>
</template>

<script>
import { getAreaWelcomeList, getAreaForTemplate, getPackagesForTemplate, getCountryList } from "@/api/sms/areaWelcome";

export default {
	data () {
		return {
			searchObj: {
				packageId: "",
				packageName: "",
        flowPoolName: "",
				area: ""
			},
			activeTab: 'area', // 当前激活的Tab
			tableData: [],
			tableLoading: false,
			templateName: "",
			columns: [
				{
					title: "模板内容（简中）",
					key: "contentCn",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
				{
					title: "模板内容（繁中）",
					key: "contentTw",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
				{
					title: "模板内容（英文）",
					key: "contentEn",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
			],
			areaTableData: [],
			areaTableLoading: false,
			areaTotal: 0,
			areaPageSize: 10,
			areaPage: 1,
			areaList: [],
			areasColumns: [
				{
					title: "国家/地区（简中）",
					key: "countryCn",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
				{
					title: "国家/地区（英文）",
					key: "countryEn",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
				{
					title: "所属大洲",
					key: "continentCn",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
			],
			packageData: [],
			packageLoading: false,
			packageTotal: 0,
			packageSize: 10,
			packagePage: 1,
			packageColumns: [
				{
					title: "套餐ID",
					key: "id",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
				{
					title: "套餐名称（简中）",
					key: "nameCn",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
				{
					title: "套餐名称（繁中）",
					key: "nameTw",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
				{
					title: "套餐名称（英文）",
					key: "nameEn",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
			],
			searchAreaLoading: false,
			searchPackageLoading: false,
			templateId: "",
			isSearching: false,
			applyPackageType: 0,//适用套餐类型  1-全部套餐,2-全部cmi套餐,3-全部渠道自建套餐,4-自选套餐
		};
	},
	methods: {
		init () {
			this.loadTemplates();
			if (this.activeTab === 'area') {
				this.areaPage = 1; // 重置页码
				this.initAreaList();
				this.loadAreaForTemplate();
			} else if (this.activeTab === 'package') {
				this.packagePage = 1; // 重置页码
				this.loadPackagesForTemplate();
			}
		},
		// 模板详情
		loadTemplates () {
			// 假设有一个接口 getTemplateList 可以获取模板数据
			let data = {
				id: this.templateId,
				current: -1,
				size: -1
			}
			getAreaWelcomeList(data).then(res => {
				if (res.code === "0000") {
					this.tableData = res.paging.data
					this.applyPackageType = res.paging.data[0].applyPackageType
				}
			}).catch(err => {
				console.error(err);
			}).finally(() => {
				this.tableLoading = false;
			});
		},
		initAreaList () {
			getAreaForTemplate({
				id: this.templateId,
				current: -1,
				size: -1
			}).then((res) => {
				if (res.code === "0000") {
					this.areaList = res.paging.data
					console.log(this.areaList, "areaList")
				}
			});
		},

		// 加载适用国家/地区数据
		loadAreaForTemplate () {
			if (this.isSearching) {
				console.log("拦截了")
				return
			}
			this.isSearching = true;
			this.areaTableLoading = true;
			getAreaForTemplate({
				id: this.templateId,
				mcc: this.searchObj.area,
				current: this.areaPage,
				size: this.areaPageSize
			}).then(res => {
				if (res.code === "0000") {
					this.areaTableData = res.paging.data
					this.areaTotal = res.paging.total
				}
			}).catch(err => {
				console.error(err);
			}).finally(() => {
				this.areaTableLoading = false;
				this.isSearching = false;
			});
		},

		// 加载适用套餐数据
		loadPackagesForTemplate () {
			this.packageLoading = true;
			getPackagesForTemplate({
				id: this.templateId,
				packageId: this.searchObj.packageId,
				packageName: this.searchObj.packageName,
        flowPoolName: this.searchObj.flowPoolName,
				current: this.packagePage,
				size: this.packageSize
			}).then(res => {
				if (res.code === "0000") {
					this.packageData = res.paging.data
					this.packageTotal = res.paging.total;
				}
			}).catch(err => {
				console.error(err);
			}).finally(() => {
				this.packageLoading = false;
			});
		},

		// Tab切换处理函数
		handleTabClick (name) {
			this.activeTab = name;
			if (name === 'area') {
				this.areaPage = 1; // 重置页码
				this.initAreaList();
				this.loadAreaForTemplate();
			} else if (name === 'package') {
				if (this.applyPackageType == '4') {
					this.packagePage = 1; // 重置页码
					this.loadPackagesForTemplate();
				}

			}
		},

		// 搜索查询方法
		searchPackage () {
			if (this.activeTab === 'area') {
				this.areaPage = 1; // 重置页码
				this.loadAreaForTemplate();
			} else if (this.activeTab === 'package') {
				this.packagePage = 1; // 重置页码
				this.loadPackagesForTemplate();
			}
		},

		// 分页处理函数
		loadByPageArea (page) {
			this.areaPage = page;
			this.loadAreaForTemplate();
		},

		loadByPagePackage (page) {
			this.packagePage = page;
			this.loadPackagesForTemplate();
		},
		back () {
			this.$router.push({
				name: "areaWelcomeSMSIndex",
			});
		}
	},
	mounted () {
		this.templateId = this.$route.query.id;
		this.init(); // 初始化函数，包括加载模板列表和套餐列表等
	},
};
</script>

<style>
/* 添加必要的样式 */
.package-info {
	margin-bottom: 40px;
}

.package-name {
	font-size: 30px;
	margin-bottom: 20px;
}

.package-txt {
	font-size: 20px;
	margin-left: 10px;
}

.package-name-txt {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.footer-textarea {
	display: flex;
	justify-content: center;
}
</style>
