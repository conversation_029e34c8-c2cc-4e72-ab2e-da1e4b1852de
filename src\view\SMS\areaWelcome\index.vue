<template>
	<Card style="width: 100%; padiing: 16px">
		<div class="ad-info" v-if="adInfo.length>0">
			{{ adInfo }}
		</div>
		<Form ref="searchForm" label-position="right" :model="searchObj" inline :rules="ruleEditValidate">
			<FormItem label="模板名称" :label-width="60">
				<Input v-model="searchObj.templateName" placeholder="请输入模板名称" clearable style="width: 200px" />
			</FormItem>
			<FormItem label="套餐ID" :label-width="65">
				<Input v-model="searchObj.packageId" placeholder="请输入套餐ID" clearable style="width: 200px" />
			</FormItem>
			<FormItem label="适用国家/地区" :label-width="100">
				<Select filterable v-model="searchObj.mcc"  placeholder="请选择适用国家/地区" :clearable="true" class="inputSty">
					<Option v-for="item in areaList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
				</Select>
			</FormItem>
			<FormItem>
				<Button style="margin: 0 2px" type="primary" @click="searchSMS" icon="ios-search" :loading="searchLoading" v-has="'searchSMS'">
					搜索
				</Button>
				<Button style="margin-left:20px" type="info" icon="ios-add" @click="SMSCommon(null, 'Add')" v-has="'addSMS'">
					新建
				</Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading">
				<template slot-scope="{ row }" slot="action">
					<Button type="primary" size="small" style="margin-right: 5px" @click="SMSDetails(row)" v-has="'SMSDeatils'">详情</Button>
					<Button type="success" size="small" style="margin-right: 5px" @click="SMSCommon(row, 'Update')" v-has="'editSMS'">编辑</Button>
					<Button type="error" size="small" @click="SMSDel(row.id)" v-has="'deleteSMS'">删除</Button>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="loadByPage"
				style="margin: 15px 0" />
		</div>

	</Card>
</template>

<script>
import {
	getAreaWelcomeList,
	delNotice,
	getIntroduce,
	getCountryList,
	delAreaWelcome
} from "@/api/sms/areaWelcome";
export default {
	components: {},
	data () {
		return {
			adInfo: "",
			searchObj: {
				packageId: "", //套餐ID
				templateName: "", //短信模板名称
				mcc: ""
			},
			areaList: [],
			ruleEditValidate: {
				SMSName: [{
					required: true,
					message: "请输入模板名称",
					trigger: "blur"
				},
				{
					type: "string",
					max: 255,
					message: "最多输入255个字",
					trigger: "blur",
				},
				],
			},
			tableData: [], //列表信息
			tableLoading: false,
			total: 0,
			pageSize: 10,
			page: 1,
			columns: [
				{
					title: "模板名称",
					key: "templateName",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
				{
					title: "模板内容（简中）",
					key: "contentCn",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
				{
					title: "模板内容（繁中）",
					key: "contentTw",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
				{
					title: "模板内容（英文）",
					key: "contentEn",
					align: "center",
					minWidth: 150,
					tooltip: true,
				},
				{
					title: "操作",
					slot: "action",
					minWidth: 220,
					maxWidth: 240,
					align: "center",
				},
			],
			operationType: "Add",
			insertLocal: 0, //记录光标位置
			statusPending: true,
			searchLoading: false,

		};
	},
	methods: {
		//表格初始化
		init () {
			//加载表格
			this.getIntroduce();
			this.loadByPage(0);
			//加载国家/地区下拉列表
			this.initAreaList();

		},
		getIntroduce(){
			getIntroduce().then((res) => {
				if (res.code === "0000") {
					this.adInfo = res.data
				}
			});
		},
		initAreaList () {
			getCountryList().then((res) => {
				if (res.code === "0000") {
					this.areaList = res.data
				}
			});
		},
		//表格数据加载
		loadByPage (e) {
			if (e === 0) {
				this.page = 1;
			}
			getAreaWelcomeList({
				current: this.page,
				size: this.pageSize,
				packageId: this.searchObj.packageId ? this.searchObj.packageId.trim() : '',
        templateName: this.searchObj.templateName ? this.searchObj.templateName.trim() : '',
				mcc: this.searchObj.mcc,
			})
				.then((res) => {
					if (res.code === "0000") {
						this.tableData = res.paging.data;
						this.total = res.paging.total;
					}
				})
				.catch((err) => {
					console.log(err);
				})
				.finally(() => {
					this.loading = false;
					this.searchLoading = false;
				});
		},
		//搜索
		searchSMS () {
			this.searchLoading = true;
			this.loadByPage(0);
		},
		//新增
    //编辑
		SMSCommon (row, type) {
      if (type == 'Add') {
        this.$router.push({
        	name: "areaWelcomeEditAdd",
          query: { type: type},
        });
      } else {
        this.$router.push({
        	name: "areaWelcomeEdit",
        	query: { id: row.id, type: type},
        });
      }
		},
		//详情
		SMSDetails (row) {
			let routeName = "areaWelcomeInfo";// 详情页面
			let queryParams = { id: row.id };

			this.$router.push({
				name: routeName,
				query: queryParams,
			});
		},

		//删除
		SMSDel (id) {
			this.$Modal.confirm({
				title: "确认删除？",
				onOk: () => {
					//id
					delAreaWelcome({id:id})
						.then((res) => {
							if (res.code === "0000") {
								this.$Notice.success({
									title: "操作提示",
									desc: "操作成功",
								});
								this.init();
							}
						})
						.catch((err) => {
							this.$Notice.error({
								title: "操作提示",
								desc: "操作失败",
							});
						});
				},
			});
		},
	},
	mounted () {
		this.init();
	},
};
</script>

<style>
.ad-info {
	font-size: 18px;
	margin: 15px 0 25px 0;
}
</style>
