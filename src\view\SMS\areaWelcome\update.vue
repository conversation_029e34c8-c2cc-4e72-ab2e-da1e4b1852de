<template>
  <!-- 新增/编辑 地区欢迎短信-->
  <Card style="width: 100%; padiing: 16px">
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80" style="padding: 0 5%;"
      @keydown.enter.native.prevent>
      <Row style="margin-top:30px; margin-bottom: 20px;">
        <Col span="24">
        <FormItem label="模板名称" prop="templateName">
          <Input v-model.trim="formValidate.templateName" placeholder="请输入模板名称" maxlength="255" clearable></Input>
        </FormItem>
        </Col>
      </Row>

      <Row style="margin-bottom: 20px;" :gutter="16">
        <Col v-for="(language, index) in languages" :key="index" :xs="24" :sm="24" :md="8" :lg="8">
        <FormItem :label="index === 0 ? '模板内容' : ''" :prop="`content${language.code}`"
          :rules="ruleValidate.templateRule">
          <h3 style="text-align: center;">{{ language.label }}</h3>
          <Input v-model.trim="formValidate[`content${language.code}`]" type="textarea" :rows="4"
            :placeholder="language.placeholder" style="min-width: 200px; word-break: break-all;" ref="textInputs"
            @focus="setInputFocus(index)" />
          <div style="text-align: left; margin-top: 10px;">
            <Button type="info" ghost size="small" @click="insertVariable(index, 'iccid')">
              ICCID
            </Button>
            <span style="margin-left: 20px;"></span>
            <Button type="info" ghost size="small" @click="insertVariable(index, 'position')">
              位置
            </Button>
          </div>
        </FormItem>
        </Col>
      </Row>

      <Tabs value="name1" style="margin-bottom: 50px;">
        <TabPane label="适用国家/地区" name="name1">
          <div class="custom-textarea">
            <Row class="options-container1">
              <Col span="24" v-for="(option1, index1) in countryOptions" :key="option1.mcc" class="option-item">
              <span>{{ option1.countryEn + '（' + option1.countryCn + '）' }}</span>
              <Button icon="md-close-circle" @click="removeCountry(index1)" class="remove-button"></Button>
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              </Col>
            </Row>
            <div class="button-container">
              <Button type="primary" ghost @click="addCountry">添加国家</Button>
              <Button type="success" ghost @click="batchAddCountry">批量添加</Button>
              <Button type="error" ghost @click="clearAllCountry">清空国家</Button>
            </div>
          </div>
        </TabPane>
        <TabPane label="适用套餐" name="name2">
          <div class="custom-textarea">
            <div v-if="spinShow" class="spinBox">
              <Spin size="large"></Spin>
            </div>
            <Scroll style="width: 100%;" :on-reach-edge="handleReachEdge">
              <div class="options-container" ref="optionsContainer" v-if="!spinShow">
                <div span="24" v-for="(option2, index2) in tempPackageOptions" :key="option2.id" class="option-item"
                  v-if="showPackageType == false">
                  <span>{{ option2.id + '（' + option2.nameCn + '）' }}</span>
                  <Button icon="md-close-circle" @click="removePackage(index2)" class="remove-button"></Button>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                </div>

                <div class="option-item" v-if="showPackageType == true">
                  <h3>{{ packageTypeShow }}</h3>
                </div>
              </div>
            </Scroll>
            <div class="button-container" v-if="!spinShow">
              <Button type="info" ghost @click="updatePackage">编辑</Button>
              <Button type="error" ghost @click="clearAllPackage" v-if="showPackageType == false">清空</Button>
            </div>
          </div>
        </TabPane>
      </Tabs>

      <footer class="footer-textarea">
        <Button type="primary" @click="sumbit" :loading="sumbitLoading" icon="md-checkmark">确定</Button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button @click="reback" icon="md-arrow-back">返回</Button>
      </footer>
    </Form>

    <!-- 添加国家 -->
    <Modal title="添加适用国家/地区" v-model="modal1" :footer-hide="true" :mask-closable="false" @on-cancel="closeModal1"
      width="500px">
      <Form ref="mccObj" :model="mccObj" :rules="mccValidate" style="padding: 0 5%;">
        <FormItem prop="country">
          <Select v-model="mccObj.country" multiple filterable style="margin-top: 20px;">
            <Option v-for="mcc in countryList" :value="mcc.mcc" :key="mcc.mcc">
              {{ mcc.countryEn + '（' + mcc.countryCn + '）'}}
            </Option>
          </Select>
        </FormItem>
      </Form>
      <div class="footer-textarea" style="margin: 30px;">
        <Button type="primary" @click="sumbitModal1" icon="md-checkmark">确定</Button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button @click="closeModal1" icon="md-arrow-back">返回</Button>
      </div>
    </Modal>

    <!-- 批量添加国家 -->
    <Modal title="批量添加适用国家/地区" v-model="modal2" :footer-hide="true" :mask-closable="false" @on-cancel="closeModal1"
      width="90%" :styles="{top: '0px'}">
      <div style="height: 750px; overflow: auto;">
        <Tabs type='card'>
          <TabPane v-for="(countries, continent) in continentsData" :key="continent" :label="continent">
            <Checkbox :indeterminate="isIndeterminate(continent)" :value="isAllSelected(continent)"
              @on-change="toggleSelectAll(continent)" style="margin: 30px 0;">
              <span style="color: brown;">全选{{continent}}</span>
            </Checkbox>

            <Row :gutter="16">
              <Col v-for="country in countries" :key="country.mcc" :xs="24" :sm="24" :md="12" :lg="8"
                style="margin-bottom: 20px;">
              <Checkbox :value="selectedCountriesMap[country.mcc]"
                @on-change="checked => toggleCountry(checked, country)">
                {{ country.countryEn }}（{{ country.countryCn }}）
              </Checkbox>
              </Col>
            </Row>
            <Row>
              <Col span="24" style="margin-top: 30px;">
              <p style="font-weight: bold; color: brown;">已勾选国家：</p>
              <ul class="selected-countries-list">
                <li v-for="country in selectedCountries" :key="country.mcc" class="selected-country">
                  {{ country.countryEn }}（{{ country.countryCn }}）
                </li>
              </ul>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </div>
      <div class="footer-textarea" style="margin: 30px;">
        <Button type="primary" @click="sumbitModal2" icon="md-checkmark">确定</Button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button @click="closeModal1" icon="md-arrow-back">返回</Button>
      </div>
    </Modal>

    <!-- 适用套餐 -->
    <Modal title="适用套餐" v-model="modal3" :footer-hide="true" :mask-closable="false" @on-cancel="closeModal1"
      width="1100px">
      <Form ref="packageObj" :model="packageObj" :rules="packageValidate" style="padding: 20px">
        <FormItem prop="applyPackageType">
          <RadioGroup v-model="packageObj.applyPackageType">
            <Radio label="1">
              <span>全部套餐</span>
            </Radio>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <Radio label="2">
              <span>全部CMI套餐</span>
            </Radio>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <Radio label="3">
              <span>全部渠道自建套餐</span>
            </Radio>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <Radio label="4">
              <span>自选套餐</span>
            </Radio>
          </RadioGroup>
        </FormItem>
        <div style="display: flex; justify-content: flex-start; flex-wrap: wrap; align-items: center;"
          v-if="packageObj.applyPackageType == '4'">
          <FormItem prop="packageName">
            <Input v-model.trim="packageObj.packageName" placeholder="请输入套餐名称" clearable />
          </FormItem>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <FormItem prop="packageId">
            <Input v-model.trim="packageObj.packageId" placeholder="请输入套餐ID" clearable />
          </FormItem>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <FormItem prop="mcc">
            <Select v-model="packageObj.mcc" placeholder="请选择套餐覆盖国家" filterable clearable>
              <Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
            </Select>
          </FormItem>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <FormItem prop="corpId">
            <Select v-model="packageObj.corpId" placeholder="请选择渠道商" filterable clearable>
              <Option :value="item.corpId" v-for="(item,index) in corpList" :key="index">{{item.corpName}}</Option>
            </Select>
          </FormItem>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <FormItem>
            <Button style="margin: 0 2px" type="info" @click="searchPackage" :loading="searchExoprtLoading"
              icon="ios-search">搜索</Button>
          </FormItem>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <FormItem>
            <Button style="margin: 0 2px" type="warning" @click="exportPackage" icon="md-arrow-round-up">导入</Button>
          </FormItem>
        </div>
      </Form>
      <div style="margin: 30px 20px" v-if="packageObj.applyPackageType == '4'">
        <Checkbox border v-model="selectAll" @on-change="handleAllChange" :disabled="data.length == 0">全选</Checkbox>
        <Table border ref="selection" :columns="columns" :data="data" style="width: 800px;"
          @on-selection-change="handleRowChange" @on-select-cancel="cancelPackage"
          @on-select-all-cancel="cancelPackageAll" :loading="tableLoading">
        </Table>
        <Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="loadByPage"
          style="margin: 15px 0" />
      </div>
      <div class="footer-textarea" style="margin: 30px;">
        <Button type="primary" @click="sumbitModal3" icon="md-checkmark">确定</Button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button @click="closeModal1" icon="md-arrow-back">返回</Button>
      </div>
    </Modal>

    <!-- 导入适用套餐文件 -->
    <Modal v-model="modal4" title="导入文件" :footer-hide="true" :mask-closable="false" width="600px"
      @on-cancel="closeModal4">
      <div>
        <Form ref="formobj" :model="formobj" :rules="ruleobj" style="font-weight:bold;">
          <FormItem label="上传文件" prop="file" style="font-size: 14px;font-weight: bold;">
            <Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/" :action="uploadUrl"
              :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
              :on-progress="fileUploading">
              <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>点击或拖拽文件上传</p>
              </div>
            </Upload>
            <ul class="ivu-upload-list" v-if="file" style="width: 100%;">
              <li class="ivu-upload-list-file ivu-upload-list-file-finish">
                <span>
                  <Icon type="ios-folder" />{{file.name}}
                </span>
                <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" @click="removeFile"></i>
              </li>
            </ul>
            <div style="width: 500px; padding-left: 70px;">
              <Button type="primary" ghost icon="ios-download" :loading="downLoading"
                @click="downloadFile">模板下载</Button>
              <Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
              <a ref="downloadLink" style="display: none"></a>
            </div>
          </FormItem>
        </Form>
        <div class="footer-textarea" style="margin: 30px;">
          <Button type="primary" :loading="uploadLoading" @click="sumbitModal4" icon="md-checkmark">确定</Button>
          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <Button @click="closeModal4" icon="md-arrow-back">返回</Button>
        </div>
      </div>
    </Modal>

    <!-- 适用套餐 -->
    <Modal title="导入失败数据" v-model="modal5" :footer-hide="true" :mask-closable="false" @on-cancel="closeModal5"
      width="900px" :styles="{top: '0px'}">
      <div style="height: 600px;overflow-y: auto;">
        <div style="margin: 30px 20px;">
          <h3 style="color: #3399ff;">不可用的套餐： {{unAvailableDataTip}}</h3>
          <Table border ref="selection" :columns="unAvailableColumns" :data="unAvailableData" style="width: 800px;">
          </Table>
        </div>
        <div style="margin: 30px 20px;">
          <h3 style="color: #3399ff;">未找到的套餐</h3>
          <div style="display: flex; justify-content: flex-start;">套餐ID：{{notFoundTip}}</div>
          <div style="display: flex; flex-wrap: wrap;">{{notFoundData}}</div>
        </div>
        <div class="footer-textarea" style="margin: 30px;">
          <Button @click="closeModal5" icon="md-arrow-back">返回</Button>
        </div>
      </div>
    </Modal>
  </Card>
</template>

<script>
  import {
    getCountryList
  } from "@/api/customer/cooperative.js";
  import {
    getCorpList
  } from '@/api/product/package/batch';
  import {
    getContinentMcc,
    getPackageList,
    uploadPackageFile,
    addWelcome,
    editWelcome,
    getAreaWelcomeList,
    getAreaForTemplate,
    getPackagesForTemplate,
    getPackageIdDwonload,
    getAllPackagesForTemplate
  } from '@/api/sms/areaWelcome';
  export default {
    data() {
      // 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
      const validateUpload = (rule, value, callback) => {
        if (!this.file) {
          callback(new Error("请上传文件"))
        } else {
          callback()
        }
      }
      // 自定义验证 适用套餐搜素条件至少一项
      const validateSearch = (rule, value, callback) => {
        const {
          applyPackageType,
          packageName,
          packageId,
          mcc,
          corpId
        } = this.packageObj;

        // 检查除了 applyPackageType 之外的属性中至少有一个不为空
        const hasValidSearchCondition = [packageName, packageId, mcc, corpId].some(field => field !== "" && field !==
          undefined);

        if (hasValidSearchCondition) {
          callback(); // 验证通过
        } else {
          callback(new Error("至少一项搜索条件不能为空")); // 验证失败，报错
        }
      };
      // 自定义验证 适用套餐搜素条件至少一项
      const validateTemplate = (rule, value, callback) => {
        const {
          contentCn,
          contentEn,
          contentTw,
        } = this.formValidate;

        const hasValidContent = [contentCn, contentEn, contentTw].some(content => {
          const trimmedContent = content ? content.trim() : '';
          return trimmedContent !== "";
        });

        if (hasValidContent) {
          callback(); // 验证通过
        } else {
          callback(new Error("至少填写一项模板内容")); // 验证失败，报错
        }
      };
      return {
        formValidate: {
          templateName: "",
          contentCn: "",
          contentEn: "",
          contentTw: ""
        },
        mccObj: {
          country: "",
        },
        packageObj: {
          applyPackageType: "",
          packageName: "",
          packageId: "",
          mcc: "",
          corpId: "",
        },
        formobj: {
          file: '',
        },
        message: "最大限制范围10MB",
        pageType: '',
        templateId: "",
        focusedInputIndex: null, // 用于存储当前聚焦的输入框索引
        file: null,
        uploadUrl: '', //上传地址
        uploadList: [],
        country: [], //下拉框选中的国家
        countryOptions: [], //适用国家列表
        applyPackageTypeCopy: '', // 存放 勾选的套餐类型->回显用
        selectionListCopy: [], // 存放 勾选的适用套餐->回显用
        showPackageType: false,
        packageTypeShow: false,
        unAvailableDataTip: '',
        notFoundTip: '',
        packageOptions: [], // 适用套餐列表
        tempPackageOptions: [], // 适用套餐列表
        continentList: [], //套餐覆盖国家列表
        selection: [], //多选
        selectionList: [], //翻页勾选List
        selectAll: false,
        countryList: [], // 国家/地区下拉框数据源
        continentsData: {}, // 接口返回的大洲数据
        selectedCountriesMap: {}, // 存储选中国家 {mcc: countryObject}
        selectedCountries: [], // 用于存储被选中的国家对象
        corpList: [],
        total: 0,
        pageSize: 10,
        page: 1,
        languages: [{
            label: '简体中文',
            code: 'Cn',
            placeholder: '请输入模板内容(简体中文)'
          },
          {
            label: '繁体中文',
            code: 'Tw',
            placeholder: '請輸入模板內容（繁體中文）'
          },
          {
            label: '英文',
            code: 'En',
            placeholder: 'Please input template content.'
          }
        ],
        iccid: 'iccid',
        position: 'position',
        columns: [{
          type: "selection",
          minWidth: 60,
          align: "center"
        }, {
          title: "套餐名称",
          key: "nameCn",
          minWidth: 150,
          align: "center",
          tooltip: true
        }, {
          title: "套餐ID",
          key: "id",
          minWidth: 150,
          align: "center",
          tooltip: true
        }, ],
        unAvailableColumns: [{
          title: "套餐ID",
          key: "id",
          minWidth: 150,
          align: "center",
          tooltip: true
        }, {
          title: "套餐名称",
          key: "nameCn",
          minWidth: 150,
          align: "center",
          tooltip: true
        }, ],
        data: [],
        unAvailableData: [],
        notFoundData: [],
        modal1: false,
        modal2: false,
        modal3: false,
        modal4: false,
        modal5: false,
        tableLoading: false,
        uploadLoading: false,
        sumbitLoading: false,
        searchExoprtLoading: false,
        downLoading: false,
        spinShow: false,
        ruleValidate: {
          templateName: [{
            required: true,
            validator: (rule, value, callback) => {
              const trimmedValue = value ? value.trim() : '';

              if (trimmedValue === '') {
                callback(new Error("模板名称不能为空或仅包含空格"));
              } else {
                callback(); // 验证通过
              }
            },
          }, ],
          templateRule: [{
            required: true,
            validator: validateTemplate,
          }]
        },
        mccValidate: {
          country: [{
            required: true,
            message: "请选择适用国家/地区",
          }, ],
        },
        packageValidate: {
          applyPackageType: [{
            required: true,
            message: '请选择',
          }],
          packageName: [{
            validator: validateSearch,
          }, ],
          packageId: [{
            validator: validateSearch,
          }, ],
          mcc: [{
            validator: validateSearch,
          }, ],
          corpId: [{
            validator: validateSearch,
          }, ],

        },
        ruleobj: {
          file: [{
            required: true,
            validator: validateUpload,
            trigger: 'change',
          }],
        },
        countOfRender: 0
      }
    },
    mounted() {
      this.templateId = this.$route.query.id
      this.pageType = this.$route.query.type == 'Add' ? '1' : '2'
      if (this.pageType == '2') {
        this.loadTemplates();
        this.initAreaList();
      }
      this.getCountryList()
      this.getCorpList()
      this.getContinentMcc()

    },
    methods: {
      sumbit() {
        this.$refs["formValidate"].validate((valid) => {
          if (valid) {
            if (this.countryOptions.length == 0) {
              this.$Message['warning']({
                background: true,
                content: '请添加适用国家/地区！',
                duration: 3
              });
              return
            }
            if ((this.applyPackageTypeCopy == '4' && Object.keys(this.packageOptions).length === 0) || !this
              .applyPackageTypeCopy) {
              this.$Message['warning']({
                background: true,
                content: '请添加适用套餐！',
                duration: 3
              });
              return
            }
            this.sumbitLoading = true
            const {
              contentCn,
              contentEn,
              contentTw,
              templateName
            } = this.formValidate;

            const formData = new FormData();
            if (this.applyPackageTypeCopy == '4') {
              const packageIdsWithChannelCreate = this.packageOptions.reduce((acc, pack) => {
                acc[pack.id] = pack.isChannelCreate;
                return acc;
              }, {});
              console.log(packageIdsWithChannelCreate,"适用套餐-提交")
              const packageJsonString = JSON.stringify(packageIdsWithChannelCreate);
              const blob = new Blob([packageJsonString], {
                type: 'application/json'
              });
              const reader = new FileReader();
              reader.onload = function(event) {
                console.log(event.target.result); // 文件的文本内容， JSON 字符串
              };
              reader.readAsText(blob);

              formData.append('packageMapFile', blob);
            }
            if (this.pageType == '2') {
              formData.append('id', this.templateId.toString());
            }
            formData.append('contentCn', contentCn);
            formData.append('contentEn', contentEn);
            formData.append('contentTw', contentTw);
            formData.append('templateName', templateName);
            formData.append('applyPackageType', this.applyPackageTypeCopy);
            formData.append('mccList', this.countryOptions.map(country => country.mcc));

            let func = this.pageType == '1' ? addWelcome : editWelcome
            func(formData).then(res => {
              if (res && res.code == '0000') {
                setTimeout(() => {
                  this.$Notice.success({
                    title: "操作提醒：",
                    desc: res.msg
                  });
                  this.$router.push({
                    name: "areaWelcomeSMSIndex",
                  });
                }, 1500);
              } else {
                throw res
              }
            }).catch((err) => {}).finally(() => {
              this.sumbitLoading = false;
            })
          }
        })
      },
      reback() {
        this.$router.push({
          name: "areaWelcomeSMSIndex",
        });
      },

      /*---------------- 模板内容 ----------------*/

      setInputFocus(index) {
        this.focusedInputIndex = index; // 更新当前聚焦的输入框索引
      },
      insertVariable(index, type) {
        const contentKey = `content${this.languages[index].code}`;
        const currentValue = this.formValidate[contentKey];
        const inputElement = this.$refs.textInputs[index].$el.querySelector('textarea');
        const cursorPosition = inputElement.selectionStart; // 获取当前光标位置

        // 在光标位置插入变量，只使用{}括号
        const newValue = currentValue.slice(0, cursorPosition) + `{${type}}` + currentValue.slice(cursorPosition);
        this.$set(this.formValidate, contentKey, newValue); // 更新v-model绑定的数据

        // 使用$nextTick确保DOM已更新
        this.$nextTick(() => {
          // 插入了文本，光标位置需要相应调整
          const newCursorPosition = cursorPosition + `{${type}}`.length + 1;
          // 检查新光标位置是否超出字符串长度，如果是，则设置为字符串末尾
          const maxLength = newValue.length;
          const finalCursorPosition = Math.min(newCursorPosition, maxLength);

          // 设置新的光标位置
          inputElement.focus(); // 重新聚焦输入框
          inputElement.setSelectionRange(finalCursorPosition, finalCursorPosition); // 设置光标位置
        });
      },

      /*---------------- 适用国家 ----------------*/

      // 清空单个国家
      removeCountry(index) {
        this.countryOptions.splice(index, 1)
      },
      // 清空全部国家
      clearAllCountry() {
        if (this.countryOptions.length != 0) {
          this.$Modal.confirm({
            title: "确认清空所有国家？",
            onOk: () => {
              this.countryOptions = [];
            },
          });
        }
      },
      //关闭添加国家弹窗
      closeModal1() {
        // 添加国家弹窗
        this.country = []
        this.$refs["mccObj"].resetFields()
        this.modal1 = false
        // 批量添加适用国家/地区弹窗
        this.modal2 = false
        this.$refs["packageObj"].resetFields()
        // 适用套餐弹窗
        this.selectAll = false
        this.modal3 = false
        this.selectionList = []
        this.data = []
        this.total = 0
        this.page = 1
        this.packageObj.packageName = ""
        this.packageObj.packageId = ""
        this.packageObj.mcc = ""
        this.packageObj.corpId = ""
      },
      closeModal4() {
        this.modal4 = false
      },
      // 添加国家弹窗
      addCountry() {
        this.mccObj.country = this.countryOptions.map(option => option.mcc);
        this.modal1 = true;
      },
      //提交添加国家
      sumbitModal1() {
        this.$refs["mccObj"].validate((valid) => {
          if (valid) {
            this.countryOptions = []
            // 存放根据mcc找到的国家对象
            const selectedCountries = this.mccObj.country.map(mcc => {
              return this.countryList.find(c => c.mcc === mcc);
            }).filter(country => country !== undefined);

            // 直接替换this.countryOptions
            this.countryOptions = selectedCountries;
            // 关闭弹窗
            this.modal1 = false;
          } else {
            return false;
          }
        })
      },
      // 批量添加
      batchAddCountry() {
        // 清空 selectedCountries 和 selectedCountriesMap
        this.selectedCountries = []
        this.selectedCountriesMap = {}

        // 遍历 countryOptions，设置选中状态并添加到 selectedCountries
        this.countryOptions.forEach(country => {
          this.$set(this.selectedCountriesMap, country.mcc, true); // 标记为选中
          if (!this.selectedCountries.some(c => c.mcc === country.mcc)) {
            this.selectedCountries.push({
              ...country
            });
          }
        });

        this.modal2 = true;
      },
      //回显批量添加国家
      isCountryPreselected(country) {
        return true; // 根据条件返回 true
      },
      toggleCountry(checked, country) {
        this.$set(this.selectedCountriesMap, country.mcc, checked);
        if (checked) {
          this.selectedCountries.push(country);
        } else {
          const index = this.selectedCountries.findIndex(c => c.mcc === country.mcc);
          if (index !== -1) {
            this.selectedCountries.splice(index, 1);
          }
        }
      },
      toggleSelectAll(continent) {
        const countries = this.continentsData[continent];
        const allSelected = this.isAllSelected(continent);
        const newSelection = !allSelected;

        // 重置 selectedCountries 和 selectedCountriesMap 中的相关状态
        this.selectedCountries = this.selectedCountries.filter(country => !countries.some(c => c.mcc === country.mcc));
        countries.forEach(country => {
          this.$set(this.selectedCountriesMap, country.mcc, false);
        });

        if (newSelection) {
          countries.forEach(country => {
            this.$set(this.selectedCountriesMap, country.mcc, true);
            this.selectedCountries.push({
              ...country
            });
          });
        }
      },
      isAllSelected(continent) {
        const countries = this.continentsData[continent];
        return countries.every(country => this.selectedCountriesMap[country.mcc] === true);
      },
      // 判断是否为部分选中状态
      isIndeterminate(continent) {
        const countries = this.continentsData[continent] || [];
        const selectedCount = countries.filter(c => this.selectedCountriesMap[c.mcc]).length;
        return selectedCount > 0 && selectedCount < countries.length;
      },
      sumbitModal2() {
        if (this.selectedCountries.length === 0) {
          return this.$Message.warning('请至少选择一个国家');
        }
        this.countryOptions = JSON.parse(JSON.stringify(this.selectedCountries));
        this.modal2 = false;
      },

      /*---------------- 适用套餐 ----------------*/

      // 清空单个套餐
      removePackage(index) {
        this.packageOptions.splice(index, 1);
        this.tempPackageOptions.splice(index, 1);
      },
      // 清空全部套餐
      clearAllPackage() {
        if (this.packageOptions.length != 0) {
          this.$Modal.confirm({
            title: "确认清空所有套餐？",
            onOk: () => {
              this.packageOptions = [];
              this.tempPackageOptions = [];
            },
          });
        }
      },
      // 编辑套餐
      updatePackage() {
        this.packageObj.applyPackageType = this.applyPackageTypeCopy
        if (this.packageObj.applyPackageType == '4') {
          this.selectionList = JSON.parse(JSON.stringify(this.packageOptions))

          //回显
          this.selectionList.forEach(item => {
            this.data.forEach(element => {
              if (element.id == item.id) {
                this.$set(element, '_checked', true)
              }
            })
          })
        }
        this.modal3 = true
      },
      // 多选
      handleRowChange(selection) {
        this.selection = selection;
        selection.map((value, index) => {
          let flag = true;
          this.selectionList.map((item, index) => {
            if (value.id === item.id) {
              flag = false
            }
          });
          //判断重复
          if (flag) {
            this.selectionList.push(value);
          }
        });
      },
      // 取消选择套餐
      cancelPackage(selection, row) {
        this.selectionList.forEach((value, index) => {
          if (value.id === row.id) {
            this.selectionList.splice(index, 1);
          }
        })
        this.selectAll = false
      },
      // 清空当前页勾选的适用套餐
      cancelPackageAll(selection, row) {
        // 清空与当前页相关的 selectionList 部分
        this.selectionList = this.selectionList.filter(item => !this.data.some(row => row.id === item.id));

        // 更新当前页每行的选中状态为未选中
        this.data.forEach(element => {
          this.$set(element, '_checked', false);
        });

        this.selectAll = false;
      },
      // 全选逻辑
      handleAllChange(value) {
        if (value) {
          // 全选
          this.tableLoading = true
          this.$refs["packageObj"].validate((valid) => {
            if (valid) {
              let vueInstance = this; // 保存对Vue实例的引用
              getPackageList({
                packageNameCn: this.packageObj.packageName,
                packageId: this.packageObj.packageId,
                mcc: this.packageObj.mcc,
                corpId: this.packageObj.corpId,
                page: -1,
                pageSize: -1,
              }).then(res => {
                let content;

                if (res.data instanceof Blob) {
                  const reader = new FileReader();
                  reader.onload = function(event) {
                    const text = event.target.result;
                    try {
                      const parsedData = JSON.parse(text);

                      // 确保这里的逻辑在解析完成后执行
                      vueInstance.handleParsedData3(parsedData); // 将处理逻辑封装在一个方法中

                    } catch (e) {
                      console.error('Failed to parse JSON:', e);
                    }
                  };
                  reader.readAsText(res.data); // 正确传递res.data

                } else if (res.data instanceof ArrayBuffer) {
                  // 处理ArrayBuffer的逻辑（如果需要的话）
                  const blob = new Blob([new Uint8Array(res.data)], {
                    type: 'text/plain'
                  });
                } else {}
                this.tableLoading = false
              })
            }
          })
        } else {
          // 取消全选
          this.selectionList = []
          this.data.forEach(element => {
            this.$set(element, '_checked', false)
          })
        }
      },
      handleParsedData3(parsedData) {
        const selectionListCopy = JSON.parse(JSON.stringify(this.selectionList))
        // 合并后的数组
        this.selectionList = this.mergeAndDeduplicate(parsedData.records, selectionListCopy, 'id');

        // 输出合并后的数组
        this.data.forEach(element => {
          this.$set(element, '_checked', true)
        })
      },
      // 全选和已选的数据去重
      mergeAndDeduplicate(array1, array2, idKey) {
        const result = [];
        const lookup = {};
        // 去重
        function addUniqueObjects(array) {
          array.forEach(item => {
            if (!lookup[item[idKey]]) {
              lookup[item[idKey]] = true;
              result.push(item);
            }
          });
        }
        addUniqueObjects(array1);
        addUniqueObjects(array2);

        return result;
      },
      // 获取适用套餐列表接口
      getPackageList(page) {
        let vueInstance = this; // 保存对Vue实例的引用
        this.tableLoading = true
        getPackageList({
          packageNameCn: this.packageObj.packageName,
          packageId: this.packageObj.packageId,
          mcc: this.packageObj.mcc,
          corpId: this.packageObj.corpId,
          page: page,
          pageSize: 10,
        }).then(res => {
          if (res.data instanceof Blob) {
            const reader = new FileReader();
            reader.onload = function(event) {
              const text = event.target.result;
              try {
                const parsedData = JSON.parse(text);

                vueInstance.handleParsedData2(page, parsedData);

              } catch (e) {
                console.error('Failed to parse JSON:', e);
              }
            };
            reader.readAsText(res.data);

          } else if (res.data instanceof ArrayBuffer) {
            const blob = new Blob([new Uint8Array(res.data)], {
              type: 'text/plain'
            });
          } else {}

        }).catch((err) => {

        }).finally(() => {
          vueInstance.tableLoading = false;
          vueInstance.searchExoprtLoading = false;
        });
      },
      handleParsedData2(page, parsedData) {
        var data = parsedData.records
        var List = []
        data.map((value, index) => {
          List.push(value)
        })
        //回显
        this.selectionList.forEach(item => {
          parsedData.records.forEach(element => {
            if (element.id == item.id) {
              this.$set(element, '_checked', true)
            }
          })
        })
        this.page = page
        this.total = Number(parsedData.total)
        this.data = List;
        // 重置全选按钮状态
        if (this.selectionList.length == this.total) {
          this.selectAll = true
        } else {
          this.selectAll = false
        }
      },
      // 分页跳转
      loadByPage(page) {
        this.$refs["packageObj"].validate((valid) => {
          if (valid) {
            this.getPackageList(page)
          }
        })
      },
      // 搜素条件
      searchPackage() {
        this.$refs["packageObj"].validate((valid) => {
          if (valid) {
            this.searchExoprtLoading = true
            this.getPackageList(1)
          }
        })
      },
      // 导入
      exportPackage() {
        this.modal4 = true
      },
      // 上传文件
      handleBeforeUpload(file, fileList) {
        const sizeLimit = file.size / 1024 / 1024 > 10
        if (sizeLimit) {
          this.$Notice.warning({
            title: "操作提示",
            desc: "超过了最大限制范围10MB"
          });
          return false;
        }
        this.file = file,
          this.uploadList = fileList
        return false;
      },
      fileUploading(event, file, fileList) {
        this.message = "文件上传中、待进度条消失后再操作"
      },
      fileSuccess(response, file, fileList) {
        this.message = '请先下载模板文件，并按格式填写后上传'
      },
      handleError(res, file) {
        var v = this
        setTimeout(function() {
          v.uploading = false;
          v.$Notice.warning({
            title: '错误提示',
            desc: "上传失败！"
          });
        }, 3000)
      },
      removeFile() {
        this.file = ''
      },
      downloadFile() {
        this.downLoading = true
        getPackageIdDwonload().then(res => {
          const content = res.data
          let fileName = "地区欢迎短信适用套餐导入模板.xlsx"
          if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
            const link = this.$refs.downloadLink // 创建a标签
            let url = URL.createObjectURL(content)
            link.download = fileName
            link.href = url
            link.click() // 执行下载
            URL.revokeObjectURL(url) // 释放url
          } else { // 其他浏览器
            navigator.msSaveBlob(content, fileName)
          }
        }).catch(err => {
          console.error(err);
        }).finally(() => {
          this.downLoading = false;
        });
      },
      // 列表勾选-适用套餐
      sumbitModal3() {
        // 首先检查 applyPackageType 是否有值
        if (!this.packageObj.applyPackageType) {
          // 如果没有值，则进行表单校验
          this.$refs["packageObj"].validate(valid => {
            if (valid) {}
          });
        } else {
          // 如果有值，则进一步检查是否为 自选套餐
          const applyPackageType = this.packageObj.applyPackageType;
          if (applyPackageType === '4') {
            // 如果为 自选套餐，则检查 selectionList 的长度
            const len = this.selectionList.length;
            if (len < 1) {
              // 如果没有选择任何项，则给出警告
              this.$Message.warning("请至少选择一条自选套餐");
            } else {
              this.packageOptions = JSON.parse(JSON.stringify(this.selectionList));
              this.loop(true) //懒加载

              this.applyPackageTypeCopy = this.packageObj.applyPackageType
              // 关闭模态框
              this.showPackageType = false
              this.closeModal1()
            }
          } else {
            // 如果不是 '4'，则进行表单校验
            this.$refs["packageObj"].validate(valid => {
              if (valid) {
                this.packageTypeShow = this.packageObj.applyPackageType == '1' ? "全部套餐" :
                  this.packageObj.applyPackageType == '2' ? "全部CMI套餐" :
                  this.packageObj.applyPackageType == '3' ? "全部渠道自建套餐" : ''
                this.applyPackageTypeCopy = this.packageObj.applyPackageType
                this.showPackageType = true
                this.closeModal1()
              }
            });
          }
        }
      },
      // 导入文件-适用套餐
      sumbitModal4() {
        this.$refs["formobj"].validate((valid) => {
          if (valid) {
            var formData = new FormData();
            formData.append('file', this.file);
            this.uploadLoading = true;
            let vueInstance = this;
            uploadPackageFile(formData).then((res) => {

              if (res.data instanceof Blob) {
                const reader = new FileReader();
                reader.onload = function(event) {
                  const text = event.target.result;
                  try {
                    const parsedData = JSON.parse(text);

                    vueInstance.handleParsedData(parsedData);

                  } catch (e) {
                    console.error('Failed to parse JSON:', e);
                  }
                };
                reader.readAsText(res.data); // 正确传递res.data

              } else if (res.data instanceof ArrayBuffer) {
                // 处理ArrayBuffer的逻辑（如果需要的话）
                const blob = new Blob([new Uint8Array(res.data)], {
                  type: 'text/plain'
                });
              } else {}
            }).catch((err) => {
              console.error('Upload failed:', err);
            }).finally(() => {
              vueInstance.uploadLoading = false;
            });
          }
        });
      },
      handleParsedData(data) {
        // 解构赋值解析后的数据
        const {
          unAvailablePackages,
          notFoundPackages,
          availablePackages
        } = data;

        // 初始化变量
        this.unAvailableData = [];
        this.notFoundData = '';
        this.packageOptions = [...this.packageOptions];
        this.unAvailableDataTip = '';
        this.notFoundTip = '';

        // 处理不可用包
        if (Array.isArray(unAvailablePackages)) {
          if (unAvailablePackages.length > 50) {
            this.unAvailableData = unAvailablePackages.slice(0, 50);
            this.unAvailableDataTip = '数据量过大，仅截取并展示前50条记录';
          } else {
            this.unAvailableData = unAvailablePackages;
          }
        }

        // 处理未找到套餐
        if (Array.isArray(notFoundPackages)) {
          const notFoundDataArray = notFoundPackages.length > 50 ? notFoundPackages.slice(0, 50) : notFoundPackages;
          this.notFoundData = notFoundDataArray.join(', ');
          if (notFoundPackages.length > 50) {
            this.notFoundTip = '数据量过大，仅截取并展示前50条记录';
          }
        }
        const existingIds = new Set(this.packageOptions.map(pkg => pkg.id));
        const newPackages = availablePackages.filter(pkg => !existingIds.has(pkg.id));
        this.packageOptions = [...this.packageOptions, ...newPackages];
        this.applyPackageTypeCopy = this.packageObj.applyPackageType;
        this.showPackageType = this.applyPackageTypeCopy === '4' ? false : true

        // 根据结果决定是否关闭弹窗
        const hasErrors = unAvailablePackages.length > 0 || notFoundPackages.length > 0;
        if (!hasErrors) {
          this.closeModal1();
          this.modal4 = false;
          this.loop(true)
        } else {
          this.modal5 = true;
        }

        // 重置文件变量（假设这是必要的）
        this.file = '';
      },
      closeModal5() {
        this.modal3 = false
        this.modal4 = false
        this.modal5 = false
        this.loop()
      },

      /*---------------- 前置数据 ----------------*/

      // 模板详情
      loadTemplates() {
        let data = {
          id: this.templateId,
          current: -1,
          size: -1
        }
        getAreaWelcomeList(data).then(res => {
          if (res.code === "0000") {
            let applyPackageType = res.paging.data[0].applyPackageType
            if (applyPackageType == '4') {
              this.showPackageType = false
              this.loadPackagesForTemplate();
            } else {
              this.showPackageType = true
              this.packageTypeShow = applyPackageType == '1' ? "全部套餐" :
                applyPackageType == '2' ? "全部CMI套餐" :
                applyPackageType == '3' ? "全部渠道自建套餐" : ''
            }
            this.applyPackageTypeCopy = res.paging.data[0].applyPackageType
            this.formValidate.templateName = res.paging.data[0].templateName
            this.formValidate.contentCn = res.paging.data[0].contentCn;
            this.formValidate.contentEn = res.paging.data[0].contentEn;
            this.formValidate.contentTw = res.paging.data[0].contentTw;
          }
        }).catch(err => {
          console.error(err);
        }).finally(() => {});
      },
      // 加载适用国家/地区
      initAreaList() {
        getAreaForTemplate({
          id: this.templateId,
          current: -1,
          size: -1
        }).then((res) => {
          if (res.code === "0000") {
            this.countryOptions = res.paging.data
          }
        });
      },
      // 加载适用套餐数据
      loadPackagesForTemplate() {
        let that = this
        this.spinShow = true;
        getAllPackagesForTemplate({
          id: this.templateId,
        }).then(res => {
          let content;
          if (res.data instanceof Blob) {
            const reader = new FileReader();
            reader.onload = function(event) {

              const text = event.target.result;
              try {
                that.packageOptions = JSON.parse(text);
                console.log(text,"text")
                console.log(that.packageOptions,"适用套餐-回显")
                that.loop()
              } catch (e) {
                console.error('Failed to parse JSON:', e);
              }
            };
            reader.readAsText(res.data);

          } else if (res.data instanceof ArrayBuffer) {
            // 处理ArrayBuffer的逻辑（如果需要的话）
            const blob = new Blob([new Uint8Array(res.data)], {
              type: 'text/plain'
            });
          } else {}
          this.spinShow = false;
        }).catch(err => {
          this.spinShow = false;
          console.error(err);
        }).finally(() => {});
      },
      loop(refresh) {
        let that = this;
        if (refresh) {
          that.countOfRender = 0
        }
        that.tempPackageOptions = []
        // 计算还可以加载多少个元素（可能是少于100个的）
        const remainingElements = that.packageOptions.length - that.countOfRender;
        const elementsToLoad = Math.min(100, remainingElements);
        // 如果没有元素需要加载，则退出函数
        if (elementsToLoad <= 0) {
          that.tempPackageOptions = that.packageOptions
          return;
        }
        // 创建一个新数组来存储这一批要加载的元素
        const newElements = [];
        for (let i = 0; i < elementsToLoad; i++) {
          newElements.push(that.packageOptions[that.countOfRender + i]);
        }

        // 更新已渲染的元素计数
        that.countOfRender += elementsToLoad;
        // 将新元素添加到已渲染的元素数组中
        that.tempPackageOptions = that.tempPackageOptions.concat(newElements);
      },
      //监听滚动条
      handleReachEdge(event) {
        console.log("监听滚动条", event)
        // 滚动到底部，加载更多数据
        this.loop();
      },
      getCountryList() {
        getCountryList().then(res => {
          if (res && res.code == '0000') {
            var list = res.data;
            this.continentList = list;
            this.continentList.sort(function(str1, str2) {
              return str1.countryEn.localeCompare(str2.countryEn);
            });
          } else {
            throw res
          }
        }).catch((err) => {})
      },
      //获取渠道商
      getCorpList() {
        getCorpList({
          "type": 1,
          "status": 1,
          "checkStatus": 2
        }).then(res => {
          if (res && res.code == '0000') {
            this.corpList = res.data;
          } else {
            throw res
          }
        }).catch((err) => {

        }).finally(() => {

        })
      },
      //获取按照大洲-国家
      getContinentMcc() {
        getContinentMcc().then(res => {
          if (res && res.code == '0000') {
            this.continentsData = res.data

            // 提取所有国家数据并放入一个数组
            let countries = [];
            for (let continent in res.data) {
              if (res.data.hasOwnProperty(continent)) {
                countries = countries.concat(res.data[continent]);
              }
            }

            // 根据国家英文名称的首字符对数组进行排序
            countries.sort((a, b) => {
              const charA = a.countryEn.charAt(0).toLowerCase();
              const charB = b.countryEn.charAt(0).toLowerCase();
              if (charA < charB) return -1;
              if (charA > charB) return 1;
              return 0;
            });
            this.countryList = countries
          } else {
            throw res
          }
        }).catch((err) => {

        }).finally(() => {

        })
      },
    }
  }
</script>

<style scoped>
  .custom-textarea {
    border: 1px solid #dcdcdc;
    width: 100%;
    height: 300px;
    border-radius: 8px;
    display: flex;
  }

  .options-container1 {
    overflow-y: auto;
    width: 75%;
    width: 100%;
    padding: 20px;
    font-size: 17px;
  }

  .options-container {
    /* overflow-y: auto; */
    /* width: 75%; */
    width: 100%;
    padding: 20px;
    font-size: 17px;
  }

  .button-container {
    width: 25%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
  }

  .option-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 8px;
  }

  .remove-button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: #ff4d4f;
    margin-left: 3px;
    padding: 0 4px;
    transition: transform 0.2s;
  }

  .remove-button:hover {
    transform: scale(1.1);
  }

  .footer-textarea {
    display: flex;
    justify-content: center;
  }

  .selected-countries-list {
    list-style-type: none;
    /* 移除默认的列表样式 */
    padding: 0;
    /* 移除内边距 */
    margin-top: 10px;
    /* 添加一些上边距 */
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  .selected-country {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #ddd;
    /* 可选：添加下边框分隔每个国家 */
    margin-right: 20px;
    cursor: pointer;
  }

  .selected-country:last-child {
    border-bottom: none;
    /* 移除最后一个国家的下边框 */
  }

  /* 可选：添加一些额外的样式，如字体大小、颜色等 */
  .selected-country span {
    font-size: 18px;
    color: #333;
  }

  .spinBox {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
