<template>
  <Card style="width: 100%;padiing: 16px;">
    <Form inline>
      <FormItem>
        <Button style="margin: 0 2px" type="info" @click="SMSAdd">
          <div style="display: flex;align-items: center;">
            <Icon type="md-add" />&nbsp;新增</div>
        </Button>
      </FormItem>
      <FormItem>
        <Button style="margin: 0 2px" type="error" v-has="'batchDelete'" @click="deleteList">
          <div style="display: flex;align-items: center;">
            <Icon type="ios-trash" />&nbsp;批量删除</div>
        </Button>
      </FormItem>
    </Form>
    <div>
      <Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading"
        @on-selection-change="handleRowChange">
        <template slot-scope="{ row, index }" slot="action">
          <Button type="success" size="small" style="margin-right: 5px" v-has="'update'" @click="SMSCommon(row,'Update')">编辑</Button>
          <Button type="error" size="small" v-has="'delete'" @click="SMSDel(row.id)">删除</Button>
        </template>
      </Table>
      <Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="loadByPage"
        style="margin: 15px 0;" />
    </div>
    <!-- 客服短信 -->
    <Modal :title="SMSTitle" v-model="SMSEditFlag" :footer-hide="true"   @on-cancel="reset('editObj')" :mask-closable="false" width="500px">
      <div style="padding: 0 16px;">
        <Form ref="editObj" :model="editObj" :rules="ruleEditValidate" label-position="top">
          <FormItem label="客服短信名称" prop="templateName">
            <Input v-model="editObj.templateName" :clearable="true" placeholder="请输入客服短信名称" type="text" ></Input>
          </FormItem>
          <FormItem label="客服短信内容" prop="contentCn">
            <Input v-model="editObj.contentCn" :clearable="true" @on-blur="handleInputBlur()" placeholder="请输入短信内容,最多支持600个字符" type="textarea" :rows="5"></Input>
                    <CheckboxGroup
                      v-if="
                         operationType != 'Info'
                      "
                      v-model="CheckBoxParams.paramSC"
                      class="checkbox-group"
                      style="text-align: left"
                      @on-change="
                        setContentParamSC(
                          $event,
                          editObj.contentCn
                        )
                      "
                    >
                      <Checkbox
                        v-for="(item, index) in CheckBoxList"
                        :key="index"
                        :label="item.value"
                        >{{ item.labelCn }}</Checkbox
                      >
                    </CheckboxGroup>
          </FormItem>
        </Form>
        <div style="text-align: center;">
          <Button type="primary" @click="submit" v-if="operationType=='Add'" v-has="'add'">提交</Button>
          <Button type="primary" @click="submit" v-if="operationType=='Update'" v-has="'update'">提交</Button>
          <Button style="margin-left: 8px" @click="reset('editObj')">重置</Button>
        </div>
      </div>
    </Modal>
  </Card>
</template>

<script>
import {
  getCustomerList,
  addCustomer,
  updateCustomer,
  delCustomer,
} from "@/api/sms/customer";
  export default {
    components: {

    },
    data() {
      return {
            insertLocal: 0, //记录光标位置
        SMSEditFlag: false,
        SMSTitle: '通知短信新增',
        editObj: {
          contentCn: '', //客服短信内容
          templateName:''
        },
        ruleEditValidate: {
                templateName: [
                        { required: true, message: '客服短信名称不能为空', trigger: 'blur' }
                    ],
                contentCn: [
                        { required: true, message: '客服短信内容不能为空', trigger: 'blur' },
						{ min:0,max:600, message: '最多支持600个字符'}
                    ],
        },
        tableData: [], //列表信息
        selection: [], //多选
        selectionIds: [], //多选ids
               CheckBoxParams: {
            paramSC: [], //激活前参数-简体中文
            paramTempSC: [], //激活前临时参数-简体中文
            // paramTC: [], //激活前参数-繁体中文
            // paramTempTC: [], //激活前临时参数-繁体中文
            // paramEN: [], //激活前参数-英文
            // paramTempEN: [], //激活前临时参数-英文
          },
                  CheckBoxList: [
            {
              labelCn: "iccid",
              value: "{iccid}",
            },
            {
              labelCn: "验证码",
              value: "{code}",
            },
       
          ],
        tableLoading: false,
        total: 0,
        pageSize: 10,
        page: 1,
        columns: [{
            type: 'selection',
            minWidth: 60,
            maxWidth: 60,
            align: 'center'
          },
          {
            title: '模板名称',
            key: 'templateName',
            align: 'center',
            minWidth: 150,
            tooltip: true
          },
          {
            title: '模板内容',
            key: 'contentCn',
            align: 'center',
            minWidth: 150,
            tooltip: true
          },
          {
            title: '操作',
            slot: 'action',
            minWidth: 150,
            maxWidth: 220,
            align: 'center'
          }
        ],
        operationType: 'Add',
      }
    },
    methods: {
      //表格初始化
      init() {
        this.loadByPage(0)
      },
      //提交
      submit() {
        this.$refs["editObj"].validate((valid) => {
          if (valid) {
          let func ;
          if(this.operationType === 'Add'){
            func = addCustomer;
          }
          if(this.operationType === 'Update'){
            func = updateCustomer;
          }

          func(this.editObj).then(res=>{

            if(res.code === '0000'){
         this.$Notice.success({
              title: '操作提示',
              desc: '操作成功'
            })
            this.init();
            }else{
                       this.$Notice.success({
              title: '操作提示',
              desc: '操作失败'
            })
            }
            this.SMSEditFlag = false;
            this.reset('editObj');


          })
          }
        })
      },
      reset(name) {
        this.$refs[name].resetFields();
        this.resetContentParam();
      },
      //表格数据加载
      loadByPage(e) {

        if(e===0){
          this.page = 1;
        }

        
          getCustomerList({ "current": e,size:'10'}).then(res=>{
            if(res.code === '0000'){
 this.tableData = res.paging.data;
 this.total = res.paging.total
            }
          })

      },
      SMSAdd() {
        this.operationType = 'Add';
        this.SMSTitle = '客服短信新增';
        this.SMSEditFlag = true;
        this.editObj = {
          "contentCn": '',
          templateName:''
        };
      },
      //详情
      //编辑
      SMSCommon(row, type) {
        this.operationType = type;
        // this.SMSTitle = type === 'Info' ? '客服短信详情' : type === 'Update' ? '客服短信编辑' : this.SMSTitle;
        this.SMSTitle = '客服短信编辑';
        this.SMSEditFlag = true;
        this.editObj = {
          "id": row.id,
          "contentCn": row.contentCn,
          "templateName": row.templateName,
        };
this.fillCheckBox(row.contentCn)
      },

    fillCheckBox(content) {
      let paramSC = [];
       this.CheckBoxList.forEach((ele) => {
          if (content.indexOf(ele.value) > -1) {
            paramSC.push(ele.value);
          }
     
        });

       this.CheckBoxParams.paramSC = paramSC;
       this.CheckBoxParams.paramTempSC = paramSC;
     
    },

      //删除
      SMSDel(ids) {
        this.$Modal.confirm({
          title: '确认删除？',
          onOk: () => {
            //id

          if(!Array.isArray(ids)){
            ids = [ids]
          }

    delCustomer(ids).then(res=>{
      if(res.code === '0000'){
        this.init()
        this.selection = [];
    this.$Notice.success({
              title: '操作提示',
              desc: '操作成功'
            })
      }else{
            this.$Notice.success({
              title: '操作提示',
              desc: '操作失败'
            })
      }
    })
        
          }
        });
      },
      //多选
      handleRowChange(selection) {
        this.selection = selection;
        this.selectionIds = [];
        selection.map((value, index) => {
          this.selectionIds.push(value.id);
        });
      },
      //批量删除
      deleteList() {
        var len = this.selection.length;
        if (len < 1) {
          this.$Message.warning('请至少选择一条记录')
          return
        }

     this.SMSDel(this.selectionIds)

      },

          handleInputBlur() {
        this.insertLocal = event.srcElement.selectionStart;
    },


    //动态参数
    setContentParamSC(e,  value) {
      var len = e.length; //已勾长度
      var before = value.substring(0, this.insertLocal); //光标前
      var after = value.substring(this.insertLocal, value.length); //光标后
      //激活前-简体中文
      if (len >= this.CheckBoxParams.paramTempSC.length) {
        //当前选中参数个数大于上次选中参数个数，说明此次事件为勾选事件，修改模板内容
        this.editObj.contentCn = before + e[len - 1] + after; //模板内容拼接
           this.insertLocal = this.editObj.contentCn.length;
      } else {
        this.filterContent( e, "contentCn");
      }
      this.CheckBoxParams.paramTempSC = e;
    },

    
    //参数置空
    resetContentParam() {
      this.insertLocal = 0;
     this.CheckBoxParams.paramSC = [];
    this.CheckBoxParams.paramTempSC = [];
    },

        /*取消插入的模板
  sceneId 场景id
  index  索引
  flag 当前选中的数组
  key 替换的字段

*/

    filterContent( flag, key) {
      const tempArr = this.CheckBoxList.map((ele) => {
        return ele.value;
      });

      tempArr.forEach((ele) => {
        if (!flag.includes(ele)) {
          this.editObj[key] = this.editObj[
            key
          ].replace(ele, "");
        }
      });
    },

    },
    mounted() {
      // this.tableData = res.data
      this.init();
    }
  }
</script>

<style>
  .inputSty {
    width: 200px;
  }
</style>
