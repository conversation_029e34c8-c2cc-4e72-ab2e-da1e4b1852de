<template>
  <Card style="width: 100%; padiing: 16px">
    <Form ref="searchForm" :model="searchObj" inline>
      <FormItem>
        <Input
          v-model="searchObj.taskName"
          placeholder="请输入任务名称"
          clearable
          style="width: 200px"
        />
      </FormItem>
      <FormItem>
        <Select
          v-model="searchObj.status"
          placeholder="请选择任务状态"
          :clearable="true"
          style="width: 200px"
        >
          <Option :value="0">等待发送</Option>
          <Option :value="1">发送中</Option>
          <Option :value="2">发送成功</Option>
          <Option :value="3">取消</Option>
        </Select>
      </FormItem>
      <FormItem>
        <DatePicker
          type="datetime"
          format="yyyy/MM/dd HH:mm:ss"
          class="inputSty"
          placeholder="请选择发送时间"
          :clearable="true"
          v-model="searchObj.startTime"
        ></DatePicker>
      </FormItem>
      <FormItem>
        <Button style="margin: 0 2px" type="primary" @click="searchSMS"  :loading="loading1">
            <Icon type="ios-search" v-show="!loading1" />&nbsp;搜索
        </Button>
        <Button style="margin: 0 2px" type="info" v-has="'add'" @click="SMSAdd">
            <Icon type="md-add" />&nbsp;新增
        </Button>
      </FormItem>
    </Form>
    <div>
      <Table
        ref="selection"
        :columns="columns"
        :data="tableData"
        :ellipsis="true"
        :loading="tableLoading"
      >
        <template slot-scope="{ row, index }" slot="action">
          <Button
            type="primary"
            size="small"
            style="margin-right: 5px"
            v-has="'view'"
            @click="SMSInfo(row)"
            >详情</Button
          >
          <Button
            type="success"
            size="small"
            style="margin-right: 5px"
            v-has="'update'"
            @click="SMSEdit(row)"
            v-if="row.status === 0"
            >编辑</Button
          >
          <Button
            v-if="row.status === 0"
            type="error"
            size="small"
            v-has="'cancel'"
            @click="SMSCancel(row)"
            >取消</Button
          >
    
        </template>
      </Table>
      <Page
        :total="total"
        :page-size="pageSize"
        :current.sync="page"
        show-total
        show-elevator
        @on-change="loadByPage"
        style="margin: 15px 0"
      />
    </div>
    <!-- 新增/编辑短信 -->
    <Modal
      :title="SMSTitle"
      v-model="SMSEditFlag"
      :footer-hide="true"
      :mask-closable="false"
      @on-cancel="reset('editObj')"
      width="500px"
    >

 

      <div style="padding: 0 16px">
               <Button
            type="success"
            size="small"
            @click="dowloadTemplate"
            style="margin-bottom: 10px"
            >下载模板</Button>
        <Form
          ref="editObj"
          :model="editObj"
          :label-width="100"
          :rules="ruleEditValidate"
        >
          <FormItem label="任务名称" prop="taskName">
            <Input
              v-model="editObj.taskName"
              :clearable="true"
              placeholder="请输入任务名称"
            ></Input>
          </FormItem>
          <FormItem label="发送时间" prop="startTime">
            <DatePicker
              type="datetime"
              format="yyyy/MM/dd HH:mm:ss"
              style="width: 100%"
              placeholder="请选择发送时间"
              :clearable="true"
              v-model="editObj.startTime"
            ></DatePicker>
          </FormItem>
          <FormItem label="发送内容" prop="taskContent">
            <Input
              v-model="editObj.taskContent"
              :clearable="true"
              placeholder="请输入短信内容,最多支持600个字符"
              type="textarea"
              :rows="5"
            ></Input>
          </FormItem>
          <FormItem label="号码录入方式" prop="type">
            <Select
              v-model="editObj.type"
              :disabled="operationType == 'Update'"
              placeholder="选择号码录入方式"
              :clearable="true"
            >
              <Option :value="0" >手动输入</Option>
              <Option :value="1">文件上传</Option>
            </Select>
          </FormItem>
          <div v-if="editObj.type == 0">
            <FormItem label="接收号码" prop="phones">
              <Input
                v-model="editObj.phones"
                :clearable="true"
                placeholder="请输入接收号码，号码之间使用 , 分隔；例如：161**********1,161**********2"
                type="textarea"
                :rows="5"
              ></Input>
            </FormItem>
          </div>
          <div v-if="editObj.type == 1">
            <FormItem label="接收号码" prop="phoneFile">
              <Upload
                :action="(NODE_ENV === 'development'?'/cmiweb/':'/api/') +'sms/task/upload'"
                :headers="headers"
                accept=".csv"
                :format="['csv']"
                :max-size="1024*5"
                :on-success="uploadSuccess"
                :on-error="uploadFails"
                :on-exceeded-size="uploadExceededSize"
                :on-format-error="uploadFormatError"
              >
                <Button type="dashed" long icon="md-add" v-has="'import'"
                  >号码导入</Button
                >
              </Upload>
            </FormItem>
          </div>
        </Form>
        <div style="text-align: center">
          <Button
            type="primary"
            @click="submit"
            v-if="operationType == 'Add'"
            :loading="addLoading"
            v-has="'add'"
            >提交</Button
          >
          <Button
            type="primary"
            @click="submit"
            v-if="operationType == 'Update'"
            :loading="addLoading"
            v-has="'update'"
            >提交</Button
          >
          <Button style="margin-left: 8px" @click="reset('editObj')"
            >重置</Button
          >
        </div>
      </div>
    </Modal>
	<!-- 号码模板文件table -->
	<Table
		:columns="modelColumns"
		:data="modelData"
		ref="modelTable"
		v-show="false"
	></Table>
  </Card>
</template>

<script>
import {
  getTaskList,
  getTaskDetails,
  addTask,
  updateTask,
  delTask,
  getTaskDwonload,
  uploadTask,
} from "@/api/sms/market.js";
import dayjs from "dayjs";
export default {
  components: {},
  data() {

    const validatePhones = (rule, value, callback) => {

        if(this.editObj.type === 0){

          if(value === ''){
             callback(new Error('接收号码不能为空'));
          }else{
                 callback();
          }

        }else{
                    callback();
        }
            };

    const validatePhoneFile = (rule, value, callback) => {
            
        if(this.editObj.type === 1){
          if(value === ''){
             callback(new Error('上传文件不能为空'));
          }else{
                 callback();
          }
        }else{
          callback();
        }
            };

    return {
      templateDownloadUrl:'',
      loading1:false,
      addLoading:false,
      NODE_ENV:'',//环境
      headers:{},//头部
      searchObj: {
        taskName: "", //任务名称
        startTime: "", //发送时间
        status: "", //发送状态
      },
      SMSEditFlag: false,
      SMSTitle: "营销短信新增",
      editObj: {
         id:'',//任务id
        taskName: "", //营销短信名称
        startTime: "", //发送时间
        taskContent: "", //发送内容
        type: "0", //上传方式
        phoneFile: "", //上传文件路径
        phones: "", //接收号码
      },
      ruleEditValidate: {

              taskName: [
                        { required: true, message: '任务名称不能为空', trigger: 'blur' }
                    ],
                    startTime: [
                        { required: true,type:'date', message: '发送时间不能为空', trigger: 'blur' },
                    ],
                    taskContent: [
                        { required: true, message: '发送内容不能为空', trigger: 'blur' },
						{ min:0,max:600, message: '最多支持600个字符'}
                    ],
                    type: [
                        { required: true,type:'number', message: '请选择号码录入方式', trigger: 'select' }
                    ],
                    phoneFile: [
                        { validator: validatePhoneFile }
                    ],
                    phones: [
                        { validator: validatePhones }
                    ],
               
        

      },
      operationType: "Add",
      tableData: [], //列表信息
      tableLoading: false,
      total: 0,
      pageSize: 10,
      page: 1,
      columns: [
        {
          type: "selection",
          minWidth: 60,
          maxWidth: 60,
          align: "center",
        },
        {
          title: "任务名称",
          key: "taskName",
          align: "center",
          minWidth: 150,
          tooltip: true,
        },
        {
          title: "发送状态",
          key: "status",
          align: "center",
          minWidth: 150,
          tooltip: true,
          render: (h, params) => {
            // 0-等待发送，1-发送中，2-发送成功 3-取消
            const row = params.row;
            const keyValue = ["等待发送", "发送中", "发送成功","取消"];
            const text = keyValue[row.status];
            return h("label", text);
          },
        },
        {
          title: "发送开始时间",
          key: "startTime",
          align: "center",
          minWidth: 150,
          tooltip: true,
        },
        {
          title: "发送完成时间",
          key: "endTime",
          align: "center",
          minWidth: 150,
          tooltip: true,
        },
        {
          title: "操作",
          slot: "action",
          minWidth: 200,
          maxWidth: 220,
          align: "center",
        },
      ],
	  modelData:[
	  	{
	  		'number': '********'
	  	},
	  ],
	  modelColumns: [
	  	{title:'号码',key:'number'}// 列名根据需要添加
	  ],
    };

  },
  created(){
    let host = process.env.NODE_ENV === 'production' ? '/cmifront': '/' ;
    // this.templateDownloadUrl = host+'/market-msg-template.csv'
  },
  methods: {

    dowloadTemplate(){
		this.$refs.modelTable.exportCsv({
			filename:'号码模板',
			// type:'xlsx',
			columns:this.modelColumns,
			data:this.modelData
		})
    },

    //表格初始化
    init() {
        var token = this.$store.state.user.token;
      if (token) { // 判断是否存在token，如果存在的话，则每个http header都加上token
        this.headers = {Authorization:`bearer ${token}`,userName:encodeURIComponent(this.$store.state.user.userName,'utf-8')};
      }
      this.NODE_ENV = process.env.NODE_ENV;
      this.loadByPage(0);
    },

    // 统一数据格式化操作 1 提交数据格式化 2 返回数据格式化
    formatValue(type) {
      if (type === 1) {
        const obj = JSON.parse(JSON.stringify(this.editObj));
        // 格式化号码列表
        if (!Array.isArray(obj.phones)) {
          obj.phones = obj.phones.split(",");
        }

        // 格式化时间
        obj.startTime = dayjs(obj.startTime).format("YYYY-MM-DD HH:mm:ss");
        
        return obj;
      }
    },

    //提交
    submit() {
      let StingToFunc ={'Add':addTask,'Update':updateTask}

    console.log(this.editObj)

      this.$refs["editObj"].validate((valid) => {
        console.log(valid);
        if (valid) {
      this.addLoading = true;
          StingToFunc[this.operationType](this.formatValue(1)).then((res) => {
            if (res.code === "0000") {
              this.$Notice.success({
                title: "操作提示",
                desc: "操作成功",
              });

              this.SMSEditFlag = false;
              this.reset("editObj");
              this.init();
            }
          }).finally(()=>{
            this.addLoading = false;
          })
        }
      });
    },
    reset(name) {
      let tempObj = {type: this.editObj.type}
      this.$refs[name].resetFields();
    this.$nextTick(()=>{
     this.$set( this.editObj,'type',tempObj.type)
    })
    },
    //表格数据加载
    loadByPage(e) {
      
        if(e===0){
          this.page = 1;
        }
      if(this.searchObj.startTime){
        this.searchObj.startTime  = dayjs(this.searchObj.startTime).format('YYYY-MM-DD HH:mm:ss')
      }
      this.tableLoading = true;
      getTaskList({ current: e, size: "10", ...this.searchObj }).then((res) => {
        if (res.code === "0000") {
          this.tableData = res.paging.data;
          this.total = res.paging.total;
        }
      }).finally(() => {
          this.tableLoading = false;
          this.loading1 = false;
        });;
    },
    //搜索
    searchSMS() {
      this.loading1 = true;
      this.loadByPage(0);
    },
    //新增
    SMSAdd() {
      this.operationType = "Add";
      this.SMSTitle = "营销短信新增";
      this.editObj = {
        id:'',//任务id
        taskName: "", //营销短信名称
        startTime: "", //发送时间
        taskContent: "", //发送内容
        type: 0, //上传方式
        phoneFile: "", //上传文件路径
        phones: "", //接收号码
      };
      this.SMSEditFlag = true;
    },
    //编辑
    SMSEdit(row) {
      this.operationType = "Update";
      this.SMSTitle = "营销短信编辑";

      getTaskDetails(row.id).then((res) => {
        if (res.code === "0000") {
          let tempObj = {};
          Object.keys(this.editObj).forEach((key) => {
            if (key === "phones") {
              tempObj[key] = res.data[key] ? res.data[key].join(",") : "";
            } else {
              tempObj[key] = res.data[key];
            }
          });
          this.editObj = tempObj;
        }

        console.log(this.editObj);
        this.SMSEditFlag = true;
      });
    },
    //详情
    SMSInfo(row) {
      this.$router.push({
        name: "marketingInfo",
        query: { 
			id: row.id ,
			searchList:encodeURIComponent(JSON.stringify(this.searchObj))
			},
      });
    },
    //取消
    SMSCancel(row) {
      var completionTime = row.endTime;
      if (completionTime) {
        this.$Notice.error({
          title: "操作提示",
          desc: "操作失败,该任务已完成",
        });
        return false;
      }

      this.$Modal.confirm({
        title: "确认取消？",
        onOk: () => {
          delTask(row.id).then((res) => {
            if (res.code === "0000") {
              this.$Notice.success({
                title: "操作提示",
                desc: "操作成功",
              });
            }
            this.init();
          });
        },
      });
    },

    // 上传成功
    uploadSuccess(response,file,fileList) {
      if (response.code === "0000" && this.editObj.type === 1) {
        this.editObj.phoneFile = response.data;
      }else{
        fileList.splice(-1,1)
          this.$Notice.error({
          title: "操作提示",
          desc: response.msg,
        });
      }
    },

    // 上传失败
    uploadFails(error,file,fileList) {
         fileList.splice(-1,1)
          this.$Notice.error({
          title: "操作提示",
          desc: error.msg,
        });
    },

// 上传超出文件大小
    uploadExceededSize(){

              this.$Notice.error({
          title: "操作提示",
          desc:'单个文件最大为5M,该文件超出指定大小',
        });

    },
    // 上传文件格式不正确
    uploadFormatError(){
              this.$Notice.error({
          title: "操作提示",
          desc:'只允许上传csv文件，格式不正确' ,
        });

    },
  },
  mounted() {
	//缓存数据
	let searchList = JSON.parse(localStorage.getItem("searchList")) === null ? '' : JSON.parse(localStorage.getItem(
		"searchList"))
	if (searchList) {
		this.searchObj.taskName = searchList.taskName === undefined ? "" : searchList.taskName
		this.searchObj.status = searchList.status === undefined ? "" : searchList.status
		this.searchObj.startTime = searchList.startTime === undefined ? "" : searchList.startTime
	}
	this.init();
	//清除缓存
	localStorage.removeItem("searchList")
  },
};
</script>

<style>

.ivu-btn > span{
  display: inline-flex;
    align-items: center;
}

.inputSty {
  width: 200px;
}
</style>
