<template>
  <Card style="width: 100%;padiing: 16px;">
    <div style="display: flex;justify-content: center;margin: 20px 0;">
      <div>
        <Form ref="formObj" :model="formObj" :label-width="150">
          <FormItem label="任务名称" prop="taskName">
            <Input v-model="formObj.taskName" readonly class="inputSty"></Input>
          </FormItem>
          <FormItem label="发送时间" prop="startTime">
            <DatePicker
              type="datetime"
              format="yyyy/MM/dd HH:mm:ss"
              class="inputSty"
              readonly
              v-model="formObj.startTime"
            ></DatePicker>
          </FormItem>
          <FormItem label="结束时间" prop="endTime" v-if="formObj.endTime">
            <DatePicker
              type="datetime"
              format="yyyy/MM/dd HH:mm:ss"
              class="inputSty"
              readonly
              v-model="formObj.endTime"
            ></DatePicker>
          </FormItem>
          <FormItem label="发送内容" prop="taskContent">
            <Input
              v-model="formObj.taskContent"
              readonly
              type="textarea"
              :rows="5"
              class="inputSty"
            ></Input>
          </FormItem>
          <FormItem label="号码录入方式" prop="type">
            <Select v-model="formObj.type" disabled class="inputSty">
              <Option :value="0">手动输入</Option>
              <Option :value="1">文件上传</Option>
            </Select>
          </FormItem>
          <div>
            <FormItem label="接收号码" prop="phones">
              <Input
                v-model="formObj.phones"
                readonly
                type="textarea"
                :rows="5"
                class="inputSty"
              ></Input>
            </FormItem>
          </div>
          <!-- <div v-if="formObj.type=='2'">
            <FormItem label="接收号码">
              <Button type="success" long icon="md-cloud-download" v-has="'export'">号码导出</Button>
            </FormItem>
          </div> -->
          <FormItem label="接收成功数目">
            <Button
              type="primary"
              style="margin: 0 2px;"
              icon="ios-search"
              :loading="loadingGroups[0]"
              @click="loadSuccessRecord"
              >接收查看</Button
            >
            <Button
              type="success"
              style="margin: 0 2px;"
              icon="md-cloud-download"
              :loading="loadingGroups[1]"
              v-has="'export'"
              @click="exportSuccessRecord"
              >号码导出</Button
            >
          </FormItem>
          <FormItem label="接收失败数目">
            <Button
              type="primary"
              style="margin: 0 2px;"
              icon="ios-search"
               :loading="loadingGroups[2]"
              @click="loadFailRecord"
              >接收查看</Button
            >
            <Button
              type="success"
              style="margin: 0 2px;"
              icon="md-cloud-download"
              v-has="'export'"
               :loading="loadingGroups[3]"
              @click="exportFailRecord"
              >号码导出</Button
            >
          </FormItem>
		  <FormItem >
			<Button   
			 style="width: 200px;"
			 @click="back()" >返回</Button>  
		  </FormItem>
        </Form>
      </div>
    </div>
    <Modal
      :title="phonesTitle"
      v-model="phonesFlag"
      :footer-hide="true"
      :mask-closable="false"
      width="500px"
    >
      <div style="padding: 0 16px;">
        <Form ref="editObj" :model="showObj" label-position="top">
          <FormItem :label="countName" prop="count">
            <Input v-model="showObj.count" readonly></Input>
          </FormItem>
          <FormItem :label="listName" prop="list">
            <Input
              v-model="showObj.list"
              readonly
              type="textarea"
              :rows="5"
            ></Input>
          </FormItem>
        </Form>
      </div>
    </Modal>
  </Card>
</template>

<script>
import { getTaskDetails, sendResult,getTaskDwonload } from "@/api/sms/market.js";
export default {
  components: {},
  data() {
    return {
      formObj: {
        taskName: "", //营销短信名称
        startTime: "", //发送时间
        endTime: "", //结束时间
        taskContent: "", //发送内容
        type: "", //上传方式
        phones: "", //接收号码
        successList: "", //成功号码
        failList: "" //失败号码
      },
      phonesFlag: false,
      phonesTitle: "", //模态框title
      countName: "", //数量名称
      listName: "", //数据名称
      showObj: {
        count: "", //数量
        list: "" //数据
      },
      taskID:'',//当前id
      loadingGroups:[]

    };
  },

  methods: {
    //成功号码加载
    loadSuccessRecord(a,b,c) {

        this.$set( this.loadingGroups,0,true)

      this.phonesTitle = "接收成功记录";
      this.countName = "发送成功数量";
      this.listName = "发送成功列表";

      this.sendResult(this.taskID, 1);
    },
    //失败号码加载
    loadFailRecord() {
        this.$set( this.loadingGroups,2,true)

      this.phonesTitle = "接收失败记录";
      this.countName = "发送失败数量";
      this.listName = "发送失败列表";

      this.sendResult(this.taskID, 3);
    },

    streamDownload(data,name){
      
							const content = data
							// 获取当前时间
							let date = new Date();
							let y = date.getFullYear();
							let m = date.getMonth() + 1;
							let d = date.getDate();
							// let H = Da.getHours();
							let time = y + "-" + m + "-" + d

							const fileName = time + name+'.txt' // 导出文件名
							if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
								const link = document.createElement('a') // 创建a标签
								let url = URL.createObjectURL(content)
								link.download = fileName
								link.href = url
								link.click() // 执行下载
								URL.revokeObjectURL(url) // 释放url
							} else { // 其他浏览器
								navigator.msSaveBlob(content, fileName)
							}
    },

    //成功号码导出
    exportSuccessRecord() {

        this.$set( this.loadingGroups,1,true);
        getTaskDwonload({ taskId:this.taskID,status:1}).then(res=>{

						this.streamDownload(res.data,'接收成功号码')
   
        }).finally(() => {

        this.$set( this.loadingGroups,1,false);
         
        });

    
    },
    //失败号码导出
    exportFailRecord() {
       this.$set( this.loadingGroups,3,true)
          getTaskDwonload({ taskId:this.taskID,status:3}).then(res=>{
           		this.streamDownload(res.data,'接收失败号码')
        }).finally(() => {

        this.$set( this.loadingGroups,3,false);
         
        });
    },
    // 获取详情信息
    getTaskDetails(id) {
      getTaskDetails(id).then(res => {
        if (res.code === "0000") {
          this.formObj = res.data;
          this.sendResult(id, "");
        }
      });
    },

    sendResult(taskId, status) {
      

      sendResult({ taskId, status }).then(res => {
        if (res.code === "0000") {
          if (status === "") {
            this.$set(this.formObj, "phones", res.data.join(","));
            return;
          } else {
            this.$set(this.showObj, "count", res.data.length);
            this.$set(this.showObj, "list", res.data.join(","));
            this.phonesFlag = true;
            return;
          }
        }
      }).finally(() => {

        this.$set( this.loadingGroups,0,false);
        this.$set( this.loadingGroups,2,false);
         
        });
    },
	back(){
		this.$router.push({
			path: '/marketingSMS',
		})
	}
  },

  mounted() {
   this.taskID = this.$route.query.id;
    this.getTaskDetails(this.taskID);
	// 保存上一页返回数据
	localStorage.setItem("searchList", decodeURIComponent(this.$route.query.searchList))
  }
};
</script>

<style scoped="scoped">
.inputSty {
  width: 350px;
}

.recordBtnSty {
  width: 200px;
  margin-right: 10px;
}

.centerSty {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
