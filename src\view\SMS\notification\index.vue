<template>
	<Card style="width: 100%; padiing: 16px">
		<Form ref="searchForm" :model="searchObj" inline>
			<FormItem>
				<Input v-model="searchObj.SMSId" placeholder="请输入模板编号(ID)" clearable style="width: 200px" />
			</FormItem>
			<FormItem>
				<Input v-model="searchObj.SMSName" placeholder="请输入模板名称" clearable style="width: 200px" />
			</FormItem>
			<FormItem>
				<Button style="margin: 0 2px" type="primary" @click="searchSMS" icon="ios-search" :loading="loading1">
					搜索
				</Button>
				<Button style="margin: 0 2px" type="info" v-has="'add'" @click="SMSAdd">
					<div style="display: flex; align-items: center">
						<Icon type="md-add" />&nbsp;新增
					</div>
				</Button>
				<Button style="margin: 0 2px" type="error" v-has="'batchDelete'" @click="deleteList">
					<div style="display: flex; align-items: center">
						<Icon type="ios-trash" />&nbsp;批量删除
					</div>
				</Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading"
				@on-selection-change="handleRowChange">
				<template slot-scope="{ row, index }" slot="action">
					<Button type="primary" size="small" style="margin-right: 5px" v-has="'view'"
						@click="SMSCommon(row, 'Info')">详情</Button>
					<Button type="success" size="small" style="margin-right: 5px" v-has="'update'"
						@click="SMSCommon(row, 'Update')">编辑</Button>
					<Button type="info" size="small" style="margin-right: 5px" v-has="'copy'"
						@click="SMSCommon(row, 'Copy')">复制</Button>
					<Button type="error" size="small" v-has="'delete'" @click="SMSDel(row.id)">删除</Button>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0" />
		</div>
		<!-- 通知短信 -->
		<Modal :title="SMSTitle" v-model="SMSEditFlag" :footer-hide="true" :mask-closable="false"
			@on-cancel="resetAll('editObj')" width="1500px">
			<div style="padding: 0 16px; height: 690px; overflow-x: auto">
				<Form ref="editObj" :model="editObj" :rules="ruleEditValidate">
					<Row>
						<Col span="8" v-if="operationType == 'Update'">
						<FormItem label="模板短信编号">
							<Input v-model="SMSId" readonly class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="8">
						<FormItem label="模板短信名称" prop="SMSName">
							<Input v-model="editObj.SMSName" :readonly="operationType == 'Info'"
								:clearable="operationType != 'Info'" placeholder="请输入模板短信名称" class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="8" v-if="operationType == 'Info'">
						<FormItem label="模板创建时间">
							<Input v-model="editObj.createTime" readonly class="inputSty"></Input>
						</FormItem>
						</Col>
					</Row>
					<Row style="margin-bottom: 10px">
						<Col span="3">
						<label class="labelSty">模板场景</label>
						</Col>
						<Col span="5">
						<label class="labelSty">模板内容(简中)</label>
						</Col>
						<Col span="5">
						<label class="labelSty">模板内容(繁中)</label>
						</Col>
						<Col span="5">
						<label class="labelSty">模板内容(英文)</label>
						</Col>
						<Col span="2">
						<label class="labelSty">模板开关</label>
						</Col>
						<Col span="4">
						<label class="labelSty">接收手机号码</label>
						</Col>
					</Row>
					<Row v-for="(obj, index) in editObj.sceneList" :key="index" style="display: flex; align-items: top">
						<Col span="3">
						<FormItem :prop="'sceneList.' + index + '.sceneName'">
							<!-- <Input v-model="obj.sceneName" readonly></Input> -->
							<label class="labelSty">{{ obj.sceneName }}</label>
						</FormItem>
						</Col>
						<Col span="5">
						<FormItem :prop="'sceneList.' + index + '.contentCn'">
							<div class="inputCenter">
								<Input v-model="obj.contentCn" type="textarea" :rows="3"
									:readonly="operationType == 'Info'" placeholder="请输入模板内容(简中)"
									@on-blur="handleInputBlur(index)"></Input>

								<div v-for="(insertItem, index1) in Object.keys(insertLists)" :key="index1">
									<CheckboxGroup v-if="
                        obj.sceneId == insertItem && operationType != 'Info'
                      " v-model="insertLists[insertItem].params.paramSC" class="checkbox-group"
										style="text-align: left" @on-change="
                        setContentParamSC(
                          $event,
                          obj.sceneId,
                          index,
                          obj.contentCn
                        )
                      ">
										<Checkbox v-for="(item, index) in insertLists[obj.sceneId].list" :key="index"
											:label="item.value">{{ item.labelCn }}</Checkbox>
									</CheckboxGroup>
								</div>
							</div>
						</FormItem>
						</Col>
						<Col span="5">
						<FormItem :prop="'sceneList.' + index + '.contentTw'">
							<div class="inputCenter">
								<Input v-model="obj.contentTw" type="textarea" :rows="3"
									:readonly="operationType == 'Info'" placeholder="請輸入範本內容(繁中)"
									@on-blur="handleInputBlur(index)"></Input>
								<div v-for="(insertItem, index1) in Object.keys(insertLists)" :key="index1">
									<CheckboxGroup v-if="
                        obj.sceneId == insertItem && operationType != 'Info'
                      " v-model="insertLists[insertItem].params.paramTC" class="checkbox-group"
										style="text-align: left" @on-change="
                        setContentParamTC(
                          $event,
                          obj.sceneId,
                          index,
                          obj.contentTw
                        )
                      ">
										<Checkbox v-for="(item, index) in insertLists[obj.sceneId].list" :key="index"
											:label="item.value">{{ item.labelTw }}</Checkbox>
									</CheckboxGroup>
								</div>
							</div>
						</FormItem>
						</Col>
						<Col span="5">
						<FormItem :prop="'sceneList.' + index + '.contentEn'">
							<div class="inputCenter">
								<Input v-model="obj.contentEn" type="textarea" :rows="3"
									:readonly="operationType == 'Info'" placeholder="Please input template content"
									@on-blur="handleInputBlur()"></Input>
								<div v-for="(insertItem, index1) in Object.keys(insertLists)" :key="index1">
									<CheckboxGroup v-if="
                        obj.sceneId == insertItem && operationType != 'Info'
                      " v-model="insertLists[insertItem].params.paramEN" class="checkbox-group"
										style="text-align: left" @on-change="
                        setContentParamEN(
                          $event,
                          obj.sceneId,
                          index,
                          obj.contentEn
                        )
                      ">
										<Checkbox v-for="(item, index) in insertLists[obj.sceneId].list" :key="index"
											:label="item.value">{{ item.labelEn }}</Checkbox>
									</CheckboxGroup>
								</div>
							</div>
						</FormItem>
						</Col>
						<Col span="2">
						<FormItem :prop="'sceneList.' + index + '.status'">
							<div class="inputCenter">
								<i-switch v-model="obj.status" :true-value="1" :false-value="2" size="large"
									@on-change="changeSwitch($event, index)" :disabled="operationType == 'Info'">
									<span slot="open">开</span>
									<span slot="close">关</span>
								</i-switch>
							</div>
						</FormItem>
						</Col>
						<Col span="4">
						<FormItem :prop="'sceneList.' + index + '.receiveType'"
							:rules="[{ required: true, message: '接收号码不能为空' }]">
							<div class="inputCenter">
								<Select placeholder="请选择接收号码" :clearable="operationType != 'Info'"
									:disabled="operationType == 'Info'" v-model="obj.receiveType">
									<Option :value="1">注册手机号</Option>
									<Option :value="2">主卡MSISDN</Option>
									<Option :value="3">主卡MSISDN或注册手机号</Option>
								</Select>
							</div>
						</FormItem>
						</Col>
					</Row>
				</Form>
				<label><label style="color: #ed4014">注</label>：每种场景内容至少填写一项。</label>
				<div style="text-align: center" v-if="operationType != 'Info'">
					<Button type="primary" @click="submit" v-if="operationType == 'Add'" :loading="addLoading"
						v-has="'add'">提交</Button>&nbsp;&nbsp;
					<Button type="primary" @click="submit" v-if="operationType == 'Update'" :loading="addLoading"
						v-has="'update'">提交</Button>&nbsp;&nbsp;
					<Button type="primary" @click="submit" v-if="operationType == 'Copy'" :loading="addLoading" 
						v-has="'copy'">提交</Button>&nbsp;&nbsp;
					<Button style="margin-left: 8px" @click="resetAll('editObj')">重置</Button>
				</div>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		getNoticeList,
		getNoticeDetails,
		addNotice,
		updateNotice,
		delNotice,
		getSceneList,
		changeDetailsStatus,
	} from "@/api/sms/notification";
	export default {
		components: {},
		data() {
			return {
				addLoading: false,
				searchObj: {
					SMSId: "", //短信模板id
					SMSName: "", //短信模板名称
				},
				tempEditObj: "", //临时存储
				SMSId: "", //仅作展示 避免前端修改提交
				editObj: {
					SMSId: "",
					SMSName: "",
					sceneList: [],
				},
				SMSEditFlag: false,
				SMSTitle: "通知短信新增",
				ruleEditValidate: {
					SMSName: [{
							required: true,
							message: "请输入模板短信名称",
							trigger: "blur"
						},
						{
							type: "string",
							max: 255,
							message: "最多输入255个字",
							trigger: "blur",
						},
					],
				},
				tableData: [], //列表信息
				selection: [], //多选
				selectionIds: [], //多选ids
				tableLoading: false,
				total: 0,
				pageSize: 10,
				page: 1,
				columns: [{
						type: "selection",
						minWidth: 60,
						maxWidth: 70,
						align: "center",
					},
					{
						title: "模板编号(ID)",
						key: "id",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "模板名称",
						key: "templateName",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "创建时间",
						key: "createTime",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "操作",
						slot: "action",
						minWidth: 220,
						maxWidth: 240,
						align: "center",
					},
				],
				operationType: "Add",
				insertLocal: 0, //记录光标位置
				statusPending: true,
				loading1: false,
				insertLists: {
					// 激活前短信
					2: {
						params: {
							paramSC: [], //激活前参数-简体中文
							paramTempSC: [], //激活前临时参数-简体中文
							paramTC: [], //激活前参数-繁体中文
							paramTempTC: [], //激活前临时参数-繁体中文
							paramEN: [], //激活前参数-英文
							paramTempEN: [], //激活前临时参数-英文
						},
						list: [{
								labelCn: "APN",
								labelTw: "APN",
								labelEn: "APN",
								value: "{APN}"
							},
							{
								labelCn: "套餐名称",
								labelTw: "套餐名稱",
								labelEn: "Package name",
								value: "{packageName}",
							},
							{
								labelCn: "位置",
								labelTw: "位置",
								labelEn: "Location",
								value: "{position}",
							},
							{
								labelCn: "运营商",
								labelTw: "運營商",
								labelEn: "Operators",
								value: "{operators}",
							},
						],
					},
					// 激活短信
					3: {
						params: {
							paramSC: [], //简体中文
							paramTempSC: [], //简体中文
							paramTC: [], //繁体中文
							paramTempTC: [], //繁体中文
							paramEN: [], //英文
							paramTempEN: [], //英文
						},
						list: [{
								labelCn: "套餐名称",
								labelTw: "套餐名稱",
								labelEn: "Package name",
								value: "{packageName}",
							},
							{
								labelCn: "套餐激活时间",
								labelTw: "套餐激活時間",
								labelEn: "active time",
								value: "{activeTime}",
							},
							{
								labelCn: "套餐失效时间",
								labelTw: "套餐失效時間",
								labelEn: "expire time",
								value: "{expireTime}",
							},
						],
					},

					// 使用中短信
					4: {
						params: {
							paramSC: [], //简体中文
							paramTempSC: [], //简体中文
							paramTC: [], //繁体中文
							paramTempTC: [], //繁体中文
							paramEN: [], //英文
							paramTempEN: [], //英文
						},
						list: [{
								labelCn: "APN",
								labelTw: "APN",
								labelEn: "APN",
								value: "{APN}"
							},
							{
								labelCn: "套餐名称",
								labelTw: "套餐名稱",
								labelEn: "Package name",
								value: "{packageName}",
							},
							{
								labelCn: "位置",
								labelTw: "位置",
								labelEn: "Location",
								value: "{position}",
							},
							{
								labelCn: "运营商",
								labelTw: "運營商",
								labelEn: "Operators",
								value: "{operators}",
							},
						],
					},

					// 激活套餐到期短信
					5: {
						params: {
							paramSC: [], //简体中文
							paramTempSC: [], //简体中文
							paramTC: [], //繁体中文
							paramTempTC: [], //繁体中文
							paramEN: [], //英文
							paramTempEN: [], //英文
						},
						list: [{
								labelCn: "套餐名称",
								labelTw: "套餐名稱",
								labelEn: "Package name",
								value: "{packageName}",
							},
							{
								labelCn: "套餐失效时间",
								labelTw: "套餐失效時間",
								labelEn: "expire time",
								value: "{expireTime}",
							},
						],
					},

					// 购买短信
					6: {
						params: {
							paramSC: [], //简体中文
							paramTempSC: [], //简体中文
							paramTC: [], //繁体中文
							paramTempTC: [], //繁体中文
							paramEN: [], //英文
							paramTempEN: [], //英文
						},
						list: [{
								labelCn: "商品名称",
								labelTw: "商品名稱",
								labelEn: "Package name",
								value: "{packageName}",
							},
							{
								labelCn: "订单编号",
								labelTw: "訂單編號",
								labelEn: "Order ID",
								value: "{orderId}",
							},
						],
					},

					// 物流短信
					7: {
						params: {
							paramSC: [], //物流参数-简体中文
							paramTempSC: [], //物流参数-简体中文
							paramTC: [], //物流参数-繁体中文
							paramTempTC: [], //物流参数-繁体中文
							paramEN: [], //物流参数-英文
							paramTempEN: [], //物流参数-英文
						},
						list: [{
								labelCn: "物流编号",
								labelTw: "物流編號",
								labelEn: "Number",
								value: "{number}",
							},
							{
								labelCn: "物流公司",
								labelTw: "物流公司",
								labelEn: "Company",
								value: "{company}",
							},
						],
					},

					// 购买套餐过期短信提醒
					8: {
						params: {
							paramSC: [], //简体中文
							paramTempSC: [], //简体中文
							paramTC: [], //繁体中文
							paramTempTC: [], //繁体中文
							paramEN: [], //英文
							paramTempEN: [], //英文
						},
						list: [{
								labelCn: "套餐名称",
								labelTw: "套餐名稱",
								labelEn: "Package name",
								value: "{packageName}",
							},
							{
								labelCn: "套餐到期时间",
								labelTw: "套餐到期時間",
								labelEn: "Expire Time",
								value: "{expireTime}",
							},
						],
					},
					// 实名制认证
					9: {
						params: {
							paramSC: [], //简体中文
							paramTempSC: [], //简体中文
							paramTC: [], //繁体中文
							paramTempTC: [], //繁体中文
							paramEN: [], //英文
							paramTempEN: [], //英文
						},
						list: [{
								labelCn: "套餐名称",
								labelTw: "套餐名稱",
								labelEn: "Package name",
								value: "{packageName}",
							},
							{
								labelCn: "newID",
								labelTw: "newID",
								labelEn: "newID",
								value: "{newID}",
							},
							{
								labelCn: "URL",
								labelTw: "URL",
								labelEn: "URL",
								value: "{URL}",
							},
							{
								labelCn: "FailureReason",
								labelTw: "FailureReason",
								labelEn: "FailureReason",
								value: "{FailureReason}",
							},
						],
					},
					// 实名制认证成功短信
					10: {
						params: {
							paramSC: [], //简体中文
							paramTempSC: [], //简体中文
							paramTC: [], //繁体中文
							paramTempTC: [], //繁体中文
							paramEN: [], //英文
							paramTempEN: [], //英文
						},
						list: [{
								labelCn: "newID",
								labelTw: "newID",
								labelEn: "newID",
								value: "{newID}",
							},
							{
								labelCn: "URL",
								labelTw: "URL",
								labelEn: "URL",
								value: "{URL}",
							},
							{
								labelCn: "FailureReason",
								labelTw: "FailureReason",
								labelEn: "FailureReason",
								value: "{FailureReason}",
							},
						],
					},
					// 认证失败短信
					11: {
						params: {
							paramSC: [], //简体中文
							paramTempSC: [], //简体中文
							paramTC: [], //繁体中文
							paramTempTC: [], //繁体中文
							paramEN: [], //英文
							paramTempEN: [], //英文
						},
						list: [{
								labelCn: "newID",
								labelTw: "newID",
								labelEn: "newID",
								value: "{newID}",
							},
							{
								labelCn: "URL",
								labelTw: "URL",
								labelEn: "URL",
								value: "{URL}",
							},
							{
								labelCn: "FailureReason",
								labelTw: "FailureReason",
								labelEn: "FailureReason",
								value: "{FailureReason}",
							},
						],
					},
					// 人工认证成功短信
					12: {
						params: {
							paramSC: [], //简体中文
							paramTempSC: [], //简体中文
							paramTC: [], //繁体中文
							paramTempTC: [], //繁体中文
							paramEN: [], //英文
							paramTempEN: [], //英文
						},
						list: [{
								labelCn: "newID",
								labelTw: "newID",
								labelEn: "newID",
								value: "{newID}",
							},
							{
								labelCn: "URL",
								labelTw: "URL",
								labelEn: "URL",
								value: "{URL}",
							},
							{
								labelCn: "FailureReason",
								labelTw: "FailureReason",
								labelEn: "FailureReason",
								value: "{FailureReason}",
							},
						],
					},
					// 人工认证失败短信
					13: {
						params: {
							paramSC: [], //简体中文
							paramTempSC: [], //简体中文
							paramTC: [], //繁体中文
							paramTempTC: [], //繁体中文
							paramEN: [], //英文
							paramTempEN: [], //英文
						},
						list: [{
								labelCn: "newID",
								labelTw: "newID",
								labelEn: "newID",
								value: "{newID}",
							},
							{
								labelCn: "URL",
								labelTw: "URL",
								labelEn: "URL",
								value: "{URL}",
							},
							{
								labelCn: "FailureReason",
								labelTw: "FailureReason",
								labelEn: "FailureReason",
								value: "{FailureReason}",
							},
						],
					},
					// 人工重新认证短信
					14: {
						params: {
							paramSC: [], //简体中文
							paramTempSC: [], //简体中文
							paramTC: [], //繁体中文
							paramTempTC: [], //繁体中文
							paramEN: [], //英文
							paramTempEN: [], //英文
						},
						list: [{
								labelCn: "newID",
								labelTw: "newID",
								labelEn: "newID",
								value: "{newID}",
							},
							{
								labelCn: "URL",
								labelTw: "URL",
								labelEn: "URL",
								value: "{URL}",
							},
							{
								labelCn: "FailureReason",
								labelTw: "FailureReason",
								labelEn: "FailureReason",
								value: "{FailureReason}",
							},
						],
					},
				},
			};
		},
		methods: {
			resetField(arr) {
				this.$refs["editObj"].fields.forEach((element) => {
					if (arr.includes(element.prop)) {
						element.resetField();
					}
				});
			},

			changeSwitch(flag, index) {
				this.initRules(index, flag);
				this.resetField([`sceneList.${index}.sceneName`]);
			},

			//场景状态切换
			statusChange(flag, index, detailId) {
				if (this.statusPending) {
					this.statusPending = false;
					// id 改为 detailId
					changeDetailsStatus({
							id: detailId,
							status: flag ? 1 : 2
						})
						.then((res) => {
							if (res.code === "0000") {
								this.$Notice.success({
									title: "操作提示",
									desc: flag ? "场景已成功开启" : "场景已成功关闭",
								});
								this.editObj.sceneList[index].status = flag ? 1 : 2;
								this.statusPending = true;
							}
						})
						.catch((err) => {
							this.editObj.sceneList[index].status = !flag ? 1 : 2;
							this.$Notice.error({
								title: "操作提示",
								desc: "操作失败"
							});
							this.statusPending = true;
						});
				}
			},

			//表格初始化
			init() {
				this.loadByPage(0);
				this.initScenceList();
			},

			initRules(index, status) {
				if (status == 1) {
					this.ruleEditValidate[`sceneList.${index}.sceneName`] = (
						rule,
						value,
						callback
					) => {
						if (
							!this.editObj.sceneList[index].contentCn &&
							!this.editObj.sceneList[index].contentTw &&
							!this.editObj.sceneList[index].contentEn
						) {
							callback(new Error("每种场景中文，繁體中文，English至少填写一项"));
						} else {
							callback();
						}
					};
				} else {
					this.ruleEditValidate[`sceneList.${index}.sceneName`] = null;
				}
			},

			initScenceList() {
				getSceneList().then((res) => {
					if (res.code === "0000") {
						let tempArr = [];
						res.data.forEach((element, index) => {
							tempArr.push({
								contentCn: "",
								contentTw: "",
								contentEn: "",
								receiveType: 1,
								sceneId: element.sceneId,
								sceneName: element.sceneName,
								status: 2,
							});
						});

						this.$set(this.editObj, "sceneList", tempArr);
					}
				});
			},

			//提交
			submit() {
				this.$refs["editObj"].validate((valid) => {
					if (valid) {
						this.addLoading = true;
						let isUpdate = this.operationType === "Update";
						let func = isUpdate ? updateNotice : addNotice;
						func({
								templateId: this.editObj.SMSId,
								templateName: this.editObj.SMSName,
								details: this.editObj.sceneList,
							})
							.then((res) => {
								if (res.code === "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.resetContentParam();
									this.SMSEditFlag = false;
									this.init();
									this.reset("editObj");

								}
							})
							.finally(() => {
								this.addLoading = false;
							});
					} else {
						this.$nextTick(() => {
							let errorEle = document.querySelector(".ivu-form-item-error-tip");
							let clientRect = errorEle.getBoundingClientRect();
							if (clientRect.bottom < 180) {
								errorEle.scrollIntoView({
									behavior: "smooth",
									block: "end"
								});
							}
						});
					}
				});
			},
			reset(name) {
				// console.log(this.$refs["editObj"])
				this.$refs[name].resetFields();
				this.resetContentParam();
				this.editObj.sceneList.forEach((ele, index) => {
					this.changeSwitch(0, index)
				})
			},
			resetAll(name) {
				this.$refs[name].resetFields();
				this.resetContentParam();
				// this.editObj.sceneList.forEach((ele, index) => {
				// 	this.changeSwitch(0, index)
				// })
			},
			//表格数据加载
			loadByPage(e) {
				if (e === 0) {
					this.page = 1;
				}
				getNoticeList({
						current: e,
						size: 10,
						templateId: this.searchObj.SMSId,
						templateName: this.searchObj.SMSName,
					})
					.then((res) => {
						if (res.code === "0000") {
							this.tableData = res.paging.data;
							this.total = res.paging.total;
						}
					})
					.catch((err) => {
						console.log(err);
					})
					.finally(() => {
						this.loading = false;
						this.loading1 = false;
					});
			},
			//搜索
			searchSMS() {
				this.loading1 = true;
				this.loadByPage(0);
			},
			//新增
			SMSAdd() {
				this.init()
				this.operationType = "Add";
				this.resetContentParam();
				this.SMSTitle = "通知短信新增";
				this.SMSEditFlag = true;
				this.reset("editObj");
			},
			//详情
			//编辑
			SMSCommon(row, type) {
				this.operationType = type;
				this.resetContentParam();
				this.SMSTitle =
					type === "Info" ?
					"短信模板详情" :
					type === "Update" ?
					"短信模板编辑" :
					type === "Copy" ?
					"短信模板复制" :
					this.SMSTitle;
				this.SMSEditFlag = true;
				this.reset("editObj");

				// TODO 联调时id替换成row.id
				getNoticeDetails(row.id).then((res) => {
					if (res.code === "0000") {
						this.editObj.sceneList = res.data;
						this.SMSId = row.id;
						this.editObj.SMSId = row.id;
						this.editObj.SMSName = row.templateName;
						this.editObj.createTime = row.createTime;

						res.data.forEach((ele, index) => {
							this.initRules(index, ele.status);
							this.fillCheckBox(
								ele.sceneId,
								ele.contentCn,
								ele.contentEn,
								ele.contentTw
							);
						});
					}
				});

				this.SMSId = row.SMSId;
			},

			fillCheckBox(id, contentCn, contentEn, contentTw) {
				let paramSC = [],
					paramTC = [],
					paramEN = [];

				if (this.insertLists[id]) {
					this.insertLists[id].list.forEach((ele) => {
						if (contentCn.indexOf(ele.value) > -1) {
							paramSC.push(ele.value);
						}
						if (contentTw.indexOf(ele.value) > -1) {
							paramTC.push(ele.value);
						}
						if (contentEn.indexOf(ele.value) > -1) {
							paramEN.push(ele.value);
						}
					});

					this.insertLists[id].params.paramSC = paramSC;
					this.insertLists[id].params.paramTempSC = paramSC;
					this.insertLists[id].params.paramTC = paramTC;
					this.insertLists[id].params.paramTempTC = paramTC;
					this.insertLists[id].params.paramEN = paramEN;
					this.insertLists[id].params.paramTempEN = paramEN;
				}
			},

			//删除
			SMSDel(id) {
				this.$Modal.confirm({
					title: "确认删除？",
					onOk: () => {
						//id
						delNotice([id])
							.then((res) => {
								if (res.code === "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.init();
								}
							})
							.catch((err) => {
								this.$Notice.error({
									title: "操作提示",
									desc: "操作失败",
								});
							});
					},
				});
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				this.selectionIds = [];
				selection.map((value, index) => {
					this.selectionIds.push(value.id);
				});
			},
			//批量删除
			deleteList() {
				var len = this.selection.length;
				if (len < 1) {
					this.$Message.warning("请至少选择一条记录");
					return;
				}
				this.$Modal.confirm({
					title: "确认删除？",
					onOk: () => {
						delNotice(this.selectionIds)
							.then((res) => {
								if (res.code === "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.selection = [];
									this.selectionIds = [];
									this.init();
								}
							})
							.catch((err) => {
								this.$Notice.error({
									title: "操作提示",
									desc: "操作失败",
								});
							});
					},
				});
			},
			gotest() {
				this.$router.push({
					name: "notificationAdd",
				});
			},

			/*取消插入的模板
  sceneId 场景id
  index  索引
  flag 当前选中的数组
  key 替换的字段

*/

			filterContent(sceneId, index, flag, key) {
				const tempArr = this.insertLists[sceneId].list.map((ele) => {
					return ele.value;
				});

				tempArr.forEach((ele) => {
					if (!flag.includes(ele)) {
						this.editObj.sceneList[index][key] = this.editObj.sceneList[index][
							key
						].replace(ele, "");
					}
				});
			},

			//参数置空
			resetContentParam() {
				this.insertLocal = 0;
				Object.keys(this.insertLists).forEach((key) => {
					this.insertLists[key].params.paramSC = [];
					this.insertLists[key].params.paramTempSC = [];
					this.insertLists[key].params.paramTC = [];
					this.insertLists[key].params.paramTempTC = [];
					this.insertLists[key].params.paramEN = [];
					this.insertLists[key].params.paramTempEN = [];
				});
			},
			//动态参数
			setContentParamSC(e, sceneId, index, value) {
				var len = e.length; //已勾长度
				var before = value.substring(0, this.insertLocal); //光标前
				var after = value.substring(this.insertLocal, value.length); //光标后
				//激活前-简体中文
				if (len >= this.insertLists[sceneId].params.paramTempSC.length) {
					//当前选中参数个数大于上次选中参数个数，说明此次事件为勾选事件，修改模板内容


					this.editObj.sceneList[index].contentCn = before + e[len - 1] + after; //模板内容拼接
					this.insertLocal = this.editObj.sceneList[index].contentCn.length;

				} else {
					this.filterContent(sceneId, index, e, "contentCn");
				}
				this.insertLists[sceneId].params.paramTempSC = e;
			},
			//动态参数
			setContentParamTC(e, sceneId, index, value) {
				var len = e.length; //已勾长度
				var before = value.substring(0, this.insertLocal); //光标前
				var after = value.substring(this.insertLocal, value.length); //光标后
				//激活前-简体中文
				if (len >= this.insertLists[sceneId].params.paramTempTC.length) {
					//当前选中参数个数大于上次选中参数个数，说明此次事件为勾选事件，修改模板内容
					this.editObj.sceneList[index].contentTw = before + e[len - 1] + after; //模板内容拼接
					this.insertLocal = this.editObj.sceneList[index].contentTw.length;

				} else {
					this.filterContent(sceneId, index, e, "contentTw");
				}
				this.insertLists[sceneId].params.paramTempTC = e;
			},
			//动态参数
			setContentParamEN(e, sceneId, index, value) {
				var len = e.length; //已勾长度
				var before = value.substring(0, this.insertLocal); //光标前
				var after = value.substring(this.insertLocal, value.length); //光标后

				//激活前-简体中文
				if (len >= this.insertLists[sceneId].params.paramTempEN.length) {
					//当前选中参数个数大于上次选中参数个数，说明此次事件为勾选事件，修改模板内容
					this.editObj.sceneList[index].contentEn = before + e[len - 1] + after; //模板内容拼接
					this.insertLocal = this.editObj.sceneList[index].contentEn.length;

				} else {
					this.filterContent(sceneId, index, e, "contentEn");
				}
				this.insertLists[sceneId].params.paramTempEN = e;
			},
			handleInputBlur() {
				this.insertLocal = event.srcElement.selectionStart;
			},
		},
		mounted() {
			// this.tableData = res.data
			this.tempEditObj = JSON.stringify(this.editObj);
			this.init();
		},
	};
</script>

<style>
	.inputSty {
		width: 200px;
	}

	.labelSty {
		display: block;
		text-align: center;
	}

	.inputCenter {
		padding: 0 5px;
		text-align: center;
	}

	.checkbox-group {
		display: flex;
		justify-content: left;
		align-items: center;
		flex-wrap: wrap;
	}

	.checkbox-group .ivu-checkbox-wrapper {
		flex-basis: 40%;
	}
</style>
