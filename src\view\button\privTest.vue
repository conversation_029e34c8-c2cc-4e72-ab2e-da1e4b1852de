<template>
	<div>
		<Card>
			<div class="search_head">
				<Button v-has="'edit'" type="primary" icon="md-search" @click="">edit</Button>&nbsp;&nbsp;
				<Button v-has="'add'" icon="ios-cloud-upload" type="success" @click="">add</Button>&nbsp;&nbsp;
        <Button v-has="'delete'" type="primary" icon="md-search" @click="">delete</Button>&nbsp;&nbsp;
			</div>
		</Card>

	</div>
</template>

<script>
export default {
  data () {
    return {

    }
  },
  watch: {
  },
  computed: {},
  methods: {

  },
  mounted () {
  },
  watch: {
  }
}
</script>
<style>

</style>
