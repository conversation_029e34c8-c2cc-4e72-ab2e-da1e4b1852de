<template>

  <!--  账户列表  -->
  <div>
    <Card>
      <div class="search_head">
        <!-- <div class="search_head" style="width: 100%; "> -->
        <Select @on-clear="clean" @on-change="getWholesalerList" v-model="userType" clearable placeholder="请选择用户类型"
          style="width: 200px ;margin-right: 10px;">
          <Option v-for="item in userTypeList" :value="item.key" :key="item.key">{{ item.value }}</Option>
        </Select>
        <Select v-model="wholesalerId" clearable placeholder="请选择客户" style="width: 200px ;margin-right: 10px;">
          <Option v-for="item in wholesalerList" :value="item.key" :key="item.key">{{ item.value }}</Option>
        </Select>
        <!-- <Select @on-change="queryTypeExchange" v-model="queryType" :clearable="false" placeholder="请选择统计方式"
          style="width: 200px;text-align: left;">
          <Option v-for="item in queryTypeList" :value="item.key" :key="item.key">{{ item.value }}</Option>
        </Select> -->

        <!-- </div> -->
        <!-- <div class="search_head" style="width: 100%;"> -->


        <!-- 年统计 -->
        <!-- <div v-if="queryType == '0'">
          <DatePicker @on-change="getStartTime" :value="startTime" type="year" :editable="false" placeholder="选择开始时间"
            style="width: 200px; margin-left: 10px;"></DatePicker>
          <DatePicker @on-change="getEndTime" :value="endTime" type="year" placeholder="选择结束时间"
            style="width: 200px;margin-left: 10px;">
          </DatePicker>
        </div> -->


        <!-- 季度统计 -->
        <!-- <div v-if="queryType == '1'">

          <DatePicker @on-change="getStartTime" type="year" :editable="false" placeholder="开始年份"
            style="width: 100px;margin-left: 10px;"></DatePicker>
          <Select @on-change="getSeason" v-model="startSeason" :clearable="true" placeholder="开始季度"
            style="width: 100px;text-align: left; margin: 0 10px;">
            <Option v-for="item in seasonList" :value="item.key" :key="item.key">{{ item.value }}</Option>
          </Select>

          至

          <DatePicker @on-change="getEndTime" type="year" :editable="false" placeholder="结束年份"
            style="width: 100px; margin-left: 10px;">
          </DatePicker>
          <Select @on-change="getSeason" v-model="endSeason" :clearable="true" placeholder="结束季度"
            style="width: 100px;margin-left: 10px;">
            <Option v-for="item in seasonList" :value="item.key" :key="item.key">{{ item.value }}</Option>
          </Select>

        </div> -->


        <!-- 月统计 -->
        <div>
          <DatePicker @on-change="getStartTime" type="month" :editable="false" placeholder="选择开始月"
            style="width: 200px;margin: 0 10px;"></DatePicker>
          <DatePicker @on-change="getEndTime" type="month" :editable="false" placeholder="选择结束月" style="width: 200px">
          </DatePicker>
        </div>
        <Button v-has="'search'" type="primary" icon="md-search" @click="searchByCondition()"
          style="margin: 0 10px;">搜索</Button>
        <!-- </div> -->

      </div>
      <div style="margin-top:20px">
        <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading"></Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
      </div>
    </Card>
  </div>
</template>

<script>
  import {
    search
  } from '@/api/cdr/cdrPrice'
  import {
    getOperatorList,
  } from '@/api/public/util'
  import publicData from '../../../libs/publicData.js'
  export default {
    data() {
      return {
        queryTypeList: [],
        wholesalerId: '',
        userTypeList: [],
        userType: '',
        queryType: null,
        wholesalerList: [],
        seasonList: [],
        startSeason: null,
        endSeason: null,
        columns: [{
            title: '月份',
            key: 'time',
            align: 'center'
          },
          {
            title: '企业/运营商',
            key: 'wholesalerName',
            align: 'center'
          },
          {
            title: '国家/地区',
            key: 'local',
            align: 'center'
          },
          {
            title: '流量总量',
            key: 'flowTotal',
            align: 'center'
          },
          {
            title: '单价/G',
            key: 'UnitPrice',
            align: 'center'
          },
          {
            title: '币种',
            key: 'currency',
            align: 'center'
          },
          {
            title: '金额',
            key: 'amount',
            align: 'center'
          }

        ],
        tableData: [{
            wholesalerName: '客户',
            local: '中国',
            time: '2021',
            localId: '101',
            flowTotal: '1000G',
            UnitPrice: '100',
            currency: '人民币',
            currencyType: '0',
            amount: '1',
          },
          {
            wholesalerName: '客户2',
            local: '日本',
            localId: '102',
            time: '2021',
            flowTotal: '1000G',
            UnitPrice: '100',
            currency: '美元',
            currencyType: '0',
            amount: '1',
          }
        ],
        loading: false,
        currentPage: 1,
        page: 0,
        startTime: null,
        endTime: null,
        total: 0
      }
    },
    computed: {

    },
    methods: {
      // 获取列表
      goPageFirst: function(page) {
        this.page = page
        this.loading = true
        var data = {
          pageNumber: page,
          pageSize: 10,
          userType: this.userType,
          startTime: this.startTime,
          endTime: this.endTime,
          // queryType: this.queryType,
          // startSeason: this.startSeason,
          // endSeason: this.endSeason,
          wholesalerId: this.wholesalerId
        }
        search(data).then(res => {
          if (res && res.code == '0000') {
            this.tableData = res.data.records
            this.total = res.data.total
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
      //根据查询方式查询用户列表
      getWholesalerList: function(type) {
        getOperatorList({
          userType: type
        }).then(res => {
          if (res && res.code == '0000') {
            this.wholesalerList = res.data
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
      clean: function() {
        this.wholesalerList = []
      },
      searchByCondition: function() {
        let params = new Array();
        let desc = '请正确的填写查询区间!'
        try {
          // if (!this.queryType) {
          //   desc = '请选择查询方式！'
          //   throw desc
          // }
          // if (!this.startTime || !this.endTime) {
          //   throw desc
          // }
          // if (this.queryType == '1' && (!this.startSeason || !this.endSeason)) {
          //   throw desc
          // }
          if (this.endTime && !this.startTime) {
            throw desc
          }
          if (this.startTime > this.endTime) {
            throw desc
          }
          // if (this.queryType == '1' && this.startTime == this.endTime && this
          //   .startSeason > this.endSeason) {
          //   throw desc
          // }
          this.goPageFirst(0)
        } catch (e) {
          this.$Notice.warning({
            title: '温馨提示',
            desc: desc
          })
        }

      },
      getSeason: function(val) {
        console.log(val)
      },
      queryTypeExchange: function(value) {
        this.startSeason = null
        this.startTime = null
        this.endSeason = null
        this.endTime = null
        this.columns.splice(0, 1);
        this.columns.unshift({
          title: value === '0' ? '年份' : (value === '1' ? '季度' : '月份'),
          key: 'time',
          align: 'center'
        })
      },
      getStartTime: function(time) {
        this.startTime = time
      },
      getEndTime: function(time) {
        this.endTime = time
      },
      // 分页跳转
      goPage(page) {
        this.goPageFirst(page)
      },
      cancelModal: function() {
        this.editModal = false
      }
    },
    mounted() {
      // const nowDate = new Date()
      // this.startTime = nowDate.getFullYear().toString()
      // this.endTime = nowDate.getFullYear().toString()
      this.userTypeList = publicData.userTypeList
      this.queryTypeList = publicData.queryTypeList
      this.seasonList = publicData.seasonList
      this.goPageFirst(0)
    },
    watch: {

    }
  }
</script>
<style>
  .search_head {
    width: 100%;
    display: flex;
    text-align: center;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .search-btn {
    width: 100px !important;
  }

  .input_modal {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    width: 100%;
  }
</style>
