<template>

  <!--  账户列表  -->
  <div>
    <Card>
      <div class="search_head">
        <!-- <Input v-model="localId" placeholder="请输入国家/地区名称..." clearable style="width: 200px ;margin-right: 10px;" />
        <Input v-model="wholesalerName" placeholder="请输入客户名称..." clearable style="width: 200px ;margin-right: 10px;" /> -->
         <Select @on-clear="clean" @on-change="getOperatorList" v-model="localId" placeholder="请选择国家/地区" clearable style="width: 200px ;margin-right: 10px;">
          <Option v-for="item in localList" :value="item.key" :key="item.key">{{ item.name }}</Option>
        </Select>
        <Select v-model="operatorId" placeholder="请选择运营商" clearable style="width: 200px ;margin-right: 10px;">
          <Option v-for="item in operatorList" :value="item.key" :key="item.key">{{ item.name }}</Option>
        </Select>
        <Button v-has="'search'" type="primary" icon="md-search" @click="searchByCondition()"
          style="margin-right: 10px;">搜索</Button>

        <Button v-has="'add'" type="success" icon="ios-add" @click="showAdd">新增批价规则</Button>

      </div>

      <div style="margin-top:20px">
        <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
          <template slot-scope="{ row }" slot="status">
            {{ row.status === '0' ?'已通过': '未通过'}}
          </template>
          <!-- 0:已通过；1：未通过 -->
          <template slot-scope="{ row, index }" slot="check">
            <Button v-if="row.status === '1'" v-has="'edit'" type="warning" size="small" style="margin-right: 10px"
              @click="changeStatus(row,'0')">通过</Button>
            <Button v-if="row.status === '1'" v-has="'edit'" type="error" size="small"
              @click="changeStatus(row,'1')">不通过</Button>
             <span v-else>...</span>
          </template>
          <template slot-scope="{ row, index }" slot="action">
            <Button v-has="'update'" type="primary" size="small" style="margin-right: 10px"
              @click="editPrice(row)">{{$t('common.edit')}}</Button>
            <Button type="error" size="small" @click="deleteWarning(row)">{{$t('common.del')}}</Button>
          </template>
        </Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
      </div>
    </Card>
    <!-- 编辑模态框 -->
    <Modal title="编辑批价信息" v-model="editModal" :mask-closable="false" @on-cancel="cancelModal">
      <Form v-if="editModal" ref="editForm" :model="modalData" :rules="rules" @keydown.enter.native="userAddOrEdit">
        <FormItem prop="wholesalerName">
          <div class="input_modal">
            <span style="width: 2%;color: red;">*</span><span style="width: 20%;">客户名称：</span>
            <Input v-model="modalData.wholesalerName" placeholder="请输入用户账号..." :disabled="reset" style="width: 80%;" />
          </div>
        </FormItem>
        <FormItem prop="localId">
          <div class="input_modal">
            <!-- <span style="width: 2%;color: red;">*</span><span style="width: 20%;">选择大洲：</span>
            <Select v-model="modalData.continent" placeholder="请选择大洲" style="width: 39% ;margin-right: 2%;">
              <Option v-for="item in continentList" :value="item.key" :key="item.key">{{ item.value }}</Option>
            </Select> -->
            <span style="width: 2%;color: red;">*</span><span style="width: 20%;">选择国家/地区：</span>

            <Select v-model="modalData.localId" placeholder="请选择国家/地区" multiple style="width: 80%;">
              <Option v-for="item in localList" :value="item.key" :key="item.key">{{ item.name }}</Option>
            </Select>


            <!-- <Select v-model="modalData.localId" placeholder="请选择国家/地区" style="width: 80%" :filterable="true"
              :filter-by-label="true">
              <Option v-for="item in localList" :value="item.key" :key="item.key">{{ item.name }}</Option>
            </Select> -->
          </div>
        </FormItem>
        <FormItem prop="UnitPrice">
          <div class="input_modal">
            <span style="width: 2%;color: red;">*</span><span style="width: 20%;">设置单价：</span>
            <Input v-model="modalData.UnitPrice" placeholder="请输入单价..." style="width: 80%;" />
          </div>
        </FormItem>
        <FormItem prop="currencyType">
          <div class="input_modal">
            <span style="width: 2%;color: red;">*</span><span style="width: 20%;">选择币种：</span>
            <Select v-model="modalData.currencyType" placeholder="请选择币种" style="width: 80%">
              <Option v-for="item in currencyList" :value="item.key" :key="item.key">{{ item.value }}</Option>
            </Select>
          </div>
        </FormItem>
      </Form>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
        <Button @click="cancelModal">取消</Button>
        <Button type="primary" @click="save">确定</Button>
      </div>
    </Modal>

  </div>
</template>

<script>
  import {
    search,
    edit,
    add,
    dele,
    changeStatus
  } from '@/api/cdr/cdrPrice'
  import {
    getOperatorList,
  } from '@/api/public/util'
  import localData from '../../../libs/localData.js'
  import publicData from '../../../libs/publicData.js'
  export default {
    data() {
      var validateLocalId = (rule, value, callback) => {
        if (!value || value.length == 0) {
          callback(new Error('请选择国家/地区'));
        } else {
          callback();
        }
      };
      var validateWholesalerName = (rule, value, callback) => {
        if (!value || value.replace(/\s/g, '') == '') {
          callback(new Error('请输入客户名称'));
        } else {
          callback();
        }
      };
      var validateUnitPrice = (rule, value, callback) => {
        if (!value || value.length == 0) {
          callback(new Error('请设置单价'));
        } else {
          callback();
        }
      };
      var validateCurrency = (rule, value, callback) => {
        if (!value || value.length == 0) {
          callback(new Error('请设置币种'));
        } else {
          callback();
        }
      };
      return {
        localList: [],
        loading4: false,
        operatorList:[],
        currencyList: [],
        continentList: [],
        editModal: false,
        reset: false,
        localId: '',
        operatorId: '',
        columns: [{
            title: '客户名称',
            key: 'wholesalerName',
            align: 'center'
          },
          {
            title: '国家/地区',
            key: 'local',
            align: 'center'
          },
          {
            title: '单价/G',
            key: 'UnitPrice',
            align: 'center'
          },
          {
            title: '币种',
            key: 'currency',
            align: 'center'
          },
          {
            title: '审批状态',
            slot: 'status',
            align: 'center'
          },
          {
            title: '审核',
            slot: 'check',
            align: 'center'
          },

          {
            title: '操作',
            slot: 'action',
            align: 'center'
          }
        ],
        tableData: [{
            wholesalerName: '客户',
            wholesalerId:'iii',
            local: '中国',
            localId: '101',
            UnitPrice: '100',
            currency: '人民币',
            currencyType: '0',
            status: '0',
          },
          {
            wholesalerName: '客户',
            wholesalerId:'iii',
            local: '日本',
            localId: '102',
            UnitPrice: '100',
            currencyType: '1',
            currency: '美元',
            status: '1',
          }
        ],
        modalData: null,
        loading: false,
        currentPage: 1,
        page: 0,
        startTime: null,
        endTime: null,
        total: 0,
        rules: {
          wholesalerName: [{
            validator: validateWholesalerName,
            trigger: 'blur'
          }],
          localId: [{
            validator: validateLocalId,
            trigger: 'blur'
          }],

          UnitPrice: [{
            validator: validateUnitPrice,
            trigger: 'blur'
          }],
          currencyType: [{
            validator: validateCurrency,
            trigger: 'blur'
          }]
        }
      }
    },
    computed: {},
    methods: {
      // 获取列表
      goPageFirst: function(page) {
        this.page = page
        this.loading = true
        var data = {
          pageNumber: page,
          pageSize: 10,
          localId: this.localId,
          operatorId: this.operatorId
        }
        search(data).then(res => {
          if (res && res.code == '0000') {
            this.tableData = res.data.records
            this.total = res.data.total
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
      clean:function(){
        this.operatorList = []
      },
      getOperatorList:function(localId){
        getOperatorList({
          localId:localId,
        }).then(res => {
          if (res && res.code == '0000') {
            this.operatorList = res.data
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
      editPrice: function(data) {
        this.reset = true
        this.modalData = data
        this.editModal = true
      },
      showAdd: function() {
        this.reset = false
        this.modalData = {
          wholesalerId: '',
          wholesalerName: '',
          localId: [],
          UnitPrice: '',
          currencyType: '',
        }
        this.editModal = true
      },
      save: function() {
        this.$refs.editForm.validate((valid) => {
          if (valid) {
            if (this.reset) { // 修改
              this.edit()
            } else {
              this.add()
            }
          }
        })
      },
      add: function() {
        let argn = []
        for (var i = 0; i < this.modalData.localId.length; i++) {
          argn.push(this.modalData.localId[i])
        }
        this.modalData.localId = argn
        // 将国家变成数组
        add(this.modalData).then(res => {
          if (res && res.code == '0000') {
            this.$Notice.success({
              title: '操作提醒',
              desc: '新增批价规则成功！'
            })
            this.editModal = false
            this.goPageFirst(0)
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          // this.userModal = false
          this.loading = false
        })
      },
      // 修改账户信息
      edit: function() {
        let argn = []
        for (var i = 0; i < this.modalData.localId.length; i++) {
          argn.push(this.modalData.localId[i])
        }
        this.modalData.localId = argn
        edit(this.modalData).then(res => {
          if (res && res.code == '0000') {
            this.$Notice.success({
              title: '操作成功',
              desc: '修改批价规则成功！'
            })
            this.editModal = false
            this.goPageFirst(0)
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          // this.userModal = false
          this.loading = false
        })
      },
      delete:function(row){
        dele({
          wholesalerId: row.wholesalerId,
          status: status
        }).then(res => {
          if (res && res.code == '0000') {
            this.$Notice.success({
              title: '操作成功',
              desc: '删除成功！'
            })
            this.editModal = false
            this.goPageFirst(0)
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          // this.userModal = false
          this.loading = false
        })
      },
      changeStatus: function(row, status) {
        changeStatus({
          wholesalerId: row.wholesalerId,
          status: status
        }).then(res => {
          if (res && res.code == '0000') {
            this.$Notice.success({
              title: '操作成功',
              desc: '状态已变更！'
            })
            this.editModal = false
            this.goPageFirst(0)
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          // this.userModal = false
          this.loading = false
        })
      },
      searchByCondition: function() {
        this.goPageFirst(0)
      },
      // 分页跳转
      goPage(page) {
        this.goPageFirst(page)
      },
      cancelModal: function() {
        this.editModal = false
      },
      deleteWarning: function(params) {
        this.$Notice.warning({
          title: '操作提醒',
          name: 'delete',
          desc: '请确定删除已选中账户？',
          render: h => {
            return h('div', [
              '请确定删除已选中批量规则？',
              h('br'),
              h('div', [
                h('Button', {
                  props: {
                    type: 'dashed',
                    size: 'small'
                  },
                  style: {
                    marginTop: '10px',
                    marginLeft: '130px'
                  },
                  on: {
                    click: () => {
                      this.$Notice.close('delete')
                    }
                  }
                }, '取消'),
                h('Button', {
                  props: {
                    type: 'error',
                    size: 'small'
                  },
                  style: {
                    marginTop: '10px',
                    marginLeft: '10px'
                  },
                  on: {
                    click: () => {
                      this.$Notice.close('delete')
                      this.delete(params)
                    }
                  }
                }, '删除')
              ])
            ])
          },
          duration: 0
        })
      }
    },
    mounted() {
      var list = localData.localList
      this.currencyList = publicData.currencyList
      for (var n = 0; n < list.length; n++) {
        this.localList = this.localList.concat(list[n].value)
      }
      let lang = this.$i18n.locale
      let sortArray = this.localList.sort(function(str1, str2) {
        return str1.name.localeCompare(str2.name, lang.split('-')[0]);
      });
      this.goPageFirst(0)
    },
    watch: {

    }
  }
</script>
<style>
  .search_head {
    width: 100%;
    display: flex;
    text-align: center;
    align-items: center;
    justify-content: flex-start;
  }

  .search-btn {
    width: 100px !important;
  }

  .input_modal {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    width: 100%;
  }
</style>
