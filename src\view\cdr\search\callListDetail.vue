<template>
	<Card>
		<Button v-has="'export'"   type="success" :loading="downloading" icon="ios-download" @click="downloadFile()"  style="margin-right: 10px;">导出</Button>
		<Button    @click="back()" >返回</Button>&nbsp;&nbsp;
		<div style="margin-top:20px" v-if="searchMode=='1'||searchMode=='2'||searchMode=='3'" >
		  <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
		  </Table>
		</div>
		<div style="margin-top:20px" v-if="searchMode=='4'" >
		  <Table :columns="columns1" :data="tableData" :ellipsis="true" :loading="loading">
		  </Table>
		</div>
		<div style="margin-top:20px" v-if="searchMode=='5'" >
		  <Table :columns="columns2" :data="tableData" :ellipsis="true" :loading="loading">
		  </Table>
		</div>
		<div style="margin-top:20px" v-if="searchMode=='6'" >
		  <Table :columns="columns3" :data="tableData" :ellipsis="true" :loading="loading">
		  </Table>
		</div>
		
		<div class="table-botton" style="margin-top:15px">
		  <Page :total="total" :current.sync="currentPage" :page-size="50" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 导出提示 -->
		<Modal  v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
		  <div style="align-items: center;justify-content:center;display: flex;">
			  <Form  label-position="left"  :label-width="150" style=" align-items: center;justify-content:center;" >
			   		  <h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					  <FormItem label="你本次导出任务ID为:">
					  	<ul>
					  		<li id="space" v-for="(taskId,i) in taskIds" :key="taskIds.i">
					  			{{taskId}}
					  		</li>
					  	</ul>
					  	<div v-if="remind">
					  		<span>……</span>
					  	</div>
					  </FormItem>
					  <FormItem label="你本次导出的文件名为:">
					  	<ul style="margin-bottom: 15px;">
					  		<li id="space" v-for="(taskName,i) in taskNames" :key="taskNames.i">
					  			{{taskName}}
					  		</li>
					  	</ul>
					  	<div  v-if="remind">
					  		<span>……</span>
					  	</div>
					  </FormItem>
						<span>{{taskName}}</span>
			   		  </FormItem>
					  <span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
			   </Form>
		  </div>
		  
		  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
		    <Button @click="cancelModal">取消</Button>
		    <Button type="primary" @click="Goto">立即前往</Button>
		  </div>
		</Modal>
	</Card>
</template>

<script>
	import {
	  exportdetail,
	  exportusedTimedetail,
	  Cdrdetailexport,
	  operatorsexport,
	  perioddetail,
	  usedTimedetail,
	  numberCdrdetail,
	  operatorCdrdetail,
	  numberdetail,
	  corpdetailexport,
	  detailExport,
	  corpdetail,
	} from '@/api/cdr/cdrSearch'
	const math = require('mathjs')
	export default {
	  data() {
	    return {
			taskId:'',
			taskName:'',
			remind: false,
			taskIds: [],
			taskNames: [],
			exportModal:false,
			searchMode:'',
			downloading:false,
			loading:false,
			columns: [{
				    title: '日期',
				    key: 'statTime',
				    align: 'center',
					minWidth: 200,
				  },
				{
			    title: 'IMSI',
			    key: 'imsi',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: 'MSISDN',
			    key: 'msisdn',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: 'ICCID',
			    key: 'iccid',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '国家/地区',
			    key: 'countryCn',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '落地运营商',
			    key: 'operatorName',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '资源供应商',
			    key: 'supplierName',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '流量总量(MB)',
			    key: 'flowByteTotal',
			    align: 'center',
				minWidth: 200,
			    // render: (h, params) => {
			    // 	const row = params.row
			    // 	const text = parseFloat(math.multiply(math.bignumber(row.flowByteTotal), 1024).toFixed(5)).toString()
			    // 	return h('label', text)
			    // }
			  },
			],
			columns1: [{
				    title: '日期',
				    key: 'statTime',
				    align: 'center',
					minWidth: 200,
				  },{
			    title: 'IMSI',
			    key: 'imsi',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: 'MSISDN',
			    key: 'msisdn',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: 'ICCID',
			    key: 'iccid',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '渠道商名称',
			    key: 'corpName',
			    align: 'center',
				minWidth: 200,
			  },
				{
				  title: '国家/地区',
				  key: 'countryCn',
				  align: 'center',
				minWidth: 200,
				},
			  {
			    title: '流量总量(MB)',
			    key: 'flowByteTotal',
			    align: 'center',
				minWidth: 200,
			    // render: (h, params) => {
			    // 	const row = params.row
			    // 	const text = parseFloat(math.multiply(math.bignumber(row.flowByteTotal), 1024).toFixed(5)).toString()
			    // 	return h('label', text)
			    // }
			  },
			],
			columns2:[{
				    title: '日期',
				    key: 'statTime',
				    align: 'center',
					minWidth: 200,
				  },{
			    title: 'IMSI',
			    key: 'himsi',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: 'VIMSI',
			    key: 'imsi',
			    align: 'center',
				minWidth: 200,
			  },
			  
			  {
			    title: '套餐ID',
			    key: 'packageId',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '套餐名称',
			    key: 'packageName',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '国家/地区',
			    key: 'countryCn',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '渠道商名称',
			    key: 'corpName',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '流量总量(MB)',
			    key: 'flowByteTotal',
			    align: 'center',
				minWidth: 200,
			    // render: (h, params) => {
			    // 	const row = params.row
			    // 	const text = parseFloat(math.multiply(math.bignumber(row.flowByteTotal), 1024).toFixed(5)).toString()
			    // 	return h('label', text)
			    // }
			  },
			],
			columns3:[{
				    title: '日期',
				    key: 'statTime',
				    align: 'center',
					minWidth: 200,
				  },{
			    title: 'IMSI',
			    key: 'himsi',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: 'VIMSI',
			    key: 'imsi',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '套餐ID',
			    key: 'packageId',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '套餐名称',
			    key: 'packageName',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '国家/地区',
			    key: 'countryCn',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '资源供应商',
			    key: 'supplierName',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '流量总量(MB)',
			    key: 'flowByteTotal',
			    align: 'center',
				minWidth: 200,
			  			// render: (h, params) => {
			  			// 	const row = params.row
			  			// 	const text = parseFloat(math.multiply(math.bignumber(row.flowByteTotal), 1024).toFixed(5)).toString()
			  			// 	return h('label', text)
			  			// }
			  },
			  {
			    title: '套餐开始时间',
			    key: 'activeTime',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '套餐结束时间',
			    key: 'expireTime',
			    align: 'center',
				minWidth: 200,
			  },
			  {
			    title: '套餐天数',
			    key: 'validDate',
			    align: 'center',
				minWidth: 200,
			  },
			  // {
			  //   title: '套餐实际使用天数',
			  //   key: 'useDays',
			  //   align: 'center'
			  // },
			],
			tableData: [],
			callListinfo:[],
			total: 0,
			currentPage: 1,
			page: 1, 
			startTime:'',
			endTime:''
			
		}
	 },
	 methods: {
		 goPageFirst(page){
			this.page=page
			this.loading=true
			if(this.searchMode==='1'){//按国家/运营商查询
				operatorCdrdetail({
				  beginTime:this.startTime,
				  endTime:this.endTime,
				  imsi:this.callListinfo.imsi,
				  mcc:this.callListinfo.mcc,
				  operatorName:this.callListinfo.operatorName,
				  supplierName:this.callListinfo.supplierName,
				  pageNumber:page,
				  pageSize:50,
				}).then(res => {
					if (res && res.code == '0000') {
						this.currentPage = page
						this.tableData = res.data
						this.total = res.count
						this.loading = false
					}
				}).catch(err => this.loading = false)
			}else if(this.searchMode==='2'){//按号码查询
				numberCdrdetail({
				  beginTime:this.startTime,
				  endTime:this.endTime,
				  imsi:this.callListinfo.imsi,
				  mcc:this.callListinfo.mcc,
				  operatorName:this.callListinfo.operatorName,
				  supplierName:this.callListinfo.supplierName,
				  pageNumber:page,
				  pageSize:50,
				}).then(res => {
					if (res && res.code == '0000') {
						this.currentPage = page
						this.tableData = res.data
						this.total = res.count
						this.loading = false
					}
				}).catch(err => this.loading = false)
			}else if(this.searchMode==='3'){//按号段查询
				numberdetail({
				  beginTime:this.startTime,
				  endTime:this.endTime,
				  imsi:this.callListinfo.imsi,
				  mcc:this.callListinfo.mcc,
				  operatorName:this.callListinfo.operatorName,
				  supplierName:this.callListinfo.supplierName,
				  pageNumber:page,
				  pageSize:50,
				}).then(res => {
					if (res && res.code == '0000') {
						this.currentPage = page
						this.tableData = res.data
						this.total = res.count
						this.loading = false
					}
				}).catch(err => this.loading = false)
			}else if(this.searchMode==='4'){//按渠道商查询
				corpdetail({
				  beginTime:this.startTime,
				  endTime:this.endTime,
				  imsi:this.callListinfo.imsi,
				  mcc:this.callListinfo.mcc,
				  corpId:this.callListinfo.corpId,
				  pageNumber:page,
				  pageSize:50,
				}).then(res => {
					if (res && res.code == '0000') {
						this.currentPage = page
						this.tableData = res.data
						this.total = res.count
						this.loading = false
					}
				}).catch(err => this.loading = false)
			}else if(this.searchMode==='5'){//按套餐周期查询
				perioddetail({
				  packageIdOrName:this.mealName,
				  pageNum:this.page,
				  pageSize:50,
				  mcc:this.callListinfo.mcc,
				  imsi:this.callListinfo.himsi,
				  vimsi:this.callListinfo.imsi,
				  packageUniqueId:this.callListinfo.packageUniqueId
				}).then(res => {
					if (res && res.code == '0000') {
						this.currentPage = page
						this.tableData = res.data
						this.total = res.count
						this.loading = false
					}
				}).catch(err => this.loading = false)
			}else if(this.searchMode==='6'){//按套餐使用时间查询
				usedTimedetail({
				  beginTime:this.startTime,
				  endTime:this.endTime,
				  packageIdOrName:this.mealName,
				  pageNum:this.page,
				  pageSize:50,
				  mcc:this.callListinfo.mcc,
				  packageUniqueId:this.callListinfo.packageUniqueId,
				  imsi:this.callListinfo.himsi,
				  vimsi:this.callListinfo.imsi,
				}).then(res => {
					if (res && res.code == '0000') {
						this.currentPage = page
						this.tableData = res.data
						this.total = res.count
						this.loading = false
					}
				}).catch(err => this.loading = false)
			}    
		 },
		 // 分页跳转
		 goPage(page) {
		   this.goPageFirst(page)
		 },
		 downloadFile(){
			this.downloading=true
			if(this.searchMode==='1'){//按国家/运营商查询
				operatorsexport({
				  beginTime:this.startTime,
				  endTime:this.endTime,
				  imsi:this.callListinfo.imsi,
				  mcc:this.callListinfo.mcc,
				  operatorName:this.callListinfo.operatorName,
				  supplierName:this.callListinfo.supplierName,
				  pageNumber:this.page,
				  pageSize:50,
          userId:this.$store.state.user.userId,
          roleId:this.$store.state.user.roleId,
				}).then(res => {
				 if(res && res.code == '0000'){
				 	this.exportModal=true
					var _this = this
					if (Object.values(res.data).length > 0) {
						Object.values(res.data).forEach(function(value) {
							_this.taskIds.push(value.taskId) 
							_this.taskNames.push(value.taskName)
							if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
								let taskid =  _this.taskIds.slice(0,3)
								let taskname = _this.taskNames.slice(0,3)
								_this.taskIds = taskid
								_this.taskNames = taskname
								_this.remind = true
							}
						})
					}
				 }
				  this.downloading = false
				}).catch(err => this.downloading = false)
			}else if(this.searchMode==='2'){//按号码查询
				Cdrdetailexport({
				  beginTime:this.startTime,
				  endTime:this.endTime,
				  imsi:this.callListinfo.imsi,
				  mcc:this.callListinfo.mcc,
          userId:this.$store.state.user.userId,
          roleId:this.$store.state.user.roleId,
				  operatorName:this.callListinfo.operatorName,
				  supplierName:this.callListinfo.supplierName,
				  pageNumber:this.page,
				  pageSize:50,
				}).then(res => {
				  if(res && res.code == '0000'){
				  	this.exportModal=true
					var _this = this
					if (Object.values(res.data).length > 0) {
						Object.values(res.data).forEach(function(value) {
							_this.taskIds.push(value.taskId) 
							_this.taskNames.push(value.taskName)
							if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
								let taskid =  _this.taskIds.slice(0,3)
								let taskname = _this.taskNames.slice(0,3)
								_this.taskIds = taskid
								_this.taskNames = taskname
								_this.remind = true
							}
						})
					}
				  }
				  this.downloading = false
				}).catch(err => this.downloading = false)
			}else if(this.searchMode==='3'){//按号段查询
				detailExport({
				 beginTime:this.startTime,
				 endTime:this.endTime,
				 imsi:this.callListinfo.imsi,
				 mcc:this.callListinfo.mcc,
         userId:this.$store.state.user.userId,
         roleId:this.$store.state.user.roleId,
				 operatorName:this.callListinfo.operatorName,
				 supplierName:this.callListinfo.supplierName,
				 pageNumber:this.page,
				 pageSize:50,
				}).then(res => {
				 if(res && res.code == '0000'){
				 	this.exportModal=true
					var _this = this
					if (Object.values(res.data).length > 0) {
						Object.values(res.data).forEach(function(value) {
							_this.taskIds.push(value.taskId) 
							_this.taskNames.push(value.taskName)
							if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
								let taskid =  _this.taskIds.slice(0,3)
								let taskname = _this.taskNames.slice(0,3)
								_this.taskIds = taskid
								_this.taskNames = taskname
								_this.remind = true
							}
						})
					}
				 }
				  this.downloading = false
				}).catch(err => this.downloading = false)
			}else if(this.searchMode==='4'){//按渠道商查询
				corpdetailexport({
				  beginTime:this.startTime,
				  endTime:this.endTime,
				  imsi:this.callListinfo.imsi,
				  mcc:this.callListinfo.mcc,
				  corpId:this.callListinfo.corpId,
          userId:this.$store.state.user.userId,
          roleId:this.$store.state.user.roleId,
				  pageNumber:this.page,
				  pageSize:50,
				}).then(res => {
				 if(res && res.code == '0000'){
				 	this.exportModal=true
					var _this = this
					if (Object.values(res.data).length > 0) {
						Object.values(res.data).forEach(function(value) {
							_this.taskIds.push(value.taskId) 
							_this.taskNames.push(value.taskName)
							if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
								let taskid =  _this.taskIds.slice(0,3)
								let taskname = _this.taskNames.slice(0,3)
								_this.taskIds = taskid
								_this.taskNames = taskname
								_this.remind = true
							}
						})
					}
				 }
				  this.downloading = false
				}).catch(err => this.downloading = false)
			}else if(this.searchMode==='5'){//按套餐周期查询
				exportdetail({
				  packageIdOrName:this.mealName,
				  pageNum:this.page,
				  pageSize:50,
          userId:this.$store.state.user.userId,
          roleId:this.$store.state.user.roleId,
				  mcc:this.callListinfo.mcc,
				  imsi:this.callListinfo.himsi,
				  vimsi:this.callListinfo.imsi,
				  packageUniqueId:this.callListinfo.packageUniqueId
				}).then(res => {
				if(res && res.code == '0000'){
					this.exportModal=true
					var _this = this
					if (Object.values(res.data).length > 0) {
						Object.values(res.data).forEach(function(value) {
							_this.taskIds.push(value.taskId) 
							_this.taskNames.push(value.taskName)
							if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
								let taskid =  _this.taskIds.slice(0,3)
								let taskname = _this.taskNames.slice(0,3)
								_this.taskIds = taskid
								_this.taskNames = taskname
								_this.remind = true
							}
						})
					}
				}
				  this.downloading = false
				}).catch(err => this.downloading = false)
			}else if(this.searchMode==='6'){//按套餐使用时间查询
				exportusedTimedetail({
				  beginTime:this.startTime,
				  endTime:this.endTime,
				  packageIdOrName:this.mealName,
          userId:this.$store.state.user.userId,
          roleId:this.$store.state.user.roleId,
				  pageNum:this.page,
				  pageSize:50,
				  mcc:this.callListinfo.mcc,
				  packageUniqueId:this.callListinfo.packageUniqueId,
				  imsi:this.callListinfo.himsi,
				  vimsi:this.callListinfo.imsi,
				}).then(res => {
				  if(res && res.code == '0000'){
				  	this.exportModal=true
					var _this = this
					if (Object.values(res.data).length > 0) {
						Object.values(res.data).forEach(function(value) {
							_this.taskIds.push(value.taskId) 
							_this.taskNames.push(value.taskName)
							if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
								let taskid =  _this.taskIds.slice(0,3)
								let taskname = _this.taskNames.slice(0,3)
								_this.taskIds = taskid
								_this.taskNames = taskname
								_this.remind = true
							}
						})
					}
				  }
				  this.downloading = false
				}).catch(err => this.downloading = false)
			}   
		 },
		 cancelModal: function() {
		 	this.exportModal=false
			this.taskIds = []
			this.taskNames = []
		 },
		 Goto(){
		 	this.$router.push({
		 	  path: '/taskList',
		 	  query: {
		 		taskId: encodeURIComponent(this.taskId),
		 		fileName:encodeURIComponent(this.taskName),
		 	  }
		 	})   
			this.cancelModal()
			 this.exportModal=false
		 },
		 back(){
			this.$router.push({
				path: '/callQuery',
			})
		 }
	 },
	 mounted(){
		 // 保存上一页返回数据
		 localStorage.setItem("cdrList", decodeURIComponent(this.$route.query.cdrList))
	 	 this.callListinfo = JSON.parse(decodeURIComponent(this.$route.query.callListinfo));
		 this.searchMode=decodeURIComponent(this.$route.query.searchMode)
		 this.startTime=this.$route.query.startTime
		 this.endTime=this.$route.query.endTime
		 this.goPageFirst(1)
	 }
	}
</script>

<style>
</style>
