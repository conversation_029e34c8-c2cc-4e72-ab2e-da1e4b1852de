<template>
	<!--  账户列表  -->
	<div>
		<Card>
			<div>
				<Form ref="form" :label-width="0" :model="form" :rules="rule" inline>
					<FormItem>
						<Select filterable v-model="searchMode" @on-change="getsearchModeList" placeholder="请选择查询方式"
							style="width: 200px ;margin-right: 10px;">
							<Option v-for="item in searchModeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
						</Select>
					</FormItem>
					<FormItem v-if="searchMode=='1'">
						<!-- 按国家/运营商查询 -->
						<Select filterable v-model="localId" :placeholder="$t('buymeal.selectCountry')"
							:clearable="true" class="inputSty" @on-change="getlocalList">
							<Option v-for="item in localList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
						</Select>
					</FormItem>
					<FormItem v-if="searchMode=='1'">
						<Select filterable v-model="operatorId" placeholder="请选择落地运营商" :clearable="true"
							class="inputSty">
							<Option v-for="item in operatorList" :value="item.operatorName" :key="item.id">{{ item.operatorName }}</Option>
						</Select>
					</FormItem>
					<FormItem v-if="searchMode=='1'">
						<Select filterable v-model="supplierId" placeholder="请选择资源供应商" :clearable="true"
							class="inputSty">
							<Option v-for="item in providers" :value="item.supplierName" :key="item.supplierId">{{ item.supplierName }}</Option>
						</Select>
					</FormItem>
					<FormItem v-if="searchMode=='4'">
						<Select filterable v-model="channelId" placeholder="请选择所属渠道" :clearable="true" class="inputSty">
							<Option :value="item.corpId" v-for="(item,index) in corpList" :key="index">{{item.corpName}}</Option>
						</Select>
					</FormItem>
					<FormItem v-if="searchMode=='2'">
						<Input v-model="number" placeholder="请输入IMSI号码，最多可输入10个号码，号码用英文,区隔" :clearable="true"
							style="width: 330px ;margin-right: 10px;" />
					</FormItem>
					<FormItem v-if="searchMode=='3'">
						<Input v-model="startNo" placeholder="请输入IMSI起始号码" :clearable="true" class="inputSty" />
					</FormItem>
					<FormItem v-if="searchMode=='3'">
						<Input v-model="endNo" placeholder="请输入IMSI结束号码" :clearable="true" class="inputSty" />
					</FormItem>
					<FormItem v-if="searchMode=='5'||searchMode=='6'">
						<Input v-model="mealName" placeholder="请输入套餐ID或名称" :clearable="true" class="inputSty" />
					</FormItem>
					<FormItem prop="date" v-if="searchMode!=''">
						<DatePicker format="yyyy-MM-dd" v-model="form.date" @on-change="handleDateChange"
							type="daterange" placeholder="选择时间段" :options="options3"></DatePicker>
					</FormItem>
					<FormItem v-if="searchMode!=''">
						<Button v-has="'search'" type="primary" icon="md-search" @click="searchByCondition()"
							:loading="searchloading" style="margin-right: 10px;">搜索</Button>
					</FormItem>
					<FormItem v-if="searchMode!=''">
						<Button v-has="'exportReport'" type="success" :loading="downloading" icon="ios-download"
							@click="downloadFile()" style="margin-right: 10px;">导出报表</Button>
					</FormItem>
					<FormItem v-if="searchMode!=''">
						<Button v-has="'exportDetail'" type="primary" :loading="detailsding" icon="ios-download"
							@click="downloaddetails()" style="margin-right: 10px;">导出详情</Button>
					</FormItem>
					<FormItem>
						<Button v-if="searchMode==='2'&& searchMode!=''" v-has="'import'" type="primary"
							icon="ios-download" @click="importFile()">文件导入</Button>
					</FormItem>
				</Form>

			</div>

			<div style="margin-top:20px" v-if="searchMode=='1'||searchMode=='2'||searchMode=='3'">
				<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row, index }" slot="action">
						<a v-has="'view'" type="primary" size="small" @click="showDetail(row)">话单详情</a>
					</template>
				</Table>
			</div>
			<div style="margin-top:20px" v-if="searchMode=='4'">
				<Table :columns="columns1" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row, index }" slot="action">
						<a v-has="'view'" type="primary" size="small" @click="showDetail(row)">话单详情</a>
					</template>
				</Table>
			</div>
			<div style="margin-top:20px" v-if="searchMode=='5'">
				<Table :columns="columns2" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row, index }" slot="action">
						<a v-has="'view'" type="primary" size="small" @click="showDetail(row)">话单详情</a>
					</template>
				</Table>
			</div>
			<div style="margin-top:20px" v-if="searchMode=='6'">
				<Table :columns="columns3" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row, index }" slot="action">
						<a v-has="'view'" type="primary" size="small" @click="showDetail(row)">话单详情</a>
					</template>
				</Table>
			</div>
			<div class="table-botton" style="margin-top:15px">
				<Page :total="total" :current.sync="currentPage" :page-size="50" show-total show-elevator
					@on-change="goPage" />
			</div>
			<a ref="downloadLink" style="display: none"></a>
		</Card>
		<Modal title="文件导入" v-model="importModal" :mask-closable="true" @on-ok="okModal" @on-cancel="cancelModal">
			<div>
				<Upload type="drag" :action="uploadUrl" :on-success="fileSuccess" :on-error="handleError"
					:before-upload="handleBeforeUpload" :on-progress="fileUploading" style="width: 480px;">
					<div style="padding: 20px 0">
						<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
						<p>点击或拖拽文件上传</p>
					</div>
				</Upload>
				<div style="width: 500px;">
					<Button type="primary" :loading="downloading" icon="ios-download"
						@click="downloadTemplate">下载文件</Button>
					<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">文件仅支持csv格式文件</Alert>
				</div>
				<ul class="ivu-upload-list" v-if="file" style="width: 500px;">
					<li class="ivu-upload-list-file ivu-upload-list-file-finish">
						<span>
							<Icon type="ios-folder" />{{file.name}}
						</span>
						<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
					</li>
				</ul>
			</div>
		</Modal>
		<!-- 模板文件table -->
		<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
		<!-- 多文件导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					<FormItem label="你本次导出任务ID为:">
						<ul>
							<li id="space" v-for="(taskId,i) in taskIds" :key="taskIds.i">
								{{taskId}}
							</li>
						</ul>
						<div v-if="remind">
							<span>……</span>
						</div>
					</FormItem>
					<FormItem label="你本次导出的文件名为:">
						<ul style="margin-bottom: 15px;">
							<li id="space" v-for="(taskName,i) in taskNames" :key="taskNames.i">
								{{taskName}}
							</li>
						</ul>
						<div v-if="remind">
							<span>……</span>
						</div>
					</FormItem>
					<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="Goto">立即前往</Button>
			</div>
		</Modal>
		
		<!-- 单文件导出提示 -->
		<Modal v-model="exportModalr" :mask-closable="true" @on-cancel="exportcancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					<FormItem label="你本次导出任务ID为:">
						<span>{{taskId}}</span>
					</FormItem>
					<FormItem label="你本次导出的文件名为:">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
				</Form>
			</div>
		
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="exportcancelModal">取消</Button>
				<Button type="primary" @click="Gotor">立即前往</Button>
			</div>
		</Modal>
	</div>
</template>

<script>
	import {
		period,
		exportdetail,
		exportperiod,
		usedTime,
		exportusedTimedetail,
		exportusedTime,
		numberCdr,
		Cdrdetailexport,
		Cdrnormalexport,
		operatorCdr,
		operatorsexport,
		opnormalexport,
		numbernormal,
		detailExport,
		normalExport,
		corpnormal,
		corpdetailexport,
		corpnormalexport,
		importFile,
		getoperator
	} from '@/api/cdr/cdrSearch'
	import {
		opsearch
	} from '@/api/channel.js'
	import {
		getCorpList,
	} from '@/api/product/package/batch'
	import {
		getOperatorList,
	} from '@/api/public/util'
	import {
		getSupply,
	} from '@/api/resoure/common'
	import localData from '../../../libs/localData.js'
	import publicData from '../../../libs/publicData.js'
	const math = require('mathjs')
	export default {

		data() {
			return {
				form: {
					startTime: "",
					endTime: "",
					date: []
				},
				options3: {
					disabledDate(date) {
						return date && date.valueOf() > Date.now();
					}
				},
				taskId: '',
				taskName: '',
				date: [],
				file: null,
				uploadUrl: '',
				searchMode: '', //查询方式
				localId: '', //国家/地区
				operatorId: '', //运营商
				supplierId: '', //供应商
				channelId: '', //渠道商
				number: '', //IMSI
				startNo: '', //起始号段
				endNo: '', //结束号段
				companyName: '', //企业名称
				mealName: '', //套餐名称
				message: '',
				corpList: [],
				providers: [],
				modelData: [{
					'imsi': '********',
				}, ],
				modelColumns: [{
						title: 'imsi',
						key: 'imsi'
					}, // 列名根据需要添加
				],
				searchModeList: [{
					value: '1',
					label: '按国家/运营商查询'
				}, {
					value: '2',
					label: '按号码查询'
				}, {
					value: '3',
					label: '按号段查询'
				}, {
					value: '4',
					label: '按渠道商查询'
				}, {
					value: '5',
					label: '按套餐周期查询'
				}, {
					value: '6',
					label: '按套餐使用时间查询'
				}],
				localList: [],
				operatorList: [],
				detailModal: false,
				importModal: false,
				localName: '',
				downloading: false,
				detailsding: false,
				searchloading: false,
				exportModal: false,
				exportModalr: false,
				plmnList: [],
				columns: [{
						title: 'IMSI',
						key: 'imsi',
						align: 'center'
					},
					{
						title: '国家/地区',
						key: 'countryCn',
						align: 'center'
					},
					{
						title: '落地运营商',
						key: 'operatorName',
						align: 'center'
					},
					{
						title: '资源供应商',
						key: 'supplierName',
						align: 'center'
					},
					{
						title: '流量总量(MB)',
						key: 'flowByteTotal',
						align: 'center',
						// render: (h, params) => {
						// 	const row = params.row
						// 	const text = parseFloat(math.multiply(math.bignumber(row.flowByteTotal), 1024).toFixed(5)).toString()
						// 	return h('label', text)
						// }
					},
					{
						title: '话单详情',
						slot: 'action',
						align: 'center'
					}
				],
				columns1: [{
						title: 'IMSI',
						key: 'imsi',
						align: 'center'
					},
					{
						title: '渠道商名称',
						key: 'corpName',
						align: 'center'
					},
					{
						title: '流量总量(MB)',
						key: 'flowByteTotal',
						align: 'center',
						// render: (h, params) => {
						// 	const row = params.row
						// 	const text = parseFloat(math.multiply(math.bignumber(row.flowByteTotal), 1024).toFixed(5)).toString()
						// 	return h('label', text)
						// }
					},
					{
						title: '话单详情',
						slot: 'action',
						align: 'center'
					}
				],
				columns2: [{
						title: 'IMSI',
						key: 'himsi',
						align: 'center'
					},
					{
						title: '套餐ID',
						key: 'packageId',
						align: 'center'
					},
					{
						title: '套餐名称',
						key: 'packageName',
						align: 'center'
					},
					// {
					//   title: '国家/地区',
					//   key: 'countryCn',
					//   align: 'center'
					// },
					{
						title: '渠道商名称',
						key: 'corpName',
						align: 'center'
					},
					{
						title: '流量总量(MB)',
						key: 'flowByteTotal',
						align: 'center',
						// render: (h, params) => {
						// 	const row = params.row
						// 	const text = parseFloat(math.multiply(math.bignumber(row.flowByteTotal), 1024).toFixed(5)).toString()
						// 	return h('label', text)
						// }
					},
					{
						title: '话单详情',
						slot: 'action',
						align: 'center'
					}
				],
				columns3: [{
						title: 'IMSI',
						key: 'himsi',
						align: 'center',
						minWidth: 200,
					},
					//  {
					//    title: 'VIMSI',
					//    key: 'imsi',
					//    align: 'center',
					// minWidth: 200,
					//  },
					{
						title: '套餐ID',
						key: 'packageId',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '套餐名称',
						key: 'packageName',
						align: 'center',
						minWidth: 200,
					},
					//        {
					//          title: '国家/地区',
					//          key: 'countryCn',
					//          align: 'center',
					// minWidth: 200,
					//        },
					//        {
					//          title: '资源供应商',
					//          key: 'supplierName',
					//          align: 'center',
					// minWidth: 200,
					//        },
					{
						title: '流量总量(MB)',
						key: 'flowByteTotal',
						align: 'center',
						minWidth: 200,
						// render: (h, params) => {
						// 	const row = params.row
						// 	const text = parseFloat(math.multiply(math.bignumber(row.flowByteTotal), 1024).toFixed(5)).toString()
						// 	return h('label', text)
						// }
					},
					{
						title: '套餐开始时间',
						key: 'activeTime',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '套餐结束时间',
						key: 'expireTime',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '套餐天数',
						key: 'validDate',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '套餐实际使用天数',
						key: 'useDays',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '话单详情',
						slot: 'action',
						align: 'center',
						minWidth: 200,
					}
				],
				tableData: [],
				details: {
					id: '1001',
					IMISI: '123321111',
					MSISDN: '13261201',
					startTime: '2021-01-01 00:00:00',
					endTime: '2021-01-02 00:00:00',
					MCC: '33321',
					MNC: '99021',
					upTraffic: '100M',
					downTraffic: '1000M',
					APN: '15986',
				},
				flowtotal: this.$t('flowtotal'),
				loading: false,
				currentPage: 1,
				page: 1,
				startTime: null,
				endTime: null,
				total: 0,
				remind: false,
				taskIds: [],
				taskNames: [],
				rule: {
					date: [{
						type: 'array',
						required: true,
						message: '请选择时间',
						trigger: 'change',
						fields: {
							0: {
								required: true,
								message: '请选择开始日期',
								trigger: 'blur',
								pattern: /.+/
							},
							1: {
								required: true,
								message: '请选择结束日期',
								trigger: 'blur',
								pattern: /.+/
							}
						}
					}],
				},
			}
		},
		computed: {

		},
		methods: {
			// 获取列表
			goPageFirst: function(page) {
				this.$refs["form"].validate(valid => {
					if (this.form.startTime) {
						this.page = page
						this.loading = true
						if (this.searchMode === '1') { //按国家/运营商查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.localId && !this.operatorId && !this.supplierId) {
								this.$Message.error("时间段不能超过10天！");
								this.loading = false
								this.searchloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							operatorCdr({
								beginTime: this.startTime,
								endTime: this.endTime,
								pageNumber: page,
								pageSize: 50,
								mcc: this.localId,
								operatorName: this.operatorId,
								supplierName: this.supplierId
							}).then(res => {
								if (res && res.code == '0000') {
									this.currentPage = page
									this.tableData = res.data
									this.total = res.count
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.loading = false
								this.searchloading = false
							})
						} else if (this.searchMode === '2') { //按号码查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.number) {
								this.$Message.error("时间段不能超过10天！");
								this.loading = false
								this.searchloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							numberCdr({
								beginTime: this.startTime,
								endTime: this.endTime,
								imsis: this.number,
								pageNumber: page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.currentPage = page
									this.tableData = res.data
									this.total = res.count
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.loading = false
								this.searchloading = false
							})
						} else if (this.searchMode === '3') { //按号段查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.startNo && !this.endNo) {
								this.$Message.error("时间段不能超过10天！");
								this.loading = false
								this.searchloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							numbernormal({
								beginTime: this.startTime,
								endTime: this.endTime,
								startImsi: this.startNo,
								endImsi: this.endNo,
								pageNumber: page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.currentPage = page
									this.tableData = res.data
									this.total = res.count
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.loading = false
								this.searchloading = false
							})
						} else if (this.searchMode === '4') { //按渠道商查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.channelId) {
								this.$Message.error("时间段不能超过10天！");
								this.loading = false
								this.searchloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							corpnormal({
								beginTime: this.startTime,
								endTime: this.endTime,
								corpId: this.channelId,
								pageNumber: page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.currentPage = page
									this.tableData = res.data
									this.total = res.count
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.loading = false
								this.searchloading = false
							})
						} else if (this.searchMode === '5') { //按套餐周期查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.mealName) {
								this.$Message.error("时间段不能超过10天！");
								this.loading = false
								this.searchloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							period({
								beginTime: this.startTime,
								endTime: this.endTime,
								packageIdOrName: this.mealName,
								pageNum: page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.currentPage = page
									this.tableData = res.data
									this.total = res.count
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.loading = false
								this.searchloading = false
							})
						} else if (this.searchMode === '6') { //按套餐使用时间查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.mealName) {
								this.$Message.error("时间段不能超过10天！");
								this.loading = false
								this.searchloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							usedTime({
								beginTime: this.startTime,
								endTime: this.endTime,
								packageIdOrName: this.mealName,
								pageNum: page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.currentPage = page
									this.tableData = res.data
									this.total = res.count
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.loading = false
								this.searchloading = false
							})
						}
					} else {
						this.searchloading = false
						this.$Message.error("请选择时间段");
					}
				})
			},
			showDetail: function(row) {
				var self = this
				// 组装查询条件
				let cdrList = {
					searchMode: this.searchMode,
					localId: this.localId,
					operatorId: this.operatorId,
					supplierId: this.supplierId,
					channelId: this.channelId,
					number: this.number,
					startNo: this.startNo,
					endNo: this.endNo,
					mealName: this.mealName,
					date: this.form.date,
					startTime: this.form.startTime,
					endTime: this.form.endTime
				}
				self.$router.push({
					name: 'callListinfo',
					query: {
						callListinfo: encodeURIComponent(JSON.stringify(row)),
						searchMode: encodeURIComponent(this.searchMode),
						startTime: this.startTime,
						endTime: this.endTime,
						cdrList: encodeURIComponent(JSON.stringify(cdrList)),
					}
				})
			},
			getlocalList() {
				this.getoperator()
			},
			getsearchModeList() {
				this.localId = ''
				this.operatorId = ''
				this.supplierId = ''
				this.number = ''
				this.startNo = ''
				this.endNo = ''
				this.mealName = ''
				this.channelId = ''
				this.tableData = []
				// this.$refs.form.resetFields()
				this.goPageFirst(1)
			},
			handleDateChange(date) {
				if (Array.isArray(date)) {
					this.form.startTime = date[0];
					this.form.endTime = date[1];
				}
			},
			//导出报表
			downloadFile() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.downloading = true
						if (this.searchMode === '1') { //按国家/运营商查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.localId && !this.operatorId && !this.supplierId) {
								this.$Message.error("时间段不能超过10天！");
								this.downloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							opnormalexport({
								beginTime: this.startTime,
								endTime: this.endTime,
								pageNumber: this.page,
								pageSize: 50,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								mcc: this.localId,
								operatorName: this.operatorId,
								supplierName: this.supplierId
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									// this.taskId=res.data.taskId
									// this.taskName=res.data.taskName
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								}
								this.downloading = false
							}).catch(err => this.downloading = false)
						} else if (this.searchMode === '2') { //按号码查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.number) {
								this.$Message.error("时间段不能超过10天！");
								this.downloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							Cdrnormalexport({
								beginTime: this.startTime,
								endTime: this.endTime,
								imsis: this.number,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								pageNumber: this.page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								}
								this.downloading = false
							}).catch(err => this.downloading = false)
						} else if (this.searchMode === '3') { //按号段查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.startNo && !this.endNo) {
								this.$Message.error("时间段不能超过10天！");
								this.downloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							normalExport({
								beginTime: this.startTime,
								endTime: this.endTime,
								startImsi: this.startNo,
								endImsi: this.endNo,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								pageNumber: this.page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								}
								this.downloading = false
							}).catch(err => this.downloading = false)
						} else if (this.searchMode === '4') { //按渠道商查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.channelId) {
								this.$Message.error("时间段不能超过10天！");
								this.downloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							corpnormalexport({
								beginTime: this.startTime,
								endTime: this.endTime,
								corpId: this.channelId,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								pageNumber: this.page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								}
								this.downloading = false
							}).catch(err => this.downloading = false)
						} else if (this.searchMode === '5') { //按套餐周期查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.mealName) {
								this.$Message.error("时间段不能超过10天！");
								this.downloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							exportperiod({
								beginTime: this.startTime,
								endTime: this.endTime,
								packageIdOrName: this.mealName,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								pageNum: this.page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.downloading = false
							})
						} else if (this.searchMode === '6') { //按套餐使用时间查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.mealName) {
								this.$Message.error("时间段不能超过10天！");
								this.downloading = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							exportusedTime({
								beginTime: this.startTime,
								endTime: this.endTime,
								packageIdOrName: this.mealName,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								pageNum: this.page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								}
								this.downloading = false
							}).catch(err => this.downloading = false)
						}
					} else {
						this.$Message.error("请选择时间段");
					}
				})

			},
			//导出详情
			downloaddetails() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.detailsding = true
						if (this.searchMode === '1') { //按国家/运营商查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.localId && !this.operatorId && !this.supplierId) {
								this.$Message.error("时间段不能超过10天！");
								this.detailsding = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							operatorsexport({
								beginTime: this.startTime,
								endTime: this.endTime,
								pageNumber: this.page,
								pageSize: 50,
								mcc: this.localId,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								operatorName: this.operatorId,
								supplierName: this.supplierId
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								}
								this.detailsding = false
							}).catch(err => this.detailsding = false)
						} else if (this.searchMode === '2') { //按号码查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.number) {
								this.$Message.error("时间段不能超过10天！");
								this.detailsding = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							Cdrdetailexport({
								beginTime: this.startTime,
								endTime: this.endTime,
								imsis: this.number,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								pageNumber: this.page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								}
								this.detailsding = false
							}).catch(err => this.detailsding = false)
						} else if (this.searchMode === '3') { //按号段查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.startNo && !this.endNo) {
								this.$Message.error("时间段不能超过10天！");
								this.detailsding = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							detailExport({
								beginTime: this.startTime,
								endTime: this.endTime,
								startImsi: this.startNo,
								endImsi: this.endNo,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								pageNumber: this.page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								}
								this.detailsding = false
							}).catch(err => this.detailsding = false)
						} else if (this.searchMode === '4') { //按渠道商查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.channelId) {
								this.$Message.error("时间段不能超过10天！");
								this.detailsding = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							corpdetailexport({
								beginTime: this.startTime,
								endTime: this.endTime,
								corpId: this.channelId,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								pageNumber: this.page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								}
								this.detailsding = false
							}).catch(err => this.detailsding = false)
						} else if (this.searchMode === '5') { //按套餐周期查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.mealName) {
								this.$Message.error("时间段不能超过10天！");
								this.detailsding = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							exportdetail({
								beginTime: this.startTime,
								endTime: this.endTime,
								packageIdOrName: this.mealName,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								pageNum: this.page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								}
								this.detailsding = false
							}).catch(err => this.detailsding = false)
						} else if (this.searchMode === '6') { //按套餐使用时间查询
							//时间段不能超过10天判断
							var startTime = Date.parse(new Date(this.form.startTime));
							var endTime = Date.parse(new Date(this.form.endTime));
							var day = (endTime - startTime) / 1000 / 3600 / 24
							if (day > 9 && !this.mealName) {
								this.$Message.error("时间段不能超过10天！");
								this.detailsding = false
								return
							}
							if (this.form.startTime) {
								let startdate = this.form.startTime.split("-");
								this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
							}
							if (this.form.endTime) {
								let enddate = this.form.endTime.split("-");
								this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
							}
							exportusedTimedetail({
								beginTime: this.startTime,
								endTime: this.endTime,
								packageIdOrName: this.mealName,
								userId: this.$store.state.user.userId,
								roleId: this.$store.state.user.roleId,
								pageNum: this.page,
								pageSize: 50
							}).then(res => {
								if (res && res.code == '0000') {
									this.exportModal = true
									var _this = this
									if (Object.values(res.data).length > 0) {
										Object.values(res.data).forEach(function(value) {
											_this.taskIds.push(value.taskId)
											_this.taskNames.push(value.taskName)
											if (_this.taskIds.length > 3 || _this.taskNames
												.length > 3) {
												let taskid = _this.taskIds.slice(0, 3)
												let taskname = _this.taskNames.slice(0, 3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										})
									}
								}
								this.detailsding = false
							}).catch(err => this.detailsding = false)
						}
					} else {
						this.$Message.error("请选择时间段");
					}
				})
			},
			//文件导入
			importFile() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						//时间段不能超过10天判断
						if (this.form.startTime) {
							let startdate = this.form.startTime.split("-");
							this.startTime = startdate[0] + startdate[1] + startdate[2] + ""
						}
						if (this.form.endTime) {
							let enddate = this.form.endTime.split("-");
							this.endTime = enddate[0] + enddate[1] + enddate[2] + ""
						}
						this.importModal = true

					}
				})

			},
			searchByCondition: function() {
				this.searchloading = true
				//校验imsi个数
				if (this.searchMode == '2') {
					this.plmnList = this.number.split(',')
					if (this.plmnList.length > 10) {
						this.$Message.warning('最多支持输入10个imsi查询!');
						return
					}
				}
				this.goPageFirst(1)
			},
			cancelModal: function() {
				this.detailModal = false
				this.exportModal = false
				this.file = null
				this.taskIds = []
				this.taskNames = []
			},
			exportcancelModal(){
				this.exportModalr = false
				this.file = ''
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.cancelModal()
				this.exportModal = false
			},
			Gotor(){
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportcancelModal()
				this.exportModalr = false
			},
			//文件上传
			okModal() {

				var formData = new FormData();
				formData.append('file', this.file);
				formData.append('beginTime', this.startTime);
				formData.append('endTime', this.endTime);
				formData.append('userId', this.$store.state.user.userId);
				formData.append('roleId', this.$store.state.user.roleId);
				importFile(formData).then(res => {
					if (res && res.code == '0000') {
						this.exportModalr = true
						this.taskId = res.data.taskId
						this.taskName = res.data.taskName
					}
					this.detailsding = false
				}).catch(err => this.detailsding = false)
			},
			// 分页跳转
			goPage(page) {
				this.goPageFirst(page)
			},
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			handleBeforeUpload(file) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: this.$t("buymeal.fileformat"),
						desc: file.name + this.$t("buymeal.incorrect")
					})
				} else {
					if (file.size > 5 * 1024 * 1024) {
						this.$Notice.warning({
							title: this.$t("buymeal.Filesize"),
							desc: file.name + this.$t("buymeal.Exceeds")
						})
					} else {
						this.file = file
					}
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			removeFile() {
				this.file = ''
			},
			//模板下载
			downloadTemplate: function() {
				this.$refs.modelTable.exportCsv({
					filename: "号码文件",
					// type:'xlsx',
					columns: this.modelColumns,
					data: this.modelData
				})
			},
			//国家/地区
			getLocalList() {
				opsearch().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.localList = list;
						this.localList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//获取运营商
			getoperator() {
				getoperator({
					mcc: this.localId === undefined ? "" : this.localId,
					pageSize: 100000,
					pageNum: 1
				}).then(res => {
					if (res && res.code == '0000') {
						var arr = res.data.records
						const operatorName = 'operatorName'; //1、定义按照过滤的对象的属性名称
						const newArr = arr.reduce((all, next) => all.some((item) => item[operatorName] == next[
							operatorName]) ? all : [...all, next], []) // 2、数组属性对象 去重
						this.operatorList = newArr
					} else {
						throw res
					}
				}).catch((err) => {})
			},
			//获取资源供应商
			getSupplier() {
				getSupply().then(res => {
					if (res && res.code == '0000') {
						this.providers = res.data
					} else {
						throw res
					}
				}).catch((err) => {})
			},
			//获取渠道商
			getChannel() {
				getCorpList({
					"type": 1,
					"status": 1,
					"checkStatus": 2
				}).then(res => {
					if (res && res.code == '0000') {
						this.corpList = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			}
		},
		mounted() {
			// this.goPageFirst(1)
			this.getLocalList()
			this.getoperator()
			this.getSupplier()
			this.getChannel()
			//缓存数据
			let cdrList = JSON.parse(localStorage.getItem("cdrList")) === null ? '' : JSON.parse(localStorage.getItem(
				"cdrList"))
			if (cdrList) {
				this.searchMode = cdrList.searchMode === undefined ? "" : cdrList.searchMode
				this.localId = cdrList.localId === undefined ? "" : cdrList.localId
				this.operatorId = cdrList.operatorId === undefined ? "" : cdrList.operatorId
				this.supplierId = cdrList.supplierId === undefined ? "" : cdrList.supplierId
				this.channelId = cdrList.channelId === undefined ? "" : cdrList.channelId
				this.number = cdrList.number === undefined ? "" : cdrList.number
				this.startNo = cdrList.startNo === undefined ? "" : cdrList.startNo
				this.endNo = cdrList.endNo === undefined ? "" : cdrList.endNo
				this.mealName = cdrList.mealName === undefined ? "" : cdrList.mealName
				this.form.startTime = cdrList.startTime === undefined ? "" : cdrList.startTime
				this.form.endTime = cdrList.endTime === undefined ? "" : cdrList.endTime
				this.form.date.push(this.form.startTime)
				this.form.date.push(this.form.endTime)
				//清除缓存
				localStorage.removeItem("cdrList")
				this.goPageFirst(1)
			} else {
				this.searchMode = '1'
			}
		},
		watch: {}
	}
</script>
<style>
	.search_head {
		width: 100%;
		display: flex;
		text-align: center;
		align-items: center;
		justify-content: flex-start;
	}

	.search-btn {
		width: 100px !important;
	}

	.inputSty {
		width: 200px;
	}

	.ivu-table td label {
		word-wrap: break-word;
		word-break: normal;
	}

	#space {
		/* height: 30px;
  	line-height: 30px; */
		font-size: 12px;
		white-space: pre-line;
		list-style: none;
	}
</style>