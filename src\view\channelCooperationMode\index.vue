<template>
	<div class="total">
		<Divider>
			<h1 class="dividerBox">{{welcome}}</h1>
		</Divider>
		<h2 class="hbox">{{$t('selectCooperationMode')}}</h2>
		<div class="typeBox">
			<div class="box" @click="routeMode('1')" v-if="cooperationModeList.includes('1')">
				<p class="fontBox">{{$t('consignmentSalesModel')}}</p>
			</div>
			<div class="box" @click="routeMode('2')" v-if="cooperationModeList.includes('2')">
				<p class="fontBox">{{$t('A2Zmode')}}</p>
			</div>
			<div class="box" @click="routeMode('3')" v-if="cooperationModeList.includes('3')">
				<p class="fontBox">{{$t('resourceMode')}}</p>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				welcome: '欢迎使用CMI全球卡业务网站',
				cooperationModeList: '',
			}
		},
		mounted() {
			let lang = this.$i18n.locale
			if (lang === 'en-US') {
				this.welcome = 'Welcome to Global Data SIM Portal'
			}
			this.cooperationModeList = this.$route.query.modeList
			document.querySelector('body').setAttribute('style', 'background-color:#F5F7F9; overflow: auto !important')
		},
		methods: {
			routeMode: function(cooperationMode) {
				sessionStorage.setItem("cooperationMode",cooperationMode)
				this.$router.push({
					path: '/home',
				})
			},
		}
	}
</script>

<style>
	.total {
		width: 100%;
	}

	.dividerBox {
		margin-top: 20px;
	}

	.hbox {
		text-align: center;
		margin: 60px 30px;
	}

	.typeBox {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-evenly;
	}

	.box {
		width: 400px;
		height: 500px;
		margin-bottom: 30px;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #fff;
		border-radius: 10px;
		border: 1px;
		cursor: pointer;
	}

	.box:hover {
		box-shadow: 0 3px 3px rgba(0, 0, 0, 0.3);
	}

	.fontBox {
		font-size: 26px;
		font-weight: bold;
	}
</style>