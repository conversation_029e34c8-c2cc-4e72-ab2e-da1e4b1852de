<template>
  <!-- 渠道商账单查询 -->
  <Card>
    <Form ref="form" :label-width="90" :model="form" :rules="ruleInline" inline>
      <FormItem label="渠道商名称" prop="corpId">
        <Select
          v-model="form.corpId"
          clearable
          multiple
          filterable
          placeholder="下拉选择渠道商"
          style="width: 200px"
        >
          <Option
            :value="item.corpId"
            v-for="(item, index) in corpLists"
            :key="index"
            >{{ item.corpName }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="开始月份:" prop="beginMonth">
        <DatePicker
          format="yyyyMM"
          v-model="form.beginMonth"
          type="month"
          placement="bottom-start"
          placeholder="请选择开始月份"
          :editable="true"
          @on-change="startChange"
        ></DatePicker
        >  
      </FormItem>
      <FormItem label="结束月份:" prop="endMonth">
        <DatePicker
          format="yyyyMM"
          v-model="form.endMonth"
          type="month"
          placement="bottom-start"
          placeholder="请选择结束月份"
          :editable="true"
          @on-change="endChange"
        ></DatePicker>
      </FormItem>
        <FormItem label="缴费状态" prop="chargeStatus">
          <Select
            v-model="form.chargeStatus"
            clearable
            filterable
            placeholder="下拉选择缴费状态"
            style="width: 130px"
          >
            <Option value="0">未付款</Option>
            <Option value="6">已核销</Option>
            <Option value="1">待确认到账</Option>
            <Option value="2">付款成功</Option>
            <Option value="3">付款失败</Option>
            <Option value="4">已取消</Option>
            <Option value="5">线上缴费中</Option>
          </Select>
        </FormItem>&nbsp;&nbsp;
      <Button
        type="primary"
        icon="md-search"
        :loading="searchLoading"
        @click="search('form')"
        v-has="'search'"
        >{{ $t("common.search") }}</Button
      >&nbsp;&nbsp;&nbsp;&nbsp;
      <Button
        type="success"
        icon="md-arrow-down"
        :loading="exportLoading"
        v-has="'export'"
        @click="exportHistoryRecords('form')"
        >{{ $t("stock.exporttb") }}</Button
      >
    </Form>
    <div>
      <Table
        no-data-text
        border
        highlight-row
        :columns="columns"
        :data="data"
        style="width: 100%; margin-top: 20px"
        :loading="loading"
      >
        <template slot-scope="{ row, index }" slot="action">
          <Button type="warning" ghost size="small" style="margin: 5px" @click="offinePay(row,'1')" v-has="'payment'"
            :disabled="!['0', '4'].includes(row.chargeStatus) || row.channelType == '2' || row.realIncome == 0">线下支付</Button>
          <Button type="error" ghost size="small" style="margin: 5px" @click="offinePay(row,'2')" v-has="'rePay'"
            :disabled="!['3'].includes(row.chargeStatus) || row.channelType == '2' || row.realIncome == 0">重新上传付款证明</Button>
        </template>
        <template slot-scope="{ row, index }" slot="file">
            <!-- :disabled="cooperationMode != '1'" -->
          <Button type="primary" ghost size="small" style="margin: 5px" @click="showInfo(row)"
           v-has="'info'">明细</Button>
          <Button type="info" ghost size="small" style="margin: 5px" @click="exportBillFile(row)"
           v-has="'bill_export'">
            账单明细下载
          </Button>
          <Button type="success" ghost size="small" style="margin: 5px" @click="exportInvoice(row)"
           :disabled="!row.invoicePath" v-has="'invoice'">Invoice下载</Button>
        </template>
      </Table>
      <Page
        :total="total"
        :current.sync="page"
        show-total
        show-elevator
        @on-change="loadByPage"
      />
    </div>
    <!-- 导出提示 -->
    <Modal
      v-model="exportModalr"
      :mask-closable="true"
      @on-cancel="exportcancelModal"
    >
      <div style="align-items: center; justify-content: center; display: flex">
        <Form
          label-position="left"
          :label-width="150"
          style="align-items: center; justify-content: center"
        >
          <h1 style="text-align: center; margin-bottom: 10px">
            {{ $t("exportMS") }}
          </h1>
          <FormItem :label="$t('exportID')">
            <span>{{ taskId }}</span>
          </FormItem>
          <FormItem :label="$t('exportFlie')">
            <span class="task-name">{{ taskName }}</span>
          </FormItem>
          <span style="text-align: left">{{ $t("downloadResult") }}</span>
        </Form>
      </div>

      <div
        slot="footer"
        style="
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <Button @click="exportcancelModal">{{ $t("common.cancel") }}</Button>
        <Button type="primary" @click="Gotor">{{ $t("Goto") }}</Button>
      </div>
    </Modal>
    <!-- 导出提示 -->
    <Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
      <div style="align-items: center; justify-content: center; display: flex">
        <Form
          label-position="left"
          :label-width="150"
          style="
            width: 500px;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
          "
        >
          <h1 style="text-align: center; margin-bottom: 20px">
            {{ $t("exportMS") }}
          </h1>
          <FormItem :label="$t('exportID')">
            <ul style="margin-bottom: 15px">
              <li id="space" v-for="(taskId, i) in taskIds" :key="taskIds.i">
                {{ taskId }}
              </li>
            </ul>
            <div v-if="remind">
              <span>……</span>
            </div>
          </FormItem>
          <FormItem :label="$t('exportFlie')">
            <ul style="margin-bottom: 15px">
              <li
                id="space"
                v-for="(taskName, i) in taskNames"
                :key="taskNames.i"
                class="task-name"
              >
                {{ taskName }}
              </li>
            </ul>
            <div v-if="remind">
              <span>……</span>
            </div>
          </FormItem>
          <span style="text-align: left">{{ $t("downloadResult") }}</span>
        </Form>
      </div>
      <div
        slot="footer"
        style="
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <Button @click="cancelModal">{{ $t("common.cancel") }}</Button>
        <Button type="primary" @click="Goto">{{ $t("Goto") }}</Button>
      </div>
    </Modal>
    <!-- 明细 -->
    <Modal title="明细" v-model="infoModal" :footer-hide="true" :mask-closable="false" width="500px">
      <Row style="margin: 20px">
        <Col span="24" style="display: flex; justify-content: flex-start;">
          <span>流量费：</span>&nbsp;&nbsp;{{ a2zAmount }}
        </Col>
      </Row>
      <Row style="margin: 20px">
        <Col span="24" style="display: flex; justify-content: flex-start;">
          <span>卡费：</span>&nbsp;&nbsp;{{ imsiAmount }}
        </Col>
      </Row>
      <Row style="margin: 20px">
        <Col span="24" style="display: flex; justify-content: flex-start;">
          <span>套餐费：</span>&nbsp;&nbsp;{{ directIncome }}
        </Col>
      </Row>
    </Modal>
    <!-- 线下付款 -->
    <Modal v-model="PaymentModal" :title="$t('channelBill.paymentPage')" :footer-hide="true" :mask-closable="false"
    	width="450px" @on-cancel="cancelModal">
    	<div>
    		<Form ref="formobj" :model="formobj" :rules="ruleobj" :label-width="100"
    			:label-height="100" inline style="font-weight:bold;">
    			<FormItem :label="$t('support.payslip')" prop="file" style="font-size: 14px;font-weight: bold;">
    				<Upload type="drag" v-model="formobj.file" :action="uploadUrl" :before-upload="handleBeforeUpload"
              :on-progress="fileUploading" style="width: 250px; margin-top: 50px;">
    					<div style="padding: 20px 0">
    						<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
    						<p>{{$t('support.uploadPicture')}}</p>
    					</div>
    				</Upload>
    				<ul class="ivu-upload-list" v-if="file" style="width: 300px;">
    					<li class="ivu-upload-list-file ivu-upload-list-file-finish">
    						<span>
    							<Icon type="ios-folder" />{{file.name}}
    						</span>
    						<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" @click="removeFile"></i>
    					</li>
    				</ul>
    			</FormItem>
          <!-- 新增CN Invoice上传区域 -->
          <FormItem :label="$t('channelBill.cnInvoice')" prop="cnInvoiceFile" style="font-size: 14px;font-weight: bold;" v-if="['2','3','5'].includes(rowBillData.accountingType)">
            <Upload type="drag" v-model="formobj.cnInvoiceFile" :action="uploadUrl" :before-upload="handleCnInvoiceBeforeUpload"
              :on-progress="cnInvoiceUploading" style="width: 250px; margin-top: 20px;">
              <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>{{$t('channelBill.uploadCnInvoice')}}</p>
              </div>
            </Upload>
            <ul class="ivu-upload-list" v-if="cnInvoiceFile" style="width: 300px;">
              <li class="ivu-upload-list-file ivu-upload-list-file-finish">
                <span>
                  <Icon type="ios-folder" />{{cnInvoiceFile.name}}
                </span>
                <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" @click="removeCnInvoiceFile"></i>
              </li>
            </ul>
          </FormItem>
          <FormItem :label="$t('fuelPack.Amount')" prop="amount">
          	<Input v-model="formobj.amount" :placeholder="$t('channelBill.inputAmount')" disabled style="width: 250px" />
          </FormItem>
        </Form>
    		<div style="text-align: center;margin: 40px 0 0 0;">
    			<Button style="margin-right: 30px" @click="cancelModal">{{$t('support.back')}}</Button>
    			<Button type="primary" @click="pictureSubmit" v-preventReClick
    				:loading="pictureLoading">{{$t('common.determine')}}</Button>
    		</div>
    	</div>
    </Modal>
    <a ref="downloadLink" style="display: none"></a>
  </Card>
</template>

<script>
import { getCoprList } from "@/api/finance/serviceRechargeApproval";
import {
  queryChannelBill,
  exportBillFile,
  addPicture,
  exportHistoryRecords
} from "@/api/channel/channelBillingQuery";
import {
  exportInvoice,
} from "@/api/finance/corp";
import { opsearch } from "@/api/channel.js";
const math = require("mathjs");
export default {
  data() {
    const validateNum = (rule, value, callback) => {
    	if (parseFloat(value) < 0) {
    	  callback(new Error(this.$t('channelBill.lessThan0')));
    	  return;
    	}

      var str1 = value
    	if (value.substr(0, 1) === '-') {
    		str1 = value.substr(1, value.length)
    	}
      var str = /^(?!0(\.0+)?$)([1-9]\d{0,7}(\.\d{1,2})?|\d{1,8}(\.\d{0,2})?)$/;
      if (!str1 || str.test(str1)) {
      	callback();
      } else{
      	callback(new Error(this.$t('channelBill.checkNumber')));
      }
    };
    // 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
    const validateUpload = (rule, value, callback) => {
    	if (!this.file) {
    		callback(new Error(this.$t('support.pleaseUploadFile')))
    	} else {
    		callback()
    	}
    }
    return {
      corpNameListCorpId: [], // 渠道商corpid列表
      form: {
        beginMonth: "",
        endMonth: "",
        chargeStatus: "",
        corpId: [],
      },
      searchObj: {
        name: "",
        countryName: "",
      },
      total: 0,
      corpLists: [],
      pageSize: 10,
      page: 1,
      loading: false,
      exportModal: false,
      exportModalr: false,
      searchLoading: false,
      exportLoading: false,
      pictureLoading: false,
      remind: false,
      infoModal: false,
      PaymentModal: false,
      taskName: "",
      taskId: "",
      accountId: "",
      corpId: "",
      corpLists: [],
      localList: [],
      taskIds: [],
      taskNames: [],
      data: [],
      columns: [
        {
          title: "渠道商名称",
          key: "corpName",
          align: "center",
          tooltip: true,
          minWidth: 150,
        },
        {
          title: "账单ID",
          key: "billId",
          align: "center",
          tooltip: true,
          minWidth: 160,
        },
        {
          title: "账单类型",
          key: "accountingType",
          align: "center",
          tooltip: true,
          minWidth: 150,
          render: (h, params) => {
            const row = params.row;
            const text =
              row.accountingType == "0"
                ? "代销"
                : row.accountingType == "1"
                ? "代销"
                : row.accountingType == "2"
                ? "A2Z"
                : row.accountingType == "3"
                ? "A2Z"
                : row.accountingType == "4"
                ? "合并"
                : row.accountingType == "5"
                ? "A2Z"
                : row.accountingType == "6"
                ? "合并"
                : row.accountingType == "7"
                ? "合并"
                : "";
            return h("label", text);
          },
        },
        {
          title: "出账月份",
          key: "statTime",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
          render: (h, params) => {
            const row = params.row;
            const year = row.statTime.slice(0, 4);
            const month = row.statTime.slice(4, 6);
            const text = `${year}-${month}`;
            return h("label", text);
          },
        },
        {
          title: "账单总金额",
          key: "saleIncome",
          align: "center",
          tooltip: true,
          minWidth: 150,
        },
        {
          title: "应缴账单金额",
          key: "realIncome",
          align: "center",
          minWidth: 200,
          render: (h, params) => {
          	const row = params.row;
          	const text = row.channelType == "2" ? '0' : row.realIncome
          	return h('label', text);
          }
        },
        {
          title: "缴费状态",
          key: "chargeStatus",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 1000,
          render: (h, params) => {
            const row = params.row;
            let text = '';
            if (row.channelType == "2") {
              text = '已核销'
            } else {
              text = row.chargeStatus == "0"
                ? "未付款"
                : row.chargeStatus == "1"
                  ? "待确认到账"
                  : row.chargeStatus == "2"
                    ? "付款成功"
                    : row.chargeStatus == "3"
                      ? "付款失败"
                      : row.chargeStatus == "4"
                        ? "已取消"
                        : row.chargeStatus == "5"
                          ? "线上缴费中"
                          : "";
            }
            return h("label", text);
          },
        },
        {
          title: "操作",
          slot: "action",
          width: 233,
          align: "center",
          fixed: "right",
        },
        {
          title: "文件下载",
          slot: "file",
          width: 275,
          align: "center",
          fixed: "right",
        },
      ],
      ruleInline: {
        beginMonth: [
          {
            type: "date",
            required: true,
            message: "请选择开始月份",
            trigger: "blur",
          },
        ],
        endMonth: [
          {
            type: "date",
            required: true,
            message: "请选择结束月份",
            trigger: "blur",
          },
        ],
      },
      uploadUrl: '', //上传地址
      uploadList: [],
      file: null,
      formobj: {
      	file: "",
        amount: '',
        cnInvoiceFile: '', // 新增字段
      },
      ruleobj: {
      	file: [{
      		required: true,
      		validator: validateUpload,
      		trigger: 'change',
      	}],
        amount: [{
      		required: true,
      		message: this.$t('channelBill.inputAmount'),
      		trigger: 'change',
      	},{
      			validator: validateNum,
      			// message: "最高支持8位整数和2位小数的正数",
      	},],
        cnInvoiceFile: [{
          required: false, // 如需必填可改为true
          validator: (rule, value, callback) => {
            callback(); // 非必填直接通过
          },
          trigger: 'change',
        }]
      },
      cnInvoiceFile: '', // 新增字段
      cnInvoiceMessage: '',
      searchBeginTime: "",
      searchEndTime: "",
      type: "",
      cooperationMode: "",
      rowType: "",
      a2zAmount: "", //流量费
      imsiAmount: "", //卡费
      directIncome: "", //套餐费
      accountId: "",
      rowBillData: {},

    };
  },
  mounted() {
    this.cooperationMode = sessionStorage.getItem("cooperationMode");
  },
  created() {
    this.getCoprList();
  },
  methods: {
    getCoprList () {
      getCoprList({
        userName: this.$store.state.user.userName,
      }).then(res => {
        if (res.code == '0000') {
          this.corpLists = res.data
          res.data.forEach(item => {
            this.corpNameListCorpId.push(item.corpId)
          })
        }
      })
    },
    startChange(e) {
      this.searchBeginTime = e;
    },
    endChange(e) {
      this.searchEndTime = e;
    },
    getTableData(page,newSearchBeginTime) {
      this.loading = true;
      queryChannelBill({
        dateStart: newSearchBeginTime ? newSearchBeginTime : this.searchBeginTime,
        dateEnd: this.searchEndTime,
        corpId: this.form.corpId.length ? this.form.corpId : this.corpNameListCorpId,
        pageNum: page,
        pageSize: this.pageSize,
        billType: this.cooperationMode,
        chargeStatus: this.form.chargeStatus
      })
        .then((res) => {
          if (res.code == "0000") {
            this.loading = false;
            this.searchLoading = false;
            this.page = page;
            this.currentPage = page;
            this.total = Number(res.count);
            this.data = res.data;
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
          this.searchLoading = false;
        });
    },
    search(name) {
      let limitTime = '202409'
      this.$refs[name].validate((valid) => {
      	if (valid) {
          // 开始时间和结束时间都早于2024年9月  数据置为空
          if (new Date(this.searchBeginTime) > new Date(this.searchEndTime)) {
            this.$Message['warning']({
              background: true,
              content: "结束月份需大于等于开始月份！"
            });
          } else if (new Date(this.searchBeginTime) < new Date(limitTime) && new Date(this.searchEndTime) < new Date(limitTime)) {
            this.page = 1
            this.currentPage = 1
            this.total = 0
            this.data = []
          } else if (new Date(this.searchBeginTime) < new Date(limitTime) && new Date(this.searchEndTime) >= new Date(limitTime)) {
            let newSearchBeginTime = '202409'
            this.searchLoading = true
            this.getTableData(1,newSearchBeginTime)
          } else {
            this.searchLoading = true
            this.getTableData(1)
          }
      	}
      })
    },
    loadByPage(page) {
      let limitTime = '202409'
      // 开始时间和结束时间都早于2024年9月  数据置为空
      if (new Date(this.searchBeginTime) > new Date(this.searchEndTime)) {
        this.$Message['warning']({
          background: true,
          content: "结束月份需大于等于开始月份！"
        });
      } else if (new Date(this.searchBeginTime) < new Date(limitTime) && new Date(this.searchEndTime) < new Date(limitTime)) {
        this.page = 1
        this.currentPage = 1
        this.total = 0
        this.data = []
      } else if (new Date(this.searchBeginTime) < new Date(limitTime) && new Date(this.searchEndTime) >= new Date(limitTime)) {
        let newSearchBeginTime = '202409'
        this.getTableData(page,newSearchBeginTime)
      } else {
        this.getTableData(page)
      }
    },
    //按条件导出
    exportHistoryRecords(name) {
      this.$refs[name].validate((valid) => {
      	if (valid) {
          let newSearchBeginTime = "";
          let limitTime = '202409';

          // 检查时间条件
          if (new Date(this.searchBeginTime) > new Date(this.searchEndTime)) {
            this.$Message['warning']({
              background: true,
              content: "结束月份需大于等于开始月份！"
            });
            return; // 直接返回，不执行后续导出
          }

          if (new Date(this.searchBeginTime) < new Date(limitTime) && new Date(this.searchEndTime) < new Date(limitTime)) {
            this.$Message['warning']({
              background: true,
              content: "未找到可导出的数据！"
            });
            return; // 直接返回，不执行后续导出
          }

          // 只有后两种情况会继续执行到这里
          if (new Date(this.searchBeginTime) < new Date(limitTime) && new Date(this.searchEndTime) >= new Date(limitTime)) {
            newSearchBeginTime = '202409';
          } else {
            newSearchBeginTime = this.searchBeginTime;
          }

          // 执行导出操作
          this.exportLoading = true;
          exportHistoryRecords({
            dateStart: newSearchBeginTime,
            dateEnd: this.searchEndTime,
            corpId: this.form.corpId.length ? this.form.corpId : this.corpNameListCorpId,
            billType: this.cooperationMode,
            chargeStatus: this.form.chargeStatus
          })
          .then((res) => {
            const content = res.data;
            let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[1]);
            if ('download' in document.createElement('a')) {
              const link = this.$refs.downloadLink;
              let url = URL.createObjectURL(content);
              link.download = fileName;
              link.href = url;
              link.click();
              URL.revokeObjectURL(url);
            } else {
              navigator.msSaveBlob(content, fileName);
            }
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {
            this.exportLoading = false;
          });
        }
      })
    },
    // Invoice
    exportInvoice(row) {
      exportInvoice({
        corpName: row.corpName,
        invoicePath: row.invoicePath,
        month: row.statTime,
        corpId: row.corpId,
        billType: row.accountingType,
        incomeId: row.id,
      })
        .then((res) => {
          if (res && res.code == "0000") {
            this.exportModalr = true;
            this.taskId = res.data.taskId;
            this.taskName = res.data.taskName;
          } else {
            throw res;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //明细
    showInfo(row) {
      this.a2zAmount = row.a2zAmount
      this.imsiAmount = row.imsiAmount
      this.directIncome = row.directIncome
      this.infoModal = true
    },
    // 账单文件导出
    exportBillFile(row) {
      let newBeginTime = ''
      let limitTime = '202409'
      if (new Date(this.searchBeginTime) < new Date(limitTime) && new Date(this.searchEndTime) >= new Date(limitTime)) {
        newBeginTime = '202409'
      } else {
        newBeginTime = this.searchBeginTime
      }
      let data = {
        "corpId": row.corpId,
        "beginMonth": newBeginTime,
        "endMonth": this.searchEndTime,
        "cooperationMode": row.accountingType,
        "corpName": row.corpName,
        "currencyCode": row.currency,
        "billType": row.accountingType,
        "id": row.id,
        "statTime": row.statTime,
        "dateStart": row.svcStartTime,
        "dateEnd": row.svcEndTime,
        "userId": this.$store.state.user.userId,
        "type": row.accountingType == '2' ? '1' : row.accountingType == '3' ? '2' : row.accountingType == '4' ? 1 :
          row.accountingType == '5' ? '3': row.accountingType == '6' ? '2' : row.accountingType == '7' ? '3' : '',
      }
      exportBillFile(data).then(res => {
      	if (res && res.code == '0000') {
      		this.exportModal = true
      		var _this = this
      		if (Object.values(res.data).length > 0) {
      			Object.values(res.data).forEach(function(value) {
      				_this.taskIds.push(value.taskId)
      				_this.taskNames.push(value.taskName)
      				if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
      					let taskid =  _this.taskIds.slice(0,3)
      					let taskname = _this.taskNames.slice(0,3)
      					_this.taskIds = taskid
      					_this.taskNames = taskname
      					_this.remind = true
      				}
      			})
      		}
      	} else {
      		throw res
      	}
      }).catch((err) => {
      	console.log(err)
      })
    },
    exportcancelModal() {
      this.exportModalr = false;
    },
    //线下支付
    offinePay(row, type) {
      this.accountId = row.id
      this.corpId = row.corpId
      this.rowType = type
      this.formobj.amount = row.realIncome.toString()
      this.rowBillData = row
      this.PaymentModal = true
    },
    // 线下支付提交
    pictureSubmit() {
    	this.$refs["formobj"].validate((valid) => {
    		if (valid) {
    			var formData = new FormData();
    			formData.append('amount', this.formobj.amount);
    			formData.append('corpId', this.corpId);
    			formData.append('accountId', this.accountId);
    			formData.append('paymentProofs', this.file); //封面
    			formData.append('repeatedUpload', this.rowType == '2' ? true : false); //封面
          formData.append('chargeType', '1'); //账单缴付
          if(this.cnInvoiceFile){
            formData.append('cnInvoice', this.cnInvoiceFile); // 新增CN Invoice
          }
    			this.pictureLoading = true
    			addPicture(formData).then((res) => {
    				if (res.code === "0000") {
    					this.$Notice.success({
    						title: this.$t('address.Operationreminder'),
    						desc: this.$t('common.Successful'),
    					});
    					this.pictureLoading = false
    					this.PaymentModal = false;
              let newSearchBeginTime = '202409'
    					this.getTableData(1, newSearchBeginTime)
    					this.file = ''
              this.cnInvoiceFile = ''
    					this.$refs["formobj"].resetFields();
    				} else {
    					throw res
    				}
    			}).catch((err) => {
    				this.pictureLoading = false
    			}).finally(() => {

    			})
    		}
    	});
    },
    // 上传文件
    handleBeforeUpload(file, fileList) {
    	const sizeLimit = file.size / 1024 / 1024 > 10
    	if (sizeLimit) {
    		this.$Notice.warning({
    			title: this.$t('address.Operationreminder'),
    			desc: this.$t('support.pictureSize')
    		});
    		return false;
    	}
    	this.file = file,
    	this.uploadList = fileList
    	return false;
    },
    fileUploading(event, file, fileList) {
    	this.message = this.$t('support.fileUploadedAndProgressDisappears')
    },
    removeFile() {
    	this.file = ''
    },
    handleCnInvoiceBeforeUpload(file, fileList) {
      const sizeLimit = file.size / 1024 / 1024 > 10
      if (sizeLimit) {
        this.$Notice.warning({
          title: this.$t('address.Operationreminder'),
          desc: this.$t('support.pictureSize')
        });
        return false;
      }
      this.cnInvoiceFile = file
      return false;
    },
    cnInvoiceUploading(event, file, fileList) {
      this.cnInvoiceMessage = this.$t('support.fileUploadedAndProgressDisappears')
    },
    removeCnInvoiceFile() {
      this.cnInvoiceFile = ''
      this.cnInvoiceMessage = ''
    },
    cancelModal() {
      this.taskIds = [];
      this.taskNames = [];
      this.exportModal = false;
      this.PaymentModal = false
      this.rowBillData = {}
      this.file = ''
      this.cnInvoiceFile = ''
      this.$refs['formobj'].resetFields()
    },
    Gotor() {
      this.$router.push({
        path: "/taskList",
        query: {
          taskId: encodeURIComponent(this.taskId),
          fileName: encodeURIComponent(this.taskName),
        },
      });
      this.exportcancelModal();
      this.exportModalr = false;
    },
    Goto() {
      this.$router.push({
        path: "/taskList",
        query: {
          taskId: encodeURIComponent(this.taskId),
          fileName: encodeURIComponent(this.taskName),
        },
      });

      this.exportModal = false;
    },
    //国家/地区
    getLocalList() {
      opsearch()
        .then((res) => {
          if (res && res.code == "0000") {
            var list = res.data;
            this.localList = list;
            this.localList.sort(function (str1, str2) {
              return str1.countryEn.localeCompare(str2.countryEn);
            });
          } else {
            throw res;
          }
        })
        .catch((err) => {})
        .finally(() => {});
    },
  },
};
</script>

<style>
#space {
  /* height: 30px;
		line-height: 30px; */
  font-size: 12px;
  white-space: pre-line;
  list-style: none;
}
.task-name {
  display: inline-block; /* 或者 block，取决于你的布局需求 */
  width: 300px; /* 根据需要设置合适的宽度 */
  /* white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; */
  word-break: break-all;
  padding: 5px; /* 内边距 */
  margin-bottom: 10px; /* 外边距 */
}
</style>
