<template>
  <!-- 出账管理 -->
  <Card>
    <Form ref="form" :label-width="80" :model="form" inline>
      <FormItem label="渠道商名称">
        <!-- <Input
          v-model="form.corpName"
          placeholder="请输入渠道商名称"
          :clearable="true"
          style="width: 190px"
        >
        </Input> -->
        <Select v-model="form.corpName" multiple style="width:260px">
        <Option v-for="item in corpNameList" :value="item.corpId" :key="item.corpId">{{ item.corpName }}</Option>
    </Select>
      </FormItem>
      <Button
        v-has="'search'"
        type="primary"
        icon="md-search"
        :loading="searchloading"
        @click="search()"
        >搜索</Button
      >&nbsp;&nbsp;
      <Button v-has="'historyBill'" type="warning" @click="GotoHistroy()">历史账单</Button
      >&nbsp;&nbsp;
    </Form>
    <!-- 表格 -->
    <Table
      :columns="columns12"
      :data="talbedata"
      style="width: 100%; margin-top: 50px"
      :loading="loading"
    >
    </Table>
    <!-- 分页 -->
    <div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px">
      <Page
        :total="total"
        :current.sync="currentPage"
        show-total
        show-elevator
        @on-change="goPage"
      />
    </div>
  </Card>
</template>

<script>
import mixin from "@/mixin/validate";
import { getCorpList,getCoprList } from "@/api/finance/serviceRechargeApproval";
import invoiceTemplate from "@/components/invoice/invoiceTemp";
const math = require("mathjs");
export default {
  mixins: [mixin],
  components: {
    invoiceTemplate,
  },
  data() {
    const validatePositiveNum = (rule, value, callback) => {
      var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
      if (!value || str.test(value)) {
        callback();
      } else {
        callback(new Error(rule.message));
      }
    };
    const validateNum = (rule, value, callback) => {
      var str1 = value;
      if (value.substr(0, 1) === "-") {
        str1 = value.substr(1, value.length);
      }
      var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
      if (!str1 || str.test(str1)) {
        callback();
      } else {
        callback(new Error(rule.message));
      }
    };
    return {
      corpNameList: [], // 渠道商名称列表
      corpNameListCorpId: [], // 渠道商corpid列表
      columns12: [
        {
          title: "渠道商名称",
          key: "corpName",
          align: "center",
        },
        {
          title: "关联销售账号",
          key: "account",
          align: "center",
        },
        {
          title: "合作模式",
          key: "cooperationMode",
					align: "center",
					render: (h, params) => {
							const row = params.row;
							const text = row.cooperationMode == '1' ? "代销" : row.cooperationMode == '2' ? "a2z" : row.cooperationMode == '3' ? "资源合作" :
								'';
							return h('label', text);
						}
        },
        {
          title: "币种",
          key: "currencyCode",
					align: "center",
					render: (h, params) => {
							const row = params.row;
							const text = row.currencyCode == '156' ? "CNY" : row.currencyCode == '840' ? "USD" : row.currencyCode == '344' ? "HKD" :
								'';
							return h('label', text);
						}
        },
        {
          title: "承诺销售金额",
          key: "contractSellAmount",
          align: "center",
        },
        {
          title: "已完成承诺金额",
          key: "completedAmount",
          align: "center",
        },
        {
          title: "本期账单",
          key: "currentPeriodBill",
          align: "center",
        },
        {
          title: "欠费金额",
          key: "arrears",
          align: "center",
        },
      ],
      form: {
        corpName: [],
			},
			searchloading:false,
      data: [],
      talbedata: [],
      row: {},
      currentPage: 1,
      total: 0,
      pageSize: 10,
      page: 1,
      spanData: [],
      loading: false,
      rule: {
        accountAdjustment: [
          {
            required: true,
            message: "请输入调账金额",
            trigger: "blur",
          },
          {
            validator: validateNum,
            message: "最高支持8位整数和2位小数的正负数",
          },
        ],
        flowAdjustment: [
          {
            required: true,
            message: "请输入流量收入",
            trigger: "blur",
          },
          {
            validator: validateNum,
            message: "最高支持8位整数和2位小数的正负数",
          },
        ],
        imsiAdjustment: [
          {
            required: true,
            message: "请输入IMSI费收入",
            trigger: "blur",
          },
          {
            validator: validateNum,
            message: "最高支持8位整数和2位小数的正负数",
          },
        ],
      },
      searchBeginTime: "",
      searchEndTime: "",
      type: "",
      cooperationMode: "",
    };
  },
	created () {
    // this.goPageFirst(1)
    this.getCoprList()
	},
	mounted () {
	},
	methods: {
    getCoprList () {
      getCoprList({
        userName: this.$store.state.user.userName,
      }).then(res => {
        if (res.code == '0000') {
          this.corpNameList = res.data
          res.data.forEach(item => {
            this.corpNameListCorpId.push(item.corpId)
          })
        }
      })
    },
    GotoHistroy() {
      this.$router.push({
        path: "/channelSellHistory",
        // query: {
        //   taskId: encodeURIComponent(this.taskId),
        //   fileName: encodeURIComponent(this.taskName),
        // },
      });
    },
    search() {
      this.searchloading = true;
      this.goPageFirst(1);
      this.currentPage = 1
    },
    goPage(page) {
      this.goPageFirst(page);
    },
    goPageFirst(page) {
      this.loading = true;
      var _this = this;
      let pageNum = page;
      let pageSize = 10;

			getCorpList({
				corpId: this.form.corpName.length ? this.form.corpName : this.corpNameListCorpId,
        pageNum,
        pageSize,
        userId: this.$store.state.user.userId,
      })
        .then((res) => {
          if (res.code == "0000") {
						_this.loading = false;
            this.searchloading = false;
            this.page = page;
            this.total = res.count;
            this.talbedata = res.data;
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          console.log(111)
          this.loading = false;
          this.searchloading = false;
        });
    },

    test(str) {
      var re = /(\d{1,3})(?=(\d{3})+(?:$|\.))/g;
      return (str + "").replace(re, "$1,");
    },
    formatNumber(num) {
      // 首先，将数字除以100并保留两位小数
      const result = (num / 100).toFixed(2);
      const formatted = new Intl.NumberFormat("en-US", {
        minimumFractionDigits: 2, // 确保总是显示两位小数
        maximumFractionDigits: 2, // 最多显示两位小数
        useGrouping: true, // 启用千位分隔符
      }).format(parseFloat(result));
      return formatted;
    },

    //计算需要合并的单元格
    getSpanData(data) {
      var t = this;
      var pos = 0;
      t.spanData = [];
      data.forEach(function (item, index) {
        if (index === 0) {
          t.spanData.push(1);
          pos = 0;
        } else {
          if (
            data[index].salesChannel === data[index - 1].salesChannel &&
            data[index].statTime === data[index - 1].statTime
          ) {
            t.spanData[pos] += 1;
            t.spanData.push(0);
          } else {
            t.spanData.push(1);
            pos = index;
          }
        }
      });
    },
  },
};
</script>

<style>
#space {
  /* height: 30px;
		line-height: 30px; */
  font-size: 12px;
  white-space: pre-line;
  list-style: none;
}
.task-name {
  display: inline-block; /* 或者 block，取决于你的布局需求 */
  width: 300px; /* 根据需要设置合适的宽度 */
  /* white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; */
  word-break: break-all;
  padding: 5px; /* 内边距 */
  margin-bottom: 10px; /* 外边距 */
}
</style>
