<template>
	<!-- 地址管理 -->
	<Card>
		<div style="width: 100%;margin-top: 50px; margin: auto;">
			<div style="display: flex;width: 100%;">
				<span style="margin-top: 4px;font-weight:bold;">{{$t('address.account')}}:</span>&nbsp;&nbsp;
				<Input v-model="fullname" :placeholder="$t('address.account')" prop="showTitle" clearable style="width: 200px" />&nbsp;&nbsp;
				<span style="margin-top: 4px;font-weight:bold;">{{$t('address.mailbox')}}:</span>&nbsp;&nbsp;
				<Input v-model="mailbox" :placeholder="$t('address.input_mailbox')" prop="showTitle" clearable style="width: 200px" />&nbsp;&nbsp;
				<Button v-has="'search'" :disabled="cooperationMode == '3'" type="primary" icon="md-search" :loading="searchloading" @click="search()">{{$t('address.search')}}</Button>
				<!-- <Button icon="md-add" type="success" style="margin-left: 20px;" @click=" address()">{{$t('address.Newaddress')}}</Button> -->
			</div>
			<!-- 表格 -->
			<Table :columns="columns12" :data="data" style="width:100%;margin-top: 50px;" :loading="loading">
				<template slot-scope="{ row }" slot="username">
					<strong>{{ row.username }}</strong>
				</template>
				<template slot-scope="{ row }" slot="email">
					<strong>{{ row.email }}</strong>
				</template>
				<template slot-scope="{ row }" slot="address">
					<strong>{{ row.address }}</strong>
				</template>
				<template slot-scope="{ row, index }" slot="action">
					<Button v-has="'update'" type="info" size="small" style="margin-right: 5px" @click="update(row)">{{$t('address.modify')}}</Button>
					<Button v-if="row.username === username ? false : true" v-has="'delete'" type="error" size="small" style="margin-right: 5px" @click="Delete(row)">{{$t('address.Delete')}}</Button>
					<Button v-has="'updatePwd'" type="warning" size="small" style="margin-right: 5px" @click="forgetpwd(row)">{{$t('address.Forgetpwd')}}</Button>
					<!-- <Button v-if="row.default" type="success" size="small" style="margin-right: 5px" @click="Ifdefault(row,1)">{{$t('address.setdefault')}}</Button>
          <Button v-else type="default" size="small" style="margin-right: 5px" @click="Ifdefault(row,2)">{{$t('address.setdefault')}}</Button> -->

				</template>
			</Table>
			<!-- 分页 -->
			<div style="margin-top: 100px;  ">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
			<!-- 添加地址 -->
			<Modal v-model="addmodel" :title="$t('address.Newaddress')" :mask-closable="false" @on-cancel="cancelModal">
				<Form ref="addform" :model="addform" :rules="rules" :label-width="100">
					<FormItem :label="$t('address.fullname')" prop="name">
						<Input v-model="addform.name" :placeholder="$t('address.input_fullname')" prop="showTitle" clearable style="width: 200px" />
					</FormItem>
					<FormItem :label="$t('address.mailbox')" prop="email">
						<Input v-model="addform.email" :placeholder="$t('address.input_mailbox')" prop="showTitle" clearable style="width: 200px" />
					</FormItem>
					<!-- 					<FormItem :label="$t('address.password')" prop="password">
						<Input v-model="addform.password" :placeholder="$t('address.input_pwd')" type="password" password prop="showTitle"
						 clearable style="width: 200px" />
					</FormItem>
					<FormItem :label="$t('address.password_ok')" prop="confirmPwd">
						<Input v-model="addform.confirmPwd" :placeholder="$t('address.input_pwd')" type="password" password prop="showTitle"
						 clearable style="width: 200px" />
					</FormItem> -->
				</Form>
				<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
					<Button @click="cancelModal">取消</Button>
					<Button type="primary" @click="addsave">确定</Button>
				</div>
			</Modal>
			<!-- 修改地址 -->
			<Modal v-model="updatemodel" :title="$t('address.modifyaddress')" :mask-closable="false" @on-cancel="updatecancelModal">
				<Form ref="updateform" :model="updateform" :rules="rules" :label-width="80">
					<!-- <FormItem :label="$t('address.fullname')" prop="name">
						<Input v-model="updateform.name" :placeholder="$t('address.input_fullname')" prop="showTitle" clearable style="width: 200px" />
					</FormItem> -->
					<FormItem :label="$t('address.mailbox')" prop="email">
						<Input v-model="updateform.email" :placeholder="$t('address.input_mailbox')" prop="showTitle" clearable style="width: 300px" />
					</FormItem>
					<!-- 					<FormItem :label="$t('address.password')" prop="password">
						<Input v-model="updateform.password" :placeholder="$t('address.input_pwd')" type="password" password prop="showTitle"
						 clearable style="width: 200px" />
					</FormItem>
					<FormItem :label="$t('address.password_ok')" prop="confirmPwd">
						<Input v-model="updateform.confirmPwd" :placeholder="$t('address.input_pwd')" type="password" password prop="showTitle"
						 clearable style="width: 200px" />
					</FormItem> -->
				</Form>
				<div slot="footer" style="text-align: center;">
					<Button @click="updatecancelModal">{{$t('common.cancel')}}</Button>
					<Button type="primary" @click="updatesave">{{$t('address.determine')}}</Button>
				</div>
			</Modal>
			<!-- 修改密码 -->
			<Modal v-model="updatepwd" :title="$t('address.Forgetpwd')" :mask-closable="false" @on-cancel="cancelModal">
				<Form ref="pwdform" :model="pwdform" :rules="pwdrules" :label-width="130">
					<FormItem :label="$t('address.oldPwd')" prop="password">
						<Input v-model="pwdform.password" type="password" password :placeholder="$t('address.input_pwd')" style="width: 300px" />
					</FormItem>
					<FormItem :label="$t('address.newpwd')" prop="newpwd">
						<Input v-model="pwdform.newpwd" type="password" password :placeholder="$t('address.input_pwd')" style="width: 300px" />
					</FormItem>
					<FormItem :label="$t('address.password_ok')" prop="password_ok">
						<Input v-model="pwdform.password_ok" type="password" password :placeholder="$t('address.input_pwd')" style="width: 300px" />
					</FormItem>
				</Form>
				<Alert type="warning" show-icon>{{$t('address.PwdRules')}}<a href="#" @click="showRules(1)">{{$t('address.watch')}}</a>{{$t('address.more')}}</Alert>
				<div slot="footer" style="text-align: center;">
					<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
					<Button type="primary" @click="pwdsave">{{$t('address.determine')}}</Button>
				</div>
			</Modal>
			<Modal v-model="Rules" :mask-closable="false" @on-cancel="showRules(2)">
				<Alert type="warning">
					<div v-if="this.$i18n.locale==='zh-CN'">
						<text-view></text-view>
					</div>
					<div v-if="this.$i18n.locale==='en-US'">
						<text-viewEn></text-viewEn>
					</div>
				</Alert>
			</Modal>

		</div>
	</Card>

</template>

<script>
	import {
		addressList,
		newAddress,
		update,
		delAddress,
		forgetPwd,
		setdefault,
		searchcorpid
	} from '@/api/channel'
	import TextView from './text.vue';
	import TextViewEn from './textEn.vue';
	import {
	  mapActions
	} from 'vuex'
	// import md5 from 'js-md5';
	export default {
		components: {
		  TextView,
		  TextViewEn
		},
		data() {
			var checkpwd = (rule, value, callback) => {
				//  校验英文的正则
				if (value == this.pwdform.newpwd) {
					//校验通过
					callback();
				} else {
					callback(new Error(this.$t("address.inconsistent")));
				}
			};
			var validateEmail = (rule, value, callback) => {
				// var emailReg = /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
				if (!value || value.indexOf("@") == -1) {
					callback(new Error(this.$t("address.emailaddress")));
				} else {
					callback();
				}
			};
			var validatePwd = (rule, value, callback) => {
				var pwpattent =
					/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/
				if (pwpattent.test(value) == false) {
					callback(new Error(this.$t("address.reset")));
					return;
				}
				if (/(.)\1{2}/i.test(value)) {
					callback(new Error(this.$t("address.appear")));
					return;
				}
				var alphnumon = /((?:0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)|9(?=0)){2}\d)/
				if (alphnumon.test(value)) {
					callback(new Error(this.$t("address.allowed"))); //字母或
					return;
				} else {
					callback();
				}
			};
			return {
				cooperationMode: '',
				fullname: '',
				mailbox: '',
				addform: {},
				Rules:false,
				updateform: {},
				pwdform: {},
				form: {},
				total: 0,
				corpId: '',
				currentPage: 1,
				page: 0,
				addmodel: false,
				updatepwd: false,
				updatemodel: false,
				defaultflg: false,
				loading: false,
				username: '',
				ifsame: false,
				searchloading: false,
				columns12: [{
						title: this.$t("address.account"),
						slot: 'username',
						align: 'center',
						render: (h, params) => {
							const row = params.row
							this.ifsame = row.username === this.username ? false : true
							return h('label', row.username)
						}
					},
					{
						title: this.$t("address.mailbox"),
						slot: 'email',
						align: 'center'
					},
					{
						title: this.$t("address.action"),
						slot: 'action',
						align: 'center'
					}
				],
				data: [],
				data1: [],
				rules: {
					email: [{
						required: true,
						message: this.$t("address.input_mailbox"),
						trigger: "blur",
					}, {
						required: true,
						validator: validateEmail
					}],
					password: [{
						required: true,
						message: this.$t("address.input_pwd"),
						trigger: "blur",
					}],
					confirmPwd: [{
						required: true,
						message: this.$t("address.input_pwd"),
						trigger: "blur",
					}],

				},
				pwdrules: {
					password: [{
						required: true,
						message: this.$t("address.input_pwd"),
						trigger: "blur",
					},{
						validator: validatePwd,
						trigger: 'blur'
					}
					],
					newpwd: [{
						required: true,
						message: this.$t("address.input_pwd"),
						trigger: "blur",
					},{
						validator: validatePwd,
						trigger: 'blur'
					}
					],
					password_ok: [{
						required: true,
						message: this.$t("address.input_pwd"),
						trigger: "blur",
					}, {
						required: true,
						validator: checkpwd,
					}, ],

				},
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			this.username = this.$store.state.user.userName
			if (this.cooperationMode != '3') {
				this.goPageFirst(1)
			}
		},
		methods: {
			...mapActions([
			  'handleLogOut'
			]),
			goPageFirst(page) {
				this.loading = true
				var _this = this
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						let corpId = res.data
						this.corpId = corpId
						let pageNum = page
						let pageSize = 10
						let username = this.fullname === "" ? null : this.fullname
						let email = this.mailbox === "" ? null : this.mailbox
						addressList({
							pageSize,
							pageNum,
							username,
							email,
							corpId
						}).then(res => {
							if (res.code == '0000') {
								_this.loading = false
								this.searchloading = false
								this.page = page
								this.currentPage = page
								this.total = res.data.total
								this.data = res.data.records
							}
						}).catch((err) => {
							console.error(err)
						}).finally(() => {
							this.loading = false
							this.searchloading = false
						})
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			//查询
			search() {
				this.goPageFirst(1)
				this.searchloading = true
			},
			// 添加地址
			address() {
				this.addmodel = true
			},
			showRules(id){
				if(id===1){
					this.Rules=true
				}else{
					this.Rules=false
				}
			},
			//修改地址
			update(row) {
				this.data1 = row
				this.updateform = Object.assign({}, row);
				this.updatemodel = true
			},
			// 忘记密码
			forgetpwd(row) {
				this.data1 = row
				// this.pwdform=row
				this.updatepwd = true
			},
			// 删除
			Delete(row) {
				this.$Modal.confirm({
					title: this.$t("address.deleteitem"),
					onOk: () => {
						let id = row.id
						delAddress({
							id
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t("address.Operationreminder"),
									desc: this.$t("address.deleted")
								})
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.loading = false
						})

					}
				});
			},
			addsave() {
				this.$refs.addform.validate(valid => {
					if (valid) {
						newAddress(this.addform).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t("address.Operationreminder"),
									desc: '新建地址成功！'
								})
								this.addmodel = false
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.loading = false
						})
					}
				})
			},
			updatesave() {
				this.$refs.updateform.validate(valid => {
					if (valid) {
						update({
							"id": this.data1.id,
							"email": this.updateform.email,
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t("address.Operationreminder"),
									desc: this.$t("common.Successful")
								})
								this.updatemodel = false
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.updatemodel = false
							this.loading = false
						})
					}
				})
			},
			pwdsave() {
				this.$refs.pwdform.validate(valid => {
					if (valid) {
						let userInfoDto = {
							"id": this.data1.id,
							"newPass": this.pwdform.newpwd,
							"oldPass": this.pwdform.password
						}

						forgetPwd(userInfoDto).then(res => {
							if (res && res.code == '0000') {
								if(this.data1.username===this.username){
									this.$Notice.success({
									  title: this.$t("address.Operationreminder"),
									  desc: this.$t("sys.successfully")
									})
									var _this = this
									setTimeout(function() {
									  _this.logout()
									}, 1000)
								}else{
									this.$Notice.success({
										title: this.$t("address.Operationreminder"),
										desc: this.$t("common.Successful")
									})
									this.updatepwd = false
									this.pwdform = {
										'password': '',
										'newpwd': '',
										'password_ok': ''
									}
									this.$refs.pwdform.resetFields()
									this.goPageFirst(1)
								}
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.loading = false
							this.updatepwd = false
							this.pwdform = {
								'password': '',
								'newpwd': '',
								'password_ok': ''
							}
							this.$refs.pwdform.resetFields()
						})
					}
				})

			},
			logout() {
			  this.handleLogOut().then(() => {
			    this.$router.push({
			      name: 'login'
			    })
			  })
			},
			// 取消
			cancelModal() {
				this.updatepwd = false
				this.addmodel = false
				this.pwdform = {
					'password': '',
					'newpwd': '',
					'password_ok': ''
				}
				this.$refs.pwdform.resetFields()
			},
			updatecancelModal() {
				this.updatemodel = false
				this.$refs.updateform.resetFields()
			},


		}


	}
</script>

<style>
</style>
