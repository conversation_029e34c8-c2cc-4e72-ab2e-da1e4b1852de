<template>
	<!-- 二维码管理 -->
	<Card>
		<!-- 搜索条件 -->
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">{{$t( 'support.Ordertime' )}}:</span>
				<DatePicker format="yyyy-MM-dd" v-model="time" @on-change="handleDateChange" type="daterange"
					:placeholder="$t('support.timeFrame')"></DatePicker>
			</div>
			<div class="search_box">
				<span class="search_box_label">{{$t('support.orderBatch')}}:</span>
				<Input v-model="orderBatch" :clearable="true" :placeholder="$t('support.inputOrderBatch')" style="width: 200px;" />
			</div>
		</div>
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label"> {{$t('support.segmentSearch')}}: </span>
				<Input v-model="startNum" :clearable="true" :placeholder="$t('support.startNumber')" style="width: 200px;" />
			</div>
			<div class="search_box">
				<Input v-model="endNum" :clearable="true" :placeholder="$t('support.endNumber')" style="width: 200px;" />
			</div>
			<div class="search_box">
				<Button v-has="'search'" :disabled="cooperationMode == '3'" type="primary" icon="md-search" :loading="searchloading" @click="search()">{{$t('common.search')}}</Button>
				<Button v-has="'codeExport'" :disabled="cooperationMode == '3'" type="info" icon="ios-cloud-download-outline" :loading="exportloading"
					style="margin-left: 10px;" @click="downloadCode()">{{$t('support.codeExport')}}</Button>
				<Button v-has="'fileExport'" :disabled="cooperationMode == '3'" type="warning" icon="ios-cloud-download-outline" :loading="downloading"
					style="margin-left: 10px;" @click="downloadFile()">{{$t('support.fileExport')}}</Button>
			</div>
		</div>
		<a ref="downloadLink" style="display: none"></a>
		<!-- 表格 -->
		<Table ref="selection" :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading"
			@on-selection-change="handleRowChange" @on-select-cancel="cancelSigle"
			@on-select-all-cancel="cancelAll">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'download'" type="primary" ghost style="margin-right: 10px;" @click="download(row)">{{$t('support.xiazai')}}</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
					<FormItem :label="$t('exportID')">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem :label="$t('exportFlie')">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">{{$t('downloadResult')}}</span>
				</Form>
			</div>
		
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		searchcorpid
	} from '@/api/channel'
	import {
		esimList,
		exportFile,
		exportQrFile,
		downloadTask,
	} from "@/api/aqCode";
	export default {
		data() {
			return {
				cooperationMode: '', //合作模式
				total: 0,
				currentPage: 1,
				page: 0,
				corpId: '',
				time: [],
				startTime: "", //出库开始时间
				endTime: "", //出库结束时间
				orderBatch: "", //订单批次
				startNum: '', //起始号段
				endNum: '', //结束号段
				taskId: '',
				taskName: '',
				loading: false,
				searchloading: false, //查询加载
				exportloading: false, //导出加载
				downloading: false, //导出加载
				exportModal: false, //导出提示对话框
				selection: [], //多选
				selectionIds: [], //多选ids
				iccidList: [], //翻页勾选
				data: [], //表格列表
				columns: [{
						type: "selection",
						width: 60,
						align: "center",
					},{
					title: "ICCID",
					key: 'iccid',
					minWidth: 120,
					align: 'center'
				},{
					title: "IMSI",
					key: 'imsi',
					minWidth: 120,
					align: 'center'
				},{
					title: "MSISDN",
					key: 'msisdn',
					minWidth: 120,
					align: 'center'
				},{
					title: this.$t('support.Ordertime'),
					key: 'createTime',
					minWidth: 120,
					align: 'center',
				},{
					title: this.$t('support.operate'),
					slot: 'action',
					minWidth: 120,
					align: 'center',
					fixed: 'right'
				}, ]
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			if (this.cooperationMode != '3') {
				this.goPageFirst(1)
			}
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						let corpId = res.data
						this.corpId = corpId
					}
					esimList({
						corpId: this.corpId,
						cooperationMode: this.cooperationMode,
						pageSize: 10,
						pageNum: page,
						startTime: this.startTime ? this.startTime + " 00:00:00" : "",
						endTime: this.endTime ? this.endTime + " 23:59:59" : "",
						orderBatch: this.orderBatch ? this.orderBatch : null,
						startNum: this.startNum ? this.startNum : undefined,
						endNum: this.endNum ? this.endNum : undefined,
					}).then(res => {
						if (res.code == '0000') {
							_this.loading = false
							this.searchloading = false
							this.page = page
							this.currentPage = page
		
							var data = res.data;
							let List = []
							// 循环遍历data
							data.map((value, index) => {
								List.push(value)
							})
							//回显
							this.iccidList.forEach(item => {
								List.forEach(element => {
									if (element.iccid == item.iccid) {
										this.$set(element, '_checked', true)
									}
								})
							})
							this.data = List;
							this.total = res.count;
						}
					}).catch((err) => {
						console.error(err)
					}).finally(() => {
						_this.loading = false
						this.searchloading = false
					})
				}).catch((err) => {
				}).finally(() => {})
			},
			search:function(){
				this.searchloading = true
				this.goPageFirst(1)
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			// 选择时间段
			handleDateChange: function (times, type) {
			  this.startTime = times[0];
			  this.endTime = times[1];
			},
			// 二维码导出
			downloadCode() {
				this.exportloading = true
				//勾选的iccid
				let iccList = []
				this.iccidList.map((value, index) => {
					iccList.push(value.iccid)
				})
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						let corpId = res.data
						this.corpId = corpId
					}
					exportQrFile({
						corpId: this.corpId,
						startTime: this.startTime ? this.startTime + " 00:00:00" : "",
						endTime: this.endTime ? this.endTime + " 23:59:59" : "",
						iccidList: iccList,
						orderBatch: this.orderBatch ? this.orderBatch : null,
						startNum: this.startNum ? this.startNum : undefined,
						endNum: this.endNum ? this.endNum : undefined,
						cooperationMode: this.cooperationMode,
					}).then((res) => {
						this.exportModal = true
						this.taskId = res.data.taskId
						this.taskName = res.data.taskName
						this.exportloading = false
					}).catch(() => this.exportloading = false)
				}).catch((err) => {
				}).finally(() => {
				})		
			},
			// 文件导出
			downloadFile:function(){
				this.downloading = true
				//勾选的iccid
				let iccList = []
				this.iccidList.map((value, index) => {
					iccList.push(value.iccid)
				})
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						let corpId = res.data
						this.corpId = corpId
					}
					exportFile({
						corpId: this.corpId,
						iccidList: iccList,
						startTime: this.startTime ? this.startTime + " 00:00:00" : "",
						endTime: this.endTime ? this.endTime + " 23:59:59" : "",
						orderBatch: this.orderBatch ? this.orderBatch : null,
						startNum: this.startNum ? this.startNum : undefined,
						endNum: this.endNum ? this.endNum : undefined,
						cooperationMode: this.cooperationMode,
					}).then((res) => {
						this.exportModal = true
						this.taskId = res.data.taskId
						// this.taskName = res.data.taskName + '.xlsx'
						this.taskName = res.data.taskName
						this.downloading = false
					}).catch(() => this.downloading = false)
				}).catch((err) => {
				}).finally(() => {
				})
			},
			// 下载
			download(row) {
				//导出操作
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						let corpId = res.data
						this.corpId = corpId
					}
					downloadTask({
						iccid: row.iccid,
						corpId: this.corpId
					}).then(res => {
						const content = res.data;
						const blob = new Blob([content]); // 构造一个blob对象来处理数据
						// let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[1]) //获取到Content-Disposition;filename  并解码
						let fileName = row.iccid +".png"
						console.log(res.headers['content-disposition'])
						if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
							const link = document.createElement('a'); // 创建a标签
							link.download = fileName; // a标签添加属性
							link.style.display = 'none';
							link.href = URL.createObjectURL(blob);
							document.body.appendChild(link);
							link.click(); // 执行下载
							URL.revokeObjectURL(link.href); // 释放url
							document.body.removeChild(link); // 释放标签
						} else { // 其他浏览器
							navigator.msSaveBlob(blob, fileName);
						}
					}).catch((err) => {
						console.log(err)
					}).finally(() => {					
					})
				}).catch((err) => {
				}).finally(() => {
				})					
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					let flag = true;
					this.iccidList.map((item, index) => {
						if (value.iccid === item.iccid) {
							flag = false
						}
					});
					//判断重复
					if (flag) {
						this.iccidList.push(value);
					}
			
				});
			},
			// 取消单选
			cancelSigle(selection, row) {
				this.iccidList.forEach((value, index) => {
					if (value.iccid === row.iccid) {
						this.iccidList.splice(index, 1);
					}
				})
			},
			// 取消全选
			cancelAll(selection, row) {
				this.iccidList = []
			},			
			//关闭提示框
			cancelModal() {
				this.exportModal=false
			},
			//跳转到下载列表
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportModal = false
			},
		}
	}
</script>

<style scoped="scoped">
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 100px;
	}

	.footer_wrap {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
