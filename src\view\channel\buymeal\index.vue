<template>
	<!-- 套餐购买 -->
	<Card>
		<Tabs>
			<TabPane v-has="'buy'" :label="$t('buymeal.manual_batch')" icon="ios-hammer">
				<!-- 选择套餐 -->
				<div style="width: 100%;">
					<Form ref="mealform" :model="mealform" :rules="rules" :label-width="100" style="display: flex;">
						<div>
							<span style="margin-top: 4px;font-weight:bold;">{{$t('order.mealname')}}:</span>&nbsp;&nbsp;
							<Input v-if="$i18n.locale === 'zh-CN'" v-model="mealform.mealname" :placeholder="$t('buymeal.input_mealname')" prop="showTitle" clearable style="width: 150px" />&nbsp;&nbsp;
							<Input v-if="$i18n.locale === 'en-US'" v-model="mealform.mealnameEn" :placeholder="$t('buymeal.input_mealname')" prop="showTitle" clearable style="width: 150px" />&nbsp;&nbsp;
							<span style="margin-top: 4px;font-weight:bold;">{{$t('buymeal.Country')}}:</span>&nbsp;&nbsp;
							<Select filterable v-model="mealform.localId"  :placeholder="$t('buymeal.selectCountry')" :clearable="true" class="inputSty" >
							  <Option v-for="item in localList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
							</Select>
							<Button v-has="'search'" :disabled="cooperationMode == '3'" type="primary" icon="md-search" style="margin-left: 10px;" :loading="searchloading"
							 @click="search()">{{$t('buymeal.search')}}</Button>&nbsp;&nbsp;
						</div>
						<div style="display: flex;">
							<FormItem v-show="cardflag" :label="$t('buymeal.input_number')" prop="cardnumber" >
							 	<Input v-model="mealform.cardnumber" prop="showTitle" clearable :placeholder="$t('buymeal.chose_number')" style="width: 250px" />
							</FormItem>
						</div>

					</Form>

					<!-- 表格 -->
					<div style="margin-top: 30px;">
						<Table border :columns="columns4" :data="data" :loading="loading" border stripe>
							<template slot-scope="{ row, index }" slot="radiotype">
							</template>
						</Table>
					</div>
					<!-- 分页 -->
					<div style=" margin-top: 10px; margin-bottom: 60px; ">
						<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
					</div>
					</FormItem>
				</div>
				<Form ref="form" :model="form" :rules="formRules"  style="width: 100%;  margin-top: 60px; margin-left: 10px;">
					<FormItem v-show="mealflag"  prop="chose_meal">
						<span style="font-weight:bold;">{{$t('buymeal.chose_meal')}}:</span>&nbsp;&nbsp;&nbsp;&nbsp;
						<Input v-model="chose_meal" readonly="readonly" prop="showTitle" style="width: 250px" />
					</FormItem>
					<!-- 添加的卡号列表 -->
					<FormItem v-for="(item, index) in form.cardList" :key="index">
						<div v-if="item.index!=0">
							<span>{{ item.number }}</span>
							<Icon type="ios-close-circle-outline" size="30" color="red" @click="removegt(index)" />
						</div>
					</FormItem>
					<!-- <FormItem :label="$t('fuelPack.Specificdate')" prop="date" style="font-weight:bold;" v-show="updatetimeFlag===true">
						<DatePicker format="yyyy/MM/dd" v-model="form.date" type="date" @on-change="handleDateChange" :v-bind:transfer="true"
						placement="top-start" :placeholder="$t('fuelPack.PleaseSelectDate')"  :editable="true" ></DatePicker>  
					</FormItem> -->
					<FormItem :label="$t('fuelPack.Specificdate')" prop="date" style="font-weight:bold;">
						<DatePicker format="yyyy/MM/dd" v-model="form.date" type="date" @on-change="handleDateChange" :v-bind:transfer="true"
						placement="top-start" :placeholder="$t('fuelPack.PleaseSelectDate')"  :editable="true" ></DatePicker>  
					</FormItem>
					<FormItem>
						<Button :disabled="cooperationMode == '3'"  type="primary" size="large" style="margin-right: 5px" :loading="orderloading" @click="order()">{{$t('buymeal.confirm')}}</Button>
						<Button size="large" style="margin-left: 100px" @click="Reset()" :disabled="cooperationMode == '3'">{{$t('buymeal.Reset')}}</Button>
					</FormItem>
					</div>
				</Form>
				<!-- 添加卡号 -->
				<Modal v-model="addmodel" title="添加卡号" @on-ok="add" @on-cancel="cancel">
					<p>确认删除该条规则？</p>
				</Modal>
			</TabPane>
			<TabPane v-has="'buyBatch'" :label="$t('buymeal.file_batch')" icon="ios-folder">
				<div style="width: 100%;">
					<div>
						<Upload type="drag" :action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
						 :on-progress="fileUploading" style="width: 500px; margin-top: 50px;margin-left: 50px;" :disabled="cooperationMode == '3'">
							<div style="padding: 20px 0">
								<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
								<p>{{$t('buymeal.upload')}}</p>
							</div>
						</Upload>
						<div style="width: 500px;margin-left: 50px;">
							<!-- <a :href="downloadUrl" download="template.csv" style="color: #FF0000; text-decoration: underline"> -->
							<Button type="primary" :loading="downloading" icon="ios-download" @click="downloadFile" :disabled="cooperationMode == '3'">{{$t('buymeal.Download')}}</Button>
							<!-- </a> -->
							<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
						</div>
						<ul class="ivu-upload-list" v-if="file" style="width: 500px; margin-left: 50px;">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />    {{file.name}}</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
							</li>
						</ul>
						<div style="width: 500px;margin-top: 50px;display: flex;">
							<Button type="primary" size="large" style="margin-left: 50px" :loading="uploadloading" @click="fileok()" :disabled="cooperationMode == '3'">{{$t('buymeal.Uploadfile')}}</Button>
							<Button size="large" style="margin-left: 10px" @click="Reset()" :disabled="cooperationMode == '3'">{{$t('buymeal.Reset')}}</Button>
						</div>
					</div>
					<!-- 模板文件table -->
					<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>

					<!-- 表格 -->
					<div style="margin-top: 80px;width: 95%;margin-left: 50px;">
						<h1>{{$t("buymeal.taskview")}}</h1>
						<Table border :columns="filecolumns" :data="fileData" :loading="fileloading" border stripe>
							<template slot-scope="{ row, index }" slot="fileName">
								<a  :loading="exporting" @click="filedownload(row)">{{row.fileName}}</a>
							</template>
							<template slot-scope="{ row, index }" slot="failFilePath">
								<span v-if="row.failFileCount===0">{{$t("buymeal.Nofailed")}}</span>
								<a v-else @click="failFiledownload(row)">{{$t("buymeal.clickdownload")}}</a>
							</template>
						</Table>
						<a ref="downloadLink" style="display: none"></a>
					</div>
					<!-- 分页 -->
					<div style=" margin-top: 10px; margin-bottom: 60px;margin-left: 50px; ">
						<Page :total="filetotal" :current.sync="filecurrentPage" show-total show-elevator @on-change="filegoPage" />
					</div>
				</div>

			</TabPane>

		</Tabs>


	</Card>
</template>

<script>
	import localData from '@/libs/localData.js'
	import publicData from '@/libs/publicData.js'
	import {
		buymealList,
		buymeal,
		mealImport,
		searchcorpid,
		searchDeposit,
		packageprice,
		buyBatch,
		filedownload,
		opsearch,
		failFiledownload
	} from '@/api/channel.js'
	// import {
	// 	opsearch
	// } from '@/api/customer/postpaid'
	import _ from "lodash";
	const math = require('mathjs')
	// import CsvExportor from "csv-exportor";
	export default {
		data() {
			var checkPhone = (rule, value, callback) => {
				var reg = /^[0-9]{1,16}$/
				if (!value) {
					callback(new Error('请输入正确的卡号'))
				} else if (!reg.test(value)) {
					callback(new Error('请输入 1 到 16 位纯数字的卡号'))
				} else {
					callback()
				}
			};
			var validateNumber = (rule, value, callback) => {
				if (!this.chose_meal) {
					callback(new Error('请选择套餐,获取折扣价!'))
				} else {
					this.packageprice()
					callback()
				}
			};
			return {
				cooperationMode: '', //合作模式
				downloadUrl: `/template.csv`, // 模板下载文件地址
				currency: '',
				payment: '',
				corpId: '',
				chose_meal: '',
				inputradio: '',
				downloading: false,
				searchloading: false,
				uploadloading: false,
				orderloading: false,
				fileloading:false,
				exporting:false,
				// updatetimeFlag:false,
				activeAt:null,//激活日期
				localList: [],
				text: '',
				message: this.$t("buymeal.Downloadmsg"),
				total: 0,
				currentPage: 1,
				loading: false,
				uploadUrl: '',
				file: null,
				mealflag: true,
				cardflag: true,
				form: {
					cardList: [{
						index: 0,
						end: 0,
						order: 0,
						number: 0,
						start: 0
					}],
				},
				mealform: {},
				tableflg: false,
				typeList: [{
					id: 1,
					value: "美国"
				}, {
					id: 2,
					value: "中国"
				}, {
					id: 3,
					value: "日本"
				}, ],
				index: 0,
				addmodel: false,
				currentChoose: '',
				columns4: [{
						title: this.$t("buymeal.choose"),
						key: 'id',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							let id = params.row;
							let flag = false;
							if (this.currentChoose === id) {
								flag = true
							} else {
								flag = false
							}
							let self = this
							return h('div', [
								h('Radio', {
									props: {
										value: flag,
									},
									on: {
										'on-change': () => {
											self.currentChoose = id;
											const row = params.row
											this.data1 = row
											const money = row.groupType == 2 ? parseFloat(math.divide(math.bignumber(row.packagePrice), 100).toFixed(
													4)).toString() :
												row.groupType == 1 ? parseFloat(math.divide(math.bignumber(row.groupPrice), 100).toFixed(4)).toString() :
												''
											self.chose_meal = this.$i18n.locale==='zh-CN' ? row.nameCn:this.$i18n.locale==='en-US' ? row.nameEn: ''
											if (self.mealform.cardnumber) {
												self.packageprice()
											}
											//判断套餐激活方式，方式1激活时间必选
											// self.updatetimeFlag=row.activationMode==='1' ? true:false
										}
									}
								})
							])
						}
					},
					{
						title: this.$t("support.mealname"),
						key: 'nameCn',
						minWidth: 200,
						render: (h, params) => {
							const row = params.row
							var text = this.$i18n.locale==='zh-CN' ? row.nameCn:this.$i18n.locale==='en-US' ? row.nameEn: ''
							return h('label',{
								style:{
									'word-break':'break-word',
								}
							},text)
						}
					},
					{
						title: this.$t("buymeal.Country"),
						key: 'countryCn',
						minWidth: 250,
						sortable: true,
						tooltip: true,
						width: 250,
					},
					{
						title: this.$t("deposit.mealId"),
						key: 'packageId',
						minWidth: 200
					},
					{
						title: this.$t("buymeal.cycletype"),
						key: 'periodUnit',
						minWidth: 200,
						render: (h, params) => {
							const row = params.row
							const text = row.periodUnit == '1' ? this.$t("buymeal.hour") : row.periodUnit == '2' ? this.$t("buymeal.day") : row.periodUnit == '3' ? this.$t("buymeal.month") :
								row.periodUnit == '4' ? this.$t("buymeal.year") : ''
							return h('label', text)
						}
					},
					{
						title: this.$t("buymeal.cycleNumber"),
						key: 'keepPeriod',
						minWidth: 200
					},
					{
						title: this.$t("buymeal.amount"),
						key: 'money',
						minWidth: 200,
						render: (h, params) => {
							const row = params.row
							const text = row.groupType == 2 ? parseFloat(math.divide(math.bignumber(row.packagePrice), 100).toFixed(2)).toString() :
								row.groupType == 1 ? parseFloat(math.divide(math.bignumber(row.groupPrice), 100).toFixed(2)).toString() : ''
							return h('label', text)
						}
					},
				],
				filecolumns:[
					{
						title: this.$t("buymeal.time"),
						key: 'createTime',
						minWidth: 130
					},
					{
						title: this.$t("buymeal.filename"),
						slot: 'fileName',
						minWidth: 160
					},
					{
						title: this.$t("buymeal.Failedfile"),
						slot: 'failFilePath',
						minWidth: 160
					},
					{
						title: this.$t("buymeal.Taskstatus"),
						key: 'status',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row
							const text = row.status == 1 ? this.$t("buymeal.Processing"): row.status == 2 ? this.$t("buymeal.completed") :''
							return h('label', text)
						}
					},
					{
						title: this.$t("buymeal.tasksTotal"),
						key: 'sourceFileCount',
						minWidth: 170
					},
					{
						title: this.$t("buymeal.successes"),
						key: 'successFileCount',
						minWidth: 120
					},
					{
						title: this.$t("buymeal.failed"),
						key: 'failFileCount',
						minWidth: 120
					},
				],
				fileData:[],
				filetotal:0,
				filecurrentPage:1,
				data: [],
				data1: [],
				modelData: [{
					'iccid': '********',
					'package_id': '********',
					'Specific_Activation_Date[YYYY/MM/DD]': '********',
				}, ],
				modelColumns: [{
						title: 'iccid',
						key: 'iccid'
					}, // 列名根据需要添加
					{
						title: 'package_id',
						key: 'package_id'
					}, // 列名根据需要添加
					{
						title: 'Specific_Activation_Date[YYYY/MM/DD]',
						key: 'Specific_Activation_Date[YYYY/MM/DD]'
					} // 列名根据需要添加
				],
				rules: {
					cardnumber: [{
							required: true,
							message: this.$t("order.chose_number"),
							trigger: "blur,change",
						},
						// {
						// 	validator: validateNumber,
						// 	trigger: 'blur'
						// }
					],
				},
				formRules:{
					// date: [{ required: true,type: 'date', message: this.$t('fuelPack.PleaseSelectDate') }]
				}
			}
		},
		mounted() {
			var list = localData.localList
			for (var n = 0; n < list.length; n++) {
				this.localList = this.localList.concat(list[n].value)
			}
			let lang = this.$i18n.locale
			let sortArray = this.localList.sort(function(str1, str2) {
				return str1.name.localeCompare(str2.name, lang.split('-')[0]);
			});
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			if (this.cooperationMode != '3') {
				this.getLocalList()
				this.goPageFirst(1)
			}
		},
		methods: {
			goPageFirst(page) {
				this.loading = true
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						let corpId = res.data
						this.corpId = corpId
						let cooperationMode = this.cooperationMode
						let pageNumber = page
						let pageSize = 10
						let mcc = this.mealform.localId
						let packageName=null
						let packageNameEn=null
						if(this.$i18n.locale === 'zh-CN'){
							packageName = this.mealform.mealname
						}
						if(this.$i18n.locale === 'en-US'){
							packageNameEn = this.mealform.mealnameEn
						}
						buymealList({
							pageNumber,
							pageSize,
							corpId,
							packageName,
							packageNameEn,
							mcc,
							cooperationMode
						}).then(res => {
							if (res.code == '0000') {
								this.loading = false
								this.searchloading = false
								this.page = page
								this.currentPage = page
								this.total = res.data.total
								this.data = res.data.record
								//封装list
								var text=""
								// this.data.map((name, index) => {
								// 		name.mccList.map((item, ids) => {
								// 			this.localList.map((value, id) => {
								// 			if (item === value.mcc) {
								// 				var country=this.$i18n.locale==='zh-CN' ? value.countryCn.toString():this.$i18n.locale==='en-US' ? value.countryEn.toString(): ''
								// 				if (text === "") {
								// 					text = text + '' + country
								// 				} else {
								// 					text = text + ', ' + country
								// 				}
								// 				this.data[index].countryCn=text
								// 			}
								// 		})
								// 	})
								// 	text=""
								// })

                this.data.map((name, index) => {
                		name.mccDtoList.map((item, ids) => {
                      var country=this.$i18n.locale==='zh-CN' ? item.countryCn.toString():this.$i18n.locale==='en-US' ? item.countryEn.toString(): ''
                      if (text === "") {
                        text = text + '' + country
                      } else {
                        text = text + ', ' + country
                      }
                      this.data[index].countryCn=text
                	})
                	text=""
                })

							}
						}).catch((err) => {
							// console.error(err)
						}).finally(() => {
							this.loading = false
							this.searchloading = false
						})
						this.buyBatch(1)
					}
				}).catch((err) => {
					// console.error(err)
				}).finally(() => {})
			},
			handleDateChange:function(date){
				this.activeAt=date
			},
			buyPackage:function(){
				this.$refs['mealform'].validateField("cardnumber", (error) => {
					if (!error) {
						this.orderloading = true
						let corpId = this.corpId
						let cooperationMode = this.cooperationMode
            let packageVO = {
            	"corpId": this.corpId,
            	"groupId": this.data1.groupId,
            	"groupPrice": this.data1.groupPrice,
            	"groupType": this.data1.groupType,
            	"iccid": this.mealform.cardnumber,
            	"keepPeriod": this.data1.keepPeriod,
            	"limitSignBizId": this.data1.limitSignBizId,
            	"mccDtoList": this.data1.mccDtoList,
            	"nameCn": this.data1.nameCn,
            	"nameEn": this.data1.nameEn,
            	"nameTw": this.data1.nameTw,
            	"packageId": this.data1.packageId,
            	"packagePrice": this.data1.packagePrice,
            	"periodUnit": this.data1.periodUnit,
            	"signBizId": this.data1.signBizId,
            	"startTime": this.data1.startTime,
            	"activeAt":this.activeAt,
            	"cooperationMode": this.cooperationMode
            }
						buymeal(packageVO).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t("address.Operationreminder"),
									desc: this.$t("buymeal.purchase")
								})
								// 清空数据
								this.chose_meal = ""
								this.payment = ""
								this.currency = ""
								this.mealform.cardnumber = ""
								this.currentChoose = ""
								this.orderloading = false
								// this.updatetimeFlag=false
								this.$refs['form'].resetFields();
								this.$refs['mealform'].resetFields();
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.orderloading = false
						})
					}
				})
			},
			// 确定套餐购买
			order() {
				if (!this.chose_meal) {
					this.$Modal.warning({
						title: this.$t("buymeal.Choosemeal"),
					});
					return
				}
				this.buyPackage()
			},
			// 文件批量上传
			fileok() {
				if (!this.file) {
					this.$Message.warning(this.$t("buymeal.toupload"))
				} else {
					this.uploadloading = true
					let formData = new FormData()
					formData.append('file', this.file)
					formData.append('corpId', this.corpId)
					formData.append('cooperationMode', this.cooperationMode)
					mealImport(
						formData
					).then(res => {
						if (res && res.code == '0000') {
							this.uploadloading = false
							this.buyBatch(1)
							this.$Notice.success({
								title: this.$t("address.Operationreminder"),
								desc: this.$t("common.Successful")
							})
							this.file = ''
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err)
					}).finally(() => {
						this.uploadloading = false
					})
				}
			},
			// 获取套餐折扣价格
			packageprice() {
				let packageVO = {
					"corpId": this.corpId,
					"groupId": this.data1.groupId,
					"groupPrice": this.data1.groupPrice,
					"groupType": this.data1.groupType,
					"iccid": this.mealform.cardnumber,
					"keepPeriod": this.data1.keepPeriod,
					"limitSignBizId": this.data1.limitSignBizId,
					"mccDtoList": this.data1.mccDtoList,
					"nameCn": this.data1.nameCn,
					"nameEn": this.data1.nameEn,
					"nameTw": this.data1.nameTw,
					"packageId": this.data1.packageId,
					"packagePrice": this.data1.packagePrice,
					"periodUnit": this.data1.periodUnit,
					"signBizId": this.data1.signBizId,
					"startTime": this.data1.startTime
				}
				packageprice(packageVO).then(res => {
					if (res && res.code == '0000') {
						this.payment = parseFloat(math.divide(math.bignumber(res.data), 100).toFixed(2)).toString()
					} else {
						// this.mealform.cardnumber = ""
						this.$refs['form'].resetFields();
						this.$refs['mealform'].resetFields();
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					this.orderloading = false
				})
			},
			Reset() {
				this.file = ''
				this.inputradio = ''
				this.chose_meal = ""
				this.payment = ""
				this.currency = ""
				this.mealform.cardnumber = ""
				this.currentChoose = ""
				// this.updatetimeFlag=false
				this.$refs['form'].resetFields();
				this.$refs['mealform'].resetFields();
			},
			search() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			goPage(page) {
				this.chose_meal = ""
				this.payment = ""
				this.currency = ""
				// this.mealform.cardnumber = null
				this.currentChoose = ""
				this.goPageFirst(page)
			},

			// 历史文件下载
			filedownload(row){
				this.exporting = true
				let id =row.id
				filedownload(id).then(res => {
					const content = res.data
					const fileName = row.fileName+'.csv' // 导出文件名
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content,fileName)
					}
				}).catch(err => this.exporting = false)
			},
			//失败文件下载
			failFiledownload(row){
				this.exporting = true
				let id =row.id
				failFiledownload(id).then(res => {
					const content = res.data
					const fileName = this.$t("buymeal.fileresult")+'.csv' // 导出文件名
					var blob = new Blob([content], {type: 'text/csv;charset=utf-8;'});
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(blob)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content,fileName)
					}
				}).catch(err => this.exporting = false)
			},
			handleSelectAll(status) {
				this.$refs.selection.selectAll(status);
			},
			// 选择类别
			choose(id) {
				this.goPageFirst(1)
				this.tableflg = true
			},
			// 添加卡号
			addcard() {
				this.$refs['form'].validateField('cardnumber', (err) => {
					if (err) {
						return;
					} else {
						this.index++;
						this.form.cardList.push({
							index: this.index,
							end: 0,
							order: 0,
							number: this.mealform.cardnumber,
							start: 0
						});
						this.mealform.cardnumber = ""
					}
				});

			},
			// 删除卡号
			removegt(index) {
				this.form.cardList.splice(index, 1);
				this.index--;
			},
			add() {

			},
			cancel() {

			},
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			handleBeforeUpload(file) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: this.$t("buymeal.fileformat"),
						desc:  file.name + this.$t("buymeal.incorrect")
					})
				} else {
					if (file.size > 5 * 1024 * 1024) {
						this.$Notice.warning({
							title: this.$t("buymeal.Filesize"),
							desc: file.name + this.$t("buymeal.Exceeds")
						})
					} else {
						this.file = file
					}
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			removeFile() {
				this.file = ''
			},
			//模板下载
			downloadFile: function() {
				this.$refs.modelTable.exportCsv({
					filename: this.$t("buymeal.templatename"),
					// type:'xlsx',
					columns: this.modelColumns,
					data: this.modelData
				})
			},
			getOperatorList() {

			},
			//国家/地区
			getLocalList() {
				opsearch().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.localList = list;
						this.localList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//获取历史文件列表
			buyBatch(page){
				this.fileloading=true
				let corpId = this.corpId
				let cooperationMode = this.cooperationMode
				let pageNumber = page
				let pageSize = 10
				buyBatch({
					corpId,
					pageNumber,
					pageSize,
					cooperationMode
				}).then(res => {
					if (res && res.code == '0000') {
						this.fileloading=false
						this.page = page
						this.filecurrentPage = page
						this.filetotal = res.data.total
						this.fileData = res.data.records
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
				this.fileloading=false
				})
			},
			filegoPage(page){
				this.buyBatch(page)
			},
		}


	}
</script>

<style>
	.inputSty {
	  width: 200px;
	}

  .ivu-table td label{
    word-wrap:break-word;
    word-break: normal;
  }
</style>
