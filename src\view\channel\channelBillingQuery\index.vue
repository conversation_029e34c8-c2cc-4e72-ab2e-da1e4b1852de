<template>
	<!-- 渠道商账单查询 -->
	<Card>
		<Form ref="form" :label-width="90" :model="form" :rules="ruleInline" inline>
			<FormItem :label="$t('channelBill.startMonth') + ':'" prop="beginMonth">
				<DatePicker format="yyyyMM" v-model="form.beginMonth" type="month" placement="bottom-start"
          :placeholder="$t('channelBill.selectStart')" :editable="true" @on-change="startChange"></DatePicker>  
			</FormItem>
			<FormItem :label="$t('channelBill.endMonth') + ':'" prop="endMonth">
				<DatePicker format="yyyyMM" v-model="form.endMonth" type="month" placement="bottom-start"
          :placeholder="$t('channelBill.selectEnd')" :editable="true" @on-change="endChange"></DatePicker>
			</FormItem>
			<Button type="primary" icon="md-search" :loading="searchLoading" v-has="'search'"
        @click="search('form')">{{$t('common.search')}}</Button>&nbsp;&nbsp;
      <Button type="info" icon="md-eye" :loading="flowLoading" @click="usageBilling"
       v-has="'bill_search'" :disabled="cooperationMode == '1'">{{$t('channelBill.dataUsage')}}</Button>&nbsp;&nbsp;
			<Button type="success" icon="md-color-wand" :loading="imsiLoading"
        @click="imsiView" v-has="'imsi_search'">{{$t('channelBill.imsiFee')}}</Button>
		</Form>
		<div>
			<Table no-data-text border highlight-row :columns="columns" :data="data" style="width: 100%; margin-top: 20px;"
			 :loading="loading">
				<template slot-scope="{ row, index }" slot="action">
          <Button type="primary" ghost size="small" style="margin: 5px" @click="showInfo(row)"
            v-has="'info'">{{$t('channelBill.detailed')}}</Button>
          <Button type="info" ghost size="small" style="margin: 5px" @click="exportBillFile(row)"
            v-has="'bill_export'">
            {{$t('channelBill.billFileDownload')}}
          </Button>
          <Button type="success" ghost size="small" style="margin: 5px" @click="exportInvoice(row)"
           :disabled="!row.invoicePath" v-has="'invoice'">{{$t('channelBill.invoiceDownload')}}</Button>
          <!-- 代销账单才展示线上支付 -->
          <Button type="primary" ghost size="small" style="margin: 5px" @click="paymentMethods(row)" v-has="'onlinePayment'"
            :disabled="['1','2','3', '5'].includes(row.chargeStatus) || row.channelType == '2' || !['0', '1'].includes(row.accountingType) || row.realIncome == 0">
            {{$t('channelBill.onlinePayment')}}
          </Button>
          <!-- 线下支付/付款、撤销、重新上传（预存模式下不做，押金模式下应缴纳金额为0也不做） -->
          <Button type="info" ghost size="small" style="margin: 5px" @click="billsDelivery(row,'1')" v-has="'payment'"
            :disabled="!['0', '4'].includes(row.chargeStatus) || row.channelType == '2' || row.realIncome == 0">
            {{$t('channelBill.offlinePayment')}}
          </Button>
          <Button type="error" ghost size="small" style="margin: 5px" @click="revoke(row)" v-has="'revoke'"
          :disabled="row.chargeStatus != '1' || row.channelType == '2' || row.realIncome == 0">
            {{$t('support.revoke')}}
          </Button>
          <Button type="warning" ghost size="small" style="margin: 5px" @click="billsDelivery(row,'2')" v-has="'rePayment'"
          :disabled="!['3'].includes(row.chargeStatus) || row.channelType == '2' || row.realIncome == 0">
            {{$t('channelBill.reUpload')}}
          </Button>
				</template>
			</Table>
       <Page
         :total="total"
         :current.sync="page"
         show-total
         show-elevator
         @on-change="loadByPage"
         style="margin: 15px 0;"
       />
		</div>
		<!-- 明细 -->
    <Modal :title="$t('channelBill.detailed')" v-model="infoModal" :footer-hide="true" :mask-closable="false" width="500px">
		  <Row style="margin: 20px">
		    <Col span="24" style="display: flex; justify-content: flex-start;">
		      <span>{{$t('channelBill.dataFee')}}：</span>&nbsp;&nbsp;{{ a2zAmount }}
		    </Col>
		  </Row>
      <Row style="margin: 20px">
        <Col span="24" style="display: flex; justify-content: flex-start;">
          <span>{{$t('channelBill.cardFee')}}：</span>&nbsp;&nbsp;{{ imsiAmount }}
        </Col>
      </Row>
		  <Row style="margin: 20px">
		    <Col span="24" style="display: flex; justify-content: flex-start;">
		      <span>{{$t('channelBill.packageFee')}}：</span>&nbsp;&nbsp;{{ directIncome }}
		    </Col>
		  </Row>
		</Modal>
    <!-- 流量计费查询弹窗 -->
    <Modal :title="$t('channelBill.dataUsage')" v-model="billModal" :footer-hide="true" :mask-closable="false"
      width="1000px" @on-cancel="cancelModal">
      <Spin size="large" fix v-if="spinShow"></Spin>
      <Form ref="searchForm" :model="searchObj" inline @submit.native.prevent>
      	<FormItem>
      		<Input v-model="searchObj.name" :placeholder="$t('channelBill.inputChargesName')" clearable style="width: 200px" />
      	</FormItem>
      	<FormItem>
          <Select v-model="searchObj.mcc" filterable clearable :placeholder="$t('buymeal.selectCountry')" clearable>
            <Option v-for="item in localList" :value="item.mcc" :key="item.mcc">{{ item.countryEn }}</Option>
          </Select>
      	</FormItem>
        <FormItem>
        	<Button type="info" icon="md-search" :loading="billLoading" @click="billSearch">{{$t('common.search')}}</Button>
        </FormItem>
      </Form>
			<Table :columns="billColumns" :data="billTableData" :ellipsis="true"></Table>
		</Modal>
    <!-- IMSI费查询 -->
    <Modal :title="$t('channelBill.imsiFee')" v-model="imsiModal" :footer-hide="true" :mask-closable="false" width="600px">
    	<Table :columns="imsiColumns" :data="imsiTableData" :ellipsis="true"></Table>
    </Modal>
    <!-- 导出提示 -->
		<Modal v-model="exportModalr" :mask-closable="true" @on-cancel="exportcancelModal">
			<div style="display: flex;align-items: center;justify-content:center;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
					<FormItem :label="$t('exportID')">
						<span>{{taskId}}</span>
					</FormItem>
					<FormItem :label="$t('exportFlie')">
						<span class="task-name">{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">{{$t('downloadResult')}}</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="exportcancelModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" @click="Gotor">{{$t('Goto')}}</Button>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="display: flex; align-items: center;justify-content:center;">
				<Form label-position="left" :label-width="150" style="width:500px; align-items: center;justify-content:center;margin-bottom: 30px;">
					<h1 style="text-align: center;margin-bottom: 20px;">{{$t('exportMS')}}</h1>
					<FormItem :label="$t('exportID')">
						<ul style="margin-bottom: 15px;">
							<li id="space" v-for="(taskId,i) in taskIds" :key="taskIds.i">
								{{taskId}}
							</li>
						</ul>
					</FormItem>
					<FormItem :label="$t('exportFlie')">
						<ul style="margin-bottom: 15px;">
							<li id="space" v-for="(taskName,i) in taskNames" :key="taskNames.i" class="task-name">
								{{taskName}}
							</li>
						</ul>
					</FormItem>
					<span style="text-align: left;">{{$t('downloadResult')}}</span>
				</Form>

			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
			</div>
		</Modal>
    <!-- 上传付款证明 -->
    <Modal v-model="PaymentModal" :title="$t('channelBill.paymentPage')" :footer-hide="true" :mask-closable="false"
    	width="450px" @on-cancel="cancelModal">
    	<div>
    		<Form ref="formobj" :model="formobj" :rules="ruleobj" :label-width="100"
    			:label-height="100" inline style="font-weight:bold;">
    			<FormItem :label="$t('support.payslip')" prop="file" style="font-size: 14px;font-weight: bold;">
    				<Upload type="drag" v-model="formobj.file" :action="uploadUrl" :before-upload="handleBeforeUpload"
              :on-progress="fileUploading" style="width: 250px; margin-top: 50px;">
    					<div style="padding: 20px 0">
    						<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
    						<p>{{$t('support.uploadPicture')}}</p>
    					</div>
    				</Upload>
    				<ul class="ivu-upload-list" v-if="file" style="width: 300px;">
    					<li class="ivu-upload-list-file ivu-upload-list-file-finish">
    						<span>
    							<Icon type="ios-folder" />{{file.name}}
    						</span>
    						<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" @click="removeFile"></i>
    					</li>
    				</ul>
    			</FormItem>
          <FormItem :label="$t('channelBill.cnInvoice')" prop="cnInvoiceFile" style="font-size: 14px;font-weight: bold;" v-if="['2','3','5'].indexOf(billRow.accountingType)>-1">
            <Upload type="drag" v-model="formobj.cnInvoiceFile" :action="uploadUrl" :before-upload="handleCnInvoiceBeforeUpload"
              :on-progress="cnInvoiceUploading" style="width: 250px; margin-top: 20px;">
              <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>{{$t('channelBill.uploadCnInvoice')}}</p>
              </div>
            </Upload>
            <ul class="ivu-upload-list" v-if="cnInvoiceFile" style="width: 300px;">
              <li class="ivu-upload-list-file ivu-upload-list-file-finish">
                <span>
                  <Icon type="ios-folder" />{{cnInvoiceFile.name}}
                </span>
                <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" @click="removeCnInvoiceFile"></i>
              </li>
            </ul>
          </FormItem>
          <FormItem :label="$t('fuelPack.Amount')" prop="amount">
          	<Input v-model="formobj.amount" :placeholder="$t('channelBill.inputAmount')" disabled style="width: 250px" />
          </FormItem>
        </Form>
    		<div style="text-align: center;margin: 40px 0 0 0;">
    			<Button style="margin-right: 30px" @click="cancelModal">{{$t('support.back')}}</Button>
    			<Button type="primary" @click="pictureSubmit" v-preventReClick
    				:loading="pictureLoading">{{$t('common.determine')}}</Button>
    		</div>
    	</div>
    </Modal>
		<Modal :title="$t('onlineOrder.onlineModalTitle')" v-model="payModal" :mask-closable="false" :width="720">
			<PaymentComponent orderType="channelBilling" :corpId="corpId" :billId="accountId"  v-if="payModal" :paySuccessInfo="paySuccessInfo" :payLoading="payStatus" :amount="cooperationDetail.realIncome"
				:currencyCode="cooperationDetail.currency" @onlinePay="onlinePay" />
			<div slot="footer" style="width: 100%; display: flex; align-items: center; justify-content: flex-end;">
				<!-- <Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="save">确定</Button> -->
				<!-- {{payDetail}} -->
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
    queryChannelBill,
    getBillInfo,
    getImsiFee,
    addPicture,
    revoke,
    exportBillFile
	} from "@/api/channel/channelBillingQuery";
  import {
  	exportInvoice,
  } from "@/api/finance/corp";
  import {
  	opsearch,
  } from '@/api/channel.js'
	import {
		getDistributorsDetail,
	} from "@/api/customer/channelShop.js";
	import {
	createOrderAndPay
} from '@/api/onlinePay/pay.js'
	import PaymentComponent from '@/components/onlinePayment/index'
import { json } from 'mathjs';
  const math = require('mathjs')
	export default {
	components: {
		PaymentComponent
	},
		data() {
      const validateNum = (rule, value, callback) => {
      	if (parseFloat(value) < 0) {
      	  callback(new Error(this.$t('channelBill.lessThan0')));
      	  return;
      	}

        var str1 = value
      	if (value.substr(0, 1) === '-') {
      		str1 = value.substr(1, value.length)
      	}
        var str = /^(?!0(\.0+)?$)([1-9]\d{0,7}(\.\d{1,2})?|\d{1,8}(\.\d{0,2})?)$/;
        if (!str1 || str.test(str1)) {
        	callback();
        } else{
        	callback(new Error(this.$t('channelBill.checkNumber')));
        }
      };
      // 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
      const validateUpload = (rule, value, callback) => {
      	if (!this.file) {
      		callback(new Error(this.$t('support.pleaseUploadFile')))
      	} else {
      		callback()
      	}
      }
			return {
				form: {
					beginMonth: "",
					endMonth: "",
				},
				searchObj: {
          name: "",
          countryName: "",
        },
        total: 0,
				pageSize: 10,
				page: 1,
        billModal: false,
        imsiModal: false,
        loading: false,
				exportModal: false,
				exportModalr: false,
				searchLoading: false,
        flowLoading: false,
        imsiLoading:false,
        billLoading: false,
        pictureLoading: false,
        PaymentModal: false,
        infoModal: false,
        spinShow: false,
				taskName: '',
				taskId: '',
        accountId: '',
        corpId: '',
        a2zAmount: "", //流量费
        imsiAmount: "", //卡费
        directIncome: "", //套餐费
				corpLists: [],
        localList: [],
				taskIds: [],
				taskNames: [],
        imsiColumns: [{
          title: this.$t('channelBill.imsiFeeType'),
          key: "ruleName",
          align: "center",
          tooltip: true,
          minWidth: 150,
        },{
          title: this.$t('channelBill.imsiFeeAmount'),
          key: "amount",
          align: "center",
          tooltip: true,
          minWidth: 150,
        },{
          title: this.$t('channelBill.quantityRange'),
          key: "begin",
          align: "center",
          tooltip: true,
          minWidth: 150,
          render: (h, params) => {
          	const row = params.row;
            let endValue = row.end == '**********' ? "+" : "-" + row.end
          	const text = row.begin + endValue
          	return h('label', text);
          }
        }],
        imsiTableData: [],
        billColumns: [{
						title: this.$t('channelBill.chargesName'),
						key: "name",
						align: "center",
						tooltip: true,
						minWidth: 150,
					},{
						title: this.$t('channelBill.country'),
						key: "countryName",
						align: "center",
						tooltip: true,
						minWidth: 150,
            render: (h, params) => {
            	const row = params.row;
            	const text = this.$i18n.locale == 'zh-CN' ? row.countryName : row.countryNameEn
            	return h('label', text);
            }
					},{
						title: this.$t('support.operator'),
						key: "operatorName",
						align: "center",
						tooltip: true,
						minWidth: 150,
					},{
						title: this.$t('channelBill.cny'),
						key: "cny",
						align: "center",
						tooltip: true,
						minWidth: 180,
            render: (h, params) => {
            	const row = params.row;
              let yuan = this.$t('yuan')
              const text = row.flowUnit == '1' ? row.cny +  ' (' + yuan + '/GB)' :
                row.flowUnit == '2' ? row.cny +  ' (' + yuan + '/MB)' : ''
            	return h('label', text);
            }
					},{
						title: this.$t('channelBill.hkd'),
						key: "hkd",
						align: "center",
						tooltip: true,
						minWidth: 150,
            render: (h, params) => {
            	const row = params.row;
              let yuan = this.$t('yuan')
              const text = row.flowUnit == '1' ? row.hkd +  ' (' + yuan + '/GB)' :
                row.flowUnit == '2' ? row.hkd +  ' (' + yuan + '/MB)' : ''
            	return h('label', text);
            }
					},{
						title: this.$t('channelBill.usd'),
						key: "usd",
						align: "center",
						tooltip: true,
						minWidth: 150,
            render: (h, params) => {
            	const row = params.row;
              let yuan = this.$t('yuan')
              let text = row.flowUnit == '1' ? row.usd +  ' (' + yuan + '/GB)' :
                row.flowUnit == '2' ? row.usd +  ' (' + yuan + '/MB)' : ''
            	return h('label', text);
            }
					},],
        billTableData: [],
        data: [],
				columns: [{
						title: this.$t('channelBill.billId'),
						key: "billId",
						align: "center",
						tooltip: true,
						minWidth: 160,
					}, {
						title: this.$t('channelBill.billType'),
						key: "accountingType",
						align: "center",
						tooltip: true,
						minWidth: 150,
            render: (h, params) => {
            	const row = params.row;
            	const text = row.accountingType == '0'
                ? this.$t('support.distribution')
                : row.accountingType == '1'
                ? this.$t('support.distribution')
                : row.accountingType == '2'
                ? this.$t('support.atoz')
                : row.accountingType == '3'
                ? this.$t('support.atoz')
                : row.accountingType == '4'
                ? this.$t('channelBill.merge')
                : row.accountingType == '5'
                ? this.$t('support.atoz')
                : row.accountingType == '6'
                ? this.$t('channelBill.merge')
                : row.accountingType == '7'
                ? this.$t('channelBill.merge')
                : '';
            	return h('label', text);
            }
					}, {
          	title: this.$t('channelBill.paymentMonth'),
          	key: "statTime",
          	align: "center",
          	minWidth: 150,
          	tooltip: true,
          	tooltipMaxWidth: 2000,
            render: (h, params) => {
            	const row = params.row;
            	const year = row.statTime.slice(0, 4);
            	const month = row.statTime.slice(4, 6);
            	const text = `${year}-${month}`
              return h('label', text);
            }
          }, {
						title: this.$t('channelBill.totalBillAmount'),
						key: "saleIncome",
						align: "center",
						tooltip: true,
						minWidth: 150,
					}, {
						title: this.$t('channelBill.accountsPayableAmount'),
						key: "realIncome",
						align: "center",
						minWidth: 200,
            render: (h, params) => {
            	const row = params.row;
            	const text = row.channelType == "2" ? '0' : row.realIncome
            	return h('label', text);
            }
					}, {
						title: this.$t('channelBill.paymentStatus'),
						key: "chargeStatus",
						align: "center",
						minWidth: 190,
						tooltip: true,
						tooltipMaxWidth: 1000,
						render: (h, params) => {
							const row = params.row;
              let text = '';
              if (row.channelType == "2") {
                text = this.$t('channelBill.verified')
              } else {
                text = row.chargeStatus == "0"
                  ? this.$t('channelBill.unpaidPayment')
                  : row.chargeStatus == "1"
                  ? this.$t('channelBill.confirmationReceipt')
                  : row.chargeStatus == "2"
                  ? this.$t('channelBill.Arrived')
                  : row.chargeStatus == "3"
                  ? this.$t('channelBill.NotCredited')
                  : row.chargeStatus == "4"
                  ? this.$t('support.cancelled')
                  :row.chargeStatus == "5"
									?	this.$t('channelBill.OnlinePaymentInProgress')
									: "";
              }
							return h('label', text);
						}
					},
					{
						title: this.$t('Operation'),
						slot: 'action',
						width: 450,
						align: 'center',
            fixed: 'right',
					},
				],
				ruleInline: {
					beginMonth: [{
						type: 'date',
						required: true,
						message: this.$t('channelBill.selectStart'),
						trigger: 'blur'
					}],
					endMonth: [{
						type: 'date',
						required: true,
						message: this.$t('channelBill.selectEnd'),
						trigger: 'blur'
					}],
				},
				searchBeginTime: '',
				searchEndTime: '',
				type: '',
        cooperationMode: '',
        rowType: '',
        currencyCode: "",//币种
        uploadUrl: '', //上传地址
        uploadList: [],
        file: null,
        formobj: {
        	file: "",
          amount: '',
        },
        ruleobj: {
        	file: [{
        		required: true,
        		validator: validateUpload,
        		trigger: 'change',
        	}],
          amount: [{
        		required: true,
        		message: this.$t('channelBill.inputAmount'),
        		trigger: 'change',
        	},{
							validator: validateNum,
							// message: "最高支持8位整数和2位小数的正数",
					},],
        },
				payModal: false,
				cardPayFromParent: false, // 根据实际情况设置这个值
				payDetail: {},
				cooperationDetail:{},
				payStatus:false,
				payCont:"",
				paySuccessInfo:{},
        cnInvoiceFile: '',
        cnInvoiceLoading: false,
				cnInvoiceMessage: '',
				//账单信息
				billRow:{}
			};
		},
		mounted() {
      this.cooperationMode = sessionStorage.getItem("cooperationMode")
      this.getDistributorsDetail()
		},
		methods: {
      startChange(e) {
        this.searchBeginTime = e
      },
      endChange(e) {
        this.searchEndTime = e
      },
			getTableData(page,newSearchBeginTime) {
        let corpId = sessionStorage.getItem("corpId")
        let coprIdList = []
        coprIdList.push(corpId)
				this.loading = true
				queryChannelBill({
					dateStart: newSearchBeginTime ? newSearchBeginTime : this.searchBeginTime,
					dateEnd: this.searchEndTime,
					corpId: coprIdList,
					pageNum: this.page,
					pageSize: this.pageSize,
          billType: this.cooperationMode
				}).then(res => {
					if (res.code == '0000') {
						this.loading = false
						this.searchLoading = false
						this.page = page
						this.currentPage = page
						this.total = Number(res.count)
						this.data = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchLoading = false
				})
			},
			search(name) {
        let limitTime = '202409'
        this.$refs[name].validate((valid) => {
        	if (valid) {
            // 开始时间和结束时间都早于2024年9月  数据置为空
            if (new Date(this.searchBeginTime) > new Date(this.searchEndTime)) {
              this.$Message['warning']({
                background: true,
                content: this.$t('channelBill.endGreaterStart')
              });
            } else if (new Date(this.searchBeginTime) < new Date(limitTime) && new Date(this.searchEndTime) < new Date(limitTime)) {
              this.page = 1
              this.currentPage = 1
              this.total = 0
              this.data = []
            } else if (new Date(this.searchBeginTime) < new Date(limitTime) && new Date(this.searchEndTime) >= new Date(limitTime)) {
              let newSearchBeginTime = '202409'
              this.searchLoading = true
              this.getTableData(1,newSearchBeginTime)
            } else {
              this.searchLoading = true
              this.getTableData(1)
            }
        	}
        })
			},
			loadByPage(page) {
				this.page = page
        let limitTime = '202409'
        this.$refs['form'].validate((valid) => {
        	if (valid) {
            // 开始时间和结束时间都早于2024年9月
            if (new Date(this.searchBeginTime) < new Date(limitTime)) {
              let newSearchBeginTime = '202409'
              this.getTableData(page, newSearchBeginTime)
            } else {
              this.getTableData(page)
            }
        	}
        })
			},
			usageBilling() {
        this.spinShow = true
        this.getBillInfo()
        this.getLocalList()
        this.billModal = true
      },
      imsiView() {
        this.getImsiFee()
        this.imsiModal = true
      },
      // Invoice
      exportInvoice(row) {
      	exportInvoice({
      		corpName: row.corpName,
      		invoicePath: row.invoicePath,
      		month: row.statTime,
      		corpId: row.corpId,
          billType: row.accountingType,
          incomeId: row.id,
      	}).then(res => {
      		if (res && res.code == '0000') {
      			this.exportModalr = true
            //至多5个文件，全部展示
      			this.taskId = res.data.taskId
      			this.taskName = res.data.taskName
      		} else {
      			throw res
      		}
      	}).catch((err) => {
      		console.log(err)
      	})
      },
      //明细
      showInfo(row) {
        this.a2zAmount = row.a2zAmount
        this.imsiAmount = row.imsiAmount
        this.directIncome = row.directIncome
        this.infoModal = true
      },
      // 账单文件导出
      exportBillFile(row) {
        let newBeginTime = ''
        let limitTime = '202409'
        if (new Date(this.searchBeginTime) < new Date(limitTime) && new Date(this.searchEndTime) >= new Date(limitTime)) {
          newBeginTime = '202409'
        } else {
          newBeginTime = this.searchBeginTime
        }
        let data = {
          "corpId": row.corpId,
          "beginMonth": newBeginTime,
          "endMonth": this.searchEndTime,
          "cooperationMode": row.accountingType,
          "corpName": row.corpName,
          "currencyCode": row.currency,
          "billType": row.accountingType,
          "id": row.id,
          "statTime": row.statTime,
          "dateStart": row.svcStartTime,
          "dateEnd": row.svcEndTime,
          "userId": row.corpId,
          "type": row.accountingType == '2' ? '1' : row.accountingType == '3' ? '2' : row.accountingType == '4' ? 1 :
            row.accountingType == '5' ? '3': row.accountingType == '6' ? '2' : row.accountingType == '7' ? '3' : '',
        }
        exportBillFile(data).then(res => {
        	if (res && res.code == '0000') {
        		this.exportModal = true
            res.data.forEach(i => {
              this.taskIds.push(i.taskId)
              this.taskNames.push(i.taskName)
            })
        	} else {
        		throw res
        	}
        }).catch((err) => {
        	console.log(err)
        })
      },
      billsDelivery(row, type) {
        this.accountId = row.id
        this.corpId = row.corpId
        this.rowType = type
        this.formobj.amount = row.realIncome.toString()
				this.billRow = row
        this.PaymentModal = true
      },
      pictureSubmit() {
      	this.$refs["formobj"].validate((valid) => {
      		if (valid) {
      			var formData = new FormData();
      			formData.append('amount', this.formobj.amount);
      			formData.append('corpId', this.corpId);
      			formData.append('accountId', this.accountId);
      			formData.append('paymentProofs', this.file); //封面
      			if (this.cnInvoiceFile) {
      				formData.append('cnInvoice', this.cnInvoiceFile);
      			}
      			formData.append('repeatedUpload', this.rowType == '2' ? true : false); //封面
            formData.append('chargeType', '1'); //账单缴付
      			this.pictureLoading = true
      			addPicture(formData).then((res) => {
      				if (res.code === "0000") {
      					this.$Notice.success({
      						title: this.$t('address.Operationreminder'),
      						desc: this.$t('common.Successful'),
      					});
      					this.pictureLoading = false
      					this.PaymentModal = false;

                let newSearchBeginTime = ''
                let limitTime = '202409'
                if (new Date(this.searchBeginTime) < new Date(limitTime)) {
                  newSearchBeginTime = '202409'
                } else {
                  newSearchBeginTime = this.searchBeginTime
                }
      					this.getTableData(1, newSearchBeginTime)
      					this.file = ''
								this.cnInvoiceFile=''
      					this.$refs["formobj"].resetFields();
      				} else {
      					throw res
      				}
      			}).catch((err) => {
      				this.pictureLoading = false
      			}).finally(() => {

      			})
      		}
      	});
      },
      //撤销
      revoke(row) {
      	this.$Modal.confirm({
      		title: this.$t('support.confirmRevocation'),
      		onOk: () => {
      			revoke({
              id: row.id
            }).then((res) => {
      				if (res.code === "0000") {
      					this.$Notice.success({
      						title: this.$t('address.Operationreminder'),
      						desc: this.$t('common.Successful'),
      					});
      				}
              let newSearchBeginTime = ''
              let limitTime = '202409'
              if (new Date(this.searchBeginTime) < new Date(limitTime)) {
                newSearchBeginTime = '202409'
              } else {
                newSearchBeginTime = this.searchBeginTime
              }
      				this.getTableData(1, newSearchBeginTime);
      			});
      		},
      	});
      },
      exportcancelModal(){
				this.exportModalr = false
				this.file = ''
				this.cnInvoiceFile = ''
			},
			cancelModal() {
				this.taskIds = []
				this.taskNames = []
        this.searchObj.name = ""
        this.searchObj.mcc = ""
        this.billModal = false
        this.imsiModal = false
        this.exportModal = false
        this.PaymentModal = false
				this.billRow = {}
        this.file = ''
				this.cnInvoiceFile=''
        this.$refs['formobj'].resetFields()
			},
			Gotor(){
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportcancelModal()
				this.exportModalr = false
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})

				this.exportModal = false
			},
      billSearch() {
        this.billLoading = true
        this.getBillInfo()
      },
      flattenAndEnrichAtzChargingDetails(originalDataArray) {
        const enrichedDetails = [];

        originalDataArray.forEach(item => {
          item.atzChargingDetails.forEach(detail => {
            const enrichedDetail = { ...detail, ...{ id: item.id, name: item.name, flowUnit: item.flowUnit, effectiveDate: item.effectiveDate, status: item.status } };
            enrichedDetails.push(enrichedDetail);
          });
        });

        return enrichedDetails;
      },
      imsiFeeDetails(originalDataArray) {
        const enrichedDetails = [];

        originalDataArray.forEach(item => {
          item.imsiAmountRanges.forEach(detail => {
            const enrichedDetail = { ...detail, ...{ ruleId: item.ruleId, ruleName: item.ruleName} };
            enrichedDetails.push(enrichedDetail);
          });
        });

        return enrichedDetails;
      },
      // 上传文件
      handleBeforeUpload(file, fileList) {
      	const sizeLimit = file.size / 1024 / 1024 > 10
      	if (sizeLimit) {
      		this.$Notice.warning({
      			title: this.$t('address.Operationreminder'),
      			desc: this.$t('support.pictureSize')
      		});
      		return false;
      	}
      	this.file = file,
      	this.uploadList = fileList
      	return false;
      },
      fileUploading(event, file, fileList) {
      	this.message = this.$t('support.fileUploadedAndProgressDisappears')
      },
      removeFile() {
      	this.file = ''
      },
      getBillInfo() {
        let corpId = sessionStorage.getItem("corpId")
      	getBillInfo({
      		corpId: corpId,
          name: this.searchObj.name,
          mcc: this.searchObj.mcc,
          cooperationMode: this.cooperationMode,
      	}).then(res => {
      		if (res.code == '0000') {
            this.billTableData = this.flattenAndEnrichAtzChargingDetails(res.data)
            this.billLoading = false
      		} else {
            this.billTableData = []
            this.billLoading = false
            throw res
          }
      	}).catch((err) => {
      		console.error(err)
      	}).finally(() => {
          this.spinShow = false
      	})
      },
      getImsiFee() {
        let corpId = sessionStorage.getItem("corpId")
      	getImsiFee({
      		corpId: corpId,
          cooperationMode: this.cooperationMode,
      	}).then(res => {
      		if (res.code == '0000') {
      			this.imsiTableData = this.imsiFeeDetails(res.data)
      		} else {
            this.imsiTableData = []
            throw res
          }
      	}).catch((err) => {
      		console.error(err)
      	}).finally(() => {
      	})
      },
      //国家/地区
      getLocalList() {
      	opsearch().then(res => {
      		if (res && res.code == '0000') {
      			var list = res.data;
      			this.localList= list;
      			this.localList.sort(function(str1, str2) {
              return str1.countryEn.localeCompare(str2.countryEn);
      			});
      		} else {
      			throw res
      		}
      	}).catch((err) => {

      	}).finally(() => {

      	})
      },
      //获取渠道商的币种
      getDistributorsDetail() {
      	getDistributorsDetail({
      		corpId: sessionStorage.getItem("corpId")
      	}).then((res) => {
      		if (res.code === "0000") {
            this.currencyCode = res.data.currencyCode
            if (this.currencyCode == '156') {
              this.billColumns = this.billColumns.filter(column => column.key !== 'hkd' && column.key !== 'usd');
            } else if (this.currencyCode == '344'){
              this.billColumns = this.billColumns.filter(column => column.key !== 'cny' && column.key !== 'usd');
            } else {
              this.billColumns = this.billColumns.filter(column => column.key !== 'cny' && column.key !== 'hkd');
            }
          }
        })
      },
      // 代销线上/线下支付
      paymentMethods(row) {
					console.log(row)
          // 代销线上支付
				let amount = row
				amount.currencyCode = amount.currency
				this.payModal = true
			  this.accountId = row.id
        this.corpId = row.corpId
				//人民币
				this.cardPayFromParent = true
				this.cooperationDetail = amount
      },
			onlinePay (details) {
				console.log("支付信息：", details)
				if (this.payStatus) {
					console.log("请求中...")
					return;
				}
				this.payStatus = true

				//封装需要传的参数
				let createAndPayData = {
					amount: this.cooperationDetail.realIncome,
					paymentMethod: details.paymentMethod,
					orderType: "channelBilling",
					adyenStateData: details.adyenStateData,
					billId: this.cooperationDetail.id,
					corpId: sessionStorage.getItem("corpId"),
					language: localStorage.getItem("local"), // 根据需要设置语言
				}

				createOrderAndPay(createAndPayData).then(res => {
					console.log("res:", res)
					if (res.code == '0000') {
						this.payStatus = false
						//通知子组件
						this.paySuccessInfo = res.data
						this.payModal = false
						this.payCont = res.data.payUrl
						// if (res.data.redirectUrl) {
						// 	window.location.href = res.data.redirectUrl
						// } else {
							document.querySelector("body").innerHTML = res.data.payUrl
							document.forms[0].submit();
						// }

					}
				}).catch(err=>{
					this.payStatus = false
				})
			},
			handleWeChatOrAlipayPayment(payUrl) {
      // 用户需要手动访问这个URL，完成支付
      	window.location.href = payUrl;
    	},
			handleCreditCardOrUnionPayPayment(payUrl) {
				// 用户需要手动访问这个URL，完成支付或3DS认证
				console.log("payUrl:",payUrl)
				window.location.href = payUrl;

			},
      handleCnInvoiceBeforeUpload(file, fileList) {
        const sizeLimit = file.size / 1024 / 1024 > 10
        if (sizeLimit) {
          this.$Notice.warning({
            title: this.$t('address.Operationreminder'),
            desc: this.$t('support.pictureSize')
          });
          return false;
        }
        this.cnInvoiceFile = file
        return false;
      },
      cnInvoiceUploading(event, file, fileList) {
        this.cnInvoiceMessage = this.$t('support.fileUploadedAndProgressDisappears')
      },
      removeCnInvoiceFile() {
        this.cnInvoiceFile = ''
				this.cnInvoiceMessage = ''
      },
    },
	};
</script>

<style>
	#space {
		/* height: 30px;
		line-height: 30px; */
		font-size: 12px;
		white-space: pre-line;
		list-style: none;
	}
  .task-name {
    display: inline-block; /* 或者 block，取决于你的布局需求 */
    width: 350px; /* 根据需要设置合适的宽度 */
    /* white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; */
    word-break: break-all;
    padding: 5px; /* 内边距 */
    margin-bottom: 10px; /* 外边距 */
  }
</style>
