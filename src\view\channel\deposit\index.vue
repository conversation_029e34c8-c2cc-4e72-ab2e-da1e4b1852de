<template>
  <!-- 押金/账户管理 -->
  <Card style="z-index: auto;">
    <Form ref="formInline" :model="formInline" inline style="margin: 50px 0; font-weight: blod;">
      <FormItem :label="$t('support.cooperationModel')" :label-width="120">
        <Input v-model="formInline.cooperationMode" readonly style="width: 250px;"></Input>
      </FormItem>
      <FormItem :label="$t('deposit.channelMode')" :label-width="100">
        <Input v-model="formInline.channelType" readonly style="width: 250px;"></Input>
      </FormItem>
      <FormItem :label="$t('deposit.currency')" :label-width="80">
        <Input v-model="formInline.currencyCode" readonly style="width: 250px;"></Input>
      </FormItem>
    </Form>
    <Table :columns="columns" :data="data" border style="width: 100%;" :loading="loading">
      <!-- 第二列：数据 + 按钮1 -->
      <template slot-scope="{ row }" slot="column2">
        <div class="cell-content">
          <div style="margin: 10px 0;">{{ row.marketingAmount }}</div>
          <Button v-has="'marketingAccountDetails'" type="info" size="small" ghost
            @click="handleDetailClick(row, 'button1')" style="margin: 0 0 10px 0;">
            {{$t('deposit.marketingAccountDetails')}}
          </Button>
        </div>
      </template>

      <!-- 第三列：数据 + 按钮2 -->
      <template slot-scope="{ row }" slot="column3">
        <div class="cell-content">
          <div style="margin: 10px 0;">{{ row.creditAmount }}</div>
          <!-- <Button type="info" size="small" ghost @click="handleDetailClick(row, 'button2')" style="margin: 0 0 10px 0;">
            信用账户详情
          </Button> -->
        </div>
      </template>
    </Table>
    <div style="margin: 50px 0; font-weight: blod; display: flex;justify-content: center;">
      <Button v-has="'Packagedetails'" :disabled="cooperationMode == '3'" size="large" type="success" style="width: 150px;margin-right: 100px;"
        @click="details(1)">{{ $t("deposit.canmeal") }}</Button>
      <Button v-has="'streamdetails'" :disabled="['2', '3'].includes(cooperationMode)" size="large" type="warning" style="minWidth: 150px;margin-right: 100px;"
        @click="details(2)">{{ $t("deposit.flow") }}</Button>
      <Button v-has="'recharge'" :disabled="cooperationMode == '3'" size="large" type="error" style="width: 150px;"
        @click="recharge">{{ $t("offlinePay.topup") }}</Button>
    </div>
  </Card>
</template>

<script>
  import {
    searchDeposit,
    getAccountManagement,
    searchcorpid,
  } from '@/api/channel.js'
  const math = require('mathjs')
  export default {
    components: {},
    data() {
      return {
        loading: false,
        corpId: '',
        cooperationMode: '',
        formInline: {
          cooperationMode: "",
          channelType: "",
          currencyCode: "",
        },
        columns: [{
            title: '',
            key: 'deposit',
            minWidth: 200,
            align: 'center',
            tooltip: true,
          },
          {
            title: this.$t("deposit.marketingAccount"),
            slot: 'column2',
            align: 'center',
            minWidth: 200,
          },
          {
            title: this.$t("deposit.creditAccount"),
            slot: 'column3',
            align: 'center',
            minWidth: 200,
          },
          // {
          //   title: this.$t("support.totalAmount"),
          //   key: 'totalAmount',
          //   minWidth: 120,
          //   align: 'center',
          //   tooltip: true,
          // },
          // {
          //   title: this.$t("deposit.deposit_money"),
          //   key: 'balance',
          //   minWidth: 120,
          //   align: 'center',
          //   tooltip: true,
          // },
          // {
          //   title: this.$t("usedLimit"),
          //   key: 'usedAmount',
          //   minWidth: 120,
          //   align: 'center',
          //   tooltip: true,
          // },
        ],
        data: [],
      }
    },
    watch: {
      // 监听 formInline.channelType 的变化
      'formInline.channelType': {
        handler(newVal) {
          this.updateColumnsTitle(newVal);
        },
        immediate: true, // 立即触发一次
      },
    },
    mounted() {
      this.cooperationMode = sessionStorage.getItem("cooperationMode")
      this.corpId = sessionStorage.getItem("corpId")
      this.goPageFirst(1)
    },
    methods: {
      goPageFirst(page) {
        this.loading = true
        let corpId = this.corpId
        let cooperationMode = this.cooperationMode
        getAccountManagement({
          corpId,
          cooperationMode
        }).then(res => {
          if (res.code == '0000') {
            this.formInline.cooperationMode = this.cooperationMode == '1' ? this.$t('support.distribution') : this.cooperationMode == '2' ? this
              .$t('support.atoz') : this.$t('support.resourceCooperation')
            this.formInline.channelType = res.data[0].channelType == '1' ? this.$t('offlinePay.deposit') : res.data[0].channelType == '2' ? this.$t(
              'offlinePay.Prepayment') : ''
            this.formInline.currencyCode = res.data[0].currencyCode == '156' ? this.$t('support.CNY') : res.data[0]
              .currencyCode == '840' ? this.$t('support.USD') : res.data[0].currencyCode == '344' ? this.$t(
                'support.HKD') : '';
            // 可用额度
            if (cooperationMode == '2') {
              this.data.balance = res.data[0].balance == '**********' ? "0" : parseFloat(math.divide(math.bignumber(res.data[0].balance), 100).toFixed(2)).toString()
            } else {
              this.data.balance = parseFloat(math.divide(math.bignumber(res.data[0].balance), 100).toFixed(2))
                .toString()
            }
            this.data = res.data
          }
        }).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.loading = false
        })
      },
      // 更新 columns 的 title
      updateColumnsTitle(channelType) {
        this.columns[0].title = channelType === this.$t('offlinePay.deposit')
          ? this.$t('deposit.depositAccount')
          : channelType === this.$t('offlinePay.Prepayment')
          ? this.$t('deposit.preDepositAccount')
          : '';
      },
      details(id) {
        if (id === 1) {
          this.$router.push({
            path: '/mealList',
          })
        } else {
          this.$router.push({
            path: '/streamList',
          })
        }

      },
      // 充值
      recharge() {
        this.$router.push({
          path: '/offlinePayment',
        })
      },
      // 营销账户详情/信用账户详情
      handleDetailClick(row, buttonType) {
        // 这里添加具体的处理逻辑
        if (buttonType === 'button1') {
          this.$router.push({
            path: '/marketingAccount',
          })
        } else if (buttonType === 'button2') {
          // this.$router.push({
          // 	path: '/marketingAccount',
          // })
        }
      },
    },
  }
</script>

<style scoped>
</style>
