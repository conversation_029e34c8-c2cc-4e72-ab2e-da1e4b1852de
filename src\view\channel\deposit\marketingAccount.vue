<template>
  <!-- 营销账户详情  -->
  <div>
    <Card>
      <div class="search_head_i">
        <Button v-has="'marketingAccountFlow'" v-preventReClick type="primary" style="margin: 30px 0;"
          @click="showAccountFlow">{{$t('deposit.marketingAccountFlow')}}</Button>
      </div>
      <!-- 数据列表 -->
      <div style="margin-top:20px">
        <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
          <template slot-scope="{ row, index }" slot="usedAmount">
            <a @click="shoWusedAmount(row)"
              v-if="row.usedAmount != null && row.usedAmount !== ''">{{row.usedAmount}}</a>
            <span v-else>\</span>
          </template>
        </Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="total" :current.sync="currentPage" :page-size="pageSize" show-total show-elevator
          @on-change="goPage" style="margin: 10px 0;" />
      </div>
      <div style="text-align: center;">
        <Button icon="ios-arrow-back" @click="reback">{{$t('support.back')}}</Button>
      </div>
    </Card>

    <!-- 营销账户流水  -->
    <Modal :title="flowTitle" v-model="accountFlowModel" :mask-closable="false" @on-cancel="cancelModel" width="1380"
      :footer-hide="true">
      <div>
        <DatePicker type="daterange" format="yyyy-MM-dd" placement="bottom-end" v-model="date" style="width: 200px"
          :placeholder="$t('flow.PleaseChoosedate')" @on-change="handleDateChange" @on-clear="hanldeDateClear">
        </DatePicker>
        <Button v-has="'searchFlow'" type="primary" icon="md-search" :loading="searchloading" @click="searchAccountFlow"
          style="margin: 0 20px;">{{$t('support.search')}}</Button>
        <Button v-has="'exportFlow'" type="success" icon="ios-cloud-download-outline" :loading="exportloading"
          @click="exportFile('1')">{{$t('stock.exporttb')}}</Button>
      </div>
      <div style="margin-top:20px">
        <Table :columns="cooperationMode == '1' ? flowColumns1 : flowColumns2" :data="flowData" :ellipsis="true"
          :loading="flowloading"></Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="flowTotal" :current.sync="flowCurrentPage" :page-size="pageSize" show-total show-elevator
          @on-change="goFlowPage" style="margin: 10px 0;" />
      </div>
      <div style="text-align: center;">
        <Button icon="ios-arrow-back" @click="cancelModel">{{$t('support.back')}}</Button>
      </div>
    </Modal>

    <!-- 营销活动返利流水详情 -->
    <Modal :title="flowTitle" v-model="rebateFlowModel" :mask-closable="false" @on-cancel="cancelModel" width="1380"
      :footer-hide="true">
      <div>
        <DatePicker type="daterange" format="yyyy-MM-dd" placement="bottom-end" v-model="date" style="width: 200px"
          :placeholder="$t('flow.PleaseChoosedate')" @on-change="handleDateChange" @on-clear="hanldeDateClear">
        </DatePicker>
        <Button v-has="'searchRebate'" type="primary" icon="md-search" :loading="searchloading"
          @click="searchAccountFlow" style="margin: 0 20px;">{{$t('support.search')}}</Button>
        <Button v-has="'exportRebate'" type="success" icon="ios-cloud-download-outline" :loading="exportloading"
          @click="exportFile('2')">{{$t('stock.exporttb')}}</Button>
      </div>
      <div style="margin-top:20px">
        <Table :columns="cooperationMode == '1' ? flowColumns3 : flowColumns4" :data="flowData" :ellipsis="true"
          :loading="flowloading"></Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="flowTotal" :current.sync="flowCurrentPage" :page-size="pageSize" show-total show-elevator
          @on-change="goFlowPage" style="margin: 10px 0;" />
      </div>
      <div style="text-align: center;">
        <Button icon="ios-arrow-back" @click="cancelModel">{{$t('support.back')}}</Button>
      </div>
    </Modal>

    <!-- 导出提示 -->
    <Modal v-model="exportModalr" :mask-closable="true" @on-cancel="exportcancelModal">
      <div style="align-items: center;justify-content:center;display: flex; flex-wrap: wrap;">
        <Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
          <h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
          <FormItem :label="$t('exportID')">
            <span>{{taskId}}</span>
          </FormItem>
          <FormItem :label="$t('exportFlie')">
            <span class="task-name">{{taskName}}</span>
          </FormItem>
          <span style="text-align: left;">{{$t('downloadResult')}}</span>
        </Form>
      </div>

      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
        <Button @click="exportcancelModal">{{$t('common.cancel')}}</Button>
        <Button type="primary" @click="Gotor">{{$t('Goto')}}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import {
    getMarketingAaccountDetails,
    selectMarketingAccountFlow,
    selectMarketingAccountFlowRebate,
    MarketingAccountFlowOut,
    MarketingAccountFlowOutRebate
  } from '@/api/channel/marketingAccount';
  export default {
    components: {},
    data() {
      return {
        taskId: "",
        taskName: "",
        corpId: "",
        cooperationMode: "",
        modelType: "",
        activityId: "",
        flowTitle: "",
        startTime: "",
        endTime: "",
        adminOrChannel: false,
        date: [],
        currentPage: 1, //表格当前页面
        flowCurrentPage: 1,
        pageSize: 10, //表格加载大小
        total: 0, //表格数据总量
        flowTotal: 0,
        loading: false, //表格加载load标识
        flowloading: false,
        searchloading: false,
        exportloading: false,
        accountFlowModel: false,
        rebateFlowModel: false,
        exportModalr: false,
        columns: [{
            title: this.$t("deposit.marketingActivities"),
            key: 'name',
            minWidth: 150,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.name === null || row.name === undefined || row.name === '') ? '\\' : row.name;
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.eventStartTime"),
            key: 'beginTime',
            align: 'center',
            minWidth: 150,
            render: (h, params) => {
              const row = params.row
              const text = (row.beginTime === null || row.beginTime === undefined || row.beginTime === '') ? '\\' :
                this.formatDateToDash(row.beginTime);
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.eventEndTime"),
            key: 'endTime',
            align: 'center',
            minWidth: 150,
            render: (h, params) => {
              const row = params.row
              const text = (row.endTime === null || row.endTime === undefined || row.endTime === '') ? '\\' :
                this.formatDateToDash(row.endTime);
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.campaignStatus"),
            key: 'status',
            minWidth: 120,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              var text = row.status == '0' ? this.$t("deposit.toBeStarted") :
                row.status == '1' ? this.$t("deposit.started") :
                row.status == '2' ? this.$t("deposit.ended") :
                row.status == '3' ? this.$t("deposit.obsolete") :
                row.status == '4' ? this.$t("deposit.earlyTerminate") : "\\"
              return h('label', {
                style: {
                  'word-break': 'break-word',
                }
              }, text)
            }
          },
          {
            title: this.$t("deposit.rebateAmount"),
            key: 'rebateAmount',
            minWidth: 130,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.rebateAmount === null || row.rebateAmount === undefined || row.rebateAmount ===
                '') ? '\\' : row.rebateAmount;
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.usedAmount"),
            slot: 'usedAmount',
            minWidth: 120,
            align: 'center',
            tooltip: true,
          },
          {
            title: this.$t("deposit.rebateBalance"),
            key: 'remainAmount',
            minWidth: 150,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.remainAmount === null || row.remainAmount === undefined || row.remainAmount ===
                '') ? '\\' : row.remainAmount;
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.arrivalTime"),
            key: 'createTime',
            minWidth: 160,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.createTime === null || row.createTime === undefined || row.createTime === '') ?
                '\\' :
                this.formatDateToDash(row.createTime);
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.effectiveTime"),
            key: 'effectiveTime',
            minWidth: 160,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.effectiveTime === null || row.effectiveTime === undefined || row.effectiveTime === '') ?
                '\\' :
                this.formatDateToDash(row.effectiveTime);
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.expirationTime"),
            key: 'expireTime',
            minWidth: 160,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.expireTime === null || row.expireTime === undefined || row.expireTime === '') ?
                '\\' :
                this.formatDateToDash(row.expireTime);
              return h('span', text);
            }
          },
        ], //表格标题
        flowColumns1: [{
            title: this.$t("deposit.spendTime"),
            key: 'consumptionDate',
            minWidth: 170,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.consumptionDate === null || row.consumptionDate === undefined || row
                  .consumptionDate === '') ? '\\' :
                this.formatDate(row.consumptionDate);
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.charge_type"),
            key: 'type',
            align: 'center',
            minWidth: 250,
            render: (h, params) => {
              const row = params.row
              var text = row.type == '1' ? this.$t("deposit.increaseMarketinRebates") :
                row.type == '2' ? this.$t("deposit.distributionPackageOrdering") :
                row.type == '3' ? this.$t("support.fuelPackpackageOrder") :
                row.type == '4' ? this.$t("support.packageCancellation") :
                row.type == '5' ? this.$t("support.fuelPackUnsubscribe") :
                row.type == '6' ? this.$t("deposit.ResetMarketingBudget") :
                row.type == '7' ? this.$t("deposit.OffsetMarketingRebateAmount") : "\\"
              return h('label', {
                style: {
                  'word-break': 'break-word',
                }
              }, text)
            }
          },
          {
            title: this.$t("deposit.connectedActivities"),
            key: 'activity',
            align: 'center',
            minWidth: 150,
            render: (h, params) => {
              const row = params.row
              const text = (row.activity === null || row.activity === undefined || row.activity === '') ? '\\' : row
                .activity
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.currency"),
            key: 'currency',
            minWidth: 120,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.currency === null || row.currency === undefined || row.currency === '') ? '\\' : row
                .currency
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.consumptionAmount"),
            key: 'amount',
            minWidth: 180,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.amount === null || row.amount === undefined || row.amount === '') ? '\\' : row
                .amount
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.accountTotalBalance"),
            key: 'totalAmount',
            minWidth: 250,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.totalAmount === null || row.totalAmount === undefined || row.totalAmount === '') ?
                '\\' : row.totalAmount
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.totalOrderNumber"),
            key: 'totalOrderId',
            minWidth: 200,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.totalOrderId === null || row.totalOrderId === undefined || row.totalOrderId ===
                '') ? '\\' : row.totalOrderId
              return h('span', text);
            }
          },
        ],
        flowColumns2: [{
            title: this.$t("deposit.settlementDate"),
            key: 'balanceDate',
            minWidth: 150,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.balanceDate === null || row.balanceDate === undefined || row.balanceDate === '') ?
                '\\' :
                this.formatDate(row.balanceDate);
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.charge_type"),
            key: 'type',
            align: 'center',
            minWidth: 200,
            render: (h, params) => {
              const row = params.row
              var text = row.type == '1' ? this.$t("deposit.increaseMarketinRebates") :
                row.type == '2' ? this.$t("deposit.dataUsageSettlemtn") :
                row.type == '3' ? this.$t("deposit.ResetMarketingBudget") : "\\"
              return h('label', {
                style: {
                  'word-break': 'break-word',
                }
              }, text)
            }
          },
          {
            title: this.$t("deposit.connectedActivities"),
            key: 'activity',
            align: 'center',
            minWidth: 150,
            render: (h, params) => {
              const row = params.row
              const text = (row.activity === null || row.activity === undefined || row.activity === '') ? '\\' : row
                .activity
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.currency"),
            key: 'currency',
            minWidth: 120,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.currency === null || row.currency === undefined || row.currency === '') ? '\\' : row
                .currency
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.consumptionAmount"),
            key: 'amount',
            minWidth: 150,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.amount === null || row.amount === undefined || row.amount === '') ? '\\' : row
                .amount
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.accountTotalBalance"),
            key: 'totalAmount',
            minWidth: 220,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.totalAmount === null || row.totalAmount === undefined || row.totalAmount === '') ?
                '\\' : row.totalAmount
              return h('span', text);
            }
          },
        ],
        flowColumns3: [{
            title: this.$t("deposit.spendTime"),
            key: 'consumptionDate',
            minWidth: 170,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.consumptionDate === null || row.consumptionDate === undefined || row
                  .consumptionDate === '') ? '\\' :
                this.formatDate(row.consumptionDate);
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.charge_type"),
            key: 'type',
            align: 'center',
            minWidth: 250,
            render: (h, params) => {
              const row = params.row
              var text = row.type == '1' ? this.$t("deposit.increaseMarketinRebates") :
                row.type == '2' ? this.$t("deposit.distributionPackageOrdering") :
                row.type == '3' ? this.$t("support.fuelPackpackageOrder") :
                row.type == '4' ? this.$t("support.packageCancellation") :
                row.type == '5' ? this.$t("support.fuelPackUnsubscribe") :
                row.type == '6' ? this.$t("deposit.ResetMarketingBudget") :
                row.type == '7' ? this.$t("deposit.OffsetMarketingRebateAmount") : "\\"
              return h('label', {
                style: {
                  'word-break': 'break-word',
                }
              }, text)
            }
          },
          {
            title: this.$t("deposit.connectedActivities"),
            key: 'activity',
            align: 'center',
            minWidth: 150,
            render: (h, params) => {
              const row = params.row
              const text = (row.activity === null || row.activity === undefined || row.activity === '') ? '\\' : row
                .activity
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.currency"),
            key: 'currency',
            minWidth: 120,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.currency === null || row.currency === undefined || row.currency === '') ? '\\' : row
                .currency
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.consumptionAmount"),
            key: 'amount',
            minWidth: 180,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.amount === null || row.amount === undefined || row.amount === '') ? '\\' : row
                .amount
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.singleActivityBalance"),
            key: 'deposit',
            minWidth: 250,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.deposit === null || row.deposit === undefined || row.deposit === '') ? '\\' : row
                .deposit
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.totalOrderNumber"),
            key: 'totalOrderId',
            minWidth: 200,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.totalOrderId === null || row.totalOrderId === undefined || row.totalOrderId ===
                  '') ? '\\' : row
                .totalOrderId
              return h('span', text);
            }
          },
        ],
        flowColumns4: [{
            title: this.$t("deposit.settlementDate"),
            key: 'balanceDate',
            minWidth: 150,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.balanceDate === null || row.balanceDate === undefined || row
                  .balanceDate === '') ? '\\' :
                this.formatDate(row.balanceDate);
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.charge_type"),
            key: 'type',
            align: 'center',
            minWidth: 200,
            render: (h, params) => {
              const row = params.row
              var text = row.type == '1' ? this.$t("deposit.increaseMarketinRebates") :
                row.type == '2' ? this.$t("deposit.dataUsageSettlemtn") :
                row.type == '3' ? this.$t("deposit.ResetMarketingBudget") : "\\"
              return h('label', {
                style: {
                  'word-break': 'break-word',
                }
              }, text)
            }
          },
          {
            title: this.$t("deposit.connectedActivities"),
            key: 'activity',
            align: 'center',
            minWidth: 150,
            render: (h, params) => {
              const row = params.row
              const text = (row.activity === null || row.activity === undefined || row.activity === '') ? '\\' : row
                .activity
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.currency"),
            key: 'currency',
            minWidth: 120,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.currency === null || row.currency === undefined || row.currency === '') ? '\\' : row
                .currency
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.consumptionAmount"),
            key: 'amount',
            minWidth: 150,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.amount === null || row.amount === undefined || row.amount === '') ? '\\' : row
                .amount
              return h('span', text);
            }
          },
          {
            title: this.$t("deposit.singleActivityBalance"),
            key: 'deposit',
            minWidth: 200,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = (row.deposit === null || row.deposit === undefined || row.deposit === '') ? '\\' : row
                .deposit
              return h('span', text);
            }
          },
        ],
        tableData: [], //表格数据
        flowData: [],
      }
    },
    mounted() {
      let corpId = sessionStorage.getItem("corpId")
      if(corpId == '' || corpId == 'null' || corpId == 'undefined'){
        //超管
        this.adminOrChannel = true
        this.corpId = this.$route.query.corpId;
        this.cooperationMode = this.$route.query.type
        // 安全地处理 searchObj
        try {
          const searchObj = this.$route.query.searchObj;
          if (searchObj) {
            localStorage.setItem("searchObj", searchObj);
          }
        } catch (e) {
          console.warn("Failed to handle searchObj", e);
        }
      } else {
        //渠道商
        this.adminOrChannel = false
        this.corpId = sessionStorage.getItem("corpId")
        this.cooperationMode = sessionStorage.getItem("cooperationMode")
      }
      this.goPageFirst(1)
    },
    methods: {
      /* --------------- 营销活动详情 --------------- */

      // 分页跳转
      goPage(page) {
        this.goPageFirst(page);
      },
      //页面加载
      goPageFirst(page) {
        this.tableData = []
        this.loading = true;
        getMarketingAaccountDetails({
          pageNum: page,
          pageSize: this.pageSize,
          corpId: this.corpId,
          cooperationMode: this.cooperationMode,
        }).then(res => {
          if (res && res.code == '0000') {
            this.total = Number(res.count);
            this.tableData = res.data;
            this.currentPage = page;
          } else {
            throw res
          }
        }).catch((err) => {

        }).finally(() => {
          this.loading = false;
        })
      },
      reback() {
        let searchObjStr = '';
        try {
          const storedData = localStorage.getItem("searchObj");
          if (storedData && storedData !== 'undefined' && storedData !== 'null') {
            searchObjStr = storedData;
          }
        } catch (e) {
          console.warn("Failed to get searchObj from localStorage", e);
        }

        if (this.adminOrChannel) {
          //客户管理-渠道商详情
          this.$router.push({
            path: '/channelDetails',
            query: {
              id: this.corpId,
              searchObj: searchObjStr
            }
          })
        } else {
          // 账户管理
          this.$router.push({
            path: '/deposit',
          })
        }
      },
      //取消
      cancelModel() {
        this.flowTitle = "";
        this.accountFlowModel = false;
        this.rebateFlowModel = false;
        this.startTime = ''
        this.endTime = ''
        this.date = []
        this.flowData = []
        this.hanldeDateClear()
      },
      // 处理时间格式
      formatDate(dateStr) {
        if (dateStr) {
          const date = new Date(dateStr.replace(/-/g, '/'));
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          const day = date.getDate();
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          const seconds = String(date.getSeconds()).padStart(2, '0');
          if (this.cooperationMode == '1') {
            let data = this.$i18n.locale === 'zh-CN' ? `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}` : this
              .$i18n.locale === 'en-US' ? `${year}-${month}-${day} ${hours}:${minutes}:${seconds}` : ""
            return data;
          } else {
            let data = this.$i18n.locale === 'zh-CN' ? `${year}年${month}月${day}日` : this
              .$i18n.locale === 'en-US' ? `${year}-${month}-${day}` : ""
            return data;
          }
        } else {
          return ""
        }
      },
      formatDateToDash(dateStr) {
        const [year, month, day] = dateStr
          .replace(/年|月|日/g, '-')
          .split('-')
          .filter(Boolean);
        let data = this.$i18n.locale === 'zh-CN' ? dateStr : `${year}-${parseInt(month, 10)}-${parseInt(day, 10)}`
        return data;
      },

      /* --------------- 营销账户流水/营销活动返利流水详情 --------------- */

      // 营销账户流水 弹窗
      showAccountFlow() {
        this.modelType = '1'
        let distribution = this.$t('deposit.marketingAccountFlow') + '（' + this.$t('support.distribution') + '）'
        let a2z = this.$t('deposit.marketingAccountFlow') + '（' + this.$t('support.atoz') + '）'
        let resourceCooperation = this.$t('deposit.marketingAccountFlow') + '（' + this.$t('support.resourceCooperation') + '）'
        this.flowTitle = this.cooperationMode == '1' ? distribution : this.cooperationMode == '2' ? a2z : resourceCooperation
        this.accountFlowModel = true
      },
      // 营销活动返利流水详情 弹窗
      shoWusedAmount(row) {
        this.modelType = '2'
        let distribution = this.$t('deposit.marketingActivityDetails') + '（' + this.$t('support.distribution') + '）'
        let a2z = this.$t('deposit.marketingActivityDetails') + '（' + this.$t('support.atoz') + '）'
        let resourceCooperation = this.$t('deposit.marketingActivityDetails') + '（' + this.$t('support.resourceCooperation') + '）'
        this.flowTitle = this.cooperationMode == '1' ? distribution : this.cooperationMode == '2' ? a2z : resourceCooperation
        this.activityId = row.activityId
        this.rebateFlowModel = true
      },
      // 获取起止时间
      handleDateChange(date) {
        if (Array.isArray(date)) {
          this.startTime = date[0];
          this.endTime = date[1];
        }
      },
      // 清除起止时间
      hanldeDateClear() {
        this.startTime = ''
        this.endTime = ''
        this.date = []
      },
      searchAccountFlow() {
        if (this.startTime == '' && this.endTime == '') {
          this.$Notice.warning({
            desc: this.$t('deposit.startEndDate')
          });
          this.flowData = []
          return
        }
        this.goFlowPageFirst(1)
      },
      goFlowPage(page) {
        this.goFlowPageFirst(page);
      },
      //查询 营销账户流水/营销活动返利流水详情 列表
      goFlowPageFirst(page) {
        this.flowData = []
        this.flowloading = true;
        // 营销账户流水
        const commonData = {
          beginDate: this.startTime,
          endDate: this.endTime,
          pageNum: page,
          pageSize: this.pageSize,
          corpId: this.corpId,
          cooperationMode: this.cooperationMode,
        };

        // 营销活动返利流水详情
        const requestData = this.modelType === '1' ? {
          ...commonData,
        } : {
          ...commonData,
          activityId: this.activityId,
        };
        let func = this.modelType == '1' ? selectMarketingAccountFlow : selectMarketingAccountFlowRebate
        func(requestData).then(res => {
          if (res && res.code == '0000') {
            this.flowTotal = Number(res.count);
            this.flowData = res.data;
            this.flowCurrentPage = page;
          } else {
            throw res
          }
        }).catch((err) => {

        }).finally(() => {
          this.flowloading = false;
        })
      },
      //导出 营销账户流水/营销活动返利流水详情
      exportFile(type) {
        if (this.startTime == '' && this.endTime == '') {
          this.$Notice.warning({
            desc: this.$t('deposit.startEndDate')
          });
          this.flowData = []
          return
        }
        this.exportloading = true
        // 营销账户流水导出
        const commonData = {
          beginDate: this.startTime,
          endDate: this.endTime,
          corpId: this.corpId,
          cooperationMode: this.cooperationMode,
        };
        // 营销活动返利流水详情
        const requestData = type === '1' ? {
          ...commonData,
        } : {
          ...commonData,
          activityId: this.activityId,
        };

        const func = type === '1' ? MarketingAccountFlowOut : MarketingAccountFlowOutRebate;
        func(requestData).then((res) => {
          if (res.code && res.code == '0000') {
            this.exportModalr = true
            this.taskId = res.data.taskId
            this.taskName = res.data.taskName
          }
        }).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.exportloading = false
        })
      },
      exportcancelModal() {
        this.exportModalr = false
      },
      Gotor() {
        this.$router.push({
          path: '/taskList',
          query: {
            taskId: encodeURIComponent(this.taskId),
            fileName: encodeURIComponent(this.taskName),
          }
        })
        this.exportModalr = false
        this.cancelModel()
      },
    },
  }
</script>
<style scoped="scoped">
  .search_head_i {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
  }
</style>
