<template>
	<!-- 套餐详情 -->
	<Card >
		<div style="width: 100%;margin-top: 50px; margin: auto;">
			<!-- 表格 -->
			<Button style="margin: 0 2px;" type="success"  @click="exportDetails(1)" v-has="'export'" :disabled="cooperationMode == '3'">
				<div style="display: flex;align-items: center;">
					<Icon type="ios-cloud-download-outline" />&nbsp;{{$t("stock.exporttb")}}</div>
			</Button>
			<a ref="downloadLink" style="display: none"></a>
			<Table :columns="columns12" :data="data" style="width:100%;margin-top: 50px;" :loading="loading">
				<template slot-scope="{ row }" slot="packageId">
					<strong>{{ row.packageId }}</strong>
				</template>
				<template slot-scope="{ row }" slot="packageName">
					<strong>{{ row.packageName }}</strong>
				</template>
			</Table>
			<!-- 分页 -->
			<div style="margin-top: 100px;">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
			
		</div>
	</Card>
</template>

<script>
	import{
		mealList,
		searchcorpid,
		PackageExport
	} from '@/api/channel.js'
	const math = require('mathjs')
	export default {
		data(){
			return {
				mealname:'',
				corpId:'',
				total:0,
				page:0,
				currentPage:1,
				loading:false,
				searchloading:false,
				form:{},
				columns12: [{
						title:this.$t("deposit.mealId"),
						slot: 'packageId',
						minWidth: 300,
						align: 'center'
					},
					{
						title:this.$t("deposit.mealname"),
						slot: 'packageName',
						align: 'center',
						minWidth: 300,
						render: (h, params) => {
							const row = params.row
							var text = this.$i18n.locale==='zh-CN' ? row.packageName:this.$i18n.locale==='en-US' ? row.packageNameEn: ''
							return h('label',{
								style:{
									'word-break':'break-word',
								}
							},text)
						}
					},
					{
						title:this.$t("deposit.mealprice"),
						key: 'price',
						minWidth: 300,
						align: 'center',
						render: (h, params) => {
							const row = params.row
							const text = row.groupType =='2' ? parseFloat(math.divide(math.bignumber(row.packagePrice),100).toFixed(2)).toString() 
							: row.groupType =='1'  ? parseFloat(math.divide(math.bignumber(row.groupPrice),100).toFixed(2)).toString() :''
							return h('label', text)
						}
					}
				],
				data:[],
				rules: {
					
				},
				cooperationMode: '',
			}
		},
		mounted(){
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			if (this.cooperationMode != '3') {
				this.goPageFirst(1)
			}
		},
		methods:{
			goPageFirst(page){
				this.loading = true
				var _this = this
				searchcorpid({
					userName:this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						let corpId=res.data
						this.corpId=corpId
						let pageNumber =page
						let pageSize=10 
						let cooperationMode = this.cooperationMode
						mealList({
							corpId,
							pageNumber,
							pageSize,
							cooperationMode
						}).then(res => {
							if (res.code == '0000') {
								_this.loading = false
								this.searchloading=false
								this.page = page
								this.currentPage=page
								this.total = res.data.total
								this.data = res.data.record
							}
						}).catch((err) => {
							console.error(err)
						}).finally(() => {
							this.loading = false
						})
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				})
			},
			search(){
				this.goPageFirst(1)
			},
			goPage(page){
				this.goPageFirst(page)
			},
			//可购买套餐导出
			exportDetails(page) {
				let pageNumber =page
				let pageSize=10 
				let corpId=this.corpId
				PackageExport({
					corpId,
					pageNumber,
					pageSize,
					cooperationMode: this.cooperationMode
				}).then(res => {
					const content = res.data
					const fileName =this.$t("deposit.ProductList")+ '.csv' // 导出文件名
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content, fileName)
					}
				}).catch(err => this.exporting = false)
			},
		}
	}
</script>

<style>
</style>
