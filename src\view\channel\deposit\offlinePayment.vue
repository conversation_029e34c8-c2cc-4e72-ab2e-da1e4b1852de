<template>
	<!-- 线上、线下支付 -->
	<Card>
		<div style="width: 100%;margin-top: 50px; margin: auto;">
			<div style="display: flex;width: 100%;align-items:center;">
				<Button style="margin: 0 2px;margin-left: 20px;" type="primary" icon="md-add" v-has="'addInvoice'"
					@click="addInvoice(null,'1')" :disabled="cooperationMode == '3'">{{$t('offlinePay.applyInvoice')}}</Button>
			</div>
			<!-- 表格 -->
			<Table :columns="columns" :data="data" border style="width:100%;margin-top: 50px;" :loading="loading">
				<template slot-scope="{ row, index }" slot="file">
					<Button type="warning" ghost size="small" style="margin-right: 5px" @click="downloadInvoice(row)"
            v-show="['4','5','6','7','9'].includes(row.chargeStatus)" v-has="'invoice'">Invoice</Button>
					<Button type="info" ghost size="small" @click="downloadPaymentProof(row)"
            v-show="['5','6','7'].includes(row.chargeStatus)&&row.paymentProofAddress!=null&&row.paymentProofAddress!=''" v-has="'payslip'">{{$t('support.payslip')}}</Button>
				</template>
        <template slot-scope="{ row, index }" slot="action">
        	<Button type="error" ghost size="small" style="margin-right: 5px" @click="revokeItem(row)"
            v-show="row.chargeStatus == '1'" v-has="'revoke'">{{$t('support.revoke')}}</Button>
          <Button type="info" ghost size="small" style="margin-right: 5px"
            v-show="row.chargeStatus == '4'" v-has="'onlinePayment'"  @click="onlineRecharge(row)">{{$t('channelBill.onlinePayment')}}</Button>
          <Button type="success" ghost size="small" style="margin-right: 5px" @click="pay(row,'1')"
            v-show="row.chargeStatus == '4'" v-has="'offlinePayment'">{{$t('channelBill.offlinePayment')}}</Button>
        	<Button type="primary" ghost size="small" style="margin-right: 5px" @click="addInvoice(row,'2')"
            v-show="row.chargeStatus == '3'" v-has="'reAddInvoice'">{{$t('offlinePay.reApplyInvoice')}}</Button>
        	<Button type="success" ghost size="small" style="margin-right: 5px" @click="pay(row,'2')"
            v-show="row.chargeStatus == '6'" v-has="'rePay'">{{$t('offlinePay.rePay')}}</Button>
        </template>
			</Table>
			<!-- 分页 -->
			<div style="margin-left: 38%; margin-top: 50px; margin-bottom: 70px; ">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
		</div>
		<!-- 申请发票 -->
		<Modal v-model="addmodel" :title="$t('offlinePay.applyInvoice')" :mask-closable="false" @on-cancel="cancelModal" :width="400">
			<Form ref="formmodel" :model="formmodel" :rules="rules" :label-width="120">
				<div>
          <FormItem :label="$t('offlinePay.invoiceType') + '：'">
            {{formmodel.type}}
          </FormItem>
          <FormItem :label="$t('deposit.currency') + '：'">
          	{{formmodel.currencyCode}}
          </FormItem>
					<FormItem :label="$t('fuelPack.Amount') + '：'" prop="amount">
						<Input v-model="formmodel.amount" clearable :placeholder="$t('channelBill.inputAmount')" style="width: 200px" />
					</FormItem>
				</div>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">{{$t('support.back')}}</Button>
				<Button type="primary" :loading="addLoading" @click="addBesure">{{$t('address.determine')}}</Button>
			</div>
		</Modal>
    <!-- 缴付、重新上传付款证明 -->
    <Modal v-model="PaymentModal" :title="$t('channelBill.paymentPage')" :footer-hide="true" :mask-closable="false"
    	width="450px" @on-cancel="cancelModal">
    	<div>
    		<Form ref="formobj" :model="formobj" :rules="ruleobj" :label-width="100"
    			:label-height="100" inline style="font-weight:bold;">
    			<FormItem :label="$t('support.payslip')+':'" prop="file" style="font-size: 14px;font-weight: bold;">
    				<Upload type="drag" v-model="formobj.file" :action="uploadUrl" :before-upload="handleBeforeUpload"
              :on-progress="fileUploading" style="width: 250px; margin-top: 50px;">
    					<div style="padding: 20px 0">
    						<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
    						<p>{{$t('support.uploadPicture')}}</p>
    					</div>
    				</Upload>
    				<ul class="ivu-upload-list" v-if="file" style="width: 300px;">
    					<li class="ivu-upload-list-file ivu-upload-list-file-finish">
    						<span>
    							<Icon type="ios-folder" />{{file.name}}
    						</span>
    						<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" @click="removeFile"></i>
    					</li>
    				</ul>
    			</FormItem>
          <FormItem :label="$t('fuelPack.Amount')+':'" prop="amount">
          	<Input v-model="formobj.amount" clearable disabled :placeholder="$t('channelBill.inputAmount')" style="width: 250px" />
          </FormItem>
        </Form>
    		<div style="text-align: center;margin: 40px 0 0 0;">
    			<Button style="margin-right: 30px" @click="cancelModal">{{$t('support.back')}}</Button>
    			<Button type="primary" @click="pictureSubmit" v-preventReClick
    				:loading="pictureLoading">{{$t('common.determine')}}</Button>
    		</div>
    	</div>
    </Modal>
    <!-- 导出提示 -->
    <Modal v-model="exportModalr" :mask-closable="true" @on-cancel="exportcancelModal">
    	<div style="align-items: center;justify-content:center;display: flex; flex-wrap: wrap;">
    		<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
    			<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
    			<FormItem :label="$t('exportID')">
    				<span>{{taskId}}</span>
    			</FormItem>
    			<FormItem :label="$t('exportFlie')">
    				<span class="task-name">{{taskName}}</span>
    			</FormItem>
    			<span style="text-align: left;">{{$t('downloadResult')}}</span>
    		</Form>
    	</div>

    	<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
    		<Button @click="exportcancelModal">{{$t('common.cancel')}}</Button>
    		<Button type="primary" @click="Gotor">{{$t('Goto')}}</Button>
    	</div>
    </Modal>
    <a ref="downloadLink" style="display: none"></a>
		<!-- 其他内容 -->
		<Modal :title="$t('onlineOrder.onlineModalTitle')" v-model="payModal" :mask-closable="false" :width="720">
			<PaymentComponent orderType="deposit" :corpId="corpId" :billId="proofDetail.id" :showDeposit="true" :paySuccessInfo="paySuccessInfo"
				v-if="payModal" :amount="proofDetail.chargeAmount" :currencyCode="proofDetail.currencyCode" :payLoading="payStatus" @onlinePay="onlinePay" />
			<div slot="footer" style="width: 100%; display: flex; align-items: center; justify-content: flex-end;">
			</div>
		</Modal>
  </Card>
</template>

<script>
	import {
    goPageInfo,
		getRechargeRecord,
    exportInvoice,
    downloadPaymentProof,
    submitPaymentProof,
    requestInvoice,
    revoke
	} from '@/api/offlinePayment'
	import {
	createOrderAndPay
} from '@/api/onlinePay/pay.js'
import PaymentComponent from '@/components/onlinePayment/index'
	export default {
		components: {
			PaymentComponent
		},
		data() {
			const validateNum = (rule, value, callback) => {
				if (parseFloat(value) < 0) {
				  callback(new Error(this.$t('channelBill.lessThan0')));
				  return;
				}

			  var str1 = value
				if (value.substr(0, 1) === '-') {
					str1 = value.substr(1, value.length)
				}
			  var str = /^(?!0(\.0+)?$)([1-9]\d{0,7}(\.\d{1,2})?|\d{1,8}(\.\d{0,2})?)$/;
			  if (!str1 || str.test(str1)) {
			  	callback();
			  } else{
			  	callback(new Error(this.$t('channelBill.checkNumber')));
			  }
			};
			// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (!this.file) {
					callback(new Error(this.$t('support.pleaseUploadFile')))
				} else {
					callback()
				}
			}
      return {
        taskId: '',
        taskName: '',
				loading: false,
				total: 0,
				currentPage: 1,
				addmodel: false,
        exportModalr: false,
        PaymentModal: false,
				addLoading: false,
        pictureLoading: false,
				columns: [{
						title: this.$t('offlinePay.topupID'),
						key: 'billId',
						align: 'center',
            minWidth: 180,
            tooltip: true,
					},
          {
          		title: this.$t('offlinePay.invoiceNumber'),
          		key: 'invoiceNo',
          		align: 'center',
              minWidth: 180,
              tooltip: true,
          	},
          {
						title: this.$t('offlinePay.invoiceType'),
						key: 'channelType',
						align: 'center',
            minWidth: 150,
            tooltip: true,
            render: (h, params) => {
            	const row = params.row;
            	var text = "";
            	switch (row.channelType) {
            		case "1":
            			text = this.$t('offlinePay.deposit');
            			break;
            		case "2":
            			text = this.$t('offlinePay.Prepayment');
            			break;
            		default:
            			text = "";
            	}
            	return h("label", text);
            },
					},
					{
						title: this.$t('offlinePay.topupAmount'),
						key: 'chargeAmount',
						align: 'center',
            minWidth: 150,
            tooltip: true,
					},
					{
						title: this.$t('deposit.currency'),
						key: 'currencyCode',
						align: 'center',
            minWidth: 150,
            tooltip: true,
            render: (h, params) => {
            	const row = params.row;
            	var text = "";
            	switch (row.currencyCode) {
            		case "156":
            			text = this.$t('support.CNY');
            			break;
            		case "344":
            			text = this.$t('support.HKD');
            			break;
            		case "840":
            			text = this.$t('support.USD');
            			break;
            		default:
            			text = "";
            	}
            	return h("label", text);
            },
					},
					{
						title: this.$t('offlinePay.applyTime'),
						key: 'chargeTime',
						align: 'center',
            minWidth: 220,
            tooltip: true,
            render: (h, params) => {
            	const row = params.row;
            	var text = "";
            	// 创建一个新的Date对象
            	const date = new Date(row.chargeTime);

            	// 提取各个部分并格式化
            	const year = date.getFullYear();
            	const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
            	const day = String(date.getDate()).padStart(2, '0');
            	const hours = String(date.getHours()).padStart(2, '0');
            	const minutes = String(date.getMinutes()).padStart(2, '0');
            	const seconds = String(date.getSeconds()).padStart(2, '0');

            	// 格式化后的日期字符串
            	text = row.chargeTime ? `${year}-${month}-${day} ${hours}:${minutes}:${seconds}` : '';
            	return h("label", text);
            },
					},
          {
          	title: this.$t('flow.Status'),
          	key: 'chargeStatus',
          	align: 'center',
            minWidth: 180,
            tooltip: true,
            render: (h, params) => {
            	const row = params.row;
            	var text = "";
            	switch (row.chargeStatus) {
            		case "1":
            			text = this.$t('offlinePay.unpaid');
            			break;
            		case "2":
            			text = this.$t('offlinePay.invoicePendingApro');
            			break;
            		case "3":
            			text = this.$t('offlinePay.invoiceAproReject');
            			break;
                case "4":
                	text = this.$t('offlinePay.payable');
                	break;
                case "5":
                	text = this.$t('offlinePay.payPendingApro');
                	break;
                case "6":
                	text = this.$t('offlinePay.paymentAproReject');
                	break;
                case "7":
                	text = this.$t('offlinePay.paid');
                	break;
                case "8":
                	text = this.$t('support.cancelled');
                	break;
								case "9":
                	text = this.$t('channelBill.OnlinePaymentInProgress');
                	break;
            		default:
            			text = "";
            	}
            	return h("label", text);
            },
          },
          {
          	title: this.$t('support.failureReason'),
          	key: 'noPassReason',
          	align: 'center',
            minWidth: 150,
            tooltip: true,
          },
          {
          	title: this.$t('offlinePay.attachment'),
          	slot: 'file',
          	align: 'center',
            fixed: 'right',
            minWidth: 170,
          },
					{
						title: this.$t('support.action'),
						slot: 'action',
						align: 'center',
            fixed: 'right',
            minWidth: 200,
					}
				],
				data: [],
        supplierList: [],//供应商列表
				uploadUrl: '', //上传地址
				uploadList: [],
				file: null,
        rowType: '',
        payRowData: {},
        invoiceType: '',
        invoiceId: '',
        invoiceRowData: {},
        cooperationMode: '',
				formobj: {
					file: "",
				  amount: '',
				},
        formmodel: {
        	type: '',
        	currencyCode: '',
        	amount: '',
        },
				ruleobj: {
					file: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
				  amount: [{
						required: true,
						message: this.$t('channelBill.inputAmount'),
						trigger: 'change',
					},{
							validator: validateNum,
							// message: "最高支持8位整数和2位小数的正数",
					},],
				},
        rules: {
          amount: [{
          	required: true,
          	message: this.$t('channelBill.inputAmount'),
          	trigger: 'change',
          },{
          		validator: validateNum,
          		// message: "最高支持8位整数和2位小数的正数",
          },],
				},
				corpId: '',
				proofDetail:{},
				payModal: false,
				cardPayFromParent: false, // 根据实际情况设置这个值
				payDetail: {},
				payStatus: false,
				paySuccessInfo: {},
				payCont: ""
      }
		},
		mounted() {
      this.cooperationMode = sessionStorage.getItem('cooperationMode')
      if (this.cooperationMode == '1' || this.cooperationMode == '2') {
        this.goPageFirst(1)
        this.goPageInfo()
      }
		},
		methods: {
      goPageInfo() {
        goPageInfo({
          corpId: sessionStorage.getItem('corpId'),
          cooperationMode: this.cooperationMode,
        }).then(res => {
        	if (res.code == '0000') {
            let currencyCode = res.data.currencyCode
            this.formmodel.currencyCode =
              currencyCode == '156' ? this.$t('support.CNY') :
              currencyCode == '344' ? this.$t('support.HKD') :
              currencyCode == '840' ? this.$t('support.USD') :
              ''
            this.formmodel.type = res.data.channelType == '1' ? this.$t('offlinePay.deposit') : this.$t('offlinePay.Prepayment')
        	}
        }).catch((err) => {
        	console.error(err)
        }).finally(() => {
        	this.loading = false
        })
      },
			goPageFirst(page) {
				this.loading = true
				var _this = this
				let pageNum = page
				let pageSize = 10
				getRechargeRecord({
          corpId: sessionStorage.getItem('corpId'),
          cooperationMode: this.cooperationMode,
					pageNum,
					pageSize,
          isDisplay: 1,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.page = page
						this.total = Number(res.count)
						this.data = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
      addInvoice(row, type) {
        this.invoiceType = type
        this.invoiceRowData = row
        this.formmodel.amount = type == '2' ? row.chargeAmount.toString() : ''
        this.invoiceId = row ? row.id : ''
        this.addmodel = true
      },
      //申请发票、重新申请发票
      addBesure(){
      	this.$refs.formmodel.validate(valid => {
      		if (valid) {
            this.addLoading = true
      			requestInvoice({
              corpId: sessionStorage.getItem('corpId'),
              amount: this.formmodel.amount,
              cooperationMode: this.cooperationMode,
              repeatedApply: this.invoiceType == '2' ? true : false,
              id: this.invoiceType == '2' ? this.invoiceId : undefined,
            }).then(res => {
      				if (res && res.code == '0000') {
      					this.$Notice.success({
      						title: this.$t('address.Operationreminder'),
      						desc: this.$t('common.Successful'),
      					})
      					this.goPageFirst(1)
      					this.currentPage=1
      					this.cancelModal()
      					this.addLoading = false
      					this.addmodel = false
      				} else {
      					throw res
      				}
      			}).catch((err) => {
      				console.log(err)
      			}).finally(() => {
      				this.addLoading = false
      			})
      		}
      	})
      },
			//撤销
      revokeItem(row) {
        let chargeId = row.billId
      	this.$Modal.confirm({
      		title: this.$t('support.confirmRevocation'),
      		onOk: () => {
      			revoke(chargeId).then((res) => {
      				if (res.code === "0000") {
      					this.$Notice.success({
      						title: this.$t('address.Operationreminder'),
      						desc: this.$t('common.Successful'),
      					});
      				}
      				this.goPageFirst(1);
      			});
      		},
      	});
      },
      pay(row, type) {
        this.rowType = type
        this.payRowData = row
        this.formobj.amount = row.chargeAmount.toString()
        this.PaymentModal = true
      },
      // 缴付、重新上传付款证明
      pictureSubmit() {
      	this.$refs["formobj"].validate((valid) => {
      		if (valid) {
      			var formData = new FormData();
      			formData.append('amount', this.formobj.amount);
      			formData.append('corpId', this.payRowData.corpId);
      			formData.append('accountId', this.payRowData.id);
      			formData.append('paymentProofs', this.file); //封面
      			formData.append('repeatedUpload', this.rowType == '2' ? true : false); //封面
      			formData.append('chargeType', '2'); //押金充值

            this.pictureLoading = true
      			submitPaymentProof(formData).then((res) => {
      				if (res.code === "0000") {
      					this.$Notice.success({
      						title: this.$t('address.Operationreminder'),
      						desc: this.$t('common.Successful'),
      					});
      					this.pictureLoading = false
      					this.PaymentModal = false;
      					this.goPageFirst(1)
      					this.file = ''
      					this.$refs["formobj"].resetFields();
      				} else {
      					throw res
      				}
      			}).catch((err) => {
      				this.pictureLoading = false
      			}).finally(() => {

      			})
      		}
      	});
      },
      // invoice下载
      downloadInvoice(row) {
        exportInvoice({
          fileAddress: row.invoiceAddress,
        }).then(res => {
        	const content = res.data
        	let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
        	if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
        		const link = this.$refs.downloadLink // 创建a标签
        		let url = URL.createObjectURL(content)
        		link.download = fileName
        		link.href = url
        		link.click() // 执行下载
        		URL.revokeObjectURL(url) // 释放url
        	} else { // 其他浏览器
        		navigator.msSaveBlob(content,fileName)
        	}
        }).catch((err) => {
        	console.log(err)
        })
      },
      // 下载付款证明
      downloadPaymentProof(row) {
        downloadPaymentProof({
          id: row.id,
        }).then(res => {
        	const content = res.data
        	let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
        	if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
        		const link = this.$refs.downloadLink // 创建a标签
        		let url = URL.createObjectURL(content)
        		link.download = fileName
        		link.href = url
        		link.click() // 执行下载
        		URL.revokeObjectURL(url) // 释放url
        	} else { // 其他浏览器
        		navigator.msSaveBlob(content,fileName)
        	}
        }).catch()
      },
      exportcancelModal(){
      	this.exportModalr = false
      	this.file = ''
      },
      Gotor(){
      	this.$router.push({
      		path: '/taskList',
      		query: {
      			taskId: encodeURIComponent(this.taskId),
      			fileName: encodeURIComponent(this.taskName),
      		}
      	})
      	this.exportcancelModal()
      	this.exportModalr = false
      },
      // 上传文件
      handleBeforeUpload(file, fileList) {
      	const sizeLimit = file.size / 1024 / 1024 > 10
      	if (sizeLimit) {
      		this.$Notice.warning({
      			title: this.$t('address.Operationreminder'),
      			desc: this.$t('support.pictureSize')
      		});
      		return false;
      	}
      	this.file = file,
      	this.uploadList = fileList
      	return false;
      },
      fileUploading(event, file, fileList) {
      	this.message = this.$t('support.fileUploadedAndProgressDisappears')
      },
      removeFile() {
      	this.file = ''
      },
			cancelModal(){
				this.addmodel = false
				this.$refs.formmodel.resetFields()
        this.PaymentModal = false
        this.file = ''
        this.$refs['formobj'].resetFields()
			},
      // 获取供应商
      getSupplierList() {
      	getSupplierList({}).then(res => {
      		if (res.code === '0000') {
            this.supplierList = res.data
      		}
      	}).catch((err) => {
      		console.log(err)
      	})
      },
			onlineRecharge(row){
				console.log(row)
				this.proofDetail  =row;
				this.payModal = true
			},
			onlinePay (details) {
			if (this.payStatus) {
				console.log("请求中...")
				return;
			}
			this.payStatus = true

			console.log("支付信息：", details)
			//封装需要传的参数
			let createAndPayData = {
				paymentMethod: details.paymentMethod,
				orderType: "deposit",
				corpId: sessionStorage.getItem("corpId"),
				billId: this.proofDetail.id,
				adyenStateData: details.adyenStateData,
				language: localStorage.getItem("local"), // 根据需要设置语言
			}
			createOrderAndPay(createAndPayData).then(res => {
				console.log(res.data, "订购详情")
				if (res.code == '0000') {
					this.payStatus = false
					//通知子组件
					this.paySuccessInfo = res.data
					this.payModal = false
					this.payCont = res.data.payUrl
					console.log(res.data.redirectUrl);
					// if (res.data.redirectUrl) {
					// window.location.href = res.data.redirectUrl
					// } else {
						document.querySelector("body").innerHTML = res.data.payUrl
						document.forms[0].submit();
					// }


				}
			}).catch(error => {
				console.error("请求失败:", error);
				// this.payModal = false
				this.payStatus = false
			});
		},
		}
	}
</script>

<style>
</style>

