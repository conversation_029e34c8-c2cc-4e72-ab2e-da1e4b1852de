<template>
	<!-- 流水详情 -->
	<Card>
		<div style="width: 100%;margin-top: 50px; margin: auto;">
			<div style="display: flex;justify-content: flex-start;align-items: flex-start">
				<Form ref="form" :model="form"  :rules="billRule" :label-width="120">
					<FormItem :label="$t('flow.Choosedate')" prop="date">
						<DatePicker type="daterange" format="yyyy-MM-dd" placement="bottom-start" v-model="form.date" style="width: 200px" :clearable="true"
						:placeholder="$t('flow.PleaseChoosedate')" @on-change="handleDateChange" @on-clear="hanldeDateClear" class="recordBtnSty"></DatePicker>
					</FormItem>
				</Form>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button  type="primary"  @click="searchBill()"  v-has="'search'" :loading="searchloading" :disabled="cooperationMode == '2' || cooperationMode == '3'">
					<div style="display: flex; align-items: center">
						<Icon type="md-search" />&nbsp;{{ $t('common.search') }}
					</div>
				</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button  type="success"  @click="exportBillFlow()" v-has="'exportBillList'"   :loading="billExportLoading"  :disabled="cooperationMode == '2' || cooperationMode == '3'">
					<div style="display: flex; align-items: center">
						<Icon type="ios-cloud-download-outline" />&nbsp;{{ $t('stock.exporttb') }}
					</div>
				</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button @click="back">
					<div style="display: flex; align-items: center">
						<Icon type="ios-arrow-back" />&nbsp;{{ $t('support.back') }}
					</div>
				</Button>
			</div>

			<!-- 表格 -->
			<Table :columns="columns12" :data="data" style="width:100%;margin-top: 50px;" :loading="loading" >
				<template slot-scope="{ row }" slot="createTime">
					<strong>{{ row.createTime }}</strong>
				</template>
				<template slot-scope="{ row }" slot="type">
					<strong>{{ row.type }}</strong>
				</template>
				<template slot-scope="{ row }" slot="currencyCode">
					<strong>{{ row.currencyCode }}</strong>
				</template>
				<template slot-scope="{ row }" slot="amount">
					<strong>{{ row.amount }}</strong>
				</template>
				<template slot-scope="{ row }" slot="tdeposit">
					<strong>{{ row.tdeposit }}</strong>
				</template>

			</Table>
			<!-- 分页 -->
			<div style="margin-top: 100px;">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>

		</div>
		<a ref="downloadLink" style="display: none"></a>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">{{ $t('exportMS') }}</h1>
					<FormItem :label="$t('exportID')">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem :label="$t('exportFlie')">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">{{ $t('downloadResult') }}</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">{{ $t('cancel') }}</Button>
				<Button type="primary" @click="Goto">{{ $t('Goto') }}</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		streamList,
		searchcorpid,
		billflowData
	} from '@/api/channel.js'
	const math = require('mathjs')
	export default {
		data() {
			return {
				cooperationMode: '', //合作模式
				chargetime: '',
				time: '',
				createTime: '',
				type: '',
				total: 0,
				page: 0,
				currentPage: 1,
				corpId: '',
				loading: false,
				searchloading: false,
				billExportLoading: false,
				exportModal: false,
				form: {
					startTime:"",
					endTime:"",
					date:[]
				},
				columns12: [{
						title: this.$t("deposit.charge_time"),
						slot: 'createTime',
						minWidth: 300,
						align: 'center'
					},
					{
						title: this.$t("deposit.charge_type"),
						slot: 'type',
						align: 'center',
						minWidth: 300,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const text =
								row.type == '1' ?
								this.$t("support.payBills") :
								row.type == '2' ?
								this.$t("support.increaseDeposit") :
								row.type == '3' ?
								this.$t("support.PreDeposit") :
								row.type == '4' ?
								this.$t("support.remunerationReturn") :
								row.type == '5' ?
								this.$t("support.packageOrder") :
								row.type == "6" ?
								this.$t("support.fuelPackpackageOrder") :
								row.type == '7' ?
								this.$t("support.packageCancellation") :
								row.type == '8' ?
								this.$t("support.fuelPackUnsubscribe") :
								row.type == '9' ?
                this.$t("support.channelIncomeAdjustment") :
                row.type == '10' ?
                this.$t("support.marketingRebate") :
                row.type == '11' ?
                this.$t("support.imsiFeeStatistics") :
                row.type == '12' ?
                this.$t("support.indemnity") :
								'';
							return h('label', text);
						}
					},
					{
						title: this.$t("deposit.currency"),
						slot: 'currencyCode',
						align: 'center',
						minWidth: 300,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const text = row.currencyCode == '156' ? this.$t("support.CNY") : row.currencyCode ==
								'840' ? this.$t("support.USD") : row.currencyCode == '344' ? this.$t(
								"support.HKD") : '';
							return h('label', text);
						}
					},
					{
						title: this.$t("deposit.Amount"),
						// title: "交易金额",
						slot: 'amount',
						minWidth: 300,
						align: 'center',
					},
					{
						title: this.$t("deposit.accountdeposit"),
						slot: 'tdeposit',
						minWidth: 300,
						align: 'center',
					},

				],
				data: [],
				rules: {},
				taskId: '',
				taskName: '',
				billRule: {
					date: [
					{ type: 'array',required: true, message: this.$t("stock.chose_time"), trigger: 'blur',
					  fields: {
							0: {type: 'date', required: true, message: this.$t("stock.chose_time")},
							1: {type: 'date', required: true, message: this.$t("stock.chose_time")}
						}
					}
					],
				},
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
		},
		methods: {
			goPageFirst(page) {
				this.loading = true
				this.searchloading = true
				var _this = this
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						this.corpId = res.data
						streamList({
							startTime: this.form.startTime === "" ? null : this.form.startTime,
							endTime: this.form.endTime === "" ? null : this.form.endTime,
							corpId:this.corpId,
							pageNum: page,
							pageSize: 10,
							cooperationMode: this.cooperationMode
						}).then(res => {
							if (res.code == '0000') {
								_this.loading = false
								this.searchloading = false
								this.page = page
								this.currentPage = page
								this.total = res.data.totalCount
								this.data = res.data.records
							}
						}).catch((err) => {
							console.error(err)
							this.searchloading = false
						}).finally(() => {
							this.loading = false
							this.searchloading = false
						})
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {})
			},
			// 账单流水 搜索
			searchBill: function() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.goPageFirst(1);
					}
				});
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			handleDateChange(date) {
				if (Array.isArray(date)) {
					this.form.startTime = date[0];
					this.form.endTime = date[1];
				}

			},
			hanldeDateClear() {
				this.form.startTime = ''
				this.form.endTime = ''
			},
			back: function() {
				this.$router.push({
					path: '/deposit',
				})
			},
			cancelModal() {
				this.exportModal = false
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
						corpId: encodeURIComponent(this.corpId),
					}
				})
				this.exportModal = false
			},
			// 账单流水 导出
			exportBillFlow: function(){
				var _this = this
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.billExportLoading = true
						searchcorpid({
							userName: this.$store.state.user.userName,
						}).then(res => {
							if (res.code == '0000') {
								this.corpId = res.data
								billflowData({
									startTime: this.form.startTime === "" ? null : this.form.startTime,
									endTime: this.form.endTime === "" ? null : this.form.endTime,
									corpId:this.corpId,
									userId: this.corpId,
									pageNum: -1,
									pageSize: -1,
									cooperationMode: this.cooperationMode
								}).then((res) => {
									this.exportModal = true
									this.taskId = res.data.taskId
									this.taskName = res.data.taskName
									this.billExportLoading = false
								}).catch((err) => {
								console.error(err)
								this.billExportLoading = false
								}).finally(() => {})
							}
						}).catch((err) => {
								console.error(err)
						}).finally(() => {})
					}
				})
			},
			}

		}

</script>

<style>
</style>
