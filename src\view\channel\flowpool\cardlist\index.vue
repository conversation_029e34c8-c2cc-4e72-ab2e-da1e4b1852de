<template>
	<!-- 卡号列表 -->
	<Card>
		<div>
			<span style="margin-top: 4px;font-weight:bold;">{{$t('flow.Channel')}}:</span>&nbsp;&nbsp;
			<span>{{corpName}}</span>
		</div>
		<div class="search-container">
			<div class="search-item">
				<span class="label">ICCID:</span>
				<Input v-model="form.iccid" :placeholder="$t('flow.inputICCID')" clearable></Input>
			</div>
			<div class="search-item">
				<span class="label">{{$t('flow.Status')}}:</span>
				<Select filterable v-model="form.type" clearable :placeholder="$t('flow.chooseStatus')">
					<Option :value="2">{{$t('flow.toassigned')}}</Option>
					<Option :value="1">{{$t('flow.Assigned')}}</Option>
				</Select>
			</div>
			<div class="search-item button-item">
				<Button v-has="'search'" :disabled="!['1', '2'].includes(cooperationMode)" type="primary" icon="md-search" :loading="searchloading" @click="search()">{{$t('order.search')}}</Button>
			</div>
			<div class="search-item button-item">
				<Button v-has="'export'" :disabled="!['1', '2'].includes(cooperationMode)" type="success" icon="ios-cloud-download-outline" :loading="downloading" @click="exportFile">{{$t('stock.exporttb')}}</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'delete'" type="error" ghost v-if="row.status==='1'" @click="deleteNumber(row)" >
				  {{$t('flow.deleteNumber')}}
				</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div  style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 导出提示 -->
		<Modal  v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
		  <div style="align-items: center;justify-content:center;display: flex;">
			  <Form  label-position="left"  :label-width="150" style=" align-items: center;justify-content:center;" >
			   		  <h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
					  <FormItem :label="$t('exportID')">
						<span style="width: 100px;">{{taskId}}</span>
			   		  </FormItem>
			   		  <FormItem :label="$t('exportFlie')">
						<span>{{taskName}}</span>
			   		  </FormItem>
					  <span style="text-align: left;">{{$t('downloadResult')}}</span>
			   </Form>
		  </div>

		  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
		    <Button @click="cancelModal">{{$t('common.cancel')}}</Button>
		    <Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
		  </div>
		</Modal>
	</Card>
</template>

<script>
	import {
		channelCardlist,
		exportCardlist,
		getStoreByCorpId,
		// Deleteiccid,
		DeleteCard
	} from "@/api/flowpool/flowpool";
	import {
		searchcorpid
	} from '@/api/channel'
	export default{
		data(){
			return{
				cooperationMode: '', //合作模式
				corpId:'',
				corpName:'',
				form:{
					iccid:'',
					type:''
				},
				total: 0,
				currentPage: 1,
				page: 0,
				taskId:'',
				taskName:'',
				exportModal:false,//导出弹窗标识
				columns:[{
					title: "ICCID",
					key: 'iccid',
					minWidth: 120,
					align: 'center'
				},
				{
					title: this.$t('flow.Status'),
					key: 'status',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  const text = row.status==='1' ? this.$t('flow.Assigned'):row.status==='2' ?  this.$t('flow.toassigned'):"";
					  return h('label', text);
					}
				},
				{
					title: this.$t('flow.typelimit')+'(MB)',
					key: 'dailyTotal',
					minWidth: 120,
					align: 'center'
				},
				{
					title: this.$t('flow.Totallimits')+'(MB)',
					key: 'total',
					minWidth: 120,
					align: 'center'
				},
				{
					title: this.$t('flow.Controllogic'),
					key: 'rateType',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  const text = row.rateType==='1' ? this.$t('flow.Continuelimit'):row.rateType==='2' ? this.$t('flow.speedlimit')
					  :row.rateType==='3' ? this.$t('flow.Stoplimit') :"";
					  return h('label', text);
					}
				},{
					title: this.$t('flow.Originlimit'),
					key: 'flowPoolName',
					minWidth: 120,
					align: 'center',
					tooltip: true
				},{
					title: this.$t('support.action'),
					slot: 'action',
					minWidth: 120,
					align: 'center',
				}
				],
				data:[{

				}],
				loading:false,
				searchloading:false,
				downloading:false,
				rule:{

				}
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			if (['1', '2'].includes(this.cooperationMode)) {
				this.goPageFirst(1)
			}
		},
		methods:{
			goPageFirst:function(page){
				this.loading = true
				this.corpId = sessionStorage.getItem("corpId")
				//获取渠道商名称
				this.getcorpName(this.corpId)
				channelCardlist({
					pageSize:10,
					pageNum:page,
					ICCID:this.form.iccid,
					status:this.form.type,
					corpId:this.corpId,
					cooperationMode: this.cooperationMode
				}).then(res => {
					if (res.code == '0000') {
						this.page = page
						this.currentPage = page
						this.total = res.count
						this.data = res.data
						this.loading = false
						this.searchloading = false
						//封装数据
						res.data.map((row,index) => {
							var text=this.$i18n.locale==='zh-CN' ? row.flowPoolName:this.$i18n.locale==='en-US' ? row.nameEn: ''
							this.data[index].flowPoolName=text
						})
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchloading = false
				})
			},
			goPage:function(page){
				this.goPageFirst(page)
			},
			search:function(){
				this.searchloading=true
				this.goPageFirst(1)
			},
			exportFile:function(){
				this.downloading = true
				exportCardlist({
					pageSize:-1,
					pageNum:-1,
					ICCID:this.form.iccid,
					Status:this.form.type,
					corpId:this.corpId,
					exportType :2,
                    userId:this.corpId
				}).then((res) => {
				  this.exportModal=true
				  this.taskId=res.data.taskId
				  this.taskName=res.data.taskName
				  this.downloading = false
				}).catch(() => this.downloading = false)
			},
			deleteNumber:function(row){
				this.$Modal.confirm({
					title: this.$t("address.deleteitem"),
					onOk: () => {
						this.iccids = []
						this.iccids.push(row.iccid)
						DeleteCard({
							corpId:this.corpId,
							flowPoolId:row.flowPoolID,
							iccids:this.iccids
						}).then(res => {
							if (res && res.code == '0000') {
								this.goPageFirst(1);
								this.iccids=[]
								this.$Notice.success({
									title: this.$t('common.Successful'),
									desc: this.$t('common.Successful')
								})
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						})
					},
				})
			},
			cancelModal(){
				this.exportModal=false
			},
			Goto(){
				this.$router.push({
				  path: '/taskList',
				  query: {
					taskId: encodeURIComponent(this.taskId),
					fileName:encodeURIComponent(this.taskName),
					corpId:encodeURIComponent(this.corpId)
				  }
				})
				this.exportModal=false
			},
			//获取渠道商名称
			getcorpName(corpId){
				getStoreByCorpId(corpId).then(res => {
					if (res.code == '0000') {
						this.corpName = res.data.corpName
					}
				}).catch((err) => {
					console.error(err)
				})
			}
		}

	}
</script>

<style lang="less" scoped>
.search-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 20px;
  align-items: center;

  .search-item {
    display: flex;
    align-items: center;

    .label {
      font-weight: bold;
      white-space: nowrap;
      margin-right: 8px;
    }

    .ivu-input-wrapper {
      width: 200px;
    }

    .ivu-select {
      width: 220px;
    }

    &.button-item {
      min-width: auto;
      flex-shrink: 0;
    }
  }
}
</style>
