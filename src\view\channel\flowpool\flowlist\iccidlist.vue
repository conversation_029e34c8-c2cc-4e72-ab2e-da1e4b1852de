<template>
	<!-- iccid列表 -->
	<Card>
		<div>
			<span style="margin-top: 4px;font-weight:bold;">ICCID:</span>&nbsp;&nbsp;
			<Input v-model="iccid" :placeholder="$t('flow.inputICCID')" clearable style="width: 200px"></Input>&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">{{ $t('flow.remark') + ':' }}</span>&nbsp;&nbsp;
			<Input v-model="cardRemark" :placeholder="$t('flow.inputRemark')" clearable style="width: 300px"></Input>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">{{ $t('flow.internetStatus') + ':' }}</span>&nbsp;&nbsp;
			<Select filterable v-model="currentRateType" :placeholder="$t('flow.inputinternetStatus')" style="width: 200px" clearable>
				<Option value="1">{{ $t('order.Normal') }}</Option>
				<Option value="2">{{ $t('flow.Cardcycle') }}</Option>
				<Option value="3">{{ $t('flow.Stopdatalimit') }}</Option>
				<Option value="4">{{ $t('flow.Restrictedspeed') }}</Option>
				<Option value="5">{{ $t('flow.Totallimitcard') }}</Option>
				<Option value="6">{{ $t('flow.Datapoollimit') }}</Option>
				<Option value="7">{{ $t('flow.stoppoollimit') }}</Option>
			</Select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">{{ $t('support.cardstate') + ':' }}</span>&nbsp;&nbsp;
			<Select filterable v-model="flowPoolStatus" :placeholder="$t('support.chose_state')" style="width: 200px" clearable>
				<Option value='1'>{{ $t('order.Normal') }}</Option>
				<Option value='2'>{{ $t('support.pause') }}</Option>
			</Select>&nbsp;&nbsp;
			<div style="display: flex;justify-content: flex-start;align-items: center;margin-top: 30px;margin-left: 20px;">
				<Button v-has="'search'" :disabled="!['1', '2'].includes(cooperationMode)" type="primary" icon="md-search" :loading="searchloading" @click="search()">{{$t('common.search')}}</Button>&nbsp;&nbsp;&nbsp;&nbsp;
				<Button v-has="'export'" :disabled="!['1', '2'].includes(cooperationMode)" style="margin: 0 2px;margin-left: 20px;" icon="ios-cloud-download-outline" type="success"
				:loading="downloading" @click="exportFile">
					{{$t('stock.exporttb')}}
				</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button v-has="'import'" :disabled="!['1', '2'].includes(cooperationMode)" type="warning" icon="md-add" @click="importIccid()">{{$t('flow.ImportICCID')}}</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button v-has="'batchDelete'" :disabled="!['1', '2'].includes(cooperationMode)" type="error" @click="deleteBatch()">{{$t('flow.Batchdelete')}}</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button v-has="'batchUpdate'" :disabled="!['1', '2'].includes(cooperationMode)" type="primary" icon="md-add" @click="updateBatch()">{{$t('flow.Batchupdate')}}</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button style="margin: 0 4px" @click="back">
					<Icon type="ios-arrow-back" />&nbsp;{{$t('support.back')}}
				</Button>

			</div>

			<!-- 表格 -->
			<Table :columns="columns" :data="data" @on-selection-change="handleRowChange"
			 @on-select-cancel="cancelSigle" @on-select-all-cancel="cancelAll" style="width:100%;margin-top: 40px;"
			 :loading="loading" @on-sort-change="changeSort">
				<template slot-scope="{ row, index }" slot="action">
					<Button v-has="'delete'" type="error" style="margin-right: 10px;" @click="deleteItem(row)">{{$t('common.del')}}</Button>
					<Button v-has="'stop'" type="warning" style="margin-right: 10px;"  v-if="row.flowPoolStatus === '2'" disabled @click="stop(row)">{{ $t('support.pause') }}</Button>
					<Button v-has="'stop'" type="warning" style="margin-right: 10px;" v-else @click="stop(row)">{{ $t('support.pause') }}</Button>
					<Button v-has="'recover'" type="success" style="margin-right: 10px;" v-if="row.flowPoolStatus === '1'" disabled @click="active(row)">{{ $t('flow.recover') }}</Button>
					<Button v-has="'recover'" type="success" style="margin-right: 10px;"v-else @click="active(row)">{{ $t('flow.recover') }}</Button>
					<Button v-has="'cardManagement'" type="primary" style="margin-right: 10px;" @click="cardItem(row)">{{ $t('flow.cardManager') }}</Button>


				</template>
			</Table>
			<!-- 分页 -->
			<div style="margin-top:15px">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
			<!-- 导入模态框 -->
			<Modal :title="$t('flow.ImportICCID')" v-model="importModal" :mask-closable="true" @on-cancel="cancelModal" width="1200px">
				<Tabs>
					<TabPane v-has="'single_import'" :label="$t('flow.Singleimport')" icon="ios-cloud-upload" >
						<div style=" display: flex;border-bottom: solid 1px #CCCCCC;">
							<Form ref="form" :model="form" :rules="rule">
								<FormItem :label="$t('flow.Choosepool')">
									<!-- <Select filterable v-model="form.flowpoolid" :clearable="true" :placeholder="$t('flow.plesepool')" style="width: 300px ;margin-right: 10px;">
										<Option v-for="item in poolList" :value="item.flowPoolId" :key="item.flowPoolId">{{  $i18n.locale==='zh-CN' ? item.flowPoolName:$i18n.locale==='en-US' ? item.nameEn: ''}}</Option>
									</Select> -->
									<span style="font-weight: bold;">{{form.flowPoolName}}</span>
								</FormItem>
								<FormItem label="ICCID" prop="iccid">
									<Input v-model="form.iccid" :placeholder="$t('flow.inputICCID')" clearable style="width: 300px"></Input>
								</FormItem>
								<FormItem :label="$t('flow.Monocyletype')" prop="singlecycle">
									<Input v-model="form.singlecycle" :placeholder="$t('flow.units')+'MB'" clearable style="width: 300px"></Input>
								</FormItem>
								<FormItem :label="$t('flow.Totallimit')" prop="totalcap">
									<Input v-model="form.totalcap" :placeholder="$t('flow.units')+'MB'" clearable style="width: 300px"></Input>
								</FormItem>
								<FormItem :label="$t('flow.Controllogic')" prop="controllogic">
									<Select filterable v-model="form.controllogic" :placeholder="$t('flow.choosecontrollogic')" style="width: 300px"
									 clearable>
										<Option value="1">{{$t('flow.Continuelimit')}}</Option>
										<Option value="2">{{$t('flow.speedlimit')}}</Option>
										<Option value="3">{{$t('flow.Stoplimit')}}</Option>
									</Select>
								</FormItem>
								<FormItem :label="$t('flow.poolAvailableTime')" prop="availableTime">
									<Input v-model="form.availableTime" :placeholder="$t('flow.fillNumber')" clearable style="width: 300px"></Input>
								</FormItem>
							</Form>
						</div>

						<div style="width: 100%; display: flex;align-items: center;justify-content:center;margin-top: 50px;">
							<Button @click="cancelModal">{{$t('support.back')}}</Button>&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="primary" :loading="importLoading" @click="confirmone">{{$t('common.determine')}}</Button>
						</div>

					</TabPane>
					<TabPane v-has="'batch_import'" :label="$t('flow.Batchimport')" icon="md-redo">
						<Form ref="formobj" :model="formobj" :rules="ruleobj">
							<FormItem :label="$t('flow.Choosepool')" >
								<!-- <Select filterable v-model="formobj.flowpoolid" :clearable="true" :placeholder="$t('flow.plesepool')"
								 style="width: 300px ;margin-right: 10px;">
									<Option v-for="item in poolList" :value="item.flowPoolId" :key="item.flowPoolId">{{  $i18n.locale==='zh-CN' ? item.flowPoolName:$i18n.locale==='en-US' ? item.nameEn: ''}}</Option>
								</Select> -->
								<span style="font-weight: bold;">{{form.flowPoolName}}</span>
							</FormItem>
							<FormItem :label="$t('flow.UploadICCID')" prop="file">
								<Upload type="drag" v-model="formobj.file" :action="uploadUrl" :on-success="fileSuccess" :on-error="handleError"
								 :before-upload="handleBeforeUpload" :on-progress="fileUploading" style="width: 500px; margin-top: 50px;margin-left: 50px;">
									<div style="padding: 20px 0">
										<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
										<p>{{$t('buymeal.upload')}}</p>
									</div>
								</Upload>
								<div style="width: 500px;margin-left: 50px;">
									<Button type="primary" :loading="downloading" icon="ios-download" @click="downloadFile">{{$t('buymeal.Download')}}</Button>
									<!-- <Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert> -->
								</div>
								<ul class="ivu-upload-list" v-if="file" style="width: 500px; margin-left: 50px;">
									<li class="ivu-upload-list-file ivu-upload-list-file-finish">
										<span>
											<Icon type="ios-folder" />{{file.name}}</span>
										<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
									</li>
								</ul>
								<div style="width: 100%; display: flex;margin-left: 50px;margin-top: 100px;">
									<Button @click="cancelModal">{{$t('support.back')}}</Button>&nbsp;&nbsp;&nbsp;&nbsp;
									<Button type="primary" :loading="importLoading" @click="confirmbatch">{{$t('common.determine')}}</Button>
								</div>

							</FormItem>

							<h1>{{$t("buymeal.taskview")}}</h1>
							<Table :columns="columnsTask" :data="taskdata" style="width:100%;margin-top: 40px;" :loading="taskloading">
								<template slot-scope="{ row, index }" slot="success">
									<Button v-if="row.successCount>0" type="success" style="margin-right: 10px;" @click="exportfiles(row,1)">{{$t('DownloadFlie')}}</Button>
								</template>
								<template slot-scope="{ row, index }" slot="fail">
									<Button  v-if="row.failCount>0" type="error" style="margin-right: 10px;" @click="exportfiles(row,2)">{{$t('DownloadFlie')}}</Button>
								</template>
							</Table>
							<div style="margin-top:15px">
								<Page :total="Tasktotal" :current.sync="TaskcurrentPage" show-total show-elevator @on-change="TaskgoPage" />
							</div>
						</Form>
					</TabPane>
				</Tabs>
				<div slot="footer">
				</div>
			</Modal>
			<a ref="downloadLink" style="display: none"></a>
			<!-- 模板文件table -->
			<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
			<!-- 导出提示 -->
			<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
				<div style="align-items: center;justify-content:center;display: flex;">
					<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
						<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
						<FormItem :label="$t('exportID')">
							<span style="width: 100px;">{{taskId}}</span>
						</FormItem>
						<FormItem :label="$t('exportFlie')">
							<span>{{taskName}}</span>
						</FormItem>
						<span style="text-align: left;">{{$t('downloadResult')}}</span>
					</Form>
				</div>

				<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
					<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
					<Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
				</div>
			</Modal>
			<!-- 单卡管理弹窗 -->
			<Modal :title="title" v-model="cardModal" :mask-closable="true" @on-cancel="cancelModal"  width="800px">
				<Form :model="form"  :rules="rule" ref="carform" :label-width="180" style="font-size: 600;">
					<div v-show="typeflag">
						<FormItem label="ICCID:" >
							<span>{{info.iccid}}</span>
						</FormItem>
					</div>
					<FormItem :label="$t('flow.Monocyletype')" prop="dailyTotal">
						<Input v-model="form.dailyTotal" :placeholder="$t('flow.mb')" clearable style="width: 500px"></Input>
					</FormItem>
					<FormItem :label="$t('flow.totallimit')" prop="total">
						<Input v-model="form.total" :placeholder="$t('flow.mb')" clearable style="width: 500px"></Input>
					</FormItem>
					<FormItem :label="$t('flow.poolAvailableTime')" prop="availableTime">
						<Input v-model="form.availableTime" :placeholder="$t('flow.fillNumber')" clearable style="width: 500px"></Input>
					</FormItem>
					<FormItem :label="$t('flow.remark')" prop="cardRemark">
						<Input v-model="form.cardRemark" :placeholder="$t('flow.enterRemark')" type="textarea" :rows="4"  maxlength="200" clearable style="width: 500px"></Input>
					</FormItem>
				</Form>
				<div style="margin: 20px;font-weight: bold;" v-show="piflag">
					<div>ICCID:</div>
				 <ul>
				 	<li id="space" v-for="(item,i) in items" :key="items.i" >
				 		{{item}}
				 	</li>
				 </ul>
				 <div style="margin: 20px;" v-if="remind">
				   <span>……</span>
				 </div>
				</div>
				<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				  <Button @click="cancelModal">{{$t('common.cancel')}}</Button>
				  <Button type="primary" @click="besure" :loading="rechargeloading">{{$t('common.determine')}}</Button>
				</div>
			</Modal>
		</div>
	</Card>
</template>

<script>
	import {
		channelIccidlist,
		exporticcid,
		Singleimport,
		Batchimport,
		Deleteiccid,
		getTaskList,
		exportTaskList,
		channelflowlist,
		cardUpadate,
		stopStatusVCard,
		recoverStatusVCard
	} from "@/api/flowpool/flowpool";
	import {
		searchcorpid
	} from '@/api/channel'
	import {
		getPage,
	} from "@/api/package/flowpool";
	export default {
		data() {
			// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (this.uploadList && this.uploadList.length === 0) {
					callback(new Error(this.$t('buymeal.toupload')))
				} else {
					callback()
				}
			}
			return {
				typeflag: false,
				piflag: false,
				remind:false,
				cooperationMode: '',
				title:'',
				sequence:'',//排序顺序
				sortField:'',//排序字段
				chooseiccid:'',
				corpId: '',
				iccid: '',
				cardRemark: '',//备注
				currentRateType: '', //上网状态
				flowPoolStatus: '',
				taskId: '',
				taskName: '',
				total: 0,
				currentPage: 1,
				page: 0,
				Tasktotal: 0,
				TaskcurrentPage: 1,
				Taskpage: 0,
				uploadUrl: '',
				uploadList: [],
				selectionList: [], //翻页勾选List
				iccids:[],//删除标识id
				poolList: [],
				selection: [], //多选
				selectionIds: [], //多选ids
				message: this.$t("buymeal.Downloadmsg"),
				loading: false,
				searchloading: false,
				downloading: false,
				importLoading: false,
				taskloading:false,
				rechargeloading: false,
				importModal: false, //导入弹框标识
				exportModal: false, //导出弹框标识
				cardModal: false,
				typeflag: false,
				modelData: [{
					'ICCID': '********',
					'ControlLogic[1：Continue After Data Limit 2：Restricted Speed After Data Limit 3：Stop After Data Limit]': '********',
					'Monocyle Type Limit(MB)': '********',
					'Totallimit(MB)': '********',
					'availableTime': '********',
				}, ],
				modelColumns: [{
						title: 'ICCID',
						key: 'ICCID'
					}, // 列名根据需要添加
					{
						title: 'ControlLogic[1：Continue After Data Limit 2：Restricted Speed After Data Limit 3：Stop After Data Limit]',
						key: 'ControlLogic[1：Continue After Data Limit 2：Restricted Speed After Data Limit 3：Stop After Data Limit]'
					}, // 列名根据需要添加
					{
						title: 'Monocyle Type Limit(MB)',
						key: 'Monocyle Type Limit(MB)'
					}, // 列名根据需要添加
					{
						title: 'Totallimit(MB)',
						key: 'Totallimit(MB)'
					}, // 列名根据需要添加
					{
						title: 'Available day to enter pool',
						key: 'availableTime'
					}, // 列名根据需要添加
				],
				form: {
					flowpoolid: '',
					iccid: '',
					singlecycle: '',
					totalcap: '',
					total: '',
					controllogic: '',
					flowPoolName:'',
					availableTime: '',
					dailyTotal: '',
					cardRemark: '',
				},
				formobj: {
					flowpoolid: '',
				},
				flowpoolId: '',
				file: null,
				columns: [{
						type: 'selection',
						width: 60,
						align: 'center'
					}, {
						title: "ICCID",
						key: 'iccid',
						minWidth: 180,
						align: 'center',
						tooltip: true,
						sortable: 'custom'
					},
					{
						title: this.$t('flow.Useddata') + '(MB)',
						key: 'usedFlow',
						minWidth: 140,
						align: 'center',
						sortable: 'custom'
					},
					{
						title: this.$t('flow.internetStatus'),
						key: 'currentRateType',
						minWidth: 280,
						align: 'center',
						sortable: 'custom',
						render: (h, params) => {
							const row = params.row;
							const text = row.currentRateType === '1' ? this.$t('order.Normal') :
								row.currentRateType === '2' ? this.$t('flow.Cardcycle') :
								row.currentRateType === '3' ? this.$t('flow.Stopdatalimit') :
								row.currentRateType === '4' ? this.$t('flow.Restrictedspeed') :
								row.currentRateType === '5' ? this.$t('flow.Totallimitcard'):
								row.currentRateType === '6' ? this.$t('flow.Datapoollimit') :
								row.currentRateType === '7' ? this.$t('flow.stoppoollimit') : '';
							return h('label', text);
						}
					},
					{
						title: this.$t('support.cardstate'),
						key: 'flowPoolStatus',
						minWidth: 120,
						align: 'center',
						sortable: 'custom',
						render: (h, params) => {
							const row = params.row;
							const text = row.flowPoolStatus === '1' ? this.$t('order.Normal') :
								row.flowPoolStatus === '2' ? this.$t('support.pause') : '';
							return h('label', text);
						}
					},
					{
						title: this.$t('flow.Monocyletype')+'(MB)',
						key: 'dailyTotal',
						minWidth: 200,
						align: 'center',
						sortable: 'custom'
					},
					{
						title: this.$t('flow.Totallimit')+'(MB)',
						key: 'total',
						minWidth: 160,
						align: 'center',
						sortable: 'custom'
					},
					{
						title: this.$t('flow.Controllogic'),
						key: 'rateType',
						minWidth: 140,
						align: 'center',
						sortable: 'custom',
						render: (h, params) => {
							const row = params.row;
							const text = row.rateType === '1' ?  this.$t('flow.Continuelimit')  : row.rateType === '2' ?this.$t('flow.speedlimit') :
								row.rateType === '3' ? this.$t('flow.Stoplimit') : "";
							return h('label', text);
						}

					},
					{
						title: this.$t('flow.ImportTime'),
						key: 'intoPoolTime',
						minWidth: 160,
						align: 'center',
						sortable: 'custom'
					},
					{
						title: this.$t('flow.availableday'),
						key: 'availableTime',
						minWidth: 150,
						align: 'center',
						sortable: 'custom',
						render: (h, params) => {
							const row = params.row;
							const text = row.availableTime === null ? ' \\ ' : row.availableTime ;
							return h('label', text);
						}
					},
					{
						title: this.$t('flow.expirationDate'),
						key: 'expiration',
						minWidth: 150,
						align: 'center',
					},
					{
						title: this.$t('flow.remark'),
						key: 'cardRemark',
						minWidth: 150,
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							let text = row.cardRemark === null ? this.$t('flow.none') : row.cardRemark;
							let length = text ? text.length : "";
							if (length > 8) {
								text = text.substring(0, 8) + "...";
								return h('div', [h('Tooltip', {
										props: {
											placement: 'bottom',
											transfer: true
										},
										style: {
											cursor: 'pointer',
										},
									},
									[
										text,
										h('label', {
												slot: 'content',
												style: {
													whiteSpace: 'normal',
													wordBreak: 'break-all' //超出隐藏
												},
											},
											row.cardRemark
										)
									])]);
							} else {
								text = text;
								return h('label', text);
							}
						}
					},
					{
						title: this.$t('support.action'),
						slot: 'action',
						minWidth: 420,
						align: 'center',
						fixed: 'right',
					},
				],
				data: [{
					type: '1212'
				}],

				columnsTask: [{
						title: this.$t('flow.Batchtime'),
						key: 'createTime',
						minWidth: 200,
						align: 'center'
					},
					{
						title: this.$t('flow.Importtotal'),
						key: 'importCount',
						minWidth: 120,
						align: 'center'
					},
					{
						title: this.$t('flow.Numbersuccess'),
						key: 'successCount',
						minWidth: 180,
						align: 'center'
					},
					{
						title: this.$t('flow.Numberfailure'),
						key: 'failCount',
						minWidth: 180,
						align: 'center'
					},
					{
						title: this.$t('flow.Successfile'),
						slot: 'success',
						minWidth: 80,
						align: 'center'
					},
					{
						title: this.$t('flow.Failurefile'),
						slot: 'fail',
						minWidth: 80,
						align: 'center'
					},
				],
				info:{},
				items: [],
				iccidlist:[],
				taskdata:[],
				rule: {
					flowpoolid: [{
						required: true,
						message: this.$t('flow.plesepool'),
						trigger: 'change',
					}],
					iccid: [ {
						required: true,
						message: this.$t('flow.inputICCID'),
						trigger: 'blur',
					},{
						pattern: /^[^\s]+(\s+[^\s]+)*$/,
						trigger: "blur",
						message: this.$t('flow.kongge'),
					}],
					// cardRemark: [{
					// 	required: true,
					// 	message: this.$t('flow.inputRemark'),
					// 	trigger: 'blur',
					// }],
					currentRateType: [{
						required: true,
						message: this.$t('flow.inputinternetStatus'),
						trigger: 'blur',
					}],
					singlecycle: [{
						required: true,
						message: this.$t('flow.Monocyleempty'),
						trigger: 'blur',
					},{
						validator: (rule, value, cb) => {
							var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value);
						},
						pattern: /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/,
						trigger: 'blur',
						message: this.$t('flow.Positivenumber'),
					}
					],
					dailyTotal:  [{
						// required: true,
						// message: this.$t('flow.Totalempty'),
						// trigger: 'blur',
					},{
						// validator: (rule, value, cb) => {
						// 	var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
						// 	return str.test(value);
						// },
						pattern: /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/,
						trigger: 'blur',
						message:this.$t('flow.Positivenumber'),
					}
					],
					totalcap: [{
						required: true,
						message: this.$t('flow.Totalempty'),
						trigger: 'blur',
					},{
						validator: (rule, value, cb) => {
							var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value);
						},
						pattern: /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/,
						trigger: 'blur',
						message: this.$t('flow.Positivenumber'),
					}
					],
					total: [{
						// required: true,
						// message: '请输入总上限',
						// trigger: 'blur',
					},{
						// validator: (rule, value, cb) => {
						// 	var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
						// 	return str.test(value) || "";
						// },
						pattern: /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/,
						message: this.$t('flow.Positivenumber'),
						trigger: 'blur',
					}
					],
					availableTime: [{
						// validator: (rule, value, cb) => {
						// 	var str = /^[1-9]\d*$/;
						// 	return str.test(value) || value == "";
						// },
						pattern:  /^[1-9]\d*$/,
						trigger: 'blur',
						message: this.$t('flow.Pleaseinteger'),
					}],
					controllogic: [{
						required: true,
						message: this.$t('flow.choosecontrollogic'),
						trigger: 'change',
					}],
				},
				info:{},
				ruleobj: {
					flowpoolid: [{
						required: true,
						message: this.$t('flow.plesepool'),
						trigger: 'change',
					}],
					file: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
				}
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			// 保存上一页返回数据
			localStorage.setItem("flowList", decodeURIComponent(this.$route.query.flowList))
			let list = JSON.parse(decodeURIComponent(this.$route.query.list))
			this.flowpoolId = list.flowPoolId
			this.form.flowPoolName = list.flowPoolName
			this.corpId= JSON.parse(decodeURIComponent(this.$route.query.corpId))
			if (this.cooperationMode == '3') {
				this.data = []
			} else {
				this.goPageFirst(1)
				this.getTaskList(1)
				this.getflow()
			}

		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				channelIccidlist({
					pageSize: 10,
					pageNum: page,
					flowPoolId: this.flowpoolId,
					ICCID: this.iccid,
					cardRemark: this.cardRemark,
					flowPoolStatus: this.flowPoolStatus,
					currentRateType: this.currentRateType,
					sequence:this.sequence,
					sortField:this.sortField
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						var data = res.data;
						let List = []
						// 循环遍历data
						data.map((value, index) => {
							List.push(value)
						})
						//回显
						this.selectionList.forEach(item => {
							List.forEach(element => {
								if (element.iccid == item.iccid) {
									this.$set(element, '_checked', true)
								}
							})
						})
						this.data = List
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			goPage: function(page) {
				this.goPageFirst(page)
			},
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			getTaskList:function(page){
				var _this = this
				getTaskList({
					pageSize: 10,
					pageNum: page,
					flowPoolId: this.flowpoolId,
				}).then(res => {
					if (res.code == '0000') {
						_this.taskloading = false
						this.Taskpage = page
						this.TaskcurrentPage = page
						this.Tasktotal = res.count
						this.taskdata = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.taskloading = false
				})
			},
			TaskgoPage: function(page) {
				this.getTaskList(page)
			},
			exportFile: function() {
				this.downloading = true
				exporticcid({
					pageSize: -1,
					pageNum: -1,
					flowPoolId: this.flowpoolId,
					ICCID: this.iccid,
					cardRemark: this.cardRemark,
					currentRateType: this.currentRateType,
					flowPoolStatus: this.flowPoolStatus,
					userId: this.corpId,
					// corpId: this.corpId,
					exportType:2
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.taskId
					this.taskName = res.data.taskName
					this.downloading = false
				}).catch(() => this.downloading = false)
			},
			exportfiles:function(row,type){
				this.exporting = true
				exportTaskList({
					id:row.id,
					type :type
				}).then(res => {
					const content = res.data
					var fileName = '' // 导出文件名
					if( type===1){
						fileName = 'success.csv'
					}
					if( type===2){
						fileName = 'fail.csv'
					}
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content,fileName)
					}
				}).catch(err => this.exporting = false)
			},
			deleteItem: function(row) {
				this.$Modal.confirm({
					title: this.$t('address.deleteitem'),
					onOk: () => {
						this.iccids = []
						this.iccids.push(row.iccid)
						Deleteiccid({
							corpId:this.corpId,
							flowPoolId:this.flowpoolId,
							iccids:this.iccids
						}).then(res => {
							if (res && res.code == '0000') {
								this.goPageFirst(1);
								this.iccids=[]
								this.$Notice.success({
									title: this.$t('address.Operationreminder'),
									desc: this.$t('common.Successful'),
								})
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						})
					}
				});
			},
			//暂停
			stop: function(row) {
				this.$Modal.confirm({
					title: this.$t('flow.confirmPause'),
					onOk: () => {
						stopStatusVCard({
							iccid:row.iccid,
							flowPoolID:this.flowpoolId,
							}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t('address.Operationreminder'),
									desc: this.$t('common.Successful'),
								})
								this.goPageFirst(this.currentPage);
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			//恢复
			active(row) {
				this.$Modal.confirm({
					title: this.$t('flow.confirmResume'),
					onOk: () => {
						recoverStatusVCard({
							iccid:row.iccid,
							flowPoolID:this.flowpoolId,
							}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t('address.Operationreminder'),
									desc: this.$t('common.Successful'),
								})
								this.goPageFirst(this.currentPage);
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			cardItem:function(data){
				this.title=this.$t('flow.cardManager')
				this.cardModal=true
				this.info.iccid = data.iccid
				this.typeflag = true
				this.piflag = false
				this.chooseiccid=1
			},

			// 单卡管理、批量修改 确定
			besure() {
				this.$refs["carform"].validate(valid => {
					if (valid) {
						this.rechargeloading = true
						if(this.chooseiccid===2){
							this.iccidlist = this.iccids
						}else{
							this.iccidlist.push(this.info.iccid)
						}
						cardUpadate({
							 availableTime: this.form.availableTime,
							 cardRemark: this.form.cardRemark,
							 dailyTotal:this.form.dailyTotal,
							 iccid:this.iccidlist,
							 total:this.form.total,
						}).then(res => {
							if (res.code === '0000') {
								let data = res.data
								this.$Notice.success({
									title: this.$t('address.Operationreminder'),
									desc: this.$t('common.Successful'),
								})
								this.rechargeloading = false
								this.goPageFirst(1)
								this.cardModal = false
								this.cancelModal()
								this.selectionList = []
								this.iccids = []
							}
						}).catch((error) => {
							this.rechargeloading = false
							console.log(error)
						}).finally(() => {
							this.rechargeloading = false
						})
					}
				})
			},

			deleteBatch: function() {
				var len = this.iccids.length;
				if (len < 1) {
					this.$Message.warning(this.$t('flow.chooserecord'))
					return
				}
				this.$Modal.confirm({
					title: this.$t('flow.Confirmdelete'),
					onOk: () => {
						Deleteiccid({
							corpId:this.corpId,
							flowPoolId:this.flowpoolId,
							iccids:this.iccids
						}).then(res => {
							if (res && res.code == '0000') {
								this.iccids = [];
								this.goPageFirst(1);
								this.$Notice.success({
									title: this.$t('common.Successful'),
									desc: this.$t('common.Successful')
								})
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						})
					}
				});
			},
			// 批量修改
			updateBatch: function() {
				var len = this.iccids.length;
				this.chooseiccid=2
				if (len < 1) {
					this.$Message.warning(this.$t('flow.chooserecord'))
					return
				}else {
					this.cardModal = true
					this.title = this.$t('flow.Batchupdate')
					this.piflag = true
					this.typeflag = false
					this.items=this.iccids
					if (len > 10){
						this.items = this.iccids.slice(0,10)
						this.remind = true
					}
				}
			},
			//多选
			handleRowChange: function(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					let flag = true;
					this.selectionList.map((item, index) => {
						if (value.iccid === item.iccid) {
							flag = false
						}
					});
					if (flag) {
						this.selectionList.push(value)
						this.iccids.push(value.iccid);
					}
				});
			},
			// 取消全选
			cancelAll(selection, row) {
				this.selection = []
				this.selectionList = []
				this.iccids = []
			},
			//取消单选
			cancelSigle(selection, row) {
				this.selectionList.forEach((value, index) => {
					if (value.iccid === row.iccid) {
						this.selectionList.splice(index, 1);
						this.iccids.splice(index, 1);
					}
				})
			},
			//取消
			cancelModal: function() {
				this.exportModal = false
				this.importModal=false
				this.cardModal = false
				this.file=''
				this.$refs['form'].resetFields()
				this.$refs['formobj'].resetFields()
				this.$refs['carform'].resetFields()
				this.form.dailyTotal = ''
				this.form.total = ''
				this.form.availableTime = ''
				this.form.cardRemark = ''
				this.iccidlist = []
				this.remind = false
			},
			importIccid: function() {
				this.importModal = true
			},

			//单个导入
			confirmone: function() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.importLoading = true
						  let channelCardFlowAddVO ={
							controlLogic:this.form.controllogic,
							dailyTotal:this.form.singlecycle,
							flowPoolTotal:this.form.totalcap,
							availableTime:this.form.availableTime,
							iccid:this.form.iccid,
							orderChannel:114,
							poolId: this.flowpoolId
						  }
						Singleimport(channelCardFlowAddVO).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t('common.Successful'),
									desc: this.$t('common.Successful')
								})
								this.goPageFirst(1)
								this.cancelModal()
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						}).finally(() => {
							this.importLoading = false
						})
					}
				})

			},
			//批量导入
			confirmbatch: function() {
				this.$refs["formobj"].validate(valid => {
					if (valid) {
						this.importLoading = true
						let formData = new FormData()
						formData.append('file', this.file)
						formData.append('poolID ', this.flowpoolId)
						Batchimport(formData).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t('common.Successful'),
									desc: this.$t('common.Successful')
								})
								this.goPageFirst(1)
								this.getTaskList(1)
								this.cancelModal()
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						}).finally(() => {
							this.importLoading = false
						})
					}
				})
			},
			//模板下载
			downloadFile: function() {
				this.$refs.modelTable.exportCsv({
					filename: "iccidList",
					// type:'xlsx',
					columns: this.modelColumns,
					data: this.modelData
				})
			},
			back: function() {
				this.$router.push({
					path: '/flowlist',
				})
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
						corpId: encodeURIComponent(this.corpId)
					}
				})
				this.exportModal = false
			},
			/**
			 * 文件上传
			 */
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			handleBeforeUpload(file, fileList) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: this.$t("buymeal.fileformat"),
						desc: file.name + this.$t("buymeal.incorrect")
					})
				} else {
					// if (file.size > 5 * 1024 * 1024) {
					// 	this.$Notice.warning({
					// 		title: this.$t("buymeal.Filesize"),
					// 		desc: file.name + this.$t("buymeal.Exceeds")
					// 	})
					// } else {
					this.file = file
					this.uploadList = fileList
					// }
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			removeFile: function() {
				this.file = ''
			},
			//获取流量池
			getflow:function(){
				channelflowlist({
					pageNum: -1,
					pageSize: -1,
					corpId:this.corpId,
				}).then(res => {
					if (res.code == '0000') {
						this.poolList = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {

				})
			},
			// 排序
			changeSort:function(data){
				this.sequence=data.order==="asc" ? 1:2
				this.sortField=data.key
				this.goPageFirst(1)
			}

		}
	}
</script>

<style>
	#space {
	  height: 25px;
	  line-height: 25px;
	  font-size: 12px;
	  white-space: pre-line;
	  list-style: none;
	  }
</style>
