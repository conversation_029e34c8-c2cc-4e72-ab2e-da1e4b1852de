<template>
	<!-- 流量池列表 -->
	<Card>
		<div>
			<span style="margin-top: 4px;font-weight:bold;">{{$t('flow.Channel')}}:</span>&nbsp;&nbsp;
			<span>{{corpName}}</span>
		</div>
		<div class="search-container">
			<div class="search-item">
				<span class="label">{{$t('flow.poolName')}}:</span>
				<Input v-model="searchObj.flowpoolname" :placeholder="$t('flow.inputPoolname')" clearable></Input>
			</div>
			<div class="search-item">
				<span class="label">{{$t('flow.Usagestatus')}}:</span>
				<Select filterable v-model="searchObj.usestatus" clearable :placeholder="$t('flow.chooseStatus')">
					<Option :value="1">{{$t('flow.speedlimit')}}</Option>
					<Option :value="2">{{$t('flow.Stoplimit')}}</Option>
					<Option :value="3">{{$t('flow.Normal')}}</Option>
				</Select>
			</div>
			<div class="search-item">
				<span class="label">{{$t('flow.upStatus')}}:</span>
				<Select filterable v-model="searchObj.shelfstatus" clearable :placeholder="$t('flow.Pleasechoose')">
					<Option :value="1">{{$t('flow.Online')}}</Option>
					<Option :value="2">{{$t('flow.Offline')}}</Option>
				</Select>
			</div>
			<div class="search-item button-item">
				<Button v-has="'search'" :disabled="!['1', '2'].includes(cooperationMode)" type="primary" icon="md-search" :loading="searchloading" @click="search()">{{$t('order.search')}}</Button>
			</div>
			<div class="search-item button-item">
				<Button v-has="'export'" :disabled="!['1', '2'].includes(cooperationMode)" type="success" icon="ios-cloud-download-outline" :loading="downloading" @click="exportFile">{{$t('stock.exporttb')}}</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'iccidlist'" type="warning" ghost style="margin-right: 10px;" @click="getIccid(row)">{{$t('showiccid_mngr')}}</Button>
				<Button v-has="'reminder'" type="primary" ghost style="margin-right: 10px;" @click="Reminder(row)">{{$t('flow.threshold')}}</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 提醒阈值弹窗 -->
		<Modal :title="$t('flow.threshold')" v-model="RemindModal" :mask-closable="true" @on-cancel="cancelModal">
			<Form ref="form" :model="form" :rules="rule">
				<FormItem :label="$t('flow.poolName')">
					<span>{{form.poolname}}</span>
				</FormItem>
				<FormItem :label="$t('flow.Usagethreshold')" prop="alarmThreshold">
					<Input v-model="form.alarmThreshold" :placeholder="$t('flow.Percentage')" clearable style="width: 300px"></Input>
				</FormItem>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" :loading="remindloading" @click="Confirm">{{$t('common.determine')}}</Button>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
					<FormItem :label="$t('exportID')">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem :label="$t('exportFlie')">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">{{$t('downloadResult')}}</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		channelflowlist,
		exportflow,
		reminder,
		getStoreByCorpId
	} from "@/api/flowpool/flowpool";
	import {
		searchcorpid
	} from '@/api/channel'
	export default {
		data() {
			return {
				cooperationMode: '', //合作模式
				corpId: '',
				corpName: '',
				form: {
					poolname: '',
					flowPoolId: '',
					alarmThreshold: ''
				},
				searchObj: {
					flowpoolname: '',
					usestatus: '',
					shelfstatus: '',
				},
				total: 0,
				currentPage: 1,
				page: 0,
				taskId: '',
				taskName: '',
				loading: false,
				searchloading: false,
				downloading: false,
				remindloading: false,
				RemindModal: false, //提醒阈值弹窗标识
				exportModal: false, //导出弹窗标识
				columns: [{
						title: this.$t('flow.poolName'),
						key: 'flowPoolName',
						minWidth: 140,
						align: 'center',
						tooltip: true,
					},
					{
						title: this.$t('flow.Usagestatus'),
						key: 'useStatus',
						minWidth: 130,
						align: 'center',
						render: (h, params) => {
						  const row = params.row;
						  const text = row.useStatus==='1' ? this.$t('flow.speedlimit'):row.useStatus==='2' ? this.$t('flow.Stoplimit')
						  :row.useStatus==='3' ? this.$t('flow.Normal'):"";
						  return h('label', text);
						}
					},
					{
						title: this.$t('flow.upStatus'),
						key: 'shelfStatus',
						minWidth: 100,
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							const text = row.shelfStatus === '1' ? this.$t('flow.Online') : row.shelfStatus === '2' ? this.$t(
								'flow.Offline') : "";
							return h('label', text);
						}
					},
					{
						title: this.$t('flow.totalusage') + '(GB)',
						key: 'flowPoolTotal',
						minWidth: 140,
						align: 'center'
					},
					{
						title: this.$t('flow.Useddata') + '(GB)',
						key: 'usedFlow',
						minWidth: 150,
						align: 'center'
					},
					{
						title: this.$t('flow.Numbericid'),
						key: 'cardCount',
						minWidth: 150,
						align: 'center'
					},
					{
						title: this.$t('flow.Resettype'),
						key: 'cycleType',
						minWidth: 150,
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							const text = row.cycleType === '1' ? this.$t('buymeal.hour') : row.cycleType === '2' ? this.$t('flow.day') :
								row.cycleType === '3' ? this.$t('flow.flowmonth') : row.cycleType === '4' ? this.$t('flow.flowyear') : "";
							return h('label', text);
						}
					},
					{
						title: this.$t('flow.Resetnumber'),
						key: 'cycleNum',
						minWidth: 180,
						align: 'center'
					},
					{
						title: this.$t('flow.Controllogic'),
						key: 'controlLogic',
						minWidth: 250,
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							const text = row.controlLogic === '1' ? this.$t(
									'flow.speedlimit') : row.controlLogic === '2' ?  this.$t('flow.Stoplimit'):
								row.controlLogic === '3' ? this.$t('flow.Continuelimit') : "";
							return h('label', text);
						}
					},
					{
						title: this.$t('flow.Validdate'),
						key: 'effectiveDate',
						minWidth: 300,
						align: 'center',
					},
					{
						title: this.$t('flow.Country'),
						key: 'supportCountry',
						minWidth: 200,
						align: 'center',
						render: (h, params) => {
						  const row = params.row;
						  let text ='';
						  row.supportCountry.map((country)=>{
							  text=text === "" ? (text + '' + country): (text + ', ' + country)
						  })
						  let length=text===""||text===null ? 0:text.length
						  if (length > 8) { //进行截取列显示字数
						    let country=text.replace(/\|/g,"</br>")
						    text = text.substring(0, 8) + "..."
						    return h('div', [h('Tooltip', {
						        props: {
						          placement: 'bottom',
						          transfer: true //是否将弹层放置于 body 内
						        },
						        style: {
						          cursor: 'pointer',
						        },
						      },
						      [ //这个中括号表示是Tooltip标签的子标签
						        text, //表格列显示文字
						        h('label', {
						            slot: 'content',
						            style: {
						              whiteSpace: 'normal'
						            },
						          },
								  country
						        )
						      ])]);
						  } else {
						    text = text;
						    return h('label', text)
						  }
						}
					},
					{
						title: this.$t('support.action'),
						slot: 'action',
						minWidth: 300,
						align: 'center',
						fixed: 'right',
					},
				],
				data: [],
				rule: {
					alarmThreshold: [{
							required: true,
							message: this.$t('flow.Pleasethreshold'),
							trigger: 'change',
						},
						{
							validator: (rule, value, cb) => {
								var str = /^[1-9]\d*$/;
								return str.test(value);
							},
							message: this.$t('flow.Pleaseinteger'),
						}
					],
				}

			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			//缓存数据
			let flowList = JSON.parse(localStorage.getItem("flowList")) === null ? '' : JSON.parse(localStorage.getItem(
				"flowList"))
			if (flowList) {
				this.searchObj.flowpoolname = flowList.flowpoolname === undefined ? "" : flowList.flowpoolname
				this.searchObj.usestatus = flowList.usestatus === undefined ? "" : flowList.usestatus
				this.searchObj.shelfstatus = flowList.shelfstatus === undefined ? "" : flowList.shelfstatus
			}
			if (['1', '2'].includes(this.cooperationMode)) {
				this.goPageFirst(1)
			}
			//清除缓存
			localStorage.removeItem("flowList")
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						this.corpId = res.data
						//获取渠道商名称
						this.getcorpName(this.corpId)
						let nameEn=null
						let flowPoolName=null
						if(this.$i18n.locale==='zh-CN'){
							flowPoolName=this.searchObj.flowpoolname
						}else if(this.$i18n.locale==='en-US'){
							nameEn=this.searchObj.flowpoolname
						}
						channelflowlist({
							pageSize: 10,
							pageNum: page,
							flowPoolName: flowPoolName,
							nameEn:nameEn,
							useStatus: this.searchObj.usestatus,
							shelfStatus: this.searchObj.shelfstatus,
							corpId: this.corpId,
							corpName: this.corpName,
							cooperationMode: this.cooperationMode
						}).then(res => {
							if (res.code == '0000') {
								this.page = page
								this.currentPage = page
								this.total = res.count
								this.data = res.data
								_this.loading = false
								this.searchloading = false
								//封装数据
								res.data.map((row,index) => {
									var text=this.$i18n.locale==='zh-CN' ? row.flowPoolName:this.$i18n.locale==='en-US' ? row.nameEn: ''
								    this.data[index].flowPoolName=text
								})
							}
						}).catch((err) => {
							console.error(err)
						}).finally(() => {
							_this.loading = false
							this.searchloading = false
						})
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchloading = false
				})
			},
			goPage: function(page) {
				this.goPageFirst(page)
			},
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			exportFile: function() {
				this.downloading = true
				let nameEn=null
				let flowPoolName=null
				if(this.$i18n.locale==='zh-CN'){
					flowPoolName=this.searchObj.flowpoolname
				}else if(this.$i18n.locale==='en-US'){
					nameEn=this.searchObj.flowpoolname
				}
				exportflow({
					pageSize: -1,
					pageNum: -1,
					flowPoolName: flowPoolName,
					nameEn:nameEn,
					useStatus: this.searchObj.usestatus,
					shelfStatus: this.searchObj.shelfstatus,
					corpId: this.corpId,
					corpName: this.corpName,
					userId: this.corpId,
					exportType: 2
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.taskId
					this.taskName = res.data.taskName
					this.downloading = false
				}).catch(() => this.downloading = false)
			},
			getIccid: function(row) {
				this.$router.push({
					path: '/iccidlist',
					query: {
						flowList: encodeURIComponent(JSON.stringify(this.searchObj)),
						list: encodeURIComponent(JSON.stringify(row)),
						corpId: encodeURIComponent(JSON.stringify(this.corpId)),
					}
				})
			},
			Reminder: function(row) {
				this.RemindModal = true
				this.form.poolname = row.flowPoolName
				this.form.flowPoolId = row.flowPoolId
				this.form.alarmThreshold = row.alarmThreshold.toString()
			},
			cancelModal: function() {
				this.RemindModal = false
				this.exportModal = false
				this.$refs['form'].resetFields()
			},
			Confirm: function() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.remindloading = true
						reminder({
							alarmThreshold: this.form.alarmThreshold,
							flowPoolId: this.form.flowPoolId
						}).then(res => {
							if (res && res.code == '0000') {
								this.goPageFirst(this.page);
								this.$Notice.success({
									title: this.$t('common.Successful'),
									desc: this.$t('common.Successful')
								})
								this.cancelModal()
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						}).finally(() => {
							this.remindloading = false
						})
					}
				})
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
						corpId: encodeURIComponent(this.corpId)
					}
				})
				this.exportModal = false
			},
			//获取渠道商名称
			getcorpName(corpId) {
				getStoreByCorpId(corpId).then(res => {
					if (res.code == '0000') {
						this.corpName = res.data.corpName
					}
				}).catch((err) => {
					console.error(err)
				})
			}
		}

	}
</script>

<style lang="less" scoped>
.search-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 20px;
  align-items: center;

  .search-item {
    display: flex;
    align-items: center;

    .label {
      font-weight: bold;
      white-space: nowrap;
      margin-right: 8px;
    }

    .ivu-input-wrapper {
      width: 200px;
    }

    .ivu-select {
      width: 220px;
    }

    &.button-item {
      min-width: auto;
      flex-shrink: 0;
    }
  }
}
</style>
