<template>
	<!-- 使用记录 -->
	<Card>
		<div>
			<span style="margin-top: 4px;font-weight: bold;">{{$t('flow.Originenterprise')}}:</span>&nbsp;&nbsp;
			<span>{{corpName}}</span>
		</div>
		<Form ref="form" :model="form"  :rules="rule"  :label-width="120" class="search-form-layout">
				<FormItem :label="$t('flow.poolName')" prop="flowpoolname" class="search-item-field">
					<Input v-model="form.flowpoolname" :placeholder="$t('flow.inputPoolname')" clearable></Input>
				</FormItem>
				<FormItem :label="$t('flow.Choosedate')" prop="date" class="search-item-field">
					<DatePicker type="daterange" format="yyyy-MM-dd" v-model="form.date" @on-change="handleDateChange" @on-clear="hanldeDateClear" :placeholder="$t('flow.PleaseChoosedate')"></DatePicker>
				</FormItem>
				<FormItem label-width="0" class="search-item-button">
					<Button v-has="'search'" :disabled="!['1', '2'].includes(cooperationMode)" type="primary" icon="md-search" :loading="searchloading" @click="search()">{{$t('order.search')}}</Button>
				</FormItem>
				<FormItem label-width="0" class="search-item-button">
					<Button v-has="'export_use'" :disabled="!['1', '2'].includes(cooperationMode)" icon="ios-cloud-download-outline" type="success"
					 :loading="downloading" @click="exportFile">
						{{$t('stock.exporttb')}}
					</Button>
				</FormItem>
				<FormItem label-width="0" class="search-item-button">
					<Button v-has="'export_flowsum'" :disabled="!['1', '2'].includes(cooperationMode)" icon="ios-arrow-dropdown" type="info"
					  @click="trafficExport">
						{{$t('flow.exportflowsum')}}
					</Button>
				</FormItem>
			</Form>
		<div style="display: flex;margin-top: 50px;">
			<span style="margin-top: 4px;font-weight:bold;">{{$t('flow.yearandmonthdate')}}:</span>&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">{{billdate}}</span>&nbsp;&nbsp;
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data" style="width: 100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'view'" type="warning" ghost style="margin-right: 10px;" @click="showDetails(row)">{{$t('flow.Clickview')}}</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 使用记录详情弹窗 -->
		<Modal :title="$t('flow.Usagedetails')" v-model="UsedModal" :mask-closable="true" @on-cancel="cancelModal" width="1000px">
			<span style="margin-top: 4px;font-weight:bold;">{{$t('flow.poolName')}}:</span>
			<span>{{poolname}}</span>
			<Button v-has="'export_usedetails'" style="margin: 0 2px;float: right;" icon="ios-cloud-download-outline" type="success"
			 :loading="detailsloading" @click="exportFileDetails">
				{{$t('stock.exporttb')}}
			</Button>
			<!-- 表格 -->
			<Table :columns="Usecolumns" :data="Usedata" style="width:100%;margin-top: 40px;" :loading="loading">
			</Table>
			<!-- 分页 -->
			<div style="margin-top:15px">
				<Page :total="usetotal" :current.sync="usecurrentPage" show-total show-elevator @on-change="usegoPage" />
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">{{$t('support.back')}}</Button>
			</div>
		</Modal>
		<!-- 选择下载参数弹窗 -->
		<Modal :title="$t('flow.requiredDataExport')" v-model="parameterModal" :mask-closable="true" @on-cancel="cancelModal" width="580px">
			<CheckboxGroup v-model="more" style="display: flex; flex-direction: column; flex-wrap: nowrap;">
			    <div style="margin: 15px;display: flex; justify-content: flex-start; align-items: center; flex-wrap: wrap;">
					<Checkbox class="checkbox" label="msisdn">
					    <span>MSISDN(H)</span>
					</Checkbox>
					<Checkbox class="checkbox" label="imsi">
					    <span>IMSI(H)</span>
					</Checkbox>
					<Checkbox class="checkbox" label="iccid">
						<span>ICCID</span>
					</Checkbox>
					<Checkbox class="checkbox" label="totalLimit">
					    <span>Total LIMIT</span>
					</Checkbox>
				</div>
				<div style="margin: 15px;display: flex; justify-content: flex-start; align-items: center; flex-wrap: wrap;">
					<Checkbox class="checkbox" label="remark">
					    <span>Remark</span>
					</Checkbox>
					<Checkbox class="checkbox" label="country">
					    <span>country or region</span>
					</Checkbox>
					<Checkbox class="checkbox" label="totalData">
					    <span>Total Data(MB)</span>
					</Checkbox>
				</div>
			</CheckboxGroup>
			<div slot="footer">
			  	<Button @click="cancelModal">{{$t('support.back')}}</Button>
			    <Button v-has="'export'" type="primary" :loading="besureLoading" @click="handle">{{$t('common.determine')}}</Button>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="exportcancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
					<FormItem :label="$t('exportID')">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem :label="$t('exportFlie')">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">{{$t('downloadResult')}}</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="exportcancelModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
			</div>
		</Modal>
		<Modal v-model="exportModalr" :mask-closable="true" @on-cancel="exportcancelModalr" width="800px">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
					<FormItem  :label="$t('exportID')">
						<ul style="margin-bottom: 15px;">
							<li id="space" v-for="(taskId,i) in taskIds" :key="taskIds.i">
								{{taskId}}
							</li>
						</ul>
						<div v-if="remind">
							<span>……</span>
						</div>
					</FormItem>
					<FormItem :label="$t('exportFlie')">
						<ul style="margin-bottom: 15px;">
							<li id="space" v-for="(taskName,i) in taskNames" :key="taskNames.i">
								{{taskName}}
							</li>
						</ul>
						<div  v-if="remind">
							<span>……</span>
						</div>
					</FormItem>
					<span style="text-align: left;margin-top: 30px;">{{$t('downloadResult')}}</span>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="exportcancelModalr">{{$t('common.cancel')}}</Button>
				<Button type="primary" @click="Gotor">{{$t('Goto')}}</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		UsageList,
		exportUsageList,
		UsageListdetails,
		exportUsagedetails,
		getStoreByCorpId,
		exportTraffic,
		requiredExport,
	} from "@/api/flowpool/flowpool";
	import {
		searchcorpid
	} from '@/api/channel'
	export default {
		data() {
			return {
				form: {
					flowpoolname: '',
					startTime:"",
					endTime:"",
					date:[]
				},
				cooperationMode: '', //合作模式
				corpName: '',
				corpId: '',
				month: '',
				flowpoolname: '',
				flowPoolId: '',
				flowPoolUniqueId:'',
				poolname: '',
				total: 0,
				currentPage: 1,
				page: 0,
				usetotal: 0,
				usecurrentPage: 1,
				usepage: 0,
				loading: false,
				searchloading: false,
				downloading: false,
				detailsloading: false,
				besureLoading: false,
				UsedModal: false, //使用记录详情弹窗标识
				parameterModal: false,
				exportModal: false, //导出弹框提示
				exportModalr: false,
				taskId: '',
				taskName: '',
				billdate: '',
				columns: [{
						title: this.$t('flow.poolName'),
						key: 'flowPoolName',
						minWidth: 150,
						align: 'center',
						tooltip: true,
					},
					{
						title: this.$t('flow.UsagestartTime'),
						key: 'startTime',
						minWidth: 150,
						align: 'center'
					},
					{
						title: this.$t('flow.UsageendTime'),
						key: 'endTime',
						minWidth: 150,
						align: 'center'
					},
					{
						title: this.$t('flow.Ratedtotalusage')+'(GB)',
						key: 'flowSum',
						minWidth: 180,
						align: 'center'
					},
					{
						title: this.$t('flow.Actualtotalusage')+'(GB)',
						key: 'useNum',
						minWidth: 180,
						align: 'center'
					},
					{
						title: this.$t('flow.Excessusage')+'(GB)',
						key: 'extraFlow',
						minWidth: 150,
						align: 'center'
					},
					{
						title: this.$t('flow.Ratedcharge'),
						key: 'ratedIncome',
						minWidth: 150,
						align: 'center'
					},
					{
						title: this.$t('flow.Excesscharge'),
						key: 'extraPrice',
						minWidth: 150,
						align: 'center'
					},
					{
						title: this.$t('flow.Totalcharge'),
						key: 'totalIncome',
						minWidth: 150,
						align: 'center'
					},
					{
						title: this.$t('flow.Details'),
						slot: 'action',
						minWidth: 150,
						align: 'center',
						fixed: 'right',
					},
				],
				Usecolumns: [{
						title: "ICCID",
						key: 'iccid',
						minWidth: 80,
						align: 'center',
						tooltip: true,
					},
					{
						title: this.$t('flow.IMSI'),
						key: 'himsi',
						minWidth: 80,
						align: 'center',
						tooltip: true,
					},
					{
						title: this.$t('flow.VIMSI'),
						key: 'imsi',
						minWidth: 80,
						align: 'center',
						tooltip: true,
					},
					{
						title: this.$t('flow.Numbertype'),
						key: 'cardType',
						minWidth: 50,
						align: 'center',
						render: (h, params) => {
						  const row = params.row;
						  const text = row.cardType==='1' ? 'H':row.cardType==='2' ? 'V':"";
						  return h('label', text);
						}

					},
					{
						title: this.$t('flow.StartTime'),
						key: 'startTime',
						minWidth: 90,
						align: 'center'
					},
					{
						title: this.$t('flow.EndTime'),
						key: 'endTime',
						minWidth: 90,
						align: 'center'
					},
					{
						title: this.$t('flow.Usage')+'(MB)',
						key: 'flowCount',
						minWidth: 50,
						align: 'center'
					},
				],
				data: [],
				Usedata: [],
				taskIds: [],
				taskNames: [],
				remind: false,
				rule: {
					// month: [{
					// 	type: 'date',
					// 	required: true,
					// 	message: this.$t('flow.Pleasemonth')
					// }, ],
					date: [
					{ type: 'array',required: true, message: this.$t("stock.chose_time"), trigger: 'blur',
					  fields: {
							0: {type: 'date', required: true, message: this.$t("deposit.startTime")},
							1: {type: 'date', required: true, message: this.$t("deposit.endTime")}
						}
					}
					],
				},
				msisdn: '',
				imsi: '',
				iccid: '',
				totalLimit: '',
				remark: '',
				country: '',
				totalData: '',
				more: [],
				flag: true
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			searchcorpid({
				userName: this.$store.state.user.userName,
			}).then(res => {
				if (res.code == '0000') {
					this.corpId = res.data
					//获取渠道商名称
					this.getcorpName(this.corpId)
				}
			}).catch((err) => {
				console.error(err)
			}).finally(() => {})
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				var nameEn=null
				var flowPoolName=null
				if(this.$i18n.locale==='zh-CN'){
					flowPoolName=this.form.flowpoolname
				}else if(this.$i18n.locale==='en-US'){
					nameEn=this.form.flowpoolname
				}
				UsageList({
					pageSize: 10,
					pageNum: page,
					flowPoolName: flowPoolName,
					nameEn:nameEn,
					startTime: this.form.startTime === "" ? null : this.form.startTime,
					endTime: this.form.endTime === "" ? null : this.form.endTime,
					corpId: this.corpId
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						this.data =res.data
						//封装数据
						res.data.map((row,index) => {
							var text=this.$i18n.locale==='zh-CN' ? row.flowPoolName:this.$i18n.locale==='en-US' ? row.nameEn: ''
						    this.data[index].flowPoolName=text
						})
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})

			},
			goPage: function(page) {
				this.goPageFirst(page)
			},
			search: function() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.searchloading = true
						this.goPageFirst(1)
					}
				})
			},
			goPageDetails: function(page) {
				var _this = this
				UsageListdetails({
					pageSize: 10,
					pageNum: page,
					flowPoolUniqueId:this.flowPoolUniqueId
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.usepage = page
						this.usecurrentPage = page
						this.usetotal = res.count
						this.Usedata = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			usegoPage: function(page) {
				this.goPageDetails(page)
			},
			handleDateChange: function(date) {
				// this.month = date
				// if (date) {
				// 	var date = new Date(date)
				// 	let month=(date.getMonth()+1)<10 ? "0"+(date.getMonth()+1):(date.getMonth()+1)
				// 	this.billmonth = date.getFullYear() + this.$t('flow.year')  + month + this.$t('flow.month')
				// } else {
				// 	this.billmonth = ""
				// }
				if (Array.isArray(date)) {
					this.form.startTime = date[0];
					this.form.endTime = date[1];
					if (this.form.startTime && this.form.endTime) {
						var date1 = new Date(this.form.startTime)
						var date2 = new Date(this.form.endTime)
						this.billdate = date1.getFullYear() + this.$t('flow.year') + (date1.getMonth() + 1) + this.$t('flow.month') + date1.getDate() + this.$t('flow.dday') + " -- " + date2.getFullYear() + this.$t('flow.year') + (date2.getMonth() + 1) + this.$t('flow.month') + date2.getDate() + this.$t('flow.dday')
					} else {
						this.billdate = ""
					}
				}
			},
			hanldeDateClear() {
				this.form.startTime = ''
				this.form.endTime = ''
			},
			showDetails: function(row) {
				this.flowPoolId = row.flowPoolId
				this.poolname = row.flowPoolName
				this.flowPoolUniqueId=row.flowPoolUniqueId
				this.goPageDetails(1)
				this.UsedModal = true
			},
			//导出使用记录
			exportFile: function() {
				this.downloading = true
				var nameEn=null
				var flowPoolName=null
				if(this.$i18n.locale==='zh-CN'){
					flowPoolName=this.form.flowpoolname
				}else if(this.$i18n.locale==='en-US'){
					nameEn=this.form.flowpoolname
				}
				exportUsageList({
					pageSize: -1,
					pageNum: -1,
					corpId: this.corpId,
					flowpoolName: flowPoolName,
					nameEn:nameEn,
					startTime: this.form.startTime === "" ? null : this.form.startTime,
					endTime: this.form.endTime === "" ? null : this.form.endTime,
					userId:this.corpId,
					exportType:2,
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.taskId
					this.taskName = res.data.taskName
					this.downloading = false
				}).catch(() => this.downloading = false)
			},
			//选择需要导出的数据
			handle() {
				let a = this.more.includes("remark") == true || this.more.includes("totalLimit")  == true
				let b = this.more.includes("msisdn") == false && this.more.includes("imsi") == false && this.more.includes("iccid") == false
				if (a && b) {
					this.$Notice.error({
						title: this.$t("address.Operationreminder"),
						desc: this.$t("support.msisdnImsiIccid")
					})
				} else if(this.more.length == 0) {
					this.$Notice.error({
						title: this.$t("address.Operationreminder"),
						desc: this.$t("flow.chooseOne")
					})
				} else {
					this.besureLoading = true
					exportTraffic({
						corpId: this.corpId,
						userId: this.corpId,
						flowPoolName:this.form.flowpoolname,
						fields: String(this.more),
						startTime: this.form.startTime === "" ? null : this.form.startTime,
						endTime: this.form.endTime === "" ? null : this.form.endTime,
						pageNum: -1,
						pageSize: -1,
					}).then((res) => {
						if (res && res.code == '0000') {
							 this.exportModalr = true
							var _this = this
							if (Object.values(res.data).length > 0) {
								Object.values(res.data).forEach(function(value) {
									_this.taskIds.push(value.id)
									_this.taskNames.push(value.fileName)
									if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
										let taskid =  _this.taskIds.slice(0,3)
										let taskname = _this.taskNames.slice(0,3)
										_this.taskIds = taskid
										_this.taskNames = taskname
										_this.remind = true
									}
								})
							}
							this.parameterModal = false
							this.more = []
							this.besureLoading = false
						}
					}).catch((err) => {
						console.error(err)
					}).finally(() => {
						this.besureLoading = false
					})
				}
			},
			// 流量汇总导出按钮
			trafficExport: function() {
				this.parameterModal = true
				this.besureLoading = false
			},
			//导出使用记录详情
			exportFileDetails: function() {
				this.detailsloading = true
				exportUsagedetails({
					pageSize: -1,
					pageNum: -1,
					flowPoolUniqueId:this.flowPoolUniqueId,
					userId: this.corpId,
					exportType:2
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.taskId
					this.taskName = res.data.taskName
					this.detailsloading = false
				}).catch(() => this.detailsloading = false)
			},
			cancelModal: function() {
				this.UsedModal = false
				this.parameterModal = false
				this.more = []
				this.besureLoading = false
			},
			exportcancelModal:function(){
				this.exportModal = false
			},
			exportcancelModalr: function() {
				this.exportModalr = false
				this.taskIds = []
				this.taskNames = []
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
						corpId: encodeURIComponent(this.corpId)
					},

				})
				this.exportModal = false
				this.UsedModal=false
			},
			Gotor() {
				this.$router.push({
					path: '/taskList',
					query: {
						corpId: encodeURIComponent(this.corpId),
					}
				})
				this.exportcancelModalr()
				this.UsedModal = false
			},
			//获取渠道商名称
			getcorpName(corpId) {
				getStoreByCorpId(corpId).then(res => {
					if (res.code == '0000') {
						this.corpName = res.data.corpName
					}
				}).catch((err) => {
					console.error(err)
				})
			},
		}
	}
</script>

<style lang="less" scoped>
.search-form-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 20px 30px; /* 垂直和水平间距 */
  margin-top: 20px;
  width: 100%;

  // 针对 FormItem 组件内部的样式调整
  :deep(.ivu-form-item) {
    margin-bottom: 0px !important; /* 移除默认的底部外边距，以便通过 gap 控制间距 */
  }

  // 针对 FormItem 标签的样式调整
  :deep(.ivu-form-item-label) {
    font-weight: bold; // 保持粗体
    white-space: nowrap; // 防止标签文本换行
  }

  // 针对输入框和日期选择器的样式调整
  .search-item-field {
    :deep(.ivu-input-wrapper),
    :deep(.ivu-date-picker) {
      width: 200px; /* 默认宽度 */
    }
    :deep(.ivu-date-picker) {
        width: 200px; /* 日期选择器特定宽度 */
    }
  }

  // 针对按钮 FormItem 的样式调整
  .search-item-button {
    :deep(.ivu-form-item-content) {
        margin-left: 0 !important; // 移除默认的左边距
    }
  }
}

#space {
  font-size: 12px;
  white-space: pre-line;
  list-style: none;
}

.checkbox {
  width: 115px;
}
</style>
