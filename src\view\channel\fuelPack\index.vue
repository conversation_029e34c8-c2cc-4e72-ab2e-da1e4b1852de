<template>
	<!-- 加油包管理 -->
	<Card>
		<div style="display: flex;margin-top: 20px;">
			<span style="margin-top: 4px;font-weight:bold;">ICCID:</span>&nbsp;&nbsp;
			<Input v-model="iccid" :placeholder="$t('flow.inputICCID')"  clearable style="width: 200px" ></Input>&nbsp;&nbsp;&nbsp;&nbsp;
			<Button v-has="'search'" :disabled="cooperationMode == '3'"  type="primary" icon="md-search" :loading="searchloading" @click="search()">{{$t('order.search')}}</Button>
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'buy_fuelpack'"  type="primary" ghost style="margin-right: 10px;" @click="buyFuelpack(row)">{{$t('fuelPack.Purchase')}}</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div  style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 购买加油包弹窗 -->
		<Modal :title="$t('fuelPack.Purchase')" v-model="buyFuelpackModal" :mask-closable="true" @on-cancel="cancelModal">
			<Form ref="form" :model="form" :rules="rule" style="font-weight: bold;">
				<FormItem label="ICCID:">
					<span>{{form.iccid}}</span>
				</FormItem>
				<FormItem :label="$t('support.mealname')+':'">
					<span>{{form.packageName}}</span>
				</FormItem>
				<FormItem :label="$t('support.DataRestrictionType')+':'">
					<span>{{form.flowLimitName}}</span>
				</FormItem>
				<FormItem :label="$t('fuelPack.SelectfuelPack')+':'" prop="packageRefuelId">
					<Select  v-model="form.packageRefuelId"  style="width: 200px;" @on-change="chooseRefuelId(form.packageRefuelId)">
						<Option :value="item.refuelId" v-for="(item,index) in fuelpackList" :key="index">{{$i18n.locale==='zh-CN' ? item.nameCn:$i18n.locale==='en-US' ? item.nameEn: ''}}</Option>
					</Select>
					<span style="margin-left: 20px;">{{$t('fuelPack.price')}}:{{require('mathjs').multiply(Number(1),Amount).toFixed(2)}}</span>
				</FormItem>
				<FormItem :label="$t('fuelPack.quantity')+':'" prop="type" v-if="form.flowLimitType==='2'">
					<RadioGroup v-model="form.type">
					        <Radio label="1" >{{form.periodUnit == '1' || form.periodUnit == '2'?$t('fuelPack.Currentday'):form.periodUnit == '3' ? $t('fuelPack.CurrentMonthly'):$t('fuelPack.CurrentYear')}}</Radio>
					        <Radio label="2" >{{form.periodUnit == '1' || form.periodUnit == '2'?$t('fuelPack.Daily'):form.periodUnit == '3' ? $t('fuelPack.Monthly'):$t('fuelPack.Yearly')}}
								<span v-if="form.type==='2'">
									({{$t('fuelPack.Remainingdays')}}
									{{form.daysRemaining}})
								</span>
							</Radio>
					</RadioGroup>
				</FormItem>
				<FormItem :label="$t('fuelPack.quantity')+':'" prop="quantity" v-if="form.flowLimitType==='1'">
					<Input v-model="form.quantity" :placeholder="$t('fuelPack.purchasequantity')" clearable style="width: 180px" @on-focus="getprice"></Input>
				</FormItem>
				<FormItem>
					<span>{{$t('fuelPack.Amount')}}:</span> 
					<span v-if="form.flowLimitType==='1'">
					{{
						require('mathjs').multiply(Number(form.quantity),Amount).toFixed(2)!="NaN"
						? (require('mathjs').multiply(Number(form.quantity), Amount).toFixed(2))
						:""
					}}</span>
					<span v-if="form.flowLimitType==='2' && form.type==='2'">
					{{
						require('mathjs').multiply(Number(form.remainderCycle),Amount).toFixed(2)!="NaN"
						? (require('mathjs').multiply(Number(form.remainderCycle),Amount).toFixed(2))
						: ""
					}}</span>
					<span v-if="form.flowLimitType==='2' && form.type==='1'">{{require('mathjs').multiply(Number(1),Amount).toFixed(2)}}</span>
					<span style="margin-left: 70px;">{{$t('deposit.currency')}}:</span>
					<span>{{form.currency}}</span>
				</FormItem>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" :loading="buyloading" @click="Confirm">{{$t('common.determine')}}</Button>
			</div>
		</Modal>
	</Card>

</template>

<script>
	import {
		getPage,
		buyPackageRefuel,
		refuelList
	} from "@/api/fuelPack";
	import {
		searchcorpid
	} from '@/api/channel'
	const math = require('mathjs')
	export default{
		data(){
			return{
				cooperationMode: '', //合作模式
				corpId:'',
				total: 0,
				currentPage: 1,
				page: 0,
				iccid:'',
				loading:false,
				searchloading:false,
				buyloading:false,
				buyFuelpackModal:false,//购买加油包弹窗
				form:{
					poolname:'',
				},
				fuelpackList:[],
				columns:[{
					title: "ICCID",
					key: 'iccid',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				},{
					title: this.$t('support.mealname'),
					key: 'packageName',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				},{
					title: this.$t('fuelPack.startDate'),
					key: 'activeTime',
					minWidth: 120,
					align: 'center'
				},
				{
					title: this.$t('fuelPack.endDate'),
					key: 'expireTime',
					minWidth: 120,
					align: 'center'
				},{
					title: this.$t('support.DataRestrictionType'),
					key: 'flowLimitType',
					minWidth: 180,
					align: 'center',
					render: (h, params) => {
						const row = params.row;
						const text = row.flowLimitType === '1' ? this.$t(
								'support.DataRestrictionCycle') : row.flowLimitType === '2' ?  this.$t('support.DataRestrictionSingle'): "";
						return h('label', text);
					}
				},{
					title: this.$t('fuelPack.packagedata'),
					key: 'flowLimitSum',
					minWidth: 180,
					align: 'center'
				},{
					title: this.$t('fuelPack.adddata'),
					key: 'purchasedPackageRefuelFlow',
					minWidth: 180,
					align: 'center'
				},{
					title: this.$t('fuelPack.usedhigh'),
					key: 'usedHighSpeedFlow',
					minWidth: 180,
					align: 'center'
				},
				{
					title: this.$t('support.action'),
					slot: 'action',
					minWidth: 120,
					align: 'center',
					fixed: 'right',
				},
				],
				data:[],
				currency:'',//币种
				Amount:'0.00',//单价
				rule:{
					packageRefuelId: [{
							required: true,
							message: this.$t('fuelPack.PleaseSelectfuelPack'),
							trigger: 'change',
						},
					],
					type: [{
							required: true,
							message: this.$t('fuelPack.quantity'),
							trigger: 'change',
						},
					],
					quantity: [{
							required: true,
							message: this.$t('fuelPack.purchasequantity'),
						},
						{
							validator: (rule, value, cb) => {
								var str = /^[1-9]\d*$/;
								return str.test(value);
							},
							message: this.$t('flow.Pleaseinteger'),
						}
					],
					
					
				}
				
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			if (this.cooperationMode != '3') {
				this.goPageFirst(1)
			}
		},
		methods:{
			goPageFirst:function(page){
				this.loading = true
				var _this = this
				searchcorpid({
					userName:this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						this.corpId=res.data
						getPage({
							corpId:res.data,
							current:page,
							size:10,
							iccid:this.iccid,
							cooperationMode: this.cooperationMode
						}).then(res => {
							if (res.code == '0000') {
								_this.loading = false
								this.searchloading = false
								this.page = page
								this.currentPage = page
								this.total = res.data.totalCount
								res.data.records.forEach((value)=>{
									value.packageName=this.$i18n.locale==='zh-CN' ? value.packageName:this.$i18n.locale==='en-US' ? value.nameEn: ''
								})
								this.data = res.data.records
							}
						}).catch((err) => {
							console.error(err)
						}).finally(() => {
							_this.loading = false
							this.searchloading = false
						})
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				})
			},
			search:function(){
				this.searchloading = true
				this.goPageFirst(1)
			},
			goPage:function(page){
				this.goPageFirst(page)
			},
			buyFuelpack:function(row){
				this.refuelList(row.packageId)
				this.form=Object.assign({}, row)
				this.form.flowLimitName=this.form.flowLimitType==='1'? this.$t('support.DataRestrictionCycle'): 
				this.form.flowLimitType === '2' ? this.$t('support.DataRestrictionSingle'): "" 
				this.form.daysRemaining=row.daysRemaining
			},
			cancelModal:function(){
				this.buyFuelpackModal=false
				this.$refs['form'].resetFields()
				this.Amount="0.00"
			},
			// 确定购买加油包
			Confirm:function(){
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.$Modal.confirm({
							title: this.$t("fuelPack.buyfuelPack"),
							width:'500px',
							onOk: () => {
								this.buyloading = true
								buyPackageRefuel({
									corpId: this.corpId,
									iccid: this.form.iccid,
									packageId: this.form.packageId,   //套餐ID
									packageRefuelId: this.form.packageRefuelId, //加油包ID
									packageUniqueId: this.form.packageUniqueId, //套餐唯一ID
									quantity: this.form.quantity, //加油包数量
									type: this.form.type //1-当日(默认),2-每日，流量限制类型为单日限量时必填，默认1
								}).then(res => {
									if (res && res.code == '0000') {
										this.goPageFirst(this.page);
										this.$Notice.success({
											title: this.$t('common.Successful'),
											desc: this.$t('common.Successful')
										})
										this.cancelModal()
									} else {
										throw res
									}
								}).catch((err) => {
									return false;
								}).finally(() => {
									this.buyloading = false
								})
							},
						})
					}
				})
			},
			getprice:function(value){
			  // this.form.Amount=math.multiply(Number(this.form.quantity), this.Amount) ? (math.multiply(Number(this.form.quantity), this.Amount)) : ""
			},
			//获取金额
			chooseRefuelId:function(){
				this.fuelpackList.forEach((item)=>{
					if(this.form.packageRefuelId===item.refuelId){
						this.form.currency=this.currency==='156'?this.$t('support.CNY')
						:this.currency==='344'?this.$t('support.HKD')
						:this.currency==='840'?this.$t('support.USD'):''
						let cny=item.spCny ?item.spCny:item.cny
						let hkd=item.spHkd ?item.spHkd:item.hkd
						let usd=item.spUsd ?item.spUsd:item.usd
						this.Amount=this.currency==='156'?cny
						:this.currency==='344'?hkd
						:this.currency==='840'?usd:''
						
					}
				})
			},
			//获取加油包列表
			refuelList:function(packageId){
				refuelList({
					corpId:this.corpId,
				},
				{
					packageID:packageId,
					cooperationMode: this.cooperationMode
				}).then(res => {
					if (res.code == '0000') {
						this.fuelpackList=res.data.refuelOutVoList
						this.currency=res.data.currencyCode
						this.buyFuelpackModal=true
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				
				})
			}
		}
	}
</script>

<style>
</style>
