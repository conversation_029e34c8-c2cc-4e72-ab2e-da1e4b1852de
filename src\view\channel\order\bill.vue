<template>
	<!-- 订单管理 -->
	<Card >
		<div style="width: 95%;margin-top: 80px; margin: auto;">
			<div style="display: flex;">
				<!-- <span style="margin-top: 4px;">{{$t('order.month')}}</span>&nbsp;&nbsp; -->
				<div style="display: block;">
					<DatePicker  v-model="form.month"  type="month" :placeholder="$t('order.chose_month')" style="width: 200px"></DatePicker>&nbsp;&nbsp;
					<span style="margin-top: 4px;">{{$t('order.expenditure')}}</span>&nbsp;&nbsp;
					<Input v-model="form.id" disabled  prop="showTitle" clearable
					 style="width: 200px" />&nbsp;&nbsp;
				</div>
				<div>
					<Button v-has="'search'" type="primary" icon="md-search" size="large"  @click="search()">{{$t('order.search')}}</Button>&nbsp;&nbsp;
					<Button v-has="'export'" icon="ios-cloud-download-outline" type="success" size="large"  @click="exportTable()">{{$t('order.exporttb')}}</Button>
				</div>
			</div>
			<!-- 表格 -->
			<Table :columns="columns12" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			</Table>
			<!-- 分页 -->
			<div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px; ">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
			
		</div>
	</Card>
</template>

<script>
	import{
		monthList,
		// monthExport
	} from '@/api/channel.js'
	export default {
		data(){
			return {
				total:0,
				currentPage:0,
				page:0,
				loading:false,
				form:{},
				columns12: [{
						title: this.$t("order.order_number"),
						key: 'orderid',
						align: 'center'
					},
					{
						title: this.$t("order.mealname"),
						key: 'meal',
						align: 'center'
					},
					{
						title: this.$t("order.count"),
						key: 'count',
						align: 'center'
					},
					{
						title: this.$t("order.order_money"),
						key: 'price',
						align: 'center'
					},
					{
						title: this.$t("order.addtime"),
						key: 'time',
						align: 'center'
					}
					
				],
				data:[{
					orderid:'15454354',
					card:'565',
					meal:'套餐1',
					state:'正常',
					count:'123',
					price:'654',
					time:'2021-03-15'
				},
				{
					orderid:'9565464',
					card:'565',
					meal:'套餐2',
					state:'正常',
					count:'312',
					price:'9445',
					time:'2021-03-15'
				},
				],
				rules:{
					
				}
			}
		},
		mounted(){
			this.goPageFirst(0)
		},
		methods:{
			goPageFirst(page){
				this.loading = true
				var _this = this
				let pageNumber = page
				let pageSize = 10
				monthList({
					pageNumber,
					pageSize,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.page = page
						this.total = res.data.total
						this.data = res.data.records
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
				})
			},
			search(){
				this.goPageFirst(0)
			},
			goPage(page){
				this.goPageFirst(page)
			},
			// 导出
			exportTable(){
				// monthExport('number').then((res) => {
				//   const content = res.data
				//   const fileName = this.$t("order.Numberform") // 导出文件名
				//   if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
				//     const link = this.$refs.downloadLink // 创建a标签
				//     let url = URL.createObjectURL(content)
				//     link.download = fileName
				//     link.href = url
				//     link.click() // 执行下载
				//     URL.revokeObjectURL(url) // 释放url
				//   } else { // 其他浏览器
				//     navigator.msSaveBlob(content, fileName)
				//   }
				// }).catch(() => this.downloading = false)
			}
			
			
			
		}
		
		
	}
</script>

<style>
</style>
