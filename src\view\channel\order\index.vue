<template>
	<!-- 订单管理 -->
	<Card>
		<div style="margin-top: 80px; margin: auto;">
			<div style="display: flex; flex-wrap: wrap; justify-content: flex-start; align-items: flex-start;">
				<div style="margin-left: 20px; margin-top: 20px;">
          <span style="font-weight:bold;">{{$t('order.mealname')}}:</span>&nbsp;&nbsp;
          <Input v-if="$i18n.locale === 'zh-CN'" v-model.trim="PackageName" :placeholder="$t('order.input_mealname')"
          	clearable style="width: 200px" />
          <Input v-if="$i18n.locale === 'en-US'" v-model="PackageNameEn" :placeholder="$t('order.input_mealname')"
          		clearable style="width: 200px" />&nbsp;&nbsp;
        </div>
				<div style="margin-left: 20px; margin-top: 20px;">
          <span style="font-weight:bold;">{{$t('order.input_number')}}:</span>&nbsp;&nbsp;
          <Input v-model.trim="iccid" :placeholder="$t('order.chose_number')" prop="showTitle" clearable
          	style="width: 200px" />
        </div>
				<div style="margin-left: 20px; margin-top: 20px;">
          <span style="font-weight:bold;">{{$t('order.timeslot')}}:</span>&nbsp;&nbsp;
          <DatePicker v-model="time_slot" type="daterange" format="yyyy-MM-dd" placement="bottom-end"
          	:placeholder="$t('order.chose_time')" style="width: 200px;margin-right: 10px;"
          	@on-change="handleDateChange" @on-clear="hanldeDateClear"></DatePicker>
        </div>
				<div style="margin-left: 20px; margin-top: 20px;">
          <span style="font-weight:bold;">{{$t('stock.attributableChannel')}}:</span>&nbsp;&nbsp;
          <Select v-model="attributableChannel" clearable style="width: 200px">
          	<Option :value="item.corpId" v-for="(item,index) in attributableChannelList" :key="item.corpId">{{item.corpName}}</Option>
          </Select>
        </div>
				<Button v-has="'view'" type="success" style="margin: 0 2px;margin-left: 20px; margin-top: 20px;"
					@click="showbill()">{{$t('order.monthly_bill')}}</Button>&nbsp;&nbsp;
				<Button v-has="'search'" :disabled="cooperationMode=='3'" type="primary" :loading="searchloading"
					style="margin: 0 2px;margin-left: 20px; margin-top: 20px;"
          @click="search()"><Icon type="md-search" />{{$t('order.search')}}</Button>
				<Button v-has="'export'" :disabled="cooperationMode=='3'" style="margin: 0 2px;margin-left: 20px; margin-top: 20px;"
					type="success" :loading="downloading" @click="exportFile">
					<Icon type="ios-cloud-download-outline" />{{$t('stock.exporttb')}}
				</Button>
				<Button :disabled="cooperationMode === '1' || cooperationMode=='3'" v-has="'trafficDetails'"
					style="margin: 0 2px;margin-left: 20px; margin-top: 20px;" icon="md-search" type="info" @click="getTrafficDetails">
					<Icon type="ios-cloud-download-outline" />{{$t('flowInfo')}}
				</Button>
			</div>
			<!-- 表格 -->
			<Table :columns="fatherOrChild == true ? columnsSon : columnsFather" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
				<template slot-scope="{ row, index }" slot="action">
					<Button v-if="row.isUsed===false && row.orderType!='7'" type="error" size="small"
						style="margin-right: 5px" @click="Delete(row)">{{$t('order.unsubscribe')}}</Button>
				</template>
			</Table>
			<!-- 分页 -->
			<div style="margin-top: 100px;">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
			<!-- 导出提示 -->
			<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
				<div style="align-items: center;justify-content:center;display: flex;">
					<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
						<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
						<FormItem :label="$t('exportID')">
							<span style="width: 100px;">{{taskId}}</span>
						</FormItem>
						<FormItem :label="$t('exportFlie')">
							<span>{{taskName}}</span>
						</FormItem>
						<span style="text-align: left;">{{$t('downloadResult')}}</span>
					</Form>
				</div>

				<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
					<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
					<Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
				</div>
			</Modal>
			<!-- 流量明细弹框-->
			<Modal :title="$t('flowInfo')" v-model="trafficDetailsFlage" :footer-hide="true" :mask-closable="false"
				@on-cancel="cancelModal" width="900px">
				<div style="padding: 0 16px">
					<div style="width: 100%;display: flex;justify-content: space-evenly; align-items: self-start;">
						<Form ref="form" :model="trafficForm" :rules="trafficRule" inline :label-width="110"
							style="display: flex;justify-content: center;align-items: center;">
							<FormItem :label="$t('fuelPack.SelectDate')" prop="date">
								<DatePicker type="daterange" format="yyyy-MM-dd" placement="bottom-end"
									v-model="trafficForm.date" style="width: 200px" :clearable="true"
									:placeholder="$t('fuelPack.SelectDate')" @on-change="handleDateChange1"
									@on-clear="hanldeDateClear1" class="recordBtnSty">
								</DatePicker>
							</FormItem>
							<FormItem :label="$t('flow.SelectDestination')" prop="localId">
								<Select filterable v-model="trafficForm.localId"
									:placeholder="$t('flow.SelectDestination')" :clearable="true" style="width: 200px"
									@on-change="getLocalList">
									<Option v-for="item in localList" :value="item.mcc" :key="item.id">
										{{ item.countryEn }}
									</Option>
								</Select>
							</FormItem>
						</Form>
						<div style="display: flex;justify-content: center;align-items: center;">
							<Button type="primary" @click="searchTraffic()" :loading="searchTrafficLoading" icon="md-search">{{$t('flow.select')}}</Button>
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              <Button type="info" @click="exportTraffic()" :loading="exportTrafficLoading" icon="md-arrow-down">{{$t('order.exporttb')}}</Button>
						</div>
					</div>
					<div>
						<Table :columns="columnsTraffic" :data="trafficData" :loading="trafficLoading"></Table>
						<!-- 分页 -->
						<div style="margin-top: 40px;">
							<Page :total="totalTraffic" :current.sync="currentPageTraffic" show-total show-elevator
								@on-change="goTrafficPage" />
						</div>
					</div>
					<div style="margin: 20px; text-align: center;">
						<Button @click="cancelModal">
							<div style="display: flex; align-items: center">
								<Icon type="ios-arrow-back" />&nbsp;{{$t('support.back')}}
							</div>
						</Button>
					</div>
				</div>
			</Modal>
      <!-- 确认删除弹窗 -->
      <Modal v-model="modal3" :footer-hide="true">
        <p style="margin-top: 20px; display: flex; justify-content: center; font-size: 16px; font-weight: bold;">
          {{$t("order.ifunsubscribe") + "?"}}
        </p>
        <div style="margin-top: 30px; display: flex; justify-content: center;">
          <Button size="small" type="default" @click="cancelModal">
            <Icon type="ios-arrow-back" />
            返回
          </Button>
          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <Button size="small" type="success" :loading="beSureLoading" @click="beSure">确定</Button>
        </div>
      </Modal>
    </div>
	</Card>
</template>

<script>
	import {
		orderList,
		Unsubscribe,
		searchcorpid,
		orderExport,
		trafficList,
		getAttributableChannelList,
	} from '@/api/channel.js'
  import {
    exportTraffic
  } from "@/api/customer/channelShop.js";
	import {
		opsearch
	} from '@/api/channel.js'
	import {
		getFatherOrChildChannel
	} from '@/api/fatherOrChildChannel'
	const math = require('mathjs')
	export default {
		data() {
			return {
				cooperationMode: '', //合作模式
				total: 0,
				currentPage: 1,
				totalTraffic: 0,
				currentPageTraffic: 1,
				corpId: '',
				searchBeginTime: '',
				searchEndTime: '',
				time_slot: '',
				page: 0,
				taskId: '', //任务Id
				taskName: '', //任务名称
				PackageName: '',
				PackageNameEn: '',
				attributableChannel: '',
				iccid: '',
				loading: false,
				searchloading: false,
				downloading: false,
				searchTrafficLoading: false,
        exportTrafficLoading: false,
				trafficLoading: false,
        beSureLoading: false,
				exportModal: false,
				trafficDetailsFlage: false,
        modal3: false,
				form: {},
				trafficForm: {
					date: [],
					startTime: "",
					endTime: "",
					localId: "",
				},
				localList: [],
				columnsSon: [{
						title: this.$t("order.order_number"),
						key: 'orderId',
						minWidth: 120,
						align: 'center'
					},
					{
						title: this.$t("order.input_number"),
						key: 'iccid',
						minWidth: 120,
						align: 'center'
					},
					{
						title: this.$t("order.mealname"),
						key: 'packageName',
						align: 'center',
						minWidth: 140,
						render: (h, params) => {
							const row = params.row
							var text = this.$i18n.locale === 'zh-CN' ? row.packageName : this.$i18n.locale ===
								'en-US' ? row.packageNameEn : ''
							return h('label', {
								style: {
									'word-break': 'break-word',
								}
							}, text)
						}
					},
					{
						title: this.$t("order.channels"),
						key: 'orderChannel',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							const text = row.orderChannel == '102' ? 'API' : row.orderChannel == '103' ?
								this.$t("order.Website") : row.orderChannel == '104' ?
								this.$t("order.BeijingMobile") : row.orderChannel == '105' ?
								this.$t("order.BulkOrder") : row.orderChannel == '106' ?
								this.$t("order.Trial") : row.orderChannel == '110' ?
								this.$t("order.Testing") : row.orderChannel == '111' ?
								this.$t("order.issuance") : row.orderChannel == '112' ?
								this.$t("order.Postpaid") : row.orderChannel == '113' ?
								'WEB' : row.orderChannel == '114' ?
								this.$t("order.Datapool") : '';
							return h('label', text)
						}
					},
					{
						title: this.$t("stock.attributableChannel"),
						key: 'revertChannelName',
						align: 'center',
						minWidth: 120,
					},
					{
						title: this.$t("order.order_state"),
						key: 'orderStatus',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							const text = row.orderStatus == '1' ? this.$t("order.delivered") : row.orderStatus ==
								'2' ? this.$t("order.Completed") :
								row.orderStatus == '3' ? this.$t("order.Cancelled") :
								row.orderStatus == '4' ? this.$t("order.approval") :
								row.orderStatus == '5' ? this.$t("order.Recycled") :
								'';
							return h('label', text)
						}
					},
					{
						title: this.$t("order.count"),
						key: 'count',
						minWidth: 120,
						align: 'center'
					},
					{
						title: this.$t("order.order_money"),
						key: 'subAmount',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row
							const text = parseFloat(math.divide(math.bignumber(row.subAmount), 100).toFixed(2))
								.toString()
							return h('label', text)
						}
					},
					{
						title: this.$t("order.addtime"),
						key: 'orderDate',
						minWidth: 120,
						align: 'center'
					},
					{
						title: this.$t("order.action"),
						slot: 'action',
						minWidth: 120,
						align: 'center'
					}
				],
				columnsFather: [{
						title: this.$t("order.order_number"),
						key: 'orderId',
						minWidth: 120,
						align: 'center'
					},
					{
						title: this.$t("order.input_number"),
						key: 'iccid',
						minWidth: 120,
						align: 'center'
					},
					{
						title: this.$t("order.mealname"),
						key: 'packageName',
						align: 'center',
						minWidth: 140,
						render: (h, params) => {
							const row = params.row
							var text = this.$i18n.locale === 'zh-CN' ? row.packageName : this.$i18n.locale ===
								'en-US' ? row.packageNameEn : ''
							return h('label', {
								style: {
									'word-break': 'break-word',
								}
							}, text)
						}
					},
					{
						title: this.$t("order.channels"),
						key: 'orderChannel',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							const text = row.orderChannel == '102' ? 'API' : row.orderChannel == '103' ?
								this.$t("order.Website") : row.orderChannel == '104' ?
								this.$t("order.BeijingMobile") : row.orderChannel == '105' ?
								this.$t("order.BulkOrder") : row.orderChannel == '106' ?
								this.$t("order.Trial") : row.orderChannel == '110' ?
								this.$t("order.Testing") : row.orderChannel == '111' ?
								this.$t("order.issuance") : row.orderChannel == '112' ?
								this.$t("order.Postpaid") : row.orderChannel == '113' ?
								'WEB' : row.orderChannel == '114' ?
								this.$t("order.Datapool") : '';
							return h('label', text)
						}
					},
					{
						title: this.$t("stock.attributableChannel"),
						key: 'revertChannelName',
						align: 'center',
						minWidth: 120,
					},
					{
						title: this.$t("order.order_state"),
						key: 'orderStatus',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							const text = row.orderStatus == '1' ? this.$t("order.delivered") : row.orderStatus ==
								'2' ? this.$t("order.Completed") :
								row.orderStatus == '3' ? this.$t("order.Cancelled") :
								row.orderStatus == '4' ? this.$t("order.approval") :
								row.orderStatus == '5' ? this.$t("order.Recycled") :
								'';
							return h('label', text)
						}
					},
					{
						title: this.$t("order.count"),
						key: 'count',
						minWidth: 120,
						align: 'center'
					},
					{
						title: this.$t("order.order_money"),
						key: 'amount',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row
							const text = parseFloat(math.divide(math.bignumber(row.amount), 100).toFixed(2))
								.toString()
							return h('label', text)
						}
					},
					{
						title: this.$t("order.channelOrderMoney"),
						key: 'subAmount',
						align: 'center',
						minWidth: 170,
						render: (h, params) => {
							const row = params.row
							const text1 = parseFloat(math.divide(math.bignumber(row.subAmount), 100).toFixed(2))
								.toString()
							const text = row.subAmount ? text1 : ''
							return h('label', text)
						}
					},
					{
						title: this.$t("order.addtime"),
						key: 'orderDate',
						minWidth: 120,
						align: 'center'
					},
					{
						title: this.$t("order.action"),
						slot: 'action',
						minWidth: 120,
						align: 'center'
					}

				],
				columnsTraffic: [{
					title: this.$t("support.date"),
					key: "date",
					align: "center",
					minWidth: 100,
					tooltip: true,
				}, {
					title: this.$t("buymeal.Country"),
					key: "countryOrRegion",
					align: "center",
					minWidth: 100,
					tooltip: true,
				}, {
					title: this.$t("flow.usageMB"),
					key: "usedTraffic",
					align: "center",
					minWidth: 100,
					tooltip: true,
				}, {
					title: this.$t("fuelPack.Amount"),
					key: "amount",
					align: "center",
					minWidth: 100,
					tooltip: true,
				}],
				data: [],
				trafficData: [],
				rules: {},
				trafficRule: {},
				attributableChannelList: [], //归属渠道商
				fatherOrChild: false,
        rowData: {},
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			if (this.cooperationMode != '3') {
				this.getFatherOrChildChannel()
				this.getAttributableChannelList()
				this.getLocalList()
			}
		},
		methods: {
			goPageFirst(page) {
        if (!this.PackageName && !this.PackageNameEn &&
          !this.iccid && !this.searchBeginTime && !this.searchEndTime
          && !this.attributableChannel)
        {
          this.total = 0
          this.data = []
          this.$Message['warning']({
            background: true,
            content: this.$t('support.searchcondition')
          });

        } else {
          this.searchloading = true
          this.loading = true
          var _this = this
          searchcorpid({
          	userName: this.$store.state.user.userName,
          }).then(res => {
          	if (res.code == '0000') {
          		let corpId = res.data
          		this.corpId = corpId
          		let cooperationMode = this.cooperationMode
          		let pageNumber = page
          		let pageSize = 10
          		let startDate = this.searchBeginTime === "" ? null : this.searchBeginTime + ' 00:00:00'
          		let endDate = this.searchEndTime === "" ? null : this.searchEndTime + ' 23:59:59'
          		let iccid = this.iccid === "" ? null : this.iccid
          		let revertCorpId = this.attributableChannel === "" ? null : this.attributableChannel
          		let orderUserId = ""
          		let packageName = null
          		let packageNameEn = null
          		if (this.$i18n.locale === 'zh-CN') {
          			packageName = this.PackageName
          		}
          		if (this.$i18n.locale === 'en-US') {
          			packageNameEn = this.PackageNameEn
          		}
          		orderList({
          			pageNumber,
          			pageSize,
          			startDate,
          			endDate,
          			corpId,
          			iccid,
          			packageName,
          			packageNameEn,
          			revertCorpId,
          			cooperationMode
          		}).then(res => {
          			if (res.code == '0000') {
          				_this.loading = false
          				this.searchloading = false
          				this.page = page
          				this.currentPage = page
          				this.total = res.data.total
          				this.data = res.data.records
          			}
          		}).catch((err) => {
          			console.error(err)
          		}).finally(() => {
          			this.loading = false
          			this.searchloading = false
          		})
          	}
          }).catch((err) => {
          	console.error(err)
          }).finally(() => {})
        }
			},
			// 查看月账单
			showbill() {
				this.$router.push({
					path: '/bill',
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			search() {
				this.goPageFirst(1)
			},
			//导出
			exportFile() {
        if (!this.PackageName && !this.PackageNameEn &&
          !this.iccid && !this.searchBeginTime && !this.searchEndTime
          && !this.attributableChannel)
        {
          this.$Message['warning']({
            background: true,
            content: this.$t('support.searchcondition')
          });
        } else {
          this.downloading = true
          let startDate = this.searchBeginTime === "" ? null : this.searchBeginTime + ' 00:00:00'
          let endDate = this.searchEndTime === "" ? null : this.searchEndTime + ' 23:59:59'
          let iccid = this.iccid === "" ? null : this.iccid
          let revertCorpId = this.attributableChannel === "" ? null : this.attributableChannel
          let packageName = null
          let packageNameEn = null
          if (this.$i18n.locale === 'zh-CN') {
          	packageName = this.PackageName
          }
          if (this.$i18n.locale === 'en-US') {
          	packageNameEn = this.PackageNameEn
          }
          orderExport({
          	corpId: this.corpId,
          	startDate: startDate,
          	endDate: endDate,
          	iccid: iccid,
          	packageName: packageName,
          	packageNameEn: packageNameEn,
          	revertCorpId: revertCorpId,
          	userId: this.corpId,
          	cooperationMode: this.cooperationMode,
          	isSubChannel: this.fatherOrChild
          }).then((res) => {
          	this.exportModal = true
          	this.taskId = res.data.taskId
          	this.taskName = res.data.taskName
          	this.downloading = false
          }).catch((err) => {
            console.log(err)
          }).finally(() => {
            this.downloading = false
          })
        }
			},
			cancelModal() {
				this.trafficDetailsFlage = false
				this.exportModal = false
        this.modal3 = false
				this.trafficForm.date = ''
				this.trafficForm.startTime = ''
				this.trafficForm.endTime = ''
				this.trafficForm.localId = ''
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
						corpId: encodeURIComponent(this.corpId)
					}
				})
				this.exportModal = false
			},
			Delete(row) {
        this.modal3 = true
        this.rowData = row
			},
      beSure () {
        this.beSureLoading = true
        let corpId = this.corpId
        Unsubscribe(corpId, {
        	"amount": this.rowData.amount,
        	"count": this.rowData.count,
        	"currencyCode": this.rowData.currencyCode,
        	"effectiveDay": this.rowData.effectiveDay,
        	"isUsed": this.rowData.isUsed,
        	"msisdn": this.rowData.msisdn,
        	"orderChannel": this.rowData.orderChannel,
        	"orderDate": this.rowData.orderDate,
        	"orderId": this.rowData.orderId,
        	"orderStatus": this.rowData.orderStatus,
        	"packageName": this.rowData.packageName,
        	"packageStatus": this.rowData.packageStatus,
        	"packageUniqueId": this.rowData.packageUniqueId
        }).then(res => {
        	if (res && res.code == '0000') {
        		this.$Notice.success({
        			title: this.$t("address.Operationreminder"),
        			desc: this.$t("order.Unsubscribe")
        		})
            this.modal3 = false
        		this.goPageFirst(1)
        	} else {
        		throw res
        	}
        }).catch((err) => {
        	console.log(err)
        }).finally(() => {
        	this.beSureLoading = false
        })
      },
			hanldeDateClear() {
				this.searchBeginTime = ''
				this.searchEndTime = ''
			},
			handleDateChange(dateArr) {
				let beginDate = this.time_slot[0] || ''
				let endDate = this.time_slot[1] || ''
				if (beginDate == '' || endDate == '') {
					return
				}
				[this.searchBeginTime, this.searchEndTime] = dateArr
			},
			// 查看流量明细
			getTrafficDetails() {
				this.trafficDetailsFlage = true
				this.goTrafficPageFirst(1)
			},
			handleDateChange1(date) {
				if (Array.isArray(date)) {
					this.trafficForm.startTime = date[0];
					this.trafficForm.endTime = date[1];
				}
			},
			hanldeDateClear1() {
				this.trafficForm.startTime = ''
				this.trafficForm.endTime = ''
			},
			//国家/地区
			getLocalList() {
				opsearch().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.localList = list;
						this.localList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			goTrafficPage(page) {
				this.goTrafficPageFirst(page)
			},
			searchTraffic() {
				this.searchTrafficLoading = true
				this.goTrafficPageFirst(1)
			},

      exportTraffic() {
        this.exportTrafficLoading = true
        exportTraffic({
          beginDate: this.trafficForm.startTime === "" ? null : this.trafficForm.startTime,
          endDate: this.trafficForm.endTime === "" ? null : this.trafficForm.endTime,
          country: this.trafficForm.localId === "" ? null : this.trafficForm.localId,
          corpId: this.corpId,
        }).then((res) => {
        	this.exportModal = true
        	this.taskId = res.data.taskId
        	this.taskName = res.data.taskName
        	this.exportTrafficLoading = false
        	this.trafficForm.startTime = ""
        	this.trafficForm.endTime = ""
          this.trafficForm.localId = ""
        }).catch((err) => {
        	console.error(err)
        	this.exportTrafficLoading = false
        }).finally(() => {})
      },

			// 流量明细分页列表加载
			goTrafficPageFirst(page) {
				this.trafficLoading = true
				var _this = this
				trafficList({
					beginDate: this.trafficForm.startTime === "" ? null : this.trafficForm.startTime,
					endDate: this.trafficForm.endTime === "" ? null : this.trafficForm.endTime,
					country: this.trafficForm.localId === "" ? null : this.trafficForm.localId,
					corpId: this.corpId,
					pageNum: page,
					pageSize: 10
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.page = page
						this.currentPageTraffic = page
						this.totalTraffic = Number(res.count)
						this.trafficData = res.data
						this.searchTrafficLoading = false
						this.trafficLoading = false
					}
				}).catch((err) => {
					console.error(err)
					this.trafficLoading = false
					this.searchTrafficLoading = false
				}).finally(() => {})
			},
			//归属渠道商列表
			getAttributableChannelList() {
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					this.corpId = res.data
					getAttributableChannelList({
						corpId: this.corpId,
						selfContain: true
					}).then(res => {
						if (res.code === '0000') {
							if (this.cooperationMode == 1) {
								this.attributableChannelList = res.data
							} else {
								//a2z模式没有子渠道商
								let list = [{
									corpName: res.data[0].corpName,
									corpId: res.data[0].corpId
								}]
								this.attributableChannelList = list
							}

							if (this.attributableChannelList.length == 1) {
								this.attributableChannel = this.attributableChannelList[0].corpId
							}
						}
					}).catch((err) => {
						console.log(err)
					})
				})
			},
			//判断渠道商账户 子/父
			getFatherOrChildChannel() {
				getFatherOrChildChannel({
					corpId: sessionStorage.getItem("corpId")
				}).then(res => {
					if (res.code == '0000') {
						this.fatherOrChild = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {})
			}
		}
	}
</script>

<style>
</style>
