<template>
	<!-- 加油包管理 -->
	<Card>
		<div class="search_head_i">
			<div class="search_box">
				<Button :disabled="show == 'false' || cooperationMode == '1' || cooperationMode == '3'" type="primary" icon="md-add" @click="add()">{{$t('support.create')}}</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table ref="selection" :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button :disabled="show == 'false'" type="error" size="small" style="margin-right: 10px" @click="deleteItem(row.id)">{{$t('address.Delete')}}</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 弹出框-->
		<Modal :title="$t('support.newAddonPack')" v-model="newGaspackFlag" :footer-hide="true" :mask-closable="false" width="400px"
		 @on-cancel="cancelModal">
			<div style="padding: 0 16px;">
				<Form ref="addObj" :model="addObj" :label-width="100" :rules="ruleValidate">
					<FormItem :label="$t('support.AddonAmount')" prop="flowValue" style="margin: 20px 0 35px 0;">
						<Input v-model="addObj.flowValue" :placeholder="$t('support.inputFlowValue')">
							<span slot="append">MB</span>
						</Input>
					</FormItem>
				</Form>
				<div slot="footer" style="text-align: center;">
					<Button @click="cancelModal">{{$t('common.cancel')}}</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<Button style="margin-left: 8px" type="primary" @click="submit" v-preventReClick
						:loading="submitFlag">{{$t('support.determine')}}</Button>
				</div>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		getPageList,
		deleteItem,
		addRefuelingBag
	} from "@/api/channel/package";
	export default {
		data() {
			const FlowLimitRule = (rule, value, callback) => {
				if (value) {
					var str = /^[1-9]\d*$/;
					return str.test(value);
				} else {
			        callback();
			    }
			};
			const flowValueRule = (rule, value, callback) => {
				if (value && value > 104857600) {
					callback(new Error(rule.message));
				} else {
			        callback();
			    }
			};
			return {
				total: 0,
				currentPage: 1,
				page: 0,
				cooperationMode: '',
				loading: false,
				searchloading: false, //查询加载
				newGaspackFlag: false, //新增加油包
				submitFlag: false, //提交
				show: "", //是否允许支持自建套餐
				data: [],//表格列表
				columns: [{
					title: this.$t('support.AddonID'),
					key: 'id',
					minWidth: 120,
					align: 'center',
					tooltip: true
				}, {
					title: this.$t('support.AddonName'),
					key: 'nameCn',
					minWidth: 120,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row;
						const text = this.$i18n.locale==='zh-CN' ? row.nameCn : this.$i18n.locale==='en-US' ? row.nameEn: ''
						return h('label', text)
					}
				}, {
					title: this.$t('support.flowValue') + '(MB)',
					key: 'flowValue',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: this.$t('address.action'),
					slot: 'action',
					minWidth: 100,
					align: 'center',
					fixed: 'right'
				}, ],
				addObj: {
					flowValue: ""
				},
				ruleValidate: {
					flowValue: [{
						required: true,
						type: 'string',
						message: this.$t('support.addAmountMandatory'),
					},{
						validator: FlowLimitRule,
						message: this.$t('flow.Pleaseinteger'),
					},,{
						validator: flowValueRule,
						message: this.$t('support.cannotExceed'),
					}],
				}
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			this.show = this.$route.query.showFalg
			if (!this.cooperationMode || this.cooperationMode == 2) {
				console.log(this.cooperationMode)
				this.goPageFirst(1)
			}
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				getPageList({
					pageNo: page,
					pageSize: 10,
					corpId: sessionStorage.getItem("corpId"),
					cooperationMode: sessionStorage.getItem("cooperationMode")
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.data = res.data;
						this.total = res.count
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			//删除
			deleteItem(refuelId) {
				let cooperationMode = sessionStorage.getItem("cooperationMode")
				this.$Modal.confirm({
					title: this.$t('flow.Confirmdelete'),
					onOk: () => {
						deleteItem(refuelId,cooperationMode).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t('address.Operationreminder'),
									desc: this.$t('common.Successful')
								})
								this.goPageFirst(this.page)
							} else {
								throw res
							}
						}).catch((err) => {})
					}
				});
			},
			cancelModal() {
				this.$refs.addObj.resetFields()
				this.newGaspackFlag = false
			},
			add() {
				this.newGaspackFlag = true
			},
			submit() {
				this.$refs["addObj"].validate(valid => {
					if (valid) {
						this.submitFlag = true
						addRefuelingBag({
							flowValue: this.addObj.flowValue,
							corpId: sessionStorage.getItem("corpId"),
							cooperationMode: sessionStorage.getItem("cooperationMode")
						}).then(res => {
							if (res.code === '0000') {
								let data = res.data
								this.$Notice.success({
									title: this.$t('address.Operationreminder'),
									desc: this.$t('common.Successful'),
								})
								this.submitFlag = false
								this.goPageFirst(1)
								this.newGaspackFlag = false
								this.cancelModal()
							}
						}).catch((error) => {
							this.submitFlag = false
							console.log(error)
						}).finally(() => {
							this.submitFlag = false
						})
					}
				})
			},
		}
	}
</script>

<style scoped="scoped">
	.search_head_i {
		margin-top: 20px;
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
	}
</style>