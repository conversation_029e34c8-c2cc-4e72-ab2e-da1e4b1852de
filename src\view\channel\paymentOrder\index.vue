<template>
  <Card>
    <div>
      <Form ref="form" :label-width="0" :model="form" inline>
        <FormItem>
          <Select filterable v-model="form.paymentStatus" clearable @on-change="handlePaymentStatusChange"
            :placeholder="$t('onlineOrder.choosePaymentStatusPlaceholder')" style="width: 300px ;margin-right: 10px;">
            <Option v-for="item in paymentStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Input v-model="form.thirdOrderNo" :placeholder="$t('onlineOrder.thirdOrderNoPlaceholder')" :clearable="true"
            style="width: 300px ;margin-right: 10px;" />
        </FormItem>
        <!-- <FormItem>
          <Input v-model="form.thirdTransactionNo" :placeholder="$t('onlineOrder.thirdTransactionNoPlaceholder')" :clearable="true" class="inputSty"
            style="width: 230px ;margin-right: 10px;" />
        </FormItem> -->
        <FormItem>
          <Input v-model="form.thirdMchorderNo" :placeholder="$t('onlineOrder.thirdMchorderNoPlaceholder')"
            :clearable="true" class="inputSty" style="width: 300px ;margin-right: 10px;" />
        </FormItem>
        <FormItem prop="date">
          <DatePicker format="yyyy-MM-dd" v-model="createDate" style="width: 300px ;" @on-change="handleDateChange"
            type="daterange" :placeholder="$t('onlineOrder.chooseCreateDate')"></DatePicker>
        </FormItem>
        <FormItem>
          <Button type="primary" icon="md-search" @click="getOrderList" :loading="searchloading"
            style="margin-left: 10px;" v-has="'search'">{{ this.$t("common.search") }}</Button>
        </FormItem>
      </Form>

    </div>
    <Table :columns="columns" :data="orderList" style="width:100%;" :loading="loading">

      <template slot-scope="{ row }" slot="action">
        <div style="display: flex;justify-content: flex-start; flex-wrap: wrap; padding-top: 10px;" v-if="corpId!=''">

          <div v-if="row.timmer != '已过期' && row.timmer">
            <div style="margin-bottom:10px">{{ $t("onlineOrder.payTxt01") }}<span style="color:red">
                {{ row.timmer }}</span> {{ $t("onlineOrder.payTxt02") }}</div>
            <Button v-has="'pay'" @click="pay(row)" type="primary" style="margin:0 10px 10px 0">{{
              $t("onlineOrder.payBtn") }}</Button>
            <Button v-has="'cancel'" @click="showCancelDialog(row)" type="error"
              style="margin:0 10px 10px 0">{{$t("common.cancel") }}</Button>
          </div>
          <Button v-has="'delete'" v-else-if="row.paymentStatus == 'EXPIRED' || row.paymentStatus == 'CLOSED'"
            @click="showDeleteDialog(row)" style="margin:0 0 10px 0;">{{ $t("common.del") }}</Button>
          <!-- <div v-else-if="row.timmer">{{row.timmer}}</div> -->
          <div v-else>
            <!-- {{ row.paymentStatus }} -->
          </div>

        </div>

      </template>
    </Table>
    <div style="margin-top: 50px;">
      <Page :total="total" :current.sync="currentPage" :page-size="pageSize" show-total show-elevator
        @on-change="goPage" />
    </div>

    <!-- 其他内容 -->
    <Modal :title="$t('onlineOrder.onlineModalTitle')" v-model="payModal" :mask-closable="false" :width="720">
      <PaymentComponent :orderType="payOrderDetail.orderType == 1 ? 'channelBilling' : 'deposit'"
        :corpId="payOrderDetail.corpId" :billId="payOrderDetail.productId" :showDeposit="false"
        :paySuccessInfo="paySuccessInfo" :payLoading="payStatus" v-if="payModal" :amount="amount.realIncome"
        :currencyCode="amount.currencyCode" @onlinePay="onlinePay" />
      <div slot="footer" style="width: 100%; display: flex; align-items: center; justify-content: flex-end;">
      </div>
    </Modal>
  </Card>
</template>

<script>
import { getOrderList, deleteOrder, closeOrder } from '@/api/onlinePay/index.js';
import { payOrder } from '@/api/onlinePay/pay.js';
import { formatTime } from '@/libs/tools.js';
import PaymentComponent from '@/components/onlinePayment/index'

export default {
  components: {
    PaymentComponent
  },
  data () {
    return {
      columns: [
        {
          title: this.$t("onlineOrder.orderUniqueId"), // Order ID
          key: 'orderId',
          align: 'center',
          minWidth: 200,
        },
        {
          title: this.$t("onlineOrder.corpId"), // Purchaser's Enterprise ID
          key: 'corpId',
          align: 'center',
          minWidth: 300,
        },
        {
          title: this.$t("onlineOrder.productId"), // Product ID or Bill ID
          key: 'productId',
          align: 'center',
          minWidth: 150,
        },
        {
          title: this.$t("onlineOrder.amount"), // Amount (in cents)
          key: 'amount',
          align: 'center',
          minWidth: 150,
          render: (h, params) => {
            const row = params.row;
            const text = row.amount / 100;
            return h('label', text);
          },
        },
        {
          title: this.$t("onlineOrder.currencyCode"), // Currency Type
          key: 'currencyCode',
          align: 'center',
          minWidth: 120,
        },
        {
          title: this.$t("onlineOrder.paymentMethod"), // Payment Method
          key: 'paymentMethod',
          align: 'center',
          minWidth: 150,
          render: (h, params) => {
            const row = params.row;
            const text = row.paymentMethod === "wechat" ?this.$t("onlineOrder.wechat") : row.paymentMethod === "alipay" ? this.$t("onlineOrder.alipay"):this.$t("onlineOrder.card");
            return h('label', text);
          },
        },
        {
          title: this.$t("onlineOrder.paymentStatus"), // Payment Status
          key: 'paymentStatus',
          align: 'center',
          minWidth: 150,
        },

        {
          title: this.$t("onlineOrder.exprieTime"), // Payment Expiry Time
          key: 'exprieTime',
          align: 'center',
          minWidth: 200,
          render: (h, params) => {
            const row = params.row;
            const text = formatTime(row.exprieTime, "Y-M-D h:m:s");
            return h('label', text);
          },
        },
        {
          title: this.$t("onlineOrder.thirdOrderNo"), // Third-Party Payment Order Identifier
          key: 'thirdOrderNo',
          align: 'center',
          minWidth: 200,
        },
        {
          title: this.$t("onlineOrder.thirdMchorderNo"), // Third-Party Payment Order Number
          key: 'thirdMchorderNo',
          align: 'center',
          minWidth: 200,
        },

        {
          title: this.$t("onlineOrder.createTime"), // Creation Time
          key: 'createTime',
          align: 'center',
          minWidth: 200,
          render: (h, params) => {
            const row = params.row;
            const text = formatTime(row.createTime, "Y-M-D h:m:s");
            return h('label', text);
          },
        },
        {
          title: this.$t('sys.opt'),
          slot: 'action',
          minWidth: 210,
          align: 'center',
          fixed: 'right',
        },
      ],
      orderTypeList: [{
        value: '1',
        label: this.$t('onlineOrder.orderTypeBill')
      },
      {
        value: '2',
        label: this.$t('onlineOrder.orderTypedeposit')
      }],
      paymentStatusList: [{
        value: 'PAYING',
        label: this.$t('onlineOrder.paying')
      },
      {
        value: 'SUCCESS',
        label: this.$t('onlineOrder.paySuccess')
      }, {
        value: 'EXPIRED',
        label: this.$t('onlineOrder.payExpired')
      }, {
        value: 'CLOSED',
        label: this.$t('onlineOrder.payclosed')
      }],
      options3: {

      },
      form: { // 搜索条件
        thirdOrderNo: '',//第三方支付订单标识
        thirdTransactionNo: '',//第三方支付交易流水号
        thirdMchorderNo: '',//第三方支付订单号
        paymentStatus: "",//支付状态

        createStartTime: "",
        createEndTime: ""
      },
      createDate: [],
      orderList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      cancelDialogVisible: false,
      deleteDialogVisible: false,
      selectedOrder: null, // 用于存储当前选中的订单
      searchloading: false,
      addedCountdownDisplay: new Set(),
      amount: {},
      payModal: false,
      currency: "",
      payOrderDetail: {},
      paySuccessInfo: {},
      payStatus:false,
      corpId:""
    };
  },
  mounted () {
    this.getOrderList();
    this.corpId=sessionStorage.getItem("corpId")&&sessionStorage.getItem("corpId")!="undefined"?sessionStorage.getItem("corpId"):""
  },
  beforeDestroy () {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    handlePaymentStatusChange (value) {
      // 当支付状态改变时，可以执行一些逻辑，比如重置日期范围等
      if (value) {
        this.createDate = []; // 清空日期范围
        this.form.createEndTime = "";
        this.form.createStartTime = ""
      }
    },
    handleDateChange (value) {
      console.log(value[0])
      // 处理日期范围变化
      this.form.createStartTime = value[0];
      this.form.createEndTime = value[1]
    },
    getOrderList () {
      this.loading = true;
      let data = {
        corpId: sessionStorage.getItem("corpId")&&sessionStorage.getItem("corpId")!="undefined"?sessionStorage.getItem("corpId"):this.$store.state.user.roleId,
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
        ...this.form // 将搜索条件添加到请求参数中
      };
      getOrderList(data).then(res => {
        res.data.records.forEach(item => {
          item.timmer = ''
        })
        this.orderList = res.data.records;

        this.startCountdown();
        this.total = res.data.total
        this.loading = false;
      }).catch(error => {
        console.error('获取订单列表失败', error);
        this.loading = false;
      });
    },
    goPage (val) {
      this.currentPage = val;
      this.getOrderList();
    },

    showCancelDialog (row) {
      this.selectedOrder = row; // 存储当前选中的订单
      this.$Modal.confirm({
        title: this.$t('common.cancel'),
        content:  this.$t('onlineOrder.closeOrderContent'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          closeOrder({ id: row.id }).then(res => {
            //提示删除成功
            if (res.code == "0000") {
              this.$Message.success(this.$t('onlineOrder.closeSuccess'),);
              //刷新列表
              this.getOrderList();
            }
          }).catch(error => {
            console.error('删除订单失败', error);
          });
        },
        onCancel: () => {
          // this.$Message.info('点击了取消');
        }
      });
    },
    showDeleteDialog (row) {
      this.selectedOrder = row; // 存储当前选中的订单
      this.$Modal.confirm({
        title: this.$t('common.del'),
        content: this.$t('onlineOrder.deleteOrderContent'),
        cancelText: this.$t('common.cancel'),
        onOk: () => {
          deleteOrder({ id: row.id }).then(res => {
            if (res.code == "0000") {
              //提示删除成功
              this.$Message.success(this.$t('onlineOrder.deleteSuccess'),);
              //刷新列表
              this.getOrderList();
            }

          }).catch(error => {
            console.error('删除订单失败', error);
          });
        },
        onCancel: () => {
          // this.$Message.info('点击了取消');
        }
      });
    },
    searchOrders () {
      this.currentPage = 1; // 重置页码为第一页
      this.getOrderList();
    },
    formatCountdown (deadline) {
      const now = new Date();
      const timeRemaining = deadline - now;

      if (timeRemaining <= 0) {
        return '已过期';
      }

      // 计算总小时数（可能跨天）
      const totalHours = Math.floor(timeRemaining / (1000 * 60 * 60));
      const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

      // 根据小时数构建返回字符串
      let formattedTime = '';
      if (totalHours > 0) {
        formattedTime += `${totalHours}时`;
      }
      formattedTime += `${minutes < 10 ? '0' : ''}${minutes}分${seconds < 10 ? '0' : ''}${seconds}秒`;

      // 确保分钟和秒数总是两位数（可选，但通常用于保持时间格式的一致性）
      return formattedTime;
    },
    startCountdown () {
      // 检查是否有任何订单尚未过期
      const hasActiveOrders = this.orderList.some(row => {
        const deadline = new Date(row.exprieTime);
        return this.formatCountdown(deadline) !== '已过期';
      });

      if (hasActiveOrders) {
        // 启动定时器，每秒更新一次订单倒计时
        this.timer = setInterval(() => {
          let allOrdersExpired = true;
          this.orderList.forEach(row => {
            if (row.paymentStatus == "PAYING") {
              const deadline = new Date(row.exprieTime);
              row.timmer = this.formatCountdown(deadline);
              // console.log(row.id,row.timmer)
              if (row.timmer !== '已过期') {
                allOrdersExpired = false;
              }else{
                row.paymentStatus="EXPIRED"
              }
            }

          });
          // 如果所有订单都已过期，则清除定时器
          if (allOrdersExpired) {
            clearInterval(this.timer);
            this.timer = null;
            console.log('所有订单都已过期，定时器已清除。');
          }
        }, 1000);
      } else {
        console.log('所有订单都已过期，不启动定时器。');
      }
    },
    pay (row) {
      this.payOrderDetail = row
      //根据弹窗需求传参，并请求支付接口
      console.log(row)

      this.amount.realIncome = row.amount / 100
      const text =
        row.currencyCode == "CNY" ?
          "156" :
          row.currencyCode == "USD" ?
            "840" :
            row.currencyCode == "HKD" ?
              "344" :
              "--";
      this.amount.currencyCode = text
      this.payModal = true
    },

    onlinePay (details) {
      if (this.payStatus) {
        console.log("请求中...")
        return;
      }

      this.payStatus = true

      console.log("支付信息：", details)

    
      //封装需要传的参数
      let createAndPayData = {
        payOrderNo:this.payOrderDetail.thirdOrderNo,
        paymentMethod: details.paymentMethod,
        adyenStateData: details.adyenStateData,
        orderType: this.payOrderDetail.orderType==1?"channelBilling":"deposit",
        billId:this.payOrderDetail.productId,
        language: localStorage.getItem("local"), // 根据需要设置语言
        corpId: sessionStorage.getItem("corpId"),
      }
      payOrder(createAndPayData).then(res => {
        console.log(res.data, "订购详情")
        if (res.code == '0000') {
          this.payStatus = false
          //通知子组件
          this.paySuccessInfo = res.data
          this.payModal = false
          this.payCont = res.data.payUrl
          console.log(res.data.redirectUrl);
          // if (res.data.redirectUrl) {
          //   window.location.href = res.data.redirectUrl
          // } else {
            document.querySelector("body").innerHTML = res.data.payUrl
            document.forms[0].submit();
          // }


        }
      }).catch(error => {
        console.error("请求失败:", error);
        // this.payModal = false
        this.payStatus = false
      });
    },
  },
};
</script>
<style scoped></style>
