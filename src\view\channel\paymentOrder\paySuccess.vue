<template>
  <div class="payment-page">
    <div class="payment-status">
      <div class="status-icon" :class="{ success: isSuccess, error: !isSuccess }">
        <span>!</span>
      </div>
      <div class="status-message">
        <!-- <p>{{ $t('paymentResultpageTexts.paymentSuccessful') }}</p> -->
        <p class="success-details">{{ $t('paymentResultpageTexts.successDetails') }}</p>
      
        <!-- <p class="success-reason">{{ $t('paymentResultpageTexts.thanksMessage') }}</p> -->
        
      </div>
    </div>
    <div class="button-group">
      <button class="btn btn-blue" @click="viewOrder">{{$t('paymentResultpageTexts.viewOrder')}}</button>
      <button class="btn" @click="goToHome">{{$t('paymentResultpageTexts.goToHome')}}</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PaymentPage',
  data() {
    return {
      isSuccess: true,
    };
  },
  methods: {
    viewOrder () {
      // 这里可以添加查看订单的逻辑，比如跳转到订单详情页面
      console.log('查看订单被点击');
      this.$router.push({
        path: '/paymentOrder/management',
      })
    },
    goToHome () {
      // 这里可以添加跳转到首页的逻辑，比如使用 vue-router 的 push 方法
      this.$router.push({
        path: '/',
      })
    },
  },
  mounted() {
      this.isSuccess = true;
  }
};
</script>

<style scoped>
.payment-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f0f8ff;
  text-align: center;
  font-family: Arial, sans-serif;
}

.payment-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.status-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.status-icon.success {
  background-color: #007bff;
}

.status-icon.error {
  background-color: red;
}

.status-icon span {
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.status-message p {
  margin: 5px 0;
}

.success-details {
  font-size: 16px;
  color: #333;
}

.error-details {
  font-size: 16px;
  color: #333;
}

.success-reason {
  font-size: 14px;
  color: #666;
}

.error-reason {
  font-size: 14px;
  color: #666;
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.btn {
  padding: 10px 20px;
  font-size: 16px;
  color: #007bff;
  border: 1px solid #007bff;
  background-color: #fff;
  border-radius: 5px;
  cursor: pointer;
}

.btn-blue {
  background-color: #007bff;
  color: white;
}
</style>
