<template>
	<!-- 库存管理 -->
	<Card >
		<div style="width: 100%; ">
			<!-- <div style="display: flex;">
				<span style="margin-top: 4px;font-weight:bold;">{{$t('stock.Card_status')}}:</span>&nbsp;&nbsp;
				<Select v-model="form.buttonType" style="width: 200px;text-align: left;margin: 0 10px;" :placeholder="$t('stock.chose_status')">
					<Option v-for="(type,typeIndex) in typeList" :value="type.value" :key="typeIndex">{{ type.label }}</Option>
				</Select>&nbsp;&nbsp;
				<span style="margin-top: 4px;font-weight:bold;">{{$t('stock.timeslot')}}:</span>&nbsp;&nbsp;
				<DatePicker v-model="time_slot" type="daterange" format="yyyy-MM-dd" placement="bottom-end":placeholder="$t('stock.chose_time')"
				 style="width: 200px;margin-right: 10px;" @on-change="handleDateChange" @on-clear="hanldeDateClear"></DatePicker>
				</Col>
				<Button v-has="'view'"  type="primary" icon="md-search"style="margin-left: 20px;" :loading="searchloading" @click="search()">{{$t('stock.search')}}</Button>
				<Button v-has="'export'" type="success" size="large" icon="ios-cloud-download-outline" style="margin-left: 20px;" @click="exportTable()">{{$t('stock.exporttb')}}</Button>
			</div> -->
			<!-- 表格 -->
			<Table :columns="columns12" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
				<template slot-scope="{ row }" slot="action">
					<Button v-has="'view'" type="warning" size="small" style="margin-right: 5px" :loading="detailsloading" @click="details(row)">{{$t('stock.details')}}</Button>
				</template>

			</Table>
			<!-- 分页 -->
			<div style="margin-top: 100px;">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
			<!-- 详情弹框 -->
			<Modal v-model="modal5" :title="Titlemessage" @on-ok="ok" @on-cancel="cancelModal" width="620px">
				<div class="search_head" style="font-weight:bold;">
					<span>MSISDN:</span>&nbsp;&nbsp;
					<span style="font-weight:bold;">{{more.msisdn}}</span>&nbsp;&nbsp;
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span style="font-weight:bold;">ICCID:</span>&nbsp;&nbsp;
					<span style="font-weight:bold;">{{more.iccid}}</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span style="font-weight:bold;">IMSI:</span>&nbsp;&nbsp;
					<span style="font-weight:bold;">{{more.imsi}}</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span style="font-weight:bold;">{{$t('stock.Code')}}</span>&nbsp;&nbsp;
					<span>{{more.pin2}}</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span>PUK:</span>&nbsp;&nbsp;
					<span>{{more.puk1}}</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span>Output File:</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span>ADM:</span>&nbsp;&nbsp;
					<span>{{more.fileNameAdm}}</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span>SDB:</span>&nbsp;&nbsp;
					<span>{{more.fileNameSdb}}</span>&nbsp;&nbsp;<br>
				</div>
			</Modal>
		</div>
	</Card>
</template>

<script>
	import{
		cardList,
		stockExport,
		detailBeta
	} from '@/api/channel.js'
	export default {
		data(){
			return {
				modal5:false,
				time_slot:'',
				taskId:'',
				searchBeginTime:'',
				searchEndTime:'',
				Titlemessage:this.$t("stock.details"),
				total:0,
				page:0,
				currentPage:1,
				loading:false,
				searchloading:false,
				detailsloading:false,
				form:{},
				typeList:[],
				columns12: [{
						title: 'IMSI',
						key: 'imsi',
						align: 'center'
					},
					{
						title: 'MSISDN',
						key: 'msisdn',
						align: 'center'
					},
					{
						title: 'ICCID',
						key: 'iccid',
						align: 'center'
					},
					{
						title: this.$t("stock.usedstate"),
						key: 'cardStatus',
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							const text = row.cardStatus == '1' ? this.$t("order.Normal") : row.cardStatus == '2' ? this.$t("order.Terminated") : row.cardStatus == '3' ? this.$t("order.Suspend")  : '';
							return h('label', text)
						}
					},
					{
						title: this.$t("stock.cardtype"),
						key: 'cardForm',
						align: 'center',
						render: (h, params) => {
							const row = params.row
							const text = row.cardForm === "1" ? this.$t("stock.PhysicalSIM") : row.cardForm === "2" ? this.$t("stock.eSIM") : row.cardForm === "3" ? this.$t("stock.TSIM") :
							 row.cardForm === "4" ? this.$t("support.imsi") :''
							return h('label', text)
						}

					},
					{
						title: this.$t("stock.action"),
						slot: 'action',
						align: 'center'
					}

				],
				data:[],
				more: {
				  msisdn: '123',
				  imsi: '1234',
				  iccid: '12345',
				  verCode: '234',
				  pin: '12',
				  puk: '23',
				  adm: 'test1.adm',
				  sdb: 'test2.adm'
				},
				rules:{

				}
			}
		},
		mounted(){
			this.goPageFirst(1)
		},
		methods:{
			goPageFirst(page){
				this.loading = true
				var _this = this
				var paymentChannel = JSON.parse(decodeURIComponent(this.$route.query.paymentChannel));
				let pageNumber = page
				let pageSize = 10
				let taskId =paymentChannel.taskId
				cardList({
					pageNumber,
					pageSize,
					taskId
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading=false
						this.page = page
						this.currentPage=page
						this.total = res.data.total
						this.data = res.data.record
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchloading=false
				})
			},
			details(row){
				this.detailsloading=true
				detailBeta({
					imsi:row.imsi
				}).then(res => {
					if (res.code == '0000') {
						this.modal5=true
						this.more = res.data
						this.detailsloading=false
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.detailsloading=false
				})
			},
			search(){
				this.goPageFirst(1)
				this.searchloading=true
			},
			goPage(page){
				this.goPageFirst(page)
			},
			// 导出
			exportTable(){
				stockExport('number').then((res) => {
				  const content = res.data
				  const fileName = '号码登记表格.txt' // 导出文件名
				  if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
				    const link = this.$refs.downloadLink // 创建a标签
				    let url = URL.createObjectURL(content)
				    link.download = fileName
				    link.href = url
				    link.click() // 执行下载
				    URL.revokeObjectURL(url) // 释放url
				  } else { // 其他浏览器
				    navigator.msSaveBlob(content, fileName)
				  }
				}).catch(() => this.downloading = false)
			},
			hanldeDateClear() {
				this.searchBeginTime = ''
				this.searchEndTime = ''
			},
			handleDateChange(dateArr) {
				let beginDate = this.time_slot[0] || ''
				let endDate = this.time_slot[1] || ''
				if (beginDate == '' || endDate == '') {
					return
				}
				[this.searchBeginTime, this.searchEndTime] = dateArr
			},
			ok(){

			},
			cancelModal(){

			}

		}


	}
</script>

<style>
	.search_head {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 20px;
	}
</style>
