<template>
	<!-- 库存管理 -->
	<Card >
		<div style="width: 100%;margin-top: 80px; margin: auto;">
			<div style="display: flex;">
				<span style="margin-top: 4px;font-weight:bold;">{{$t('stock.order_number')}}:</span>&nbsp;&nbsp;
				<Input v-model="taskName" :placeholder="$t('stock.input_number')" prop="showTitle" clearable
				 style="width: 200px" />&nbsp;&nbsp;
				<span style="margin-top: 4px;font-weight:bold;">{{$t('order.timeslot')}}:</span>&nbsp;&nbsp;
				<DatePicker v-model="time_slot" type="daterange" format="yyyy-MM-dd" placement="bottom-end":placeholder="$t('stock.chose_time')"
				 style="width: 200px;margin-right: 10px;" @on-change="handleDateChange" @on-clear="hanldeDateClear"></DatePicker>
				<Button v-has="'search'"  type="primary" icon="md-search" :loading="searchloading" @click="search()">{{$t('stock.search')}}</Button>
	           <!-- v-has="'showiccid'" -->
				<Button  style="margin-left: 20px;" type="warning"  @click="showiccid()">{{$t('stock.showiccid')}}</Button>
			</div>
			<!-- 表格 -->
			<Table :columns="columns12" :data="data" style="width:100%;margin-top: 40px;" :loading="loading" >
				<template slot-scope="{ row }" slot="taskName">
					<strong>{{ row.taskName }}</strong>
				</template>
				<template slot-scope="{ row }" slot="taskNum">
					<strong>{{ row.taskNum }}</strong>
				</template>
				<template slot-scope="{ row }" slot="createTime">
					<strong>{{ row.createTime }}</strong>
				</template>
				<template slot-scope="{ row }" slot="action">
					<Button v-has="'view'" type="warning" size="small" style="margin-right: 5px" @click="details(row)">{{$t('stock.details')}}</Button>
				</template>
				
			</Table>
			<!-- 分页 -->
			<div style=" margin-top: 100px;">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
			
			
		</div>
	</Card>
</template>

<script>
	import{
		stockList,
		searchcorpid
	} from '@/api/channel.js'
	export default {
		data(){
			return {
				taskName:'',
				time_slot:'',
				corpId:'',
				searchBeginTime:'',
				searchEndTime:'',
				total: 0,
				page:0,
				currentPage:1,
				loading:false,
				searchloading:false,
				form:{},
				columns12: [{
						title: this.$t("stock.order_number"),
						slot: 'taskName',
						align: 'center'
					},
					{
						title: this.$t("stock.card_number"),
						slot: 'taskNum',
						align: 'center'
					},
					{
						title: this.$t("stock.addtime"),
						slot: 'createTime',
						align: 'center'
					},
					{
						title: this.$t("stock.action"),
						slot: 'action',
						align: 'center'
					}
					
				],
				data:[],
				rules:{
					
				}
			}
		},
		mounted(){
			this.goPageFirst(1)
		},
		methods:{
			goPageFirst(page){
				this.loading = true
				var _this = this
				this.data=[]
				searchcorpid({
					userName:this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						let corpId=res.data
						this.corpId=corpId
						let pageNumber = page
						let pageSize = 10
						// 判断时间段是否是当天
						let startDate = this.searchBeginTime===""?null:this.searchBeginTime+' 00:00:00'
						let endDate = this.searchEndTime===""?null:this.searchEndTime+' 23:59:59'
						let taskName=this.taskName===""?null:this.taskName.toString()
						stockList({
							pageNumber,
							pageSize,
							startDate,
							endDate,
							corpId,
							taskName
						}).then(res => {
							if (res.code == '0000') {
								_this.loading = false
								this.searchloading=false
								this.page = page
								this.currentPage=page
								this.total = res.data.total
								this.data = res.data.record
							}
						}).catch((err) => {
							// console.error(err)
						}).finally(() => {
							this.loading = false
							this.searchloading=false
						})
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				})
			},
			details(row){
				this.$router.push({
					path: '/cardList',
					query: {
						paymentChannel: encodeURIComponent(JSON.stringify(row))
					}
				})
			},
			search(){
				this.goPageFirst(1)
				this.searchloading=true
			},
			showiccid(){
				this.$router.push({
					path: '/showiccid',
					query: {
						corpId: encodeURIComponent(this.corpId)
					}
				})
			},
			goPage(page){
				this.goPageFirst(page)
			},
			hanldeDateClear() {
				this.searchBeginTime = ''
				this.searchEndTime = ''
			},
			handleDateChange(dateArr) {
				let beginDate = this.time_slot[0] || ''
				let endDate = this.time_slot[1] || ''
				if (beginDate == '' || endDate == '') {
					return
				}
				[this.searchBeginTime, this.searchEndTime] = dateArr
			},
		}
		
		
	}
</script>

<style>
</style>
