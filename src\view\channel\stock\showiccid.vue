<template>
	<!-- 库存管理 -->
	<Card>
		<div style="width: 100%; ">
			<div style="display: flex;justify-content: flex-start;align-items: center;flex-wrap: wrap;">
				<span style="margin: 5px;font-weight:bold;">ICCID</span>&nbsp;&nbsp;
				<Input v-model.trim="iccid" :placeholder="$t('support.ICCIDenter')" prop="showTitle" clearable
					style="width: 200px;margin: 5px;" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<span style="margin: 5px;font-weight:bold;">{{$t('stock.attributableChannel')}}</span>&nbsp;&nbsp;
				<Select v-model="attributableChannel" filterable style="width: 200px;text-align: left;margin: 0 10px;"
					clearable>
					<Option v-for="item in attributableChannelList" :value="item.corpId" :key="item.corpId">{{ item.corpName }}</Option>
				</Select>&nbsp;&nbsp;
				<span style="margin: 5px;font-weight:bold;">{{$t('stock.availablePackages')}}</span>&nbsp;&nbsp;
				<Select v-model="isHasPackage" filterable style="width: 200px;text-align: left;margin: 5px 10px;" clearable>
					<Option :value="1">{{$t('order.yes')}}</Option>
					<Option :value="2">{{$t('order.no')}}</Option>
				</Select>&nbsp;&nbsp;
				<Button v-has="'view'" :disabled="cooperationMode == '3'" type="primary" icon="md-search" style="margin-left: 20px;"
					:loading="searchloading" @click="search()">{{$t('stock.search')}}</Button>
				<Button v-has="'export'" :disabled="cooperationMode == '3'" style="margin: 0 2px;margin-left: 20px;" icon="ios-cloud-download-outline"
					type="success" :loading="downloading" @click="exportFile">
					{{$t('stock.exporttb')}}
				</Button>
				<Button v-has="'transfer'" :disabled="cooperationMode == '2' || cooperationMode == '3'" style="margin: 0 2px;margin-left: 20px;"
					icon="ios-brush-outline" type="info" @click="transfer">
					{{$t('stock.transfer')}}
				</Button>
			</div>
			<!-- 表格 -->
			<Table :columns="columns12" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
				<template slot-scope="{ row }" slot="action">
					<Button v-has="'view'" type="warning" size="small" style="margin-right: 5px"
						@click="details(row)">{{$t('stock.details')}}</Button>
				</template>
			</Table>
			<!-- 分页 -->
			<div style="margin-top: 100px;">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
			<!-- 详情弹框 -->
			<Modal v-model="modal5" :title="Titlemessage" @on-ok="ok" @on-cancel="cancelModal" width="620px">
				<div class="search_head" style="font-weight:bold;">
					<span>MSISDN:</span>&nbsp;&nbsp;
					<span style="font-weight:bold;">{{more.msisdn}}</span>&nbsp;&nbsp;
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span style="font-weight:bold;">ICCID:</span>&nbsp;&nbsp;
					<span style="font-weight:bold;">{{more.iccid}}</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span style="font-weight:bold;">IMSI:</span>&nbsp;&nbsp;
					<span style="font-weight:bold;">{{more.imsi}}</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span style="font-weight:bold;">{{$t('stock.Code')}}</span>&nbsp;&nbsp;
					<span>{{more.pin2}}</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span>PUK:</span>&nbsp;&nbsp;
					<span>{{more.puk1}}</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span>Output File:</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span>ADM:</span>&nbsp;&nbsp;
					<span>{{more.fileNameAdm}}</span>&nbsp;&nbsp;<br>
				</div>
				<div class="search_head" style="font-weight:bold;">
					<span>SDB:</span>&nbsp;&nbsp;
					<span>{{more.fileNameSdb}}</span>&nbsp;&nbsp;<br>
				</div>
			</Modal>
			<!-- 导出提示 -->
			<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
				<div style="align-items: center;justify-content:center;display: flex;">
					<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
						<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
						<FormItem :label="$t('exportID')">
							<span style="width: 100px;">{{taskId}}</span>
						</FormItem>
						<FormItem :label="$t('exportFlie')">
							<span>{{taskName}}</span>
						</FormItem>
						<span style="text-align: left;">{{$t('downloadResult')}}</span>
					</Form>
				</div>

				<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
					<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
					<Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
				</div>
			</Modal>
			<!-- 划拨子渠道商 -->
			<Modal :title="$t('stock.transfer')" v-model="subChannelModal" :footer-hide="true" :mask-closable="false"
				@on-cancel="cancelModal" width="500px">
				<div style="padding: 0 10px;">
					<Form ref="editObj" :model="editObj" :label-width="80" :rules="ruleEditValidate"
						style="display: flex; flex-wrap: wrap;">
						<FormItem :label="$t('stock.subChannel')" prop="subChannel">
							<Select v-model="editObj.subChannel" filterable style="width: 200px">
								<Option v-for="item in attributableChannelList2"
									:value="item.corpId + '-' +item.parentCorpId" :key="item.corpId">{{item.corpName}}
								</Option>
							</Select>
						</FormItem>
						<FormItem prop="file">
							<div style="display: flex;">
								<Upload v-model="editObj.file" :action="uploadUrl" :on-success="fileSuccess"
									:on-error="handleError" :before-upload="handleBeforeUpload" ref="upload"
									:on-progress="fileUploading">
									<Button icon="ios-cloud-upload-outline">{{$t('flow.UploadICCID')}}</Button>
								</Upload>
								<div style="width: 250px; margin-left: 30px;">
									<Button type="info" icon="ios-download" @click="downloadFile">{{$t('support.downloadfile')}}</Button>
								</div>
							</div>
							<ul class="ivu-upload-list" v-if="file">
								<li class="ivu-upload-list-file ivu-upload-list-file-finish">
									<span>
										<Icon type="ios-folder" />{{file.name}}
									</span>
									<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style=""
										@click="removeFile"></i>
								</li>
							</ul>
						</FormItem>
					</Form>
					<div style="text-align: center; margin-top: 20px">
						<Button v-has="'submit'" type="primary" :loading="submitLoading" @click="submit()"
							style="margin-right: 20px;">{{$t('support.submit')}}</Button>
						<Button style="margin-left: 8px" @click="cancelModal">{{$t('support.back')}}</Button>
					</div>
				</div>
			</Modal>
			<Table :columns="SubmodelColumns" :data="SubmodelData" ref="modelTable" v-show="false"></Table>
		</div>
	</Card>
</template>

<script>
	import {
		detailCharlie,
		stockExport,
		detailBeta,
		searchcorpid,
		getAttributableChannelList,
		transferChannel
	} from '@/api/channel.js'
	export default {
		data() {
			// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (this.uploadList && this.uploadList.length === 0) {
					callback(new Error(this.$t('support.pleaseUploadFile')))
				} else {
					callback()
				}
			}
			return {
				cooperationMode: '', //合作模式
				iccid: '',
				attributableChannel: '', //归属渠道商
				isHasPackage: '', //是否有可用套餐
				modal5: false,
				time_slot: '',
				searchBeginTime: '',
				searchEndTime: '',
				Titlemessage: this.$t("stock.details"),
				total: 0,
				page: 0,
				currentPage: 1,
				loading: false,
				searchloading: false,
				detailsloading: false,
				downloading: false,
				submitLoading: false,
				exportModal: false,
				subChannelModal: false, //划拨弹窗
				taskId: '', //任务Id
				taskName: '', //任务名称
				form: {},
				attributableChannelList: [],
				attributableChannelList2: [],
				legth: '',
				columns12: [{
						title: 'IMSI',
						key: 'imsi',
						align: 'center',
						minWidth: 120,
					},
					{
						title: 'MSISDN',
						key: 'msisdn',
						align: 'center',
						minWidth: 120,
					},
					{
						title: 'ICCID',
						key: 'iccid',
						minWidth: 120,
						align: 'center'
					},
					{
						title: this.$t("stock.usedstate"),
						key: 'cardStatus',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							const text = row.cardStatus == '1' ? this.$t("order.Normal") : row.cardStatus == '2' ?
								this.$t("order.Terminated") : row.cardStatus == '3' ? this.$t("order.Suspend") :
								'';
							return h('label', text)
						}
					},
					{
						title: this.$t("stock.cardtype"),
						key: 'cardForm',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row
							const text = row.cardForm === "1" ? this.$t("stock.PhysicalSIM") : row.cardForm ===
								"2" ? this.$t("stock.eSIM") : row.cardForm === "3" ? this.$t("stock.TSIM") :
								row.cardForm === "4" ? this.$t("support.imsi") : ''
							return h('label', text)
						}

					},
					{
						title: this.$t("stock.Storagetime"),
						key: 'createTime',
						align: 'center',
						minWidth: 120,
					},
					{
						title: this.$t("stock.attributableChannel"),
						key: 'corpName',
						align: 'center',
						minWidth: 120,
					},
					{
						title: this.$t("stock.action"),
						slot: 'action',
						align: 'center',
						minWidth: 120,
					}

				],
				data: [],
				corpId: '',
				more: {
					msisdn: '123',
					imsi: '1234',
					iccid: '12345',
					verCode: '234',
					pin: '12',
					puk: '23',
					adm: 'test1.adm',
					sdb: 'test2.adm'
				},
				rules: {

				},
				uploadUrl: '',
				file: null,
				editObj: {
					subChannel: '', //子渠道商
					file: '', //文件
				},
				ruleEditValidate: {
					subChannel: [{
						required: true,
						type: "string",
						message: this.$t('stock.subChannelProviderEmpty'),
					}],
					file: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
				},
				uploadList: [],
				SubmodelData: [{
					'ICCID': '********',
				}, ],
				SubmodelColumns: [{
						title: 'ICCID',
						key: 'ICCID'
					}, // 列名根据需要添加
				],
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			if (this.cooperationMode != '3') {
				this.getAttributableChannelList()
				this.goPageFirst(1)
			}
			
		},
		methods: {
			goPageFirst(page) {
				this.loading = true
				var _this = this
				let pageNumber = page
				let pageSize = 10
				let iccid = this.iccid
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						let corpId= res.data
						let chooseCorpId = this.attributableChannel
						let cooperationMode = this.cooperationMode
						let isHasPackage = this.isHasPackage
						detailCharlie({
							pageNumber,
							pageSize,
							corpId,
							iccid,
							cooperationMode,
							chooseCorpId,
							isHasPackage
						}).then(res => {
							if (res.code == '0000') {
								_this.loading = false
								this.searchloading = false
								this.page = page
								this.currentPage = page
								this.total = res.data.total
								this.data = res.data.record
							}
						}).catch((err) => {
							console.error(err)
						}).finally(() => {
							this.loading = false
							this.searchloading = false
						})
					}
				}).catch((err) => {
					// console.error(err)
				}).finally(() => {})

			},
			details(row) {
				this.detailsloading = true
				detailBeta({
					imsi: row.imsi
				}).then(res => {
					if (res.code == '0000') {
						this.modal5 = true
						this.more = res.data
						this.detailsloading = false
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.detailsloading = false
				})
			},
			search() {
				this.goPageFirst(1)
				this.searchloading = true
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			// 导出
			exportFile() {
				this.downloading = true
				stockExport({
					corpId: this.corpId,
					userId: this.corpId,
					cooperationMode: this.cooperationMode,
					chooseCorpId: this.attributableChannel
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.taskId
					this.taskName = res.data.taskName
					this.downloading = false
				}).catch(() => this.downloading = false)
			},
			//划拨
			transfer() {
				this.subChannelModal = true
				this.getAttributableChannelList2()
			},
			// 文件上传
			fileSuccess(response, file, fileList) {
				this.message = this.$t("support.downTemplateFilelAndUpload")
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: this.$t("common.Error"),
						desc: this.$t("support.uploadFailed")
					});
				}, 3000);
			},
			handleBeforeUpload(file, fileList) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: this.$t("buymeal.fileformat"),
						desc: this.$t("support.files") + file.name + this.$t("buymeal.incorrect")
					})
				} else {
					this.file = file
					this.uploadList = fileList
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = this.$t("support.fileUploadedAndProgressDisappears")
			},
			removeFile() {
				this.file = ''
			},
			// 模板下载
			downloadFile: function() {
				this.$refs.modelTable.exportCsv({
					filename: this.$t('showiccid_mngr'),
					columns: this.SubmodelColumns,
					data: this.SubmodelData
				})
			},
			// 子渠道商划拨 提交
			submit() {
				this.$refs["editObj"].validate((valid) => {
					if (valid) {
						this.submitLoading = true
						let func;
						//文件上传 
						let formData = new FormData()
						formData.append('file', this.file)
						formData.append('parentCorpId', this.editObj.subChannel.split("-")[1])
						formData.append('corpId', this.editObj.subChannel.split("-")[0])
						transferChannel(formData).then((res) => {
							if (res.code === "0000") {
								this.$Notice.success({
									title: this.$t("address.Operationreminder"),
									desc: this.$t("common.Successful"),
								});
								this.submitLoading = false
								this.file = null
								this.subChannelModal = false;
								this.goPageFirst(1)
								this.uploadList = []
								this.$refs["editObj"].resetFields();
							}
						})
					}
				});
			},
			hanldeDateClear() {
				this.searchBeginTime = ''
				this.searchEndTime = ''
			},
			handleDateChange(dateArr) {
				let beginDate = this.time_slot[0] || ''
				let endDate = this.time_slot[1] || ''
				if (beginDate == '' || endDate == '') {
					return
				}
				[this.searchBeginTime, this.searchEndTime] = dateArr
			},
			ok() {

			},
			cancelModal() {
				this.modal5 = false
				this.exportModal = false
				this.subChannelModal = false
				this.$refs["editObj"].resetFields();
				this.file= ''
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
						corpId: encodeURIComponent(this.corpId)
					}
				})
				this.exportModal = false
			},

			/**
			 * ----------------------------------初始化信息-----------------------------------
			 */

			// 归属渠道商列表
			getAttributableChannelList: function() {
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					this.corpId = res.data
					getAttributableChannelList({
						corpId: this.corpId,
						selfContain: true
					}).then(res => {
						if (res.code === '0000') {
							if (this.cooperationMode == 1) {
								this.attributableChannelList = res.data
							} else {
								//a2z模式没有子渠道商
								let list = [{
									corpName: res.data[0].corpName,
									corpId: res.data[0].corpId
								}]
								this.attributableChannelList = list
							}
							
							if (this.attributableChannelList.length == 1) {
								this.attributableChannel = this.attributableChannelList[0].corpId
							}
						}
					}).catch((err) => {
						console.log(err)
					})
				})
			},
			getAttributableChannelList2: function() {
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					this.corpId = res.data
					getAttributableChannelList({
						corpId: this.corpId,
						selfContain: false
					}).then(res => {
						if (res.code === '0000') {
							this.attributableChannelList2 = res.data
						}
					}).catch((err) => {
						console.log(err)
					})
				})
			},
		}
	}
</script>

<style>
	.search_head {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 20px;
	}
</style>