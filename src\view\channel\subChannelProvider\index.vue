<template>
	<!-- 子渠道商管理 -->
	<Card>
		<!-- 搜索条件 -->
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">{{$t('support.subChannelName')}}</span>
				<Input v-model="subChannelName" :clearable="true" :placeholder="$t('support.input')"
					style="width: 300px;" />
			</div>
			<div class="search_box">
				<Button v-has="'search'" :disabled="cooperationMode =='2' || cooperationMode == '3'" type="info" icon="md-search"
					:loading="searchloading" @click="search()"
					style="margin-right: 20px;">{{$t('common.search')}}</Button>
				<Button v-has="'add'" :disabled="cooperationMode == '2' || cooperationMode == '3'" type="primary" icon="md-add"
					style="margin-left: 10px;" @click="add">{{$t('support.create')}}</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table ref="selection" :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading"
			@on-selection-change="handleRowChange" @on-select-cancel="cancelSigle" @on-select-all-cancel="cancelAll">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'info'" type="info" ghost style="margin-right: 10px;"
					@click="showdetail(row)">{{$t('stock.details')}}</Button>
				<Button v-has="'update'" type="success" ghost style="margin-right: 10px;"
					@click="update(row)">{{$t('support.edit2')}}</Button>
				<Button v-has="'delete'" type="warning" ghost style="margin-right: 10px;"
					@click="delItem(row)">{{$t('address.Delete')}}</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 新增/编辑弹窗 -->
		<Modal :title="subChannelTitle" v-model="subChannelModal" :footer-hide="true" :mask-closable="false"
			@on-cancel="cancelModal" width="780px">
			<div style="padding: 0 10px;">
				<Form ref="editObj" :model="editObj" :label-width="110" :rules="ruleEditValidate"
					style="display: flex; flex-wrap: wrap;">
					<FormItem :label="$t('support.subChannelName')" prop="subChannelName">
						<Input v-model="editObj.subChannelName" maxlength="50" :placeholder="$t('support.input')"
							clearable style="width: 250px"></Input>
					</FormItem>
					<FormItem :label="$t('support.contactEmail')" prop="email">
						<Input v-model="editObj.email" :placeholder="$t('address.input_mailbox')"
							clearable style="width: 250px"></Input>
					</FormItem>
					<FormItem :label="$t('support.purchasePackage')" prop="file" style="width: 500px;" :rules="subChannelFlag == 'Update' ? [{required: false}] :
						ruleEditValidate.file">
						<div style="display: flex;">
							<Upload v-model="editObj.file" :action="uploadUrl" :on-success="fileSuccess"
								:on-error="handleError" :before-upload="handleBeforeUpload" ref="upload"
								:on-progress="fileUploading">
								<Button icon="ios-cloud-upload-outline">{{$t('support.clickToUpload')}}</Button>
							</Upload>
							<div style="width: 500px;margin-left: 50px;">
								<Button type="primary" icon="ios-download"
									@click="downloadFile">{{$t('support.downloadfile')}}</Button>
							</div>
						</div>
						<ul class="ivu-upload-list" v-if="file">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />{{file.name}}
								</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style=""
									@click="removeFile"></i>
							</li>
						</ul>
					</FormItem>
					<FormItem :label="$t('support.packageProfitMargin')" prop="profitMargin">
						<Input type="number" v-model="editObj.profitMargin" :placeholder="$t('support.input')"
							clearable style="width: 250px">
						<span slot="append">%</span>
						</Input>
					</FormItem>
					<FormItem :label="$t('support.fuelPackProfitMargin')" prop="refuelProfitMargin">
						<Input type="number" v-model="editObj.refuelProfitMargin" :placeholder="$t('support.input')"
							clearable style="width: 250px">
						<span slot="append">%</span>
						</Input>
					</FormItem>
					<FormItem :label="$t('support.totalAmount')" prop="totalDeposit">
						<Input v-model="editObj.totalDeposit" :placeholder="$t('support.input')"
							clearable style="width: 250px">
						<span slot="append">{{$t('yuan')}}</span>
						</Input>
					</FormItem>
					<FormItem :label="$t('support.accountPermissions')" prop="accountPermissions">
						<Select v-model="editObj.accountPermissions" filterable :placeholder="$t('support.selectCharacter')"
							clearable :disabled="subChannelFlag == 'Update'" style="width: 250px">
							<Option v-for="item in accountPermissionsList" :value="item.id" :key="item.id">{{item.roleName}}</Option>
						</Select>
					</FormItem>
				</Form>
				<div style="text-align: center">
					<Button v-has="'submit'" type="primary" :loading="submitLoading" @click="submit()"
						style="margin-right: 20px;">{{$t('support.submit')}}</Button>
					<Button style="margin-left: 8px" @click="cancelModal">{{$t('support.back')}}</Button>
				</div>
			</div>
		</Modal>
		<!-- 详情弹窗 -->
		<Modal :title="subChannelTitleInfo" v-model="subChannelModalInfo" :footer-hide="true" :mask-closable="false"
			@on-cancel="cancelModalInfo" width="800px">
			<div style="padding: 0 10px;">
				<Form ref="editObjInfo" :model="editObjInfo" :label-width="120"
					style="display: flex; flex-wrap: wrap;">
					<FormItem :label="$t('support.subChannelName')" prop="subChannelName">
						<Input v-model="editObjInfo.subChannelName" maxlength="50"
							readonly :placeholder="$t('support.input')"
							style="width: 250px"></Input>
					</FormItem>
					<FormItem :label="$t('support.contactEmail')" prop="email">
						<Input v-model="editObjInfo.email" readonly :placeholder="$t('address.input_mailbox')"
							style="width: 250px"></Input>
					</FormItem>
					<FormItem :label="$t('support.purchasePackage')" style="width: 370px;display: felx; justify-content: center;">
						<Button icon="ios-albums" type="info"
							@click="showPackageModal">{{$t('flow.Clickview')}}</Button>
					</FormItem>
					<FormItem :label="$t('support.packageProfitMargin')" prop="profitMargin">
						<Input type="number" v-model="editObjInfo.profitMargin"
							readonly :placeholder="$t('support.input')"
							style="width: 250px">
						<span slot="append">%</span>
						</Input>
					</FormItem>
					<FormItem :label="$t('support.fuelPackProfitMargin')" prop="refuelProfitMargin">
						<Input type="number" v-model="editObjInfo.refuelProfitMargin"
							readonly :placeholder="$t('support.input')"
							style="width: 250px">
						<span slot="append">%</span>
						</Input>
					</FormItem>
					<FormItem :label="$t('support.totalAmount')" prop="totalDeposit">
						<Input v-model="editObjInfo.totalDeposit"
							readonly :placeholder="$t('support.input')"
							style="width: 250px">
						<span slot="append">{{$t('yuan')}}</span>
						</Input>
					</FormItem>
					<FormItem :label="$t('support.accountPermissions')" prop="accountPermissions">
						<Select v-model="editObjInfo.accountPermissions" filterable
							disabled :placeholder="$t('support.selectCharacter')"
							style="width: 250px">
							<Option v-for="item in accountPermissionsList" :value="item.id" :key="item.id">{{item.roleName}}</Option>
						</Select>
					</FormItem>
					<FormItem :label="$t('support.channelAppSecret')" prop="appSecret">
						<Input v-model="editObjInfo.appSecret" readonly
							style="width: 450px"></Input>
					</FormItem>
					<FormItem :label="$t('support.channelAppKey')" prop="appKey">
						<Input v-model="editObjInfo.appKey" readonly
							style="width: 450px"></Input>
					</FormItem>
				</Form>
				<div style="text-align: center">
					<Button @click="cancelModalInfo">{{$t('support.back')}}</Button>
				</div>
			</div>
		</Modal>
		<!-- 子渠道商列表 -->
		<Table :columns="SubmodelColumns" :data="SubmodelData" ref="modelTable" v-show="false"></Table>
		<!-- 详情 — 可购买套餐 -->
		<Modal :title="$t('support.purchasePackage')" v-model="purchasePackageModal" :mask-closable="false"
			@on-cancel="close" width="600px">
			<purchase-packge v-if="purchasePackageModal" :CCorpId="CCorpId" :local="purchasePackageLocal" />
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="close">{{$t('support.back')}}</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import purchasePackge from './modal/purchasePackge.vue'
	import {
		subChannelProviderList,
		delItem,
		addSubChannel,
		editSubChannel,
		getAccountPermissionsList,
	} from "@/api/subChannelProvider";
	import {
		getRoleList
	} from "@/api/system/privilege";
	export default {
		components: {
			purchasePackge
		},
		data() {
			const validateEmail = (rule, value, callback) => {
				if (!value || value.indexOf("@") == -1) {
					callback(new Error(this.$t('address.emailaddress')));
				} else {
					callback();
				}
			};
			// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (!this.file) {
					callback(new Error(this.$t('support.pleaseUploadFile')))
				} else {
					callback()
				}
			}
			return {
				cooperationMode: '', //合作模式
				total: 0,
				currentPage: 1,
				page: 0,
				subChannelName: "", //子渠道商名称
				loading: false,
				searchloading: false, //查询加载
				submitLoading: false,
				selection: [], //多选
				selectionIds: [], //多选ids
				iccidList: [], //翻页勾选
				accountPermissionsList: [], //账户权限列表
				uploadList: [],
				data: [],
				CCorpId: '',
				columns: [{
					type: "selection",
					width: 60,
					align: "center",
				}, {
					title: this.$t('support.subChannelName'),
					key: 'subChannelName',
					minWidth: 120,
					align: 'center'
				}, {
					title: this.$t('order.addtime'),
					key: 'createTime',
					minWidth: 120,
					align: 'center'
				}, {
					title: this.$t('support.totalAmount'),
					key: 'totalDeposit',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
						const row = params.row
						var text = row.totalDeposit / 100
						return h('label', text)
					}
				}, {
					title: this.$t('deposit.deposit_money'),
					key: 'deposit',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
						const row = params.row
						var text = row.deposit / 100
						return h('label', text)
					}
				}, {
					title: this.$t('usedLimit'),
					key: 'usedAmount',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
						const row = params.row
						var text = (row.totalDeposit - row.deposit) / 100
						return h('label', text)
					}
				}, {
					title: this.$t('common.manageOperate'),
					slot: 'action',
					minWidth: 200,
					align: 'center',
					fixed: 'right'
				}, ],
				subChannelTitle: '',
				subChannelTitleInfo: '',
				subChannelFlag: '',
				subChannelModal: false, //子渠道商弹窗
				subChannelModalInfo: false,
				purchasePackageModal: false, //可购买套餐详情弹窗
				purchasePackageLocal: '', //语言
				editObj: {
					subChannelName: '', //子渠道商名称
					file: '', //文件
					email: '', //联系人邮箱
					profitMargin: '', //套餐利润率
					refuelProfitMargin: '', //加油包利润率
					totalDeposit: '', //总额度
					accountPermissions: '', //账号权限
				},
				editObjInfo: {
					subChannelName: '', //子渠道商名称
					email: '', //联系人邮箱
					profitMargin: '', //套餐利润率
					refuelProfitMargin: '', //加油包利润率
					totalDeposit: '', //总额度
					accountPermissions: '', //账号权限
				},
				uploadUrl: '',
				file: null,
				ruleEditValidate: {
					subChannelName: [{
						required: true,
						type: "string",
						message: this.$t('support.subChannelEmpty'),
					}, {
						pattern: /^[^\s]+(\s+[^\s]+)*$/,
						trigger: "blur",
						message: this.$t('flow.kongge'),
					}, {
            // 新的校验规则，检查是否包含&符号
            validator: (rule, value, callback) => {
              if (value && value.includes('&')) {
                callback(new Error(this.$t('support.subChannelAmpersand')));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },],
					email: [{
							required: true,
							message: this.$t('support.contactEmailEmpty'),
						},
						{
							validator: validateEmail,
							trigger: "blur",
						},
						{
							required: true,
							type: "email",
							trigger: "blur",
							message: this.$t('support.EmailFormatError'),
						},
					],
					file: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
					profitMargin: [{
						pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,
						message: this.$t('flow.negative'),
					}],
					refuelProfitMargin: [{
						required: true,
						message: this.$t('support.fuelPackProfitMarginEmpty'),
					},{
						pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,
						message: this.$t('flow.negative'),
					}],
					totalDeposit: [{
						required: true,
						message: this.$t('support.totalAmountEmpty'),
					}, {
						pattern: /^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/,
						message: this.$t('flow.Positivenumber'),
					}],
					accountPermissions: [{
						required: true,
						message: this.$t('support.accountPermissionsEmpty'),
					}],
				},
				SubmodelColumns: [{
						title: "Package ID",
						key: "Package ID"
					}, // 列名根据需要添加
					{
						title: "Price",
						key: "Price"
					}, // 列名根据需要添加
				],
				SubmodelData: [{
					'Package ID': '********',
					'Price': '',
				}, ],
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			this.corpId = sessionStorage.getItem("corpId")
			if (this.cooperationMode == '1') {
				this.goPageFirst(1)
				this.getAccountPermissionsList()
			}
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				subChannelProviderList({
					parentCorpId: this.corpId,
					pageSize: 10,
					pageNum: page,
					subChannelName: this.subChannelName ? this.subChannelName : "", //子渠道商名称
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						if (res.data) {
							var data = res.data;
							let List = []
							// 循环遍历data
							data.map((value, index) => {
								List.push(value)
							})
							//回显
							this.iccidList.forEach(item => {
								List.forEach(element => {
									if (element.iccid == item.iccid) {
										this.$set(element, '_checked', true)
									}
								})
							})
							this.data = List;
							this.total = res.count;
						} else {
							//搜不到数据列表置空
							this.data = []
						}
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					let flag = true;
					this.iccidList.map((item, index) => {
						if (value.iccid === item.iccid) {
							flag = false
						}
					});
					//判断重复
					if (flag) {
						this.iccidList.push(value);
					}

				});
			},
			// 取消单选
			cancelSigle(selection, row) {
				this.iccidList.forEach((value, index) => {
					if (value.iccid === row.iccid) {
						this.iccidList.splice(index, 1);
					}
				})
			},
			// 取消全选
			cancelAll(selection, row) {
				this.iccidList = []
			},
			// 查看详情
			showdetail(row) {
				this.subChannelFlag = 'Info'
				this.subChannelTitleInfo = this.$t('support.subChannelDetails')
				this.editObjInfo = Object.assign({}, row);
				this.editObjInfo.totalDeposit = this.editObjInfo.totalDeposit / 100
				this.CCorpId = row.ccorpId
				this.subChannelModalInfo = true


			},
			// 编辑
			update(row) {
				this.subChannelFlag = 'Update'
				this.subChannelTitle = this.$t('support.editSubChannel')
				this.editObj = Object.assign({}, row);
				this.editObj.totalDeposit = this.editObj.totalDeposit / 100
				this.CCorpId = row.ccorpId
				this.subChannelModal = true
			},
			// 新增
			add() {
				this.subChannelFlag = 'Add'
				this.subChannelTitle = this.$t('support.addSubChannel')
				this.subChannelModal = true
			},
			// 删除
			delItem(row) {
				let corpId = row.ccorpId
				this.$Modal.confirm({
					title: this.$t('flow.Confirmdelete'),
					onOk: () => {
						delItem(corpId).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t("address.Operationreminder"),
									desc: this.$t("common.Successful")
								})
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			// 关闭弹窗
			cancelModal() {
				this.file = ''
				this.$refs.editObj.resetFields()
				this.subChannelModal = false
			},
			cancelModalInfo() {
				this.file = ''
				this.subChannelModalInfo = false
			},
			// 文件上传
			fileSuccess(response, file, fileList) {
				this.message = this.$t("support.downTemplateFilelAndUpload")
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: this.$t("common.Error"),
						desc: this.$t("support.uploadFailed")
					});
				}, 3000);
			},
			handleBeforeUpload(file, fileList) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: this.$t("buymeal.fileformat"),
						desc: this.$t("support.files") + file.name + this.$t("buymeal.incorrect")
					})
				} else {
					this.file = file
					this.uploadList = fileList
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = this.$t("support.fileUploadedAndProgressDisappears")
			},
			removeFile() {
				this.file = ''
			},
			// 模板下载
			downloadFile: function() {
				this.$refs.modelTable.exportCsv({
					filename: "Sub-channel available package list",
					columns: this.SubmodelColumns,
					data: this.SubmodelData
				})
			},
			// 提交
			submit() {
				this.$refs["editObj"].validate((valid) => {
					if (valid) {
						this.submitLoading = true
						let func;
						//文件上传
						let formData = new FormData()
						formData.append('cCorpId', this.CCorpId)
						formData.append('pCorpId', this.corpId)
						formData.append('subChannelName', this.editObj.subChannelName)
						formData.append('email', this.editObj.email)
						this.editObj.profitMargin || this.editObj.profitMargin === 0 ? formData.append('profitMargin', this.editObj.profitMargin) : undefined
						formData.append('refuelProfitMargin', this.editObj.refuelProfitMargin)
						formData.append('deposit', this.editObj.totalDeposit)
						formData.append('accountPermissions', this.editObj.accountPermissions)
						if (this.subChannelFlag === "Add") {
							formData.append('file', this.file)
							func = addSubChannel;
						} else {
							if (this.file) {
								formData.append('file', this.file)
							}
							func = editSubChannel;
						}
						func(formData).then((res) => {
							if (res.code === "0000") {
								this.$Notice.success({
									title: this.$t("address.Operationreminder"),
									desc: this.$t("common.Successful"),
								});
								this.submitLoading = false
								this.file = null
								this.subChannelModal = false;
								this.uploadList = []
								this.goPageFirst(1)
								this.$refs["editObj"].resetFields();
							}
						}).catch((err) => {
							console.error(err)
							this.submitLoading = false
						}).finally(() => {
							this.submitLoading = false
						})
					}
				});
			},
			// 可购买套餐列表
			showPackageModal() {
				this.purchasePackageModal = true
			},
			close() {
				this.purchasePackageModal = false
			},

			/**
			 * ----------------------------------初始化信息-----------------------------------
			 */

			// 账户权限列表
			getAccountPermissionsList: function() {
				getRoleList({
						corpId: this.corpId ? this.corpId : undefined
					})
					.then((res) => {
						if (res.code === "0000") {
							this.accountPermissionsList = res.data;
						} else {
							throw res;
						}
					})
					.catch((err) => {
						console.log(err);
					})
					.finally(() => {});
			},
		},
	}
</script>

<style scoped="scoped">
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		width: 400px;
		padding: 0 15px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 170px;
	}

	.editBox {
		display: felx;
	}

	::v-deep input::-webkit-outer-spin-button,
	::v-deep input::-webkit-inner-spin-button {
		-webkit-appearance: none !important;
	}

	::v-deep input[type="number"] {
		-moz-appearance: textfield;
	}
</style>
