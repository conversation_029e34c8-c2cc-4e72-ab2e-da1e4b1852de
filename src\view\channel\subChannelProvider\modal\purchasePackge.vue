<!-- 可购买套餐详情 -->
<template>
	<div style="padding: 0 16px">
		<div>
			<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading" max-height="600"></Table>
		</div>
		<div class="table-botton" style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" :page-size="pageSize" show-total show-elevator
				@on-change="loadRecords" />
		</div>
	</div>
</template>
<script>
	import {
		mealList
	} from '@/api/channel.js';
	export default {
		props: {
			CCorpId: {
			  type: String,
			  default: "",
			},
		},
		data() {
			return {
				localName: '',
				currentPage: 1,
				loading: false,
				total: 0,
				pageSize: 10,
				corpId: '',
				cooperationMode: '', //合作模式
				columns: [{
						title: this.$t('deposit.mealname'),
						key: 'packageName',
						align: 'center',
						minWidth: 100,
						render: (h, params) => {
							const row = params.row
							var text = this.$i18n.locale==='zh-CN' ? row.packageName :
							 this.$i18n.locale==='en-US' ? row.packageNameEn: ''
							return h('label', text)
						}
					},
					{
						title: this.$t('support.pakageId'),
						key: 'packageId',
						align: 'center',
						minWidth: 100,
					},
					{
						title: this.$t('fuelPack.price'),
						key: 'packagePrice',
						align: 'center',
						minWidth: 100,
						render: (h, params) => {
							const row = params.row
							var text = row.packagePrice/100
							return h('label', text)
						}
					},
				],
				tableData: []
			}
		},
		methods: {
			loadRecords(page) {
				this.loading = true
				let pageNumber = page
				let pageSize = 10
				let cooperationMode = this.cooperationMode
				let corpId = this.CCorpId
				mealList({
					corpId,
					pageNumber,
					pageSize,
					cooperationMode
				}).then(res => {
					if (res.code == '0000') {
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.data.total
						this.tableData = res.data.record
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
				})
			},
		},
		mounted: function() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			this.loadRecords(1);
		}
	};
</script>


<style scoped>

</style>