<template>
	<!-- 套餐详情 -->
	<Card>
		<div style="width: 100%;margin-top: 50px; margin: auto;">
			<div style="display: flex;">
				<span style="margin-top: 4px;font-weight:bold;">{{$t('support.mealname')}}:</span>&nbsp;&nbsp;
				<Input v-model="mealname" :placeholder="$t('support.input_mealname')" prop="showTitle" clearable style="width: 200px" />&nbsp;&nbsp;
				<!-- <span style="margin-top: 4px;font-weight:bold;">{{$t('support.cardtype')}}:</span>&nbsp;&nbsp; -->
				<!-- <Select v-model="status" style="width: 200px;text-align: left;margin: 0 10px;" :placeholder="$t('support.chose_type')">&nbsp;&nbsp;
					<Option v-for="(type,typeIndex) in typeList" :value="type.value" :key="typeIndex">{{ type.label }}</Option>
				</Select>&nbsp;&nbsp; -->
				<span style="margin-top: 4px;font-weight:bold;">{{$t('support.timeslot')}}:</span>&nbsp;&nbsp;
				<DatePicker v-model="time_slot" type="daterange" format="yyyy-MM-dd" placement="bottom-end" :placeholder="$t('support.chose_time')"
				 style="width: 200px;margin-right: 10px;" @on-change="handleDateChange" @on-clear="hanldeDateClear"></DatePicker>
				<Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search()">{{$t('support.search')}}</Button>
			</div>
			<!-- 表格 -->
			<Table :columns="columns12" :data="data" style="width:100%;margin-top: 50px;" :loading="loading">
				<template slot-scope="{ row }" slot="action" style="display: flex;">
					<div style="display: flex;">
						<div>
							<Button v-has="'view'" type="warning" size="small" style="margin-right: 5px" @click="details(row)">{{$t('support.used_details')}}</Button>
						</div>
						<div v-if="row.status=='正常'">
							<Button v-has="'view'" type="success" size="small" style="margin-right: 5px" @click="activation(row,0)"
							 :disabled="true">{{$t('support.activation')}}</Button>
						</div>
						<div v-else>
							<Button v-has="'view'" type="success" size="small" style="margin-right: 5px" @click="activation(row,1)">{{$t('support.activation')}}</Button>
						</div>
						<!-- <div v-if="row.status=='冻结'">
							<Button v-has="'view'" type="error" size="small" @click="frozen(row,0)" :disabled="true">{{$t('support.frozen')}}</Button>
						</div>
						<div v-else>
							<Button v-has="'view'" type="error" size="small" @click="frozen(row,1)">{{$t('support.frozen')}}</Button>
						</div> -->
					</div>
				</template>
			</Table>
			<!-- 分页 -->
			<div style="margin-top: 100px;">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>

		</div>
	</Card>
</template>

<script>
	import {
		supmealList,
		doActivation,
		recoveryPackage,
		searchcorpid
	} from '@/api/channel.js'
	export default {
		data() {
			return {
				mealname: '',
				status: '',
				total: 0,
				currentPage: 1,
				loading: false,
				searchloading: false,
				time_slot: '',
				searchBeginTime: '',
				searchEndTime: '',
				form: {},
				typeList: [],
				columns12: [{
						title: this.$t("support.mealname"),
						key: 'packageName',
						align: 'center'
					},
					{
						title: this.$t("support.isused"),
						key: 'used',
						align: 'center',
						render: (h, params) => {
							const row = params.row
							const text =row.packageStatus=='3' ?  "是": row.packageStatus==='' ? '':"否"
							return h('label', text)
						}
					},
					{
						title: this.$t("support.meal_time"),
						key: 'expireTime',
						align: 'center'
					},
					{
						title: this.$t("support.Activation_state"),
						key: 'state',
						align: 'center',
						render: (h, params) => {
						  const row = params.row;
						  // [1.待使用 2.使用中 3.已使用 4.(未激活)已过期]
						  // const text = row.packageStatus != '1' ? row.packageStatus == '-1' ? '已使用' : '未使用' : '使用中';
						  var text = "";
						  switch (row.packageStatus) {
						    case "1":
						      text = "待激活";
						      break;
						    case "2":
						      text = "已激活";
						      break;
						    case "3":
						      text = "已使用";
						      break;
						      // case "4":
						      //   text = "已激活待计费";
						      //   break;
						    case "5":
						      text = "已过期";
						      break;
						    case "6":
						      text = "激活中";
						      break;
						    default:
						      text = "未知";
						  }
						  return h('label', text);
						},
					},
					// {
					// 	title: this.$t("support.used_flow"),
					// 	key: 'flow',
					// 	align: 'center'
					// },
					{
						title: this.$t("support.used_cycle"),
						key: 'usetime',
						align: 'center',
						render: (h, params) => {
							const row = params.row
							const keepPeriod = row.keepPeriod
							const text = row.periodUnit =='1'  ? keepPeriod+"小时" : row.periodUnit =='2' ? keepPeriod+"日" :row.periodUnit =='3' ? keepPeriod+"月" :row.periodUnit =='4' ? keepPeriod+"年" :''
							return h('label', text)
						}
					},

					// {
					// 	title: this.$t("support.used_cycle"),
					// 	key: 'cycle',
					// 	align: 'center'
					// },
					{
						title: this.$t("support.action"),
						slot: 'action',
						align: 'center',
						width: '250'
					}
				],
				data: [{
						meal: '套餐1',
						used: '是',
						time: '2021-03-15',
						state: '正常',
						flow: '1G',
						cycle: '每月',
						usetime: '一月'
					},
					{
						meal: '套餐1',
						used: '是',
						time: '2021-03-15',
						state: '未激活',
						flow: '2G',
						cycle: '每月',
						usetime: '一周'
					},
				],
				rules: {

				}
			}
		},
		mounted() {
			this.goPageFirst(1)
		},
		methods: {
			goPageFirst(page) {
				this.loading = true
				var _this = this
				searchcorpid({
					userName:this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
				let corpId=res.data
				let pageNumber = page
				let pageSize = 10
				let startTime = this.searchBeginTime === "" ? null : this.searchBeginTime
				let endTime = this.searchEndTime === "" ? null : this.searchEndTime
				var paymentChannel = JSON.parse(decodeURIComponent(this.$route.query.paymentChannel));
				let imsi = paymentChannel.imsi
				let mealname = this.mealname === "" ? null : this.mealname
				let status = this.status === "" ? null : this.status
				supmealList({
					pageNumber,
					pageSize,
					startTime,
					endTime,
					corpId,
					imsi,
					mealname,
					status
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.total = res.data.totalCount
						this.data = res.data.records
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchloading = false
				})
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				})
			},
			search() {
				this.goPageFirst(1)
				this.searchloading = true
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			// 使用详情
			details() {
				var paymentChannel = JSON.parse(decodeURIComponent(this.$route.query.paymentChannel));
				let imsi = paymentChannel.imsi
				this.$router.push({
					path: '/useList',
					query: {
						paymentChannel: encodeURIComponent(JSON.stringify(paymentChannel))
					}
				})
			},
			// 激活
			activation() {
				this.$Modal.warning({
					title: "是否激活该项?",
					onOk: () => {
						let PackageVO = {
							"dataBundleId": "string",
							"hImsi": "string",
							"iccid": "string",
							"mcc": "string",
							"msisdn": "string"
						}
						doActivation(PackageVO).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提醒',
									desc: '激活成功！'
								})
								this.goPageFirst(0)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.loading = false
						})
					}
				});
			},
			// 冻结
			frozen() {
				this.$Modal.warning({
					title: "是否冻结该项?",
					onOk: () => {
						recoveryPackage({
							"packageUniqueId": "string"
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提醒',
									desc: '冻结成功！'
								})
								this.goPageFirst(0)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.loading = false
						})
					}
				});
			},
			hanldeDateClear() {
				this.searchBeginTime = ''
				this.searchEndTime = ''
			},
			handleDateChange(dateArr) {
				let beginDate = this.time_slot[0] || ''
				let endDate = this.time_slot[1] || ''
				if (beginDate == '' || endDate == '') {
					return
				}
				[this.searchBeginTime, this.searchEndTime] = dateArr
			},


		}


	}
</script>

<style>
</style>
