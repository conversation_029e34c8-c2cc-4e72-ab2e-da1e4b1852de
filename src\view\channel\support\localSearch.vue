<template>
  <!-- 位置更新记录 -->
  <div>
    <Card>
      <!-- <div class="search_head">
        <Input v-model="phoneNum" placeholder="请输入手机号码..." clearable style="width: 200px ;margin-right: 10px;" />
        <Input v-model="IMSI" placeholder="请输入IMSI..." clearable style="width: 200px ;margin-right: 10px;" />
        <Button v-has="'search'" type="primary" icon="md-search" @click="searchByCondition()"
          style="margin-right: 10px;">搜索</Button>
      </div> -->
      <div style="margin-top:20px">
        <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading"></Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="total" :current.sync="page" show-total show-elevator @on-change="goPage" />
      </div>
    </Card>
  </div>
</template>

<script>
  import {
    getUpdateRecords
  } from '@/api/server/card'

  export default {

    data() {
      return {
        phoneNum: '',
        IMSI: '',
        columns: [{
            title: '上报时间',
            key: 'time',
            align: 'center'
          },
          {
            title: '上报位置',
            key: 'local',
            align: 'center'
          },
          {
            title: '卡类型',
            key: 'activeType',
            align: 'center'
          },

        ],

        tableData: [{
            local: '四川新希望大厦',
            activeType: '自动激活',
            time: '2021-03-16 12:12:12',
          },
          {
            local: '四川新希望大厦',
            activeType: '自动激活',
            time: '2021-03-16 12:12:12',
          }
        ],
        loading: false,
        page: 1,
        total: 0
      }
    },
    computed: {

    },
    methods: {
      getUpdateRecords: function(page) {
        this.loading = true;
        this.page = page;
        getUpdateRecords({
          phoneNum: this.phoneNum,
          IMSI: this.IMSI,
          pageNumber: page,
          pageSize: 10,
        }).then(res => {
          if (res && res.code == '0000') {
            this.tableData = res.data;
            this.total = res.total;
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err);
        }).finally(() => {
          this.loading = false;
        })
      },

      searchByCondition: function() {
        this.getUpdateRecords(1);
      },
      // 分页跳转
      goPage: function(page) {
        this.getUpdateRecords(page);
      }
    },
    mounted() {
      this.getUpdateRecords(1);
    },
    watch: {}
  }
</script>
<style>

</style>
