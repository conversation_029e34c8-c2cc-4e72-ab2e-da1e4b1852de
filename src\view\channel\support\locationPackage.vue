<template>
  <!--  账户列表 当前位置套餐  -->
  <div>
    <Card>
	 <div class="search_head_i">
			<div class="search_box">
			  <Button style="margin: 0 4px" @click="reBack">
				<Icon type="ios-arrow-back" />&nbsp;{{$t('support.back')}}
			  </Button>
			</div>
	</div>
      <span style="font-weight:bold;">{{$t('support.position')}}：{{localName}}</span>
      </br>
      <div style="margin-top:20px">
        <Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading" max-height="500">
          <template slot-scope="{ row, index }" slot="action">
            <Button v-has="'active'" type="success" size="small" @click="activation(row)" v-if="row.packageStatus=='1'">{{$t('support.activation')}}</Button>
            <Button v-has="'active'" type="success" size="small" disabled v-else-if="row.packageStatus=='2'">{{$t('support.activated')}}</Button>
            <Button v-has="'active'" type="success" size="small" disabled v-else-if="row.packageStatus=='3'">{{$t('support.Used')}}</Button>
            <Button v-has="'active'" type="success" size="small" disabled v-else-if="row.packageStatus=='4'">{{$t('support.Activatedpending')}}</Button>
            <Button v-has="'active'" type="success" size="small" disabled v-else-if="row.packageStatus=='5'">{{$t('support.Expired')}}</Button>
            <Button v-has="'active'" type="success" size="small" @click="activation(row)" v-else-if="row.packageStatus=='6'">{{$t('support.Activating')}}</Button>
            <Button v-has="'active'" type="success" size="small" disabled v-else>" "</Button>
          </template>
        </Table>
      </div>
      <div style="margin-top:15px">
        <Page :total="total" :page-size="pageSize" :current.sync="pageP" show-total show-elevator @on-change="getLocalMeals" />
      </div>
    </Card>
  </div>
</template>

<script>
  import {
    getLocalMeals,
    //当前位置套餐激活
  } from '@/api/server/card';
  import {
  	searchcorpid,
	doActivation
  } from '@/api/channel';
import cooperationModeVue from '../../../components/main/components/cooperation-mode/cooperation-mode.vue';
  const math = require('mathjs');
  export default {
    data() {
      return {
        localName: '',
        loading: false,
        pageP: 1,
        total: 0,
        pageSize: 10,
        columns: [],
        tableData: [],
		corpId:''
      }
    },
    methods: {
      //页面初始化
      init: function() {
        this.columns = [{
            title: this.$t('support.mealname'),
            key: 'packageName',
            align: 'center',
			minWidth: 150,
			render: (h, params) => {
				const row = params.row
				var text = this.$i18n.locale==='zh-CN' ? row.packageName:this.$i18n.locale==='en-US' ? row.packageNameEn: ''
				return h('label',{
					style:{
						'word-break':'break-word',
					}
				},text)
			}
          },
          {
            title: this.$t('support.Activation_state'),
            key: 'packageStatus',
			minWidth: 130,
            align: 'center',
            render: (h, params) => {
              const row = params.row;
              var text = "";
              switch (row.packageStatus) {
                case "1":
                  text = this.$t('support.Unuse');
                  break;
                case "2":
                  text = this.$t('support.activated');
                  break;
                case "3":
                  text = this.$t('support.Used');
                  break;
                case "4":
                  text = this.$t('support.Activatedpending');
                  break;
                case "5":
                  text = this.$t('support.Expired');
                  break;
                case "6":
                  text = this.$t('support.Activating');
                  break;
                default:
                  text = " ";
              }
              return h('label', text);
            },
          },
          {
            title: this.$t('support.Activationmethod'),
            key: 'activeType',
            align: 'center',
			minWidth: 130,
            render: (h, params) => {
              const row = params.row;
              const text = row.activeType == '1' ? this.$t('support.Automatic') : row.activeType == '2' ? this.$t('support.Manual') : ' ';
              return h('label', text)
            }
          },
          {
            title: this.$t('support.Periodtype'),
            key: 'periodUnit',
            align: 'center',
			minWidth: 130,
            render: (h, params) => {
              const row = params.row;
              const text = row.periodUnit === '1' ? this.$t("buymeal.hour") : (row.periodUnit === '2' ? this.$t("buymeal.day") :
                (row.periodUnit === '3' ? this.$t("buymeal.month") : (row.periodUnit === '4' ? this.$t("buymeal.year") : ' ')));
              return h('label', text);
            },
          },
          {
            title: this.$t('support.Continuouscycle'),
            key: 'keepPeriod',
			minWidth: 130,
            align: 'center'
          },
          // {
          //   title: '套餐有效期',
          //   key: 'expireTime',
          //   align: 'center'
          // },
          {
            title: this.$t('support.meal_time'),
            key: 'expireTime',
			minWidth: 150,
            align: 'center'
          },
          {
            title: this.$t('deposit.mealprice'),
            key: 'price',
            align: 'center',
			minWidth: 120,
            // render: (h, params) => {
            //   const row = params.row;
            //   const text =  parseFloat(math.divide(math.bignumber(row.price), 100).toFixed(2)).toString();
            //   return h('label', text);
            // }
          },
          {
            title: this.$t('deposit.currency'),
            key: 'currencyCode',
            align: 'center',
			minWidth: 120,
            render: (h, params) => {
              const row = params.row;
              const text = row.currencyCode === '156' ? this.$t('support.CNY') : (row.currencyCode === '840' ? this.$t('support.USD') :
                (row.currencyCode === '344' ? this.$t('support.HKD') : ' '));
              return h('label', text);
            },
          },
          {
            title: this.$t('support.Activatepackage'),
            slot: 'action',
			minWidth: 120,
            align: 'center'
          }
        ];
		this.localName = this.$i18n.locale==='zh-CN' ?  this.$route.query.localName:this.$i18n.locale==='en-US' ? this.$route.query.localNameEn:'';
		searchcorpid({
			userName: this.$store.state.user.userName,
		}).then(res => {
			this.corpId = res.data
			//加载页面
			 this.getLocalMeals(1);
		}).catch((err) => {
			console.error(err)
		}).finally(() => {})
		// 保存MSISDN，ICCID，IMSI
		localStorage.setItem("backmsisdn",this.$route.query.backmsisdn)
		localStorage.setItem("backiccid",this.$route.query.backiccid)
		localStorage.setItem("backimsi",this.$route.query.backimsi)
      },
      getLocalMeals: function(page) {
        var mcc = this.$route.query.mcc;
        if (mcc == null || mcc == '') {
          this.$Notice.error({
            title: this.$t("address.Operationreminder"),
            desc: this.$t("support.obtained")
          });
          return false;
        };
        this.loading = true;
        this.pageP = page;
        getLocalMeals({
          imsi: this.$route.query.imsi,
          mcc: mcc,
		  corpId:this.corpId,
          pageNumber: page,
          pageSize: this.pageSize,
        }).then(res => {
          if (res && res.code == '0000') {
            var data = res.data;
            this.tableData = data.records;
            this.total = data.totalCount;
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
      //当前位置套餐激活
      activation: function(row) {
        doActivation({
          dataBundleId: row.packageId, //待激活套餐
          hImsi: this.$route.query.imsi,
          mcc: this.$route.query.mcc,
        }).then(res => {
          if (res && res.code == '0000') {
            this.$Notice.success({
              title: this.$t("address.Operationreminder"),
              desc: this.$t("common.Successful")
            })
            this.getLocalMeals(this.pageP);
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {})
      },
      //查询当前位置套餐列表
      searchByCondition: function() {
        this.getLocalMeals(1);
      },
		reBack(){
		  this.$router.push({
			name: 'support_mngr'
		  })
		}
    },
    mounted: function() {
		let cooperationMode = sessionStorage.getItem("cooperationMode")
		if (cooperationMode != '3') {
			this.init();
		}
    },
    watch: {

    }
  };
</script>
<style>
  .search_head {
    width: 100%;
    display: flex;
    text-align: center;
    align-items: center;
    justify-content: flex-start;
  }

  .search-btn {
    width: 100px !important;
  }
</style>
