<template>
	<div style="padding: 0 16px;">
		<Form :model="esimInfo" label-position="left" :label-width="105" style="font-size: 16px;">
		    <FormItem :label="$t('support.smDpAddress')">
		        <Input readonly v-model="esimInfo.smdpAddress" :title="esimInfo.smdpAddress"></Input>
		    </FormItem>
		    <FormItem :label="$t('support.activationCode')">
		        <Input readonly v-model="esimInfo.matchingId" :title="esimInfo.matchingId"></Input>
		    </FormItem>
		    <FormItem :label="$t('support.esimStatus')">
		        <Input readonly v-model="esimInfo.state" :title="esimInfo.state"></Input>
		    </FormItem>
			<FormItem :label="$t('support.eid')">
			    <Input readonly v-model="esimInfo.eid" :title="esimInfo.eid"></Input>
			</FormItem>
			<FormItem :label="$t('support.installationTime')">
			    <Input readonly v-model="esimInfo.firstDownloadDate" :title="esimInfo.firstDownloadDate"></Input>
			</FormItem>
			<FormItem :label="$t('support.installationEquipment')">
			    <Input readonly v-model="esimInfo.device" :title="esimInfo.device"></Input>
			</FormItem>
			<FormItem :label="$t('support.instalAmount')">
			    <Input readonly v-model="esimInfo.downloadCounter" :title="esimInfo.downloadCounter"></Input>
			</FormItem>
			<FormItem :label="$t('support.updateTime')">
			    <Input readonly v-model="esimInfo.updateDate" :title="esimInfo.updateDate"></Input>
			</FormItem>
		</Form>
	</div>
</template>

<script>
	import {
		getEsimInfo
	} from '@/api/aqCode';
	export default {
		props: ['esimIccid'],
		data() {
			return {
				esimInfo: {
					smdpAddress: '',
					matchingId: '',
					state: '',
					eid: '',
					firstDownloadDate: '',
					device: '',
					downloadCounter: '',
					updateDate: '',
				},
			}
		},
		methods: {
			//获取esim信息
			goPageFirst: function() {
				var _this = this
				getEsimInfo({
					iccid: this.esimIccid
				}).then(res => {
					if (res.code == '0000') {
						this.esimInfo = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
		},
		mounted() {
			this.goPageFirst()
		}
	}		
</script>

<style scoped="scoped">
	.ivu-form-item {
		margin-bottom: 10px;
	}
</style>
