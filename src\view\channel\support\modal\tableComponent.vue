<template>
 <div>
	<table class="mailTable" :style="styleObject" >
	  <tr v-for="index in rowCount" >
	    <td style="background-color: #F8F8F9;border: 1px solid #DCDEE2;
	   height: 40px;
        width: 200px;
        padding-left: 10px;
        color:#333;" >{{tableData[index*2-2].key}}</td>
	    <td style="border: 1px solid #DCDEE2;
        height: 40px;
        width: 200px;
        padding-left: 10px;
        color:#333;" >{{tableData[index*2-2].value}}</td>
	    <td style="background-color: #F8F8F9;border: 1px solid #DCDEE2;
        height: 40px;
        width: 200px;
        padding-left: 10px;
        color:#333;" >{{tableData[index*2-1] !== undefined ? tableData[index*2-1].key : ''}}</td>
	    <td style="border: 1px solid #DCDEE2;
        height: 40px;
        width: 200px;
        padding-left: 10px;
        color:#333;" align="center" valign="middle" v-if="tableData[index*2-1].value===0">
	      <Button ghost type="warning"  @click="showView">{{$t('support.view')}}</Button>
      </td>
      <td style="border: 1px solid #DCDEE2;
        height: 40px;
        width: 200px;
        padding-left: 10px;
        color:#333;"  v-else>{{tableData[index*2-1] !== undefined ? tableData[index*2-1].value : ''}}</td>
	  </tr>
	</table>
  <Table v-if="realNameInfoData && realNameInfoData.length>0" ref="selection" :columns="columns" :data="realNameInfoData" :ellipsis="true" style="margin-top: 20px; width: 930px;">
    <template slot-scope="{ row, index }" slot="action">
      <Button type="warning" ghost size="small" style="margin-right: 20px" @click="showView(row)"
        >{{$t('support.view')}}</Button>
    </template>
  </Table>
	<Modal :title="$t('support.registration')" v-model="Modal" :mask-closable="false" @on-cancel="cancelModal" width="500px">
	 	<Form>
			<FormItem :label="$t('support.NameChinese')">
				<span>{{form.nameCh}}</span>
			</FormItem>
			<FormItem :label="$t('support.NameEnglish')">
				<span>{{form.name}}</span>
			</FormItem>
			<FormItem :label="$t('support.IDnumber')">
				<span>{{form.certificatesId}}</span>
			</FormItem>
			<FormItem :label="$t('support.IDtype')">
				<span>{{form.certificatesType}}</span>
			</FormItem>
			<FormItem :label="$t('support.Issuingcountry')">
				<span>{{form.passportCountry}}</span>
			</FormItem>
			<FormItem v-if="flag" :label="$t('support.errorDesc')">
				<span>{{form.errorDesc}}</span>
			</FormItem>
    </Form>
		<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
	 		<Button @click="cancelModal">{{$t('support.back')}}</Button>
	 	</div>
	</Modal>
 </div>

</template>

<script>
	import {
		getRealinfo
	} from '@/api/server/card'
export default {
  data() {
    return {
      styleObject: {},
      form:{},
      Modal:false,
      s_showByRow: true,
      flag: false,
      columns: [{
      		title: this.$t('support.Complete'),
      		key: "authStatus",
      		align: "left",
      		minWidth: 180,
      		tooltip: true,
          render: (h, params) => {
          	var row = params.row;
            var color = (row.useStatus==='2'&& row.authStatus === "3") ? '#2b85e4' :
              (row.useStatus==='2'&& row.authStatus === "5") ? '#00aa00' :
            	(row.useStatus==='1'&& row.authStatus === "1" && row.isRepeat === "1") ? '#ff0000' :
              (row.useStatus==='1'&& row.authStatus === "1" && row.isRepeat === "2") ? '#69e457' :
              (row.useStatus==='1'&& row.authStatus === "2") ? '#e47a49' :
              (row.useStatus==='3'&& row.authStatus === "3") ? '#dd74e4' :
              (row.useStatus==='3'&& row.authStatus === "5") ? '#24cbe4' :
            	(row.useStatus==='3'&& row.authStatus === "4") ? '#7009e4' :
              (row.realnameId) ? '#e4b809' : '';

          	var text = (row.useStatus==='2'&& row.authStatus === "3") ? this.$t('support.Approved'):
      				(row.useStatus==='2'&& row.authStatus === "5") ? this.$t('support.IDexpired') :
      				(row.useStatus==='1'&& row.authStatus === "1" && row.isRepeat === "1") ? this.$t('support.WaitingUser') :
      				(row.useStatus==='1'&& row.authStatus === "1" && row.isRepeat === "2") ? this.$t('support.AuthFailed') :
      				(row.useStatus==='1'&& row.authStatus === "2") ? this.$t('support.process') :
      				(row.useStatus==='3'&& row.authStatus === "3") ? this.$t('support.Approved') :
      				(row.useStatus==='3'&& row.authStatus === "5") ? this.$t('support.IDexpired') :
      				(row.useStatus==='3'&& row.authStatus === "4") ? this.$t('support.Rejected') :
      				(row.realnameId) ? this.$t('support.NotCertified') : this.$t('support.NoCertification')

          	return h('label', {
          		style: {
          			color: color
          		}
          	}, text)
          },
      	},
        {
        	title: this.$t('support.registrationrules'),
        	key: "ruleName",
        	align: "left",
        	minWidth: 170,
        	tooltip: true
        },
      	{
      		title: this.$t('support.registrationcountry'),
          key: "realCountry",
      		align: "left",
      		minWidth: 170,
      		tooltip: true,
          render: (h, params) => {
            const row = params.row;
            let lastTent = "..."

            let manyCountry = [...new Set(this.realNameInfoData[params.index].realCountry)]
            let countryName;
            let countryName1;
            if (manyCountry && manyCountry.length === 1) {
              countryName = this.$i18n.locale==='zh-CN' ? manyCountry[0].countryCN.toString() : this.$i18n.locale==='en-US' ? manyCountry[0].countryEN.toString(): ''
            } else if (manyCountry && manyCountry.length > 1) {
              countryName = this.$i18n.locale==='zh-CN' ? manyCountry[0].countryCN.toString() : this.$i18n.locale==='en-US' ? manyCountry[0].countryEN.toString(): ''
              countryName1 = this.$i18n.locale==='zh-CN' ? manyCountry[1].countryCN.toString() : this.$i18n.locale==='en-US' ? manyCountry[1].countryEN.toString(): ''
            }

            if (!manyCountry || manyCountry.length === 0) {
              return h('span', '');
            }
            if (manyCountry.length === 1) {
              return h('span', countryName);
            }

            if (manyCountry.length === 2) {
              // 直接显示两个元素，不需要 Tooltip
              return h('div', [
                h('div', countryName),
                h('div', countryName1),
              ]);
            }
            // 当有超过两个元素时，使用 Tooltip 显示前两个元素，并在 Tooltip 中显示所有元素
            return h('div', [
              h('Tooltip', {
                props: {
                  placement: 'bottom',
                  transfer: true,
                },
                style: {
                  cursor: 'pointer',
                },
              }, [
                h('span', {
                  style: {
                    display: 'block',
                  },
                }, countryName),
                h('span', {}, countryName1),
                h('div', {}, lastTent),
                h('ul', {
                  slot: 'content',
                  style: {
                    listStyleType: 'none',
                    whiteSpace: 'normal',
                    wordBreak: 'break-all' ,//超出隐藏
                  },
                },
                  manyCountry.map(item => { // 从第三个元素开始映射
                    let itemCountryName = this.$i18n.locale==='zh-CN' ? item.countryCN.toString() : this.$i18n.locale==='en-US' ? item.countryEN.toString(): ''
                    return h('li', itemCountryName);
                  })),
                ]),
              ]);
          }
      	},
        {
        	title: this.$t('support.validityPeriod'),
        	key: "deadline",
        	align: "left",
        	minWidth: 210,
        	tooltip: true,
        	render: (h, params) => {
        		var row = params.row;
            var text = row.deadline==='1' ? this.$t('support.longTerm') : row.deadline ==='2' ? this.$t('support.singlePackageEffective') : ""
            return h('label', text)
        	},
        },
        {
        	title: this.$t('support.registration'),
          slot: "action",
          align: "left",
        	width: 170,
        },
      ],
    };
  },
  props: ['tableData', 'realNameInfoData', 'tableStyle', 'showByRow'],
  computed: {
    rowCount: function() {
      return Math.ceil(this.tableData.length/2);
    }
  },
  created() {
    this.styleObject = this.tableStyle;
    if(this.showByRow !== undefined){
      this.s_showByRow = this.showByRow;
    }
  },
  methods: {
    cancelModal(){
      this.Modal=false
    },
    showView(row){
      this.Modal=true
      this.form = JSON.parse(JSON.stringify(row))
      this.form.certificatesType = row.certificatesType === '1' ? this.$t('support.Passport') : row.certificatesType === '2' ? this.$t('support.Permit') :
      	row.certificatesType === '3' ? this.$t('support.HKIdentityCard') : row.certificatesType === '4' ? this.$t('support.MacauIdentityCard') : ""
      if (row.useStatus==='1'&& row.authStatus === "2") {
        // 认证中不展示错误描述
        this.flag = true
        this.form.errorDesc= row.errorDesc === '0' ? this.$t('support.picturesAreNotSatisfied') : row.errorDesc === '1' ? this.$t('support.nameIsInconsistent') : this.ShowList.errorDesc === '2' ? this.$t('support.certificateHasExpired') :
        	row.errorDesc === '3' ? this.$t('support.IDIsInconsistent') : row.errorDesc === '4' ? this.$t('support.sixteenyYearsOld') : ""
      } else {
        this.flag = false
      }
    },
  },
}
</script>
<style>
　　.mailTable, .mailTable tr, .mailTable tr td{ border:1px solid #E6EAEE; }
　　.mailTable{ font-size: 12px; color: #71787E; }
　　.mailTable tr td{ border:1px solid #E6EAEE; width: 150px; height: 35px; line-height: 35px; box-sizing: border-box; padding: 0 3px; }
　  .column {
	  background: #00FF00;
      color: #00FF00; }
</style>
