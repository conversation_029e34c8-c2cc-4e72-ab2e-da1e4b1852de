<!-- 套餐详情 -->
<style scoped>

</style>
<template>
  <div>
    <div>
      <Form ref="detailsObj" :model="detailsObj" :label-width="100">
        <Row>
          <Col span="8">
          <FormItem label="套餐名称">
            <Input v-model="detailsObj.packageName" readonly></Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="周期类型">
            <Select v-model="detailsObj.periodUnit" disabled>
              <Option v-for="item in periodUnitList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="持续周期">
            <Input v-model="detailsObj.keepPeroid" readonly></Input>
          </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
          <FormItem label="套餐过期时间">
            <Input v-model="detailsObj.overdueTime" readonly></Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="使用时间">
            <Input v-model="detailsObj.usingTime" readonly></Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="激活类型">
            <Select v-model="detailsObj.activationType" disabled>
              <Option v-for="item in activationTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
          <FormItem label="套餐价格">
            <Input v-model="detailsObj.priceCNY" readonly></Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="币种:">
            <span>人民币</span>
          </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
          <FormItem label="最新位置">
            <Input v-model="detailsObj.position" readonly></Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="上报时间">
            <Input v-model="detailsObj.reportingTime" readonly></Input>
          </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="24">
          <Tabs :value="checked" @on-click="tagChange">
            <TabPane label="流量使用详情" name="trafficInfo">
              <div v-if="checked == 'trafficInfo'">
                <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading" max-height="500">
                </Table>
                <Page :total="total" :current.sync="currentpage" show-total show-elevator
                  @on-change="loadByPage(1)" style="margin: 15px 0;" />
              </div>
            </TabPane>
            <TabPane label="VIMSI位置更新详情" name="vimsiInfo">
              <div v-if="checked == 'vimsiInfo'">
                <Table :columns="columnsV" :data="tableDataV" :ellipsis="true" :loading="loading" max-height="500">
                </Table>
                <Page :total="totalV" :current.sync="currentpageV" show-total show-elevator
                  @on-change="loadByPage(2)" style="margin: 15px 0;" />
              </div>
            </TabPane>
          </Tabs>
          </Col>
        </Row>
      </Form>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      id: String,
    },
    data() {
      return {
        checked: 'trafficInfo',
        detailsObj: {
          packageName: '', //套餐名称
          periodUnit: '', //周期类型
          keepPeroid: '', //持续周期
          effectiveDay: '', //套餐订购以后的有效期，单位为天
          usingTime: '', //使用时间
          activationType: '', //激活类型（H卡/V卡激活）
          priceCNY: '', //套餐价格-人民币
          priceHKD: '', //套餐价格-港币
          priceUSD: '', //套餐价格-美元
          position: '', //位置
          reportingTime: '', //上报时间
        },
        activationTypeList: [{
          value: 'V',
          label: 'V卡'
        }, {
          value: 'H',
          label: 'H卡'
        }],
        periodUnitList: [{
            value: '24小时',
            label: '24小时'
          }, {
            value: '每天',
            label: '每天'
          },
          {
            value: '每周',
            label: '每周'
          },
          {
            value: '每月',
            label: '每月'
          }
        ],
        total: 0,
        loading: false,
        pageSize: 5,
        currentpage: 1,
        columns: [{
            title: 'IMSI',
            key: 'IMSI',
            align: 'center',
            minWidth: 120
          }, {
            title: '卡片类型',
            key: 'cardType',
            align: 'center',
            minWidth: 120
          },
          {
            title: '开始时间',
            key: 'startTime',
            align: 'center',
            minWidth: 130
          },
          {
            title: '结束时间',
            key: 'endTime',
            align: 'center',
            minWidth: 130
          },
          {
            title: '国家/地区',
            key: 'region',
            align: 'center',
            minWidth: 120
          },
          {
            title: '流量(G)',
            key: 'flow',
            align: 'center',
            minWidth: 120
          }
        ],
        tableData: [],


        totalV: 0,
        pageSizeV: 5,
        currentpageV: 1,
        columnsV: [{
            title: 'VIMSI号码',
            key: 'VIMSI',
            align: 'center',
          },
          {
            title: '位置上报时间',
            key: 'upTime',
            align: 'center',
          },

          {
            title: '位置上报地点',
            key: 'upPlace',
            align: 'center',
          }

        ],
        tableDataV: [{
            VIMSI: '521',
            upTime: '2021-04-06 12:52:03',
            upPlace: '四川省成都市'
          },
          {
            VIMSI: '52456',
            upTime: '2021-04-07 10:24:23',
            upPlace: '四川省成都市'
          }
        ],
      }
    },
    methods: {
      init() {
        this.loading = true;
        this.tableData = [{
            IMSI: '123455',
            cardType: 'V卡',
            startTime: '2021-01-20',
            endTime: '2021-01-23',
            region: '中国大陆',
            flow: '12',
          },
          {
            IMSI: '1234556',
            cardType: 'V卡',
            startTime: '2021-01-20',
            endTime: '2021-01-23',
            region: '中国香港',
            flow: '30',
          },
          {
            IMSI: '1234558',
            cardType: 'V卡',
            startTime: '2021-01-20',
            endTime: '2021-01-23',
            region: '泰国',
            flow: '10',
          },
          {
            IMSI: '1234559',
            cardType: 'V卡',
            startTime: '2021-01-20',
            endTime: '2021-01-23',
            region: '新加坡',
            flow: '11',
          },
          {
            IMSI: '12345553',
            cardType: 'V卡',
            startTime: '2021-01-20',
            endTime: '2021-01-23',
            region: '马来西亚',
            flow: '12',
          }
        ];
        this.total = this.tableData.length;
        this.loading = false;

        this.detailsObj = {
          packageName: '测试套餐1', //套餐名称
          periodUnit: '每月', //周期类型
          keepPeroid: '4', //持续周期
          overdueTime: '2021-03-16 12:12:12', //套餐订购以后的有效期，单位为天
          priceCNY: '49.99', //套餐价格-人民币
          priceHKD: '149.99', //套餐价格-港币
          priceUSD: '24.99', //套餐价格-美元
          usingTime: '2021-03-01 08:00:00', //使用时间
          activationType: 'V', //激活类型（H卡/V卡激活）
          position: '美国', //位置
          reportingTime: '2021-03-01 23:00:00', //上报时间
        }
      },
      loadByPage(type) {
        if(type == 1){ //流量详情

        }else{  //VIMSI位置更新记录详情

        }
      },
      geiDetailsById(id) {
        // this.init();
      },

      tagChange:function(name){
        //vimsiInfo  trafficInfo
        this.checked = name

      },
    },
    mounted: function() {
      this.init();
    },
    watch: {
      id(newVal, oldVal) {
        this.geiDetailsById(newVal)
      }
    }
  };
</script>
