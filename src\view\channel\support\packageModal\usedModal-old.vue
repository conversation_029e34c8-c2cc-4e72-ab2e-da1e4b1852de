<!-- 已购买套餐 -->

<style scoped>

</style>
<template>
  <div>
    <div>
      <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading" max-height="500">
        <template slot-scope="{ row, index }" slot="local">
          <router-link :to="{path:'/localSearch'}" v-has="'view'" type="primary" size="small"
            style="margin-right: 10px">
            位置记录详情
          </router-link>
        </template>
        <template slot-scope="{ row, index }" slot="trafficDetail">
          <a href="#" v-has="'view'" type="primary" size="small" style="margin-right: 10px" @click="">流量使用详情</a>
        </template>
      </Table>
    </div>
    <div class="table-botton" style="margin-top:15px">
      <Page :total="total" :current.sync="currentpage" show-total show-elevator @on-change="getUpdateRecords(page)" />
    </div>
  </div>
</template>
<script>
  import {
    getUsedPackages
  } from '@/api/serverMnrg/package'
  export default {
    // props: {
    //   row: Object,
    // },
    data() {
      return {
        page: 0,
        loading: false,
        currentpage: 1,
        total: 0,
        columns: [{
            title: '套餐名称',
            key: 'packageName',
            align: 'center'
          },
          {
            title: '使用时间',
            key: 'usedTime',
            align: 'center'
          },
          {
            title: '分配VIMSI',
            key: 'VIMSI',
            align: 'center'
          },
          {
            title: '位置记录详情',
            slot: 'local',
            align: 'center'
          },
          {
            title: '流量使用详情',
            slot: 'trafficDetail',
            align: 'center'
          }
        ],
        // IMSI packageName usedTime local traffic
        tableData: [{
            IMSI: '89a897s9f',
            packageName: '新年套餐',
            usedTime: '2021-01-23 12:45:52',
            VIMSI: 'xxxx',
          },
          {
            IMSI: '89a897s9f',
            packageName: '新年套餐',
            usedTime: '2021-01-23 12:45:52',
            VIMSI: 'yyyy',
          }
        ]
      }
    },
    methods: {
      getUsedPackages: function(page) {
        this.loading = true
        this.page = page
        getUsedPackages({
          cardId: this.cardId, //TODO  替换成当前用户
          pageNumber: page,
          pageSize: 10,
        }).then(res => {
          if (res && res.code == '0000') {
            this.tableData = res.data
            this.total = res.total
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
    },
    mounted: function() {
      this.getUsedPackages(0)
    },
    watch: {

    }
  };
</script>
