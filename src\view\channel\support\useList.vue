<template>
	<!-- 套餐详情 -->
	<Card >
		<div style="width: 100%;margin-top: 50px; margin: auto;">
			<div style="display: flex;">
				<!-- <span style="margin-top: 4px;font-weight:bold;">IMSI:</span>&nbsp;&nbsp;
				<Input v-model="form.richtextTitle" :placeholder="$t('support.input')" prop="showTitle" clearable style="width: 200px" />&nbsp;&nbsp; -->
				<span style="margin-top: 4px;font-weight:bold;">{{$t('support.timeslot')}}:</span>&nbsp;&nbsp;
				<DatePicker v-model="time_slot" type="daterange" format="yyyy-MM-dd" placement="bottom-end" :placeholder="$t('support.chose_time')"
				 style="width: 200px;margin-right: 10px;" @on-change="handleDateChange" @on-clear="hanldeDateClear"></DatePicker>
				<Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading"  @click="search()">{{$t('support.search')}}</Button>
			</div>
			<!-- 表格 -->
			<Table :columns="columns12" :data="data" style="width:100%;margin-top: 50px;" :loading="loading">
			</Table>
			<!-- 分页 -->
			<div style="margin-top: 100px;">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>

		</div>
	</Card>
</template>

<script>
	import{
		supUseList
	} from '@/api/channel.js'
	export default {
		data() {
			return {
				total: 0,
				currentPage: 1,
				page:0,
				time_slot:'',
				searchBeginTime:'',
				searchEndTime:'',
				loading: false,
				searchloading:false,
				form: {},
				typeList:[],
				columns12: [
					// {
					// 	title: 'IMSI',
					// 	key: 'IMSI',
					// 	align: 'center'
					// },
					{
						title: this.$t("support.Report_time"),
						key: 'reportTime',
						align: 'center'
					},
					{
						title: this.$t("support.Report_address"),
						key: 'mcc',
						align: 'center'
					},
					{
						title: this.$t("support.Activation"),
						key: 'activeType',
						align: 'center',
						render: (h, params) => {
						  const row = params.row;
						  const text = row.activeType == '1' ? '自动激活' : row.activeType == '2' ? '手动激活' : '无';
						  return h('label', text)
						}
					},
					
				],
				data: [{
						IMSI: '56454',
						time: '2021-03-15',
						address: '成都',
						activation:'手动激活'
					},
					{
						IMSI: '945454',
						time: '2021-03-15',
						address: '香港',
						activation:'自动激活'
					}
				],
				rules: {

				}
			}
		},
		mounted() {
			this.goPageFirst(1)
		},
		methods: {
			goPageFirst(page){
				this.loading = true
				var _this = this
				var paymentChannel = JSON.parse(decodeURIComponent(this.$route.query.paymentChannel));
				let imsi=paymentChannel.imsi
				let pageNumber = page
				let pageSize = 10
				let startTime = this.searchBeginTime === "" ? null : this.searchBeginTime
				let endTime = this.searchEndTime === "" ? null : this.searchEndTime
				supUseList({
					pageNumber,
					pageSize,
					imsi,
					startTime,
					endTime,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading=false
						this.page = page
						this.currentPage=page
						this.total = res.data.totalCount
						this.data = res.data.records
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchloading=false
				})
			},
			search() {
               this.goPageFirst(1)
			   this.searchloading=true
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			hanldeDateClear() {
				this.searchBeginTime = ''
				this.searchEndTime = ''
			},
			handleDateChange(dateArr) {
				let beginDate = this.time_slot[0] || ''
				let endDate = this.time_slot[1] || ''
				if (beginDate == '' || endDate == '') {
					return
				}
				[this.searchBeginTime, this.searchEndTime] = dateArr
			},

		}


	}
</script>

<style>
</style>
