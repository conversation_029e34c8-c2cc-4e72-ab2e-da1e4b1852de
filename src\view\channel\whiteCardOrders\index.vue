<template>
	<!-- 渠道商白卡订单管理 -->
	<Card>
		<!-- 搜索条件 -->
		<div style="padding: 20px 5px;display: flex;justify-content: flex-start;flex-wrap: wrap;">
			<div class="search_box">
				<span><strong>{{$t('common.cardType')}}</strong></span>&nbsp;&nbsp;
				<Select v-model="cardForm" clearable :placeholder="$t('support.selectCardtype')" filterable style="width: 200px">
					<Option :value="1">{{$t('stock.PhysicalSIM')}}</Option>
					<Option :value="2">{{$t('stock.eSIM')}}</Option>
					<Option :value="4">IMSI</Option>
				</Select>
			</div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<div class="search_box">
				<span><strong>{{$t('support.physicalOrCustomized')}}</strong></span>&nbsp;&nbsp;
				<Select v-model="chargingMode" clearable :placeholder="$t('common.pleaseChoose')" filterable style="width: 200px">
					<Option :value="2">{{$t('common.PhysicalSIM')}}</Option>
					<Option :value="1">{{$t('support.customizedSIM')}}</Option>
				</Select>
			</div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<div class="search_box">
				<Button v-has="'search'" :disabled="!cooperationMode || !corpId || cooperationMode == '3'" type="primary" icon="md-search" :loading="searchloading"
					@click="search()">{{$t('address.search')}}</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button v-has="'add'" :disabled="!cooperationMode || !corpId || cooperationMode == '3'" type="primary" ghost icon="md-add" @click="add()">{{$t('support.createOrder')}}</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table ref="selection" :columns="columns" :data="data" style="width:100%;margin-top: 20px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<div style="padding: 10px 5px 5px 0 ;">
					<Button v-has="'revoke'" v-show="row.orderStatus == 1" type="error" ghost size="small" style="margin: 0 5px 5px 0"
						@click="revoke(row)">{{$t('support.revoke')}}</Button>
					<Button v-has="'invoice'" v-show="![1,2].includes(+row.orderStatus) && row.chargingMode == 1" type="primary" ghost
						size="small" style="margin: 0 5px 5px 0"
						@click="downloadInvoice(row)">{{$t('support.downloadInvoice')}}</Button>
					<Button v-has="'list'" v-show="[7,8].includes(+row.orderStatus)" type="success" ghost size="small"
						style="margin: 0 5px 5px 0"
						@click="downloadNumberList(row)">{{$t('support.downloadList')}}</Button>
					<Button v-has="'payslip'" v-show="(row.orderStatus == 3 || row.orderStatus == 9) && row.chargingMode == 1" type="warning" ghost size="small"
						style="margin: 0 5px 5px 0" @click="upload(row)">{{$t('support.uploadPayslip')}}</Button>
				</div>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 新增订单 -->
		<Modal v-model="ordersModal" :title="$t('support.createOrder')" :footer-hide="true" :mask-closable="false"
			width="550px" @on-cancel="cancelModal">
			<div>
				<Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="135"
					 inline style="font-weight:bold;">
					<div style="width:540px;display: flex;flex-direction: column;">
						<FormItem prop="chargingMode">
						    <RadioGroup v-model="formValidate.chargingMode" style="display: flex; justify-content: flex-start;">
						        <div style="line-height: 30px;">
									<Radio :label="2" style="font-size: 16px;">{{$t('common.PhysicalSIM')}}</Radio>
								</div>
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						        <div style="line-height: 30px;">
									<Radio :label="1" style="font-size: 16px;">{{$t('support.customizedSIM')}}</Radio>&nbsp;&nbsp;&nbsp;&nbsp;
									<Tooltip max-width="400" :content="description"
										style="color: blue; font-weight: bold; font-size: 30px; vertical-align: middle; pointer:cursor; word-break: break-all;">
										<Icon type="ios-alert-outline" />
									</Tooltip>
								</div>
						    </RadioGroup>
						</FormItem>
					</div>
					<FormItem :label="$t('common.cardType')" prop="cardForm" style="width:450px">
						<Select v-model="formValidate.cardForm" :disabled="!formValidate.chargingMode" clearable :placeholder="$t('support.selectCardtype')">
							<Option :value="1">{{$t('stock.PhysicalSIM')}}</Option>
							<Option :value="2">{{$t('stock.eSIM')}}</Option>
							<Option v-if="formValidate.chargingMode=='2'" :value="4">IMSI</Option>
						</Select>
					</FormItem>
          <FormItem :label="$t('support.productPackaging')" prop="cardPackage" style="width:450px" v-if="formValidate.cardForm == '1'"
            :rules="formValidate.cardForm == '1' ? ruleValidate.cardPackage : [{required: false}]">
          	<Select v-model="formValidate.cardPackage" clearable :placeholder="$t('support.selectProductPackaging')">
          		<Option :value="1">{{$t('support.nakedCard')}}</Option>
              <Option :value="2">{{$t('support.packagingCard')}}</Option>
          	</Select>
          </FormItem>
					<FormItem :label="$t('common.quantity')" prop="count" style="width:450px">
						<Input :placeholder="$t('support.inputCardNumber')" v-model="formValidate.count" clearable />
					</FormItem>
          <FormItem :label="$t('support.receivingCountry')" prop="mcc" style="width:450px"
            :rules="formValidate.cardForm == '1' ? ruleValidate.mcc : [{required: false}]">
            <Select v-model="formValidate.mcc" filterable clearable :placeholder="$t('support.selectReceivingCountry')">
              <Option v-for="item in localList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
            </Select>
          </FormItem>
					<FormItem :label="$t('support.receiveeAddress')" prop="address" style="width:450px" :rules="formValidate.cardForm == '1' ?
						ruleValidate.logistic : [{required: false}]">
						<Input :placeholder="$t('support.inputAddress')" v-model.trim="formValidate.address" clearable />
					</FormItem>
          <FormItem :label="$t('support.postalCode')" prop="postcode" style="width:450px" :rules="formValidate.cardForm == '1' ? ruleValidate.postcode : [{required: false}]">
          	<Input :placeholder="$t('support.selectPostalCode')" v-model.trim="formValidate.postcode" clearable />
          </FormItem>
					<FormItem :label="$t('support.receiverName')" prop="recipient" style="width:450px" :rules="formValidate.cardForm == '1' ?
						ruleValidate.logistic : [{required: false}]">
						<Input :placeholder="$t('support.inputRecrver')" v-model.trim="formValidate.recipient" clearable />
					</FormItem>
					<FormItem :label="$t('support.contactNumber')" prop="phoneNumber" style="width:450px" :rules="formValidate.cardForm == '1' ?
						ruleValidate.logistic : [{required: false}]">
						<Input :placeholder="contactNumberFormat" v-model="formValidate.phoneNumber" clearable />
					</FormItem>
					<FormItem :label="$t('support.cooperationModel')" prop="cooperationMode" style="width:450px">
						<Select v-model="formValidate.cooperationMode" clearable
							:placeholder="$t('selectCooperationMode')">
							<Option v-if="this.cooperationMode == 1" :value="1">{{$t('support.distribution')}}</Option>
							<Option v-else :value="2">{{$t('support.atoz')}}</Option>
						</Select>
					</FormItem>
          <FormItem :label="$t('support.template')" prop="templateId" style="width:450px">
          	<Select v-model="formValidate.templateId" clearable filterable
          		:placeholder="$t('support.chosetemplate')">
              <Option :value="item.templateId" v-for="(item,index) in tempList" :key="index">{{item.templateName}}</Option>
            </Select>
          </FormItem>
          <FormItem :label="$t('support.language')" prop="language" style="width:450px">
          	<Select v-model="formValidate.language" clearable
          		:placeholder="$t('support.selectLanguage')">
              <Option :value="1">{{$t('common.traditionalChinese')}}</Option>
              <Option :value="2">{{$t('common.english')}}</Option>
          		<Option :value="3">{{$t('common.simplifiedChinese')}}</Option>
          	</Select>
          </FormItem>
          <div style="width:540px;margin: 10px 0 30px 0;"
            v-if="formValidate.chargingMode == '1'">
              <FormItem style="width:450px" prop="agree" :rules="formValidate.chargingMode == '1' ? ruleValidate.agree : [{required: false}]">
                <Row>
                  <Col span="12">
                    <CheckboxGroup v-model="formValidate.agree">
                     <Checkbox>
                     {{$t('support.agree')}}
                     <a href="javascript:void(0);" style="color: blue; text-decoration: underline; cursor: pointer;" @click="instance('info')">{{$t('support.Agreements')}}</a>
                     </Checkbox>
                    </CheckboxGroup>
                  </Col>
                </Row>
              </FormItem>
          </div>
				</Form>
				<div style="text-align: center;margin: 4px 0;">
					<Button style="margin-right: 30px" @click="cancelModal">{{$t('support.back')}}</Button>
					<Button type="primary" @click="submit" v-preventReClick
						:loading="submitLoading">{{$t('common.determine')}}</Button>
				</div>
			</div>
		</Modal>
		<!-- 上传付款证明 -->
		<Modal v-model="PaymentModal" :title="$t('support.uploadPayslip')" :footer-hide="true" :mask-closable="false"
			width="450px" @on-cancel="cancelModal">
			<div>
				<Form ref="formobj" :model="formobj" :rules="ruleobj" :label-width="100"
					:label-height="100" inline style="font-weight:bold;">
					<FormItem :label="$t('support.payslip')" prop="file" style="font-size: 14px;font-weight: bold;">
						<Upload type="drag" v-model="formobj.file" :action="uploadUrl" :before-upload="handleBeforeUpload" :on-progress="fileUploading"
							style="width: 250px; margin-top: 50px;">
							<div style="padding: 20px 0">
								<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
								<p>{{$t('support.uploadPicture')}}</p>
							</div>
						</Upload>
						<ul class="ivu-upload-list" v-if="file" style="width: 300px;">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />{{file.name}}
								</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" @click="removeFile"></i>
							</li>
						</ul>
					</FormItem>
				</Form>
				<div style="text-align: center;margin: 4px 0;">
					<Button style="margin-right: 30px" @click="cancelModal">{{$t('support.back')}}</Button>
					<Button type="primary" @click="pictureSubmit" v-preventReClick
						:loading="pictureLoading">{{$t('common.determine')}}</Button>
				</div>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelExportModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
					<FormItem :label="$t('exportID')">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem :label="$t('exportFlie')">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">{{$t('downloadResult')}}</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelExportModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
			</div>
		</Modal>
		<a ref="downloadLink" style="display: none"></a>
	</Card>
</template>

<script>
	import {
		addOrder,
		downloadNumberList,
		downloadInvoice,
		getDescription,
		addPicture,
		revoke,
    getContent
	} from "@/api/channel/whiteCardOrders";
	import {
		getList,
    downloadFile,
	} from "@/api/product/whiteCardOrders";
  import {
  	opsearch
  } from '@/api/channel.js';
  import {
  	getChannelSmsTemplate,
  } from '@/api/channel.js'
  export default {
		data() {
			// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (!this.file) {
					callback(new Error(this.$t('support.pleaseUploadFile')))
				} else {
					callback()
				}
			}
			return {
				total: 0,
				currentPage: 1,
				cooperationMode: "",
				corpId: "",
				orderStatus: "",
				cardForm: "", //主卡形态
				chargingMode: "",
				orderId: "",
				payOrderStatus: "",
				taskId: "",
				taskName: "",
				description: "", //说明
				uploadUrl: '', //上传地址
				contactNumberFormat: "",//电话备注
        content: "",//协议内容
				uploadList: [],
				file: null,
				loading: false,
				submitLoading: false, //新增订单提交
				pictureLoading: false,//上传付款证明标识
				searchloading: false,
				exportModal: false, //导出弹框标识
				ordersModal: false, //新增订单弹窗
				PaymentModal: false, //付款证明
				data: [], //表格列表
        tempList: [], //短信模板
        localList: [],
				columns: [{
					title: this.$t('support.orderId'),
					key: 'orderId',
					minWidth: 180,
					align: 'center',
					tooltip: true,
				}, {
					title: this.$t('support.Ordertime'),
					key: 'createTime',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.createTime) {
							var time = new Date(row.createTime)
							const y = time.getFullYear() // 可返回一个表示年份的 4 位数字
							const m = time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time
								.getMonth() +
								1 // 可返回表示月份的数字。返回值是 0（一月） 到 11（十二月） 之间的一个整数 // 注意： 一月为 0, 二月为 1, 以此类推。
							const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate()
							const h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
							const min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
							const seconds = time.getSeconds() < 10 ? '0' + time.getSeconds() : time
								.getSeconds()
							text = y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + seconds
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: this.$t('support.Cardtype'),
					key: 'cooperationMode',
					minWidth: 135,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text = row.cooperationMode == '1' ? this.$t('support.distribution') : row.cooperationMode == '2' ? this.$t('support.atoz'): ''
						return h('label', text)
					},
				}, {
					title: this.$t('common.cardType'),
					key: 'cardForm',
					minWidth: 135,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text = row.cardForm == '1' ? this.$t('stock.PhysicalSIM') : row.cardForm == '2' ?
							this.$t('stock.eSIM') : row.cardForm == '4' ? "IMSI" : ''
						return h('label', text)
					},
				}, {
					title: this.$t('support.physicalOrCustomized'),
					key: 'chargingMode',
					minWidth: 160,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text = row.chargingMode == 1 ? this.$t('support.customizedSIM') : row
							.chargingMode == 2 ? this.$t('common.PhysicalSIM') : ''
						return h('label', text)
					},
				}, {
					title: this.$t('common.quantity'),
					key: 'count',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: this.$t('order.order_state'),
					key: 'orderStatus',
					align: 'center',
					minWidth: 186,
					render: (h, params) => {
						const row = params.row
						const color = row.orderStatus == '1' ? '#2b85e4' :
                  row.orderStatus == '2' ? '#00aa00' :
                  row.orderStatus == '3' ? '#ff0000' :
                  row.orderStatus == '4' ? '#69e457' :
                  row.orderStatus == '5' ? '#e47a49' :
                  row.orderStatus == '6' ? '#dd74e4' :
                  row.orderStatus == '7' ? '#24cbe4' :
                  row.orderStatus == '8' ? '#7009e4' :
                  row.orderStatus == '9' ? '#e4b809' :
                  row.orderStatus == '10' ? '#ff9900' :
                  row.orderStatus == '11' ? '#e4e424' : // 新增
                  row.orderStatus == '12' ? '#24e424' : // 新增
                  row.orderStatus == '13' ? '#e42424' : // 新增
                  '';

    				const text = row.orderStatus == '1' ? this.$t('support.ordered') :
                 row.orderStatus == '2' ? this.$t('support.cancelled') :
                 row.orderStatus == '3' ? this.$t('support.pendingPayment') :
                 row.orderStatus == '4' ? this.$t('support.PaymentConfirmed') :
                 row.orderStatus == '5' ? this.$t('order.delivered') :
                 row.orderStatus == '6' ? this.$t('support.deliveryProgress') :
                 row.orderStatus == '7' ? this.$t('support.delivered') :
                 row.orderStatus == '8' ? this.$t('support.deliveryFailed') :
                 row.orderStatus == '9' ? this.$t('support.PaymentNotConfirmed') :
                 row.orderStatus == '10' ? this.$t('support.paymentConfirmed') :
                 row.orderStatus == '11' ? this.$t('support.rollbackInProgress'): // 新增
                 row.orderStatus == '12' ? this.$t('support.rollbackSuccess') : // 新增
                 row.orderStatus == '13' ? this.$t('support.rollbackFailure')  : // 新增
                 '';
						return h('label', {
							style: {
								color: color
							}
						}, text)
					}
				}, {
					title: this.$t('support.receiveeAddress'),
					key: 'address',
					minWidth: 140,
					align: 'center',
					tooltip: true,
				}, {
					title: this.$t('support.receiverName'),
					key: 'addressee',
					minWidth: 130,
					align: 'center',
					tooltip: true,
				}, {
					title: this.$t('support.contactNumber'),
					key: 'phoneNumber',
					minWidth: 130,
					align: 'center',
					tooltip: true,
				}, {
					title: this.$t('support.deliveryCompany'),
					key: 'logisticCompany',
					minWidth: 140,
					align: 'center',
					tooltip: true,
				}, {
					title: this.$t('support.trackingNumber'),
					key: 'logistic',
					minWidth: 140,
					align: 'center',
					tooltip: true,
				}, {
					title: this.$t('support.orderBatch'),
					key: 'orderBatch',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: this.$t('support.generateInvoice'),
					key: 'invoiceTime',
					minWidth: 165,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.invoiceTime) {
							var time = new Date(row.invoiceTime)
							const y = time.getFullYear() // 可返回一个表示年份的 4 位数字
							const m = time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time
								.getMonth() +
								1 // 可返回表示月份的数字。返回值是 0（一月） 到 11（十二月） 之间的一个整数 // 注意： 一月为 0, 二月为 1, 以此类推。
							const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate()
							const h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
							const min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
							const seconds = time.getSeconds() < 10 ? '0' + time.getSeconds() : time
								.getSeconds()
							text = y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + seconds
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: this.$t('support.uploadPayslipTime'),
					key: 'paymentProofsTime',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.paymentProofsTime) {
							var time = new Date(row.paymentProofsTime)
							const y = time.getFullYear() // 可返回一个表示年份的 4 位数字
							const m = time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time
								.getMonth() +
								1 // 可返回表示月份的数字。返回值是 0（一月） 到 11（十二月） 之间的一个整数 // 注意： 一月为 0, 二月为 1, 以此类推。
							const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate()
							const h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
							const min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
							const seconds = time.getSeconds() < 10 ? '0' + time.getSeconds() : time
								.getSeconds()
							text = y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + seconds
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: this.$t('support.orderConfirmTime'),
					key: 'confirmTime',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.confirmTime) {
							var time = new Date(row.confirmTime)
							const y = time.getFullYear() // 可返回一个表示年份的 4 位数字
							const m = time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time
								.getMonth() +
								1 // 可返回表示月份的数字。返回值是 0（一月） 到 11（十二月） 之间的一个整数 // 注意： 一月为 0, 二月为 1, 以此类推。
							const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate()
							const h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
							const min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
							const seconds = time.getSeconds() < 10 ? '0' + time.getSeconds() : time
								.getSeconds()
							text = y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + seconds
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: this.$t('support.deliveryTime'),
					key: 'deliverTime',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.deliverTime) {
							var time = new Date(row.deliverTime)
							const y = time.getFullYear() // 可返回一个表示年份的 4 位数字
							const m = time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time
								.getMonth() +
								1 // 可返回表示月份的数字。返回值是 0（一月） 到 11（十二月） 之间的一个整数 // 注意： 一月为 0, 二月为 1, 以此类推。
							const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate()
							const h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
							const min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
							const seconds = time.getSeconds() < 10 ? '0' + time.getSeconds() : time
								.getSeconds()
							text = y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + seconds
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: this.$t('address.action'),
					slot: 'action',
					minWidth: 220,
					align: 'center',
					fixed: 'right'
				}, {
					title: this.$t('support.template'),
					key: 'templateName',
					minWidth: 160,
					align: 'center',
					tooltip: true,
				}, {
					title: this.$t('support.language'),
					key: 'language',
					minWidth: 160,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text =
              row.language == 1 ? this.$t('common.traditionalChinese') :
              row.language == 2 ? this.$t('common.english') :
              row.language == 3 ? this.$t('common.simplifiedChinese') : ""
						return h('label', text)
					},
				}, {
					title: this.$t('support.productPackaging'),
					key: 'cardPackage',
					minWidth: 160,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text = row.cardPackage == 1 ? this.$t('support.nakedCard') :
             row.cardPackage == 2 ? this.$t('support.packagingCard') : ""
						return h('label', text)
					},
				},],
				formValidate: {
					chargingMode: "",
					cardForm: "",
					count: "",
					address: "",
					recipient: "",
					phoneNumber: "",
					cooperationMode: "",
          cardPackage:"",
          mcc: "",
          postcode: "",
          templateId: "",
          language: "",
          agree: [], //协议
				},
				ruleValidate: {
					chargingMode: [{
						required: true,
						message: this.$t('support.PaymentMandatory'),
					}],
					cardForm: [{
						required: true,
						message: this.$t('support.cardTypeMandatory'),
					}],
					count: [{
						required: true,
						message: this.$t('support.cardMumberMandatory'),
					}, {
						pattern: /^[0-9]*[1-9][0-9]*$/,
						trigger: "blur",
						message: this.$t('support.wrongFormat'),
					}],
					address: [{
						required: true,
						message: this.$t('support.addressMandatory'),
					}],
					recipient: [{
						required: true,
						message: this.$t('support.receiverMandatory'),
					}],
					phoneNumber: [{
						required: true,
						message: this.$t('support.contactNumberMandatory'),
					}],
					cooperationMode: [{
						required: true,
						message: this.$t('support.cooperationMandatory'),
					}],
          cardPackage: [{
						required: true,
						message: this.$t('support.productPackagingEmpty'),
					}],
          mcc: [{
          	required: true,
          	message: this.$t('support.receivingCountryEmpty'),
          }],
          postcode: [{
          	required: true,
          	message: this.$t('support.postalCodeEmpty'),
          }],
          templateId: [{
          	required: true,
          	message: this.$t('support.SMSempty'),
          }],
          language: [{
          	required: true,
          	message: this.$t('support.languageEmpty'),
          }],
          agree: [{
            required: true,
            type: 'array',
            min: 1,
            message: this.$t('support.agreement'),
            trigger: 'change' ,
          }]
        },
				formobj: {
					file: ""
				},
				ruleobj: {
					file: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
				},
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			this.corpId = sessionStorage.getItem("corpId")
			if (this.cooperationMode && this.corpId && this.cooperationMode !='3') {
				this.goPageFirst(1)
				this.getDescription()
			}
		},
		methods: {
			goPageFirst: function(page) {
        this.data = []
				this.loading = true
				var _this = this
				getList({
					pageNum: page,
					pageSize: 10,
					orderUserId: this.corpId,
					cooperationMode: this.cooperationMode,
					cardForms: this.cardForm,
					chargingMode: this.chargingMode
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.data = res.data;
						this.total = res.count
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			//新增订单
			add() {
				this.ordersModal = true
				this.formValidate.cooperationMode = this.cooperationMode == 1 ? this.formValidate.cooperationMode = 1 : this.formValidate.cooperationMode = 2
        this.getChannelSmsTemplate()
        this.getLocalList()
        this.getContent()
      },
			//订单撤销
			revoke(row) {
				this.$Modal.confirm({
					title: this.$t('support.confirmRevocation'),
					onOk: () => {
						revoke(row.orderId).then((res) => {
							if (res.code === "0000") {
								this.$Notice.success({
									title: this.$t('address.Operationreminder'),
									desc: this.$t('common.Successful'),
								});
							}
							this.goPageFirst(1);
						});
					},
				});
			},
			//下载号码列表
			downloadNumberList(row) {
				downloadNumberList({
					corpId: this.corpId,
					orderBatch: row.orderBatch
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.data.taskId
					this.taskName = res.data.data.taskName
				}).catch()
			},
			//下载Invoice
			downloadInvoice(row) {
				downloadFile({
					id: row.orderId,
					type: 1
				}).then(res => {
					const content = res.data
					let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content,fileName)
					}
				}).catch()
			},
			cancelExportModal() {
				this.exportModal = false
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportModal = false
			},
			cancelModal() {
        this.formValidate.chargingMode= ''
				this.$refs["formValidate"].resetFields();
				this.ordersModal = false
				this.PaymentModal = false
				this.file = ''
				this.$refs['formobj'].resetFields()
			},
			//上传付款证明
			upload(row) {
				this.PaymentModal = true
				this.payOrderStatus = row.orderStatus
				this.orderId = row.orderId
			},
			// 上传文件
			handleBeforeUpload(file, fileList) {
				const sizeLimit = file.size / 1024 / 1024 > 10
				if (sizeLimit) {
					this.$Notice.warning({
						title: this.$t('address.Operationreminder'),
						desc: this.$t('support.pictureSize')
					});
					return false;
				}
				this.file = file,
				this.uploadList = fileList
				return false;
			},
			fileUploading(event, file, fileList) {
				this.message = this.$t('support.fileUploadedAndProgressDisappears')
			},
			removeFile() {
				this.file = ''
			},
			instance (type) {
			  this.$Modal.info({
			      title: this.$t("support.AgreementContent"),
			      content: this.content,
            width: '800px',
			  });
			},
      //新增订单 提交
			submit() {
				this.$refs["formValidate"].validate((valid) => {
					if (valid) {
            this.submitLoading = true
            addOrder({
            	orderUserId: sessionStorage.getItem("corpId"),
            	chargingMode: this.formValidate.chargingMode,
            	cardForm: this.formValidate.cardForm,
            	count: this.formValidate.count,
            	address: this.formValidate.address,
            	recipient: this.formValidate.recipient,
            	phoneNumber: this.formValidate.phoneNumber,
            	cooperationMode: this.formValidate.cooperationMode,
              cardPackage: this.formValidate.cardForm == "1" ? this.formValidate.cardPackage : undefined,
              mcc: this.formValidate.mcc,
              postcode: this.formValidate.postcode,
              templateId: this.formValidate.templateId,
              language: this.formValidate.language,
            }).then((res) => {
            	if (res.code === "0000") {
            		this.$Notice.success({
            			title: this.$t('address.Operationreminder'),
            			desc: this.$t('common.Successful'),
            		});
            		this.submitLoading = false
            		this.ordersModal = false;
            		this.goPageFirst(1)
            		this.$refs["formValidate"].resetFields();
            	} else {
            		throw res
            	}
            }).catch((err) => {
            	this.submitLoading = false
            }).finally(() => {

            })
					}
				});
			},
			pictureSubmit() {
				this.$refs["formobj"].validate((valid) => {
					if (valid) {
						var formData = new FormData();
						formData.append('corpId', this.corpId);
						formData.append('orderId', this.orderId);
						formData.append('orderStatus', this.payOrderStatus);
						formData.append('paymentProofs', this.file); //封面
						this.pictureLoading = true
						addPicture(formData).then((res) => {
							if (res.code === "0000") {
								this.$Notice.success({
									title: this.$t('address.Operationreminder'),
									desc: this.$t('common.Successful'),
								});
								this.pictureLoading = false
								this.PaymentModal = false;
								this.goPageFirst(1)
								this.file = ''
								this.$refs["formobj"].resetFields();
							} else {
								throw res
							}
						}).catch((err) => {
							this.pictureLoading = false
						}).finally(() => {

						})
					}
				});
			},
			/*——————————————————————— 进入页面前加载————————————————————————*/
			//获取说明文字
			getDescription() {
				getDescription({
					corpId: this.corpId,
				}).then(res => {
					if (res && res.code == '0000') {
						this.description = res.data.illustrate;
						this.contactNumberFormat = res.data.phoneIllustrate;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
      // 获取短信模板
      getChannelSmsTemplate() {
      	getChannelSmsTemplate({
          corpId: sessionStorage.getItem('corpId'),
          cooperationMode: sessionStorage.getItem("cooperationMode")
        }).then(res => {
      		if (res.code === '0000') {
      			this.tempList = res.data;
      		}
      	}).catch((err) => {
      		console.log(err)
      	}).finally(() => {

      	})
      },
      //国家/地区
      getLocalList() {
      	opsearch().then(res => {
      		if (res && res.code == '0000') {
      			var list = res.data;
      			this.localList= list;
      			this.localList.sort(function(str1, str2) {
              return str1.countryEn.localeCompare(str2.countryEn);
      			});
      		} else {
      			throw res
      		}
      	}).catch((err) => {

      	}).finally(() => {

      	})
      },
      // 协议内容
      getContent() {
      	getContent().then(res => {
      		if (res && res.code == '0000') {
      			this.content = this.$i18n.locale === 'zh-CN' ? res.data.protocolCn : res.data.protocolEn
      		} else {
      			throw res
      		}
      	}).catch((err) => {

      	}).finally(() => {

      	})
      },
    }
	}
</script>

<style scoped="scoped">
	.search_box {
		padding: 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 160px;
	}
</style>
