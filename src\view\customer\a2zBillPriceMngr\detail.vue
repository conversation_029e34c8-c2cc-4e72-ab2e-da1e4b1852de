<template>
  <!-- A~Z计费价格管理 规则详情页-->
	<Card style="width: 100%; padiing: 16px">
		<Form ref="searchForm" :model="searchObj" inline @submit.native.prevent :label-width="70"
			style="margin: 30px 0">
      <Row>
        <Col span="24">
          <FormItem label="规则名称:" style="font-weight: bold;margin-left: 20px;">
          	{{name}}
          </FormItem>
        </Col>
      </Row>
			<FormItem label="国家" style="font-weight: bold;">
        <Select filterable v-model="searchObj.mcc" placeholder="请选择国家" clearable style="width: 180px;">
        	<Option v-for="(item1, index1) in countryList" :value="item1.mcc" :key="item1.mcc">{{item1.countryCn}}</Option>
        </Select>
			</FormItem>
			<FormItem>
				<Button style="margin: 0 2px" type="info" @click="search" :loading="searchloading" v-has="'search'">
					<Icon type="ios-search" />&nbsp;搜索
				</Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading"></Table>
			<Page :total="total" :page-size="pageSize" :current.sync="currentPage" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0" />
		</div>
  </Card>
</template>

<script>
	import {
		queryRuleDeatil,
	} from "@/api/customer/a2zBillPriceMngr.js";
  import {
  	opsearchAll,
  } from '@/api/operators';
	export default {
		components: {},
		data() {
			return {
        name: "", //规则名称
				searchObj: {
					mcc: "", //规则名称
				},
				total: 0,
				pageSize: 10,
				page: 1,
				currentPage: 1,
				loading: false,
				searchloading: false,
        originData: [],
        countryList: [], // 国家列表
				tableData: [],
				columns: [{
						title: "国家",
						key: "countryName",
						align: "center",
						minWidth: 150,
						tooltip: true
					},
					{
						title: "运营商",
            key: "operatorName",
						align: "center",
						minWidth: 160,
						tooltip: true
					},
          {
          	title: "人民币单价",
            key: "cny",
          	align: "center",
          	minWidth: 150,
          	tooltip: true,
            render: (h, params) => {
            	const row = params.row
              const text = this.originData.flowUnit == '1' ? row.cny + ' (元/GB)' :
              this.originData.flowUnit == '2' ? row.cny + ' (元/MB)' : ''
            	return h('label', text)
            }
          },
          {
          	title: "港元单价",
            key: "hkd",
          	align: "center",
          	minWidth: 150,
          	tooltip: true,
            render: (h, params) => {
            	const row = params.row
              const text = this.originData.flowUnit == '1' ? row.hkd + ' (元/GB)' :
              this.originData.flowUnit == '2' ? row.hkd + ' (元/MB)' : ''
            	return h('label', text)
            }
          },
          {
          	title: "美元单价",
            key: "usd",
          	align: "center",
          	minWidth: 150,
          	tooltip: true,
            render: (h, params) => {
            	const row = params.row
              const text = this.originData.flowUnit == '1' ? row.usd + ' (元/GB)' :
              this.originData.flowUnit == '2' ? row.usd + ' (元/MB)' : ''
            	return h('label', text)
            }
          },
				],
			};
		},

    created() {
      this.originData = JSON.parse(decodeURIComponent(this.$route.query.rowData)) // 解密row
    },

		mounted() {
      this.name = this.originData.name
			this.goPageFirst(1);
      this.getLocalList();
		},

		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				queryRuleDeatil({
          id: this.originData.id,
          name: this.name,
          mcc: this.searchObj.mcc,
					size: 10,
					current: page,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = Number(res.count)
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},

			//表格数据加载
			loadByPage(page) {
				this.goPageFirst(page);
			},

			//搜索
			search() {
				this.searchloading = true
				this.goPageFirst(1);
			},

			/** -------------------------------------------------------------*/
      //获取国家
      getLocalList() {
      	opsearchAll().then(res => {
      		if (res && res.code == '0000') {
      			var list = res.data;
      			this.countryList = list;
      			this.countryList.sort(function(str1, str2) {
      				return str1.countryCn.localeCompare(str2.countryCn);
      			});
      		} else {
      			throw res
      		}
      	}).catch((err) => {

      	}).finally(() => {

      	})
      },
		},
  };
</script>

<style>
</style>
