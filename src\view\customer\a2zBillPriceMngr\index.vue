<template>
  <!-- 流量计费价格管理 -->
	<Card style="width: 100%; padiing: 16px">
		<Form ref="searchForm" :model="searchObj" inline @submit.native.prevent :label-width="70"
			style="margin: 30px 0">
			<FormItem label="规则名称" style="font-weight: bold;">
				<Input v-model.trim="searchObj.name" placeholder="请输入规则名称" clearable style="width: 200px" />
			</FormItem>
			<FormItem>
				<Button style="margin: 0 2px" type="info" @click="search" :loading="searchloading" v-has="'search'">
					<Icon type="ios-search" />&nbsp;搜索
				</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button style="margin: 0 2px" type="primary" @click="addItem" v-has="'add'">
        	<Icon type="ios-add" />&nbsp;新建
        </Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
				<template slot-scope="{ row, index }" slot="view">
					<Button type="info" ghost size="small" style="margin-right: 20px" @click="viewItem(row)" v-has="'detail'">点击查看</Button>
				</template>
        <template slot-scope="{ row, index }" slot="action">
          <Button type="primary" ghost size="small" style="margin-right: 15px" @click="exportHandle(row)"
             v-has="'export'">导出</Button>
          <Button type="success" ghost size="small" style="margin-right: 15px" @click="importHandle(row)"
            :disabled="row.status === '4'" v-has="'import'">导入</Button>
					<Button type="warning" ghost size="small" style="margin-right: 15px" @click="updateItem(row)"
            :disabled="row.status === '4'" v-has="'update'">修改</Button>
					<Button type="error" ghost size="small" @click="deleteItem(row)"
            :disabled="row.status === '4' || row.status === '3'" v-has="'delete'">删除</Button>
				</template>
        <template slot-scope="{ row, index }" slot="approval">
        	<Button type="success" ghost size="small" style="margin-right: 20px" :disabled="row.status === '5' || row.status === '2'"
           @click="approve(row, '1')" v-has="'audit'">通过</Button>
        	<Button type="error" ghost size="small" style="margin-right: 20px" :disabled="row.status === '5' || row.status === '2'"
          @click="approve(row, '2')" v-has="'audit'">不通过</Button>
        </template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="currentPage" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0" />
		</div>
		<!-- 新建、修改规则弹窗 -->
		<Modal :title="title" v-model="ruleModal" :footer-hide="true" :mask-closable="false" width="1100px"
			@on-cancel="cancelModal">
			<div style="padding: 0 16px">
				<Form ref="addRule" :model="addRule" :rules="ruleValidate">
          <Row>
            <Col span="8">
              <FormItem label="规则名称" prop="name">
              	<Input v-model.trim="addRule.name" clearable :disabled="title == '修改规则'" :maxlength="50" placeholder="请输入规则名称" class="flowInputSty" ></Input>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="生效日期" prop="effectiveDate">
                <DatePicker type="date" v-model="addRule.effectiveDate" placeholder="选择生效日期" class="flowInputSty" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="定价流量单位" prop="flowUnit">
              	<Select v-model="addRule.flowUnit" placeholder="请选择定价流量单位" clearable class="flowInputSty">
              		<Option value='1'>GB</Option>
              		<Option value='2'>MB</Option>
              	</Select>
              </FormItem>
            </Col>
          </Row>
          <Spin size="large" fix v-if="spinShow"></Spin>
          <div style="display: flex; flex-wrap: nowrap; flex-direction: column; margin-top: 20px;">
          	<div v-for="(item,index) in addRule.details" :key="index" class="billRuleSty">
          		<div class="billRuleBox">
          	    <h4 v-if="index == 0">国家</h4>
          	    <FormItem :prop="'details.' + index + '.mcc'" :rules="ruleValidate.mcc">
          	    	<Select filterable v-model="item.mcc" placeholder="请选择国家" clearable style="width: 180px;"
                    @on-change="handleCountryChange(index, $event)">
                    <Option v-for="country in mccList" :key="country.id" :value="country.mcc">{{ country.countryCn }}</Option>
          	    	</Select>
          	    </FormItem>
          	  </div>
          	  <div class="billRuleBox">
          	    <h4 v-if="index == 0">运营商</h4>
          	    <FormItem :prop="'details.' + index + '.operatorId'" :rules="ruleValidate.operatorId">
          	    	<Select filterable v-model="item.operatorId" placeholder="请选择运营商" clearable style="width: 180px;"
                    @on-change="handleOperatorChange(index, $event)">
          	    		<Option v-for="operator in filteredOperators[index]" :key="operator.id" :title="operator.operatorName" :value="operator.id">{{operator.operatorName}}</Option>
          	    	</Select>
          	    </FormItem>
          	  </div>
          	  <div class="billRuleBox">
          	    <h4 v-if="index == 0">人民币单价</h4>
          	    <FormItem :prop="'details.' + index + '.cny'" :rules="ruleValidate.cny">
          	    	<Input v-model.trim="item.cny" type="number" :clearable="true" placeholder="请输入人民币单价" style="width: 160px;"></Input>
          	    </FormItem>
          	  </div>
              <div class="billRuleBox">
                <h4 v-if="index == 0">港币单价</h4>
                <FormItem :prop="'details.' + index + '.hkd'" :rules="ruleValidate.hkd">
                	<Input v-model.trim="item.hkd" type="number" :clearable="true" placeholder="请输入港币单价" style="width: 160px;"></Input>
                </FormItem>
              </div>
          	  <div class="billRuleBox">
          	    <h4 v-if="index == 0">美元单价</h4>
          	    <FormItem :prop="'details.' + index + '.usd'" :rules="ruleValidate.usd">
          	    	<Input v-model.trim="item.usd" type="number" :clearable="true" placeholder="请输入美元单价" style="width: 160px;"></Input>
          	    </FormItem>
          	  </div>
          		<Button type="error" size="small" style="margin-left: 10px; margin-top: 5px; width: 40px; height: 25px;" @click="removeRule(index)"  v-if="index != 0">删除</Button>
          	</div>
          	<div style="display: flex; justify-content: flex-end; margin-top: 5px; margin-right: -5px;">
          		<Button type="info" size="small" @click="addRuleHandle">添加</Button>
          	</div>
          </div>
				</Form>
				<div style="text-align: center; margin-top: 30px;">
					<Button @click="cancelModal">返回</Button>
					<Button style="margin-left: 20px" :loading="submitFlag" type="primary" @click="submit">确定</Button>
				</div>
			</div>
		</Modal>

    <!-- 规则导入弹窗 -->
    <Modal title="规则导入" v-model="exportRules" :mask-closable="false" width="600px"
     @on-cancel="cancelModal">
    	<Form ref="formobj" :model="formobj" :rules="formobjRule" :label-width="100" :label-height="100"
    	 inline style="font-weight:bold;">
    		<FormItem label="文件"  style="width:510px">
    			<Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/"
    			:action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
    			:on-progress="fileUploading">
    				<div style="padding: 20px 0">
    					<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
    					<p>点击或拖拽文件上传</p>
    				</div>
    			</Upload>
    	    <ul class="ivu-upload-list" v-if="file" style="width: 100%;">
    	      <li class="ivu-upload-list-file ivu-upload-list-file-finish">
    	        <span><Icon type="ios-folder" />{{file.name}}</span>
    	        <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
    	      </li>
    	    </ul>
    			<div style="width: 100%;">
    				<Button type="primary" icon="ios-download" @click="downloadFile">下载模板文件</Button>
    				<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
    				<a ref="downloadLink" style="display: none"></a>
    			</div>
    		</FormItem>
    		<FormItem label="计费规则名称" prop="name">
    			<Input placeholder="请输入计费规则名称" :maxlength="50" v-model.trim="formobj.name" clearable class="inputSty" />
    		</FormItem>
    		<FormItem label="生效日期" prop="effectiveDate">
    		  <DatePicker type="date" v-model="formobj.effectiveDate" placeholder="选择生效日期" class="inputSty" />
    		</FormItem>
        <FormItem label="定价流量单位" prop="flowUnit">
        	<Select v-model="formobj.flowUnit" placeholder="请选择定价流量单位" clearable class="inputSty">
        		<Option value='1'>GB</Option>
        		<Option value='2'>MB</Option>
        	</Select>
        </FormItem>
    	</Form>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
      	<Button @click="cancelModal">取消</Button>
      	<Button type="primary" :loading="uploadLoading" @click="handleUpload">确定</Button>
      </div>
    </Modal>

    <!-- 没导入数据弹窗 -->
    <Modal title="部分导入失败数据" v-model="importFailModal" :footer-hide="true" :mask-closable="false"
      width="600px">
    	<Table ref="selection" :columns="FailColumns" :data="FailTableData" :ellipsis="true"></Table>
    </Modal>

    <Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>

    <a ref="downloadLink" style="display: none"></a>
  </Card>
</template>

<script>
	import {
		queryA2ZBillPrice,
    addA2ZBillPriceRule,
    updateA2ZBillPriceRule,
    delA2ZBillPriceRule,
    approveA2ZRule,
    getMccOperators,
    queryRuleDeatil,
    exportRuleDeatil,
    uploadRule,
	} from "@/api/customer/a2zBillPriceMngr.js";
	import {
		getAttributableChannelList
	} from '@/api/channel.js'
	import {
		opsearchAll,
	} from '@/api/operators';
  import Vue from 'vue';
  export default {
		components: {},
		data() {
      const validateMccOperatorUniqueness = (rule, value, callback) => {
        const matches = rule.field.match(/details\.(\d+)\.operatorId/);
        const currentIndex = parseInt(matches[1], 10); // 提取索引并转换为整数

        // 检查是否已存在同一MCC下operatorId为空的情况
        const hasEmptyOperatorIdForSameMcc = this.addRule.details.some((detail, index) => {
          return index !== currentIndex && detail.mcc === this.addRule.details[currentIndex].mcc && !detail.operatorId;
        });

        // 检查 mcc 和 operatorId 的组合是否已存在（不包括当前正在验证的这一项）
        const seenPairs = new Set();
        this.addRule.details.forEach((detail, index) => {
          if (index !== currentIndex) { // 排除当前正在验证的这一项
            const key = `${detail.mcc}-${detail.operatorId}`;
            seenPairs.add(key);
          }
        });

        // 检查当前项是否与已存在的组合重复
        const currentKey = `${this.addRule.details[currentIndex].mcc}-${value}`;
        if (seenPairs.has(currentKey)) {
          callback(new Error('国家 + 运营商 的组合必须唯一'));
        } else if (hasEmptyOperatorIdForSameMcc) {
          callback(new Error('此国家已有为空运营商'));
        } else {
          callback(); // 没有错误
        }

      };
			const checkCny = (rule, value, callback) => {
        const matches = rule.field.match(/details\.(\d+)\.cny/);
        const index = parseInt(matches[1], 10); // 提取索引并转换为整数
				if (!(value || this.addRule.details[index].hkd || this.addRule.details[index].usd)) {
          callback(new Error('请至少填写其中一个单价'));
				} else if (!(value > 0 || this.addRule.details[index].hkd > 0 || this.addRule.details[index].usd > 0)) {
          callback(new Error('至少一个单价大于0'));
        } else {
					callback();
				}
			};
      const checkHkd = (rule, value, callback) => {
        const matches = rule.field.match(/details\.(\d+)\.hkd/);
        const index = parseInt(matches[1], 10); // 提取索引并转换为整数
      	if (!(value || this.addRule.details[index].cny || this.addRule.details[index].usd)) {
          callback(new Error('请至少填写其中一个单价'));
      	} else if (!(value > 0 || this.addRule.details[index].cny > 0 || this.addRule.details[index].usd > 0)) {
          callback(new Error('至少一个单价大于0'));
        } else {
      		callback();
      	}
      };
      const checkUsd = (rule, value, callback) => {
        const matches = rule.field.match(/details\.(\d+)\.usd/);
        const index = parseInt(matches[1], 10); // 提取索引并转换为整数
      	if (!(value || this.addRule.details[index].cny || this.addRule.details[index].hkd)) {
          callback(new Error('请至少填写其中一个单价'));
      	} else if (!(value > 0 || this.addRule.details[index].cny > 0 || this.addRule.details[index].hkd > 0)) {
          callback(new Error('至少一个单价大于0'));
        } else {
      		callback();
      	}
      };

			return {
				searchObj: {
					name: "", //规则名称
				},
        title: "",
        id: '',
				total: 0,
				pageSize: 10,
				page: 1,
				currentPage: 1,
        index: 0,
				loading: false,
				searchloading: false,
				submitFlag: false,
        uploadLoading: false,
				ruleModal: false, //新建规则弹窗
        spinShow: false,
        exportRules: false, //导入规则弹窗
        importFailModal: false,
        formobj: {
          name: '',
          effectiveDate: '',
          flowUnit: '',
        },
        file: null,
        uploadUrl: '',
        message: '文件仅支持csv格式文件,大小不能超过10MB',
        addRule: {
          name: '',
          effectiveDate: '',
          flowUnit: '',
          details: [{
            index: 0,
            mcc: '',
            countryName: '',
            operatorId: '' ,
            operatorName: '',
            cny: '',
            hkd: '',
            usd: '',
          }]
        },
        operatorId: '',
        originData: {},
        formobjRule: {},
        mccList: [],
        operatorsByMcc: [],
        filteredOperators: [], // 存储每个索引对应的过滤后的运营商列表
				tableData: [],
        FailTableData: [],
        modelData: [{
          'Country or region': '多个国家用数显"|"分割',
          'Operators': '',
          'USD unit price': '',
          'HKD unit price': '',
          'CNY unit price': '',
        }],
				columns: [{
						title: "规则名称",
						key: "name",
						align: "center",
						minWidth: 150,
						tooltip: true
					},
					{
						title: "生效日期",
            key: "effectiveDate",
						align: "center",
						minWidth: 150,
						tooltip: true
					},
          {
          	title: "详情",
            slot: "view",
          	minWidth: 120,
            align: 'center'
          },
          {
          	title: "操作",
            slot: "action",
            align: "center",
          	minWidth: 250,
          },
          {
          	title: "状态",
            key: "status",
            align: "center",
          	minWidth: 120,
            render: (h, params) => {
            	const row = params.row
            	const text =
                row.status == '1' ? "新建待审批" :
                row.status == '2' ? "正常" :
                row.status == '3' ? "修改待审批" :
                row.status == '4' ? "删除待审批" :
                row.status == '5' ? "审批不通过" :
                row.status == '6' ? "删除"
                : ''
            	return h('label', text)
            },
          },
          {
          	title: "审批操作",
            slot: "approval",
            align: "center",
          	minWidth: 200,
            fixed: 'right',
          }
				],
        modelColumns: [{
        		title: 'Country or region',
        		minWidth: '200',
            key: 'Country or region',
        	},
        	{
        		title: 'Operators',
        		minWidth: '150',
            key: 'Operators',
        	},
          {
          	title: 'USD unit price',
          	minWidth: '150',
            key: 'USD unit price',
          },
          {
          	title: 'HKD unit price',
          	minWidth: '150',
            key: 'HKD unit price',
          },
          {
          	title: 'CNY unit price',
          	minWidth: '150',
            key: 'CNY unit price',
          }
        ],
        FailColumns: [{
        		title: "国家",
        		key: "countryName",
        		align: "center",
        		minWidth: 120,
        		tooltip: true
        	},
        	{
        		title: "运营商",
            key: "operatorName",
        		align: "center",
        		minWidth: 120,
        		tooltip: true
        	},
          {
          	title: "人民币单价",
            key: "cny",
          	align: "center",
          	minWidth: 100,
          	tooltip: true,
          },
          {
          	title: "港元单价",
            key: "hkd",
          	align: "center",
          	minWidth: 100,
          	tooltip: true,
          },
          {
          	title: "美元单价",
            key: "usd",
          	align: "center",
          	minWidth: 100,
          	tooltip: true,
          },
        ],
        ruleValidate: {
					name: [{
						required: true,
						message: "规则名称不能为空",
						trigger: "change",
					}],
          effectiveDate: [{
            required: true,
            type: 'date',
            message: '生效日期不能为空',
            trigger: 'change'
          }],
          flowUnit: [{
          	required: true,
          	message: "定价流量范围不能为空",
          	// trigger: "change",
          }],
          mcc: [{
						required: true,
						message: "国家不能为空",
						trigger: "change",
					}],
          operatorId: [{
						validator: validateMccOperatorUniqueness,
						// trigger: "change"
					}],
          cny: [{
						validator: checkCny,
						// trigger: "change"
					}, {
						validator: (rule, value, cb) => {
						  // 如果 value 是空字符串、undefined 或者 null，则不进行校验
						  if (!value || value === '') {
						    return cb(); // 直接返回，不传入错误信息
						  }

						  // 正则表达式用于匹配最多8位整数和8位小数
						  var str = /^(([1-9]\d{0,7})|0)(\.\d{0,8})?$/;

						  // 使用正则表达式进行校验
						  if (str.test(value)) {
						    return cb(); // 校验成功，直接返回，不传入错误信息
						  } else {
						    // 校验失败，返回错误信息
						    return cb(new Error('最高支持8位整数和8位小数正数或零'));
						  }
						},
						trigger: 'blur', // 或者 'change'，取决于你希望何时触发校验
						message: '最高支持8位整数和8位小数正数或零' // 错误提示信息
					}],
          hkd: [{
						validator: checkHkd,
						// trigger: "change"
					}, {
						validator: (rule, value, cb) => {
						  // 如果 value 是空字符串、undefined 或者 null，则不进行校验
						  if (!value || value === '') {
						    return cb(); // 直接返回，不传入错误信息
						  }

						  // 正则表达式用于匹配最多8位整数和8位小数
						  var str = /^(([1-9]\d{0,7})|0)(\.\d{0,8})?$/;

						  // 使用正则表达式进行校验
						  if (str.test(value)) {
						    return cb(); // 校验成功，直接返回，不传入错误信息
						  } else {
						    // 校验失败，返回错误信息
						    return cb(new Error('最高支持8位整数和8位小数正数或零'));
						  }
						},
						trigger: 'blur', // 或者 'change'，取决于你希望何时触发校验
						message: '最高支持8位整数和8位小数正数或零' // 错误提示信息
					}],
          usd: [ {
						validator: checkUsd,
						// trigger: "change"
					}, {
						validator: (rule, value, cb) => {
						  // 如果 value 是空字符串、undefined 或者 null，则不进行校验
						  if (!value || value === '') {
						    return cb(); // 直接返回，不传入错误信息
						  }

						  // 正则表达式用于匹配最多8位整数和8位小数
						  var str = /^(([1-9]\d{0,7})|0)(\.\d{0,8})?$/;

						  // 使用正则表达式进行校验
						  if (str.test(value)) {
						    return cb(); // 校验成功，直接返回，不传入错误信息
						  } else {
						    // 校验失败，返回错误信息
						    return cb(new Error('最高支持8位整数和8位小数正数或零'));
						  }
						},
						trigger: 'blur', // 或者 'change'，取决于你希望何时触发校验
						message: '最高支持8位整数和8位小数正数或零' // 错误提示信息
					}],
				},
			};
		},

    created() {
      this.filteredOperators = this.addRule.details.map(() => []);
    },

    watch: {
      // 监听 addRule.details 的变化，并在变化后更新 filteredOperators
      'addRule.details': {
        handler(newVal) {
          if (Array.isArray(newVal)) {
            this.updateFilteredOperators(newVal); // 假设这是你的更新方法
          }
        },
        deep: true,
      },
    },

		mounted() {
      this.goPageFirst(1);
      this.getLocalList();
      this.getMccOperators();
		},

		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				queryA2ZBillPrice({
					size: 10,
					current: page,
					name: this.searchObj.name,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = Number(res.count)
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},

			//表格数据加载
			loadByPage(page) {
				this.goPageFirst(page);
			},

			//搜索
			search() {
				this.searchloading = true
				this.goPageFirst(1);
			},

      // 自动回填生效日期
      setTodayDate() {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0'); // 注意月份是从0开始的，所以要+1
        const date = String(today.getDate()).padStart(2, '0');
        this.addRule.effectiveDate = `${year}-${month}-${date}`;
      },

			//新建
			addItem() {
        this.title = "新建规则"
        this.setTodayDate()
				this.ruleModal = true
			},

      // 修改
      updateItem(row) {
        this.title = "修改规则"
        this.addRule.name = row.name
        this.addRule.effectiveDate = row.effectiveDate
        this.addRule.flowUnit = row.flowUnit
        this.id = row.id
        this.getEditData(row)
        this.ruleModal = true
      },

      // 新增/修改 提交
      submit() {
      	this.$refs["addRule"].validate((valid) => {
          // 新增
          var addData = JSON.parse(JSON.stringify(this.addRule));
          addData.effectiveDate = this.dateTransfer(addData.effectiveDate)
          addData.details.forEach(detail => {
              // 检查并转换cny
              if (detail.cny === "") {
                  detail.cny = 0;
              }
              // 检查并转换hkd
              if (detail.hkd === "") {
                  detail.hkd = 0;
              }
              // 检查并转换usd
              if (detail.usd === "") {
                  detail.usd = 0;
              }
          });
          // 修改
          let updataData = JSON.parse(JSON.stringify(addData))
          if (this.title == "修改规则") {
            updataData['id'] = this.id
            updataData['isCover'] = true
          }
          if (valid) {
            let func = this.title == "新建规则" ? addA2ZBillPriceRule : updateA2ZBillPriceRule
            let resultData = this.title == "新建规则" ? addData : updataData
      			this.submitFlag = true;
      			func(resultData).then(res => {
      				if (res && res.code == '0000') {
      					setTimeout(() => {
      						this.$Notice.success({
      							title: "操作提醒：",
      							desc: "操作成功！"
      						});
      						this.submitFlag = false;
      						this.addRule = false;
                  this.cancelModal()
      						this.goPageFirst(this.currentPage);
      					}, 1500);
      				} else {
      					this.submitFlag = false;
      					throw res
      				}
      			}).catch((err) => {
      				this.submitFlag = false;
      			}).finally(() => {

      			})
      		}
      	})
      },

      // 删除
      deleteItem(row) {
        this.$Modal.confirm({
          title: '确认删除？',
          onOk: () => {
            delA2ZBillPriceRule({
              id: row.id
            }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirst(1)
              } else {
                throw res
              }
            }).catch((err) => {
            })
          }
        });
      },

      // 导出
      exportHandle(row) {
        exportRuleDeatil({
          id: row.id,
          name: row.name,
          mcc: row.mcc,
          size: -1,
          current: -1,
        })
        .then((res) => {
          const content = res.data;
      		let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
          if ("download" in document.createElement("a")) {
            // 支持a标签download的浏览器
            const link = document.createElement("a"); // 创建a标签
            let url = URL.createObjectURL(content);
            link.download = fileName;
            link.href = url;
            link.click(); // 执行下载
            URL.revokeObjectURL(url); // 释放url
          } else {
            // 其他浏览器
            navigator.msSaveBlob(content, fileName);
          }
        })
          .catch();
      },

      // 导入
      importHandle(row) {
        this.originData.id = row.id
        this.originData.name = row.name
        this.exportRules = true
      },

      handleUpload() {
      	if (!this.file) {
      		this.$Message.warning('请选择需要上传的文件')
      		return
      	} else {
      		this.$refs.formobj.validate(valid => {
      			if (valid) {
              this.uploadLoading = true
              let effectiveDate  = this.formobj.effectiveDate ? this.dateTransfer(this.formobj.effectiveDate) : ""
      				let formData = new FormData()
      				formData.append('id', this.originData.id)
      				formData.append('file', this.file)
      				formData.append('name', this.formobj.name)
      				formData.append('flowUnit', this.formobj.flowUnit ? this.formobj.flowUnit : '')
      				formData.append('effectiveDate', effectiveDate)
      				uploadRule(formData).then(res => {
      					if (res.code === '0000') {
      						this.$Notice.success({
      							title: '操作成功',
      							desc: '操作成功'
      						})
      						this.uploadLoading=false
                  this.currentPage = 1
                  this.goPageFirst(1)
                  if (res.data.length == 0) {
                    this.cancelModal()
                  } else {
                    this.importFailModal = true
                    const newData = res.data.map(item => {
                        return {
                            ...item,
                            hkd: item.hkd === null ? "" : item.hkd,
                            usd: item.usd === null ? "" : item.usd,
                            cny: item.cny === null ? "" : item.cny
                        };
                    });
                    this.FailTableData = newData
                  }
      					} else {
      						throw res
      					}
      				}).catch((err) => {
      					console.log(err)
      				}).finally(() => {
      					this.uploadLoading=false
      					this.cancelModal()
      				})
      			}
      		})
      	}
      },

      // 通过/不通过
      approve(row, type) {
        this.$Modal.confirm({
        	title: type == 1 ? '确认执行审核通过？' : '确认执行审核不通过？',
        	onOk: () => {
        		approveA2ZRule({
              id: row.id,
              authStatus: type
            }).then(res => {
        			if (res && res.code == '0000') {
        				this.$Notice.success({
        					title: '操作提示',
        					desc: '操作成功'
        				})
        				this.loadByPage(this.page);
        			} else {
        				throw res
        			}
        		}).catch((err) => {
        		})
        	}
        });
      },

      cancelModal() {
        // 手动重置表单最有效
        this.addRule = {
          name: '',
          effectiveDate: '',
          flowUnit: '',
          details: [
            {
              index: 0,
              mcc: '',
              countryName: '',
              operatorId: '',
              operatorName: '',
              cny: '',
              hkd: '',
              usd: '',
            },
          ]
        }
        this.$refs["addRule"].resetFields();
        this.ruleModal = false;
        //导入弹窗
        this.file = null
        this.$refs["formobj"].resetFields();
        this.exportRules = false
      },

      // 详情查看
      viewItem(row) {
        this.$router.push({
          name:'a2zBillPriceDetail',
          query: {
            rowData : encodeURIComponent(JSON.stringify(row)),
          }
        })
      },

      // 动态添加
      addRuleHandle() {
				this.index++;
				this.addRule.details.push({
					index: this.index,
					mcc: '',
					operatorId: '',
					cny: '',
					hkd: '',
					usd: '',
				});
			},

      // 动态删除
      removeRule(index) {
        this.addRule.details.splice(index, 1);
        this.index--;
      },

      dateTransfer(dateStr) {
        var date = new Date(dateStr);

        // 使用getFullYear(), getMonth() + 1, getDate() 方法来获取年、月、日
        // 注意getMonth()返回的是0-11的整数，所以需要加1来转换为常见的月份表示
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2); // 确保月份是两位数
        var day = ('0' + date.getDate()).slice(-2); // 确保日期是两位数

        // 将年、月、日拼接成 "yyyy-mm-dd" 格式
        var formattedDate = year + '-' + month + '-' + day;
        return formattedDate;
      },

      handleCountryChange(index, mcc) {
        const country = this.mccList.find(c => c.mcc === mcc);
        if (country) {
          Vue.set(this.addRule.details[index], 'mcc', mcc);
          Vue.set(this.addRule.details[index], 'countryName', country.countryCn);
          this.filteredOperators[index] = this.operatorsByMcc[mcc] || [];
        }
      },

      handleOperatorChange(index, operatorId) {
        const operator = this.filteredOperators[index].find(o => o.id === operatorId);
        if (operator) {
          Vue.set(this.addRule.details[index], 'operatorId', operatorId);
          Vue.set(this.addRule.details[index], 'operatorName', operator.operatorName);
        } else {
          // 删除operatorId的情况下
          Vue.set(this.addRule.details[index], 'operatorId', null);
          Vue.set(this.addRule.details[index], 'operatorName', null);
        }
      },

      handleBeforeUpload(file) {
        if (!/^.+(\.csv)$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式不正确',
            desc: '文件 ' + file.name + ' 格式不正确，请上传.csv。'
          })
        } else {
          if (file.size > 10 * 1024 * 1024) {
            this.$Notice.warning({
              title: '文件大小超过限制',
              desc: '文件 ' + file.name + '超过了最大限制范围10MB'
            })
          } else {
            this.file = file
          }
        }
        return false
      },

      fileUploading(event, file, fileList) {
      	this.message = '文件上传中、待进度条消失后再操作'
      },

      fileSuccess(response, file, fileList) {
      	this.message = '请先下载模板文件，并按格式填写后上传'
      },

      handleError(res, file) {
      	var v = this
      	setTimeout(function() {
      		v.uploading = false;
      		v.$Notice.warning({
      			title: '错误提示',
      			desc: "上传失败！"
      		});
      	}, 3000);
      },

      //下载模板文件
      downloadFile() {
      	this.$refs.modelTable.exportCsv({
      		filename: "规则导入文件",
      		type:'csv',
      		columns: this.modelColumns,
      		data: this.modelData
      	})
      },

      removeFile() {
      	this.file = ''
      },

			/** -------------------------------------------------------------*/
			//获取国家
			getLocalList() {
				opsearchAll().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.mccList = list;
						this.mccList.sort(function(str1, str2) {
							return str1.countryCn.localeCompare(str2.countryCn);
						});
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
      // 获取供应商
			getMccOperators() {
				getMccOperators({}).then(res => {
					if (res.code == '0000') {
						this.operatorsByMcc = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {})
			},

      //修改调详情接口回显数据
      getEditData: function(row) {
        this.spinShow = true
      	queryRuleDeatil({
          id: row.id,
          name: row.name,
          mcc: '',
      		size: -1,
      		current: -1,
      	}).then(res => {
      		if (res.code == '0000') {
            this.addRule.details = res.data
      		}
      	}).catch((err) => {
      		console.error(err)
      	}).finally(() => {
          this.spinShow = false
      	})
      },

      // 修改的时候回显运营商列表源数组
      updateFilteredOperators(newVal) {
        // 根据 addRule.details 中的每个条目的 mcc 来更新 filteredOperators
        this.filteredOperators = this.addRule.details.map(detail => {
          // 假设 operatorsByMcc[detail.mcc] 存在且是一个数组
          return this.operatorsByMcc[detail.mcc] || []; // 如果不存在，则返回一个空数组
        });
      },
    },
	};
</script>

<style>
	.flowInputSty {
		width: 250px;
	}

	/* 去掉input为number的上下箭头 */
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}

	input[type="number"] {
		-moz-appearance: textfield;
	}

  .billRuleSty {
    display: flex;
  }

  .billRuleBox {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
    align-items: center;
    margin-right: 30px;
  }

  .inputSty {
  	width: 250px;
  }
</style>
