<template>
  <!-- 出账管理 -->
  <Card>
    <Form ref="form" :label-width="80" :rules="rule" :model="form" inline>
      <FormItem label="统计日期" prop="startTime">
        <DatePicker
          format="yyyyMMdd"
          @on-change="getTime"
          :editable="false"
          type="daterange"
          :placeholder="$t('Period')"
          clearable
          style="width: 200px; margin: 0 10px 0 0"
        >
        </DatePicker>
      </FormItem>
      <FormItem label="客户名称">
        <Input
          v-model="form.corpName"
          placeholder="请输入客户名称"
          :clearable="true"
          style="width: 190px"
        >
        </Input>
      </FormItem>

      <Button
        type="primary"
        icon="md-search"
        v-preventReClick
        :loading="searchloading"
        @click="search()"
        v-has="'search'"
        >搜索</Button
      >&nbsp;&nbsp;
    </Form>
    <!-- 表格 -->
    <Table
      :columns="columns12"
      :data="talbedata"
      style="width: 100%; margin-top: 50px"
      :loading="loading"
    >
      <!-- <template slot-scope="{ row, index }" slot="Operators">
					<a @click="viewOperators(row)">查看更多</a>
				</template> -->
      <!-- <template slot-scope="{ row, index }" slot="action">
					<Button v-has="'update'" type="primary" size="small" style="margin-right: 5px"
						@click="updaterow(row)">修改</Button>
					<Button v-has="'delete'" type="error" size="small" style="margin-right: 5px"
						:loading="deleteLoading" @click="remove(row)">删除</Button>
				</template> -->
    </Table>
    <!-- 分页 -->
    <div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px">
      <Page
        :total="total"
        :current.sync="currentPage"
        show-total
        show-elevator
        @on-change="goPage"
      />
    </div>
  </Card>
</template>

<script>
import { getCorpList } from "@/api/product/package/batch";
import mixin from "@/mixin/validate";
import {
	getOnlineIncomeList,
	StatChannelincomeInfoDay,
  exportTotalTask,
  exportDetailTask,
} from "@/api/finance/online";
import {
  getCorpIncomeList,
  createInvoiceNo,
  updateChannel,
  OpexportTotalTask,
  OpexportDetailTask,
  exportInvoice,
  channelAuth,
  batchExportInvoice,
  exportA2Zdeatil,
} from "@/api/finance/corp";
import { exportOtInvoice } from "@/api/finance/other";
import invoiceTemplate from "@/components/invoice/invoiceTemp";
import {
  CustomerPage,
  queryCustomer,
  UpdateCustomer,
  exportflowSettle,
  exportflowUsed,
  exportPackage,
  exportPackageUsed,
  exportSummaryFile,
  exportTaxation,
  generateActualBill,
  otherAuth,
} from "@/api/finance/other";
const math = require("mathjs");
export default {
  mixins: [mixin],
  components: {
    invoiceTemplate,
  },
  data() {
    const validatePositiveNum = (rule, value, callback) => {
      var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
      if (!value || str.test(value)) {
        callback();
      } else {
        callback(new Error(rule.message));
      }
    };
    const validateNum = (rule, value, callback) => {
      var str1 = value;
      if (value.substr(0, 1) === "-") {
        str1 = value.substr(1, value.length);
      }
      var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
      if (!str1 || str.test(str1)) {
        callback();
      } else {
        callback(new Error(rule.message));
      }
    };
    return {

      time: [],
      isSearch: true,
      showUpOnline: false,
      rule: {
        startTime: [
          {
            required: true,
            message: "时间不能为空",
          },
        ],
      },
      columns12: [
        {
          title: "统计日期",
          key: "statTime",
          align: "center",
        },
        {
          title: "客户名称",
          key: "corpName",
          align: "center",
        },
        {
          title: "币种",
          key: "currency",
					align: "center",
					minWidth: 100,
						render: (h, params) => {
							const row = params.row;
							const text = row.currency == '156' ? "CNY" : row.currency == '840' ? "USD" : row.currency == '344' ? "HKD" :
								'';
							return h('label', text);
						}
        },
        {
          title: "当前初始余额",
          key: "initialDeposit",
          align: "center",
        },
        {
          title: "订单总额",
          key: "orderAmount",
          align: "center",
        },
        {
          title: "退订订单总额",
          key: "unsubscribeAmount",
          align: "center",
        },
        {
          title: "代销充值总额",
          key: "chargeAmount",
          align: "center",
        },
        {
          title: "当日押金应剩余额",
          key: "remainDeposit",
          align: "center",
        },
        {
          title: "当日押金实际余额",
          key: "deposit",
          align: "center",
        },
      ],
      form: {
        corpName: "",
        startTime: null,
        endTime: null,
      },
      invoiceForm: [
        {
          invoiceNameDesc: "",
          billingPeriod: "",
        },
      ],
      data: [],
      searchObj: {},
      talbedata: [],
      row: {},
      currentPage: 1,
      total: 0,
      pageSize: 10,
      page: 1,
      file: null, //税费文件
      Customerfile: null, //客户账单文件
      uploadUrl: "",
      downloading: false,
      exportModal: false,
      exportModalr: false,
      updateModal: false,
      updateOTModal: false,
      CerateModal: false,
      selectModeModal: false,
      searchloading: false,
      Cerateloading: false,
      Invoiceloading: false,
      OTloading: false,
      updateLoading: false,
      accountingLoading: false,
      batchInvoiceLoading: false,
      taskName: "",
      taskId: "",
      spanData: [],
      loading: false,
      default:
        "Payment Instruction \nPlease remit payment to beneficiary China Mobile International Limited by telegraph transfer\nAccount Name: China Mobile International Limited\nName of Bank: The Hongkong & Shanghai Banking Corporation Limited\nBank Address: 1 Queen's Road, Central, Hong Kong\nAccount Number: 848-021796-838\nSWIFT Code: HSBCHKHHHKH\n*Please quote our invoice number(s) with your payment instructions to the bank upon remittance.\n*Please email remittance <NAME_EMAIL> for update of your account.\nThis computer generated document requires no signature.",
      remind: false,
      taskIds: [],
      taskNames: [],
      searchBeginTime: "",
      searchEndTime: "",
      type: "",
      cooperationMode: "",
      /**
       * ---------------生成发票相关----------------
       */
      id: null, //选择行自增主键
      invoiceNoCopy: "",
      invoice_model: false, //预览模态框
      invoiceType: "",
      billType: "",

    };
  },
  created() {},
  mounted() {

  },
  methods: {
    hanldeDateClear() {
      this.startTime = "";
      this.endTime = "";
    },
    handleDateChange(dateArr) {
      let beginDate = this.time[0] || "";
      let endDate = this.time[1] || "";
      if (beginDate == "" || endDate == "") {
        return;
      }
      [this.startTime, this.endTime] = dateArr;
		},
		getTime: function (times, type) {
      this.form.startTime = times[0];
      this.form.endTime = times[1];
    },
    getTableData(page) {
			this.page = page;
			// console.log('----',this.form.startTime+"",this.form.endTime.toString(),123);
      //线上收入
      StatChannelincomeInfoDay({
				corpName: this.form.corpName.replace(/\s+/g, ''),
        startTime: this.form.startTime,
        endTime: this.form.endTime,
        pageNum: this.page,
        pageSize: 10,
      }).then((res) => {
        if (res.code === "0000") {
          this.total = res.count;
          this.talbedata = res.data;
          this.searchloading = false;
          this.getSpanData(this.data);
        }
      });

      this.searchloading = false;
    },
    search() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.isSearch = true;
          this.searchloading = true;
          this.getTableData(1);
          this.currentPage = 1
        }
      });
    },
    goPage(page) {
			this.getTableData(page)
    },

    test(str) {
      var re = /(\d{1,3})(?=(\d{3})+(?:$|\.))/g;
      return (str + "").replace(re, "$1,");
    },
    formatNumber(num) {
      // 首先，将数字除以100并保留两位小数
      const result = (num / 100).toFixed(2);
      const formatted = new Intl.NumberFormat("en-US", {
        minimumFractionDigits: 2, // 确保总是显示两位小数
        maximumFractionDigits: 2, // 最多显示两位小数
        useGrouping: true, // 启用千位分隔符
      }).format(parseFloat(result));
      return formatted;
    },

    //计算需要合并的单元格
    getSpanData(data) {
      var t = this;
      var pos = 0;
      t.spanData = [];
      data.forEach(function (item, index) {
        if (index === 0) {
          t.spanData.push(1);
          pos = 0;
        } else {
          if (
            data[index].salesChannel === data[index - 1].salesChannel &&
            data[index].statTime === data[index - 1].statTime
          ) {
            t.spanData[pos] += 1;
            t.spanData.push(0);
          } else {
            t.spanData.push(1);
            pos = index;
          }
        }
      });
    },
  },
};
</script>

<style>
#space {
  /* height: 30px;
		line-height: 30px; */
  font-size: 12px;
  white-space: pre-line;
  list-style: none;
}
.task-name {
  display: inline-block; /* 或者 block，取决于你的布局需求 */
  width: 300px; /* 根据需要设置合适的宽度 */
  /* white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; */
  word-break: break-all;
  padding: 5px; /* 内边距 */
  margin-bottom: 10px; /* 外边距 */
}
</style>
