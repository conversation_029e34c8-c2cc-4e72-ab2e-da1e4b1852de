<template>
	<div style="width: 100%; padiing: 16px">
		<div>
			<div>
				<Form ref="formObj" :model="formObj" :label-width="170" :rules="ruleAddValidate">
					<Card style="margin-bottom: 16px;padiing: 16px">
					<p slot="title" style="color: rgb(87,163,244);">基础信息</p>
					<Row justify="center">
						<Col span="12">
						<FormItem label="渠道商名称" prop="corpName">
							<Input v-model="formObj.corpName" :readonly="typeFlag == 'info'" maxlength="50"
								:clearable="typeFlag != 'info'" placeholder="请输入渠道商名称" class="inputSty"></Input>
            </FormItem>
						</Col>
						<Col span="12">
						<FormItem label="渠道商状态" prop="channelStatus">
							<Select v-model="formObj.channelStatus" placeholder="请选择渠道商状态"
								:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty">
								<Option v-for="item in purchaseStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="12">
						<FormItem label="公司名称" prop="companyName">
							<Input v-model="formObj.companyName" :clearable="true" placeholder="请输入公司名称" maxlength="200"
								class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="地址" prop="address">
							<Input v-model="formObj.address" placeholder="请输入地址" :clearable="true"
								class="inputSty"></Input>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="12">
						<FormItem label="EBS Code" prop="ebsCode">
							<Input v-model="formObj.ebsCode" :readonly="typeFlag == 'info'"
								:clearable="typeFlag != 'info'" maxlength="50" placeholder="请输入EBS Code"
								class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12">
            <FormItem label="联系人邮箱" prop="mail">
            	<Input v-model="formObj.mail" type="email" :readonly="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" maxlength="50" placeholder="请输入联系人邮箱"
            		class="inputSty"></Input>
            </FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="12">
						<FormItem label="货币种类" prop="currencyCode">
							<Select v-model="formObj.currencyCode" placeholder="请选择货币种类" :disabled="typeFlag != 'add'"
								:clearable="false" class="inputSty">
								<Option value="156">人民币</Option>
								<Option value="344">港币</Option>
								<Option value="840">美元</Option>
							</Select>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="渠道合作模式" prop="channelCooperationMode">
							<Select v-model="formObj.channelCooperationMode" placeholder="请选择渠道合作模式" multiple
								:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty"
								@on-change="getpackageInfo">
								<Option :disabled="modeList.includes('1')" value='1'>代销</Option>
								<Option :disabled="modeList.includes('2')" value='2'>A2Z</Option>
								<Option :disabled="modeList.includes('3')" value='3'>资源合作</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
          <Row>
          	<Col span="12">
          	<FormItem label="内部订单" prop="internalOrder">
          		<Select v-model="formObj.internalOrder" placeholder="请选择是否内部订单" :clearable="true"
          			class="inputSty">
          			<Option value="0">是</Option>
          			<Option value="1">否</Option>
          		</Select>
          	</FormItem>
          	</Col>
          	<Col span="12">
          	<FormItem label="退订规则" prop="unsubscribeRule" :rules="formObj.channelCooperationMode != '3' ?
          	ruleAddValidate.unsubscribeRule : [{required: false}]">
          		<Select v-model="formObj.unsubscribeRule" placeholder="请选择退订规则"
          			:readonly="typeFlag == 'info'" :disabled="typeFlag == 'info'"
          			:clearable="typeFlag != 'info'" class="inputSty">
          			<Option value="1">自然月内退订</Option>
          			<Option value="2">有效期内退订</Option>
          		</Select>
          	</FormItem>
          	</Col>
          </Row>
          <Row>
          	<Col span="12">
          	<FormItem label="销售经理" prop="salesMail">
              <Select v-model="formObj.salesMail" placeholder="请输入销售经理邮箱" filterable
              	:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty">
              	<Option v-for="(item,index) in salesMailList" :value="item" :key="index">{{ item }}</Option>
              </Select>
          	</FormItem>
          	</Col>
          	<Col span="12">
          	</Col>
          </Row>
					</Card>

          <Card style="margin-bottom: 16px;padiing: 16px">
					<p slot="title" style="color: rgb(87,163,244);">通知信息</p>
          <Row>
          	<Col span="12">
          	<FormItem label="激活通知" prop="activateNotification" :rules="formObj.channelCooperationMode != '3' ?
          	ruleAddValidate.activateNotification : [{required: false}]">
          		<Select v-model="formObj.activateNotification" placeholder="请选择是否激活通知"
          			:readonly="typeFlag == 'info'" :disabled="typeFlag == 'info'"
          			:clearable="typeFlag != 'info'" class="inputSty">
          			<Option value="1">开</Option>
          			<Option value="2">关</Option>
          		</Select>
          	</FormItem>
          	</Col>
          	<Col span="12">
          	<FormItem label="激活通知url" prop="activateNotificationUrl" :rules="formObj.activateNotification == '1' ?
          	ruleAddValidate.activateNotificationUrl : [{required: false}]">
          		<Input v-model="formObj.activateNotificationUrl" placeholder="请输入激活通知url"
          			:readonly="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty"
          			maxlength="255">
          		</Input>
          	</FormItem>
          	</Col>
          </Row>
          <Row>
          	<Col span="12">
          	<FormItem label="到期通知" prop="overdueNotify" :rules="formObj.channelCooperationMode != '3' ?
          	ruleAddValidate.overdueNotify : [{required: false}]">
          		<Select v-model="formObj.overdueNotify" placeholder="请选择到期通知"
          			:readonly="typeFlag == 'info'" :disabled="typeFlag == 'info'"
          			:clearable="typeFlag != 'info'" class="inputSty">
          			<Option value="1">开</Option>
          			<Option value="2">关</Option>
          		</Select>
          	</FormItem>
          	</Col>
          	<Col span="12">
          	<FormItem label="到期通知url" prop="overdueNotifyUrl" :rules="formObj.overdueNotify == '1' ?
          	ruleAddValidate.overdueNotifyUrl : [{required: false}]">
          		<Input v-model="formObj.overdueNotifyUrl" placeholder="请输入到期通知url"
          			:readonly="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty"
          			maxlength="255">
          		</Input>
          	</FormItem>
          	</Col>
          </Row>
          <Row>
          	<Col span="12">
          	<FormItem label="ESIM状态变更通知" prop="esimNotifySwitch">
          		<Select v-model="formObj.esimNotifySwitch" placeholder="请选择ESIM状态变更通知"
          			:readonly="typeFlag == 'info'" :disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty">
          			<Option value="1">开</Option>
          			<Option value="2">关</Option>
          		</Select>
          	</FormItem>
          	</Col>
          	<Col span="12">
          	<FormItem label="ESIM状态变更通知url" prop="esimNotifyUrl" :rules="formObj.esimNotifySwitch == '1' ?
          	ruleAddValidate.esimNotifyUrl : [{required: false}]">
          		<Input v-model="formObj.esimNotifyUrl" placeholder="请输入ESIM状态变更通知url"
          			:readonly="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty" maxlength="255">
          		</Input>
          	</FormItem>
          	</Col>
          </Row>
          <Row>
            <Col span="12" v-if="typeFlag === 'info'">
            <FormItem label="渠道商AppKey" prop="appkey">
            	<Input v-model="formObj.appkey" :readonly="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" maxlength="32" placeholder="请输入渠道商AppKey"
            		class="inputSty"></Input>
            </FormItem>
            </Col>
            <Col span="12" v-if="typeFlag === 'info'">
            <FormItem label="渠道商AppSecret" prop="appSecret">
            	<Input v-model="formObj.appSecret" :readonly="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" maxlength="32" placeholder="请输入渠道商AppSecret"
            		class="inputSty"></Input>
            </FormItem>
            </Col>
          </Row>
          <div style="display: flex; flex-wrap: wrap;">
          	<div style="width: 50%">
          		<FormItem v-for="(item, index) in formObj.packageUsePercentage" :key="index"
          			label="套餐用量提醒百分比(%)" :prop="'packageUsePercentage.' + index + '.value'" :rules="ruleAddValidate.value">
          			<div>
          				<Input class="input-text t inputSty" type="number" v-model="item.value"
          					:readonly="typeFlag == 'info'" :clearable="typeFlag != 'info'"
          					placeholder="请输入"></Input>&nbsp;&nbsp;&nbsp;&nbsp;
          				<Button type="error" v-if="item.index != 1 && typeFlag != 'info'" ghost @click="handleRemove(index)">删除</Button>
          			</div>
          		</FormItem>
          		<div style="width: 80px; margin-left: 150px;" v-if="typeFlag !== 'info'">
          			<Button type="success" ghost long @click="handleAdd" icon="md-add">添加</Button>
          		</div>
          	</div>
          	<div style="width: 50%">
          		<FormItem label="流量通知URL" prop="packageUseNotifyUrl">
          			<Input class="inputSty" v-model="formObj.packageUseNotifyUrl" :readonly="typeFlag == 'info'"
          				:clearable="typeFlag != 'info'" placeholder="请输入"></Input>
          		</FormItem>
          	</div>
          </div>
					</Card>

          <Card style="margin-bottom: 16px;padiing: 16px" v-if="formObj.channelCooperationMode.includes('1')">
					<p slot="title" style="color: rgb(87,163,244);">代销信息</p>
          <Row>
          	<Col span="12">
          	<FormItem label="可用金额提醒阀值" prop="depositNotify" :rules="formObj.channelCooperationMode.includes('1') ?
          	ruleAddValidate.depositNotify : [{required: false}]">
          		<Input v-model="formObj.depositNotify" :readonly="typeFlag == 'info'"
          			:clearable="typeFlag != 'info'" maxlength="13" placeholder="请输入可用金额提醒阀值"
          			class="inputSty"></Input>
          	</FormItem>
          	</Col>

          	<Col span="12">

          	</Col>
          </Row>
					<Row>
						<Col span="12">
						<FormItem label="渠道商类型" prop="channelType" :rules="formObj.channelCooperationMode.includes('1') ?
						ruleAddValidate.channelType : [{required: false}]">
							<Select v-model="formObj.channelType" placeholder="请选择渠道商类型"
								:readonly="typeFlag == 'info'" :disabled="typeFlag == 'info'"
								:clearable="typeFlag != 'info'" class="inputSty">
								<Option value="1">押金模式</Option>
								<Option value="2">预存模式</Option>
							</Select>
						</FormItem>
						</Col>
            <Col span="12">
            <FormItem label="购买折扣(%)" prop="discount">
            	<Input maxlength="3" v-model="formObj.discount" :readonly="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" placeholder="请输入购买折扣(%)" class="inputSty">
            	</Input>
            </FormItem>
            </Col>
						<!-- <Col span="12">

						<FormItem label="总预存款" prop="totalDeposit"
							v-show="formObj.channelType == '2' && typeFlag === 'info'">
							<Input v-model="formObj.totalDeposit" :readonly="typeFlag == 'info'"
								:disabled="typeFlag == 'info'" class="inputSty"> </Input>
						</FormItem>

						</Col> -->
					</Row>
          <Row>
          	<Col span="12">
          	<FormItem label="代销合约开始时间" prop="contractBeginTime" :rules="formObj.contractEndTime ?
            ruleAddValidate.contractBeginTime : [{required: false}]">
          		<DatePicker type="datetime" format="yyyy/MM/dd HH:mm:ss" placeholder="请选择代销合约开始时间"
          			:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'"
          			v-model="formObj.contractBeginTime" class="inputSty"></DatePicker>
          	</FormItem>
          	</Col>
          	<Col span="12">
          	<FormItem label="代销合约结束时间" prop="contractEndTime" :rules="formObj.contractBeginTime ?
            ruleAddValidate.contractEndTime : [{required: false}]">
          		<DatePicker type="datetime" format="yyyy/MM/dd HH:mm:ss" placeholder="请选择代销合约结束时间"
          			:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'"
          			v-model="formObj.contractEndTime" class="inputSty"></DatePicker>
          	</FormItem>
          	</Col>
          </Row>
          <Row>
          	<Col span="12">
          	<FormItem label="合约期承诺销售金额" prop="contractSellAmount">
          		<Input v-model="formObj.contractSellAmount" :readonly="typeFlag == 'info'"
          			:clearable="typeFlag != 'info'" maxlength="13" placeholder="请输入承诺销售金额"
          			class="inputSty"></Input>
          	</FormItem>
          	</Col>
            <Col span="12">
            <FormItem label="出账规则" prop="distributionAccountingPeriodId">
            	<Select v-model="formObj.distributionAccountingPeriodId" placeholder="请选择出账规则"
            		:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty">
            		<Option v-for="item in acountingList" :value="item.id" :key="item.id">{{ item.name }}
            		</Option>
            	</Select>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="24">
            <FormItem label="IMSI费规则" prop="consignmentImsiAmount">
            	<Select v-model="formObj.consignmentImsiAmount" placeholder="请选择IMSI费规则" multiple filterable
            		:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" style="width: 85%;">
            		<Option v-for="item in imsiFeeList" :value="item.id" :key="item.id">{{ item.ruleName }}</Option>
            	</Select>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="24">
            <FormItem label="短信模板" prop="dxSmsTemplateVo" :rules="formObj.channelCooperationMode.includes('1') ?
          	ruleAddValidate.dxSmsTemplateVo : [{required: false}]">
            	<Select filterable multiple v-model="formObj.dxSmsTemplateVo" placeholder="请选择短信模板"
            		:readonly="typeFlag == 'info'" :disabled="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" style="width: 85%;">
            		<Option v-for="(item,index) in tempList" :key="index" :value="JSON.stringify({
            			templateId: item.id,
            			templateName: item.templateName,
            			cooperationMode: '1',
            		})">{{ item.templateName }}</Option>
            	</Select>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="24">
            <FormItem label="可购买套餐组" prop="dxPackageInfosString" v-if="formObj.channelCooperationMode.includes('1')"
              :rules="formObj.channelCooperationMode.includes('1') ? ruleAddValidate.dxPackageInfosString : [{required: false}]">
            	<Select v-model="formObj.dxPackageInfosString" multiple placeholder="请选择可购买套餐组"
            		:disabled="typeFlag == 'info' || packageFlag" :clearable="typeFlag != 'info'"  style="width: 85%;">
            		<Option v-for="item in dxPackageList" :value="JSON.stringify({
            			groupId: item.groupId,
            			groupName: item.groupName,
            			channelCooperationMode: item.cooperationMode,
            		})" :key="item.groupId">{{ item.groupName }}
            		</Option>
            	</Select>
            </FormItem>
            </Col>
          </Row>
					<Row>
					  <Col span="12" v-if="typeFlag == 'info'">
					  <FormItem label="营销账户" prop="marketingAmount">
					  	<Input v-model="formObj.marketingAmount " :readonly="typeFlag == 'info'"
					  		:clearable="typeFlag != 'info'" placeholder="请输入营销账户" class="inputSty"></Input>
					  </FormItem>
					  </Col>
					  <Col span="12">
					  <FormItem label="信用账户" prop="creditAmount">
					  	<Input v-model="formObj.creditAmount " :readonly="typeFlag == 'info'"
					  		:clearable="typeFlag != 'info'" placeholder="请输入信用账户" class="inputSty"></Input>
					  </FormItem>
					  </Col>
					</Row>
          <!-- <Row>
						<Col span="12">
						<FormItem label="渠道商编码" prop="channelCode">
							<Input v-model="formObj.channelCode" :readonly="typeFlag == 'info'"
								:clearable="typeFlag != 'info'" maxlength="30" placeholder="请输入渠道商编码"
								class="inputSty"></Input>
						</FormItem>
						</Col>
					</Row> -->
					<!-- <Row>
						<Col span="12">
						<FormItem label="渠道商URL" prop="channelUrl">
							<Input v-model="formObj.channelUrl" :readonly="typeFlag == 'info'"
								:clearable="typeFlag != 'info'" placeholder="请输入渠道商URL" maxlength="255"
								class="inputSty"></Input>
						</FormItem>
						</Col>

						<Col span="12">
						<FormItem label="间接收入比例(%)" prop="indirectEarningsRatio">
							<Input v-model="formObj.indirectEarningsRatio" maxlength="3"
								@on-change="changedIndirectEarningsRatio" :readonly="typeFlag == 'info'"
								:clearable="typeFlag != 'info'" placeholder="请输入间接收入比例(%)" class="inputSty">
							</Input>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="12">
						<FormItem label="直接收入比例(%)" prop="directEarningsRatio">
							<Input v-model="formObj.directEarningsRatio" maxlength="3" :readonly="typeFlag == 'info'"
								:clearable="typeFlag != 'info'" placeholder="请输入直接收入比例(%)" class="inputSty">
							</Input>
						</FormItem>
						</Col>

						<Col span="12">
						<FormItem label="限制类型" prop="limitType"
							:class="{'ivu-form-item-required1':!formObj.indirectEarningsRatio}">
							<Select v-model="formObj.limitType" placeholder="请选择限制类型" :disabled="typeFlag == 'info'"
								:clearable="typeFlag != 'info'" @on-change="limitTypeChange" class="inputSty">
								<Option value="1">次数</Option>
								<Option value="2">年限</Option>
							</Select>
						</FormItem>
						</Col>
					</Row> -->
					<!-- <Row>
						<Col span="12">
						<FormItem label="购买次数" :class="{'ivu-form-item-required1':!formObj.indirectEarningsRatio}"
							prop="accountNum" v-show="formObj.limitType == '1'">
							<Input v-model="formObj.accountNum" :readonly="typeFlag == 'info'" placeholder="请输入购买次数"
								maxlength="11" class="inputSty">
							<span slot="append">次</span></Input>
						</FormItem>
						<FormItem label="购买时间" :class="{'ivu-form-item-required1':!formObj.indirectEarningsRatio}"
							prop="createTime" v-show="formObj.limitType == '2'">
							<Input v-model="formObj.createTime" :readonly="typeFlag == 'info'" placeholder="请输入购买时间"
								class="inputSty">
							<span slot="append">年</span>
							</Input>
						</FormItem>
						</Col>
					</Row> -->
					</Card>
					<Card style="margin-bottom: 16px;padiing: 16px" v-if="formObj.channelCooperationMode.includes('2')">
					<p slot="title" style="color: rgb(87,163,244);">A2Z信息</p>
          <Row>
          	<Col span="12">
          	<FormItem label="渠道商类型" prop="a2zChannelType" :rules="formObj.channelCooperationMode.includes('2') ?
          	ruleAddValidate.a2zChannelType : [{required: false}]">
          		<Select v-model="formObj.a2zChannelType" placeholder="请选择渠道商类型"
          			:readonly="typeFlag == 'info'" :disabled="typeFlag == 'info'"
          			:clearable="typeFlag != 'info'" class="inputSty">
          			<Option value="1">押金模式</Option>
          			<Option value="2">预存模式</Option>
          		</Select>
          	</FormItem>
          	</Col>
            <Col span="12">

            </Col>
          </Row>
          <Row>
          	<Col span="12">
          	<FormItem label="A2Z合约开始时间" prop="a2zContractStartTime" :rules="formObj.a2zContractEndTime ?
            ruleAddValidate.a2zContractStartTime : [{required: false}]">
          		<DatePicker type="datetime" format="yyyy/MM/dd HH:mm:ss" placeholder="请选择A2Z合约开始时间"
          			:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'"
          			v-model="formObj.a2zContractStartTime" class="inputSty"></DatePicker>
          	</FormItem>
          	</Col>
          	<Col span="12">
          	<FormItem label="A2Z合约结束时间" prop="a2zContractEndTime" :rules="formObj.a2zContractStartTime ?
            ruleAddValidate.a2zContractEndTime : [{required: false}]">
          		<DatePicker type="datetime" format="yyyy/MM/dd HH:mm:ss" placeholder="请选择A2Z合约结束时间"
          			:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'"
          			v-model="formObj.a2zContractEndTime" class="inputSty"></DatePicker>
          	</FormItem>
          	</Col>
          </Row>
          <Row>
            <Col span="24">
            <FormItem label="可购买套餐组" prop="a2zPackageInfosString" v-if="formObj.channelCooperationMode.includes('2')"
              :rules="formObj.channelCooperationMode.includes('2') ? ruleAddValidate.a2zPackageInfosString : [{required: false}]">
            	<Select v-model="formObj.a2zPackageInfosString" multiple placeholder="请选择可购买套餐组"
            		:disabled="typeFlag == 'info' || packageFlag" :clearable="typeFlag != 'info'"  style="width: 85%;">
            		<Option v-for="item in a2zPackageList" :value="JSON.stringify({
            			groupId: item.groupId,
            			groupName: item.groupName,
            			channelCooperationMode: item.cooperationMode,
            		})" :key="item.groupId">{{ item.groupName }}
            		</Option>
            	</Select>
            </FormItem>
            </Col>
            <Col span="24">
              <FormItem label="流量计费规则" prop="a2zRuleId" :rules="formObj.channelCooperationMode.includes('2') ?
          	ruleAddValidate.a2zRuleId : [{required: false}]">
              	<Select filterable multiple v-model="formObj.a2zRuleId" placeholder="请选择流量计费规则"
              		:readonly="typeFlag == 'info'" :disabled="typeFlag == 'info'"
              		:clearable="typeFlag != 'info'" style="width: 85%;">
              		<Option v-for="item in a2zRuleList" :value="item.id" :key="item.id">{{ item.name }}</Option>
              	</Select>
              </FormItem>
            </Col>
          </Row>
					<Row>
						<Col span="12">
						<FormItem label="允许自建套餐" prop="allowNewPackage" :rules="formObj.channelCooperationMode != '3' ?
						ruleAddValidate.allowNewPackage : [{required: false}]">
							<Select v-model="formObj.allowNewPackage" placeholder="请选择允许自建套餐"
								@on-change="changeChannelCreat" :disabled="typeFlag == 'info'"
								:clearable="typeFlag != 'info'" class="inputSty">
								<Option value='1'>是</Option>
								<Option value='2'>否</Option>
							</Select>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="自建套餐上限" prop="limitPacakageNum" v-show="formObj.allowNewPackage == '1'">
							<Input v-model="formObj.limitPacakageNum" :readonly="typeFlag == 'info'"
								:clearable="typeFlag != 'info'" placeholder="请输入自建套餐上限" class="inputSty"></Input>
						</FormItem>
						</Col>
					</Row>
          <Row>
          	<Col span="24">
            <FormItem label="国家卡池关联组" prop="groupId" v-show="formObj.allowNewPackage == '1'">
            	<Select v-model="formObj.groupId" filterable multiple placeholder="选择国家卡池关联组"
            		:disabled="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" style="width: 85%;">
            		<Option :title="item.groupName" v-for="(item,index) in groupIdList"
            			:value="item.groupId" :key="item.groupId">{{ item.groupName.length > 30 ? item.groupName.substring(0,30) + "…" : item.groupName }}
            		</Option>
            	</Select>
            </FormItem>
          	</Col>
          </Row>
					<Row>
						<Col span="24">
						<FormItem label="可用速度模板" prop="upccTemplateIds" v-show="formObj.allowNewPackage == '1'">
							<Select v-model="formObj.upccTemplateIds" filterable multiple placeholder="选择速度模板"
								:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" style="width: 85%;">
								<Option :title="item.templateName" v-for="item in TemplatesCreatList"
									:value="item.templateId" :key="item.templateId">{{ item.templateName.length > 30 ? item.templateName.substring(0,30) + "…" : item.templateName }}
								</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="24">
						<FormItem label="可用定向应用" prop="appids" v-if="formObj.channelCooperationMode.includes('2') && formObj.allowNewPackage == '1'">
							<Select v-model="formObj.appids" filterable multiple placeholder="选择应用列表"
								:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" style="width: 85%;">
								<Option v-for="appid in appidsList" :value="appid.id" :key="appid.id">{{ appid.appName }}</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
          <Row>
            <Col span="12">
            <FormItem label="自建套餐是否审批" prop="approvalPackage" :rules="formObj.channelCooperationMode.includes('2') && formObj.allowNewPackage == '1' ?
          	ruleAddValidate.approvalPackage : [{required: false}]">
            	<Select v-model="formObj.approvalPackage" filterable placeholder="选择自建套餐是否审批"
            		:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty">
            		<Option value="1">是</Option>
            		<Option value="2">否</Option>
            	</Select>
            </FormItem>
            </Col>
            </Col>
            <Col span="12">
            <FormItem label="额度用尽提醒阈值" prop="runoutofBalanceRemindThreshold">
            	<Input v-model="formObj.runoutofBalanceRemindThreshold " :readonly="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" placeholder="请输入额度用尽提醒阈值" class="inputSty"></Input>
            </FormItem>
            </Col>
          </Row>
					<Row>
            <Col span="12">
            <FormItem label="禁止购买提醒阈值" prop="prohibitiveBuyRemindThreshold">
            	<Input v-model="formObj.prohibitiveBuyRemindThreshold " :readonly="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" placeholder="请输入禁止购买提醒阈值" class="inputSty"></Input>
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem label="停止使用提醒阈值" prop="stopUseRemindThreshold">
            	<Input v-model="formObj.stopUseRemindThreshold " :readonly="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" placeholder="请输入停止使用提醒阈值" class="inputSty"></Input>
            </FormItem>
            </Col>
					</Row>
          <Row>
            <Col span="12">
            <FormItem label="合约期承诺销售金额" prop="a2zDepositAmount">
            	<Input v-model="formObj.a2zDepositAmount" :readonly="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" maxlength="13" placeholder="请输入承诺销售金额"
            		class="inputSty"></Input>
            </FormItem>
            </Col>
            <Col span="12" v-if="typeFlag == 'info'">
            <FormItem label="营销账户" prop="a2zMarketingAmount">
            	<Input v-model="formObj.a2zMarketingAmount " :readonly="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" placeholder="请输入营销账户" class="inputSty"></Input>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem label="出账规则" prop="a2zAccountingPeriodId">
            	<Select v-model="formObj.a2zAccountingPeriodId" placeholder="请选择出账规则"
            		:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty">
            		<Option v-for="item in acountingList" :value="item.id" :key="item.id">{{ item.name }}
            		</Option>
            	</Select>
            </FormItem>
            </Col>
          </Row>
          <Row>
          	<Col span="24">
          	<FormItem label="IMSI费规则" prop="a2zImsiAmount">
          		<Select v-model="formObj.a2zImsiAmount" placeholder="请选择IMSI费规则" multiple filterable
          			:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" style="width: 85%;">
          			<Option v-for="item in imsiFeeList" :value="item.id" :key="item.id">{{ item.ruleName }}
          			</Option>
          		</Select>
          	</FormItem>
          	</Col>
          </Row>
          <Row>
            <Col span="24">
            <FormItem label="短信模板" multiple prop="a2zSmsTemplateVo" :rules="formObj.channelCooperationMode.includes('2') ?
          	ruleAddValidate.a2zSmsTemplateVo : [{required: false}]">
            	<Select filterable multiple v-model="formObj.a2zSmsTemplateVo" placeholder="请选择短信模板"
            		:readonly="typeFlag == 'info'" :disabled="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" style="width: 85%;">
            		<Option v-for="(item,index) in tempList" :key="index" :value="JSON.stringify({
            			templateId: item.id,
            			templateName: item.templateName,
            			cooperationMode: '2',
            		})">{{ item.templateName }}</Option>
            	</Select>
            </FormItem>
            </Col>
          </Row>
					<Row v-if="typeFlag === 'info'">
						<Col span="12">
						<FormItem label="A2Z预存款额度" prop="a2zPreDeposit">
							<Input v-model="formObj.a2zPreDeposit " placeholder="请输入A2Z预存款额度"
								:readonly="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12" v-if="formObj.channelCooperationMode.includes('2')">
						<FormItem>
							<Button v-has="'a2zStop'" type="error" icon="md-close"
								:disabled="formObj.a2zCardUseSwitch != '1'" ghost
								@click="a2zStop">{{a2zStopStatus}}</Button>&nbsp;&nbsp;&nbsp;&nbsp;
							<Button v-has="'a2zRecover'" type="success" icon="md-checkmark"
								:disabled="formObj.a2zCardUseSwitch  !== '2'" ghost
								@click="a2zRecover">{{a2zRecoverStatus}}</Button>
						</FormItem>
						</Col>
					</Row>
          </Card>
          <Card style="margin-bottom: 16px; padding: 16px" v-if="formObj.channelCooperationMode.includes('2')">
            <p slot="title" style="color: rgb(87,163,244);">运营商配置信息</p>
            <Row v-for="(config, index) in formObj.a2zOperators" :key="index">
              <Col span="6">
                <FormItem :label="'国家/地区'" :prop="'a2zOperators.' + index + '.a2zOperatorMcc'" :rules="formObj.channelCooperationMode.includes('2') ? ruleAddValidate.country : [{required: false}]">
                  <Select filterable v-model="config.a2zOperatorMcc" placeholder="请选择国家/地区"
                          :disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" style="width: 85%;"
                          @on-change="handleCountryChange(config.a2zOperatorMcc, index,1)">
                    <Option v-for="item in localList" :value="item.mcc" :key="item.mcc">{{ item.countryEn }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem :label="'计费规则'" multiple :prop="'a2zOperators.' + index + '.a2zOperatorChargeType'" :rules="formObj.channelCooperationMode.includes('2') ? ruleAddValidate.rule : [{required: false}]">
                  <Select v-model="config.a2zOperatorChargeType" placeholder="请选择计费规则"
                          :readonly="typeFlag == 'info'" :disabled="typeFlag == 'info'"
                          :clearable="typeFlag != 'info'" style="width: 85%;">
                    <Option v-for="item in chargingRule" :key="item.code" :value="item.code">{{ item.chargingName }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem :label="'运营商'" :prop="'a2zOperators.' + index + '.a2zOperator'" :rules="formObj.channelCooperationMode.includes('2') ? ruleAddValidate.operator : [{required: false}]">
                  <div style="display: flex; align-items: center;">
                    <Select filterable multiple v-model="config.a2zOperator" placeholder="请选择运营商"
                            :disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty" style="width: 80%; margin-right: 10px;">
                      <Option v-for="item in config.operatorsList" :key="item.id" :value="item.id">{{ item.operatorName }}</Option>
                    </Select>

                    <Button type="error" size="small" style="width: 40px; height: 25px;" :loading="deleteLoading" @click="removeRule(index)" v-if="formObj.a2zOperators.length > 1 && index != 0 && typeFlag !== 'info'">删除</Button>
                    <Button type="info" size="small" :loading="addLoading" @click="addRuleHandle" v-if="index === formObj.a2zOperators.length - 1 && typeFlag !== 'info'">添加</Button>
                  </div>
                </FormItem>
              </Col>
            </Row>
          </Card>
          <Card style="margin-bottom: 16px;padiing: 16px" v-if="formObj.channelCooperationMode.includes('3')">
          <p slot="title" style="color: rgb(87,163,244);">资源合作信息</p>
          <Row>
            <Col span="12">
            <FormItem label="渠道商类型" prop="resourceChannelType" :rules="formObj.channelCooperationMode.includes('3') ?
            ruleAddValidate.resourceChannelType : [{required: false}]">
            	<Select v-model="formObj.resourceChannelType" placeholder="请选择渠道商类型"
            		:readonly="typeFlag == 'info'" :disabled="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" class="inputSty">
            		<Option value="1">押金模式</Option>
            		<Option value="2">预存模式</Option>
            	</Select>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem label="额度用尽提醒阈值" prop="resourceRunoutofBalanceRemindThreshold">
            	<Input v-model="formObj.resourceRunoutofBalanceRemindThreshold " :readonly="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" placeholder="请输入额度用尽提醒阈值" class="inputSty"></Input>
            </FormItem>
            </Col>
          	<Col span="12">
          	<FormItem label="禁止购买提醒阈值" prop="resourceProhibitiveBuyRemindThreshold">
          		<Input v-model="formObj.resourceProhibitiveBuyRemindThreshold " :readonly="typeFlag == 'info'"
          			:clearable="typeFlag != 'info'" placeholder="请输入禁止购买提醒阈值" class="inputSty"></Input>
          	</FormItem>
          	</Col>
          </Row>
          <Row >
            <Col span="12">
            <FormItem label="停止使用提醒阈值" prop="resourceStopUseRemindThreshold">
            	<Input v-model="formObj.resourceStopUseRemindThreshold " :readonly="typeFlag == 'info'"
            		:clearable="typeFlag != 'info'" placeholder="请输入停止使用提醒阈值" class="inputSty"></Input>
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem label="出账规则" prop="a2zAccountingPeriodId" v-if="formObj.channelCooperationMode.includes('2')">
            	<Select v-model="formObj.a2zAccountingPeriodId" placeholder="请选择出账规则"
            		disabled :clearable="typeFlag != 'info'" class="inputSty">
            		<Option v-for="item in acountingList" :value="item.id" :key="item.id">{{ item.name }}
            		</Option>
            	</Select>
            </FormItem>
            <FormItem label="出账规则" prop="resourceAccountingPeriodId" v-else>
            	<Select v-model="formObj.resourceAccountingPeriodId" placeholder="请选择出账规则"
            		:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" class="inputSty">
            		<Option v-for="item in acountingList" :value="item.id" :key="item.id">{{ item.name }}
            		</Option>
            	</Select>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="24">
            <FormItem label="IMSI费规则" prop="resourceImsiAmount">
            	<Select v-model="formObj.resourceImsiAmount" placeholder="请选择IMSI费规则" multiple filterable
            		:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" style="width: 85%;">
            		<Option v-for="item in imsiFeeList" :value="item.id" :key="item.id">{{ item.ruleName }}</Option>
            	</Select>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="24">
              <FormItem label="流量计费规则" prop="resourceRuleId" :rules="formObj.channelCooperationMode.includes('3') ?
          	ruleAddValidate.resourceRuleId : [{required: false}]">
              	<Select v-model="formObj.resourceRuleId" multiple placeholder="请选择流量计费规则"
              		:disabled="typeFlag == 'info'" :clearable="typeFlag != 'info'" style="width: 85%;">
              		<Option v-for="item in a2zRuleList" :value="item.id" :key="item.id">{{ item.name }}
              		</Option>
              	</Select>
              </FormItem>
            </Col>
          </Row>
          </Card style="margin-bottom: 16px;padiing: 16px;">

					<Card style="margin-bottom: 16px;padiing: 16px; color: brown;" v-if="typeFlag === 'info'">
					<Row>
						<Col span="24">
						<FormItem label="充值记录">
							<Button type="primary" ghost class="inputSty" long @click="getRechargeRecord"
								icon="md-add">查看充值记录</Button>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="24">
						<FormItem label="套餐购买记录">
							<Button type="info" ghost class="inputSty" long @click="getPackageRecord"
								icon="md-add">查看套餐购买记录</Button>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="24">
						<FormItem label="账单流水">
							<Button type="error" ghost class="inputSty" long @click="getBillFlow"
								icon="md-add">查看账单流水</Button>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="24">
						<FormItem v-has="'trafficDetails'" label="流量明细">
							<Button type="warning" ghost class="inputSty" long @click="getTrafficDetails"
								icon="md-add">查看流量明细</Button>
						</FormItem>
						</Col>
					</Row>
					<Row v-if="formObj.channelCooperationMode.includes('1')">
						<Col span="24">
						<FormItem v-has="'marketingAccountConsignment'"  label="营销款账户详情【代销】">
							<Button type="success" ghost class="inputSty" long @click="toMarketAccout(1)" icon="md-add">营销款账户详情【代销】</Button>
						</FormItem>
						</Col>
					</Row>
					<Row v-if="formObj.channelCooperationMode.includes('2')">
						<Col span="24">
						<FormItem v-has="'marketingAccountA2Z'" label="营销款账户详情【A2Z】">
							<Button type="success" ghost class="inputSty" long @click="toMarketAccout(2)" icon="md-add">营销款账户详情【A2Z】</Button>
						</FormItem>
						</Col>
					</Row>
          <Row v-if="formObj.channelCooperationMode.includes('3')">
          	<Col span="24">
          	<FormItem v-has="'marketingAccountResource'" label="营销款账户详情【资源合作】">
          		<Button type="success" ghost class="inputSty" long @click="toMarketAccout(3)" icon="md-add">营销款账户详情【资源合作】</Button>
          	</FormItem>
          	</Col>
          </Row>
          </Card>
					<div style="text-align: center" v-if="typeFlag != 'info'">
						<Button type="primary" @click="submit" :loading="submitLoading">提交</Button>
						<Button style="margin-left: 8px" @click="reset('formObj')">重置</Button>
					</div>
					<div style="text-align: center; margin-top: 20px;" v-if="typeFlag === 'info'">
						<Button @click="back">返回</Button>
					</div>
					</Card>
				</Form>
			</div>
		</div>
		<!-- 查看充值记录弹出框-->
		<Modal title="充值记录" v-model="rechargeRecordFlag" :footer-hide="true" :mask-closable="false" width="800px">
			<div style="padding: 0 16px">
				<Button style="margin: 0 2px" type="success" v-has="true || 'export'" @click="exportDistributorsRecord">
					<div style="display: flex; align-items: center">
						<Icon type="ios-cloud-download-outline" />&nbsp;导出
					</div>
				</Button>
				<div style="margin: 20px 0">
					<Table :columns="rechargeColumns" :data="rechargeTableData" :ellipsis="true"
						:loading="rechargeTableLoading"></Table>
					<Page :total="rechargeTotal" :page-size="rechargePageSize" :current.sync="rechargePage" show-total
						show-elevator @on-change="getDistributorsRecord" style="margin: 15px 0" />
				</div>
			</div>
		</Modal>
		<!-- 套餐购买记录弹出框-->
		<Modal title="套餐购买记录" v-model="packageRecordFlage" :footer-hide="true" :mask-closable="false"
			@on-cancel="packagePurchaseCancel" width="1300px">
			<div style="padding: 0 16px">
				<div style="display: flex; flex-directratioion: row">
					<DatePicker type="month" format="yyyy-MM" placeholder="请选择月份" :clearable="true"
						@on-change="selectTime" class="recordBtnSty"></DatePicker>
					<Button style="margin: 0 2px" type="primary" @click="getPurchaseRecords(0)">
						<div style="display: flex; align-items: center">
							<Icon type="ios-search" />&nbsp;查询
						</div>
					</Button>
					<Button style="margin: 0 2px" type="success" v-has="'buyRecord'" @click="exportPackageRecord">
						<div style="display: flex; align-items: center">
							<Icon type="ios-cloud-download-outline" />&nbsp;导出购买记录
						</div>
					</Button>
					<Button style="margin: 0 2px" type="success" v-has="'payRecord'" @click="exportRemunerateDetail">
						<div style="display: flex; align-items: center">
							<Icon type="ios-cloud-download-outline" />&nbsp;导出酬金详情
						</div>
					</Button>
				</div>
				<div style="margin: 20px 0">
					<Table :columns="packageColumns" :data="packageTableData" :ellipsis="true"
						:loading="packageTableLoading">
						<template slot-scope="{ row, index }" slot="orderInfo">
							<!-- v-has="'view'" -->
							<a style="color: #55aaff" @click="showOrderInfo(row.orderId)">查看详情</a>
						</template>

						<template slot-scope="{ row, index }" slot="action">
							<!-- v-has="'unsubscribe'" -->
							<Button size="small" v-if="
                  (row.orderStatus == '2' && row.orderType == '2') ||
                  ((row.orderStatus == '1' || row.orderStatus == '2') &&
                    row.orderType == '3') || (row.orderStatus == '2' && row.orderType == '7')
                " type="error" @click="unsubscribeAll(row.orderId)" style="margin-right: 5px">退订</Button>

							<!--  v-has="'check'" -->
							<Button type="primary" v-if="row.orderStatus == '4'" size="small" style="margin-right: 5px"
								@click="examine(row.orderId, '2')">通过</Button>

							<!--  v-has="'check'" -->
							<Button type="primary" v-if="row.orderStatus == '4'" size="small" style="margin-right: 5px"
								@click="examine(row.orderId, '3')">不通过</Button>
						</template>
					</Table>
					<Page :total="packageTotal" :page-size="packagePageSize" :current.sync="packagePage" show-total
						show-elevator @on-change="getPurchaseRecords" style="margin: 15px 0" />
				</div>
			</div>
		</Modal>
		<!-- 子单详情弹出框-->
		<Modal title="子订单详情记录" v-model="packageRecordDetailsFlage" :footer-hide="true" :mask-closable="false"
			width="900px">
			<div style="margin: 20px 0">
				<Table :columns="packageDetailsColumns" :data="packageDetailsTableData" :ellipsis="true"
					:loading="packageTableDetailsLoading">
					<template slot-scope="{ row, index }" slot="action1">
						<!-- v-has="'unsubscribe'" -->
						<Button size="small" v-if="
                (row.orderStatus == '2' && row.orderType == '2') ||
                ((row.orderStatus == '1' || row.orderStatus == '2') &&
                  row.orderType == '3')&&row.orderType != '7'
              " type="error" @click="unsubscribeModal(row.id)" style="margin-right: 5px">退订</Button>

						<!--  v-has="'check'" -->
						<Button type="primary" v-if="row.orderStatus == '4'&& row.orderType != '7'" size="small"
							style="margin-right: 5px" @click="examineModal(row.id, '2')">通过</Button>

						<!--  v-has="'check'" -->
						<Button type="primary" v-if="row.orderStatus == '4'&& row.orderType != '7'" size="small"
							style="margin-right: 5px" @click="examineModal(row.id, '3')">不通过</Button>
					</template>
				</Table>
				<Page :total="packageDetailsTotal" :page-size="packageDetailsPageSize"
					:current.sync="packageDetailsPage" show-total show-elevator @on-change="getPurchaseRecordsDetails"
					style="margin: 15px 0" />
			</div>
		</Modal>
		<!-- 账单流水弹出框-->
		<Modal title="账单流水" v-model="billFlowFlage" :footer-hide="true" :mask-closable="false" @on-cancel="cancelModal"
			width="800px">
			<div style="padding: 0 16px">
				<div style="display: flex;justify-content: flex-start;align-items: flex-start">
					<Form ref="form" :model="form" :rules="billRule" :label-width="120">
						<FormItem label="选择起止日期" prop="date">
							<DatePicker type="daterange" format="yyyy-MM-dd" placement="bottom-end" v-model="form.date"
								style="width: 200px" :clearable="true" placeholder="选择日期范围"
								@on-change="handleDateChange" @on-clear="hanldeDateClear" class="recordBtnSty">
							</DatePicker>
						</FormItem>
					</Form>&nbsp;&nbsp;
					<Button type="primary" @click="searchBill()" v-has="'search'" :loading="searchloading">
						<div style="display: flex; align-items: center">
							<Icon type="md-search" />&nbsp;搜索
						</div>
					</Button>&nbsp;&nbsp;&nbsp;&nbsp;
					<Button type="success" @click="exportBillFlow" v-has="'exportBillList'"
						:loading="billExportLoading">
						<div style="display: flex; align-items: center">
							<Icon type="ios-cloud-download-outline" />&nbsp;导出
						</div>
					</Button>&nbsp;&nbsp;&nbsp;&nbsp;
					<Button @click="cancelModal">
						<div style="display: flex; align-items: center">
							<Icon type="ios-arrow-back" />&nbsp;返回
						</div>
					</Button>
				</div>
				<div>
					<Table :columns="columns12" :data="billData" :loading="billLoading" no-data-text="暂无数据">
					</Table>
					<!-- 分页 -->
					<div style="margin-top: 40px;">
						<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
					</div>
				</div>
			</div>
		</Modal>
		<!-- 流量明细弹出框-->
		<Modal title="flowInfo" v-model="trafficDetailsFlage" :footer-hide="true" :mask-closable="false"
			@on-cancel="cancelModal" width="900px">
			<div style="padding: 0 16px">
				<div style="width: 100%;display: flex;justify-content: space-evenly; align-items: self-start;">
					<Form ref="form" :model="trafficForm" :rules="trafficRule" inline :label-width="110"
						style="display: flex;justify-content: center;align-items: center;">
						<FormItem label="Select Date" prop="date">
							<DatePicker type="daterange" format="yyyy-MM-dd" placement="bottom-end"
								v-model="trafficForm.date" style="width: 200px" :clearable="true"
								placeholder="Please Select Date" @on-change="handleDateChange1"
								@on-clear="hanldeDateClear1" class="recordBtnSty">
							</DatePicker>
						</FormItem>
						<FormItem label="Select destination" prop="localId">
							<Select filterable v-model="trafficForm.localId" placeholder="Select destination"
								:clearable="true" @on-change="getLocalList" style="width: 200px;">
								<Option v-for="item in localList" :value="item.mcc" :key="item.id">{{item.countryEn}}
								</Option>
							</Select>
						</FormItem>
					</Form>
					<div style="display: flex;justify-content: center;align-items: center;">
						<Button type="primary" @click="searchTraffic()" :loading="searchTrafficLoading" icon="md-search">query</Button>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="info" @click="exportTraffic()" :loading="exportTrafficLoading" icon="md-arrow-down">Export</Button>
					</div>
				</div>
				<div>
					<Table :columns="columnsTraffic" :data="trafficData" :loading="trafficLoading"
						no-data-text="No Data"></Table>
					<!-- 分页 -->
					<div style="margin-top: 40px;">
						<Page :total="totalTraffic" :current.sync="currentPageTraffic" show-total show-elevator
							@on-change="goTrafficPage" />
					</div>
				</div>
				<div style="margin: 20px; text-align: center;">
					<Button @click="cancelModal">
						<div style="display: flex; align-items: center">
							<Icon type="ios-arrow-back" />&nbsp;Return
						</div>
					</Button>
				</div>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					<FormItem label="你本次导出任务ID为:">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem label="你本次导出的文件名为:">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="Goto">立即前往</Button>
			</div>
		</Modal>
		<a ref="downloadLink" style="display: none"></a>
	</div>
</template>

<script>
	import {
		getDistributorsDetail,
		getPackages,
		getPurchaseRecords,
		getAllpurchasePackageGroup,
		getPurchasePackageGroup,
		exportDistributorsRecord,
		exportRemunerate,
		exportPurchaseRecord,
		getDistributorsRecord,
		getDistributorsPurchaseRecord,
		UpdateCustermInfo,
		addCustermInfo,
		billflowList,
		billflowData,
		trafficList,
    exportTraffic,
		a2zStop,
		a2zRecover
	} from "@/api/customer/channelShop.js";
	import {
		unsubscribeBatch,
		examineOrderBatch,
		getOrderLists,
		getDetailsList,
		downLoadData,
		unsubscribe,
		examineOrder,
	} from "@/api/product/porder/index";
	import {
		forEach,
		string
	} from "mathjs";
	import {
		opsearch,
    getSalesMailList,
    getChannelA2zOperator
	} from '@/api/channel.js';
	import {
		upccList
	} from '@/api/package/upcc';
	import {
		getCardPoolGroup,
	} from '@/api/associationGroup/cardPoolMccGroup.js'
	import {
		getAppInfo,
	} from '@/api/package/targetedApplications'
	import {
	  gettemplate
	} from '@/api/mastercdr'
  import {
    getAcountingPeriod
  } from '@/api/finance/acountingPeriod.js'
  import {
  	queryA2ZBillPrice
  } from "@/api/customer/a2zBillPriceMngr.js";
  import {
  	queryImsiFee,
  } from "@/api/customer/imsiFeeMngr.js";
  import {
    getAllOperators,
    getChoiceOperators,
  } from "@/api/operators.js";
  export default {
		components: {},
		data() {
			const validatePositiveNum = (rule, value, callback) => {
				var str = /^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/;
				if (!value || str.test(value)) {
					callback();
				} else {
					callback(new Error(rule.message));
				}
			};
			const validateIntegerNum = (rule, value, callback) => {
				var str = /^[0-9]\d*$/;
				if (str.test(value)) {
					callback();
				} else {
					callback(new Error(rule.message));
				}
			};
			var validateEmail = (rule, value, callback) => {
				if (!value || value.indexOf("@") == -1) {
					callback(new Error("请输入有效的邮箱地址"));
				} else {
					callback();
				}
			};
			var validateRepeat = (rule, value, callback) => {
				var row_index = rule.field.replace(/[^0-9]/ig, "");
				if (this.formObj.packageUsePercentage.length > 1) {
					for (var index in this.formObj.packageUsePercentage) {
						var item = this.formObj.packageUsePercentage[index];
						if (item.value == value && index != row_index) {
							callback(new Error('套用用量提醒百分比唯一性,不能重复'));
							return;
						}
					}
					callback();
				} else {
					callback();
				}
			}
			var validatePackageUse = (rule, value, callback) => {
				let Percentagelist = JSON.parse(JSON.stringify(this.formObj.packageUsePercentage))
				for (let index=0; index<Percentagelist.length; index++) {
					if (Percentagelist[index].value == "") {
						Percentagelist.splice(index, 1)
					}
				}
				//如果有百分比，url必填
				if (Percentagelist.length != 0 && !value) {
					callback(new Error(rule.message));
				} else {
					callback();
				}
			};
      // 校验代销结束时间不能早于开始时间
      var validateContractEndTime = (rule, value, callback) => {
        let contractBeginTime = new Date(this.formObj.contractBeginTime);
        let contractEndTime = new Date(value);
        if (value && contractBeginTime && !(value > contractBeginTime)) {
          callback(new Error(rule.message));
        } else {
      		callback();
      	}
      };
      // 校验a2z结束时间不能早于a2z开始时间
      var validateA2ZContractEndTime = (rule, value, callback) => {
        let a2zContractStartTime = new Date(this.formObj.a2zContractStartTime);
        let a2zContractEndTime = new Date(value);
        if (value && a2zContractStartTime && !(value > a2zContractStartTime)) {
          callback(new Error(rule.message));
        } else {
      		callback();
      	}
      };
			return {
				submitLoading: false,
				dxPackageList: [],
        a2zPackageList: [],
				purchaseStatusList: [{
						label: "允许订购",
						value: "1",
					},
					{
						label: "不允许订购",
						value: "2",
					},
				],
				packageFlag: false,
				typeFlag: "info",
				recordDetailId: "",
				corpId: "",
				index: 1,
        byteCount: 0, // 当前字节数
        maxByteLength: 220, // 最大字节数限制
        searchObj: {},
				formObj: {
					resetPrice: "", //充值金额
					deposit: "", //可用额度
					internalOrder: "", //内部订单
					activateNotification: "", //是否激活通知
					overdueNotify: "", //到期通知
					channelType: "", //渠道商类型
          a2zChannelType: "",//A2Z渠道商类型
          resourceChannelType: "", //资源合作渠道商类型
					totalDeposit: 0, //总预存款（预存模式下展示）
					corpName: "", //渠道商名称
					channelStatus: 1, //渠道商状态
					appkey: "", //appkey
					appSecret: "", //appSecret
					channelCode: "", //渠道商编码
					activateNotificationUrl: "", //激活通知url
					overdueNotifyUrl: "", //到期通知url
					esimNotifySwitch: '',//esim状态变更通知
					esimNotifyUrl: '',//esim状态变更通知url
					channelUrl: "", //渠道商url
					ebsCode: "", //EBS Code
					dxPackageInfosString: "", //可购买套餐组
          a2zPackageInfosString: "",
					unsubscribeRule: "", //退订规则
					currencyCode: "", //币种
					mail: "", //联系人邮箱
					depositNotify: "", //可用金额提醒阀值
					threshold2: "", //押金不足不允许购买阀值
					discount: "", //购买折扣
					limitType: "", //限制类型
					indirectEarningsRatio: "", //间接比例
					directEarningsRatio: "", //直接比例
					createTime: "", //购买时间
					accountNum: "", //购买次数
					contractBeginTime: "", //代销合约开始时间
					contractEndTime: "", //代销合约结束时间
					contractSellAmount: "", //承诺金额
          a2zDepositAmount: "",//A2Z承诺金额
					accounts: "", //帐号
					'address': '',
					'companyName': '',
					allowNewPackage: '2', //允许自建套餐
					limitPacakageNum: '', //自建套餐上限
					groupId: "", //国家卡池关联组
					upccTemplateIds: [], //可用速度模板
					channelCooperationMode: '', //渠道合作模式
					runoutofBalanceRemindThreshold: '', //额度用尽提醒阈值
          resourceRunoutofBalanceRemindThreshold: '', //额度用尽提醒阈值资源合作
					prohibitiveBuyRemindThreshold: '', //禁止购买提醒阈值
          resourceProhibitiveBuyRemindThreshold: '', //禁止购买提醒阈值资源合作
					stopUseRemindThreshold: '', //停止使用提醒阈值
          resourceStopUseRemindThreshold: '',//资源合作
					a2zCardUseSwitch: '1', //判断A2Z按钮是否置灰（后端返回的值判断该渠道商是a2z停用还是恢复）
					packageUsePercentage: [{
						value: '',
						index: 1,
					}], //套餐用量百分比
					packageUseNotifyUrl: "", //套餐用量百分比url
					appids: [],//可用定向应用列表
          salesMail: "", //销售邮箱
          distributionAccountingPeriodId: "", //代销出账规则
          a2zAccountingPeriodId: "",//A2Z出账规则
          resourceAccountingPeriodId: "",//资源合作出账规则
          a2zContractStartTime: "",//A2Z合约开始时间
          a2zContractEndTime: "",//A2Z合约结束时间
          a2zRuleId: "",//流量计费规则
          resourceRuleId: "", //资源合作流量计费规则
          approvalPackage: "",//自建套餐是否审批
          dxSmsTemplateVo: "", //代销短信模板
          a2zSmsTemplateVo: "", //A2Z短信模板
          consignmentImsiAmount: "", //代销imsi费规则
          resourceImsiAmount: "",//资源合作imsi费规则
          a2zImsiAmount: "", //A2Zimsi费规则
          marketingAmount: "",//A2Z营销账户
          creditAmount: "",//A2Z信用账户
          a2zMarketingAmount: "",//代销营销账户
          a2zOperators:[ //运营商配置信息
            {
              a2zOperatorMcc:"",
              a2zOperatorChargeType: "",
              a2zOperator:[],
              operatorsList: [],
              index:0
            }
          ],
				},
        deleteLoading: false,
        addLoading: false,
				changedRuleValidate: [
					"limitType",
					"createTime",
					"accountNum",
          // 改页面原型后，间接比例不展示，不按照原来的校验了
					// "contractBeginTime",
					// "contractEndTime",
				],
				changedChannelCreatValidate: [
					"limitPacakageNum",
					"groupId",
					"upccTemplateIds",
				],
				ruleAddValidate: {
					corpName: [{
						required: true,
						type: "string",
						message: "渠道商名称不能为空",
					}, {
						pattern: /^[^\s]+(\s+[^\s]+)*$/,
						trigger: "blur",
						message: '渠道商名称有空格',
					}, {
            // 新的校验规则，检查是否包含&符号
            validator: (rule, value, callback) => {
              if (value && value.includes('&')) {
                callback(new Error('渠道商名称不能包含&符号'));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },{
          // validator: this.validateByteLength,
          },],
					channelStatus: [{
						required: true,
						message: "渠道商状态不能为空",
						trigger: "change",
					}, ],
					companyName: [{
							required: true,
							type: 'string',
							message: '公司名称不能为空',
							trigger: 'blur'
						},
            {
              // 新的校验规则，检查是否包含&符号
              validator: (rule, value, callback) => {
                if (value && value.includes('&')) {
                  callback(new Error('公司名称不能包含&符号'));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
            {
              // validator: this.validateByteLength,
            },
					],
					address: [{
							required: true,
							type: 'string',
							message: '地址不能为空',
							trigger: 'blur'
						},
						{
							max: 200,
							message: '最长200位'
						}
					],
					ebsCode: [{
						required: true,
						type: "string",
						message: "EBSCode不能为空",
					}, ],
					internalOrder: [{
						required: true,
						type: 'string',
						message: '内部订单不能为空',
						trigger: 'change'
					}],
					channelType: [{
						required: true,
						type: "string",
						trigger: "change",
						message: "渠道商类型不能为空",
					}, ],
          a2zChannelType: [{
          	required: true,
          	type: "string",
          	trigger: "change",
          	message: "渠道商类型不能为空",
          }, ],
					resourceChannelType: [{
          	required: true,
          	type: "string",
          	trigger: "change",
          	message: "渠道商类型不能为空",
          }, ],
          dxPackageInfosString: [{
						required: true,
						type: "array",
						message: "可购买套餐组不能为空",
					}, ],
          a2zPackageInfosString: [{
						required: true,
						type: "array",
						message: "可购买套餐组不能为空",
					}, ],
					unsubscribeRule: [{
						required: true,
						type: "string",
						trigger: "change",
						message: "退订规则不能为空",
					}, ],
					activateNotification: [{
						required: true,
						type: "string",
						trigger: "change",
						message: "是否激活通知不能为空",
					}, ],
					overdueNotify: [{
						required: true,
						type: "string",
						trigger: "change",
						message: "到期通知不能为空",
					}, ],
					esimNotifySwitch: [{
						required: true,
						type: "string",
						trigger: "change",
						message: "ESIM状态变更通知不能为空",
					}, ],
					esimNotifyUrl: [{
						required: true,
						type: "string",
						trigger: "change",
						message: "ESIM状态变更通知url不能为空",
					}, ],
					currencyCode: [{
						required: true,
						type: "string",
						message: "货币种类不能为空",
					}, ],
					mail: [{
							required: true,
							message: "联系人邮箱不能为空",
						},
						{
							validator: validateEmail,
							trigger: "blur",
						},
						{
							required: true,
							type: "email",
							trigger: "blur",
							message: "联系人邮箱格式错误",
						},
					],
					depositNotify: [{
							required: true,
							message: "可用金额提醒阀值不能为空",
						},
						{
							pattern: /^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/,
							message: "押金提醒阀值最高支持8位整数和2位小数正数或零",
						},
					],
					threshold2: [{
						validator: validatePositiveNum,
						message: "不允许购买阀值仅支持正数或零",
					}, ],
					activateNotificationUrl: [{
						required: true,
						type: "string",
						message: "激活通知url不能为空"
					}],
					overdueNotifyUrl: [{
						required: true,
						type: "string",
						message: "到期通知url不能为空"
					}],
					discount: [{
							pattern: /^([1-9]|[1-9]\d|100)$/,
							message: "折扣区间为1-100",
						},

						{
							validator: validatePositiveNum,
							message: "购买折扣仅支持正数或零",
						},
					],
					indirectEarningsRatio: [{
							pattern: /^([1-9]|[1-9]\d|100)$/,
							message: "填入区间为1-100",
						},
						{
							validator: validatePositiveNum,
							message: "间接比例仅支持正数或零",
						},
					],
					directEarningsRatio: [{
							pattern: /^([1-9]|[1-9]\d|100)$/,
							message: "填入区间为1-100",
						},
						{
							validator: validatePositiveNum,
							message: "直接比例仅支持正数或零",
						},
					],
					accountNum: [{
						validator: validateIntegerNum,
						message: "购买次数仅支持正整数或零",
					}, ],
					contractSellAmount: [{
						validator: validatePositiveNum,
						message: "承诺金额最高支持8位整数和2位小数正数或零",
					}, ],
          a2zDepositAmount: [{
						validator: validatePositiveNum,
						message: "承诺金额最高支持8位整数和2位小数正数或零",
					}, ],
					limitType: [{
						required: true,
						message: "限制类型不能为空",
					}, ],
					createTime: [{
						required: true,
						message: "购买时间不能为空",
					}, ],
					accountNum: [{
						required: true,
						message: "购买次数不能为空",
					}, ],
					contractBeginTime: [{
						required: true,
						// type: 'date',
						message: "代销合约开始时间不能为空",
						// trigger: 'change',
						// pattern: /.+/
					}],
					contractEndTime: [{
						required: true,
						// type: 'date',
						message: "代销合约结束时间不能为空",
						// trigger: 'change',
						// pattern: /.+/
					}, {
							validator: validateContractEndTime,
              message: "代销合约结束时间不能早于等于代销合约开始时间",
					}],
					allowNewPackage: [{
						required: true,
						message: "是否允许自建不能为空",
					}],
					limitPacakageNum: [{
							required: true,
							message: "自建上限不能为空",
						},
						{
							// pattern: /^[1-9]+[0-9]*$/,
							// message: "只支持正整数",
						},
					],
					groupId: [{
						required: true,
            type: "array",
						message: "国家卡池关联组不能为空",
					}, ],
					upccTemplateIds: [{
						required: true,
						message: "可用速度模板不能为空",
					}, ],
					channelCooperationMode: [{
						required: true,
						message: "渠道合作模式不能为空",
					}, ],
					runoutofBalanceRemindThreshold: [{
						validator: validatePositiveNum,
						message: "最高支持8位整数和2位小数正数或零",
					}, ],
          resourceRunoutofBalanceRemindThreshold: [{
            validator: validatePositiveNum,
            message: "最高支持8位整数和2位小数正数或零",
          }],
					prohibitiveBuyRemindThreshold: [{
						validator: validatePositiveNum,
						message: "最高支持8位整数和2位小数正数或零",
					}, ],
          resourceProhibitiveBuyRemindThreshold: [{
						validator: validatePositiveNum,
						message: "最高支持8位整数和2位小数正数或零",
					}, ],
          stopUseRemindThreshold: [{
						validator: validatePositiveNum,
						message: "最高支持8位整数和2位小数正数或零",
					}, ],
          resourceStopUseRemindThreshold: [{
						validator: validatePositiveNum,
						message: "最高支持8位整数和2位小数正数或零",
					}, ],
					a2zPreDeposit: [{
						validator: validatePositiveNum,
						message: "最高支持8位整数和2位小数正数或零",
					}, ],
					value: [{
						pattern: /^([0-9]{1,2}|100)$/,
						message: "请输入0~100区间内的正整数",
					}, {
						validator: validateRepeat,
						message:'套用用量提醒百分比不能重复',
					}, ],
					packageUseNotifyUrl: [{
						validator: validatePackageUse,
						message: "流量通知URL不能为空"
					}],
          salesMail: [{
							required: true,
							message: "销售经理邮箱不能为空",
						}
					],
          acountRule: [{
          	required: true,
						type: "array",
          	message: "出账规则不能为空",
          }, ],
          a2zRuleId: [{
          	required: true,
						type: "array",
          	message: "流量计费规则不能为空",
          }, ],
          resourceRuleId: [{
          	required: true,
						type: "array",
          	message: "流量计费规则不能为空",
          }, ],
          approvalPackage: [{
          	required: true,
          	message: "自建套餐是否审批不能为空",
          }, ],
          dxSmsTemplateVo: [{
          	required: true,
						type: "array",
          	message: "短信模板不能为空",
          }, ],
          a2zSmsTemplateVo: [{
          	required: true,
          	type: "array",
          	message: "短信模板不能为空",
          }, ],
          a2zContractStartTime: [{
            required: true,
            message: "A2Z合约开始时间不能为空",
          }, ],
          a2zContractEndTime: [{
            required: true,
            message: "A2Z合约开始时间不能为空",
          }, {
							validator: validateA2ZContractEndTime,
              message: "A2Z合约结束时间不能早于等于A2Z合约开始时间",
					}],
          // marketingAmount: [{
          //   validator: validatePositiveNum,
          //   message: "营销账户高支持8位整数和2位小数正数或零",
          // }],
          creditAmount: [{
            validator: validatePositiveNum,
            message: "信用账户最高支持8位整数和2位小数正数或零",
          }],
          // a2zMarketingAmount: [{
          //   validator: validatePositiveNum,
          //   message: "营销账户最高支持8位整数和2位小数正数或零",
          // }],
        },
				rechargeRecordFlag: false,
				packageRecordFlage: false,
				packageRecordDetailsFlage: false,
				billFlowFlage: false,
				trafficDetailsFlage: false,
				exportModal: false, //导出弹框标识
				rechargeTableData: [], //列表信息
				rechargeTableLoading: false,
				rechargeTotal: 0,
				rechargePageSize: 10,
				rechargePage: 1,
				rechargeColumns: [{
						title: "充值时间",
						key: "chargeTime",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "充值金额",
						key: "amount",
						align: "center",
						minWidth: 150,
						tooltip: true,
						render: (h, params) => {
							return h("span", params.row.amount);
						},
					},
					{
						title: "充值类型",
						key: "chargeType",
						align: "center",
						minWidth: 180,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const text =
								row.chargeType == "1" ?
								"缴付账单" :
								row.chargeType == "2" ?
								"增加押金" :
								row.chargeType == "3" ?
								"增加预存款" :
								row.chargeType == "4" ?
								"酬金返还" :
								row.chargeType == "5" ?
								"渠道商代销收入调账" :
								row.chargeType == "6" ?
								"A2Z押金充值" :
								row.chargeType == "7" ?
								"A2Z账单缴付" :
                row.chargeType == "8" ?
                "营销返利-代销" :
                row.chargeType == "9" ?
                "营销返利-A2Z" :
                row.chargeType == "10" ?
                "A~Z预存款充值" :
                row.chargeType == "11" ?
                "渠道商A2Z收入金额调账" :
                row.chargeType == "12" ?
                "渠道商资源合作收入金额调账" :
                row.chargeType == "13" ?
                "A2Z补计费" :
                row.chargeType == "14" ?
                "充值类型枚举值错误" :
								"--";

							return h("span", text);
						},
					},
					{
						title: "币种",
						key: "currencyCode",
						align: "center",
						minWidth: 150,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							//156 CNY,840 美元, 344 港币
							const text =
								row.currencyCode == "156" ?
								"人民币" :
								row.currencyCode == "840" ?
								"美元" :
								row.currencyCode == "344" ?
								"港币" :
								"--";
							return h("label", text);
						},
					},
				],
				searchMonth: "",
				packageListType: "1", //默认所有渠道套餐
				packageTableData: [], //列表信息
				packageTableLoading: false,
				packageTableDetailsLoading: false,
				searchloading: false,
				searchTrafficLoading: false,
        exportTrafficLoading: false,
				billExportLoading: false,
				billLoading: false,
				trafficLoading: false,
				packageTotal: 0,
				packageDetailsTotal: 0,
				packagePageSize: 10,
				packageDetailsPageSize: 10,
				packagePage: 1,
				packageDetailsPage: 1,
				packageColumns: [],
				packageDetailsTableData: [],
				packageDetailsColumns: [{
						title: "订单编号",
						key: "id",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "ICCID",
						key: "iccid",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "购买套餐",
						key: "nameEn",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "订单状态",
						key: "orderStatus",
						minWidth: 150,
						align: "center",
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.orderStatus) {
								case "1":
									text = "待发货";
									break;
								case "2":
									text = "已完成";
									break;
								case "3":
									text = "已退订/已回滚";
									break;
								case "4":
									text = "激活退订待审批";
									break;
								case "5":
									text = "已回收";
									break;
								default:
									text = "未知状态";
							}
							return h("label", text);
						},
					},

					{
						title: "金额",
						key: "amount",
						align: "center",
						minWidth: 100,
						tooltip: true,
						render: (h, params) => {
							return h("span", params.row.amount);
						},
					},
					{
						title: "币种",
						key: "currencyCode",
						align: "center",
						minWidth: 100,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							//156 CNY,840 美元, 344 港币
							const text =
								row.currencyCode == "156" ?
								"人民币" :
								row.currencyCode == "840" ?
								"美元" :
								row.currencyCode == "344" ?
								"港币" :
								"获取失败";
							return h("label", text);
						},
					},
					{
						title: "操作",
						slot: "action1",
						width: 200,
						fixed: "right",
						align: "center",
					},
				],
				total: 0, //账单流水总条数
				currentPage: 1,
				totalTraffic: 0, //流量明细总条数
				currentPageTraffic: 1,
				form: {
					startTime: "",
					endTime: "",
					date: []
				},
				trafficForm: {
					date: [],
					startTime: "",
					endTime: "",
					localId: "",
				},
				columns12: [{
					title: "时间",
					key: "createTime",
					align: "center",
					minWidth: 120,
					tooltip: true,
				}, {
					title: "类型",
					key: "type",
					align: "center",
					minWidth: 100,
					render: (h, params) => {
						const row = params.row;
						const text =
							row.type == "1" ?
							"缴付账单" :
							row.type == "2" ?
							"增加押金" :
							row.type == "3" ?
							"增加预存款" :
							row.type == "4" ?
							"酬金返还" :
							row.type == "5" ?
							"套餐订购" :
							row.type == "6" ?
							"加油包订购" :
							row.type == "7" ?
							"套餐退订" :
							row.type == "8" ?
							"加油包退订" :
							row.type == "9" ?
							"渠道商收入调账" :
              row.type == "10" ?
              "营销返利" :
              row.type == '11' ?
              "imsi费统计" :
              row.type == '12' ?
              "赔付" :
							"";
						return h("label", text);
					},
				}, {
					title: "币种",
					key: "currencyCode",
					align: "center",
					minWidth: 100,
					tooltip: true,
					render: (h, params) => {
						const row = params.row;
						//156 CNY,840 美元, 344 港币
						const text =
							row.currencyCode == "156" ?
							"人民币" :
							row.currencyCode == "840" ?
							"美元" :
							row.currencyCode == "344" ?
							"港币" :
							"获取失败";
						return h("label", text);
					},
				}, {
					title: "交易金额",
					key: "amount",
					align: "center",
					minWidth: 100,
					tooltip: true,
				}, {
					title: "账户余额",
					key: "tdeposit",
					align: "center",
					minWidth: 100,
					tooltip: true,
				}],
				columnsTraffic: [{
					title: "date",
					key: "date",
					align: "center",
					minWidth: 100,
					tooltip: true,
				}, {
					title: "Country/Region",
					key: "countryOrRegion",
					align: "center",
					minWidth: 100,
					tooltip: true,
				}, {
					title: "Usage (MB)",
					key: "usedTraffic",
					align: "center",
					minWidth: 100,
					tooltip: true,
				}, {
					title: "Amount",
					key: "amount",
					align: "center",
					minWidth: 100,
					tooltip: true,
				}],
				billData: [],
				trafficData: [],
				taskId: '',
				taskName: '',
				localList: [],
				billRule: {
					date: [{
						type: 'array',
						required: true,
						message: '请选择时间段',
						trigger: 'blur',
						fields: {
							0: {
								type: 'date',
								required: true,
								message: '请选择时间段'
							},
							1: {
								type: 'date',
								required: true,
								message: '请选择时间段'
							}
						}
					}],
				},
				trafficRule: {},
				groupIdList: [],
				TemplatesCreatList: [],
				newPackageNums: '', //判断国家卡池关联组是否能修改
				a2zStopStatus: "A2Z停用",
				a2zRecoverStatus: "A2Z恢复",
				a2zClick: false,
				packageUseShow: false, //流量通知URL吧是否必填
				newArr: [],
        newArr1: [],
        newArr2: [],
				modeList: [],
				appidsList: [],//可用定向应用列表
				appBindIds: [],//可用定向应用套餐绑定
				appList: [], //自己按套餐改为否时，传可用定向应用原数据给后端
        tempList: [],//短信模板
        acountingList: [],//出账规则
        a2zRuleList: [],//流量计费规则
        imsiFeeList: [],//IMSI费规则列表
        salesMailList: [],//销售邮箱列表
        operator: [],//运营商列表
        chargingRule: [
          { code: '1', chargingName: '按国家计费' },
          { code: '2', chargingName: '按运营商计费' }
          // 其他计费规则...
        ],
			};
		},

		created() {
			this.$nextTick(() => {
				this.changedRuleValidate.forEach((element) => {
					this.$set(this.ruleAddValidate[element][0], 'required', false);
				});
			})
			this.$nextTick(() => {
				this.changedChannelCreatValidate.forEach((element) => {
					this.$set(this.ruleAddValidate[element][0], 'required', false);
				});
			})
		},

		methods: {
			examineModal(id, type) {
				this.$Modal.confirm({
					title: type == "2" ? "确认执行通过操作？" : "确认执行不通过操作？",
					onOk: () => {
						examineOrder({
								id: id,
								status: type,
							})
							.then((res) => {
								if (res && res.code == "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.getPurchaseRecordsDetails(0);
								} else {
									throw res;
								}
							})
							.catch((err) => {});
					},
				});
			},
			//解绑
			unsubscribeModal(id) {
				this.$Modal.confirm({
					title: "确认退订？",
					onOk: () => {
						unsubscribe(id)
							.then((res) => {
								if (res && res.code == "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});

									if (this.packageDetailsTableData.length === 1) {
										this.packageDetailsTableData = [];
										this.packageRecordDetailsFlage = false;
										this.getPurchaseRecords(0);
									} else {
										this.getPurchaseRecordsDetails(0, this.recordDetailId);
									}


								} else {
									throw res;
								}
							})
							.catch((err) => {});
					},
				});
			},
      handleCountryChange(mcc, index, flag) {
        if (mcc) {
          this.getOperatorsByMcc(mcc, index, flag);
        } else {
          // this.$set(this.formObj.a2zOperators[index], 'a2zOperator', []);
          this.$set(this.formObj.a2zOperators[index], 'operatorsList', []); // 清空 operatorsList
        }
      },
      getOperatorsByMcc(mcc, index, flag) {
        getChoiceOperators({
          mcc: mcc,
          pageSize: -1,
          pageNum: -1
        }).then((res) => {
          if (res.code === '0000') {
            const operators = res.data.records.map(op => ({
              id: op.operatorId,
              operatorName: op.operatorName
            }));
            operators.sort((str1, str2) => {
              return str1.operatorName.localeCompare(str2.operatorName);
            });
            this.$set(this.formObj.a2zOperators[index], 'operatorsList', operators); // 更新 operatorsList
            if (flag==1){
              this.$set(this.formObj.a2zOperators[index], 'a2zOperator', []); // 清空 a2zOperator
            }
          }
        }).catch((err) => {
          console.error('获取运营商列表失败:', err);
        });
      },
      addRuleHandle() {
        this.formObj.a2zOperators.push({
          a2zOperatorMcc: "",
          a2zOperatorChargeType: "",
          a2zOperator: [],
          index: this.formObj.a2zOperators.length + 1
        });
      },
      removeRule(index) {
        if (this.formObj.a2zOperators.length > 1 && index !== 0) {
          this.formObj.a2zOperators.splice(index, 1);
        }
      },
			packageGroupsFormatData1(data) {
				let arr = [];
				let twoArr = [];
				getPurchasePackageGroup({
					list: 1,
				}).then((res) => {
					if (res.code === "0000") {
						//将后端返回的"可购买套餐"对象{}变为数组[]得到newArr
						for (let i in data) {
							this.newArr1.push({
								groupId: i,
								groupName: data[i].groupName,
								cooperationMode: data[i].cooperationMode
							})
						}
						if (this.newArr1.length > 0) { //空数组不进入循环
							//将newArr1和"可购买套餐data"下拉框进行对比，将newArr1多余的套餐push进下拉框中
							for(var i=0; i<this.newArr1.length; i++) {
								var name = this.newArr1[i].groupName
								var num = this.newArr1[i].groupId
								var type = this.newArr1[i].cooperationMode
								var isExist = false
								twoArr = JSON.parse(JSON.stringify(res.data))//对象深拷贝，修改不影响之前的
								for(var j=0; j<twoArr.length; j++) {
									var name2 = twoArr[j].groupName
									var num2=twoArr[j].groupId
									var type2=twoArr[j].cooperationMode
									if (num == num2) {
										//如果newArr1的groupId和下拉框的相同，则跳出循环，继续找下一个
										isExist = true
										break;
									}
								}
								if(!isExist) { //如果newArr1中存在下拉框不包含的值，则push进下twoArr拉框中
									twoArr.push({
										groupId: num,
										groupName: name,
										cooperationMode: type
									})
								}
								res.data = twoArr
							}
						}
						// 可购买套餐下拉框列表赋值
						this.dxPackageList = res.data

						// 循环后端返回的对象
						for (const key in data) {
							//判断有无key值
							if (Object.hasOwnProperty.call(data, key)) {
								//获取 后端返回的套餐名称
								const element = data[key].groupName;
								const cooperationMode = data[key].cooperationMode;
								//过滤出后端返回与前端下拉框一致的数据
								let packageList = this.dxPackageList.filter(item => item.groupId === key)
								// 把数据push进arr中，加上合作模式
								arr.push(
									JSON.stringify({
										groupId: key,
										groupName: element,
										channelCooperationMode: cooperationMode,
									})
								);
							}
						}
					}
				});
				return arr;
			},

      packageGroupsFormatData2(data) {
      	let arr = [];
      	let twoArr = [];
      	getPurchasePackageGroup({
      		list: 2,
      	}).then((res) => {
      		if (res.code === "0000") {
      			//将后端返回的"可购买套餐"对象{}变为数组[]得到newArr
      			for (let i in data) {
      				this.newArr2.push({
      					groupId: i,
      					groupName: data[i].groupName,
      					cooperationMode: data[i].cooperationMode
      				})
      			}
      			if (this.newArr2.length > 0) { //空数组不进入循环
      				//将newArr2和"可购买套餐data"下拉框进行对比，将newArr2多余的套餐push进下拉框中
      				for(var i=0; i<this.newArr2.length; i++) {
      					var name = this.newArr2[i].groupName
      					var num = this.newArr2[i].groupId
      					var type = this.newArr2[i].cooperationMode
      					var isExist = false
      					twoArr = JSON.parse(JSON.stringify(res.data))//对象深拷贝，修改不影响之前的
      					for(var j=0; j<twoArr.length; j++) {
      						var name2 = twoArr[j].groupName
      						var num2=twoArr[j].groupId
      						var type2=twoArr[j].cooperationMode
      						if (num == num2) {
      							//如果newArr2的groupId和下拉框的相同，则跳出循环，继续找下一个
      							isExist = true
      							break;
      						}
      					}
      					if(!isExist) { //如果newArr2中存在下拉框不包含的值，则push进下twoArr拉框中
      						twoArr.push({
      							groupId: num,
      							groupName: name,
      							cooperationMode: type
      						})
      					}
      					res.data = twoArr
      				}
      			}
      			// 可购买套餐下拉框列表赋值
      			this.dxPackageList = res.data

      			// 循环后端返回的对象
      			for (const key in data) {
      				//判断有无key值
      				if (Object.hasOwnProperty.call(data, key)) {
      					//获取 后端返回的套餐名称
      					const element = data[key].groupName;
      					const cooperationMode = data[key].cooperationMode;
      					//过滤出后端返回与前端下拉框一致的数据
      					let packageList = this.dxPackageList.filter(item => item.groupId === key)
      					// 把数据push进arr中，加上合作模式
      					arr.push(
      						JSON.stringify({
      							groupId: key,
      							groupName: element,
      							channelCooperationMode: cooperationMode,
      						})
      					);
      				}
      			}
      		}
      	});
      	return arr;
      },

			init() {
				getDistributorsDetail({
					corpId: this.corpId
				}).then((res) => {
					if (res.code === "0000") {
						this.newPackageNums = res.data["newPackageNum"];

						this.$set(this.formObj, "accountNum", res.data["indirectCount"]);
						this.$set(this.formObj, "accounts", res.data["accounts"]);
						this.$set(this.formObj, "channelCooperationMode", res.data["channelCooperationMode"])
						//渠道合作模多选下拉框只能多，不能少
						this.modeList = res.data["channelCooperationMode"]
						this.$set(this.formObj, "a2zPreDeposit", res.data["a2zPreDeposit"]);
						this.$set(this.formObj, "runoutofBalanceRemindThreshold", res.data["runoutofBalanceRemindThreshold"]);
            this.$set(this.formObj, "resourceRunoutofBalanceRemindThreshold", res.data["resourceRunoutofBalanceRemindThreshold"]);
						this.$set(this.formObj, "prohibitiveBuyRemindThreshold", res.data["prohibitiveBuyRemindThreshold"]);
            this.$set(this.formObj, "resourceProhibitiveBuyRemindThreshold", res.data["resourceProhibitiveBuyRemindThreshold"]);
						this.$set(this.formObj, "stopUseRemindThreshold", res.data["stopUseRemindThreshold"]);
						this.$set(this.formObj, "resourceStopUseRemindThreshold", res.data["resourceStopUseRemindThreshold"]);
						this.$set(this.formObj, "a2zCardUseSwitch", res.data["a2zCardUseSwitch"]);
						this.$set(this.formObj, "overdueNotify", res.data["overdueNotify"]);
						this.$set(this.formObj, "overdueNotifyUrl", res.data["overdueNotifyUrl"]);
						this.$set(this.formObj, "packageUseNotifyUrl", res.data["packageUseNotifyUrl"]);
						this.$set(this.formObj, "salesMail", res.data["salesMail"]);
            // 代销和A2Z信息模板回显
            const dxSmsTemplateVo = res.data["smsTemplateVo"].filter(template => template.cooperationMode === "1");
            const a2zSmsTemplateVo = res.data["smsTemplateVo"].filter(template => template.cooperationMode === "2");
            this.$set(
            	this.formObj, "dxSmsTemplateVo", dxSmsTemplateVo.map(template => JSON.stringify(template))
            );
            this.$set(
            	this.formObj, "a2zSmsTemplateVo", a2zSmsTemplateVo.map(template => JSON.stringify(template))
            );
            this.$set(this.formObj, "distributionAccountingPeriodId", res.data["distributionAccountingPeriodId"]);
            this.$set(this.formObj, "a2zAccountingPeriodId", res.data["a2zAccountingPeriodId"]);
            this.$set(this.formObj, "resourceAccountingPeriodId", res.data["resourceAccountingPeriodId"]);
            // imsi费规则回显
            const consignmentImsiAmount = res.data["freeImsiVo"].filter(template => template.cooperationMode === "1");
            const a2zImsiAmount = res.data["freeImsiVo"].filter(template => template.cooperationMode === "2");
            const resourceImsiAmount = res.data["freeImsiVo"].filter(template => template.cooperationMode === "3");

            const consignmentImsiAmountRuleIds = consignmentImsiAmount.map(item => item.ruleId);
            const a2zImsiAmountRuleIds = a2zImsiAmount.map(item => item.ruleId);
            const resourceImsiAmountRuleIds = resourceImsiAmount.map(item => item.ruleId);
            this.$set(this.formObj, "consignmentImsiAmount", consignmentImsiAmountRuleIds);
            this.$set(this.formObj, "a2zImsiAmount", a2zImsiAmountRuleIds);
            this.$set(this.formObj, "resourceImsiAmount", resourceImsiAmountRuleIds);

            this.$set(this.formObj, "approvalPackage", res.data["approvalPackage"]);
            // 合约时间
            this.$set(this.formObj, "a2zContractStartTime", res.data["a2zContractStartTime"] === null ? "" : new Date(res.data["a2zContractStartTime"]));
            this.$set(this.formObj, "a2zContractEndTime",  res.data["a2zContractEndTime"] === null ? "" : new Date(res.data["a2zContractEndTime"]));
            this.$set(this.formObj, "contractBeginTime", res.data["contractStartTime"] === null ? "" : new Date(res.data["contractStartTime"]));
            this.$set(this.formObj, "contractEndTime", res.data["contractEndTime"] === null ? "" : new Date(res.data["contractEndTime"]));
            this.$set(this.formObj, "a2zChannelType", res.data["a2zChannelType"]);
            this.$set(this.formObj, "resourceChannelType", res.data["resourceChannelType"]);
            this.$set(this.formObj, "a2zRuleId", res.data["a2zRuleId"]);
            this.$set(this.formObj, "resourceRuleId", res.data["resourceRuleId"]);
            this.$set(
            	this.formObj, "a2zDepositAmount", res.data["a2zDepositAmount"]
            );
            this.$set(
            	this.formObj, "groupId", res.data["groupId"]
            );
						// 如果authObj===null 则取原来表数据值，否则取authObj
						let data = {}
						if (res.data["authObj"]) {
							data = res.data["authObj"]
						} else {
							data = res.data
						}
						this.$set(this.formObj, "unsubscribeRule", data["unsubscribeRule"]);
						this.$set(this.formObj, "corpName", data["corpName"]);
						this.$set(this.formObj, "channelStatus", data["isSub"]);
						this.$set(this.formObj, "appkey", data["appKey"]);
						this.$set(this.formObj, "appSecret", data["appSecret"]);
						this.$set(this.formObj, "channelCode", data["channelCode"]);
						this.$set(this.formObj, "activateNotificationUrl", data["activateNotificationUrl"]);
						this.$set(this.formObj, "channelUrl", data["channelUrl"]);
						this.$set(this.formObj, "ebsCode", data["ebsCode"]);
						this.$set(this.formObj, "activateNotification", data["activateNotification"]);
						this.$set(this.formObj, "address", data["address"]);
						this.$set(this.formObj, "companyName", data["companyName"]);
						this.$set(this.formObj, "currencyCode", data["currencyCode"]);
						this.$set(this.formObj, "mail", data["email"]);
						this.$set(this.formObj, "resetPrice", data["resetPrice"]);
						this.$set(this.formObj, "deposit", data["deposit"]);
						this.$set(this.formObj, "depositNotify", data["depositeRemindThreshold"]);
						this.$set(this.formObj, "discount", data["discount"]);
						this.$set(this.formObj, "internalOrder", data["internalOrder"]);
						this.$set(this.formObj, "channelType", data["channelType"]);
						this.$set(this.formObj, "totalDeposit", data["totalDeposit"]);
						this.$set(this.formObj, "limitType", data["indirectType"] ? String(data["indirectType"]) : "");
						this.$set(this.formObj, "esimNotifySwitch", data["esimNotification"]);
						this.$set(this.formObj, "esimNotifyUrl", data["esimNotificationUrl"]);
            // 代销营销账户
            this.$set(this.formObj, "marketingAmount", data["marketingAmount"]);
            this.$set(this.formObj, "creditAmount", data["creditAmount"]);
            // A2Z信用账户、营销账户
            this.$set(this.formObj, "a2zMarketingAmount", data["a2zMarketingAmount"]);
						//前端的格式是数字数组[1,2],后端返回的是字符串数组['1','2'],需要map梳理
						this.appList = data['appids'] //自建套餐是改否时传后端返回的值回去
						this.appBindIds = res.data['appBindIds'] ? res.data['appBindIds'] : [];//绑定的套餐不允许删除
						this.$set(this.formObj, "appids", data['appids']);//不能对data['appids']直接map，会影响其他数据
            if (res.data["channelCooperationMode"].includes('2')) {
              getChannelA2zOperator({
                corpId: this.corpId
              }).then((res) => {
                if (res.code === '0000') {
                  let a2zOperators = res.data;
                  if (a2zOperators && a2zOperators.length > 0) {
                    a2zOperators = a2zOperators.map((config, index) => {
                      this.handleCountryChange(config.country.mcc,index);
                      console.log(config.operators.map(op => op.id))
                      return {
                        a2zOperatorMcc: config.country.mcc,
                        a2zOperatorChargeType: config.a2zOperatorChargeType, // 将 rule 转换为数组
                        a2zOperator: config.operators.map(op => op.id), // 提取 operatorCode
                        index: index
                      };
                    });
                  } else {
                    a2zOperators = [{
                      a2zOperatorMcc: "",
                      a2zOperatorChargeType: "",
                      a2zOperator: [],
                      index: 0
                    }];
                  }
                  this.$set(this.formObj, "a2zOperators", a2zOperators);
                }
              });
            }

            // 间接收入比例不为空启动校验
						if (data["indirectRatio"]) {
							this.changedRuleValidate.forEach((element) => {
								this.$set(this.ruleAddValidate[element][0], 'required', true);
							});
						}
						//允许自建套餐为是时启动校验
						if (data["allowNewPackage"] == '1') {
							this.changedChannelCreatValidate.forEach((element) => {
								this.$set(this.ruleAddValidate[element][0], 'required', true);
							});
						} else {
							this.changedChannelCreatValidate.forEach((element) => {
								this.$set(this.ruleAddValidate[element][0], 'required', false);
							});
						}
						this.$set(
							this.formObj,
							"indirectEarningsRatio",
							data["indirectRatio"]
						);
						this.$set(
							this.formObj,
							"directEarningsRatio",
							data["directRatio"]
						);
						this.$set(this.formObj, "createTime", data["indirectCount"]);

						this.$set(
							this.formObj,
							"contractSellAmount",
							data["depositAmount"]
						);
						this.$set(
							this.formObj,
							"allowNewPackage",
							data["allowNewPackage"]
						);
						if (this.typeFlag === "info") {
							this.$set(
								this.formObj,
								"limitPacakageNum",
								res.data["newPackageNum"] + "/" + data["limitPackageNum"]
							);
						} else {
							this.$set(
								this.formObj,
								"limitPacakageNum",
								String(data["limitPackageNum"])
							);
							this.ruleAddValidate['limitPacakageNum'][1].pattern = /^[1-9]+[0-9]*$/;
							this.ruleAddValidate['limitPacakageNum'][1].message = "只支持正整数";
						}
						this.$set(
							this.formObj,
							"upccTemplateIds",
							data["upccTemplateInfo"]
						);
						this.$set(this.formObj, "corpId", data["corpId"]);
						this.$set(this.formObj, "accounts", data["accounts"]);
						if (!this.a2zClick) {
              let originalObject = JSON.parse(JSON.stringify(data["packageGroups"]))

              const mode1Objects = {};
              const mode2Objects = {};

              for (const key in originalObject) {
                if (originalObject.hasOwnProperty(key)) {
                    const group = originalObject[key];
                    if (group.cooperationMode === "1") {
                        mode1Objects[key] = group;
                    } else if (group.cooperationMode === "2") {
                        mode2Objects[key] = group;
                    }
                }
              }
              // 代销和A2Z可购买套餐回显
              if (res.data["channelCooperationMode"].includes('1')) {
                this.$set(
                	this.formObj,
                	"dxPackageInfosString",
                	this.packageGroupsFormatData1(mode1Objects)
                );
              }
              if (res.data["channelCooperationMode"].includes('2')) {
                this.$set(
                	this.formObj,
                	"a2zPackageInfosString",
                	this.packageGroupsFormatData2(mode2Objects)
                );
              }
						}
						if (data["packageUsePercentage"].length == 0) {
							this.formObj.packageUsePercentage = [{
								value: '',
								index: 1,
							}]
						} else {
							this.$set(this.formObj, "packageUsePercentage", data["packageUsePercentage"]);
						}
					}
				});
			},

			//总单退订
			unsubscribeAll(id) {
				this.$Modal.confirm({
					title: "确认全部退订？",
					onOk: () => {
						unsubscribeBatch(id)
							.then((res) => {
								if (res && res.code == "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.getPurchaseRecords(0);
								} else {
									throw res;
								}
							})
							.catch((err) => {});
					},
				});
			},

			//审核
			examine(id, type) {
				this.$Modal.confirm({
					title: type == "2" ? "确认执行通过操作？" : "确认执行不通过操作？",
					onOk: () => {
						examineOrderBatch({
								id: id,
								status: type,
							})
							.then((res) => {
								if (res && res.code == "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.getPurchaseRecords(this.packagePage);
								} else {
									throw res;
								}
							})
							.catch((err) => {});
					},
				});
			},

			limitTypeChange(data) {
				if (data == '1') {
					this.ruleAddValidate['accountNum'][0].required = true;
					this.ruleAddValidate['createTime'][0].required = false;
				} else {
					this.ruleAddValidate['accountNum'][0].required = false;
					this.ruleAddValidate['createTime'][0].required = true;
				}

			},

			// 是否允许自建套餐
			changeChannelCreat() {
				if (this.formObj.allowNewPackage == 1) {
					this.changedChannelCreatValidate.forEach(element => {
						this.ruleAddValidate[element][0].required = true;
					})
					//如果渠道商合作模式包含A2Z并且自建为是时获取可用定向应用列表
					if (this.formObj.channelCooperationMode.includes('2')) {
						this.getAppidsList()
					}
				} else {
					this.changedChannelCreatValidate.forEach(element => {
						this.ruleAddValidate[element][0].required = false;
					})
					this.formObj.limitPacakageNum = ""
					this.formObj.groupId = []
					this.formObj.upccTemplateIds = []
				}
				//如果渠道商合作模式包含A2Z并且自建为否时清空可用定向应用列表
				if (this.formObj.channelCooperationMode.includes('2')) {
					if( this.typeFlag != "update" ) {
						this.formObj.appids = []
					} else {
						this.formObj.appids = this.appList
					}
				}
			},

			// 间接收入比例(%)不为空，则加校验
			changedIndirectEarningsRatio() {
				if (this.formObj.indirectEarningsRatio) {
					this.changedRuleValidate.forEach(element => {
						this.ruleAddValidate[element][0].required = true;
					});
				} else {
					this.changedRuleValidate.forEach(element => {
						this.ruleAddValidate[element][0].required = false;
					});
					this.$refs["formObj"].validate();
				}
			},

			// 充值记录查询接口
			getDistributorsRecord(page) {
				if (page === 0) {
					this.rechargePage = 1;
				}

				this.rechargeTableLoading = true;
				getDistributorsRecord({
					corpId: this.corpId,
					pageNumber: this.rechargePage,
					pageSize: 10,
				}).then((res) => {
					if (res.code === "0000") {
						this.rechargeTableData = res.data.records;
						this.rechargeTotal = res.data.totalCount;
					}
					this.rechargeTableLoading = false;
					this.rechargeRecordFlag = true;
				});
			},
			getpackageInfo() {
				// 合作模式为代销时，可用金额和渠道商类型必填。A2Z时为选填
        if (this.formObj.channelCooperationMode.includes('1')) {
          this.getPackages1()
        }
        if (this.formObj.channelCooperationMode.includes('2')) {
          this.getPackages2()
          if (this.typeFlag == "add") {
            this.formObj.resourceAccountingPeriodId = ''
          }
        }
				//如果渠道商合作模式包含A2Z并且自建为是时获取可用定向应用列表
				if (this.formObj.allowNewPackage == '1') {
					if (this.formObj.channelCooperationMode.includes('2')) {
						this.getAppidsList()
					} else {
						if (this.typeFlag != "update") {
							this.formObj.appids = []
						} else {
							this.formObj.appids = this.appList
						}
					}
				}
			},
			// 初始化可购买套餐
			getPackages1() {
				let arr = [];
				let twoArr = []
				getPurchasePackageGroup({
					list: 1,
				}).then((res) => {
					if (res.code === "0000") {
						if (this.typeFlag === "add") {
              this.dxPackageList = res.data.filter(item => item.cooperationMode === "1");
						} else {
              this.newArr1 = this.newArr1.filter(item => item.cooperationMode === "1");
							if (this.newArr1.length > 0) { //空数组不进入循环
								//将newArr1和"可购买套餐"下拉框进行对比，将newArr1多余的套餐push进下拉框中
								for(var i=0; i<this.newArr1.length; i++) {
									var name = this.newArr1[i].groupName
									var num = this.newArr1[i].groupId
									var type = this.newArr1[i].cooperationMode
									var isExist = false
									twoArr = JSON.parse(JSON.stringify(res.data))//对象深拷贝，修改不影响之前的
									for(var j=0; j<twoArr.length; j++) {
										var name2 = twoArr[j].groupName
										var num2= twoArr[j].groupId
										var type2= twoArr[j].cooperationMode
										if (num == num2) {
											//如果newArr1的groupId和下拉框的相同，则跳出循环，继续找下一个
											isExist = true
											break;
										}
									}
									if(!isExist) { //如果newArr1中存在下拉框不包含的值，则push进下twoArr拉框中
										twoArr.push({
											groupId: num,
											groupName: name,
											cooperationMode: type
										})
									}
									res.data = twoArr
								}
							}
							this.dxPackageList = res.data;

							// 循环后端返回的对象
							for (const key in this.newArr1) {
								//判断有无key值
								if (Object.hasOwnProperty.call(this.newArr1, key)) {
									//获取 后端返回的套餐名称
									const element = this.newArr1[key].groupName;
									const cooperationMode = this.newArr1[key].cooperationMode;
									//过滤出后端返回与前端下拉框一致的数据
									let packageList = this.dxPackageList.filter(item => item.groupId === key)
									// 把数据push进arr中，加上合作模式
									arr.push(
										JSON.stringify({
											groupId: key,
											groupName: element,
											channelCooperationMode: cooperationMode,
										})
									);
								}
							}
						}
					}
				});
				return arr
			},

      getPackages2() {
        let arr = [];
        let twoArr = []
        getPurchasePackageGroup({
        	list: 2,
        }).then((res) => {
        	if (res.code === "0000") {
        		if (this.typeFlag === "add") {
              this.a2zPackageList = res.data.filter(item => item.cooperationMode === "2");
        		} else {
              this.newArr2 = this.newArr2.filter(item => item.cooperationMode === "2");
        			if (this.newArr2.length > 0) { //空数组不进入循环
        				//将newArr2和"可购买套餐"下拉框进行对比，将newArr2多余的套餐push进下拉框中
        				for(var i=0; i<this.newArr2.length; i++) {
        					var name = this.newArr2[i].groupName
        					var num = this.newArr2[i].groupId
        					var type = this.newArr2[i].cooperationMode
        					var isExist = false
        					twoArr = JSON.parse(JSON.stringify(res.data))//对象深拷贝，修改不影响之前的
        					for(var j=0; j<twoArr.length; j++) {
        						var name2 = twoArr[j].groupName
        						var num2= twoArr[j].groupId
        						var type2= twoArr[j].cooperationMode
        						if (num == num2) {
        							//如果newArr2的groupId和下拉框的相同，则跳出循环，继续找下一个
        							isExist = true
        							break;
        						}
        					}
        					if(!isExist) { //如果newArr2中存在下拉框不包含的值，则push进下twoArr拉框中
        						twoArr.push({
        							groupId: num,
        							groupName: name,
        							cooperationMode: type
        						})
        					}
        					res.data = twoArr
        				}
        			}
        			this.a2zPackageList = res.data;

        			// 循环后端返回的对象
        			for (const key in this.newArr2) {
        				//判断有无key值
        				if (Object.hasOwnProperty.call(this.newArr2, key)) {
        					//获取 后端返回的套餐名称
        					const element = this.newArr2[key].groupName;
        					const cooperationMode = this.newArr2[key].cooperationMode;
        					//过滤出后端返回与前端下拉框一致的数据
        					let packageList = this.a2zPackageList.filter(item => item.groupId === key)
        					// 把数据push进arr中，加上合作模式
        					arr.push(
        						JSON.stringify({
        							groupId: key,
        							groupName: element,
        							channelCooperationMode: cooperationMode,
        						})
        					);
        				}
        			}
        		}
        	}
        });
        return arr
      },

			formatpackageInfosStringToJSON(arr) {
				let tempArr = [];

				arr.forEach((element) => {
					tempArr.push(JSON.parse(element));
				});

				return tempArr;
			},

			//提交
			submit() {
				if (this.typeFlag === "add") {
					this.ruleAddValidate['limitPacakageNum'][1].pattern = /^[1-9]+[0-9]*$/;
					this.ruleAddValidate['limitPacakageNum'][1].message = "只支持正整数";
					this.ruleAddValidate['limitPacakageNum'][1].trigger = 'change';
				}
				if (this.formObj.allowNewPackage == 1) {
					this.changedChannelCreatValidate.forEach(element => {
						this.ruleAddValidate[element][0].required = true;
					})
				} else if (this.formObj.allowNewPackage == 2) {
					this.changedChannelCreatValidate.forEach(element => {
						this.ruleAddValidate[element][0].required = false;
					})
					this.$refs["formObj"].validate();
					this.formObj.limitPacakageNum = ""
					this.formObj.groupId = []
					this.formObj.upccTemplateIds = []
				} else {
					this.$refs["formObj"].validate();
					this.formObj.limitPacakageNum = ""
					this.formObj.groupId = []
					this.formObj.upccTemplateIds = []
				}
				this.$refs["formObj"].validate((valid) => {
					if (valid) {
            // 校验同一个国家同一个计费方式下的运营商是否重复
            const configMap = new Map();
            for (const config of this.formObj.a2zOperators) {
              const key = `${config.a2zOperatorMcc}-${config.a2zOperatorChargeType}`;
              if (!configMap.has(key)) {
                configMap.set(key, new Set(config.a2zOperator));
              } else {
                const existingOperators = configMap.get(key);
                for (const operator of config.a2zOperator) {
                  if (existingOperators.has(operator)) {
                    this.$Notice.error({
                      title: '操作提示',
                      desc: '同一个国家和同一个计费方式下的运营商不能重复。'
                    });
                    return;
                  }
                  existingOperators.add(operator);
                }
              }
            }
            this.formObj.a2zOperators = this.formObj.a2zOperators
              .map((operator, index) => {
                // 映射新的对象结构
                return {
                  a2zOperatorMcc: operator.a2zOperatorMcc || "",
                  a2zOperatorChargeType: operator.a2zOperatorChargeType || "",
                  a2zOperator: operator.a2zOperator || [],
                  index: operator.index || index
                };
              })
              .map((operator) => {
                // 校验字段完整性
                const { a2zOperatorMcc, a2zOperatorChargeType, a2zOperator } = operator;
                const allEmpty = a2zOperatorMcc === "" && a2zOperatorChargeType === "" && a2zOperator.length === 0;
                const allFilled = a2zOperatorMcc !== "" && a2zOperatorChargeType !== "" && a2zOperator.length > 0;

                if (!allEmpty && !allFilled) {
                  this.$Notice.error({
                    title: '操作提示',
                    desc: '请完整填入每行运营商配置信息'
                  });
                  return;
                }
                return operator;
              })
              .filter((operator) => {
                // 过滤掉不符合条件的对象
                const { a2zOperatorMcc, a2zOperatorChargeType, a2zOperator } = operator;
                return (
                  a2zOperatorMcc !== "" ||
                  a2zOperatorChargeType !== "" ||
                  a2zOperator.length > 0
                );
              });
						let func;
						if (this.typeFlag === "add") {
							func = addCustermInfo;
						} else {
							func = UpdateCustermInfo;
						}
						this.submitLoading = true;

						//合作模式仅为 “资源合作”时
						if (!this.formObj.dxPackageInfosString || this.formObj.dxPackageInfosString.length == 0) {
							this.formObj.dxPackageInfosString = []
							this.formObj.packageInfos = []
						} else {
							this.formObj.packageInfos = this.formatpackageInfosStringToJSON(
								this.formObj.dxPackageInfosString
							);
						}
            if (!this.formObj.a2zPackageInfosString || this.formObj.a2zPackageInfosString.length == 0) {
            	this.formObj.a2zPackageInfosString = []
            	this.formObj.packageInfos = []
            } else {
            	this.formObj.packageInfos = this.formatpackageInfosStringToJSON(
            		this.formObj.a2zPackageInfosString
            	);
            }
            let packageInfos = [...this.formObj.a2zPackageInfosString, ...this.formObj.dxPackageInfosString]
            let packageInfosString = [...this.formObj.a2zPackageInfosString, ...this.formObj.dxPackageInfosString]

            // 拼接A2Z和代销的短信模板
            let smsTemplateVo = [...this.formObj.dxSmsTemplateVo, ...this.formObj.a2zSmsTemplateVo]

						const formData = JSON.parse(JSON.stringify(this.formObj));
						formData.contractSellAmount = this.$moneyCover(formData.contractSellAmount, 100);
            formData.a2zDepositAmount = this.$moneyCover(formData.a2zDepositAmount, 100);
						formData.resetPrice = this.$moneyCover(formData.resetPrice, 100);
						formData.deposit = this.$moneyCover(formData.deposit, 100);
						formData.totalDeposit = this.$moneyCover(formData.totalDeposit, 100);
						formData.depositNotify = this.$moneyCover(formData.depositNotify, 100);
						formData.runoutofBalanceRemindThreshold = this.$moneyCover(formData
							.runoutofBalanceRemindThreshold, 100);
            formData.resourceRunoutofBalanceRemindThreshold = this.$moneyCover(formData
            	.resourceRunoutofBalanceRemindThreshold, 100);
						formData.prohibitiveBuyRemindThreshold = this.$moneyCover(formData
							.prohibitiveBuyRemindThreshold, 100);
            formData.resourceProhibitiveBuyRemindThreshold = this.$moneyCover(formData
            	.resourceProhibitiveBuyRemindThreshold, 100);
						formData.stopUseRemindThreshold = this.formObj.channelCooperationMode.includes('2') && formData.stopUseRemindThreshold ? this.$moneyCover(formData.stopUseRemindThreshold, 100) : 0;
						formData.resourceStopUseRemindThreshold = this.$moneyCover(formData.resourceStopUseRemindThreshold, 100);
            // 短信模板
            formData.smsTemplateVo = this.formatpackageInfosStringToJSON(smsTemplateVo)
            // 可购买套餐组a2z和代销拼接后的
            formData.packageInfosString = packageInfosString
            formData.packageInfos = this.formatpackageInfosStringToJSON(packageInfos)
            // 出账规则
            formData.distributionAccountingPeriodId = this.formObj.distributionAccountingPeriodId ? this.formObj.distributionAccountingPeriodId : undefined
            formData.a2zAccountingPeriodId = this.formObj.a2zAccountingPeriodId ? this.formObj.a2zAccountingPeriodId : undefined
            formData.resourceAccountingPeriodId = this.formObj.channelCooperationMode.includes('2') && this.formObj.channelCooperationMode.includes('3') ? this.formObj.a2zAccountingPeriodId :
              this.formObj.resourceAccountingPeriodId ? this.formObj.resourceAccountingPeriodId : undefined
            // 合约时间
            formData.contractBeginTime = this.formObj.contractBeginTime ? this.formObj.contractBeginTime : undefined
            formData.contractEndTime = this.formObj.contractEndTime ? this.formObj.contractEndTime : undefined
            formData.a2zContractStartTime = this.formObj.a2zContractStartTime ? this.formObj.a2zContractStartTime : undefined
            formData.a2zContractEndTime = this.formObj.a2zContractEndTime ? this.formObj.a2zContractEndTime : undefined
            // imsi费规则
            formData.consignmentImsiAmount = this.formObj.consignmentImsiAmount ? this.formObj.consignmentImsiAmount : undefined
            formData.a2zImsiAmount = this.formObj.a2zImsiAmount ? this.formObj.a2zImsiAmount : undefined
            formData.resourceImsiAmount = this.formObj.resourceImsiAmount ? this.formObj.resourceImsiAmount : undefined
            // 可购买套餐组
            formData.a2zPackageInfosString = this.formObj.a2zPackageInfosString ? this.formObj.a2zPackageInfosString : undefined
            formData.dxPackageInfosString = this.formObj.dxPackageInfosString ? this.formObj.dxPackageInfosString : undefined
            // 短信模板
            formData.dxSmsTemplateVo = this.formObj.dxSmsTemplateVo ? this.formObj.dxSmsTemplateVo : undefined
            formData.a2zSmsTemplateVo = this.formObj.a2zSmsTemplateVo ? this.formObj.a2zSmsTemplateVo : undefined
            // 流量计费规则
            formData.a2zRuleId = this.formObj.a2zRuleId ? this.formObj.a2zRuleId : undefined
            formData.resourceRuleId = this.formObj.resourceRuleId ? this.formObj.resourceRuleId : undefined
            // 营销账户、信用账户
            // formData.marketingAmount = this.formObj.channelCooperationMode.includes('1') && formData.marketingAmount ? this.$moneyCover(formData.marketingAmount, 100) : 0
            formData.creditAmount = this.formObj.channelCooperationMode.includes('1') && formData.creditAmount ? this.$moneyCover(formData.creditAmount, 100) : 0
            // formData.a2zMarketingAmount = this.formObj.channelCooperationMode.includes('2') && formData.a2zMarketingAmount ? this.$moneyCover(formData.a2zMarketingAmount, 100) : 0
						if (formData.allowNewPackage == 2) {
							delete formData.groupId
							delete formData.limitPacakageNum
							delete formData.upccTemplateIds
						} else {
							formData.groupId = formData.groupId
						}
						// 若是新增或修改，则删掉marketingAmount，a2zMarketingAmount
						if (this.typeFlag === "add" || this.typeFlag === "update") {
							delete formData.marketingAmount
							delete formData.a2zMarketingAmount
						}
						func(formData)
							.then((res) => {
								if (res.code === "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.$router.push({
										name: "channelIndex"
									});
								}
							})
							.finally(() => {
								this.submitLoading = false;
							});
					}
				});
			},

			// 重置
			reset(name) {
				if ("add" != this.typeFlag) {
					this.corpId = this.$route.query.id;
					if (this.$route.query.type === "Add") {
						this.$refs[name].resetFields();
						this.typeFlag = "add";
						this.packageFlag = false;
					} else if (this.$route.query.type === "Update") {
						this.init();
						this.$set(
							this.formObj,
							"dxPackageInfosString",
							this.packageGroupsFormatData1(null)
						);
            this.$set(
            	this.formObj,
            	"a2zPackageInfosString",
            	this.packageGroupsFormatData2(null)
            );
						this.typeFlag = "update";
						this.packageFlag = false;
					} else {
						this.init();
					}
				}
			},

			back() {
				// 返回渠道商页面
				this.$router.push({
					path: '/channelInfo',
				})
			},

			//押金缴纳记录
			getRechargeRecord() {
				this.getDistributorsRecord(0);
			},

			//缴费记录导出
			exportRechargeRecord() {
				this.$Notice.success({
					title: "操作提示",
					desc: "操作成功",
				});
			},

			//加载表列信息
			loadPackageRecordColumns() {
				// zh-CN  en-US
				let lang = this.$i18n.locale;
				let Obj = {
					"zh-CN": "packageName",
					"en-US": 'nameEn'
				}

				var defaultColumns = [{
						title: "订单详情",
						width: 100,
						slot: "orderInfo",
						align: "center",
					},
					{
						title: "订单日期",
						key: "orderDate",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "订单ID",
						key: "orderUniqueId",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "ICCID",
						key: "iccid",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},

					{
						title: "购买套餐",
						key: Obj[lang],
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "购买渠道",
						key: "orderChannel",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "订单状态",
						key: "orderStatus",
						align: "center",
						minWidth: 150,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.orderStatus) {
								case "1":
									text = "待发货";
									break;
								case "2":
									text = "已完成";
									break;
								case "3":
									text = "已退订";
									break;
								case "4":
									text = "激活退订待审批";
									break;
								case "5":
									text = "部分退订";
									break;
								case "6":
									text = "部分发货";
									break;
								case "7":
									text = "已回收";
									break;
								case "8":
									text = "部分回收";
									break;
								case "9":
									text = "复合状态";
									break;
								default:
									text = "未知状态";
							}
							return h("label", text);
						},
					},
					{
						title: "购买份数",
						key: "count",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},

					{
						title: "金额",
						key: "amount",
						align: "center",
						minWidth: 100,
						tooltip: true,
						render: (h, params) => {
							return h("span", params.row.amount);
						},
					},
					{
						title: "币种",
						key: "currencyCode",
						align: "center",
						minWidth: 100,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							//156 CNY,840 美元, 344 港币
							const text =
								row.currencyCode == "156" ?
								"人民币" :
								row.currencyCode == "840" ?
								"美元" :
								row.currencyCode == "344" ?
								"港币" :
								"--";
							return h("label", text);
						},
					},
					{
						title: "操作",
						slot: "action",
						width: 200,
						fixed: "right",
						align: "center",
					},
				];

				this.packageColumns = defaultColumns;
			},

			//套餐购买记录
			getPackageRecord() {
				this.loadPackageRecordColumns();
				this.getPurchaseRecords(0);
			},

			getPurchaseRecords(page) {
				this.packagePage = page
				if (page === 0) {
					this.packagePage = 1;
				}
				this.packageTableLoading = true;
				getOrderLists({
					orderUserId: this.corpId,
					pageNumber: page,
					pageSize: 10,
					startTime: this.searchMonth,
				}).then((res) => {
					if (res.code === "0000") {
						this.packageTableData = res.data.records;
						this.packageTotal = res.data.totalCount;
					}
					this.packageTableLoading = false;
					this.packageRecordFlage = true;
				});

				//根据所选月份进行条件查询
			},

			getPurchaseRecordsDetails(page, id) {
				if (page === 0) {
					this.packageDetailsPage = 1;
				}
				this.packageTableDetailsLoading = true;

				getDetailsList({
					orderId: id || this.recordDetailId,
					pageNumber: page,
					pageSize: 10,
				}).then((res) => {
					if (res.code === "0000") {
						this.packageDetailsTableData = res.data.records;
						this.packageDetailsTotal = res.data.totalCount;
					}
					this.packageTableDetailsLoading = false;
					this.packageRecordDetailsFlage = true;
				});

				//根据所选月份进行条件查询
			},

			//订单详情
			showOrderInfo(id) {
				// this.orderId = id;
				this.packageRecordDetailsFlage = true;
				this.recordDetailId = id;
				this.getPurchaseRecordsDetails(0, id);
			},

			selectTime(date) {
				this.searchMonth = date;
			},

			typeSelect(e) {
				this.packageFlag = false;
				if ("1" === e) {
					//套卡加载
				} else if ("2" === e) {
					//套餐加载
				} else {
					this.packageFlag = true;
					this.formObj.dxPackageInfosString = [];
          this.formObj.a2zPackageInfosString = [];
				}
			},
			// 套餐购买记录弹出框关闭
			packagePurchaseCancel() {
				this.searchMonth = "";
			},

			fileDownload(name, extra, data) {
				const content = data;
				const fileName = name + "." + extra; // 导出文件名
				if ("download" in document.createElement("a")) {
					// 支持a标签download的浏览器
					const link = document.createElement("a"); // 创建a标签
					let url = URL.createObjectURL(content);
					link.download = fileName;
					link.href = url;
					link.click(); // 执行下载
					URL.revokeObjectURL(url); // 释放url
				} else {
					// 其他浏览器
					navigator.msSaveBlob(content, fileName);
				}

				this.$Notice.success({
					title: "操作提示",
					desc: "操作成功",
				});
			},

			//套餐记录导出
			exportPackageRecord() {
				downLoadData({
					startTime: this.searchMonth,
					orderUserId: this.corpId,
					pageNumber: -1,
					pageSize: -1,
				}).then((res) => {
					if (res) {
						this.fileDownload("套餐记录", "xlsx", res.data);
					}
				});
			},

			// 导出酬金
			exportRemunerateDetail() {
				exportRemunerate({
					month: this.searchMonth,
					corpId: this.corpId
				}).then(
					(res) => {
						if (res) {
							this.fileDownload("酬金详情", "xlsx", res.data);
						}
					}
				);
			},

			// 导出充值记录
			exportDistributorsRecord() {
				exportDistributorsRecord({
					corpId: this.corpId
				}).then((res) => {
					this.fileDownload("充值记录", "csv", res.data);
				});
			},

			// 查看账单流水
			getBillFlow() {
				this.billFlowFlage = true
			},

			// 获取查看账单时间
			handleDateChange(date) {
				if (Array.isArray(date)) {
					this.form.startTime = date[0];
					this.form.endTime = date[1];
				}
			},

			// 清除查看账单时间
			hanldeDateClear() {
				this.form.startTime = ''
				this.form.endTime = ''
			},

			// 账单流水分页列表加载
			goPageFirst(page) {
				this.billLoading = true
				this.searchloading = true
				var _this = this
				billflowList({
					startTime: this.form.startTime === "" ? null : this.form.startTime,
					endTime: this.form.endTime === "" ? null : this.form.endTime,
					corpId: this.corpId,
					pageNum: page,
					pageSize: 10
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false

						this.page = page
						this.currentPage = page
						this.total = res.data.totalCount
						this.billData = res.data.records
						this.searchloading = false
						this.billLoading = false
					}
				}).catch((err) => {
					console.error(err)
					this.billLoading = false
					this.searchloading = false
				}).finally(() => {})
			},

			// 账单流水 搜索
			searchBill: function() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.goPageFirst(1);
					}
				});
			},

			goPage(page) {
				this.goPageFirst(page)
			},

			// 账单流水 导出
			exportBillFlow: function() {
				var _this = this
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.billExportLoading = true
						billflowData({
							startTime: this.form.startTime === "" ? null : this.form.startTime,
							endTime: this.form.endTime === "" ? null : this.form.endTime,
							corpId: this.corpId,
							userId: this.$store.state.user.userId,
							pageNum: -1,
							pageSize: -1
						}).then((res) => {
							this.exportModal = true
							this.taskId = res.data.taskId
							this.taskName = res.data.taskName
							this.billExportLoading = false
							this.billData = []
							this.form.date = []
							this.total = 0
						}).catch((err) => {
							console.error(err)
							this.billExportLoading = false
						}).finally(() => {})
					}
				})
			},

			// 查看流量明细
			getTrafficDetails() {
				this.trafficDetailsFlage = true
				this.goTrafficPageFirst(1)
			},

			// 获取流量明细时间
			handleDateChange1(date) {
				if (Array.isArray(date)) {
					this.trafficForm.startTime = date[0];
					this.trafficForm.endTime = date[1];
				}
			},

			// 清除流量明细时间
			hanldeDateClear1() {
				this.trafficForm.startTime = ''
				this.trafficForm.endTime = ''
			},

			goTrafficPage(page) {
				this.goTrafficPageFirst(page)
			},

			searchTraffic() {
				this.searchTrafficLoading = true
				this.goTrafficPageFirst(1)
			},

      // 导出流量明细
      exportTraffic() {
        this.exportTrafficLoading = true
        exportTraffic({
          beginDate: this.trafficForm.startTime === "" ? null : this.trafficForm.startTime,
          endDate: this.trafficForm.endTime === "" ? null : this.trafficForm.endTime,
          country: this.trafficForm.localId === "" ? null : this.trafficForm.localId,
          corpId: this.corpId,
        }).then((res) => {
          this.exportModal = true
        	this.taskId = res.data.taskId
        	this.taskName = res.data.taskName
        	this.exportTrafficLoading = false
        	this.trafficForm.startTime = ""
        	this.trafficForm.endTime = ""
          this.trafficForm.localId = ""
        }).catch((err) => {
        	console.error(err)
        	this.exportTrafficLoading = false
        }).finally(() => {})
      },

			// 流量明细分页列表加载
			goTrafficPageFirst(page) {
				this.trafficLoading = true
				var _this = this
				trafficList({
					beginDate: this.trafficForm.startTime === "" ? null : this.trafficForm.startTime,
					endDate: this.trafficForm.endTime === "" ? null : this.trafficForm.endTime,
					country: this.trafficForm.localId === "" ? null : this.trafficForm.localId,
					corpId: this.corpId,
					pageNum: page,
					pageSize: 10
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.page = page
						this.currentPageTraffic = page
						this.totalTraffic = Number(res.count)
						this.trafficData = res.data
						this.searchTrafficLoading = false
						this.trafficLoading = false
					}
				}).catch((err) => {
					console.error(err)
					this.trafficLoading = false
					this.searchTrafficLoading = false
				}).finally(() => {})
			},

			cancelModal() {
				this.exportModal = false
				this.billFlowFlage = false,
				this.trafficDetailsFlage = false,
				this.$refs.form.resetFields()
				this.trafficForm.date = ''
				this.form.date = ''
				this.billData = []
				this.total = 0
				this.trafficForm.startTime = ''
				this.trafficForm.endTime = ''
				this.trafficForm.localId = ''
			},

			Goto() {
				this.trafficDetailsFlage = false; // 立即更新状态
				this.exportModal = false;       // 立即更新状态
				this.billFlowFlage = false;     // 立即更新状态

				this.$router.push({
				  path: '/taskList',
				  query: {
				    taskId: encodeURIComponent(this.taskId),
				    fileName: encodeURIComponent(this.taskName),
				    // corpId: encodeURIComponent(this.corpId)
				  }
				});
			},

			//国家/地区
			getLocalList() {
				opsearch().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.localList = list;
						this.localList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
      // getAllOperatorsList() {
      //   getAllOperators().then(res => {
      //     if (res && res.code == '0000') {
      //       var list = res.data;
      //       this.operator = list;
      //       this.operator.sort(function(str1, str2) {
      //         return str1.operatorName.localeCompare(str2.operatorName);
      //       });
      //     } else {
      //       throw res
      //     }
      //   }).catch((err) => {
      //
      //   }).finally(() => {
      //
      //   })
      // },
			//国家卡池关联组
			nationalCardPoolGroup() {
				getCardPoolGroup({
					num: -1,
					size: -1,
				}).then(res => {
					if (res.code == '0000') {
						this.groupIdList = res.data.records
					}
				}).catch((err) => {
					console.error(err)
				})
			},

			//选择模板查询
			getTemplate() {
				upccList({
					pageNum: -1,
					pageSize: -1,
				}).then(res => {
					if (res.code == '0000') {
						this.TemplatesCreatList = res.data.records
					}
				}).catch((err) => {
					console.error(err)
				})
			},

			//增加 提醒百分比
			handleAdd() {
				this.index++;
				this.formObj.packageUsePercentage.push({
					value: '',
					index: this.index,
				});
			},

			//删除
			handleRemove(index) {
				this.formObj.packageUsePercentage.splice(index, 1);
				this.index--;
			},

			//A2Z停用
			a2zStop() {
				a2zStop({
					corpId: this.formObj.corpId
				}).then(res => {
					if (res.code == '0000') {
						this.formObj.a2zCardUseSwitch = '2'
						//具体描述 参考后端
						if (res.status == '1') {
							this.a2zStopStatus == '停用中'
						} else {
							this.a2zStopStatus == 'A2Z停用'
						}
						this.a2zClick = true
						this.init()
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},

			//A2Z恢复
			a2zRecover() {
				a2zRecover({
					corpId: this.formObj.corpId
				}).then(res => {
					if (res.code == '0000') {
						this.formObj.a2zCardUseSwitch = '1'
						//具体描述 参考后端
						if (res.status == '1') {
							this.a2zRecoverStatus == '恢复中'
						} else {
							this.a2zRecoverStatus == 'A2Z恢复'
						}
						this.a2zClick = true
						this.init()
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},

			//可用定向列表
			getAppidsList: function(page) {
				getAppInfo().then(res => {
					if (res.code == '0000') {
						this.appidsList = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				})
			},

      // 获取短信模板
      gettemplateAll() {
      	gettemplate().then(res => {
      		if (res.code === '0000') {
      			var list = res.data;
      			this.tempList = list;
      			this.tempList.sort(function(str1, str2) {
      				return str1.templateName.localeCompare(str2.templateName);
      			});
      		}
      	}).catch((err) => {
      		console.log(err)
      	})
      },

      // 出账规则列表
      getAcountingPeriod: function() {
      	getAcountingPeriod({
      		current: -1,
      		size: -1,
      	}).then(res => {
      		if (res.code == '0000') {
            this.acountingList = res.data
      		}
      	}).catch((err) => {
      		console.error(err)
      	}).finally(() => {
      	})
      },

      // 流量计费规则
      queryA2ZBillPrice: function() {
      	queryA2ZBillPrice({
      		size: -1,
      		current: -1,
      	}).then(res => {
      		if (res.code == '0000') {
      			this.a2zRuleList = res.data
      		}
      	}).catch((err) => {
      		console.error(err)
      	}).finally(() => {
      	})
      },

      // IMSI规则
      getImsiFeeList() {
      	queryImsiFee({
      		size: -1,
      		current: -1,
      	}).then(res => {
      		if (res.code == '0000') {
      			this.imsiFeeList = res.data
      		}
      	}).catch((err) => {
      		console.error(err)
      	}).finally(() => {
      	})
      },


      // 销售邮箱列表
      getSalesMailList() {
      	getSalesMailList().then(res => {
      		if (res.code == '0000') {
      			this.salesMailList = res.data
      		}
      	}).catch((err) => {
      		console.error(err)
      	}).finally(() => {
      	})
      },
      // 计算字节数
      calculateByteLength(str) {
        let totalBytes = 0;
        for (let i = 0; i < str.length; i++) {
          const char = str.charAt(i);
          // 中文字符（包括中文符号）占用 3 个字节
          if (/[\u4e00-\u9fa5\u3000-\u303f\uff00-\uffef]/.test(char)) {
            totalBytes += 3;
          } else {
            // 英文字符、数字及英文符号占用 1 个字节
            totalBytes += 1;
          }
        }
        return totalBytes;
      },

      // 处理输入事件
      handleInput(value) {
        this.byteCount = this.calculateByteLength(value);
      },

      // 异步校验
      validateByteLength(rule, value, callback) {
        const byteCount = this.calculateByteLength(value);
        if (byteCount > this.maxByteLength) {
          callback(new Error("已超过 220 字节！"));
        } else {
          callback();
        }
      },
			toMarketAccout(type){
				this.$router.push({
					name:"adminMarketingAccount",
					query: {
				    corpId: this.corpId,
				    type: type,
            searchObj: encodeURIComponent(JSON.stringify(this.searchObj))
				  }
				})
			}
    },
		mounted() {
			this.getLocalList()
			this.getTemplate()
			this.nationalCardPoolGroup()
      this.gettemplateAll()
      this.getAcountingPeriod()
      this.queryA2ZBillPrice()
      this.getImsiFeeList()
      this.getSalesMailList()
			if ("add" != this.typeFlag) {
        this.corpId = this.$route.query.id;
				if (this.$route.query.type === "Add") {
					this.typeFlag = "add";
					this.packageFlag = false;
				} else if (this.$route.query.type === "Update") {
					this.init();
					this.typeFlag = "update";
					this.packageFlag = false;
				} else {
					this.init();
					// 保存上一页返回数据
					localStorage.setItem("searchObj", decodeURIComponent(this.$route.query.searchObj))
          this.searchObj = JSON.parse(localStorage.getItem("searchObj")) === null ? '' : JSON.parse(localStorage.getItem("searchObj"))
				}
			}
		},
	};
</script>

<style scoped="scoped">
	.inputSty,
	.ivu-select {
		width: 400px;
	}

	.recordBtnSty {
		width: 200px;
		margin-right: 10px;
	}

	.centerSty {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/deep/.input-text input::-webkit-outer-spin-button,
	/deep/.input-text input::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}
	/deep/.input-text input[type="number"] {
		-moz-appearance: textfield;
	}
</style>
<style>
	.ivu-form-item-required1>.ivu-form-item-label:before {
		display: none;
	}
</style>
