<template>
	<Card style="width: 100%; padiing: 16px">
		<Form ref="searchForm" :model="searchObj" inline @submit.native.prevent>
			<FormItem>
				<Input v-model="searchObj.wholesalerName" placeholder="请输入渠道商名称" clearable style="width: 200px" />
			</FormItem>
			<FormItem>
				<Select v-model="searchObj.purchaseStatus" style="width: 200px" placeholder="请选择渠道商状态"
					:clearable="true">
					<Option v-for="item in purchaseStatusList" :value="item.value" :key="item.value">{{ item.label }}
					</Option>
				</Select>
			</FormItem>

			<FormItem>
				<Button style="margin: 0 2px" type="primary" @click="searchChannel" v-has="'search'">
					<div style="display: flex; align-items: center">
						<Icon type="ios-search" />&nbsp;搜索
					</div>
				</Button>
				<Button style="margin: 0 2px" v-has="'add'" type="info" @click="channelCommon(null, 'Add')">
					<div style="display: flex; align-items: center">
						<Icon type="md-add" />&nbsp;新增
					</div>
				</Button>
				<Button style="margin: 0 2px" type="error" v-has="'batchDelete'" @click="deleteList(null)">
					<div style="display: flex; align-items: center">
						<Icon type="ios-trash" />&nbsp;批量删除
					</div>
				</Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading"
				@on-selection-change="handleRowChange">
				<template slot-scope="{ row, index }" slot="action">
					<Button type="primary" size="small" style="margin-right: 5px"
						@click="channelCommon(row.corpId, 'Info')">详情</Button>
					<Button type="success" size="small" style="margin-right: 5px" disabled v-if="row.checkStatus == 4"
						@click="channelCommon(row.corpId, 'Update')">编辑</Button>
					<Button type="success" size="small" style="margin-right: 5px" v-else
						@click="channelCommon(row.corpId, 'Update')">编辑</Button>
					<Button type="error" size="small" v-has="'delete'" disabled
						v-if="row.checkStatus == 5||row.checkStatus == 4" @click="deleteList(row.corpId)">删除</Button>
					<Button type="error" size="small" v-has="'delete'" v-else
						@click="deleteList(row.corpId)">删除</Button>
				</template>
				<template slot-scope="{ row, index }" slot="approval">
					<Button type="success" size="small" style="margin-right: 5px" v-has="'check'"
						v-if="[1,4,5].includes(+row.checkStatus) " @click="cooperativeApproval(row, 2)">通过</Button>
					<Button type="error" size="small" v-has="'check'" v-if="[1,4,5].includes(+row.checkStatus) "
						@click="cooperativeApproval(row, 3)">不通过</Button>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0" />
		</div>

		<!--  通过渠道商申请弹窗 -->

		<Modal v-model="approvalCrossModal" :footer-hide="true" :mask-closable="false">
			<div class="approvalMoal">
				<p>企业是否可用</p>

				<Button @click="hasCompanyExit(0)">不可用</Button>
				<Button type="primary" style="margin-left: 30px" @click="hasCompanyExit(1)">可用</Button>
			</div>
		</Modal>

		<!-- 押金充值弹出框-->
		<!-- <Modal title="押金充值" v-model="rechargeFlag" :footer-hide="true" :mask-closable="false" width="560px">
			<div style="padding: 0 16px">
				<Form ref="rechargeObj" :model="rechargeObj" :label-width="100" :rules="ruleRechargeValidate">
					<FormItem label="充值金额" prop="amount">
						<Input v-model="rechargeObj.amount" :clearable="true" placeholder="请输入充值金额"
							class="inputSty"></Input>
					</FormItem>
					<FormItem label="充值货币种类" prop="codeType">
						<Select v-model="rechargeObj.codeType" placeholder="请选择充值货币种类" :clearable="true"
							class="inputSty">
							<Option value="156">人民币</Option>
							<Option value="344">港币</Option>
							<Option value="840">美元</Option>
						</Select>
					</FormItem>
				</Form>
				<div style="text-align: center">
					<Button type="primary" @click="rechargeSubmit">充值</Button>
					<Button style="margin-left: 8px" @click="reset('rechargeObj')">重置</Button>
				</div>
			</div>
		</Modal> -->
	</Card>
</template>

<script>
	import {
		getDistributorsList,
		getPurchaseRecords,
		// rechargeDistributors,
		approvalChannel,
		deleteDistributors
	} from "@/api/customer/channelShop.js";
	export default {
		components: {},
		data() {
			// const validatePositiveNum = (rule, value, callback) => {
			// 	var str = /^(([1-9]\d{0,9})|0)(\.\d{0,5})?$/;
			// 	return str.test(value);
			// };
			// const validateIntegerNum = (rule, value, callback) => {
			// 	var str = /^[0-9]\d*$/;
			// 	return str.test(value);
			// };
			return {
				approvalCurrentCropId: "", //审核的行
				searchObj: {
					wholesalerName: "", //渠道商名称
					purchaseStatus: "" //渠道商购买状态
				},
				tableData: [], //列表信息
				selection: [], //多选
				selectionIds: [], //多选ids
				tableLoading: false,
				total: 0,
				pageSize: 10,
				page: 1,
				columns: [{
						type: "selection",
						minWidth: 60,
						align: "center"
					},
					{
						title: "渠道商名称",
						key: "corpName",
						align: "center",
						minWidth: 120,
						tooltip: true
					},

					{
						title: "创建时间",
						key: "createTime",
						align: "center",
						minWidth: 150,
						tooltip: true
					},
					{
						title: "EBS Code",
						key: "ebsCode",
						align: "center",
						minWidth: 120,
						tooltip: true
					},
					{
						title: "总额度",
						key: "totalDeposit",
						align: "center",
						minWidth: 120,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const text = row.channelType == '1' ? row.totalDeposit : row.channelType == '2' ? row
								.deposit : '0.00';
							return h(
								"label", text
							);
						}
					},
					{
						title: "可用额度",
						key: "deposit",
						align: "center",
						minWidth: 120,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
              const text = (parseFloat(row.deposit) + parseFloat(row.creditAmount)).toFixed(2)
							return h(
								"label",
								text
							);
						}
					},
					{
						title: "已用额度",
						key: "usedAmount",
						align: "center",
						minWidth: 100,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							return h(
								"label",
								row.usedAmount
							);
						}
					},
          {
          	title: "代销渠道类型",
          	key: "channelType",
          	align: "center",
          	minWidth: 120,
          	tooltip: true,
          	render: (h, params) => {
          		const row = params.row;
          		const text = row.channelType == '1' ? '押金模式' : row.channelType == '2' ? '预存模式' : '';
          		return h("label", text);
          	}
          },
					{
						title: "A2Z总信用额度",
						key: "a2zTotalDeposit",
						align: "center",
						minWidth: 120,
						tooltip: true,
					},
					{
						title: "已用信用额度",
						key: "a2zUsedDeposit",
						align: "center",
						minWidth: 120,
						tooltip: true,
					},
					{
						title: "A2Z渠道类型",
						key: "a2zChannelType",
						align: "center",
						minWidth: 120,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const text = row.a2zChannelType == '1' ? '押金模式' : row.a2zChannelType == '2' ? '预存模式' : '';
							return h("label", text);
						}
					},
					{
						title: "币种",
						key: "currencyCode",
						align: "center",
						minWidth: 100,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							//156 CNY,840 美元, 344 港币
							const text =
								row.currencyCode == "156" ?
								"人民币" :
								row.currencyCode == "840" ?
								"美元" :
								row.currencyCode == "344" ?
								"港币" :
								"获取失败";
							return h("label", text);
						}
					},
					{
						title: "订购状态",
						key: "isSub",
						align: "center",
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							const color = row.isSub == "1" ? "#00cc66" : "#ff0000";
							const text = row.isSub == "1" ? "允许订购" : "不允许订购";
							return h(
								"label", {
									style: {
										color: color
									}
								},
								text
							);
						}
					},
					{
						title: "操作",
						slot: "action",
						minWidth: 180,
						align: "center"
					},
					{
						title: "审批状态",
						key: "checkStatus",
						align: "center",
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							const keyValue = {
								1: {
									color: "#27A1FF",
									text: "新建待审批"
								},
								2: {
									color: "#00cc66",
									text: "通过"
								},
								3: {
									color: "#ff0000",
									text: "不通过"
								},
								4: {
									color: "#ff0000",
									text: "删除待审批"
								},
								5: {
									color: "#ffa554",
									text: "修改待审批"
								}
							};

							return h(
								"label", {
									style: {
										color: keyValue[row.checkStatus].color
									}
								},
								keyValue[row.checkStatus].text
							);
						}
					},
					{
						title: "审批操作",
						slot: "approval",
						minWidth: 150,
						align: "center"
					}
				],
				typeList: [{
						label: "批发商",
						value: "1"
					},
					{
						label: "酬金渠道商",
						value: "2"
					},
					{
						label: "能力开放渠道商",
						value: "3"
					}
				],
				purchaseStatusList: [{
						label: "允许订购",
						value: "1"
					},
					{
						label: "不允许订购",
						value: "2"
					}
				],
				approvalStatusList: [{
						label: "待审批",
						value: "0"
					},
					{
						label: "已审批",
						value: "1"
					},
					{
						label: "审批未通过",
						value: "-1"
					}
				],
				rechargeFlag: false,
				approvalCrossModal: false,
			};
		},

		mounted() {
      //缓存数据
      let searchObj = JSON.parse(localStorage.getItem("searchObj")) === null ? '' : JSON.parse(localStorage.getItem(
      	"searchObj"))
      if (searchObj) {
      	this.searchObj.wholesalerName = searchObj.wholesalerName === undefined ? "" : searchObj.wholesalerName
      	this.searchObj.purchaseStatus = searchObj.purchaseStatus === undefined ? "" : searchObj.purchaseStatus
      }
      this.init()
      //清除缓存
      localStorage.removeItem("searchObj")
		},

		methods: {
			//表格初始化
			init() {
				this.getDistributorsList(0);
			},

			formatMockData(data) {
				// 下划线转换驼峰
				function toHump(name) {
					return name.replace(/\_(\w)/g, function(all, letter) {
						return letter.toUpperCase();
					});
				}

				const arr = [];

				data.forEach(element => {
					let obj = {};

					Object.keys(element).forEach(key => {
						obj[toHump(key)] = element[key].value;
					});

					arr.push(obj);
				});

				console.log(arr[0]);

				return arr;
			},
			getDistributorsList(e) {
				if (e === 0) {
					this.page = 1;
				}

				getDistributorsList({
					corpName: this.searchObj.wholesalerName,
					isSub: this.searchObj.purchaseStatus,
					pageNumber: this.page,
					pageSize: 10,
					isNeedAuth: true
				}).then(res => {
					if (res.code === "0000") {
						let List = []
						// 循环遍历data
						res.data.records.map((value, index) => {
							if (value.authObj) {
								List.push(value.authObj)
								value.authObj.totalDeposit = value.totalDeposit
								value.authObj.deposit = value.deposit
								value.authObj.usedAmount = value.usedAmount
								value.authObj.a2zUsedDeposit= value.a2zUsedDeposit
							} else {
								List.push(value)
							}
						})
						this.tableData = List;
						this.total = res.data.totalCount;
					}
				});
			},

			//表格数据加载
			loadByPage(e) {
				this.page = e;
				this.getDistributorsList(e);
			},
			//搜索
			searchChannel() {
				this.getDistributorsList(0);

			},

			channelAdd() {
				this.$router.push({
					name: "channel"
				});
			},
			//新增
			//详情
			//编辑
			//复制
			channelCommon(id, type) {
				this.$router.push({
					name: "channel" + type,
					query: {
						id,
						type,
						searchObj: encodeURIComponent(JSON.stringify(this.searchObj))
					},
				});
			},

			//多选
			handleRowChange(selection) {
				this.selection = selection;
				this.selectionIds = [];
				selection.map((value, index) => {
					this.selectionIds.push(value.corpId);
				});
			},
			//批量删除
			deleteList(id) {
				if (id) {
					var ids = [id];
				} else {
					var len = this.selection.length;
					if (len < 1) {
						this.$Message.warning("请至少选择一条记录");
						return;
					}
				}

				this.$Modal.confirm({
					title: "确认删除？",
					onOk: () => {
						deleteDistributors({
							corpIds: id ? ids : this.selectionIds
						}).then(
							res => {
								if (res.code === "0000") {
									this.init();
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功"
									});
									this.selection = [];
									this.selectionIds = [];
								} else {
									this.$Notice.error({
										title: "操作提示",
										desc: "操作失败"
									});
								}
							}
						);
					}
				});
			},

			cooperativeApproval(row, type) {
				this.approvalCurrentCropId = row.corpId;


				approvalChannel({
					corpId: this.approvalCurrentCropId,
					status: type
				}).then(res => {
					if (res.code === "0000") {
						if (row.checkStatus === '4' && type === 3) {
							// this.approvalCrossModal = true;
						}
						this.init();
					}
				});

			},

			// 不通过判断企业
			hasCompanyExit(flag) {
				if (!flag) {
					approvalChannel({
						corpId: this.approvalCurrentCropId,
						status: '3'
					});
				}
				this.approvalCrossModal = false;
			}
		},
	};
</script>

<style>
	.approvalMoal {
		text-align: center;
	}

	.approvalMoal p {
		font-size: 20px;
		margin: 20px 0;
	}
</style>
