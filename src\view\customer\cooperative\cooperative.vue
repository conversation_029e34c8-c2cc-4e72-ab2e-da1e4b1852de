<template>
  <Card style="width: 100%;padiing: 16px;">
    <div>
      <Form ref="searchForm" :model="searchObj" :label-width="100">
        <FormItem label="合作运营商名称">
          <Input v-model='searchObj.corpName' placeholder="请输入合作运营商" readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="合作运营商ID">
          <Input v-model='searchObj.corpId' placeholder="合作运营商ID" readonly style="width: 200px;" />
        </FormItem>
        <FormItem>
          <Button style="margin: 0 2px;" type="success" :loading="exportLoading" @click="exportDetails" v-has="'export'">
            <div style="display: flex;align-items: center;">
              <Icon type="ios-cloud-download-outline" />&nbsp;使用明细导出</div>
          </Button>
        </FormItem>
      </Form>
    </div>
    <div>
      <Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading">
      </Table>
      <Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="loadByPage"
        style="margin: 15px 0;" />
    </div>
  </Card>
</template>

<script>
  import {
    getMorePage,
    exportMore
  } from '@/api/customer/cooperative';
  export default {
    components: {

    },
    data() {
      return {
        searchObj: {
          'id': '',
          'cooperativeName': '', //合作运营商名称
        },
        tableData: [], //列表信息
        selection: [], //多选
        selectionIds: [], //多选ids
        tableLoading: false,
        exportLoading: false,
        total: 0,
        pageSize: 10,
        page: 1,
        columns: [{
            type: 'selection',
            minWidth: 60,
            align: 'center'
          },
          {
            title: 'ICCID',
            key: 'iccid',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '套餐名称',
            key: 'packageName',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '激活时间',
            key: 'activeTime',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '到期时间',
            key: 'expireTime',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '激活国家/地区',
            key: 'mcc',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '金额',
            key: 'amount',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '币种',
            key: 'currencyCode',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          }
        ]
      }
    },
    methods: {
      // 页面加载
      goPageFirst (page) {
        var v = this
        v.tableLoading = true
        let pageSize = 10
        let pageNumber = page
        getMorePage({
            corpId: this.searchObj.corpId,
            pageNumber,
            pageSize
          }).then(res => {
          if (res && res.code == '0000') {
            var data = res.data
            v.tableData = data.record
            v.total = data.total
            v.tableLoading = false
            v.searchLoading = false
          } else {
            throw res
          }
        }).catch((err) => {
          v.tableLoading = false
        })
      },
      //表格数据加载
      loadByPage(e) {
        this.page = e
        this.goPageFirst(e)
      },
      //明细导出
      exportDetails() {
      	this.$Modal.confirm({
      		title: '确认导出？',
      		onOk: () => {
            this.exportLoading = true
      			// let current = this.page
      			// let size = 10
      			let corpId  = this.searchObj.corpId
      			exportMore({
      				// current,
      				// size,
      				corpId
      			}).then(res => {
      				const content =  res.data
              // 获取当前时间
              let date = new Date();
              let y = date.getFullYear();
              let m = date.getMonth() + 1;
              let d = date.getDate();
              // let H = Da.getHours();
              let time = y + "-" + m + "-" + d
              const fileName = time + '.txt' // 导出文件名
              if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
                const link = document.createElement('a') // 创建a标签
                let url = URL.createObjectURL(content)
                link.download = fileName
                link.href = url
                link.click() // 执行下载
                URL.revokeObjectURL(url) // 释放url
              } else { // 其他浏览器
                navigator.msSaveBlob(content, fileName)
              }
              this.exportLoading = false
            }).catch(err => {
              this.exportLoading = false
              this.exporting = false
            })
      		}
      	})
      },
    },
    mounted() {
      // this.tableData = res.data
      var cooperative = JSON.parse(decodeURIComponent(this.$route.query.cooperative));
      this.searchObj = cooperative;
      // this.init();
      //加载列表信息
      this.goPageFirst(1);
    }
  }
</script>

<style>
  .inputSty {
    width: 200px;
  }
</style>
