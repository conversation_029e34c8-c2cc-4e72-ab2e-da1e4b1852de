<template>
  <Card style="width: 100%;padiing: 16px;">
    <Form ref="searchForm" :model="searchObj" inline>
      <FormItem>
        <span class="input_notice">合作运营商：</span>&nbsp;&nbsp;
        <Input v-model='searchObj.cooperativeName' placeholder="请输入合作运营商名称" clearable style="width: 200px;" />
      </FormItem>
      <FormItem>
        <Button style="margin: 0 2px;" type="primary" :loading="searchLoading" @click="searchCooperative">
          搜索
        </Button>
        <Button style="margin: 0 2px" type="info" v-has="'add'"  @click="cooperativeAdd">
          <div style="display: flex;align-items: center;">
            <Icon type="md-add" />&nbsp;新增</div>
        </Button>
        <Button style="margin: 0 2px" type="error" v-has="'batchDelete'" :loading="batchDeleteLoading" @click="deleteList">
            <Icon type="ios-trash" />&nbsp;批量删除
        </Button>
      </FormItem>
    </Form>
    <div>
      <Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading"
        @on-selection-change="handleRowChange">
        <template slot-scope="{ row, index }" slot="packages">
          <a type="primary" size="small" @click="loadTariffPackageList(row)">资费详情</a>
        </template>
        <template slot-scope="{ row, index }" slot="action">
          <Button type="primary" size="small" style="margin-right: 5px" v-has="'view'" @click="cooperativeCommon(row,'Info')">详情</Button>
          <Button type="success" size="small" style="margin-right: 5px" v-has="'update'" @click="cooperativeCommon(row,'Update')">编辑</Button>
          <Button type="error" size="small" v-has="'delete'" :loading="row.delLoading" @click="cooperativeDel(row)">删除</Button>
        </template>
        <template slot-scope="{ row, index }" slot="approval">
          <Button v-if="row.checkStatus === '1' ||row.checkStatus === '4'" type="success" size="small" style="margin-right: 5px" v-has="'check'" :loading="row.checkPassLoading" @click="cooperativeApproval(row,'Pass')">通过</Button>
          <Button v-if="row.checkStatus === '1' ||row.checkStatus === '4'" type="error" size="small" v-has="'check'" :loading="row.checkUnPassLoading" @click="cooperativeApproval(row,'Fail')">不通过</Button>
        </template>
      </Table>
      <Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="loadByPage"
        style="margin: 15px 0;" />
    </div>
    <!-- 合作运营商新增/编辑 -->
    <Modal :title="title" v-model="cooperativeEditFlag" :footer-hide="true" :mask-closable="true" width="1000px" @on-cancel="close">
      <div style="padding: 0 16px;">
        <Form ref="editObj" :model="editObj" :label-width="180" :rules="ruleEditValidate">
          <Row>
            <Col span="12">
            <FormItem label="合作运营商名称" prop="corpName">
              <Input v-model="editObj.corpName" :clearable="true" placeholder="请输入合作运营商名称" class="inputSty"></Input>
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem label="App Key" prop="appkey">
              <Input v-model="editObj.appkey" :clearable="true" placeholder="请输入App Key" class="inputSty"></Input>
            </FormItem>
            </Col>
          </Row>
		  <Row>
		    <Col span="12">
		    <FormItem label="公司名称" prop="companyName">
		      <Input v-model="editObj.companyName" :clearable="true" placeholder="请输入公司名称" class="inputSty"></Input>
		    </FormItem>
		    </Col>
		    <Col span="12">
		    <FormItem label="地址" prop="address">
		      <Input v-model="editObj.address" :clearable="true" placeholder="请输入地址" class="inputSty"></Input>
		    </FormItem>
		    </Col>
		  </Row>
          <Row>
            <Col span="12">
            <FormItem label="Username" prop="username">
              <Input v-model="editObj.username" :clearable="true" placeholder="请输入Username" class="inputSty"></Input>
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem label="URL" prop="url">
              <Input v-model="editObj.url" :clearable="true" placeholder="请输入URL" class="inputSty"></Input>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem label="内部订单" prop="internalOrder">
              <Select filterable v-model="editObj.internalOrder" placeholder="请选择是否内部订单" :clearable="true" class="inputSty">
                <Option value="0">是</Option>
                <Option value="1">否</Option>
              </Select></Input>
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem label="币种" prop="currencyCode">
              <Select filterable v-model="editObj.currencyCode" placeholder="请选择币种" :clearable="true" class="inputSty">
                <Option value="156">人民币</Option>
                <Option value="344">港币</Option>
                <Option value="840">美元</Option>
              </Select>
            </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="12">
            <FormItem label="EBS Code" prop="ebsCode">
              <Input v-model="editObj.ebsCode" :clearable="true" placeholder="请输入EBS Code" class="inputSty"></Input>
            </FormItem>
            </Col>
            <!-- <Col span="12">
            <FormItem label="公司名称" prop="companyName">
              <Input v-model="editObj.companyName" :clearable="true" placeholder="请输入公司名称" class="inputSty"></Input>
            </FormItem>
            </Col> -->
          </Row>
          <!-- <Row>
            <Col span="12">
            <FormItem label="地址" prop="address">
              <Input v-model="editObj.address" :clearable="true" placeholder="请输入地址" class="inputSty"></Input>
            </FormItem>
            </Col>
          </Row> -->
          <Row>
            <Col span="12">
            <FormItem label="扣费模式" prop="billType">
              <Select filterable v-model="editObj.billType" placeholder="请选择扣费模式" :clearable="true" class="inputSty" @on-change="changeBillType">
                <Option v-for="item in tariffTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>
            </Col>
            <Col span="12">
            <FormItem label="结算方式" prop="settleType">
              <Select filterable v-model="editObj.settleType" placeholder="请选择结算方式" :clearable="true" class="inputSty" disabled>
                <Option v-for="item in methodList" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </FormItem>
            </Col>
          </Row>
          <div v-if="editObj.settleType==='1'">
            <Row v-for="(obj,index) in editObj.packages" :key="index">
              <Col span="12" >
              <FormItem label="资费编码"
              :prop="'packages.' + index + '.billContent'" :rules="[
                { required: true, message: '请输入资费编码',trigger: 'change' },
                { max: 50,message:'最长50位'}
                ]"
              >
                <Input v-model="obj.billContent" :clearable="true" placeholder="请输入资费编码" class="inputSty"></Input>
              </FormItem>
              </Col>
              <Col span="12">
              <FormItem label="套餐价格"  style="position: relative;"
              :prop="'packages.' + index + '.price'" :rules="[
                {required: true, message: '套餐价格不能为空',trigger: 'blur'},
                {pattern: /^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger: 'blur', message: '请输入1-12位数字，整数首位非0（可精确到小数点后2位）' },
                ]"
              >
                <Input v-model="obj.price" :clearable="true" placeholder="请输入套餐价格" class="inputSty">
                  <span slot="append">元</span>
                </Input>
                <div @click="delPackageBtn(index)" style="position: absolute;top: 0;right: 20px;">
                  <Tooltip content="删除该项套餐" placement="right">
                    <Icon type="md-trash" size="22" color="#ff3300" />
                  </Tooltip>
                </div>
              </FormItem>
              </Col>
              <Col span="24">
              <FormItem label="套餐名称"
              :prop="'packages.' + index+ '.packageId'"
              :rules="[{type: 'array',required: true, message: '请选择套餐', trigger: 'change' }]"
              >
                <Select filterable v-model="obj.packageId" placeholder="请选择套餐" :clearable="true" class="inputSty" multiple>
                  <Option v-for="(item,index) in packageList" :value="item.id" :key="index">{{ item.nameCn }}</Option>
                </Select>
              </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="12">
              <FormItem label="套餐" prop="packages">
                <Button type="dashed" class="inputSty" long @click="addPackage" icon="md-add">添加套餐</Button>
              </FormItem>
              </Col>
            </Row>
          </div>
          <div v-if="editObj.settleType==='2'">
            <Row v-for="(obj,index) in editObj.direction" :key="index">
              <Col span="12">
              <FormItem label="流量方向"
              :prop="'direction.' + index+ '.mcc'"
              :rules="[{type: 'array',required: true, message: '请选择流量方向', trigger: 'change' }]"
              >
                <Select filterable v-model="obj.mcc"  placeholder="请选择流量方向" :clearable="true" class="inputSty" multiple>
                  <Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
                </Select>
              </FormItem>
              </Col>
              <Col span="12">
              <FormItem label="流量单价" style="position: relative;"
              :prop="'direction.' + index+ '.price'"
              :rules="[{required: true, message: '请输入流量单价',trigger: 'blur' },
              {pattern: /^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger: 'blur', message: '请输入1-12位数字，整数首位非0（可精确到小数点后2位）' },
              ]"
              >
                <Input v-model="obj.price" :clearable="true" placeholder="请输入流量单价" class="inputSty" >
                  <span slot="append">元/G</span>
                </Input>
                <div @click="delDirectionBtn(index)" style="position: absolute;top: 0;right: 20px;">
                  <Tooltip content="删除该项方向" placement="right">
                    <Icon type="md-trash" size="22" color="#ff3300" />
                  </Tooltip>
                </div>
              </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="24">
              <FormItem label="方向" prop="direction">
                <Button type="dashed" class="inputSty" long @click="addDirection" icon="md-add">添加方向</Button>
              </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
        <div style="text-align: center;">
          <Button type="primary" @click="add('editObj')" v-if="typeFlag == 'Add'" :loading="addLoading" v-has="'add'">提交</Button>
          <Button type="primary" @click="update('editObj')" v-if="typeFlag == 'Update'" :loading="addLoading" v-has="'update'">提交</Button>
          <Button style="margin-left: 8px" @click="reset('editObj')">重置</Button>
        </div>
      </div>
    </Modal>
    <!-- 套餐列表弹出框-->
    <Modal title="资费套餐" v-model="tariffPackageFlag" :footer-hide="true" :mask-closable="false" width="700px">
      <div style="padding: 0 16px;height: 350px;overflow-y:auto;">
        <Form ref="tariffPackageList" :model="tariffPackageList" :label-width="80" >
          <div v-if="tariffPackageList.settleType==='1'">
            <Row v-for="(obj,index) in tariffPackageList.packages" :key="index">
              <Col span="12">
              <FormItem label="套餐名称"
              :prop="'packages.' + index+ '.packageId'"
              >
                <Select v-model="obj.packageId" placeholder="请选择套餐" class="inputSty" multiple disabled>
                  <Option v-for="(item,index) in packageList" :value="item.id" :key="index">{{ item.nameCn }}</Option>
                </Select>
              </FormItem>
              </Col>
              <Col span="12">
              <FormItem label="套餐价格"  style="position: relative;"
              :prop="'packages.' + index + '.price'"
              >
                <Input v-model="obj.price" placeholder="请输入套餐价格" class="inputSty" readonly>
                  <span slot="append">元</span>
                </Input>
              </FormItem>
              </Col>
              <Col span="12" >
              <FormItem label="资费编码"
              :prop="'packages.' + index + '.billContent'"
              >
                <Input v-model="obj.billContent" placeholder="请输入资费编码" class="inputSty" readonly></Input>
              </FormItem>
              </Col>
            </Row>
          </div>
          <div v-if="tariffPackageList.settleType==='2'">
            <Row v-for="(obj,index) in tariffPackageList.direction" :key="index">
              <Col span="12">
              <FormItem label="流量方向"
              :prop="'direction.' + index+ '.mcc'"
              >
                <Select v-model="obj.mcc"  placeholder="请选择流量方向" class="inputSty" multiple disabled>
                  <Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryCn }}</Option>
                </Select>
              </FormItem>
              </Col>
              <Col span="12">
              <FormItem label="流量单价" style="position: relative;"
              :prop="'direction.' + index+ '.price'"
              >
                <Input v-model="obj.price" readonly placeholder="请输入流量单价" class="inputSty">
                  <span slot="append">元/G</span>
                </Input>
              </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    </Modal>
    <Modal
      v-model="modal2"
      title="删除审核不通过"
      :mask-closable="false"
      @on-cancel="cancel2"
      >
        <h3>确认企业是否还可用？</h3>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
        <Button type="primary" @click="checkAvailable('true')">可用</Button>
        <Button @click="checkAvailable('fail')">不可用</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
  import {
    getPage,
    add,
    update,
    check,
    del,
    getPackageList,
    getDetail,
    getCountryList
  } from '@/api/customer/cooperative';
  import {
  	uuid
  } from 'vue-uuid';
  const math = require('mathjs')
  export default {
    components: {
    },
    data() {
      return {
        title: '合作运营商新增',
        uuid: '',
        continentList: [],
        searchObj: {
          'cooperativeName': '', //合作运营商名称
        },
        cooperativeEditFlag: false,
        editObj: {
          'corpName': '', //合作运营商名称
          'appkey': '',
          'username': '',
          'url': '', // URL
          'currencyCode': '',
          'internalOrder': '',
          'address': '',
          'companyName': '',
          'ebsCode': '', //EBSCode
          'billType': '', //扣费类型
          'settleType': '', //结算方式
          'packages': [], //套餐
          'direction': [] ,//方向
        },
        ruleEditValidate: {
          corpName: [
            { required: true, message: '请输入合作运营商名称',trigger: 'blur,change'},
            { max: 50,message:'最长50位'}
          ],
          appkey: [
            { required: true, message: '请输入AppKey',trigger: 'blur,change' },
            { max: 255,message:'最长255位'}
          ],
		  companyName: [{
		    required: true,
		    type: 'string',
		    message: '公司名称不能为空',
		    trigger: 'blur'
		  },
		  { max: 50,message:'最长50位'}
		  ],
		  address: [{
		    required: true,
		    type: 'string',
		    message: '地址不能为空',
		    trigger: 'blur'
		  },
		  { max: 200,message:'最长200位'}
		  ],
          username: [
            { required: true, message: '请输入username',trigger: 'blur,change' },
            { max: 255,message:'最长255位'}
          ],
          url: [
            { required: true, message: '请输入URL',trigger: 'blur,change' },
            { max: 255,message:'最长255位'}
          ],
          internalOrder: [
            { required: true, type: 'string', message: '请选择是否为内部订单', trigger: 'change' }
          ],
          currencyCode: [{ required: true, type: 'string', message: '币种不能为空', trigger: 'change' },
          ],
          ebsCode: [
            { required: true, message: '请输入EBSCode',trigger: 'blur,change' },
            { max: 50,message:'最长50位'}
          ],
          billType: [
            { required: true, message: '请选择扣费类型'},
          ],
          settleType: [
            { required: true, message: '请选择结算方式',trigger: 'blur,change' }
          ],
          packages: [
            { type: 'array',required: true, message: '请添加套餐',trigger: 'change' }
          ],
          direction: [
            { type: 'array',required: true, message: '请添加方向',trigger: 'change' }
          ]
        },
        tariffTypeList: [{
          label: '按编码扣费',
          value: '1'
        }, {
          label: '按地区扣费',
          value: '2'
        }],
        methodList: [{
          label: '二次定价',
          value: '1'
        }, {
          label: '方向流量',
          value: '2'
        }],
        packageList: [],
        tableData: [], //列表信息
        selection: [], //多选
        selectionIds: [], //多选ids
        total: 0,
        pageSize: 10,
        page: 1,
        columns: [{
            type: 'selection',
            minWidth: 60,
            align: 'center'
          },
          {
            title: '合作运营商名称',
            key: 'corpName',
            align: 'center',
            minWidth: 120,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: 'APP Key',
            key: 'appkey',
            align: 'center',
            minWidth: 120,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: 'Username',
            key: 'username',
            align: 'center',
            minWidth: 120,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: 'EBS Code',
            key: 'ebsCode',
            align: 'center',
            minWidth: 120,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: 'URL',
            key: 'url',
            align: 'center',
            minWidth: 120,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '扣费模式',
            key: 'billType',
            align: 'center',
            minWidth: 120,
            tooltip: true,
            render: (h, params) => {
              const row = params.row;
              const text = row.billType == '1' ? '按照编码扣费' : row.billType == '2' ?  '按照地区扣费' : '';
              return h('label', text);
            }
          },
          {
            title: '结算方式',
            key: 'settleType',
            align: 'center',
            minWidth: 120,
            tooltip: true,
            render: (h, params) => {
              const row = params.row;
              const text = row.settleType == '1' ? '二次定价' : row.settleType == '2' ?  '方向流量' : '';
              return h('label', text);
            }
          },
          {
            title: '资费详情',
            slot: 'packages',
            minWidth: 100,
            align: 'center'
          },
          {
            title: '操作',
            slot: 'action',
            minWidth: 200,
            align: 'center'
          },
          {
            title: '审批状态',
            key: 'checkStatus',
            align: 'center',
            minWidth: 120,
            render: (h, params) => {
              const row = params.row;
              const color = row.checkStatus == '1' ? '#2d8cf0' : row.checkStatus == '2' ? '#00cc66' : row.checkStatus == '3' ? '#ff0000':'#ed4014';
                '#27A1FF';
              const text = row.checkStatus == '1' ? '新建待审批' : row.checkStatus == '2' ? '通过' : row.checkStatus == '3' ? '不通过': row.checkStatus == '4' ? '删除待审批':'';
              return h('label', {
                style: {
                  color: color
                }
              }, text);
            },
          },
          {
            title: '审批操作',
            slot: 'approval',
            minWidth: 160,
            align: 'center'
          }
        ],
        typeFlag: 'Add',
        tariffPackageFlag: false,
        tariffPackageList: {}, //展示资费套餐
        tableLoading: false,
        searchLoading: false,
        addLoading: false,
        batchDeleteLoading: false,
        modal2: false,
        checkItem: {}
      }
    },
    methods: {
      getLocalList(){
        getCountryList().then(res => {
          if (res && res.code == '0000') {
            var list = res.data;
            this.continentList = list;
            this.continentList.sort(function(str1, str2) {
              return str1.countryEn.localeCompare(str2.countryEn);
            });
          } else {
            throw res
          }
        }).catch((err) => {
        })
      },
      getPackageList(){
        getPackageList({
          isTerminal: 2,
          page: 1,
          pageSize: 9999,
		  isNeedAuth: true,
        }).then(res => {
          var list = res.data.data;
          this.packageList = list;
          this.packageList.sort(function(str1, str2) {
            return str1.nameCn.localeCompare(str2.nameCn);
          });
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
      // 页面加载
      goPageFirst (page) {
        this.selection = [];
        this.selectionIds = [];
        var v = this
        v.tableLoading = true
        let pageSize = 10
        let pageNumber = page
        getPage({
          corpName: v.searchObj.cooperativeName,
          pageNumber,
          pageSize
        }).then(res => {
          if (res && res.code == '0000') {
            var data = res.data.record
            var item = []
            data.forEach(e =>{
              var newitem = {};
              var channel = e.channel
              var eopAccessDetail = e.eopAccessDetail
              var billRuleDetail = e.billRuleDetail
              newitem.billType = channel == null ? '' : channel.billType
              newitem.ebsCode = channel == null ? '' : channel.ebsCode
              newitem.corpName = channel == null ? '' : channel.corpName
              newitem.appkey = eopAccessDetail == null ? '' : eopAccessDetail.appKey
              newitem.username = eopAccessDetail == null ? '' : eopAccessDetail.userName
              newitem.url = eopAccessDetail == null ? '' : eopAccessDetail.notifyUrl
              newitem.corpId = channel == null ? '' : channel.corpId
              newitem.corpName = channel == null ? '' : channel.corpName
              newitem.checkStatus = channel == null ? '' : channel.checkStatus
              newitem.settleType = channel == null ? '' : channel.settleType
              newitem.billRule = channel == null ? '' : channel.billRule
              newitem.settleRule = channel == null ? '' : channel.settleRule
              newitem.currencyCode = channel == null ? '' : channel.currencyCode
              newitem.address = channel == null ? '' : channel.address
              newitem.companyName = channel == null ? '' : channel.companyName
              newitem.internalOrder = channel == null ? '' : channel.internalOrder
              // var packages = []
              // var direction = []
              // var settleRuleDetail = e.settleRuleDetail
              // if(settleRuleDetail !== null){
              //   settleRuleDetail.forEach(e =>{
              //     var p = {}
              //     var d = {}
              //     if(newitem.settleType === '1'){
              //       p.packageId = e.packageId
              //       p.price = e.price.toString()
              //       packages.push(p)
              //     }
              //     if(newitem.settleType === '2'){
              //       d.mcc = e.mcc
              //       d.price = e.price.toString()
              //       direction.push(d)
              //     }
              //   });
              // }
              // newitem.packages = packages
              // newitem.direction = direction
              item.push(newitem)
            });
            v.tableData = item
            v.total = res.data.total
            v.tableLoading = false
            v.searchLoading = false
            if (this.tableData.length) {
              this.tableData.map(item => {
                this.$set(item, 'delLoading', false)
                this.$set(item, 'checkPassLoading', false)
                this.$set(item, 'checkUnPassLoading', false)
                return item
              })
            }
          } else {
            throw res
          }
        }).catch((err) => {
          v.tableLoading = false
          v.searchLoading = false
          if (this.tableData.length) {
            this.tableData.map(item => {
              this.$set(item, 'delLoading', false)
              this.$set(item, 'checkPassLoading', false)
              this.$set(item, 'checkUnPassLoading', false)
              return item
            })
          }
        })
        // v.init()
        // setTimeout(function() {
        // 	v.tableLoading = false
        // 	v.searchLoading = false
        // }, 1000);
      },
      //提交
      add(name) {
        this.$refs[name].validate((valid) => {
          if (valid) {
            this.addLoading = true
            //封装运营商基本信息
            let channel = {
              corpName: this.editObj.corpName,
              ebsCode: this.editObj.ebsCode,
              billType: this.editObj.billType,
              settleType: this.editObj.settleType,
              currencyCode: this.editObj.currencyCode,
              // address: this.editObj.address,
              // companyName: this.editObj.companyName,
              internalOrder: this.editObj.internalOrder,
              corpId: uuid.v1().substr(0, 32),
              status: '1',
			  address: this.editObj.address,
			  companyName: this.editObj.companyName,
            }
			
            //封装EOP实体
            let eopAccessDetail = {
              appKey: this.editObj.appkey,
              userName: this.editObj.username,
              notifyUrl: this.editObj.url,
            }
            //封装计费信息
            let billRuleDetail = []
            //封装套餐集合
            let packages = this.editObj.packages
            if(this.editObj.settleType === '1' && packages != null && packages.length > 0 && packages[0].packageId != ''){
              packages.forEach(e=>{
                var billRuleDetailDTO = {}
                var ids = e.packageId
                var ps = []
                ids.forEach(i => {
                  var item = {}
                  item.packageId = i
                  item.packageName = this.packageList.find(p => {
                    return p.id === i;
                  }).nameCn;
                  ps.push(item)
                })
                billRuleDetailDTO.packages = ps;
                billRuleDetailDTO.price = math.multiply(math.bignumber(e.price),100).toString();
                billRuleDetailDTO.billContent = e.billContent;
                billRuleDetail.push(billRuleDetailDTO)
              });
            }
            //封装结算规则详情实体
            let settleRuleDetails = []
            //封装国家集合
            var country = this.editObj.direction
            if(this.editObj.settleType === '2' && country != null && country.length > 0 && country[0].mcc != ''){
              country.forEach(e=>{
                var SettleRuleDetailDTO = {}
                SettleRuleDetailDTO.mcc = e.mcc;
                SettleRuleDetailDTO.price = math.multiply(math.bignumber(e.price),100).toString();
                settleRuleDetails.push(SettleRuleDetailDTO)
              });
            }
            add({
                channel,
                eopAccessDetail,
                settleRuleDetails,
                billRuleDetail
              }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.cooperativeEditFlag = false
                this.addLoading = false
                this.reset(name)
                this.page = 1
                this.goPageFirst(1)
              } else {
                throw res
              }
            }).catch((err) => {
              this.addLoading = false
            })
          }
        })
      },
      //编辑
      update(name) {
        this.$refs[name].validate((valid) => {
          if (valid) {
            this.addLoading = true
            //封装运营商基本信息
            let channel = {
              corpId: this.editObj.corpId,
              corpName: this.editObj.corpName,
              ebsCode: this.editObj.ebsCode,
              billType: this.editObj.billType,
              settleType: this.editObj.settleType,
              billRule: this.editObj.billRule,
              settleRule: this.editObj.settleRule,
              currencyCode: this.editObj.currencyCode,
              address: this.editObj.address,
              companyName: this.editObj.companyName,
              internalOrder: this.editObj.internalOrder,
			  address: this.editObj.address,
			  companyName: this.editObj.companyName,
            }
            //封装EOP实体
            let eopAccessDetail = {
              appKey: this.editObj.appkey,
              userName: this.editObj.username,
              notifyUrl: this.editObj.url,
            }
            //封装计费信息
            let billRuleDetail = []
            //封装套餐集合
            let packages = this.editObj.packages
            if(this.editObj.settleType === '1' && packages != null && packages.length > 0 && packages[0].packageId != ''){
              packages.forEach(e=>{
                var billRuleDetailDTO = {}
                var ids = e.packageId
                var ps = []
                ids.forEach(i => {
                  var item = {}
                  item.packageId = i
                  item.packageName = this.packageList.find(p => {
                    return p.id === i;
                  }).nameCn;
                  ps.push(item)
                })
                billRuleDetailDTO.packages = ps;
                billRuleDetailDTO.price = math.multiply(math.bignumber(e.price),100).toString();
								// billRuleDetailDTO.price = e.price.toString();
                billRuleDetailDTO.billContent = e.billContent;
                billRuleDetail.push(billRuleDetailDTO)
              });
            }
            //封装结算规则详情实体
            let settleRuleDetails = []
            //封装国家集合
            var country = this.editObj.direction
            if(this.editObj.settleType === '2' && country != null && country.length > 0 && country[0].mcc != ''){
              country.forEach(e=>{
                var SettleRuleDetailDTO = {}
                SettleRuleDetailDTO.mcc = e.mcc;
                SettleRuleDetailDTO.price = math.multiply(math.bignumber(e.price),100).toString();
				// SettleRuleDetailDTO.price = e.price.toString();
                settleRuleDetails.push(SettleRuleDetailDTO)
              });
            }
            update({
                channel,
                eopAccessDetail,
                settleRuleDetails,
                billRuleDetail
              }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.cooperativeEditFlag = false
                this.addLoading = false
                this.reset(name)
                this.page = 1
                this.goPageFirst(1)
              } else {
                throw res
              }
            }).catch((err) => {
              this.addLoading = false
            })
          }
        })
      },
      changeBillType(){
        if (this.editObj.billType === '1'){
          // this.$refs['editObj'].resetFields("packages");
          this.editObj.settleType = '1'
        }else if (this.editObj.billType === '2'){
          // this.$refs['editObj'].resetFields("direction");
          this.editObj.settleType = '2'
        }else{
          this.editObj.settleType = ''
        }
      },
      reset(name) {
        this.editObj = {
          'corpId':this.editObj.corpId,
          'billRule':this.editObj.billRule,
          'settleRule':this.editObj.settleRule,
          'corpName': '', //合作运营商名称
          'AppKey': '',
          'username': '',
          'url': '', // URL
          'ebsCode': '', //EBSCode
          'billType': '', //扣费类型
          'settleType': '', //结算方式
          'packages': [{
            "packageId": [],
            "price": '',
            'billContent': '', //资费编码
          }], //套餐
          'direction': [{
            "mcc": [],
            "price": '',
          }], //方向
          'currencyCode': '',
          'address': '',
          'companyName': '',
          'internalOrder': '',
        };
        this.$refs['editObj'].resetFields();
      },
      close() {
        this.editObj = {
          'corpName': '', //合作运营商名称
          'AppKey': '',
          'username': '',
          'url': '', // URL
          'ebsCode': '', //EBSCode
          'billType': '', //扣费类型
          'settleType': '', //结算方式
          'packages': [{
            "packageId": [],
            "price": '',
            'billContent': '', //资费编码
          }], //套餐
          'direction': [{
            "mcc": [],
            "price": '',
          }], //方向
          'currencyCode': '',
          'address': '',
          'companyName': '',
          'internalOrder': '',
        };
        this.$refs['editObj'].resetFields();
      },
      //查看套餐信息
      loadTariffPackageList(row) {
        this.tariffPackageFlag = true;
        this.getDetail(row,'detail')
        // this.tariffPackageList = {
        //   'settleType': row.settleType, //结算方式
        //   'packages': row.packages, //套餐
        //   'direction': row.direction ,//方向
        // };
      },
      getDetail(row,type){
        var v = this
        getDetail({
            corpId : row.corpId
          }).then(res => {
          if (res && res.code == '0000') {
            var e = res.data
            var item = []
            var newitem = {};
            var packages = []
            var direction = []
            var settleRuleDetail = e.settleRuleDetails
            var billRuleDetail = e.billRuleDetail
            if(row.settleType === '1' && billRuleDetail !== null){
              billRuleDetail.forEach(e =>{
                var p = {}
                p.packageId = []
                e.packages.forEach(ps => {
                  p.packageId.push(ps.packageId)
                })
                p.price = e.price === null ? '':e.price.toString();
                p.billContent = e.billContent
                packages.push(p)
              });
            }
            if(row.settleType === '2' && settleRuleDetail !== null){
              settleRuleDetail.forEach(e =>{
                var d = {}
                d.mcc = e.mcc
                d.price = e.price === null ? '':e.price.toString();
                direction.push(d)
              });
            }
            newitem.packages = packages
            newitem.direction = direction
            item.push(newitem)
            if(type === 'detail'){
              v.tariffPackageList = {
                'settleType': row.settleType, //结算方式
                'packages': packages, //套餐
                'direction': direction ,//方向
              };
            }
            if(type === 'Update'){
              v.editObj = {
                'corpName': row.corpName, //合作运营商名称
                'appkey': row.appkey,
                'username': row.username,
                'url': row.url, // URL
                'ebsCode': row.ebsCode, //EBSCode
                'billType': row.billType, //扣费类型
                'settleType': row.settleType, //结算方式
                // 'billContent': row.billContent, //资费编码
                'packages': packages, //套餐
                'direction': direction ,//方向
                'corpId': row.corpId,
                'billId': row.billId,
                'settleRule': row.settleRule,
                'billRule': row.billRule,
                'currencyCode': row.currencyCode,
                'address': row.address,
                'companyName': row.companyName,
                'internalOrder': row.internalOrder,
              };
            }
          } else {
            throw res
          }
        }).catch((err) => {
        })
      },
      //表格数据加载
      loadByPage(e) {
        this.page = e
        this.goPageFirst(e)
      },
      //搜索
      searchCooperative() {
        this.searchLoading = true
        this.page = 1
        this.goPageFirst(1)
      },
      //新增
      cooperativeAdd() {
        this.reset()
        this.typeFlag = 'Add';
        this.cooperativeEditFlag = true;
      },
      addPackage() {
        this.editObj.packages.push({
          "packageId": [],
          "price": '',
          'billContent': '', //资费编码
        })
      },
      addDirection() {
        this.editObj.direction.push({
          "mcc": [],
          "price": '',
        })
      },
      delPackageBtn(index) {
        this.editObj.packages.splice(index, 1);
      },
      delDirectionBtn(index) {
        this.editObj.direction.splice(index, 1);
      },
      //详情
      //编辑
      //复制
      cooperativeCommon(row, type) {
        if (type === 'Info') {
          this.typeFlag = type;
          this.$router.push({
            name: 'cooperativeInfo',
            query: {
              cooperative: encodeURIComponent(JSON.stringify(row))
            }
          })
        }
        if (type === 'Update') {
          this.title = '合作运营商编辑';
          this.typeFlag = type;
          this.cooperativeEditFlag = true;
          this.getDetail(row,'Update')
        }
      },
      //审核
      cooperativeApproval(row, type) {
        var item = {}
        item.corpId = row.corpId
        item.checkStatus = row.checkStatus
        if (type === 'Pass') {
          this.$Modal.confirm({
            title: '确认通过？',
            onOk: () => {
              item.approvalStatus = '2'
              row.checkPassLoading = true
              this.check(item)
            }
          })
        }
        if (item.checkStatus === '4' && type === 'Fail') {
          this.checkItem = row
          row.checkUnPassLoading = true
          this.modal2 = true
        }
        if(item.checkStatus !== '4' && type === 'Fail') {
          this.$Modal.confirm({
            title: '确认不通过？',
            onOk: () => {
              item.approvalStatus = '3'
              row.checkUnPassLoading = true
              this.check(item)
            }
          })
        }
      },
      checkAvailable(type){
        var data = {}
        data.corpId = this.checkItem.corpId
        data.checkStatus = this.checkItem.checkStatus
        if(type === 'true'){
          data.approvalStatus = '3'
          data.available = true
          this.check(data)
        }
        if(type === 'fail'){
          data.approvalStatus = '3'
          data.available = false
          this.check(data)
        }
        this.modal2 = false
      },
      check(i){
        //修改审核状态
        check({
            originCheackStatus: i.checkStatus,
            toCheackStatus: i.approvalStatus,
            corpId: i.corpId,
            available: i.available
          }).then(res => {
          if (res && res.code == '0000') {
            this.$Notice.success({
              title: '操作提示',
              desc: '操作成功'
            })
            this.page = 1
            this.goPageFirst(1)
          } else {
            throw res
          }
        }).catch((err) => {
          this.$Notice.error({
            title: '操作提示',
            desc: '操作失败'
          })
        })
      },
      cancel2(){
        this.modal2 = false
        this.checkItem = {}
        this.page = 1
        this.goPageFirst(1)
      },
      //删除
      cooperativeDel(item) {
        this.$Modal.confirm({
          title: '确认删除？',
          onOk: () => {
            item.delLoading = true
            var ids = []
            ids.push(item.corpId)
            del(ids).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.page = 1
                this.goPageFirst(1)
              } else {
                throw res
              }
            }).catch((err) => {
              this.$Notice.error({
                title: '操作提示',
                desc: '操作失败'
              })
            })
          }
        });
      },
      //多选
      handleRowChange(selection) {
        this.selection = selection;
        this.selectionIds = [];
        selection.map((value, index) => {
          this.selectionIds.push(value.corpId);
        });
      },
      //批量删除
      deleteList() {
        var len = this.selection.length;
        if (len < 1) {
          this.$Message.warning('请至少选择一条记录')
          return
        }
        this.$Modal.confirm({
          title: '确认删除？',
          onOk: () => {
            this.batchDeleteLoading = true
            console.log("选择:"+JSON.stringify(this.selectionIds))
            del(this.selectionIds).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.batchDeleteLoading = false
                this.page = 1
                this.goPageFirst(1)
              } else {
                throw res
              }
            }).catch((err) => {
              this.batchDeleteLoading = false
              this.$Notice.error({
                title: '操作提示',
                desc: '操作失败'
              })
            })
          }
        });
      }
    },
    mounted() {
      //获取国家/地区信息
      this.getLocalList();
      //加载套餐列表
      this.getPackageList();
      //加载列表信息
      this.goPageFirst(1);
    }
  }
</script>

<style>
  .input_notice {
    font-size: 15px;
    font-weight:bold;
  }
  .inputSty {
    width: 200px;
  }
</style>
