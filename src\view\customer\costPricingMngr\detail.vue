<template>
  <!-- 成本价格管理 详情页-->
	<Card style="width: 100%; padiing: 16px">
		<Form ref="searchForm" :model="searchObj" inline @submit.native.prevent :label-width="90"
			style="margin: 30px 0">
      <Row>
        <Col span="24">
          <FormItem label="资源供应商:" style="font-weight: bold;margin-left: 20px;">
          	{{supplierName}}
          </FormItem>&nbsp;&nbsp;&nbsp;&nbsp;
          <FormItem label="币种:" style="font-weight: bold;margin-left: 20px;">
          	{{currencyCode}}
          </FormItem>
        </Col>
      </Row>
			<FormItem label="国家" style="font-weight: bold;">
        <Select filterable v-model="searchObj.mcc" placeholder="请选择国家" clearable style="width: 180px;">
        	<Option v-for="(item1, index1) in countryList" :value="item1.mcc" :key="item1.mcc">{{item1.countryCn}}</Option>
        </Select>
			</FormItem>
			<FormItem>
				<Button style="margin: 0 2px" type="info" @click="search" :loading="searchloading" v-has="'search'">
					<Icon type="ios-search" />&nbsp;搜索
				</Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading"></Table>
			<Page :total="total" :page-size="pageSize" :current.sync="currentPage" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0" />
		</div>
  </Card>
</template>

<script>
	import {
		queryCostPricingDeatil,
	} from "@/api/customer/costPricingMngr.js";
  import {
  	opsearchAll,
  } from '@/api/operators';
	export default {
		components: {},
		data() {
			return {
        supplierName: "", //资源供应商名称
        currencyCode: "", //币种
				searchObj: {
					mcc: "", //规则名称
				},
				total: 0,
				pageSize: 10,
				page: 1,
				currentPage: 1,
				loading: false,
				searchloading: false,
        originData: [],
        countryList: [], // 国家列表
				tableData: [{
          countryName: 'HONG kong',
          operatorName: 'CMHK',
          price: '0.1',
          mcc: '460'
        }],
				columns: [{
						title: "国家",
						key: "countryName",
						align: "center",
						minWidth: 150,
						tooltip: true
					},
          {
          	title: "运营商",
          	key: "operatorName",
          	align: "center",
          	minWidth: 160,
          	tooltip: true
          },
					{
						title: "TADIG",
            key: "tadig",
						align: "center",
						minWidth: 160,
						tooltip: true
					},
          {
          	title: "计费方式",
            key: "type",
          	align: "center",
          	minWidth: 160,
          	tooltip: true,
            render: (h, params) => {
            	var row = params.row;
            	var text = "";
            	switch (row.type) {
            		case "1":
            			text = "标准";
            			break;
            		case "2":
            			text = "包天";
            			break;
            		case "3":
            			text = "包月";
            			break;
                case "4":
                  text = "包年";
                  break;
            		default:
            			text = "";
            	}
            	return h('label', text);
            },
          },
          {
          	title: "流量单价(MB)/总价",
            key: "price",
          	align: "center",
          	minWidth: 150,
          	tooltip: true,
          },
          {
          	title: "流量计费上限",
            key: "upperLimit",
          	align: "center",
          	minWidth: 150,
          	tooltip: true,
          },
				],
			};
		},

    created() {
      this.originData = JSON.parse(decodeURIComponent(this.$route.query.rowData)) // 解密row
    },

		mounted() {
      this.supplierName = this.originData.supplierName
      this.currencyCode = this.originData.currencyCode == '156' ? "CNY" : this.originData.currencyCode == '840'
       ? "USD" : this.originData.currencyCode == '344' ? "HKD" : this.originData.currencyCode == '978' ? "EUR" : ''
      this.goPageFirst(1);
      this.getLocalList();
		},

		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				queryCostPricingDeatil({
          id: this.originData.id,
          supplierName: this.supplierName,
          mcc: this.searchObj.mcc,
					size: 10,
					current: page,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = Number(res.count)
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},

			//表格数据加载
			loadByPage(page) {
				this.goPageFirst(page);
			},

			//搜索
			search() {
				this.searchloading = true
				this.goPageFirst(1);
			},

			/** -------------------------------------------------------------*/
      //获取国家
      getLocalList() {
      	opsearchAll().then(res => {
      		if (res && res.code == '0000') {
      			var list = res.data;
      			this.countryList = list;
      			this.countryList.sort(function(str1, str2) {
      				return str1.countryCn.localeCompare(str2.countryCn);
      			});
      		} else {
      			throw res
      		}
      	}).catch((err) => {

      	}).finally(() => {

      	})
      },
    },
  };
</script>

<style>
</style>
