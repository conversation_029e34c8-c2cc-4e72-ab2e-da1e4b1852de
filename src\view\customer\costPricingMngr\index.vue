<template>
  <!-- 成本价格管理 -->
	<Card style="width: 100%; padiing: 16px">
		<Form ref="searchForm" :model="searchObj" inline @submit.native.prevent :label-width="90"
			style="margin: 30px 0">
      <FormItem label="资源供应商:" prop="supplierId">
        <Select v-model="searchObj.supplierId" style="width: 200px;" filterable clearable placeholder="请选择资源供应商">
          <Option v-for="item in supplierList" :value="item.supplierId" :key="item.supplierId">{{item.supplierName}}</Option>
        </Select>
      </FormItem>
			<FormItem>
				<Button style="margin: 0 2px" type="info" @click="search" :loading="searchloading" v-has="'search'">
					<Icon type="ios-search" />&nbsp;搜索
				</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button style="margin: 0 2px" type="primary" @click="addItem" v-has="'add'">
        	<Icon type="ios-add" />&nbsp;新建
        </Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button style="margin: 0 2px" type="success" @click="fullSync" v-has="'fullSync'">
        	<Icon type="ios-brush-outline" />&nbsp;全量同步
        </Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button style="margin: 0 2px" type="warning" :loading="fullApprovalLoading" @click="fullApproval" v-has="'fullApproval'">
        	<Icon type="ios-checkmark" />&nbsp;全量审批
        </Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
				<template slot-scope="{ row, index }" slot="view">
					<Button type="info" ghost size="small" style="margin-right: 20px" @click="viewItem(row)" v-has="'view'">点击查看</Button>
				</template>
        <template slot-scope="{ row, index }" slot="action">
					<Button type="primary" ghost size="small" @click="exportHandle(row)" style="margin-right: 15px"
            v-has="'export'">导出</Button>
					<Button type="success" ghost size="small" @click="importHandle(row)" style="margin-right: 15px"
            :disabled="row.status === '4'" v-has="'import'">导入</Button>
          <Button type="warning" ghost size="small" @click="updateItem(row)" style="margin-right: 15px"
            :disabled="row.status === '4'" v-has="'update'">修改</Button>
					<Button type="error" ghost size="small" @click="deleteItem(row)" :disabled="row.status === '4' || row.status === '3'"
            v-has="'delete'">删除</Button>
				</template>
        <template slot-scope="{ row, index }" slot="approval">
        	<Button type="success" ghost size="small" style="margin-right: 20px" :disabled="row.status === '5' || row.status === '2'"
           @click="approve(row, '1')" v-has="'audit'">通过</Button>
        	<Button type="error" ghost size="small" style="margin-right: 20px" :disabled="row.status === '5' || row.status === '2'"
          @click="approve(row, '2')" v-has="'audit'">不通过</Button>
        </template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="currentPage" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0" />
		</div>
		<!-- 新建、修改规则弹窗 -->
		<Modal :title="title" v-model="ruleModal" :footer-hide="true" :mask-closable="false" width="1450px"
			@on-cancel="cancelModal">
			<div style="padding: 0 16px">
				<Form ref="addRule" :model="addRule" :rules="ruleValidate">
          <Row>
            <Col span="8">
              <FormItem label="资源供应商:" prop="supplierId">
                <Select v-model="addRule.supplierId" style="width: 220px;" :maxlength="50" filterable clearable
                  :disabled="title == '修改规则'" placeholder="请选择资源供应商" @on-change="handleSupplierChange">
                  <Option v-for="item in supplierList" :value="item.supplierId" :key="item.supplierId">{{item.supplierName}}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="生效日期" prop="effectiveDate">
                <DatePicker type="date" v-model="addRule.effectiveDate" placeholder="选择生效日期" style="width: 220px;" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="选择币种" prop="currencyCode">
              	<Select v-model="addRule.currencyCode" placeholder="请选择币种" clearable style="width: 220px;">
              		<Option value='156'>人民币</Option>
              		<Option value='840'>美元</Option>
              		<Option value='344'>港币</Option>
              		<Option value='978'>欧元</Option>
              	</Select>
              </FormItem>
            </Col>
          </Row>
          <Spin size="large" fix v-if="spinShow"></Spin>
          <div style="display: flex; flex-wrap: nowrap; flex-direction: column; margin-top: 20px;">
          	<div v-for="(item,index) in addRule.details" :key="index" class="costRuleSty">
          		<div class="costRuleBox">
          	    <h4 v-if="index == 0">国家</h4>
          	    <FormItem :prop="'details.' + index + '.mcc'" :rules="ruleValidate.mcc">
          	    	<Select filterable v-model="item.mcc" placeholder="请选择国家" clearable style="width: 180px;"
                    @on-change="handleCountryChange(index, $event)">
                    <Option v-for="country in countryList" :key="country.mcc" :value="country.mcc">{{ country.countryCn }}</Option>
          	    	</Select>
          	    </FormItem>
          	  </div>
          	  <div class="costRuleBox">
          	    <h4 v-if="index == 0">运营商</h4>
          	    <FormItem :prop="'details.' + index + '.operatorId'" :rules="ruleValidate.operatorId">
          	    	<Select filterable v-model="item.operatorId" placeholder="请选择运营商" clearable style="width: 180px;"
                    @on-change="handleOperatorChange(index, $event)">
          	    		<Option v-for="operator in filteredOperators[index]" :key="operator.operatorId" :title="operator.operatorName" :value="operator.operatorId">{{operator.operatorName}}</Option>
          	    	</Select>
          	    </FormItem>
          	  </div>
              <div class="costRuleBox">
                <h4 v-if="index == 0">TADIG</h4>
                <FormItem :prop="'details.' + index + '.tadig'" :rules="ruleValidate.tadig">
                	<Select filterable v-model="item.tadig" placeholder="请选择TADIG" clearable style="width: 180px;"
                    @on-change="handleTadigChange(index, $event)">
                		<Option v-for="tadig in getTadigsForOperator(item.mcc, item.operatorId)" :key="tadig" :title="tadig" :value="tadig">{{tadig}}</Option>
                	</Select>
                </FormItem>
              </div>
              <div class="costRuleBox">
                <h4 v-if="index == 0">计费方式</h4>
                <FormItem :prop="'details.' + index + '.type'" :rules="ruleValidate.type">
                	<Select filterable v-model="item.type" placeholder="请选择计费方式" clearable style="width: 120px;">
                		<Option value="1">标准</Option>
                		<Option value="2">包天</Option>
                		<Option value="3">包月</Option>
                		<Option value="4">包年</Option>
                	</Select>
                </FormItem>
              </div>
          	  <div class="costRuleBox">
          	    <h4 v-if="index == 0">流量单价(MB)/总价</h4>
          	    <FormItem :prop="'details.' + index + '.price'" :rules="ruleValidate.price">
          	    	<Input v-model.trim="item.price" type="number" :clearable="true" placeholder="请输入流量单价" style="width: 180px;"></Input>
          	    </FormItem>
          	  </div>
              <div class="costRuleBox">
                <h4 v-if="index == 0">流量计费上限</h4>
                <FormItem v-if="item.type == '2'" :prop="'details.' + index + '.upperLimit'" :rules="ruleValidate.upperLimit">
                	<Input v-model.trim="item.upperLimit" type="number" :clearable="true" placeholder="请输入流量计费上限" style="width: 180px;"></Input>
                </FormItem>
              </div>
              <div class="delete-button-container" style="flex: 0 0 auto; margin-left: auto;">
                  <Button type="error" size="small" style="width: 40px; height: 25px;" :loading="deleteLoading" @click="removeRule(index)" v-if="index != 0">删除</Button>
              </div>
          	</div>
          	<div style="display: flex; justify-content: flex-end; margin-top: 5px; ">
          		<Button type="info" size="small" :loading="addLoading" @click="addRuleHandle">添加</Button>
          	</div>
          </div>
				</Form>
				<div style="text-align: center; margin-top: 30px;">
					<Button @click="cancelModal" :loading="rebackLoading">返回</Button>
					<Button style="margin-left: 20px" :loading="submitFlag" type="primary" @click="submit">确定</Button>
				</div>
			</div>
		</Modal>
    <!-- 规则导入弹窗 -->
    <Modal title="规则导入" v-model="exportRules" :mask-closable="false" width="600px"
     @on-cancel="cancelModal">
    	<Form ref="formobj" :model="formobj" :rules="formobjRule" :label-width="100" :label-height="100"
    	 inline style="font-weight:bold;">
    		<FormItem label="文件"  style="width:510px">
    			<Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/"
    			:action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
    			:on-progress="fileUploading">
    				<div style="padding: 20px 0">
    					<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
    					<p>点击或拖拽文件上传</p>
    				</div>
    			</Upload>
    	    <ul class="ivu-upload-list" v-if="file" style="width: 100%;">
    	      <li class="ivu-upload-list-file ivu-upload-list-file-finish">
    	        <span><Icon type="ios-folder" />{{file.name}}</span>
    	        <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
    	      </li>
    	    </ul>
    			<div style="width: 100%;">
    				<Button type="primary" icon="ios-download" @click="downloadFile">下载模板文件</Button>
    				<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
    				<a ref="downloadLink" style="display: none"></a>
    			</div>
    		</FormItem>
    		<FormItem label="生效日期" prop="effectiveDate">
    		  <DatePicker type="date" v-model="formobj.effectiveDate" placeholder="选择生效日期" class="inputSty" />
    		</FormItem>
        <FormItem label="选择币种" prop="currencyCode">
        	<Select v-model="formobj.currencyCode" placeholder="请选择币种" clearable style="width: 200px;">
        		<Option value='156'>人民币</Option>
        		<Option value='840'>美元</Option>
        		<Option value='344'>港币</Option>
        		<Option value='978'>欧元</Option>
        	</Select>
        </FormItem>
    	</Form>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
      	<Button @click="cancelModal">取消</Button>
      	<Button type="primary" :loading="uploadLoading" @click="handleUpload">确定</Button>
      </div>
    </Modal>
    <Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
    <!-- 全量同步弹窗 -->
    <Modal title="全量同步" v-model="fullSyncModel" :mask-closable="false" width="600px"
     @on-cancel="cancelModal">
    	<Form ref="formobj" :model="formobj" :rules="formobjRule" :label-width="100" :label-height="100"
    	 inline style="font-weight:bold;">
    		<FormItem label="文件"  style="width:510px">
    			<Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/"
    			:action="uploadUrl" :on-error="handleError" :before-upload="handleSyncBeforeUpload"
    			:on-progress="fileUploading">
    				<div style="padding: 20px 0">
    					<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
    					<p>点击或拖拽文件上传</p>
    				</div>
    			</Upload>
    	    <ul class="ivu-upload-list" v-if="file" style="width: 100%;">
    	      <li class="ivu-upload-list-file ivu-upload-list-file-finish">
    	        <span><Icon type="ios-folder" />{{file.name}}</span>
    	        <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
    	      </li>
    	    </ul>
    			<div style="width: 100%;">
    				<!-- <Button type="primary" icon="ios-download" @click="downloadSyncFile">下载模板文件</Button> -->
    				<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{syncMessage}}</Alert>
    				<a ref="downloadLink" style="display: none"></a>
    			</div>
    		</FormItem>
    	</Form>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
      	<Button @click="cancelModal">取消</Button>
      	<Button type="primary" :loading="fullSyncLoading" @click="handleFullSync">确定</Button>
      </div>
    </Modal>
    <Table :columns="syncModelColumns" :data="syncModelData" ref="syncModelTable" v-show="false"></Table>
    <!-- 没导入数据弹窗 -->
    <Modal :title="falgModel== '1' ? '部分导入失败数据' : '同步失败数据'" v-model="importFailModal" :footer-hide="true" :mask-closable="false" :width="modelWidth">
      <Table ref="selection" :columns="falgModel== '1' ? FailColumns : syncFailColumns" :data="falgModel== '1' ? FailTableData : syncFailTableData" :ellipsis="true">
      </Table>
      <div style="margin-top: 50px; font-weight: bold; text-align: center;" v-if="ellipsis">由于同步失败数据超过50条，此处已进行省略……</div>
    </Modal>
  </Card>
</template>

<script>
	import {
		queryCostPricing,
    addCostPricing,
    updateCostPricing,
    delCostPricing,
    approveCostPricing,
    queryCostPricingDeatil,
    exportCostPricingDeatil,
    uploadRule,
    allSync,
    allAudit,
    getCountryInfo,
	} from "@/api/customer/costPricingMngr.js";
  import {
    supplier
  } from '@/api/ResourceSupplier'
  import Vue from 'vue';
  export default {
		components: {},
		data() {
      const validateOperatorUniqueness = (rule, value, callback) => {
        const matches = rule.field.match(/details\.(\d+)\.operatorId/);
        const currentIndex = parseInt(matches[1], 10); // 提取索引并转换为整数

        // 检查是否已存在同一MCC下operatorId为空的情况
        const hasEmptyOperatorIdForSameMcc = this.addRule.details.some((detail, index) => {
          return index !== currentIndex && detail.mcc === this.addRule.details[currentIndex].mcc && !detail.operatorId;
        });

        if (hasEmptyOperatorIdForSameMcc) {
          callback(new Error('此国家已有为空运营商'));
        } else {
          callback(); // 没有错误
        }

      };
      const validateMccOperTadigUniqueness = (rule, value, callback) => {
        const matches = rule.field.match(/details\.(\d+)\.tadig/);
        const currentIndex = parseInt(matches[1], 10);

        const currentDetail = this.addRule.details[currentIndex];
        const currentMcc = currentDetail.mcc;
        const currentOperatorId = currentDetail.operatorId;
        const currentTadig = value; // 当前tadig的值

        // 标记是否找到完全相同的记录
        let hasExactMatch = false;

        // 遍历所有details，检查是否有完全相同的记录
        this.addRule.details.forEach((detail, index) => {
            if (index !== currentIndex) { // 排除当前正在验证的记录
                if (detail.mcc === currentMcc && detail.operatorId === currentOperatorId && detail.tadig === currentTadig) {
                    hasExactMatch = true; // 找到完全相同的记录
                }
            }
        });
        // 检查是否存在相同的mcc和operatorId但tadig不同或为空的情况（之前的逻辑）
        let hasConflict = false;
        this.addRule.details.forEach((detail, index) => {
            if (index !== currentIndex) { // 排除当前正在验证的记录
                if (detail.mcc === currentMcc && detail.operatorId === currentOperatorId) {
                    // 如果tadig值不同，或者一个为空一个不为空
                    if (
                        (!currentTadig && detail.tadig) || // 当前tadig为空，但其他记录tadig不为空
                        (currentTadig && !detail.tadig)) { // 当前tadig不为空，但其他记录tadig为空
                        hasConflict = true;
                    }
                }
            }
        });

        // 根据检查结果调用callback
        if (hasExactMatch) {
            callback(new Error('国家 + 运营商 + tadig 保持唯一'));
        } else if (hasConflict) {
            callback(new Error('国家 + 运营商的组合存在tadig为空的情况'));
        } else {
            callback();
        }
      };
			return {
				searchObj: {
					supplierId: "", //资源供应商
				},
        supplierList: [],
        title: "",
        id: '',
        falgModel: '',
        modelWidth: '',
				total: 0,
				pageSize: 10,
				page: 1,
				currentPage: 1,
        index: 0,
				loading: false,
				searchloading: false,
				submitFlag: false,
        uploadLoading: false,
        deleteLoading: false,
        addLoading: false,
        rebackLoading: false,
        fullSyncLoading: false,
        fullApprovalLoading: false,
				ruleModal: false, //新建规则弹窗
        spinShow: false,
        exportRules: false, //导入规则弹窗
        importFailModal: false,
        fullSyncModel: false, //全量同步弹窗
        ellipsis: false,
        addRule: {
          supplierId: '',
          effectiveDate: '',
          currencyCode: '',
          details: [{
            index: 0,
            mcc: '',
            // countryName: '',
            operatorId: '' ,
            // operatorName: '',
            tadig: '',
            type: '',
            price: '',
            upperLimit: '',
          }]
        },
        operatorId: '',
        selectedSupplier: null, // 额外用于存储选中的供应商
        supplierName: null, //修改时候存储的供应商
        file: null,
        uploadUrl: '',
        message: '文件仅支持csv格式文件,大小不能超过10MB',
        syncMessage: '文件支持 .xls或 .xlsx格式文件,大小不能超过10MB',
        formobj: {
          effectiveDate: '',
          currencyCode: '',
        },
        formobjRule: {},
        originData: {},
        countryList: [], // 用于存储国家列表
        operatorsByMcc: {}, // 用于根据MCC存储运营商列表
        filteredOperators: [], // 根据选择的国家过滤后的运营商列表，每个索引对应一个数组
				// 模板文件
        tableData: [],
        modelData: [{
          'Country or region': '',
          'Operators': '',
          'unit price(MB)': '',
        }],
        syncModelData: [{
          '方向(中文)': '',
          '方向(英文)': '',
          '大洲': '',
          '资源提供商': '',
          '运营商': '',
          '结算货币': '',
          '原币价格': '',
          'Remark': '',
          '成本最后变动日期': '',
          '港币（对比用）': '',
          '美金（对比用）': '',
          '序号': '',
          '状态': '',
          '生效日期': '',
          '失效日期': '',
          '承诺量覆盖标记': '',
        }],
        syncModelColumns: [{
            title: '方向(中文)',
              key: '方向(中文)',
            }, {
              title: '方向(英文)',
              key: '方向(英文)',
            }, {
              title: '大洲',
              key: '大洲',
            }, {
              title: '资源提供商',
              key: '资源提供商',
            }, {
              title: '运营商',
              key: '运营商',
            }, {
              title: '结算货币',
              key: '结算货币',
            }, {
              title: '原币价格',
              key: '原币价格',
            }, {
              title: 'Remark',
              key: 'Remark',
            }, {
              title: '成本最后变动日期',
              key: '成本最后变动日期',
            }, {
              title: '港币（对比用）',
              key: '港币（对比用）',
            }, {
              title: '美金（对比用）',
              key: '美金（对比用）',
            }, {
              title: '序号',
              key: '序号',
            }, {
              title: '状态',
              key: '状态',
            }, {
              title: '生效日期',
              key: '生效日期',
            },{
              title: '失效日期',
              key: '失效日期',
            }, {
              title: '承诺量覆盖标记',
              key: '承诺量覆盖标记',
        },],
        // 失败弹窗
        FailTableData: [],
        FailColumns: [{
        		title: "国家",
        		key: "countryName",
        		align: "center",
        		minWidth: 120,
        		tooltip: true
        	},
        	{
        		title: "运营商",
            key: "operatorName",
        		align: "center",
        		minWidth: 120,
        		tooltip: true
        	},
          {
          	title: "计费方式",
            key: "type",
          	align: "center",
          	minWidth: 160,
          	tooltip: true,
            render: (h, params) => {
            	var row = params.row;
            	var text = "";
            	switch (row.type) {
            		case "1":
            			text = "标准";
            			break;
            		case "2":
            			text = "包天";
            			break;
            		case "3":
            			text = "包月";
            			break;
                case "4":
                  text = "包年";
                  break;
            		default:
            			text = "";
            	}
            	return h('label', text);
            },
          },
          {
          	title: "单价",
            key: "price",
          	align: "center",
          	minWidth: 100,
          	tooltip: true,
          },
          {
          	title: "流量计费上限",
            key: "upperLimit",
          	align: "center",
          	minWidth: 120,
          	tooltip: true
          },
        ],
        modelColumns: [{
        		title: 'Country or region',
        		minWidth: '200',
            key: 'Country or region',
        	},
        	{
        		title: 'Operators',
        		minWidth: '150',
            key: 'Operators',
        	},
          {
          	title: 'Type',
          	minWidth: '150',
            key: 'Type',
          },
          {
          	title: 'Unit price(MB) Or Total price',
          	minWidth: '150',
            key: 'Unit price(MB) Or Total price',
          },
          {
          	title: 'Upper Limit(MB)',
          	minWidth: '150',
            key: 'Upper Limit(MB)',
          },
        ],
				syncFailColumns: [{
        		title: "供应商名称",
        		key: "supplierName",
        		align: "center",
        		minWidth: 120,
        		tooltip: true
        	},
        	{
        		title: "国家",
            key: "countryName",
        		align: "center",
        		minWidth: 120,
        		tooltip: true
        	},
          {
          	title: "运营商",
            key: "operatorName",
          	align: "center",
          	minWidth: 120,
          	tooltip: true
          },
          {
          	title: "TADIG",
            key: "tadig",
          	align: "center",
          	minWidth: 120,
          	tooltip: true
          },
          {
          	title: "币种",
            key: "currencyCode",
          	align: "center",
          	minWidth: 160,
          	tooltip: true,
          },
          {
          	title: "计费方式",
            key: "type",
          	align: "center",
          	minWidth: 160,
          	tooltip: true,
            render: (h, params) => {
            	var row = params.row;
            	var text = "";
            	switch (row.type) {
            		case "1":
            			text = "标准";
            			break;
            		case "2":
            			text = "包天";
            			break;
            		case "3":
            			text = "包月";
            			break;
                case "4":
                  text = "包年";
                  break;
            		default:
            			text = "";
            	}
            	return h('label', text);
            },
          },
          {
          	title: "流量单价(MB)",
            key: "price",
          	align: "center",
          	minWidth: 110,
          	tooltip: true,
          },
          {
          	title: "流量计费上限",
            key: "upperLimit",
          	align: "center",
          	minWidth: 120,
          	tooltip: true
          },
          {
          	title: "失败原因",
            key: "msg",
          	align: "center",
          	minWidth: 120,
          	tooltip: true
          },
        ],
        syncFailTableData: [],
        columns: [{
						title: "资源供应商",
						key: "supplierName",
						align: "center",
						minWidth: 150,
						tooltip: true
					},
          {
            title: "生效日期",
            key: "effectiveDate",
            align: "center",
            minWidth: 150,
            tooltip: true,
          },
					{
            title: "币种",
            key: "currencyCode",
            align: "center",
            minWidth: 150,
            tooltip: true,
            render: (h, params) => {
            	const row = params.row;
            	//156 CNY,840 美元, 344 港币
            	const text =
            		row.currencyCode == "156" ?
            		"人民币" :
            		row.currencyCode == "840" ?
            		"美元" :
            		row.currencyCode == "344" ?
            		"港币" :
                row.currencyCode == "978" ?
                "欧元" :
            		"--";
            	return h("label", text);
            },
					},
          {
          	title: "详情",
            slot: "view",
          	minWidth: 120,
            align: 'center'
          },
          {
          	title: "操作",
            slot: "action",
            align: "center",
          	minWidth: 250,
          },
          {
          	title: "状态",
            key: "status",
            align: "center",
          	minWidth: 120,
            render: (h, params) => {
            	const row = params.row
            	const text =
                row.status == '1' ? "新建待审批" :
                row.status == '2' ? "正常" :
                row.status == '3' ? "修改待审批" :
                row.status == '4' ? "删除待审批" :
                row.status == '5' ? "审批不通过" :
                row.status == '6' ? "删除"
                : ''
            	return h('label', text)
            },
          },
          {
          	title: "审批操作",
            slot: "approval",
            align: "center",
          	minWidth: 200,
            fixed: 'right',
          }
				],
				ruleValidate: {
					supplierId: [{
						required: true,
						message: "资源供应商不能为空",
						trigger: "change",
					}],
          effectiveDate: [{
            required: true,
            type: 'date',
            message: '生效日期不能为空',
            trigger: 'change'
          }],
          currencyCode: [{
          	required: true,
          	message: "币种不能为空",
          	trigger: "change",
          }],
          mcc: [{
						required: true,
						message: "国家不能为空",
						trigger: "change",
					}],
          operatorId: [{
						validator: validateOperatorUniqueness,
					}],
          tadig: [{
						validator: validateMccOperTadigUniqueness,
					}],
          type: [{
          	required: true,
          	message: "计费方式不能为空",
          	trigger: "change",
          }],
          price: [{
						required: true,
						message: "流量单价不能为空",
						trigger: "change",
					}, {
						validator: (rule, value, cb) => {
						  // 如果 value 是空字符串、undefined 或者 null，则不进行校验
						  if (!value || value === '') {
						    return cb(); // 直接返回，不传入错误信息
						  }

						  // 正则表达式用于匹配最多8位整数和8位小数
						  var str = /^(?!0(\.0+)?$)([1-9]\d{0,7}(\.\d{1,8})?|\d{1,8}(\.\d{1,8})?)$/;

						  // 使用正则表达式进行校验
						  if (str.test(value)) {
						    return cb(); // 校验成功，直接返回，不传入错误信息
						  } else {
						    // 校验失败，返回错误信息
						    return cb(new Error('最高支持8位整数和8位小数正数'));
						  }
						},
						trigger: 'blur',
						message: '最高支持8位整数和8位小数正数' // 错误提示信息
					}],
          upperLimit: [{
						required: true,
						message: "流量计费上限不能为空",
						trigger: "change",
					}, {
						validator: (rule, value, cb) => {
						  // 如果 value 是空字符串、undefined 或者 null，则不进行校验
						  if (!value || value === '') {
						    return cb(); // 直接返回，不传入错误信息
						  }

						  // 正则表达式用于匹配最多8位整数和8位小数
						  var str = /^(?!0(\.0+)?$)([1-9]\d{0,7}(\.\d{1,8})?|\d{1,8}(\.\d{1,8})?)$/;

						  // 使用正则表达式进行校验
						  if (str.test(value)) {
						    return cb(); // 校验成功，直接返回，不传入错误信息
						  } else {
						    // 校验失败，返回错误信息
						    return cb(new Error('最高支持8位整数和8位小数正数'));
						  }
						},
						trigger: 'blur',
						message: '最高支持8位整数和8位小数正数' // 错误提示信息
					}],
        },
			};
		},

    created() {
      this.filteredOperators = this.addRule.details.map(() => []);
    },

    watch: {
      // 监听 addRule.details 的变化，并在变化后更新 filteredOperators
      'addRule.details': {
        handler(newVal) {
          if (Array.isArray(newVal)) {
            this.updateFilteredOperators(newVal);
          }
        },
        deep: true,
      },
    },

		mounted() {
      this.goPageFirst(1);
      this.getsupplier();
      this.getCountryInfo();
		},

		methods: {
			goPageFirst: function(page) {
				this.loading = true
				queryCostPricing({
					size: 10,
					current: page,
					supplierId: this.searchObj.supplierId,
				}).then(res => {
					if (res.code == '0000') {
						this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = Number(res.count)
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchloading = false
				})
			},

			//表格数据加载
			loadByPage(page) {
				this.goPageFirst(page);
			},

			//搜索
			search() {
				this.searchloading = true
				this.goPageFirst(1);
			},

      handleSupplierChange(e) {
        this.selectedSupplier = this.supplierList.find(supplier => supplier.supplierId === e);
      },

      // 自动回填生效日期
      setTodayDate() {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0'); // 注意月份是从0开始的，所以要+1
        const date = String(today.getDate()).padStart(2, '0');
        this.addRule.effectiveDate = `${year}-${month}-${date}`;
      },

			//新建
			addItem() {
        this.title = "新建规则"
        this.setTodayDate()
				this.ruleModal = true
			},

      // 修改
      updateItem(row) {
        this.title = "修改规则"
        this.supplierName = row.supplierName
        this.addRule.supplierId = row.supplierId
        this.addRule.effectiveDate = row.effectiveDate
        this.addRule.currencyCode = row.currencyCode
        this.id = row.id
        this.getEditData(row)
        this.ruleModal = true
      },

      // 新增/修改 提交
      submit() {
      	this.$refs["addRule"].validate((valid) => {
          var addData = JSON.parse(JSON.stringify(this.addRule))
          // 处理时间格式
          addData.effectiveDate = this.dateTransfer(addData.effectiveDate)
          // 如果不为包天，则清除流量计费上限为空
          addData.details = this.setUpperLimitIfTypeNotTwo(addData.details)
          var updataData = JSON.parse(JSON.stringify(addData))
          if (this.title == "修改规则") {
            updataData.id = this.id
            updataData.isCover = true
            updataData.supplierName= this.supplierName
          } else {
            // 新增
            addData.supplierName = this.selectedSupplier.supplierName
          }
          if (valid) {
            let func = this.title == "新建规则" ? addCostPricing : updateCostPricing
            let resultData = this.title == "新建规则" ? addData : updataData
      			this.submitFlag = true;
      			func(resultData).then(res => {
      				if (res && res.code == '0000') {
      					setTimeout(() => {
      						this.$Notice.success({
      							title: "操作提醒：",
      							desc: "操作成功！"
      						});
      						this.submitFlag = false;
      						this.addRule = false;
                  this.cancelModal()
      						this.goPageFirst(this.currentPage);
      					}, 1500);
      				} else {
      					this.submitFlag = false;
      					throw res
      				}
      			}).catch((err) => {
      				this.submitFlag = false;
      			}).finally(() => {

      			})
      		}
      	})
      },

      // 删除
      deleteItem(row) {
        this.$Modal.confirm({
          title: '确认删除？',
          onOk: () => {
            delCostPricing({
              id: row.id
            }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirst(1)
              } else {
                throw res
              }
            }).catch((err) => {
            })
          }
        });
      },

      // 导出
      exportHandle(row) {
        exportCostPricingDeatil({
          id: row.id,
          supplierName: row.supplierName,
          size: -1,
          current: -1,
        })
        .then((res) => {
          const content = res.data;
      		let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
          if ("download" in document.createElement("a")) {
            // 支持a标签download的浏览器
            const link = document.createElement("a"); // 创建a标签
            let url = URL.createObjectURL(content);
            link.download = fileName;
            link.href = url;
            link.click(); // 执行下载
            URL.revokeObjectURL(url); // 释放url
          } else {
            // 其他浏览器
            navigator.msSaveBlob(content, fileName);
          }
        })
          .catch();
      },

      // 导入
      importHandle(row) {
        this.falgModel = '1'
        this.modelWidth = '400px'
        this.originData.id = row.id
        this.getsupplier()
        this.exportRules = true
      },

      handleUpload() {
      	if (!this.file) {
      		this.$Message.warning('请选择需要上传的文件')
      		return
      	} else {
      		this.$refs.formobj.validate(valid => {
      			if (valid) {
              this.uploadLoading = true
              let effectiveDate  = this.formobj.effectiveDate ? this.dateTransfer(this.formobj.effectiveDate) : ""
      				let formData = new FormData()
      				formData.append('id', this.originData.id)
      				formData.append('file', this.file)
      				formData.append('currencyCode', this.formobj.currencyCode ? this.formobj.currencyCode : '')
      				formData.append('effectiveDate', effectiveDate)
      				uploadRule(formData).then(res => {
      					if (res.code === '0000') {
      						this.$Notice.success({
      							title: '操作成功',
      							desc: '操作成功'
      						})
                  this.uploadLoading=false
                  this.currentPage=1
                  this.goPageFirst(1)
                  if (res.data.length == 0) {
                    this.cancelModal()
                  } else {
                    this.importFailModal = true
                    const newData = res.data.map(item => {
                        return {
                            ...item,
                            hkd: item.price === null ? "" : item.price,
                        };
                    });
                    this.FailTableData = newData
                  }
      					} else {
      						throw res
      					}
      				}).catch((err) => {
      					console.log(err)
      				}).finally(() => {
      					this.uploadLoading=false
      					this.cancelModal()
      				})
      			}
      		})
      	}
      },

      // 通过/不通过
      approve(row, type) {
        this.$Modal.confirm({
        	title: type == 1 ? '确认执行审核通过？' : '确认执行审核不通过？',
        	onOk: () => {
        		approveCostPricing({
              id: row.id,
              authStatus: type
            }).then(res => {
        			if (res && res.code == '0000') {
        				this.$Notice.success({
        					title: '操作提示',
        					desc: '操作成功'
        				})
        				this.loadByPage(this.page);
        			} else {
        				throw res
        			}
        		}).catch((err) => {
        		})
        	}
        });
      },

      // 全量同步
      fullSync() {
        this.falgModel = '2'
        this.modelWidth = '1250px'
        this.fullSyncModel = true
      },

      handleFullSync() {
        if (!this.file) {
        	this.$Message.warning('请选择需要上传的文件')
        	return
        } else {
        	this.$refs.formobj.validate(valid => {
        		if (valid) {
              this.fullSyncLoading = true
        			let formData = new FormData()
        			formData.append('file', this.file)
        			allSync(formData).then(res => {
        				if (res.code === '0000') {
        					this.$Notice.success({
        						title: '操作成功',
        						desc: '操作成功'
        					})
                  this.currentPage=1
                  this.goPageFirst(1)
                  if (res.data.length == 0) {
                    this.cancelModal()
                  } else {
                    this.importFailModal = true
                    if (res.data.length > 50) {
                      this.syncFailTableData = res.data.slice(0, 50)
                      this.ellipsis = true
                    } else {
                      this.syncFailTableData = res.data
                      this.ellipsis = false
                    }
                  }
        				} else {
        					throw res
        				}
        			}).catch((err) => {
        				console.log(err)
        			}).finally(() => {
        				this.fullSyncLoading=false
        				this.cancelModal()
        			})
        		}
        	})
        }
      },

      //全量审批
      fullApproval() {
        this.$Modal.confirm({
          title: "确定执行审批？",
        	onOk: () => {
            this.fullApprovalLoading = true
        		allAudit().then(res => {
        			if (res.code === '0000') {
        				this.$Notice.success({
        					title: '操作成功',
        					desc: '操作成功'
        				})
        		    this.goPageFirst(1)
        			} else {
        				throw res
        			}
        		}).catch((err) => {
        			console.log(err)
        		}).finally(() => {
        			this.fullApprovalLoading=false
        		})
        	}
        });
      },

      cancelModal() {
        this.rebackLoading = true
        // 手动重置表单最有效
        this.addRule = {
          name: '',
          effectiveDate: '',
          currencyCode: '',
          details: [
            {
              index: 0,
              mcc: '',
              countryName: '',
              operatorId: '',
              operatorName: '',
              tadig: '',
              type: '',
              price: '',
              upperLimit: '',
            },
          ]
        }
        this.$refs["addRule"].resetFields();
        this.ruleModal = false;
        //导入弹窗、全量同步弹窗
        this.file = null
        this.$refs["formobj"].resetFields();
        this.exportRules = false
        this.chargeType = false
        this.rebackLoading = false
        this.fullSyncModel = false
      },

      // 详情查看
      viewItem(row) {
        this.$router.push({
          name:'costPricingDetails',
          query: {
            rowData : encodeURIComponent(JSON.stringify(row)),
          }
        })
      },

      // 动态添加
      addRuleHandle() {
        this.addLoading = true
				this.index++;
				this.addRule.details.push({
					index: this.index,
					mcc: '',
					operatorId: '',
          type: '',
					price: '',
          upperLimit: '',
				});
        this.addLoading = false
			},

      // 动态删除
      removeRule(index) {
        this.deleteLoading = true
        this.addRule.details.splice(index, 1);
        this.index--;
        this.deleteLoading = false
      },

      setUpperLimitIfTypeNotTwo(data) {
        data.forEach(item => {
          if (item.type !== "2") {
            item.upperLimit = "";
          }
        });
        return data;
      },

      dateTransfer(dateStr) {
        var date = new Date(dateStr);

        // 使用getFullYear(), getMonth() + 1, getDate() 方法来获取年、月、日
        // 注意getMonth()返回的是0-11的整数，所以需要加1来转换为常见的月份表示
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2); // 确保月份是两位数
        var day = ('0' + date.getDate()).slice(-2); // 确保日期是两位数

        // 将年、月、日拼接成 "yyyy-mm-dd" 格式
        var formattedDate = year + '-' + month + '-' + day;
        return formattedDate;
      },

      handleCountryChange(index, mcc) {
        const country = this.countryList.find(c => c.mcc === mcc);
        if (country) {
          Vue.set(this.addRule.details[index], 'mcc', mcc);
          Vue.set(this.addRule.details[index], 'countryName', country.countryCn);
          this.filteredOperators[index] = this.operatorsByMcc[mcc] || [];
          // 重置运营商和 TADIG 选择
          Vue.set(this.addRule.details[index], 'operatorId', '');
          Vue.set(this.addRule.details[index], 'tadig', '');
        }
      },

      handleOperatorChange(index, operatorId) {
        const operator = this.filteredOperators[index].find(o => o.operatorId === operatorId);
        if (operator) {
          Vue.set(this.addRule.details[index], 'operatorId', operatorId);
          Vue.set(this.addRule.details[index], 'operatorName', operator.operatorName);
        } else {
          // 删除operatorId的情况下
          Vue.set(this.addRule.details[index], 'operatorId', '');
          Vue.set(this.addRule.details[index], 'operatorName', '');
        }
      },

      handleTadigChange(index, tadig) {
        Vue.set(this.addRule.details[index], 'tadig', tadig);
      },

      //获取动态的TADIG数据源
      getTadigsForOperator(mcc, operatorId) {
        if (operatorId) {
          // 遍历所有国家及其运营商，找到对应operatorId的TADIGs
          const operator = this.operatorsByMcc[mcc].find(op => op.operatorId === operatorId);
          return operator ? operator.tadigs : [];
        }
      },

      handleBeforeUpload(file) {
        if (!/^.+(\.csv)$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式不正确',
            desc: '文件 ' + file.name + ' 格式不正确，请上传.csv。'
          })
        } else {
          if (file.size > 10 * 1024 * 1024) {
            this.$Notice.warning({
              title: '文件大小超过限制',
              desc: '文件 ' + file.name + '超过了最大限制范围10MB'
            })
          } else {
            this.file = file
          }
        }
        return false
      },

      handleSyncBeforeUpload(file) {
        if (!/^.+(\.(xls|xlsx))$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式不正确',
            desc: '文件 ' + file.name + ' 格式不正确，请上传.xls或 .xlsx 格式文件。'
          })
        } else {
          if (file.size > 10 * 1024 * 1024) {
            this.$Notice.warning({
              title: '文件大小超过限制',
              desc: '文件 ' + file.name + '超过了最大限制范围10MB'
            })
          } else {
            this.file = file
          }
        }
        return false
      },

      fileUploading(event, file, fileList) {
      	this.message = '文件上传中、待进度条消失后再操作'
        this.syncMessage = '文件上传中、待进度条消失后再操作'
      },

      fileSuccess(response, file, fileList) {
      	this.message = '请先下载模板文件，并按格式填写后上传'
      },

      handleError(res, file) {
      	var v = this
      	setTimeout(function() {
      		v.uploading = false;
      		v.$Notice.warning({
      			title: '错误提示',
      			desc: "上传失败！"
      		});
      	}, 3000);
      },

      //下载模板文件
      downloadFile() {
      	this.$refs.modelTable.exportCsv({
      		filename: "规则导入文件",
      		type:'csv',
      		columns: this.modelColumns,
      		data: this.modelData
      	})
      },

      downloadSyncFile() {
        this.$refs.syncModelTable.exportCsv({
        	filename: "全量同步文件",
        	type:'csv',
        	columns: this.syncModelColumns,
        	data: this.syncModelData
        })
      },

      removeFile() {
      	this.file = ''
      },

			/** -------------------------------------------------------------*/
      // 获取资源供应商
      getsupplier() {
        supplier({
          pageNum: -1,
          pageSize: -1,
        }).then(res => {
          if (res.code == '0000') {
            this.supplierList = res.data
          }
        }).catch((err) => {
          console.error(err)
        }).finally(() => {})
      },

      //获取国家->运营商->TADIG
      getCountryInfo() {
        getCountryInfo().then(res => {
          if (res.code == '0000') {
            let originalData = res.data.map(country => {
              return {
                ...country,
                operators: country.operatorNames.map(operator => ({
                  operatorId: operator.operatorId,
                  operatorName: operator.operatorName,
                  tadigs: Object.values(operator.tadigs) // 假设 tadigs 是对象形式，需要转换为数组
                }))
              };
            });
            this.countryList = originalData;

            //获取运营商列表数据
            this.operatorsByMcc = {};
            originalData.forEach(country => {
              this.operatorsByMcc[country.mcc] = country.operators;
            });

            // 初始化 filteredOperators 和 details 的其他字段
            this.addRule.details.forEach((detail, index) => {
              this.filteredOperators[index] = [];
              detail.operatorId = '';
              detail.tadig = '';
            });
          }
        }).catch((err) => {
          console.error(err)
        }).finally(() => {})
      },

      //修改调详情接口回显数据
      getEditData: function(row) {
        this.spinShow = true
      	queryCostPricingDeatil({
          id: row.id,
          name: row.name,
          mcc: '',
      		size: -1,
      		current: -1,
      	}).then(res => {
      		if (res.code == '0000') {
            this.addRule.details = res.data
      		}
      	}).catch((err) => {
      		console.error(err)
      	}).finally(() => {
          this.spinShow = false
      	})
      },

      // 修改的时候回显运营商列表源数组
      updateFilteredOperators() {
        // 根据 addRule.details 中的每个条目的 mcc 来更新 filteredOperators
        this.filteredOperators = this.addRule.details.map(detail => {
          // 假设 operatorsByMcc[detail.mcc] 存在且是一个数组
          return this.operatorsByMcc[detail.mcc] || []; // 如果不存在，则返回一个空数组
        });
      },
    },
	};
</script>

<style>
	/* 去掉input为number的上下箭头 */
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}

	input[type="number"] {
		-moz-appearance: textfield;
	}

  .costRuleSty {
    display: flex;
  }

  .costRuleBox {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: column;
    align-items: center;
    margin-right: 50px;
  }

  .delete-button-container{
    display: flex;
    margin-left: auto
  }

  .inputSty {
  	width: 250px;
  }
</style>

