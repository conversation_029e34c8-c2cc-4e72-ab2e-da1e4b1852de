<template>
	<!-- 卡号列表 -->
	<Card>
		<div>
			<span style="margin-top: 4px;font-weight:bold;">客户:</span>&nbsp;&nbsp;
			<span>{{corpName}}</span>&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">客户类型:</span>&nbsp;&nbsp;
			<span>{{corpType}}</span>
		</div>
		<div style="display: flex;margin-top: 20px;">
			<span style="margin-top: 4px;font-weight:bold;">ICCID:</span>&nbsp;&nbsp;
			<Input v-model="form.iccid" placeholder="请输入ICCID"  clearable style="width: 200px" ></Input>&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">状态:</span>&nbsp;&nbsp;
			<Select filterable v-model="form.type"  :clearable="true"  placeholder="请选择状态" style="width: 200px ;margin-right: 10px;">
			  <Option  :value="2" >待分配</Option>
			  <Option  :value="1" >已分配</Option>
			</Select>&nbsp;&nbsp;&nbsp;&nbsp;
			<Button  v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search()">搜索</Button>
			<Button  v-has="'export'" style="margin: 0 2px;margin-left: 20px;" icon="ios-cloud-download-outline" type="success" :loading="downloading" @click="exportFile" >
			  导出
			</Button>&nbsp;&nbsp;&nbsp;&nbsp;
			<Button  @click="back">
				<Icon type="ios-arrow-back" />&nbsp;返回
			</Button>
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'delete'" type="error" ghost v-if="row.status==='1'" @click="deleteNumber(row)" >
				  移除
				</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div  style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 导出提示 -->
		<Modal  v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
		  <div style="align-items: center;justify-content:center;display: flex;">
			  <Form  label-position="left"  :label-width="150" style=" align-items: center;justify-content:center;" >
			   		  <h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					  <FormItem label="你本次导出任务ID为:">
						<span style="width: 100px;">{{taskId}}</span>
			   		  </FormItem>
			   		  <FormItem label="你本次导出的文件名为:">
						<span>{{taskName}}</span>
			   		  </FormItem>
					  <span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
			   </Form>
		  </div>

		  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
		    <Button @click="cancelModal">取消</Button>
		    <Button type="primary" @click="Goto">立即前往</Button>
		  </div>
		</Modal>
	</Card>
</template>

<script>
	import {
		Cardlist,
		exportCardlist,
		// Deleteiccid,
		DeleteCard
	} from "@/api/customer/flowpool";
	import {
		getStoreByCorpId
	} from "@/api/flowpool/flowpool";
	export default{
		data(){
			return{
				corpId: '',
				corpName:'',
				corpType:'',
				form:{
					iccid:'',
					type:''
				},
				total: 0,
				currentPage: 1,
				page: 0,
				taskId:'',
				taskName:'',
				exportModal:false,//导出弹框标识
				columns:[{
					title: "ICCID",
					key: 'iccid',
					minWidth: 120,
					align: 'center'
				},
				{
					title: "状态",
					key: 'status',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  const text = row.status==='2' ? '待分配':row.status==='1' ? '已分配':"";
					  return h('label', text);
					}
				},
				{
					title: "单周期类型限量(MB)",
					key: 'dailyTotal',
					minWidth: 120,
					align: 'center'
				},
				{
					title: "总限量(MB)",
					key: 'total',
					minWidth: 120,
					align: 'center'
				},
				{
					title: "控制逻辑",
					key: 'rateType',
					minWidth: 120,
					align: 'center',
          render: (h, params) => {
            const row = params.row;
            const text = row.rateType==="1" ? "达量继续使用":row.rateType==='2' ? "达量限速"
            :row.rateType==='3' ? "达量停用":"";
            return h('label', text);
          }
				},{
					title: "归属流量池",
					key: 'flowPoolName',
					minWidth: 120,
					align: 'center'
				},{
					title: "操作",
					slot: 'action',
					minWidth: 120,
					align: 'center',
				}
				],
				loading:false,
				searchloading:false,
				downloading:false,
				rule:{

				},
				data:[]
			}
		},
		mounted() {
			// 保存上一页返回数据
			localStorage.setItem("ObjList", decodeURIComponent(this.$route.query.ObjList))
			this.corpId= JSON.parse(decodeURIComponent(this.$route.query.obj)).corpId
      this.corpType= JSON.parse(decodeURIComponent(this.$route.query.obj)).type
			//获取渠道商名称
			this.getcorpName(this.corpId)
      // 处理客户类型
      this.corpType = this.corpType === '1' ? "渠道商" : this.corpType === '3' ? "合作商" : this.corpType === '4' ? "后付费": "";
			this.goPageFirst(1)
    },
		methods:{
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				Cardlist({
					pageSize:10,
					pageNum:page,
					ICCID:this.form.iccid,
					status:this.form.type,
					corpId: this.corpId,
					corpType: this.corpType,
					cooperationMode: 1
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						this.data = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			goPage:function(page){
				this.goPageFirst(page)
			},
			search:function(){
				this.searchloading = true
				this.goPageFirst(1)
			},
			exportFile:function(){
				this.downloading = true
				exportCardlist({
					pageSize:-1,
					pageNum:-1,
					ICCID:this.form.iccid,
					Status:this.form.type,
					corpId:this.corpId,
					exportType :1,
					userId:this.$store.state.user.userId
				}).then((res) => {
				  this.exportModal=true
				  this.taskId=res.data.taskId
				  this.taskName=res.data.taskName
				  this.downloading = false
				}).catch(() => this.downloading = false)
			},
			deleteNumber:function(row){
				this.$Modal.confirm({
					title: "确认删除该项?",
					onOk: () => {
						this.iccids = []
						this.iccids.push(row.iccid)
						DeleteCard({
							corpId: this.corpId,
							flowPoolId:row.flowPoolID,
							iccids: this.iccids
						}).then(res => {
							if (res && res.code == '0000') {
								this.goPageFirst(1);
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						})
					},
				})
			},
			back:function(){
				this.$router.push({
				  path:'/channelPool',
				  query: {
				    // callListinfo: encodeURIComponent(JSON.stringify(row)),
				  }
				})
			},
			cancelModal(){
				this.exportModal=false
			},
			Goto(){
				this.$router.push({
				  path: '/taskList',
				  query: {
					taskId: encodeURIComponent(this.taskId),
					fileName:encodeURIComponent(this.taskName),
					// corpId:encodeURIComponent(this.corpId)
				  }
				})
				this.exportModal=false
			},
			//获取渠道商名称
			getcorpName(corpId){
				getStoreByCorpId(corpId).then(res => {
					if (res.code == '0000') {
						this.corpName = res.data.corpName
					}
				}).catch((err) => {
					console.error(err)
				})
			}
		}

	}
</script>

<style>
</style>
