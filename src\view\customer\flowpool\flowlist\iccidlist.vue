<template>
	<!-- iccid列表 -->
	<Card>
		<div>
			<span style="margin-top: 4px;font-weight:bold;">ICCID:</span>&nbsp;&nbsp;
			<Input v-model="iccid" placeholder="请输入ICCID" clearable
				style="width: 200px"></Input>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">备注:</span>&nbsp;&nbsp;
			<Input v-model="cardRemark" placeholder="请输入备注" clearable
				style="width: 300px"></Input>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">上网状态:</span>&nbsp;&nbsp;
			<Select filterable v-model="currentRateType" placeholder="请选择上网状态" style="width: 200px" clearable>
				<Option value="1">正常</Option>
				<Option value="2">单卡周期达量限速</Option>
				<Option value="3">单卡周期达量停用</Option>
				<Option value="4">单卡总量达量限速</Option>
				<Option value="5">单卡总量达量停用</Option>
				<Option value="6">流量池总量达量限速</Option>
				<Option value="7">流量池总量达量停用</Option>
			</Select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">卡片状态:</span>&nbsp;&nbsp;
			<Select filterable v-model="flowPoolStatus" placeholder="请选择卡片状态" style="width: 200px" clearable>
				<Option value='1'>正常</Option>
				<Option value='2'>暂停</Option>
			</Select>&nbsp;&nbsp;
			<div
				style="display: flex;justify-content: flex-start;align-items: center;margin-top: 30px;margin-left: 20px;">
				<Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading"
					@click="search()">搜索</Button>&nbsp;&nbsp;&nbsp;&nbsp;
				<Button v-has="'export'" style="margin: 0 2px;margin-left: 20px;" icon="ios-cloud-download-outline"
					type="success" :loading="downloading" @click="exportFile">
					导出
				</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button v-has="'import'" type="warning" icon="md-add"
					@click="importIccid()">ICCID导入</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button v-has="'batchDelete'" type="error" icon="ios-trash"
					@click="deleteBatch()">批量删除</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button v-has="'batchUpdate'" type="primary" icon="md-add"
					@click="updateBatch()">批量修改</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button style="margin: 0 4px" @click="back">
					<Icon type="ios-arrow-back" />&nbsp;返回
				</Button>
			</div>

			<!-- 表格 -->
			<Table :columns="columns" :data="data" @on-selection-change="handleRowChange"
				@on-select-cancel="cancelSigle" @on-select-all-cancel="cancelAll" style="width:100%;margin-top: 40px;"
				:loading="loading" @on-sort-change="changeSort">
				<template slot-scope="{ row, index }" slot="action">
					<Button v-has="'delete'" type="error" style="margin-right: 10px;"
						@click="deleteItem(row)">删除</Button>
					<Button v-has="'stop'" type="warning" style="margin-right: 10px;" v-if="row.flowPoolStatus === '2'"
						disabled @click="stop(row)">暂停</Button>
					<Button v-has="'stop'" type="warning" style="margin-right: 10px;" v-else
						@click="stop(row)">暂停</Button>
					<Button v-has="'recover'" type="success" style="margin-right: 10px;"
						v-if="row.flowPoolStatus === '1'" disabled @click="active(row)">恢复</Button>
					<Button v-has="'recover'" type="success" style="margin-right: 10px;" v-else
						@click="active(row)">恢复</Button>
					<Button v-has="'cardManagement'" type="primary" style="margin-right: 10px;"
						@click="cardItem(row)">单卡管理</Button>
				</template>
			</Table>
			<!-- 分页 -->
			<div style="margin-top:15px">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
			<!-- 导入模态框 -->
			<Modal title="ICCID导入" v-model="importModal" :mask-closable="true" @on-cancel="cancelModal" width="900px">
				<Tabs>
					<TabPane v-has="'single_import'" label="单个导入" icon="ios-cloud-upload">
						<div style="display: flex;border-bottom: solid 1px #CCCCCC;">
							<Form ref="form" :model="form" :rules="rule">
								<FormItem label="选择流量池:">
									<!-- <Select filterable v-model="form.flowpoolid" :clearable="true" placeholder="请选择流量池" style="width: 300px ;margin-right: 10px;">
										<Option v-for="item in poolList" :value="item.flowPoolId" :key="item.flowPoolId">{{ item.flowPoolName }}</Option>
									</Select> -->
									<span style="font-weight: bold;">{{form.flowPoolName}}</span>
								</FormItem>
								<FormItem label="ICCID" prop="iccid">
									<Input v-model="form.iccid" placeholder="请输入ICCID" clearable
										style="width: 300px"></Input>
								</FormItem>
								<FormItem label="单周期类型上限" prop="singlecycle">
									<Input v-model="form.singlecycle" placeholder="单位MB" clearable
										style="width: 300px"></Input>
								</FormItem>
								<FormItem label="总上限" prop="totalcap">
									<Input v-model="form.totalcap" placeholder="单位MB" clearable
										style="width: 300px"></Input>
								</FormItem>
								<FormItem label="控制逻辑" prop="controllogic">
									<Select filterable v-model="form.controllogic" placeholder="请选择控制逻辑"
										style="width: 300px" clearable>
										<Option value="1">达量继续使用</Option>
										<Option value="2">达量限速</Option>
										<Option value="3">达量停用</Option>
									</Select>

								</FormItem>
								<FormItem label="入池可用时长" prop="availableTime">
									<Input v-model="form.availableTime" placeholder="填写可用的单周期数量" clearable
										style="width: 300px"></Input>
								</FormItem>
							</Form>
						</div>

						<div
							style="width: 100%; display: flex;align-items: center;justify-content:center;margin-top: 50px;">
							<Button @click="cancelModal">返回</Button>&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="primary" :loading="importLoading" @click="confirmone">确定</Button>
						</div>

					</TabPane>
					<TabPane v-has="'batch_import'" label="批量导入" icon="md-redo">
						<Form ref="formobj" :model="formobj" :rules="ruleobj">
							<FormItem label="选择流量池:">
								<!-- <Select filterable v-model="formobj.flowpoolid" :clearable="true" placeholder="请选择流量池" style="width: 300px ;margin-right: 10px;">
									<Option v-for="item in poolList" :value="item.flowPoolId" :key="item.flowPoolId">{{ item.flowPoolName }}</Option>
								</Select> -->
								<span style="font-weight: bold;">{{form.flowPoolName}}</span>
							</FormItem>
							<FormItem label="上传ICCID列表" prop="file">
								<Upload type="drag" v-model="formobj.file" :action="uploadUrl" :on-success="fileSuccess"
									:on-error="handleError" :before-upload="handleBeforeUpload"
									:on-progress="fileUploading"
									style="width: 500px; margin-top: 50px;margin-left: 50px;">
									<div style="padding: 20px 0">
										<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
										<p>点击或拖拽文件上传</p>
									</div>
								</Upload>
								<div style="width: 500px;margin-left: 50px;">
									<Button type="primary" :loading="downloading" icon="ios-download"
										@click="downloadFile">下载模板文件</Button>
								</div>
								<ul class="ivu-upload-list" v-if="file" style="width: 500px; margin-left: 50px;">
									<li class="ivu-upload-list-file ivu-upload-list-file-finish">
										<span>
											<Icon type="ios-folder" />{{file.name}}
										</span>
										<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style=""
											@click="removeFile"></i>
									</li>
								</ul>
								<div style="width: 100%; display: flex;margin-left: 50px;margin-top: 100px;">
									<Button @click="cancelModal">返回</Button>&nbsp;&nbsp;&nbsp;&nbsp;
									<Button type="primary" :loading="importLoading" @click="confirmbatch">确定</Button>
								</div>

							</FormItem>

							<h1>历史任务查看</h1>
							<Table :columns="columnsTask" :data="taskdata" style="width:100%;margin-top: 40px;"
								:loading="loading">
								<template slot-scope="{ row, index }" slot="success">
									<Button v-if="row.successCount>0" type="success" style="margin-right: 10px;"
										@click="exportfiles(row,1)">点击下载</Button>
								</template>
								<template slot-scope="{ row, index }" slot="fail">
									<Button v-if="row.failCount>0" type="error" style="margin-right: 10px;"
										@click="exportfiles(row,2)">点击下载</Button>
								</template>
							</Table>
							<div style="margin-top:15px">
								<Page :total="Tasktotal" :current.sync="TaskcurrentPage" show-total show-elevator
									@on-change="TaskgoPage" />
							</div>
						</Form>
					</TabPane>
				</Tabs>
				<div slot="footer">
				</div>
			</Modal>
			<a ref="downloadLink" style="display: none"></a>
			<!-- 模板文件table -->
			<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
			<!-- 导出提示 -->
			<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
				<div style="align-items: center;justify-content:center;display: flex;">
					<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
						<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
						<FormItem label="你本次导出任务ID为:">
							<span style="width: 100px;">{{taskId}}</span>
						</FormItem>
						<FormItem label="你本次导出的文件名为:">
							<span>{{taskName}}</span>
						</FormItem>
						<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
					</Form>
				</div>

				<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
					<Button @click="cancelModal">取消</Button>
					<Button type="primary" @click="Goto">立即前往</Button>
				</div>
			</Modal>
			<!-- 卡管理弹窗 -->
			<Modal :title="title" v-model="cardModal" :mask-closable="true" @on-cancel="cancelModal" width="800px">
				<Form :model="form" :rules="rule" ref="carform" :label-width="100" style="font-size: 600;">
					<div v-show="typeflag">
						<FormItem label="ICCID:">
							<span>{{info.iccid}}</span>
						</FormItem>
					</div>
					<FormItem label="单周期类型上限" prop="dailyTotal">
						<Input v-model="form.dailyTotal" placeholder="单位MB" clearable style="width: 500px"></Input>
					</FormItem>
					<FormItem label="总上限" prop="total">
						<Input v-model="form.total" placeholder="单位MB" clearable style="width: 500px"></Input>
					</FormItem>
					<FormItem label="入池可用时长" prop="availableTime">
						<Input v-model="form.availableTime" placeholder="填写可用的单周期数量" clearable
							style="width: 500px"></Input>
					</FormItem>
					<FormItem label="备注" prop="cardRemark">
						<Input v-model="form.cardRemark" type="textarea" :rows="4" placeholder="填写备注信息" maxlength="200"
							clearable style="width: 500px"></Input>
					</FormItem>
				</Form>
				<div style="margin: 20px;font-weight: bold;" v-show="piflag">
					<div>ICCID:</div>
					<ul>
						<li id="space" v-for="(item,i) in items" :key="items.i">
							{{item}}
						</li>
					</ul>
					<div style="margin: 20px;" v-if="remind">
						<span>……</span>
					</div>
				</div>

				<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
					<Button @click="cancelModal">返回</Button>
					<Button type="primary" @click="besure" :loading="rechargeloading">确定</Button>
				</div>
			</Modal>
		</div>
	</Card>
</template>

<script>
	import {
		channelIccidlist,
		exporticcid,
		Singleimport,
		Batchimport,
		Deleteiccid,
		updateiccid,
		getTaskList,
		exportTaskList,
		stopStatusVCard,
		recoverStatusVCard,
		cardUpadate
	} from "@/api/customer/flowpool";
	import {
		channelflowlist
	} from "@/api/flowpool/flowpool";
	export default {
		data() {
			// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (this.uploadList && this.uploadList.length === 0) {
					callback(new Error('请上传文件'))
				} else {
					callback()
				}
			}
			return {
				typeflag: false,
				piflag: false,
				remind: false,
				title: '',
				sequence: '', //排序顺序
				sortField: '', //排序字段
				chooseiccid:'',
				corpId: '',
				iccid: '',
				cardRemark: '', //备注
				currentRateType: '', //上网状态
				flowPoolStatus: '', //卡片状态
				taskId: '',
				taskName: '',
				total: 0,
				currentPage: 1,
				page: 0,
				Tasktotal: 0,
				TaskcurrentPage: 1,
				Taskpage: 0,
				uploadUrl: '',
				uploadList: [],
				poolList: [],
				selection: [], //多选
				selectionIds: [], //多选ids
				selectionList: [], //翻页勾选List
				iccids: [],
				flowpoolId: '',
				message: this.$t("buymeal.Downloadmsg"),
				loading: false,
				searchloading: false,
				downloading: false,
				importLoading: false,
				importModal: false, //导入弹框标识
				exportModal: false, //导出弹框标识
				cardModal: false, //单卡管理/批量修改标识
				rechargeloading: false,
				modelData: [{
					'ICCID': '********',
					'控制逻辑[1：达量继续使用 2：达量限速 3：达量停用]': '********',
					'单周期类型上限(MB)': '********',
					'总上限(MB)': '********',
					'入池使用时长': '********',
				}, ],
				modelColumns: [{
						title: 'ICCID',
						key: 'ICCID'
					}, // 列名根据需要添加
					{
						title: '控制逻辑[1：达量继续使用 2：达量限速 3：达量停用]',
						key: '控制逻辑[1：达量继续使用 2：达量限速 3：达量停用]',
					}, // 列名根据需要添加
					{
						title: '单周期类型上限(MB)',
						key: '单周期类型上限(MB)'
					}, // 列名根据需要添加
					{
						title: '总上限(MB)',
						key: '总上限(MB)'
					}, // 列名根据需要添加
					{
						title: '入池使用时长',
						key: '入池使用时长'
					}, // 列名根据需要添加

				],
				form: {
					flowpoolid: '',
					flowPoolName: '',
					iccid: '',
					singlecycle: '',
					totalcap: '',
					total: '',
					controllogic: '',
					availableTime: '',
					dailyTotal: '',
					cardRemark: '',
				},
				formobj: {
					flowpoolid: '',
				},
				file: null,
				columns: [{
						type: 'selection',
						width: 60,
						align: 'center'
					}, {
						title: "ICCID",
						key: 'iccid',
						minWidth: 180,
						align: 'center',
						tooltip: true,
						sortable: 'custom'
					},
					{
						title: "已用流量(MB)",
						key: 'usedFlow',
						minWidth: 130,
						align: 'center',
						sortable: 'custom'
					},
					{
						title: "上网状态",
						key: 'currentRateType',
						minWidth: 150,
						align: 'center',
						sortable: 'custom',
						render: (h, params) => {
							const row = params.row;
							const text = row.currentRateType === '1' ? '正常' :
								row.currentRateType === '2' ? '单卡周期达量限速' :
								row.currentRateType === '3' ? '单卡周期达量停用' :
								row.currentRateType === '4' ? '单卡总量达量限速' :
								row.currentRateType === '5' ? '单卡总量达量停用' :
								row.currentRateType === '6' ? '流量池总量达量限速' :
								row.currentRateType === '7' ? '流量池总量达量停用' : '';
							return h('label', text);
						}
					},
					{
						title: "卡片状态",
						key: 'flowPoolStatus',
						minWidth: 120,
						align: 'center',
						sortable: 'custom',
						render: (h, params) => {
							const row = params.row;
							var text = row.flowPoolStatus === '1' ? '正常' :
								row.flowPoolStatus === '2' ? '暂停' : '';
							return h('label', text);
						}
					},
					{
						title: "单周期类型上限(MB)",
						key: 'dailyTotal',
						minWidth: 170,
						align: 'center',
						sortable: 'custom'
					},
					{
						title: "总上限(MB)",
						key: 'total',
						minWidth: 120,
						align: 'center',
						sortable: 'custom'
					},
					{
						title: "控制逻辑",
						key: 'rateType',
						minWidth: 120,
						align: 'center',
						sortable: 'custom',
						render: (h, params) => {
							const row = params.row;
							const text = row.rateType === '1' ? "达量继续使用" : row.rateType === '2' ? "达量限速" :
								row.rateType === '3' ? "达量停用" : "";
							return h('label', text);
						}
					},
					{
						title: "入池时间",
						key: 'intoPoolTime',
						minWidth: 150,
						align: 'center',
						sortable: 'custom'
					},
					{
						title: "可用时长",
						key: 'availableTime',
						minWidth: 150,
						align: 'center',
						sortable: 'custom',
						render: (h, params) => {
							const row = params.row;
							const text = row.availableTime === null ? " \\ " : row.availableTime;
							return h('label', text);
						}
					},
					{
						title: "到期时间",
						key: 'expiration',
						minWidth: 150,
						align: 'center',
					},
					{
						title: "备注",
						key: 'cardRemark',
						minWidth: 150,
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							let text = row.cardRemark === null ? "无" : row.cardRemark;
							if (text.length > 8) {
								text = text.substring(0, 8) + "...";
								return h('div', [h('Tooltip', {
										props: {
											placement: 'bottom',
											transfer: true
										},
										style: {
											cursor: 'pointer',
										},
									},
									[
										text,
										h('label', {
												slot: 'content',
												style: {
													whiteSpace: 'normal',
													wordBreak: 'break-all' //超出隐藏
												},
											},
											row.cardRemark
										)
									])]);
							} else {
								text = text;
								return h('label', text);
							}
						}
					},
					{
						title: "操作",
						slot: 'action',
						minWidth: 350,
						align: 'center',
						fixed: 'right',
					},
				],
				data: [],
				taskdata: [],
				columnsTask: [{
						title: "批量导入时间",
						key: 'createTime',
						minWidth: 200,
						align: 'center'
					},
					{
						title: "导入总数",
						key: 'importCount',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "成功数量",
						key: 'successCount',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "失败数量",
						key: 'failCount',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "成功文件",
						slot: 'success',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "失败文件",
						slot: 'fail',
						minWidth: 120,
						align: 'center'
					},
				],
				rule: {
					flowpoolid: [{
						required: true,
						message: '请选择流量池',
						trigger: 'change',
					}],
					iccid: [ {
						required: true,
						message: '请输入ICCID',
						trigger: 'blur',
					},{
						pattern: /^[^\s]+(\s+[^\s]+)*$/,
						trigger: "blur",
						message: '不允许输入空格',
					}],
					singlecycle: [{
						required: true,
						message: '请输入单周期类型上限',
						trigger: 'blur',
					}, {
						validator: (rule, value, cb) => {
							var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value) || "";
						},
						// pattern: /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/,
						message: '最高支持8位整数和2位小数的正数或零',
						trigger: 'blur',
					}],
					dailyTotal: [{
						// required: true,
						// message: '请输入单周期类型上限',
						// trigger: 'blur',
					}, {
						// validator: (rule, value, cb) => {
						// 	var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
						// 	return str.test(value) || "";
						// },
						pattern: /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/,
						message: '最高支持8位整数和2位小数的正数或零',
						trigger: 'blur',
					}],
					totalcap: [{
						required: true,
						message: '请输入总上限',
						trigger: 'blur',
					}, {
						validator: (rule, value, cb) => {
							var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value) || "";
						},
						// pattern: /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/,
						message: '最高支持8位整数和2位小数的正数或零',
						trigger: 'blur',
					}],
					total: [{
						// required: true,
						// message: '请输入总上限',
						// trigger: 'blur',
					}, {
						// validator: (rule, value, cb) => {
						// 	var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
						// 	return str.test(value) || "";
						// },
						pattern: /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/,
						message: '最高支持8位整数和2位小数的正数或零',
						trigger: 'blur',
					}],
					controllogic: [{
						required: true,
						message: '请选择控制逻辑',
						trigger: 'change',
					}],
					availableTime: [{
						// validator: (rule, value, cb) => {
						// 	var str = /^[1-9]\d*$/;
						// 	return str.test(value) || value == "";
						// },
						pattern: /^[1-9]\d*$/,
						message: '请输入正整数',
						trigger: 'blur',
					}],
					// cardRemark: [{
					// 	validator: (rule, value, cb) => {
					// 		var str = "^.{6}$";
					// 		return str.test(value)
					// 	},
					// 	message: '超过200字符',
					// }]
				},
				info: {},
				items: [],
				iccidlist: [],

				ruleobj: {
					flowpoolid: [{
						required: true,
						message: '请选择流量池',
						trigger: 'change',
					}],
					file: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
				}

			}
		},
		mounted() {
			// 保存上一页返回数据
			localStorage.setItem("flowList", decodeURIComponent(this.$route.query.flowList))
			let list = JSON.parse(decodeURIComponent(this.$route.query.list))
			this.flowpoolId = list.flowPoolId
			this.form.flowPoolName = list.flowPoolName
			this.corpId = JSON.parse(decodeURIComponent(this.$route.query.corpId))
			this.goPageFirst(1)
			this.getTaskList(1)
			this.getflow()
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				channelIccidlist({
					pageSize: 10,
					pageNum: page,
					flowPoolId: this.flowpoolId,
					ICCID: this.iccid,
					cardRemark: this.cardRemark,
					currentRateType: this.currentRateType,
					flowPoolStatus: this.flowPoolStatus,
					sequence: this.sequence,
					sortField: this.sortField
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						var data = res.data;
						let List = []
						// 循环遍历data
						data.map((value, index) => {
							List.push(value)
						})
						//回显
						this.selectionList.forEach(item => {
							List.forEach(element => {
								if (element.iccid == item.iccid) {
									this.$set(element, '_checked', true)
								}
							})
						})
						this.data = List
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			goPage: function(page) {
				this.goPageFirst(page)
			},
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			getTaskList: function(page) {
				var _this = this
				getTaskList({
					pageSize: 10,
					pageNum: page,
					flowPoolId: this.flowpoolId,
				}).then(res => {
					if (res.code == '0000') {
						_this.taskloading = false
						this.Taskpage = page
						this.TaskcurrentPage = page
						this.Tasktotal = res.count
						this.taskdata = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.taskloading = false
				})
			},
			TaskgoPage: function(page) {
				this.getTaskList(page)
			},
			exportFile: function() {
				this.downloading = true
				exporticcid({
					pageSize: -1,
					pageNum: -1,
					flowPoolId: this.flowpoolId,					
					ICCID: this.iccid,
					cardRemark: this.cardRemark,
					currentRateType: this.currentRateType,
					flowPoolStatus: this.flowPoolStatus,
					userId: this.$store.state.user.userId,
					exportType: 1
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.taskId
					this.taskName = res.data.taskName
					this.downloading = false
				}).catch(() => this.downloading = false)
			},
			exportfiles: function(row, type) {
				this.exporting = true
				exportTaskList({
					id: row.id,
					type: type
				}).then(res => {
					const content = res.data
					var fileName = '' // 导出文件名
					if (type === 1) {
						fileName = '成功文件.csv'
					}
					if (type === 2) {
						fileName = '失败文件.csv'
					}
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content, fileName)
					}
				}).catch(err => this.exporting = false)
			},
			cardItem: function(data) {
				this.title = "单卡管理"
				this.cardModal = true
				this.info.iccid = data.iccid
				this.typeflag = true
				this.piflag = false
				this.chooseiccid=1
			},
			deleteItem: function(row) {
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						this.iccids = []
						this.iccids.push(row.iccid)
						Deleteiccid({
							corpId: this.corpId,
							flowPoolId: this.flowpoolId,
							iccids: this.iccids
						}).then(res => {
							if (res && res.code == '0000') {
								this.goPageFirst(1);
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						})
					}
				});
			},
			//暂停
			stop: function(row) {
				this.$Modal.confirm({
					title: '确认暂停该项？',
					onOk: () => {
						stopStatusVCard({
							iccid: row.iccid,
							flowPoolID: this.flowpoolId,
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.goPageFirst(this.currentPage);
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			//恢复
			active(row) {
				this.$Modal.confirm({
					title: '确认恢复该项？',
					onOk: () => {
						recoverStatusVCard({
							iccid: row.iccid,
							flowPoolID: this.flowpoolId,
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.goPageFirst(this.currentPage);
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			deleteBatch: function() {
				var len = this.iccids.length;
				if (len < 1) {
					this.$Message.warning('请至少选择一条记录')
					return
				}
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						Deleteiccid({
							corpId: this.corpId,
							flowPoolId: this.flowpoolId,
							iccids: this.iccids
						}).then(res => {
							if (res && res.code == '0000') {
								this.iccids = [];
								this.goPageFirst(1);
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						})
					}
				});
			},
			// 批量修改
			updateBatch: function() {
				var len = this.iccids.length;
				this.chooseiccid=2
				if (len < 1) {
					this.$Message.warning('请至少选择一条记录')
					return
				} else {
					this.cardModal = true
					this.title = "批量修改"
					this.piflag = true
					this.typeflag = false
					this.items = this.iccids
					if (len > 10) {
						this.items = this.iccids.slice(0, 10)
						this.remind = true
					}
				}
			},
			//多选
			handleRowChange: function(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					
					let flag = true;
					this.selectionList.map((item, index) => {
						if (value.iccid === item.iccid) {
							flag = false
						}
					});
					if (flag) {						
						this.selectionList.push(value)
						this.iccids.push(value.iccid)
					}
				});
			},

			// 取消全选
			cancelAll(selection, row) {
				this.selection = []
				this.selectionList = []
				this.iccids = []
			},
			//取消单选
			cancelSigle(selection, row) {
				this.selectionList.forEach((value, index) => {
					if (value.iccid === row.iccid) {
						this.selectionList.splice(index, 1);
						this.iccids.splice(index, 1);					
					}
				})
			},
			//取消
			cancelModal: function() {
				this.exportModal = false
				this.importModal = false
				this.cardModal = false
				this.file = ''
				this.$refs['form'].resetFields()
				this.$refs['formobj'].resetFields()
				this.$refs['carform'].resetFields()
				this.form.dailyTotal = ''
				this.form.total = ''
				this.form.availableTime = ''
				this.form.cardRemark = ''
				this.iccidlist = []
				this.remind = false
			},
			importIccid: function() {
				this.importModal = true
			},
			//单个导入
			confirmone: function() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.importLoading = true
						let channelCardFlowAddVO = {
							controlLogic: this.form.controllogic,
							dailyTotal: this.form.singlecycle,
							flowPoolTotal: this.form.totalcap,
							availableTime: this.form.availableTime,
							iccid: this.form.iccid,
							orderChannel: 114,
							poolId: this.flowpoolId
						}
						Singleimport(channelCardFlowAddVO).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.goPageFirst(1)
								this.cancelModal()
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						}).finally(() => {
							this.importLoading = false
						})
					}
				})

			},
			//批量导入
			confirmbatch: function() {
				this.$refs["formobj"].validate(valid => {
					if (valid) {
						this.importLoading = true
						let formData = new FormData()
						formData.append('file', this.file)
						formData.append('poolID ', this.flowpoolId)
						Batchimport(formData).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.goPageFirst(1)
								this.getTaskList(1)
								this.cancelModal()
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						}).finally(() => {
							this.importLoading = false
						})
					}
				})
			},
			//模板下载
			downloadFile: function() {
				this.$refs.modelTable.exportCsv({
					filename: "iccidList",
					// type:'xlsx',
					columns: this.modelColumns,
					data: this.modelData
				})
			},
			back: function() {
				this.$router.push({
					path: '/channelflowlist',
					query: {
						obj: encodeURIComponent(JSON.stringify(JSON.parse(decodeURIComponent(this.$route.query
							.obj)))),
						ObjList: encodeURIComponent(JSON.stringify(JSON.parse(decodeURIComponent(this.$route
							.query.ObjList)))),
					}
				})
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
						// corpId: encodeURIComponent(this.corpId)
					}
				})
				this.exportModal = false
			},
			/**
			 * 文件上传
			 */
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			handleBeforeUpload(file, fileList) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: this.$t("buymeal.fileformat"),
						desc: file.name + this.$t("buymeal.incorrect")
					})
				} else {
					if (file.size > 5 * 1024 * 1024) {
						this.$Notice.warning({
							title: this.$t("buymeal.Filesize"),
							desc: file.name + this.$t("buymeal.Exceeds")
						})
					} else {
						this.file = file
						this.uploadList = fileList
					}
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			removeFile: function() {
				this.file = ''
			},
			//获取流量池
			getflow: function() {
				channelflowlist({
					pageNum: -1,
					pageSize: -1,
					corpId: this.corpId,
				}).then(res => {
					if (res.code == '0000') {
						this.poolList = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {

				})
			},
			// 单卡管理/批量修改 确定
			besure() {
				this.$refs["carform"].validate(valid => {
					if (valid) {
						this.rechargeloading = true
						if (this.chooseiccid===2) {
							this.iccidlist = this.iccids
						} else {
							this.iccidlist.push(this.info.iccid)
						}
						cardUpadate({
							availableTime: this.form.availableTime,
							cardRemark: this.form.cardRemark,
							dailyTotal: this.form.dailyTotal,
							iccid: this.iccidlist,
							total: this.form.total,
						}).then(res => {
							if (res.code === '0000') {
								let data = res.data
								this.$Notice.success({
									title: '操作成功',
									desc: '操作成功'
								})
								this.rechargeloading = false
								this.goPageFirst(1)
								this.cardModal = false
								this.cancelModal()
								this.selectionList = []
								this.iccids = []
							}
						}).catch((error) => {
							this.rechargeloading = false
							console.log(error)
						}).finally(() => {
							this.rechargeloading = false
						})
					}
				})
			},

			// 排序
			changeSort: function(data) {
				this.sequence = data.order === "asc" ? 1 : 2
				this.sortField = data.key
				this.goPageFirst(1)
			}

		},
	}
</script>

<style>
	#space {
		height: 25px;
		line-height: 25px;
		font-size: 12px;
		white-space: pre-line;
		list-style: none;
	}
</style>
