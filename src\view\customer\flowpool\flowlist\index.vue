<template>
	<!-- 流量池列表 -->
	<Card>
		<div>
			<span style="margin-top: 4px;font-weight:bold;">渠道商:</span>&nbsp;&nbsp;
			<span>{{corpName}}</span>&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">客户类型:</span>&nbsp;&nbsp;
			<span>{{corpType}}</span>
		</div>
		<div style="display: flex;margin-top: 20px;">
			<span style="margin-top: 4px;font-weight:bold;">流量池名称:</span>&nbsp;&nbsp;
			<Input v-model="searchObj.flowpoolname" placeholder="请输入流量池名称"  clearable style="width: 200px" ></Input>&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">使用状态:</span>&nbsp;&nbsp;
			<Select filterable v-model="searchObj.usestatus"  :clearable="true"  placeholder="请选择使用状态" style="width: 200px ;margin-right: 10px;">
			  <Option  :value="1" >达量限速</Option>
			  <Option  :value="2" >达量停用</Option>
			  <Option  :value="3" >正常</Option>
			</Select>&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">上架状态:</span>&nbsp;&nbsp;
			<Select filterable v-model="searchObj.shelfstatus"  :clearable="true"  placeholder="请选择上架状态" style="width: 200px ;margin-right: 10px;">
			  <Option  :value="1" >上架</Option>
			  <Option  :value="2" >下架</Option>
			</Select>&nbsp;&nbsp;&nbsp;&nbsp;
			<Button  v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search()">搜索</Button>
			<Button  v-has="'export'" style="margin: 0 2px;margin-left: 20px;" icon="ios-cloud-download-outline" type="success" :loading="downloading" @click="exportFile" >
			  导出
			</Button>
			<Button style="margin: 0 4px" @click="back">
				<Icon type="ios-arrow-back" />&nbsp;返回
			</Button>
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'iccidlist'" type="warning" ghost style="margin-right: 10px;" @click="getIccid(row)">ICCID列表</Button>
				<Button :disabled ="row.useStatus==='1'"  v-has="'recharge'" type="primary" ghost style="margin-right: 10px;" @click="recharge(row)">流量充值</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div  style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 流量充值弹窗 -->
		<Modal title="流量充值" v-model="rechargeModal" :mask-closable="true" @on-cancel="cancelModal" >
			<Form :model="form"  :rules="rule" ref="form" >
				<FormItem label="流量池名称:" >
					<span>{{form.poolname}}</span>
				</FormItem>
				<FormItem label="充值额度:" prop="recharge">
					<Input v-model="form.recharge" placeholder="单位GB"  clearable style="width: 300px" >
						 <span slot="append">GB</span>
					</Input>
				</FormItem>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
			  <Button @click="cancelModal">返回</Button>
			  <Button type="primary" @click="Confirm" :loading="rechargeloading">确定</Button>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal  v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
		  <div style="align-items: center;justify-content:center;display: flex;">
			  <Form  label-position="left"  :label-width="150" style=" align-items: center;justify-content:center;" >
			   		  <h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					  <FormItem label="你本次导出任务ID为:">
						<span style="width: 100px;">{{taskId}}</span>
			   		  </FormItem>
			   		  <FormItem label="你本次导出的文件名为:">
						<span>{{taskName}}</span>
			   		  </FormItem>
					  <span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
			   </Form>
		  </div>
		
		  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
		    <Button @click="cancelModal">取消</Button>
		    <Button type="primary" @click="Goto">立即前往</Button>
		  </div>
		</Modal>
	</Card>
</template>

<script>
	import {
		flowlist,
		exportflow,
		recharge
	} from "@/api/customer/flowpool";
	import {
		getStoreByCorpId
	} from "@/api/flowpool/flowpool";
	export default{
		data(){
			return{
				corpName:'',
				corpType:'',
				corpId:'',
				form:{
					poolname:'',
					flowPoolId :'',
					recharge:'0',
				},
				searchObj:{
					flowpoolname:'',
					usestatus:'',
					shelfstatus:'',
				},
				total: 0,
				currentPage: 1,
				page: 0,
				taskId:'',
				taskName:'',
				loading:false,
				searchloading:false,
				downloading:false,
				rechargeloading:false,
				rechargeModal:false,//提醒阈值弹窗标识
				exportModal:false,//导出弹窗标识
				objlist:{},
				columns:[{
					title: "流量池名称",
					key: 'flowPoolName',
					minWidth: 120,
					align: 'center',
					tooltip: true 
				},
				{
					title: "使用状态",
					key: 'useStatus',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  const text = row.useStatus==='1' ? "达量限速":row.useStatus==='2' ? "达量停用"
					  :row.useStatus==='3' ? "正常":"";
					  return h('label', text);
					}
				},
				{
					title: "上架状态",
					key: 'shelfStatus',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  const text = row.shelfStatus==='1' ? "上架":row.shelfStatus==='2' ? "下架":"";
					  return h('label', text);
					}
				},
				{
					title: "总流量(GB)",
					key: 'flowPoolTotal',
					minWidth: 120,
					align: 'center'
				},
				{
					title: "已用流量(GB)",
					key: 'usedFlow',
					minWidth: 120,
					align: 'center'
				},
				{
					title: "卡号数量",
					key: 'cardCount',
					minWidth: 120,
					align: 'center'
				},
				{
					title: "重置周期类型",
					key: 'cycleType',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  const text = row.cycleType==='1' ? "24小时":row.cycleType==='2' ? "自然日"
					  :row.cycleType==='3' ? "自然月":row.cycleType==='4' ? "自然年":"";
					  return h('label', text);
					}
				},
				{
					title: "重置周期数",
					key: 'cycleNum',
					minWidth: 120,
					align: 'center'
				},
				{
					title: "控制逻辑",
					key: 'controlLogic',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  const text = row.controlLogic==='1' ? "达量限速":row.controlLogic==='2' ? "达量停用"
					  :row.controlLogic==='3' ? "达量继续使用":"";
					  return h('label', text);
					}
				},
				{
					title: "有效日期",
					key: 'effectiveDate',
					minWidth: 280,
					align: 'center',
				},
				{
					title: "支持国家",
					key: 'supportCountry',
					minWidth: 200,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  let text ='';
					  row.supportCountry.map((country)=>{
						  text=text === "" ? (text + '' + country): (text + ', ' + country)
					  })
					  let length=text===""||text===null ? 0:text.length
					  if (length > 8) { //进行截取列显示字数
					    let country=text.replace(/\|/g,"</br>")
					    text = text.substring(0, 8) + "..."
					    return h('div', [h('Tooltip', {
					        props: {
					          placement: 'bottom',
					          transfer: true //是否将弹层放置于 body 内
					        },
					        style: {
					          cursor: 'pointer',
					        },
					      },
					      [ //这个中括号表示是Tooltip标签的子标签
					        text, //表格列显示文字
					        h('label', {
					            slot: 'content',
					            style: {
					              whiteSpace: 'normal',
					            },
					          },
							  country
					        )
					      ])]);
					  } else {
					    text = text;
					    return h('label', text)
					  }
					}
				},
				{
					title: "操作",
					slot: 'action',
					minWidth: 250,
					align: 'center',
					fixed: 'right',
				},
				],
				data:[],
				rule:{
					recharge:[
						{required:true,message:'请输入充值额度',trigger:'blur'},
						{
							validator: (rule, value, cb) => {
								var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
								return str.test(value);
							},
							message: '最高支持8位整数和2位小数的正数或零',
						}
					]
				}
				
			}
		},
		mounted() {
			// 保存上一页返回数据
			// let ObjList = JSON.parse(localStorage.getItem("ObjList")) === "undefined" ? '' : JSON.parse(localStorage.getItem(
			// 	"ObjList"))
			// if (!ObjList) {
			// 	localStorage.setItem("ObjList", decodeURIComponent(this.$route.query.ObjList))
			// }
			localStorage.setItem("ObjList", decodeURIComponent(this.$route.query.ObjList))
			//缓存数据
			let flowList = JSON.parse(localStorage.getItem("flowList")) === null ? '' : JSON.parse(localStorage.getItem(
				"flowList"))
			if (flowList) {
				this.searchObj.flowpoolname = flowList.flowpoolname === undefined ? "" : flowList.flowpoolname
				this.searchObj.usestatus = flowList.usestatus === undefined ? "" : flowList.usestatus
				this.searchObj.shelfstatus = flowList.shelfstatus === undefined ? "" : flowList.shelfstatus
			}
			this.objlist= JSON.parse(decodeURIComponent(this.$route.query.obj))
			//获取渠道商名称
			this.getcorpName(this.objlist.corpId)
			this.corpType= this.objlist.type
			// 处理客户类型
			this.corpType = this.corpType === '1' ? "渠道商" : this.corpType === '3' ? "合作商" : this.corpType === '4' ? "后付费": "";
			this.corpId=this.objlist.corpId
			this.goPageFirst(1)
			//清除缓存
			localStorage.removeItem("flowList")
		},
		methods:{
			goPageFirst:function(page){
				this.loading = true
				var _this = this
				flowlist({
					pageSize:10,
					pageNum:page,
					flowPoolName:this.searchObj.flowpoolname,
					useStatus:this.searchObj.usestatus,
					shelfStatus:this.searchObj.shelfstatus,
					corpId:this.corpId,
					corpName: this.corpName,
					cooperationMode: 1
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						this.data = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			goPage:function(page){
				this.goPageFirst(page)
			},
			search:function(){
				this.searchloading = true
				this.goPageFirst(1)
			},
			exportFile:function(){
				this.downloading = true
				exportflow({
					pageSize: -1,
					pageNum: -1,
					flowPoolName: this.searchObj.flowpoolname,
					useStatus: this.searchObj.usestatus,
					shelfStatus: this.searchObj.shelfstatus,
					corpId: this.corpId,
					corpName: this.corpName,
					userId: this.$store.state.user.userId,
					exportType: 1
				}).then((res) => {
				  this.exportModal=true
				  this.taskId=res.data.taskId
				  this.taskName=res.data.taskName
				  this.downloading = false
				}).catch(() => this.downloading = false)
			},
			getIccid:function(row){
				this.$router.push({
				  path:'/channeliccidlist',
				  query: {
				    flowList: encodeURIComponent(JSON.stringify(this.searchObj)),
					list:encodeURIComponent(JSON.stringify(row)),
					obj:encodeURIComponent(JSON.stringify(this.objlist)),
					corpId:encodeURIComponent(JSON.stringify(this.corpId)),
					ObjList:encodeURIComponent(JSON.stringify(JSON.parse(decodeURIComponent(this.$route.query.ObjList)))),
				  }
				})
			},
			recharge:function(row){
				this.rechargeModal=true
				this.form.poolname=row.flowPoolName
				// this.form.recharge=row.recharge
				this.form.flowPoolId=row.flowPoolId
			},
			cancelModal:function(){
				this.rechargeModal=false
				this.exportModal=false
				this.$refs['form'].resetFields()
			},
			Confirm:function(){
				this.$refs["form"].validate(valid => {
				  if (valid) {
					this.rechargeloading=true
					recharge({
					  flowValue : this.form.recharge,
					  flowPoolId :this.form.flowPoolId
					}).then(res => {
					  if (res && res.code == '0000') {
						this.goPageFirst(this.page);
					    this.$Notice.success({
					      title: '操作提示',
					      desc: '操作成功'
					    })
						this.cancelModal()
					  } else {
					    throw res
					  }
					}).catch((err) => {
					  return false;
					}).finally(() => {
						this.rechargeloading=false
					})   
				  }
				})
			},
			back:function(){
				this.$router.push({
				  path:'/channelPool',
				  query: {
				    // callListinfo: encodeURIComponent(JSON.stringify(row)),
				  }
				})
			},
			Goto(){
				this.$router.push({
				  path: '/taskList',
				  query: {
					taskId: encodeURIComponent(this.taskId),
					fileName:encodeURIComponent(this.taskName),
					// corpId:encodeURIComponent(this.corpId)
				  }
				})    
				this.exportModal=false
			},
			//获取渠道商名称
			getcorpName(corpId){
				getStoreByCorpId(corpId).then(res => {
					if (res.code == '0000') {
						this.corpName = res.data.corpName
					}	
				}).catch((err) => {
					console.error(err)
				})
			}
		}
		
	}
</script>

<style>
</style>
