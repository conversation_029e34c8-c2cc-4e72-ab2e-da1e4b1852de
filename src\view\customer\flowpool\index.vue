<template>
	<!-- 流量池管理 -->
	<Card>
		<div style="display: flex;margin-top: 20px;">
			<span style="margin-top: 4px;font-weight:bold;">客户名称:</span>&nbsp;&nbsp;
			<Input v-model="searchObj.corpname" placeholder="请输入客户名称" clearable style="width: 200px"></Input>&nbsp;&nbsp;&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">客户类型:</span>&nbsp;&nbsp;
			<Select filterable v-model="searchObj.type" :clearable="true" placeholder="请选择客户类型" style="width: 200px ;margin-right: 10px;">
				<Option :value="1">渠道商</Option>
				<Option :value="3">合作商</Option>
				<Option :value="4">后付费</Option>
			</Select>&nbsp;&nbsp;&nbsp;&nbsp;
			<Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search()">{{$t('order.search')}}</Button>
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data"  style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'cardlist'" type="warning" ghost style="margin-right: 10px;" @click="showiccid(row)">卡号列表</Button>
				<Button v-has="'flowlist'" type="primary" ghost style="margin-right: 10px;" @click="showflowpool(row)">流量池列表</Button>
				<Button v-has="'userecord'" type="success" ghost style="margin-right: 10px;" @click="showuser(row)">使用记录</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
	</Card>
</template>

<script>
	import {
		getPage,
	} from "@/api/customer/flowpool";
	export default {
		data() {
			return {
				total: 0,
				currentPage: 1,
				page: 0,
				loading: false,
				searchloading: false,
				searchObj: {
					corpname: '',
					type: ''
				},
				columns: [{
					title: "客户名称",
					key: 'corpName',
					minWidth: 120,
					align: 'center'
				}, {
					title: "币种",
					key: 'currencyCode',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  var text = "";
					  switch (row.currencyCode) {
					    case "156":
					      text = "人民币";
					      break;
					    case "840":
					      text = "美元";
					      break;
					    case "344":
					      text = "港币";
					      break;
					    default:
					      text = "未知";
					  }
					  return h('label', text);
					},
				}, {
					title: "客户类型",
					key: 'type',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  const text = row.type === '1' ? "渠道商" : row.type === '3' ? "合作商":
					 	row.type === '4' ? "后付费": "";
					  return h('label', text);
					},
				}, {
					title: "操作",
					slot: 'action',
					minWidth: 250,
					align: 'center'
				} ],
				data:[]
			}
		},
		mounted() {
			//缓存数据
			let ObjList = JSON.parse(localStorage.getItem("ObjList")) === null ? '' : JSON.parse(localStorage.getItem(
				"ObjList"))
			if (ObjList) {
				this.searchObj.type = ObjList.type === undefined ? "" : ObjList.type
				this.searchObj.corpname = ObjList.corpname === undefined ? "" : ObjList.corpname
			}
			this.goPageFirst(1)
			//清除缓存
			localStorage.removeItem("ObjList")
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				getPage({
					pageSize: 10,
					pageNum: page,
					corpName: this.searchObj.corpname,
					type: this.searchObj.type,
					cooperationMode: 1
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						this.data = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			goPage: function(page) {
				this.goPageFirst(page)
			},
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			showiccid: function(row) {
				this.$router.push({
					path: '/channelcardlist',
					query: {
						ObjList: encodeURIComponent(JSON.stringify(this.searchObj)),
						obj: encodeURIComponent(JSON.stringify(row)),
					}
				})
			},
			showflowpool: function(row) {
				this.$router.push({
					path: '/channelflowlist',
					query: {
						ObjList: encodeURIComponent(JSON.stringify(this.searchObj)),
						obj: encodeURIComponent(JSON.stringify(row)),
					}
				})
			},
			showuser: function(row) {
				this.$router.push({
					path: '/channeluserecord',
					query: {
						ObjList: encodeURIComponent(JSON.stringify(this.searchObj)),
						obj: encodeURIComponent(JSON.stringify(row)),
					}
				})
			},

		}
	}
</script>

<style>
</style>
