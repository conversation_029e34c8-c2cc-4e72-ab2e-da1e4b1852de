<template>
	<!-- 使用记录 -->
	<Card>
		<div>
			<span style="margin-top: 4px;font-weight:bold;">归属企业:</span>&nbsp;&nbsp;
			<span>{{corpName}}</span>
		</div>
		<div style="display: flex;margin-top: 20px;">
			<Form ref="form" :model="form" :label-width="100" :rules="rule" style="display: flex; flex-wrap: wrap;">
				<FormItem label="流量池名称:">
					<Input v-model="form.flowpoolname" placeholder="请输入流量池名称" clearable
						style="width: 200px"></Input>&nbsp;&nbsp;&nbsp;&nbsp;
				</FormItem>
				<FormItem label="选择起止日期:" prop="date">
					<DatePicker type="daterange" format="yyyy-MM-dd" placement="bottom-end" v-model="form.date"
						style="width: 200px" placeholder="选择日期范围" @on-change="handleDateChange"
						@on-clear="hanldeDateClear"></DatePicker>
				</FormItem>
        <FormItem :label-width="0" style="margin: 0 2px;margin-left: 20px;">
        	<Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading"
        		@click="search()">搜索</Button>
        </FormItem>
        <FormItem :label-width="0">
        	<Button v-has="'export_use'" style="margin: 0 2px;margin-left: 20px;"
        		icon="ios-cloud-download-outline" type="success" :loading="downloading" @click="exportFile">
        		导出
        	</Button>
        </FormItem>
        <FormItem :label-width="0">
        	<Button v-has="'export_flowsum'" style="margin: 0 2px;margin-left: 20px;" icon="ios-arrow-dropdown"
        		type="info" @click="trafficExport">
        		流量汇总导出
        	</Button>
        </FormItem>
        <FormItem :label-width="0" style="margin: 0 2px;margin-left: 20px;">
        	<Button style="margin: 0 4px" @click="back">
        		<Icon type="ios-arrow-back" />&nbsp;返回
        	</Button>
        </FormItem>
			</Form>
		</div>
		<div style="display: flex;margin-top: 20px;">
			<span style="margin-top: 4px;font-weight:bold;">账单日期:</span>&nbsp;&nbsp;
			<span style="margin-top: 4px;font-weight:bold;">{{billdate}}</span>&nbsp;&nbsp;
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button type="warning" ghost style="margin-right: 10px;" @click="showDetails(row)">点击查看</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 使用记录详情弹窗 -->
		<Modal title="使用记录详情" v-model="UsedModal" :mask-closable="true" @on-cancel="cancelModal" width="1000px">
			<span style="margin-top: 4px;font-weight:bold;">流量池名称:</span>
			<span>{{poolname}}</span>
			<Button style="margin: 0 2px;float: right;" icon="ios-cloud-download-outline" type="success"
				:loading="downloading" @click="exportFileDetails">
				导出
			</Button>
			<!-- 表格 -->
			<Table :columns="Usecolumns" :data="Usedata" style="width:100%;margin-top: 40px;" :loading="useloading">
			</Table>
			<!-- 分页 -->
			<div style="margin-top:15px">
				<Page :total="usetotal" :current.sync="usecurrentPage" show-total show-elevator
					@on-change="usegoPage" />
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">返回</Button>
			</div>
		</Modal>
		<!-- 选择下载参数弹窗 -->
		<Modal title="请先选择需要的数据进行导出" v-model="parameterModal" :mask-closable="true" @on-cancel="cancelModal" width="580px">
			<CheckboxGroup v-model="more" style="display: flex; flex-direction: column; flex-wrap: nowrap;">
			    <div style="margin: 15px;display: flex; justify-content: flex-start; align-items: center; flex-wrap: wrap;">
					<Checkbox class="checkbox" label="msisdn">
					    <span>MSISDN(H)</span>
					</Checkbox>
					<Checkbox class="checkbox" label="imsi">
					    <span>IMSI(H)</span>
					</Checkbox>
					<Checkbox class="checkbox" label="iccid">
						<span>ICCID</span>
					</Checkbox>
					<Checkbox class="checkbox" label="totalLimit">
					    <span>Total LIMIT</span>
					</Checkbox>
				</div>
				<div style="margin: 15px;display: flex; justify-content: flex-start; align-items: center; flex-wrap: wrap;">
					<Checkbox class="checkbox" label="remark">
					    <span>Remark</span>
					</Checkbox>
					<Checkbox class="checkbox" label="country">
					    <span>country or region</span>
					</Checkbox>
					<Checkbox class="checkbox" label="totalData">
					    <span>Total Data(MB)</span>
					</Checkbox>
				</div>
			</CheckboxGroup>
			<div slot="footer">
			  	<Button @click="cancelModal">返回</Button>
			    <Button v-has="'export'" type="primary" :loading="besureLoading" @click="handle">确定</Button>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="exportcancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					<FormItem label="你本次导出任务ID为:">
						<span>{{taskId}}</span>
					</FormItem>
					<FormItem label="你本次导出的文件名为:">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="exportcancelModal">取消</Button>
				<Button type="primary" @click="Goto">立即前往</Button>
			</div>
		</Modal>
		<Modal v-model="exportModalr" :mask-closable="true" @on-cancel="exportcancelModalr" width="800px">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					<FormItem label="你本次导出任务ID为:">
						<ul style="margin-bottom: 15px;">
							<li id="space" v-for="(taskId,i) in taskIds" :key="taskIds.i">
								{{taskId}}
							</li>
						</ul>
						<div v-if="remind">
							<span>……</span>
						</div>
					</FormItem>
					<FormItem label="你本次导出的文件名为:">
						<ul style="margin-bottom: 15px;">
							<li id="space" v-for="(taskName,i) in taskNames" :key="taskNames.i">
								{{taskName}}
							</li>
						</ul>
						<div  v-if="remind">
							<span>……</span>
						</div>
					</FormItem>
					<span style="text-align: left;margin-top: 30px;">请前往下载管理-下载列表查看及下载。</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="exportcancelModalr">取消</Button>
				<Button type="primary" @click="Gotor">立即前往</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		UsageList,
		exportUsageList,
		UsageListdetails,
		exportUsagedetails
	} from "@/api/customer/flowpool";
	import {
		getStoreByCorpId,
		exportTraffic
	} from "@/api/flowpool/flowpool";
	export default {
		data() {
			return {
				form: {
					flowpoolname: '',
					// date: '',
					startTime: "",
					endTime: "",
					date: []
				},
				corpId: '',
				corpName: '',
				date: '',
				flowpoolname: '',
				flowPoolUniqueId: '',
				poolname: '',
				corpName: '',
				taskId: '',
				taskName: '',
				total: 0,
				currentPage: 1,
				page: 0,
				usetotal: 0,
				usecurrentPage: 1,
				usepage: 0,
				loading: false,
				searchloading: false,
				downloading: false,
				detailsloading: false,
				useloading: false,
				besureLoading: false,
				UsedModal: false, //使用记录详情弹窗标识
				parameterModal: false,
				exportModal: false, //导出弹框标识
				exportModalr: false, //导出弹框标识
				billdate: '',
				columns: [{
						title: "流量池名称",
						key: 'flowPoolName',
						minWidth: 120,
						align: 'center',
						tooltip: true,
					},
					{
						title: "使用开始时间",
						key: 'startTime',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "使用结束时间",
						key: 'endTime',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "额定总流量(GB)",
						key: 'flowSum',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "实际使用总流量(GB)",
						key: 'useNum',
						minWidth: 130,
						align: 'center'
					},
					{
						title: "超额流量(GB)",
						key: 'extraFlow',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "额定收入(元)",
						key: 'ratedIncome',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "超额收入(元)",
						key: 'extraPrice',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "总收入(元)",
						key: 'totalIncome',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "详情",
						slot: 'action',
						minWidth: 120,
						align: 'center',
						fixed: 'right',
					},
				],
				Usecolumns: [{
						title: "ICCID",
						key: 'iccid',
						minWidth: 80,
						align: 'center',
						tooltip: true,
					},
					{
						title: "HIMSI号码",
						key: 'himsi',
						minWidth: 80,
						align: 'center',
						tooltip: true,
					},
					{
						title: "VIMSI号码",
						key: 'imsi',
						minWidth: 80,
						align: 'center',
						tooltip: true,
					},
					{
						title: "号码类型",
						key: 'cardType',
						minWidth: 50,
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							const text = row.cardType === '1' ? 'H' : row.cardType === '2' ? 'V' : "";
							return h('label', text);
						}
					},
					{
						title: "开始时间",
						key: 'startTime',
						minWidth: 90,
						align: 'center'
					},
					{
						title: "结束时间",
						key: 'endTime',
						minWidth: 90,
						align: 'center'
					},
					{
						title: "用量(MB)",
						key: 'flowCount',
						minWidth: 50,
						align: 'center'
					},
				],
				data: [],
				Usedata: [],
				taskIds: [],
				taskNames: [],
				remind: false,
				rule: {
					// date: [{
					// 	type: 'date',
					// 	required: true,
					// 	message: '请选择月份'
					// }, ],
					date: [{
						type: 'array',
						required: true,
						message: '请选择时间',
						trigger: 'blur',
						fields: {
							0: {
								type: 'date',
								required: true,
								message: '请选择开始日期'
							},
							1: {
								type: 'date',
								required: true,
								message: '请选择结束日期'
							}
						}
					}],
				},
				msisdn: '',
				imsi: '',
				iccid: '',
				totalLimit: '',
				remark: '',
				country: '',
				totalData: '',
				more: [],
				flag: true
			}
		},
		mounted() {
			// 保存上一页返回数据
			localStorage.setItem("ObjList", decodeURIComponent(this.$route.query.ObjList))
			this.corpId = JSON.parse(decodeURIComponent(this.$route.query.obj)).corpId
			//获取渠道商名称
			this.getcorpName(this.corpId)
			// this.goPageFirst(1)
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				UsageList({
					pageSize: 10,
					pageNum: page,
					flowPoolName: this.form.flowpoolname,
					// date: this.date,
					startTime: this.form.startTime === "" ? null : this.form.startTime,
					endTime: this.form.endTime === "" ? null : this.form.endTime,
					corpId: this.corpId,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						this.data = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			goPage: function(page) {
				this.goPageFirst(page)
			},
			search: function() {
				this.$refs["form"].validate(valid => {
					if (valid) {
						this.searchloading = true
						this.goPageFirst(1)
					}
				})
			},
			goPageDetails: function(page) {
				this.useloading = true
				var _this = this
				UsageListdetails({
					pageSize: 10,
					pageNum: page,
					flowPoolUniqueId: this.flowPoolUniqueId,
					corpId: this.corpId,
				}).then(res => {
					if (res.code == '0000') {
						_this.useloading = false
						this.usepage = page
						this.usecurrentPage = page
						this.usetotal = res.count
						this.Usedata = res.data
						this.UsedModal = true
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.useloading = false
				})
			},
			usegoPage: function(page) {
				this.goPageDetails(page)
			},
			// handleDateChange: function(date) {
			// 	console.log(date)
			// 	this.date = date
			// 	if (date) {
			// 		var date = new Date(date)
			// 		this.billmonth = date.getFullYear() + "年" + (date.getMonth() + 1) + "月"
			// 	} else {
			// 		this.billmonth = ""
			// 	}
			// },
			handleDateChange(date) {
				if (Array.isArray(date)) {
					this.form.startTime = date[0];
					this.form.endTime = date[1];
					if (this.form.startTime && this.form.endTime) {
						var date1 = new Date(this.form.startTime)
						var date2 = new Date(this.form.endTime)
						this.billdate = date1.getFullYear() + "年" + (date1.getMonth() + 1) + "月" + date1.getDate() + "日" +
							" -- " + date2.getFullYear() + "年" + (date2.getMonth() + 1) + "月" + date2.getDate() + "日"
					} else {
						this.billdate = ""
					}
				}
			},
			hanldeDateClear() {
				this.form.startTime = ''
				this.form.endTime = ''
			},
			showDetails: function(row) {
				this.flowPoolId = row.flowPoolId
				this.poolname = row.flowPoolName
				this.flowPoolUniqueId = row.flowPoolUniqueId
				this.goPageDetails(1)
				this.UsedModal = true
			},
			//导出使用记录
			exportFile: function() {
				this.downloading = true
				exportUsageList({
					pageSize: -1,
					pageNum: -1,
					flowpoolName: this.form.flowpoolname,
					startTime: this.form.startTime === "" ? null : this.form.startTime,
					endTime: this.form.endTime === "" ? null : this.form.endTime,
					corpId: this.corpId,
					userId: this.$store.state.user.userId,
					exportType: 1,
				}).then((res) => {
					this.exportModal = true
					this.showid = true
					this.showname= true
					this.taskId = res.data.taskId
					this.taskName = res.data.taskName
					this.downloading = false
				}).catch(() => this.downloading = false)
			},
			//选择需要导出的数据
			handle() {
				let a = this.more.includes("remark") == true || this.more.includes("totalLimit")  == true
				let b = this.more.includes("msisdn") == false && this.more.includes("imsi") == false && this.more.includes("iccid") == false
				if (a && b) {
					this.$Notice.error({
						title: "操作提示",
						desc: '请选择msisdn、imsi、iccid其中一项！'
					})
				} else if(this.more.length == 0) {
					this.$Notice.error({
						title: "操作提示",
						desc: '请至少选择一条'
					})
				} else {
					this.besureLoading = true
					exportTraffic({
						corpId: this.corpId,
						userId: this.corpId,
						flowPoolName:this.form.flowpoolname,
						fields: String(this.more),
						startTime: this.form.startTime === "" ? null : this.form.startTime,
						endTime: this.form.endTime === "" ? null : this.form.endTime,
						pageNum: -1,
						pageSize: -1,
					}).then((res) => {
						if (res && res.code == '0000') {
							 this.exportModalr = true
							var _this = this
							if (Object.values(res.data).length > 0) {
								Object.values(res.data).forEach(function(value) {
									_this.taskIds.push(value.id)
									_this.taskNames.push(value.fileName)
									if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
										let taskid =  _this.taskIds.slice(0,3)
										let taskname = _this.taskNames.slice(0,3)
										_this.taskIds = taskid
										_this.taskNames = taskname
										_this.remind = true
									}
								})
							}
							this.parameterModal = false
							this.more = []
							this.besureLoading = false
						}
					}).catch((err) => {
						console.error(err)
					}).finally(() => {
						this.besureLoading = false
					})
				}
			},
			// 流量汇总导出
			trafficExport: function() {
				this.parameterModal = true
				this.besureLoading = false
			},
			//导出使用记录详情
			exportFileDetails: function() {
				this.detailsloading = true
				exportUsagedetails({
					pageSize: -1,
					pageNum: -1,
					flowPoolUniqueId: this.flowPoolUniqueId,
					userId: this.$store.state.user.userId,
					exportType: 1,
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.taskId
					this.taskName = res.data.taskName
					this.detailsloading = false
				}).catch(() => this.detailsloading = false)
			},
			cancelModal: function() {
				this.UsedModal = false
				this.parameterModal = false
				this.more = []
				this.besureLoading = false
			},
			exportcancelModal: function() {
				this.exportModal = false
			},
			exportcancelModalr: function() {
				this.exportModalr = false
				this.taskIds = []
				this.taskNames = []
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
						// corpId: encodeURIComponent(this.corpId),
					}
				})
				this.exportModal = false
				this.UsedModal = false
			},
			Gotor() {
				this.$router.push({
					path: '/taskList',
					query: {
						// corpId: encodeURIComponent(this.corpId),
					}
				})
				this.exportcancelModalr()
				this.UsedModal = false
			},
			back: function() {
				this.$router.push({
					path: '/channelPool',
					query: {
						// callListinfo: encodeURIComponent(JSON.stringify(row)),
					}
				})
			},
			//获取渠道商名称
			getcorpName(corpId) {
				getStoreByCorpId(corpId).then(res => {
					if (res.code == '0000') {
						this.corpName = res.data.corpName
					}
				}).catch((err) => {
					console.error(err)
				})
			}
		}

	}
</script>

<style>
	#space {
		/* height: 30px;
		line-height: 30px; */
		font-size: 12px;
		white-space: pre-line;
		list-style: none;
	}
	.checkbox {
		width: 115px;
	}
</style>
