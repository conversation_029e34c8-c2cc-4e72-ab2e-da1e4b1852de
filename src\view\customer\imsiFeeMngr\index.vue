<template>
  <!-- IMSI费管理 -->
	<Card style="width: 100%; padiing: 16px">
		<Form ref="searchForm" :model="searchObj" inline @submit.native.prevent :label-width="80"
			style="margin: 30px 0">
			<FormItem label="规则名称" style="font-weight: bold;">
				<Input v-model.trim="searchObj.ruleName" placeholder="请输入规则名称" clearable style="width: 200px" />
			</FormItem>
			<FormItem>
				<Button style="margin: 0 2px" type="info" @click="search" :loading="searchloading" v-has="'search'">
					<Icon type="ios-search" />&nbsp;搜索
				</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button style="margin: 0 2px" type="primary" @click="addItem" v-has="'add'">
        	<Icon type="ios-add" />&nbsp;新增
        </Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
        <template slot-scope="{ row, index }" slot="action">
					<Button type="primary" ghost size="small" style="margin-right: 20px" @click="updateItem(row)"
             v-has="'update'">修改</Button>
					<Button type="info" ghost size="small" style="margin-right: 20px" @click="viewItem(row)"
             v-has="'detail'">详情</Button>
          <Button type="warning" ghost size="small" @click="deleteItem(row)"
             v-has="'delete'">删除</Button>
				</template>
			</Table>
			<!-- <Page :total="total" :page-size="pageSize" :current.sync="currentPage" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0" /> -->
		</div>
		<!-- 新建、修改、详情 IMSI费规则弹窗 -->
		<Modal :title="title" v-model="ruleModal" :footer-hide="true" :mask-closable="false" width="1000px"
			@on-cancel="cancelModal">
			<div style="padding: 0 16px">
				<Form ref="addRule" :model="addRule" :rules="ruleValidate" label-position="left">
          <Row>
            <Col span="24">
              <FormItem label="IMSI费规则" prop="ruleName">
              	<Input v-model.trim="addRule.ruleName" clearable :disabled="pageType == '2' || pageType == '3'" placeholder="请输入规则名称" class="imsiInputSty" ></Input>
              </FormItem>
            </Col>
          </Row>
          <Spin size="large" fix v-if="spinShow"></Spin>
          <div style="display: flex; flex-wrap: nowrap; flex-direction: column; margin-top: 20px;">
          	<div v-for="(item,index) in addRule.details" :key="index">
          		<Row justify="space-around" class="code-row-bg">
          		  <Col span="8">
                  <FormItem label="IMSI费金额" :prop="'details.' + index + '.amount'" :rules="ruleValidate.amount">
                  	<Input v-model.trim="item.amount" :clearable="true" :disabled="pageType == '3'" placeholder="请输入IMSI费金额" class="imsiInputSty"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem label="数量区间" :prop="'details.' + index + '.begin'" :rules="ruleValidate.begin">
                  	<Input v-model.trim="item.begin" type="number" :clearable="true" disabled placeholder="请输入起始区间" class="imsiInputSty"></Input>
                  </FormItem>
                </Col>
                <Col span="8">
                  <FormItem :prop="'details.' + index + '.end'" :rules="ruleValidate.end">
                  	<Input v-model.trim="item.end" :clearable="true"
                      :disabled="index == (addRule.details.length - 1) || pageType == '3'" placeholder="请输入结束区间" class="imsiInputSty"></Input>
                    <Button type="error" size="small" style="margin-left: 10px; margin-top: 2px; width: 70px; height: 25px;" :disabled="pageType == '3'"
                      @click="removeRule(index)"  v-if="index != 0">删除档位</Button>
                  </FormItem>
                </Col>
              </Row>
          	</div>
          	<div style="display: flex; justify-content: flex-end; margin-top: 5px; margin-right: 30px;">
          		<Button type="info" size="small" @click="addRuleHandle" :disabled="pageType == '3'" style="width: 70px;">添加档位</Button>
          	</div>
          </div>
				</Form>
				<div style="text-align: center; margin-top: 30px;">
					<Button @click="cancelModal">返回</Button>
					<Button style="margin-left: 20px" :loading="submitFlag" type="primary" @click="submit" v-if="pageType != '3'">确定</Button>
				</div>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		queryImsiFee,
    addImsiFee,
    updateImsiFee,
    delImsiFee,
    detailImsiFee,
	} from "@/api/customer/imsiFeeMngr.js";
  import Vue from 'vue';
  export default {
		components: {},
		data() {
      const validateEnd = (rule, value, callback) => {
        const matches = rule.field.match(/details\.(\d+)\.end/);
        if (!matches) {
          callback(new Error('Field format is incorrect'));
          return;
        }
        const currentIndex = parseInt(matches[1], 10);
        const detailsArray = this.addRule.details;
        const isLastItem = currentIndex === detailsArray.length - 1;

        if (isLastItem) {
          // 如果是最后一个元素，直接跳过所有校验
          callback();
          return;
        }

        // 对非最后一个元素进行校验
        if (!value || value === '无上限') {
          // 如果value为空或'无上限'，则判断为校验失败
          callback(new Error('结束区间不能为空且不能是"无上限"'));
        } else if (!/^\d+$/.test(value)) {
          // 校验value是否是一个正整数（不含0开头的多位数和负数）
          callback(new Error('请输入正整数'));
        } else if (parseInt(detailsArray[currentIndex].begin, 10) >= parseInt(value, 10)) {
          // 校验结束区间是否大于起始区间
          callback(new Error('结束区间不能小于或等于起始区间'));
        } else if (parseInt(value, 10) > 999999997) {
          // 校验结束区间是否小于9999999998
          callback(new Error('结束区间不能大于等于 9999999998'));
        } else {
          // 所有校验通过
          callback();
        }
      };
			return {
				searchObj: {
					ruleName: "", //规则名称
				},
        title: "",
				total: 0,
				pageSize: 10,
				page: 1,
				currentPage: 1,
        index: 0,
        id: "",
        pageType: '',
				loading: false,
				searchloading: false,
				submitFlag: false,
				ruleModal: false, //新建规则弹窗
        spinShow: false,
        addRule: {
          ruleName: '',
          details: [{
            amount: '',
            begin: '1', // 初始化为1
            end: '无上限',
          }]
        },
				tableData: [],
				columns: [{
						title: "规则名称",
						key: "ruleName",
						align: "center",
						minWidth: 150,
						tooltip: true
					},
					{
						title: "关联渠道商",
            key: "corpName",
						align: "center",
						minWidth: 150,
						tooltip: true,
            render: (h, params) => {
              const row = params.row;
              let lastTent = "..."

              let manyCorpName = [...new Set(this.tableData[params.index].corpName)]

              if (!manyCorpName || manyCorpName.length === 0) {
                return h('span', '');
              }

              if (manyCorpName.length === 1) {
                return h('span', manyCorpName[0]);
              }

              if (manyCorpName.length === 2) {
                // 直接显示两个元素，不需要 Tooltip
                return h('div', [
                  h('div', manyCorpName[0]),
                  h('div', manyCorpName[1]),
                ]);
              }
              // 当有超过两个元素时，使用 Tooltip 显示前两个元素，并在 Tooltip 中显示所有元素
              return h('div', [
                h('Tooltip', {
                  props: {
                    placement: 'bottom',
                    transfer: true,
                  },
                  style: {
                    cursor: 'pointer',
                  },
                }, [
                  h('span', {
                    style: {
                      display: 'block',
                    },
                  }, manyCorpName[0]),
                  h('span', {}, manyCorpName[1]),
                  h('div', {}, lastTent),
                  h('ul', {
                    slot: 'content',
                    style: {
                      listStyleType: 'none',
                      whiteSpace: 'normal',
                      wordBreak: 'break-all' ,//超出隐藏
                    },
                  },
                    manyCorpName.map(item => { // 从第三个元素开始映射
                      return h('li', item);
                    })),
                  ]),
                ]);
            }
					},
          {
          	title: "操作",
            slot: "action",
            align: "center",
          	minWidth: 300,
          },
				],
				ruleValidate: {
          ruleName: [{
          	required: true,
          	message: "IMSI费规则名称不能为空",
          	trigger: "change",
          } ],
          amount: [{
						validator: (rule, value, cb) => {
						  // 如果 value 是空字符串、undefined 或者 null，则不进行校验
						  if (!value || value === '') {
						    return cb(); // 直接返回，不传入错误信息
						  }
						  // 正则表达式用于匹配最多8位整数和2位小数
						  var str = /^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/;
						  // 使用正则表达式进行校验
						  if (str.test(value)) {
						    return cb(); // 校验成功，直接返回，不传入错误信息
						  } else {
						    // 校验失败，返回错误信息
						    return cb(new Error('最高支持8位整数和2位小数正数或零'));
						  }
						},
						trigger: 'blur', // 或者 'change'，取决于你希望何时触发校验
						message: '最高支持8位整数和2位小数正数或零' // 错误提示信息
					}],
          begin: [{
          	required: true,
          	message: "开始区间不能为空",
          	trigger: "change",
          }, {
						validator: (rule, value, cb) => {
              var str = /^[1-9]\d*$/;
              return str.test(value);
            },
            message: '请输入正整数',
          }],
          end: [{
          	required: true,
          	message: "结束区间不能为空",
          	trigger: "change",
          }, {
            validator: validateEnd,
          }],
				},
			};
		},
    watch: {
      'addRule.details': {
        handler(newVal) {
          if (newVal.length > 0) {
            if (newVal.length > 1) {
              // 自动设置下一个条目的起始区间
              for (let i = 1; i < newVal.length; i++) {
                if (newVal[i - 1].end) {
                  newVal[i].begin = (parseInt(newVal[i - 1].end, 10) + 1).toString();
                }
              }
            }
          }
        },
        deep: true,
      },
    },

		mounted() {
      this.goPageFirst(1);
		},

		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				queryImsiFee({
					size: 10,
					current: page,
					ruleName: this.searchObj.ruleName,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = Number(res.count)
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},

			//表格数据加载
			loadByPage(page) {
				this.goPageFirst(page);
			},

			//搜索
			search() {
				this.searchloading = true
				this.goPageFirst(1);
			},

			//新建
			addItem() {
        this.pageType = '1'
        this.title = "IMSI费规则新增"
				this.ruleModal = true
			},

      // 修改
      updateItem(row) {
        this.spinShow = true
        this.pageType = '2'
        this.title = "IMSI费规则修改"
        this.id = row.id
        detailImsiFee({id: row.id}).then(res => {
        	if (res.code == '0000') {
            res.data.imsiAmountVO[res.data.imsiAmountVO.length - 1].end = "无上限"
            this.addRule.ruleName = JSON.parse(JSON.stringify(res.data.ruleName))
            this.addRule.details = JSON.parse(JSON.stringify(res.data.imsiAmountVO))
            this.ruleModal = true
        	}
          this.spinShow = false
        }).catch((err) => {
        	console.error(err)
          this.spinShow = false
        }).finally(() => {
        })
        this.ruleModal = true
      },

      // 新增/修改 提交
      submit() {
      	this.$refs["addRule"].validate((valid) => {
          if (valid) {
            // 不改变页面
            let details = JSON.parse(JSON.stringify(this.addRule.details))
            details[details.length - 1].end = "9999999999";
            let newData = details.map(item => ({
                ...item,
                amount: item.amount
            }));

            let data = {
              ruleName: this.addRule.ruleName,
              imsiAmountVO: newData
            }
            let updateData = {
              id: this.id,
              ruleName: this.addRule.ruleName,
              imsiAmountVO: newData
            }
      			this.submitFlag = true;
            let func = this.pageType == "1" ? addImsiFee : updateImsiFee
            let resultData = this.pageType == "1" ? data : updateData
      			func(resultData).then(res => {
      				if (res && res.code == '0000') {
      					setTimeout(() => {
      						this.$Notice.success({
      							title: "操作提醒：",
      							desc: "操作成功！"
      						});
      						this.submitFlag = false;
                  this.cancelModal()
      						this.goPageFirst(this.currentPage);
      					}, 1500);
      				} else {
      					this.submitFlag = false;
      					throw res
      				}
      			}).catch((err) => {
      				this.submitFlag = false;
      			}).finally(() => {

      			})
      		}
      	})
      },

      // 删除
      deleteItem(row) {
        this.$Modal.confirm({
          title: '确认删除？',
          onOk: () => {
            delImsiFee({
              id: row.id
            }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirst(1)
              } else {
                throw res
              }
            }).catch((err) => {
              this.goPageFirst(1)
            })
          }
        });
      },

      cancelModal() {
        this.addRule = {
          ruleName: '',
          details: [
            {
              amount: '',
              begin: '1',
              end: '无上限',
            },
          ]
        }
        // 手动重置表单最有效
        this.$refs["addRule"].resetFields();
        this.ruleModal = false;
      },

      // 详情查看
      viewItem(row) {
        this.pageType = '3'
        this.title = "IMSI费规则详情"
        detailImsiFee({id: row.id}).then(res => {
        	if (res.code == '0000') {
            res.data.imsiAmountVO[res.data.imsiAmountVO.length - 1].end = "无上限"
            this.addRule.ruleName = JSON.parse(JSON.stringify(res.data.ruleName))
            this.addRule.details = JSON.parse(JSON.stringify(res.data.imsiAmountVO))
            this.ruleModal = true
        	}
        }).catch((err) => {
        	console.error(err)
        }).finally(() => {
        })
      },

      // 动态添加
      addRuleHandle() {
        // 假设有一个方法用于添加新的条目
        const newItem = {
          begin: '', // 根据前一个条目的end来计算
          end: '',   // 初始化为空
          amount: '',
        };

        // 设置新条目的begin
        if (this.addRule.details.length > 0) {
          newItem.begin = (parseInt(this.addRule.details[this.addRule.details.length - 1].end, 10) + 1).toString();
        } else {
          newItem.begin = '1'; // 如果是第一个条目，则begin为1
        }

        // 添加新条目到数组中
        this.addRule.details.push(newItem);

        // 更新最后一个条目的end为'9999999999'
        if (this.addRule.details.length > 0) {
          this.addRule.details[this.addRule.details.length - 1].end = '无上限';
          // 每次点击添加，就删除上一个end的赋值'9999999999'
          this.addRule.details[this.addRule.details.length - 2].end = this.addRule.details[this.addRule.details.length - 2].end !== '无上限' ? this.addRule.details[this.addRule.details.length - 2].end : "" ;
        }
      },

      // 动态删除
      removeRule(index) {
        // 移除指定索引的条目
        this.addRule.details.splice(index, 1);

        // 更新剩余条目的结束区间（如果需要）
        this.updateRemainingEndValues();
      },

      updateRemainingEndValues() {
        for (let i = 1; i < this.addRule.details.length; i++) {
          this.addRule.details[i].begin = (parseInt(this.addRule.details[i - 1].end, 10) + 1).toString();
          // 如果需要，这里也可以设置end的值，但在这个例子中我们假设end是自动计算的或手动设置的
        }

        // 如果需要，可以设置最后一个条目的end为'9999999999'或其他值
        if (this.addRule.details.length > 0) {
          this.addRule.details[this.addRule.details.length - 1].end = '无上限'; // 或者根据逻辑计算
        }
      },

      dateTransfer(dateStr) {
        var date = new Date(dateStr);

        // 使用getFullYear(), getMonth() + 1, getDate() 方法来获取年、月、日
        // 注意getMonth()返回的是0-11的整数，所以需要加1来转换为常见的月份表示
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2); // 确保月份是两位数
        var day = ('0' + date.getDate()).slice(-2); // 确保日期是两位数

        // 将年、月、日拼接成 "yyyy-mm-dd" 格式
        var formattedDate = year + '-' + month + '-' + day;
        return formattedDate;
      },

      handleCountryChange(index, mcc) {
        const country = this.mccList.find(c => c.mcc === mcc);
        if (country) {
          Vue.set(this.addRule.details[index], 'mcc', mcc);
          Vue.set(this.addRule.details[index], 'countryName', country.countryCn);
          this.filteredOperators[index] = this.operatorsByMcc[mcc] || [];
        }
      },

      handleOperatorChange(index, operatorId) {
        const operator = this.filteredOperators[index].find(o => o.id === operatorId);
        if (operator) {
          Vue.set(this.addRule.details[index], 'operatorId', operatorId);
          Vue.set(this.addRule.details[index], 'operatorName', operator.operatorName);
        }
      },
    }
	};
</script>

<style>
	.imsiInputSty {
		width: 200px;
	}

  /* 去掉input为number的上下箭头 */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
  	-webkit-appearance: none;
  }

  input[type="number"] {
  	-moz-appearance: textfield;
  }
</style>
