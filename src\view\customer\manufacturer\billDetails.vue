<template>
	<Card style="width: 100%;padiing: 16px;">
		<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading">
		</Table>
		<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="goPage"
		 style="margin: 15px 0;" />
		<div style="text-align: center;margin-top: 20px;">
		    <Button style="margin-right: 8px" @click="back">返回</Button>
		    <Button v-has="'export'" type="primary" @click="exportbill" icon="ios-cloud-download-outline" :loading="exportLoading" >导出</Button>
		</div>
		<!-- 导出提示 -->
		<Modal  v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
		  <div style="align-items: center;justify-content:center;display: flex;">
			  <Form  label-position="left"  :label-width="150" style=" align-items: center;justify-content:center;" >
			   		  <h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					  <FormItem label="你本次导出任务ID为:">
						<span style="width: 100px;">{{taskId}}</span>
			   		  </FormItem>
			   		  <FormItem label="你本次导出的文件名为:">
						<span>{{taskName}}</span>
			   		  </FormItem>
					  <span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
			   </Form>
		  </div>

		  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
		    <Button @click="cancelModal">取消</Button>
		    <Button type="primary" @click="Goto">立即前往</Button>
		  </div>
		</Modal>
	</Card>
</template>

<script>
	import {
	  exportflow,
	  flowdetails,
	} from '@/api/customer/manufacturer';
	import mailTable from './tableComponent.vue'
	export default {
		components: {
			mailTable,
		},
		data() {
			return {
				taskName:'',
				taskId:'',
				billInfo:[],
				exportModal:false,
				tableData: [], //列表信息
				total: 0,
				pageSize: 10,
				page: 1,
				tableLoading:false,
				exportLoading:false,
				columns: [{
						title: '账单月份',
						key: 'statTime',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '所属企业',
						key: 'corpName',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '国家/地区',
						key: 'countryCn',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '落地运营商',
						key: 'operatorName',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '上行流量/G',
						key: 'flowUpLinkTotal',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '下行流量/G',
						key: 'flowDownLinkTotal',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '流量总量/G',
						key: 'flowByteTotal',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '流量单价/G',
						key: 'amount',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '计费币种',
						key: 'currencyCodeName',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '账单金额',
						key: 'totalPrice',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
				]
			}
		},
		methods: {
			loadByPage(page){
				var v = this
				v.tableLoading = true
				let pageSize = 10
				let pageNum = page
				this.page=page
				flowdetails({
				    amount : this.billInfo.amount,
				    corpId : this.billInfo.corpId,
            userId:this.$store.state.user.userId,
            roleId:this.$store.state.user.roleId,
					currencyCode  : this.billInfo.currencyCode,
					mcc  : this.billInfo.mcc,
					month  : this.billInfo.statTime,
				    pageNum,
				    pageSize
				  }).then(res => {
				  if (res && res.code == '0000') {
				    var data = res.data
				    v.tableData = data
				    v.total = res.count
				    v.tableLoading = false
				  } else {
				    throw res
				  }
				}).catch((err) => {
				  v.tableLoading = false
				})
			},
			goPage(e){
				this.page=e
				this.loadByPage(e)
			},
			back(){
				//跳转回终端分页
				this.$router.go(-1)
			},
			exportbill(){
				exportflow({
					amount : this.billInfo.amount,
					corpId : this.billInfo.corpId,
					currencyCode  : this.billInfo.currencyCode,
					mcc  : this.billInfo.mcc,
					month  : this.billInfo.statTime,
          userId:this.$store.state.user.userId,
          roleId:this.$store.state.user.roleId,
				    pageNum:this.page,
				    pageSize:10
				}).then(res => {
				 if(res && res.code == '0000'){
					 this.exportModal=true
					 this.taskId=res.data.taskId
					 this.taskName=res.data.taskName
				 }
				  this.downloading = false
				}).catch(err => this.downloading = false)
			},
			cancelModal: function() {
				this.exportModal=false
			},
			Goto(){
				this.$router.push({
				  path: '/taskList',
				  query: {
					taskId: encodeURIComponent(this.taskId),
					fileName:encodeURIComponent(this.taskName),
				  }
				})
				this.exportModal=false
			},
		},
		mounted(){
		 var billInfo=JSON.parse(decodeURIComponent(this.$route.query.billInfo));
          this.billInfo=billInfo
		  this.loadByPage(1)
		}
	}
</script>

<style>
</style>
