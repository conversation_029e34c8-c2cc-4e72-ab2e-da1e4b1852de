<template>
	<Card style="width: 100%;padiing: 16px;">
		<div style="width: 100%;display: flex;margin-bottom: 16px;">
			<Input v-model='imsi' placeholder="请输入IMSI号码"  :clearable="true"  style="width: 200px;margin-right: 10px;" />
			<Button v-has="'search'"  style="margin: 0 2px;margin-right: 10px;" type="primary" :loading="searchLoading" @click="searchDetails">
			    搜索
			</Button>
			<Button  v-has="'export'"  type="success"  :loading="exportLoading" icon="ios-cloud-download-outline" @click="exportDetails()"  style="margin-right: 10px;">导出</Button>
			<a ref="downloadLink" style="display: none"></a>
		</div>

		<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading">
		</Table>
		<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="loadByPage"
		 style="margin: 15px 0;" />
		 <div style="text-align: center;margin-top: 20px;">
		     <Button style="margin-right: 8px" @click="back">返回</Button>
		 </div>
		 <!-- 导出提示 -->
		 <Modal  v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
		   <div style="align-items: center;justify-content:center;display: flex;">
		 	  <Form  label-position="left"  :label-width="150" style=" align-items: center;justify-content:center;" >
		 	   		  <h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
		 			  <FormItem label="你本次导出任务ID为:">
		 				<span style="width: 100px;">{{taskId}}</span>
		 	   		  </FormItem>
		 	   		  <FormItem label="你本次导出的文件名为:">
		 				<span>{{taskName}}</span>
		 	   		  </FormItem>
		 			  <span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
		 	   </Form>
		   </div>

		   <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
		     <Button @click="cancelModal">取消</Button>
		     <Button type="primary" @click="Goto">立即前往</Button>
		   </div>
		 </Modal>
	</Card>
</template>

<script>
	import {
	exportdetail,
	getflowdetail
	} from '@/api/customer/manufacturer';
	const math = require('mathjs')
	export default {
		components: {

		},
		data() {
			return {
				exportModal:false,
				taskId:'',
				taskName:'',
				flowInfo:[],
				tableData: [], //列表信息
				corpName:'',
				month:'',
				total: 0,
				pageSize: 10,
				page: 1,
				imsi:'',
				searchLoading:false,
				tableLoading:false,
				exportLoading:false,
				columns: [{
						title: '账单月份',
						key: 'statTime',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: 'IMSI',
						key: 'imsi',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},{
						title: 'VIMSI',
						key: 'himsi',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '企业/渠道商',
						key: 'corpName',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '国家/地区',
						key: 'countryCn',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '落地运营商',
						key: 'operatorName',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '流量总量(G)',
						key: 'flowByteTotal',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
						// render: (h, params) => {
						// 	const row = params.row
						// 	const text = parseFloat(math.multiply(math.bignumber(row.flowByteTotal), 1024).toFixed(5)).toString()
						// 	return h('label', text)
						// }
					},
				]
			}
		},
		methods: {
			goPageFirst: function(page) {
				getflowdetail({
					amount : this.flowInfo.amount,
					corpId : this.flowInfo.corpId,
					currencyCode  : this.flowInfo.currencyCode,
            userId:this.$store.state.user.userId,
            roleId:this.$store.state.user.roleId,
					mcc  : this.flowInfo.mcc,
					month:this.flowInfo.statTime,
					imsi:this.imsi,
					pageNum:page,
					pageSize:10
				}).then(res => {
				  if (res && res.code == '0000') {
					this.currentPage = page
				    this.tableData = res.data
				    this.total = res.count
				  } else {
				    throw res
				  }
				}).catch((err) => {
				  console.log(err)
				}).finally(() => {
				  this.loading = false
				  this.searchloading=false
				})
			},
			loadByPage(e){
				this.goPageFirst(e)
			},
			searchDetails(){
				this.searchloading=true
				this.goPageFirst(1)
			},
			exportDetails(){
				this.exportLoading = true
				exportdetail({
					amount : this.flowInfo.amount,
					corpId : this.flowInfo.corpId,
					currencyCode  : this.flowInfo.currencyCode,
					mcc  : this.flowInfo.mcc,
					month:this.flowInfo.statTime,
          userId:this.$store.state.user.userId,
          roleId:this.$store.state.user.roleId,
					imsi:this.imsi,
					pageNum:this.page,
					pageSize:10
				}).then(res => {
					if(res && res.code == '0000'){
						 this.exportModal=true
						 this.taskId=res.data.taskId
						 this.taskName=res.data.taskName
					}
					this.exportLoading = false
				}).catch(err => this.exportLoading = false)
			},
			cancelModal: function() {
				this.exportModal=false
			},
			Goto(){
				this.$router.push({
				  path: '/taskList',
				  query: {
					taskId: encodeURIComponent(this.taskId),
					fileName:encodeURIComponent(this.taskName),
				  }
				})
				this.exportModal=false
			},
			back(){
				//跳转回终端分页
				this.$router.go(-1)
			},
		},
		mounted(){
		   var flowInfo=JSON.parse(decodeURIComponent(this.$route.query.flowInfo));
		   this.flowInfo=flowInfo
		   this.goPageFirst(1)
		}
	}
</script>

<style>
</style>
