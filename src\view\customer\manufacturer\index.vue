<template>
	<Card style="width: 100%;padiing: 16px;">
		<Form ref="searchForm" :model="searchObj" inline>
			<FormItem>
				<span class="input_notice">终端厂商：</span>&nbsp;&nbsp;
				<Input v-model='searchObj.manufacturerName' placeholder="请输入终端厂商名称" clearable style="width: 200px;" />
			</FormItem>
			<FormItem>
				<span class="input_notice">类型：</span>&nbsp;&nbsp;
				<Select filterable v-model="searchObj.manufacturerType" style="width: 200px;" placeholder="请选择终端厂商类型" :clearable="true">
					<Option v-for="item in manufacturerTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
				</Select>
			</FormItem>
			<FormItem>
				<Button style="margin: 0 2px;" type="primary" v-has="'search'" :loading="searchLoading" @click="searchManufacturer">
					搜索
				</Button>
				<Button style="margin: 0 2px" type="info" v-has="'add'" @click="manufacturerAdd">
					<div style="display: flex;align-items: center;">
						<Icon type="md-add" />&nbsp;厂商新增</div>
				</Button>
				<Button style="margin: 0 2px" type="error" v-has="'batchDelete'" :loading="batchDeleteLoading" @click="deleteList">
					<Icon type="ios-trash" />&nbsp;批量删除
				</Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading"
			 @on-selection-change="handleRowChange">
				<template slot-scope="{ row, index }" slot="action">
					<Button type="primary" size="small" style="margin-right: 5px" v-has="'view'" @click="manufacturerCommon(row,'Info')">使用记录</Button>
					<Button type="success" size="small" style="margin-right: 5px" v-has="'update'" @click="manufacturerCommon(row,'Update')">编辑</Button>
					<Button type="warning" size="small" style="margin-right: 5px" v-has="'plmnlist'" @click="plmnlistManage(row)">PLMNLIST管理</Button>
					<Button type="error" size="small" v-has="'delete'" :loading="row.delLoading" @click="manufacturerDel(row)">删除</Button>
				</template>
				<template slot-scope="{ row, index }" slot="approval">
					<Button v-if="row.checkStatus === '1' ||row.checkStatus === '4'" type="success" size="small" style="margin-right: 5px"
					 v-has="'check'" :loading="row.checkPassLoading" @click="manufacturerApproval(row,'Pass')">通过</Button>
					<Button v-if="row.checkStatus === '1' ||row.checkStatus === '4'" type="error" size="small" v-has="'check'"
					 :loading="row.checkUnPassLoading" @click="manufacturerApproval(row,'Fail')">不通过</Button>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="loadByPage"
			 style="margin: 15px 0;" />
		</div>
		<!-- 终端厂商新增/编辑 -->
		<Modal :title="title" v-model="manufacturerEditFlag" :footer-hide="true" :mask-closable="false" width="1000px"
		 @on-cancel="reset('editObj')">
			<div style="padding: 0 16px;">
				<Form ref="editObj" :model="editObj" :label-width="180" :rules="ruleEditValidate">
					<Row>
						<Col span="12">
						<FormItem label="终端厂商名称" prop="manufacturerName">
							<Input v-model="editObj.manufacturerName" :clearable="true" placeholder="请输入终端厂商名称" class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="APPkey&APPSecret生成方式" prop="eopCreateType">
							<Select filterable v-model="editObj.eopCreateType" placeholder="请选择生成方式" :clearable="true" class="inputSty">
								<Option v-for="item in appMethodList" :value="item.value" :key="item.value">{{ item.label }}</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
					<Row v-if="editObj.eopCreateType === '2'">
						<Col span="12">
						<FormItem label="AppKey" prop="AppKey">
							<Input v-model="editObj.AppKey" :clearable="true" placeholder="请输入AppKey" class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="APPSecret" prop="AppSecret">
							<Input v-model="editObj.AppSecret" :clearable="true" placeholder="请输入APPSecret" class="inputSty"></Input>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="12">
						<FormItem label="公司名称" prop="companyName">
							<Input v-model="editObj.companyName" :clearable="true" placeholder="请输入公司名称" class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="地址" prop="address">
							<Input v-model="editObj.address" :clearable="true" placeholder="请输入地址" class="inputSty"></Input>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="12">
						<FormItem label="内部订单" prop="internalOrder">
							<Select filterable v-model="editObj.internalOrder" placeholder="请选择是否内部订单" :clearable="true" class="inputSty">
								<Option value="0">是</Option>
								<Option value="1">否</Option>
							</Select></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="币种" prop="currencyCode">
							<Select filterable v-model="editObj.currencyCode" placeholder="请选择币种" :clearable="true" class="inputSty">
								<Option value="156">人民币</Option>
								<Option value="344">港币</Option>
								<Option value="840">美元</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="12">
						<FormItem label="EBS Code" prop="EBSCode">
							<Input v-model="editObj.EBSCode" :clearable="true" placeholder="请输入EBS Code" class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="通知URL" prop="URL">
							<Input v-model="editObj.URL" :clearable="true" placeholder="请输入URL" class="inputSty"></Input>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="12">
						<FormItem label="激活通知" prop="activeNotifyType">
							<i-switch v-model="editObj.activeNotifyType" size="large">
								<span slot="open">开</span>
								<span slot="close">关</span>
							</i-switch>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="到期通知" prop="dueNotifyType">
							<i-switch v-model="editObj.dueNotifyType" size="large">
								<span slot="open">开</span>
								<span slot="close">关</span>
							</i-switch>
						</FormItem>
						</Col>
					</Row>
					<Row>
						<Col span="12">
						<FormItem label="厂商类型" prop="manufacturerType">
							<Select filterable v-model="editObj.manufacturerType" placeholder="请选择厂商类型" :clearable="true" class="inputSty">
								<Option v-for="item in manufacturerTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
					<!-- 展示套餐计费/流量计费 -->
					<Row>
						<Col span="24">
						<Tabs v-if="editObj.manufacturerType==='8'" style="margin-left: 100px;" :value="checked" @on-click="tagChange" :animated="false">
							<TabPane label="套餐计费" name="packagesInfo">
								<div v-for="(obj,index) in editObj.packages" :key="index" v-if="checked == 'packagesInfo'">
									<Row>
										<Col span="12">
										<FormItem label="套餐名称" :prop="'packages.' + index + '.packageName'" :rules="[
									    {required: true, message: '套餐名称不能为空', trigger: 'blur'},
									    { max: 200,message:'最长200位'}
									    ]"
										 style="margin-left: -100px;">
											<Input v-model="obj.packageName" :clearable="true" placeholder="请输入套餐名称" class="inputSty"></Input>
										</FormItem>
										</Col>
										<Col span="12">
										<FormItem label="套餐价格" :prop="'packages.' + index + '.price'" :rules="[
									    {required: true, message: '套餐价格不能为空',trigger: 'blur'},
									    {pattern: /^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger: 'blur', message: '请输入1-12位数字，整数首位非0（可精确到小数点后2位）' },
									    ]" style="margin-left: -30px;">
											<Input v-model="obj.price" :clearable="true" placeholder="请输入套餐价格" class="inputSty">
											<span slot="append">元</span>
											</Input>
											<div @click="delPackageBtn(index)" style="position: absolute;top: 0;right: 40px;">
												<Tooltip content="删除该项套餐" placement="left">
													<Icon type="md-trash" size="22" color="#ff3300" />
												</Tooltip>
											</div>
										</FormItem>
										</Col>

									</Row>
								</div>
								<Row>
									<Col span="12">
									<FormItem label="套餐" prop="packages" style="margin-left: -100px;">
										<Button type="dashed" class="inputSty" long @click="addPackage" icon="md-add">添加套餐</Button>
									</FormItem>
									</Col>
								</Row>

							</TabPane>
							<TabPane label="流量计费" name="flowInfo">
								<div style="padding-bottom: 20px;">
									<span>套餐名称:{{editObj.manufacturerName}}流量套餐</span>
								</div>
								<div v-for="(obj,index) in editObj.flowList" :key="index" v-if="checked == 'flowInfo'">
									<Row>
										<Col span="12">
										<FormItem label="选择流量方向" :prop="'flowList.' + index + '.direction'" :rules="[{type:'array',required: true, message: '流量方向不能为空', trigger: 'change'}]"
										 style="margin-left: -50px;">
											<Select filterable v-model="obj.direction" transfer placeholder="请选择流量方向" :clearable="true" class="inputSty" multiple >
												<Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
											</Select>
										</FormItem>
										</Col>
										<Col span="12">
										<FormItem label="流量单价" :prop="'flowList.' + index + '.price'" :rules="[
										{required: true, message: '流量单价不能为空',trigger: 'blur'},
										{pattern: /^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger: 'blur', message: '请输入1-12位数字，整数首位非0（可精确到小数点后2位）' },
										]" style="margin-left: -30px;">
											<Input v-model="obj.price" :clearable="true" placeholder="请输入流量单价" class="inputSty">
											<span slot="append">元/GB</span>
											</Input>
											<div @click="delFlowBtn(index)" style="position: absolute;top: 0;right: 20px;">
												<Tooltip content="删除该项方向" placement="left">
													<Icon type="md-trash" size="22" color="#ff3300" />
												</Tooltip>
											</div>
										</FormItem>
										</Col>
									</Row>
								</div>
								<Row>
									<Col span="12">
									<FormItem label="方向" prop="flowList" style="margin-left: -50px;">
										<Button type="dashed" class="inputSty" long @click="addFlow" icon="md-add">添加方向</Button>
									</FormItem>
									</Col>
								</Row>

							</TabPane>
						</Tabs>
						</Col>
					</Row>
				</Form>
				<div style="text-align: center;margin-top: 20px;">
					<Button type="primary" @click="add('editObj')" v-if="operationType=='Add'" :loading="addLoading" v-has="'add'">提交</Button>
					<Button type="primary" @click="update('editObj')" v-if="operationType=='Update'" :loading="addLoading" v-has="'update'">提交</Button>
					<Button style="margin-left: 8px" @click="reset('editObj')">重置</Button>
				</div>
			</div>
		</Modal>
		<Modal v-model="modal2" title="删除审核不通过" :mask-closable="false" @on-cancel="cancel2">
			<h3>确认企业是否还可用？</h3>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button type="primary" @click="checkAvailable('true')">可用</Button>
				<Button @click="checkAvailable('fail')">不可用</Button>
			</div>
		</Modal>
		<!-- PLMNLIST管理 -->
		<Modal v-model="modal1" title="PLMNLIST管理" :mask-closable="false" @on-cancel="cancelModal" width="700px">
			<Tabs type="card" value="name1" @on-click="choseTab">
				<TabPane label="添加MCC" name="name1">
					<div class="search_head" style="font-weight:bold;font-size: 18px; margin: 15px 10px;">
						<span>厂商名称:</span>&nbsp;&nbsp;
						<span style="font-weight:bold;">{{corpName}}</span>&nbsp;&nbsp;
					</div>
					<div class="search_head" style="margin: 30px 0px 80px 0px;">
						<Form ref="formmodel" :model="formmodel" :label-width="70" inline style="font-weight:bold;">
							<div v-for="(item, index) in formmodel.MCCList" :key="index" style="display: flex; justify-content: flex-start;">
								<FormItem label="国家" :prop="'MCCList.' + index+ '.mcc'" :rules="[{required: true, message: '请输入MCC码', trigger: 'blur' },]">
									<Select filterable v-model="item.mcc" placeholder="选择或输入国家" :clearable="true" style="width: 180px">
									  <Option v-for="i in continentList" :value="i.mcc" :key="i.id">{{i.countryEn}}</Option>
									</Select>
								</FormItem>

								<FormItem :label="'MNC'" :prop="'MCCList.' + index+ '.mnc'" :rules="[{required: true, message: '请输入MNC码', trigger: 'blur' },]" >
									<Input v-model="item.mnc" placeholder="多个MNC用|分隔" clearable
									 :onkeyup="item.mnc = item.mnc.replace(/\s+/g,'')" style="width: 200px" />
								</FormItem>
								<FormItem>
									<Button type="error" size="small" style="margin-left: 10px;" @click="removemcc(index)">删除</Button>
								</FormItem>
							</div>
							<FormItem>
								<Button  type="primary" size="small" style="margin-left: 10px" @click="addmcc()">添加国家</Button>
							</FormItem>
						</Form>
					</div>
				</TabPane>
				<TabPane label="添加文件" name="name2">
					<div class="search_head" style="font-weight:bold;font-size: 18px; margin: 15px 10px;">
						<span>厂商名称:</span>&nbsp;&nbsp;
						<span style="font-weight:bold;">{{corpName}}</span>&nbsp;&nbsp;
					</div>
					<div class="search_head" style="margin: 50px 0;">
						<Form ref="formValidate" :model="formValidate" :label-width="100" :label-height="100"
						 inline style="font-weight:bold;">
							<FormItem label="文件"  style="width:510px" prop="file">
								<Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/" v-model="formValidate.file"
								:action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
								:on-progress="fileUploading"
								>
									<div style="padding: 20px 0">
										<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
										<p>点击或拖拽文件上传</p>
									</div>
								</Upload>
				                <ul class="ivu-upload-list" v-if="formValidate.file" style="width: 100%;">
				                	<li class="ivu-upload-list-file ivu-upload-list-file-finish">
				                		<span>
				                			<Icon type="ios-folder" />{{formValidate.file.name}}</span>
				                		<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
				                	</li>
				                </ul>
								<div style="width: 100%;">
									<Button type="primary" :loading="downloading" icon="ios-download" @click="downloadFile">下载模板文件</Button>
									<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
									<a ref="downloadLink" style="display: none"></a>
								</div>
							</FormItem>
						</Form>
					</div>
				</TabPane>
			</Tabs>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" :loading="addmccLoading" @click="handleUpload">确定</Button>
			</div>
		</Modal>
		<!-- 模板文件table -->
		<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
	</Card>
</template>

<script>
	import {
		getPage,
		add,
		update,
		check,
		del,
		getMore,
		updateMccInput,
		addMccFile,
		getPlmnlistDetail
	} from '@/api/customer/manufacturer';
	import {
		getCountryList
	} from '@/api/customer/cooperative';
	const math = require('mathjs')
	//TODO 套餐列表接口
	export default {
		components: {

		},
		data() {
			return {
				checked: 'packagesInfo',
				title: '终端厂商新增',
				searchObj: {
					'manufacturerName': '', //终端厂商名称
					'manufacturerType': '', //终端厂商类型
				},
				manufacturerEditFlag: false,
				operationType: 'Add',
				editObj: {
					'corpId': '',
					'manufacturerName': '', //终端厂商名称
					'eopCreateType': '', //APPkey&APPSecret生成方式
					'AppKey': '',
					'AppSecret': '',
					'currencyCode': '',
					'internalOrder': '',
					'address': '',
					'companyName': '',
					'URL': '', // URL
					'EBSCode': '',
					'activeNotifyType': false, //激活通知开关
					'dueNotifyType': false, //到期通知开关
					'manufacturerType': '', //终端厂商类型
					'paymentMode': '', //付费模式
					'packages': [], //套餐集合
					'flowList': [], //流量集合
				},
				orderObj: {
					'manufacturerName': '', //终端厂商名称
					'paymentMode': '', //付费模式
					'packages': [], //套餐名称
					'flowList': [], //流量方向
					'flowPool': [], //流量池
				},
				appMethodList: [{
					'value': '1',
					'label': '自动生成'
				}, {
					'value': '2',
					'label': '手动输入'
				}], //通知类型
				manufacturerTypeList: [{
					'value': '7',
					'label': '线上厂商'
				}, {
					'value': '8',
					'label': '线下厂商'
				}], //厂商类型
				paymentModeList: [{
					'value': '1',
					'label': '按套餐'
				}, {
					'value': '2',
					'label': '按流量'
				}], //付费模式
				continentList: [], //国家列表
				ruleEditValidate: {
					manufacturerName: [{
							required: true,
							type: 'string',
							message: '终端厂商名称不能为空',
							trigger: 'blur'
						},
						{
							max: 50,
							message: '最长50位'
						}
					],
					eopCreateType: [{
						required: true,
						type: 'string',
						message: 'APPkey&APPSecret生成方式不能为空',
						trigger: 'change'
					}],
					AppKey: [{
							required: true,
							type: 'string',
							message: 'APPkey不能为空',
							trigger: 'blur'
						},
						{
							max: 255,
							message: '最长255位'
						}
					],
					AppSecret: [{
							required: true,
							type: 'string',
							message: 'APPSecret不能为空',
							trigger: 'blur'
						},
						{
							max: 255,
							message: '最长255位'
						}
					],
					companyName: [{
							required: true,
							type: 'string',
							message: '公司名称不能为空',
							trigger: 'blur'
						},
						{
							max: 50,
							message: '最长50位'
						}
					],
					address: [{
							required: true,
							type: 'string',
							message: '地址不能为空',
							trigger: 'blur'
						},
						{
							max: 200,
							message: '最长200位'
						}
					],
					internalOrder: [{
						required: true,
						type: 'string',
						message: '内部订单不能为空',
						trigger: 'change'
					}],
					EBSCode: [{
							required: true,
							type: 'string',
							message: 'EBS Code不能为空',
							trigger: 'blur'
						},
						{
							max: 50,
							message: '最长50位'
						}
					],
					URL: [{
							required: true,
							type: 'string',
							message: 'URL不能为空',
							trigger: 'blur'
						},
						{
							max: 255,
							message: '最长255位'
						}
					],
					activeNotifyType: [{
						required: true,
						type: 'boolean',
						message: '激活通知开关不能为空',
						trigger: 'blur'
					}],
					dueNotifyType: [{
						required: true,
						type: 'boolean',
						message: '到期通知开关不能为空',
						trigger: 'blur'
					}],
					manufacturerType: [{
						required: true,
						type: 'string',
						message: '厂商类型不能为空',
						trigger: 'change'
					}],
					currencyCode: [{
						required: true,
						type: 'string',
						message: '币种不能为空',
						trigger: 'change'
					}],
					paymentMode: [{
						required: true,
						type: 'string',
						message: '付费模式不能为空',
						trigger: 'change'
					}],
					packages: [{
						type: 'array',
						required: true,
						message: '请选择套餐',
						trigger: 'change'
					}],
					flowList: [{
						type: 'array',
						required: true,
						message: '请填写方向',
						trigger: 'change'
					}]
				},
				ruleOrderValidate: {},
				tableData: [], //列表信息
				selection: [], //多选
				selectionIds: [], //多选ids
				total: 0,
				pageSize: 10,
				page: 1,
				columns: [{
						type: 'selection',
						minWidth: 60,
						align: 'center'
					},
					{
						title: '厂商名称',
						key: 'corpName',
						align: 'center',
						minWidth: 120,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: 'APPKey',
						key: 'appKey',
						align: 'center',
						minWidth: 120,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: 'AppSecret',
						key: 'appSecret',
						align: 'center',
						minWidth: 120,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: 'EBS Code',
						key: 'ebsCode',
						align: 'center',
						minWidth: 120,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '通知URL',
						key: 'notifyUrl',
						align: 'center',
						minWidth: 120,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '厂商类型',
						key: 'type',
						align: 'center',
						minWidth: 120,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const text = row.type == 7 ? '线上厂商' : '线下厂商';
							return h('label', text);
						}
					},
					{
						title: '操作',
						slot: 'action',
						minWidth: 280,
						align: 'center'
					},
					{
						title: '审批状态',
						key: 'checkStatus',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							const color = row.checkStatus == 1 ? '#2d8cf0' : row.checkStatus == 2 ? '#00cc66' : row.checkStatus == 3 ?
								'#ff0000' : '#ed4014';
							'#27A1FF';
							const text = row.checkStatus == 1 ? '新建待审批' : row.checkStatus == 2 ? '通过' : row.checkStatus == 3 ? '不通过' : row
								.checkStatus == 4 ? '删除待审批' : '';
							return h('label', {
								style: {
									color: color
								}
							}, text);
						},
					},
					{
						title: '审批操作',
						slot: 'approval',
						minWidth: 160,
						align: 'center'
					}
				],
				modalHeight: '100%',
				modalOverflowX: 'visible',
				tableLoading: false,
				searchLoading: false,
				addLoading: false,
				batchDeleteLoading: false,
				downloading: false,
				addmccLoading: false,
				modal2: false,
				checkItem: {},
				modal1: false,
				index: 0,
				tabid: 'name1',
				uploadUrl: '',
				formValidate: {
					file: null
				},
				formmodel: {
					mcc: "",
					mnc: "",
					MCCList: [{
						index: 0,
						mnc: "",
						mcc: "",
						corpId: ""
					}],
				},
				plmnCorpId: "",
				MCCListArr: [],
				message: '文件仅支持csv格式文件,大小不能超过5MB',
				modelColumns: [{
						title: '国家名称（英文）',
						minWidth: '300',
						key: 'countryName',
					},
					{
						title: 'MNC|MNC|MNC',
						minWidth: '300',
						key: 'mnc'
					}
				],
				modelData: [],
				corpName: "", //详情
			}
		},
		methods: {
			// 页面加载
			goPageFirst(page) {
				this.selection = [];
				this.selectionIds = [];
				var v = this
				v.tableLoading = true
				let pageSize = 10
				let pageNumber = page
				getPage({
					corpName: v.searchObj.manufacturerName,
					corpType: v.searchObj.manufacturerType,
					pageNumber,
					pageSize
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data
						v.tableData = data.records
						v.total = data.totalCount
						v.tableLoading = false
						v.searchLoading = false
						if (this.tableData.length) {
							this.tableData.map(item => {
								this.$set(item, 'delLoading', false)
								this.$set(item, 'checkPassLoading', false)
								this.$set(item, 'checkUnPassLoading', false)
								return item
							})
						}
					} else {
						throw res
					}
				}).catch((err) => {
					v.tableLoading = false
					v.searchLoading = false
					if (this.tableData.length) {
						this.tableData.map(item => {
							this.$set(item, 'delLoading', false)
							this.$set(item, 'checkPassLoading', false)
							this.$set(item, 'checkUnPassLoading', false)
							return item
						})
					}
				})
			},
			getLocalList() {
				getCountryList().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.continentList = list;
						this.continentList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
					} else {
						throw res
					}
				}).catch((err) => {})
			},
			//提交
			add(name) {
				var t = this
				t.$refs[name].validate((valid) => {
					if (valid) {
						t.addLoading = true
						var pl = t.editObj.packages;
						var fl = t.editObj.flowList;
						var packageInfo = []
						var flowInfo = []
						//封装套餐集合
						if (pl != null && pl[0].packageName != '') {
							pl.forEach(e => {
								var TerminalPackage = {}
								TerminalPackage.packageName = e.packageName;
								TerminalPackage.price = math.multiply(math.bignumber(e.price), 100).toString();
								TerminalPackage.currencyCode = e.currency;
								packageInfo.push(TerminalPackage)
							});
						}
						//封装国家集合
						if (fl != null && fl[0].direction != '') {
							fl.forEach(e => {
								var TerminalPackage = {}
								TerminalPackage.mccList = e.direction;
								TerminalPackage.packageName = t.editObj.manufacturerName+"流量套餐";
								TerminalPackage.price = math.multiply(math.bignumber(e.price), 100).toString();
								// TerminalPackage.currencyCode = e.currency;
								flowInfo.push(TerminalPackage)
							});
						}
						add({
							'corpName': t.editObj.manufacturerName, //终端厂商名称
							'eopCreateType': this.editObj.eopCreateType, //APPkey&APPSecret生成方式
							'appKey': t.editObj.AppKey,
							'appSecret': t.editObj.AppSecret,
							'notifyUrl': t.editObj.URL, // URL
							'ebsCode': t.editObj.EBSCode,
							'activeNotifyType': t.editObj.activeNotifyType === true ? '1' : '2', //激活通知开关
							'dueNotifyType': t.editObj.dueNotifyType === true ? '1' : '2', //到期通知开关
							'type': t.editObj.manufacturerType, //终端厂商类型
							'settleType': t.editObj.paymentMode, //付费模式
							// 'extras': extras, //套餐集合
							'packageInfo':packageInfo,//套餐集合
							'flowInfo':flowInfo,//流量集合
							'currencyCode': t.editObj.currencyCode,
							'address': t.editObj.address,
							'companyName': t.editObj.companyName,
							'internalOrder': t.editObj.internalOrder,
						}).then(res => {
							if (res && res.code == '0000') {
								t.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								t.manufacturerEditFlag = false;
								t.addLoading = false
								t.page = 1
								t.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							t.addLoading = false
						})
					}
				})
			},
			reset(name) {
				this.editObj = {
					'corpId': this.editObj.corpId,
					'manufacturerName': '', //终端厂商名称
					'AppKey': '',
					'AppSecret': '',
					'CMIAppKey': '',
					'CMIAppSecret': '',
					'URL': '', // URL
					'EBSCode': '',
					'activeNotifyType': false, //激活通知开关
					'dueNotifyType': false, //到期通知开关
					'manufacturerType': '', //终端厂商类型
					'paymentMode': '', //付费模式
					'packages': [{
						"packageName": '',
						"price": '',
						// "currency": ''
					}], //套餐集合
					'flowList': [{
						"packageName": '',
						"direction": '',
						"price": '',
						// "currency": ''
					}], //流量集合
					'currencyCode': '',
					'address': '',
					'companyName': '',
					'internalOrder': '',
				};
				this.$refs.editObj.resetFields()
			},
			cancel2() {
				this.modal2 = false
				this.checkItem = {}
				this.page = 1
				this.goPageFirst(1)
			},
			//表格数据加载
			loadByPage(e) {
				this.page = e
				this.goPageFirst(e)
			},
			//搜索
			searchManufacturer() {
				this.searchLoading = true
				this.page = 1
				this.goPageFirst(1)
			},
			//设置高度
			setModalHeight(length) {
				// if (length >= 2) {
				//   this.modalOverflowX = 'visible';
				//   this.modalHeight = '100%';
				// } else {
				//   this.modalOverflowX = 'visible';
				//   this.modalHeight = '100%';
				// }
			},
			tagChange:function(name){
			  //vimsiInfo  trafficInfo
			  this.checked = name

			},
			//厂商新增
			manufacturerAdd() {
				this.reset()
				this.manufacturerEditFlag = true;
				this.operationType = 'Add';
			},
			//厂商付费模式添加与删除
			addPackage() {
				this.editObj.packages.push({
					"packageName": '',
					"price": '',
					"currency": ''
				})
			},
			addFlow() {
				this.editObj.flowList.push({
					"packageName": '',
					"direction": '',
					"price": '',
					"currency": ''
				});
			},
			delPackageBtn(index) {
				this.editObj.packages.splice(index, 1);
			},
			delFlowBtn(index) {
				this.editObj.flowList.splice(index, 1);
			},
			delOrderPackageBtn(index) {
				this.orderObj.packages.splice(index, 1);
			},
			delOrderFlowBtn(index) {
				this.orderObj.flowList.splice(index, 1);
			},
			//详情
			//编辑
			manufacturerCommon(row, type) {
				if (type === 'Info') {
					this.operationType = type;
					this.$router.push({
						name: 'manufacturerInfo',
						query: {
							manufacturer: encodeURIComponent(JSON.stringify(row))
						}
					})
				}
				if (type === 'Update') {
					//跳转
					this.operationType = type;
					this.$router.push({
						name: 'manufacturer_update',
						query: {
							manufacturer: encodeURIComponent(JSON.stringify(row))
						}
					})
				}
			},
			//审核
			manufacturerApproval(row, type) {
				var data = {}
				data.id = row.corpId
				if (type === 'Pass') {
					this.$Modal.confirm({
						title: '确认通过？',
						onOk: () => {
							data.passed = true
							row.checkPassLoading = true
							this.check(data)
						}
					})
				}
				if (row.checkStatus === '4' && type === 'Fail') {
					this.checkItem = row
					row.checkUnPassLoading = true
					this.modal2 = true
				}
				if (row.checkStatus !== '4' && type === 'Fail') {
					this.$Modal.confirm({
						title: '确认不通过？',
						onOk: () => {
							data.passed = false
							row.checkUnPassLoading = true
							this.check(data)
						}
					})
				}
			},
			checkAvailable(type) {
				var data = {}
				data.id = this.checkItem.corpId
				if (type === 'true') {
					data.passed = false
					data.available = true
					this.check(data)
				}
				if (type === 'fail') {
					data.passed = false
					data.available = false
					this.check(data)
				}
				this.modal2 = false
			},
			check(data) {
				//修改审核状态
				check(data.id, data).then(res => {
					if (res && res.code == '0000') {
						this.$Notice.success({
							title: '操作提示',
							desc: '操作成功'
						})
						this.page = 1
						this.goPageFirst(1)
					} else {
						throw res
					}
				}).catch((err) => {
					this.$Notice.error({
						title: '操作提示',
						desc: '操作失败'
					})
				})
			},
			//删除
			manufacturerDel(item) {
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						item.delLoading = true
						var idList = []
						idList.push(item.corpId)
						del(idList).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.page = 1
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							this.$Notice.error({
								title: '操作提示',
								desc: '操作失败'
							})
						})
					}
				});
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				this.selectionIds = [];
				selection.map((value, index) => {
					this.selectionIds.push(value.corpId);
				});
			},
			//批量删除
			deleteList() {
				var len = this.selection.length;
				if (len < 1) {
					this.$Message.warning('请至少选择一条记录')
					return
				}
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						this.batchDeleteLoading = true
						del(this.selectionIds).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.batchDeleteLoading = false
								this.page = 1
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							this.batchDeleteLoading = false
							this.$Notice.error({
								title: '操作提示',
								desc: '操作失败'
							})
						})
					}
				});
			},
			// plmnlist管理
			plmnlistManage(row) {
				this.corpName = row.corpName
				this.plmnCorpId = row.corpId
				this.getPlmnlist()
				this.modal1 = true
			},
			cancelModal(){
				this.modal1 = false
				this.$refs.formmodel.resetFields()
				this.formmodel.MCCList.splice(1,this.index)
				this.formValidate.file = ""
				this.addmccLoading = false
				this.goPageFirst(1)
				this.page=1
			},
			// 选择标签
			choseTab(name) {
				this.tabid = name
			},
			// 删除mcc
			removemcc(index) {
				this.formmodel.MCCList.splice(index, 1);
				this.index--;
			},
			// 添加MCC
			addmcc() {
				this.index++;
				this.formmodel.MCCList.push({
					index: this.index,
					mnc: "",
					mcc: "",
					corpId: "",
				});
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			handleBeforeUpload(file) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: '文件格式不正确',
						desc: '文件 ' + file.name + ' 格式不正确，请上传.csv格式文件。'
					})
				} else {
					this.formValidate.file = file
				}
				return false
			},
			// 删除上传的文件
			removeFile() {
				this.formValidate.file = ''
			},
			//下载模板文件
			downloadFile() {
				this.$refs.modelTable.exportCsv({
					filename: "终端厂商plmnlist",
					type:'csv',
					columns: this.modelColumns,
					data: this.modelData
				})
			},
			// 获取plmnlist详情
			getPlmnlist() {
				getPlmnlistDetail({corpId : this.plmnCorpId}).then(res => {
					if (res.code == '0000') {
						this.formmodel.MCCList = res.data
					}
				}).catch((err) => {
					console.error(err)
				})
			},
			async handleUpload() {
				if (this.tabid === 'name1') {
					// 添加MCC
					let length = this.formmodel.MCCList ? this.formmodel.MCCList.length : ""
					if(length<1){
						this.$Message.warning('请添加至少一组mcc')
						return
					}
					this.$refs.formmodel.validate(valid => {
						if (valid) {
							this.addmccLoading=true
							let formData = new FormData()

							this.formmodel.MCCList.forEach((i)=>{
								i.corpId = this.plmnCorpId
								delete i.index
								delete i.id
								this.MCCListArr = JSON.stringify(this.formmodel.MCCList)
							})
							updateMccInput(this.MCCListArr).then(res => {
								if (res.code === '0000') {
									let data = res.data
									this.$Notice.success({
										title: '操作提醒',
										desc: '修改成功'
									})
									this.addmccLoading=false
									this.cancelModal()
									this.page=1
									this.goPageFirst(1)
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.addmccLoading=false
								this.cancelModal()
							})
						}
					})
				} else {
					// 添加文件
					if (!this.formValidate.file) {
						this.$Message.warning('请选择需要上传的文件')
						return
					} else {
						this.$refs.formValidate.validate(valid => {
							if (valid) {
								let formData = new FormData()
								formData.append('file', this.formValidate.file)
								formData.append('corpId', this.plmnCorpId)
								addMccFile(formData).then(res => {
									if (res.code === '0000') {
										this.$Notice.success({
											title: '操作成功',
											desc: '添加成功'
										})
										this.addmccLoading=false
										this.cancelModal()
										this.page=1
										this.goPageFirst(1)
									} else {
										throw res
									}
								}).catch((err) => {
									console.log(err)
								}).finally(() => {
									this.addmccLoading=false
									this.cancelModal()
								})
							}
						})
					}
				}
			},
		},
		mounted() {
			//加载列表信息
			this.goPageFirst(1);
			//获取国家/地区信息
			this.getLocalList();
		}
	}
</script>

<style>
	.input_notice {
		font-size: 15px;
		font-weight: bold;
	}

	.inputSty {
		width: 200px;
	}
</style>
