<template>
  <Card style="width: 100%;padiing: 16px;">
      <Form  ref="searchObj" :model="searchObj" :label-width="100" :rules="rule">
        <FormItem label="终端厂商名称">
          <Input v-model='searchObj.corpName' placeholder="请输入终端厂商" readonly style="width: 200px;" />
        </FormItem>
       <FormItem label="选择月份" v-if="TabFlag==='1'||TabFlag===''"  >
		  <DatePicker  type="month" format="yyyy-MM" placeholder="请选择月份" :clearable="true"
		   v-model="searchObj.searchMonth" style="width: 200px;" @on-change="handleDateChange"
		    @on-clear="hanldeDateClear"></DatePicker>
        </FormItem>
		<FormItem label="选择开始月份" v-if="TabFlag==='2'"   >
		  <DatePicker   type="month" format="yyyy-MM" placeholder="请选择月份" :clearable="true"
		   v-model="searchObj.startMonth" style="width: 200px;" @on-change="startMonthChange"
		        @on-clear="startMonthDateClear"></DatePicker>
		</FormItem>
		<FormItem label="选择结束月份"  v-if="TabFlag==='2'" >
		  <DatePicker  type="month" format="yyyy-MM" placeholder="请选择月份" :clearable="true"
		   v-model="searchObj.endMonth" style="width: 200px;" @on-change="endMonthChange"
		        @on-clear="endMonthDateClear"></DatePicker>
		</FormItem>
          <Button style="margin: 0 2px;" type="primary" :loading="searchLoading" @click="searchDetails">
              搜索
          </Button>
          <Button style="margin: 0 2px;" icon="ios-cloud-download-outline" type="success" :loading="exportLoading" @click="exportDetails" v-has="'export'">
            导出
          </Button>
      </Form>

	<div>
		<Tabs value="1" @on-click="switchTab">
			<Tab-pane label="套餐计费" name="1">
				<div>
				  <Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading">
				  </Table>
				  <Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="loadByPage"
				    style="margin: 15px 0;" />
				</div>
			</Tab-pane>
			<Tab-pane label="流量计费" name="2">
				<Button  v-has="'export'"  type="success"  :loading="totalLoading"
				icon="ios-cloud-download-outline" @click="exportTotal()"  style="margin-bottom: 10px;">导出总流量明细</Button>
				<div>
				  <Table  :columns="columns1" :data="tableData1" :ellipsis="true" :loading="tableLoading">
				    <template slot-scope="{ row, index }" slot="action">
				      <a   type="primary" size="small" @click="showDetail(row)">账单详情</a> |
					    <a   type="primary" size="small" @click="showFlowDetail(row)">流量明细</a>
				    </template>
				  </Table>
					<Page :total="flowtotal" :page-size="flowpageSize" :current.sync="flowpage" show-total show-elevator @on-change="flowloadByPage"
					  style="margin: 15px 0;" />
				</div>
			</Tab-pane>
			<!-- 导出提示 -->
			<Modal  v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			  <div style="align-items: center;justify-content:center;display: flex;">
				  <Form  label-position="left"  :label-width="150" style=" align-items: center;justify-content:center;" >
				   		  <h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
						  <FormItem label="你本次导出任务ID为:">
							<span style="width: 100px;">{{taskId}}</span>
				   		  </FormItem>
				   		  <FormItem label="你本次导出的文件名为:">
							<span>{{taskName}}</span>
				   		  </FormItem>
						  <span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
				   </Form>
			  </div>

			  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
			    <Button @click="cancelModal">取消</Button>
			    <Button type="primary" @click="Goto">立即前往</Button>
			  </div>
			</Modal>
		</Tabs>
	</div>

  </Card>
</template>

<script>
  import {
    getMorePage,
    exportMore,
	getflow,
	exportall,
	exportlist
  } from '@/api/customer/manufacturer';
  export default {
    components: {

    },
    data() {
      return {
		taskId:'',
		taskName:'',
        searchObj: {
          'id': '',
          'manufacturerName': '', //终端厂商名称
          'searchMonth': '',
		  'endMonth':'',
		  'startMonth':''
        },
        tableData: [], //列表信息
		TabFlag:'1',
		tableData1:[],
        selection: [], //多选
        selectionIds: [], //多选ids
        tableLoading: false,
        searchLoading: false,
        exportLoading: false,
		totalLoading:false,
		exportModal:false,
        total: 0,
        pageSize: 10,
        page: 1,
		flowtotal:0,
		flowpageSize: 10,
		flowpage: 1,
		modeType:'',
        columns: [
          {
            title: '日期',
            key: 'useDate',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: 'VIMSI',
            key: 'vimsi',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '计费模式',
            key: 'settleType',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
            // render: (h, params) => {
            //   const row = params.row;
            //   const text = row.settleType == '1' ? '按套餐' : '按流量';
            //   return h('label', text);
            // }
          },
          {
            title: '套餐名称/流量方向',
            key: 'itemName',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '数量',
            key: 'count',
            align: 'center',
            minWidth: 100,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '单价',
            key: 'price',
            align: 'center',
            minWidth: 100,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '总价',
            key: 'totalPrice',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '币种',
            key: 'currency',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          }
        ],
		columns1:[
          {
            title: '账单月份',
            key: 'statTime',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '企业/渠道商',
            key: 'corpName',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '国家/地区',
            key: 'countryCn',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,

          },
          {
            title: '流量总量(G)',
            key: 'flowByteTotal',
            align: 'center',
            minWidth: 100,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '单价/G',
            key: 'amount',
            align: 'center',
            minWidth: 100,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '币种',
            key: 'currencyCodeName',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: '账单金额',
            key: 'totalPrice',
            align: 'center',
            minWidth: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
		  {
		    title: '账单详情',
		    slot: 'action',
		    align: 'center',
		    minWidth: 150,
		    tooltip: true,
		    tooltipMaxWidth: 2000,
		  }
        ],
		rule: {
		  startMonth: [
			{ required: true,message: '请选择开始月份', trigger: 'change'}
		  ],
		  endMonth: [
		  	{ required: true,message: '请选择结束月份', trigger: 'change'}
		  ],
		  month: [
		  	{ required: true,message: '请选择月份', trigger: 'change'}
		  ],
		},
      }
    },
    methods: {
      // 页面加载
      goPageFirst (page) {
        var v = this
        v.tableLoading = true
        let pageSize = 10
        let pageNumber = page
        getMorePage({
            id: this.searchObj.corpId,
            yearMonth: this.searchObj.searchMonth,
            pageNumber,
            pageSize
          }).then(res => {
          if (res && res.code == '0000') {
            var data = res.data
            v.tableData = data.records
            v.total = data.totalCount
            v.tableLoading = false
            v.searchLoading = false
          } else {
            throw res
          }
        }).catch((err) => {
          v.tableLoading = false
		  v.searchLoading = false
        })
      },
	  flowgoPageFirst(page){
		var v = this
		let pageSize = 10
		let pageNum = page
		var startmonth=null
		var endmonth=null
		if(this.searchObj.startMonth){
			let startdate=this.searchObj.startMonth.split("-");
			startmonth=startdate[0]+startdate[1]+""
		}
		if(this.searchObj.endMonth){
			let enddate=this.searchObj.endMonth.split("-");
			endmonth=enddate[0]+enddate[1]+""
		}
		//校验开始月份-结束月份
		if(startmonth==null||endmonth===null){
			this.$Modal.warning({title:"请选择开始月份和结束月份"});
			v.searchLoading = false
			return
		}
		// this.$refs["searchObj"].validate(valid => {
		//   if (valid) {
			v.tableLoading = true
			getflow({
			    corpId: this.searchObj.corpId,
			    startMonth: startmonth,
				endMonth:endmonth,
			    pageNum,
			    pageSize
			  }).then(res => {
			  if (res && res.code == '0000') {
			    var data = res.data
				v.tableData1 = data
			    v.flowtotal = res.count
			    v.tableLoading = false
			    v.searchLoading = false
			  } else {
			    throw res
			  }
			}).catch((err) => {
			  v.tableLoading = false
			  v.searchLoading = false
			})
		  // }else{
			 //  v.searchLoading = false
		  // }
		  // })

	  },
      //表格初始化
      init() {
        for (var i = 1; i <= 10; i++) {
          this.tableData.push({
            "createTime": "2021/03/01 08:00:00",
            "VIMSI": "VIMSI" + i,
            "paymentMode": i % 3 != '1' ?  '1' : '2',
            "mode": i % 3 != '1' ?  '套餐'+i : '中国大陆',
            "count": i % 3 != '1' ?  '3' : '2',
            "price": i % 4 != '1' ? i % 4 == '2' ? '99.00' : '149.00' : '199.00',
            "totalPrice": i % 4 != '1' ? i % 4 == '2' ? '99.00' : '149.00' : '199.00',
            "currency": 'CNY'
          })
        }
        this.total = this.tableData.length;
      },
      //表格数据加载
      loadByPage(e) {
        this.page = e
        this.goPageFirst(e)
      },
	  flowloadByPage(e){
		this.flowpage = e
		this.flowgoPageFirst(e)
	  },
      //导出
      exportDetails() {
        var t = this
      	// t.$Modal.confirm({
      	// 	title: '确认导出？',
      	// 	onOk: () => {

			//判断是套餐计费还是流量计费导出
			if(this.TabFlag==='1'){    //套餐计费
			t.exportLoading = true
					exportMore({
						id: t.searchObj.corpId,
						yearMonth: t.searchObj.searchMonth,
					}).then(res => {
						const content =  res.data
				  // 获取当前时间
				  let date = new Date();
				  let y = date.getFullYear();
				  let m = date.getMonth() + 1;
				  let d = date.getDate();
				  // let H = Da.getHours();
				  let time = y + "-" + m + "-" + d
				  const fileName = time + '.txt' // 导出文件名
				  if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
				    const link = document.createElement('a') // 创建a标签
				    let url = URL.createObjectURL(content)
				    link.download = fileName
				    link.href = url
				    link.click() // 执行下载
				    URL.revokeObjectURL(url) // 释放url
				  } else { // 其他浏览器
				    navigator.msSaveBlob(content, fileName)
				  }
				  t.exportLoading = false
				}).catch(err => {
				  t.exportLoading = false
				  t.exporting = false
				})
			}else{ //流量计费
				var startmonth=null
				var endmonth=null
				// this.$refs["searchObj"].validate(valid => {
				//   if (valid) {

					if(this.searchObj.startMonth){
						let startdate=this.searchObj.startMonth.split("-");
						startmonth=startdate[0]+startdate[1]+""
					}
					if(this.searchObj.endMonth){
						let enddate=this.searchObj.endMonth.split("-");
						endmonth=enddate[0]+enddate[1]+""
					}
					//校验开始月份-结束月份
					if(startmonth==null||endmonth===null){
						this.$Modal.warning({title:"请选择开始月份和结束月份"});
						return
					}
					 t.exportLoading = true
					exportlist({
						corpId: this.searchObj.corpId,
						startMonth: startmonth,
						endMonth:endmonth,
						userId:this.$store.state.user.userId,
						roleId:this.$store.state.user.roleId,
						pageNum:this.page,
						pageSize:10
					}).then(res => {
						if(res && res.code == '0000'){
							 this.exportModal=true
							 this.taskId=res.data.taskId
							 this.taskName=res.data.taskName
						}
						this.exportLoading = false
					}).catch(err => this.exportLoading = false)
				  // }})

			}

        //   }
        // })
      },
	  //导出总流量明细
	  exportTotal(){

		var startmonth=null
		var endmonth=null
		if(this.searchObj.startMonth){
			let startdate=this.searchObj.startMonth.split("-");
			startmonth=startdate[0]+startdate[1]+""
		}
		if(this.searchObj.endMonth){
			let enddate=this.searchObj.endMonth.split("-");
			endmonth=enddate[0]+enddate[1]+""
		}
		//校验开始月份-结束月份
		if(startmonth==null||endmonth===null){
			this.$Modal.warning({title:"请选择开始月份和结束月份"});
			return
		}
		// this.$refs["searchObj"].validate(valid => {
		//   if (valid) {
			this.totalLoading = true
			exportall({
				corpId: this.searchObj.corpId,
				startMonth: startmonth,
				endMonth:endmonth,
        userId:this.$store.state.user.userId,
        roleId:this.$store.state.user.roleId,
				pageNum:this.page,
				pageSize:10
			}).then(res => {
				if(res && res.code == '0000'){
					 this.exportModal=true
					 this.taskId=res.data.taskId
					 this.taskName=res.data.taskName
				}
				this.totalLoading = false
			}).catch(err => this.totalLoading = false)
		  // }
		  // })
	  },
	  cancelModal: function() {
	  	this.exportModal=false
	  },
	  Goto(){
	  	this.$router.push({
	  	  path: '/taskList',
	  	  query: {
	  		taskId: encodeURIComponent(this.taskId),
	  		fileName:encodeURIComponent(this.taskName),
	  	  }
	  	})
	  	this.exportModal=false
	  },
      //日期查询
      searchDetails(){
        this.searchLoading = true
        this.page = 1
		if ('1' === this.TabFlag|| this.TabFlag===undefined) {
			this.goPageFirst(1);
		} else {
			this.flowgoPageFirst(1);
		}
      },
      handleDateChange(dateArr){
        this.searchObj.searchMonth=dateArr
      },
	  startMonthChange(dateArr){
		this.searchObj.startMonth=dateArr
	  },
	  endMonthChange(dateArr){
	  	this.searchObj.endMonth=dateArr
	  },
      hanldeDateClear() {
        this.searchObj.searchMonth = ''
      },
	  startMonthDateClear() {
	    this.searchObj.startMonth=''
	  },
	  endMonthDateClear() {
	  	this.searchObj.endMonth=''
	  },
	  switchTab(e) {
	  	this.TabFlag = e
	  	if ('1' === e) {
	  		this.goPageFirst(1);
	  	} else {
	  		this.flowgoPageFirst(1);
	  	}
		// this.searchObj.searchMonth = ''
		console.log(this.searchObj.startMonth)
		console.log(this.searchObj.endMonth)
	  },
	  //账单详情
	  showDetail(row){
		this.$router.push({
		  name:'billInfo',
		  query: {
		    billInfo: encodeURIComponent(JSON.stringify(row))
		  }
		})
	  },
	  //流量明细
	  showFlowDetail(row){
		this.$router.push({
		  name:'flowInfo',
		  query: {
		    flowInfo: encodeURIComponent(JSON.stringify(row)),
			corpName: encodeURIComponent(this.searchObj.corpName),
			month:encodeURIComponent(this.searchObj.searchMonth)
		  }
		})
	  }
    },
    mounted() {
      // this.tableData = res.data
      var manufacturer = JSON.parse(decodeURIComponent(this.$route.query.manufacturer));
      this.searchObj = manufacturer;
	  this.modeType = manufacturer.settleType;
      this.goPageFirst(1);
    }
  }
</script>

<style>
  .inputSty {
    width: 200px;
  }
</style>
