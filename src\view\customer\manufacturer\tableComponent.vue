<template>
 <table class="mailTable" :style="styleObject" v-if="s_showByRow">
    <tr v-for="(item,index) in tableData" >
      <td v-if="index===0" style="background-color: #0573C8;border: 1px solid #DCDEE2;
    height: 40px;
	width: 200px;
    padding-left: 10px;
	text-align: center;
    color:#333;" >{{item.key}}</td>
	<td v-else style="border: 1px solid #DCDEE2;
	height: 40px;
	width: 200px;
	text-align: center;
	padding-left: 10px;
	color:#333;" >{{item.key}}</td>
      <td v-if="index===0" style="background-color: #0573C8;border: 1px solid #DCDEE2;
    height: 40px;
	width: 200px;
	text-align: center;
    padding-left: 10px;
    color:#333;" >{{item.value}}</td>
	  <td v-else style="border: 1px solid #DCDEE2;
	height: 40px;
	width: 200px;
	text-align: center;
	padding-left: 10px;
	color:#333;" >{{item.value}}</td>
    </tr>
  </table>
</template>

<script>
export default {
  data() {
    return {
      styleObject: {},
      s_showByRow: true,
    };
  },
  props: ['tableData', 'tableStyle', 'showByRow'],
  computed: {
    rowCount: function() {
      return Math.ceil(this.tableData.length);
    }
  },
  created() {
    this.styleObject = this.tableStyle;
    if(this.showByRow !== undefined){
      this.s_showByRow = this.showByRow;
    }
  },
}
</script>
<style>
　　.mailTable, .mailTable tr, .mailTable tr td{ border:1px solid #E6EAEE; }
　　.mailTable{ font-size: 12px; color: #71787E; }
　　.mailTable tr td{ border:1px solid #E6EAEE; width: 150px; height: 35px; line-height: 35px; box-sizing: border-box; padding: 0 3px; }
　  .column { 
	  background: #00FF00;
      color: #00FF00; }
</style>