<template>
  <!-- 测试IMSI -->
  <div>
    <Card>
      <div class="search_head">
        <span style="font-weight:bold;">IMSI号码：</span>&nbsp;&nbsp;
        <Input placeholder="输入号码..." v-model="imsiCondition" clearable style="width: 200px" />&nbsp;&nbsp;
        <Button  type="primary" icon="md-search" :loading="searchLoading" @click="search()">搜索</Button>&nbsp;&nbsp;
        <Button v-has="'batchDelete'" icon="md-del" type="error" :loading="batchDeleteLoading" @click="deleteBatch()">批量删除</Button>&nbsp;&nbsp;
        <Button v-has="'add'" icon="md-add" type="success" @click="add()">新增</Button>&nbsp;&nbsp;
      </div>
      <div v-has="'search'" style="margin-top:20px">
        <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="searchLoading" @on-selection-change="handleRowChange">
          <template slot-scope="{ row, index }" slot="action">
            <Button v-has="'delete'" type="error" size="small" :loading="row.delLoading" @click="deleteItem(row)">删除</Button>
          </template>
        </Table>
        <Page :total="total" :page-size="pageSize" :current.sync="page" show-sizer show-total show-elevator @on-change="loadByPage" @on-page-size-change="loadByPageSize"
          style="margin: 15px 0;" />
      </div>
    </Card>
    <Modal
      v-model="modal"
      title="新增页面"
      :mask-closable="false"
      @on-cancel="cancel">
      <div class="search_head">
        <Form ref="form" :model="form" :rules="ruleInline" :label-width="120" :label-height="100" inline style="font-weight:bold;">
          <FormItem label="IMSI号码" prop="imsi" >
              <Input placeholder="请输入号码..." v-model="form.imsi" clearable style="width: 300px" />&nbsp;&nbsp;
          </FormItem>
        </Form>
      </div>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
        <Button @click="cancel">取消</Button>
        <Button type="primary" :loading="addLoading" @click="addImsi('form')">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import {
  getPage,
  add,
  del
} from "@/api/customer/testImsi";
export default {
  data() {
    return {
      total: 0,
      pageSize: 10,
      page: 1,
      modal: false,
      imsiCondition: '',
      searchLoading: false,
      addLoading: false,
      form: {
        imsi: "",
      },
      ruleInline: {
        imsi: [{ 
			required: true,
			message: '请输入30位以内的号码',
			min:0,
			max:30,
			trigger: 'blur'}],
      },
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: "IMSI号码",
          key: "imsi",
          align: "center",
        },
        {
          title: "创建时间",
          key: "createTime",
          align: "center",
        },
        {
          title: '操作',
          slot: 'action',
          minWidth: 200,
          align: 'center'
        },
      ],
      tableData: [],
      loading: false,
      addLoading: false,
      batchDeleteLoading: false,
      selection: [], //多选
      selectionIds: [], //多选ids
    };
  },
  created(){
  },
  mounted() {
    this.getTableData(1,10)
  },
  methods: {
    getTableData(page,pageSize){
      getPage({
        imsi: this.imsiCondition,
        pageNum: page,
        pageSize: pageSize,
      }).then(res => {
        if (res.code === "0000") {
          this.tableData = res.data;
          this.total = res.count;
          this.searchLoading = false
          if (this.tableData.length) {
            this.tableData.map(item => {
              this.$set(item, 'delLoading', false)
              return item
            })
          }
        }else {
          throw res
        }
      }).catch((err) => {
          this.searchLoading = false
          if (this.tableData.length) {
            this.tableData.map(item => {
              this.$set(item, 'delLoading', false)
              return item
            })
          }
      })
    },
    search(){
      this.searchLoading = true
      this.page = 1
      this.getTableData(this.page,this.pageSize)
    },
    loadByPage(page){
      this.page = page
      this.getTableData(page,this.pageSize)
    },
    loadByPageSize(pageSize){
      this.pageSize = pageSize
      this.getTableData(this.page,pageSize)
    },
    //多选
    handleRowChange(selection) {
      this.selection = selection;
      this.selectionIds = [];
      selection.map((value, index) => {
        this.selectionIds.push(value.id);
      });
    },
    //批量删除
    deleteBatch() {
      var len = this.selection.length;
      if (len < 1) {
        this.$Message.warning('请至少选择一条记录')
        return
      }
      this.$Modal.confirm({
        title: '确认删除？',
        onOk: () => {
          this.batchDeleteLoading = true
          del(this.selectionIds).then(res => {
            if (res && res.code == '0000') {
              this.$Notice.success({
                title: '操作提示',
                desc: '操作成功'
              })
              this.batchDeleteLoading = false
              this.page = 1
              this.getTableData(this.page,this.pageSize)
            } else {
              throw res
            }
          }).catch((err) => {
            this.batchDeleteLoading = false
            this.$Notice.error({
              title: '操作提示',
              desc: '操作失败'
            })
          })
        }
      });
    },
    deleteItem(item){
      this.$Modal.confirm({
        title: '确认删除？',
        onOk: () => {
          item.delLoading = true
          var idList = []
          idList.push(item.id)
          del(idList).then(res => {
            if (res && res.code == '0000') {
              this.$Notice.success({
                title: '操作提示',
                desc: '操作成功'
              })
              this.page = 1
              this.getTableData(this.page,this.pageSize)
            } else {
              throw res
            }
          }).catch((err) => {
            this.$Notice.error({
              title: '操作提示',
              desc: '操作失败'
            })
          })
        },
      })
    },
    add(){
      this.modal = true
    },
    addImsi(name){
      var t = this
      t.$refs[name].validate((valid) => {
        if (valid) {
          t.addLoading = true
          add({
              'imsi': t.form.imsi,
            }).then(res => {
            if (res && res.code == '0000') {
              t.$Notice.success({
                title: '操作提示',
                desc: '操作成功'
              })
              t.cancel()
              t.page = 1
              t.getTableData(t.page,t.pageSize)
            } else {
              throw res
            }
          }).catch((err) => {
            t.addLoading = false
			this.modal = false
          })
        }
      })
    },
    cancel(){
      this.modal = false
      this.$refs.form.resetFields()
      this.addLoading = false
    }
  },
};
</script>

<style></style>
