<template>
  <Card style="width: 100%;padding: 16px;">
    <div style="width: 80%;margin: 0 auto;">
      <Form ref="editObj" :model="editObj" :label-width="180" :rules="ruleEditValidate">
        <Row>
          <Col span="8">
          <FormItem label="终端厂商名称" prop="corpName">
            <Input v-model="editObj.corpName" :clearable="true" placeholder="请输入终端厂商名称" class="inputSty"></Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="EBS Code" prop="ebsCode">
            <Input v-model="editObj.ebsCode" :clearable="true" placeholder="请输入EBS Code" class="inputSty"></Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="通知URL" prop="notifyUrl">
            <Input v-model="editObj.notifyUrl" :clearable="true" placeholder="请输入URL" class="inputSty"></Input>
          </FormItem>
          </Col>
        </Row>
        <Row >
          <Col span="8">
          <FormItem label="APPkey&APPSecret生成方式" prop="eopCreateType" >
            <Select filterable v-model="editObj.eopCreateType" placeholder="请选择生成方式" :clearable="true" class="inputSty">
              <Option v-for="item in appMethodList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </FormItem>
          </Col>
          <div v-if="editObj.eopCreateType === '2'">
            <Col span="8">
            <FormItem label="AppKey" prop="appKey">
              <Input v-model="editObj.appKey" :clearable="true" placeholder="请输入AppKey" class="inputSty"></Input>
            </FormItem>
            </Col>
            <Col span="8">
            <FormItem label="APPSecret" prop="appSecret">
              <Input v-model="editObj.appSecret" :clearable="true" placeholder="请输入APPSecret" class="inputSty"></Input>
            </FormItem>
            </Col>
          </div>
        </Row>
        <Row>
          <Col span="8">
          <FormItem label="公司名称" prop="companyName">
            <Input v-model="editObj.companyName" :clearable="true" placeholder="请输入公司名称" class="inputSty"></Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="地址" prop="address">
            <Input v-model="editObj.address" :clearable="true" placeholder="请输入地址" class="inputSty"></Input>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="内部订单" prop="internalOrder">
            <Select filterable v-model="editObj.internalOrder" placeholder="请选择是否内部订单" :clearable="true" class="inputSty">
              <Option value="0">是</Option>
              <Option value="1">否</Option>
            </Select></Input>
          </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
          <FormItem label="币种" prop="currencyCode">
            <Select filterable v-model="editObj.currencyCode" placeholder="请选择币种" :clearable="true" class="inputSty">
              <Option value="156">人民币</Option>
              <Option value="344">港币</Option>
              <Option value="840">美元</Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="激活通知" prop="activeNotifyType">
            <i-switch v-model="editObj.activeNotifyType" size="large">
                <span slot="open">开</span>
                <span slot="close">关</span>
            </i-switch>
          </FormItem>
          </Col>
          <Col span="8">
          <FormItem label="到期通知" prop="dueNotifyType">
            <i-switch v-model="editObj.dueNotifyType" size="large">
                <span slot="open">开</span>
                <span slot="close">关</span>
            </i-switch>
          </FormItem>
          </Col>
        </Row>
        <Row>
          <Col span="8">
          <FormItem label="厂商类型" prop="type">
            <Select filterable v-model="editObj.type" placeholder="请选择厂商类型" :clearable="true" class="inputSty" @on-change="changeType">
              <Option v-for="item in manufacturerTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
          </FormItem>
          </Col>
        </Row>
		<Row>
			<div v-if="editObj.type==='8'">
				<Tabs  style="margin-left: 100px;" :value="checked" @on-click="tagChange">
					<TabPane label="套餐计费" name="1">
						<div>
						  <Button style="margin: 10px 0" type="primary" @click="ruleAdd('pkge')" >
							<div style="display: flex;align-items: center;">
							  <Icon type="md-add" />&nbsp;添加套餐</div>
						  </Button>
						  <Table :columns="pkgeColumns" :data="pkgeTableData" :loading="pkgeTableLoading">
							<template slot-scope="{ row, index }" slot="action">
							  <Button type="success" size="small" style="margin-right: 5px" @click="ruleUpdate(row,'pkge')">编辑</Button>
							  <Button type="error" size="small"  :loading="row.delLoading" @click="deleteItem(row,'pkge')">删除</Button>
							</template>
						  </Table>
						  <Page :total="pkgetotal" :page-size="pkgepageSize" :current.sync="pkgepage" show-total show-elevator @on-change="loadByPage"
						   style="margin: 15px 0;" />
						</div>
						
					</TabPane>
					<TabPane label="流量计费" name='2'>
						<div>
						  <Button style="margin: 10px 0" type="primary" @click="ruleAdd('flow')" >
							<div style="display: flex;align-items: center;">
							  <Icon type="md-add" />&nbsp;添加方向</div>
						  </Button>
						  <div style="padding-bottom: 20px;">
							<span>套餐名称:{{editObj.corpName}}流量套餐</span>  
						  </div>
						  <Table :columns="flowColumns" :data="flowTableData" :loading="flowTableLoading">
							<template slot-scope="{ row, index }" slot="action">
							  <Button type="success" size="small" style="margin-right: 5px" @click="ruleUpdate(row,'flow')">编辑</Button>
							  <Button type="error" size="small" :loading="row.delLoading" @click="deleteItem(row,'flow')">删除</Button>
							</template>
						  </Table>
						  <Page :total="flowtotal" :page-size="flowpageSize" :current.sync="flowpage" show-total show-elevator @on-change="flowloadByPage"
						   style="margin: 15px 0;" />
						</div>
					</TabPane>
				</Tabs>
			</div>
		</Row>
      </Form>
      <div style="text-align: center;margin-top: 20px;">
        <Button style="margin-right: 8px" @click="back">返回</Button>
		<Button type="primary" @click="update('editObj')" :loading="addLoading" >提交</Button>
      </div>
    </div>
	<Modal
	:title="pkgeTitle"
	v-model="pkgeEditFlag"
	:mask-closable="false"
	@on-cancel="reset('pkgeObj')">
		<Form ref="pkgeObj" :model="pkgeObj" :label-width="180" :rules="pkgeValidate">
			<Row>
				<FormItem label="套餐名称" prop="packageName">
					<Input v-model="pkgeObj.packageName" :clearable="true" placeholder="请输入套餐名称" class="inputSty"></Input>
				</FormItem>
			</Row>
			<Row>
				<FormItem label="套餐单价" prop="price" >
				  <Input v-model="pkgeObj.price" :clearable="true" placeholder="请输入套餐单价" class="inputSty">
				    <span slot="append">元</span>
				  </Input>
				</FormItem>
			</Row>
		</Form>
		<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
		  <Button @click="reset('pkgeObj')">返回</Button>
		  <Button type="primary" v-if="pkgeType === 'add'" @click="itemAdd('pkgeObj','pkge')" :loading="addItemLoading">确定</Button>
		  <Button type="primary" v-if="pkgeType === 'update'" @click="updateItem('pkgeObj','pkge')" :loading="addItemLoading">确定</Button>
		</div>
	</Modal>
	<Modal
	:title="flowTitle"
	v-model="flowEditFlag"
	:mask-closable="false"
	@on-cancel="reset('flowObj')">
		<Form ref="flowObj" :model="flowObj" :label-width="180" :rules="flowValidate">
			<Row>
				<FormItem label="选择流量方向" prop="mccList" >
				  <Select filterable v-model="flowObj.mccList" placeholder="请选择流量方向" :clearable="true" class="inputSty" multiple>
					<Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
				  </Select>
				</FormItem>
			</Row>
			<Row>
				<FormItem label="流量单价" prop="price" >
				  <Input v-model="flowObj.price" :clearable="true" placeholder="请输入流量单价" class="inputSty">
				    <span slot="append">元/GB</span>
				  </Input>
				</FormItem>
			</Row>
		</Form>
		<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
		  <Button @click="reset('flowObj')">返回</Button>
		  <Button type="primary" v-if="flowType === 'add'" @click="itemAdd('flowObj','flow')" :loading="addItemLoading">确定</Button>
		  <Button type="primary" v-if="flowType === 'update'" @click="updateItem('flowObj','flow')" :loading="addItemLoading">确定</Button>
		</div>
	</Modal>
  </Card>
</template>

<script>
  import {
    getRule,
    update,
	addItem,
	updateItem,
	deleteItem,
	checkDelete,
	deleteBatch
  } from '@/api/customer/manufacturer';
  import {
    getCountryList
  } from '@/api/customer/cooperative';
  const math = require('mathjs')
  export default {
    components: {

    },
    data() {
      return {
		pkgetotal: 0,
		pkgepageSize: 10,
		pkgepage: 1,
		flowtotal: 0,
		flowpageSize: 10,
		flowpage: 1,
		checked:'1',
		pkgeType: 'add',
		pkgeTitle:'添加套餐',
		pkgeEditFlag: false,
		flowType: 'add',
		flowTitle:'添加方向',
		flowEditFlag: false,
		currencyOne:'',//保存最初的币种
		//终端厂商编辑数据结构
    editObj: {
      'corpId': '',
      'corpName': '', //终端厂商名称
      'eopCreateType': '', //APPkey&APPSecret生成方式
      'appKey': '',
      'appSecret': '',
      'notifyUrl': '', // URL
      'ebsCode': '',
      'activeNotifyType': false, //激活通知开关
      'dueNotifyType': false, //到期通知开关
      'type': '', //终端厂商类型
      'settleType': '', //付费模式
      'packages': [], //套餐集合
      'flowList': [], //流量集合
      'currencyCode': '',
      'internalOrder': '',
      'address': '',
      'companyName': '',
    },
		//终端厂商编辑数据校验规范
		ruleEditValidate: {
		  corpName: [{
		    required: true,
		    type: 'string',
		    message: '终端厂商名称不能为空',
		    trigger: 'blur'
		  },
		  { max: 50,message:'最长50位'}
		  ],
		  eopCreateType: [{
		    required: true,
		    type: 'string',
		    message: 'APPkey&APPSecret生成方式不能为空',
		    trigger: 'change'
		  }
		  ],
		  appKey: [{
		    required: true,
		    type: 'string',
		    message: 'APPkey不能为空',
		    trigger: 'blur'
		  },
		  { max: 255,message:'最长255位'}
		  ],
		  appSecret: [{
		    required: true,
		    type: 'string',
		    message: 'APPSecret不能为空',
		    trigger: 'blur'
		  },
		  { max: 255,message:'最长255位'}
		  ],
      companyName: [{
        required: true,
        type: 'string',
        message: '公司名称不能为空',
        trigger: 'blur'
      },
      { max: 50,message:'最长50位'}
      ],
      address: [{
        required: true,
        type: 'string',
        message: '地址不能为空',
        trigger: 'blur'
      },
      { max: 200,message:'最长200位'}
      ],
      internalOrder: [{
        required: true,
        type: 'string',
        message: '内部订单不能为空',
        trigger: 'change'
      }
      ],
		  ebsCode: [{
		    required: true,
		    type: 'string',
		    message: 'EBS Code不能为空',
		    trigger: 'blur'
		  },
		  { max: 50,message:'最长50位'}
		  ],
		  notifyUrl: [{
		    required: true,
		    type: 'string',
		    message: 'URL不能为空',
		    trigger: 'blur'
		  },
		  { max: 255,message:'最长255位'}
		  ],
		  activeNotifyType: [{
		    required: true,
		    type: 'boolean',
		    message: '激活通知开关不能为空',
		    trigger: 'blur'
		  }],
		  dueNotifyType: [{
		    required: true,
		    type: 'boolean',
		    message: '到期通知开关不能为空',
		    trigger: 'blur'
		  }],
		  type: [{
		    required: true,
		    type: 'string',
		    message: '厂商类型不能为空',
		    trigger: 'change'
		  }],
		  settleType: [{
		    required: true,
		    type: 'string',
		    message: '付费模式不能为空',
		    trigger: 'change'
		  }],
      currencyCode: [{
        required: true,
        type: 'string',
        message: '币种不能为空',
        trigger: 'change'
      }],
		  packages: [{
		    type: 'array',
		    required: true,
		    message: '请选择套餐',
		    trigger: 'change'
		  }],
		  flowList: [{
		    type: 'array',
		    required: true,
		    message: '请填写方向',
		    trigger: 'change'
		  }]
		},
		//套餐添加/编辑数据结构
		pkgeObj: {
			'packageName': '',
			// 'currencyCode': '',
			'price': ''
		},
		//套餐数据校验规范
		pkgeValidate: {
		  packageName: [
			{required: true, message: '套餐名称不能为空', trigger: 'blur'},
			{ max: 200,message:'最长200位'}
		  ],
		 //  currencyCode: [
			// {required: true, message: '币种不能为空',trigger: 'change'}
		 //  ],
		  price: [
			{required: true, message: '流量单价不能为空',trigger: 'blur'},
			{pattern: /^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger: 'blur', message: '请输入1-12位数字，整数首位非0（可精确到小数点后2位）' },
		  ],
		},
		//方向添加/编辑数据结构
		flowObj: {
			'packageName': '',
			'mccList': [],
			'mcc': '',
			// 'currencyCode': '',
			'price': ''
		},
		//方向数据校验规范
		flowValidate: {
		  packageName: [
			{required: true, message: '套餐名称不能为空', trigger: 'blur'},
			{ max: 200,message:'最长200位'}
		  ],
		  mccList: [
			{type:'array',required: true, message: '流量方向不能为空', trigger: 'change'}
		  ],
		  mcc: [
			{type:'array',required: true, message: '流量方向不能为空', trigger: 'change'}
		  ],
		 //  currencyCode: [
			// {required: true, message: '币种不能为空',trigger: 'change'}
		 //  ],
		  price: [
			{required: true, message: '流量单价不能为空',trigger: 'blur'},
			{pattern: /^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger: 'blur', message: '请输入1-12位数字，整数首位非0（可精确到小数点后2位）' },
		  ],
		},
		appMethodList: [{
		  'value': '1',
		  'label': '自动生成'
		}, {
		  'value': '2',
		  'label': '手动输入'
		}], //通知类型
		manufacturerTypeList: [{
		  'value': '7',
		  'label': '线上厂商'
		}, {
		  'value': '8',
		  'label': '线下厂商'
		}], //厂商类型
		paymentModeList: [{
		  'value': '1',
		  'label': '按套餐'
		}, {
		  'value': '2',
		  'label': '按流量'
		}], //付费模式
    pkgeTableLoading: false,
		pkgeColumns: [
		  {
		    title: '套餐名称',
		    key: 'packageName',
		    align: 'center',
		    minWidth: 120,
		    tooltip: true,
		    tooltipMaxWidth: 2000,
		  },
		  {
		    title: "币种",
		    key: "currencyCode",
		    align: "center",
		    minWidth: 100,
		    tooltip: true,
		    render: (h, params) => {
		      const row = params.row;
		      //156 CNY,840 美元, 344 港币
		      const text =
		        row.currencyCode == "156"
		          ? "人民币"
		          : row.currencyCode == "840"
		          ? "美元"
		          : row.currencyCode == "344"
		          ? "港币"
		          : "获取失败";
		      return h("label", text);
		    }
		  },
		  {
		    title: '套餐价格',
		    key: 'price',
		    align: 'center',
		    minWidth: 120,
		    tooltip: true,
		    tooltipMaxWidth: 2000,
		  },
		  {
		    title: '操作',
		    slot: 'action',
		    minWidth: 160,
		    align: 'center'
		  }
		],
		pkgeTableData: [],
		flowColumns: [
		  {
		    title: '流量方向',
		    key: 'countryName',
		    align: 'center',
		    minWidth: 120,
		    tooltip: true,
		    tooltipMaxWidth: 2000,
		  },
		  {
		    title: "币种",
		    key: "currencyCode",
		    align: "center",
		    minWidth: 100,
		    tooltip: true,
		    render: (h, params) => {
		      const row = params.row;
		      //156 CNY,840 美元, 344 港币
		      const text =
		        row.currencyCode == "156"
		          ? "人民币"
		          : row.currencyCode == "840"
		          ? "美元"
		          : row.currencyCode == "344"
		          ? "港币"
		          : "获取失败";
		      return h("label", text);
		    }
		  },
		  {
		    title: '流量单价',
		    key: 'price',
		    align: 'center',
		    minWidth: 120,
		    tooltip: true,
		    tooltipMaxWidth: 2000,
		  },
		  {
		    title: '操作',
		    slot: 'action',
		    minWidth: 160,
		    align: 'center'
		  }
		],
		flowTableData: [],
		flowTableLoading: false,
		addLoading: false,
		addItemLoading: false,
		continentList: [],//国家列表
		corpId: '',
		manufacturer: {}
      }
    },
    methods: {
		changeSettleType(){//切换结算类型
			//获取国家/地区信息
			if(this.editObj.settleType === '2'){
				this.getLocalList();
			}
			if(this.editObj.settleType === '1' || this.editObj.settleType === '2'){
				this.getRule(1)
			}
		},
		changeType(){//切换厂商类型
			//获取国家/地区信息
			if(this.editObj.type === '8' && (this.editObj.settleType === '1' || this.editObj.settleType === '2')){
				this.getRule(1)
			}
		},
		getLocalList(){
		  getCountryList().then(res => {
		    if (res && res.code == '0000') {
		      var list = res.data;
		      this.continentList = list;
		      this.continentList.sort(function(str1, str2) {
		        return str1.countryEn.localeCompare(str2.countryEn);
		      });
		    } else {
		      throw res
		    }
		  }).catch((err) => {
		  })
		},
		getRule(page){
			//获取基本信息
			var t = this
			var corpId = t.editObj.corpId
			var settleType = t.editObj.settleType
			let pageSize = 10
			let pageNumber = page
			this.pkgepage = page
			this.flowpage = page
			getRule({corpId,settleType,pageSize,pageNumber}).then(res => {
			    if (res && res.code == '0000') {
			      var data = res.data
				  if(t.editObj.settleType === '1'){
					  t.pkgeTableData = data ? data:[]
					  t.pkgetotal = res.count
				  }else{
					  t.flowTableData = data ? data:[]
					  t.flowtotal = res.count
				  }
			    } else {
			      throw res
			    }
			}).catch((err) => {
			})
		},
		//表格数据加载
		loadByPage(e) {
			this.pkgepage = e
			this.getRule(e)
		},
		flowloadByPage(e){
			this.flowpage = e
			this.getRule(e)
		},
		//编辑
		update(name) {
		  var t = this
			console.log(t.editObj.settleType)
			console.log(t.pkgeTableData)
		  if (t.editObj.settleType === '1' && t.pkgeTableData.length <= 0 ) {
			t.$Message.warning('请添加套餐')
			return
		  }
		  if (t.editObj.settleType === '2' && t.flowTableData.length <= 0) {
			t.$Message.warning('请添加流量')
			return
		  }
		  t.$refs[name].validate((valid) => {
		    if (valid) {
		      t.addLoading = true
		      var data = {
		        'corpName': t.editObj.corpName, //终端厂商名称
		        'eopCreateType': this.editObj.eopCreateType, //APPkey&APPSecret生成方式
		        'appKey': t.editObj.appKey,
		        'appSecret': t.editObj.appSecret,
		        'notifyUrl': t.editObj.notifyUrl, // URL
		        'ebsCode': t.editObj.ebsCode,
		        'activeNotifyType': t.editObj.activeNotifyType === true ? '1' : '2', //激活通知开关
		        'dueNotifyType': t.editObj.dueNotifyType === true ? '1' : '2', //到期通知开关
		        'type': t.editObj.type, //终端厂商类型
		        // 'settleType': t.editObj.settleType, //付费模式
            'currencyCode': t.editObj.currencyCode,
            'address': t.editObj.address,
            'companyName': t.editObj.companyName,
            'internalOrder': t.editObj.internalOrder,
		      }
		      update(t.editObj.corpId,data).then(res => {
		        if (res && res.code == '0000') {
		          t.$Notice.success({
		            title: '操作提示',
		            desc: '操作成功'
		          })
		          t.addLoading = false
				  //跳转回终端分页
				  this.$router.push({
				    name: 'manufacturerIndex',
				  })
		        } else {
		          throw res
		        }
		      }).catch((err) => {
		        t.addLoading = false
		      })
		    }
		  })
		},
		ruleAdd(type){ //添加
			if(type === 'pkge'){
				this.pkgeType = 'add'
				this.pkgeEditFlag = true
				this.pkgeTitle = '新增套餐'
				this.pkgeObj= {
					'packageName': '',
					// 'currencyCode': '',
					'price': ''
				}
				this.$refs.pkgeObj.resetFields()
			}
			if(type === 'flow'){
				this.flowType = 'add'
				this.flowEditFlag = true
				this.flowTitle = '新增方向'
				this.flowObj= {
					'packageName': '',
					'mccList': [],
					'mcc': '',
					// 'currencyCode': '',
					'price': ''
				}
				this.$refs.flowObj.resetFields()
			}
		},
		ruleUpdate(row,type){
			if(type === 'pkge'){ //编辑套餐
				this.pkgeType = 'update'
				this.pkgeEditFlag = true
				this.pkgeTitle = '编辑套餐'
				this.pkgeObj= {
					'id': row.id,
					'packageName': row.packageName,
					// 'currencyCode': row.currencyCode,
					'price': row.price
				}
				this.pkgeObj.price = this.pkgeObj.price.toString()
			}
			if(type === 'flow'){ //编辑流量
				this.flowType = 'update'
				this.flowEditFlag = true
				this.flowTitle = '编辑流量'
				this.flowObj= {
					'ids': row.ids,
					'packageName': row.packageName,
					'mccList': row.mccList,
					// 'currencyCode': row.currencyCode,
					'price': row.price
				}
				this.flowObj.price = this.flowObj.price.toString()
			}
		},
		itemAdd(name,type){
			var t = this
			t.$refs[name].validate((valid) => {
				if (valid) {
					t.addItemLoading = true
					var data = {
						corpId: t.editObj.corpId,
						settleType: t.editObj.settleType,
						currencyCode:t.currencyOne
					}
					if(type === 'pkge'){
						// data.currencyCode =  t.pkgeObj.currencyCode
						data.packageName = t.pkgeObj.packageName
						data.price = math.multiply(math.bignumber(t.pkgeObj.price),100).toString()
					}else{
						// data.currencyCode =  t.flowObj.currencyCode
						data.packageName = t.editObj.corpName+"流量套餐"
						data.price = math.multiply(math.bignumber(t.flowObj.price),100).toString()
						data.mccList = t.flowObj.mccList
					}
					//添加结算规则
					addItem(data).then(res => {
					    if (res && res.code == '0000') {
					      var data = res.data
					      t.tableData = data
						  t.addItemLoading = false
						  t.$Notice.success({
						    title: '操作提示',
						    desc: '操作成功'
						  })
						  t.reset()
						  t.getRule()
					    } else {
					      throw res
					    }
					}).catch((err) => {
						t.addItemLoading = false
						// t.reset()
					})
				}
			})
		},
		updateItem(name,type){
			var t = this
			t.$refs[name].validate((valid) => {
				if (valid) {
					t.addItemLoading = true
					var data = {
						corpId: t.editObj.corpId,
						settleType: t.editObj.settleType,
						currencyCode:t.currencyOne
					}
					if(type === 'pkge'){
						// data.currencyCode =  t.pkgeObj.currencyCode
						data.packageName = t.pkgeObj.packageName
						data.price = math.multiply(math.bignumber(t.pkgeObj.price),100).toString()
						data.id = t.pkgeObj.id
					}else{
						// data.currencyCode =  t.flowObj.currencyCode
						data.packageName = t.flowObj.packageName
						data.price = math.multiply(math.bignumber(t.flowObj.price),100).toString()
						data.mccList = t.flowObj.mccList
						// data.id = t.flowObj.id
						data.ids=t.flowObj.ids
					}
					updateItem(data).then(res => {
					  if (res && res.code == '0000') {
						t.$Notice.success({
						  title: '操作提示',
						  desc: '操作成功'
						})
						t.addItemLoading = false
						t.reset()
						t.getRule()
					  } else {
						throw res
					  }
					}).catch((err) => {
						t.addItemLoading = false
						// t.reset()
					})
				}
			})
		},
		reset(){
			this.pkgeEditFlag = false
			this.flowEditFlag = false
		},
		deleteItem(row,type){
			let packageId = row.packageId
			checkDelete({packageId}).then(res => {
			  if (res && res.code == '0000') {
				var result = res.data
				var tt = result === true ? '确认删除?':'删除会造成已绑定的卡池激活失败，请谨慎删除!'
				this.$Modal.confirm({
				  title: tt,
				  onOk: () => {
					// 判断是套餐删除还是流量删除
					if(type==='flow'){
						this.deleteBatch(row.ids)
					}else{
						this.delRule(row.id)
					}
					
				    
				  }
				})
			  } else {
				throw res
			  }
			}).catch((err) => {
			})
		},
		delRule(data){
			deleteItem(data).then(res => {
			  if (res && res.code == '0000') {
				this.$Notice.success({
				  title: '操作提示',
				  desc: '操作成功'
				})
				this.getRule(1)
			  } else {
				throw res
			  }
			}).catch((err) => {
			})
		},
		deleteBatch(data){
			deleteBatch(data).then(res => {
			  if (res && res.code == '0000') {
				this.$Notice.success({
				  title: '操作提示',
				  desc: '操作成功'
				})
				this.getRule(1)
			  } else {
				throw res
			  }
			}).catch((err) => {
			})
		},
		back(){
			//跳转回终端分页
			this.$router.push({
			  name: 'manufacturerIndex',
			})
		},
		tagChange:function(name){
		  this.pkgepage = 1
		  this.flowpage = 1
		  this.checked = name
		  this.editObj.settleType=name
		  this.getRule(1)
		},
		
    },
    mounted() {
      var manufacturer = JSON.parse(decodeURIComponent(this.$route.query.manufacturer));
      this.manufacturer = manufacturer;
      this.editObj = manufacturer;
      this.editObj.activeNotifyType = this.editObj.activeNotifyType === '1' ? true : false
      this.editObj.dueNotifyType = this.editObj.dueNotifyType === '1' ? true : false
	  this.currencyOne=this.editObj.currencyCode
      // if(this.editObj.settleType === '2'){
        this.getLocalList();
      // }
      if(this.editObj.type === '8'){
        this.getRule(1);
      }
    }
  }
</script>

<style>
  .inputSty {
    width: 200px;
  }
</style>
