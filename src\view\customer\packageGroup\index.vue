<template>
	<Card style="width: 100%; padiing: 16px">
		<Form ref="searchForm" :model="searchObj" inline @submit.native.prevent>
			<FormItem>
				<Input v-model="searchObj.groupName" placeholder="请输入套餐组名称" clearable style="width: 200px" />
			</FormItem>
			<FormItem>
				<Select v-model="searchObj.groupType" placeholder="请选择套餐组类型" style="width: 200px" clearable>
					<Option value="1">二次定价</Option>
					<Option value="2">非二次定价</Option>
				</Select>
			</FormItem>
			<FormItem>
				<Button style="margin: 0 2px" type="primary" v-has="'search'" @click="searchPackageGroup">
					<div style="display: flex; align-items: center">
						<Icon type="ios-search" />&nbsp;搜索
					</div>
				</Button>
				<Button style="margin: 0 2px" type="info" v-has="'add'" @click="packageGroupAdd">
					<div style="display: flex; align-items: center">
						<Icon type="md-add" />&nbsp;新增
					</div>
				</Button>
				<Button style="margin: 0 2px" type="error" v-has="'batchDelete'" @click="deleteList">
					<div style="display: flex; align-items: center">
						<Icon type="ios-trash" />&nbsp;批量删除
					</div>
				</Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading"
				@on-selection-change="handleRowChange" @on-select-cancel="cancelSigle"
				@on-select-all-cancel="cancelAll">
				<template slot-scope="{ row, index }" slot="include">
					<Button type="info" size="small" style="margin-right: 5px" v-has="'view'"
						@click="packageGroupInfo(row)">详情</Button>
				</template>
				<template slot-scope="{ row, index }" slot="action">
					<Button type="success" size="small" v-has="'update'" style="margin-right: 5px"
						@click="packageGroupEdit(row)" :disabled="row.isChannelCreate == '1'">编辑</Button>
					<Button type="error" size="small" :disabled="row.isChannelCreate == '1'" v-has="'delete'" style="margin-right: 5px" @click="packageGroupDel(row.groupId)">删除</Button>
					<Button type="primary" size="small" v-has="'export'" @click="exportfile(row)">导出</Button>
				</template>
				<template slot-scope="{ row, index }" slot="approval">
					<Button type="success" size="small" style="margin-right: 5px" v-has="'check'"
						v-if="[1].includes(+row.auditStatus)" @click="cooperativeApproval(row, '2')">通过</Button>
					<Button type="error" size="small" v-has="'check'" v-if="[1].includes(+row.auditStatus)"
						@click="cooperativeApproval(row, '3')">不通过</Button>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0" />
		</div>
		<!-- 套餐组修改 -->
		<Modal :title="packageGroupTitle" v-model="packageGroupEditFlag" :footer-hide="true" :mask-closable="false"
			@on-cancel="cancelModal">
			<div style="padding: 0 16px">
				<Form ref="editObj" :model="editObj" :label-width="118" :rules="ruleEditValidate">
					<FormItem label="套餐组名称" prop="groupName">
						<Input v-model="editObj.groupName" :clearable="true" maxlength="50"
							placeholder="请输入套餐组名称"></Input>
					</FormItem>
					<FormItem label="合作模式" prop="cooperationMode">
						<Select :disabled="operationType == 'Update'" v-model="editObj.cooperationMode" placeholder="请选择合作模式"
							style="width: 200px">
							<Option value="1">代销</Option>
							<Option value="2">A2Z</Option>
						</Select>
					</FormItem>
					<FormItem label="套餐组类型" prop="groupType">
						<Select :disabled="operationType !== 'Add'" v-model="editObj.groupType" placeholder="请选择套餐组类型"
							style="width: 200px">
							<Option value="1">二次定价</Option>
							<Option value="2">非二次定价</Option>
						</Select>
					</FormItem>
					<FormItem label="套餐列表选择方式" prop="choseType">
						<Select v-model="editObj.choseType" placeholder="请选择方式" style="width: 200px">
							<Option value="1">页面选择展示</Option>
							<Option value="2">文件上传展示</Option>
						</Select>
					</FormItem>
					<FormItem label="套餐列表" v-if="editObj.choseType==='1'">
						<Select v-if="editObj.choseType==='1'" v-model="editObj.packages" multiple
							class="packages-two-price" placeholder="请选择套餐" @on-open-change="selectionDropdwon"
							:clearable="true">
							<div class="select-list" slot="prefix">
								<Tag v-for="(item, index) in editObj.packages" :key="index" closable
									@on-close="deletePackages(index)">
									{{
                  item.packageName
                }}
								</Tag>
							</div>
							<!-- 支持套餐名称搜索 -->
							<Form ref="form" style="display: flex;width: 100%;">
								<FormItem label="套餐名称:" style="display: flex;">
									<Input v-model="packagegroupName" :clearable="true" maxlength="50"
										style="width: 200px" placeholder="请输入套餐名称"></Input>
								</FormItem>
								<FormItem>
				  			<Button style="margin: 0 2px" type="primary" v-has="'search'"
										@click="searchPackagegroupName">
										<div style="display: flex; align-items: center">
											<Icon type="ios-search" />&nbsp;搜索
				  				</div>
				  			</Button>
								</FormItem>
							</Form>

							<Table :columns="packageSelectColumns" :data="packageSelectTableData" :ellipsis="true"
								:loading="packageLoading" @on-select="selectPackage" @on-select-cancel="cancelPackage"
								@on-select-all="selectPackage" @on-select-all-cancel="cancelPackageAll"
								@on-row-click="onRowClick" style="margin-top: 10px;">
							</Table>

							<Page :style="{ margin: '10px 20px' }" :total="packageSelectTableTotal" :page-size="10"
								:current.sync="packageSelectTableCurrent" show-total show-elevator
								@on-change="loadByPackageSelectTableCurrentPage" style="margin: 15px 0" />
						</Select>
					</FormItem>
					<FormItem label="上传套餐列表" prop="file" v-if="editObj.choseType==='2'">
						<div style="display: flex;">
							<Upload v-model="editObj.file" :action="uploadUrl" :on-success="fileSuccess"
								:on-error="handleError" :before-upload="handleBeforeUpload" ref="upload"
								:on-progress="fileUploading">
								<Button icon="ios-cloud-upload-outline">点击上传</Button>
							</Upload>
							<div style="width: 500px;margin-left: 50px;">
								<Button type="primary" icon="ios-download" @click="downloadFile">下载模板</Button>
							</div>
						</div>
						<ul class="ivu-upload-list" v-if="file">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />{{file.name}}
								</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style=""
									@click="removeFile"></i>
							</li>
						</ul>
					</FormItem>
				</Form>
				<div style="text-align: center">
					<Button type="primary" @click="submit('editObj')" v-if="operationType == 'Add'"
						v-has="'add'">提交</Button>
					<Button type="primary" @click="submit('editObj')" v-if="operationType == 'Update'"
						v-has="'update'">提交</Button>
					<Button style="margin-left: 8px" @click="reseteditObj('editObj',1)">重置</Button>
				</div>
			</div>
		</Modal>
		<!-- 二次定价模板文件table -->
		<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
		<!-- 非二次定价模板文件table -->
		<Table :columns="NotmodelColumns" :data="NotmodelData" ref="modelTable" v-show="false"></Table>
		<!-- 套餐组详情 -->
		<Modal title="套餐组详情" v-model="packageGroupInfoFlag" :footer-hide="true" :mask-closable="false" width="950px"
			@on-cancel="cancelModal">
			<Form :label-width="100" style="font-weight: bold;">
				<div>
					<FormItem label="套餐组名称:">
						<span>{{info.groupName}}</span>
					</FormItem>
				</div>
				<div>
					<FormItem label="套餐组类型:">
						<span>{{info.groupType === '1' ? '二次定价' : info.groupType === '2' ? '非二次定价' : ''}}</span>
					</FormItem>
				</div>
				<div>
					<FormItem label="合作模式:">
						<span>{{info.cooperationMode==='1' ? '代销' : info.cooperationMode==='2' ? 'A2Z':''}}</span>
					</FormItem>
				</div>
			</Form>
			<div style="padding: 0 16px">
				<Table :columns="packageColumns" :data="packageTableData" :ellipsis="true" :loading="packageLoading"
					max-height="500"></Table>
				<div class="table-botton" style="margin-top: 15px">
					<Page :total="packageTotal" :page-size="packagePageSize" :current.sync="packageCurrentPage"
						show-total show-elevator @on-change="loadByPackagePage" style="margin: 15px 0" />
				</div>
			</div>
		</Modal>
		<a ref="downloadLink" style="display: none"></a>
	</Card>
</template>

<script>
	import {
		queryPackageGroupRelation,
		queryPackageGroupDetail,
		updatePackageGroup,
		deletePackageGroup,
		getChoosePackageList,
		deletePackageGroupSingle,
		addNewPackageGroup,
		updateNewPackageGroup,
		approvalPackageGroup,
		createByFile,
		exportFile,
	} from "@/api/customer/channelShop.js";
	import expandRow from '@/components/tables/authtable_expand';
	export default {
		components: {},
		// props:['row','editObj'],
		data() {
			const validateTwicePrice = (rule, value, cb) => {
				return !this.twicePriceFormat;
			};
			// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (this.uploadList && this.uploadList.length === 0) {
					callback(new Error('请上传文件'))
				} else {
					callback()
				}
			}
			return {
				searchObj: {
					groupName: "", //套餐组名称
					groupType: "", //套餐组类型
				},
				uploadList: [],
				packagegroupName: '', //套餐名称
				uploadUrl: '',
				file: null,
				/**
				 * 二次定价模板
				 */
				modelData: [{
					'套餐ID': '********',
					'加油包ID': '********',
					'人民币价格': '********',
					'港币价格': '********',
					'美元价格': '********',
				}, ],
				modelColumns: [{
						title: '套餐ID',
						key: '套餐ID'
					}, // 列名根据需要添加
					{
						title: '加油包ID',
						key: '加油包ID'
					}, // 列名根据需要添加
					{
						title: '人民币价格',
						key: '人民币价格'
					}, // 列名根据需要添加
					{
						title: '港币价格',
						key: '港币价格'
					}, // 列名根据需要添加
					{
						title: '美元价格',
						key: '美元价格'
					} // 列名根据需要添加
				],
				/**
				 * 非二次定价模板
				 */
				NotmodelData: [{
					'套餐ID': '********',
					'加油包ID': '********',
				}, ],
				NotmodelColumns: [{
						title: '套餐ID',
						key: '套餐ID'
					}, // 列名根据需要添加
					{
						title: '加油包ID',
						key: '加油包ID'
					}, // 列名根据需要添加
				],
				twicePriceFormat: false,
				packageGroupEditFlag: false,
				packageGroupInfoFlag: false,
				packageGroupTitle: "",
				editObj: {
					groupName: "", //套餐组名称
					cooperationMode: "",//合作模式
					groupType: "", //套餐组类型
					choseType: "", //套餐组选择方式
					file: "", //上传文件
					packages: [{
						refuelDetailList: [], //加油包组
					}], //套餐组
				},
				tempSelectDataArrzj: [],
				editObjreset: {},
				currentRow: "", //当前的行
				packageList: [],
				ruleEditValidate: {
					groupName: [{
						required: true,
						type: "string",
						message: "套餐组名称不能为空",
					}, ],
					cooperationMode: [{
						required: true,
						message: "合作模式不能为空",
						trigger: "change",
					}, ],
					groupType: [{
						required: true,
						message: "套餐组类型不能为空",
						trigger: "change",
					}, ],
					choseType: [{
						required: true,
						message: "套餐列表选择方式不能为空",
						trigger: "change",
					}, ],
		
				file: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
					packages: [{
							required: true,
							message: "套餐组列表不能为空",
						},
						{
							validator: validateTwicePrice,
							message: "二次定价格最高支持10位整数和2位小数正数或零",
						},
					],

					"packages.index.cny": [{
						required: true,
						message: "人民币不能为空",
					}, ],
					"packages.index.hkd": [{
						required: true,
						message: "港币不能为空",
					}, ],
					"packages.index.usd": [{
						required: true,
						message: "美元不能为空",
					}, ],
				},
				ruleEditValidateadd: {
					groupName: [{
						required: true,
						type: "string",
						message: "套餐组名称不能为空",
					}, ],
					cooperationMode: [{
						required: true,
						message: "合作模式不能为空",
						trigger: "change",
					}, ],
					groupType: [{
						required: true,
						message: "套餐组类型不能为空",
						trigger: "change",
					}, ],
					choseType: [{
						required: true,
						message: "套餐列表选择方式不能为空",
						trigger: "change",
					}, ],
					file: [{
						required: true,
						validator: validateUpload,
		 			trigger: 'change',
					}],
					packages: [{
							required: true,
							message: "套餐组列表不能为空",
						},
						{
							validator: validateTwicePrice,
							message: "二次定价格最高支持10位整数和2位小数正数或零",
						},
					],

					"packages.index.cny": [{
						required: true,
						message: "人民币不能为空",
					}, ],
					"packages.index.hkd": [{
						required: true,
						message: "港币不能为空",
					}, ],
					"packages.index.usd": [{
						required: true,
						message: "美元不能为空",
					}, ],
				},
				operationType: "Add",
				tableData: [], //列表信息
				selection: [], //多选
				selectionIds: [], //多选ids
				selectionList: [], //翻页勾选
				tableLoading: false,
				total: 0,
				pageSize: 10,
				page: 1,
				columns: [{
						type: "selection",
						minWidth: 60,
						maxWidth: 60,
						align: "center",
					},
					{
						title: "套餐组名称",
						key: "groupName",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "创建时间",
						key: "createTime",
						align: "center",
						minWidth: 120,
						tooltip: true,
					},
					{
						title: "套餐组类型",
						key: "groupType",
						align: "center",
						minWidth: 120,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const text =
								row.groupType === "1" ? "二次定价" : row.groupType === "2" ? "非二次定价" : "";
							return h("label", text);
						},
					},
					{
						title: "包含套餐",
						slot: "include",
						minWidth: 120,
						align: "center",
					},
					{
						title: "操作",
						slot: "action",
						minWidth: 300,
						align: "center",
					},		
					{
						title: "审批状态",
						key: "checkStatus",
						align: "center",
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							const color =
								row.auditStatus == "3" ?
								"#ff0000" :
								row.auditStatus == "2" ?
								"#00cc66" :
								"#27A1FF";
							const text =
								row.auditStatus == "3" ?
								"审批未通过" :
								row.auditStatus == "2" ?
								"审批通过" :
								"待审批";
							return h(
								"label", {
									style: {
										color: color,
									},
								},
								text
							);
						},
					},
					{
						title: "审批操作",
						slot: "approval",
						minWidth: 150,
						align: "center",
					},
				],
				//套餐表格
				packagePageSize: 10,
				packagePage: 0,
				packageLoading: false,
				packageCurrentPage: 1,
				packageTotal: 2,
				packageColumns: [{
						title: "套餐ID",
						key: "id",
						align: "center",
						minWidth: 130,
					},
					{
						title: "套餐名称",
						key: "packageName",
						align: "center",
						minWidth: 130,
					},
					{
						title: "覆盖国家/地区",
						key: "mcc",
						align: "center",
						minWidth: 110,
					},
					{
						title: "人民币价格",
						key: "cny",
						align: "center",
						minWidth: 80,
					},
					{
						title: "港币价格",
						key: "hkd",
						align: "center",
						minWidth: 80,
					},
					{
						title: "美元价格",
						key: "usd",
						align: "center",
						minWidth: 80,
					},
					
				],
				packageTableData: [],
				// 新增二次定价变量
				packageSelectTableData: [],
				tempSelectDataArr: [],
				packageSelectColumns: [{
						type: "selection",
						width: 60,
						align: "center",
					},
					{ //添加的expand
						type: 'expand',
						width: 20,
						render: (h, params) => {
							return h(expandRow, {
								style: {
									padding: 0
								},
								props: {
									row: params.row,
									editObj: this.editObj,
									tempSelectDataArrzj: this.tempSelectDataArrzj,
								}
							})
						}
					},
					{
						title: "套餐名称",
						key: "nameCn",
						align: "center",
					},

					{
						title: "人民币价格",
						key: "cny",
						width: 150,
						align: "center",
						render: (h, {
							row,
							column,
							index
						}) => {
							if (this.editObj.groupType === "1") {
								let cancelSection = () => {
									if (this.tempSelectDataArr.length) {
										let flag = false;
										this.tempSelectDataArr.forEach((ele) => {
											if (ele.id === this.packageSelectTableData[index].id) {
												flag = true;
												ele.cny = this.packageSelectTableData[index].cny;
												ele.hkd = this.packageSelectTableData[index].hkd;
												ele.usd = this.packageSelectTableData[index].usd;
											}
										});
										if (!flag) {
											this.tempSelectDataArr.push(
												this.packageSelectTableData[index]
											);
										}
									} else {
										this.tempSelectDataArr.push(
											this.packageSelectTableData[index]
										);
									}

									this.editObj.packages.forEach((ele) => {
										if (ele.packageId === this.packageSelectTableData[index].id) {
											ele.cny = this.packageSelectTableData[index].cny
											this.$nextTick(() => {
												if (document.querySelector(".errorPrice")) {
													this.twicePriceFormat = true;
												} else {
													this.twicePriceFormat = false;
												}
											});
										}
									});
								};

								this.editObj.packages.forEach((ele) => {
									if (ele.packageId === this.packageSelectTableData[index].id) {
										this.packageSelectTableData[index]._checked = true;
									}
								});

								return ( <
									div >
									<
									Input maxlength = "13"
									v-model = {
										this.packageSelectTableData[index].cny
									}
									v-on: on-change = {
										cancelSection
									}
									placeholder = "请输入二次定价价格" /
									> {
										/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(
											this.packageSelectTableData[index].cny
										) ? null : ( <
											p class = "errorPrice"
											style = "text-align: left;color:red" >
											二次定价格最高支持10位整数和2位小数正数或零 <
											/p>
										)
									} <
									/div>
								);
							} else {
								return <span > {
									row.cny
								} < /span>;
							}
						},
					},
					{
						title: "港币价格",
						key: "hkd",
						width: 150,
						align: "center",
						render: (h, {
							row,
							column,
							index
						}) => {
							if (this.editObj.groupType === "1") {
								let cancelSection = () => {
									if (this.tempSelectDataArr.length) {
										let flag = false;
										this.tempSelectDataArr.forEach((ele) => {
											if (ele.id === this.packageSelectTableData[index].id) {
												flag = true;
												ele.cny = this.packageSelectTableData[index].cny;
												ele.hkd = this.packageSelectTableData[index].hkd;
												ele.usd = this.packageSelectTableData[index].usd;
											}
										});

										if (!flag) {
											this.tempSelectDataArr.push(
												this.packageSelectTableData[index]
											);
										}
									} else {
										this.tempSelectDataArr.push(
											this.packageSelectTableData[index]
										);
									}
									this.editObj.packages.forEach((ele) => {
										if (ele.packageId === this.packageSelectTableData[index].id) {
											ele.hkd = this.packageSelectTableData[index].hkd
										}
									});
									this.$nextTick(() => {
										if (document.querySelector(".errorPrice")) {
											this.twicePriceFormat = true;
										} else {
											this.twicePriceFormat = false;
										}
									});
								};

								this.editObj.packages.forEach((ele) => {
									if (ele.packageId === this.packageSelectTableData[index].id) {
										this.packageSelectTableData[index]._checked = true;
									}
								});
								return ( <
									div >
									<
									Input v-model = {
										this.packageSelectTableData[index].hkd
									}
									v-on: on-change = {
										cancelSection
									}
									placeholder = "请输入二次定价价格" /
									>

									{
										/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(
											this.packageSelectTableData[index].hkd
										) ? null : ( <
											p class = "errorPrice"
											style = "text-align: left;color:red" >
											二次定价格最高支持10位整数和2位小数正数或零 <
											/p>
										)
									} <
									/div>
								);
							} else {
								return <span > {
									row.hkd
								} < /span>;
							}
						},
					},
					{
						title: "美元价格",
						key: "usd",
						width: 150,
						align: "center",
						render: (h, {
							row,
							column,
							index
						}) => {
							if (this.editObj.groupType === "1") {
								let cancelSection = () => {
									if (this.tempSelectDataArr.length) {
										let flag = false;
										this.tempSelectDataArr.forEach((ele) => {
											if (ele.id === this.packageSelectTableData[index].id) {
												flag = true;
												ele.cny = this.packageSelectTableData[index].cny;
												ele.hkd = this.packageSelectTableData[index].hkd;
												ele.usd = this.packageSelectTableData[index].usd;
											}
										});
										if (!flag) {
											this.tempSelectDataArr.push(
												this.packageSelectTableData[index]
											);
										}
									} else {
										this.tempSelectDataArr.push(
											this.packageSelectTableData[index]
										);
									}
									this.editObj.packages.forEach((ele) => {
										if (ele.packageId === this.packageSelectTableData[index].id) {
											ele.usd = this.packageSelectTableData[index].usd
											this.$nextTick(() => {
												if (document.querySelector(".errorPrice")) {
													this.twicePriceFormat = true;
												} else {
													this.twicePriceFormat = false;
												}
											});
										}
									});
								};

								this.editObj.packages.forEach((ele) => {
									if (ele.packageId === this.packageSelectTableData[index].id) {
										this.packageSelectTableData[index]._checked = true;
									}
								});
								return ( <
									div >
									<
									Input v-model = {
										this.packageSelectTableData[index].usd
									}
									v-on: on-change = {
										cancelSection
									}
									placeholder = "请输入二次定价价格" /
									> {
										/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(
											this.packageSelectTableData[index].usd
										) ? null : ( <
											p class = "errorPrice"
											style = "text-align: left;color:red" >
											二次定价格最高支持10位整数和2位小数正数或零 <
											/p>
										)
									} <
									/div>
								);
							} else {
								return <span > {
									row.usd
								} < /span>;
							}
						},
					},
				],
				packageSelectTableCurrent: 1,
				packageSelectTableTotal: 0,
				info: [],
			};
		},
		methods: {
			deletePackages(index) {
				this.editObj.packages.splice(index, 1)
			},
			formatMockData(data) {
				// 下划线转换驼峰
				function toHump(name) {
					return name.replace(/\_(\w)/g, function(all, letter) {
						return letter.toUpperCase();
					});
				}

				const arr = [];

				data.forEach((element) => {
					let obj = {};

					Object.keys(element).forEach((key) => {
						obj[toHump(key)] = element[key].value;
					});

					arr.push(obj);
				});

				console.log(arr[0]);

				return arr;
			},
			//表格初始化
			init() {
				this.getPackageGroupList(0);
				this.getPackageList(0);
			},
			changeType() {
			},
			getPackageGroupList(page) {
				if (page === 0) {
					this.page = 1;
				}

				queryPackageGroupRelation({
					groupName: this.searchObj.groupName,
					groupType: this.searchObj.groupType,
					pageSize: 10,
					pageNum: this.page,
				}).then((res) => {
					if (res.code === "0000") {
						// this.tableData = res.data.records;
						// this.total = res.data.total;
						var data = res.data;
						let List = []
						// 循环遍历data
						data.records.map((value, index) => {
							List.push(value)
						})
						//回显
						this.selectionList.forEach(item => {
							List.forEach(element => {
								if (element.groupId == item.groupId) {
									this.$set(element, '_checked', true)
								}
							})
						})
						this.tableData = List;
						this.total = data.total;
					}
				});
			},
			updatePackageGroup() {
				updatePackageGroup().then((res) => {
					if (res.code === "0000") {
						this.$Notice.success({
							title: "操作提示",
							desc: "更新成功",
						});
					}
				});
			},
			deletePackageGroup(id) {
				deletePackageGroupSingle({
					groupId: id
				}).then((res) => {
					if (res.code === "0000") {
						this.init();
						this.$Notice.success({
							title: "操作提示",
							desc: "删除成功",
						});
					}
				});
			},
			loadByPackageSelectTableCurrentPage(e) {
				this.getPackageList(e);
			},
			selectionDropdwon(flag) {
				this.packagegroupName = ""
				if (flag) {
					this.packageSelectTableCurrent = 1;
					this.getPackageList(0);
				}
			},
			// 查询套餐名称
			searchPackagegroupName() {
				this.getPackageList(0)
			},
			getPackageList(page) {
				if (page === 0) {
					this.packageSelectTableCurrent = 1;
				}
				getChoosePackageList({
					pageNum: page,
					pageSize: 10,
					groupId: this.operationType === "Update" ? this.editObj.groupId : '',
					packageName: this.packagegroupName
				}).then((res) => {
					if (res.code === "0000") {

						if (this.editObj.packages && this.editObj.packages.length) {
							this.editObj.packages.forEach((element, index) => {
								res.data.records.forEach((element1) => {
									if (element1.id === element.packageId) {
										element1["_checked"] = true;
									}
								});
							});
						}

						if (this.tempSelectDataArr.length) {
							this.tempSelectDataArr.forEach((ele) => {
								res.data.records.forEach((element1) => {
									if (element1.id === ele.id) {
										element1.cny = ele.cny;
										element1.hkd = ele.hkd;
										element1.usd = ele.usd;
									}
								});
							});
						}

						// 格式化显示时间
						// res.data.records.forEach(element1 => {
						//   element1.cny = this.$moneyCover(element1.cny, 1 / 100);
						//   element1.hkd = this.$moneyCover(element1.hkd, 1 / 100);
						//   element1.usd = this.$moneyCover(element1.usd, 1 / 100);
						// });

						this.packageSelectTableData = res.data.records;
						this.packageSelectTableTotal = res.data.total;


					}
				});
			},
			//提交
			submit(name) {
				this.$nextTick(() => {
					if (document.querySelector(".errorPrice")) {
						this.twicePriceFormat = true;
					} else {
						this.twicePriceFormat = false;
					}
				});
				this.$refs[name].validate((valid) => {
					if (valid) {
						let func;
						//文件上传 
						if (this.editObj.choseType === '2') {
							let formData = new FormData()
							formData.append('file', this.file)
							formData.append('groupName', this.editObj.groupName)
							formData.append('cooperationMode', this.editObj.cooperationMode)
							formData.append('groupType', this.editObj.groupType)
							if (this.operationType != "Add") {
								formData.append('groupId', this.editObj.groupId)
							}
							createByFile(formData).then((res) => {
								if (res.code === "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.file = null
									this.tempSelectDataArr = [];
									this.tempSelectDataArrzj = []
									this.packageGroupEditFlag = false;
									this.init();
									this.uploadList = []
									this.reset("editObj");
								}
							})
						} else { //非文件上传
							if (this.editObj.choseType === '1' && this.editObj.packages.length < 1) {
								this.$Message.warning('套餐组列表不能为空')
								return
							}
							if (this.operationType === "Add") {
								func = addNewPackageGroup;
							} else {
								func = updateNewPackageGroup;
							}
							func(this.editObj).then((res) => {
								if (res.code === "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.tempSelectDataArr = [];
									this.tempSelectDataArrzj = []
									this.init();
									this.packageGroupEditFlag = false;
									this.reset("editObj");
								}
							});
						}
					}
				});
			},
			reset(name, id) {
				this.packageSelectTableData = [];
				this.packageSelectTableTotal = 0;
				if (this.operationType === "Update" && id === 1) {
					this.$refs[name].fields.forEach(function(e) {
						if (e.prop !== "groupType") {
							e.resetField();
						}
					});
				} else {
					this.$refs[name].resetFields();
				}
			},
			reseteditObj(name, id) {
				this.packageSelectTableData = [];
				this.packageSelectTableTotal = 0;
				if (this.operationType === "Update" && id === 1) {
					this.$refs[name].fields.forEach(function(e) {
						if (e.prop !== "groupType") {
							e.resetField();
						}
					});
				} else {
					this.$refs[name].resetFields();
				}
				if (this.operationType === "Update") {
					let row = this.editObjreset
					this.editObj = {
						groupName: row.groupName, //套餐组名称
						cooperationMode: row.cooperationMode, //合作模式
						groupType: row.groupType, //套餐组名称
						groupId: row.groupId, //套餐组名称
						// packages: row.packages, //币种
						choseType: '1' //套餐列表选择方式
					};

					// 获取已经选择的套餐
					queryPackageGroupDetail({
						groupId: row.groupId,
						groupType: row.groupType,
						pageSize: -1,
						pageNum: 1,
					}).then((res) => {
						if (res.code === "0000") {
							if (res.data) {
								let arr = res.data.records.map((ele) => {
									//加油包组装
									let refuelDetailList = ele.refuelPackList.map((item) => {
										return {
											refuelId: item.id,
											cny: item.cny,
											hkd: item.hkd,
											usd: item.usd,
										}
									})
									return {
										packageName: ele.packageName,
										packageId: ele.id,
										cny: ele.cny,
										hkd: ele.hkd,
										usd: ele.usd,
										refuelDetailList: refuelDetailList
									};
								});
								this.editObj.packages = arr;
							}
						}
					});
				}
			},
			onRowClick(row, index) {
				// this.packageSelectTableData.forEach((item,i)=>{ //每次只能展开一个，其他自动收起
				// 	i !== index ? this.packageSelectTableData[i]._expanded = false : '';
				// })
				// this.packageSelectTableData[index]._expanded = !this.packageSelectTableData[index]._expanded;
				// this.packageSelectTableData.sort()
			},
			// 新增选择套餐包
			selectPackage(selection, row) {

				selection.forEach((select, index) => {
					//收缩展开项
					this.packageSelectTableData.splice();
					this.packageSelectTableData[index]._expanded = false

					// 获取对应的索引
					let _index = this.packageSelectTableData.findIndex(ele => {
						return ele.id == select.id
					})
					selection[index]._index = _index;
				})

				// 选中并传值
				let arr = selection.map((ele) => {
					this.packageSelectTableData[ele._index]._checked = true;

					//加油包组装
					let refuelDetailList = ele.refuelList.map((item) => {
						return {
							refuelId: item.id,
							cny: item.cny,
							hkd: item.hkd,
							usd: item.usd,
						}
					})
					return {
						packageName: ele.nameCn,
						packageId: ele.id,
						cny: ele.cny,
						hkd: ele.hkd,
						usd: ele.usd,
						refuelDetailList: refuelDetailList
					};
				});

				if (this.editObj.packages && this.editObj.packages.length) {
					let tempArr = [];

					arr.forEach((item) => {
						let flag = true;
						this.editObj.packages.forEach((ele) => {
							if (ele.packageId === item.packageId) {
								flag = false;
							}
						});
						if (flag) {
							//判断是否重复
							tempArr.push(item);
						}
					});

					this.editObj.packages = this.editObj.packages.concat(tempArr);
				} else {
					this.$set(this.editObj, "packages", arr);
				}
			},
			// 取消选择套餐包
			cancelPackage(selection, row) {
				if (this.editObj.packages && this.editObj.packages.length) {
					this.editObj.packages = this.editObj.packages.filter((ele) => {
						return ele.packageId !== row.id;
					});
				}

				this.packageSelectTableData.forEach((ele, index) => {
					if (ele.id === row.id) {
						ele._checked = false;
						//收缩展开项
						this.packageSelectTableData.splice();
						this.packageSelectTableData[index]._expanded = false
					}
				});
			},
			// 取消选择套餐包
			cancelPackageAll(selection, row) {
				if (this.editObj.packages && this.editObj.packages.length) {
					this.editObj.packages = [];
				}

				this.packageSelectTableData.forEach((ele, index) => {
					ele._checked = false;
					//收缩展开项
					this.packageSelectTableData.splice();
					this.packageSelectTableData[index]._expanded = false
				});
			},

			//表格数据加载
			loadByPage(e) {
				this.getPackageGroupList(e);
			},
			//套餐列表加载
			loadByPackagePage(page) {
				if (page === 0) {
					this.packageCurrentPage = 1;
				}
				queryPackageGroupDetail({
					groupId: this.currentRow.groupId,
					groupType: this.currentRow.groupType,
					pageSize: 10,
					pageNum: this.packageCurrentPage,
				}).then((res) => {
					if (res.code === "0000" && res.data) {
						this.packageTableData = res.data.records;
						this.packageTotal = res.data.total;
					}
				});
			},
			//搜索
			searchPackageGroup() {
				this.getPackageGroupList(0);
			},
			//新增
			packageGroupAdd() {
				this.packageGroupTitle = "套餐组新增";
				this.operationType = "Add";
				this.editObj = {
					groupType: "", //套餐组类型
					groupName: "", //套餐组名称
					cooperationMode: "", //合作模式
					choseType: "2", //套餐组选择方式
					packages: [], //套餐组
					file: ''
				};
				this.packageGroupEditFlag = true;
			},
			//详情
			packageGroupInfo(row) {
				this.packageGroupTitle = "套餐组详情";
				this.operationType = "Info";
				this.packageGroupInfoFlag = true;
				this.info.groupName = row.groupName
				this.info.cooperationMode = row.cooperationMode
				this.info.groupType = row.groupType
				this.currentRow = row;			
				this.loadByPackagePage(0);
			},
			//编辑
			packageGroupEdit(row) {
				this.packageGroupTitle = "套餐组编辑";
				this.operationType = "Update";
				this.editObjreset = row
				// 获取已经选择的套餐
				queryPackageGroupDetail({
					groupId: row.groupId,
					groupType: row.groupType,
					pageSize: -1,
					pageNum: 1,
				}).then((res) => {
					if (res.code === "0000") {
						if (res.data) {
							let arr = res.data.records.map((ele) => {
								//加油包组装
								let refuelDetailList = ele.refuelPackList.map((item) => {
									return {
										refuelId: item.id,
										cny: item.cny,
										hkd: item.hkd,
										usd: item.usd,
									}
								})
								return {
									packageName: ele.packageName,
									packageId: ele.id,
									cny: ele.cny,
									hkd: ele.hkd,
									usd: ele.usd,
									refuelDetailList: refuelDetailList
								};
							});
							this.editObj.packages = arr;
							this.editObj = {
								groupName: row.groupName, //套餐组名称
								cooperationMode: row.cooperationMode, //合作模式
								groupType: row.groupType, //套餐组名称
								groupId: row.groupId, //套餐组名称
								packages: arr,
								choseType: '1' //套餐列表选择方式
							};
						}
					}
				});
				this.packageGroupEditFlag = true;
			},
			//删除
			packageGroupDel(id) {
				this.$Modal.confirm({
					title: "确认删除？",
					onOk: () => {
						//id
						this.deletePackageGroup(id);
					},
				});
			},
			// 导出
			exportfile(data) {
				// this.taskloading = true
				var _this = this
				exportFile({
					groupId: data.groupId,
					groupName: data.groupName,
					groupType: data.groupType,
					pageSize: -1,
					pageNum: -1,
				}).then(res => {
					const content = res.data
					let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[1]) //获取到Content-Disposition;filename  并解码
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content, fileName)
					}
				}).finally(() => {
					// this.taskloading = false
				})
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					let flag = true;
					this.selectionList.map((item, index) => {
						if (value.groupId === item.groupId) {
							flag = false
						}
					});
					//判断重复
					if (flag) {
						this.selectionList.push(value);
					}

				});
			},
			// 取消单选
			cancelSigle(selection, row) {
				this.selectionList.forEach((value, index) => {
					if (value.groupId === row.groupId) {
						this.selectionList.splice(index, 1);
					}
				})
			},
			// 取消全选
			cancelAll(selection, row) {
				this.selectionList = []
			},
			//批量删除
			deleteList() {
				var len = this.selectionList.length;
				if (len < 1) {
					this.$Message.warning("请至少选择一条记录");
					return;
				}

				this.$Modal.confirm({
					title: "确认删除？",
					onOk: () => {
						let ids = []
						this.selectionList.map((value, index) => {
							ids.push(value.groupId)
						})
						deletePackageGroup(ids).then((res) => {
							if (res.code === "0000") {
								this.init();
								this.$Notice.success({
									title: "操作提示",
									desc: "操作成功",
								});

								this.selection = [];
								this.selectionList = [];
							}
						});
					},
				});
			},
			cooperativeApproval(row, flag) {
				approvalPackageGroup({
					groupId: row.groupId,
					status: flag
				}).then(
					(res) => {
						if (res.code === "0000") {
							this.init();
							this.$Notice.success({
								title: "操作提示",
								desc: "操作成功",
							});
						}
					}
				);

				// 是否通过审核
			},
			cancelModal() {
				this.packageGroupEditFlag = false
				this.packageTableData = [];
				this.packageSelectTableData = [];
				this.tempSelectDataArr = [];
				this.tempSelectDataArrzj = []
				this.packageSelectTableCurrent = 1;
				this.packageCurrentPage = 1;
				// this.reset("editObj");
				this.file = null
				this.uploadList = []
				this.$refs["editObj"].resetFields();
			},
			/**
			 * 文件上传
			 */
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			handleBeforeUpload(file, fileList) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: "文件格式不正确",
						desc: '文件 ' + file.name + ' 格式不正确，请上传csv格式文件。'
					})
				} else {
					this.file = file
					this.uploadList = fileList
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			removeFile() {
				this.file = ''
			},
			//模板下载
			downloadFile: function() {
				if (this.editObj.groupType === '1') { //二次定价
					this.$refs.modelTable.exportCsv({
						filename: "二次定价套餐列表",
						// type:'csv',
						columns: this.modelColumns,
						data: this.modelData
					})
				} else { //非二次定价
					this.$refs.modelTable.exportCsv({
						filename: "非二次定价套餐列表",
						// type:'csv',
						columns: this.NotmodelColumns,
						data: this.NotmodelData
					})
				}

			},
		},
		mounted() {
			// this.tableData = res.data
			this.init();
		},
	};
</script>

<style>
	.inputSty {
		width: 200px;
	}

	.packages-two-price .ivu-select-selection {
		max-height: 120px;
		overflow-y: auto;
	}

	.packages-two-price .ivu-select-dropdown {
		width: 700px;
		max-height: inherit;
		left: 0;
	}

	td.ivu-table-expanded-cell {
		padding: 0;
	}
</style>
