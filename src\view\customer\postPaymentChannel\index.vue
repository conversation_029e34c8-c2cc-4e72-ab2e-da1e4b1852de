<template>
	<Card style="width: 100%;padiing: 16px;">
		<Form ref="searchForm" :model="searchObj" style="display: flex;">
			<FormItem style="display: flex;">
				<span class="input_notice">后付费渠道名称：</span>&nbsp;&nbsp;
				<Input v-model='searchObj.paymentChannel' placeholder="请输入后付费渠道名称" clearable style="width: 200px;" />
			</FormItem>
			<FormItem style="display: flex;margin: 0 5px;">
				<Button type="primary" icon="md-search" :loading="searchLoading" @click="searchPaymentChannel()" style="margin: 0 2px;">搜索</Button>
				<Button style="margin: 0 2px" type="info" v-has="'add'" @click="paymentChannelAdd">
					<div style="display: flex;align-items: center;">
						<Icon type="md-add" />&nbsp;新增</div>
				</Button>
				<Button style="margin: 0 2px" type="success" v-has="'orderAdd'" @click="orderAdd">
					<div style="display: flex;align-items: center;">
						<Icon type="md-add" />&nbsp;订单新增</div>
				</Button>
				<Button style="margin: 0 2px" type="error" v-has="'batchDelete'" @click="deleteList">
					<div style="display: flex;align-items: center;">
						<Icon type="ios-trash" />&nbsp;批量删除</div>
				</Button>
			</FormItem>
		</Form>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading"
			 @on-selection-change="handleRowChange">
				<template slot-scope="{ row, index }" slot="action">
					<Button type="primary" size="small" style="margin-right: 5px" v-has="'view'" @click="paymentChannelCommon(row,'Info')">详情</Button>
					<Button type="success" size="small" style="margin-right: 5px" v-has="'update'" @click="paymentChannelCommon(row,'Update')">编辑</Button>
					<Button type="error" size="small" v-has="'delete'" @click="paymentChannelDel(row)">删除</Button>
				</template>
				<template slot-scope="{ row, index }" slot="approval">
					<Button v-if="row.checkStatus=='1'|| row.checkStatus=='4'" type="success" size="small" style="margin-right: 5px"
					 v-has="'check'" @click="paymentChannelApproval(row,'2')">通过</Button>
					<Button v-if="row.checkStatus=='1'|| row.checkStatus=='4'" type="error" size="small" v-has="'check'" @click="paymentChannelApproval(row,'3')">不通过</Button>
				</template>
			</Table>
			<div class="table-botton" style="margin-top:15px">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" style="margin: 10px 0;" />
			</div>
		</div>
		<!-- 后付费渠道新增 -->
		<Modal :title="paymentChannelTitle" v-model="paymentChannelEditFlag" :footer-hide="true" :mask-closable="false"
		 @on-cancel="AddcancelModal" width="750px">
			<div style="padding: 0 16px;">
				<Form ref="editObj" :model="editObj" :label-width="120" :rules="ruleEditValidate">
					<Row>
						<Col span="12">
						<FormItem label="后付费渠道名称" prop="paymentChannelName">
							<Input v-model="editObj.paymentChannelName" :clearable="true" placeholder="请输入后付费渠道名称" class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="EBS Code" prop="EBSCode">
							<Input v-model="editObj.EBSCode" :clearable="true" placeholder="请输入EBSCode" class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="内部订单" prop="internalOrder">
							<Select filterable v-model="editObj.internalOrder" placeholder="请选择是否内部订单" :clearable="true" class="inputSty">
								<Option value="0">是</Option>
								<Option value="1">否</Option>
							</Select></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="币种" prop="currency">
							<Select v-model="editObj.currency" placeholder="请选择币种" :clearable="true" class="inputSty">
								<Option value="156">人民币</Option>
								<Option value="344">港币</Option>
								<Option value="840">美元</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
					<Row>
					  <Col span="12">
					  <FormItem label="公司名称" prop="companyName">
					    <Input v-model="editObj.companyName" :clearable="true" placeholder="请输入公司名称" class="inputSty"></Input>
					  </FormItem>
					  </Col>
					  <Col span="12">
					  <FormItem label="地址" prop="address">
					    <Input v-model="editObj.address" :clearable="true" placeholder="请输入地址" class="inputSty"></Input>
					  </FormItem>
					  </Col>
					</Row>
					<Row>
						<Col span="12">
						<FormItem label="付费模式" prop="paymentMode">
							<Select v-model="editObj.paymentMode" placeholder="请选择付费模式" :clearable="true" @on-change="getpaymentMode" class="inputSty">
								<Option v-for="item in paymentModeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
					<div v-if="editObj.paymentMode==='2'">
						<div v-for="(obj,index) in editObj.flowList" :key="index">
							<Row>
								<Col span="12">
								<FormItem label="套餐名称" :prop="'flowList.' + index+ '.packageName'" :rules="[{required: true, message: '请选择套餐' },]">
									<Select v-model="obj.packageName" filterable placeholder="请选择套餐" :clearable="true" class="inputSty" multiple>
										<Option v-for="item in packageList" :value="item.packageShowName" :key="item.packageId">{{ item.packageShowName }}</Option>
									</Select>
								</FormItem>
								</Col>
							</Row>
							<Row>
								<Col span="12">
								<FormItem label="流量方向" :prop="'flowList.' + index+ '.mcc'" :rules="[{required: true, message: '请选择流量方向' },]">
									<Select v-model="obj.mcc" filterable style="width: 200px;margin-right: 20px;" placeholder="请选择流量方向" :clearable="true"
									 multiple>
										<Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
									</Select>
								</FormItem>
								</Col>
								<Col span="12">
								<FormItem label="结算价格" style="position: relative;" :prop="'flowList.' + index+ '.price'" :rules="[{required: true, message: '请输入结算价格' },
								 {pattern: /^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger: 'blur', message: '请输入1-12位数字，整数首位非0（可精确到小数点后2位）' },
								 ]">
									<Input v-model="obj.price" :clearable="true" placeholder="请输入结算价格" class="inputSty">
									<span slot="append">元/G</span>
									</Input>
									<div @click="delFlowBtn(index)" style="position: absolute;top: 0;right: -15px;">
										<Tooltip content="删除该项方向" placement="right">
											<Icon type="md-trash" size="22" color="#ff3300" />
										</Tooltip>
									</div>
								</FormItem>
								</Col>
							</Row>

						</div>
						<Row>
							<Col span="12">
							<FormItem label="方向" prop="flowList">
								<Button type="dashed" class="inputSty" long @click="addFlow" icon="md-add">添加方向</Button>
							</FormItem>
							</Col>
						</Row>
					</div>
					<div v-if="editObj.paymentMode==='1'">
						<Row v-for="(obj,index) in editObj.packages" :key="index">
							<Col span="12">
							<FormItem label="套餐名称" :prop="'packages.' + index+ '.packageName'" :rules="[{required: true, message: '请选择套餐' },]">
								<Select v-model="obj.packageName" filterable placeholder="请选择套餐" :clearable="true" @on-change="getpackages"
								 class="inputSty" multiple>
									<Option v-for="item in packageList" :value="item.packageShowName" :key="item.packageId">{{ item.packageShowName }}</Option>
								</Select>
							</FormItem>
							</Col>
							<Col span="12">
							<FormItem label="结算价格" style="position: relative;" :prop="'packages.' + index+ '.price'" :rules="[{required: true, message: '请输入结算价格' },
							{pattern: /^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger: 'blur', message: '请输入1-12位数字，整数首位非0（可精确到小数点后2位）' },
							]">
								<Input v-model="obj.price" :clearable="true" placeholder="请输入结算价格" class="inputSty">
								<span slot="append">元</span>
								</Input>
								<div @click="delPackageBtn(index)" style="position: absolute;top: 0;right: -15px;">
									<Tooltip content="删除该项套餐" placement="right">
										<Icon type="md-trash" size="22" color="#ff3300" />
									</Tooltip>
								</div>
							</FormItem>
							</Col>
						</Row>
						<Row>
							<Col span="12">
							<FormItem label="套餐" prop="packages">
								<Button type="dashed" class="inputSty" long @click="addPackage" icon="md-add">添加套餐</Button>
							</FormItem>
							</Col>
						</Row>
					</div>
				</Form>
				<div style="text-align: center;">
					<Button type="primary" :loading="AddLoading" @click="submit('Add')" v-if="typeFlag=='Add'" v-has="'add'">提交</Button>
					<Button type="primary" :loading="AddLoading" @click="submit('Update')" v-if="typeFlag=='Update'" v-has="'update'">提交</Button>
					<Button style="margin-left: 8px" @click="reset('editObj')">重置</Button>
				</div>
			</div>
		</Modal>
		<!-- 订单新增 -->
		<Modal title="订单新增" v-model="orderAddFlag" :footer-hide="true" :mask-closable="false" @on-cancel="ordercancelModal"
		 width="420px">
			<div style="padding: 0 16px;">
				<Form ref="orderObj" :model="orderObj" :label-width="100" :rules="ruleOrderValidate">
					<FormItem label="订单名称" prop="orderName">
						<Input v-model="orderObj.orderName" :clearable="true" placeholder="请输入订单名称" class="inputSty"></Input>
					</FormItem>
					<FormItem label="后付费渠道" prop="paymentChannel">
						<Select v-model="orderObj.paymentChannel" filterable placeholder="请选择后付费渠道" :clearable="true" @on-change="getorder"
						 class="inputSty">
							<Option v-for="(obj,index) in ChannelList" :value="obj.corpId" :key="index">{{ obj.corpName }}</Option>
						</Select>
					</FormItem>

					<FormItem label="付费模式" prop="paymentMode">
						<Select v-model="orderObj.paymentMode" placeholder="请选择付费模式" :clearable="true" class="inputSty">
							<Option v-for="item in paymentModeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
						</Select>
					</FormItem>

					<div>
						<FormItem label="套餐名称" prop="package">
							<Select v-model="orderObj.package" filterable placeholder="请选择套餐名称" :clearable="true" class="inputSty">
								<Option v-for="item in orderpackageList" :value="item.packageId" :key="item.packageId">{{ item.packageName }}</Option>
							</Select>
						</FormItem>
					</div>
					<FormItem label="卡号列表" :rules="[{required: true, message: '请上传文件', trigger: 'blur' },]">
						<Upload action="" :on-success="fileSuccess" :before-upload="handleBeforeUpload" :on-progress="fileUploading">
							<Button type="dashed" class="inputSty" long icon="md-add">上传卡号</Button>
						</Upload>
						<ul class="ivu-upload-list" v-if="file">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span><i class="ivu-icon ivu-icon-ios-stats"></i>{{file.name}}</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
							</li>
						</ul>

					</FormItem>
					<!-- 模板文件table -->
					<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
				</Form>
				<div style="text-align: center;">
					<Button type="primary" :loading="orderLoading" @click="orderSubmit" v-has="'orderAdd'">提交</Button>
					<Button style="margin-left: 8px" @click="reset('orderObj')">重置</Button>
					<Button type="primary" style="margin-left: 8px" icon="ios-download" @click="downloadFile">下载模板</Button>
				</div>
			</div>
		</Modal>
		<!-- 确定企业是否可用 -->
		<Modal title="确认提示" v-model="confirmmark" :footer-hide="true" width="350px">
			<span style="text-align: center;margin: 4px 0;">
				请选择企业是否可用？
			</span>
			<div style="text-align: center;margin: 10px 0 ">
				<Button type="primary" :loading="orderLoading" @click="confirmmethod(true)" v-has="'orderAdd'">可用</Button>
				<Button style="margin-left: 8px" :loading="orderLoading" @click="confirmmethod(false)">不可用</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import localData from '../../../libs/localData.js';
	import {
		searchPostpaid,
		PostpaidAdd,
		OrderAdd,
		BatchDelete,
		PostpaidUpdate,
		PostpaidDelete,
		Approval,
		searchMonthbill,
		searchmeal,
		monthExport,
		queryPackageList,
		queryOrderlList,
		queryChannelList,
		queryDetailForUpdate,
		opsearch
	} from '@/api/customer/postpaid'
	import _ from "lodash";
	const math = require('mathjs')
	export default {
		components: {

		},
		data() {
			return {
				index: 0,
				continentList: '',
				ChannelList: [],
				searchObj: {
					'paymentChannelName': '', //后付费渠道名称
				},
				corpId: '',
				status: '',
				confirmmark: false,
				paymentChannelEditFlag: false,
				paymentChannelTitle: '后付费渠道新增',
				typeFlag: 'Add',
				editObj: {
					'paymentChannelName': '', //后付费渠道名称
					'currency': '', //币种
					'internalOrder': "", //内部订单
					'EBSCode': '',
					'paymentMode': '', //付费模式
					'packages': [], //套餐集合
					'flowList': [], //流量集合
					'address': '',
					'companyName': '',
				},
				orderAddFlag: false,
				orderObj: {
					'orderName': '',
					'paymentChannel': '',
					'renewal': '',
					'paymentMode': '',
					'direction': '',
					'package': '',
					'cardList': [],
					file: null,
				},
				file: null,
				paymentModeList: [{
					label: '流量付费',
					value: '2'
				}, {
					label: '套餐付费',
					value: '1'
				}],
				paymentChannellList: [{
					label: '后付费渠道商1',
					value: '1'
				}, {
					label: '后付费渠道商2',
					value: '2'
				}],
				renewalList: [{
					label: '自动续订',
					value: '1'
				}],
				packageList: [],
				orderpackageList: [],
				packageNameList: [],
				ruleEditValidate: {
					paymentChannelName: [{
						// type:'number',
						required: true,
						message: '请输入后付费渠道名称',
						trigger: 'blur'
					}, {
						min: 0,
						max: 50,
						message: '请输入50位字符以内的后付费渠道名称',
						trigger: 'blur'
					}, ],
					internalOrder: [{
						required: true,
						type: 'string',
						message: '内部订单不能为空',
						trigger: 'change'
					}],
					companyName: [{
					  required: true,
					  type: 'string',
					  message: '公司名称不能为空',
					  trigger: 'blur'
					},
					{ max: 50,message:'最长50位'}
					],
					address: [{
					  required: true,
					  type: 'string',
					  message: '地址不能为空',
					  trigger: 'blur'
					},
					{ max: 200,message:'最长200位'}
					],
					EBSCode: [{
						required: true,
						message: '请输入EBSCode',
						trigger: 'blur'
					}, {
						min: 0,
						max: 50,
						message: '请输入50位字符以内EBSCode',
						trigger: 'blur'
					}, ],
					currency: [{
						required: true,
						message: '请选择币种',
						// trigger: 'blur'
					}],
					paymentMode: [{
						required: true,
						message: '请选择付费模式',
						// trigger: 'blur'
					}],
					packages: [{
						type: 'array',
						required: true,
						message: '请添加套餐',
						trigger: 'blur,change'
					}],
					flowList: [{
						type: 'array',
						required: true,
						message: '请添加方向',
						trigger: 'blur,change'
					}]


				},
				ruleOrderValidate: {
					orderName: [{
						required: true,
						message: '请输入订单名称',
						trigger: 'blur'
					}, {
						min: 0,
						max: 50,
						message: '请输入50位字符以内订单名称',
						trigger: 'blur'
					}, ],
					paymentChannel: [{
						required: true,
						message: '请选择后付费渠道',
						// trigger: 'blur'
					}],
					paymentMode: [{
						required: true,
						message: '请选择付费模式',
						// trigger: 'blur'
					}],
					package: [{
						required: true,
						message: '请选择套餐名称',
						// trigger: 'blur'
					}],

				},
				tableData: [], //列表信息
				selection: [], //多选
				selectionIds: [], //多选ids
				tableLoading: false,
				searchLoading: false,
				AddLoading: false,
				orderLoading: false,
				packagesLists: [],
				packages: [],
				flowLists: [],
				flowList: [],
				firstpaymentMode: '',
				total: 0,
				pageSize: 10,
				page: 1,
				currentPage: 1,
				updateindex: 0,
				updateflg: false,
				modelData: [{
					'iccid': '********',
				}, ],
				modelColumns: [{
						title: 'iccid',
						key: 'iccid'
					}, // 列名根据需要添加
				],
				columns: [{
						type: 'selection',
						minWidth: 60,
						align: 'center'
					},
					{
						title: '后付费渠道名称',
						key: 'corpName',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: 'EBS Code',
						key: 'ebsCode',
						align: 'center',
						minWidth: 120,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '付费模式',
						key: 'settleType',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const text = row.settleType == '1' ? '套餐付费' : row.settleType == '2' ? '流量付费' : '';
							return h('label', text);
						}
					},

					{
						title: '币种',
						key: 'currencyCode',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const text = row.currencyCode == '156' ? '人民币' : row.currencyCode == '840' ? '美元' : row.currencyCode == '344' ?
								'港币' : '';
							return h('label', text);
						}
					},
					{
						title: '操作',
						slot: 'action',
						minWidth: 220,
						align: 'center'
					}
				]
			}
		},
		methods: {
			//表格初始化
			init() {
				this.columns.push({
					title: '审批状态',
					key: 'checkStatus',
					align: 'center',
					minWidth: 120,
					render: (h, params) => {
						const row = params.row;
						const color = row.checkStatus == '1' ? '#27A1FF' : row.checkStatus == '2' ? '#00cc66' :
							row.checkStatus == '3' ? '#ff0000' : '#ff0000';
						const text = row.checkStatus == '1' ? '新建待审批' : row.checkStatus == '2' ? '通过' : row.checkStatus == '3' ? '未通过' :
							row.checkStatus == '4' ? '删除待审批' : '';
						return h('label', {
							style: {
								color: color
							}
						}, text);
					},
				});
				this.columns.push({
					title: '审批操作',
					slot: 'approval',
					minWidth: 220,
					align: 'center'
				});
				this.goPageFirst(1)
			},
			//模板下载
			downloadFile: function() {
				this.$refs.modelTable.exportCsv({
					filename: '卡号列表模板',
					// type:'xlsx',
					columns: this.modelColumns,
					data: this.modelData
				})
			},
			// 页面加载
			goPageFirst(page) {
				this.tableLoading = true
				var _this = this
				let channelName = this.searchObj.paymentChannel
				let pageNum = page
				let pageSize = 10
				searchPostpaid({
					pageNum,
					pageSize,
					channelName
				}).then(res => {
					if (res.code == '0000') {
						_this.tableLoading = false
						this.searchLoading = false
						this.page = page
						this.total = res.count
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.searchLoading = false
					this.tableLoading = false
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			//搜索
			searchPaymentChannel() {
				this.searchLoading = true
				this.currentPage = 1
				this.goPageFirst(1)
			},
			//国家/地区
			getLocalList() {
				opsearch().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.continentList = list;
						this.continentList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			getorder(name) {
				// 获取当前可以订单订购得套餐
				this.queryOrderlList(name)
			},
			encapsulation() {
				this.paymentChannelEditFlag = false
				if (this.editObj.paymentMode === '1') {
					// 套餐付费
					this.editObj.packages.forEach((item) => {
						this.packageList.forEach((value) => {
							item.packageName.forEach((packageName) => {
								if (value.packageShowName === packageName) {
									this.packageNameList.push({
										"packageId": value.packageId,
										"packageName": packageName,
									})
								}
							})
						})
						this.packagesLists.push({
							"packages": this.packageNameList,
							"price": math.multiply(math.bignumber(item.price), 100).toString()
						})
						this.packageNameList = []
						this.editObj.packages = []
					})
				} else {
					// 流量付费
					this.editObj.flowList.forEach((item) => {
						this.packageList.forEach((value) => {
							item.packageName.forEach((packageName) => {
								if (value.packageShowName === packageName) {
									this.packageNameList.push({
										"packageId": value.packageId,
										"packageName": packageName,
									})
								}
							})
						})
						this.flowLists.push({
							"packages": this.packageNameList,
							"price": math.multiply(math.bignumber(item.price), 100).toString(),
							"mcc": item.mcc
						})
						this.packageNameList = []
						this.editObj.flowList = []
					})

				}
			},
			//提交
			submit(name) {
				if (name === 'Add') {
					// 新增
					this.$refs["editObj"].validate((valid) => {
						if (valid) {
							let packageMap = new Map();
							this.packageList.forEach((value) => {
								packageMap.set(value.packageShowName, value.packageName);
							});
							// 封装
							this.AddLoading = true
							this.encapsulation()
							let channelName = this.editObj.paymentChannelName
							let currencyCode = this.editObj.currency
							let ebsCode = this.editObj.EBSCode
							let internalOrder = this.editObj.internalOrder
							let flowlist = null
							let packagelist = null
							let settleType = this.editObj.paymentMode
							let address=this.editObj.address
							let companyName=this.editObj.companyName
							if (this.editObj.paymentMode === '1') {
								if (this.packagesLists.length > 0) {
									this.packagesLists.map((value, index) => {
										value.packages.map((pc, pcIndex) => {
											pc.packageName = packageMap.get(pc.packageName);
										});
									});
									packagelist = this.packagesLists
								}
							} else {
								if (this.flowLists.length > 0) {
									this.flowLists.map((value, index) => {
										value.packages.map((pc, pcIndex) => {
											pc.packageName = packageMap.get(pc.packageName);
										});
									});
									flowlist = this.flowLists
								}
							}
							PostpaidAdd({
								channelName,
								currencyCode,
								ebsCode,
								internalOrder,
								flowlist,
								packagelist,
								settleType,
								address,
								companyName
							}).then(res => {
								if (res.code === '0000') {
									this.$Notice.success({
										title: '操作成功',
										desc: '操作成功'
									})
									this.AddLoading = false
									this.paymentChannelEditFlag = false
									this.goPageFirst(1)
									this.currentPage = 1
									this.reset()
								}
							}).catch((err) => {
								this.AddLoading = false
								this.paymentChannelEditFlag = false
								this.reset()
								console.log(err)
							})
						}
					})
				} else {
					// 修改
					this.$refs["editObj"].validate((valid) => {
						if (valid) {
							let packageMap = new Map();
							this.packageList.forEach((value) => {
								packageMap.set(value.packageShowName, value.packageName);
							});
							// 封装
							this.AddLoading = true
							this.encapsulation()
							let channelId = this.editObj.corpId
							let channelName = this.editObj.paymentChannelName
							let currencyCode = this.editObj.currency
							let ebsCode = this.editObj.EBSCode
							let internalOrder = this.editObj.internalOrder
							let flowlist = null
							let packagelist = null
							let settleType = this.editObj.paymentMode
							let address=this.editObj.address
							let companyName=this.editObj.companyName
							if (this.editObj.paymentMode === '1') {
								if (this.packagesLists.length > 0) {
									this.packagesLists.map((value, index) => {
										value.packages.map((pc, pcIndex) => {
											pc.packageName = packageMap.get(pc.packageName);
										});
									});
									packagelist = this.packagesLists
								}
							} else {
								if (this.flowLists.length > 0) {
									this.flowLists.map((value, index) => {
										value.packages.map((pc, pcIndex) => {
											pc.packageName = packageMap.get(pc.packageName);
										});
									});
									flowlist = this.flowLists
								}
							}
							PostpaidUpdate({
								channelId,
								channelName,
								currencyCode,
								ebsCode,
								internalOrder,
								flowlist,
								packagelist,
								settleType,
								address,
								companyName
							}).then(res => {
								if (res.code === '0000') {
									this.$Notice.success({
										title: '操作成功',
										desc: '操作成功'
									})
									this.AddLoading = false
									this.currentPage = 1
									this.goPageFirst(1)
									this.AddcancelModal()
								} else {
									this.AddLoading = false
									this.currentPage = 1
									this.goPageFirst(1)
									this.AddcancelModal()
								}
							}).catch((err) => {
								this.AddLoading = false
								this.AddcancelModal()
							})

						}
					})
				}

			},
			//订单提交
			orderSubmit() {
				if (!this.file) {
					this.$Message.warning('请上传卡号！')
					return
				} else {
					this.$refs["orderObj"].validate((valid) => {
						if (valid) {
							this.orderLoading = true
							let formData = new FormData()
							formData.append('corpId', this.orderObj.paymentChannel)
							formData.append('orderName', this.orderObj.orderName)
							formData.append('packageId', this.orderObj.package)
							formData.append('payType', this.orderObj.paymentMode)
							formData.append('file', this.orderObj.file)
							OrderAdd(formData).then(res => {
								if (res.code === '0000') {
									this.$Notice.success({
										title: '操作成功',
										desc: '操作成功'
									})
									this.orderLoading = false
									this.ordercancelModal()
									this.currentPage = 1
									this.goPageFirst(1)
								}
							}).catch((err) => {
								this.orderLoading = false
								this.ordercancelModal()
								console.log(err)
							})

						}
					})
				}

			},
			reset(name) {
				this.editObj.packages.splice(0, this.index);
				this.editObj.flowList.splice(0, this.index);
				this.$refs.editObj.resetFields()
				this.$refs.orderObj.resetFields()
				this.packagesLists = []
				this.packageNameList = []
				this.flowLists = []
			},
			//新增
			paymentChannelAdd() {
				this.typeFlag = 'Add';
				this.paymentChannelTitle = '后付费渠道新增';
				this.editObj.packages = []
				this.editObj.flowList = []
				this.editObj = {
					'paymentChannelName': '', //后付费渠道名称
					'currency': '', //币种
					'internalOrder': "", //内部订单
					'paymentMode': '', //付费模式
					'packages': [{
						'packageName': '',
						'price': ''
					}], //套餐集合
					'flowList': [{
						'packageName': '',
						'mcc': '',
						'price': ''
					}], //流量集合
					'address': '',
					'companyName': '',
				};
				this.paymentChannelEditFlag = true;
			},
			//订单新增
			orderAdd() {
				this.orderObj = {
					'orderName': '',
					'paymentChannel': '',
					'renewal': '',
					'paymentMode': '',
					// 'direction': '',
					'package': '',
					'cardList': []
				};
				this.queryChannelList()
				this.orderAddFlag = true;
			},
			addPackage() {
				this.index++;
				this.editObj.packages.push({
					"currency": '',
					"index": this.index
				})
			},
			addFlow() {
				this.index++;
				this.editObj.flowList.push({
					"packageName": '',
					"index": this.index
				})
			},
			delPackageBtn(index) {
				this.editObj.packages.splice(index, 1);
			},
			delFlowBtn(index) {
				this.editObj.flowList.splice(index, 1);
			},
			//详情
			getpackages(id) {

			},
			//编辑
			paymentChannelCommon(row, type) {
				if (type === 'Info') {
					this.typeFlag = type;
					this.$router.push({
						name: 'paymentChannelInfo',
						query: {
							paymentChannel: encodeURIComponent(JSON.stringify(row))
						}
					})
				}
				if (type === 'Update') {
					this.editObj.packages.splice(0, this.index);
					this.editObj.flowList.splice(0, this.index);
					this.editObj.flowList = []
					this.editObj.packages = []
					this.packages = []
					this.flowList = []
					this.updateindex = 0
					this.index = 0
					// 调用编辑查询详情
					queryDetailForUpdate({
						channelId: row.corpId
					}).then(res => {
						if (res.code === '0000') {
							// 封装
							res.data.map(item => {
								if (row.settleType === '1') {
									// 套餐付费
									this.packages.push({
										"packageName": item.packageName,
										"price": parseFloat(math.divide(math.bignumber(item.price), 100).toFixed(2)).toString()
									})
									this.updateindex++
									if (this.packages.length === 0) {
										// 套餐付费
										this.packages = [{
											"packageName": '',
											"price": ''
										}]
									}
								} else {
									// 流量付费
									this.flowList.push({
										"packageName": item.packageName,
										"price": parseFloat(math.divide(math.bignumber(item.price), 100).toFixed(2)).toString(),
										"mcc": item.mcc
									})
									this.updateindex++
									if (this.flowList.length === 0) {
										// 流量付费
										this.flowList = [{
											"packageName": '',
											"price": '',
											"mcc": ''
										}]
									}
								}
								this.index++
								this.firstpaymentMode = row.settleType
							})
						}
					}).catch((err) => {
						console.log(err)
					})

					this.editObj = {
						'corpId': row.corpId,
						'paymentChannelName': row.corpName, //后付费渠道名称
						'currency': row.currencyCode, //币种
						'internalOrder':row.internalOrder, //内部订单
						'paymentMode': row.settleType, //付费模式
						'packages': this.packages, //套餐集合
						'flowList': this.flowList, //流量集合
						'EBSCode': row.ebsCode, //EBSCode
						'address': row.address,
						'companyName': row.companyName,
					};
					this.typeFlag = type;
					this.paymentChannelTitle = '后付费渠道编辑';
					this.paymentChannelEditFlag = true;
				}
			},
			//审核
			paymentChannelApproval(row, type) {
				let corpId = row.corpId
				let status = type
				if (status === '3' && row.checkStatus === '4') {
					this.corpId = corpId
					this.status = status
					this.confirmmark = true
				} else {
					this.$Modal.confirm({
						title: '确认审批？',
						onOk: () => {
							Approval({
								corpId,
								status
							}).then(res => {
								if (res.code === '0000') {
									this.$Notice.success({
										title: '操作成功',
										desc: '操作成功'
									})
									this.currentPage = 1
									this.goPageFirst(1)
								}
							}).catch((err) => {
								console.log(err)
							})
						}
					});
				}
			},
			// 不通过时，确认企业是否可用
			confirmmethod(row) {
				let corpId = this.corpId
				let status = this.status
				let frostCrop = row
				this.confirmmark = false
				Approval({
					corpId,
					status,
					frostCrop
				}).then(res => {
					if (res.code === '0000') {
						this.$Notice.success({
							title: '操作成功',
							desc: '操作成功'
						})
						this.currentPage = 1
						this.goPageFirst(1)
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			//删除
			paymentChannelDel(row) {
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						let channelId = row.corpId
						PostpaidDelete({
							channelId
						}).then(res => {
							if (res.code === '0000') {
								this.$Notice.success({
									title: '操作成功',
									desc: '操作成功'
								})
								this.currentPage = 1
								this.goPageFirst(1)
							}
						}).catch((err) => {
							console.log(err)
						})
					}
				});
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				// this.selectionIds = [];
				selection.map((value, index) => {
					this.selectionIds.push(value.corpId);
				});
			},
			//批量删除
			deleteList() {
				var len = this.selection.length;
				if (len < 1) {
					this.$Message.warning('请至少选择一条记录')
					return
				}
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						BatchDelete({
							channelIdList: this.selectionIds
						}).then(res => {
							if (res.code === '0000') {
								this.$Notice.success({
									title: '操作成功',
									desc: '操作成功'
								})
								this.selectionIds = [];
								this.selection = [];
								this.currentPage = 1
								this.goPageFirst(1)
							}
						}).catch((err) => {
							this.selectionIds = [];
							this.selection = [];
							console.log(err)
						})
					}
				});
			},
			handleBeforeUpload(file) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: '文件格式不正确',
						desc: '文件 ' + file.name + ' 格式不正确，请上传.csv格式文件。'
					})
				} else {
					this.file = file
					this.orderObj.file = file
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			removeFile() {
				this.file = ''
			},
			// 新增取消
			AddcancelModal() {
				this.paymentChannelEditFlag = false
				this.editObj.packages.splice(0, this.index);
				this.editObj.flowList.splice(0, this.index);
				this.$refs.editObj.resetFields()
				this.packagesLists = []
				this.packageNameList = []
				this.flowLists = []
				this.currentPage = 1
				this.goPageFirst(1)

			},
			//选择付费模式
			getpaymentMode() {
				if (this.firstpaymentMode === '1') {
					// 是套餐付费就清空流量付费
					this.editObj.flowList.splice(0, this.index);
				} else {
					// 是流量付费就清空套餐付费
					this.editObj.packages.splice(0, this.index);
				}
			},
			// 订单新增取消
			ordercancelModal() {
				this.orderAddFlag = false
				this.$refs.orderObj.resetFields()
				this.file = ''
			},
			// 获取当前可以订购得套餐
			queryPackageList() {
				queryPackageList().then(res => {
					if (res && res.code == '0000') {
						this.packageList = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			// 获取当前可以订单订购得套餐
			queryOrderlList(name) {
				let channelId = ""
				if (name) {
					channelId = name
				}
				queryOrderlList({
					channelId,
				}).then(res => {
					if (res && res.code == '0000') {
						this.orderpackageList = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			// 查询订单渠道接口
			queryChannelList() {
				queryChannelList().then(res => {
					if (res && res.code == '0000') {
						this.ChannelList = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			}


		},
		mounted() {
			// this.tableData = res.data
			this.init();
			//获取国家/地区信息
			this.getLocalList();
			// 获取可订购套餐
			this.queryPackageList()
		}
	}
</script>

<style>
	.input_notice {
		font-size: 15px;
		font-weight: bold;
	}

	.inputSty {
		width: 200px;
	}
</style>
