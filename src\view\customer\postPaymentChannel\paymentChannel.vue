<template>
	<Card style="width: 100%;padiing: 16px;">
		<div>
			<Form ref="showForm" inline :model="showObj" :label-width="120">
				<FormItem label="后付费渠道名称:">
					<a href="#">
						<p>{{showObj.corpName}}</p>
					</a>
				</FormItem>
				<FormItem label="币种:">
					<a href="#">
						<p v-if="showObj.currencyCode=='156'">人民币</p>
						<p v-if="showObj.currencyCode=='344'">港币</p>
						<p v-if="showObj.currencyCode=='840'">美元</p>
					</a>
				</FormItem>
				<FormItem label="付费模式:">
					<a href="#">
						<p v-if="showObj.settleType=='2'">流量付费</p>
						<p v-if="showObj.settleType=='1'">套餐付费</p>
					</a>
				</FormItem>
			</Form>
		</div>
		<div>
			<Tabs value="1" @on-click="switchTab">
				<Tab-pane label="本月账单" name="1">
					<Form ref="searchForm"  :model="searchObj" >
						<!-- <FormItem label="选择月份">
							<DatePicker type="month" format="yyyy-MM" placeholder="请选择月份" :clearable="true" v-model="searchObj.searchMonth"
							 style="width: 200px;" @on-change="handleDateChange" @on-clear="hanldeDateClear"></DatePicker>
						</FormItem> -->
						<FormItem>
							<!-- <Button type="primary" icon="md-search" :loading="searchLoading" @click="searchDetails()" style="margin: 0 2px;">搜索</Button> -->
							<Button style="margin: 0 2px;" type="success"  @click="exportDetails" v-has="'export'">
								<div style="display: flex;align-items: center;">
									<Icon type="ios-cloud-download-outline" />&nbsp;账单导出</div>
							</Button>
							<a ref="downloadLink" style="display: none"></a>
						</FormItem>
					</Form>
					<Table :columns="billColumns" :data="billTableData" :ellipsis="true" :loading="billTableLoading"></Table>
					<Page :total="billTotal"  :current.sync="billPage" show-total show-elevator @on-change="getBillListByPage"
					 style="margin: 15px 0;" />
				</Tab-pane>
				<Tab-pane :label="payTabLabel" name="2">
					<Table :columns="payColumns" :data="payTableData" :ellipsis="true" :loading="payTableLoading"></Table>
					<!-- <Page :total="payTotal" :page-size="payPageSize" :current.sync="payPage" show-total show-elevator @on-change="getPayListByPage"
            style="margin: 15px 0;" /> -->
				</Tab-pane>
			</Tabs>
		</div>
	</Card>
</template>

<script>
	import {
		queryFlowBills,
		queryPackageBills,
		searchmeal,
		monthExport,
		queryFlowDetails
	} from '@/api/customer/postpaid'
	const math = require('mathjs')
	export default {
		components: {

		},
		data() {
			return {
				showObj: {
					'paymentChannelName': '', //后付费渠道名称
					'currency': '',
					'paymentMode': '' //付费模式
				},
				searchObj: {
					// 'paymentChannelName': '', //后付费渠道名称
					'searchMonth': ''
				},
				paymentModeList: [{
					label: '流量付费',
					value: '1'
				}, {
					label: '套餐付费',
					value: '2'
				}],
				TabFlag: '',
				modeType: '', //避免浏览器修改mode值
				payTabLabel: '流量付费详情',
				billTableData: [], //账单列表信息
				billTableLoading: false,
				searchLoading: false,
				billTotal: 0,
				// billPageSize: 10,
				billPage: 1,
				billColumns: [{
						title: 'ICCID',
						key: 'iccid',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						 tooltipMaxWidth: 2000,
					},
					{
						title: '套餐名称',
						key: 'packageName',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '使用时间',
						key: 'useDate',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '使用国家/地区',
						key: 'mcc',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '流量(G)',
						key: 'flowCount',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: '币种',
						key: 'currencyCode',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
						render: (h, params) => {
							const row = params.row;
							const text = row.currencyCode == '156' ? '人民币' :row.currencyCode == '840' ? '美元' :row.currencyCode == '344' ? '港币' : '';
							return h('label', text);
						}
					},
					{
						title: '价格',
						key: 'price',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
						// render: (h, params) => {
						// 	const row = params.row;
						// 	const text = math.divide(row.price,100).toString();
						// 	return h('label', text);
						// }
						
					}
				],
				payTableData: [], //付费列表信息
				payTableLoading: false,
				payTotal: 0,
				payPageSize: 10,
				payPage: 1,
				payColumns: [{
					title: '流量方向',
					key: 'mcc',
					align: 'center',
					minWidth: 150,
					tooltip: true,
					tooltipMaxWidth: 2000,
				}, {
					title: '单价（元/G）',
					key: 'price',
					align: 'center',
					minWidth: 150,
					tooltip: true,
					tooltipMaxWidth: 2000,
					render: (h, params) => {
						const row = params.row;
						const text = parseFloat(math.divide(math.bignumber(row.price),100).toFixed(2)).toString();
						return h('label', text);
					}
				}],
				searchMonth:''
			}
		},
		methods: {
			//表格初始化
			init() {
				this.BillgoPageFirst(1);
			},
			//账单表格数据加载
			BillgoPageFirst(page) {
				this.billTableLoading = true
				let pageNum = page
				let pageSize = 10
				let channelId = this.showObj.corpId
				// let month = this.searchMonth
				// 判断付费模式是流量付费还是套餐付费
				if (this.showObj.settleType === '2') {
					// 流量付费
					queryFlowBills({
						channelId,
						pageNum,
						pageSize,
					}).then(res => {
						if (res.code == '0000') {
							this.billTableLoading = false
							this.searchLoading = false
							this.billPage = page
							this.billTotal = res.count
							this.billTableData = res.data
						}
					}).catch((err) => {
						console.error(err)
					}).finally(() => {
						this.searchLoading = false
						this.billTableLoading = false
					})
				} else {
					// 套餐付费
					queryPackageBills({
						channelId,
						pageNum,
						pageSize,
					}).then(res => {
						if (res.code == '0000') {
							this.billTableLoading = false
							this.searchLoading = false
							this.billPage = page
							this.billTotal = res.count
							this.billTableData = res.data
						}
					}).catch((err) => {
						console.error(err)
					}).finally(() => {
						this.searchLoading = false
						this.billTableLoading = false
					})
				}

			},
			handleDateChange(dateArr){
				this.searchMonth=dateArr
			},
			hanldeDateClear(){
				this.searchMonth=''
			},
			//套餐付费详情表格数据加载
			flowgoPageFirst(page) {
				this.payTableLoading = true;
				let channelId = this.showObj.corpId
				if(this.showObj.settleType === '2'){
					// 流量付费详情
					queryFlowDetails({
						channelId,
					}).then(res => {
						if (res.code == '0000') {
							this.payTableLoading = false;
							this.searchLoading = false
							this.payTableData = res.data
						}
					}).catch((err) => {
						console.error(err)
					}).finally(() => {
						this.searchLoading = false
						this.payTableLoading = false;
					})
				}else{
					// 套餐付费详情
					searchmeal({
						channelId,
					}).then(res => {
						if (res.code == '0000') {
							this.payTableLoading = false;
							this.searchLoading = false
							this.payTableData = res.data
						}
					}).catch((err) => {
						console.error(err)
					}).finally(() => {
						this.searchLoading = false
						this.payTableLoading = false;
					})
				}

			},
			//日期查询
			// searchDetails() {
			// 	this.searchLoading = true
			// 	this.BillgoPageFirst(1)
			// },
			//明细导出
			exportDetails() {
				this.$Modal.confirm({
					title: '确认导出？',
					onOk: () => {
						let channelId = this.showObj.corpId
						monthExport({
							channelId
						}).then(res => {
							const content = res.data
							// 获取当前时间
							let date = new Date();
							let y = date.getFullYear();
							let m = date.getMonth() + 1;
							let d = date.getDate();
							// let H = Da.getHours();

							let time = y + "-" + m + "-" + d

							const fileName = time + '.txt' // 导出文件名
							if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
								const link = this.$refs.downloadLink // 创建a标签
								let url = URL.createObjectURL(content)
								link.download = fileName
								link.href = url
								link.click() // 执行下载
								URL.revokeObjectURL(url) // 释放url
							} else { // 其他浏览器
								navigator.msSaveBlob(content, fileName)
							}
						}).catch(err => this.exporting = false)
					}
				})
			},
			switchTab(e) {
				this.TabFlag = e
				if ('1' === e) {
					this.BillgoPageFirst(1);
				} else {
					this.flowgoPageFirst(1);
				}
			},
			getBillListByPage(start) {
				//根据所选月份进行条件查询
				this.billTableLoading = true;
				this.billPage = start;
				//加载数据 注意区分type
				this.billTableData = [];
				this.billTableLoading = false;
				this.BillgoPageFirst(start);
			},
			getPayListByPage(start) {
				//根据所选月份进行条件查询
				this.payTableLoading = true;
				this.payPage = start;
				//加载数据 注意区分type
				// this.payTotal = 10;
				this.payTableData = [];
				this.payTableLoading = false;
				this.flowgoPageFirst(start)
			},
			loadColunmsByPaymentMode(mode) {
				if ('1' === mode) {
					this.payTabLabel = '套餐付费详情';
					this.billColumns.splice(2, 3);
					this.billColumns.splice(2, 0, {
						title: '到期时间',
						key: 'expireTime',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					});
					this.billColumns.splice(2, 0, {
						title: '激活时间',
						key: 'activeTime',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					});
					this.payColumns = [{
						title: '套餐名称',
						key: 'packageName',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					}, {
						title: '单价（元）',
						key: 'price',
						align: 'center',
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
						render: (h, params) => {
							const row = params.row;
							const text = parseFloat(math.divide(math.bignumber(row.price),100).toFixed(2)).toString();
							return h('label', text);
						}
					},
					];
				}
			}
		},
		mounted() {
			// this.tableData = res.data
			var paymentChannel = JSON.parse(decodeURIComponent(this.$route.query.paymentChannel));
			this.showObj = paymentChannel;
			this.modeType = paymentChannel.settleType;
			this.loadColunmsByPaymentMode(paymentChannel.settleType);
			this.init();
		}
	}
</script>

<style>
	.inputSty {
		width: 200px;
	}
</style>
