<template>
	<!-- 零级渠道商首页 -->
	<Card style="width: 100%; padiing: 16px">
		<Button style="margin: 0 2px" type="info" @click="ZeroChannel()" v-has="'add'">
			<div style="display: flex; align-items: center">
				<Icon type="md-add" />&nbsp;新增
			</div>
		</Button>
		<div style="margin: 20px 2px">
			<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading">
				<template slot-scope="{ row, index }" slot="action">
					<Button type="success" size="small" style="margin-right: 5px" v-has="'update'"
						@click="zeroChannelupdate(row)">编辑</Button>
					<Button type="error" size="small" @click="deleteList(row)" v-has="'delete'">删除</Button>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="currentPage" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0" />
		</div>
	</Card>
</template>

<script>
	import {
		queryZeroChannel,
		deleteZeroChannel,
	} from "@/api/customer/zeroChannel.js";
	export default {
		data() {
			return {
				columns: [{
						title: "渠道商名称",
						key: "corpName",
						align: "center",
						minWidth: 120,
						tooltip: true
					},
					{
						title: "渠道商状态",
						key: "isSub",
						align: "center",
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							const color = row.isSub == "1" ? "#00cc66" : "#ff0000";
							const text = row.isSub == "1" ? "允许订购" : row.isSub == "2" ? "不允许订购" : '';
							return h(
								"label", {
									style: {
										color: color
									}
								},
								text
							);
						}
					},
					{
						title: "渠道商AppKey",
						key: "appkey",
						align: "center",
						minWidth: 130,
						tooltip: true
					},
					{
						title: "渠道商AppSecret",
						key: "appsecret",
						align: "center",
						minWidth: 140,
						tooltip: true
					},
					{
						title: "关联子渠道商列表",
						key: "subChannel",
						align: "center",
						minWidth: 140,
						render: (h, params) => {
							const row = params.row;
							let text = ""
							if (row.relationChannel != null) {
								text = row.relationChannel[0].corpName
							}							
							if (row.relationChannel!=null&& row.relationChannel.length > 1) {
								return h('div', [
									h('Tooltip', {
											props: {
												placement: 'bottom',
												transfer: true,
											},
											style: {
												cursor: 'pointer',
											}
										},
										[
											h('span', {
												style: {
													display: "block",
												}
											}, row.relationChannel[0].corpName),
											h('span', {}, row.relationChannel[1].corpName),
											h('span', {}, "…"),
											h('ul', {
												slot: 'content',
												style: {
													whiteSpace: 'normal',
													wordBreak: 'break-all' //超出隐藏
												},
											}, this.tableData[params.index].relationChannel.map(
											item => {
												return h('li', item.corpName)

											}))

										]
									)
								])
							} else {
								text = text;
								return h('label', text)
							};
						}
					},
					{
						title: "操作",
						slot: "action",
						minWidth: 200,
						align: "center"
					},
				],
				tableData: [{}],
				tableLoading: false,
				total: 0,
				pageSize: 10,
				currentPage: 1,
			}
		},
		methods: {
			//表格初始化
			init() {
				this.goPageFirst(1)
			},
			// 零级渠道商分页列表
			goPageFirst: function(page) {
				this.tableLoading = true
				var _this = this
				queryZeroChannel({
					pageSize: this.pageSize,
					pageNum: page,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.tableLoading = false
						this.currentPage = page
						this.total = res.count
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.tableLoading = false
				})
			},
			// 跳转页码
			loadByPage(page) {
				this.goPageFirst(page);
			},
			// 删除零级渠道商
			deleteList(row) {
				if (row.relationChannel !== null) {
					this.$Modal.warning({
						title: "存在关联子渠道商，不允许删除零级渠道!",
					})
				} else {
					this.$Modal.confirm({
						title: "确认删除该零级渠道商？",
						onOk: () => {
							deleteZeroChannel({
								corpId: row.corpId
							}).then(
								res => {
									if (res.code === "0000") {
										this.init();
										this.$Notice.success({
											title: "操作提示",
											desc: "操作成功"
										});
									} else {
										this.$Notice.error({
											title: "操作提示",
											desc: "操作失败"
										});
									}
								}
							);
						}
					});
				}
			},
			// 跳转 新增 零级渠道商
			ZeroChannel() {
				this.$router.push({
					name: 'zeroChannelAdd',
				});
			},
			// 跳转 编辑 零级渠道商
			zeroChannelupdate(row) {
				var obj = row
				this.$router.push({
					name: 'zeroChannelUpdate',
					query: {
						obj: encodeURIComponent(JSON.stringify(obj))
					}
				});
			}
		},
		mounted() {
			this.init();
		}
	}
</script>

<style>
	li {
		list-style: none;
	}
</style>
