<template>
	<!-- 零级渠道商新增  -->
	<Card style="width: 100%; padiing: 16px">
		<div style="margin: 20px 10%">
			<div>
				<Form ref="formObj" :model="formObj" :label-width="150" :rules="ruleValidate" style="font-size: 25px;">
					<Row justify="center">
						<Col span="12">
						<FormItem label="渠道商名称" prop="corpName">
							<Input v-model="formObj.corpName" maxlength="50" :clearable="true" placeholder="请输入渠道商名称"
								class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="渠道商状态" prop="isSub">
							<Select v-model="formObj.isSub" :clearable="true" placeholder="请选择渠道商状态" class="inputSty">
								<Option v-for="item in purchaseStatusList" :value="item.value" :key="item.value">
									{{ item.label }}
								</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
					<Row justify="center">
						<Col span="12">
						<FormItem label="公司名称" prop="companyName">
							<Input v-model="formObj.companyName" maxlength="200" :clearable="true" placeholder="请输入公司名称"
								class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="地址" prop="address">
							<Input v-model="formObj.address" placeholder="请输入地址" :clearable="true"
								class="inputSty"></Input>
						</FormItem>
						</Col>
					</Row>
					<Row justify="center">
						<Col span="12">
						<FormItem label="EBS Code" prop="ebsCode">
							<Input v-model="formObj.ebsCode" :clearable="true" maxlength="50" placeholder="请输入EBS Code"
								class="inputSty"></Input>
						</FormItem>
						</Col>
						<Col span="12">
						<FormItem label="联系人邮箱" prop="email">
							<Input v-model="formObj.email" :clearable="true" type="email" maxlength="50"
								placeholder="请输入联系人邮箱" class="inputSty"></Input>
						</FormItem>
						</Col>
					</Row>
					<Row justify="center">
						<Col span="12">
						<FormItem label="关联子渠道商">
							<Button style="margin-left: 8px" type="info" icon="md-add"
								@click="getSubChannel">查看</Button>
						</FormItem>
						</Col>
					</Row>
					<div style="text-align: center; margin-top: 20px;">
						<Button style="margin-left: 8px" @click="reset('formObj')"
							v-has="'reset'">重置</Button>&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="primary" :loading="submitLoading" @click="submit" v-has="'submit'">提交</Button>
					</div>
				</Form>
				<!-- 关联子渠道商弹出框-->
				<Modal title="关联子渠道商" v-model="subChannelFlage" :footer-hide="true" :mask-closable="false"
					:closable="false" width="700px">
					<div style="padding: 0 16px">
						<div style="display: flex;justify-content: flex-start;align-items: flex-start">
							<Form ref="searchObj" :model="searchObj" :rules="subChannelRule" :label-width="120">
								<FormItem label="渠道商名称" prop="corpName">
									<Input v-model="searchObj.corpName" placeholder="请输入渠道商名称" clearable
										style="width: 200px" />
								</FormItem>
							</Form>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="primary" :loading="searchloading">
								<div style="display: flex; align-items: center" @click="search">
									<Icon type="md-search" v-has="'search'" />&nbsp;搜索
								</div>
							</Button>
						</div>
						<div>
							<Table ref="table" :columns="columns" :data="subChannelData" :loading="subChannelLoading"
								no-data-text="暂无数据" style="display: flex; justify-content: space-evenly;">
								<template slot-scope="{ row ,index}" slot="corpName">
									<strong>{{ row.corpName }}</strong>
								</template>
								<template #checkBox="{ row, index }">
									<i-Switch v-model="row.checked" @on-change="showCheckBox(row,row.checked,index)" />
								</template>
								<template #action="{ row, index }">
									<Input type="number" v-model="row.provinceCode" :readonly="!row.checked"
										@on-blur="show(row,row.provinceCode,index)" />
									<!-- <div v-if="row.checked && (row.provinceCode===null ||row.provinceCode==='')">
										<p class="errorPrice" style="text-align: left;color:red">
											请输入省份编码
										</p>
									</div> -->
								</template>
							</Table>
							<!-- 分页 -->
							<div style="margin-top: 40px;">
								<Page :total="total" :current.sync="currentPage" show-total show-elevator
									@on-change="goPage" />
							</div>
						</div>
						<div style="text-align: center; margin-top: 20px;">
							<Button @click="cancelModal">返回</Button>&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="primary" :loading="besureLoading" @click="besure">确定</Button>
						</div>
					</div>
				</Modal>
			</div>
		</div>
	</Card>
</template>

<script>
	import {
		querySubChannel,
		subChannel,
	} from "@/api/customer/zeroChannel.js";
	export default {
		data() {
			var validateEmail = (rule, value, callback) => {
				if (!value || value.indexOf("@") == -1) {
					callback(new Error("请输入有效的邮箱地址"));
				} else {
					callback();
				}
			};
			return {
				checked: false,
				submitLoading: false,
				searchloading: false,
				subChannelLoading: false,
				besureLoading: false,
				subChannelFlage: false,
				total: 0,
				currentPage: 1,
				purchaseStatusList: [{
						label: "允许订购",
						value: "1",
					},
					{
						label: "不允许订购",
						value: "2",
					},
				],
				formObj: {
					corpName: '', //渠道商名称
					isSub: '', //渠道商状态
					companyName: '', //公司名称
					address: '', //地址
					ebsCode: '',
					email: '', //邮箱
					relationChannel: [],
				},
				searchObj: {
					corpName: "", //渠道商名称
				},
				ruleValidate: {
					corpName: [{
						required: true,
						type: "string",
						message: "渠道商名称不能为空",
					}, {
            // 新的校验规则，检查是否包含&符号
            validator: (rule, value, callback) => {
              if (value && value.includes('&')) {
                callback(new Error('渠道商名称不能包含&符号'));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },],
					isSub: [{
						required: true,
						message: "渠道商状态不能为空",
						trigger: "change",
					}, ],
					companyName: [{
							required: true,
							type: 'string',
							message: '公司名称不能为空',
							trigger: 'blur'
						},
            {
              // 新的校验规则，检查是否包含&符号
              validator: (rule, value, callback) => {
                if (value && value.includes('&')) {
                  callback(new Error('公司名称不能包含&符号'));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
					],
					address: [{
							required: true,
							type: 'string',
							message: '地址不能为空',
							trigger: 'blur'
						},
						{
							max: 200,
							message: '最长200位'
						}
					],
					ebsCode: [{
						required: true,
						type: "string",
						message: "EBSCode不能为空",
					}, ],
					email: [{
							required: true,
							message: "联系人邮箱不能为空",
						},
						{
							validator: validateEmail,
							trigger: "blur",
						},
						{
							required: true,
							type: "email",
							trigger: "blur",
							message: "联系人邮箱格式错误",
						},
					],
				},
				subChannelRule: {},
				columns: [{
						title: "渠道商名称",
						slot: "corpName",
						align: "center",
						minWidth: 200,
						tooltip: true,
					},
					{
						title: "关联",
						width: 120,
						align: 'center',
						slot: 'checkBox',
					},
					{
						title: "渠道编码",
						width: 200,
						align: 'center',
						slot: 'action',
					},
				],
				subChannelData: [], //关联子渠道商列表数据
				corps: [],
				beforeUpdateChannelDate: [],
				List:[]
			}
		},
		mounted() {
			//清除缓存
			localStorage.removeItem("relationChannel")
		},
		methods: {
			// 查看子渠道商
			getSubChannel() {
				this.subChannelFlage = true
				this.goPageFirst(1,0)
			},
			// 关联子渠道商对话框列表
			goPageFirst: function(page,flag) {
				this.subChannelLoading = true
				var _this = this
				querySubChannel({
					pageSize: 10,
					pageNum: page,
					corpName: this.searchObj.corpName,
					topChannelCorpId: this.formObj.corpName,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.subChannelLoading = false
						this.currentPage = page
						this.total = res.count
						this.subChannelData = res.data
						// 回显
						//缓存中提取
						let relationChannel=JSON.parse(localStorage.getItem("relationChannel")) === [] ?
						'' : JSON.parse(localStorage.getItem("relationChannel"))
						let List= flag===0 ? relationChannel:this.corps
						if(List){
							this.subChannelData.forEach((i) => {
								List.forEach((item) => {
									if (item.corpId === i.corpId) {
										i.provinceCode = item.provinceCode
										i.checked = item.checked
									}
								})
							})
						}
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.subChannelLoading = false
				})
			},
			search() {
				this.goPageFirst(1)
			},
			goPage(page) {
				this.goPageFirst(page,1)
				this.List=this.beforeUpdateChannelDate
			},
			// 点击勾选开关触发
			showCheckBox: function(row, checked, index) {
				this.subChannelData.filter(item => item.corpId === row.corpId).forEach(item => {
					item.checked = checked
					if (checked) {
						item.provinceCode = row.provinceCode
						this.corps.push({
							"corpId": row.corpId,
							"corpName": row.corpName,
							"provinceCode": row.provinceCode,
							"checked": checked
						})
					} else {
						row.provinceCode = null
						item.provinceCode = row.provinceCode
					}
					// item.provinceCode = checked ? row.provinceCode : null
					if(!checked){ //不勾选
						this.corps.forEach((i, index) => {
							if (i.corpId === row.corpId) {
								this.corps.splice(index, 1)
							}
						})
					}
				})
			},
			// 输入渠道编码触发
			show: function(row, provinceCode, index) {
				this.subChannelData.forEach((item, index) => {
					if(item.checked){
						// 判断渠道编码是否输入
						let exit=false
						this.corps.forEach((i) => {
							//判断是否重复
							if (item.corpId === i.corpId) {
								exit=true
							}
						})
						if(exit){
							this.corps.forEach((i, index) => {
								if (i.corpId === row.corpId) {
									this.corps[index]={
										"corpId": row.corpId,
										"corpName": row.corpName,
										"provinceCode": provinceCode,
										"checked":item.checked
									}
								}
							})
						}else{
							this.corps.push({
								"corpId": row.corpId,
								"corpName": row.corpName,
								"provinceCode": provinceCode,
								"checked":item.checked
							})
						}
					}
				})
			},
			// 关联子渠道商
			besure() {
				// try {
				// 	this.corps.forEach((i) => {
				// 		// 如果勾选但是没有输入渠道编码 就报异常
				// 		if (i.checked === true && (i.provinceCode === '' || i.provinceCode === null)) {
				// 			throw new Error('End Loop ')
				// 		}
				// 	})
				// 	this.formObj.relationChannel=this.corps
				// 	localStorage.setItem("relationChannel", JSON.stringify(this.formObj.relationChannel))
				// 	this.subChannelFlage = false
				// 	this.searchObj.corpName = ""
				// } catch (e) {
				// 	if (e.message === 'End Loop ') throw e
				// }
				this.formObj.relationChannel=this.corps
				localStorage.setItem("relationChannel", JSON.stringify(this.formObj.relationChannel))
				this.subChannelFlage = false
				this.searchObj.corpName = ""
			},
			cancelModal() {
				let relationChannel=JSON.parse(localStorage.getItem("relationChannel")) === [] ?
				'' : JSON.parse(localStorage.getItem("relationChannel"))
				this.corps=relationChannel
				this.subChannelFlage = false
				this.searchObj.corpName = ""
			},
			//重置
			reset() {
				this.formObj = {
					corpName: '',
					isSub: '',
					companyName: '',
					address: '',
					ebsCode: '',
					email: '',
					relationChannel: [],
				}
				this.subChannelData = []
				this.searchObj.corpName = ''
				this.$refs["formObj"].resetFields();
			},
			//提交
			submit() {
				this.$refs["formObj"].validate((valid) => {
					if (valid) {
						var data = this.formObj;
						// 勾选框触发一次，渠道编码输入框触发一次，需要去重
						let list = data.relationChannel
						function fn(list) {
							for (let i = 0; i < list.length; i++){
								for (let j = i+1; j < list.length; j++){
									if ( list[i].corpId == list[j].corpId){
										list.splice(j, 1);
										j--
									}
								}
							}
							return list
						}
						var data1 = {
							corpName: data.corpName,
							isSub: data.isSub,
							address: data.address,
							ebsCode: data.ebsCode,
							email: data.email,
							ebsCode: data.ebsCode,
							companyName: data.companyName,
							// relationChannel: data.relationChannel
							relationChannel: fn(list)
						}
						this.submitLoading = true;
						subChannel(data1).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								});
								this.$router.push({
									name: 'zeroLevelChannel',
								});
							} else {
								this.submitLoading = false;
								throw res
							}
						}).catch((err) => {
							this.submitLoading = false;
						})
					}
				})
			},
		},
	}
</script>

<style>
	.inputSty {
		width: 250px;
	}
	/* 去掉input为number的上下箭头 */
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
	    -webkit-appearance: none;
	}
	input[type="number"]{
	    -moz-appearance: textfield;
	}
</style>
