<template>
	<Card>
		<DatePicker format="yyyyMMdd" v-model="time" @on-change="handleDateChange" :editable="false" type="daterange"
			:placeholder="$t('Period')" clearable style="width: 200px ;margin: 0 10px 0 0;" @on-clear="hanldeDateClear">
		</DatePicker>
		<Input v-model="taskId" :placeholder="$t('TaskID')" clearable style="width: 200px ;margin-right: 10px;" />
		<Input v-model="fileName" :placeholder="$t('FileName')" clearable style="width: 200px ;margin-right: 10px;" />
		<Button v-has="'search'" type="primary" icon="md-search" @click="search()" :loading="searchloading"
			style="margin-right: 10px;">{{$t('deposit.search')}}</Button>
		<div style="margin-top:20px">
			<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
				　　<template slot-scope="{ row, index }" slot="action">
					<Button v-if="row.taskStatus==='2'" v-has="'export'" type="primary" @click="downloadFile(row)" style="margin-right: 10px;">
						{{$t('DownloadFlie')}}
					</Button>
					<Button v-else v-has="'export'" type="primary" disabled @click="downloadFile(row)" style="margin-right: 10px;">
						{{$t('DownloadFlie')}}
					</Button>
				</template>
			</Table>
		</div>
		<a ref="downloadLink" style="display: none"></a>
		<div class="table-botton" style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
	</Card>
</template>

<script>
	import {
		queryTask,
		getDownloadFileUrl,
	} from '@/api/download';
	import {
		searchcorpid
	} from '@/api/channel.js';
	export default {
		data() {
			return {
				time: [],
				fileName: '',
				taskId: '',
				corpId: '',
				startTime: '',
				endTime: '',
				channelCorpId: '',
				searchloading: false,
				loading: false,
				total: 0,
				currentPage: 1,
				page: 1,
				tableData: [{}],
				columns: [{
						title: this.$t('Tasks'),
						key: 'id',
						align: 'center'
					},
					{
						title: this.$t('Description'),
						key: 'taskDesc',
						align: 'center'
					},
					{
						title: this.$t('TasksStatus'),
						key: 'taskStatus',
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							const text = row.taskStatus == '1' ? this.$t('buymeal.Processing') : row.taskStatus ==
								'2' ? this.$t('order.Completed') : row.taskStatus == '3' ? this.$t(
									'common.failure') : ''
							return h('label', text);
						}
					},
					{
						title: this.$t('File'),
						key: 'fileName',
						align: 'center'
					},
					{
						title: this.$t('CreationTime'),
						key: 'createTime',
						align: 'center'
					},
					{
						title: this.$t('FinishedTime'),
						key: 'endTime',
						align: 'center'
					},
					{
						title: this.$t('Operation'),
						slot: 'action',
						align: 'center'
					}
				],
			}
		},
		methods: {
			goPageFirst(page) {
				let corpId = ""
				searchcorpid({
					userName: this.$store.state.user.userName
				}).then(res => {
					if (res.code == '0000') {
						corpId = this.corpId || res.data
					}
					return corpId 
				}).then(corpId => {
					queryTask({
						taskId: this.taskId,
						fileName: this.fileName,
						pageNum: page,
						pageSize: 10,
						startTime: this.startTime === "" ? null : this.startTime,
						endTime: this.endTime === "" ? null : this.endTime,
						corpId: corpId || this.$store.state.user.userId,
						roleId: this.$store.state.user.roleId
					}).then(res => {
						if (res && res.code == '0000') {
							this.currentPage = page
							this.tableData = res.data.records
							this.total = res.data.total
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err)
					}).finally(() => {
						this.loading = false
						this.searchloading = false
					})

				}).catch((err) => {}).finally(() => {})
			},
			// 分页跳转
			goPage(page) {
				this.goPageFirst(page)
			},
			search() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			handleDateChange(dateArr) {
				let beginDate = this.time[0] || ''
				let endDate = this.time[1] || ''
				if (beginDate == '' || endDate == '') {
					return
				}
				[this.startTime, this.endTime] = dateArr
			},
			hanldeDateClear() {
				this.startTime = ''
				this.endTime = ''
			},
			//文件下载
			downloadFile(row) {
				let id = row.id
				let corpId = null
				if (this.corpId) {
					corpId = this.$store.state.user.userId
				}
				const link = this.$refs.downloadLink // 创建a标签
				link.href = getDownloadFileUrl(id, corpId)
				link.click() // 执行下载
			},
		},

		mounted() {
			this.taskId = decodeURIComponent(this.$route.query.taskId);
			this.fileName = decodeURIComponent(this.$route.query.fileName);
			this.corpId = decodeURIComponent(this.$route.query.corpId);
			// if (!!this.corpId) {
			// 	searchcorpid({
			// 		userName: this.$store.state.user.userName
			// 	}).then(res => {
			// 		if (res.code == '0000') {
			// 			let corpId = res.data
			// 			this.corpId = corpId
			// 		}
			// 	}).catch((err) => {}).finally(() => {})
			// 	if (this.corpId != '') {
			// 		console.log(this.corpId) 
			// 	}
			// }
			if (this.taskId === 'undefined') {
				this.taskId = ''
			}
			if (this.fileName === 'undefined') {
				this.fileName = ''
			}
			if (this.corpId === 'undefined') {
				this.corpId = ''
			}
			this.goPageFirst(1)
		}
	}
</script>

<style>
</style>
