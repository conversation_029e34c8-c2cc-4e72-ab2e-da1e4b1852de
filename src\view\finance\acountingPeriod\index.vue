<template>
	<!-- 账期管理 -->
	<Card>
		<!-- 搜索条件 -->
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">账期名称</span>
				<Input v-model.trim="name" maxlength="50" clearable placeholder="请输入账期名称" class="seachSty"/>
			</div>
      <div class="search_box">
        <span class="search_box_label">账期类型</span>
        <Select v-model="type" placeholder="请选择账期类型" clearable class="seachSty">
          <Option value="1">标准账期</Option>
          <Option value="2">自然月</Option>
          <Option value="3">定制账期</Option>
        </Select>
      </div>
			<div style="width: 110px; display: flex;justify-content: center; margin-bottom: 20px;">
				<Button type="primary" icon="md-search" :loading="searchloading" @click="searchOne()" v-has="'search'">搜索</Button>
			</div>
			<div style="width: 110px; display: flex;justify-content: center; margin-bottom: 20px;">
				<Button type="info" icon="md-add" @click="addItem" v-has="'add'">新增</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data" style="width:100%; margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button type="error" ghost @click="delItem(row)" v-has="'delete'">删除</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 新增弹窗 -->
		<Modal v-model="modal1" title="账期新建" @on-cancel="cancelModal" :mask-closable="false" width="700px">
			<Form ref="formObj" :model="formObj"  :rules="ruleAddValidate" :label-width="100" style="padding: 10px;" @submit.native.prevent>
				<Row>
				  <Col span="12">
            <FormItem label="账期名称" prop="name">
            	<Input v-model.trim="formObj.name" maxlength="50" clearable placeholder="请输入账期名称" class="addInputSty"/>
            </FormItem>
          </Col>
				  <Col span="12">
            <FormItem label="账期类型" prop="type">
              <Select v-model="formObj.type" placeholder="请选择账期类型" class="addInputSty">
                <Option value="1">标准账期</Option>
                <Option value="2">自然月</Option>
                <Option value="3">定制账期</Option>
              </Select>
            </FormItem>
          </Col>
				</Row>
        <Row v-if="formObj.type == '3'">
          <Col span="12">
            <FormItem label="出账周期" prop="accountingPeriod" :rules="formObj.type == '3' ? ruleAddValidate.accountingPeriod : [{required: false}]">
              <Input v-model.trim="formObj.accountingPeriod" type="number" clearable placeholder="请输入出账周期" class="addInputSty numSty">
                <template #append>
                  <span>月</span>
                </template>
              </Input>
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem label="出账次数" prop="paymentNum" :rules="formObj.type == '3' ? ruleAddValidate.paymentNum : [{required: false}]">
            	<Input id="accountingTimes" v-model.number="formObj.paymentNum" type="number" clearable placeholder="请输入出账次数" class="addInputSty numSty" />
            </FormItem>
          </Col>
        </Row>
        <Row v-if="formObj.type == '3'">
          <Col span="12">
            <FormItem label="记账开始时间" prop="startTime" :rules="formObj.type == '3' ? ruleAddValidate.startTime : [{required: false}]">
              <DatePicker type="date" :options="options1" v-model="formObj.startTime" placeholder="选择记账开始时间" style="width: 200px" />
            </FormItem>
          </Col>
          <Col span="12">
            <FormItem :label="'出账时间' + (index + 1)" v-for="(item, index) in timePickers" :key="index" prop="'cycleStart' + index" :rules="getRules(index)">
            	<DatePicker type="date" :options="options1" :disabled="(index === timePickers.length - 1) || (!formObj.startTime || !formObj.accountingPeriod)"
                v-model="timePickers[index]" placeholder="选择出账时间" style="width: 200px" @on-change="changeTime(index, $event)" />
            </FormItem>
          </Col>
        </Row>
			</Form>
			<div slot="footer" class="footer_wrap">
				<Button icon="ios-arrow-back" @click="cancelModal">返回</Button>
        <Button type="primary" @click="submit" v-preventReClick :loading="submitLoading">确定</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
  import {
    getAcountingPeriod,
    addItem,
		delItem
	} from '@/api/finance/acountingPeriod.js'
	export default {
		data() {
      //校验出账时间
      const validateCycleStart = (rule, value, callback) => {

        // 第一档出场时间不能小于记账开始时间
        const startTime = new Date(this.formObj.startTime);
        startTime.setHours(0, 0, 0, 0); // 确保startTime也是当天的0点

        const timePickers = this.timePickers.map(timeStr => {
          const date = new Date(timeStr);
          date.setHours(0, 0, 0, 0); // 重置时间为当天的0点
          return date;
        });
        let lastCycleEndTime = this.timePickers[this.timePickers.length - 1]
        let statrtCycleEndTime = this.timePickers[0]

        if (timePickers.length > 0 && (timePickers[0].getTime() < startTime.getTime() || timePickers[0].getTime() === startTime.getTime())) {
          callback(new Error('出账时间不能小于或等于记账开始时间'));
          return;
        }

        if (this.formObj.paymentNum == '2' && new Date(statrtCycleEndTime) > new Date(lastCycleEndTime)) {
          callback(new Error('出账时间不能超过最后一个出账时间'));
          return;
        }

        // 校验每一档出账时间都要大于上一档出账时间
        for (let i = 1; i < this.timePickers.length; i++) {
          if (i == this.timePickers.length - 1) {
            callback();
          }
          if (this.timePickers[i] && this.timePickers[i - 1] &&  !(this.timePickers[i] > this.timePickers[i - 1])) {
            callback(new Error(`出账时间${i + 1}不能小于或等于出账时间${i}`));
            return;
          }
          // 如果存在 lastCycleEndTime，则校验不能大于它
          if (lastCycleEndTime && new Date(this.timePickers[i]) > new Date(lastCycleEndTime)) {
            callback(new Error('出账时间不能超过最后一个出账时间'));
            return;
          }
        }
        // for (let i = 1; i < timePickers.length; i++) {
        //   if (!(timePickers[i] > timePickers[i - 1])) {
        //     callback(new Error(`出账时间${i + 1}不能小于或等于出账时间${i}`));
        //     return;
        //   }
        //   // 如果存在 lastCycleEndTime，则校验不能大于它
        //   if (lastCycleEndTime && timePickers[i] > lastCycleEndTime) {
        //     callback(new Error('出账时间不能超过最后一个出账时间'));
        //     return;
        //   }
        // }

        // if (timePickersDates.length != this.formObj.paymentNum) {
        // 	callback(new Error('出账开始时间不能为空'));
        // 	return;
        // } else {
        // 	callback();
        // }
      };
			return {
				total: 0,
				currentPage: 1,
				page: 0,
				name: "", //账期名称
				type: "", //账期类型
				loading: false,
				searchloading: false, //查询加载
				data: [], //表格列表
				columns: [{
					title: "账期名称",
					key: 'name',
					minWidth: 150,
					align: 'center',
					tooltip: true
				}, {
					title: "账期类型",
					key: 'type',
					minWidth: 150,
					align: 'center',
					tooltip: true,
          render: (h, params) => {
            const row = params.row;
            const text = row.type == '1' ? '标准账期' : row.type == '2' ? '自然月' : row.type == '3' ? '定制账期': '';
            return h('label', text);
          }
				}, {
					title: "出账周期（月）",
					key: 'accountingPeriod',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: "出账次数",
					key: 'paymentNum',
					minWidth: 100,
					align: 'center',
					tooltip: true,
				}, {
					title: "服务开始时间",
					key: 'start',
					minWidth: 160,
					align: 'center',
					tooltip: true, // 开启 tooltip
          render: (h, params) => {
            const row = params.row;
            const servicesCycles = row.servicesCycles;
            let lastTent = "..."

            if (servicesCycles.length === 0) {
              return h('span', ''); // 或者其他适当的占位符
            }

            if (servicesCycles.length === 1) {
              return h('span', servicesCycles[0].start);
            }

            if (servicesCycles.length === 2) {
              // 直接显示两个元素，不需要 Tooltip
              return h('div', [
                h('div', servicesCycles[0].start),
                h('div', servicesCycles[1].start),
              ]);
            }

            // 当有超过两个元素时，使用 Tooltip 显示前两个元素，并在 Tooltip 中显示所有元素
            return h('div', [
              h('Tooltip', {
                props: {
                  placement: 'bottom',
                  transfer: true,
                },
                style: {
                  cursor: 'pointer',
                },
              }, [
                h('span', {
                  style: {
                    display: 'block',
                  },
                }, servicesCycles[0].start),
                h('span', {}, servicesCycles[1].start),
                h('div', {}, lastTent),
                h('ul', {
                  slot: 'content',
                  style: {
                    listStyleType: 'none',
                    whiteSpace: 'normal',
                    wordBreak: 'break-all' ,//超出隐藏
                  },
                },
                  this.data[params.index].servicesCycles.map(item => { // 从第三个元素开始映射
                    return h('li', item.start);
                  })),
                ]),
              ]);
            },
          }, {
					title: "服务结束时间",
					key: 'end',
					minWidth: 160,
					align: 'center',
          tooltip: true, // 开启 tooltip
          render: (h, params) => {
            const row = params.row;
            const servicesCycles = row.servicesCycles;
            let lastTent = "..."

            if (servicesCycles.length === 0) {
              return h('span', ''); // 或者其他适当的占位符
            }

            if (servicesCycles.length === 1) {
              return h('span', servicesCycles[0].end);
            }

            if (servicesCycles.length === 2) {
              // 直接显示两个元素，不需要 Tooltip
              return h('div', [
                h('div', servicesCycles[0].end),
                h('div', servicesCycles[1].end),
              ]);
            }

            // 当有超过两个元素时，使用 Tooltip 显示前两个元素，并在 Tooltip 中显示所有元素
            return h('div', [
              h('Tooltip', {
                props: {
                  placement: 'bottom',
                  transfer: true,
                },
                style: {
                  cursor: 'pointer',
                },
              }, [
                h('span', {
                  style: {
                    display: 'block',
                  },
                }, servicesCycles[0].end),
                h('span', {}, servicesCycles[1].end),
                h('div', {}, lastTent),
                h('ul', {
                  slot: 'content',
                  style: {
                    listStyleType: 'none',
                    whiteSpace: 'normal',
                    wordBreak: 'break-all' ,//超出隐藏
                  },
                },
                  this.data[params.index].servicesCycles.map(item => { // 从第三个元素开始映射
                    return h('li', item.end);
                  })),
                ]),
              ]);
            },
          }, {
					title: "关联渠道商",
					key: 'corpPeriods',
					minWidth: 250,
					align: 'center',
					tooltip: true,
          render: (h, params) => {
            const row = params.row;
            const corpPeriods = row.corpPeriods;
            let lastTent = "..."

            if (corpPeriods.length === 0) {
              return h('span', ''); // 或者其他适当的占位符
            }

            if (corpPeriods.length === 1) {
              return h('span', corpPeriods[0].corpName);
            }

            if (corpPeriods.length === 2) {
              // 直接显示两个元素，不需要 Tooltip
              return h('div', [
                h('div', corpPeriods[0].corpName),
                h('div', corpPeriods[1].corpName),
              ]);
            }

            // 当有超过两个元素时，使用 Tooltip 显示前两个元素，并在 Tooltip 中显示所有元素
            return h('div', [
              h('Tooltip', {
                props: {
                  placement: 'bottom',
                  transfer: true,
                },
                style: {
                  cursor: 'pointer',
                },
              }, [
                h('span', {
                  style: {
                    display: 'block',
                  },
                }, corpPeriods[0].corpName),
                h('span', {}, corpPeriods[1].corpName),
                h('div', {}, lastTent),
                h('ul', {
                  slot: 'content',
                  style: {
                    listStyleType: 'none',
                    whiteSpace: 'normal',
                    wordBreak: 'break-all' ,//超出隐藏
                  },
                },
                  this.data[params.index].corpPeriods.map(item => { // 从第三个元素开始映射
                    return h('li', item.corpName);
                  })),
                ]),
              ]);
          },
        },  {
					title: "操作",
					slot: 'action',
					minWidth: 120,
					align: 'center',
					fixed: 'right'
				}, ],
				// ——————————**新增/编辑**————————————
				modal1: false,
				submitLoading: false,
				title: "",
				index: 0,
        timePickers: [],
        options1: {
          disabledDate(date) {
            // 如果日期的天数大于28，则返回true以禁用该日期
            return date && date.getDate() > 28;
          }
        },
				formObj: {
					name: "",
					type: "",
          accountingPeriod: null,
          paymentNum: '',
          startTime: "",
          cycleStart: {},
				},
				ruleAddValidate: {
					name: [{
						required: true,
						type: 'string',
						message: '账期名称不能为空',
					}],
					type: [{
						required: true,
						message: '账期类型不能为空',
					}],
					accountingPeriod: [{
						required: true,
						message: '出账周期不能为空',
            trigger: 'blur',
					},
          {
            validator: (rule, value, cb) => {
            	var str = /^[1-9]\d*$/;
            	return str.test(value);
            },
            message: "请输入正整数",
          }],
          paymentNum: [{
          	required: true,
            type: 'number',
          	message: '出账次数不能为空',
            trigger: 'change',
          },
          {
            validator: (rule, value, cb) => {
            	var str = /^[1-9]\d*$/;
            	return str.test(value);
            },
            message: "请输入正整数",
          }],
          startTime: [{
          	required: true,
            type: 'date',
          	message: '记账开始时间不能为空',
            trigger: 'change'
          }],
          cycleStart: [
          {
            validator: validateCycleStart,
          }]
				}
			}
		},
		mounted() {
			this.goPageFirst(1)
		},
    watch: {
      // 监听formObj.paymentNum的变化，动态调整timePickers数组的长度
      'formObj.paymentNum'(newVal) {
        // 当出账次数变化时，更新timePickers数组
        this.updateTimePickers(newVal);
        // 调用自动回填函数（如果有的话）
        this.updateLastPaymentDate();
      },
      // 还需要监听startTime和accountingPeriod的变化来自动回填
      'formObj.startTime'(newVal) {
        if (newVal) {
          this.updateLastPaymentDate();
        }
      },
      'formObj.accountingPeriod'(newVal) {
        if (newVal) {
          this.updateLastPaymentDate();
        }
      },
    },
		methods: {
      goPageFirst: function(page) {
				this.loading = true
				var _this = this
				getAcountingPeriod({
					current: page,
					size: 10,
					name: this.name,
					type: this.type
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = Number(res.count)
            this.data = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			searchOne: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			// ————————————————————————————****新增账期****————————————————————————
			//新增应用
			addItem() {
				this.modal1= true
			},
      // 取消
      cancelModal() {
      	this.modal1= false
      	this.$refs['formObj'].resetFields()
      },
			//删除应用
			delItem(row) {
				this.$Modal.confirm({
				  title: '确认删除？',
				  onOk: () => {
				    delItem({
              id: row.id
            }).then(res => {
				      if (res && res.code == '0000') {
				        this.$Notice.success({
				          title: '操作提示',
				          desc: '操作成功'
				        })
				        this.goPageFirst(1)
				      } else {
				        throw res
				      }
				    }).catch((err) => {

				    })
				  }
				});
			},
			//提交新建账期
			submit() {
        let timePickersDates = this.timePickers.filter(date => date !== null && date !== "" && date !== undefined);
        if (this.formObj.type == '3' && timePickersDates.length !== this.formObj.paymentNum) {
          this.$Message['warning']({
            background: true,
            content: '请确保所有出账时间都已完整选择！'
          });
        } else {
          this.$refs["formObj"].validate((valid) => {
            if (valid) {
              let startTime = this.formObj.type == '3' ? this.formatDateToYYYYMMDD(this.formObj.startTime) : undefined;
          		this.submitLoading = true;
              this.timePickers = this.formatDatesToYYYYMMDD(this.timePickers)
          		addItem({
          			name: this.formObj.name,
          			type: this.formObj.type,
                startTime: this.formObj.type == '3' ? startTime : undefined,
                paymentTimes: this.formObj.type == '3' ? this.timePickers : undefined,
          			paymentNum: this.formObj.type == '3' ? this.formObj.paymentNum : undefined,
                accountingPeriod: this.formObj.type == '3' ? this.formObj.accountingPeriod : undefined,
          		}).then(res => {
          			if (res && res.code == '0000') {
          				this.$Notice.success({
          					title: '操作提示',
          					desc: '操作成功'
          				});
          				setTimeout(() => {
          					this.submitLoading = false;
          					this.cancelModal();
          					this.goPageFirst(1)
          				}, 1500);
          			} else {
          				this.submitLoading = false;
          				throw res
          			}
          		}).catch((err) => {
          			this.submitLoading = false;
          		})
          	}
          })
        }
			},
      // 校验出账时间
      getRules(index) {
        // 根据需要返回规则，最后一个元素是只读的，所以不需要验证
        return index ===
          this.timePickers.length - 1 ? [{ required: false, validator: () => true }]
          : this.formObj.type === '3' ? this.ruleAddValidate.cycleStart : [{ required: false }];
      },
      //回填最后一个出账时间
      updateLastPaymentDate() {
          const startDate = new Date(this.formObj.startTime);
          const monthsToAdd = parseInt(this.formObj.accountingPeriod, 10) * (this.formObj.accountingTimes || 1);

          // 创建一个新的日期对象以避免修改原始startDate
          let targetDate = new Date(startDate);

          // 备份原始日期（日）
          const originalDay = targetDate.getDate();

          // 设置新的月份
          targetDate.setMonth(targetDate.getMonth() + monthsToAdd);

          // 检查日期是否仍然有效（即是否仍然是原始日期）
          if (targetDate.getDate() !== originalDay) {
              targetDate.setDate(1);
          }
          // 格式化日期
          const lastPaymentDate = this.formatDate(targetDate);
          this.timePickers[this.timePickers.length - 1] = lastPaymentDate;
      },
      // 出账次数改变时 回填出账时间
      updateTimePickers(paymentNum) {
        // 根据出账次数来更新timePickers数组
        // 这里只是一个示例，您需要根据实际情况来设置timePickers的值
        this.timePickers = Array.from({ length: paymentNum }, (_, index) => null); // 初始化为null或其他默认值
      },
      formatDate(date) {
        // 格式化日期为YYYY-MM-DD
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
      },
      changeTime(index, value) {
        this.timePickers[index] = value; // 确保 value 被正确设置到 timePickers 数组中

        // 更新 formObj.cycleStart 对象，用新的值覆盖旧的值（如果已存在）
        this.formObj.cycleStart[index] = value;
      },
      formatDateToYYYYMMDD(dateString) {
          // 解析ISO 8601格式的日期时间字符串
          const date = new Date(dateString);

          // 检查日期是否有效
          if (isNaN(date.getTime())) {
              throw new Error('Invalid date string');
          }

          // 使用padStart方法来确保月份和日期都是两位数
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0'); // getMonth() 返回的是从0开始的月份，所以需要加1
          const day = String(date.getDate()).padStart(2, '0');

          // 拼接日期字符串
          return `${year}-${month}-${day}`;
      },
      formatDatesToYYYYMMDD(dates) {
          return dates.map(dateStr => {
              // 尝试将字符串解析为Date对象
              const date = new Date(dateStr);

              // 检查日期是否有效
              if (isNaN(date.getTime())) {
                  // 如果日期无效，返回原始字符串或进行其他错误处理
                  return dateStr;
              }

              // 将Date对象格式化为yyyy-mm-dd格式的字符串
              // 注意：月份从0开始，所以需要+1；日期和月份需要补零
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');

              // 返回格式化后的字符串
              return `${year}-${month}-${day}`;
          });
      },
    }
	}
</script>

<style scoped="scoped">
	.search_head_i {
		margin-top: 30px;
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 120px;
	}

  .seachSty {
    width: 300px;
  }

  .addInputSty {
     width: 200px;
  }

  /deep/.numSty input::-webkit-outer-spin-button,
  /deep/.numSty input::-webkit-inner-spin-button {
      -webkit-appearance: none;
  }
  /deep/.numSty input[type="number"] {
      -moz-appearance: textfield;
  }

	.footer_wrap {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
