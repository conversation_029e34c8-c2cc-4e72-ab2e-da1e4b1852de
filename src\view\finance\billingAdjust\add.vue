<template>
  <div>
    <Card>
      <!-- 搜索条件 -->
      <div class="search_head_i">
        <!-- <div class="search_box">
          <span class="search_box_label">账单类型</span>
          <Select
              v-model="searchCondition.billType"
              clearable
              placeholder="下拉选择账单类型"
              style="width: 200px"
            >
              <Option
                value="1"
                >渠道商收入</Option
              >
              <Option
                value="2"
                >白卡订单收入</Option
              >
            </Select> 
        </div> -->
        <div class="search_box">
          <span class="search_box_label">公司名称</span>
          <Select
              v-model="searchCondition.corpId"
              clearable
              multiple
              filterable
              placeholder="下拉选择客户名称"
              style="width: 200px"
            >
              <Option
                :value="item.corpId"
                v-for="(item, index) in corpLists"
                :key="index"
                >{{ item.companyName }}</Option
              >
            </Select>
        </div>
        <div class="search_box">
          <span class="search_box_label">账期</span>
          <DatePicker format="yyyyMM" v-model="searchCondition.billingPeriod" type="month" placement="bottom-start" placeholder="请选择账期"
				     @on-change="handleBillingPeriodMonth" :editable="true"></DatePicker>  
        </div>
        <div style="width: 110px; display: flex;justify-content: center; margin-bottom: 20px;">
          <Button type="primary" icon="md-search" v-has="'search'" :loading="searchloading" @click="searchOne()">搜索</Button>
        </div>
      </div>
      <!-- 表格 -->
      <div>
          <Table :columns="columns" :data="data" :ellipsis="true" :loading="loading">
            <template slot-scope="{ row, index }" slot="action">
              <Button type="info" ghost size="small" style="margin: 5px" @click="showModal(row)"
                :disabled="row.isAdjustment == '1' || row.authStatus == '3'" v-has="'update'">账单调整</Button>
            </template>
          </Table>
      </div>
      <!-- 分页 -->
      <div style="margin-top:15px">
        <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
      </div>
    </Card>

    <Modal v-model="modal" title="账单调整" :mask-closable="false" @on-cancel="cancelModal" width="620px">
      <div class="search_head" style="margin: 0px;">
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="135" :label-height="100"
          inline style="font-weight:bold;">
          <FormItem prop="adjustWay"  style="margin-left: 0;">
            <div class="input_modal">
              <RadioGroup v-model="formValidate.adjustWay" @on-change="changeBillType">
                <Radio label="1">
                    <span>重新生成</span>
                </Radio>
                <Radio label="2">
                  <span>赔付</span>
                </Radio>
              </RadioGroup>
            </div>
          </FormItem>
          <FormItem label="上传附件说明"  style="width:510px">
            <Upload multiple type="drag" :action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :on-progress="fileUploading" :before-upload="handleBeforeUpload"
            >
              <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>点击或拖拽文件上传</p>
              </div>
            </Upload>
              <ul class="ivu-upload-list" v-if="formValidate.file" style="width: 100%;">
                <li class="ivu-upload-list-file ivu-upload-list-file-finish">
                  <span>
                    <Icon type="ios-folder" />  {{formValidate.file.name}}</span>
                  <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
                </li>
              </ul>
            <div style="width: 100%;">
              <Alert type="warning">{{message}}</Alert>
              <a ref="downloadLink" style="display: none"></a>
            </div>
          </FormItem>
          <FormItem label="调整原因" prop="reason" >
            <Select v-model="formValidate.reason" filterable placeholder="下拉选择调整原因" style="width:300px;"  clearable>
              <Option :value="index" v-for="(item,index) in reasons" :key="item">{{item}}</Option>
            </Select>
          </FormItem>
          <FormItem prop="noteType"  style="margin-left: 0;" v-if="formValidate.adjustWay == '2'" :rules="
             formValidate.adjustWay == '2' ? ruleValidate.noteType1 :[{required: false}]">
            <div class="input_modal">
              <RadioGroup v-model="formValidate.noteType">
                <Radio label="1">
                    <span>Credit Note</span>
                </Radio>
                <Radio label="2">
                  <span>Debit Note</span>
                </Radio>
              </RadioGroup>
            </div>
          </FormItem>
          <FormItem label="代销收入:" style="font-size: large;" prop="dx" v-if="formValidate.adjustWay == '1' && [ '0', '1', '4', '6', '7'].includes(cooperationMode)" :rules="
             formValidate.adjustWay == '1' ? ruleValidate.dxRule :[{required: false}]">
						<Input v-model='formValidate.dx' placeholder="请输入代销收入" :clearable="true" style="width: 300px;margin-right: 10px;">
						<span slot="append">元</span>
						</Input>
					</FormItem>
          <FormItem label="流量收入:" style="font-size: large;" prop="ll" v-if="formValidate.adjustWay == '1' && !(['0', '1', '8'].includes(cooperationMode))" :rules="
             formValidate.adjustWay == '1' ? ruleValidate.llRule :[{required: false}]">
          	<Input v-model='formValidate.ll' placeholder="请输入流量收入" :clearable="true" style="width: 300px;margin-right: 10px;">
          	<span slot="append">元</span>
          	</Input>
          </FormItem>
          <FormItem label="IMSI费收入:" style="font-size: large;" prop="imsi" v-if="formValidate.adjustWay == '1' && ['0', '1', '2', '3', '5'].includes(cooperationMode)" :rules="
             formValidate.adjustWay == '1' ? ruleValidate.imsiRule :[{required: false}]">
          	<Input v-model='formValidate.imsi' placeholder="请输入IMSI费收入" :clearable="true" style="width: 300px;margin-right: 10px;">
          	<span slot="append">元</span>
          	</Input>
          </FormItem>
          <FormItem label="代销IMSI费收入:" style="font-size: large;" prop="dximsi" v-if="formValidate.adjustWay == '1' && ['4', '6', '7'].includes(cooperationMode)" :rules="
             formValidate.adjustWay == '1' ? ruleValidate.dximsiRule :[{required: false}]">
          	<Input v-model='formValidate.dximsi' placeholder="请输入代销IMSI费收入" :clearable="true" style="width: 300px;margin-right: 10px;">
          	<span slot="append">元</span>
          	</Input>
          </FormItem>
          <FormItem label="流量IMSI费收入:" style="font-size: large;" prop="llimsi" v-if="formValidate.adjustWay == '1' && ['4', '6', '7'].includes(cooperationMode)" :rules="
             formValidate.adjustWay == '1' ? ruleValidate.llimsiRule :[{required: false}]">
          	<Input v-model='formValidate.llimsi' placeholder="请输入流量IMSI费收入" :clearable="true" style="width: 300px;margin-right: 10px;">
          	<span slot="append">元</span>
          	</Input>
          </FormItem>
          <FormItem label="调账金额:" style="font-size: large;" prop="dx" v-if="formValidate.adjustWay == '1' && cooperationMode == '8'" :rules="
             formValidate.adjustWay == '1' ? ruleValidate.dxRule :[{required: false}]">
          	<Input v-model='formValidate.dx' placeholder="请输入调账金额" :clearable="true" style="width: 300px;margin-right: 10px;">
          	<span slot="append">元</span>
          	</Input>
          </FormItem>
          <FormItem label="赔付金额:" prop="payoutsum" v-if="formValidate.adjustWay == '2'" :rules="
             formValidate.adjustWay == '2' ? ruleValidate.payoutsum1 :[{required: false}]">
            <Input placeholder="请输入赔付金额" v-model="formValidate.payoutsum" clearable style="width: 300px" />&nbsp;&nbsp;
          </FormItem>
          <FormItem label="描述" prop="depicted">
            <Input placeholder="请输入200字以内的描述..." maxlength="200" v-model="formValidate.depicted" clearable style="width: 300px" />&nbsp;&nbsp;
          </FormItem>
        </Form>
      </div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">返回</Button>
				<Button type="primary" :loading="addLoading" @click="handleUpload">提交</Button>
			</div>
		</Modal>

  </div>

</template>

<script>
  import {
    getChannelBill,
    getBlankCardBill,
    addAdjustInfo,
    getReasonList,
	} from '@/api/finance/other.js'
  import { getCorpList } from '@/api/product/package/batch';
const math = require('mathjs')

export default {
  data() {
    const validateNum = (rule, value, callback) => {
      debugger;
      var str1 = value
      if (value.substr(0, 1) === '-') {
        str1 = value.substr(1, value.length)
      }
      var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
      if (!str1 || str.test(str1)) {
        callback();
      } else {
        callback(new Error(rule.message));
      }
    };
    return {
      total: 0,
      currentPage: 1,
      page: 0,
      corpLists: [],
      corpNameListCorpId: [],
      billingPeriod: '',
      searchCondition: {
        // billType: '',
        corpId: [],
        billingPeriod: '',
      },
      loading: false,
      data: [], //表格列表
      columns: [{
					title: "公司名称",
					key: 'companyName',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "客户EBS编码",
					key: 'ebscode',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "Invoice no.",
					key: 'invoiceNo',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "币种",
					key: 'currency',
					minWidth: 150,
					align: 'center',
					render: (h, params) => {
            const row = params.row;
            const text = row.currency == '156' ? "CNY" : row.currency == '840' ? "USD" : row.currency == '344' ? "HKD" :
              '';
            return h('label', text);
          }
				},
        {
					title: "实际收入金额",
					key: 'realIncome',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "服务开始时间",
					key: 'svcStartTime',
					minWidth: 160,
					align: 'center',
					tooltip: true, // 开启 tooltip
        },
        {
					title: "服务结束时间",
					key: 'svcEndTime',
					minWidth: 160,
					align: 'center',
          tooltip: true, // 开启 tooltip
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          minWidth: 150,
        },
      ],
      batchInvoiceLoading: false,
      searchloading: false, //查询加载
      modal: false,
      modal1: false,
      formValidate: {
        adjustWay: '1',
        file: null,
        reason: '',
        newbillsum: '',
        noteType: '',
        payoutsum:'',
        depicted: '',
        dx: '',
        imsi: '',
        ll: '',
        dximsi: '',
        llimsi: '',
      },
      ruleValidate: {
        adjustWay: [
          { required: true, message: '请选择账单调账类型',trigger: 'change' }
        ],
        file: [{
          required: true,
          message: '请上传文件',
          trigger: 'blur'
        }],
        reason: [
          { required: true, type: 'string', message: '请选择调整原因'}
        ],
        newbillsum1: [
          {
            required: true,
            type: 'string',
            message: '请输入新帐单金额',
          },
          {
            validator: (rule, value, cb) => {
              var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
              return str.test(value);
            },
            message: '最高支持8位整数和2位小数的正数或零',
          }
        ],
        noteType1: [
          { required: true, message: '请选择Note类型',trigger: 'change' }
        ],
        payoutsum1: [
          {
            required: true,
            type: 'string',
            message: '请输入赔付金额',
          },
          {
            validator: (rule, value, cb) => {
              var str = /^(?!0(\.0+)?$)([1-9]\d{0,7}(\.\d{1,2})?|\d{1,8}(\.\d{1,2})?)$/;
              return str.test(value);
            },
            message: '最高支持8位整数和2位小数的正数',
          }
        ],
        depicted: [
          { required: true, type: 'string', message: '请输入描述'}
        ],
        dxRule: [{
            required: true,
            message: '请输入代销收入',
            type: 'string',
          },
          {
            validator: (rule, value, cb) => {
              var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
              return str.test(value);
            },
            message: '最高支持8位整数和2位小数的正数或零',
          },
        ],
        llRule: [{
            required: true,
            message: '请输入流量收入',
            type: 'string',
          },
          {
            validator: (rule, value, cb) => {
              var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
              return str.test(value);
            },
            message: '最高支持8位整数和2位小数的正数或零',
          },
        ],
        imsiRule: [{
            required: true,
            message: '请输入imsi费收入',
            type: 'string',
          },
          {
            validator: (rule, value, cb) => {
              var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
              return str.test(value);
            },
            message: '最高支持8位整数和2位小数的正数或零',
          },
        ],
        dximsiRule: [{
            required: true,
            message: '请输入代销imsi费收入',
            type: 'string',
          },
          {
            validator: (rule, value, cb) => {
              var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
              return str.test(value);
            },
            message: '最高支持8位整数和2位小数的正数或零',
          },
        ],
        llimsiRule: [{
            required: true,
            message: '请输入流量imsi费收入',
            type: 'string',
          },
          {
            validator: (rule, value, cb) => {
              var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
              return str.test(value);
            },
            message: '最高支持8位整数和2位小数的正数或零',
          },
        ],
      },
      reasons: [],
      message: '大小不能超过10MB',
      uploadUrl: '',
      addLoading: false,
      billOrgInfo: {},
      cooperationMode: '',
    }
  },
  created() {
    this.getCoprList();
  },
  methods: {
    getCoprList () {
      getCorpList({
					"type": 1,
					"status": 1,
					"checkStatus": 2
				}).then(res => {
        if (res.code == '0000') {
          this.corpLists = res.data
          res.data.forEach(item => {
            this.corpNameListCorpId.push(item.corpId)
          })
          this.goPageFirst(1)
        }
      })
    },
    // 获取账期
    handleBillingPeriodMonth(month) {
      this.billingPeriod = month;
    },
    goPageFirst: function(page) {
      this.loading = true
      var _this = this
      // let func = this.searchCondition.billType == '1' ? getChannelBill : getBlankCardBill
      getChannelBill({
        pageNum: page,
        pageSize: 10,
        corpId: this.searchCondition.corpId.length ? this.searchCondition.corpId : this.corpNameListCorpId,
        statTime: this.billingPeriod
      }).then(res => {
        if (res.code == '0000') {
          _this.loading = false
          this.searchloading = false
          this.page = page
          this.currentPage = page
          this.total = Number(res.count)
          this.data = res.data
        } else {
          this.data = []
          this.total = 0
        }
      }).catch((err) => {
        console.error(err)
      }).finally(() => {
        _this.loading = false
        this.searchloading = false
      })
    },
    searchOne: function() {
      // if (!this.searchCondition.billType) {
      //   this.$Notice.error({
      //   	title: "操作提醒",
      //   	desc: "请选择账单类型"
      //   })
      // } else {
        this.searchloading = true
        this.goPageFirst(1)
      // }
    },
    goPage(page) {
      this.goPageFirst(page)
    },
    showModal: function(row) {
      this.getReasonList()
      this.cooperationMode = row.accountingType;
      this.billOrgInfo = row;
      this.modal=true;
    },
    changeBillType: function(){
      if (this.formValidate.adjustWay == '1') {
        this.formValidate.noteType = '';
        this.formValidate.payoutsum = '';
      } else if(this.formValidate.adjustWay == '2'){
        this.formValidate.dx=''
        this.formValidate.imsi=''
        this.formValidate.ll=''
        this.formValidate.dximsi=''
        this.formValidate.llimsi=''
      }
    },
    // 选择标签
    choseTab(name) {
      this.tabid = name
    },
    removeFile() {
      this.formValidate.file = ''
    },
    handleError(res, file) {
      var v = this
      setTimeout(function() {
        v.uploading = false;
        v.$Notice.warning({
          title: '错误提示',
          desc: "上传失败！"
        });
      }, 3000);
    },
    handleBeforeUpload(file) {
      // if (!/^.+(\.csv)$/.test(file.name)) {
      //   this.$Notice.warning({
      //     title: '文件格式不正确',
      //     desc: '文件 ' + file.name + ' 格式不正确，请上传.csv格式文件。'
      //   })
      // } else {
      this.formValidate.file = file
      // }
      return false
    },
    fileUploading(event, file, fileList) {
      this.message = '文件上传中、待进度条消失后再操作'
    },
    fileSuccess(response, file, fileList) {
      this.message = '请先下载模板文件，并按格式填写后上传'
    },
    handleUpload() {
      if (!this.formValidate.file) {
        this.$Message.warning('请选择需要上传的文件')
        return
      } else {
        this.$refs.formValidate.validate(valid => {
          if (valid) {
            this.addLoading=true
            let formData = new FormData();
            this.handleFormData(formData);
            addAdjustInfo(formData).then(res => {
              if (res.code === '0000') {
                this.$Notice.success({
                  title: '操作成功',
                  desc: '添加成功'
                })
                this.addLoading=false
                this.cancelModal()
                this.currentPage=1
                this.goPageFirst(1)
              } else {
                throw res
              }
            }).catch((err) => {
              console.log(err)
            }).finally(() => {
              this.addLoading=false
              this.cancelModal()
            })
          }
        })
      }
    },
    handleFormData(formData) {
      formData.append('incomeId', this.billOrgInfo.id)
      formData.append('adjustWay', this.formValidate.adjustWay)
      formData.append('applyName', this.$store.state.user.userName)
      formData.append('corpId', this.billOrgInfo.corpId)
      formData.append('invoiceNo', this.billOrgInfo.invoiceNo)
      const { cooperationMode } = this;
      if (this.formValidate.adjustWay == '1') {
        if ("0" == cooperationMode || "1" == cooperationMode) {
            formData.append('packageAdjustment', this.formValidate.dx)
            formData.append('distributionImsiAdjustment', this.formValidate.imsi);
        } else if ("2" == cooperationMode || "3" == cooperationMode | "5" == cooperationMode) {
            formData.append('useAdjustment', this.formValidate.ll)
            formData.append('a2zImsiAdjustment', this.formValidate.imsi);
        } else if ("4" == cooperationMode || "6" == cooperationMode || "7" == cooperationMode) {
            formData.append('packageAdjustment', this.formValidate.dx)
            formData.append('useAdjustment', this.formValidate.ll);
            formData.append('distributionImsiAdjustment', this.formValidate.dximsi)
            formData.append('a2zImsiAdjustment', this.formValidate.llimsi);
        } else if ("8" == cooperationMode) {
            formData.append('packageAdjustment', this.formValidate.dx)
            formData.append('distributionImsiAdjustment', 0);
            formData.append('useAdjustment', 0)
            formData.append('a2zImsiAdjustment', 0);
        }
      }
      if (this.formValidate.adjustWay == '2') {
        formData.append('adjustType', this.formValidate.noteType)
        formData.append('indemnity', this.formValidate.payoutsum)
      }
      formData.append('oldAmount', this.billOrgInfo.realIncome)
      formData.append('svcStartTime', this.billOrgInfo.svcStartTime)
      formData.append('svcEndTime', this.billOrgInfo.svcEndTime)
      formData.append('adjustReason', this.formValidate.reason)
      formData.append('message', this.formValidate.depicted)
      formData.append('descFile', this.formValidate.file)
      formData.append('currency', this.billOrgInfo.currency)
      formData.append('billingPeriod', this.billOrgInfo.statTime)
      formData.append('accountingType', this.billOrgInfo.accountingType)
    },
    // 调整原因
    getReasonList() {
    	getReasonList().then(res => {
    		if (res.code == '0000') {
    			this.reasons = res.data
    		}
    	}).catch((err) => {
    		console.error(err)
    	}).finally(() => {
    	})
    },
    cancelModal() {
				this.modal = false
				this.addLoading=false
				this.$refs.formValidate.resetFields()
				this.formValidate.adjustWay='1'
				this.formValidate.reason=''
				this.formValidate.newbillsum=''
        this.formValidate.noteType=''
				this.formValidate.payoutsum=''
				this.formValidate.depicted=''
				this.formValidate.file=null
        this.formValidate.dx=''
        this.formValidate.imsi=''
        this.formValidate.ll=''
        this.formValidate.dximsi=''
        this.formValidate.llimsi=''
        this.billOrgInfo = {};
			},
  }
}
</script>
<style scoped>
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}
  	.modal_content {
		padding: 0 16px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 85px;
	}

	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.box {
		padding: 0 10px;
		color: #878787;
		line-height: 38px;
		background-color: #f7f7f7;
		border: 1px solid #dcdee2;
		border-bottom: none;
	}
  	.search_head {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 20px;
		flex-wrap: wrap;
	}

	.search_head_label {
		margin-top: 20px;
		font-size: 17px;
	}

	.search_head_box {
		display: flex;
		justify-content: flex-start;
		margin: 20px 30px 0px 0;
	}
  .input_modal {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		flex-wrap: nowrap;
		width: 100%;
	}
</style>
