<template>
  <div>
    <Card>
      <!-- 搜索条件 -->
        <div class="search_head_i">
          <div class="search_box">
            <span class="search_box_label">申请编号</span>
            <Input v-model.trim="searchCondition.applyId" maxlength="50" clearable placeholder="请输入申请编号" class="seachSty"/>
          </div>
          <div class="search_box">
            <span class="search_box_label">客户名称</span>
            <Select
              v-model="searchCondition.corpId"
              clearable
              filterable
              placeholder="下拉选择客户名称"
              class="seachSty"
            >
              <Option
                :value="item.corpId"
                v-for="(item, index) in corpLists"
                :key="index"
                >{{ item.corpName }}</Option
              >
            </Select>
          </div>
          <div class="search_box">
            <span class="search_box_label">业务类型</span>
            <Select filterable v-model="searchCondition.businessType" placeholder="请选择业务类型" clearable class="seachSty">
              <Option :value="item.value" v-for="(item,index) in businessType" :key="index">{{item.label}}</Option>
            </Select>
          </div>
          <div class="search_box">
            <span class="search_box_label">审批状态</span>
            <Select filterable v-model="searchCondition.authStatus" placeholder="请选择审批状态" clearable class="seachSty">
              <Option :value="item.value" v-for="(item,index) in approvStatus" :key="index">{{item.label}}</Option>
            </Select>
          </div>
          <div class="search_box">
            <span class="search_box_label">调账单号</span>
            <Input v-model.trim="searchCondition.adjustId" maxlength="50" clearable placeholder="请输入调账单号" class="seachSty"/>
          </div>
          <div class="search_box">
            <span class="search_box_label">原账单发票号</span>
            <Input v-model.trim="searchCondition.invoiceNo" maxlength="50" clearable placeholder="请输入原账单发票号" class="seachSty"/>
          </div>
          <div class="search_box">
            <span class="search_box_label">账期</span>
            <DatePicker format="yyyyMM" v-model="searchCondition.billingPeriod" type="month" placement="bottom-start" placeholder="请选择账期"
				     @on-change="handleBillingPeriodMonth" :editable="true" class="seachSty"></DatePicker>  
          </div>
          <div class="search_box">
            <span class="search_box_label">审批环节</span>
            <Select filterable v-model="searchCondition.authProcess" placeholder="请选择审批环节" clearable class="seachSty">
              <Option :value="item.value" v-for="(item,index) in approvProcess" :key="index">{{item.label}}</Option>
            </Select>
          </div>
          <div class="search_box">
            <span class="search_box_label">调整方式</span>
            <Select filterable v-model="searchCondition.adjustWay" placeholder="请选择调整方式" clearable class="seachSty">
              <Option :value="item.value" v-for="(item,index) in adjustWay" :key="index">{{item.label}}</Option>
            </Select>
          </div>
          <div class="search_box">
            <span class="search_box_label">调账类型</span>
            <Select filterable v-model="searchCondition.adjustType" placeholder="请选择调账类型" clearable class="seachSty">
              <Option :value="item.value" v-for="(item,index) in adjustTypeList" :key="index">{{item.label}}</Option>
            </Select>
          </div>
          <div class="search_box">
            <span class="search_box_label">申请人</span>
            <Input v-model.trim="searchCondition.applyName" maxlength="50" clearable placeholder="请输入申请人" class="seachSty"/>
          </div>
          <div class="search_box">
            <span class="search_box_label">同步RAP状态</span>
            <Select filterable v-model="searchCondition.rapStatus" placeholder="请选择同步RAP状态" clearable class="seachSty">
              <Option :value="item.value" v-for="(item,index) in rapStatus" :key="index">{{item.label}}</Option>
            </Select>
          </div>
          <div class="search_box">
            <span class="search_box_label">申请日期</span>
            <DatePicker format="yyyy-MM-dd" v-model="searchCondition.applyDate" type="date" placement="bottom-start" placeholder="请选择申请日期"
               @on-change="handleApplyDate" :editable="true" class="seachSty"></DatePicker>  
          </div>
          <div class="search_box">
            <span class="search_box_label">结算月份</span>
            <DatePicker format="yyyy-MM" v-model="searchCondition.settlementMonth" type="month" placement="bottom-start" placeholder="请选择结算月份"
               @on-change="handleSettlementMonth" :editable="true" class="seachSty"></DatePicker>  
          </div>
        </div>
        <div class="search_head_i">
          <div style="width: 110px; display: flex;justify-content: center; margin-bottom: 20px;">
            <Button type="primary" ghost icon="md-search" v-has="'search'" :loading="searchloading" @click="searchOne()">搜索</Button>
          </div>
          <div style="width: 110px; display: flex;justify-content: center; margin-bottom: 20px;">
            <Button type="info" ghost icon="md-add" v-has="'add'" @click="addItem">新增调账</Button>
          </div>
          <div style="display: flex;justify-content: center; margin-bottom: 20px;margin-left:20px;margin-right:25px">
			      <Button type="warning" ghost v-has="'bill_list_export'" icon="ios-download" @click="exportSum()" >导出列表</Button>
          </div>
          <div style="display: flex;justify-content: center; margin-bottom: 20px;">
             <Button type="info" ghost v-has="'batch_invoice_export'" icon="ios-download" @click="batchExportInvoice" :loading="batchInvoiceLoading">批量下载Invoice</Button>
          </div>
        </div>
        <!-- 表格 -->
        <Table ref="selection" :columns="columns" :data="data" style="width:100%; margin-top: 40px;" :loading="loading"
          @on-selection-change="handleRowChangeInvoice" @on-select-cancel="cancelInvoice"
				  @on-select-all-cancel="cancelInvoiceAll">
            <template slot-scope="{ row, index }" slot="download">
              <Button :disabled="row.authStatus != '审核完成'" v-has="'invoice_export'" type="success" ghost size="small" @click="exportInvoice(row)">下载Invoice</Button>
            </template>
        </Table>
        <!-- 分页 -->
        <div style="margin-top:15px">
          <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
        </div>
        <!-- 导出提示 -->
        <Modal v-model="exportModalr" :mask-closable="true" @on-cancel="exportcancelModal">
          <div style="align-items: center;justify-content:center;display: flex; flex-wrap: wrap;">
            <Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
              <h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
              <FormItem label="你本次导出任务ID为:">
                <span>{{taskId}}</span>
              </FormItem>
              <FormItem label="你本次导出的文件名为:">
                <span class="task-name">{{taskName}}</span>
              </FormItem>
              <span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
            </Form>
          </div>

          <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
            <Button @click="exportcancelModal">取消</Button>
            <Button type="primary" @click="Gotor">立即前往</Button>
          </div>
        </Modal>
        <!-- 导出提示 -->
        <Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
          <div style="align-items: center;justify-content:center;display: flex;">
            <Form label-position="left" :label-width="150" style="width:500px; align-items: center;justify-content:center;margin-bottom: 30px;">
              <h1 style="text-align: center;margin-bottom: 20px;">导出提示</h1>
              <FormItem label="你本次导出任务ID为:">
                <ul style="margin-bottom: 15px;">
                  <li id="space">
                    {{taskId}}
                  </li>
                </ul>
                <div v-if="remind">
                  <span>……</span>
                </div>
              </FormItem>
              <FormItem label="你本次导出的文件名为:">
                <ul style="margin-bottom: 15px;">
                  <li id="space"  class="task-name">
                    {{taskName}}
                  </li>
                </ul>
                <div  v-if="remind">
                  <span>……</span>
                </div>
              </FormItem>
              <span style="text-align: left;">请前往<span style="font-weight: bold;">下载管理-下载列表</span>查看及下载。</span>
            </Form>

          </div>
          <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
            <Button @click="cancelModal">取消</Button>
            <Button type="primary" @click="Goto">立即前往</Button>
          </div>
        </Modal>
      </Card>
  </div>

</template>

<script>
  import {
    getBillInfoByProps,
    exportAdjustInfo,
    batchExportInvoice,
    singleExportInvoice,
	} from '@/api/finance/other.js'
  import { getCorpList } from '@/api/product/package/batch';
export default {
  data() {
    return {
      total: 0,
      currentPage: 1,
      page: 0,
      corpLists: [],
      billingPeriod: '',
      applyDate: '',
      settlementMonth: '',
      businessType: [
        {
          value: 1,
          label: "A-Z Sales",
        },
        {
          value: 2,
          label: "Channel Sales",
        },
        // {
        //   value: 3,
        //   label: "SIM Card Fees",
        // },
        {
          value: 4,
          label: "Combine",
        },
      ],
      approvStatus: [
        {
          value: 1,
          label: "草稿",
        },
        {
          value: 2,
          label: "审核中",
        },
        {
          value: 3,
          label: "审核完成",
        },
        {
          value: 4,
          label: "审核拒绝",
        }
      ],
      approvProcess: [
        {
          value: 1,
          label: "调账申请提交",
        },
        {
          value: 2,
          label: "业务经理审核",
        },
        {
          value: 3,
          label: "财务审核",
        },
        {
          value: 4,
          label: "DI确认",
        }
      ],
      adjustWay: [
        {
          value: 1,
          label: "重新生成",
        },
        {
          value: 2,
          label: "赔付",
        },
      ],
      adjustTypeList: [
        {
          value: 1,
          label: "Credit Note",
        },
        {
          value: 2,
          label: "Debit Note",
        }
      ],
      rapStatus: [
        {
          value: 1,
          label: "未同步",
        },
        {
          value: 2,
          label: "待同步",
        },
        {
          value: 3,
          label: "已同步",
        }
      ],
      searchCondition: {
        applyId: '',
        corpId: '',
        businessType: '',
        authStatus: '',
        adjustId: '',
        invoiceNo: '',
        billingPeriod: '',
        authProcess: '',
        adjustWay: '',
        adjustType: '',
        applyName: '',
        rapStatus: '',
        applyDate: '',
        settlementMonth: ''
      },
      loading: false,
      data: [], //表格列表
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
          fixed: 'left',
        },
        {
					title: "申请编号",
					key: 'applyId',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "客户名称",
					key: 'corpName',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "审批环节",
					key: 'authProcess',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "审批状态",
					key: 'authStatus',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "申请人",
					key: 'applyName',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "申请日期",
					key: 'applyDateStr',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "EBS CODE",
					key: 'ebscode',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "业务类型",
					key: 'businessType',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "调账原因",
					key: 'adjustReason',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "调账描述",
					key: 'message',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "原账单发票号",
					key: 'invoiceNo',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "调账单号",
					key: 'adjustId',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "合同主体",
					key: 'contract',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "调整方式",
					key: 'adjustWay',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "调账类型",
					key: 'adjustType',
					minWidth: 150,
					align: 'center',
					tooltip: true,
				},
        {
					title: "账单币种",
					key: 'currency',
					minWidth: 150,
					align: 'center',
          render: (h, params) => {
            const row = params.row;
            const text = row.currency == '156' ? "CNY" : row.currency == '840' ? "USD" : row.currency == '344' ? "HKD" :
              '';
            return h('label', text);
          }
				},
        {
					title: "调账金额",
					key: 'totalAdjustment',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
					title: "服务期开始时间",
					key: 'svcStartTime',
					minWidth: 160,
					align: 'center',
					tooltip: true, // 开启 tooltip
        },
        {
					title: "服务期结束时间",
					key: 'svcEndTime',
					minWidth: 160,
					align: 'center',
          tooltip: true, // 开启 tooltip
        },
        {
					title: "账期",
					key: 'billingPeriod',
					minWidth: 150,
					align: 'center',
					tooltip: true,
				},
        {
					title: "同步RAP状态",
					key: 'rapStatus',
					minWidth: 150,
					align: 'center',
					tooltip: true
				},
        {
        	title: "文件下载",
        	slot: 'download',
        	width: 150,
        	align: 'center',
          fixed: 'right',
        },
      ],
      batchInvoiceLoading: false,
      searchloading: false, //查询加载
      modal1: false,
      selection: [], //多选
      selectionList: [], //翻页勾选List
      taskName: '',
      taskId: '',
      remind: false,
      exportModalr: false,
      exportModal: false,
    }
  },
  created() {
    this.getCorpList();
    this.goPageFirst(1);
  },
  methods: {
    getCorpList () {
      getCorpList({
        "type": 1,
        "status": 1,
        "checkStatus": 2
      }).then(res => {
        if (res.code == '0000') {
          this.corpLists = res.data
        }
      })
    },
    goPageFirst: function(page) {
      this.loading = true
      const { searchCondition } = this;
      var _this = this
      getBillInfoByProps({
        num: page,
        size: 10,
        applyId: searchCondition.applyId,
        corpId: searchCondition.corpId,
        businessType: searchCondition.businessType,
        authStatus: searchCondition.authStatus,
        adjustId: searchCondition.adjustId,
        invoiceNo: searchCondition.invoiceNo,
        billingPeriod: this.billingPeriod,
        authProcess: searchCondition.authProcess,
        adjustWay: searchCondition.adjustWay,
        adjustType: searchCondition.adjustType,
        applyName: searchCondition.applyName,
        rapStatus: searchCondition.rapStatus,
        applyDate: this.applyDate,
        settlementMonth: this.settlementMonth ? this.settlementMonth + '-01' : '',
      }).then(res => {
        if (res.code == '0000') {
          _this.loading = false
          this.searchloading = false
          this.page = page
          this.currentPage = page
          this.total = Number(res.count)
          this.data = res.data
          // 如果没有invoicePath 就不让勾选进行批量下载invoice
          this.data.forEach(i => {
            if (!i.id) {
              this.$set(i, '_disabled', true)
            }
          })
        }
      }).catch((err) => {
        console.error(err)
      }).finally(() => {
        _this.loading = false
        this.searchloading = false
      })
    },
    searchOne: function() {
      const { searchCondition } = this;
      this.searchloading = true
      this.goPageFirst(1)
    },
    goPage(page) {
      this.goPageFirst(page)
    },
    // 获取账期
    handleBillingPeriodMonth(month) {
      this.billingPeriod = month;
    },
    handleApplyDate(date) {
      this.applyDate = date;
    },
    handleSettlementMonth(month) {
      this.settlementMonth = month;
    },
    // 新建
    addItem: function() {
      this.$router.push({
        path: '/addBillingAdjust',
        // path: '/aprvDetails',
        // query: {
        //   searchObj: encodeURIComponent(JSON.stringify(this.searchObj)),
        // }
      })
    },
    handleRowChangeInvoice(selection) {
      debugger;
      this.selection = selection;
      selection.map((value, index) => {
        let flag = true;
        this.selectionList.map((item, index) => {
          if (value.id === item.id) {
            flag = false
          }
        });
        //判断重复
        if (flag) {
          this.selectionList.push(value);
        }
      });
    },
    cancelInvoice(selection, row) {
      this.selectionList.forEach((value, index) => {
        if (value.id === row.id) {
          this.selectionList.splice(index, 1);
        }
      })
    },
    cancelInvoiceAll(selection, row) {
      this.selectionList = []
    },
    exportSum() {
      const { searchCondition } = this;
      //下载总汇总文件
      exportAdjustInfo({
        num: this.page,
        size: 10,
        applyId: searchCondition.applyId,
        corpId: searchCondition.corpId,
        businessType: searchCondition.businessType,
        authStatus: searchCondition.authStatus,
        adjustId: searchCondition.adjustId,
        invoiceNo: searchCondition.invoiceNo,
        billingPeriod: this.billingPeriod,
        authProcess: searchCondition.authProcess,
        adjustWay: searchCondition.adjustWay,
        adjustType: searchCondition.adjustType,
        applyName: searchCondition.applyName,
        rapStatus: searchCondition.rapStatus,
        userId: this.$store.state.user.userId,
        applyDate: this.applyDate,
        settlementMonth: this.settlementMonth ? this.settlementMonth + '-01' : '',
      }).then(res => {
        if (res && res.code == '0000') {
          this.exportModal = true;
          if (res.data) {
            this.taskId = res.data.taskId
						this.taskName = res.data.taskName
          }
        } else {
          throw res
        }
      }).catch((err) => {
        console.log(err)
      })

    },
    exportInvoice(row) {
      singleExportInvoice({
        "id": row.id,
        "type": '2',
      }).then(res => {
        const content =  res.data
        let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
        if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
          const link = document.createElement('a') // 创建a标签
          let url = URL.createObjectURL(content)
          link.download = fileName
          link.href = url
          link.click() // 执行下载
          URL.revokeObjectURL(url) // 释放url
        } else { // 其他浏览器
          navigator.msSaveBlob(content, fileName)
        }
      }).catch((err) => {
      }).finally(() => {
      })
    },
    // 批量导出Invoice
    batchExportInvoice() {
      var len = this.selection.length;
      if (len < 1) {
        this.$Message.warning("请至少选择一条记录");
        return;
      }
      var ids = []
      this.selectionList.forEach((value, index) => {
        ids.push(value.id)
      })
      this.batchInvoiceLoading = true
      batchExportInvoice({
          "ids": ids,
          "userId": this.$store.state.user.userId,
        }).then((res) => {
          if (res.code === "0000") {
            this.exportModalr = true
            this.taskId = res.data.taskId
            this.taskName = res.data.taskName
            this.selection = [];
            this.selectionList = []
            this.getTableData(1);
          } else {
            throw res
          }
        })
        .catch((err) => {
        }).finally(() => {
          this.batchInvoiceLoading = false
        })
    },
    Gotor(){
      this.$router.push({
        path: '/taskList',
        query: {
          taskId: encodeURIComponent(this.taskId),
          fileName: encodeURIComponent(this.taskName),
        }
      })
      this.exportcancelModal()
      this.exportModalr = false
    },
    Goto() {
      this.$router.push({
        path: '/taskList',
        query: {
          taskId: encodeURIComponent(this.taskId),
          fileName: encodeURIComponent(this.taskName),
        }
      })
      this.exportModal = false
    },
    cancelModal() {
      this.exportModal = false
      this.remind = false
    },
    exportcancelModal(){
      this.exportModalr = false
    },
  }
}
</script>
<style scoped>
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}
  	.modal_content {
		padding: 0 16px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 130px;
	}

	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

  .seachSty {
    width: 200px;
  }

	.box {
		padding: 0 10px;
		color: #878787;
		line-height: 38px;
		background-color: #f7f7f7;
		border: 1px solid #dcdee2;
		border-bottom: none;
	}
  #space {
		/* height: 30px;
		line-height: 30px; */
		font-size: 12px;
		white-space: pre-line;
		list-style: none;
	}
  .task-name {
    display: inline-block; /* 或者 block，取决于你的布局需求 */
    width: 300px; /* 根据需要设置合适的宽度 */
    /* white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; */
    word-break: break-all;
    padding: 5px; /* 内边距 */
    margin-bottom: 10px; /* 外边距 */
  }
</style>
