<template>
  <!-- 渠道商收入账单 -->
  <div>
	<!-- <Button v-has="'search'"  style="margin-bottom: 10px;" type="primary" icon="md-search" size="large" @click="search('form')" >搜索</Button>&nbsp;&nbsp; -->
    <!-- 表格 -->
    <h3 >渠道商收入</h3>
	<!-- 导出明细按钮 -->
	<Button  type="success" v-has="'corp_detail_export'"  ghost  icon="ios-download" @click="exportDetail(null,'month')"  style="margin-right: 10px;margin-top: 10px;">Sales Revenue Report</Button>
	<!-- 导出汇总按钮 -->
	<Button  type="warning"  v-has="'corp_sum_export'" ghost  icon="ios-download" @click="exportSum(null,'month')"  style="margin-right: 10px;margin-top: 10px;">Settlement</Button>
    <Table no-data-text highlight-row border :columns="columns" :data="data" style="width: 100%; margin-top: 20px;" :loading="loading" >
      <template slot-scope="{ row, index }" slot="download">
        <Button type="info" ghost size="small"  style="margin: 10px 0px 10px -35px;" v-has="'corp_detail_export'" @click="exportDetail(row,'month')">Sale Revenue</Button>
        <Button type="success" ghost size="small" style="margin-left: 10px;" v-has="'invoice_export'" @click="exportInvoice(row)">Invoice</Button>
        <Button type="warning" ghost size="small"  style="margin-bottom: 10px;" v-has="'corp_sum_export'" @click="exportSum(row,'month')">CMLink Global Data SIM Sales Revenue Report</Button>
      </template>
      <template slot-scope="{ row, index }" slot="action">
        <Button v-if="row.isUpdate==='1'" type="primary" size="small" style="margin-right: 5px" v-has="'online_sum_export'" @click="update(row)">点击修改</Button>
        <Button v-else type="primary" disabled size="small" style="margin-right: 5px" v-has="'online_sum_export'" @click="update(row)">点击修改</Button>
		<Button type="success" ghost size="small" style="width: 80px;" v-has="'get_invoice'" @click="showInvoiceView(row,'detail')">生成Invoice</Button>
      </template>
    </Table>
    <Page :total="total" :page-size="pageSize" :current.sync="page" show-sizer show-total show-elevator @on-change="loadByPage" @on-page-size-change="loadByPageSize"
      style="margin: 15px 0;" />
	<!-- 修改界面 -->
	<Modal title="编辑" v-model="updateModal" :mask-closable="true" @on-cancel="cancel1">
		<div style="align-items: center;justify-content:center;display: flex;">
		    <Form  ref="corpList"  :model="corpList" label-position="left" :rules="rule" :label-width="150" style=" align-items: center;justify-content:center;" >
				<FormItem label="客户名称:" style="font-size: large;">
				  <span style="font-weight: bold;">{{corpList.corpName}}</span>
				</FormItem>
				<FormItem label="调账金额:" style="font-size: large;" prop="accountAdjustment">
				  <Input v-model='corpList.accountAdjustment' placeholder="请输入调账金额" :clearable="true"  style="width: 190px;margin-right: 10px;" >
					<span slot="append">元</span>
				  </Input>
				</FormItem>
				<FormItem label="服务开始时间:" style="font-size: large;" prop="svcStartTime">
				  <DatePicker format="yyyyMMdd" v-model="corpList.svcStartTime" @on-change="startTimeDateChange" type="date"
				    placeholder="选择时间" ></DatePicker>
				</FormItem>
				<FormItem label="服务结束时间:" style="font-size: large;" prop="svcEndTime">
				  <DatePicker format="yyyyMMdd" v-model="corpList.svcEndTime" @on-change="endTimeDateChange" type="date"
				    placeholder="选择时间" ></DatePicker>
				</FormItem>
		    </Form>
		</div>
		<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
		  <Button @click="cancel1">返回</Button>
		  <Button type="primary" @click="confirm">确定</Button>
		</div>
	</Modal>
	<!-- 导出提示 -->
	<Modal  v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
	  <div style="align-items: center;justify-content:center;display: flex;">
		  <Form  label-position="left"  :label-width="150" style=" align-items: center;justify-content:center;" >
			  <h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
			  <FormItem label="你本次导出任务ID为:" style="font-size: large;">
				<span style="font-weight: bold;">{{taskId}}</span>
			  </FormItem>
			  <FormItem label="你本次导出的文件名为:" style="font-size: large;">
				<span style="font-weight: bold;">{{taskName}}</span>
			  </FormItem>
			  <span style="text-align: left;margin-bottom: 10px;">请前往<span style="font-weight: bold;">下载管理-下载列表</span>查看及下载。</span>
			</Form>
	  </div>
	  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
	    <Button @click="cancelModal">取消</Button>
	    <Button type="primary" @click="Goto">立即前往</Button>
	  </div>
	</Modal>
	<!-- 生成发票 -->
	<Modal v-model="invoice_model" title="生成发票预览" @on-ok="createInvoice" @on-cancel="cancelInvoice" width="800px" :styles="{top: '10px'}">
	  <Card width="750px">
	    <invoiceTemplate :AccountNo="invoiceInfo.AccountNo" :address="invoiceInfo.address" :AmountDue="invoiceInfo.AmountDue"
	    :InvoiceNo="invoiceInfo.InvoiceNo" :InvoiceDate="invoiceInfo.InvoiceDate" :FileTitle="invoiceInfo.FileTitle"
	     :InvoiceDesc="invoiceInfo.InvoiceDesc" :AmountTax="invoiceInfo.AmountTax"
		 :Tax="invoiceInfo.Tax" :TotalAmount="invoiceInfo.TotalAmount"
		 :columns="invoiceColumns" :data="invoiceInfo.data" @InvoiceDesc='getdesc'/>
	  </Card>
	  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
	    <Button @click="cancelInvoice">取消</Button>
	    <Button type="primary" @click="createInvoice">生成Invoice</Button>
	  </div>
	</Modal>
  </div>
</template>

<script>
import {
  getCorpIncomeList,
  createInvoiceNo,
  createInvoice,
  updateChannel,
  OpexportTotalTask,
  OpexportDetailTask
} from "@/api/finance/corp";
// import {
// 	  exportInvoice,
// 	} from "@/api/finance/other";
import invoiceTemplate from '@/components/invoice/invoiceTemp'
const math = require('mathjs')
export default {
  components: {
    invoiceTemplate
  },
  props: {
    form: {
      incomeType: "",
      beginMonth: "",
      endMonth: "",
      corp: '',
	  data:'',
	  count:''
    },
    searchBeginTime:'',
    searchEndTime:'',
  },
  data() {
    return {
      total: 0,
      pageSize: 10,
      page: 1,
      searchObj: {},
	  corpList:{},
	  exportModal:false,
	  updateModal:false,
	  startTime:'',
	  endTime:'',
	  taskId:'',
	  taskName:'',
      columns: [
        {
          title: "渠道商名称",
          key: "corpName",
          align: "center",
          minWidth: 130,
          tooltip: true,
          tooltipMaxWidth: 2000,
          fixed: 'left',
        },
        {
          title: "客户EBS编码",
          key: "ebscode",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
        },
        {
          title: "Invoice no.",
          key: "invoiceNo",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
        },
        {
          title: "币种",
          key: "currency",
          align: "center",
          minWidth: 100,
          render: (h, params) => {
          	const row = params.row;
          	const text = row.currency == '156' ? "CNY" :row.currency == '840' ? "USD" :row.currency == '344' ? "HKD": '';
          	return h('label', text);
          }
        },
        {
          title: "直接收入总额",
          key: "directIncome",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
		  render: (h, params) => {
		  	const row = params.row;
		  	const text = parseFloat(math.divide(math.bignumber(row.directIncome), 100).toFixed(2)).toString()
		  	return h('label', text);
		  }
		  
        },
        {
          title: "调账金额",
          key: "accountAdjustment",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
		  render: (h, params) => {
		  	const row = params.row;
		  	const text = parseFloat(math.divide(math.bignumber(row.accountAdjustment), 100).toFixed(2)).toString()
		  	return h('label', text);
		  }
        },
        {
          title: "间接收入总额",
          key: "indirectIncome",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
		  render: (h, params) => {
		  	const row = params.row;
		  	const text = parseFloat(math.divide(math.bignumber(row.indirectIncome), 100).toFixed(2)).toString()
		  	return h('label', text);
		  }
        },
        {
          title: "总销售额",
          key: "saleIncome",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
		  render: (h, params) => {
		  	const row = params.row;
		  	const text = parseFloat(math.divide(math.bignumber(row.saleIncome), 100).toFixed(2)).toString()
		  	return h('label', text);
		  }
        },
        {
          title: "直接收入酬金",
          key: "dirRemuneration",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
		  render: (h, params) => {
		  	const row = params.row;
		  	const text = parseFloat(math.divide(math.bignumber(row.dirRemuneration), 100).toFixed(2)).toString()
		  	return h('label', text);
		  }
        },
        {
          title: "间接收入酬金",
          key: "indRemuneration",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
		  render: (h, params) => {
		  	const row = params.row;
		  	const text = parseFloat(math.divide(math.bignumber(row.indRemuneration), 100).toFixed(4)).toString()
		  	return h('label', text);
		  }
        },
        {
          title: "酬金总额",
          key: "totleRemuneration",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
		  render: (h, params) => {
		  	const row = params.row;
		  	const text = parseFloat(math.divide(math.bignumber(row.totleRemuneration), 100).toFixed(2)).toString()
		  	return h('label', text);
		  }
        },
        {
          title: "实际收入金额",
          key: "realIncome",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
		  render: (h, params) => {
		  	const row = params.row;
		  	const text = parseFloat(math.divide(math.bignumber(row.realIncome), 100).toFixed(2)).toString()
		  	return h('label', text);
		  }
        },
        {
          title: "服务开始时间",
          key: "svcStartTime",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
        },
        {
          title: "服务结束时间",
          key: "svcEndTime",
          align: "center",
          minWidth: 150,
          tooltip: true,
          tooltipMaxWidth: 2000,
        },
        {
          title: '文件下载',
          slot: 'download',
          minWidth: 300,
          align: 'center',
          fixed: 'right',
        },
        {
          title: '编辑',
          slot: 'action',
          minWidth: 200,
          align: 'center',
          fixed: 'right',
        },
      ],
	  desc:'',
      data: [],
      loading: false,
      addLoading: false,
      modal1: false,
      rule: {
        accountAdjustment: [
          { required: true, message: '请输入调账金额', trigger: 'blur' },
        ],
        svcStartTime: [{ type: 'date',required: true, message: '请选择服务开始时间', trigger: 'change'}],
        svcEndTime: [{ type: 'date',required: true, message: '请选择服务结束时间', trigger: 'change'}],
      },
      updateLoading: false,
      /**
       * ---------------生成发票相关----------------
       */
      id: null,   //选择行自增主键
      invoice_model: false,  //预览模态框
      invoiceInfo:{
        AccountNo: '北京博新創億科技股份有限公司',
        address: '北京市海淀区首都體育館南路6號3幢557室',
        AmountDue:'CNY 1,360.00',
        InvoiceNo:'IN-************-GDS',
        InvoiceDate:'30-Mar-2021',
        FileTitle:'INVOICE',
        InvoiceDesc:null,
        data: [{
          description:'GDS-Sales Settlement-Mar2021',
          billingPeriod:'25-Feb-2021 to 24-Mar-2021',
          qty:'1',
          unitPrice:'1,360.00',
          amount:'1,360.00',
        },
        {
          description:'Amount before Tax',
          billingPeriod:null,
          qty:null,
          unitPrice:'CNY',
          amount:'1,360.00',
        },
        {
          description:'TAX',
          billingPeriod:null,
          qty:null,
          unitPrice:'CNY',
          amount:'1,360.00',
        },
        {
          description:'Total Amount Due',
          billingPeriod:null,
          qty:null,
          unitPrice:'CNY',
          amount:'1,360.00',
        }]
      },
      invoiceColumns:[{
            title: 'Description',
            align: 'center',
            width: 220,
            key: 'description'
          },
          {
            title: 'Billing Period',
            align: 'center',
            width: 220,
            key: 'billingPeriod'
          },
          {
            title: 'Qty',
            align: 'center',
            width: 60,
            key: 'qty'
          },
          {
            title: 'Unit Price',
            align: 'center',
            width: 115,
            key: 'unitPrice'
          },
          {
            title: 'amount',
            align: 'center',
            width: 116,
            key: 'amount'
          }
        ]
    };
  },
  created(){

  },
  mounted() {
    this.searchObj = this.form
    this.getTableData()
	// this.data= this.form.data
	// this.total= this.form.count
  },
  methods: {
    getTableData(){
      getCorpIncomeList({
        beginMonth: this.searchBeginTime,
        endMonth: this.searchEndTime,
		corpId: this.searchObj.corpId,
		corpName:this.searchObj.corpName,
        pageNum: this.page,
        pageSize: this.pageSize
      }).then(res => {
        if (res.code === "0000") {
          this.data = res.data;
          this.total = res.count;
        }
      });
    },
	search(){
		this.getTableData()
	},
    loadByPage(page){
      this.page = page
      this.getTableData(page,this.pageSize)
    },
    loadByPageSize(pageSize){
      this.pageSize = pageSize
      this.getTableData(this.page,pageSize)
    },
    exportDetail(row, type){
		let corpId=null
		let corpName=null
		if(row!=null){
			corpId=row.corpId
			corpName=row.corpName
		}
      //下载总明细文件
      OpexportDetailTask({
           beginMonth: this.searchBeginTime,
           endMonth: this.searchEndTime,
           corpId: corpId,
		   corpName: corpName,
           // fileNamePart: ,
		   // type:[1]
       }).then(res => {
         if (res && res.code == '0000') {
            this.exportModal=true
            this.taskId=res.data.taskId
            this.taskName=res.data.taskName
          } else {
            throw res
          }
        }).catch((err) => {
             console.log(err)
        })
    },
    exportSum(row, type){
		let corpId=null
		let corpName=null
		if(row!=null){
			corpId=row.corpId
			corpName=row.corpName
		}
       //下载总汇总文件
       OpexportTotalTask({
       	beginMonth: this.searchBeginTime,
       	endMonth: this.searchEndTime,
       	corpId: corpId,
       	corpName: corpName,
       	// fileNamePart: ,
       	// type:[1]
       }).then(res => {
          if (res && res.code == '0000') {
             this.exportModal=true
             this.taskId=res.data.taskId
             this.taskName=res.data.taskName
           } else {
             throw res
           }
         }).catch((err) => {
              console.log(err)
         })
      
    },
    exportInvoice(row, type){
		exportInvoice({
			corpName :row.corpName,
			invoicePath :row.invoicePath,
			month:row.statTime
		}).then(res => {
		   if (res && res.code == '0000') {
		      this.exportModal=true
		      this.taskId=res.data.taskId
		      this.taskName=res.data.taskName
		    } else {
		      throw res
		    }
		  }).catch((err) => {
		       console.log(err)
		  })
    },
    update(row){ //修改
     this.updateModal=true
	 this.corpList = Object.assign({}, row);
	 this.corpList.accountAdjustment=parseFloat(math.divide(math.bignumber(row.accountAdjustment), 100).toFixed(2)).toString()
     // this.endTime=this.corpList.svcEndTime
     // this.startTime=this.corpList.svcStartTime
	},
    cancel1(){
      this.updateModal = false
      this.updateLoading = false
    },
	cancelModal() {
		this.exportModal=false
	},
	Goto(){
		this.$router.push({
		  path: '/taskList',
		  query: {
			taskId: encodeURIComponent(this.taskId),
			fileName:encodeURIComponent(this.taskName),
		  }
		})
	  this.exportModal=false
	},
	endTimeDateChange(date) {
		this.endTime = date;
	},
	startTimeDateChange(date) {
		this.startTime = date;
	},
	confirm(){
		console.log(1111)
		this.$refs["corpList"].validate((valid) => {
		  if (valid) {
		    this.$Modal.info({
				title:'只能修改一次，是否确认提交修改？',
				onOk: () => {
					updateChannel({
						accountAdjustment :math.multiply(math.bignumber(this.corpList.accountAdjustment), 100).toString(),
						id :this.corpList.id,
						svcEndTime :this.endTime===""?this.corpList.svcEndTime:this.endTime,
						svcStartTime:this.startTime===""?this.corpList.svcStartTime:this.startTime
					}).then(res => {
					  if (res.code === "0000") {
					    this.updateModal=false
						this.getTableData()
					  }
					});
				}
		    })
		  }
		})
	},
   /**
    * 生成发票
    */
   createInvoice: function() {
   	if(this.desc){
   		//TODO 生成发票参数设置
   		createInvoice({
   			   address: this.invoiceInfo.address,
   			   id: this.id,
   			   invoiceDesc: this.desc,
   			   type:2
   		 }).then(res => {
   		   if (res && res.code == '0000') {
   		     this.$Notice.success({
   		       title: '操作提示',
   		       desc: '发票生成成功'
   		     })
   			   this.invoice_model=false
   		       //刷新页面数据
   		        this.loadByPage(this.page)
   		    } else {
   		      throw res
   		    }
   		  }).catch((err) => {
   		       console.log(err)
   		  })
   	}else{
   		this.$Message.error("发票说明不能为空");
   	}
     
   },
   /**
    * 生成票号
    */
   createInvoiceNo:function(row,needNewNo){
     createInvoiceNo(row.id,row.corpId,2).then(res => {
       if (res && res.code == '0000') {
   		this.address = res.data.address
   		if(needNewNo===2){
   			this.InvoiceNo = res.data.invoiceNo
   		}else{
   			this.InvoiceNo = row.invoiceNo
   		}
   		let currency = row.currency == '156' ? "CNY" :row.currency == '840' ? "USD" :row.currency == '344' ? "HKD": '';
   		this.invoiceInfo = {
   			  AccountNo: row.corpName,
   			  address: this.address,
   			  AmountDue:currency+" "+(row.packageIncome+row.useIncome),
   			  InvoiceNo:this.InvoiceNo,
   			  InvoiceDate:res.data.invoiceDate,
   			  FileTitle:'INVOICE',
   			  InvoiceDesc:row.invoiceDesc,
   			  AmountTax:currency+" "+(row.packageIncome+row.useIncome),
   			  Tax:currency+" "+(row.taxation),
   			  TotalAmount:currency+" "+(row.packageIncome+row.useIncome),
   			  data:[{
   				description:res.data.invoiceNameDesc,  //账单名称
   				billingPeriod:res.data.billingPeriod,
   				qty:'1',
   				unitPrice:row.packageIncome+row.useIncome,
   				amount:row.packageIncome+row.useIncome,
   		     }]
   		}
   		this.invoice_model = true
        } else {
          throw res
        }
      }).catch((err) => {
           console.log(err)
      })
   },
   /**
    * 生成发票预览退出
    */
   cancelInvoice: function() {
     this.id = null
     this.invoice_model = false
     this.invoiceInfo=[]
   },
   showInvoiceView: function(row) {
     this.id = row.id  //设置选中行主键
     //判断发票是否是实际发票
     if(row.invoiceNo == null || (row.invoiceNo !=null && row.invoiceNo.substr(0, 2) != "IN")){
       //非正式发票，需要调用发票编号生成接口
       this.createInvoiceNo(row,2)
     }else{
   	this.createInvoiceNo(row,1)  
     }
     
   },
   //获取发票说明
   getdesc(data){
   	this.desc=data
   },
  },
};
</script>

<style></style>
