<template>
	<!-- 历史真实账单 -->
	 <Card>
		<Form ref="form" :label-width="60" :model="form"  inline>
			<FormItem label="客户">
			  <Input v-model='form.CorpName' placeholder="请输入客户" :clearable="true"  style="width: 190px;" >
			  </Input>
			</FormItem>
			<FormItem label="结算月份"  prop="Month">
			  <DatePicker format="yyyyMM" v-model="form.Month" type="month" placement="bottom-start" placeholder="请选择月份" @on-change="handleChange" :editable="true" ></DatePicker>
			</FormItem>
			<!-- v-has="'search'" -->
			<Button v-has="'search'" type="primary" icon="md-search"  :loading="searchloading" @click="search()" >搜索</Button>&nbsp;&nbsp;
			<Button    @click="back()" >返回</Button>&nbsp;&nbsp;
	    </Form>
		<!-- 表格 -->
		<Table  :columns="columns" :data="data" :ellipsis="true" :loading="loading" style="margin-left: 15px;">
		  <template slot-scope="{ row, index }" slot="download">
		  	<Button v-if="row.invoicePath==='' || row.invoicePath===null" disabled   v-has="'export_invoice'" type="error" ghost  style="margin-right: 5px;margin-top: 5px;"  @click="exportCommon(row,'Invoice')">Invoice</Button>
			<Button v-else   type="error" ghost  style="margin-right: 5px;margin-top: 5px;"  v-has="'export_invoice'" @click="exportCommon(row,'Invoice')">Invoice</Button>
			<Button v-if="row.taxationPath==='' || row.taxationPath===null" disabled v-has="'export_taxation'" type="success" ghost  style="margin-right: 5px;margin-top: 5px;"  @click="exportCommon(row,'taxation')">税费文件</Button>
			<Button v-else  type="success" ghost  style="margin-right: 5px;margin-top: 5px;" v-has="'export_taxation'" @click="exportCommon(row,'taxation')">税费文件</Button>
		  	<Button v-if="row.billPath==='' || row.billPath===null" disabled type="info" ghost  style="margin-right: 5px;margin-top: 5px;" v-has="'export_customer'" @click="exportCommon(row,'Customer')">客户账单</Button>
			<Button v-else type="info" ghost  style="margin-right: 5px;margin-top: 5px;" v-has="'export_customer'"  @click="exportCommon(row,'Customer')" >客户账单</Button>
		  </template>
		  <template slot-scope="{ row, index }" slot="update">
		  	<Button v-has="'get_invoice'" type="success" size="small" style="margin-right: 5px"  @click="showInvoiceView(row)">生成Invoice</Button>
		  </template>
		</Table>
		<Page :total="total" :page-size="pageSize" :current.sync="page" show-sizer show-total show-elevator @on-change="loadByPage" @on-page-size-change="loadByPageSize"
		  style="margin: 15px 0;" />
		<!-- 生成发票 -->
		<Modal v-model="invoice_model" title="生成发票预览" @on-ok="createInvoice" @on-cancel="cancelInvoice" width="800px" :styles="{top: '10px'}">
			<Card width="750px">
				<invoiceHistory ref="dataForm"  :AccountNo="invoiceInfo.AccountNo" :address="invoiceInfo.address" :AmountDue="invoiceInfo.AmountDue"
				 :InvoiceNo="invoiceInfo.InvoiceNo" :InvoiceDate="invoiceInfo.InvoiceDate" :FileTitle="invoiceInfo.FileTitle"
				 :InvoiceDesc="invoiceInfo.InvoiceDesc" :AmountTax="invoiceInfo.AmountTax" :Tax="invoiceInfo.Tax" :TotalAmount="invoiceInfo.TotalAmount"
				 :columns="invoiceColumns" :data="invoiceInfo.data" :invoiceForm.sync="invoiceForm" @InvoiceDesc='getdesc' />
			</Card>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelInvoice">取消</Button>
				<Button type="primary" :loading="Invoiceloading" @click="createInvoice">生成Invoice</Button>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal  v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
		  <div style="align-items: center;justify-content:center;display: flex;">
			  <Form  label-position="left"  :label-width="150" style=" align-items: center;justify-content:center;margin-bottom: 30px;" >
		      <h1 style="text-align: center;margin-bottom: 20px;">导出提示</h1>
		      <FormItem label="你本次导出任务ID为:" style="margin-bottom: 15px;">
		        <span style="font-weight: bold;">{{taskId}}</span>
		      </FormItem>
		      <FormItem label="你本次导出的文件名为:" style="margin-bottom: 20px;">
		        <span style="font-weight: bold;">{{taskName}}</span>
		      </FormItem >
					  <span style="text-align: left;">请前往<span style="font-weight: bold;">下载管理-下载列表</span>查看及下载。</span>
			  </Form>
		  </div>
		  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
		    <Button @click="cancelModal">取消</Button>
		    <Button type="primary" @click="Goto">立即前往</Button>
		  </div>
		</Modal>
	 </Card>
</template>

<script>
import {
	  exportOtInvoice,
	  exportTaxation,
	  queryHistoryBill,
	  exportCustomer,
	  createOtInvoice,
	  createOtInvoiceNo
	} from "@/api/finance/other";
import invoiceHistory from '@/components/invoice/invoiceHistory'
const math = require('mathjs')
export default {
  components: {
  	invoiceHistory
  },
  data() {
	const validatePositiveNum = (rule, value, callback) => {
	  var str = /^(([0-9]\d*)|0)(\.\d{0,2})?$/;
	  if (!value || str.test(value)) {
	    callback();
	  } else {
	    callback(new Error(rule.message));
	  }
	};

	const validateIntegerNum = (rule, value, callback) => {
	  var str = /^[0-9]\d*$/;
	  if (str.test(value)) {
	    callback();
	  } else {
	    callback(new Error(rule.message));
	  }
	};
	return {
		form:{

		},
		invoiceForm:{
			invoiceNameDesc:'',
			billingPeriod:''
		},
		searchloading:false,
		Invoiceloading:false,
		loading:false,
		data:[],
		month:'',
		total: 0,
		pageSize: 10,
		page: 1,
		taskName:'',
		taskId:'',
		exportModal:false,
		desc:'',
		default:"Payment Instruction \nPlease remit payment to beneficiary China Mobile International Limited by telegraph transfer\nAccount Name: China Mobile International Limited\nName of Bank: The Hongkong & Shanghai Banking Corporation Limited\nBank Address: 1 Queen's Road, Central, Hong Kong\nAccount Number: 848-021796-838\nSWIFT Code: HSBCHKHHHKH\n*Please quote our invoice number(s) with your payment instructions to the bank upon remittance.\n*Please email remittance <NAME_EMAIL> for update of your account.\nThis computer generated document requires no signature.",
		columns: [
		  {
		    title: "客户名称",
		    key: "corpName",
		    align: "center",
			minWidth: 200,
			fixed: 'left',
		  },
		  {
		    title: "结算月份",
		    key: "statTime",
		    align: "center",
			minWidth: 200,
		  },
		  {
		    title: "服务开始时间",
		    key: "svcStartTime",
		    align: "center",
		  			minWidth: 200,
		  },
		  {
		    title: "服务结束时间",
		    key: "svcEndTime",
		    align: "center",
		  			minWidth: 200,
		  },
		  {
		    title: "Invoice no.",
		    key: "invoiceNo",
		    align: "center",
			minWidth: 200,
		  },
		  {
		    title: "币种",
		    key: "currency",
		    align: "center",
			minWidth: 200,
		    render: (h, params) => {
		    	const row = params.row;
		    	const text = row.currency == '156' ? "CNY" :row.currency == '840' ? "USD" :row.currency == '344' ? "HKD": '';
		    	return h('label', text);
		    }
		  },
		  {
		    title: "套餐收入",
		    key: "packageIncome",
		    align: "center",
			minWidth: 200,
			render: (h, params) => {
				const row = params.row;
				const text = parseFloat(math.divide(math.bignumber(row.packageIncome), 100).toFixed(2)).toString()
				return h('label', text);
			}
		  },
		  {
		    title: "用量收入",
		    key: "useIncome",
		    align: "center",
			minWidth: 200,
			render: (h, params) => {
				const row = params.row;
				const text = parseFloat(math.divide(math.bignumber(row.useIncome), 100).toFixed(2)).toString()
				return h('label', text);
			}
		  },
		  {
		    title: "总收入",
		    key: "totalIncome",
		    align: "center",
			minWidth: 200,
			render: (h, params) => {
				const row = params.row;
				const text = parseFloat(math.divide(math.bignumber(row.totalIncome), 100).toFixed(2)).toString()
				return h('label', text);
			}
		  },
		  {
		    title: "税费",
		    key: "taxation",
		    align: "center",
			minWidth: 200,
			render: (h, params) => {
				const row = params.row;
				const text = parseFloat(math.divide(math.bignumber(row.taxation), 100).toFixed(2)).toString()
				return h('label', text);
			}
		  },
		  {
		    title: '文件下载',
		    slot: 'download',
		    minWidth: 350,
		    align: 'center',
			fixed: 'right',
		  },
		  {
		    title: '操作',
		    slot: 'update',
		    minWidth: 200,
		    align: 'center',
			fixed: 'right',
		  },
		],
		/**
		   * ---------------生成发票相关----------------
		   */
		  id: null,   //选择行自增主键
		  invoice_model: false,  //预览模态框
		  invoiceInfo:{
		    AccountNo: '北京博新創億科技股份有限公司',
		    address: '北京市海淀区首都體育館南路6號3幢557室',
		    AmountDue:'CNY 1,360.00',
		    InvoiceNo:'IN-************-GDS',
		    InvoiceDate:'30-Mar-2021',
		    FileTitle:'INVOICE',
		    InvoiceDesc:null,
		    data: [{
		      description:'GDS-Sales Settlement-Mar2021',
		      billingPeriod:'25-Feb-2021 to 24-Mar-2021',
		      qty:'1',
		      unitPrice:'1,360.00',
		      amount:'1,360.00',
		    },
		    {
		      description:'Amount before Tax',
		      billingPeriod:null,
		      qty:null,
		      unitPrice:'CNY',
		      amount:'1,360.00',
		    },
		    {
		      description:'TAX',
		      billingPeriod:null,
		      qty:null,
		      unitPrice:'CNY',
		      amount:'1,360.00',
		    },
		    {
		      description:'Total Amount Due',
		      billingPeriod:null,
		      qty:null,
		      unitPrice:'CNY',
		      amount:'1,360.00',
		    }]
		  },
			invoiceColumns: [{
					title: '* description',
					align: 'center',
					width: 220,
					slot: 'description',
					renderHeader: (h, params) => {
					  return h('div', [
						    h('span', { style: { color: 'red' } }, '*'),
							h('span', ' description'),
					  ])
					}
				},
				{
					title: '* Billing Period',
					align: 'center',
					width: 220,
					slot: 'billingPeriod',
					renderHeader: (h, params) => {
					  return h('div', [
						    h('span', { style: { color: 'red' } }, '*'),
							h('span', ' Billing Period'),
					  ])
					}
				},
				{
					title: 'Qty',
					align: 'center',
					width: 60,
					key: 'qty'
				},
				{
					title: 'Unit Price',
					align: 'center',
					width: 115,
					key: 'unitPrice'
				},
				{
					title: 'amount',
					align: 'center',
					width: 116,
					key: 'amount'
				}
			]
	}
  },
  mounted() {
	// 保存上一页返回数据
	localStorage.setItem("formList", decodeURIComponent(this.$route.query.formList))
    this.getTableData(1)
  },
  methods: {
	getTableData(page){
	  this.page=page
	  queryHistoryBill({
	    month: this.month,
	    corpName:this.form.CorpName,
	    pageNum: this.page,
	    pageSize: this.pageSize,
	  }).then(res => {
	    if (res.code === "0000") {
	      this.data = res.data;
	      this.total = res.count;
		  this.searchloading=false
	    }
	  }).catch(err => this.searchloading=false);
	},
	search(){
		this.searchloading=true
		this.getTableData(1)
	},
	handleChange(month){
		this.month = month;
	},
	loadByPage(page){
	  this.page = page
	  this.getTableData(page,this.pageSize)
	},
	loadByPageSize(pageSize){
	  this.pageSize = pageSize
	  this.getTableData(this.page,pageSize)
	},
	/**
	 * 文件下载导出
	 */
	exportCommon(row,name){
		if(name==='Invoice'){
			//Invoice导出
			exportOtInvoice({
				corpName :row.corpName,
				invoicePath :row.invoicePath,
				month:row.statTime,
        userId:this.$store.state.user.userId
			}).then(res => {
			   if (res && res.code == '0000') {
			      this.exportModal=true
			      this.taskId=res.data.taskId
			      this.taskName=res.data.taskName
			    } else {
			      throw res
			    }
			  }).catch((err) => {
			      console.log(err)
			  })
		}else if(name==='taxation'){
			//税费文件导出
			exportTaxation({
				corpName:row.corpName,
				taxationPath:row.taxationPath,
				month:row.statTime,
				userId:this.$store.state.user.userId
			}).then(res => {
			   if (res && res.code == '0000') {
			      this.exportModal=true
			      this.taskId=res.data.taskId
			      this.taskName=res.data.taskName
			    } else {
			      throw res
			    }
			  }).catch((err) => {
			       console.log(err)
			  })
		}else if(name==='Customer'){
			//客户账单导出
			exportCustomer({
				billPath:row.billPath,
				corpName :row.corpName ,
				month:row.statTime,
        userId: this.$store.state.user.userId
			}).then(res => {
			   if (res && res.code == '0000') {
			      this.exportModal=true
			      this.taskId=res.data.taskId
			      this.taskName=res.data.taskName
			    } else {
			      throw res
			    }
			  }).catch((err) => {
			       console.log(err)
			  })
		}
	},
	cancelModal() {
		this.exportModal=false
	},
	Goto(){
		this.$router.push({
		  path: '/taskList',
		  query: {
			taskId: encodeURIComponent(this.taskId),
			fileName:encodeURIComponent(this.taskName),
		  }
		})
	  this.exportModal=false
	},
	/**
	 * 生成发票
	 */
	createInvoice: function() {
		if (this.desc) {
			this.$refs.dataForm.$refs.invoiceForm.validate((valid) => {
				if (valid) {
					this.invoiceInfo.InvoiceDesc = this.desc
					this.Invoiceloading = true
					// //TODO 生成发票参数设置
					createOtInvoice({
						companyName:this.invoiceInfo.AccountNo,
					  address: this.invoiceInfo.address,
						id: this.id,
						invoiceDesc: this.desc,
						type:2,
						invoiceNameDesc:this.invoiceForm.invoiceNameDesc,
						billingPeriod:this.invoiceForm.billingPeriod,
					}).then(res => {
						if (res && res.code == '0000') {
							this.$Notice.success({
								title: '操作提示',
								desc: '发票生成成功'
							})
							this.invoice_model = false
							this.Invoiceloading = false
							//刷新页面数据
							this.loadByPage(this.page)
						} else {
							throw res
						}
					}).catch((err) => {
						this.Invoiceloading = false
						console.log(err)
					})
				}
			})
		} else {
			this.$Message.error("发票说明不能为空");
		}

	},
	test(str) {
	      var re = /(\d{1,3})(?=(\d{3})+(?:$|\.))/g
	      return (str+ '').replace(re, '$1,')
	},
	/**
	 * 生成票号
	 */
	createInvoiceNo:function(row,needNewNo){
	  createOtInvoiceNo(row.id,row.corpId,2).then(res => {
	    if (res && res.code == '0000') {
			this.address = res.data.address
			if(needNewNo===2){
				this.InvoiceNo = res.data.invoiceNo
			}else{
				this.InvoiceNo = row.invoiceNo
			}
			let currency = row.currency == '156' ? "CNY" :row.currency == '840' ? "USD" :row.currency == '344' ? "HKD": '';
			let  Amount= this.test(parseFloat(math.divide(math.bignumber(row.totalIncome), 100).toFixed(2)).toString())
			let  AmountTotal=this.test(parseFloat(math.divide(math.bignumber(row.packageIncome+row.useIncome), 100).toFixed(2)).toString())
			let taxation=this.test(parseFloat(math.divide(math.bignumber(row.taxation), 100).toFixed(2)).toString())
			let AmountTax=this.test(parseFloat(math.divide(math.bignumber(row.totalIncome-row.taxation), 100).toFixed(2)).toString())
			this.invoiceInfo = {
				  AccountNo: res.data.companyName,
				  address: this.address,
				  AmountDue:currency+" "+Amount,
				  InvoiceNo:this.InvoiceNo,
				  InvoiceDate:res.data.invoiceDate,
				  FileTitle:'INVOICE',
				  InvoiceDesc:row.invoiceDesc===null ?this.default:row.invoiceDesc,
				  AmountTax:currency+" "+AmountTax,
				  Tax:currency+" "+taxation,
				  TotalAmount:currency+" "+AmountTotal,
				  data:[{
					description:res.data.invoiceNameDesc.replace(/\s*\(\s*%\s*s\s*\)\s*/g, ''),  //账单名称
					billingPeriod:res.data.billingPeriod,
					qty:'1',
					unitPrice:AmountTotal,
					amount:AmountTotal
			     }]
			}
			this.invoiceForm={
				invoiceNameDesc:res.data.invoiceNameDesc.replace(/\s*\(\s*%\s*s\s*\)\s*/g, ''), //账单名称
				billingPeriod: res.data.billingPeriod,
			}
			this.invoice_model = true
	     } else {
	       throw res
	     }
	   }).catch((err) => {
	        console.log(err)
	   })
	},
	/**
	 * 生成发票预览退出
	 */
	cancelInvoice: function() {
	  this.id = null
	  this.invoice_model = false
	  this.invoiceInfo=[]
	  this.$refs.dataForm.$refs.invoiceForm.resetFields()
	},
	showInvoiceView: function(row) {
	  this.id = row.id  //设置选中行主键
	  //判断发票是否是实际发票
	  if(row.invoiceNo == null || (row.invoiceNo !=null && row.invoiceNo.substr(0, 2) != "IN")){
	    //非正式发票，需要调用发票编号生成接口
	    this.createInvoiceNo(row,2)
	  }else{
		this.createInvoiceNo(row,1)
	  }

	},
	//获取发票说明
	getdesc(data){
		this.desc=data
	},
	//返回
	back(){
		this.$router.push({
			path: '/billing',
		})
	}
  }
}
</script>

<style>
</style>
