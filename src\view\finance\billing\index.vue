<template>
	<!-- 出账管理 -->
	<Card>
		<Form ref="form" :label-width="90" :model="form" :rules="ruleInline" inline>
			<FormItem label="收入类型:" prop="incomeType">
				<Select @on-change="changeType" filterable v-model="form.incomeType" placeholder="下拉选择收入类型" clearable>
					<Option :value="item.value" v-for="(item,index) in typeList" :key="index">{{item.label}}</Option>
				</Select>
			</FormItem>
			<FormItem v-if="form.incomeType != '5' && form.incomeType!='6'" label="开始月份:" prop="beginMonth" key="beginMonth">
				<DatePicker format="yyyyMM" v-model="form.beginMonth" type="month" placement="bottom-start" placeholder="请选择开始月份"
				 @on-change="handleChangeBeginMonth" :editable="true"></DatePicker>  
			</FormItem>
			<FormItem v-if="form.incomeType != '5' && form.incomeType!='6'" label="结束月份:" prop="endMonth" key="endMonth">
				<DatePicker format="yyyyMM" v-model="form.endMonth" type="month" placement="bottom-start" placeholder="请选择结束月份"
				 @on-change="handleChangeEndMonth" :editable="true"></DatePicker>
			</FormItem>
			<FormItem v-if="form.incomeType != '5' && form.incomeType!='6' && form.incomeType != '4' && form.incomeType" :label="form.incomeType == '1' ? '选择客户:': '公司名称:'">
				<Select v-model="form.corpId" clearable :placeholder="form.incomeType == '1' ? '下拉选择客户': '下拉选择公司'" filterable style="width:200px">
					<Option :value="item.corpId" v-for="(item,index) in corpLists" :key="index">{{form.incomeType == '1' ? item.corpName : item.companyName}}</Option>
				</Select>
			</FormItem>
      <FormItem v-if="form.incomeType == '4' && form.incomeType" label="公司名称:">
      	<Select v-model="form.customer" clearable placeholder="下拉选择公司名称" filterable style="width:200px">
      		<Option v-for="item in customerList" :value="item.corpId" :key="item.corpId">{{ item.companyName }}</Option>
      	</Select>
      </FormItem>
      <FormItem v-if="form.incomeType == '5'" label="资源供应商:">
      	<Select filterable v-model="form.supplierShortenName" placeholder="请选择资源供应商" clearable>
      		<Option v-for="(item1, value1) in supplierShortenList" :value="value1" :key="value1">{{value1}}</Option>
      	</Select>
      </FormItem>
      <FormItem v-if="form.incomeType == '5'" label="供应商编码:">
      	<Input v-model='form.supplierShortenCode' placeholder="请输入供应商编码" :clearable="true"></Input>
      </FormItem>
      <FormItem v-if="form.incomeType == '5' || form.incomeType=='6'" label="结算周期:" prop="statDate" key="statDate">
      	<DatePicker format="yyyyMM" v-model="form.statDate" type="month" placement="bottom-start" placeholder="请选择结算周期"
      	 @on-change="handleChangeStatDate" :editable="true"></DatePicker>
      </FormItem>
      <FormItem v-if="form.incomeType == '5'" label="同步Rap状态:">
				<Select filterable v-model="form.syncRap" placeholder="下拉选择同步Rap状态" clearable>
					<Option value="1">未上传</Option>
					<Option value="2">待上传</Option>
					<Option value="3">已上传</Option>
					<Option value="4">上传失败</Option>
				</Select>
			</FormItem>
			<FormItem v-if="form.incomeType == '5'" label="审批状态:">
				<Select filterable v-model="form.approvalStatus" placeholder="下拉选择审批状态" clearable>
					<Option value="1">未审批</Option>
					<Option value="2">审批完成</Option>
				</Select>
			</FormItem>&nbsp;&nbsp;
      <Button v-has="'search'" style="margin-right: 10px;" type="primary" icon="md-search" :loading="searchloading" @click="search('form')">搜索</Button>
      <Button v-has="'exportWhite'" style="margin-right: 10px;" v-if="form.incomeType == '4'" type="error" icon="md-arrow-down" :loading="downloading" @click="exportWhite('form')">导出</Button>
      <Button v-has="'exportSummary'" style="margin-right: 10px;" v-if="form.incomeType == '5'" ghost type="info" icon="md-arrow-down" :loading="summaryLoading" @click="exportCost('form', '1')">汇总表导出</Button>
      <Button v-has="'exportDeatil'" style="margin-right: 10px;" v-if="form.incomeType == '5'" ghost type="success" icon="md-arrow-down" :loading="detailLoading" @click="exportCost('form', '2')">明细表导出</Button>
      <Button v-has="'approval'" style="margin-right: 10px;" v-if="form.incomeType == '5'" ghost type="primary" icon="md-checkmark" @click="financialApproval">运营审批确认</Button>
      <Button v-has="'addCost'" style="margin-right: 10px;" v-if="form.incomeType == '5'" ghost type="warning" icon="md-add" @click="addCostItem('1')">新增成本</Button>
			<!-- 批量导出Invoice下载 -->
			<Button v-has="'batchDownloadInvoice'" type="primary" style="margin-right: 10px;" icon="md-download" :loading="batchDownloadLoading" @click="batchDownloadBlankCardInvoice" v-show="isSearch === true && form.incomeType === 4">Invoice批量下载</Button>
      <Button v-has="'rechargeExport'" v-show="form.incomeType == '6'" type="error" icon="md-arrow-down" :loading="downloading" @click="exportRechargeFreeFeeConsumption('form')">导出</Button>

    </Form>
		<div v-has="'online_search'" v-if="isSearch === true && form.incomeType === 1">
			<!-- <Online :form="form"  ref="child" :searchBeginTime="searchBeginTime" :searchEndTime="searchEndTime"></Online> -->
			<h3>线上收入</h3>
			<Table no-data-text border highlight-row :columns="columns" :data="data" style="width: 100%; margin-top: 20px;"
			 :loading="loading" :span-method="handleSpan">
				<template slot-scope="{ row, index }" slot="action">
					<Button type="info" ghost size="small" style="margin-right: 5px;" v-has="'online_sum_export'" @click="exportOnline(row,'sum')">线上收入账单汇总</Button>
					<Button type="success" ghost size="small" v-has="'online_detail_export'" @click="exportOnline(row,'detail')">线上收入报表明细</Button>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="page" show-sizer show-total show-elevator @on-change="loadByPage"
			 @on-page-size-change="loadByPageSize" style="margin: 15px 0;" />
		</div>
		<div v-has="'corp_search'" v-if="isSearch === true && form.incomeType === 2">
			<!-- <Corp :form="form" ref="corp" :searchBeginTime="searchBeginTime" :searchEndTime="searchEndTime"></Corp> -->
			 <div style="display: flex;align-items: center">
					<h3 style="margin-right: 10px;">渠道商收入</h3>
					<div >
							<!-- 导出明细按钮 -->
						<Button type="info" v-has="'corps_detail_export'" ghost icon="ios-download" @click="exportDetail(null,'month')"
						style="margin-right: 10px;">Distribution Sales Revenue Report</Button>
						<!-- 导出汇总按钮 -->
						<Button type="success" v-has="'corps_sum_export'" ghost icon="ios-download" @click="exportSum(null,'month')" style="margin-right: 10px;">Settlement</Button>
						<!-- 批量下载Invoice -->
						<Button type="warning" v-has="'batch_invoice_export'" ghost icon="ios-download" @click="batchExportInvoice" style="margin-right: 10px;">批量下载Invoice</Button>
						<!-- 立即出账 -->
						<Button type="error" v-has="'account_debit'" ghost icon="ios-list-box" @click="rapidPaymentOut" style="margin-right: 10px;">立即出账</Button>

					</div>
			 </div>
		<Table ref="selection" max-height="565" no-data-text highlight-row border :columns="columnsOP" :data="data" style="width: 100%; margin-top: 15px;"
			 :loading="loading" @on-selection-change="handleRowChangeInvoice" @on-select-cancel="cancelItem"
				@on-select-all-cancel="cancelInvoiceAll">
				<template slot-scope="{ row, index }" slot="download">
					<div style="padding-top: 5px;">
						<Poptip trigger="hover" placement="bottom-end" width="330" :transfer="true">
							<Button type="primary" size="small">下载</Button>
							<div slot="content" style="display: flex; flex-wrap: wrap; gap: 8px;">
								<Button class="special-button" type="info" ghost size="small"  v-has="'corp_detail_export'" @click="exportDetail(row,'month')"
								:disabled="['2', '3', '5'].includes(row.accountingType)" title="Distribution Sale Revenue">Distribution Sale Revenue</Button>
								<Button class="special-button" v-has="'imsi_detail_export'" type="warning" ghost size="small"  @click="exportIMSIdeatil(row)" title="IMSI Detail">IMSI Detail</Button>
								<Button class="special-button" v-if="!row.invoicePath" disabled type="success" ghost size="small"  v-has="'invoice_export'"
								@click="exportInvoice(row)" title="Invoice">Invoice</Button>
								<Button class="special-button" v-else type="success" ghost size="small" v-has="'invoice_export'" @click="exportInvoice(row)" title="Invoice">Invoice</Button>
								<Button class="special-button" type="error" ghost size="small"  v-has="'a2z_detail_export'" @click="exportA2Zdeatil(row)"
								:disabled="!['2', '3', '4', '5','6' , '7'].includes(row.accountingType)" title="A~Z Detail">A~Z Detail</Button>
								<Button class="special-button" type="error" ghost size="small" v-has="'a2z_summary_export'" @click="exportA2ZSummary(row)"
								:disabled="!['2', '3', '4', '5','6' , '7'].includes(row.accountingType)" title="A~Z Summary">A~Z Summary</Button>
								<Button type="warning" ghost size="small"  v-has="'corp_sum_export'" @click="exportSum(row,'month')"
									class="special-button" title="CMLink Global Data SIM Sales Revenue Report">
									CMLink Global Data SIM Sales Revenue Report
								</Button>
							</div>
						</Poptip>
					</div>
				</template>
				<template slot-scope="{ row, index }" slot="action" >
					<div style="padding-top: 5px;">
						<Poptip trigger="hover" placement="bottom-end" width="230" :transfer="true">
							<Button type="primary" size="small">编辑</Button>
							<div slot="content" style="display: flex; flex-wrap: wrap; gap: 8px;">
								<Button class="special-button" v-if="(row.isUpdate==='1' || row.authStatus==='2'|| row.authStatus==='3' || row.authStatus==='4') && row.isAdjustment != '1'" ghost type="primary" size="small"  v-has="'update'" @click="update(row)">点击修改</Button>
								<Button class="special-button" v-else ghost type="primary" disabled size="small"  v-has="'update'" @click="update(row)">点击修改</Button>
								<Button class="special-button" v-has="'inputImsi'" :disabled="row.authStatus == '3' || row.invoicePath || row.accountingType == '1'" type="info" ghost size="small"  @click="inputImsi(row)">录入IMSI费</Button>
								<Button class="special-button" type="success" ghost size="small" v-has="'get_invoice'" @click="showInvoiceView(row,'detail')">生成Invoice</Button>
							</div>
						</Poptip>
					</div>
				</template>
				<template slot-scope="{ row, index }" slot="approveAction">
					<Button v-if="row.authStatus==='3'" v-has="'check'" type="success" ghost style="margin-top: 5px;width: 65px;"  size="small" class="special-button"  @click="channelapprove(1,row)">通过</Button>
					<Button v-if="row.authStatus==='3'" v-has="'check'" style="width: 65px;"  type="error" ghost size="small" class="special-button"   @click="channelapprove(2,row)">不通过</Button>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="page" show-sizer show-total show-elevator @on-change="loadByPage"
			 @on-page-size-change="loadByPageSize" style="margin: 15px 0;" />
		</div>
		<div v-has="'other_corp_search'" v-if="isSearch === true && form.incomeType === 3">
			<!-- <Other :form="form" ref="Other" :searchBeginTime="searchBeginTime" :searchEndTime="searchEndTime"></Other> -->
			<h3>其他客户收入</h3>
			<!-- 导出按钮 -->
			<Button v-has="'export_corps'" type="info" :loading="downloading" icon="ios-download" @click="downloadFile(0)" style="margin-right: 10px;margin-top: 10px;">导出其他客户收入汇总表(暂估)</Button>
			<Button v-has="'export_reality'" type="success" :loading="downloading" icon="ios-download" @click="downloadFile(1)"
			 style="margin-right: 10px;margin-top: 10px;">导出其他客户收入汇总表(真实)</Button>
			<Button v-has="'create_real_bill'" type="warning" :loading="downloading" @click="createReal()" style="margin-right: 10px;margin-top: 10px;">生成真实账单</Button>
			<Button v-has="'history_really_bill'" type="primary" :loading="downloading" @click="createHistory()" style="margin-right: 10px;margin-top: 10px;">历史真实账单</Button>
			<Table ref="selection" :columns="columnsOT" :data="data" :ellipsis="true" :loading="loading" @on-selection-change="handleRowChange"
			 @on-select="selectPackage" @on-select-cancel="cancelPackage" @on-select-all="selectPackage" @on-select-all-cancel="cancelPackageAll"
			 style="width: 100%; margin-top: 20px;">

				<template slot-scope="{ row, index }" slot="download">
					<Button type="primary" ghost style="margin-right: 5px;margin-top: 5px;" v-has="'other_mealsum_export'" @click="exportCommon(row,'Package')">按套餐结算汇总报表</Button>
					<Button type="success" ghost style="margin-right: 5px;margin-top: 5px;" v-has="'other_flowsum_export'" @click="exportCommon(row,'flowSettle')">按流量结算汇总报表</Button>
					<Button type="warning" ghost style="margin-right: 5px;margin-top: 5px;" v-has="'other_mealdetail_export'" @click="exportCommon(row,'PackageUsed')">按套餐使用明细报表</Button>
					<Button type="info" ghost style="margin-right: 5px;margin-top: 5px;" v-has="'other_flowdetail_export'" @click="exportCommon(row,'flowUsed')">按流量使用明细报表</Button>
				</template>
				<template slot-scope="{ row, index }" slot="update">
					<Button v-if="row.invoiceNo !=null
					&& row.invoiceNo.substr(0, 2) === 'IN'" disabled type="primary" size="small"
					 style="margin-right: 5px" v-has="'update'" @click="updateOT(row)">点击修改</Button>
					<Button v-else type="primary" size="small" style="margin-right: 5px" v-has="'update'" @click="updateOT(row)">点击修改</Button>
				</template>
				<template slot-scope="{ row, index }" slot="approveAction">
					<Button v-if="row.authStatus==='3'"  v-has="'check'" type="success" ghost  size="small" style="margin-right: 5px"  @click="otherapprove(1,row)">通过</Button>
					<div style="height: 6px;"></div>
					<Button v-if="row.authStatus==='3'"  v-has="'check'" type="error" ghost size="small"   @click="otherapprove(2,row)">不通过</Button>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="page" show-sizer show-total show-elevator @on-change="loadByPage"
			 @on-page-size-change="loadByPageSize" style="margin: 15px 0;" />
		</div>
    <!-- 充值赠费消耗汇总表报表 -->
    <div v-has="'online_search'" v-if="isSearch === true && form.incomeType === 6">
      <h3>充值赠费消耗汇总表报表</h3>
      <Table no-data-text border highlight-row :columns="RechargeFreeFeeConsumption" :data="data" style="width: 100%; margin-top: 20px;"
             :loading="loading">
      </Table>
      <Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="loadByPage"
            style="margin: 15px 0;" />
    </div>

    <!-- 白卡订单收入 -->
		<div v-has="'online_search'" v-if="isSearch === true && form.incomeType === 4">
			<h3>白卡订单收入</h3>
			<Table
				ref="whiteCardTable"
				no-data-text
				border
				highlight-row
				:columns="whiteCradColumns"
				:data="data"
				style="width: 100%; margin-top: 20px;"
				:loading="loading"
				@on-selection-change="handleSelectionChange"
				@on-select-cancel="handleSelectionCancel"
				@on-select-all="handleSelectAll">
				<template slot-scope="{ row }" slot="action">
					<Button
						type="primary"
						size="small"
						v-has="'singleDownloadInvoice'"
						ghost
						:loading="downloadingMap[row.orderId]"
						:disabled="downloadingMap[row.orderId]"
						@click="downloadInvoice(row)">
						Invoice下载
					</Button>
				</template>
			</Table>
			<div style="margin: 10px 0;">
				<span>已选择 {{ selectedBlankCardRows.length }} 条记录</span>
			</div>
			<Page
				:total="total"
				:page-size="pageSize"
				:current.sync="page"
				show-total
				show-elevator
				@on-change="loadByPage"
				style="margin: 15px 0;" />
		</div>
    <!-- 成本报表收入 -->
    <div v-has="'online_search'" v-if="isSearch === true && form.incomeType === 5">
    	<h3>成本报表收入</h3>
    	<Table no-data-text border highlight-row :columns="costPriceColumns" :data="data" style="width: 100%; margin-top: 20px;"
    	 :loading="loading" @on-selection-change="handleRowChangeInvoice" @on-select-cancel="cancelItem"
				@on-select-all-cancel="cancelInvoiceAll">
        <template slot-scope="{ row, index }" slot="action">
        	<Button v-has="'updateCost'" type="info" ghost size="small" style="margin-right: 5px" @click="updateCostItem(row, '2')" :disabled="row.approvalStatus != '1'">修改</Button>
        </template>
    	</Table>
    	<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="loadByPage"
    	 @on-page-size-change="loadByPageSize" style="margin: 15px 0;" />
    </div>
    <!-- 修改渠道商收入界面 -->
		<Modal title="编辑" v-model="updateModal" :mask-closable="true" @on-cancel="cancel1">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form ref="corpListOP" :model="corpListOP" label-position="left" :rules="rule" :label-width="150" style=" align-items: center;justify-content:center;">
					<FormItem label="客户名称:" style="font-size: large;">
						<span style="font-weight: bold;">{{corpListOP.corpName}}</span>
					</FormItem>
					<FormItem label="代销收入:" style="font-size: large;" prop="accountAdjustment" v-if="['0', '1', '4', '6', '7'].includes(cooperationMode)">
						<Input v-model='corpListOP.accountAdjustment' placeholder="请输入代销收入" :clearable="true" style="width: 190px;margin-right: 10px;">
						<span slot="append">元</span>
						</Input>
					</FormItem>
          <FormItem label="流量收入:" style="font-size: large;" prop="flowAdjustment" v-if="!(['0', '1'].includes(cooperationMode))">
          	<Input v-model='corpListOP.flowAdjustment' placeholder="请输入流量收入" :clearable="true" style="width: 190px;margin-right: 10px;">
          	<span slot="append">元</span>
          	</Input>
          </FormItem>
          <FormItem label="IMSI费收入:" style="font-size: large;" prop="imsiAdjustment">
          	<Input v-model='corpListOP.imsiAdjustment' placeholder="请输入IMSI费收入" :clearable="true" style="width: 190px;margin-right: 10px;">
          	<span slot="append">元</span>
          	</Input>
          </FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancel1">返回</Button>
				<Button type="primary" :loading="updateLoading" @click="confirm">确定</Button>
			</div>
		</Modal>
		<!-- 选择合作模式出账 -->
		<Modal title="选择合作模式出账" v-model="selectModeModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form ref="accountingList" :model="accountingList" label-position="left" :rules="ruleMode" :label-width="100" style=" align-items: center;justify-content:center;">
					<FormItem label="选择出账:" style="font-size: large;" prop="accountingType">
            <Select v-model='accountingList.accountingType' placeholder="请选择出账" style="width: 200px;" clearable>
                <Option value="1">代销</Option>
                <Option value="2">流量</Option>
                <Option value="3">合并出账</Option>
            </Select>
					</FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">返回</Button>
				<Button type="primary" :loading="accountingLoading" @click="accountingConfirm">确定</Button>
			</div>
		</Modal>
    <!-- 修改其他界面 -->
    <Modal title="编辑" v-model="updateOTModal" :mask-closable="true" @on-cancel="cancelModal">
    	<div style="align-items: center;justify-content:center;display: flex;">
    		<Form ref="corpList" :model="corpList" label-position="left" :rules="ruleOT" :label-width="150" style=" align-items: center;justify-content:center;">
    			<FormItem label="客户名称:" style="font-size: large;">
    				<span style="font-weight: bold;">{{corpList.corpName}}</span>
    			</FormItem>
    			<FormItem label="结算月份:" style="font-size: large;">
    				<span style="font-weight: bold;">{{corpList.statTime}}</span>
    			</FormItem>
    			<FormItem label="服务开始时间:" style="font-size: large;">
    				<span style="font-weight: bold;">{{corpList.svcStartTime}}</span>
    			</FormItem>
    			<FormItem label="服务结束时间:" style="font-size: large;">
    				<span style="font-weight: bold;">{{corpList.svcEndTime}}</span>
    			</FormItem>
    			<FormItem label="套餐收入:" style="font-size: large;" prop="packageIncome">
    				<Input v-model='corpList.packageIncome' placeholder="请输入套餐收入" :clearable="true" style="width: 230px;margin-right: 10px;">
    				<span slot="append">元</span>
    				</Input>
    			</FormItem>
    			<FormItem label="用量收入:" style="font-size: large;" prop="useIncome">
    				<Input v-model='corpList.useIncome' placeholder="请输入用量收入" :clearable="true" style="width: 230px;margin-right: 10px;">
    				<span slot="append">元</span>
    				</Input>
    			</FormItem>
    		</Form>
    	</div>
    	<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
    		<Button @click="cancelModal">返回</Button>
    		<Button type="primary" :loading="OTloading" @click="OTconfirm">确定</Button>
    	</div>
    </Modal>
    <!-- 生成真实账单 -->
		<Modal title="生成真实账单" v-model="CerateModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form ref="cerateList" :model="cerateList" label-position="left" :rules="ruleCerate" :label-width="150" style=" align-items: center;justify-content:center;">
					<FormItem label="客户名称:" style="font-size: large;">
						<span style="font-weight: bold;">{{cerateList.corpName}}</span>
					</FormItem>
					<FormItem label="结算月份:" style="font-size: large;">
						<span style="font-weight: bold;">{{cerateList.statTime}}</span>
					</FormItem>
					<FormItem label="服务开始时间:" style="font-size: large;">
						<span style="font-weight: bold;">{{cerateList.svcStartTime}}</span>
					</FormItem>
					<FormItem label="服务结束时间:" style="font-size: large;">
						<span style="font-weight: bold;">{{cerateList.svcEndTime}}</span>
					</FormItem>
					<FormItem label="套餐收入:" style="font-size: large;" prop="packageIncome">
						<span style="font-weight: bold;">{{cerateList.packageIncome}}</span>
					</FormItem>
					<FormItem label="用量收入:" style="font-size: large;" prop="useIncome">
						<span style="font-weight: bold;">{{cerateList.useIncome}}</span>
					</FormItem>
					<FormItem label="总收入:" style="font-size: large;" prop="useIncome">
						<span style="font-weight: bold;">{{cerateList.totalIncome}}</span>
					</FormItem>
					<FormItem label="税费:" style="font-size: large;" prop="taxation">
						<Input v-model='cerateList.taxation' placeholder="请输入税费" :clearable="true" style="width: 190px;margin-right: 10px;">
						<span slot="append">元</span>
						</Input>
					</FormItem>
					<FormItem label="上传税费文件:" style="font-size: large;">
						<Upload :action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
						 :on-progress="fileUploading">
							<Button icon="ios-cloud-upload-outline">点击上传</Button>
						</Upload>
						<ul class="ivu-upload-list" v-if="file">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />{{file.name}}</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
							</li>
						</ul>
					</FormItem>
					<FormItem label="上传客户账单:" style="font-size: large;">
						<Upload :action="uploadUrl" :on-success="CustomerfileSuccess" :on-error="CustomerhandleError" :before-upload="CustomerhandleBeforeUpload"
						 :on-progress="CustomerfileUploading">
							<Button icon="ios-cloud-upload-outline">点击上传</Button>
						</Upload>
						<ul class="ivu-upload-list" v-if="Customerfile">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />{{Customerfile.name}}</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeCustomerfile"></i>
							</li>
						</ul>
					</FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">返回</Button>
				<Button type="primary" :loading="Cerateloading" @click="Cerateconfirm">确定</Button>
			</div>
		</Modal>
		<!-- 生成发票 -->
		<Modal v-model="invoice_model" title="生成发票预览" @on-ok="createInvoice" @on-cancel="cancelInvoice" width="800px" :styles="{top: '10px'}">
			<Card width="750px">
				<invoiceTemplate ref="dataForm"  :AccountNo="invoiceInfo.AccountNo" :address="invoiceInfo.address" :AmountDue="invoiceInfo.AmountDue"
				 :InvoiceNo="invoiceInfo.InvoiceNo" :InvoiceDate="invoiceInfo.InvoiceDate" :FileTitle="invoiceInfo.FileTitle"
				 :InvoiceDesc="invoiceInfo.InvoiceDesc" :AmountTax="invoiceInfo.AmountTax" :Tax="invoiceInfo.Tax" :TotalAmount="invoiceInfo.TotalAmount"
				 :columns="invoiceColumns" :data="invoiceInfo.data" :invoiceForm.sync="invoiceForm" :currencyCode="invoiceInfo.currencyCode" @InvoiceDesc='getdesc' />
			</Card>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelInvoice">取消</Button>
				<Button type="primary" :loading="Invoiceloading" @click="createInvoice">生成Invoice</Button>
			</div>
		</Modal>
		<!-- 新增/修改成本 -->
    <Modal :title="titleName" v-model="costModal" :mask-closable="true" @on-cancel="cancelModal">
    	<div style="align-items: center;justify-content:center;display: flex;">
    		<Form ref="corpListCost" :model="corpListCost" label-position="left" :rules="ruleCost" :label-width="150" style="align-items: center;justify-content:center;">
    			<FormItem label="结算周期:" style="font-size: large;" prop="statDate" v-if="costType == '1'">
    				<DatePicker format="yyyyMM" v-model="corpListCost.statDate" type="month" placement="bottom-start" placeholder="请选择结算周期"
    				 @on-change="handleStatDate" :editable="true" style="width: 200px;"></DatePicker>
    			</FormItem>
    			<FormItem label="资源供应商:" style="font-size: large;" prop="supplierShortenName" v-if="costType == '1'">
            <Select filterable v-model="corpListCost.supplierShortenName" placeholder="请选择资源供应商" clearable
              @on-change="changeCode($event)">
            	<Option v-for="(item1, value1) in supplierShortenList" :value="value1" :key="value1">{{value1}}</Option>
            </Select>
    			</FormItem>
          <FormItem label="供应商编码:" style="font-size: large;" prop="supplierShortenCode" v-if="costType == '1'">
          	<Input v-model='corpListCost.supplierShortenCode' disabled placeholder="请输入供应商编码" :clearable="true" style="width: 200px;"></Input>
          </FormItem>
          <FormItem label="币种:" style="font-size: large;" prop="currency" v-if="costType == '1'">
          	<Select v-model="corpListCost.currency" style="width: 200px;" filterable clearable placeholder="请选择资源供应商">
          	  <Option value="CNY">人民币</Option>
          	  <Option value="HKD">港币</Option>
          	  <Option value="USD">美元</Option>
          	  <Option value="EUR">欧元</Option>
          	</Select>
          </FormItem>
          <FormItem label="成本预估:" style="font-size: large;" prop="totalAmount">
          	<Input v-model='corpListCost.totalAmount' placeholder="请输入成本预估" :clearable="true"></Input>
          </FormItem>
          <FormItem label="成本类型:" style="font-size: large;" prop="type">
            <Input v-model='corpListCost.type' placeholder="请输入成本类型" :clearable="true"></Input>
          </FormItem>
    		</Form>
    	</div>
    	<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
    		<Button @click="cancelModal">返回</Button>
    		<Button type="primary" :loading="costLoading" @click="costConfirm">确定</Button>
    	</div>
    </Modal>
    <!-- 导出提示 -->
		<Modal v-model="exportModalr" :mask-closable="true" @on-cancel="exportcancelModal">
			<div style="align-items: center;justify-content:center;display: flex; flex-wrap: wrap;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					<FormItem label="你本次导出任务ID为:">
						<span>{{taskId}}</span>
					</FormItem>
					<FormItem label="你本次导出的文件名为:">
						<span class="task-name">{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="exportcancelModal">取消</Button>
				<Button type="primary" @click="Gotor">立即前往</Button>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style="width:500px; align-items: center;justify-content:center;margin-bottom: 30px;">
					<h1 style="text-align: center;margin-bottom: 20px;">导出提示</h1>
					<FormItem label="你本次导出任务ID为:">
						<ul style="margin-bottom: 15px;">
							<li id="space" v-for="(taskId,i) in taskIds" :key="taskIds.i">
								{{taskId}}
							</li>
						</ul>
						<div v-if="remind">
							<span>……</span>
						</div>
					</FormItem>
					<FormItem label="你本次导出的文件名为:">
						<ul style="margin-bottom: 15px;">
							<li id="space" v-for="(taskName,i) in taskNames" :key="taskNames.i" class="task-name">
								{{taskName}}
							</li>
						</ul>
						<div  v-if="remind">
							<span>……</span>
						</div>
					</FormItem>
					<span style="text-align: left;">请前往<span style="font-weight: bold;">下载管理-下载列表</span>查看及下载。</span>
				</Form>

			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="Goto">立即前往</Button>
			</div>
		</Modal>
    <!-- 没有invoicePath的数据 -->
    <Modal :title="modelTitle" v-model="invoiceModal"  :mask-closable="false" width="900px" @on-cancel="cancelInvoiceFile">
      <Table ref="selection" :columns="invoiceFileColumns" :data="invoiceFileTableData" :ellipsis="true"></Table>
      <h3 style="margin: 30px 0; text-decoration: underline; text-decoration-color: red; text-decoration-style: solid;">{{ invoiceTitle }}</h3>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
        <Button @click="cancelInvoiceFile">返回</Button>
        <Button type="primary" :loading="besureSubmitLoading" @click="besureSubmit">确定</Button>
      </div>
    </Modal>
    <!-- 输入imsi费弹窗 -->
    <Modal title="录入IMSI费" v-model="imsiModal" :mask-closable="true" @on-cancel="cancel1" width="600">
    	<Form ref="imsiOP" :model="imsiOP" label-position="left" :rules="imsiRule" :label-width="150" style=" align-items: center;justify-content:center;">
    		<FormItem label="IMSI费金额:" style="font-size: large;" prop="amount" >
    			<Input v-model='imsiOP.amount' placeholder="请输入imsi费金额" clearable >
    			<span slot="append">元</span>
    			</Input>
    		</FormItem>
    	  <FormItem label="IMSI费明细:" style="font-size: large;" prop="desc">
    	    <Input v-model="imsiOP.desc" maxlength="500" clearable type="textarea" :autosize="{minRows: 3,maxRows: 10}" placeholder="请输入imsi费明细"></Input>
    	  </FormItem>
    	</Form>
    	<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
    		<Button @click="cancel1">返回</Button>
    		<Button type="primary" :loading="imsiLoading" @click="imsiConfirm">确定</Button>
    	</div>
    </Modal>
    <a ref="downloadLink" style="display: none"></a>
  </Card>
</template>

<script>
	import {
		getCorpList
	} from "@/api/product/package/batch";
	import Online from './online.vue';
	import Corp from './corp.vue';
	import Other from './other.vue';
	import mixin from '@/mixin/validate'
	import {
		getOnlineIncomeList,
		exportTotalTask,
		exportDetailTask
	} from "@/api/finance/online";
	import {
		getCorpIncomeList,
		createInvoiceNo,
		createInvoice,
		updateChannel,
		OpexportTotalTask,
		OpexportDetailTask,
		exportInvoice,
		channelAuth,
    batchExportInvoice,
    exportIMSIdeatil,
    exportA2Zdeatil,
    exportA2ZSummary,
    rapidPaymentOut,
    updateImsi
	} from "@/api/finance/corp";
	import {
		exportOtInvoice,
	} from "@/api/finance/other";
	import invoiceTemplate from '@/components/invoice/invoiceTemp'
	import {
		CustomerPage,
		queryCustomer,
		UpdateCustomer,
		exportflowSettle,
		exportflowUsed,
		exportPackage,
		exportPackageUsed,
		exportSummaryFile,
		exportTaxation,
		generateActualBill,
		otherAuth,
		batchDownloadWhiteCardInvoice,
    getRechargeFreeFeeConsumption,
    exportA2zAmountByMonth,
	} from "@/api/finance/other";
	import {
    downloadFile,
	} from "@/api/product/whiteCardOrders";
	import {
	  exportBillFile
	} from "@/api/channel/channelBillingQuery";
	import {
	  supplier,
    getSupplierShortenCode
	} from '@/api/ResourceSupplier'
  import {
    getCostReportList,
    exportCostSummary,
    exportCostDetail,
    addCostItemFunc,
    updateCostItemFunc,
    approvalConfirm
  } from "@/api/finance/cost";
  import {
    getWhiteCardList,
    getCustomerList,
    whiteCardFile,
  } from "@/api/finance/whiteCard";
	import {
    searchcorpid,
  } from '@/api/channel.js'
  const math = require('mathjs')
	export default {
		mixins: [mixin],
		components: {
			Online,
			Corp,
			Other,
			invoiceTemplate
		},
		data() {
			const validatePositiveNum = (rule, value, callback) => {
				var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
				if (!value || str.test(value)) {
					callback();
				} else {
					callback(new Error(rule.message));
				}
			};
			const validateNum = (rule, value, callback) => {
				var str1 = value
				if (value.substr(0, 1) === '-') {
					str1 = value.substr(1, value.length)
				}
				var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
				if (!str1 || str.test(str1)) {
					callback();
				} else {
					callback(new Error(rule.message));
				}
			};
			return {
				isSearch: true,
				showUpOnline: false,
				form: {
					incomeType: "",
					beginMonth: "",
					endMonth: "",
					corpId: '',
					searchBeginTime: '',
					searchEndTime: '',
					data: '',
					count: '',
          supplierShortenName: '',
          supplierShortenCode: '',
          statDate: '',
          syncRap: '',
          approvalStatus: '',
          customer: '',
				},
        invoiceForm:[{
          invoiceNameDesc:'',
          billingPeriod:'',
        },],
				data: [],
				searchObj: {},
        row: {},
				total: 0,
				pageSize: 10,
				page: 1,
				file: null, //税费文件
				Customerfile: null, //客户账单文件
				uploadUrl: '',
				downloading: false,
				exportModal: false,
				exportModalr: false,
				updateModal: false,
				updateOTModal: false,
				CerateModal: false,
        selectModeModal: false,
        costModal: false,
        invoiceModal: false,
        imsiModal: false,
				searchloading: false,
				Cerateloading: false,
				Invoiceloading: false,
				OTloading: false,
				updateLoading: false,
        accountingLoading: false,
        summaryLoading: false,
        detailLoading: false,
        costLoading: false,
        besureSubmitLoading: false,
        imsiLoading: false,
				taskName: '',
				taskId: '',
        titleName: "",
				spanData: [],
				loading: false,
				corpList: {},
				corpLists: [],
				corpListOP: {
          id: '',
          corpName: '',
          accountAdjustment: '',
          flowAdjustment: '',
          imsiAdjustment: '',
        },
				cerateList: {},
        corpListCost: {
          statDate: '',
          supplierName: '',
          currency: '',
          totalAmount: '',
          type: '',
        },
        supplierShortenList: [], //资源供应商
        customerList: [], //白卡订单客户列表
        costType: '',
				startTime: '',
				endTime: '',
				InvoiceNo: '',
				address: '',
				desc: '',
				default: "Payment Instruction \nPlease remit payment to beneficiary China Mobile International Limited by telegraph transfer\nAccount Name: China Mobile International Limited\nName of Bank: The Hongkong & Shanghai Banking Corporation Limited\nBank Address: 1 Queen's Road, Central, Hong Kong\nAccount Number: 848-021796-838\nSWIFT Code: HSBCHKHHHKH\n*Please quote our invoice number(s) with your payment instructions to the bank upon remittance.\n*Please email remittance <NAME_EMAIL> for update of your account.\nThis computer generated document requires no signature.",
				selection: [], //多选
				selectionIds: [], //多选ids
				selectionTypes: [], //多选类型
				selectionList: [], //翻页勾选List
        imsiOP: {
          id: "",
          amount: "",
          desc: "",
        },
        accountingList: {
          accountingType:　"",
        },
				remind: false,
				taskIds: [],
				taskNames: [],
        costIdList: [],
				columns: [{
						title: "订购渠道",
						key: "salesChannel",
						align: "center",
						render: (h, params) => {
							const obj = {
								102: "API",
								103: "官网（H5）",
								104: "北京移动",
								105: "批量售卖",
								106: "推广活动",
								110: "测试渠道",
								111: "合作发卡",
								112: "后付费发卡",
								113: "WEB",
								114: "流量池WEB",
							};
							return h(
								"span",
								obj[params.row.salesChannel]
							);
						},
						tooltip: true,
						minWidth: 200,
						tooltipMaxWidth: 2000,
					},
					{
						title: "结算月份",
						key: "statTime",
						align: "center",
						tooltip: true,
						minWidth: 200,
						tooltipMaxWidth: 2000,
					},
					{
						title: "套餐数量",
						key: "packageNum",
						align: "center",
						tooltip: true,
						minWidth: 200,
						tooltipMaxWidth: 2000,
					},
					{
						title: "加油包数量",
						key: "refuelNum",
						align: "center",
						tooltip: true,
						minWidth: 200,
						tooltipMaxWidth: 2000,
					},
					{
						title: "套餐收入金额",
						key: "amount",
						align: "center",
						tooltip: true,
						minWidth: 200,
						tooltipMaxWidth: 2000,
						render: (h, params) => {
							const row = params.row;
							const text = parseFloat(math.divide(math.bignumber(row.amount), 100).toFixed(2)).toString()
							return h('label', text);
						}
					},
					{
						title: "加油包收入金额",
						key: "amountRefuel",
						align: "center",
						tooltip: true,
						minWidth: 200,
						tooltipMaxWidth: 2000,
						render: (h, params) => {
							const row = params.row;
							const text = parseFloat(math.divide(math.bignumber(row.amountRefuel), 100).toFixed(2)).toString()
							return h('label', text);
						}
					},
					{
						title: "总收入金额",
						key: "amountTotal",
						align: "center",
						tooltip: true,
						minWidth: 200,
						tooltipMaxWidth: 2000,
						render: (h, params) => {
							const row = params.row;
							const text = parseFloat(math.divide(math.bignumber(row.amountTotal), 100).toFixed(2)).toString()
							return h('label', text);
						}
					},
					{
						title: "币种",
						key: "currency",
						align: "center",
						minWidth: 200,
						render: (h, params) => {
							const row = params.row;
							const text = row.currency == '156' ? "CNY" : row.currency == '840' ? "USD" : row.currency == '344' ? "HKD" :
								'';
							return h('label', text);
						}
					},
					{
						title: '文件下载',
						slot: 'action',
						minWidth: 270,
            fixed: 'right',
						align: 'center'
					},
				],
				columnsOP: [
          {
            type: 'selection',
            width: 60,
            align: 'center',
            fixed: 'left',
          },
          {
						title: "公司名称",
						key: "companyName",
            align: "center",
            width: 150,
            tooltip: true,
            tooltipMaxWidth: 2000,
            fixed: 'left',
          },
          {
            title: "客户EBS编码",
            key: "ebscode",
            align: "center",
            width: 120,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: "Invoice no.",
            key: "invoiceNo",
            align: "center",
						minWidth: 135,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: "币种",
            key: "currency",
            align: "center",
            width: 70,
            tooltip: true,
            render: (h, params) => {
              const row = params.row;
              const text = row.currency == '156' ? "CNY" : row.currency == '840' ? "USD" : row.currency == '344' ? "HKD" : '';
              return h('label', text);
            }
          },
          {
            title: "直接收入总额",
            key: "directIncome",
            align: "center",
            width: 110,
            tooltip: true,
            tooltipMaxWidth: 2000,
            render: (h, params) => {
              const row = params.row;
              const text = parseFloat(math.divide(math.bignumber(row.directIncome), 100).toFixed(2)).toString()
              return h('label', text);
            }

          },
          {
            title: "调账金额",
            key: "accountAdjustment",
            align: "center",
            width: 110,
            tooltip: true,
            tooltipMaxWidth: 2000,
            render: (h, params) => {
              const row = params.row;
              const text = parseFloat(math.divide(math.bignumber(row.accountAdjustment), 100).toFixed(2)).toString()
              return h('label', text);
            }
          },
          {
            title: "间接收入总额",
            key: "indirectIncome",
            align: "center",
            width: 110,
            tooltip: true,
            tooltipMaxWidth: 2000,
            render: (h, params) => {
              const row = params.row;
              const text = parseFloat(math.divide(math.bignumber(row.indirectIncome), 100).toFixed(2)).toString()
              return h('label', text);
            }
          },
          {
            title: "流量收入总额",
            key: "flowTotalAmount",
            align: "center",
            width: 110,
            tooltip: true,
            tooltipMaxWidth: 2000,
            render: (h, params) => {
              const row = params.row;
              const text = parseFloat(math.divide(math.bignumber(row.flowTotalAmount), 100).toFixed(2)).toString()
              return h('label', text);
            }
          },
          {
            title: "流量调账金额",
            key: "flowAdjustAmount",
            align: "center",
            width: 110,
            tooltip: true,
            tooltipMaxWidth: 2000,
            render: (h, params) => {
              const row = params.row;
              const text = parseFloat(math.divide(math.bignumber(row.flowAdjustAmount), 100).toFixed(2)).toString()
              return h('label', text);
            }
          },
          {
            title: "IMSI费收入总额",
            key: "imsiTotalAmount",
            align: "center",
            width: 130,
            tooltip: true,
            tooltipMaxWidth: 2000,
            render: (h, params) => {
              const row = params.row;
              const text = parseFloat(math.divide(math.bignumber(row.imsiTotalAmount), 100).toFixed(2)).toString()
              return h('label', text);
            }
          },
          {
            title: "IMSI费调账金额",
            key: "imsiAdjustAmount",
            align: "center",
            width: 130,
            tooltip: true,
            tooltipMaxWidth: 2000,
            render: (h, params) => {
              const row = params.row;
              const text = parseFloat(math.divide(math.bignumber(row.imsiAdjustAmount), 100).toFixed(2)).toString()
              return h('label', text);
            }
          },
          {
            title: "总销售额",
            key: "saleIncome",
            align: "center",
						width: 110,
            tooltip: true,
            tooltipMaxWidth: 2000,
            render: (h, params) => {
              const row = params.row;
              const text = parseFloat(math.divide(math.bignumber(row.saleIncome), 100).toFixed(2)).toString()
              return h('label', text);
            }
          },
					// {
					// 	title: "直接渠道服务费",
					// 	key: "dirRemuneration",
					// 	align: "center",
					// 	minWidth: 150,
					// 	tooltip: true,
					// 	tooltipMaxWidth: 2000,
					// 	render: (h, params) => {
					// 		const row = params.row;
					// 		const text = parseFloat(math.divide(math.bignumber(row.dirRemuneration), 100).toFixed(2)).toString()
					// 		return h('label', text);
					// 	}
					// },
					// {
					// 	title: "间接渠道服务费",
					// 	key: "indRemuneration",
					// 	align: "center",
					// 	minWidth: 150,
					// 	tooltip: true,
					// 	tooltipMaxWidth: 2000,
					// 	render: (h, params) => {
					// 		const row = params.row;
					// 		const text = parseFloat(math.divide(math.bignumber(row.indRemuneration), 100).toFixed(4)).toString()
					// 		return h('label', text);
					// 	}
					// },
					// {
					// 	title: "渠道服务费总额",
					// 	key: "totleRemuneration",
					// 	align: "center",
					// 	minWidth: 150,
					// 	tooltip: true,
					// 	tooltipMaxWidth: 2000,
					// 	render: (h, params) => {
					// 		const row = params.row;
					// 		const text = parseFloat(math.divide(math.bignumber(row.totleRemuneration), 100).toFixed(2)).toString()
					// 		return h('label', text);
					// 	}
					// },
          {
            title: "Top Up Bonus",
            key: "sellRebateUsedAmount",
            align: "center",
						width: 120,
            tooltip: true,
            tooltipMaxWidth: 2000,
            render: (h, params) => {
              const row = params.row;
              const text = parseFloat(math.divide(math.bignumber(row.sellRebateUsedAmount), 100).toFixed(2)).toString()
              return h('label', text);
            }
          },
          {
            title: "实际收入金额",
            key: "realIncome",
            align: "center",
						width: 110,
            tooltip: true,
            tooltipMaxWidth: 2000,
            render: (h, params) => {
              const row = params.row;
              const text = parseFloat(math.divide(math.bignumber(row.realIncome), 100).toFixed(2)).toString()
              return h('label', text);
            }
          },
          {
            title: "服务开始时间",
            key: "svcStartTime",
            align: "center",
            width: 110,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
          {
            title: "服务结束时间",
            key: "svcEndTime",
            align: "center",
            width: 120,
            tooltip: true,
            tooltipMaxWidth: 2000,
          },
					{
            title: "上传RAP状态",
            key: "uploadStatus",
            align: "center",
          	minWidth: 120,
            tooltip: true,
            fixed: 'right',
            render: (h, params) => {
              const row = params.row;
              const text = row.uploadStatus ==='1' ? "未上传" : row.uploadStatus==='2' ? "已上传" :""
              return h('label', text);
            }
          },
          {
            title: "审批状态",
            key: "authStatus",
            align: "center",
						minWidth: 85,
            tooltip: true,
            tooltipMaxWidth: 2000,
            fixed: 'right',
            render: (h, params) => {
              const row = params.row;
              const text = row.authStatus==='1' ? "通过":
              row.authStatus==='2' ? "不通过":
              row.authStatus==='3' ? "待审核":
               row.authStatus==='4' ? "":""
              return h('label', text);
            }
          },
					{
            title: '审批操作',
            slot: 'approveAction',
            minWidth: 100,
            align: 'center',
            fixed: 'right',
          },
          {
            title: '文件下载',
            slot: 'download',
						width: 100,
						align: 'center',
						fixed: 'right',
          },
          {
            title: '编辑',
            slot: 'action',
            align: 'center',
            width: 100,
            fixed: 'right',
          },


        ],
				columnsOT: [{
						type: 'selection',
						width: 60,
						align: 'center'
					},
					{
						title: "客户名称",
						key: "corpName",
						align: "center",
						minWidth: 200,
						// fixed: 'left',
					},
					{
						title: "结算月份",
						key: "statTime",
						align: "center",
						minWidth: 200,
					},
					{
						title: "客户EBS编码",
						key: "ebscode",
						align: "center",
						minWidth: 200,
					},
					{
						title: "Invoice no.",
						key: "invoiceNo",
						align: "center",
						minWidth: 200,
					},
					{
						title: "币种",
						key: "currency",
						align: "center",
						minWidth: 200,
						render: (h, params) => {
							const row = params.row;
							const text = row.currency == '156' ? "CNY" : row.currency == '840' ? "USD" : row.currency == '344' ? "HKD" :
								'';
							return h('label', text);
						}
					},
					{
						title: "套餐收入",
						key: "packageIncome",
						align: "center",
						minWidth: 200,
						render: (h, params) => {
							const row = params.row;
							const text = parseFloat(math.divide(math.bignumber(row.packageIncome), 100).toFixed(2)).toString()
							return h('label', text);
						}
					},
					{
						title: "用量收入",
						key: "useIncome",
						align: "center",
						minWidth: 200,
						render: (h, params) => {
							const row = params.row;
							const text = parseFloat(math.divide(math.bignumber(row.useIncome), 100).toFixed(2)).toString()
							return h('label', text);
						}
					},
					{
						title: "总收入",
						key: "totalIncome",
						align: "center",
						minWidth: 200,
						render: (h, params) => {
							const row = params.row;
							const text = parseFloat(math.divide(math.bignumber(row.totalIncome), 100).toFixed(2)).toString()
							return h('label', text);
						}
					},
					{
						title: "服务开始时间",
						key: "svcStartTime",
						align: "center",
						minWidth: 200,
					},
					{
						title: "服务结束时间",
						key: "svcEndTime",
						align: "center",
						minWidth: 200,
					},
					{
						title: '文件下载',
						slot: 'download',
						minWidth: 340,
						align: 'center',
						fixed: 'right',
					},
					{
						title: '编辑',
						slot: 'update',
						minWidth: 90,
						align: 'center',
						fixed: 'right',
					},
					{
						title: "审批状态",
						key: "authStatus",
						align: "center",
						minWidth: 90,
						tooltip: true,
						tooltipMaxWidth: 2000,
						fixed: 'right',
						render: (h, params) => {
							const row = params.row;
							const text = row.authStatus==='1' ? "通过":
							row.authStatus==='2' ? "不通过":
							row.authStatus==='3' ? "待审核":
							 row.authStatus==='4' ? "":""
							return h('label', text);
						}
					},
					{
						title: '审批操作',
						slot: 'approveAction',
						minWidth: 90,
						align: 'center',
						fixed: 'right',
					},
				],
        RechargeFreeFeeConsumption: [{
          title: "结算月份",
          key: "monthOfSettlement",
          align: "center",
          minWidth: 130,
          tooltip: true,
          fixed: 'left',
        },
          {
            title: "币种",
            key: "currencyCode",
            align: "center",
            minWidth: 100,
            render: (h, params) => {
              const row = params.row;
              const text = row.currencyCode == '156' ? "CNY" : row.currencyCode == '840' ? "USD" : row.currencyCode == '344' ? "HKD" :
                  '';
              return h('label', text);
            }
          },
          {
            title: "Top Up Bonus",
            key: "topUpBonus",
            align: "center",
            minWidth: 150,
            tooltip: true,
          },
          {
            title: "Top Up Bonus(HKD)",
            key: "topUpBonusHkd",
            align: "center",
            minWidth: 150,
            tooltip: true,
          },
        ],
				whiteCradColumns: [
					{
						type: 'selection',
						width: 60,
						align: 'center',
						fixed: 'left'
					},
					{
						title: "客户公司名称",
						key: "corpName",
						align: "center",
						minWidth: 130,
							tooltip: true,
							fixed: 'left',
					},
					{
						title: "EBSCode",
						key: "ebsCode",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "Invoice no.",
						key: "invoiceNo",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
				  {
				  	title: "白卡数量",
				  	key: "cardNum",
				  	align: "center",
				  	minWidth: 150,
				  	tooltip: true,
				  },
          {
          	title: "白卡单价",
          	key: "listedPrice",
          	align: "center",
          	minWidth: 150,
          	tooltip: true,
          },
					{
						title: "币种",
						key: "currencyCode",
						align: "center",
						minWidth: 100,
						render: (h, params) => {
							const row = params.row;
							const text = row.currencyCode == '156' ? "CNY" : row.currencyCode == '840' ? "USD" : row.currencyCode == '344' ? "HKD" :
								'';
							return h('label', text);
						}
					},
				  {
				  	title: "总金额",
				  	key: "amount",
				  	align: "center",
				  	minWidth: 150,
				  	tooltip: true,
				  },
				  {
				  	title: "订单日期",
				  	key: "orderDate",
				  	align: "center",
				  	minWidth: 150,
				  	tooltip: true,
				  },
				  {
				  	title: "结算月份",
				  	key: "period",
				  	align: "center",
				  	minWidth: 150,
				  	tooltip: true,
				  },
          {
          	title: "交付月份",
          	key: "period",
          	align: "center",
          	minWidth: 150,
          	tooltip: true,
          },
				  {
					title: '操作',
					key: 'action',
					width: 150,
					align: 'center',
					slot: 'action'
				  }
				],
				costPriceColumns: [{
            type: 'selection',
            width: 60,
            align: 'center',
            fixed: 'left',
          },
          {
						title: "结算周期",
						key: "statDate",
						align: "center",
						minWidth: 130,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: "资源供应商",
						key: "supplierShortenName",
						align: "center",
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: "供应商编码",
						key: "supplierShortenCode",
						align: "center",
						minWidth: 150,
						tooltip: true,
						tooltipMaxWidth: 2000,
					},
					{
						title: "币种",
						key: "currency",
						align: "center",
						minWidth: 100,
            render: (h, params) => {
            	const row = params.row;
            	var text = "";
            	switch (row.currency) {
            		case "CNY":
            			text = "人民币";
            			break;
            		case "HKD":
            			text = "港币";
            			break;
                case "USD":
                	text = "美元";
                	break;
                case "EUR":
                	text = "欧元";
                	break;
            		default:
            			text = "";
            	}
            	return h("label", text);
            },
					},
				  {
				  	title: "成本预估",
				  	key: "totalAmount",
				  	align: "center",
				  	minWidth: 150,
				  	tooltip: true,
				  },
				  {
				  	title: "成本类型",
				  	key: "type",
				  	align: "center",
				  	minWidth: 150,
				  	tooltip: true,
				  },
				  {
				  	title: "审批状态",
				  	key: "approvalStatus",
				  	align: "center",
				  	minWidth: 150,
				  	tooltip: true,
            render: (h, params) => {
            	const row = params.row;
            	var text = "";
            	switch (row.approvalStatus) {
            		case "1":
            			text = "未审批";
            			break;
            		case "2":
            			text = "审批完成";
            			break;
            		default:
            			text = "";
            	}
            	return h("label", text);
            },
				  },
				  {
				  	title: "同步Rap状态",
				  	key: "syncRap",
				  	align: "center",
				  	minWidth: 150,
				  	tooltip: true,
            render: (h, params) => {
            	const row = params.row;
            	var text = "";
            	switch (row.syncRap) {
            		case "1":
            			text = "未上传";
            			break;
            		case "2":
            			text = "待上传";
            			break;
                case "3":
                	text = "已上传";
                	break;
                case "4":
                	text = "上传失败";
                	break;
            		default:
            			text = "";
            	}
            	return h("label", text);
            },
				  },
				  {
				  	title: "操作",
				  	slot: "action",
				  	align: "center",
				  	minWidth: 150,
				  	tooltip: true,
            fixed: 'right',
				  },
				],
        ruleInline: {
					incomeType: [{
						type: 'number',
						required: true,
						message: '请选择收入类型',
						trigger: 'change'
					}],
					beginMonth: [{
						type: 'date',
						required: true,
						// validator: validateDate,
						message: '请选择开始月份',
						trigger: 'change',
					}],
					endMonth: [{
						type: 'date',
						required: true,
						message: '请选择结束月份',
						trigger: 'blur'
					}],
          statDate: [{
						type: 'date',
						required: true,
						message: '请选择结算周期',
						trigger: 'blur'
					}],
				},
				rule: {
					accountAdjustment: [{
							required: true,
							message: '请输入调账金额',
							trigger: 'blur'
						},
						{
							validator: validateNum,
							message: "最高支持8位整数和2位小数的正负数",
						},
					],
          flowAdjustment: [{
							required: true,
							message: '请输入流量收入',
							trigger: 'blur'
						},
						{
							validator: validateNum,
							message: "最高支持8位整数和2位小数的正负数",
						},
					],
          imsiAdjustment: [{
							required: true,
							message: '请输入IMSI费收入',
							trigger: 'blur'
						},
						{
							validator: validateNum,
							message: "最高支持8位整数和2位小数的正负数",
						},
					],
				},
        ruleMode: {
          accountingType: [{
          	required: true,
          	message: '请选择出账',
          	trigger: 'blur'
          }],
        },
				ruleCerate: {
					taxation: [{
							message: '请输入税费',
							trigger: 'blur',
						},
						{
							validator: validatePositiveNum,
							message: "最高支持8位整数和2位小数的正数或零",
						},
					],
				},
				ruleOT: {
					packageIncome: [{
							required: true,
							message: '请输入套餐收入',
							trigger: 'blur',
						},
						{
							validator: validatePositiveNum,
							message: "最高支持8位整数和2位小数的正数或零",
						},
					],
					useIncome: [{
							required: true,
							message: '请输入用量收入',
							trigger: 'blur',
						},
						{
							validator: validatePositiveNum,
							message: "最高支持8位整数和2位小数的正数或零",
						},
					],
					taxation: [{
						required: true,
						message: '请输入税费',
						trigger: 'blur',
					}],
					svcStartTime: [{
						type: 'date',
						required: true,
						message: '请选择服务开始时间',
						trigger: 'blur',
					}],
					svcEndTime: [{
						type: 'date',
						required: true,
						message: '请选择服务结束时间',
						trigger: 'blur',
					}],
				},
				ruleCost: {
					statDate: [{
						type: 'date',
						required: true,
						message: '请选择结算周期',
					}],
          supplierShortenName: [{
          	required: true,
          	message: '请选择资源供应商',
          }],
          supplierShortenCode: [{
          	required: true,
          	message: '请输入供应商编码',
          }],
          currency: [{
          	required: true,
          	message: '请选择币种',
          }],
          totalAmount: [
            {
            	required: true,
            	message: "请输入成本预估",
            }, {
            	validator: (rule, value, cb) => {
            	  // 如果 value 是空字符串、undefined 或者 null，则不进行校验
            	  if (!value || value === '') {
            	    return cb(); // 直接返回，不传入错误信息
            	  }

            	  // 正则表达式用于匹配最多8位整数和2位小数的正数
            	  var str = /^(?!0(\.0+)?$)([1-9]\d{0,7}(\.\d{1,2})?|\d{1,8}(\.\d{1,2})?)$/;

            	  // 使用正则表达式进行校验
            	  if (str.test(value)) {
            	    return cb(); // 校验成功，直接返回，不传入错误信息
            	  } else {
            	    // 校验失败，返回错误信息
            	    return cb(new Error('最高支持8位整数和2位小数的正数'));
            	  }
            	},
            	trigger: 'blur',
            	message: '最高支持8位整数和2位小数的正数' // 错误提示信息
            }
          ],
          type: [{
          	required: true,
          	message: '请输入成本类型',
          }],
				},
        imsiRule: {
          amount: [
            {
            	required: true,
            	message: "请输入imsi费金额",
            }, {
            	validator: validatePositiveNum,
            	message: "最高支持8位整数和2位小数的正数或零",
            }
          ],
          desc: [
            {
            	required: true,
            	message: "请输入imsi费描述",
            },
          ]
        },
        typeList: [{
						value: 1,
						label: "线上收入",
					},
					{
						value: 2,
						label: "渠道商收入",
					},
					{
						value: 3,
						label: "其他客户收入",
					},
          {
          	value: 4,
          	label: "白卡订单收入",
          },
          {
          	value: 5,
          	label: "成本报表收入",
          },
          {
            value: 6,
            label: "充值赠费消耗汇总表报表",
          },
				],
				onlineCorpList: [{
					corpId: 103,
					corpName: "官网H5",
				}, ],
				searchBeginTime: '',
				searchEndTime: '',
        searchStatDate: '',
        addStatDate: '',
				type: '',
        cooperationMode: '',
        invoiceTitle: '',
        modelTitle: '',
        modelType: '',
				/**
				 * ---------------生成发票相关----------------
				 */
				id: null, //选择行自增主键
        invoiceNoCopy: '',
				invoice_model: false, //预览模态框
        invoiceType: '',
        billType: '',
        rowData: {},
				invoiceInfo: {
					AccountNo: '北京博新創億科技股份有限公司',
					address: '北京市海淀区首都體育館南路6號3幢557室',
					AmountDue: 'CNY 1,360.00',
					InvoiceNo: 'IN-************-GDS',
					InvoiceDate: '30-Mar-2021',
					FileTitle: 'INVOICE',
					InvoiceDesc: null,
					data: [{
							description: 'GDS-Sales Settlement-Mar2021',
							billingPeriod: '25-Feb-2021 to 24-Mar-2021',
							qty: '1',
							unitPrice: '1,360.00',
							amount: '1,360.00',
						},
						{
							description: 'Amount before Tax',
							billingPeriod: null,
							qty: null,
							unitPrice: 'CNY',
							amount: '1,360.00',
						},
						{
							description: 'TAX',
							billingPeriod: null,
							qty: null,
							unitPrice: 'CNY',
							amount: '1,360.00',
						},
						{
							description: 'Total Amount Due',
							billingPeriod: null,
							qty: null,
							unitPrice: 'CNY',
							amount: '1,360.00',
						}
					]
				},
				invoiceColumns: [{
						title: '* Description',
						align: 'center',
						width: 220,
						slot: 'description',
						renderHeader: (h, params) => {
						  return h('div', [
							    h('span', { style: { color: 'red' } }, '*'),
								h('span', ' description'),
						  ])
						}
					},
					{
						title: '* Billing Period',
						align: 'center',
						width: 220,
						slot: 'billingPeriod',
						renderHeader: (h, params) => {
						  return h('div', [
							    h('span', { style: { color: 'red' } }, '*'),
								h('span', ' Billing Period'),
						  ])
						}
					},
					{
						title: 'Qty',
						align: 'center',
						width: 60,
						key: 'qty'
					},
					{
						title: 'Unit Price',
						align: 'center',
						width: 115,
						key: 'unitPrice'
					},
					{
						title: 'amount',
						align: 'center',
						width: 116,
						key: 'amount'
					}
				],
        invoiceFileColumns: [
          {
          	title: "公司名称",
          	key: "companyName",
          	align: "center",
          	minWidth: 130,
          	tooltip: true,
          	tooltipMaxWidth: 2000,
          },
          {
          	title: "客户EBS编码",
          	key: "ebscode",
          	align: "center",
          	minWidth: 150,
          	tooltip: true,
          	tooltipMaxWidth: 2000,
          },
          {
          	title: "Invoice no.",
          	key: "invoiceNo",
          	align: "center",
          	minWidth: 150,
          	tooltip: true,
          	tooltipMaxWidth: 2000,
          }
        ],
        invoiceFileTableData: [],
        batchIds: [],
        dataToSend: {},
        selectedBlankCardRows: [], // 保存已选择的白卡订单数据
        selectedBlankCardIds: new Set(), // 保存已选择的白卡订单ID
        hasBlankCardOrder: false,
        downloadingMap: {}, // 新增：用于存储每个按钮的下载状态
				//批量下载按钮的loading
				batchDownloadLoading: false,
      };
		},
		created() {
			this.ruleInline.beginMonth.push({
				validator: this.validateDate,
				trigger: "change"
			})
			this.ruleInline.endMonth.push({
				validator: this.validateDate,
				trigger: "change"
			})
		},
		mounted() {
			//缓存数据
			let formList = JSON.parse(localStorage.getItem("formList")) === null ? '' : JSON.parse(localStorage.getItem(
				"formList"))
			if (formList != null) {
				this.form.incomeType = formList.incomeType === undefined ? "" : Number(formList.incomeType)
				this.form.corpId = formList.corpId === undefined ? "" : formList.corpId
				this.searchObj.corpId = formList.corpId === undefined ? "" : formList.corpId
				this.form.beginMonth = formList.searchBeginTime === undefined ? "" : formList.searchBeginTime
				this.form.endMonth = formList.searchEndTime === undefined ? "" : formList.searchEndTime
				this.searchBeginTime = formList.searchBeginTime === undefined ? "" : formList.searchBeginTime
				this.searchEndTime = formList.searchEndTime === undefined ? "" : formList.searchEndTime
				//如果选择类型不为空时，调用客户查询接口
				if (this.form.incomeType) {
					this.changeType(this.form.incomeType)
					this.getTableData(1)
				}
				//清除缓存
				localStorage.removeItem("formList")
			}
			this.searchObj = this.form
		},
		methods: {
			getTableData(page) {
				this.page = page
				var t = this
				if (this.type === 1) { //线上收入
					getOnlineIncomeList({
						beginMonth: this.searchBeginTime,
						endMonth: this.searchEndTime,
						salesChannel: this.searchObj.corpId,
						pageNum: this.page,
						pageSize: this.pageSize
					}).then(res => {
						if (res.code === "0000") {
							this.total = res.count;
							t.data = res.data
							this.searchloading = false
							this.getSpanData(t.data)
						}
					});
				}
				if (this.type === 2) { //渠道商收入
					getCorpIncomeList({
						beginMonth: this.searchBeginTime,
						endMonth: this.searchEndTime,
						corpId: this.searchObj.corpId,
						corpName: this.searchObj.corpName,
						pageNum: this.page,
						pageSize: this.pageSize
					}).then(res => {
						if (res.code === "0000") {
              var data = res.data
              var List = []
              data.map((value, index) => {
                List.push(value)
              })
              //回显
              this.selectionList.forEach(item => {
              	res.data.forEach(element => {
              		if (element.id == item.id) {
              			this.$set(element, '_checked', true)
              		}
              	})
              })

              this.data = List;
              // 如果没有invoicePath 就不让勾选进行批量下载invoice
              // this.data.forEach(i => {
              //   if (!i.invoicePath) {
              //     this.$set(i, '_disabled', true)
              //   }
              // })
              this.total = Number(res.count);
              this.searchloading = false
						}
					});
				}
				if (this.type === 3) { //其他客户收入
					CustomerPage({
						beginMonth: this.searchBeginTime,
						endMonth: this.searchEndTime,
						corpId: this.searchObj.corpId,
						corpName: this.searchObj.corpName,
						pageNum: this.page,
						pageSize: this.pageSize,
						type: [3, 4, 7, 8]
					}).then(res => {
						if (res.code === "0000") {
							//回显
							this.selectionTypes.forEach(item => {
								res.data.forEach(element => {
									if (element.id == item.id) {
										this.$set(element, '_checked', true)
									}
								})
							})
							this.data = res.data;
							this.total = res.count;
							this.searchloading = false
						}
					});
				}
        if (this.type === 4) { //白卡订单收入
          getWhiteCardList({
          	pageSize: this.pageSize,
          	pageNum: this.page,
          	corpId: this.form.customer,
          	beginDate: this.searchBeginTime,
          	endDate: this.searchEndTime,
          }).then(res => {
          	if (res.code === "0000") {
          		this.data = res.data;
							this.selectedBlankCardRows.forEach(row => {
								this.data.forEach(item => {
									if (item.id === row.id) {
										this.$set(item, '_checked', true)
									}
								})
							})
          		this.total = Number(res.count);
          	}
          });
				}
        if (this.type === 5) { //成本报表收入
        	getCostReportList({
            supplierShortenName: this.form.supplierShortenName,
            supplierShortenCode: this.form.supplierShortenCode,
            statDate: this.searchStatDate,
            approvalStatus: this.form.approvalStatus,
            syncRap: this.form.syncRap,
        		current: this.page,
        		size: this.pageSize,
        	}).then(res => {
        		if (res.code === "0000") {
              var data = res.data
              var List = []
              data.map((value, index) => {
                List.push(value)
              })

              //回显
              this.selectionList.forEach(item => {
              	res.data.forEach(element => {
              		if (element.id == item.id) {
              			this.$set(element, '_checked', true)
              		}
              	})
              })

              this.data = List;
              // 如果状态为审批完成 就不让勾选
              this.data.forEach(i => {
                if (i.approvalStatus != '1') {
                  this.$set(i, '_disabled', true)
                }
              })
              this.total = Number(res.count);
        		}
        	});
        }
        if (this.type === 6) { //出账管理新增充值赠费消耗汇总表报表
          getRechargeFreeFeeConsumption({
            pageSize: this.pageSize,
            pageNum: this.page,
            month: this.searchStatDate,
          }).then(res => {
            if (res.code === "0000") {
              this.data = res.data;
              this.total = Number(res.count);
            }
          });
        }
        this.searchloading = false
			},
			search(name) {
				var _this = this
				this.$refs[name].validate((valid) => {
					if (valid) {
						this.isSearch = true
						this.searchloading = true
						this.selectionTypes = []
						this.getTableData(1)
					}
				})
			},
			// 新增选择套餐包
			selectPackage(selection, row) {
				// this.selection = selection;
				// this.selectionIds = [];
				// this.selectionTypes.push(row);
			},
			// 取消选择套餐包
			cancelPackage(selection, row) {
				this.selectionTypes.forEach((value, index) => {
					if (value.id === row.id) {
						this.selectionTypes.splice(index, 1);
					}
				})
			},
			// 取消全选选择套餐包
			cancelPackageAll(selection, row) {
				this.selectionTypes = []
			},
			exportOnline(row, type) {
				if (type === 'sum') { //下载汇总文件
					//TODO 导出请求
					exportTotalTask({
						month: row.statTime,
						salesChannel: row.salesChannel,
						userId: this.$store.state.user.userId
					}).then(res => {
						if (res.code === "0000") {
							if (res.data.taskId != '' && res.data.taskName != '') { //导出弹框提示
								this.exportModal = true
								var _this = this
								if (Object.values(res.data).length > 0) {
									Object.values(res.data).forEach(function(value) {
										_this.taskIds.push(value.taskId)
										_this.taskNames.push(value.taskName)
										if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
											let taskid =  _this.taskIds.slice(0,3)
											let taskname = _this.taskNames.slice(0,3)
											_this.taskIds = taskid
											_this.taskNames = taskname
											_this.remind = true
										}
									})
								}
							} else {
								this.$Message.error('获取下载任务失败');
							}
						}
					});
				}
				if (type === 'detail') { //下载明细文件
					//TODO 导出请求
					exportDetailTask({
						month: row.statTime,
						salesChannel: row.salesChannel,
						userId: this.$store.state.user.userId
					}).then(res => {
						if (res.code === "0000") {
							if (res.data.taskId != '' && res.data.taskName != '') { //导出弹框提示
								this.exportModal = true
								var _this = this
								if (Object.values(res.data).length > 0) {
									Object.values(res.data).forEach(function(value) {
										_this.taskIds.push(value.taskId)
										_this.taskNames.push(value.taskName)
										if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
											let taskid =  _this.taskIds.slice(0,3)
											let taskname = _this.taskNames.slice(0,3)
											_this.taskIds = taskid
											_this.taskNames = taskname
											_this.remind = true
										}
									})
								}
							} else {
								this.$Message.error('获取下载任务失败');
							}
						} else {
              throw res
            }
					});
				}

			},
			loadByPage(page) {
				this.page = page
				this.getTableData(page, this.pageSize)
			},
			loadByPageSize(pageSize) {
				this.pageSize = pageSize
				this.getTableData(this.page, pageSize)
			},
			exportcancelModal(){
				this.exportModalr = false
				this.file = ''
			},
			cancelModal() {
				this.exportModal = false
				this.updateOTModal = false
				this.CerateModal = false
				this.$refs.corpList.resetFields()
				this.$refs.cerateList.resetFields()
				this.Customerfile = ''
				this.file = ''
				this.taskIds = []
				this.taskNames = []
				this.remind = false
        this.selectModeModal = false
        this.accountingList.accountingType = ""
        // 清空新增、修改 成本弹窗
        this.$refs.corpListCost.resetFields()
        this.corpListCost = {}
        this.costModal = false
        // 清空运营审批勾选数据
        this.costIdList = []
			},
			Gotor(){
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportcancelModal()
				this.exportModalr = false
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportModal = false
			},
      exportDetail(row, type) {
				let corpId = null
				let corpName = null
				let currency = null
				let statTime = null
        let id = null
        let accountingType = null
				if (row != null) {
					corpId = row.corpId
					corpName = row.corpName
					currency = row.currency
					statTime = row.statTime
          id = row.id
          accountingType = row.accountingType
				}
				//下载总明细文件
				OpexportDetailTask({
          id: id,
					beginMonth: this.searchBeginTime,
					endMonth: this.searchEndTime,
					corpId: corpId,
					corpName: corpName,
					currencyCode: currency,
					userId: this.$store.state.user.userId,
					statTime: statTime,
          billType: accountingType
					// fileNamePart: ,
					// type:[1]
				}).then(res => {
					if (res && res.code == '0000') {
						this.exportModal = true
						var _this = this
						if (Object.values(res.data).length > 0) {
							Object.values(res.data).forEach(function(value) {
								_this.taskIds.push(value.taskId)
								_this.taskNames.push(value.taskName)
								if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
									let taskid =  _this.taskIds.slice(0,3)
									let taskname = _this.taskNames.slice(0,3)
									_this.taskIds = taskid
									_this.taskNames = taskname
									_this.remind = true
								}
							})
						}
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			exportSum(row, type) {
				let corpId = null
				let corpName = null
				let statTime = null
        let id = null
        let accountingType = null
				if (row != null) {
					corpId = row.corpId
					corpName = row.corpName
					statTime = row.statTime
          id = row.id
          accountingType = row.accountingType
				}
				//下载总汇总文件
				OpexportTotalTask({
          id: id,
					beginMonth: this.searchBeginTime,
					endMonth: this.searchEndTime,
					corpId: corpId,
					corpName: corpName,
					userId: this.$store.state.user.userId,
					statTime: statTime,
          billType: accountingType
					// fileNamePart: ,
					// type:[1]
				}).then(res => {
					if (res && res.code == '0000') {
						this.exportModal = true
						var _this = this
						if (Object.values(res.data).length > 0) {
							Object.values(res.data).forEach(function(value) {
								_this.taskIds.push(value.taskId)
								_this.taskNames.push(value.taskName)
								if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
									let taskid =  _this.taskIds.slice(0,3)
									let taskname = _this.taskNames.slice(0,3)
									_this.taskIds = taskid
									_this.taskNames = taskname
									_this.remind = true
								}
							})
						}
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				})

			},
			exportInvoice(row, type) {
				exportInvoice({
					corpName: row.corpName,
					invoicePath: row.invoicePath,
					month: row.statTime,
					corpId: this.$store.state.user.userId,
          billType: row.accountingType,
          incomeId: row.id,
				}).then(res => {
					if (res && res.code == '0000') {
						this.exportModalr = true
						this.taskId = res.data.taskId
						this.taskName = res.data.taskName
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				})
			},
      exportA2Zdeatil(row) {
				exportA2Zdeatil({
          id: row.id,
					corpId: row.corpId,
					beginDate: row.svcStartTime,
					endDate: row.svcEndTime,
          billType: row.accountingType,
          userId: this.$store.state.user.userId,
          type: row.accountingType == '2' ? '1' : row.accountingType == '3' ? '2' : row.accountingType == '4' ? 1 :
            row.accountingType == '5' ? '3': row.accountingType == '6' ? '2' : row.accountingType == '7' ? '3' : '',
				}).then(res => {
					if (res && res.code == '0000') {
            this.exportModal = true
            var _this = this
            if (Object.values(res.data).length > 0) {
            	Object.values(res.data).forEach(function(value) {
            		_this.taskIds.push(value.taskId)
            		_this.taskNames.push(value.taskName)
            		if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
            			let taskid =  _this.taskIds.slice(0,3)
            			let taskname = _this.taskNames.slice(0,3)
            			_this.taskIds = taskid
            			_this.taskNames = taskname
            			_this.remind = true
            		}
            	})
            }
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				})
			},
      exportA2ZSummary(row) {
      	exportA2ZSummary({
          id: row.id,
      		corpId: row.corpId,
      		beginDate: row.svcStartTime,
      		endDate: row.svcEndTime,
          billType: row.accountingType,
          userId: this.$store.state.user.userId,
          type: row.accountingType == '2' ? '1' : row.accountingType == '3' ? '2' : row.accountingType == '4' ? 1 :
            row.accountingType == '5' ? '3': row.accountingType == '6' ? '2' : row.accountingType == '7' ? '3' : '',
      	}).then(res => {
      		if (res && res.code == '0000') {
      			this.exportModalr = true
      			this.taskId = res.data.taskId
      			this.taskName = res.data.taskName
      		} else {
      			throw res
      		}
      	}).catch((err) => {
      		console.log(err)
      	})
      },
      exportIMSIdeatil(row) {
				exportIMSIdeatil({
          id: row.id,
					corpId: row.corpId,
					cooperationMode: row.accountingType,
					dateStart: row.svcStartTime,
					dateEnd: row.svcEndTime,
          billType: row.accountingType,
          userId: this.$store.state.user.userId,
				}).then(res => {
					if (res && res.code == '0000') {
						this.exportModalr = true
						this.taskId = res.data.taskId
						this.taskName = res.data.taskName
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				})
			},
      // 充值赠费消耗汇总表报表导出
      exportRechargeFreeFeeConsumption(name) {
        this.$refs[name].validate((valid) => {
          if (valid) {
            this.downloading = true
            exportA2zAmountByMonth({
              month: this.searchStatDate,
            }).then(res => {
              const content = res.data
              let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[1]) //获取到Content-Disposition;filename  并解码

              console.log(fileName)
              if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
                const link = this.$refs.downloadLink // 创建a标签
                let url = URL.createObjectURL(content)
                link.download = fileName
                link.href = url
                link.click() // 执行下载
                URL.revokeObjectURL(url) // 释放url
              } else { // 其他浏览器
                navigator.msSaveBlob(content, fileName)
              }
            }).catch((err) => {
              console.log(err)
            }).finally(() => {
              this.downloading = false
            })
          }
        })
      },
      // 白卡订单收入导出
      exportWhite(name) {
        this.$refs[name].validate((valid) => {
        	if (valid) {
            this.downloading = true
        		whiteCardFile({
        			corpId: this.form.customer,
        			beginDate: this.searchBeginTime,
        			endDate: this.searchEndTime,
        		}).then(res => {
        			const content = res.data
        			let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[1]) //获取到Content-Disposition;filename  并解码
        			if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
        				const link = this.$refs.downloadLink // 创建a标签
        				let url = URL.createObjectURL(content)
        				link.download = fileName
        				link.href = url
        				link.click() // 执行下载
        				URL.revokeObjectURL(url) // 释放url
        			} else { // 其他浏览器
        				navigator.msSaveBlob(content, fileName)
        			}
        		}).catch((err) => {
        			console.log(err)
        		}).finally(() => {
              this.downloading = false
            })
        	}
        })
      },
      //成本报表收入——明细、汇总导出
      exportCost(name, type) {
        this.$refs[name].validate((valid) => {
        	if (valid) {
            if (type == '1') {
              this.summaryLoading = true
            } else {
              this.detailLoading = true
            }
            let func = type == '1' ? exportCostSummary : exportCostDetail
            func({
            	beginDate: this.searchBeginTime,
            	endDate: this.searchEndTime,
            	corpName: this.searchObj.corpName,
            	pageNum: this.page,
            	pageSize: this.pageSize,
            	supplierShortenName: this.form.supplierShortenName,
            	supplierShortenCode: this.form.supplierShortenCode,
            	statDate: this.searchStatDate,
            	syncRap: this.form.syncRap,
            	approvalStatus: this.form.approvalStatus,
            }).then(res => {
            	const content = res.data
            	let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[1]) //获取到Content-Disposition;filename  并解码
            	if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
            		const link = this.$refs.downloadLink // 创建a标签
            		let url = URL.createObjectURL(content)
            		link.download = fileName
            		link.href = url
            		link.click() // 执行下载
            		URL.revokeObjectURL(url) // 释放url
            	} else { // 其他浏览器
            		navigator.msSaveBlob(content, fileName)
            	}
            }).catch().finally(() => {
              this.summaryLoading = false
              this.detailLoading = false
            })
        	}
        })
      },
      update(row) { //修改
				this.updateModal = true;
        this.cooperationMode = row.accountingType;
        this.corpListOP.id = row.id
        this.corpListOP.corpName = row.corpName
				this.corpListOP.accountAdjustment = parseFloat(math.divide(math.bignumber(row.accountAdjustment), 100).toFixed(2)).toString()
				this.corpListOP.flowAdjustment = parseFloat(math.divide(math.bignumber(row.flowAdjustAmount), 100).toFixed(2)).toString()
				this.corpListOP.imsiAdjustment = parseFloat(math.divide(math.bignumber(row.imsiAdjustAmount), 100).toFixed(2)).toString()
			},
			updateOT(row) {
				this.updateOTModal = true
				this.corpList = Object.assign({}, row);
				this.corpList.taxation = parseFloat(math.divide(math.bignumber(row.taxation), 100).toFixed(2)).toString()
				this.corpList.useIncome = parseFloat(math.divide(math.bignumber(row.useIncome), 100).toFixed(2)).toString()
				this.corpList.packageIncome = parseFloat(math.divide(math.bignumber(row.packageIncome), 100).toFixed(2)).toString()
				this.endTime = this.corpList.svcEndTime
				this.startTime = this.corpList.svcStartTime
			},
			// 渠道商审批
			channelapprove(status,row){
				this.$Modal.confirm({
					title: status === 1 ? '确认执行审核通过？' : '确认执行审核不通过？',
					onOk: () => {
						channelAuth({
							id:row.id,
							status :status
						}).then(res => {
							if (res.code === "0000") {
								this.getTableData(1)
								this.cancel1()
							}
						});
					}
				})
			},
			// 其他客户收入审批
			otherapprove(status,row){
				this.$Modal.confirm({
					title: status ===1  ? '确认执行审核通过？' : '确认执行审核不通过？',
					onOk: () => {
						otherAuth({
							id:row.id,
							status :status
						}).then(res => {
							if (res.code === "0000") {
								this.getTableData(1)
								this.cancel1()
							}
						});
					}
				})
			},
			cancel1() {
				this.updateModal = false
				this.updateLoading = false
				this.$refs.corpListOP.resetFields()
        this.imsiModal = false
        this.$refs.imsiOP.resetFields()
			},
			confirm() {
				this.$refs["corpListOP"].validate((valid) => {
					if (valid) {
            this.updateLoading = true
						updateChannel({
							accountAdjustment: math.multiply(math.bignumber(this.corpListOP.accountAdjustment), 100).toString(),
              flowAdjustment: math.multiply(math.bignumber(this.corpListOP.flowAdjustment), 100).toString(),
              imsiAdjustment: math.multiply(math.bignumber(this.corpListOP.imsiAdjustment), 100).toString(),
							id: this.corpListOP.id,
						}).then(res => {
							if (res.code === "0000") {
								this.updateModal = false
								this.getTableData(1)
								this.cancel1()
							}
						}).catch((err) => {
              this.updateLoading = false
            });
					}
				})
			},
			OTconfirm() {
				this.$refs["corpList"].validate((valid) => {
					if (valid) {
						this.$Modal.confirm({
							title: '真实账单生成后修改不会修改真实账单,是否确认提交修改?',
							width: 550,
							onOk: () => {
								this.OTloading = true
								var formData = new FormData();
								formData.append("id", this.corpList.id)
								formData.append("packageIncome ", math.multiply(math.bignumber(this.corpList.packageIncome), 100).toString())
								formData.append("svcEndTime ", this.endTime)
								formData.append("svcStartTime ", this.startTime)
								formData.append("useIncome", math.multiply(math.bignumber(this.corpList.useIncome), 100).toString())
								UpdateCustomer(formData).then(res => {
									if (res.code === "0000") {
										this.updateOTModal = false
										this.getTableData(1)
										this.OTloading = false
									}
								});
								this.selectionTypes = []
							}
						});
					}
				})

			},
			accountingConfirm() {
				this.$refs["accountingList"].validate((valid) => {
					if (valid) {
            let invoiceTypeCopy = this.accountingList.accountingType
            let type = this.accountingList.accountingType == '1' ? '代销' : this.accountingList.accountingType == '2' ? '流量' : '合并出账'
						this.$Modal.confirm({
							title: '确认选择 ' + type + " 进行出账",
							width: 550,
							onOk: () => {
								this.accountingLoading = true
                //判断发票是否是实际发票
                if (this.invoiceNoCopy == null || (this.invoiceNoCopy != null && this.invoiceNoCopy.substr(0, 2) != "IN")) {
                	//非正式发票，需要调用发票编号生成接口
                	this.createInvoiceNo(this.row, 2, invoiceTypeCopy, this.row.accountingType)
                } else {
                	this.createInvoiceNo(this.row, 1, invoiceTypeCopy, this.row.accountingType)
                }
							}
						});
					}
				})

			},
      //生成真实账单
			Cerateconfirm() {
				this.Cerateloading = true
				var formData = new FormData();
				formData.append("idList", this.cerateList.idList)
				formData.append("corpId ", this.cerateList.corpId)
				formData.append("corpName ", this.cerateList.corpName)
				formData.append("currency ", this.cerateList.currency)
				formData.append("statTime ", this.cerateList.statTime)
				formData.append("svcEndTime ", this.cerateList.svcEndTime)
				formData.append("svcStartTime ", this.cerateList.svcStartTime)
				formData.append("packageIncome ", math.multiply(math.bignumber(this.cerateList.packageIncome), 100).toString())
				formData.append("useIncome", math.multiply(math.bignumber(this.cerateList.useIncome), 100).toString())
				formData.append("totalIncome", math.multiply(math.bignumber(this.cerateList.totalIncome), 100).toString())
				if (this.file) {
					formData.append("taxationFile", this.file)
				}
				if (this.Customerfile) {
					formData.append("billPathFile", this.Customerfile)
				}
				let taxationflg = true
				if (this.cerateList.taxation) {
					var str = /^(([0-9]\d*)|0)(\.\d{0,2})?$/;
					if (!str.test(this.cerateList.taxation)) {
						this.$refs["cerateList"].validate((valid) => {
							if (valid) {
								taxationflg = true
							} else {
								taxationflg = false
							}
						})
					}
					if (taxationflg) {
						formData.append("taxation", math.multiply(math.bignumber(this.cerateList.taxation), 100).toString())
					}
				} else {
					formData.append("taxation", 0)
				}
				if (taxationflg) {
					generateActualBill(formData).then(res => {
						if (res.code === "0000") {
							this.CerateModal = false
							this.Cerateloading = false
							this.$Notice.success({
								title: "提示",
								desc: "成功生成真实账单！"
							})
							this.selectionTypes = []
							this.getTableData(1)
							this.cancelModal()
						}
					}).catch((err) => {
						this.cancelModal()
						this.Cerateloading = false
					});
				}
			},
			//导出其他客户收入汇总表
			downloadFile(type) {
				// type :0（暂估）/1（真实），
				exportSummaryFile({
					beginMonth: this.searchBeginTime,
					endMonth: this.searchEndTime,
					corpId: this.searchObj.corpId,
					userId: this.$store.state.user.userId,
					corpName: this.searchObj.corpName,
					type: [3, 4, 7, 8],
					exportType: type
				}).then(res => {
					if (res && res.code == '0000') {
						this.exportModal = true
						var _this = this
						if (Object.values(res.data).length > 0) {
							Object.values(res.data).forEach(function(value) {
								_this.taskIds.push(value.taskId)
								_this.taskNames.push(value.taskName)
								if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
									let taskid =  _this.taskIds.slice(0,3)
									let taskname = _this.taskNames.slice(0,3)
									_this.taskIds = taskid
									_this.taskNames = taskname
									_this.remind = true
								}
							})
						}
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			//选择简称时回填编码
			changeCode(data) {
			  //匹配简称找到对应的编码
        this.corpListCost.supplierShortenCode = this.supplierShortenList[data]
			},
      //新增、修改成本
      addCostItem(type) {
        this.costType = type
        this.titleName = "新增成本"
        this.costModal = true
      },
      updateCostItem(row, type) {
        this.costType = type
        this.corpListCost = JSON.parse(JSON.stringify(row))
        this.titleName = "修改成本"
        this.costModal = true
      },
      costConfirm() {
        let addData = {
          statDate: this.addStatDate,
          supplierShortenName: this.corpListCost.supplierShortenName,
          supplierShortenCode: this.corpListCost.supplierShortenCode,
          currency: this.corpListCost.currency,
          totalAmount: this.corpListCost.totalAmount,
          type: this.corpListCost.type
        }
        let updateData = {
          id: this.corpListCost.id,
          totalAmount: this.corpListCost.totalAmount,
          type: this.corpListCost.type
        }
        let func = this.costType == '1' ? addCostItemFunc : updateCostItemFunc
        let resultData = this.costType == '1' ? addData : updateData
        this.$refs["corpListCost"].validate((valid) => {
        	if (valid) {
            this.costLoading = true
        		func(resultData).then(res => {
        			if (res.code === "0000") {
                this.$Notice.success({
                	title: "操作提示",
                	desc: "操作成功！",
                });
        				this.updateModal = false
        				this.getTableData(1)
        				this.cancelModal()
        			}
        		}).catch((err) => {
            }).finally(() => {
              this.costLoading = false
            })
        	}
        })
      },
      // 运营审批确认弹窗
      financialApproval() {
        var len = this.selection.length;
        if (len < 1) {
        	this.$Message.warning("请至少选择一条记录");
        	return;
        } else {
          this.selectionList.forEach((value, index) => {
          	this.costIdList.push(value.id)
          })
          this.$Modal.confirm({
          	title: "确定执行审核确认？",
          	onOk: () => {
          		approvalConfirm({
                statDate: this.searchStatDate,
                dataIds: this.costIdList,
              }).then((res) => {
          			if (res.code === "0000") {
          				this.$Notice.success({
          					title: "操作提示",
          					desc: "操作成功！",
          				});
                  this.costIdList = []
                  this.selectionList = []
                  this.getTableData(1)
          			}
          		}).catch((err) => {
              }).finally(() => {
              })
          	},
          });
        }
      },
      //多选
			handleRowChange(selection) {
				this.selection = selection;
				this.selectionIds = [];
				selection.map((value, index) => {
					let flag = true;
					this.selectionTypes.map((item, index) => {
						if (value.id === item.id) {
							flag = false
						}
					});
					//判断重复
					if (flag) {
						this.selectionTypes.push(value);
					}
				});
			},
      handleRowChangeInvoice(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					let flag = true;
					this.selectionList.map((item, index) => {
						if (value.id === item.id) {
							flag = false
						}
					});
					//判断重复
					if (flag) {
						this.selectionList.push(value);
					}
				});
			},
      cancelItem(selection, row) {
      	this.selectionList.forEach((value, index) => {
      		if (value.id === row.id) {
      			this.selectionList.splice(index, 1);
      		}
      	})
      },
      cancelInvoiceAll(selection, row) {
      	this.selectionList = []
      },
      cancelInvoiceFile() {
        this.modelType = ''
        this.batchIds = []
        this.dataToSend = {}
        this.invoiceModal = false
      },
      // 批量导出Invoice
			batchExportInvoice() {
        this.modelTitle = '缺少Invoice的数据项'
        this.modelType = '1'
        var len = this.selection.length;
        if (len < 1) {
        	this.$Message.warning("请至少选择一条记录");
        	return;
        }
        let excludedItems = [];

        // 遍历this.selectionList
        this.selectionList.forEach((value, index) => {
          // 检查如果返回invoicePath
          if (value.invoicePath && value.invoicePath != null && value.invoicePath !== '' &&  value.invoicePath !== undefined) {
            // 如果invoicePath有效，则添加id到batchIds数组
            this.batchIds.push(value.id);
          } else {
            // 如果invoicePath无效，则添加该对象到剔除数组
            excludedItems.push(value);
          }
        });

        if (this.batchIds.length == 0) {
          this.$Message.warning({
            background: true,
            content: '所有勾选项均无invoice文件，请重新勾选！'
          })
        } else if (excludedItems.length > 0 && this.batchIds.length > 0) {
          this.invoiceFileTableData = excludedItems
          this.invoiceTitle = '您勾选的数据项中，以上数据没有对应的invoice文件。是否继续下载那些有invoice文件的数据项？'
          this.invoiceModal = true
        } else if (this.batchIds.length > 0 && excludedItems.length == 0){
          this.$Modal.confirm({
          	title: '确认执行批量下载Invoice？',
          	onOk: () => {
          		this.besureSubmit()
          	}
          })
        }
      },
      // 立即出账
      rapidPaymentOut() {
        this.modelTitle = '非定制账期选中项'
        this.modelType = '2'
        var len = this.selection.length;
        if (len < 1) {
        	this.$Message.warning("请至少选择一条记录");
        	return;
        }

        let inComeIds = [];
        let createInvoiceVos = [];
        let nonPeriodItems = []; // 用于存储isCustomize为false的对象

        this.selectionList.forEach(item => {
          if (item.isCustomize) {
            if (item.invoicePath && item.invoicePath != null && item.invoicePath !== '' && item.invoicePath !== undefined) {
              // 如果invoicePath存在，将id添加到inComeIds数组
              inComeIds.push(item.id);
            } else {
              // 如果invoicePath不存在，则构建createInvoiceVos对象
              let createInvoiceVo = {
                channelExportDTO: {
                  beginMonth: this.searchBeginTime,
                  billType: item.accountingType,
                  cooperationMode: item.accountingType,
                  corpId: item.corpId,
                  corpName: item.corpName,
                  currencyCode: item.currency,
                  dateEnd: item.svcEndTime,
                  dateStart: item.svcStartTime,
                  endMonth: this.searchEndTime,
                  id: item.id,
                  statTime: item.statTime,
                  type: item.accountingType === '2' ? '1' :
                         item.accountingType === '3' ? '2' :
                         item.accountingType === '4' ? '1' :
                         item.accountingType === '5' ? '3' :
                         item.accountingType === '6' ? '2' :
                         item.accountingType === '7' ? '3' : '',
                  userId: this.$store.state.user.userId,
                },
                corpId: item.corpId,
                id: item.id,
                invoiceDesc: item.invoiceDesc ? item.invoiceDesc : this.default,
                type: item.type,
                invoiceType: item.accountingType ? item.accountingType : undefined,
              };
              // 将构建好的对象添加到createInvoiceVos数组
              createInvoiceVos.push(createInvoiceVo);
            }
          } else {
            // 如果isCustomize为false，则添加到nonPeriodItems数组
            nonPeriodItems.push(item);
          }
        });
        this.dataToSend = {
          createInvoiceVos: createInvoiceVos,
          inComeIds: inComeIds
        };
        //定制账期为空
        if (this.isDataToSendEmpty(this.dataToSend)) {
          this.$Message.warning({
            background: true,
            content: '勾选项均非定制账期，请重新勾选！'
          })
        } else if (!this.isDataToSendEmpty(this.dataToSend) && nonPeriodItems.length > 0) {
          this.invoiceFileTableData = nonPeriodItems
          this.invoiceTitle = '您勾选的数据项中，以上数据不是定制账期。是否继续处理其余定制账期的数据项？'
          this.invoiceModal = true
        } else if (!this.isDataToSendEmpty(this.dataToSend) && nonPeriodItems.length == 0){
          this.$Modal.confirm({
          	title: '确认执行立即出账？',
          	onOk: () => {
          		this.besureSubmit()
          	}
          })
        }
      },
      besureSubmit() {
        this.besureSubmitLoading = true
        if (this.modelType == '1') {
          batchExportInvoice({
          		"ids": this.batchIds,
              "userId": this.$store.state.user.userId,
          	}).then((res) => {
          		if (res.code === "0000") {
                this.exportModalr = true
                this.taskId = res.data.taskId
                this.taskName = res.data.taskName
                this.selection = []
                this.selectionList = []
                this.cancelInvoiceFile()
          			this.getTableData(1);
          		} else {
                throw res
              }
          	})
          	.catch((err) => {
          	}).finally(() => {
              this.cancelInvoiceFile()
              this.besureSubmitLoading = false
          	})
        } else if (this.modelType == '2') {
          rapidPaymentOut(this.dataToSend).then((res) => {
          		if (res.code === "0000") {
                this.$Notice.success({
                	title: '操作成功',
                	desc: '操作成功'
                })
                this.selection = []
                this.selectionList = []
                this.cancelInvoiceFile()
          			this.getTableData(1);
          		} else {
                throw res
              }
          	})
          	.catch((err) => {
          	}).finally(() => {
              this.cancelInvoiceFile()
              this.besureSubmitLoading = false
          	})
        }
      },
      /**
			 *生成真实账单
			 */
			createReal() {
				var len = this.selectionTypes.length;
				if (len < 1) {
					this.$Message.warning('请至少选择一个客户')
					return
				}
				let nowDate = new Date();
				const date = {
					year: nowDate.getFullYear(),
					month: nowDate.getMonth() + 1,
					date: nowDate.getDate(),
				}
				//数组排序
				this.selectionTypes.sort(function(str1, str2) {
					return str2.statTime.localeCompare(str1.statTime);
				});

				let svcEndTime = null
				let svcStartTime = null
				let packageIncome = 0
				let useIncome = 0
				let corpName = this.selectionTypes[0].corpName
				let mark = 0
				let idList = []
				let month = null
				let statTime = this.selectionTypes[0].statTime
				//是否连续的标志
				var lianxu = false;
				//是否为临时票号
				let hao = false
				//封装数据
				this.selectionTypes.map((value, index) => {
					svcEndTime = value.svcEndTime
					svcStartTime = value.svcStartTime
					packageIncome = packageIncome + value.packageIncome
					useIncome = useIncome + value.useIncome
					idList.push(value.id)
					if (value.corpName != corpName) {
						mark = mark + 1
					}
					corpName = value.corpName
					if (Number(statTime) - Number(value.statTime) > 1) {
						lianxu = true
					}
					if (value.invoiceNo != null && value.invoiceNo.substr(0, 2) === "IN") {
						hao = true
					}
					statTime = value.statTime
				})

				//校验是否有不同客户
				if (mark > 0) {
					this.$Message.warning('请选择同一个客户')
					return
				}
				//校验月份是否连续
				if (lianxu) {
					this.$Message.warning('请选择连续月份的客户')
					return
				}
				//校验是否生成过账单
				if (hao) {
					this.$Message.warning('已经生成真实账单的数据不能被勾选')
					return
				}
				//月份小于10补0
				if (date.month < 10) {
					month = "0" + date.month
				} else {
					month = date.month
				}
				this.cerateList = {
					idList: idList,
					corpId: this.selectionTypes[0].corpId,
					corpName: this.selectionTypes[0].corpName,
					currency: this.selectionTypes[0].currency,
					statTime: date.year + "" + month,
					svcStartTime: len < 1 ? this.selectionTypes[0].svcStartTime : svcStartTime,
					svcEndTime: len < 1 ? svcEndTime : this.selectionTypes[0].svcEndTime,
					packageIncome: parseFloat(math.divide(math.bignumber(packageIncome), 100).toFixed(2)).toString(),
					useIncome: parseFloat(math.divide(math.bignumber(useIncome), 100).toFixed(2)).toString(),
					totalIncome: parseFloat(math.divide(math.bignumber(packageIncome + useIncome), 100).toFixed(2)).toString(),
				}
				this.CerateModal = true
			},
			/**
			 *历史真实账单
			 */
			createHistory() {
				this.form.searchBeginTime = this.searchBeginTime
				this.form.searchEndTime = this.searchEndTime
				this.$router.push({
					path: '/history',
					query: {
						formList: encodeURIComponent(JSON.stringify(this.form)),
					}
				})
			},
			/**
			 * 文件下载导出
			 */
			exportCommon(row, name) {
				if (name === 'Package') {
					//按套餐结算汇总报表
					exportPackage({
						corpId: row.corpId,
						corpName: row.corpName,
						month: row.statTime,
						userId: this.$store.state.user.userId
					}).then(res => {
						if (res && res.code == '0000') {
							this.exportModal = true
							var _this = this
							if (Object.values(res.data).length > 0) {
								Object.values(res.data).forEach(function(value) {
									_this.taskIds.push(value.taskId)
									_this.taskNames.push(value.taskName)
									if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
										let taskid =  _this.taskIds.slice(0,3)
										let taskname = _this.taskNames.slice(0,3)
										_this.taskIds = taskid
										_this.taskNames = taskname
										_this.remind = true
									}
								})
							}
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err)
					})
				} else if (name === 'flowSettle') {
					//按流量结算汇总报表
					exportflowSettle({
						corpId: row.corpId,
						corpName: row.corpName,
						month: row.statTime,
						userId: this.$store.state.user.userId
					}).then(res => {
						if (res && res.code == '0000') {
							this.exportModal = true
							var _this = this
							if (Object.values(res.data).length > 0) {
								Object.values(res.data).forEach(function(value) {
									_this.taskIds.push(value.taskId)
									_this.taskNames.push(value.taskName)
									if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
										let taskid =  _this.taskIds.slice(0,3)
										let taskname = _this.taskNames.slice(0,3)
										_this.taskIds = taskid
										_this.taskNames = taskname
										_this.remind = true
									}
								})
							}
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err)
					})
				} else if (name === 'PackageUsed') {
					//按套餐使用明细报表
					exportPackageUsed({
						corpId: row.corpId,
						corpName: row.corpName,
						month: row.statTime,
						userId: this.$store.state.user.userId
					}).then(res => {
						if (res && res.code == '0000') {
							this.exportModal = true
							var _this = this
							if (Object.values(res.data).length > 0) {
								Object.values(res.data).forEach(function(value) {
									_this.taskIds.push(value.taskId)
									_this.taskNames.push(value.taskName)
									if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
										let taskid =  _this.taskIds.slice(0,3)
										let taskname = _this.taskNames.slice(0,3)
										_this.taskIds = taskid
										_this.taskNames = taskname
										_this.remind = true
									}
								})
							}
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err)
					})
				} else if (name === 'flowUsed') {
					//按流量使用明细报表
					exportflowUsed({
						corpId: row.corpId,
						corpName: row.corpName,
						month: row.statTime,
						userId: this.$store.state.user.userId
					}).then(res => {
						if (res && res.code == '0000') {
							this.exportModal = true
							var _this = this
							if (Object.values(res.data).length > 0) {
								Object.values(res.data).forEach(function(value) {
									_this.taskIds.push(value.taskId)
									_this.taskNames.push(value.taskName)
									if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
										let taskid =  _this.taskIds.slice(0,3)
										let taskname = _this.taskNames.slice(0,3)
										_this.taskIds = taskid
										_this.taskNames = taskname
										_this.remind = true
									}
								})
							}
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err)
					})
				} else if (name === 'Invoice') {
					//Invoice导出
					exportInvoice({
						corpName: row.corpName,
						invoicePath: row.invoicePath,
						month: row.statTime,
					}).then(res => {
						if (res && res.code == '0000') {
							this.exportModal = true
							var _this = this
							if (Object.values(res.data).length > 0) {
								Object.values(res.data).forEach(function(value) {
									_this.taskIds.push(value.taskId)
									_this.taskNames.push(value.taskName)
									if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
										let taskid =  _this.taskIds.slice(0,3)
										let taskname = _this.taskNames.slice(0,3)
										_this.taskIds = taskid
										_this.taskNames = taskname
										_this.remind = true
									}
								})
							}
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err)
					})
				} else if (name === 'taxation') {
					//税费文件导出
					exportTaxation({
						corpName: row.corpName,
						taxationPath: row.taxationPath,
						month: row.statTime,
						userId: this.$store.state.user.userId
					}).then(res => {
						if (res && res.code == '0000') {
							this.exportModal = true
							var _this = this
							if (Object.values(res.data).length > 0) {
								Object.values(res.data).forEach(function(value) {
									_this.taskIds.push(value.taskId)
									_this.taskNames.push(value.taskName)
									if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
										let taskid =  _this.taskIds.slice(0,3)
										let taskname = _this.taskNames.slice(0,3)
										_this.taskIds = taskid
										_this.taskNames = taskname
										_this.remind = true
									}
								})
							}
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err)
					})
				}

			},
			/**
			 * 税费文件上传
			 */
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			handleBeforeUpload(file) {
				// if (!/^.+(\.pdf)$/.test(file.name)) {
				// 	this.$Notice.warning({
				// 		title: "文件格式不正确",
				// 		desc:  '文件 ' + file.name + ' 格式不正确，请上传pdf格式文件。'
				// 	})
				// } else {
				this.file = file
				// }
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			/**
			 * 客户账单文件上传
			 */
			CustomerfileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			CustomerhandleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			CustomerhandleBeforeUpload(file) {
				// if (!/^.+(\.pdf)$/.test(file.name)) {
				// 	this.$Notice.warning({
				// 		title: "文件格式不正确",
				// 		desc:  '文件 ' + file.name + ' 格式不正确，请上传pdf格式文件。'
				// 	})
				// } else {
				this.Customerfile = file
				// }
				return false
			},
			CustomerfileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			removeFile() {
				this.file = ''
			},
			removeCustomerfile() {
				this.Customerfile = ''
			},
			/**
			 * 生成发票
			 */
			createInvoice: function() {
        let vaild = this.$refs.dataForm.validateInvoiceForm();
				if (this.desc) {
					if (vaild) {
            let invoiceType2;
            if (this.invoiceType) {
              if (this.rowData.accountingType == '4') {
                //账期类型为4，选择出账类型"流量"传2
                if (this.invoiceType == '1') {
                  invoiceType2= '1'
                } else if (this.invoiceType == '2') {
                  invoiceType2= '2'
                } else {
                  invoiceType2= this.rowData.accountingType
                }
              } else if (this.rowData.accountingType == '6') {
                //账期类型为6，选择出账类型"流量"传3
                if (this.invoiceType == '1') {
                  invoiceType2= '1'
                } else if (this.invoiceType == '2') {
                  invoiceType2= '3'
                } else {
                  invoiceType2= this.rowData.accountingType
                }
              } else if (this.rowData.accountingType == '7') {
                //账期类型为7，选择出账类型"流量"传5
                if (this.invoiceType == '1') {
                  invoiceType2= '1'
                } else if (this.invoiceType == '2') {
                  invoiceType2= '5'
                } else {
                  invoiceType2= this.rowData.accountingType
                }
              } else {
                //其它账期类型则根据选择的出账类型传
                if (this.invoiceType == '1') {
                  invoiceType2= '1'
                } else if (this.invoiceType == '2') {
                  invoiceType2= '2'
                } else {
                  invoiceType2= this.rowData.accountingType
                }
              }
            } else {
              //如果没选出账类型,则传对应的账期类型则可
              invoiceType2 = this.rowData.accountingType
            }
						this.invoiceInfo.InvoiceDesc = this.desc
						this.Invoiceloading = true
						//TODO 生成发票参数设置
						createInvoice({
							companyName: this.invoiceInfo.AccountNo,
							address: this.invoiceInfo.address,
							id: this.id,
							invoiceDesc: this.desc,
							invoiceNameDesc:this.invoiceForm[0].invoiceNameDesc,
							billingPeriod:this.invoiceForm[0].billingPeriod,
					    usageInvoiceNameDesc:this.invoiceForm[1].invoiceNameDesc,
					    usageBillingPeriod:this.invoiceForm[1].billingPeriod,
					    imsiInvoiceNameDesc:this.invoiceForm[2].invoiceNameDesc,
					    imsiBillingPeriod:this.invoiceForm[2].billingPeriod,
							type: 2,
					    invoiceType: this.invoiceType ? this.invoiceType : undefined,
              billType: this.billType,
              "channelExportDTO": {
                "corpId": this.rowData.corpId,
                "beginMonth": this.searchBeginTime,
                "endMonth": this.searchEndTime,
                "cooperationMode": this.rowData.accountingType,
                "corpName": this.rowData.corpName,
                "currencyCode": this.rowData.currency,
                "billType": invoiceType2,
                "id": this.rowData.id,
                "statTime": this.rowData.statTime,
                "dateStart": this.rowData.svcStartTime,
                "dateEnd": this.rowData.svcEndTime,
                "userId": this.$store.state.user.userId,
                "type": this.rowData.accountingType == '2' ? '1' : this.rowData.accountingType == '3' ? '2' : this.rowData.accountingType == '4' ? 1 :
                  this.rowData.accountingType == '5' ? '3': this.rowData.accountingType == '6' ? '2' : this.rowData.accountingType == '7' ? '3' : '',
              }
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '发票生成成功'
								})
								this.invoice_model = false
								this.Invoiceloading = false
								//刷新页面数据
								this.loadByPage(this.page)
							} else {
								throw res
							}
						}).catch((err) => {
							this.Invoiceloading = false
							console.log(err)
						})
					}
				} else {
					this.$Message.error("发票说明不能为空");
				}
			},
			test(str) {
				var re = /(\d{1,3})(?=(\d{3})+(?:$|\.))/g
				return (str + '').replace(re, '$1,')
			},
      formatNumber(num) {
        // 首先，将数字除以100并保留两位小数
        const result = (num / 100).toFixed(2);
          const formatted = new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 2, // 确保总是显示两位小数
            maximumFractionDigits: 2, // 最多显示两位小数
            useGrouping: true        // 启用千位分隔符
        }).format(parseFloat(result));
        return formatted;
      },
			/**
			 * 生成票号
			 */
			createInvoiceNo: function(row, needNewNo, invoiceTypeCopy, billType) {
        this.invoiceType = invoiceTypeCopy
        this.billType = billType
				createInvoiceNo(row.id, row.corpId, 2).then(res => {
					if (res && res.code == '0000') {
						this.address = res.data.address
						if (needNewNo === 2) {
							this.InvoiceNo = res.data.invoiceNo
						} else {
							this.InvoiceNo = row.invoiceNo
						}
						let currency = row.currency == '156' ? "CNY" : row.currency == '840' ? "USD" : row.currency == '344' ? "HKD" :
							'';
						let saleIncome = this.test(parseFloat(math.divide(math.bignumber(row.saleIncomeOrigin), 100).toFixed(2)).toString())
						let realIncome = this.test(parseFloat(math.divide(math.bignumber(row.realIncomeOrigin), 100).toFixed(2)).toString())
						let taxation = this.formatNumber(row.taxation)
						let AmountTax = this.test(parseFloat(math.divide(math.bignumber(row.realIncomeOrigin - row.taxation), 100).toFixed(2)).toString())
            // 代销、流量、合并出账、不拆分出账
            let distributionAmount;
            let usageAmount;
            let imsiAmount;
            let TotalAmount;

            // 判断accountingType 是否返回 4|6|7
            if (row.accountingType == '4' || row.accountingType == '6' || row.accountingType == '7') {
              if (invoiceTypeCopy == '1') {
                // 代销
                if (row.authStatus == '3') {
                  // 待审批
                  distributionAmount = this.formatNumber(row.directIncome + row.indirectIncome + row.arrears - row.totleRemuneration - row.sellRebateUsedAmount)
                  usageAmount = this.formatNumber(0)
                  imsiAmount = this.formatNumber(row.sellImsiAmount)
                  TotalAmount = this.formatNumber(row.directIncome + row.indirectIncome + row.arrears - row.totleRemuneration + row.sellImsiAmount - row.sellRebateUsedAmount)
                } else {
                  distributionAmount = this.formatNumber(row.directIncome + row.accountAdjustment + row.indirectIncome + row.arrears - row.totleRemuneration - row.sellRebateUsedAmount)
                  usageAmount = this.formatNumber(0)
                  imsiAmount = this.formatNumber(row.sellImsiAmount + row.sellImsiAdjustAmount)
                  TotalAmount = this.formatNumber(row.directIncome + row.accountAdjustment + row.indirectIncome + row.arrears - row.totleRemuneration + row.sellImsiAmount + row.sellImsiAdjustAmount - row.sellRebateUsedAmount)
                }
              } else if (invoiceTypeCopy == '2') {
                // 流量
                if (row.authStatus == '3') {
                  // 待审批
                  distributionAmount = this.formatNumber(0)
                  usageAmount = this.formatNumber(row.flowTotalAmount)
                  imsiAmount = this.formatNumber(row.a2zImsiAmount + row.resourceImsiAmount)
                  TotalAmount = this.formatNumber(row.flowTotalAmount + row.a2zImsiAmount + row.resourceImsiAmount)
                } else {
                  distributionAmount = this.formatNumber(0)
                  usageAmount = this.formatNumber(row.flowTotalAmount + row.flowAdjustAmount)
                  imsiAmount = this.formatNumber(row.a2zImsiAmount + row.resourceImsiAmount + row.a2zImsiAdjustAmount + row.resourceImsiAdjustAmount)
                  TotalAmount = this.formatNumber(row.flowTotalAmount + row.flowAdjustAmount + row.a2zImsiAmount + row.resourceImsiAmount + row.a2zImsiAdjustAmount + row.resourceImsiAdjustAmount)
                }
              } else {
                // 合并出账
                if (row.authStatus == '3') {
                  // 待审批
                  distributionAmount = this.formatNumber(row.directIncome + row.indirectIncome + row.arrears - row.totleRemuneration - row.sellRebateUsedAmount)
                  usageAmount = this.formatNumber(row.flowTotalAmount)
                  imsiAmount = this.formatNumber(row.imsiTotalAmount)
                  TotalAmount = this.formatNumber(row.directIncome + row.indirectIncome + row.arrears - row.totleRemuneration + row.flowTotalAmount + row.imsiTotalAmount)
                } else {
                  distributionAmount = this.formatNumber(row.directIncome + row.accountAdjustment + row.indirectIncome + row.arrears - row.totleRemuneration - row.sellRebateUsedAmount)
                  usageAmount = this.formatNumber(row.flowTotalAmount + row.flowAdjustAmount)
                  imsiAmount = this.formatNumber(row.imsiTotalAmount + row.imsiAdjustAmount)
                  TotalAmount = this.formatNumber(row.directIncome + row.accountAdjustment + row.indirectIncome + row.arrears - row.totleRemuneration + row.flowTotalAmount + row.flowAdjustAmount + row.imsiTotalAmount + row.imsiAdjustAmount - row.sellRebateUsedAmount)
                }
              }
            } else {
              // 不支持拆分
              if (row.authStatus == '3') {
                // 待审批
                distributionAmount = this.formatNumber(row.directIncome + row.indirectIncome + row.arrears - row.totleRemuneration - row.sellRebateUsedAmount)
                usageAmount = this.formatNumber(row.flowTotalAmount)
                imsiAmount = this.formatNumber(row.imsiTotalAmount)
                TotalAmount = this.formatNumber(row.directIncome + row.indirectIncome + row.arrears - row.totleRemuneration + row.flowTotalAmount + row.imsiTotalAmount - row.sellRebateUsedAmount)
              } else {
                distributionAmount = this.formatNumber(row.directIncome + row.accountAdjustment + row.indirectIncome + row.arrears - row.totleRemuneration - row.sellRebateUsedAmount)
                usageAmount = this.formatNumber(row.flowTotalAmount + row.flowAdjustAmount)
                imsiAmount = this.formatNumber(row.imsiTotalAmount + row.imsiAdjustAmount)
                TotalAmount = this.formatNumber(row.directIncome + row.accountAdjustment + row.indirectIncome + row.arrears - row.totleRemuneration + row.flowTotalAmount + row.flowAdjustAmount + row.imsiTotalAmount + row.imsiAdjustAmount - row.sellRebateUsedAmount)
              }
            }

            let distributionDescription = res.data.invoiceNameDesc.replace('%s', 'Distribution')
            let usageDescription = res.data.invoiceNameDesc.replace('%s', 'Usage')
            let imsiDescription = res.data.invoiceNameDesc.replace('%s', 'IMSI')

            let distributionPeriod = res.data.billingPeriod
            let usagePeriod = res.data.billingPeriod
            let imsiPeriod = res.data.billingPeriod

            this.invoiceInfo = {
							AccountNo: res.data.companyName,
							address: this.address,
							AmountDue: currency + " " + TotalAmount,
							InvoiceNo: this.InvoiceNo,
							InvoiceDate: res.data.invoiceDate,
							FileTitle: 'INVOICE',
							InvoiceDesc: row.invoiceDesc === undefined ? this.default : row.invoiceDesc,
							AmountTax: currency + " " + realIncome,
							Tax: taxation,
							TotalAmount: TotalAmount,
              currencyCode: currency,
							data: [
                {
                  description: distributionDescription, //账单名称
                  billingPeriod: distributionPeriod,
                  qty: '1',
                  unitPrice: distributionAmount,
                  amount: distributionAmount
                },
                {
                	description: usageDescription, //账单名称
                	billingPeriod: usagePeriod,
                	qty: '1',
                	unitPrice: usageAmount,
                	amount: usageAmount
                },
                {
                	description: imsiDescription, //账单名称
                	billingPeriod: imsiPeriod,
                	qty: '1',
                	unitPrice: imsiAmount,
                	amount: imsiAmount
                }
              ]
						}
						this.invoiceForm = [
              {
                invoiceNameDesc: distributionDescription, //账单名称
                billingPeriod: distributionPeriod,
              },
              {
                invoiceNameDesc: usageDescription, //账单名称
                billingPeriod: usagePeriod,
              },
              {
                invoiceNameDesc: imsiDescription,
                billingPeriod: imsiPeriod,
              }
						]
            this.selectModeModal = false
            this.accountingList.accountingType = ""
            this.accountingLoading = false
						this.invoice_model = true
					} else {
            this.accountingLoading = false
						throw res
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			/**
			 * 生成发票预览退出
			 */
			cancelInvoice: function() {
				this.id = null
				this.invoice_model = false
				this.invoiceInfo = []
				this.$refs.dataForm.$refs.invoiceForm.resetFields()
			},
			showInvoiceView: function(row) {
        this.rowData = row
        this.id = row.id //设置选中行主键
        this.invoiceNoCopy = row.invoiceNo
        // 判断accountingType 是否返回 4|6|7 并且没有发票路径的时候要先选择出账模式 accountingType
        if (row.accountingType == '4' || row.accountingType == '6' || row.accountingType == '7') {
          this.selectModeModal = true
          this.row = JSON.parse(JSON.stringify(row))
        } else {
          //判断发票是否是实际发票
          if (row.invoiceNo == null || (row.invoiceNo != null && row.invoiceNo.substr(0, 2) != "IN")) {
          	//非正式发票，需要调用发票编号生成接口
          	this.createInvoiceNo(row, 2, null, row.accountingType)
          } else {
          	this.createInvoiceNo(row, 1, null , row.accountingType)
          }
        }
			},
			//获取发票说明
			getdesc(data) {
				this.desc = data
			},
      //计算需要合并的单元格
			getSpanData(data) {
				var t = this
				var pos = 0
				t.spanData = []
				data.forEach(function(item, index) {
					if (index === 0) {
						t.spanData.push(1)
						pos = 0
					} else {
						if ((data[index].salesChannel === data[index - 1].salesChannel) && (data[index].statTime === data[index - 1].statTime)) {
							t.spanData[pos] += 1
							t.spanData.push(0)
						} else {
							t.spanData.push(1)
							pos = index
						}
					}
				})
			},
			handleSpan({
				row,
				column,
				rowIndex,
				columnIndex
			}) {
				if ([0, 1, 8].includes(columnIndex)) {
					const _row = this.spanData[rowIndex]
					const _col = _row > 0 ? 1 : 0
					return {
						rowspan: _row,
						colspan: _col
					}
				}
			},
			// 获取开始月份
			handleChangeBeginMonth(month) {
				// this.isSearch = false
				this.searchBeginTime = month;
			},
			// 获取结束月份
			handleChangeEndMonth(month) {
				// this.isSearch = false
				this.searchEndTime = month;
			},
			// 获取结算周期
      handleChangeStatDate(month) {
				// this.isSearch = false
				this.searchStatDate = month;
			},
      handleStatDate(month) {
        this.addStatDate = month;
      },
      //获取资源供应商
      getSupplierShortenCode(type, data) {
        getSupplierShortenCode().then(res => {
        	if (res && res.code == '0000') {
            this.supplierShortenList = res.data
        	} else {
        		throw res
        	}
        }).catch((err) => {
        	console.log(err)
        }).finally(() => {
        })
      },
      //切换收入类型
			changeType(type) {
        this.total=0
				this.type = type
				if (type === 1) {
					this.corpLists = this.onlineCorpList
					this.isSearch = true
					this.data = []
				}
				if (type === 2) {
					getCorpList({ //获取渠道商列表
						"type": 1,
					}).then(res => {
						if (res && res.code == '0000') {
							// 当公司名称为空时，去掉该对象
							const filteredData  = res.data.filter(item => item.companyName !== null && item.companyName !== undefined && item.companyName !== '');
							this.corpLists = filteredData
						} else {
							throw res
						}
					}).catch((err) => {

					}).finally(() => {

					})
					this.$refs['form'].validate((valid) => {
						if (valid) {
							this.isSearch = true
							this.data = []
							// this.getTableData()
						}
					})
				}
				if (type === 3) {
					getCorpList({ //获取其他客户列表
						"types": [3, 4, 7, 8]
					}).then(res => {
						if (res && res.code == '0000') {
							this.corpLists = res.data;
						} else {
							throw res
						}
					}).catch((err) => {

					}).finally(() => {

					})
					this.$refs['form'].validate((valid) => {
						if (valid) {
							this.isSearch = true
							this.data = []
							// this.getTableData()
						}
					})
				}
        if (type === 4) {
          getCustomerList().then(res => {
          	if (res && res.code == '0000') {
              this.customerList = res.data
          	} else {
          		throw res
          	}
          }).catch((err) => {
          	console.log(err)
          }).finally(() => {
          })
        	this.$refs['form'].validate((valid) => {
        		if (valid) {
        			this.isSearch = true
        			this.data = []
        		}
        	})
        }
        if (type === 5) {
          this.getSupplierShortenCode()
        	this.$refs['form'].validate((valid) => {
        		if (valid) {
        			this.isSearch = true
        			this.data = []
        		}
        	})
        }
        if(type===6){
          this.$refs['form'].validate((valid) => {
            if (valid) {
              this.isSearch = true
              this.data = []
            }
          })
        }
			},
      isArrayOrEmptyObject(value) {
        return (Array.isArray(value) && value.length === 0) ||
               (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0);
      },
      isDataToSendEmpty(dataToSend) {
        return this.isArrayOrEmptyObject(dataToSend.createInvoiceVos) && this.isArrayOrEmptyObject(dataToSend.inComeIds);
      },
      //输入IMSI费
      inputImsi(row) {
        this.imsiOP.id = row.id
        this.imsiModal = true
      },
      imsiConfirm() {
        this.$refs["imsiOP"].validate((valid) => {
        	if (valid) {
            this.imsiLoading = true
            // 金额单位转换：元 -> 分
            const resultData = {
              ...this.imsiOP,
              amount: Math.round(this.imsiOP.amount * 100), // 金额转换为分单位
            };
        		updateImsi(resultData).then(res => {
        			if (res.code === "0000") {
                this.$Notice.success({
                	title: "操作提示",
                	desc: "操作成功！",
                });
        				this.imsiModal = false
        				this.getTableData(1)
        				this.cancel1()
        			}
        		}).catch((err) => {
            }).finally(() => {
              this.imsiLoading = false
            })
        	}
        })
      },
      // 处理表格选择变化
      handleSelectionChange(selection) {
        // 获取当前页面的数据ID集合
        const currentPageIds = new Set(this.data.map(item => item.id));

        // 保留不在当前页面的已选数据
        const otherPagesSelection = this.selectedBlankCardRows.filter(row => !currentPageIds.has(row.id));

        // 合并当前页面新的选中数据
        this.selectedBlankCardRows = [...otherPagesSelection, ...selection];

        // 更新选中ID集合
        this.selectedBlankCardIds = new Set(this.selectedBlankCardRows.map(row => row.id));
      },

      // 取消选择
      handleSelectionCancel(selection, row) {
          this.selectedBlankCardRows = this.selectedBlankCardRows.filter(item => item.id !== row.id);
          this.selectedBlankCardIds.delete(row.id);
      },

      // 全选/取消全选
      handleSelectAll(selection) {
          // 获取当前页面的数据ID集合
          const currentPageIds = new Set(this.data.map(item => item.id));

          // 保留不在当前页面的已选数据
          const otherPagesSelection = this.selectedBlankCardRows.filter(row => !currentPageIds.has(row.id));

          if (selection.length === 0) {
              // 取消全选：只保留其他页面的数据
              this.selectedBlankCardRows = otherPagesSelection;
          } else {
              // 全选：合并其他页面的数据和当前页面的选中数据
              this.selectedBlankCardRows = [...otherPagesSelection, ...selection];
          }

          // 更新选中ID集合
          this.selectedBlankCardIds = new Set(this.selectedBlankCardRows.map(row => row.id));
      },

      // 批量下载白卡订单Invoice
      batchDownloadBlankCardInvoice() {
        if (this.selectedBlankCardRows.length === 0) {
          this.$Message.warning('请至少选择一条记录');
          return;
        }
				let that = this;
        const ids = this.selectedBlankCardRows.map(row => row.orderId);

				this.$Modal.confirm({
					title: '确认批量下载Invoice？',
					content: '是否确认下载所选记录的Invoice？',
					onOk: () => {
						this.batchDownloadLoading = true;
						// searchcorpid({
						// 	userName: this.$store.state.user.userName,
						// }).then(res => {
						// 	if (res.code == '0000') {
								let corpId = this.$store.state.user.userId;
								batchDownloadWhiteCardInvoice(ids,corpId).then(res => {
									this.batchDownloadLoading = false;
									if(res.code == '0000') {
										this.exportModal = true
										var _this = this
										if (res.data && res.data.taskId && res.data.taskName) {
											_this.taskIds.push(res.data.taskId)
											_this.taskNames.push(res.data.taskName)
											if(_this.taskIds.length > 3 || _this.taskNames.length > 3){
												let taskid =  _this.taskIds.slice(0,3)
												let taskname = _this.taskNames.slice(0,3)
												_this.taskIds = taskid
												_this.taskNames = taskname
												_this.remind = true
											}
										}
									}else{
										that.$Message.error(res.msg);
									}
								}).catch(err => {
									that.batchDownloadLoading = false
									that.$Message.error('下载失败');
								});
						// 	}
						// }).catch(err => {
						// 	that.batchDownloadLoading = false
						// });
					}
				});
			},

      // 单个下载Invoice
      async downloadInvoice(row) {
        // 如果正在下载中，直接返回
        if (this.downloadingMap[row.orderId]) {
          return;
        }

        // 设置下载状态
        this.$set(this.downloadingMap, row.orderId, true);
        try {
          await downloadFile({
            id: row.orderId,
            type: '1'
          }).then(res => {
            const content = res.data;
            const fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1]));

            if ('download' in document.createElement('a')) {
              const link = document.createElement('a');
              const url = URL.createObjectURL(content);
              link.download = fileName;
              link.href = url;
              link.click();
              URL.revokeObjectURL(url);
            } else {
              navigator.msSaveBlob(content, fileName);
            }
          }).catch(err => {
            this.$Message.error('下载失败');
          });
        } finally {
          // 无论成功失败，都重置下载状态
          this.$set(this.downloadingMap, row.orderId, false);
        }
      },
    },
	};
</script>

<style scoped>
	#space {
		/* height: 30px;
		line-height: 30px; */
		font-size: 12px;
		white-space: pre-line;
		list-style: none;
	}
  .task-name {
    display: inline-block; /* 或者 block，取决于你的布局需求 */
    width: 300px; /* 根据需要设置合适的宽度 */
    /* white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; */
    word-break: break-all;
    padding: 5px; /* 内边距 */
    margin-bottom: 10px; /* 外边距 */
  }

  /* 添加新的样式 */
  .special-button {
    width: 88px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
		margin:0 5px 5px 0;
  }
</style>
