<template>
  <!-- 线上收入账单 -->
  <div>
	<!-- <Button v-has="'search'"  style="margin-bottom: 10px;" type="primary" icon="md-search" size="large" @click="search('form')" >搜索</Button>&nbsp;&nbsp; -->
    <!-- 表格 -->
    <h3>线上收入</h3>
    <Table no-data-text border highlight-row :columns="columns" :data="data" style="width: 100%; margin-top: 20px;" :loading="loading" :span-method="handleSpan" >
      <template slot-scope="{ row, index }" slot="action">
        <Button type="info" ghost size="small" style="margin-right: 5px" v-has="'online_sum_export'" @click="exportCommon(row,'sum')">线上收入账单汇总</Button>
        <Button type="success" ghost size="small" v-has="'online_detail_export'" @click="exportCommon(row,'detail')">线上收入报表明细</Button>
      </template>
    </Table>
    <Page :total="total" :page-size="pageSize" :current.sync="page" show-sizer show-total show-elevator @on-change="loadByPage" @on-page-size-change="loadByPageSize"
      style="margin: 15px 0;" />
    <!-- 导出提示 -->
    <Modal  v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
      <div style="align-items: center;justify-content:center;display: flex;">
    	  <Form  label-position="left"  :label-width="150" style=" align-items: center;justify-content:center;margin-bottom: 30px;" >
          <h1 style="text-align: center;margin-bottom: 20px;">导出提示</h1>
          <FormItem label="你本次导出任务ID为:" style="margin-bottom: 15px;">
            <span style="font-weight: bold;">{{taskId}}</span>
          </FormItem>
          <FormItem label="你本次导出的文件名为:" style="margin-bottom: 20px;">
            <span style="font-weight: bold;">{{taskName}}</span>
          </FormItem >
    			  <span style="text-align: left;">请前往<span style="font-weight: bold;">下载管理-下载列表</span>查看及下载。</span>
    	  </Form>
      </div>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
        <Button @click="cancelModal">取消</Button>
        <Button type="primary" @click="Goto">立即前往</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import {
  getOnlineIncomeList,
  exportTotalTask,
  exportDetailTask
} from "@/api/finance/online";
const math = require('mathjs')
export default {
  props: {
   form: {
     incomeType: "",
     beginMonth: "",
     endMonth: "",
     corp: '',
   },
   searchBeginTime:'',
   searchEndTime:'',
  },
  data() {
    return {
      total: 0,
      pageSize: 10,
      page: 1,
      searchObj: {},
      columns: [
        {
          title: "销售渠道",
          key: "salesChannel",
          align: "center",
          render: (h, params) => {
            const obj = {
              102: "API",
              103: "官网（H5）",
              104: "北京移动",
              105: "批量售卖",
              106: "推广活动",
              110: "测试渠道",
              111: "合作发卡",
              112: "后付费发卡",
              113: "WEB",
              114: "流量池WEB",
            };
            return h(
              "span",
               obj[params.row.salesChannel]
            );
          },
          tooltip: true,
		  minWidth: 200,
          tooltipMaxWidth: 2000,
        },
        {
          title: "结算月份",
          key: "statTime",
          align: "center",
          tooltip: true,
		  minWidth: 200,
          tooltipMaxWidth: 2000,
        },
        {
          title: "套餐数量",
          key: "packageNum",
          align: "center",
          tooltip: true,
		  minWidth: 200,
          tooltipMaxWidth: 2000,
        },
        {
          title: "套餐收入金额",
          key: "amount",
          align: "center",
          tooltip: true,
		  minWidth: 200,
          tooltipMaxWidth: 2000,
		  render: (h, params) => {
		  	const row = params.row;
		  	const text = parseFloat(math.divide(math.bignumber(row.amount), 100).toFixed(2)).toString()
		  	return h('label', text);
		  }
        },
        {
          title: "币种",
          key: "currency",
          align: "center",
		  minWidth: 200,
          render: (h, params) => {
          	const row = params.row;
          	const text = row.currency == '156' ? "CNY" :row.currency == '840' ? "USD" :row.currency == '344' ? "HKD": '';
          	return h('label', text);
          }
        },
        {
          title: '文件下载',
          slot: 'action',
          minWidth: 200,
          align: 'center'
        },
      ],
      data:[],
      spanData: [],
      loading: false,
      downLoad: false,
      taskName: '',
      taskId: '',
      exportModal: false
    };
  },
  created(){
  },
  mounted() {
    this.searchObj = this.form
    this.getTableData()
  },
  methods: {
    getTableData(){
		var t = this
      getOnlineIncomeList({
        beginMonth: this.searchBeginTime,
        endMonth: this.searchEndTime,
        salesChannel: this.searchObj.corpId,
        pageNum: this.page,
        pageSize: this.pageSize
      }).then(res => {
        if (res.code === "0000") {
          this.total = res.count;
		  t.data=res.data
		  this.getSpanData(t.data)
        }
      });
    },
    exportCommon(row, type){
      if (type === 'sum') { //下载汇总文件
        //TODO 导出请求
		exportTotalTask({
		  month: row.statTime,
		  salesChannel: row.salesChannel,
		}).then(res => {
		  if (res.code === "0000") {
		    if(res.data.taskId != '' && res.data.taskName != ''){//导出弹框提示
		      this.exportModal = true
			  this.taskId=res.data.taskId
			  this.taskName=res.data.taskName
		    }else{
		      this.$Message.error('获取下载任务失败');
		    }
		  }
		});
      }
      if (type === 'detail') { //下载明细文件
        //TODO 导出请求
		exportDetailTask({
		  month: row.statTime,
		  salesChannel: row.salesChannel,
		}).then(res => {
		  if (res.code === "0000") {
		    if(res.data.taskId != '' && res.data.taskName != ''){//导出弹框提示
		      this.exportModal = true
			  this.taskId=res.data.taskId
			  this.taskName=res.data.taskName
		    }else{
		      this.$Message.error('获取下载任务失败');
		    }
		  }
		});
      }
      
    },
	search(){
		this.getTableData()
	},
    loadByPage(page){
      this.page = page
      this.getTableData(page,this.pageSize)
    },
    loadByPageSize(pageSize){
      this.pageSize = pageSize
      this.getTableData(this.page,pageSize)
    },
    cancelModal() {
    	this.exportModal=false
    },
    Goto(){
    	this.$router.push({
    	  path: '/taskList',
    	  query: {
    		taskId: encodeURIComponent(this.taskId),
    		fileName:encodeURIComponent(this.taskName),
    	  }
    	})
      this.exportModal=false
    },
    //计算需要合并的单元格
    getSpanData(data){
      var t = this
      var pos = 0
      t.spanData = []
      data.forEach(function(item,index){
        if(index === 0){
          t.spanData.push(1)
          t.pos = 0
        }else{
          if((data[index].salesChannel === data[index-1].salesChannel) && (data[index].statTime === data[index-1].statTime)){
            t.spanData[pos] += 1
            t.spanData.push(0)
          }else{
            t.spanData.push(1)
            t.pos = 1
          }
        }
      })
    },
    handleSpan ({ row, column, rowIndex, columnIndex }) {
        if([0,1,5].includes(columnIndex)){
          const _row = this.spanData[rowIndex]
          const _col = _row > 0 ? 1:0
          return{
            rowspan: _row,
            colspan: _col
          }
        }
    }
  },
};
</script>

<style></style>
