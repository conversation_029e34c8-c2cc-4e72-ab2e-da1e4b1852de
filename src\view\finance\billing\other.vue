<template>
	<!-- 其他客户收入账单 -->
	<div>
		<!-- <Button v-has="'search'"  style="margin-bottom: 10px;" type="primary" icon="md-search" size="large" @click="search('form')" >搜索</Button>&nbsp;&nbsp; -->
		<!-- 表格 -->
		<h3>其他客户收入</h3>
		<!-- 导出按钮 -->
		<Button  type="success" :loading="downloading" icon="ios-download" @click="downloadFile()"  style="margin-right: 10px;margin-top: 10px;">导出其他客户收入汇总表</Button>
		<Table :columns="columns" :data="data" style="width: 100%; margin-top: 20px;" :ellipsis="true" :loading="loading" >
		  <template slot-scope="{ row, index }" slot="download">
		    <Button type="primary" ghost  style="margin-right: 5px;margin-top: 5px;" v-has="'online_sum_export'" @click="exportCommon(row,'Package')">按套餐结算汇总报表</Button>
		    <Button type="success" ghost   style="margin-right: 5px;margin-top: 5px;" v-has="'online_detail_export'" @click="exportCommon(row,'flowSettle')">按流量结算汇总报表</Button>
		    <Button type="warning" ghost   style="margin-right: 5px;margin-top: 5px;" v-has="'online_detail_export'" @click="exportCommon(row,'PackageUsed')">按套餐使用明细报表</Button>
			<Button type="info" ghost  style="margin-right: 5px;margin-top: 5px;" v-has="'online_detail_export'" @click="exportCommon(row,'flowUsed')">按流量使用明细报表</Button>
			<Button type="error" ghost  style="margin-right: 5px;margin-top: 5px;" v-has="'online_detail_export'" @click="exportCommon(row,'Invoice')">Invoice</Button>
		    <Button type="success" ghost  style="margin-right: 5px;margin-top: 5px;" v-has="'online_detail_export'" @click="exportCommon(row,'taxation')">税费文件</Button>
		  </template>
		  <template slot-scope="{ row, index }" slot="update">
		    <Button v-if="row.isUpdate==='1'" type="primary" size="small" style="margin-right: 5px" v-has="'online_sum_export'" @click="update(row)">点击修改</Button>
		    <Button v-else type="primary" disabled size="small" style="margin-right: 5px" v-has="'online_sum_export'" @click="update(row)">点击修改</Button>
			<Button type="success" size="small" style="margin-right: 5px" v-has="'online_detail_export'" @click="showInvoiceView(row)">生成Invoice</Button>
		  </template>
		</Table>
		<Page :total="total" :page-size="pageSize" :current.sync="page" show-sizer show-total show-elevator @on-change="loadByPage" @on-page-size-change="loadByPageSize"
		  style="margin: 15px 0;" />
		<!-- 修改界面 -->
		<Modal title="编辑" v-model="updateModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
			    <Form  ref="corpList"  :model="corpList" label-position="left" :rules="rule" :label-width="150" style=" align-items: center;justify-content:center;" >
					<FormItem label="客户名称:" style="font-size: large;">
					  <span style="font-weight: bold;">{{corpList.corpName}}</span>
					</FormItem>
					<FormItem label="服务开始时间:" style="font-size: large;" prop="svcStartTime">
					  <DatePicker format="yyyyMMdd" v-model="corpList.svcStartTime" @on-change="startTimeDateChange" type="date"
					    placeholder="选择时间" ></DatePicker>
					</FormItem>
					<FormItem label="服务结束时间:" style="font-size: large;" prop="svcEndTime">
					  <DatePicker format="yyyyMMdd" v-model="corpList.svcEndTime" @on-change="endTimeDateChange" type="date"
					    placeholder="选择时间" ></DatePicker>
					</FormItem>
					<FormItem label="套餐收入:" style="font-size: large;" prop="packageIncome">
					  <Input v-model='corpList.packageIncome' placeholder="请输入套餐收入" :clearable="true"  style="width: 190px;margin-right: 10px;" >
						<span slot="append">元</span>
					  </Input>
					</FormItem>
					<FormItem label="用量收入:" style="font-size: large;" prop="useIncome">
					  <Input v-model='corpList.useIncome' placeholder="请输入用量收入" :clearable="true"  style="width: 190px;margin-right: 10px;" >
						<span slot="append">元</span>
					  </Input>
					</FormItem>
					<FormItem label="税费:" style="font-size: large;" prop="taxation">
					  <Input v-model='corpList.taxation' placeholder="请输入税费" :clearable="true"  style="width: 190px;margin-right: 10px;" >
						<span slot="append">元</span>
					  </Input>
					</FormItem>
					<FormItem label="上传税费文件:" style="font-size: large;">
						<Upload   :action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
						 :on-progress="fileUploading" >
							<Button icon="ios-cloud-upload-outline">点击上传</Button>
						</Upload>
						<ul class="ivu-upload-list" v-if="file" style="width: 500px;">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />{{file.name}}</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
							</li>
						</ul>
					</FormItem>
			    </Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
			  <Button @click="cancelModal">返回</Button>
			  <Button type="primary" @click="confirm">确定</Button>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal  v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
		  <div style="align-items: center;justify-content:center;display: flex;">
			  <Form  label-position="left"  :label-width="150" style=" align-items: center;justify-content:center;" >
				  <h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
				  <FormItem label="你本次导出任务ID为:" style="font-size: large;">
					<span style="font-weight: bold;">{{taskId}}</span>
				  </FormItem>
				  <FormItem label="你本次导出的文件名为:" style="font-size: large;">
					<span style="font-weight: bold;">{{taskName}}</span>
				  </FormItem>
				  <span style="text-align: left;margin-bottom: 10px;">请前往<span style="font-weight: bold;">下载管理-下载列表</span>查看及下载。</span>
				</Form>
		  </div>
		  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
		    <Button @click="cancelModal">取消</Button>
		    <Button type="primary" @click="Goto">立即前往</Button>
		  </div>
		</Modal>
		<!-- 生成发票 -->
		<Modal v-model="invoice_model" title="生成发票预览" @on-ok="createInvoice" @on-cancel="cancelInvoice" width="800px" :styles="{top: '10px'}">
		  <Card width="750px">
		    <invoiceTemplate :AccountNo="invoiceInfo.AccountNo" :address="invoiceInfo.address" :AmountDue="invoiceInfo.AmountDue"
		    :InvoiceNo="invoiceInfo.InvoiceNo" :InvoiceDate="invoiceInfo.InvoiceDate" :FileTitle="invoiceInfo.FileTitle"
		     :InvoiceDesc="invoiceInfo.InvoiceDesc" :AmountTax="invoiceInfo.AmountTax"
			 :Tax="invoiceInfo.Tax" :TotalAmount="invoiceInfo.TotalAmount"
			 :columns="invoiceColumns" :data="invoiceInfo.data" @InvoiceDesc='getdesc'/>
		  </Card>
		  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
		    <Button @click="cancelInvoice">取消</Button>
		    <Button type="primary" @click="createInvoice">生成Invoice</Button>
		  </div>
		</Modal>
	</div>
</template>

<script>
	import {
	  CustomerPage,
	  queryCustomer,
	  UpdateCustomer,
	  exportflowSettle,
	  exportflowUsed,
	  // exportInvoice,
	  exportPackage,
	  exportPackageUsed,
	  exportSummaryFile,
	  exportTaxation
	} from "@/api/finance/other";
	import {
	  createInvoiceNo,
	  createInvoice
	} from "@/api/finance/corp";
	import invoiceTemplate from '@/components/invoice/invoiceTemp'
	const math = require('mathjs')
	export default {
	components: {
	  invoiceTemplate
	},
	  props: {
	    form: {
		  incomeType: "",
		  beginMonth: "",
		  endMonth: "",
		  corp: '',
	    },
		searchBeginTime:'',
		searchEndTime:'',
	  },
	  data() {
	    return {
			file:null,
			uploadUrl: '',
			total: 0,
			pageSize: 10,
			page: 1,
			searchObj: {},
            corpList:{},
			downloading:false,
			startTime:'',
			endTime:'',
			/**
			 * ---------------生成发票相关----------------
			 */
			id: null,   //选择行自增主键
			invoice_model: false,  //预览模态框
			invoiceInfo:{
			  AccountNo: '北京博新創億科技股份有限公司',
			  address: '北京市海淀区首都體育館南路6號3幢557室',
			  AmountDue:'CNY 1,360.00',
			  InvoiceNo:'IN-************-GDS',
			  InvoiceDate:'30-Mar-2021',
			  FileTitle:'INVOICE',
			  InvoiceDesc:null,
			  AmountTax:'',
			  Tax:'',
			  TotalAmount:'',
			  data: [{
			    description:'GDS-Sales Settlement-Mar2021',
			    billingPeriod:'25-Feb-2021 to 24-Mar-2021',
			    qty:'1',
			    unitPrice:'1,360.00',
			    amount:'1,360.00',
			  },
			  {
			    description:'Amount before Tax',
			    billingPeriod:null,
			    qty:null,
			    unitPrice:'CNY',
			    amount:'1,360.00',
			  },
			  {
			    description:'TAX',
			    billingPeriod:null,
			    qty:null,
			    unitPrice:'CNY',
			    amount:'1,360.00',
			  },
			  {
			    description:'Total Amount Due',
			    billingPeriod:null,
			    qty:null,
			    unitPrice:'CNY',
			    amount:'1,360.00',
			  }]
			},
			invoiceColumns:[{
			      title: 'Description',
			      align: 'center',
			      width: 220,
			      key: 'description'
			    },
			    {
			      title: 'Billing Period',
			      align: 'center',
			      width: 220,
			      key: 'billingPeriod'
			    },
			    {
			      title: 'Qty',
			      align: 'center',
			      width: 60,
			      key: 'qty'
			    },
			    {
			      title: 'Unit Price',
			      align: 'center',
			      width: 115,
			      key: 'unitPrice'
			    },
			    {
			      title: 'amount',
			      align: 'center',
			      width: 116,
			      key: 'amount'
			    }
			  ],
			columns: [
			  {
			    title: "客户名称",
			    key: "corpName",
			    align: "center",
				minWidth: 200,
				fixed: 'left',
			  },
			  {
			    title: "结算月份",
			    key: "statTime",
			    align: "center",
				minWidth: 200,
			  },
			  {
			    title: "客户EBS编码",
			    key: "ebscode",
			    align: "center",
				minWidth: 200,
			  },
			  {
			    title: "Invoice no.",
			    key: "invoiceNo",
			    align: "center",
				minWidth: 200,
			  },
			  {
			    title: "币种",
			    key: "currency",
			    align: "center",
				minWidth: 200,
			    render: (h, params) => {
			    	const row = params.row;
			    	const text = row.currency == '156' ? "CNY" :row.currency == '840' ? "USD" :row.currency == '344' ? "HKD": '';
			    	return h('label', text);
			    }
			  },
			  {
			    title: "套餐收入",
			    key: "packageIncome",
			    align: "center",
				minWidth: 200,
				render: (h, params) => {
					const row = params.row;
					const text = parseFloat(math.divide(math.bignumber(row.packageIncome), 100).toFixed(2)).toString()
					return h('label', text);
				}
			  },
			  {
			    title: "用量收入",
			    key: "useIncome",
			    align: "center",
				minWidth: 200,
				render: (h, params) => {
					const row = params.row;
					const text = parseFloat(math.divide(math.bignumber(row.useIncome), 100).toFixed(2)).toString()
					return h('label', text);
				}
			  },
			  {
			    title: "总收入",
			    key: "totalIncome",
			    align: "center",
				minWidth: 200,
				render: (h, params) => {
					const row = params.row;
					const text = parseFloat(math.divide(math.bignumber(row.totalIncome), 100).toFixed(2)).toString()
					return h('label', text);
				}
			  },
			  {
			    title: "税费",
			    key: "taxation",
			    align: "center",
				minWidth: 200,
				render: (h, params) => {
					const row = params.row;
					const text = parseFloat(math.divide(math.bignumber(row.taxation), 100).toFixed(2)).toString()
					return h('label', text);
				}
			  },
			  {
			    title: "服务开始时间",
			    key: "svcStartTime",
			    align: "center",
				minWidth: 200,
			  },
			  {
			    title: "服务结束时间",
			    key: "svcEndTime",
			    align: "center",
				minWidth: 200,
			  },
			  {
			    title: '文件下载',
			    slot: 'download',
			    minWidth: 350,
			    align: 'center',
				fixed: 'right',
			  },
			  {
			    title: '编辑',
			    slot: 'update',
			    minWidth: 200,
			    align: 'center',
				fixed: 'right',
			  },
			],
			data: [],
			loading: false,
			downLoad: false,
			taskName: '测试',
			taskId: 'test',
			exportModal: false,
			updateModal:false,
			InvoiceNo:'',
			address:'',
			desc:'',
			rule: {
				packageIncome: [
					{ required: true, message: '请输入套餐收入', trigger: 'blur',
					}
				],
				useIncome: [
					{ required: true, message: '请输入用量收入', trigger: 'blur',
					}
				],
				taxation: [
					{ required: true, message: '请输入税费', trigger: 'blur',
					}
				],
				svcStartTime: [
					{ type: 'date',required: true, message: '请选择服务开始时间', trigger: 'blur',
					}
				],
			    svcEndTime: [
				    { type: 'date',required: true, message: '请选择服务结束时间', trigger: 'blur',
				    }
			    ],
			},
		}
		},
		created(){
		},
		mounted() {
		  this.searchObj = this.form
		  this.getTableData()
		},
		methods: {
			getTableData(){
			  CustomerPage({
			    beginMonth: this.searchBeginTime,
			    endMonth: this.searchEndTime,
			    corpId: this.searchObj.corpId,
			    corpName:this.searchObj.corpName,
			    pageNum: this.page,
			    pageSize: this.pageSize,
				type:[3,4,7,8]
			  }).then(res => {
			    if (res.code === "0000") {
			      this.data = res.data;
			      this.total = res.count;
			    }
			  });
			},
			search(){
				this.getTableData()
			},
			loadByPage(page){
			  this.page = page
			  this.getTableData(page,this.pageSize)
			},
			loadByPageSize(pageSize){
			  this.pageSize = pageSize
			  this.getTableData(this.page,pageSize)
			},
			cancelModal() {
				this.exportModal=false
				this.updateModal=false
				this.$refs.corpList.resetFields()
			},
			confirm(){
				this.$refs["corpList"].validate((valid) => {
				  if (valid) {
				    this.$Modal.info({
						title:'只能修改一次，是否确认提交修改？',
						onOk: () => {
							var formData = new FormData();
							formData.append("id",this.corpList.id)
							formData.append("packageIncome ",math.multiply(math.bignumber(this.corpList.packageIncome), 100).toString())
							formData.append("svcEndTime ",this.endTime)
							formData.append("svcStartTime ",this.startTime)
							formData.append("taxation",math.multiply(math.bignumber(this.corpList.taxation), 100).toString())
							formData.append("useIncome",math.multiply(math.bignumber(this.corpList.useIncome), 100).toString())
							if(this.file){
								formData.append("multipartFile",this.file)
							}
							UpdateCustomer(formData).then(res => {
							  if (res.code === "0000") {
							    this.updateModal=false
								this.getTableData()
							  }
							});
						}
				    })
				  }
				})

			},
			Goto(){
				this.$router.push({
				  path: '/taskList',
				  query: {
					taskId: encodeURIComponent(this.taskId),
					fileName:encodeURIComponent(this.taskName),
				  }
				})
			  this.exportModal=false
			},
			//导出其他客户收入汇总表
			downloadFile(){
				exportSummaryFile({
					beginMonth: this.searchBeginTime,
					endMonth: this.searchEndTime,
					corpId: this.searchObj.corpId,
					corpName:this.searchObj.corpName,
          userId:this.$store.state.user.userId
				}).then(res => {
				   if (res && res.code == '0000') {
				     this.exportModal=true
				     this.taskId=res.data.taskId
				     this.taskName=res.data.taskName
				    } else {
				      throw res
				    }
				  }).catch((err) => {
				       console.log(err)
				  })
			},
			/**
			 * 文件下载导出
			 */
			exportCommon(row,name){
				if(name==='Package'){
					//按套餐结算汇总报表
					exportPackage({
						corpId:row.corpId,
						corpName :row.corpName,
						month :row.statTime,
            userId:this.$store.state.user.userId
					}).then(res => {
					   if (res && res.code == '0000') {
					      this.exportModal=true
					      this.taskId=res.data.taskId
					      this.taskName=res.data.taskName
					    } else {
					      throw res
					    }
					  }).catch((err) => {
					       console.log(err)
					  })
				}else if(name==='flowSettle'){
					//按流量结算汇总报表
					exportflowSettle({
						corpId:row.corpId,
						corpName :row.corpName,
						month :row.statTime,
            userId: this.$store.state.user.userId
					}).then(res => {
					   if (res && res.code == '0000') {
					      this.exportModal=true
					      this.taskId=res.data.taskId
					      this.taskName=res.data.taskName
					    } else {
					      throw res
					    }
					  }).catch((err) => {
					       console.log(err)
					  })
				}else if(name==='PackageUsed'){
					//按套餐使用明细报表
					exportPackageUsed({
						corpId:row.corpId,
						corpName :row.corpName,
						month :row.statTime,
            userId:this.$store.state.user.userId
					}).then(res => {
					   if (res && res.code == '0000') {
					      this.exportModal=true
					      this.taskId=res.data.taskId
					      this.taskName=res.data.taskName
					    } else {
					      throw res
					    }
					  }).catch((err) => {
					       console.log(err)
					  })
				}else if(name==='flowUsed'){
					//按流量使用明细报表
					exportflowUsed({
						corpId:row.corpId,
						corpName :row.corpName,
						month :row.statTime,
            userId: this.$store.state.user.userId
					}).then(res => {
					   if (res && res.code == '0000') {
					      this.exportModal=true
					      this.taskId=res.data.taskId
					      this.taskName=res.data.taskName
					    } else {
					      throw res
					    }
					  }).catch((err) => {
					       console.log(err)
					  })
				}else if(name==='Invoice'){
					//Invoice导出
					exportInvoice({
						corpName :row.corpName,
						invoicePath :row.invoicePath,
						month:row.statTime
					}).then(res => {
					   if (res && res.code == '0000') {
					      this.exportModal=true
					      this.taskId=res.data.taskId
					      this.taskName=res.data.taskName
					    } else {
					      throw res
					    }
					  }).catch((err) => {
					       console.log(err)
					  })
				}else if(name==='taxation'){
					//税费文件导出
					exportTaxation({
						corpName:row.corpName,
						taxationPath:row.invoicePath,
						month:row.statTime,
						userId:this.$store.state.user.userId
					}).then(res => {
					   if (res && res.code == '0000') {
					      this.exportModal=true
					      this.taskId=res.data.taskId
					      this.taskName=res.data.taskName
					    } else {
					      throw res
					    }
					  }).catch((err) => {
					       console.log(err)
					  })
				}

			},
			/**
			 * 生成发票
			 */
			createInvoice: function() {
				if(this.desc){
					//TODO 生成发票参数设置
					createInvoice({
						   address: this.invoiceInfo.address,
						   id: this.id,
						   invoiceDesc: this.desc,
						   type:2
					 }).then(res => {
					   if (res && res.code == '0000') {
					     this.$Notice.success({
					       title: '操作提示',
					       desc: '发票生成成功'
					     })
						   this.invoice_model=false
					       //刷新页面数据
					        this.loadByPage(this.page)
					    } else {
					      throw res
					    }
					  }).catch((err) => {
					       console.log(err)
					  })
				}else{
					this.$Message.error("发票说明不能为空");
				}

			},
			/**
			 * 生成票号
			 */
			createInvoiceNo:function(row,needNewNo){
			  createInvoiceNo(row.id,row.corpId,2).then(res => {
			    if (res && res.code == '0000') {
					this.address = res.data.address
					if(needNewNo===2){
						this.InvoiceNo = res.data.invoiceNo
					}else{
						this.InvoiceNo = row.invoiceNo
					}
					let currency = row.currency == '156' ? "CNY" :row.currency == '840' ? "USD" :row.currency == '344' ? "HKD": '';
					this.invoiceInfo = {
						  AccountNo: row.corpName,
						  address: this.address,
						  AmountDue:currency+" "+(row.packageIncome+row.useIncome),
						  InvoiceNo:this.InvoiceNo,
						  InvoiceDate:res.data.invoiceDate,
						  FileTitle:'INVOICE',
						  InvoiceDesc:row.invoiceDesc,
						  AmountTax:currency+" "+(row.packageIncome+row.useIncome),
						  Tax:currency+" "+(row.taxation),
						  TotalAmount:currency+" "+(row.packageIncome+row.useIncome),
						  data:[{
							description:res.data.invoiceNameDesc,  //账单名称
							billingPeriod:res.data.billingPeriod,
							qty:'1',
							unitPrice:row.packageIncome+row.useIncome,
							amount:row.packageIncome+row.useIncome,
					     }]
					}
					this.invoice_model = true
			     } else {
			       throw res
			     }
			   }).catch((err) => {
			        console.log(err)
			   })
			},
			/**
			 * 生成发票预览退出
			 */
			cancelInvoice: function() {
			  this.id = null
			  this.invoice_model = false
			  this.invoiceInfo=[]
			},
			showInvoiceView: function(row) {
			  this.id = row.id  //设置选中行主键
			  //判断发票是否是实际发票
			  if(row.invoiceNo == null || (row.invoiceNo !=null && row.invoiceNo.substr(0, 2) != "IN")){
			    //非正式发票，需要调用发票编号生成接口
			    this.createInvoiceNo(row,2)
			  }else{
				this.createInvoiceNo(row,1)
			  }

			},
			//获取发票说明
			getdesc(data){
				this.desc=data
			},
			update(row){
				this.updateModal=true
				this.corpList = Object.assign({}, row);
				this.corpList.taxation=parseFloat(math.divide(math.bignumber(row.taxation), 100).toFixed(2)).toString()
				this.corpList.useIncome=parseFloat(math.divide(math.bignumber(row.useIncome), 100).toFixed(2)).toString()
				this.corpList.packageIncome=parseFloat(math.divide(math.bignumber(row.packageIncome), 100).toFixed(2)).toString()
				this.endTime=this.corpList.svcEndTime
				this.startTime=this.corpList.svcStartTime
			},
			endTimeDateChange(date) {
				this.endTime = date;
			},
			startTimeDateChange(date) {
				this.startTime = date;
			},
			/**
			 * 文件上传
			 */
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			handleBeforeUpload(file) {
				if (!/^.+(\.pdf)$/.test(file.name)) {
					this.$Notice.warning({
						title: "文件格式不正确",
						desc:  '文件 ' + file.name + ' 格式不正确，请上传pdf格式文件。'
					})
				} else {
					this.file = file
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			removeFile() {
				this.file = ''
			},
		}
	}
</script>

<style>
</style>
