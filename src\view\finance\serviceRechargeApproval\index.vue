<template>
	<!-- 自服务充值审批 -->
	<Card>
		<!-- 搜索条件 -->
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">渠道商名称:</span>
				<Input v-model.trim="corpName" placeholder="请输入渠道商名称" clearable
					style="width: 200px;" />
			</div>
			<div class="search_box">
				<span class="search_box_label">审批状态:</span>
				<Select v-model="status" clearable placeholder="选择审批状态" filterable style="width: 200px;">
					<Option value="1">待审批</Option>
					<Option value="2">审批通过</Option>
					<Option value="3">审批不通过</Option>
				</Select>
			</div>
      <div class="search_box">
      	<span class="search_box_label">审批人:</span>
      	<Input v-model.trim="authName" placeholder="请输入审批人" clearable
      		style="width: 200px;" />
      </div>&nbsp;&nbsp;&nbsp;&nbsp;
			<div style="padding: 0 5px;">
				<Button type="primary" icon="md-search" :loading="searchloading" v-has="'search'" @click="search()">搜索</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table ref="selection" :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
      <template slot-scope="{ row, index }" slot="action">
      	<Button type="info" ghost size="small" style="margin: 5px" @click="exportPaymentProofs(row)"
          :disabled="!row.paymentProofAddress">下载付款证明</Button>
      </template>
    </Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
    <a ref="downloadLink" style="display: none"></a>
	</Card>
</template>
<script>
	import {
		getList,
    exportPaymentProofs
	} from "@/api/finance/serviceRechargeApproval.js";
	export default {
		data() {
			return {
				total: 0,
				currentPage: 1,
				page: 0,
				corpName: "",
        status: "",
        authName: "",
				loading: false,
				searchloading: false, //查询加载
				data: [], //表格列表
				columns: [{
					title: "渠道商名称",
					key: 'cropName',
					minWidth: 150,
					align: 'center',
					tooltip: true
				}, {
					title: "充值/账单金额",
					key: 'amount',
					minWidth: 150,
					align: 'center',
					tooltip: true,
				}, {
					title: "充值/缴付日期",
					key: 'chargeTime',
					minWidth: 200,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.chargeTime) {
							var time = new Date(row.chargeTime)
							text = time.getFullYear() + '年' + (time.getMonth() + 1) + '月' + time.getDate() +
								'日 ' + time.getHours().toString().padStart(2, "0") + ':' + time.getMinutes()
								.toString().padStart(2, "0") + ':' + time.getSeconds().toString().padStart(2,
									"0");
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: "付款证明",
					slot: 'action',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: "审批状态",
					key: 'authStatus',
					minWidth: 120,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text = row.authStatus == '1' ? '待审批' : row.authStatus == '2' ? '审批通过' : row.authStatus == '3' ? '审批不通过' :''
						return h('label', text)
					}
				},  {
					title: '审批人',
					key: 'authName',
					align: 'center',
					minWidth: 120,
				}, {
					title: "不通过原因",
					key: 'noPassReason',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, ],
			}
		},
		mounted() {
			this.goPageFirst(1)
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				getList({
          id: '',
					pageNum: page,
					pageSize: 10,
					corpName: this.corpName,
					status: this.status,
					authName: this.authName,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.data = res.data
						this.total = Number(res.count)
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			goPage(page) {
				this.goPageFirst(page)
			},
      exportPaymentProofs: function(row) {
        exportPaymentProofs({
        	id: row.id,
        }).then(res => {
        	const content = res.data
        	let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
        	if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
        		const link = this.$refs.downloadLink // 创建a标签
        		let url = URL.createObjectURL(content)
        		link.download = fileName
        		link.href = url
        		link.click() // 执行下载
        		URL.revokeObjectURL(url) // 释放url
        	} else { // 其他浏览器
        		navigator.msSaveBlob(content,fileName)
        	}
        }).catch()
      },
    }
	}
</script>

<style scoped="scoped">
	.search_head_i {
		margin-top: 30px;
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 100px;
	}
</style>
