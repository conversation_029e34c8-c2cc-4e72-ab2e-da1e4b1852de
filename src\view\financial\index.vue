<template>
  <Card>
    <Button type="success" @click="showInvoiceView(1)" style="margin-right: 10px;">生成发票</Button>
    <Modal v-model="invoice_model" title="生成发票预览" @on-ok="createInvoice" @on-cancel="cancelInvoice" width="800px" :styles="{top: '10px'}">
      <Card width="750px">
        <invoiceTemplate :AccountNo="invoiceInfo.AccountNo" :address="invoiceInfo.address" :AmountDue="invoiceInfo.AmountDue"
        :InvoiceNo="invoiceInfo.InvoiceNo" :InvoiceDate="invoiceInfo.InvoiceDate" :FileTitle="invoiceInfo.FileTitle"
         :InvoiceDesc="invoiceInfo.InvoiceDesc" :columns="invoiceInfo.columns" :data="invoiceInfo.data"/>
      </Card>
      
    </Modal>
  </Card>
</template>

<script>
  import invoiceTemplate from '@/components/invoice/invoiceTemp'
  export default {
    components: {
      invoiceTemplate
    },
    data() {
      return {
        id: null,
        invoice_model: false,
        invoiceInfo:{
          AccountNo: '北京博新創億科技股份有限公司',
          address: '北京市海淀区首都體育館南路6號3幢557室',
          AmountDue:'CNY 1,360.00',
          InvoiceNo:'IN-************-GDS',
          InvoiceDate:'30-Mar-2021',
          FileTitle:'INVOICE',
          InvoiceDesc: 'Payment Instruction\nPlease remit payment to beneficiary China Mobile International Limited by telegraph\ntransfer Account Name: China Mobile International Limited\nName of Bank: The Hongkong & Shanghai Banking Corporation Limited\nBank Address: 1 Queen\'s Road, Central, Hong Kong\nAccount Number: 848-021796-838 SWIFT\nCode: HSBCHKHHHKH\n*Please quote our invoice number(s) with your payment instructions to the bank upon remittance.\n*Please email remittance <NAME_EMAIL> for update of your \naccount. This computer generated document requires no signature.',
          columns:[{
                title: 'Description',
                align: 'center',
                width: 220,
                key: 'description'
              },
              {
                title: 'Billing Period',
                align: 'center',
                width: 220,
                key: 'billingPeriod'
              },
              {
                title: 'Qty',
                align: 'center',
                width: 60,
                key: 'qty'
              },
              {
                title: 'Unit Price',
                align: 'center',
                width: 115,
                key: 'unitPrice'
              },
              {
                title: 'amount',
                align: 'center',
                width: 116,
                key: 'amount'
              }
            ],
          data: [{
            description:'GDS-Sales Settlement-Mar2021',
            billingPeriod:'25-Feb-2021 to 24-Mar-2021',
            qty:'1',
            unitPrice:'1,360.00',
            amount:'1,360.00',
          },
          {
            description:'Amount before Tax',
            billingPeriod:null,
            qty:null,
            unitPrice:'CNY',
            amount:'1,360.00',
          },
          {
            description:'TAX',
            billingPeriod:null,
            qty:null,
            unitPrice:'CNY',
            amount:'1,360.00',
          },
          {
            description:'Total Amount Due',
            billingPeriod:null,
            qty:null,
            unitPrice:'CNY',
            amount:'1,360.00',
          }]
        }
      }
    },
    methods: {
      showInvoiceView: function(id) {
        this.id = id
        console.log("TODO,展示发票预览")
        this.invoice_model = true
      },

      /**
       * 生成发票
       */
      createInvoice: function() {

      },
      /**
       * 生成发票预览退出
       */
      cancelInvoice: function() {
        this.id = null
        this.invoice_model = false
      }
    },
    mounted() {

    }
  }
</script>

<style>
</style>
