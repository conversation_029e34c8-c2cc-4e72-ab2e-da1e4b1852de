<template>
	<!-- 流量使用详情 -->
	<Card >
		<div style="display: flex;width: 100%;">
			<Form ref="form" :model="form" :rules="rules" :label-width="100" style="display: flex;">
				<FormItem label="ICCID" prop="richtextTitle">
					<Input v-model="form.ICCID" placeholder="请输入ICCID" prop="showTitle" clearable style="width: 200px" />
				</FormItem>
				<FormItem label="MSISDN" prop="richtextTitle">
					<Input v-model="form.MSISDN" placeholder="请输入MSISDN" prop="showTitle" clearable style="width: 200px" />
				</FormItem>
				<FormItem label="HIMSI" prop="richtextTitle">
					<Input v-model="form.HIMSI" placeholder="请输入HIMSI" prop="showTitle" clearable style="width: 200px" />
				</FormItem>
				<FormItem prop="richtextTitle">
					<Button type="primary" icon="md-search" size="large"  @click="search()">查询</Button>
					<Button type="success" icon="ios-cloud-download-outline" size="large" style="margin-left: 20px;" @click="back()">返回</Button>
				</FormItem>
			</Form>
		</div>
		<!-- 表格 -->
		<Table :columns="columns12" :data="data" style="width:100%;margin-top: 50px;" :loading="loading">
		</Table>
		<!-- 分页 -->
		<div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px; ">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
	</Card>
</template>

<script>
	export default {
		data() {
			return {
				form: {},
				total: 0,
				currentPage: 0,
				loading:false,
				typeList:[],
				columns12: [{
						title: '日期',
						key: 'time',
						align: 'center'
					},
					{
						title: '使用流量',
						key: 'flow',
						align: 'center'
					},
					{
						title: '套餐名称',
						key: 'mealname',
						align: 'center'
					},
				],
				data: [{
						time: '2021-03-12',
						flow: '300M',
						mealname:'欧洲３日游１Ｇ',
					},
					{   
						time: '2021-03-12',
						flow: '300M',
						mealname:'欧洲３日游１Ｇ',
					},
				],
				rules: {}
			}
		},
		mounted() {
			this.goPageFirst(0)
		},
		methods: {
			goPageFirst(page){
				// this.loading=false
			},
			goPage() {

			},
			// 搜索
			search() {

			},
			// 导出
			exportTable() {

			},
			details(row) {
				this.$router.push({
					path: '/channel/detailsList',
				})
			},
			back(){
				this.$router.go(-1)
			}

		}


	}
</script>

<style>
</style>
