<template>
  <!-- 流量池 -->
  <Card>
    <div style="width: 100%;margin-top: 50px; margin: auto;">
      <div style="display: flex;width: 100%;">
        <Input v-model="poolname" placeholder="请输入流量池名称" prop="showTitle" clearable style="width: 200px" />
        <Button v-has="'search'" type="primary" icon="md-search" size="large" style="margin-left: 20px;" @click="search()">查询</Button>
        <Button v-has="'add'" type="success" icon="md-add" size="large" style="margin-left: 20px;" @click=" addPool()">新建流量池</Button>
      </div>
      <!-- 表格 -->
      <Table :columns="columns12" :data="data" style="width:100%;margin-top: 50px;" :loading="loading">
        <template slot-scope="{ row, index }" slot="action">
          <Button v-has="'update'" type="primary" size="small" style="margin-right: 5px" @click="update(row)">修改</Button>
		  <Button v-has="'view'" type="warning" size="small" style="margin-right: 5px" @click="details(row)">详情</Button>
          <Button v-has="'delete'" v-if="row.number===0" type="error" size="small" @click="remove(row)">删除</Button>
        </template>
      </Table>
      <!-- 分页 -->
      <div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px; ">
        <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
      </div>
      <!-- 添加流量池 -->
      <Modal v-model="addmodel" title="添加流量池" :mask-closable="false" @on-cancel="cancelModal">
        <Form ref="formmodel" :model="formmodel" :rules="rules" :label-width="120">
          <FormItem label="流量池名称" prop="poolname">
            <Input v-model="formmodel.poolname" placeholder="请输入流量池名称" clearable style="width: 200px" />
          </FormItem>
          <FormItem label="客户名称" prop="customername">
            <Input v-model="formmodel.customername" placeholder="请输入客户名称" clearable style="width: 200px" />
          </FormItem>
          <FormItem label="支持的国家" prop="country">
            <Select v-model="formmodel.country" style="width: 200px;text-align: left;margin: 0 10px;" multiple placeholder="请选择国家"
              @on-select="choose($event)">
              <Option v-for="(type,typeIndex) in localList" :value="type.name" :key="typeIndex">{{ type.name }}</Option>
            </Select>
          </FormItem>
          <FormItem label="选择卡池" prop="cardpool">
            <Select v-model="formmodel.cardpool" style="width: 200px;text-align: left;margin: 0 10px;" multiple placeholder="请选择卡池"
              @on-select="choose($event)">
              <Option v-for="(type,typeIndex) in cardpool" :value="type.name" :key="typeIndex">{{ type.name }}</Option>
            </Select>
          </FormItem>
          <FormItem label="流量池总量" prop="poolcount">
            <Input v-model="formmodel.poolcount" placeholder="请输入流量池总量" clearable style="width: 200px" />
          </FormItem>
		  <FormItem label="重置周期类型" prop="poolcount">
		    <Select  v-model="cycle" style="width: 200px;text-align: left;margin: 0 10px;" placeholder="请选择重置周期类型"
		      @on-select="choose($event)">
		      <Option v-for="(type,typeIndex) in cycleList" :value="type.value" :key="typeIndex">{{ type.value }}</Option>
		    </Select>
		  </FormItem>
		  <FormItem label="重置周期数" prop="poolcount">
		    <Input v-model="formmodel.poolcount" placeholder="请输入重置周期数" clearable style="width: 200px" />
		  </FormItem>
         <!-- <FormItem label="重置周期" prop="cycle">
            <RadioGroup v-model="single" @on-change="selectcycle($event)">
              <Radio label="是">是</Radio>
              <Radio label="否">否</Radio>
            </RadioGroup>
            <Select v-show="cycleflg" v-model="cycle" style="width: 200px;text-align: left;margin: 0 10px;" placeholder="请选择"
              @on-select="choose($event)">
              <Option v-for="(type,typeIndex) in cycleList" :value="type.value" :key="typeIndex">{{ type.value }}</Option>
            </Select>

          </FormItem> -->
          <FormItem label="控制逻辑" prop="logic">
			<Select v-model="formmodel.logic" placeholder="请选择控制逻辑"  :clearable="true" style="width: 200px">
			  <Option value="CNY">达量停用</Option>
			  <Option value="HKD">达量限速</Option>
			</Select>
          </FormItem>
          <FormItem label="有效日期" prop="time" style="display: flex;">
            <DatePicker v-model="formmodel.starttime" type="date" placeholder="开始日期" style="margin-left: -100px;width: 120px"></DatePicker>
            <DatePicker v-model="formmodel.endtime" type="date" placeholder="结束日期" style="margin-left: 30px; width: 120px"></DatePicker>
          </FormItem>
          <FormItem label="通过文件添加">
            <Upload type="drag" :action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
              :on-progress="fileUploading" style="width: 100%; margin-top: 50px;margin-left: -50px;">
              <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>点击或拖拽文件上传</p>
              </div>
            </Upload>

            <ul class="ivu-upload-list" v-if="file" style="width: 100%; margin-left: -50px;">
              <li class="ivu-upload-list-file ivu-upload-list-file-finish">
                <span><i class="ivu-icon ivu-icon-ios-stats"></i>{{file.name}}</span>
                <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
              </li>
            </ul>
          </FormItem>
        </Form>
        <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
          <Button @click="cancelModal">取消</Button>
          <Button type="primary" @click="save">确定</Button>
        </div>
      </Modal>
	  <!-- 修改流量池 -->
	  <Modal v-model="updatemodel" title="修改流量池" :mask-closable="false" @on-cancel="cancelModal">
	    <Form ref="upform" :model="upform" :rules="rules" :label-width="120">
	      <FormItem label="流量池名称" >
	        <Input v-model="upform.poolname" placeholder="请输入流量池名称"  disabled clearable style="width: 200px" />
	      </FormItem>
	      <FormItem label="客户名称" >
	        <Input v-model="upform.customername" placeholder="请输入客户名称" disabled clearable style="width: 200px" />
	      </FormItem>
	      <FormItem label="支持的国家" prop="country">
	        <Select v-model="upform.country" style="width: 200px;text-align: left;margin: 0 10px;" multiple placeholder="请选择国家"
	          @on-select="choose($event)">
	          <Option v-for="(type,typeIndex) in localList" :value="type.name" :key="typeIndex">{{ type.name }}</Option>
	        </Select>
	      </FormItem>
	      <FormItem label="选择卡池" prop="cardpool">
	        <Select v-model="upform.cardpool" style="width: 200px;text-align: left;margin: 0 10px;" multiple placeholder="请选择卡池"
	          @on-select="choose($event)">
	          <Option v-for="(type,typeIndex) in cardpool" :value="type.name" :key="typeIndex">{{ type.name }}</Option>
	        </Select>
	      </FormItem>
	      <FormItem label="流量池总量" prop="poolcount">
	        <Input v-model="upform.poolcount" placeholder="请输入流量池总量" clearable style="width: 200px" />
	      </FormItem>
		  <FormItem label="重置周期类型" prop="poolcount">
		    <Select  v-model="upform.poolcount" style="width: 200px;text-align: left;margin: 0 10px;" placeholder="请选择重置周期类型"
		      @on-select="choose($event)">
		      <Option v-for="(type,typeIndex) in cycleList" :value="type.value" :key="typeIndex">{{ type.value }}</Option>
		    </Select>
		  </FormItem>
		  <FormItem label="重置周期数" prop="poolcount">
		    <Input v-model="upform.poolcount" placeholder="请输入重置周期数" clearable style="width: 200px" />
		  </FormItem>
	      <!-- <FormItem label="重置周期" prop="cycle">
	        <RadioGroup v-model="single" @on-change="selectcycle($event)">
	          <Radio label="是">是</Radio>
	          <Radio label="否">否</Radio>
	        </RadioGroup>
	        <Select v-show="cycleflg" v-model="cycle" style="width: 200px;text-align: left;margin: 0 10px;" placeholder="请选择"
	          @on-select="choose($event)">
	          <Option v-for="(type,typeIndex) in cycleList" :value="type.value" :key="typeIndex">{{ type.value }}</Option>
	        </Select>
	      </FormItem> -->
	      <FormItem label="控制逻辑" prop="logic">
			<Select v-model="upform.logic" placeholder="请选择控制逻辑"  :clearable="true" style="width: 200px">
			  <Option value="CNY">达量停用</Option>
			  <Option value="HKD">达量限速</Option>
			</Select>
	      </FormItem>
	      <FormItem label="有效日期" prop="time" style="display: flex;">
	        <DatePicker v-model="upform.starttime" type="date" placeholder="开始日期" style="margin-left: -100px;width: 120px"></DatePicker>
	        <DatePicker v-model="upform.endtime" type="date" placeholder="结束日期" style="margin-left: 30px; width: 120px"></DatePicker>
	      </FormItem>
	      <FormItem label="通过文件添加">
	        <Upload type="drag" :action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
	          :on-progress="fileUploading" style="width: 100%; margin-top: 50px;margin-left: -50px;">
	          <div style="padding: 20px 0">
	            <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
	            <p>点击或拖拽文件上传</p>
	          </div>
	        </Upload>
	  
	        <ul class="ivu-upload-list" v-if="file" style="width: 100%; margin-left: -50px;">
	          <li class="ivu-upload-list-file ivu-upload-list-file-finish">
	            <span><i class="ivu-icon ivu-icon-ios-stats"></i>{{file.name}}</span>
	            <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
	          </li>
	        </ul>
	      </FormItem>
	    </Form>
	    <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
	      <Button @click="cancelModal">取消</Button>
	      <Button type="primary" @click="updatesave">确定</Button>
	    </div>
	  </Modal>
    </div>
  </Card>
</template>

<script>
  import localData from '@/libs/localData.js'
  import publicData from '@/libs/publicData.js'
  import {
    search,
    addpool,
  } from '@/api/flowPool'
  export default {
    data() {
      return {
        localList: [],
        loading: false,
        modelloading: true,
        form: {},
        poolname: '',
        cardpool: [],
        formmodel: {},
		upform:{},
        cycleList: [
			{
			    id: '0',
			    value: '每日'
			  },
			{
            id: '1',
            value: '每年'
          },
          {
            id: '2',
            value: '每季度'
          },
          {
            id: '3',
            value: '每月'
          },
        ],
        total: 0,
        currentPage: 0,
        page: 0,
        index: 0,
        cycle: '',
        cycleflg: false,
        columns12: [{
            title: '流量池名称',
            key: 'poolname',
            align: 'center'
          }, {
            title: '流量池状态',
            key: 'status',
            align: 'center',
            render: (h, params) => {
              const row = params.row;
              const text = row.status == '1' ? '正常' : '异常';
              return h('label', text);
            }
          },
          {
            title: '创建时间',
            key: 'time',
            align: 'center'
          },
          {
            title: 'SIM卡数量',
            key: 'number',
            align: 'center'
          },
          {
            title: '流量池总量',
            key: 'count',
            align: 'center'
          },
          {
            title: '流量池已用量',
            key: 'usecount',
            align: 'center'
          },
          {
            title: '用量占比',
            key: 'Proportion',
            align: 'center'
          },
          // {
          // 	title: '流量池状态',
          // 	key: 'poostate',
          // 	align: 'center'
          // },
          {
            title: '客户',
            key: 'Operators',
            align: 'center'
          },
          {
            title: '操作',
            slot: 'action',
            align: 'center'
          }
        ],
        data: [{
            poolname: '12321',
            status: '1',
            time: '2021-03-23',
            number: 0,
            count: '654M',
            usecount: '45M',
            Proportion: '0.4234',
            Operators: '小李'
          },
          {
            poolname: '12321',
            status: '1',
            time: '2021-03-23',
            number: 35,
            count: '654M',
            usecount: '45M',
            Proportion: '0.4234',
            Operators: '小李'
          },

        ],
        uploadUrl: '',
        file: null,
        addmodel: false,
		updatemodel:false,
        single: '',
        rules: {
          poolname: [{
            required: true,
            message: "请输入流量池名称",
            trigger: "blur",
          }],
          customername: [{
            required: true,
            message: "请输入客户名称",
            trigger: "blur",
          }],
          country: [{
            required: true,
            message: "请选择国家",
            trigger: "blur",
          }],
          cardpool: [{
            required: true,
            message: "请选择卡池",
            trigger: "blur",
          }],
          poolcount: [{
            required: true,
            message: "请输入流量池总量",
            trigger: "blur",
          }],
          cycle: [{
            required: true,
            message: "请输入周期",
            trigger: "blur",
          }],
          logic: [{
            required: true,
            message: "请输入控制逻辑",
            trigger: "blur",
          }],
          time: [{
            required: true,
            message: "请选择日期",
            trigger: "blur",
          }],




        }
      }
    },
    mounted() {
      this.goPageFirst(0)
      var list = localData.localList
      this.currencyList = publicData.currencyList
      for (var n = 0; n < list.length; n++) {
        this.localList = this.localList.concat(list[n].value)
      }
    },
    methods: {
      goPageFirst(page) {
        this.loading = true
        var _this = this
        let pageNumber = page
        let pageSize = 10
        let poolname = this.poolname
        search({
          pageNumber,
          pageSize,
          poolname
        }).then(res => {
          if (res.code == '0000') {
            _this.loading = false
            this.page = page
            this.total = res.data.total
            this.records = res.data.records
          }
        }).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.loading = false
        })
      },
      // 分页
      goPage(page) {
        this.goPageFirst(page)
      },
      // 查询
      search() {
        this.goPageFirst(0)
      },
      // 新建流量池
      addPool() {
        this.addmodel = true
      },
      // 删除
      remove() {
        this.$Modal.confirm({
          title: '确认删除该项？',
          onOk: () => {
            this.$Notice.success({
              title: '操作提醒',
              desc: '删除流量池成功！'
            })
            // delpool(this.formdel).then(res => {
            // 	if (res && res.code == '0000') {
            // 		this.$Notice.success({
            // 			title: '操作提醒',
            // 			desc: '删除流量池成功！'
            // 		})
            // 		this.deletemodel = false
            // 		this.goPageFirst(0)
            // 	} else {
            // 		throw res
            // 	}
            // }).catch((err) => {
            // 	console.log(err)
            // }).finally(() => {
            // 	this.loading = false
            // })

          }
        });
      },
      update(row) {
        this.updatemodel = true
		this.formmodel.poolname=row.poolname
		this.formmodel.customername=row.Operators
      },
      // 详情
      details() {
        this.$router.push({
          path: '/poolList',
        })
      },
      // 弹框确定
      save() {
        if (!this.file) {
          this.$Message.warning('请选择需要上传的文件')
        } else {
          this.$refs.formmodel.validate(valid => {
            if (valid) {
              addpool(this.formmodel).then(res => {
                if (res && res.code == '0000') {
                  this.$Notice.success({
                    title: '操作提醒',
                    desc: '新增流量池成功！'
                  })
                  this.addmodel = false
                  this.goPageFirst(0)
                } else {
                  throw res
                }
              }).catch((err) => {
                console.log(err)
              }).finally(() => {
                this.loading = false
              })
            }
          })
        }
      },
	  updatesave(){
		if (!this.file) {
		  this.$Message.warning('请选择需要上传的文件')
		} else {
		  this.$refs.upform.validate(valid => {
		    if (valid) {
		      addpool(this.upform).then(res => {
		        if (res && res.code == '0000') {
		          this.$Notice.success({
		            title: '操作提醒',
		            desc: '修改流量池成功！'
		          })
		          this.addmodel = false
		          this.goPageFirst(0)
		        } else {
		          throw res
		        }
		      }).catch((err) => {
		        console.log(err)
		      }).finally(() => {
		        this.loading = false
		      })
		    }
		  })
		}
	  },
      cancelModal() {
        this.addmodel = false
		this.updatemodel=false
		this.$refs.formmodel.resetFields()
		this.$refs.upform.resetFields()
      },
      // 选择重置周期
      selectcycle(id) {
        if (id === '是') {
          this.cycleflg = true
        } else {
          this.cycleflg = false
        }
      },
      fileSuccess(response, file, fileList) {
        this.message = '请先下载模板文件，并按格式填写后上传'
      },
      handleError(res, file) {
        var v = this
        setTimeout(function() {
          v.uploading = false;
          v.$Notice.warning({
            title: '错误提示',
            desc: "上传失败！"
          });
        }, 3000);
      },
      fileUploading(event, file, fileList) {
        this.message = '文件上传中、待进度条消失后再操作'
      },
      handleBeforeUpload(file) {
        // 校验文件格式
        if (!/.doc$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式不正确',
            desc: '文件 ' + file.name + ' 格式不正确，请上传doc格式文件。'
          })
        } else {
          // 校验文件大小
          const filesize = file.size / 2048 / 2048 < 2;
          if (!filesize) {
            this.$Notice.warning({
              title: '文件大小不能超过2M',
              desc: '文件 ' + file.name + '文件大小不能超过2M。'
            })
          } else {
            this.file = file
          }
        }
        return false
      },
      removeFile() {
        this.file = ''
      },
    }
  }
</script>

<style>
</style>
