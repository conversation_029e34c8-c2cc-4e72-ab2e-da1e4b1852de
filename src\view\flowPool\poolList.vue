<template>
	<!-- 详情 -->
	<Card>
		<div style="display: flex;width: 100%;">
			<Input v-model="customer " placeholder="请输入客户" prop="showTitle" clearable style="width: 200px" />
			<Button v-has="'search'" type="primary" icon="md-search" size="large" style="margin-left: 20px;" @click="search()">查询</Button>
			<Button v-has="'add'" type="info" icon="md-add" size="large" style="margin-left: 20px;" @click="singlePool()">单卡导入</Button>
			<Button v-has="'import'" type="warning" icon="md-add" size="large" style="margin-left: 20px;" @click="batchPool()">批量导入</Button>
			<Button v-has="'batchDelete'" type="error" icon="ios-remove" size="large" style="margin-left: 20px;" @click="deletePool()">批量删除</Button>
		</div>
		<!-- 表格 -->
		<Table :columns="columns12" :data="data" :ellipsis="true" style="width:100%;margin-top: 50px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="used">
				<!-- <a @click="showflow"> -->
				<span>{{row.used}}</span>
				<!-- </a> -->
			</template>
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'view'" type="warning" size="small" @click="showflow(row)">使用流量详情</Button>
				<Button v-has="'update'" type="primary" size="small" style="margin-left: 2px" @click="update(row)">修改</Button>
				<Button v-has="'delete'" type="error" size="small" style="margin-left: 2px" @click="remove(row)">删除</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px; ">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 修改流量池 -->
		<Modal v-model="updatemodel" title="修改流量池" :mask-closable="false" @on-cancel="cancelModal">
			<Form ref="form" :model="form" :rules="rules" :label-width="120">
				<FormItem label="HIMSI号码 ：">
					<sapn>{{form.HIMSI}}</sapn>
				</FormItem>
				<FormItem label="MSISDN号码 ：">
					<sapn>{{form.MSISDN}}</sapn>
				</FormItem>
				<FormItem label="ICCID号码 ：">
					<sapn>{{form.ICCID}}</sapn>
				</FormItem>
				<FormItem label="单日流量上限 ：" prop="oneupper">
					<Input v-model="form.oneupper" placeholder="请输入单日流量上限" prop="showTitle" clearable style="width: 200px" />
				</FormItem>
				<FormItem label="总流量上限 ：" prop="totalupper">
					<Input v-model="form.totalupper" placeholder="请输入总流量上限" prop="showTitle" clearable style="width: 200px" />
				</FormItem>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="updatesave">确定</Button>
			</div>
		</Modal>
		<!-- 单卡导入 -->
		<Modal v-model="singlemodel" title="单卡导入" :mask-closable="false" @on-cancel="cancelModal">
			<Form ref="singleform" :model="singleform" :rules="rules" :label-width="120">
				<FormItem label="HIMSI号码 ：" prop="hnumber">
					<Input v-model="singleform.hnumber" placeholder="请输入HIMSI号码" prop="showTitle" clearable style="width: 200px" />
				</FormItem>
				<FormItem label="MSISDN号码 ：" prop="mnumber">
					<Input v-model="singleform.mnumber" placeholder="请输入MSISDN号码" prop="showTitle" clearable style="width: 200px" />
				</FormItem>
				<FormItem label="ICCID号码 ：" prop="inumber">
					<Input v-model="singleform.inumber" placeholder="请输入ICCID号码" prop="showTitle" clearable style="width: 200px" />
				</FormItem>
				<FormItem label="单日流量上限 ：" prop="oneupper">
					<Input v-model="singleform.oneupper" placeholder="请输入单日流量上限" prop="showTitle" clearable style="width: 200px" />
				</FormItem>
				<FormItem label="总流量上限 ：" prop="totalupper">
					<Input v-model="singleform.totalupper" placeholder="请输入总流量上限" prop="showTitle" clearable style="width: 200px" />
				</FormItem>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="singlesave">确定</Button>
			</div>
		</Modal>
		<!-- 批量导入 -->
		<Modal v-model="batchmodel" title="批量导入" :mask-closable="false" @on-cancel="cancelModal">
			<Form ref="form" :model="form" :rules="rules" :label-width="100">
				<FormItem label="通过文件添加">
					<Upload type="drag" :action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
					 :on-progress="fileUploading" style="width: 100%; margin-top: 50px;margin-left: -50px;">
						<div style="padding: 20px 0">
							<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
							<p>点击或拖拽文件上传</p>
						</div>
					</Upload>

					<ul class="ivu-upload-list" v-if="file">
						<li class="ivu-upload-list-file ivu-upload-list-file-finish">
							<span><i class="ivu-icon ivu-icon-ios-stats"></i>{{file.name}}</span>
							<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
						</li>
					</ul>
				</FormItem>

			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="batchsave">确定</Button>
			</div>
		</Modal>
		<!-- 删除号码 -->
		<Modal v-model="deletemodel" title="批量删除" :mask-closable="false" @on-cancel="cancelModal">
			<Form ref="formdel" :model="formdel" :rules="rules" :label-width="100">
				<!-- 				<FormItem label="删除号码" prop="numbertype" style="width: 100%;">
					<Select v-model="formdel.numbertype" style="width: 200px;text-align: left;margin: 0 10px;" placeholder="请选择号码类型"
					 @on-select="choose($event)">
						<Option v-for="(type,typeIndex) in numberList" :value="type.value" :key="typeIndex">{{ type.value }}</Option>
					</Select>
				</FormItem> -->
				<FormItem label="通过文件删除">
					<Upload type="drag" :action="deluploadUrl" :on-success="delfileSuccess" :on-error="delhandleError" :before-upload="delhandleBeforeUpload"
					 :on-progress="delfileUploading" style="width: 100%; margin-top: 50px;margin-left: -50px;">
						<div style="padding: 20px 0">
							<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
							<p>点击或拖拽文件上传</p>
						</div>
					</Upload>

					<ul class="ivu-upload-list" v-if="filedel" style="width: 100%;margin-left: -50px;">
						<li class="ivu-upload-list-file ivu-upload-list-file-finish">
							<span><i class="ivu-icon ivu-icon-ios-stats"></i>{{filedel.name}}</span>
							<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="delFile"></i>
						</li>
					</ul>
				</FormItem>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="savedel">确定</Button>
			</div>
		</Modal>
		<!-- 流量详情 -->
		<Modal v-model="poolmodel" title="使用流量详情" :mask-closable="false" @on-cancel="cancelpool">
			<Table :columns="columns" :data="flowform" :ellipsis="true" style="margin-bottom: 50px;" :loading="loading">
			</Table>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="saveflow">确定</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import localData from '@/libs/localData.js'
	import publicData from '@/libs/publicData.js'
	import {
		updatepool,
		delpool,
		searchcust,
		delBatch
	} from '@/api/flowPool'
	export default {
		data() {
			//校验号码
			var checkPhone = (rule, value, callback) => {
				let reg = /^[0-9]\d*$/;
				if (!value) {
					callback(new Error('请输入正确的号码'))
				} else if (!reg.test(value)) {
					callback(new Error('请输入纯数字号码'))
				} else {
					callback()
				}
			};
			return {
				singlemodel: false,
				batchmodel: false,
				poolmodel: false,
				loading: false,
				total: 0,
				page: 0,
				customer: '',
				localList: [],
				numberList: [{
					id: 1,
					value: 'HIMSI'
				}, {
					id: 2,
					value: 'ICCID'
				}, ],
				currentPage: 0,
				updatemodel: false,
				deletemodel: false,
				form: {},
				formdel: {},
				singleform: {},
				cardpool: [],
				uploadUrl: '',
				deluploadUrl: '',
				filedel: null,
				file: null,
				cycle: '',
				cycleflg: false,
				single: '',
				cycleList: [{
						id: '1',
						value: '每年'
					},
					{
						id: '1',
						value: '每季度'
					},
					{
						id: '1',
						value: '每月'
					},
				],
				columns12: [{
						title: 'HIMSI',
						key: 'HIMSI',
						align: 'center'
					},
					{
						title: 'MSISDN',
						key: 'MSISDN',
						align: 'center'
					},
					{
						title: 'ICCID',
						key: 'ICCID',
						align: 'center'
					},
					{
						title: '加入流量池时间',
						key: 'time',
						align: 'center'
					},
					{
						title: '已用流量',
						slot: 'used',
						align: 'center'
					},
					{
						title: '客户',
						key: 'Operators',
						align: 'center'
					},
					{
						title: '单日流量上限',
						key: 'up',
						align: 'center'
					},
					{
						title: '总流量上限',
						key: 'upcpunt',
						align: 'center'
					},
					{
						title: '操作',
						slot: 'action',
						align: 'center',
						width: '300px'
					},
				],
				data: [],
				columns: [{
						title: '卡类型',
						key: 'state',
						align: 'center',
						width: 242
					},
					// {
					// 	title: '卡号',
					// 	key: 'card',
					// 	align: 'center'
					// },
					{
						title: '使用流量',
						key: 'count',
						align: 'center',
						width: 242
					},
				],
				flowform: [],
				rules: {
					hnumber: [{
						required: true,
						message: "请输入HIMSI号码",
						trigger: "blur",
					}, {
						required: true,
						validator: checkPhone,
					}, ],
					mnumber: [{
						required: true,
						message: "请输入MSISDN号码",
						trigger: "blur",
					}, {
						required: true,
						validator: checkPhone,
					}, ],
					inumber: [{
						required: true,
						message: "请输入ICCID号码",
						trigger: "blur",
					}, {
						required: true,
						validator: checkPhone,
					}, ],
					oneupper: [{
						required: true,
						message: "请输入单日流量上限",
						trigger: "blur",
					}],
					totalupper: [{
						required: true,
						message: "请输入总流量上限",
						trigger: "blur",
					}],
					numbertype: [{
						required: true,
						message: "请选择号码类型",
						trigger: "blur",
					}],

				}

			}
		},
		mounted() {
			this.goPageFirst(0)
			var list = localData.localList
			this.currencyList = publicData.currencyList
			for (var n = 0; n < list.length; n++) {
				this.localList = this.localList.concat(list[n].value)
			}
		},
		methods: {
			goPageFirst(page) {
				this.loading = true
				var _this = this
				let pageNumber = page
				let pageSize = 10
				let poolname = this.form.poolname
				searchcust({
					pageNumber,
					pageSize,
					poolname
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.page = page
						this.total = res.data.total
						this.records = res.data.records
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			search() {
				this.goPageFirst(this.page)
			},
			// 查看使用流量详情
			showflow() {
				this.poolmodel = true
			},
			// 修改
			update() {
				this.updatemodel = true
				this.form = this.data[1]
			},
			// 删除
			remove() {
				this.$Modal.confirm({
					title: '确认删除该项？',
					onOk: () => {
						this.$Notice.success({
							title: '操作提醒',
							desc: '删除流量池成功！'
						})
						delpool(this.formdel).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提醒',
									desc: '删除流量池成功！'
								})
								this.deletemodel = false
								this.goPageFirst(0)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.loading = false
						})

					}
				});
			},
			// 单卡导入
			singlePool() {
				this.singlemodel = true

			},
			// 批量导入
			batchPool() {
				this.batchmodel = true
			},
			// 批量删除
			deletePool() {
				this.deletemodel = true
			},
			cancelModal() {
				this.deletemodel = false
				this.poolmodel = false
				this.updatemodel = false
				this.singlemodel = false
				this.batchmodel = false
				this.$refs.singleform.resetFields()
			},
			saveflow() {
				this.poolmodel = false
			},
			// 弹框修改
			updatesave() {
				this.$refs.form.validate(valid => {
					if (valid) {
						updatepool(this.form).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提醒',
									desc: '修改流量池成功！'
								})
								this.updatemodel = false
								this.goPageFirst(0)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.loading = false
						})
					}
				})
			},
			// 单卡导入
			singlesave() {
				this.$refs.singleform.validate(valid => {
					if (valid) {
						updatepool(this.singleform).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提醒',
									desc: '单卡导入成功！'
								})
								this.singlemodel = false
								this.goPageFirst(0)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.loading = false
						})
					}
				})
			},
			// 批量导入
			batchsave() {
				if (!this.file) {
					this.$Message.warning('请选择需要上传的文件')
				} else {
					this.$refs.form.validate(valid => {
						if (valid) {
							addpool(this.form).then(res => {
								if (res && res.code == '0000') {
									this.$Notice.success({
										title: '操作提醒',
										desc: '修改流量池成功！'
									})
									this.batchmodel = false
									this.goPageFirst(0)
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.loading = false
							})
						}
					})
				}
			},
			// 批量删除
			savedel() {
				if (!this.filedel) {
					this.$Message.warning('请选择需要上传的文件')
				} else {
					this.$refs.formdel.validate(valid => {
						if (valid) {
							delBatch(this.formdel).then(res => {
								if (res && res.code == '0000') {
									this.$Notice.success({
										title: '操作提醒',
										desc: '删除流量池成功！'
									})
									this.deletemodel = false
									this.goPageFirst(0)
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.loading = false
							})
						}
					})
				}
			},
			choose() {

			},
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			handleBeforeUpload(file) {
				// 校验文件格式
				if (!/.doc$/.test(file.name)) {
					this.$Notice.warning({
						title: '文件格式不正确',
						desc: '文件 ' + file.name + ' 格式不正确，请上传doc格式文件。'
					})
				} else {
					// 校验文件大小
					const filesize = file.size / 2048 / 2048 < 2;
					if (!filesize) {
						this.$Notice.warning({
							title: '文件大小不能超过2M',
							desc: '文件 ' + file.name + '文件大小不能超过2M。'
						})
					} else {
						this.file = file
					}
				}
				return false
			},
			removeFile() {
				this.file = ''
			},
			delfileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			delhandleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			delfileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			delhandleBeforeUpload(file) {
				// 校验文件格式
				if (/.mp3$/.test(file.name) || /.mp4$/.test(file.name)) {
					this.$Notice.warning({
						title: '文件格式不正确',
						desc: '文件 ' + file.name + ' 格式不正确，请上传jpg、jpeg、png、gif多种图片格式文件。'
					})
				} else {
					// 校验文件大小
					const filesize = file.size / 2048 / 2048 < 2;
					if (!filesize) {
						this.$Notice.warning({
							title: '文件大小不能超过2M',
							desc: '文件 ' + file.name + '文件大小不能超过2M。'
						})
					} else {
						this.filedel = file
					}
				}
				return false
			},
			delFile() {
				this.filedel = ''
			},
			// 选择重置周期
			selectcycle(id) {
				if (id === '是') {
					this.cycleflg = true
				} else {
					this.cycleflg = false
				}
			},

		}
	}
</script>

<style>

</style>
