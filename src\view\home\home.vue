<template>
	<div>
		<div v-show="!cooperationMode || cooperationMode == '3'">
			<Divider>
				<h3>{{welcome}}</h3>
			</Divider>
		</div>
		<div style="width: 100%; padding: 20px 2px 0px 2px;" v-show="cooperationMode == '1' || cooperationMode == '2'">
			<Row :gutter="12">
				<Col :xs="24" :lg="18" span="12">
				<!-- 代销 -->
				<Row :gutter="12" v-if="cooperationMode == '1'">
					<Col span="8" :xs="24" :md='12' :lg="8" :icon-size="30" v-for="(infor, i) in inforCardData"
						:key="`infor-${i}`">
					<Card shadow class="headerBox">
						<count-to :count="infor.count" :title="infor.title" :imgUrl="infor.imgUrl"
							:colors="infor.color"></count-to>
					</Card>
					</Col>
				</Row>
				<!-- A2Z -->
				<Row :gutter="12" v-else>
					<Col span="8" :xs="24" :md='12' :lg="12" :icon-size="30" v-for="(infor, i) in inforCardData2"
						:key="`infor-${i}`">
					<Card shadow class="headerBox">
						<count-to :count="infor.count" :title="infor.title" :imgUrl="infor.imgUrl"
							:colors="infor.color"></count-to>
					</Card>
					</Col>
				</Row>
				<!-- 套餐订购数 -->
				<Row>
					<Col span="24" :md="24" :lg="24" style="margin-top: 12px;">
					<Card shadow class="pakageBox">
						<chart-bar class="pakageBoxs" :value="barData" v-if="falg" />
					</Card>
					</Col>
				</Row>
				<Row :gutter="12">
					<Col span="8" :xs="24" :md='12' :lg="8" style="margin-top: 12px;">
					<Card shadow class="pieBox">
						<chart-pie class="pieDetails" :value="pieData2" :inText="title2" :number="number2"
							:text="$t('esimkazhang')" :minAngle="minAngle1" v-if="falg">
						</chart-pie>
					</Card>
					</Col>
					<Col span="8" :xs="24" :md='12' :lg="8" style="margin-top: 12px;">
					<Card shadow class="pieBox">
						<chart-pie class="pieDetails" :value="pieData4" :inText="title4" :number="number4"
							:text="$t('imsikazhang')" :minAngle="minAngle2" v-if="falg">
						</chart-pie>
					</Card>
					</Col>
					<Col span="8" :xs="24" :md='12' :lg="8" style="margin-top: 12px;">
					<Card shadow class="pieBox">
						<chart-pie class="pieDetails" :value="pieData5" :inText="title5" :number="number5"
							:text="$t('simkazhang')" :minAngle="minAngle3" v-if="falg">
						</chart-pie>
					</Card>
					</Col>
				</Row>
				</Col>
				<Col :xs="24" :lg="6" span="12">
				<Row>
					<Col span="24">
					<Card shadow class="infoBox">
						<p class="infomation">{{$t('announcement')}}</p>
						<div class="animate-box" ref="animateBox">
							<div :class="inforHeight > boxHeight ? 'animate' : ''"
							  ref="animate">{{text}}</div>
						</div>
					</Card>
					</Col>
				</Row>
				<Row>
					<Col span="24" style="margin-top: 12px;">
					<Card shadow class="pieBoxs">
						<chart-pie class="pieDetails" :value="pieData1" :inText="title3" :number="number3"
							:text="$t('cardkazhang')" :minAngle="minAngle4" v-if="falg">
						</chart-pie>
						<chart-twopie class="pieDetails" :value="pieData3" :inText="title3" :number="number3"
							:minAngle="minAngle5" v-if="falg"></chart-twopie>
					</Card>
					</Col>
				</Row>
				</Col>
			</Row>
		</div>
	</div>
</template>

<script>
	import InforCard from '@/components/info-card'
	import CountTo from './modal/count-to.vue'
	import {
		ChartPie,
		ChartBar,
		ChartTwopie
	} from '@/components/charts'
	import {
		searchcorpid,
		getKanBan
	} from '@/api/channel'
	import {
		getAnnouncement
	} from '@/api/system/announcement'
	import imgOne from '@/assets/images/distributionModel.png'
	import imgTwo from '@/assets/images/availableLimit.png'
	import imgThree from '@/assets/images/monthAmount.png'
	import imgFour from '@/assets/images/creditLimit .png'
	import imgFive from '@/assets/images/preDepositLimit.png'
	import imgSix from '@/assets/images/usedAmount.png'

	export default {
		name: 'home',
		components: {
			InforCard,
			CountTo,
			ChartPie,
			ChartBar,
			ChartTwopie
		},
		data() {
			return {
				corpId: '',
				welcome: '欢迎使用CMI全球卡业务网站',
				cooperationMode: '', //合作模式
				text: "",
				title1: this.$t('support.Used'),
				title2: this.$t('esimTotal'),
				title3: this.$t('totalCards'),
				title4: this.$t('imsiTotal'),
				title5: this.$t('simTotal'),
				number1: '',
				number2: '',
				number3: '',
				number4: '',
				number5: '',
				falg: false,
				minAngle1: 3,
				minAngle2: 3,
				minAngle3: 3,
				minAngle4: 3,
				minAngle5: 3,
				boxHeight: '',
				inforHeight: '',
				inforCardData: [{
						title: this.$t('support.totalAmount'),
						imgUrl: imgOne,
						count: '',
						color: 'rgba(66, 137, 244, 0.1)'
					},
					{
						title: this.$t('deposit.deposit_money'),
						imgUrl: imgTwo,
						count: '',
						color: 'rgba(238, 249, 247, 1)',
					},
					{
						title: this.$t('quotaUsedMonth'),
						imgUrl: imgThree,
						count: '',
						color: 'rgba(255, 246, 235, 1)'
					},
				],
				inforCardData2: [{
						title: this.$t('AtoZTotal'),
						imgUrl: imgFour,
						count: '',
						color: 'rgba(66, 137, 244, 0.1)'
					},
					// {
					// 	title: this.$t('AtoZTotal'),
					// 	imgUrl: imgFive,
					// 	count: '',
					// 	color: 'rgba(238, 249, 247, 1)'
					// },
					{
						title: this.$t('creditsUsed'),
						imgUrl: imgSix,
						count: '',
						color: 'rgba(255, 246, 235, 1)'
					},
				],
				barData: [],
				pieData1: [{
						value: '',
						name: this.$t('support.Used')
					},
					{
						value: '',
						name: this.$t('unused')
					},
				],
				pieData2: [{
						value: '',
						name: this.$t('support.Used')
					},
					{
						value: '',
						name: this.$t('unused')
					},
				],
				pieData3: [{
						value: '',
						name: this.$t('stock.eSIM')
					},
					{
						value: '',
						name: this.$t('shitika')
					},
					{
						value: '',
						name: this.$t('imsika')
					},
				],
				pieData4: [{
						value: '',
						name: this.$t('support.Used')
					},
					{
						value: '',
						name: this.$t('unused')
					},
				],
				pieData5: [{
						value: '',
						name: this.$t('support.Used')
					},
					{
						value: '',
						name: this.$t('unused')
					},
				],
			}
		},
		computed: {

		},
		methods: {
			// 获取数据
			goPageFirst: function() {
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						this.corpId = res.data
						getKanBan({
							corpId: this.corpId,
							mode: this.cooperationMode
						}).then(res => {
							if (res && res.code == '0000') {
								let data = res.data
								this.barData = data.saleStatList
								if (this.cooperationMode == '1') {
									//代销卡
									this.inforCardData[0].count = data.totalDeposit == null ? '' : data
										.totalDeposit.toLocaleString()
									this.inforCardData[1].count = data.deposit == null ? '' : data
										.deposit.toLocaleString()
									this.inforCardData[2].count = data.usedDepositMonth === null ? '' :
										data.usedDepositMonth.toLocaleString()
								} else {
									//A2Z卡
									this.inforCardData2[0].count = data.a2zPreDeposit == null ? '' :
										data.a2zPreDeposit == "99999999.99" ? "0" :
										data.a2zPreDeposit.toLocaleString()
									//A2Z不展示总信用额度
									// this.inforCardData2[1].count = data.a2zTotalDeposit == null ? '' :
									// 	data.a2zTotalDeposit.toLocaleString()
									this.inforCardData2[1].count = data.a2zUsedDepositMonth == null ?
										'' : data.a2zUsedDepositMonth.toLocaleString()
								}
								// 后端返回的数据都是字符串，需要先将字符串转为数字类型后相加，再转为string赋值给图形，因为pie接收的类型是string
                let esimCardUsedCount = parseInt(data.esimCardUsedCount, 10) || 0; // 转换为整数，如果转换失败则默认为0
								let imsiCardUsedCount = parseInt(data.imsiCardUsedCount, 10) || 0;
								let classicCardUsedCount = parseInt(data.classicCardUsedCount, 10) || 0;
                let totalCardUseCount = esimCardUsedCount + imsiCardUsedCount + classicCardUsedCount;
                let cardUseCount = String(totalCardUseCount);
								// 卡总量
								this.number1 = String(cardUseCount)
								this.number2 = String(data.esimCardCount)
								this.number4 = String(data.imsiCardCount)
								this.number3 = String(data.cardCount)
								this.number5 = String(data.classicCardCount)
								//已使用
								this.pieData1[0].value = String(cardUseCount)
								this.pieData2[0].value = String(data.esimCardUsedCount)
								this.pieData4[0].value = String(data.imsiCardUsedCount)
								this.pieData5[0].value = String(data.classicCardUsedCount)
								//未使用
								this.pieData1[1].value = String(data.cardCount - cardUseCount)
								this.pieData2[1].value = String(data.esimCardCount - data
									.esimCardUsedCount)
								this.pieData4[1].value = String(data.imsiCardCount - data
									.imsiCardUsedCount)
								this.pieData5[1].value = String(data.classicCardCount - data
									.classicCardUsedCount)
								//卡总数
								this.pieData3[0].value = String(data.esimCardCount)
								this.pieData3[1].value = String(data.classicCardCount)
								this.pieData3[2].value = String(data.imsiCardCount)

								var esimUnused = data.esimCardCount - data.esimCardUsedCount //esim未使用
								var imsiUnused = data.imsiCardCount - data.imsiCardUsedCount //imsi未使用
								var classicUnused = data.classicCardCount - data.classicCardUsedCount //实体未使用
								var cardUnused = data.cardCount - cardUseCount //卡总量未使用
								if ((data.esimCardCount == data.esimCardUsedCount && data.esimCardUsedCount > 0) ||
								 (esimUnused == data.esimCardCount && esimUnused > 0)) {
									this.minAngle1 = 0
								}
								if ((data.imsiCardCount == data.imsiCardUsedCount && data.imsiCardUsedCount > 0) ||
								 (imsiUnused == data.imsiCardCount && imsiUnused > 0)) {
									this.minAngle2 = 0
								}
								if ((data.classicCardCount == data.classicCardUsedCount && data.classicCardUsedCount > 0) ||
								 (classicUnused == data.classicCardCount && classicUnused > 0)) {
									this.minAngle3 = 0
								}
								if ((data.cardCount == cardUseCount && cardUseCount > 0) || (cardUnused == data.cardCount && cardUnused > 0)) {
									this.minAngle4 = 0
								}
								if ((data.cardCount == data.esimCardCount && data.esimCardCount > 0) || (data.cardCount == data.classicCardCount &&
								 data.classicCardCount > 0) || (data.cardCount == data.imsiCardCount && data.imsiCardCount > 0)) {
									this.minAngle5 = 0
								}
								//数据没加载完之前图形不出来
								this.falg = true
							} else {
								throw res
							}
						}).catch((err) => {
							console.error(err)
						}).finally(() => {})
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {})
			},
			//获取公告
			getAnnouncement: function() {
				getAnnouncement().then(res => {
					if (res && res.code == '0000') {
						this.text = res.data.notice
						this.$nextTick(() => {
							// 盒子高度
							let el = this.$refs.animateBox
							this.boxHeight = el.offsetHeight
							//文本高度
							let el2 = this.$refs.animate
							this.inforHeight = el2.offsetHeight
						})
					} else {
						throw res
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {})
			}
		},
		mounted() {
			let lang = this.$i18n.locale
			if (lang === 'en-US') {
				this.welcome = 'Welcome'
			}
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			//如果是渠道商  合作模式
			if (this.cooperationMode=='1' || this.cooperationMode=='2') {
				this.goPageFirst()
				this.getAnnouncement()
			}
		}
	}
</script>

<style lang="less">
	.count-style {
		font-size: 50px;
	}

	.headerBox {
		width: 100%;
		// height: 112px;
		padding: 8px;
	}

	.pakageBox {
		height: 358px;
		padding: 8px;
	}

	.pakageBoxs {
		height: 310px;
	}

	.pieBox {
		height: 274px;
		box-sizing: border-box;
		padding: 8px 8px 14px 8px;
	}

	.pieDetails {
		height: 220px;
	}

	.pieBoxs {
		height: 488px;
		padding: 8px 8px 14px 8px;
	}

	.infoBox {
		height: 268px;
		padding: 8px 8px 12px 8px;
	}

	.infomation {
		height: 20px;
		font-size: 14px;
		// font-family: PingFang SC-Semibold, PingFang SC;
		font-weight: 600;
		color: #323232;
		line-height: 20px;
	}

	.animate-box {
		height: 180px;
		font-size: 14px;
		// font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 20px;
		overflow: hidden;
		word-break:break-all;
		margin-top: 16px;
	}

	.animate {
		animation: 15s wordsLoop linear infinite normal;
	}

	@keyframes wordsLoop {
		0% {
			transform: translateY(0);
			-webkit-transform: translateY(0);
		}

		100% {
			transform: translateY(-100%);
			-webkit-transform: translateY(-100%);
		}
	}

	@-webkit-keyframes wordsLoop {
		0% {
			transform: translateY(0);
			-webkit-transform: translateY(0);
		}

		100% {
			transform: translateY(-100%);
			-webkit-transform: translateY(-100%);
		}
	}

	.animate:hover {
		animation-play-state: paused;
	}
</style>
