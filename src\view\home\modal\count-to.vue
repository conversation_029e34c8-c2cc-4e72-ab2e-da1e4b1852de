<template>
	<div class="count-to-wrapper">
		<div class="imgBox" :style="styleVar">
			<img :src="imgUrl" class="imgOneBox">
		</div>
		<div class="descBox">
			<p class="countBox">{{count}}</p>
			<p class="titleBox">
				{{title}}
				<Tooltip max-width="200" placement="bottom" transfer>
				    <img v-if="title == '当月已用额度' || title == 'Credit Used in Bill Cycle'"
				        class="helpImgBox" :src="helpImg">
					<div slot="content" class="tooltip">
						<p class="tooltipText">{{$t('twofivebefore')}}</p>
						<p class="tooltipText">{{$t('twofiveafter')}}</p>
					</div>
				</Tooltip>
			</p>
		</div>
	</div>
</template>

<script>
	import helpImg from '@/assets/images/help.png'
	export default {
		name: 'CountTo',
		components: {
			
		},
		props: {
			count: String,
			title: String,
			imgUrl: {
				type: String,
				default: ""
			},
			colors: {
				type: String,
				default: ""
			}
		},
		data() {
			return {
				helpImg: helpImg,
			}
		},
		computed: {
			styleVar() {
				return {
					'background-color': this.colors
				}
			}
		}
	}
</script>

<style lang="less">
	.count-to-wrapper {
		display: flex;
		justify-content: flex-start;
		align-items: center;
	}
	.imgBox {
		height: 64px;
		padding: 8px;
		border-radius: 8px 8px 8px 8px;
		display: flex; 
		justify-content: center;
		align-items: center;
		margin-right: 20px;
	}
	.imgBox {
		background-color: var(background-color)
	}
	.imgOneBox {
		border-color: rgba(0, 0, 0, 0.5);
		width: 48px;
		height: 48px;
	}
	.descBox {
		display: flex;
		flex-direction: column;
	}
	.countBox {
		word-break: break-all;
		font-size: 26px;
		// font-family: PingFang SC-Semibold, PingFang SC;
		font-weight: 600;
		color: #323232;
		line-height: 32px;
	}
	.titleBox {
		height: 20px;
		margin-top: 12px;
		font-size: 14px;
		// font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 20px;
		opacity: 0.8;
	}
	.helpImgBox {
		width: 13px;
		height: 13px;
		border-radius: 0px 0px 0px 0px;
		opacity: 1;
		vertical-align: middle;
		cursor: pointer;
		position: relative;
	}
	.tooltip {
		width: 100%;
		border-radius: 8px;
	}
	.tooltipText {
		font-size: 12px;
		// font-family: PingFang SC-Regular, PingFang SC;
		font-weight: 400;
		color: #FFFFFF;
		line-height: 18px;
	}
</style>