<template>
  <div :class="['4'].includes(pageType) ? 'content-box1' : 'content-box'">
    <a ref="downloadLink" style="display: none"></a>
    <!-- 调账 -->
    <Card style="width: 90%; height: 100%;overflow-y: auto;" v-if="['1', '2', '3'].includes(pageType)">
      <Spin size="large" fix v-if="spinShow">
        <Icon type="ios-loading" size=24 class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
      <h1 style="align-items: center; padding: 30px 0;">调账审批详情页</h1>
      <!-- 审批详情 -->
      <Row class="row-box">
        <Col :xs="24" :md='12' :lg="8" span="8" class="col-box">
        <span class="nowarp">申请编号：</span>&nbsp;&nbsp;<span>{{ propinfo.applyId }}</span>
        </Col>
        <Col :xs="24" :md='12' :lg="8" span="8" class="col-box">
        <span class="nowarp">调整方式：</span>&nbsp;&nbsp;<span>{{ propinfo.adjustWay }}</span>
        </Col>
        <Col :xs="24" :md='12' :lg="8" span="8" class="col-box">
        <span class="nowarp">调账类型：</span>&nbsp;&nbsp;<span>{{ propinfo.adjustType }}</span>
        </Col>
        <Col :xs="24" :md='12' :lg="8" span="8" class="col-box">
        <span class="nowarp">客户名称：</span>&nbsp;&nbsp;<span>{{ propinfo.corpName }}</span>
        </Col>
        <Col :xs="24" :md='12' :lg="8" span="8" class="col-box">
        <span class="nowarp">业务类型：</span>&nbsp;&nbsp;<span>{{ propinfo.businessType }}</span>
        </Col>
        <Col :xs="24" :md='12' :lg="8" span="8" class="col-box">
        <span class="nowarp">EBSCode：</span>&nbsp;&nbsp;<span>{{ propinfo.ebscode }}</span>
        </Col>
        <Col :xs="24" :md='12' :lg="8" span="8" class="col-box">
        <span class="nowarp">原账单发票号：</span>&nbsp;&nbsp;<span>{{ propinfo.invoiceNo }}</span>
        </Col>
        <Col :xs="24" :md='12' :lg="8" span="8" class="col-box">
        <span class="nowarp">调账单号：</span>&nbsp;&nbsp;<span>{{ propinfo.adjustId }}</span>
        </Col>
        <Col :xs="24" :md='12' :lg="8" span="8" class="col-box">
        <span class="nowarp">调账总额：</span>&nbsp;&nbsp;<span>{{ propinfo.totalAdjustment }}</span>
        </Col>
        <Col span="24" class="col-box">
        <span class="nowarp">调账原因：</span>&nbsp;&nbsp;<span>{{ propinfo.adjustReason }}</span>
        </Col>
        <Col span="24" class="col-box">
        <span class="nowarp">调账描述：</span>&nbsp;&nbsp;<span>{{ propinfo.message }}</span>
        </Col>
        <Col span="24" class="col-box" v-if="pageType == '3'">
        <span class="nowarp">审批结果：</span>&nbsp;&nbsp;<span>{{propinfo.authStatus}}{{propinfo.noPassReason}}</span>
        </Col>
      </Row>
      <Row class="row-box2">
        <Col :xs="24" :md='12' :lg="8" span="8" class="col-box">
        <span>说明文件：</span>&nbsp;&nbsp;
        <Button type="warning" ghost :loading="downInfoLoading" @click="rebuild('1')">点击下载</Button>
        </Col>
        <Col :xs="24" :md='12' :lg="8" span="8" class="col-box" v-if="propinfo.authStatus == '审核完成' && pageType == '3'">
        <span>重新生成Invoice：</span>&nbsp;&nbsp;
        <Button type="info" ghost :loading="downLoading" @click="rebuild('2')">点击下载</Button>
        </Col>
      </Row>
      <!-- 表格 -->
      <div class="table_box">
        <Table :columns="columns" :data="data" :ellipsis="true" :loading="loading"></Table>
      </div>
      <!-- 操作按钮 -->
      <div class="op_btn_box">
        <!-- 业务经理待办/财务专员待办 -->
        <div v-if="pageType == '1' || pageType == '2'">
          <Button style="margin: 0 4px" type="success" :disabled="showButton" :loading="passLoading"
            @click="approval('1',pageType)">审批通过</Button>
          <Button style="margin: 0 4px" type="error" :disabled="showButton"
            @click="approval('2',pageType)">审批不通过</Button>
        </div>
        <!-- 申请人待办 -->
        <div v-if="pageType == '3'">
          <Button style="margin: 0 4px" type="success" :disabled="showButton" :loading="receivedLoading"
            @click="received">已阅</Button>
        </div>
      </div>
      <!-- 不通过弹窗 -->
      <Modal v-model="modal" title="确认执行审批不通过？" @on-cancel="cancelModal" :mask-closable="false" width="60%">
        <Form ref="formItemReason" :model="formItemReason" :rules="ruleValidate" @submit.native.prevent>
          <FormItem prop="reasonText">
            <Input v-model="formItemReason.reasonText" maxlength="200" placeholder="请输入不通过原因……"></Input>
          </FormItem>
        </Form>
        <div slot="footer" style="text-align: right;">
          <Button style="margin-left: 8px" @click="cancelModal">取消</Button>
          <Button type="primary" :loading="noPassLoading" @click="confirm">确定</Button>
        </div>
      </Modal>
    </Card>

    <!-- 营销活动-代销添加充值记录待办 - 4-->
    <div class="card-box2" v-if="['4'].includes(pageType)">
      <div v-if="showInfo">
        <Spin size="large" fix v-if="spinShow">
          <Icon type="ios-loading" size=24 class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>

        <h1 class="h1-box" style="text-align: center; margin: 20px 0;">添加充值记录审批</h1>

        <div style="display: flex; flex-wrap: wrap; gap: 16px;">
          <!-- 左侧两卡片 (70%宽度) -->
          <div style="flex: 7; min-width: 300px; display: flex; flex-direction: column; gap: 16px;">
            <!-- 基本信息卡片 -->
            <Card style="flex: 1;">
              <div slot="title" class="info-header">
                <Icon type="ios-information-circle" /> 基本信息
              </div>
              <Form ref="formObj" :model="formItem" :label-width="80">
                <Row :gutter="16">
                  <Col :xs="24" :md="12">
                  <FormItem label="公司名称:">
                    <Input v-model="formItem.companyName" readonly />
                  </FormItem>
                  </Col>
                  <Col :xs="24" :md="12">
                  <FormItem label="公司币种:">
                    <Input v-model="formItem.currencyCode" readonly />
                  </FormItem>
                  </Col>
                  <Col span="24">
                  <div style="max-height: 400px; overflow-y: auto;">
                    <Table :columns="talbeColumns" :data="talbedata" :loading="loading"
                      style="width: 100%; margin-bottom: 16px;">
                    </Table>
                  </div>
                  </Col>
                </Row>
              </Form>
            </Card>

            <!-- 文件下载卡片 -->
            <Card style="flex: 1; min-height: 150px;">
              <div slot="title" class="info-header">
                <Icon type="ios-download" /> 文件下载
              </div>
              <!-- 文件下载内容 -->
            </Card>
          </div>

          <!-- 右侧两卡片 (30%宽度) -->
          <div style="flex: 3; min-width: 250px; display: flex; flex-direction: column; gap: 16px;">
            <!-- 操作卡片 -->
            <Card style="flex: 1;">
              <div slot="title" class="info-header">
                <Icon type="ios-build" /> 操作信息
              </div>
              <Form ref="formItemReason" :model="formItemReason" :rules="ruleValidate" @submit.native.prevent>
                <FormItem>
                  <Input v-model="formItemReason.reasonText" clearable type="textarea"
                    :autosize="{minRows: 3, maxRows: 10}" placeholder="不通过原因..." />
                </FormItem>
              </Form>
              <div style="display: flex; justify-content: center; gap: 24px; margin-top: 16px;">
                <Button type="error" icon="md-close" :disabled="showButton" :loading="noPassLoading"
                  @click="examine('3')">
                  审批不通过
                </Button>
                <Button type="success" icon="md-checkmark" :disabled="showButton" :loading="passLoading"
                  @click="examine('0')">
                  审批通过
                </Button>
              </div>
            </Card>

            <!-- 空白卡片 -->
            <Card style="flex: 1; min-height: 150px;">
              <!-- 空白卡片内容 -->
            </Card>
          </div>
        </div>
      </div>

      <div v-else class="error-message">
        <h4>请求数据失败，请重新进入！</h4>
      </div>
    </div>

    <!-- 营销活动-结算审批（A2Z/代销）-5-->
    <div style="width: 100%; height: 100vh; display: flex; flex-direction: column;" v-if="['5'].includes(pageType)">
      <!-- Loading 状态 -->
      <Spin size="large" fix v-if="spinShow">
        <Icon type="ios-loading" size=24 class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>

      <!-- 标题 -->
      <h1 style="margin: 30px 0 20px 30px;">营销活动结算审批</h1>

      <!-- 主内容区 -->
      <div style="flex: 1; display: flex; overflow: hidden; padding: 0 16px;">
        <!-- 左侧区域 (60%) -->
        <div style="flex: 6; display: flex; flex-direction: column; min-width: 0; padding: 0 8px; overflow: hidden;">
          <!-- 基本信息卡片 (60%高度) -->
          <Card
            style="flex: 6; display: flex; flex-direction: column; margin-bottom: 16px; min-height: 0; overflow: hidden;">
            <div slot="title" style="color: #1890ff; font-size: 16px; font-weight: bold; text-align: left;">
              <Icon type="ios-information-circle" /> 基本信息
            </div>
            <div style="flex: 1; display: flex; flex-direction: column; min-height: 0; overflow: hidden;">
              <Row :gutter="16" style="margin-bottom: 16px; flex-shrink: 0;">
                <Col :xs="24" :md="12" style="margin-bottom: 16px;">
                <div style="display: flex; align-items: center;">
                  <span style="flex: 1; text-align: right; padding-right: 10px;">活动名称：</span>
                  <span style="flex: 1; text-align: left;">{{ marketingActivities.campaignName }}</span>
                </div>
                </Col>
                <Col :xs="24" :md="12" style="margin-bottom: 16px;">
                <div style="display: flex; align-items: center;">
                  <span style="flex: 1; text-align: right; padding-right: 10px;">适用合作模式：</span>
                  <span style="flex: 1; text-align: left;">
                    {{ cooperationMode == '1' ? '代销' : cooperationMode == '2' ? 'A2Z' : '' }}
                  </span>
                </div>
                </Col>
                <Col :xs="24" :md="12" style="margin-bottom: 16px;">
                <div style="display: flex; align-items: center;">
                  <span style="flex: 1; text-align: right; padding-right: 10px;">活动开始时间：</span>
                  <span style="flex: 1; text-align: left;">{{ marketingActivities.startTime }}</span>
                </div>
                </Col>
                <Col :xs="24" :md="12" style="margin-bottom: 16px;">
                <div style="display: flex; align-items: center;">
                  <span style="flex: 1; text-align: right; padding-right: 10px;">活动结束时间：</span>
                  <span style="flex: 1; text-align: left;">{{ marketingActivities.endTime }}</span>
                </div>
                </Col>
                <Col :xs="24" :md="12" style="margin-bottom: 16px;">
                <div style="display: flex; align-items: center;">
                  <span style="flex: 1; text-align: right; padding-right: 10px;">总计返利：</span>
                  <span style="flex: 1; text-align: left;">{{ marketingActivities.totalRebate }}</span>
                </div>
                </Col>
                <Col :xs="24" :md="12" style="margin-bottom: 16px;">
                <div style="display: flex; justify-content: center;">
                  <Button type="warning" ghost :disabled="showButton" :loading="downLoading"
                    @click="exportMarketingFile(null, '1')" style="width: 200px;">导出</Button>
                </div>
                </Col>
              </Row>

              <!-- 表格区域 -->
              <div style="flex: 1; min-height: 0; display: flex; flex-direction: column; overflow: hidden;">
                <div style="max-height: 240px; overflow: auto;">
                  <Table :data="mergedData" :columns="cooperationMode == '1' ? distributionColumns : A2ZColumns"
                    style="width: 100%;" @on-select="handleSingleSelect" @on-select-cancel="handleSelectCancel"
                    @on-select-all="handleSelectAll" @on-select-all-cancel="handleSelectAllCancel" row-key="id"
                    ref="table">
                    <template slot-scope="{ row, index }" slot="action">
                      <Button type="success" ghost size="small" @click="showRebateEditModal(row)"
                        :disabled="isEditable || row.status != '1'">编辑</Button>
                    </template>
                  </Table>
                </div>
                <Page :current="currentPage" :total="total" :page-size="pageSize" @on-change="handlePageChange"
                  show-total show-elevator style="margin-top: 16px; text-align: center; flex-shrink: 0;" />
              </div>
            </div>
          </Card>

          <!-- 文件下载卡片 (40%高度) -->
          <Card style="flex: 4; display: flex; flex-direction: column; min-height: 0; overflow: hidden;">
            <div slot="title" style="color: #1890ff; font-size: 16px; font-weight: bold; text-align: left;">
              <Icon type="ios-download" /> 文件下载
            </div>
            <div style="flex: 1; display: flex; flex-direction: column; min-height: 0; overflow: hidden;">
              <div style="max-height: 220px; overflow: auto;">
                <Table :data="fileData" :columns="fileColumns" style="width: 100%;">
                  <template slot-scope="{ row, index }" slot="action">
                    <Button type="info" ghost size="small" @click="exportMarketingFile(row, '2')">下载</Button>
                  </template>
                </Table>
              </div>
            </div>
          </Card>
        </div>

        <!-- 右侧区域 (40%) -->
        <div style="flex: 4; display: flex; flex-direction: column; min-width: 0; padding: 0 8px; overflow: hidden;">
          <!-- 操作信息卡片 (自适应高度) -->
          <Card style="margin-bottom: 16px; flex-shrink: 0;">
            <div slot="title" style="color: #1890ff; font-size: 16px; font-weight: bold; text-align: left;">
              <Icon type="ios-settings" /> 操作信息
            </div>
            <div style="padding: 16px;">
              <Input v-model="infoValue" maxlength="200" type="textarea" :rows="4"
                style="margin-bottom: 16px; width: 100%;" />
              <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                <Button type="info" ghost style="flex: 1; min-width: 120px;" :loading="approveAllLoading"
                  :disabled="showButton" @click="approveAll">一键全部审批通过</Button>
                <Button type="success" ghost style="flex: 1; min-width: 120px;" :loading="toggleApproveLoading"
                  :disabled="showButton" @click="toggleApprove">审批通过</Button>
                <Button type="error" ghost style="flex: 1; min-width: 120px;" :loading="toggleNoApproveLoading"
                  :disabled="showButton" @click="toggleNoApprove">审批不通过</Button>
                <Button type="primary" ghost style="flex: 1; min-width: 120px;" :disabled="showButton"
                  @click="fileApprove">文件审批</Button>
              </div>
            </div>
          </Card>

          <!-- 流转意见卡片 (剩余高度) -->
          <Card style="flex: 1; display: flex; flex-direction: column; min-height: 0; overflow: hidden;">
            <div slot="title" style="color: #1890ff; font-size: 16px; font-weight: bold; text-align: left;">
              <Icon type="ios-chatboxes" /> 流转意见
            </div>
            <div style="flex: 1; min-height: 0; display: flex; flex-direction: column; overflow: hidden;">
              <div style="max-height: 400px; overflow: auto;">
                <Table :data="opinionData" :columns="opinionColumns" style="width: 100%;">
                  <template slot-scope="{ index }" slot="id">
                    {{ index + 1 }}
                  </template>
                </Table>
              </div>
            </div>
          </Card>
        </div>
      </div>

      <!-- 文件审批弹窗 -->
      <Modal title="文件审批" v-model="fileModal" :mask-closable="false" width="600px" @on-cancel="cancelFileModal">
        <Form ref="fileObj" :model="fileObj" :rules="ruleobj" :label-width="100" :label-height="100" inline
          style="font-weight:bold;" @submit.native.prevent>
          <FormItem label="文件" style="width:510px" prop="file">
            <Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/" :action="uploadUrl"
              :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
              :on-progress="fileUploading">
              <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>点击或拖拽文件上传</p>
              </div>
            </Upload>
            <ul class="ivu-upload-list" v-if="file" style="width: 100%;">
              <li class="ivu-upload-list-file ivu-upload-list-file-finish">
                <span>
                  <Icon type="ios-folder" />{{file.name}}
                </span>
                <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
              </li>
            </ul>
            <div style="width: 100%;">
              <Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
            </div>
          </FormItem>
        </Form>
        <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
          <Button @click="cancelFileModal">取消</Button>
          <Button type="primary" :loading="uploadLoading" @click="handleUpload">确定</Button>
        </div>
      </Modal>

      <!-- 编辑预计返利 -->
      <Modal v-model="rebateModal" title="编辑预计返利" :mask-closable="false">
        <Form ref="rebateEditForm" :model="currentRebateEdit" :rules="ruleEdit" :label-width="100"
          @submit.native.prevent>
          <FormItem label="预计返利" prop="value">
            <Input v-model="currentRebateEdit.value" placeholder="请输入预计返利" clearable />
          </FormItem>
        </Form>
        <div slot="footer">
          <Button @click="rebateModal = false">取消</Button>
          <Button type="primary" @click="saveRebateEdit">确定</Button>
        </div>
      </Modal>
    </div>
  </div>
</template>

<script>
  import store from '@/store'
  import {
    getToken,
    getShowButton,
  } from "@/api/jumpOperations/operationAgencyApproval";
  import {
    exportFile,
    businessManagerAudits,
    financialAudits,
    applicantConfirmation,
    searchAdjustInfo,
    getRechargeRecord,
    examineRechargeRecord,
    exportMarketingFile,
    marketingFileApproval,
    getCampaign,
    getMktSettlementPage,
    download,
    getOpinions
  } from '@/api/jumpOperations/billingAdjust.js'
  export default {
    data() {
      // 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
      const validateUpload = (rule, value, callback) => {
        if (!this.file) {
          callback(new Error("请上传文件！"))
        } else {
          callback()
        }
      };
      return {
        showInfo: true,
        showButton: false,
        spinShow: true,
        modal: false,
        fileModal: false,
        rebateModal: false,
        loading: false,
        searchloading: false, //查询加载
        downLoading: false,
        downInfoLoading: false,
        noPassLoading: false,
        passLoading: false,
        receivedLoading: false,
        uploadLoading: false,
        approveAllLoading: false,
        toggleApproveLoading: false,
        toggleNoApproveLoading: false,
        pageType: '', //调账审批 1-业务经理 2-财务专员 3-申请人 ； 营销活动审批 4-添加充值记录审批 5-营销活动结束结算审批（A2Z/代销）
        token: '',
        userName: '',
        ticket: "",
        queryParams: "", //url
        isEditable: false,
        currentPage: 1,
        pageSize: 10,
        rawData: [],
        total: 0,
        cooperationMode: "",
        fileType: "",
        uploadUrl: "",
        message: "请上传.xlsx文件，大小限制为10MB以内。",
        file: "",
        infoValue: "",
        selectedRows: [], // 存储选中的行
        editedRebates: {}, // 存储编辑过的预计返利字段
        currentRebateEdit: {
          rowId: null,
          value: ''
        },
        fileObj: {
          file: "",
        },
        propinfo: {
          applyId: '',
          adjustWay: '', //1重新生成 2赔付
          adjustType: '',
          corpName: '',
          businessType: '',
          ebscode: '',
          invoiceNo: '',
          adjustId: '',
          totalAdjustment: '',
          adjustReason: '',
          message: '',
        },
        reasons: [{
            value: '1',
            label: "渠道商收入",
          },
          {
            value: '2',
            label: "其他客户收入",
          },
          {
            value: '3',
            label: "其他客户收入",
          },
        ],
        formItem: {
          companyName: "",
          currencyCode: "",
        },
        marketingActivities: {
          campaignName: "",
          cooperationMode: "",
          startTime: "",
          endTime: "",
          rechargeAmount: "",
          totalRebate: "",
        },
        data: [], //表格列表
        talbedata: [],
        fileData: [], //文件下载列表
        opinionData: [], //流转意见列表
        columns: [{
            title: "服务开始时间",
            key: 'svcStartTime',
            minWidth: 160,
            align: 'center',
            tooltip: true, // 开启 tooltip
          },
          {
            title: "服务结束时间",
            key: 'svcEndTime',
            minWidth: 160,
            align: 'center',
            tooltip: true, // 开启 tooltip
          },
          {
            title: "合同主体",
            key: 'contract',
            minWidth: 150,
            align: 'center',
            tooltip: true
          },
          {
            title: "原账单币种",
            key: 'currency',
            minWidth: 150,
            align: 'center',
            render: (h, params) => {
              const row = params.row;
              const text = row.currency == '156' ? "CNY" : row.currency == '840' ? "USD" : row.currency == '344' ?
                "HKD" :
                '';
              return h('label', text);
            }
          },
          {
            title: "原发票金额",
            key: 'oldAmount',
            minWidth: 150,
            align: 'center',
            tooltip: true
          },
          {
            title: "调账币种",
            key: 'currency',
            minWidth: 150,
            align: 'center',
            render: (h, params) => {
              const row = params.row;
              const text = row.currency == '156' ? "CNY" : row.currency == '840' ? "USD" : row.currency == '344' ?
                "HKD" :
                '';
              return h('label', text);
            }
          },
          {
            title: "调账金额",
            key: 'totalAdjustment',
            minWidth: 150,
            align: 'center',
            tooltip: true
          },
          {
            title: "账期",
            key: 'billingPeriod',
            minWidth: 150,
            align: 'center',
            tooltip: true
          },
        ],
        talbeColumns: [{
            title: "充值金额",
            key: 'rechargeAmount',
            minWidth: 160,
            align: 'left',
            tooltip: true, // 开启 tooltip
          },
          {
            title: "充值时间",
            key: 'settlementTime',
            minWidth: 160,
            align: 'left',
            tooltip: true, // 开启 tooltip
          },
        ],
        distributionColumns: [{
            type: 'selection',
            width: 60,
            align: 'center',
            disabled: (row) => row._disabled
          },
          {
            title: '客户名称',
            key: 'companyName',
            align: 'center',
            tooltip: true,
            minWidth: 150,
          },
          {
            title: '已充值金额',
            key: 'rechargeAmount',
            align: 'center',
            tooltip: true,
            minWidth: 150,
          },
          {
            title: '预计返利',
            key: 'actualReturn',
            align: 'center',
            tooltip: true,
            minWidth: 150,
          },
          {
            title: '操作',
            slot: 'action',
            align: 'center',
            tooltip: true,
            width: 100,
          }
        ],
        A2ZColumns: [{
            type: 'selection',
            width: 60,
            align: 'center',
            disabled: (row) => row._disabled
          },
          {
            title: '客户名称',
            key: 'companyName',
            align: 'center',
            tooltip: true,
            minWidth: 150,
          },
          {
            title: '上期金额',
            key: 'preAmount',
            align: 'center',
            tooltip: true,
            minWidth: 150,
          },
          {
            title: '本期金额',
            key: 'currAmount',
            align: 'center',
            tooltip: true,
            minWidth: 150,
          },
          {
            title: '预计返利',
            key: 'actualReturn',
            align: 'center',
            tooltip: true,
            minWidth: 150,
          },
          {
            title: '操作',
            slot: 'action',
            align: 'center',
            tooltip: true,
            width: 100,
          }
        ],
        fileColumns: [{
            title: '文件名',
            key: 'paramFile',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, params) => {
              const row = params.row;
              const parts = row.paramFile.split('_');
              const text = parts.slice(1).join('_');
              return h('span', text);
            },
          },
          {
            title: '上传时间',
            key: 'createTime',
            align: 'center',
            tooltip: true,
            minWidth: 150,
          },
          {
            title: '上传人',
            key: 'username',
            align: 'center',
            tooltip: true,
            minWidth: 120,
          },
          {
            title: '点击下载',
            slot: 'action',
            align: 'center',
            tooltip: true,
            width: 90,
          },
        ],
        opinionColumns: [{
            title: '序号',
            slot: 'id',
            align: 'center',
            tooltip: true,
            minWidth: 80,
          },
          {
            title: '操作人',
            key: 'username',
            align: 'center',
            tooltip: true,
            minWidth: 120,
          },
          {
            title: '操作时间',
            key: 'createTime',
            align: 'center',
            tooltip: true,
            minWidth: 150,
          },
          {
            title: '操作类型',
            key: 'operationType',
            align: 'center',
            tooltip: true,
            minWidth: 150,
            render: (h, params) => {
              const row = params.row
              const text = row.operationType == '1' ? "一键全部审批通过" :
                row.operationType == '2' ? "审批通过" :
                row.operationType == '3' ? "审批不通过" :
                row.operationType == '4' ? "文件审批" : ""
              return h('span', text);
            }
          },
          {
            title: '处理意见',
            key: 'msg',
            align: 'center',
            tooltip: true,
            minWidth: 140,
          },
        ],
        formItemReason: {
          reasonText: '',
        },
        ruleValidate: {
          reasonText: [{
            required: true,
            message: '原因不能为空'
          }],
        },
        ruleobj: {
          file: [{
            required: true,
            validator: validateUpload,
            trigger: 'change',
          }],
        },
        ruleEdit: {
          value: [{
              required: true,
              message: "预计返利不能为空",
            },
            {
              pattern: /^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/,
              message: "最高支持8位整数和2位小数正数或零",
            },
          ],
        },
      }
    },
    computed: {
      mergedData() {
        return (this.rawData || []).map(item => ({
          ...item,
          actualReturn: this.editedRebates[item.id] !== undefined ?
            this.editedRebates[item.id] : item.actualReturn,
          _checked: this.selectedRows.includes(item.id),
        }));
      },
    },
    created() {
      let queryParams = new URLSearchParams(window.location.search);
      if (queryParams.get("webType")) {
        localStorage.setItem("webType", queryParams.get("webType"));
      }

      // 根据 webType 分别处理存储逻辑
      if (['5'].includes(queryParams.get("webType"))) {
        const pageId = Math.random().toString(36).substr(2, 9);
        sessionStorage.setItem('currentPageId', pageId);
        console.log(111)
        // 存储参数时加上前缀
        if (queryParams.get("procUniqueId")) {
          localStorage.setItem(`page_${pageId}_procUniqueId`, queryParams.get("procUniqueId"));
        }
        if (queryParams.get("campaignId")) {
          localStorage.setItem(`page_${pageId}_campaignId`, queryParams.get("campaignId"));
        }
        if (queryParams.get("batchId")) {
          localStorage.setItem(`page_${pageId}_batchId`, queryParams.get("batchId"));
        }
        if (queryParams.get("todoNodeId")) {
          localStorage.setItem(`page_${pageId}_todoNodeId`, queryParams.get("todoNodeId"));
        }
        if (queryParams.get("todoUniqueId")) {
          localStorage.setItem(`page_${pageId}_todoUniqueId`, queryParams.get("todoUniqueId"));
        }
        if (queryParams.get("secondEdit")) {
          localStorage.setItem(`page_${pageId}_secondEdit`, queryParams.get("secondEdit"));
        }
      } else {
        console.log(222)
        // 直接存储参数
        if (queryParams.get("id")) {
          localStorage.setItem("id", queryParams.get("id"));
        }
        if (queryParams.get("procUniqueId")) {
          localStorage.setItem("procUniqueId", queryParams.get("procUniqueId"));
        }
        if (queryParams.get("todoNodeId")) {
          localStorage.setItem("todoNodeId", queryParams.get("todoNodeId"));
        }
        if (queryParams.get("todoUniqueId")) {
          localStorage.setItem("todoUniqueId", queryParams.get("todoUniqueId"));
        }
        if (queryParams.get("campaignId")) {
          localStorage.setItem("campaignId", queryParams.get("campaignId"));
        }
        if (queryParams.get("corpId")) {
          localStorage.setItem("corpId", queryParams.get("corpId"));
        }
        if (queryParams.get("secondEdit")) {
          localStorage.setItem("secondEdit", queryParams.get("secondEdit"));
        }
        if (queryParams.get("batchId")) {
          localStorage.setItem("batchId", queryParams.get("batchId"));
        }
      }

      this.parseUrlParams();

      // 结算审批 从 localStorage 恢复状态
      const savedSelectedRows = localStorage.getItem("selectedRows");
      const savedEditedRebates = localStorage.getItem("editedRebates");

      if (savedSelectedRows) {
        this.selectedRows = JSON.parse(savedSelectedRows);
      }
      if (savedEditedRebates) {
        this.editedRebates = JSON.parse(savedEditedRebates);
      }
    },
    mounted() {
      this.pageType = localStorage.getItem("webType");
      if (this.pageType == '5') {
        const pageId = sessionStorage.getItem('currentPageId');
        this.isEditable = localStorage.getItem(`page_${pageId}_secondEdit`) === 'true'
      }
    },
    methods: {
      /*----------------- 公共方法 -----------------*/
      // 按钮是否可用
      getShowButton: function(type) {
        const pageId = sessionStorage.getItem('currentPageId');
        console.log(pageId, "pageId")
        let todoUniqueId;

        if (localStorage.getItem("webType") === '5') {
          // 对于pageType=5的情况，尝试带前缀的key
          todoUniqueId = pageId ?
            localStorage.getItem(`page_${pageId}_todoUniqueId`) :
            localStorage.getItem('todoUniqueId'); // 回退方案
        } else {
          // 其他pageType直接获取
          todoUniqueId = localStorage.getItem('todoUniqueId');
        }
        getShowButton({
          "ssoAccessToken": this.token,
          "todoUniqueId": todoUniqueId,
        }).then((res) => {
          if (res.code === "0000") {
            if (res.data == '1') {
              this.showButton = false
            } else {
              this.showButton = true
            }
          }
        }).catch((err) => {
          console.error(err, "err1")
        }).finally(() => {})
      },
      // 获取页面url携带的参数
      parseUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        let params = {};
        for (let [key, value] of urlParams.entries()) {
          params[key] = value.trim();
        }
        this.queryParams = params;

        // 优先处理ticket
        if (this.queryParams.ticket) {
          this.getToken(this.queryParams.ticket);
          sessionStorage.setItem("ticket", this.queryParams.ticket);
        }
        // 如果没有ticket再检查是否需要重定向
        else {
          this.checkRedirect();
        }
      },
      // 检查是否需要重定向
      async checkRedirect() {
        if (!window.location.hash.includes("#redirected")) {
          try {
            const key = 'todo-url';  // 动态传入不同的 key
            const redirectUrl = await this.$getRedirectUrl(key);
            if (redirectUrl) {
              const paramsUrl = this.getUrlWithoutParams();
              let url = redirectUrl + '?service=' + paramsUrl;
              window.location.replace(`${url}#redirected`);
            }
          } catch (error) {
            console.error("重定向失败:", error);
          }
        }
      },
      getToken: function(ticket) {
        getToken({
            ticket: ticket,
            service: this.getUrlWithoutParams(),
          })
          .then((res) => {
            if (res.code === "0000") {
              this.showInfo = true;
              this.token = res.accessToken;
              store.state.user.userName = res.tryUser
              this.userName = res.tryUser;
              this.getShowButton(); // 按钮是否可用
              if (['1', '2', '3'].includes(this.pageType)) {
                //调账
                this.getAdjustInfo();
              } else if (['4'].includes(this.pageType)) {
                //营销活动
                this.getRechargeRecord();
              } else if (['5'].includes(this.pageType)) {
                this.getCampaign()
              }
            }
          })
          .catch((err) => {
            console.error(err, 'err2');
            this.$Notice.error({
              title: "操作提示",
              desc: err.msg || err.description || "接口失败",
            });
          })
          .finally(() => {
            sessionStorage.removeItem("ticket")
          });
      },
      // 获取url不携带参数
      getUrlWithoutParams() {
        var url = window.location.href;
        var index = url.indexOf("?");
        if (index !== -1) {
          return url.substring(0, index);
        } else {
          return url;
        }
      },
      // 处理时间格式
      formatDate(dateStr) {
        // 将日期字符串转换为 Date 对象
        const date = new Date(dateStr.replace(/T/, ' ').replace(/\+.+/, ''));

        // 获取年、月、日
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
        const day = String(date.getDate()).padStart(2, '0');

        // 返回格式化后的日期字符串
        return `${year}年${parseInt(month)}月${parseInt(day)}日`;
      },
      /*----------------- 调账 -----------------*/
      getAdjustInfo() {
        searchAdjustInfo({
            id: localStorage.getItem("id"),
            ssoAccessToken: this.token,
            num: "1",
            size: "10"
          })
          .then((res) => {
            if (res.code == "0000") {
              // 获取数据成功
              this.data = res.data
              this.propinfo = JSON.parse(JSON.stringify(res.data[0]));
              this.propinfo.noPassReason = res.data[0].authStatus == '审核拒绝' ? '（' + res.data[0].noPassReason + '）' :
                ""
            }
          })
          .catch((err) => {
            console.error(err, "err3")
            this.$Notice.error({
              title: "操作提示",
              desc: err.msg || err.description || "接口失败",
            });
          })
          .finally(() => {
            this.spinShow = false;
          });
      },
      showModal: function(row) {
        this.modal = true;
      },
      rebuild(type) {
        if (type == '1') {
          this.downInfoLoading = true
        } else {
          this.downLoading = true
        }
        exportFile({
          "id": localStorage.getItem("id"),
          "type": type,
          "ssoAccessToken": this.token,
        }).then(res => {
          const content = res.data
          let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[
            1])) //获取到Content-Disposition;filename  并解码
          if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
            const link = this.$refs.downloadLink // 创建a标签
            let url = URL.createObjectURL(content)
            link.download = fileName
            link.href = url
            link.click() // 执行下载
            URL.revokeObjectURL(url) // 释放url
          } else { // 其他浏览器
            navigator.msSaveBlob(content, fileName)
          }
        }).catch((err) => {
          console.error(err, 'err4')
        }).finally(() => {
          this.downInfoLoading = false
          this.downLoading = false
        })
      },
      // 审核操作
      approval: function(type, pageType) {
        // type 2 通过
        if (type == '1') {
          // pageType  1 业务经理审核  ;   2 财务审核
          let func = pageType == '1' ? businessManagerAudits : financialAudits
          this.$Modal.confirm({
            title: "确定执行审核通过？",
            onOk: () => {
              this.passLoading = true
              func({
                "ssoAccessToken": this.token,
                "id": localStorage.getItem('id'),
                "outcome": type,
                "procUniqueId": localStorage.getItem('procUniqueId'),
                "noPassReason": "",
                "userName": this.userName
              }).then((res) => {
                if (res.code === "0000") {
                  this.$Notice.success({
                    title: "操作提示",
                    desc: "操作成功！",
                  });
                  this.getShowButton()
                }
              }).catch((err) => {
                console.error(err, "err5")
                this.$Notice.error({
                  title: "操作提示",
                  desc: err.msg || err.description || "接口失败",
                })
              }).finally(() => {
                this.passLoading = false
              })
            },
          });
        } else {
          this.modal = true
        }
      },
      cancelModal() {
        this.$refs['formItemReason'].resetFields()
        this.modal = false
      },
      cancelFileModal() {
        this.file = ''
        this.$refs["fileObj"].resetFields();
        this.fileModal = false
      },
      // 审核不通过
      confirm() {
        // pageType 1 业务经理 ； 2  财务专员
        let func = this.pageType == '1' ? businessManagerAudits : financialAudits
        this.$refs.formItemReason.validate((valid) => {
          if (valid) {
            this.noPassLoading = true
            func({
              "ssoAccessToken": this.token,
              "id": localStorage.getItem('id'),
              "outcome": "2",
              "procUniqueId": localStorage.getItem('procUniqueId'),
              "noPassReason": this.formItemReason.reasonText,
              "userName": this.userName
            }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.$refs['formItemReason'].resetFields()
                this.modal = false
                this.getShowButton()
              }
            }).catch((err) => {
              console.error(err, "err6")
              this.$Notice.error({
                title: "操作提示",
                desc: err.msg || err.description || "接口失败",
              })
            }).finally(() => {
              this.noPassLoading = false
            })
          }
        })
      },
      // 已阅操作
      received: function() {
        this.$Modal.confirm({
          title: "确定执行已阅？",
          onOk: () => {
            this.receivedLoading = true
            applicantConfirmation({
              "id": localStorage.getItem('id'),
              "ssoAccessToken": this.token,
              "procUniqueId": localStorage.getItem('procUniqueId'),
              "userName": this.userName
            }).then((res) => {
              if (res.code === "0000") {
                this.$Notice.success({
                  title: "操作提示",
                  desc: "操作成功！",
                });
                this.getShowButton()
              }
            }).catch((err) => {
              console.error(err, "err7")
              this.$Notice.error({
                title: "操作提示",
                desc: err.msg || err.description || "接口失败",
              })
            }).finally(() => {
              this.receivedLoading = false
            })
          },
        });
      },
      /*----------------- 营销活动-添加充值记录 -----------------*/
      // 营销活动-获取添加充值记录信息
      getRechargeRecord() {
        getRechargeRecord({
            campaignId: localStorage.getItem("campaignId"),
            corpId: localStorage.getItem("corpId"),
            ssoAccessToken: this.token,
            batchId: localStorage.getItem("batchId"),
          })
          .then((res) => {
            if (res.code == "0000") {
              // 获取数据成功
              this.formItem.companyName = res.data.companyName
              this.formItem.currencyCode = res.data.currencyCode == '156' ? "人民币" : res.data.currencyCode == '840' ?
                "美元" : res.data.currencyCode == '344' ? "港币" : '';
              this.talbedata = res.data.paymentHistorys
              this.formItemReason.reasonText = res.data.msg
            }
          })
          .catch((err) => {
            console.error(err, "err8")
            this.$Notice.error({
              title: "操作提示",
              desc: err.msg || err.description || "接口失败",
            });
          })
          .finally(() => {
            this.spinShow = false;
          });
      },
      // 营销活动-审批添加充值记录
      examine(type) {
        let title = type == '0' ? "确定执行审批通过？" : "确定执行审批不通过？"
        let noPassReason = type == '0' ? "" : this.formItemReason.reasonText
        // 验证逻辑
        let isValid = true; // 假设验证通过
        let errorMessage = ""; // 存储错误信息

        if (type === '3') {
          if (!this.formItemReason.reasonText || this.formItemReason.reasonText.trim() === "") {
            isValid = false;
            errorMessage = "请输入不通过原因！";
          }
        }

        if (!isValid) {
          // 如果验证不通过，显示错误信息
          this.$Notice.warning({
            title: '提示',
            desc: errorMessage,
          });
          return;
        }

        this.$Modal.confirm({
          title: title,
          onOk: () => {
            if (type == '0') {
              this.passLoading = true
            } else {
              this.noPassLoading = true
            }
            examineRechargeRecord({
              "ssoAccessToken": this.token,
              "procUniqueId": localStorage.getItem('procUniqueId'),
              "ids": this.talbedata.map(i => i.id),
              "remark": noPassReason,
              "status": type,
              "userName": this.userName,
            }).then((res) => {
              if (res.code === "0000") {
                this.$Notice.success({
                  title: "操作提示",
                  desc: "操作成功！",
                });
                this.$refs['formItemReason'].resetFields()
                this.getShowButton()
              }
            }).catch((err) => {
              console.error(err, "err9")
              this.$Notice.error({
                title: "操作提示",
                desc: err.msg || err.description || "接口失败",
              })
            }).finally(() => {
              this.passLoading = false
              this.noPassLoading = false
            })
          },
        });
      },

      /*----------------- 营销活动-结算审批 -----------------*/
      // 单独勾选
      handleSingleSelect(selection, row) {
        const index = this.selectedRows.indexOf(row.id);
        if (index === -1) {
          this.selectedRows.push(row.id);
        } else {
          this.selectedRows.splice(index, 1);
        }
      },
      // 单独取消
      handleSelectCancel(selection, row) {
        this.$store.commit('toggleSelection', row.id);
        const index = this.selectedRows.indexOf(row.id);
        if (index !== -1) {
          this.selectedRows.splice(index, 1);
        }
      },
      // 当页勾选
      handleSelectAll(selection) {
        // 只选择当前页未禁用的行ID
        const enabledIds = this.rawData
          .filter(item => !item._disabled) // 只选择未禁用的行
          .map(item => item.id);

        // 合并已选中的ID（保留之前已选的禁用项）
        this.selectedRows = [...new Set([...this.selectedRows, ...enabledIds])];
      },
      // 当页取消
      handleSelectAllCancel(selection) {
        const currentPageIds = this.rawData.map((item) => item.id);
        this.selectedRows = this.selectedRows.filter(
          (id) => !currentPageIds.includes(id)
        );
      },
      // 显示编辑弹窗
      showRebateEditModal(row) {
        this.currentRebateEdit = {
          rowId: row.id,
          value: row.actualReturn
        };
        this.rebateModal = true;
      },
      // 保存编辑
      saveRebateEdit() {
        this.$refs.rebateEditForm.validate((valid) => {
          if (valid) {
            // 保存修改
            this.$set(this.editedRebates, this.currentRebateEdit.rowId, this.currentRebateEdit.value);
            this.rebateModal = false;
            this.$Notice.success({
              title: '操作提示',
              desc: '修改成功！'
            });
          }
        })
      },
      // 翻页跳转
      handlePageChange(page) {
        this.currentPage = page
        this.fetchData(page)
      },
      fileApprove() {
        this.fileModal = true
      },
      handleBeforeUpload(file) {
        if (!/^.+(\.xlsx)$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式不正确',
            desc: '文件 ' + file.name + ' 格式不正确，请上传.xlsx。'
          })
        } else {
          if (file.size > 10 * 1024 * 1024) {
            this.$Notice.warning({
              title: '文件大小超过限制',
              desc: '文件 ' + file.name + '超过了最大限制范围10MB'
            })
          } else {
            this.file = file
          }
        }
        return false
      },
      fileUploading(event, file, fileList) {
        this.message = '文件上传中、待进度条消失后再操作'
      },
      fileSuccess(response, file, fileList) {
        this.message = '请先下载模板文件，并按格式填写后上传'
      },
      handleError(res, file) {
        var v = this
        setTimeout(function() {
          v.uploading = false;
          v.$Notice.warning({
            title: '错误提示',
            desc: "上传失败！"
          });
        }, 3000);
      },
      removeFile() {
        this.file = ''
      },
      // csv表头
      getCSVHeaders() {
        if (this.cooperationMode == '1') {
          return [{
              title: 'Id',
              key: 'id'
            },
            {
              title: '客户名称',
              key: 'corpName'
            },
            {
              title: '已充值金额',
              key: 'rechargeAmount'
            },
            {
              title: '预计返利',
              key: 'actualReturn'
            },
          ];
        } else if (this.cooperationMode == '2') {
          return [{
              title: 'Id',
              key: 'id'
            },
            {
              title: '客户名称',
              key: 'corpName'
            },
            {
              title: '上期金额',
              key: 'currAmount'
            },
            {
              title: '本期金额',
              key: 'preAmount'
            },
            {
              title: '预计返利',
              key: 'actualReturn'
            },
          ];
        }
        return [];
      },
      // 数据转为csv
      ConvertToCSV(data) {
        // 获取表头
        const headers = this.getCSVHeaders();

        // 生成 CSV 列头
        const headerRow = headers.map((header) => header.title).join(",");

        // 将每行数据转换为 CSV 格式
        const rows = data.map((item) => {
          return headers
            .map((header) => {
              const value = item[header.key];
              // 处理特殊字符（如逗号、换行符）
              if (typeof value === "string" && (value.includes(",") || value.includes("\n"))) {
                return `"${value.replace(/"/g, '""')}"`; // 转义双引号
              }
              // 如果值为空，返回空字符串
              if (value === "" || value === undefined || value === null) {
                return "";
              }
              return value;
            })
            .join(",");
        });

        // 合并列头和行数据
        const csvString = [headerRow, ...rows].join("\n");
        return csvString;
      },
      // 文件下载列表信息 / 流转意见列表信息
      getCommonData(operationType = null) {
        const pageId = sessionStorage.getItem('currentPageId');
        const campaignId = localStorage.getItem(`page_${pageId}_campaignId`);
        const batchId = localStorage.getItem(`page_${pageId}_batchId`);

        const params = {
          campaignId: campaignId,
          batchId: batchId,
          current: -1,
          size: -1,
          ssoAccessToken: this.token
        };

        if (operationType !== null) {
          params.operationType = operationType;
        }

        return getOpinions(params)
          .then((res) => {
            if (res.code === "0000") {
              return res.data;
            }
            throw new Error(res.description || '接口报错');
          })
          .catch((err) => {
            console.error(err, "err10");
            this.$Notice.error({
              title: "操作提示",
              desc: err.msg || err.description || "接口失败",
            });
            throw err;
          });
      },
      getFileList() {
        this.getCommonData("4")
          .then(data => {
            this.fileData = data;
          })
          .catch(() => {
            console.error(err, "err18");
          });
      },
      getOpinions() {
        this.getCommonData()
          .then(data => {
            this.opinionData = data;
          })
          .catch(() => {
            console.error(err, "err19");
          });
      },
      // 文件下载导出/结算导出
      exportMarketingFile(row, type) {
        let func = type == '1' ? exportMarketingFile : download
        let resultData = {}
        if (type == '1') {
          const pageId = sessionStorage.getItem('currentPageId');
          const campaignId = localStorage.getItem(`page_${pageId}_campaignId`);
          const batchId = localStorage.getItem(`page_${pageId}_batchId`);
          this.downLoading = true
          resultData = {
            "campaignId": campaignId,
            "cooperationMode": this.cooperationMode,
            "ssoAccessToken": this.token,
            "batchId": batchId,
          }
        } else {
          resultData = {
            name: row.paramFile,
            "ssoAccessToken": this.token,
          }
        }
        console.log(resultData, "resultData")

        func(resultData, this.token).then(res => {
          const content = res.data
          let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[
            1])) //获取到Content-Disposition;filename  并解码
          if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
            const link = this.$refs.downloadLink // 创建a标签
            let url = URL.createObjectURL(content)
            link.download = fileName
            link.href = url
            link.click() // 执行下载
            URL.revokeObjectURL(url) // 释放url
          } else { // 其他浏览器
            navigator.msSaveBlob(content, fileName)
          }
        }).catch((err) => {
          console.error(err, 'err11')
        }).finally(() => {
          this.downLoading = false
        })
      },
      // 结算审批信息
      getCampaign() {
        const pageId = sessionStorage.getItem('currentPageId');
        getCampaign({
            type: "1",
            campaignId: localStorage.getItem(`page_${pageId}_campaignId`),
            ssoAccessToken: this.token,
          })
          .then((res) => {
            if (res.code == "0000") {
              // 获取数据成功
              this.cooperationMode = res.data.cooperationMode
              this.marketingActivities = res.data
              this.marketingActivities.startTime = this.formatDate(res.data.startTime)
              this.marketingActivities.endTime = this.formatDate(res.data.endTime)
              this.marketingActivities.totalRebate = this.cooperationMode == "1" ? res.data.expectAmountRefund + "/" +
                res.data.totalAmountRefund : this.cooperationMode == "2" ? res.data.expectAmountRefund : ""
              this.fetchData(1)
              this.getOpinions()
              this.getFileList()
            }
          })
          .catch((err) => {
            console.error(err, "err12")
            this.$Notice.error({
              title: "操作提示",
              desc: err.msg || err.description || "接口失败",
            });
          })
          .finally(() => {
            this.spinShow = false;
          });
      },
      // 审批分页列表
      async fetchData(page) {
        const pageId = sessionStorage.getItem('currentPageId');
        const campaignId = localStorage.getItem(`page_${pageId}_campaignId`);
        const batchId = localStorage.getItem(`page_${pageId}_batchId`);
        this.loading = true
        try {
          // 调用后端接口获取数据
          const res = await getMktSettlementPage({
            campaignId: campaignId,
            ssoAccessToken: this.token,
            batchId: batchId,
            cooperationMode: this.cooperationMode,
            current: page,
            size: this.pageSize,
          });

          if (res.code === "0000") {
            this.rawData = res.data.records;
            this.total = res.data.total;
            this.currentPage = page;

            // 设置禁用状态和回显数据
            this.rawData.forEach((row) => {
              this.$set(row, '_disabled', row.status != '1'); // 禁用非状态1的行
              row._checked = this.selectedRows.includes(row.id);
              row.actualReturn = this.editedRebates[row.id] || row.actualReturn;
            });

          } else {
            console.log(err,"err21")
            this.$Notice.error({
              title: "操作提示",
              desc: `获取数据失败：${res.description || '未知错误'}`,
            });
          }
        } catch (err) {
          console.log(err,"err22")
          this.$Notice.error({
            title: "操作提示",
            desc: `接口调用失败：${err.description || '网络错误'}`,
          });
        } finally {
          this.loading = false;
          this.spinShow = false;
        }
      },
      // 勾选审批
      async toggleApprove() {
        if (this.toggleApproveLoading) return;
        this.toggleApproveLoading = true;
        const pageId = sessionStorage.getItem('currentPageId');
        const campaignId = localStorage.getItem(`page_${pageId}_campaignId`);
        const batchId = localStorage.getItem(`page_${pageId}_batchId`);
        try {
          const res = await getMktSettlementPage({
            campaignId,
            batchId,
            ssoAccessToken: this.token,
            cooperationMode: this.cooperationMode,
            current: -1,
            size: -1,
            status: 1,
          });
          if (res.code === "0000") {
            const selectedIds = this.selectedRows
            if (selectedIds.length === 0) {
              this.$Notice.warning({
                title: '提示',
                desc: '至少勾选一条数据！',
              });
              return;
            }

            const data = res.data.records
              .filter(item => selectedIds.includes(item.id))
              .map(item => ({
                ...item,
                actualReturn: this.editedRebates[item.id] !== undefined ?
                  this.editedRebates[item.id] // 如果编辑过，使用编辑后的值（包括空字符串）
                  :
                  item.actualReturn, // 如果未编辑过，使用原始值
              }));
            console.log(data, "data")
            const csvString = this.ConvertToCSV(data);
            // 将 csvString 转换为 Blob 对象
            await this.submitApprove(csvString, '1', '2', '2');
          }
        } catch (err) {
          console.log(err,"err23")
          this.$Notice.error({
            title: "操作提示",
            desc: err.description,
          });
        } finally {
          this.toggleApproveLoading = false;
        }
      },
      // 勾选审核不通过
      async toggleNoApprove() {
        if (this.toggleNoApproveLoading) return;
        this.toggleNoApproveLoading = true;
        const pageId = sessionStorage.getItem('currentPageId');
        const campaignId = localStorage.getItem(`page_${pageId}_campaignId`);
        const batchId = localStorage.getItem(`page_${pageId}_batchId`);
        try {
          const res = await getMktSettlementPage({
            campaignId,
            batchId,
            ssoAccessToken: this.token,
            cooperationMode: this.cooperationMode,
            current: -1,
            size: -1,
            status: 1,
          });
          if (res.code === "0000") {
            const selectedIds = this.selectedRows
            if (selectedIds.length === 0) {
              this.$Notice.warning({
                title: '提示',
                desc: '至少勾选一条数据！',
              });
              return;
            }

            const data = res.data.records
              .filter(item => selectedIds.includes(item.id))
              .map(item => ({
                ...item,
                actualReturn: this.editedRebates[item.id] !== undefined ?
                  this.editedRebates[item.id] // 如果编辑过，使用编辑后的值（包括空字符串）
                  :
                  item.actualReturn, // 如果未编辑过，使用原始值
              }));
            console.log(data, "data")
            const csvString = this.ConvertToCSV(data);
            // 将 csvString 转换为 Blob 对象
            await this.submitApprove(csvString, '1', '3', '3');
          }
        } catch (err) {
          console.log(err,"err24")
          this.$Notice.error({
            title: "操作提示",
            desc: err.description,
          });
        } finally {
          this.toggleNoApproveLoading = false;
        }
      },
      // 一键审批
      async approveAll() {
        this.$Modal.confirm({
          title: "确认执行全部审批通过？",
          onOk: async () => {
            try {
              const pageId = sessionStorage.getItem('currentPageId');
              const campaignId = localStorage.getItem(`page_${pageId}_campaignId`);
              const batchId = localStorage.getItem(`page_${pageId}_batchId`);
              if (this.approveAllLoading) return;
              this.approveAllLoading = true;
              // 获取所有数据
              const res = await getMktSettlementPage({
                campaignId: campaignId,
                ssoAccessToken: this.token,
                batchId: batchId,
                cooperationMode: this.cooperationMode,
                current: -1,
                size: -1,
                status: 1,
              });
              if (res.code === "0000") {
                // 处理数据 - 只包含未禁用的行
                const finalData = res.data.records
                  .filter(item => !item._disabled) // 只选择未禁用的行
                  .map(item => ({
                    ...item,
                    actualReturn: this.editedRebates[item.id] !== undefined ?
                      this.editedRebates[item.id] : // 如果编辑过，使用编辑后的值
                      item.actualReturn, // 如果未编辑过，使用原始值
                  }));

                if (finalData.length === 0) {
                  this.$Notice.warning({
                    title: "操作提示",
                    desc: "没有可审批的数据（所有行均被禁用）",
                  });
                  return;
                }

                // 转换为 CSV
                const csvString = this.ConvertToCSV(finalData);
                // 提交 CSV 数据
                await this.submitApprove(csvString, '1', '2', '1');
              }
            } catch (err) {
              console.log(err,"err25")
              this.$Notice.error({
                title: "操作提示",
                desc: err.description,
              });
            } finally {
              this.approveAllLoading = false;
            }
          },
          onCancel: () => {
            this.$Message.info("已取消审批");
          },
        });
      },
      // 文件审批
      handleUpload() {
        this.$refs["fileObj"].validate((valid) => {
          if (valid) {
            this.uploadLoading = true
            this.submitApprove(null, '2', '2', '4');
          }
        });
      },
      resetState() {
        this.selectedRows = [];
        this.editedRebates = {};
      },
      // 审批方法
      async submitApprove(csvString, type, status, operationType) {
        console.log(csvString, "csvString")
        // 公共参数

        const pageId = sessionStorage.getItem('currentPageId');
        const campaignId = localStorage.getItem(`page_${pageId}_campaignId`);
        const batchId = localStorage.getItem(`page_${pageId}_batchId`);
        const secondEdit = localStorage.getItem(`page_${pageId}_secondEdit`);
        const procUniqueId = localStorage.getItem(`page_${pageId}_procUniqueId`);
        const formData = new FormData();

        // 添加文件到 FormData
        if (type === '1') {
          const blob = new Blob([csvString], {
            type: 'text/csv'
          });
          formData.append('file', blob, 'data.csv');
        } else {
          formData.append('file', this.file);
        }

        // 添加其他参数到 FormData
        formData.append('type', this.cooperationMode);
        formData.append('campaignId', campaignId);
        formData.append('secondEdit', secondEdit);
        formData.append('procUniqueId', procUniqueId);
        formData.append('batchId', batchId);
        formData.append('status', status);
        formData.append('msg', this.infoValue);
        formData.append('userName', this.userName);
        formData.append('operationType', operationType);

        try {
          // 调用审批接口
          const res = await marketingFileApproval(formData, this.token);
          console.log("接口返回的完整响应:", res); // 调试用
          if (res.code === "0000") {
            this.$Notice.success({
              title: "操作提示",
              desc: "操作成功",
            });
            this.getShowButton();
          } else {
            throw new Error(res.description || "审批失败，未知原因"); // 明确抛出错误信息
          }
        } catch (err) {
          console.error(err,"err26");
          this.$Notice.error({
            title: "操作提示",
            desc: err.description || err.msg || "审批失败，请检查网络或数据", // 兜底提示
          });
        } finally {
          // 重新加载数据-不管审批成不成功，抛不抛异常、都刷新列表
          this.getCampaign();
          this.resetState();
          this.infoValue = ""
          this.file = ''
          this.$refs["fileObj"].resetFields();
          this.fileModal = false
          // 重置加载状态
          this.uploadLoading = false;
          this.approveAllLoading = false;
          this.toggleApproveLoading = false;
        }
      },
    },
    beforeDestroy() {
      const pageId = sessionStorage.getItem('currentPageId');
      const webType = localStorage.getItem("webType");

      // 根据 webType 分别处理清理逻辑
      if (webType === '5' && pageId) {
        // 只清理 pageType 为 5 的带前缀的存储
        const prefix = `page_${pageId}_`;
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith(prefix)) {
            localStorage.removeItem(key);
          }
        });
        sessionStorage.removeItem('currentPageId');
      } else {
        // 清理普通存储
        localStorage.removeItem("id");
        localStorage.removeItem("procUniqueId");
        localStorage.removeItem("todoNodeId");
        localStorage.removeItem("todoUniqueId");
        localStorage.removeItem("campaignId");
        localStorage.removeItem("corpId");
        localStorage.removeItem("secondEdit");
        localStorage.removeItem("batchId");
      }

      // 清理 webType
      localStorage.removeItem("webType");

      // 在组件销毁前保存状态到 localStorage
      localStorage.setItem("selectedRows", JSON.stringify(this.selectedRows));
      localStorage.setItem("editedRebates", JSON.stringify(this.editedRebates));
    },
  }
</script>
<style scoped>
  .content-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    background-color: rgb(245, 247, 249);
  }

  .row-box {
    margin: 40px 0 0 0;
    font-weight: bold;
    padding: 0 0 0 100px;
  }

  .row-box2 {
    font-weight: bold;
    padding: 0 0 0 100px;
  }

  .col-box {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-right: 20px;
    text-align: left;
  }

  .flex-center {
    display: flex;
    align-items: center;
    /* 垂直居中 */
    justify-content: center;
    /* 水平居中 */
  }

  .col-box2 {
    display: flex;
    justify-content: center;
    /* 让每个 Col 的内容居中 */
    margin-bottom: 30px;
  }

  .nowarp {
    white-space: nowrap;
  }

  .table_box {
    margin-top: 50px;
  }

  .op_btn_box {
    margin-top: 30px;
  }

  .op_btn_box div {
    width: 50%;
    margin-left: 25%;
    display: flex;
    justify-content: space-around;
  }

  .content-box1 {
    width: 100%;
    height: 100%;
    display: flex;
    background-color: rgb(245, 247, 249);
  }

  .card-box2 {
    width: 100%;
    padding: 12px;
    overflow: auto;
  }

  .row-box3 {
    margin: 60px 0 0 0;
  }


  .row-box4 {
    font-weight: bold;
    margin: 60px 0 0 0;
  }

  .button-box {
    margin: 60px 0 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* 修改为更具体的样式 */
  .ivu-table .no-spin-input::-webkit-outer-spin-button,
  .ivu-table .no-spin-input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0;
  }

  .ivu-table .no-spin-input {
    -moz-appearance: textfield !important;
    width: 120px;
    padding: 5px 10px;
    /* 添加内边距优化显示 */
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .card-box2>div>div {
      flex-direction: column;
    }

    .card-box2>div>div>div {
      flex: 1 1 100% !important;
    }
  }

  .h1-box {
    font-size: 24px;
    color: #17233d;
    margin-bottom: 24px;
  }

  .info-header {
    font-size: 16px;
    color: #2d8cf0;
    font-weight: bold;
    display: flex;
    align-items: center;
  }

  .info-header i {
    margin-right: 8px;
  }

  .error-message {
    text-align: center;
    padding: 40px;
    color: #ed4014;
  }
</style>
