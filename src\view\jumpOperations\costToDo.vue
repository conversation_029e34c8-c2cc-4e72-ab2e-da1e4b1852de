<template>
  <div class="content-box">
    <card style="width: 1000px; margin-top: 20px" v-if="pageType == '1'">
      <div v-if="showInfo">
        <Spin size="large" fix v-if="spinShow">
          <Icon type="ios-loading" size=24 class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>
        <h1 style="align-items: center">成本报表</h1>
        <!-- 表格 -->
        <Table
          :columns="costColumns"
          :data="costData"
          style="width: 100%; margin-top: 50px"
          :loading="loading"
        >
        </Table>
        <Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator @on-change="loadByPage"></Page>
        <div class="button-box">
          <Button type="success" icon="md-checkmark" :disabled="showButton" @click="received" :loading="receivedLoading">已阅</Button>
        </div>
      </div>
      <div v-else>
        <h4 class="h4-box">请求数据失败，请重新进入！</h4>
      </div>
    </card>
  </div>
</template>

<script>
import {
  getToken,
  getShowButton,
} from "@/api/jumpOperations/operationAgencyApproval";
import {
  getCostReportList,
  received
} from "@/api/jumpOperations/costToDo";
import store from '@/store'
const math = require('mathjs')
export default {
  data() {
    return {
      pageType: "",
      userName: "",
      queryParams: "",//url
      ticket: "",
      total: 0,
      pageSize: 10,
      page: 1,
      selectionList: [],
      costData: [],
      showInfo: true,
      spinShow: true,
      showButton: true,
      costColumns: [{
				title: "结算周期",
				key: "statDate",
				align: "center",
				minWidth: 130,
					tooltip: true,
				tooltipMaxWidth: 2000,
			},
			{
				title: "资源供应商",
				key: "supplierShortenName",
				align: "center",
				minWidth: 150,
				tooltip: true,
				tooltipMaxWidth: 2000,
			},
			{
				title: "供应商编码",
				key: "supplierShortenCode",
				align: "center",
				minWidth: 150,
				tooltip: true,
				tooltipMaxWidth: 2000,
			},
			{
				title: "币种",
				key: "currency",
				align: "center",
				minWidth: 100,
			},
			{
				title: "成本预估",
				key: "totalAmount",
				align: "center",
				minWidth: 150,
				tooltip: true,
			},
			{
				title: "成本类型",
				key: "type",
				align: "center",
				minWidth: 150,
				tooltip: true,
			},
			{
				title: "审批状态",
				key: "approvalStatus",
				align: "center",
				minWidth: 150,
				tooltip: true,
        render: (h, params) => {
        	const row = params.row;
        	var text = "";
        	switch (row.approvalStatus) {
        		case "1":
        			text = "未审批";
        			break;
        		case "2":
        			text = "审批完成";
        			break;
        		default:
        			text = "";
        	}
        	return h("label", text);
        },
			},
			{
				title: "同步Rap状态",
				key: "syncRap",
				align: "center",
				minWidth: 150,
				tooltip: true,
        render: (h, params) => {
        	const row = params.row;
        	var text = "";
        	switch (row.syncRap) {
        		case "1":
        			text = "未上传";
        			break;
        		case "2":
        			text = "待上传";
        			break;
            case "3":
            	text = "已上传";
            	break;
            case "4":
            	text = "上传失败";
            	break;
        		default:
        			text = "";
        	}
        	return h("label", text);
        },
      }],
    };
  },
  watch: {},
  created() {
    let queryParams = new URLSearchParams(window.location.search);

    if (queryParams.get("page")) {
      localStorage.setItem("page", queryParams.get("page"));
    }
    if (queryParams.get("procId")) {
      localStorage.setItem("procId", queryParams.get("procId"));
    }
    if (queryParams.get("batchId")) {
      localStorage.setItem("batchId", queryParams.get("batchId"));
    }
    if (queryParams.get("procUniqueId")) {
      localStorage.setItem("procUniqueId", queryParams.get("procUniqueId"));
    }
    if (queryParams.get("todoNodeId")) {
      localStorage.setItem("todoNodeId", queryParams.get("todoNodeId"));
    }
    if (queryParams.get("todoUniqueId")) {
      localStorage.setItem("todoUniqueId", queryParams.get("todoUniqueId"));
    }

    this.parseUrlParams();
  },
  mounted() {
    this.pageType = localStorage.getItem("page");
  },
  methods: {
    // 获取成本列表
    loadByPage(page) {
      console.log("调用列表接口")
      this.loading = true
      this.page = page
      getCostReportList({
        ssoAccessToken: this.token,
        batchId: localStorage.getItem("batchId"),
        size: this.pageSize,
        current: page,
      }).then(res => {
      	if (res.code === "0000") {
          console.log(res.data,"res.data")
          this.costData = res.data;
          this.total = Number(res.count);
      	}
      }).catch((err) => {
        console.error(err);
        this.$Notice.error({
          title: "操作提示",
          desc: err.description
        })
      })
      .finally(() => {
        this.loading = false;
        this.spinShow = false
      });
    },
    // 已阅操作
    received: function() {
    	this.$Modal.confirm({
    		title: "确定执行已阅？",
    		onOk: () => {
          this.receivedLoading = true
    			received({
            "ssoAccessToken": this.token,
            "procId": localStorage.getItem('procId'),
            "procUniqueId": localStorage.getItem('procUniqueId'),
            "todoUniqueId": localStorage.getItem('todoUniqueId')
          }).then((res) => {
    				if (res.code === "0000") {
    					this.$Notice.success({
    						title: "操作提示",
    						desc: "操作成功！",
    					});
              this.getShowButton()
    				}
    			}).catch((err) => {
            this.$Notice.error({
              title: "操作提示",
              desc: err.description
            })
          }).finally(() => {
            this.receivedLoading = false
          })
    		},
    	});
    },
    // 按钮是否可用
    getShowButton: function (type) {
      getShowButton({
        ssoAccessToken: this.token,
        todoUniqueId: localStorage.getItem("todoUniqueId"),
      })
        .then((res) => {
          if (res.code === "0000") {
            if (res.data == "1") {
              // 未办结
              this.showButton = false;
            } else {
              // 办结
              this.showButton = true;
            }
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {});
    },
    getToken: function (ticket) {
      getToken({
        ticket: ticket,
        service: this.getUrlWithoutParams(),
      })
        .then((res) => {
          if (res.code === "0000") {
            this.showInfo = true
            this.token = res.accessToken;
            this.userName = res.tryUser;
            this.getShowButton(); // 按钮是否可用
            if (this.pageType == "1") {
              // 获取成本报表收入表格数据
              console.log("进入页面")
              this.loadByPage(this.page);
            }
            console.log("获取token成功")
          }
        })
        .catch((err) => {
          console.log("获取token报错")
          console.error(err);
          this.$Notice.error({
            title: "操作提示",
            desc: err.description,
          });
          this.showInfo = false
        })
        .finally(() => {
          sessionStorage.removeItem("ticket")
        });
    },
    // 获取url不携带参数
    getUrlWithoutParams() {
      var url = window.location.href;
      var index = url.indexOf("?");
      if (index !== -1) {
        return url.substring(0, index);
      } else {
        return url;
      }
    },
    // 获取页面url携带的参数
    parseUrlParams() {
      const urlParams = new URLSearchParams(window.location.search);
      let params = {};
      for (let [key, value] of urlParams.entries()) {
        params[key] = value.trim();
      }
      this.queryParams = params;

      // 优先处理ticket
      if (this.queryParams.ticket) {
        this.getToken(this.queryParams.ticket);
        sessionStorage.setItem("ticket", this.queryParams.ticket);
      }
      // 如果没有ticket再检查是否需要重定向
      else {
        this.checkRedirect();
      }
    },
    async checkRedirect() {
      if (!window.location.hash.includes("#redirected")) {
        try {
          const key = 'todo-url';  // 动态传入不同的 key
          const redirectUrl = await this.$getRedirectUrl(key);
          if (redirectUrl) {
            const paramsUrl = this.getUrlWithoutParams();
            let url = redirectUrl + '?service=' + paramsUrl;
            window.location.replace(`${url}#redirected`);
          }
        } catch (error) {
          console.error("重定向失败:", error);
        }
      }
    },
    beforeDestroy() {
      // 组件销毁前执行的代码
      localStorage.removeItem("page");
      localStorage.removeItem("procId");
      localStorage.removeItem("batchId");
      localStorage.removeItem("procUniqueId");
      localStorage.removeItem("todoNodeId");
      localStorage.removeItem("todoUniqueId");
    },
  },
};
</script>

<style scoped>
.content-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: rgb(245, 247, 249);
}

.h4-box{
  width: 100%;
  margin: 0 0 30px 0;
  align-items: center;
}

.button-box{
    margin: 60px 0 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
