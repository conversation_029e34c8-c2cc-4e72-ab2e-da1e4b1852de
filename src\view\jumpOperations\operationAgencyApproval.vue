<template>
  <div :class="['4', '5'].includes(pageType) ? 'content-box1' : 'content-box'">
    <!-- （渠道商账单查询、渠道商销售数据）线下支付/账单缴付 - 财务-4 /运营-5审批 -->
    <div class="card-box2" v-if="['4', '5'].includes(pageType)">
      <div v-if="showInfo">
        <Spin size="large" fix v-if="spinShow">
          <Icon type="ios-loading" size=24 class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>
        <Row>
          <Col span="24" style="text-align: center; margin-top: 20px;">
             <h1 class="h1-box">待办审批详情页</h1>
          </Col>
        </Row>
        <div style="display: flex;">
          <Row style="margin-right: 16px;">
            <!-- 基本信息 -->
            <Col span="24" style="margin-bottom: 20px;">
              <card style="width: 100%;">
                <h3 class="info-header">基本信息</h3>
                <Form res="formObj" :model="formItem" :label-width="120">
                  <Row class="row-box">
                    <Col :xs="24" :md='24' :lg="12" span="24">
                      <FormItem label="渠道商名称" style="min-width: 280px;">
                        <Input v-model="corpName" readonly></Input>
                      </FormItem>
                    </Col>
                    <Col :xs="24" :md='24' :lg="12" span="24">
                      <FormItem label="充值/缴付时间" style="min-width: 280px;">
                        <Input v-model="time" readonly></Input>
                      </FormItem>
                    </Col>
                    <Col :xs="24" :md='24' :lg="12" span="24">
                      <FormItem label="充值/账单金额" style="min-width: 280px;">
                        <Input v-model="amount" readonly></Input>
                      </FormItem>
                    </Col>
                    <Col :xs="24" :md='24' :lg="12" span="24">
                      <FormItem label="EBSCode" style="min-width: 280px;">
                        <Input v-model="ebsCode" readonly></Input>
                      </FormItem>
                    </Col>
                    <Col :xs="24" :md='24' :lg="12" span="24">
                      <FormItem label="币种" style="min-width: 280px;">
                        <Input v-model="currency" readonly></Input>
                      </FormItem>
                    </Col>
                    <Col :xs="24" :md='24' :lg="12" span="24" v-if="pageType == '5'">
                      <FormItem label="审批结果" style="min-width: 280px;">
                        <Input v-model="approvalResult" readonly></Input>
                      </FormItem>
                    </Col>
                  </Row>
                </Form>
              </card>
            </Col>
            <!-- 文件信息 -->
            <Col span="24">
             <card style="width: 100%;">
               <h3 class="info-header">文件下载</h3>
               <Form res="formObj" :model="formItem" :label-width="120">
                <Row class="row-box">
                  <Col :xs="24" :md='24' :lg="12" span="24" class="col-box">
                    <FormItem label="付款证明">
                       <Button type="info" ghost size="small" @click="downloadFile" :loading="downLoading">下载付款证明</Button>
                    </FormItem>
                  </Col>
                  <Col :xs="24" :md='24' :lg="12" span="24" class="col-box" v-if="pageType == '4'">
                    <FormItem label="Invoice">
                       <Button type="warning" ghost size="small" @click="downloadInvoice('1', null)" :loading="invoiceLoading">点击下载</Button>
                    </FormItem>
                  </Col>
                  <Col :xs="24" :md='24' :lg="12" span="24" class="col-box" v-if="['4', '5'].includes(pageType)">
                    <FormItem label="CN Invoice">
                       <Button type="primary" ghost size="small" @click="downloadCnInvoice" :loading="cnInvoiceLoading" :disabled="!cnInvoiceAddress">CN Invoice下载</Button>
                    </FormItem>
                  </Col>
                </Row>
              </Form>
             </card>
            </Col>
          </Row>
          <Row>
            <!-- 操作 -->
            <Col span="24" style="margin-bottom: 20px;">
              <card style="width: 100%;">
                <h3 class="info-header">操作</h3>
                <Form ref="formItemReason" :model="formItemReason" :rules="ruleValidate" @submit.native.prevent>
                	<FormItem style="margin-top: 30px; min-width: 500px;">
                		<Input v-model="formItemReason.reasonText" :disabled="showButton" clearable type="textarea" :autosize="{minRows: 3,maxRows: 10}" :placeholder="pageType == '4' ? '请输入未到账原因...' : ''"></Input>
                	</FormItem>
                  <FormItem label="收款编码" style="margin-top: 30px; width: 500px;" v-if="pageType == '4'">
                  	<Input v-model="formItemReason.receiptNum" :disabled="showButton" clearable maxlength='80' placeholder="请输入收款编码..."></Input>
                  </FormItem>
                </Form>
                <div class="button-box" v-if="pageType == '4'">
                  <Button type="error" icon="md-close" :disabled="showButton" :loading="noPassLoading" @click="arrived('1')">未到账</Button>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <Button type="success" icon="md-checkmark" :disabled="showButton" :loading="passLoading" @click="arrived('2')">确认到账</Button>
                </div>
                <div class="button-box" v-if="pageType == '5'">
                  <Button type="success" icon="md-checkmark" :disabled="showButton" @click="received('5')" :loading="receivedLoading">已阅</Button>
                </div>
              </card>
            </Col>
            <Col span="24">
              <card style="width: 100%; min-height: 100px;"></card>
            </Col>
          </Row>
        </div>
      </div>
      <div v-else>
        <h4 class="h4-box">请求数据失败，请重新进入！</h4>
      </div>
    </div>
    <!-- （渠道商账单查询、渠道商销售数据）线下支付/充值待办 -发票申请详情页-->
    <card v-if="pageType == '1' || pageType == '2'" style="width: 1500px; margin-top: 20px; padding: 0; overflow: hidden;">
      <div v-if="showInfo">
      <Spin size="large" fix v-if="spinShow">
        <Icon type="ios-loading" size="24" class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
      <h1 class="billing-title">发票申请详情页</h1>
      <div class="bill-container">
        <div class="bill-main-content">
          <!-- 基本信息区 -->
          <div class="bill-left-content">
            <div class="bill-section">
              <div class="section-title blue-title">基本信息</div>
              <div class="red-divider"></div>
              <div class="info-grid">
                <div class="info-label">渠道商名称：</div>
                <div class="info-value">{{ corpName }}</div>
                <div class="info-label">EBSCode：</div>
                <div class="info-value">{{ ebsCode }}</div>
                <div class="info-label">发票申请时间：</div>
                <div class="info-value">{{ time }}</div>
                <div class="info-label">发票类型：</div>
                <div class="info-value">{{ channelType }}</div>
                <div class="info-label">币种：</div>
                <div class="info-value">{{ currency }}</div>
                <div class="info-label">金额：</div>
                <div class="info-value">{{ amount }}</div>
              </div>
            </div>
          </div>
          <div class="bill-vertical-divider"></div>
          <!-- 操作信息区 -->
          <div class="bill-right-content">
            <div class="bill-section">
              <div class="section-title blue-title">操作信息</div>
              <div class="red-divider"></div>
              <div class="remark-textarea">
                <Form ref="formItemReason" :model="formItemReason">
                  <FormItem prop="reasonText">
                    <Input v-model="formItemReason.reasonText" :disabled="showButton" type="textarea" :rows="4" placeholder="请输入审批意见" />
                  </FormItem>
                </Form>
              </div>
              <div class="confirm-button-container">
                 <template  v-if="pageType == '1'">
                  <Button type="error" icon="md-close" style="margin-right: 20px;" :disabled="showButton" @click="invoiceApproval('1', '1')">审批不通过</Button>
                  <Button type="success" icon="md-checkmark" :disabled="showButton" :loading="passLoading" @click="invoiceApproval('1', '2')">审批通过</Button>
                </template>
                <template  v-if="pageType == '2'">
                  <Button type="error" icon="md-close" style="margin-right: 20px;" :disabled="showButton" @click="invoiceApproval('2', '1')">审批不通过</Button>
                  <Button type="success" icon="md-checkmark" :disabled="showButton" :loading="passLoading" @click="invoiceApproval('2', '2')">审批通过</Button>
                </template>
              </div>
            </div>
          </div>
        </div>
        <!-- 底部区域 -->
        <div class="bill-bottom-content">
          <!-- 文件下载区 -->
          <div class="bill-bottom-left">
            <div class="section-title blue-title">文件下载</div>
            <div class="red-divider"></div>
            <div class="table-container">
              <Table :columns="fileColumns" :data="fileData" height="300" size="small" :border="false" ellipsis="true"></Table>
            </div>
          </div>
          <div class="bill-bottom-vertical-divider"></div>
          <!-- 流转意见区 -->
          <div class="bill-bottom-right">
            <div class="section-title blue-title">流转意见</div>
            <div class="red-divider"></div>
            <Table :columns="flowColumns" :data="flowData" height="300" size="small" :border="false" :ellipsis="true">
              <template slot="index" slot-scope="{ index }">
                {{ index + 1 }}
              </template>
            </Table>
          </div>
        </div>
      </div>
      <!-- 不通过弹窗 -->
      <!-- <Modal v-model="modal2" title="确认执行审核不通过？" @on-cancel="cancelModal" :mask-closable="false" width="60%">
        	<Form ref="formItemReason" :model="formItemReason" :rules="ruleValidate" @submit.native.prevent>
          <FormItem prop="reasonText">
            <Input v-model="formItemReason.reasonText" maxlength="200" placeholder="请输入不通过原因……"></Input>
          </FormItem>
        </Form>
        <div slot="footer" style="text-align: right;">
          <Button style="margin-left: 8px" @click="cancelModal">取消</Button>
          <Button type="primary" :loading="noPassLoading" @click="invoiceConfirm">确定</Button>
        </div>
      </Modal> -->
      </div>
      <div v-else>
        <h4 class="h4-box">请求数据失败，请重新进入！</h4>
      </div>
    </card>
    <!-- 充值待办 -缴费详情页-->
    <card class="card-box" v-if="pageType == '6' || pageType == '7'">
      <!-- 财务6、运营7 -->
      <div v-if="showInfo">
        <Spin size="large" fix v-if="spinShow">
          <Icon type="ios-loading" size=24 class="demo-spin-icon-load"></Icon>
          <div>Loading</div>
        </Spin>
        <h1 class="h1-box">缴费详情页</h1>
        <Row class="row-box">
          <Col :xs="24" :md='12' :lg="8" span="8" class="col-box"> <span>渠道商名称： </span>&nbsp;&nbsp;{{corpName}} </Col>
          <Col :xs="24" :md='12' :lg="8" span="8" class="col-box"> <span>EBSCode： </span>&nbsp;&nbsp;{{ebsCode}} </Col>
          <Col :xs="24" :md='12' :lg="8" span="8" class="col-box"> <span>缴费时间： </span>&nbsp;&nbsp;{{time}} </Col>
          <Col :xs="24" :md='12' :lg="8" span="8" class="col-box"> <span>发票类型： </span>&nbsp;&nbsp;{{channelType}} </Col>
          <Col :xs="24" :md='12' :lg="8" span="8" class="col-box"> <span>币种： </span>&nbsp;&nbsp;{{currency}} </Col>
          <Col :xs="24" :md='12' :lg="8" span="8" class="col-box"> <span>金额： </span>&nbsp;&nbsp;{{amount}} </Col>
        </Row>
        <Row>
          <Col :xs="24" :md='12' :lg="8" span="8" class="col-box">
            <span>付款证明： </span>&nbsp;&nbsp;
            <Button type="info" ghost size="small" style="margin: 5px" @click="downloadInvoice('2', '1')"
              :loading="downLoading">点击下载</Button>
          </Col>
          <Col :xs="24" :md='12' :lg="8" span="8" offset="8">
            <span>Invoice： </span>&nbsp;&nbsp;
            <Button type="warning" ghost size="small" style="margin: 5px" @click="downloadInvoice('2', '2')"
              :loading="invoiceLoading">点击下载</Button>
          </Col>
        </Row>
        <!-- 增加一个收款编码 -->
        <Row>
          <Col  v-if="pageType == '6'">
            <span>收款编码： </span>&nbsp;&nbsp;
            <Input style="width: 400px;" v-model="approvalReceiptNum" :disabled="showButton" clearable maxlength='80' placeholder="请输入收款编码..."></Input>
          </Col>
        </Row>

        <Row class="row-box" v-if="pageType == '7'">
          <Col span="24">
            <span>审批结果： </span>&nbsp;&nbsp;{{authStatus}}{{resulet}}
          </Col>
        </Row>
        <div v-if="pageType == '6'" class="button-box">
          <Button type="error" icon="md-close" :disabled="showButton" @click="approval('1')">审批不通过</Button>
          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <Button type="success" icon="md-checkmark" :disabled="showButton" :loading="passLoading" @click="approval('2')">审批通过</Button>
        </div>
        <div v-if="pageType == '7'" class="button-box">
          <Button type="success" icon="md-checkmark" :disabled="showButton" @click="received('17')" :loading="receivedLoading">已阅</Button>
        </div>
        <!-- 不通过弹窗 -->
        <Modal v-model="modal2" title="确认执行审核不通过？" @on-cancel="cancelModal" :mask-closable="false" width="60%">
        	<Form ref="formItemReason" :model="formItemReason" :rules="ruleValidate" @submit.native.prevent>
        		<FormItem prop="reasonText">
        			<Input v-model="formItemReason.reasonText" maxlength="200" placeholder="请输入不通过原因……"></Input>
        		</FormItem>
        	</Form>
        	<div slot="footer" style="text-align: right;">
        		<Button style="margin-left: 8px" @click="cancelModal">取消</Button>
        		<Button type="primary" :loading="noPassLoading" @click="confirm">确定</Button>
          </div>
        </Modal>
      </div>
      <div v-else>
        <h4 class="h4-box">请求数据失败，请重新进入！</h4>
      </div>
    </card>
    <a ref="downloadLink" style="display: none"></a>
  </div>
</template>

<script>
  import store from '@/store'
  import {
  	operationAgencyApproval,
  	getPicture,
    approval,
    getShowButton,
    getToken,
    received,
    downloadPaymentProofs,
    downloadInvoice,
    getRechargeRecord,
    salesApproval,
    financialApproval,
    getInvoiceAuditRecord,
    downloadCnInvoiceFile
  } from '@/api/jumpOperations/operationAgencyApproval'
  export default {
  	data() {
      return {
        corpName: "",
        time: "",
        amount: "",
        ebsCode: "",
        time: "",
        channelType: "",
        currency: "",
        approvalResult: "",
        pictureUrl: "",
        queryParams: "",//url
        ticket: "",
        pageType: "",
        resulet: "",
        userName: "",
        token: "",
        authStatus: "",
        fileAddress: "",
        paymentProofAddress: "",
        pageApproval: '',
        approvalPage: '',
        formItemReason: {
          reasonText: "",
          receiptNum: "",
        },
        ruleValidate: {
          reasonText: [{ required: true, message: '未到账原因不能为空' }],
          receiptNum: [{ required: true, message: '收款编码不能为空' }],
        },
        spinShow: true,
        noPassLoading: false,
        passLoading: false,
        receivedLoading: false,
        downLoading: false,
        invoiceLoading: false,
        showButton: true,
        modal2: false,
        showInfo: true,
        formItem: {},
        approvalReceiptNum: "",
        fileColumns: [
          { title: '文件名', key: 'fileName', align: 'center', minWidth: 200 },
          { title: '生成时间', key: 'createTime', align: 'center', minWidth: 160 },
          { title: '文件大小', key: 'fileSize', align: 'center', minWidth: 80 },
          { title: '点击下载', key: 'action', align: 'center', minWidth: 100,
            render: (h, params) => {
              return h('Button', {
                props: { type: 'default', size: 'small' },
                style: { marginRight: '5px' },
                on: { click: () => { /* 可扩展下载逻辑 */ } }
              }, '下载');
            }
          }
        ],
        fileData: [],
        flowColumns: [
          { title: '序号', slot: 'index', align: 'center', width: 65 },
          { title: '操作人', key: 'operateUser', align: 'center' },
          { title: '操作时间', key: 'operateTime', align: 'center', width: 150 },
          { title: '操作类型', key: 'operationType', align: 'center', width: 130 },
          { title: '处理意见', key: 'dispositionComments', align: 'center', ellipsis: true, tooltip: true, width: 200 }
        ],
        flowData: [],
        formRules: {
          reasonText: [
            { required: false, message: '请输入审批意见', trigger: 'blur' }
          ]
        },
        cnInvoiceLoading: false,
        cnInvoiceAddress:""
      }
    },
    created() {
      let queryParams = new URLSearchParams(window.location.search)
      if (queryParams.get('id')){
        localStorage.setItem('id', queryParams.get('id'))
      }
      if (queryParams.get('corpId')){
        localStorage.setItem('corpId', queryParams.get('corpId'))
      }
      if (queryParams.get('page')){
        localStorage.setItem('page', queryParams.get('page'))
      }
      if (queryParams.get('chargeId')){
        localStorage.setItem('chargeId', queryParams.get('chargeId'))
      }
      if (queryParams.get('procUniqueId')){
        localStorage.setItem('procUniqueId', queryParams.get('procUniqueId'))
      }
      if (queryParams.get('todoNodeId')){
        localStorage.setItem('todoNodeId', queryParams.get('todoNodeId'))
      }
      if (queryParams.get('todoUniqueId')){
        localStorage.setItem('todoUniqueId', queryParams.get('todoUniqueId'))
      }
      if (queryParams.get('cooperationMode')){
        localStorage.setItem('cooperationMode', queryParams.get('cooperationMode'))
      }
      this.parseUrlParams()
    },
    mounted() {
      this.pageType = localStorage.getItem('page')
    },
    methods: {
      // 获取页面ticket
      parseUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        let params = {};
        for (let [key, value] of urlParams.entries()) {
          params[key] = value.trim();
        }
        this.queryParams = params;

        // 优先处理ticket
        if (this.queryParams.ticket) {
          this.getToken(this.queryParams.ticket);
          sessionStorage.setItem("ticket", this.queryParams.ticket);
        }
        // 如果没有ticket再检查是否需要重定向
        else {
          this.checkRedirect();
        }
      },
      async checkRedirect() {
        if (!window.location.hash.includes("#redirected")) {
          try {
            const key = 'todo-url';  // 动态传入不同的 key
            const redirectUrl = await this.$getRedirectUrl(key);
            if (redirectUrl) {
              const paramsUrl = this.getUrlWithoutParams();
              let url = redirectUrl + '?service=' + paramsUrl;
              window.location.replace(`${url}#redirected`);
            }
          } catch (error) {
            console.error("重定向失败:", error);
          }
        }
      },
      // 获取token
      getToken: function(ticket) {
      	getToken({
          "ticket": ticket,
          "service": this.getUrlWithoutParams(),
      	}).then(res => {
      		if (res.code === '0000') {
            this.showInfo = true
            this.token = res.accessToken
            store.state.user.userName = res.tryUser
            this.userName = res.tryUser
            this.pageType = localStorage.getItem('page')
            if (this.pageType == '4' || this.pageType == '5' || this.pageType == '6' || this.pageType == '7') {
              this.goPageFirst(res.accessToken)
            } else if (this.pageType == '1' || this.pageType == '2') {
              this.getInvoicePage(res.accessToken)
              this.getInvoiceAuditRecord();
            }
            this.getShowButton(res.accessToken)
      		}
      	}).catch((err) => {
          this.$Notice.error({
          	title: "操作提示",
          	desc: err.description
          })
          this.showInfo = false
      	}).finally(() => {
          sessionStorage.removeItem("ticket")
      	})
      },
      // 获取url不携带参数
      getUrlWithoutParams() {
        var url = window.location.href;
        var index = url.indexOf("?");
        if (index !== -1) {
          return url.substring(0, index);
        } else {
          return url;
        }
      },
      // 缴付账单——获取审批信息
    	goPageFirst: function(token) {
    		operationAgencyApproval({
          "ssoAccessToken": this.token,
          id: localStorage.getItem('id'),
          pageNum: -1,
          pageSize: -1,
    		}).then(res => {
    			if (res.code === '0000') {
            this.corpName = res.data[0].cropName
            this.time = this.addHoursToUTCDate(res.data[0].chargeTime, 8)
            this.amount = res.data[0].amount
            this.ebsCode = res.data[0].ebsCode
            this.fileAddress = res.data[0].invoiceAddress
            this.paymentProofAddress = res.data[0].paymentProofAddress
            this.channelType = res.data[0].channelType == '1' ? '押金' : '预存'
            this.currency =
              res.data[0].currency == "156"
                ? "人民币"
                : res.data[0].currency == "344"
                ? "港币"
                : res.data[0].currency == "840"
                ? "美元"
                : "";
            this.resulet = res.data[0].authStatus == '2' ? '' : '（' + res.data[0].noPassReason + "）"
            this.authStatus = res.data[0].authStatus == '2' ? "审批通过" : res.data[0].authStatus == '3' ? "审批不通过" : ''
            this.approvalResult = this.authStatus + this.resulet
            this.invoiceNo = res.data[0].invoiceNo
            this.cnInvoiceAddress = res.data[0].cnInvoiceAddress
            if(this.pageType == '6') {
              this.approvalReceiptNum = res.data[0].receiptNum
            }
            if(this.pageType == '4' || this.pageType == '5') {
              this.formItemReason.receiptNum = res.data[0].receiptNum
              this.formItemReason.reasonText = res.data[0].noPassReason
            }
          }
    		}).catch((err) => {
          this.$Notice.error({
          	title: "操作提示",
          	desc: err.description
          })
    		}).finally(() => {
    			this.spinShow = false
    		})
    	},
      // 缴付账单 付款证明
      downloadFile: function() {
        this.downLoading = true
        downloadPaymentProofs({
        	id: localStorage.getItem('id'),
          ssoAccessToken: this.token,
        }).then(res => {
        	const content = res.data
        	let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
        	if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
        		const link = this.$refs.downloadLink // 创建a标签
        		let url = URL.createObjectURL(content)
        		link.download = fileName
        		link.href = url
        		link.click() // 执行下载
        		URL.revokeObjectURL(url) // 释放url
        	} else { // 其他浏览器
        		navigator.msSaveBlob(content,fileName)
        	}
        }).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.downLoading = false
        })
      },
      // 缴付账单1 invoice下载、押金充值2 （invoice下载、付款证明）
      downloadInvoice: function(page, type) {
        let resultData;
        if (page == '1') {
          //缴付账单
          resultData = {
            fileAddress: this.fileAddress,
            ssoAccessToken: this.token,
            corpName: this.corpName,
            invoiceNo: this.invoiceNo,
          }
          this.invoiceLoading = true
        } else {
          // 押金充值
          // type 1 付款证明  ； type 2 invoice
          resultData = {
            fileAddress: type == '1' ? this.paymentProofAddress : this.fileAddress,
            ssoAccessToken: this.token,
          }
          if (type == '1') {
            this.downLoading = true
          } else {
            this.invoiceLoading = true
          }
        }
        downloadInvoice(resultData).then(res => {
        	const content = res.data
        	let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
        	if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
        		const link = this.$refs.downloadLink // 创建a标签
        		let url = URL.createObjectURL(content)
        		link.download = fileName
        		link.href = url
        		link.click() // 执行下载
        		URL.revokeObjectURL(url) // 释放url
        	} else { // 其他浏览器
        		navigator.msSaveBlob(content,fileName)
        	}
        }).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.downLoading = false
          this.invoiceLoading = false
        })
      },
      // 账单缴付-财务审批
      arrived: function(type) {
        let title = type == '2' ? "确定执行确认到账？" : "确定执行未到账？"
        let noPassReason = type == '2' ? "" : this.formItemReason.reasonText
        let status = type == '2' ? true : false
        // 验证逻辑
        let isValid = true; // 假设验证通过
        let errorMessage = ""; // 存储错误信息

        if (type === '2') {
          if (!this.formItemReason.receiptNum || this.formItemReason.receiptNum.trim() === "") {
            isValid = false;
            errorMessage = "请输入收款编码！";
          }
        } else {
          if (!this.formItemReason.reasonText || this.formItemReason.reasonText.trim() === "") {
            isValid = false;
            errorMessage = "请输入未到账原因！";
          }
        }

        if (!isValid) {
          // 如果验证不通过，显示错误信息
          this.$Notice.warning({
            title: '提示',
            desc: errorMessage,
          });
          return;
        }

        this.$Modal.confirm({
        	title: title,
        	onOk: () => {
            if (type == '2') {
              this.passLoading = true
            } else {
              this.noPassLoading = true
            }
        		approval({
              "ssoAccessToken": this.token,
              "id": localStorage.getItem('id'),
              "noPassReason": noPassReason,
              "receiptNum": this.formItemReason.receiptNum,
              "status": status,
              "todoUniqueId": localStorage.getItem('todoUniqueId'),
              "userName": this.userName,
            }).then((res) => {
        			if (res.code === "0000") {
        				this.$Notice.success({
        					title: "操作提示",
        					desc: "操作成功！",
        				});
                this.$refs['formItemReason'].resetFields()
                this.getShowButton()
        			}
        		}).catch((err) => {
              this.$Notice.error({
              	title: "操作提示",
              	desc: err.description
              })
            }).finally(() => {
              this.passLoading = false
              this.noPassLoading = false
            })
        	},
        });
      },
      // 押金充值-审核操作
      approval: function(type) {
        if(type == '2') {
          if(this.pageType == '6') {
            if(this.approvalReceiptNum == '') {
              this.$Notice.warning({
                title: '提示',
                desc: "请输入收款编码！",
              });
              return;
            }
          }
          this.$Modal.confirm({
          	title: "确定执行审核通过？",
          	onOk: () => {
              this.passLoading = true
          		approval({
                "ssoAccessToken": this.token,
                "id": localStorage.getItem('id'),
                "noPassReason": "",
                "status": true,
                "todoUniqueId": localStorage.getItem('todoUniqueId'),
                "userName": this.userName,
                "receiptNum": this.approvalReceiptNum
              }).then((res) => {
          			if (res.code === "0000") {
          				this.$Notice.success({
          					title: "操作提示",
          					desc: "操作成功！",
          				});
                  // 清空收款编码
                  //this.approvalReceiptNum = ''
                  this.getShowButton()
          			}
          		}).catch((err) => {
                this.$Notice.error({
                	title: "操作提示",
                	desc: err.description
                })
              }).finally(() => {
                this.passLoading = false
              })
          	},
          });
        } else {
          this.modal2 = true
        }
      },
      cancelModal() {
        this.$refs['formItemReason'].resetFields()
        this.modal2 = false
      },
      // 缴付账单\押金充值-审核不通过
      confirm() {
      	this.$refs.formItemReason.validate((valid) => {
      		if (valid) {
            this.noPassLoading = true
      			approval({
              "ssoAccessToken": this.token,
              "id": localStorage.getItem('id'),
              "noPassReason": this.formItemReason.reasonText,
              "status": false,
              "todoUniqueId": localStorage.getItem('todoUniqueId'),
              "userName": this.userName,
            }).then(res => {
      				if (res && res.code == '0000') {
      					this.$Notice.success({
      						title: '操作提示',
      						desc: '操作成功'
      					})
      					this.$refs['formItemReason'].resetFields()
      					this.modal2 = false
                this.getShowButton()
      				}
      			}).catch((err) => {
              this.$Notice.error({
                title: "操作提示",
                desc: err.description
              })
            }).finally(() => {
              this.noPassLoading = false
            })
      		}
      	})
      },
      // 缴付账单、押金充值-已阅操作
      received: function(procId) {
      	this.$Modal.confirm({
      		title: "确定执行已阅？",
      		onOk: () => {
            this.receivedLoading = true
      			received({
              "ssoAccessToken": this.token,
              "procUniqueId": localStorage.getItem('procUniqueId'),
              "todoUniqueId": localStorage.getItem('todoUniqueId'),
              "procId": procId,
            }).then((res) => {
      				if (res.code === "0000") {
      					this.$Notice.success({
      						title: "操作提示",
      						desc: "操作成功！",
      					});
                this.getShowButton()
      				}
      			}).catch((err) => {
              this.$Notice.error({
                title: "操作提示",
                desc: err.description
              })
            }).finally(() => {
              this.receivedLoading = false
            })
      		},
      	});
      },
      // 按钮是否可用
      getShowButton: function(type) {
      	getShowButton({
          "ssoAccessToken": this.token,
          "todoUniqueId": localStorage.getItem('todoUniqueId')
      	}).then((res) => {
      		if (res.code === "0000") {
            if (res.data == '1') {
              this.showButton = false
            } else {
              this.showButton = true
            }
      		}
      	}).catch((err) => {
      	  console.error(err)
      	}).finally(() => {
      	})
      },
      // 发票申请 ——页面数据
      getInvoicePage: function(token) {
      	getRechargeRecord({
          "ssoAccessToken": this.token,
          corpId: localStorage.getItem('corpId'),
          chargeId: localStorage.getItem('chargeId'),
          cooperationMode: localStorage.getItem('cooperationMode'),
          pageNum: -1,
          pageSize: -1,
      	}).then(res => {
      		if (res.code === '0000') {
            this.corpName = res.data[0].corpName
            this.ebsCode = res.data[0].ebsCode
            this.time = this.addHoursToUTCDate(res.data[0].chargeTime, 8)
            this.channelType = res.data[0].channelType == '1' ? '押金' : '预存'
            this.amount = res.data[0].chargeAmount
            this.currency =
              res.data[0].currencyCode == "156"
                ? "人民币"
                : res.data[0].currencyCode == "344"
                ? "港币"
                : res.data[0].currencyCode == "840"
                ? "美元"
                : "";
            this.formItemReason.reasonText = res.data[0].noPassReason
          }
      	}).catch((err) => {
          this.$Notice.error({
          	title: "操作提示",
          	desc: err.description
          })
      	}).finally(() => {
      		this.spinShow = false
      	})
      },
      // 流转意见接口
      getInvoiceAuditRecord() {
        getInvoiceAuditRecord({
          "procUniqueId": localStorage.getItem('procUniqueId'),
          "ssoAccessToken": this.token,
        }).then(res => {
          if (res.code === '0000') {
            let tempData = res.data;
            tempData.forEach(item => {
              item.operateTime = this.addHoursToUTCDate(item.operateTime, 8)
            })
            this.flowData = tempData;
          }
        });
      },
      // 销售/财务——发票申请-审批通过
      invoiceApproval: function(page, type) {
        if (type == '2') {
          // type 1 销售； type 2 财务
          let func = page == '1' ? salesApproval : financialApproval
          this.$Modal.confirm({
          	title: "确定执行审核通过？",
          	onOk: () => {
              this.passLoading = true
          		func({
                "ssoAccessToken": this.token,
                "chargeId": localStorage.getItem('chargeId'),
                "todoUniqueId": localStorage.getItem('todoUniqueId'),
                "procUniqueId": localStorage.getItem('procUniqueId'),
                "status": true,
                "dispositionComments": this.formItemReason.reasonText
              }).then((res) => {
          			if (res.code === "0000") {
          				this.$Notice.success({
          					title: "操作提示",
          					desc: "操作成功！",
          				});
                  this.getShowButton()
                  this.getInvoicePage()
                  this.getInvoiceAuditRecord()
          			}
          		}).catch((err) => {
                this.$Notice.error({
                	title: "操作提示",
                	desc: err.description
                })
              }).finally(() => {
                this.passLoading = false
              })
          	},
          });
        } else {
          this.approvalPage = page
          this.invoiceConfirm()
        }
      },
      // 销售/财务——发票申请-审批不通过
      invoiceConfirm() {
        if (this.formItemReason.reasonText.trim() == '') {
          return this.$Notice.warning({
            title: '提示',
            desc: '请输入审批意见！'
          })
        }
        this.$Modal.confirm({
          title: "确定执行？",
          onOk: () => {
            let func = this.approvalPage == '1' ? salesApproval : financialApproval
            this.noPassLoading = true
      			func({
              "ssoAccessToken": this.token,
              "chargeId": localStorage.getItem('chargeId'),
              "todoUniqueId": localStorage.getItem('todoUniqueId'),
              "procUniqueId": localStorage.getItem('procUniqueId'),
              "dispositionComments": this.formItemReason.reasonText,
              "status": false,
            }).then(res => {
      				if (res && res.code == '0000') {
      					this.$Notice.success({
      						title: '操作提示',
      						desc: '操作成功'
      					})
      					// this.$refs['formItemReason'].resetFields()
      					// this.modal2 = false
                this.getShowButton()
                this.getInvoicePage()
                this.getInvoiceAuditRecord()
      				}
      			}).catch((err) => {
              this.$Notice.error({
                title: "操作提示",
                desc: err.description
              })
            }).finally(() => {
              this.noPassLoading = false
            })
      		}
      	})
      },
      addHoursToUTCDate(utcDateString, hoursToAdd) {
          const date = new Date(utcDateString);

          // 增加小时数
          date.setUTCHours(date.getUTCHours() + hoursToAdd, date.getUTCMinutes(), date.getUTCSeconds(), 0); // 最后一个参数是毫秒，这里设为0

          // 格式化日期时间字符串为YYYY-MM-DD HH:mm:ss
          const year = date.getUTCFullYear();
          const month = String(date.getUTCMonth() + 1).padStart(2, '0');
          const day = String(date.getUTCDate()).padStart(2, '0');
          const hours = String(date.getUTCHours()).padStart(2, '0');
          const minutes = String(date.getUTCMinutes()).padStart(2, '0');
          const seconds = String(date.getUTCSeconds()).padStart(2, '0');
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      beforeDestroy() {
        // 组件销毁前执行的代码
        localStorage.removeItem('id');
        localStorage.removeItem('corpId');
        localStorage.removeItem('page');
        localStorage.removeItem('chargeId');
        localStorage.removeItem('procUniqueId');
        localStorage.removeItem('todoNodeId');
        localStorage.removeItem('todoUniqueId');
        localStorage.removeItem('cooperationMode');
      },
      // CN Invoice下载方法
      downloadCnInvoice() {
        this.cnInvoiceLoading = true;
        // 调用CN Invoice下载API
        downloadCnInvoiceFile({
        	id: localStorage.getItem('id'),
          ssoAccessToken: this.token,
        }).then(res => {
        	const content = res.data
        	let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
        	if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
        		const link = this.$refs.downloadLink // 创建a标签
        		let url = URL.createObjectURL(content)
        		link.download = fileName
        		link.href = url
        		link.click() // 执行下载
        		URL.revokeObjectURL(url) // 释放url
        	} else { // 其他浏览器
        		navigator.msSaveBlob(content,fileName)
        	}
        }).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.cnInvoiceLoading = false
        })
      },
    }
  }
</script>

<style scoped>
  .content-box{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgb(245,247,249);
  }

  .content-box1 {
    width: 100%;
    height: 100%;
    display: flex;
    background-color: rgb(245,247,249);
  }

  .col-box{
    margin-bottom: 60px;
  }

  .card-box{
    width: 60%;
    padding: 20px 10px;
  }

  .h1-box{
    margin: 0 0 30px 0;
    align-items: left;
  }

  .h4-box{
    width: 100%;
    margin: 0 0 30px 0;
    align-items: center;
  }

  .row-box{
    margin: 60px 0 0 0;
  }

  .button-box{
    margin: 60px 0 0 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .demo-spin-col{
    height: 100px;
    position: relative;
    border: 1px solid #eee;
  }

  .demo-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
  }

  @keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
  }

  .card-box2 {
    width: 100%;
    padding: 12px;
  }

  .info-header::before {
    content: "|";
    color: blue;
    display: inline-block;
    width: 0.5em;
    height: 1em;
    margin-right: 0.3em;
  }

  /* 嵌套账单待办通知风格样式，仅用于发票申请详情页 */
  .bill-container {
    padding: 0 20px;
  }
  .bill-main-content {
    display: flex;
  }
  .bill-bottom-content {
    display: flex;
  }
  .bill-left-content {
    flex: 1;
    padding-right: 20px;
    border-right: 1px solid #e8e8e8;
  }
  .bill-right-content {
    flex: 1;
    padding-left: 20px;
  }
  .bill-bottom-left {
    flex: 1;
    padding-right: 20px;
    border-right: 1px solid #e8e8e8;
  }
  .bill-bottom-right {
    flex: 1;
    padding-left: 20px;
  }
  .bill-section {
    margin-bottom: 0;
  }
  .bill-vertical-divider, .bill-bottom-vertical-divider {
    width: 1px;
    background-color: #e8e8e8;
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -1px;
    z-index: 10;
    height: 100%;
  }
  .billing-title {
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    color: #333;
    margin: 20px 0;
  }
  .info-grid {
    display: grid;
    grid-template-columns: 150px 1fr 150px 1fr;
    grid-row-gap: 25px;
    padding: 10px 0;
  }
  .info-label {
    text-align: right;
    padding-right: 10px;
    color: #333;
    font-weight: normal;
  }
  .info-value {
    text-align: left;
  }
  .remark-textarea {
    padding: 10px 0;
    margin-bottom: 20px;
  }
  .remark-textarea textarea {
    width: 100%;
    height: 150px;
    border: 1px solid #dcdee2;
    padding: 8px;
    resize: none;
  }
  .confirm-button-container {
    display: flex;
    justify-content: center;
    margin: 10px 0;
  }
  .blue-title {
    color: #00b7ee;
    font-size: 16px;
    font-weight: bold;
    padding: 0 0 5px 0;
    margin-bottom: 0;
    text-align: left;
  }
  .red-divider {
    height: 1px;
    background-color: #e8e8e8;
    margin-bottom: 15px;
    width: 100%;
  }
  .table-container {
    width: 100%;
    overflow-x: hidden;
  }


</style>
