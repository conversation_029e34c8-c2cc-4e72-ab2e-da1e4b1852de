<template>
  <div class="content-box">
    <card style="width: 1000px; margin-top: 20px" v-if="pageType == '1'">
      <h1 style="align-items: center">渠道商销售数据</h1>
      <!-- 表格 -->
      <Table
        :columns="columns12"
        :data="talbedata"
        height="400"
        style="width: 100%; margin-top: 50px"
        :loading="loading"
      >
      </Table>
    </card>
    <card style="width: 1500px; margin-top: 20px; padding: 0; overflow: hidden;" v-if="pageType == '2'">
      <Spin size="large" fix v-if="spinShow">
        <Icon type="ios-loading" size="24" class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
      <h1 class="billing-title">催缴账单待办通知</h1>

      <div class="bill-container">
        <div class="bill-main-content">
          <!-- 左侧区域 -->
          <div class="bill-left-content">
            <!-- 基本信息区 -->
            <div class="bill-section">
              <div class="section-title blue-title">基本信息</div>
              <div class="red-divider"></div>
              <div class="info-grid">
                <div class="info-label">渠道商公司名称：</div>
                <div class="info-value">{{ reminderObj.corpName }}</div>
                <div class="info-label">渠道商简称：</div>
                <div class="info-value">{{ reminderObj.companyName }}</div>

                <div class="info-label">账单周期：</div>
                <div class="info-value">{{ reminderObj.svcStartTime }}-{{ reminderObj.svcEndTime }}</div>
                <div class="info-label">出账时间：</div>
                <div class="info-value">{{ reminderObj.createTime }}</div>

                <div class="info-label">账单金额：</div>
                <div class="info-value">{{ reminderObj.realIncome }}</div>
                <div class="info-label">币种：</div>
                <div class="info-value">{{ reminderObj.currency == "156" ? "CNY" : reminderObj.currency == "840" ? "USD" : reminderObj.currency == "344" ? "HKD" : "" }}</div>
              </div>
            </div>
          </div>

          <!-- 中间垂直分割线 -->
          <div class="bill-vertical-divider"></div>

          <!-- 右侧区域 -->
          <div class="bill-right-content">
            <!-- 操作信息区 -->
            <div class="bill-section">
              <div class="section-title blue-title">操作信息</div>
              <div class="red-divider"></div>
              <div class="remark-textarea">
                <Form ref="formRef" :model="formModel" :rules="formRules">
                  <FormItem prop="remark">
                    <Input v-model="formModel.remark" type="textarea" :rows="4" placeholder="请输入审阅意见" />
                  </FormItem>
                </Form>
              </div>
              <div class="confirm-button-container">
                <Button type="default" size="small" :disabled="showButton" @click="handleSubmit">已阅</Button>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部区域，文件下载和流转意见放在同一水平线上 -->
        <div class="bill-bottom-content">
          <!-- 文件下载区 -->
          <div class="bill-bottom-left">
            <div class="section-title blue-title">文件下载</div>
            <div class="red-divider"></div>
            <div class="table-container">
              <Table :columns="fileColumns" :data="reminderObj.channelDownload" height="300" size="small" :border="false" ellipsis="true">
              </Table>
            </div>
          </div>

          <!-- 中间垂直分割线 -->
          <div class="bill-bottom-vertical-divider"></div>

          <!-- 流转意见区 -->
          <div class="bill-bottom-right">
            <div class="section-title blue-title">流转意见</div>
            <div class="red-divider"></div>
            <Table :columns="flowColumns" :data="flowData" height="300" size="small" :border="false" :ellipsis="true">
              <!-- 序号用index -->
              <template slot="index" slot-scope="{ index }">
                {{ index + 1 }}
              </template>
            </Table>
          </div>
        </div>
      </div>
    </card>
    <card class="page3-box" v-if="pageType == '3'">
      <h1 style="align-items: center">承诺量完成情况</h1>
      <Row class="row-box">
        <Col span="12" class="col-box">
          <span>渠道商：</span>&nbsp;&nbsp;{{ corpName }}
        </Col>
        <Col span="12" class="col-box">
          <span>合作模式：</span>&nbsp;&nbsp;{{ cooperationMode }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="24" class="col-box">
          <span>合约开始时间：</span>&nbsp;&nbsp;{{ date }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="24" class="col-box">
          <span>承诺金额: </span>&nbsp;&nbsp;{{ promiseAmount }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="24" class="col-box">
          <span>币种: </span>&nbsp;&nbsp;{{ currencyCode }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="24" class="col-box">
          <span>已完成金额: </span>&nbsp;&nbsp;{{ amount }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="24" class="col-box">
          <span>已完成百分比: </span>&nbsp;&nbsp;{{ rate }}
        </Col>
      </Row>
      <Row
        class="row-box"
        style="display: flex; justify-content: center; align-items: center"
      >
        <Button
          type="primary"
          icon="md-checkmark"
          :loading="besureSubmitLoading"
          :disabled="showButton"
          @click="channelPushFinish"
        >
          确认
        </Button>
      </Row>
    </card>
    <!-- 财务人员 -->
    <card class="page3-box" v-if="pageType == '4'">
      <Spin size="large" fix v-if="spinShow">
        <Icon type="ios-loading" size="24" class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
      <h1 style="align-items: center">白卡订单详情页</h1>
      <Row class="row-box">
        <Col span="12" class="col-box">
          <span>渠道商名称：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.corpName }}
        </Col>
        <Col span="12" class="col-box">
          <span>付款时间：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.payTime }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="12" class="col-box">
          <span>EBSCode：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.ebsCode }}
        </Col>
        <Col span="12" class="col-box">
          <span>订单ID：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.orderId }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="12" class="col-box">
          <span>付款金额：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.amount }}
        </Col>
        <Col span="12" class="col-box">
          <span>币种：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.currencyCode == "156" ? "人民币" : whiteCardOperateObj.currencyCode == "344" ? "港币" : whiteCardOperateObj.currencyCode == "840" ? "美元" : '' }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="12" class="col-box">
          <span>卡片数量：</span>&nbsp;&nbsp;{{whiteCardOperateObj.cardNum}}
        </Col>
        <Col span="12" class="col-box">
          <span>卡片类型：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.cardForm == '1' ? '普通卡（实体卡）' : whiteCardOperateObj.cardForm == '2' ? 'Esim卡' : whiteCardOperateObj.cardForm == '3' ? '贴片卡' : whiteCardOperateObj.cardForm == '4' ? 'IMSI号' : '未知' }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="12" class="col-box">
          <span>付款证明: </span>&nbsp;&nbsp;&nbsp;&nbsp; <Button type="info" size="small" ghost :loading="downLoading" @click="exportPaymentProofs">点击下载查看</Button>
        </Col>
        <Col span="12" class="col-box">
          <span>Invoice: </span>&nbsp;&nbsp;&nbsp;&nbsp; <Button type="warning" size="small" ghost :loading="invoiceLoading" @click="downloadInvoice">点击下载查看</Button>
        </Col>
      </Row>
      <Row
        style="display: flex; justify-content: center; align-items: center; margin: 80px 0 0 -100px;"
      >
        <Button
          type="success"
          icon="md-checkmark"
          :loading="besureSubmitLoading"
          :disabled="showButton"
          @click="whiteCardOperate(1)"
        >
          确认到账
        </Button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button
          type="error"
          :loading="besureSubmitLoading"
          :disabled="showButton"
          @click="whiteCardOperate(2)"
        >
          未到账
        </Button>
      </Row>
    </card>
    <card style="width: 1000px; margin-top: 20px;" v-if="pageType == '5'">
      <h1 style="align-items: center">出账待办</h1>
      <!-- 表格 -->
      <Table
        :columns="billColumns"
        :data="billTalbeData"
        style="width: 100%; margin-top: 50px"
        :loading="billLoading"
      >
      </Table>
      <Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator
      	@on-change="getDisbursePage" style="margin: 15px 0;" />
    </card>
    <!-- 运营人员 -->
    <card class="page3-box" v-if="pageType == '6'">
      <Spin size="large" fix v-if="spinShow">
        <Icon type="ios-loading" size="24" class="demo-spin-icon-load"></Icon>
        <div>Loading</div>
      </Spin>
      <h1 style="align-items: center">白卡订单详情页</h1>
      <Row class="row-box">
        <Col span="12" class="col-box">
          <span>渠道商名称：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.corpName }}
        </Col>
        <Col span="12" class="col-box">
          <span>付款时间：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.payTime }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="12" class="col-box">
          <span>EBSCode：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.ebsCode }}
        </Col>
        <Col span="12" class="col-box">
          <span>订单ID：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.orderId }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="12" class="col-box">
          <span>付款金额：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.amount }}
        </Col>
        <Col span="12" class="col-box">
          <span>币种：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.currencyCode == "156" ? "人民币" : whiteCardOperateObj.currencyCode == "344" ? "港币" : whiteCardOperateObj.currencyCode == "840" ? "美元" : '' }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="12" class="col-box">
          <span>卡片数量：</span>&nbsp;&nbsp;{{whiteCardOperateObj.cardNum}}
        </Col>
        <Col span="12" class="col-box">
          <span>卡片类型：</span>&nbsp;&nbsp;{{ whiteCardOperateObj.cardForm == '1' ? '普通卡（实体卡）' : whiteCardOperateObj.cardForm == '2' ? 'Esim卡' : whiteCardOperateObj.cardForm == '3' ? '贴片卡' : whiteCardOperateObj.cardForm == '4' ? 'IMSI号' : '未知' }}
        </Col>
      </Row>
      <Row class="row-box">
        <Col span="12" class="col-box">
          <span>付款证明: </span>&nbsp;&nbsp;&nbsp;&nbsp; <Button type="info" size="small" ghost :loading="downLoading" @click="exportPaymentProofs">点击下载查看</Button>
        </Col>
      </Row>
      <Row
        style="display: flex; justify-content: center; align-items: center; margin: 80px 0 0 -100px;"
      >
        <Button
          type="success"
          icon="md-checkmark"
          :loading="receivedLoading"
          :disabled="showButton"
          @click="received"
        >
          已阅
        </Button>
      </Row>
    </card>
    <!-- 待办提示 -->
    <Modal v-model="tipsModal" :closable="false" :mask-closable="false">
      <div style="align-items: center; justify-content: center; display: flex">
        您的客户有新的账单生成，请及时查看
      </div>
      <div
        slot="footer"
        style="
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <Button type="primary" @click="confirm">确认</Button>
      </div>
    </Modal>
    <a ref="downloadLink" style="display: none"></a>
  </div>
</template>

<script>
import {
  whiteCardOperate,
  getWhiteCardOperateObj,
  exportPaymentProofs,
  getreminders,
  getremindersPushFinish,
  getChannelPromiseDTO,
  channelPushFinish,
  pushFinish,
  getPage,
  getToken,
  getShowButton,
  downloadInvoice,
  getDisbursePage,
  receivedOperation,
  downloadReminderFile,
  getOpinion
} from "@/api/jumpOperations/operationAgencyApproval";
import {
		downloadFile,
	} from "@/api/product/whiteCardOrders";
import store from '@/store'
const math = require('mathjs')
export default {
  data() {
    return {
      tipsModal: false,
      besureSubmitLoading: false,
      pageType: "",
      columns12: [
        {
          title: "渠道商名称",
          key: "corpName",
          align: "center",
        },
        {
          title: "关联销售账号",
          key: "account",
          align: "center",
        },
        {
          title: "合作模式",
          key: "cooperationMode",
          align: "center",
          render: (h, params) => {
            const row = params.row;
            const text =
              row.cooperationMode == "1"
                ? "代销"
                : row.cooperationMode == "2"
                ? "a2z"
                : row.cooperationMode == "3"
                ? "资源合作"
                : "";
            return h("label", text);
          },
        },
        {
          title: "币种",
          key: "currencyCode",
          align: "center",
          render: (h, params) => {
            const row = params.row;
            const text =
              row.currencyCode == "156"
                ? "CNY"
                : row.currencyCode == "840"
                ? "USD"
                : row.currencyCode == "344"
                ? "HKD"
                : "";
            return h("label", text);
          },
        },
        {
          title: "承诺销售金额",
          key: "contractSellAmount",
          align: "center",
        },
        {
          title: "已完成承诺金额",
          key: "completedAmount",
          align: "center",
        },
        {
          title: "本期账单",
          key: "currentPeriodBill",
          align: "center",
        },
        {
          title: "欠费金额",
          key: "arrears",
          align: "center",
        },
      ],
      talbedata: [],
      billColumns: [
        {
      		title: "公司名称",
      		key: "companyName",
      		align: "center",
      		minWidth: 130,
      		tooltip: true,
      		fixed: 'left',
      	},
      	{
      		title: "客户EBS编码",
      		key: "ebscode",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      	},
      	{
      		title: "Invoice no.",
      		key: "invoiceNo",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      	},
      	{
      		title: "币种",
      		key: "currency",
      		align: "center",
      		minWidth: 100,
      		render: (h, params) => {
      			const row = params.row;
      			const text = row.currency == '156' ? "CNY" : row.currency == '840' ? "USD" : row.currency == '344' ? "HKD" :
      				'';
      			return h('label', text);
      		}
      	},
      	{
      		title: "直接收入总额",
      		key: "directIncome",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      		tooltipMaxWidth: 2000,
      		render: (h, params) => {
      			const row = params.row;
      			const text = parseFloat(math.divide(math.bignumber(row.directIncome), 100).toFixed(2)).toString()
      			return h('label', text);
      		}

      	},
      	{
      		title: "调账金额",
      		key: "accountAdjustment",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      		tooltipMaxWidth: 2000,
      		render: (h, params) => {
      			const row = params.row;
      			const text = parseFloat(math.divide(math.bignumber(row.accountAdjustment), 100).toFixed(2)).toString()
      			return h('label', text);
      		}
      	},
      	{
      		title: "间接收入总额",
      		key: "indirectIncome",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      		tooltipMaxWidth: 2000,
      		render: (h, params) => {
      			const row = params.row;
      			const text = parseFloat(math.divide(math.bignumber(row.indirectIncome), 100).toFixed(2)).toString()
      			return h('label', text);
      		}
      	},
        {
        	title: "流量收入总额",
        	key: "flowTotalAmount",
        	align: "center",
        	minWidth: 150,
        	tooltip: true,
        	tooltipMaxWidth: 2000,
        	render: (h, params) => {
        		const row = params.row;
        		const text = parseFloat(math.divide(math.bignumber(row.flowTotalAmount), 100).toFixed(2)).toString()
        		return h('label', text);
        	}
        },
        {
        	title: "流量调账金额",
        	key: "flowAdjustAmount",
        	align: "center",
        	minWidth: 150,
        	tooltip: true,
        	tooltipMaxWidth: 2000,
        	render: (h, params) => {
        		const row = params.row;
        		const text = parseFloat(math.divide(math.bignumber(row.flowAdjustAmount), 100).toFixed(2)).toString()
        		return h('label', text);
        	}
        },
        {
        	title: "IMSI费收入总额",
        	key: "imsiTotalAmount",
        	align: "center",
        	minWidth: 150,
        	tooltip: true,
        	tooltipMaxWidth: 2000,
        	render: (h, params) => {
        		const row = params.row;
        		const text = parseFloat(math.divide(math.bignumber(row.imsiTotalAmount), 100).toFixed(2)).toString()
        		return h('label', text);
        	}
        },
        {
        	title: "IMSI费调账金额",
        	key: "imsiAdjustAmount",
        	align: "center",
        	minWidth: 150,
        	tooltip: true,
        	tooltipMaxWidth: 2000,
        	render: (h, params) => {
        		const row = params.row;
        		const text = parseFloat(math.divide(math.bignumber(row.imsiAdjustAmount), 100).toFixed(2)).toString()
        		return h('label', text);
        	}
        },
      	{
      		title: "总销售额",
      		key: "saleIncome",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      		tooltipMaxWidth: 2000,
      		render: (h, params) => {
      			const row = params.row;
      			const text = parseFloat(math.divide(math.bignumber(row.saleIncome), 100).toFixed(2)).toString()
      			return h('label', text);
      		}
      	},
      	{
      		title: "直接渠道服务费",
      		key: "dirRemuneration",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      		tooltipMaxWidth: 2000,
      		render: (h, params) => {
      			const row = params.row;
      			const text = parseFloat(math.divide(math.bignumber(row.dirRemuneration), 100).toFixed(2)).toString()
      			return h('label', text);
      		}
      	},
      	{
      		title: "间接渠道服务费",
      		key: "indRemuneration",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      		tooltipMaxWidth: 2000,
      		render: (h, params) => {
      			const row = params.row;
      			const text = parseFloat(math.divide(math.bignumber(row.indRemuneration), 100).toFixed(4)).toString()
      			return h('label', text);
      		}
      	},
      	{
      		title: "渠道服务费总额",
      		key: "totleRemuneration",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      		tooltipMaxWidth: 2000,
      		render: (h, params) => {
      			const row = params.row;
      			const text = parseFloat(math.divide(math.bignumber(row.totleRemuneration), 100).toFixed(2)).toString()
      			return h('label', text);
      		}
      	},
      	{
      		title: "实际收入金额",
      		key: "realIncome",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      		tooltipMaxWidth: 2000,
      		render: (h, params) => {
      			const row = params.row;
      			const text = parseFloat(math.divide(math.bignumber(row.realIncome), 100).toFixed(2)).toString()
      			return h('label', text);
      		}
      	},
      	{
      		title: "服务开始时间",
      		key: "svcStartTime",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      		tooltipMaxWidth: 2000,
      	},
      	{
      		title: "服务结束时间",
      		key: "svcEndTime",
      		align: "center",
      		minWidth: 150,
      		tooltip: true,
      		tooltipMaxWidth: 2000,
      	}
      ],
      billTalbeData: [],
      userName: "",
      corpName: "",
      cooperationMode: "",
      promiseAmount: "",
      currencyCode: "",
      rate: "",
      time: "",
      token: "",
      amount: "",
      date: "",
      pictureUrl: "",
      resulet: "",
      queryParams: "", //url
      ticket: "",
      fileAddress: "",
      total: 0,
      pageSize: 10,
      page: 1,
      spinShow: true,
      receivedLoading: false,
      showButton: true,
      downLoading: false,
      invoiceLoading: false,
      billLoading: false,
      reminderObj: {}, // 催缴列表数据
      whiteCardOperateObj: {}, // 白名单订单数据
      billingDetailColumns: [
        {
          title: "文件名",
          key: "fileName",
          align: "center",
        },
        {
          title: "发送时间",
          key: "sendTime",
          align: "center",
          width: 150
        },
        {
          title: "文件大小",
          key: "fileSize",
          align: "center",
          width: 80
        },
        {
          title: "已确认情况",
          key: "status",
          align: "center",
          width: 100
        }
      ],
      billingDetailData: [
        {
          fileName: "ZR文件_20230331.pdf",
          sendTime: "2023/03/31(16:21:10)",
          fileSize: "7.5KB",
          status: "已确认"
        },
        {
          fileName: "CMAA_Guest_Data_CSV_随机生成.csv",
          sendTime: "2023/03/31(16:21:10)",
          fileSize: "10KB",
          status: "已确认"
        }
      ],
      formModel: {
        remark: "",
      },
      formRules: {
        remark: [
          { required: true, message: '请输入审阅意见', trigger: 'blur' }
        ]
      },
      fileData: [
      ],
      fileColumns: [
        {
          title: "文件名",
          key: "fileName",
          align: "center",
          minWidth: 200
        },
        {
          title: "生成时间",
          key: "createTime",
          align: "center",
          minWidth: 160
        },
        {
          title: "文件大小",
          key: "fileSize",
          align: "center",
          minWidth: 80
        },
        {
          title: "点击下载",
          key: "action",
          align: "center",
          minWidth: 100,
          render: (h, params) => {
            return h('Button', {
              props: {
                type: 'default',
                size: 'small'
              },
              style: {
                marginRight: '5px'
              },
              on: {
                click: () => {
                  this.downloadReminderFile(params.row);
                }
              }
            }, '下载');
          }
        }
      ],
      // 流转意见表格配置
      flowColumns: [
        {
          title: "序号",
          slot: 'index',
          align: "center",
          width: 80
        },
        {
          title: "操作人",
          key: "executorName",
          align: "center",
        },
        {
          title: "操作时间",
          key: "time",
          align: "center",
          width: 150,
        },
        {
          title: "操作类型",
          key: "status",
          align: "center",
          width: 100
        },
        {
          title: "处理意见",
          key: "opinion",
          align: "center",
          ellipsis: true,
          tooltip: true,
          width: 230
        }
      ],
      flowData: [
      ],
    };
  },
  watch: {},
  created() {
    let queryParams = new URLSearchParams(window.location.search);

    if (queryParams.get("userId")) {
      localStorage.setItem("userId", queryParams.get("userId"));
    }
    if (queryParams.get("id")) {
      localStorage.setItem("id", queryParams.get("id"));
    }
    if (queryParams.get("page")) {
      localStorage.setItem("page", queryParams.get("page"));
    }
    if (queryParams.get("corpId")) {
      localStorage.setItem("corpId", queryParams.get("corpId"));
    }
    if (queryParams.get("mode")) {
      localStorage.setItem("mode", queryParams.get("mode"));
    }
    if (queryParams.get("procUniqueId")) {
      localStorage.setItem("procUniqueId", queryParams.get("procUniqueId"));
    }
    if (queryParams.get("todoNodeId")) {
      localStorage.setItem("todoNodeId", queryParams.get("todoNodeId"));
    }
    if (queryParams.get("todoUniqueId")) {
      localStorage.setItem("todoUniqueId", queryParams.get("todoUniqueId"));
    }
    if (queryParams.get("orderId")) {
      localStorage.setItem("orderId", queryParams.get("orderId"));
    }
    if (queryParams.get("batchId")) {
      localStorage.setItem("batchId", queryParams.get("batchId"));
    }
    this.parseUrlParams();
  },
  mounted() {
    this.pageType = localStorage.getItem("page");
  },
  methods: {
    // 外部下载凭证
		exportPaymentProofs() {
      this.downLoading = true
				exportPaymentProofs({
          orderId: localStorage.getItem("orderId"),
          ssoAccessToken: this.token,
					// type: 2
				}).then(res => {
					const content = res.data
					let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content,fileName)
					}
				}).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.downLoading = false
        })
			},
    // 操作白卡是否到账
    // invoice下载
    downloadInvoice: function(row) {
      this.invoiceLoading = true
      downloadInvoice({
      	fileAddress: this.fileAddress,
        ssoAccessToken: this.token,
        corpName: '',
      }).then(res => {
      	const content = res.data
      	let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
      	if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
      		const link = this.$refs.downloadLink // 创建a标签
      		let url = URL.createObjectURL(content)
      		link.download = fileName
      		link.href = url
      		link.click() // 执行下载
      		URL.revokeObjectURL(url) // 释放url
      	} else { // 其他浏览器
      		navigator.msSaveBlob(content,fileName)
      	}
      }).catch((err) => {
        console.error(err)
      }).finally(() => {
        this.invoiceLoading = false
      })
    },
    whiteCardOperate(paid) {
      whiteCardOperate({
        orderId: localStorage.getItem("orderId"),
        ssoAccessToken: this.token,
        paid:paid
      })
        .then((res) => {
          if (res.code == "0000") {
            // 获取数据成功
            console.log(res);
            this.$Notice.success({
              title: "操作提示",
              desc: "操作成功！",
            });
            this.getShowButton();
          }
        })
        .catch((err) => {
          console.error(err);
          this.$Notice.error({
            title: "操作提示",
            desc: err.description,
          });
        })
        .finally(() => {
          // this.loading = false;
        });
    },
    // 获取白卡订单详情数据
    getWhiteCardOperateObj() {
      getWhiteCardOperateObj({
        orderId: localStorage.getItem("orderId"),
        ssoAccessToken: this.token,
      })
        .then((res) => {
          if (res.code == "0000") {
            // 获取数据成功
            console.log(res);
            this.fileAddress = res.data.invoicePath
            this.whiteCardOperateObj = res.data;
          }
        })
        .catch((err) => {
          // console.error(err);
          this.$Notice.error({
            title: "操作提示",
            desc: err.msg,
          });
        })
        .finally(() => {
          this.spinShow = false;
        });
    },
    // 催缴列表数据
    getreminders() {
      getreminders({
        id: localStorage.getItem("id"),
        ssoAccessToken: this.token,
        todoNodeId: localStorage.getItem("todoNodeId"),
        procUniqueId: localStorage.getItem("procUniqueId"),
      })
        .then((res) => {
          if (res.code == "0000") {
            // 获取数据成功
            console.log(res);
            this.reminderObj = res.data;
            //查看审阅列表
            this.getOpinion();
          }
        })
        .catch((err) => {
          // console.error(err);
          this.$Notice.error({
            title: "操作提示",
            desc: err.msg,
          });
        })
        .finally(() => {
          this.spinShow = false;
        });
    },
    // 完成催缴事项待办
    getremindersPushFinish() {
      getremindersPushFinish({
        ssoAccessToken: this.token,
        todoNodeId: localStorage.getItem("todoNodeId"),
        procUniqueId: localStorage.getItem("procUniqueId"),
        opinion:this.formModel.remark
      })
        .then((res) => {
          if (res.code == "0000") {
            this.getOpinion();
            this.formModel.remark = '';
            // 获取数据成功
            console.log(res);
            this.$Notice.success({
              title: "操作提示",
              desc: "操作成功！",
            });
            this.getShowButton();
          }
        })
        .catch((err) => {
          console.error(err);
          this.$Notice.error({
            title: "操作提示",
            desc: err.description,
          });
        })
        .finally(() => {
          // this.loading = false;
        });
    },
    // 获取承诺量完成情况数据
    getChannelPromiseDTO() {
      getChannelPromiseDTO({
        ssoAccessToken: this.token,
        corpId: localStorage.getItem("corpId"),
        mode: localStorage.getItem("mode"),
        todoNodeId: localStorage.getItem("todoNodeId"),
        procUniqueId: localStorage.getItem("procUniqueId"),
      })
        .then((res) => {
          if (res.code == "0000") {
            this.$Notice.success({
              title: "操作提示",
              desc: "操作成功！",
            });
            // 获取数据成功
            this.corpName = res.data.corpName;
            this.promiseAmount = res.data.promiseAmount;
            this.amount = res.data.amount;
            this.rate = res.data.rate;
            this.date = res.data.date;
            this.cooperationMode =
              res.data.cooperationMode == "1"
                ? "代销"
                : res.data.cooperationMode == "2"
                ? "A2Z"
                : res.data.cooperationMode == "3"
                ? "资源合作"
                : "";
            this.currencyCode =
              res.data.currencyCode == "156"
                ? "人民币"
                : res.data.currencyCode == "344"
                ? "港币"
                : res.data.currencyCode == "840"
                ? "美元"
                : "";
          }
        })
        .catch((err) => {
          console.error(err);
          this.$Notice.error({
            title: "操作提示",
            desc: err.description
          })
        })
        .finally(() => {
          // this.loading = false;
        });
    },
    // 完成承诺量完成情况待办
    channelPushFinish() {
      this.besureSubmitLoading = true;
      channelPushFinish({
        ssoAccessToken: this.token,
        todoNodeId: localStorage.getItem("todoNodeId"),
        procUniqueId: localStorage.getItem("procUniqueId"),
      })
        .then((res) => {
          if (res.code == "0000") {
            this.$Notice.success({
              title: "操作提示",
              desc: "操作成功！",
            });
            this.getShowButton();
            // 获取数据成功
            console.log(res);
          }
        })
        .catch((err) => {
          console.error(err);
          this.$Notice.error({
            title: "操作提示",
            desc: err.description
          })
        })
        .finally(() => {
          this.besureSubmitLoading = false;
        });
    },
    // 完成渠道商销售列表待办
    confirm() {
      pushFinish({
        ssoAccessToken: this.token,
        todoNodeId: localStorage.getItem("todoNodeId"),
        procUniqueId: localStorage.getItem("procUniqueId"),
      })
        .then((res) => {
          if (res.code == "0000") {
            this.$Notice.success({
              title: "操作提示",
              desc: "操作成功！",
            });
            this.tipsModal = false;
            this.getShowButton();
          }
        })
        .catch((err) => {
          console.error(err);
          this.$Notice.success({
            title: "操作提示",
            desc: err.msg,
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取渠道商销售数据列表——运营
    getPage() {
      this.loading = true;
      var _this = this;
      getPage({
        ssoAccessToken: this.token,
        todoNodeId: localStorage.getItem("todoNodeId"),
        procUniqueId: localStorage.getItem("procUniqueId"),
        userId: localStorage.getItem("userId"),
        batchId: localStorage.getItem("batchId"),
      })
        .then((res) => {
          if (res.code == "0000") {
            _this.loading = false;
            this.talbedata = res.data.list;
          }
        })
        .catch((err) => {
          console.error(err);
          this.$Notice.error({
            title: "操作提示",
            desc: err.msg,
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取出账列表——财务
    getDisbursePage(page) {
      this.page = page;
      this.billLoading = true;
      var _this = this;
      getDisbursePage({
        "batchId": localStorage.getItem("batchId"),
        "pageNum": page,
        "pageSize": this.pageSize,
        "ssoAccessToken": this.token
      })
        .then((res) => {
          if (res.code == "0000") {
            _this.billLoading = false;
            this.billTalbeData = res.data;
            this.total = Number(res.count);
          }
        })
        .catch((err) => {
          console.error(err);
          this.$Notice.error({
            title: "操作提示",
            desc: err.msg,
          });
        })
        .finally(() => {
          this.billLoading = false;
        });
    },
    // 白卡——运营人员——已阅操作
    received: function() {
    	this.$Modal.confirm({
    		title: "确定执行已阅？",
    		onOk: () => {
          this.receivedLoading = true
    			receivedOperation({
            "ssoAccessToken": this.token,
            "procUniqueId": localStorage.getItem('procUniqueId'),
            "orderId": localStorage.getItem('orderId')
          }).then((res) => {
    				if (res.code === "0000") {
    					this.$Notice.success({
    						title: "操作提示",
    						desc: "操作成功！",
    					});
              this.getShowButton()
    				}
    			}).catch((err) => {
            this.$Notice.error({
              title: "操作提示",
              desc: err.description
            })
          }).finally(() => {
            this.receivedLoading = false
          })
    		},
    	});
    },
    // 按钮是否可用
    getShowButton: function (type) {
      getShowButton({
        ssoAccessToken: this.token,
        todoUniqueId: localStorage.getItem("todoUniqueId"),
      })
        .then((res) => {
          if (res.code === "0000") {
            if (res.data == "1") {
              // 未办结
              this.showButton = false;
            } else {
              // 办结
              this.showButton = true;
            }
            console.log(!this.showButton, localStorage.getItem("page") == "1");
            if ((localStorage.getItem("page") == "1" || localStorage.getItem("page") == "5") && !this.showButton) {
              this.tipsModal = true;
            }
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {});
    },
    getToken: function (ticket) {
      getToken({
        ticket: ticket,
        service: this.getUrlWithoutParams(),
      })
        .then((res) => {
          if (res.code === "0000") {
            this.token = res.accessToken;
            this.userName = res.tryUser;
            this.getShowButton(); // 按钮是否可用
            if (this.pageType == "1") {
              // 获取渠道商销售数据
              this.getPage();
            } else if (this.pageType == "2") {
              // 获取出账待办信息
              console.log("获取出账待办信息");
              this.getreminders();
            } else if (this.pageType == "3") {
              // 获取承诺量完成情况
              console.log("获取承诺量完成情况");
              this.getChannelPromiseDTO();
            } else if (this.pageType == "4" || this.pageType == "6") {
              // 获取白卡订单详情数据
              console.log("获取白卡订单详情数据");
              this.getWhiteCardOperateObj();
            } else if (this.pageType == "5") {
              this.getDisbursePage(this.page)
              console.log("出账待办-财务人员");
            }
          }
        })
        .catch((err) => {
          console.error(err);
          this.$Notice.error({
            title: "操作提示",
            desc: err.description,
          });
        })
        .finally(() => {
          sessionStorage.removeItem("ticket")
        });
    },
    // 获取url不携带参数
    getUrlWithoutParams() {
      var url = window.location.href;
      var index = url.indexOf("?");
      if (index !== -1) {
        return url.substring(0, index);
      } else {
        return url;
      }
    },
    // 获取页面url携带的参数
    parseUrlParams() {
      const urlParams = new URLSearchParams(window.location.search);
      let params = {};
      for (let [key, value] of urlParams.entries()) {
        params[key] = value.trim();
      }
      this.queryParams = params;

      // 优先处理ticket
      if (this.queryParams.ticket) {
        this.getToken(this.queryParams.ticket);
        sessionStorage.setItem("ticket", this.queryParams.ticket);
      }
      // 如果没有ticket再检查是否需要重定向
      else {
        this.checkRedirect();
      }
    },
	// 检查是否需要重定向
    async checkRedirect() {
      if (!window.location.hash.includes("#redirected")) {
        try {
          const key = 'todo-url';  // 动态传入不同的 key
          const redirectUrl = await this.$getRedirectUrl(key);
          if (redirectUrl) {
            const paramsUrl = this.getUrlWithoutParams();
            let url = redirectUrl + '?service=' + paramsUrl;
            window.location.replace(`${url}#redirected`);
          }
        } catch (error) {
          console.error("重定向失败:", error);
        }
      }
    },
    beforeDestroy() {
      // 组件销毁前执行的代码
      localStorage.removeItem("id");
      localStorage.removeItem("page");
      localStorage.removeItem("corpId");
      localStorage.removeItem("mode");
      localStorage.removeItem("procUniqueId");
      localStorage.removeItem("todoNodeId");
      localStorage.removeItem("todoUniqueId");
      localStorage.removeItem("orderId");
      localStorage.removeItem("batchId");
    },
    // 处理确认按钮点击
    handleSubmit() {
      if(this.formModel.remark == ''||this.formModel.remark == null){
        this.$Notice.error({
          title: "操作提示",
          desc: "请输入审阅意见",
        });
        return;
      }
      this.getremindersPushFinish();
    },
    // 查看审阅列表
    getOpinion() {
      console.log(localStorage.getItem("corpId"))
      getOpinion({
        "ssoAccessToken": this.token,
        "procUniqueId": localStorage.getItem("procUniqueId"),
      }).then((res) => {
        if (res.code === "0000") {
          this.flowData = res.data;
        }
      });

    },
    // 下载文件
    downloadReminderFile(file) {
      let data = {
        "ssoAccessToken":this.token,
        "filePath":file.filePath
      }
     downloadReminderFile(data).then(res => {
					const content = res.data
					let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content,fileName)
					}
				}).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.downLoading = false
        })
    },
  },
};
</script>

<style scoped>
.content-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: rgb(245, 247, 249);
}
.ivu-row {
  margin: 10px 0;
  font-size: 16px;
  font-weight: bold;
}
.page3-box {
  width: 700px;
  margin-top: 20px;
  padding: 20px 10px;
}

.row-box {
  margin: 40px 0 0 0;
}
.col-box {
  display: flex;
  justify-content: flex-start;
}
.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 账单催缴专属样式 - 只对pageType=2生效 */
.bill-container {
  padding: 0 20px;
}

.bill-main-content {
  display: flex;
}

.bill-bottom-content {
  display: flex;
}

.bill-left-content {
  flex: 1;
  padding-right: 20px;
  border-right: 1px solid #e8e8e8;
}

.bill-right-content {
  flex: 1;
  padding-left: 20px;
}

.bill-bottom-left {
  flex: 1;
  padding-right: 20px;
  border-right: 1px solid #e8e8e8;
}

.bill-bottom-right {
  flex: 1;
  padding-left: 20px;
}

.bill-section {
  margin-bottom: 0;
}

.bill-vertical-divider, .bill-bottom-vertical-divider {
  width: 1px;
  background-color: #e8e8e8;
  top: 0;
  bottom: 0;
  left: 50%;
  margin-left: -1px;
  z-index: 10;
  height: 100%;
}

.billing-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  color: #333;
  margin: 20px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 150px 1fr 150px 1fr;
  grid-row-gap: 25px;
  padding: 10px 0;
}

.info-label {
  text-align: right;
  padding-right: 10px;
  color: #333;
  font-weight: normal;
}

.info-value {
  text-align: left;
}

.remark-textarea {
  padding: 10px 0;
  margin-bottom: 30px;
}

.remark-textarea textarea {
  width: 100%;
  height: 150px;
  border: 1px solid #dcdee2;
  padding: 8px;
  resize: none;
}

.confirm-button-container {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}

.blue-title {
  color: #00b7ee;
  font-size: 16px;
  font-weight: bold;
  padding: 0 0 5px 0;
  margin-bottom: 0;
  text-align: left;
}

.red-divider {
  height: 1px;
  background-color: #e8e8e8;
  margin-bottom: 15px;
  width: 100%;
}

.table-container {
  width: 100%;
  overflow-x: hidden;
}
</style>
