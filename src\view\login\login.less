.login{
    width: 100%;
    height: 100%;
    overflow: auto;
    background-image: url('../../assets/images/background.png');
    background-repeat: no-repeat;
    background-size: cover;
    // background-position: center;
	&-header{
		width: 100%;
		padding: 30px 60px 20px 60px;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: center;
		.left-box{
			display: flex;
			justify-content: flex-start;
			align-items: center;
			.cmlink{
				width: 150px;
				min-width: 100px;
				height: 80px;
			}
			.line{
				margin: 0 30px;
				width: 1px;
				height: 76px;
				background-color: rgb(122,175,85);
			}
			.name{
				font-size: 32px;
				font-weight: bolder;
				background-image: linear-gradient(to right, rgb(0,133,208) 30%, rgb(137,185,41) 60%);
				color: transparent;
				-webkit-background-clip: text;
			}
		}
		.right-box{
			display: flex;
			justify-content: flex-end;
			align-items: center;
			font-size: 16px;
			cursor: pointer;
			.line{
				margin-left: 10px;
				margin-right: 10px;
				width: 3px;
				height: 100%;
			}
		}
	}
    &-section{
		width: 100%;
		height: 70px;
		margin-bottom: 20px;
		display: flex;
		justify-content: center;
		align-items: center;
		box-sizing: border-box;
		.trending-box{
			width: 50%;
			height: 100%;
			position: relative;
			z-index: 2;
			.trending{
				width: 100%;
				height: 90%;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				z-index: 2;
				.global{
					width: 65px;
					min-width: 65px;
					height: 100%;
					background-color: #FFFFFF;
					border: 4px solid rgb(143,195,31);
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
					position: relative;
					z-index: 3;
				}
				.parallelogram{
					width: 100%;
					min-width: 150px;
					height: 85%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: flex-start;
					position: relative;
					left: -25px;
					z-index: 2;
					.text-box{
						width: 100%;
						height: 88%;
						background-color: #FFFFFF;
						transform: skewX(-20deg);
						.text-box-child{
							padding-left: 60px;
							max-width: 100%;
							display: inline-block;
							vertical-align: top;
							overflow: hidden;
							white-space: nowrap;
							line-height: 50px;
							transform: skewX(20deg);
							cursor: pointer;
							.marquee{
								line-height: 50px;
								font-size: 16px;
								font-weight: 600;
								background-image: linear-gradient(to right, #0085d0 40%, #89b929 50%);
								//将盒子沿着文字裁剪
								-webkit-background-clip: text;
								//设置文字为透明色
								-webkit-text-fill-color: transparent;
								//将背景应用到文字上
								background-clip: text;
								animation: 20s wordsLoop linear infinite normal;
								float: left;
							}
							.marquees{
								line-height: 50px;
								font-size: 16px;
								font-weight: 600;
								background-image: linear-gradient(to right, #0085d0 40%, #89b929 50%);
								//将盒子沿着文字裁剪
								-webkit-background-clip: text;
								//设置文字为透明色
								-webkit-text-fill-color: transparent;
								//将背景应用到文字上
								background-clip: text;
							}
							@keyframes wordsLoop {
								0% {
									transform: translateX(0);
									-webkit-transform: translateX(0);
								}

								100% {
									transform: translateX(-100%);
									-webkit-transform: translateX(-100%);
								}
							}

							@-webkit-keyframes wordsLoop {
								0% {
									transform: translateX(0);
									-webkit-transform: translateX(0);
								}

								100% {
									transform: translateX(-100%);
									-webkit-transform: translateX(-100%);
								}
							}
						}
						.marquee:hover {
							animation-play-state: paused;
						}
					}
					.blueline{
						width: 80%;
						height: 12%;
						background-color: rgb(16,141,211);
						transform: skewX(-18deg);
					}
				}
			}
			.onebox{
				width: 2%;
				height: 20px;
				margin-left: 11%;
				background-color: rgb(143,195,31);
				transform: skewX(-66deg);
				position: absolute;
				z-index: 1;
				top: 63%;
			}
			.twobox{
				width: 2%;
				height: 20px;
				margin-left: 14%;
				background-color: rgb(143,195,31);
				transform: skewX(-66deg);
				position: absolute;
				z-index: 1;
				top: 63%;
			}
			.threebox{
				width: 2%;
				height: 20px;
				margin-left: 17%;
				background-color: rgb(143,195,31);
				transform: skewX(-66deg);
				position: absolute;
				z-index: 1;
				top: 63%;
			}
			.greenline{
				width: 70%;
				height: 20px;
				margin-left: 16%;
				background-color: rgb(143,195,31);
				transform: skewX(-40deg);
				position: absolute;
				z-index: 1;
				top: 63%;
			}
		}
	}
	&-con{
		width: 100%;
		height: 550px;
		margin: 30px 0;
		display: flex;
		justify-content: center;
		align-items: center;
		.card{
			width: 88%;
			height: 100%;
			padding: 30px;
			box-sizing: border-box;
			background-image: url('../../assets/images/city.png');
			background-size: cover;
			background-repeat: no-repeat;
			background-position: left bottom;
			border-radius: 10px;
			box-shadow: 2px 2px 6px rgb(179,212,236);
			-moz-box-shadow: 2px 2px 6px rgb(179,212,236);
			-webkit-box-shadow: 2px 2px 6px rgb(179,212,236);
			position: relative;
			z-index: 3;
			.login-box{
				width: 30%;
				min-width: 280px;
				position: absolute;
				top: 10%;
				right: 1%;
				// 去掉登录下面的横线
				/deep/.ivu-tabs-ink-bar{
					height: 0;
				}
				/deep/.ivu-tabs-bar {
				    border-bottom: 0px solid transparent;
					margin-left: -14%;
					display: flex;
					justify-content: center;
					align-items: center;
				}
				/deep/.ivu-tabs-nav .ivu-tabs-tab-active {
				    color: #2d8cf0;
				    font-weight: 700;
				    font-size: 18px;
				}
			}

		}
        &-header{
            font-size: 16px;
            font-weight: 300;
            text-align: center;
            padding: 30px 0;
        }
        .form-con{
            padding: 10px 0 0;
        }
        .login-tip{
            font-size: 10px;
            text-align: center;
            color: #c3c3c3;
        }
    }

    &-cood{
      width: 100%;
      height: 550px;
      margin: 30px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      .card{
      	width: 88%;
      	height: 100%;
        padding: 30px;
      	box-sizing: border-box;
      	background-image: url('../../assets/images/city.png');
      	background-size: cover;
      	background-repeat: no-repeat;
      	background-position: left;
      	border-radius: 10px;
      	box-shadow: 2px 2px 6px rgb(179,212,236);
      	-moz-box-shadow: 2px 2px 6px rgb(179,212,236);
      	-webkit-box-shadow: 2px 2px 6px rgb(179,212,236);
      	position: relative;
      	z-index: 3;
      	.relogin-box{
      		width: 30%;
          min-width: 280px;
          position: absolute;
          top: 10%;
          right: 1%;
          // 去掉登录下面的横线
          /deep/.ivu-tabs-ink-bar{
          	height: 0;
          }
          /deep/.ivu-tabs-bar {
            border-bottom: 0px solid transparent;
          	margin-left: -14%;
          	display: flex;
          	justify-content: center;
          	align-items: center;
          }
      		/deep/.ivu-tabs-nav .ivu-tabs-tab-active {
              color: #2d8cf0;
              font-weight: 700;
              font-size: 18px;
      		}
      		.form-con{
      		    padding: 10px 0 0;
      		}
      		.login-tip{
      		    font-size: 10px;
      		    text-align: center;
      		    color: #c3c3c3;
      		}
      		/deep/.ivu-tabs-bar {
      			display: flex;
      			justify-content: center;
      			align-items: center;
      		}
      	}
      }
    }


    &-conPwd{
		width: 100%;
		height: 550px;
		margin: 30px 0;
		display: flex;
		justify-content: center;
		align-items: center;
		.card{
			width: 88%;
			height: 100%;
			box-sizing: border-box;
			background-image: url('../../assets/images/city.png');
			background-size: cover;
			background-repeat: no-repeat;
			background-position: left;
			border-radius: 10px;
			box-shadow: 2px 2px 6px rgb(179,212,236);
			-moz-box-shadow: 2px 2px 6px rgb(179,212,236);
			-webkit-box-shadow: 2px 2px 6px rgb(179,212,236);
			display: flex;
			justify-content: flex-end;
			align-items: center;
			flex-wrap: wrap;
			position: static;
			z-index: 3;
			.relogin-box{
				width: 100%;
				height: 100%;
				margin-right: 20px;
				display: flex;
				justify-content: flex-end;
				align-items: center;
				/deep/.ivu-tabs-nav .ivu-tabs-tab-active {
				    color: #2d8cf0;
				    font-size: 16px;
				}
				&-header{
				    font-size: 16px;
				    font-weight: 300;
				    text-align: center;
				    padding: 30px 0;
				}
				.form-con{
				    padding: 10px 0 0;
				}
				.login-tip{
				    font-size: 10px;
				    text-align: center;
				    color: #c3c3c3;
				}
				/deep/.ivu-tabs-bar {
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}
    }
}
#loginTabs .ivu-tabs-nav-scroll{
   display: flex;
    justify-content: center;
    align-items: flex-end;
    height: 50px;
}
