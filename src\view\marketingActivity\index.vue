<template>
  <Card style="width: 100%; padiing: 16px">
    <Form ref="searchForm" label-position="right" :model="searchObj" inline :rules="formRuleList">
      <FormItem label="活动名称" :label-width="80">
        <Input v-model="searchObj.campaignName" placeholder="请输入活动名称" clearable />
      </FormItem>
      <FormItem label="合作模式" :label-width="80">
        <Select class="fixed-width-select" filterable v-model="searchObj.cooperationMode" placeholder="请选择合作模式"
          clearable>
          <Option value="1">代销</Option>
          <Option value="2">A2Z</Option>
        </Select>
      </FormItem>
      <FormItem label="活动开始时间" :label-width="100">
        <DatePicker v-model="searchObj.startTime" type="date" placeholder="请选择开始时间" />
      </FormItem>
      <FormItem label="活动结束时间" :label-width="100" prop="endTime">
        <DatePicker v-model="searchObj.endTime" type="date" placeholder="请选择结束时间" />
      </FormItem>
      <FormItem label="参与客户" :label-width="80">
        <Select v-model="searchObj.corpId" placeholder="请选择参与客户" filterable clearable>
          <Option v-for="item in customerList" :value="item.corpId" :key="item.corpId" class="company-option">{{ item.companyName }}</Option>
        </Select>
      </FormItem>
      <FormItem>
        <Button type="primary" @click="loadByPage(1)" icon="ios-search" v-has="'search'">搜索</Button>
        <Button type="info" @click="newMarketing" icon="ios-add" style="margin-left: 20px"
          v-has="'addMarketingActivity'">新建</Button>
      </FormItem>
    </Form>
    <Table :columns="columns" :data="tableData" :loading="tableLoading" :ellipsis="true">
      <template slot-scope="{ row }" slot="campaignStatus">
        <span :style="{ color: getStatusColor(row.campaignStatus) }">{{ getStatusText(row.campaignStatus) }}</span>
      </template>
      <template slot-scope="{ row }" slot="company">
        <Button type="primary" size="small" @click="viewCompanyDetails(row)" v-has="'ruleDescription'">点击查看</Button>
      </template>
      <template slot-scope="{ row }" slot="rules">
        <Button type="primary" size="small" @click="viewRulesPopup(row)" v-has="'participateInCompany'">点击查看</Button>
      </template>
      <template slot-scope="{ row }" slot="action" >
        <div v-has="'updateMarketingActivity'">
          <Button type="primary" size="small" @click="editMarketingObj(row)" v-if="row.campaignStatus === '0'||row.campaignStatus === '1'">编辑</Button>
        </div>
      </template>
    </Table>
    <Page :total="total" show-total show-elevator :page-size="searchObj.pageSize" :current.sync="searchObj.pageNum"
      @on-change="loadByPage" style="margin: 15px 0" />
    <!-- 引入参与公司详情组件 -->
    <CompanyModal :companyVisible="companyModalVisible" :selectedRow="selectedRow" :companyDetails="companyDetails"
      @companyClose="companyClose" @refresh-table="loadByPage(1)" />

    <!-- 引入规则详情组件 -->
    <RulesModal :rulesVisible="rulesModalVisible" :selectedRow="selectedRow" :ruleDetails="ruleDetails"
      @ruleClose="ruleClose" />


  </Card>
</template>

<script>
import { getCampaignList, getRuleDetails, getCorpDetails, getCorpList, getCampaignPage } from "@/api/marketingActivity/index";
import RulesModal from "@/components/marketingActivity/rules.vue";
import CompanyModal from "@/components/marketingActivity/company.vue";
import { formatDateTime, formatDate } from "@/libs/tools";

export default {
  components: { RulesModal, CompanyModal },
  data () {
    return {
      searchObj: {
        campaignName: "",
        cooperationMode: "",
        startTime: "",
        endTime: "",
        corpId: "",
        pageSize: 10,
        pageNum: 1
      },
      customerList: [],
      customerLoading: false,
      tableData: [],
      tableLoading: false,
      total: 0,
      columns: [
        { title: "活动名称", key: "campaignName", align: "center" },
        {
          title: "合作模式", key: "cooperationMode", align: "center", render: (h, params) => {
            return h('span', params.row.cooperationMode === '1' ? '代销' : 'A2Z');
          }
        },
        {
          title: "活动开始时间",
          key: "startTime",
          align: "center",
          render: (h, params) => {
            return h('span', formatDate(params.row.startTime));
          }
        },
        {
          title: "活动结束时间",
          key: "endTime",
          align: "center",
          render: (h, params) => {
            return h('span', formatDate(params.row.endTime));
          }
        },
        { title: "活动状态", slot: "campaignStatus", align: "center" },
        { title: "规则描述", slot: "rules", align: "center" },
        { title: "参与公司", slot: "company", align: "center" },
        { title: "操作", slot: "action", align: "center" },
      ],
      rulesModalVisible: false,
      companyModalVisible: false,
      editMarketingModalVisible: false,
      selectedRow: {}, // 用于存储当前选中的行数据
      involvedCompaniesColumnsDx: [
        { title: '公司名称', key: 'companyName', align: "center" },
        { title: '已充值金额', key: 'rechargeAmount', align: "center" },
        { title: '预计返利金额', key: 'expectedRebate', align: "center" }
      ],
      involvedCompaniesColumnsA2Z: [
        { title: '公司名称', key: 'companyName', align: "center" },
        { title: '上期金额', key: 'rechargeAmount', align: "center" },
        { title: '本期金额', key: 'expectedRebate', align: "center" },
        { title: '预计返利', key: 'expectedRebate', align: "center" }
      ],
      involvedCompaniesTableData: [],

      involvedCompaniesPageSize: 10,
      involvedCompaniesPage: 1,
      involvedCompaniesTotal: 0,
      involvedCompaniesLoading: false,
      ruleDetails: {
        sequentialGrowth: 0,
        sequentialGrowth2: 0,
        sequentialGrowth3: 0,
        returnRatio: 0,
        returnRatio2: 0
      },
      companyDetails: {
        totalExpectedRebate: 0, // 总预计返利
        corpList: [] // 公司列表数据
      },
      formRuleList: {
        endTime: [
          { validator: this.validateEndTime, trigger: 'change' }
        ],
      }
      //结束时间不能小于开始时间


    };
  },
  methods: {
    getCorpList () {
      this.customerLoading = true;
      getCorpList().then(response => {
        this.customerList = response.data;
        this.customerLoading = false;
      }).catch(() => {
        this.customerLoading = false;
      });
    },
    searchMarketing () {
      this.$refs.searchForm.validate(async (valid) => {
        if (valid) {
          this.tableLoading = true;
          const { campaignName, cooperationMode, startTime, endTime, corpId, pageSize, pageNum } = this.searchObj;
          //需要将：campaignName前后去除空格
          getCampaignPage({
            campaignName: campaignName.trim(),
            cooperationMode,
            startTime: this.formatDateToServer(startTime),
            endTime: this.formatDateToServer(endTime),
            corpId,
            pageSize,
            pageNum
          }).then(response => {
            if (response.code === '0000') {
              this.tableData = response.data;
              this.total = parseInt(response.count);
            } else {
              this.$Message.error(response.message || '获取数据失败');
            }
            this.tableLoading = false;
          }).catch(() => {
            this.$Message.error('获取数据失败');
            this.tableLoading = false;
          });
        }
      })

    },
    newMarketing () {
      this.$router.push({ name: "marketingActivityAdd", query: { type: "add" } });
    },

    viewRulesPopup (row) {
      this.rulesModalVisible = true; // 显示规则描述弹窗
      //数据初始化
      this.ruleDetails = {};
      this.selectedRow = {};
      //请求规则详情接口，传参mcId
      getRuleDetails({
        mcId: row.id
      }).then(response => {
        // 获取规则详情接口返回数据,并传参给组件RulesModal
        this.selectedRow = response.data; // 存储当前选中的行数据
        this.ruleDetails = response.data; // 存储规则详情数据
      }).catch(() => {
        this.rulesModalVisible = false;
      });
    },
    viewCompanyDetails (row) {
      // 获取公司详情数据
      getCorpDetails({
        mcId: row.id
      }).then(response => {
        if (response.code === '0000') {
          this.selectedRow = {
            ...row,
            ...response.data
          }; // 合并行数据和详情数据
          this.companyDetails = response.data; // 存储公司详情数据
          this.companyModalVisible = true; // 显示参与公司详情弹窗
        } else {
          this.$Message.error(response.message || '获取公司详情失败');
        }
      }).catch(() => {
        this.$Message.error('获取公司详情失败');
      });
    },
    ruleClose () {
      this.rulesModalVisible = false;
    },
    companyClose () {
      this.companyModalVisible = false;
    },
    editMarketingObj (row) {
      this.selectedRow = row; // 存储当前选中的行数据
      this.$router.push({
        name: "marketingActivityUpdate",
        query: {
          mcId: row.id
        }
      });
    },
    saveEditMarketing () {
      // 实现保存编辑的功能，例如通过 API 提交修改后的数据
      this.editMarketingModalVisible = false; // 关闭编辑弹窗
      // 可能需要刷新表格数据等操作
    },
    getStatusText (campaignStatus) {
      switch (campaignStatus) {
        case "0": return "待开始";
        case "1": return "已开始";
        case "2": return "已结束";
        case "3": return "已作废";
        case "4": return "提前结束";
        case "5": return "重新结算中";
        default: return "";
      }
    },
    getStatusColor (campaignStatus) {
      switch (campaignStatus) {
        case "0": return "gray";
        case "1": return "green";
        case "2": return "red";
        case "3": return "black";
        case "4": return "orange";
        case "5": return "blue";
        default: return "";
      }
    },
    formatDateToClient (dateString) {
      return formatDate(dateString);
    },
    formatDateToServer (dateString) {
      if (!dateString) return '';
      return formatDateTime(dateString, this.searchObj.endTime === dateString);
    },
    loadByPage (page) {
      this.searchObj.pageNum = page;
      this.searchMarketing();
    },
    init () {
      this.loadByPage(1);
      this.getCorpList();
    },
    involvedCompaniesLoadByPage (page) {
      this.involvedCompaniesPage = page
    },
    validateEndTime (rule, value, callback) {
      if (value&&(value < this.searchObj.startTime)) {
        callback(new Error('结束时间不能小于开始时间'));
      } else {
        callback();
      }
    }
  },
  mounted () {
    this.init(); // 页面加载时自动执行搜索
  },
};
</script>

<style lang="less" scoped>
.fixed-width-select {
  width: 162px;
  /* 调整为适合你的布局的宽度 */
}

.detail-rule {
  padding: 10px;
  border: 1px solid #ccc;

}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.company-option) {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


</style>
