<template>
  <Card class="card-wrapper">
    <Form ref="campaignForm" :model="campaign" :rules="formRules" :label-width="120" class="form-wrapper">
      <div class="form-section">
        <!-- 基本信息部分 -->
        <div class="section-header">基本信息</div>
        <div class="section-content">
          <Row :gutter="24">
            <Col span="10">
            <FormItem label="活动名称" prop="name">
              <Input v-model="campaign.name" placeholder="请输入活动名称" class="uniform-input"
                :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.name)" />
            </FormItem>
            </Col>
            <Col span="10">
            <FormItem label="合作模式" prop="cooperationMode">
              <Select v-model="campaign.cooperationMode" placeholder="请选择合作模式" class="uniform-input"
                @on-change="handleCooperationModeChange"
                :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.cooperationMode)">
                <Option value="1">代销</Option>
                <Option value="2">A2Z</Option>
              </Select>
            </FormItem>
            </Col>
            <Col span="2">
            </Col>
          </Row>

          <!-- 代销模式 -->
          <template v-if="campaign.cooperationMode == 1">
            <Row :gutter="24">
              <Col span="10">
              <FormItem label="活动起止时间" prop="activityTimeRange">
                <DatePicker v-model="campaign.activityTimeRange" type="daterange" class="uniform-input"
                  placement="bottom-start" placeholder="请选择活动起止时间范围"
                  :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.activityTimeRange)"
                  :options="datePickerOptions" @on-change="handleActivityTimeChange" />
              </FormItem>
              </Col>
              <Col span="10">
              <FormItem label="返还金有效期" prop="restitutionFundsTime">
                <DatePicker v-model="campaign.restitutionFundsTime" type="date" class="uniform-input"
                  placeholder="请选择返还金有效期"
                  :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.restitutionFundsTime)" />
              </FormItem>
              </Col>
              <Col span="2">
              </Col>
            </Row>
            <Row :gutter="24">
              <Col span="10">
              <FormItem label="返还金总额" prop="totalAmountRefund">
                <div class="amount-input-group">
                  <InputNumber v-model="campaign.totalAmountRefund" placeholder="请输入返还金总额" :min="0.01" class="uniform-input"
                    :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.totalAmountRefund)" />
                  <span class="unit-label">港币</span>
                </div>
              </FormItem>
              </Col>
              <Col span="10">
              <FormItem label="活动状态" prop="campaignStatus" v-if="mcId">
                <Select v-model="campaign.campaignStatus" placeholder="请选择活动状态" class="uniform-input"
                  :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.campaignStatus)">
                  <Option v-for="option in availableStatusOptions" :value="option.value" :key="option.value">
                    {{ option.label }}
                  </Option>
                </Select>
              </FormItem>
              </Col>
              <Col span="2">
              </Col>
            </Row>
          </template>

          <!-- A2Z模式 -->
          <template v-else>
            <Row :gutter="24">
              <Col span="10">
              <FormItem label="环比起止时间" prop="sequentialTimeRange">
                <DatePicker v-model="campaign.sequentialTimeRange" type="daterange" class="uniform-input"
                  placement="bottom-start" placeholder="请选择环比起止时间范围"
                  :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.sequentialStartTime)"
                  @on-change="handleSequentialTimeChange" />
              </FormItem>
              </Col>
              <Col span="10">
              <FormItem label="活动起止时间" prop="activityTimeRange">
                <DatePicker v-model="campaign.activityTimeRange" type="daterange" class="uniform-input"
                  placement="bottom-start" placeholder="请选择活动起止时间范围"
                  :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.activityTimeRange)"
                  @on-change="handleActivityTimeChange" />
              </FormItem>
              </Col>
              <Col span="2">
              </Col>
            </Row>
            <Row :gutter="24">
              <Col span="10">
              <FormItem label="返还金有效期" prop="restitutionFundsTime">
                <DatePicker v-model="campaign.restitutionFundsTime" type="date" class="uniform-input"
                  placeholder="请选择返还金有效期"
                  :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.restitutionFundsTime)" />
              </FormItem>
              </Col>
              <Col span="10">
              <FormItem label="活动状态" prop="campaignStatus" v-if="mcId">
                <Select v-model="campaign.campaignStatus" placeholder="请选择活动状态" class="uniform-input"
                  :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.campaignStatus)">
                  <Option v-for="option in availableStatusOptions" :value="option.value" :key="option.value">
                    {{ option.label }}
                  </Option>
                </Select>
              </FormItem>
              </Col>
              <Col span="2">
              </Col>
            </Row>
          </template>
        </div>

        <!-- 规则信息部分 -->
        <div class="section-header">{{ campaign.cooperationMode == 1 ? "累计返还规则" : "规则信息" }}</div>
        <div class="section-content">
          <!-- 返还规则部分 -->
          <div class="rules-section">
            <FormItem :label-width="5" class="rules-header">
              <div class="rules-container">
                <Row :gutter="24">
                  <!-- 代销模式规则 -->
                  <template v-if="campaign.cooperationMode == 1">
                    <div v-for="(rule, index) in campaign.ruleList" :key="index" class="rule-item" >
                      <Col span="10">
                      <FormItem label="预存款" :prop="'ruleList.' + index + '.deposit'" :rules="formRules.deposit">
                        <div class="input-wrapper">
                          <InputNumber v-model="rule.deposit" :min="0.01" :step="1" placeholder="请输入预存款金额"
                            class="uniform-input"
                            :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.ruleList)" />
                          <span class="unit-label">港币</span>
                        </div>
                      </FormItem>
                      </Col>
                      <Col span="10">
                      <FormItem label="返还比例" :prop="'ruleList.' + index + '.rebateRate'" :rules="formRules.rebateRate">
                        <div class="input-wrapper">
                          <InputNumber v-model="rule.rebateRate" :min="1" :max="100" :step="1" placeholder="请输入返还比例"
                            class="uniform-input"
                            :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.ruleList)" />
                          <span class="unit-label">%</span>
                        </div>
                      </FormItem>
                      </Col>
                      <Col span="2" class="action-buttons">
                      <div v-if="campaign.ruleList.length > 1" class="rule-action">
                        <Button type="error" size="small" @click="removeRule(index)" class="delete-button"
                          :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.ruleList)">
                          <Icon type="md-remove" />
                        </Button>
                      </div>
                      </Col>
                    </div>
                    <div class="rule-item">
                      <Col span="20">
                      </Col>
                      <Col span="2" class="action-buttons">
                      <Button type="success" size="small" @click="addRule" class="add-button"
                        :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.ruleList)">
                        <Icon type="md-add" />
                      </Button>
                      </Col>
                    </div>
                  </template>

                  <!-- A2Z模式规则 -->
                  <template v-else>
                    <div v-for="(rule, index) in campaign.ruleList" :key="index" class="rule-item">
                      <Col span="10">
                      <FormItem label="环比增幅" :prop="'ruleList.' + index + '.growthRate'" :rules="formRules.growthRate">
                        <div class="input-wrapper">
                          <InputNumber v-model="rule.growthRate" :min="1" :max="100" :step="1" placeholder="请输入环比增幅"
                            class="uniform-input"
                            :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.ruleList)" />
                          <span class="unit-label">%</span>
                        </div>
                      </FormItem>
                      </Col>
                      <Col span="10">
                      <FormItem label="返还比例" :prop="'ruleList.' + index + '.rebateRate'" :rules="formRules.rebateRate">
                        <div class="input-wrapper">
                          <InputNumber v-model="rule.rebateRate" :min="1" :max="100" :step="1" placeholder="请输入返还比例"
                            class="uniform-input"
                            :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.ruleList)" />
                          <span class="unit-label">%</span>
                        </div>
                      </FormItem>
                      </Col>
                      <Col span="2" class="action-buttons">
                      <div v-if="campaign.ruleList.length > 1" class="rule-action">
                        <Button type="error" size="small" @click="removeRule(index)" class="delete-button"
                          :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.ruleList)">
                          <Icon type="md-remove" />
                        </Button>
                      </div>
                      </Col>
                    </div>
                    <div class="rule-item">
                      <Col span="20">
                      </Col>
                      <Col span="2" class="action-buttons">
                      <Button type="success" size="small" @click="addRule" class="add-button"
                        :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.ruleList)">
                        <Icon type="md-add" />
                      </Button>
                      </Col>
                    </div>
                  </template>
                </Row>
              </div>
            </FormItem>
          </div>
        </div>

        <!-- 代销 立即返还规则部分 -->
        <div class="section-header" v-if="campaign.cooperationMode == 1">立即返还规则</div>
        <div class="section-content" v-if="campaign.cooperationMode == 1">
          <!-- 返还规则部分 -->
          <div class="rules-section">
            <FormItem :label-width="5" class="rules-header">
              <div class="rules-container">
                <Row :gutter="24">
                  <Col span="10">
                  <FormItem label="返还金生效期" prop="effectiveTime" :required="isImmediateReturnSectionActivelyFilled">
                    <div class="input-wrapper">
                    <DatePicker v-model="campaign.effectiveTime" type="date"
                      placement="bottom-start" placeholder="请选择返还金生效期" class="uniform-input"
                      :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.returnImmediatelyList)"
                      :options="effectiveTimeDatePickerOptions" />
                      </div>
                  </FormItem>
                  </Col>
                </Row>
                <Row :gutter="24">
                  <!-- 代销模式规则 -->
                  <template>
                    <div v-for="(rule2, index2) in campaign.returnImmediatelyList" :key="index2" class="rule-item">

                      <Col span="10">
                      <FormItem label="预存款" :prop="'returnImmediatelyList.' + index2 + '.predeposit'"
                        :rules="formRules.predeposit" :label-width="120" >
                        <div class="input-wrapper">
                          <InputNumber v-model="rule2.predeposit" :min="0" :step="1" placeholder="请输入预存款金额"
                            class="uniform-input"
                            :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.returnImmediatelyList)" />
                          <span class="unit-label">港币</span>
                        </div>
                      </FormItem>
                      </Col>
                      <Col span="10">
                      <FormItem label="返还比例" :prop="'returnImmediatelyList.' + index2 + '.distributionReturnRatio'"
                        :rules="formRules.distributionReturnRatio" :label-width="120" >
                        <div class="input-wrapper">
                          <InputNumber v-model="rule2.distributionReturnRatio" :min="0" :max="100" :step="1"
                            placeholder="请输入返还比例" class="uniform-input"
                            :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.returnImmediatelyList)" />
                          <span class="unit-label">%</span>
                        </div>
                      </FormItem>
                      </Col>
                      <Col span="2" class="action-buttons">
                      <div v-if="campaign.returnImmediatelyList.length > 0" class="rule-action">
                        <Button type="error" size="small" @click="removeRule2(index2)" class="delete-button"
                          :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.returnImmediatelyList)">
                          <Icon type="md-remove" />
                        </Button>
                      </div>
                      </Col>
                    </div>
                    <div class="rule-item">
                      <Col span="20">
                      </Col>
                      <Col span="2" class="action-buttons">
                      <Button type="success" size="small" @click="addRule2" class="add-button"
                        :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.returnImmediatelyList)">
                        <Icon type="md-add" />
                      </Button>
                      </Col>
                    </div>
                  </template>
                </Row>
              </div>
            </FormItem>
          </div>
        </div>

        <!-- 公司信息部分 -->
        <div class="section-header">公司信息</div>
        <div class="section-content">
          <div class="company-section">
            <div class="company-header">
              <Button type="primary" @click="editCompany" class="add-company-button"
                :disabled="!isFieldEditable || (isFieldEditable !== true && !isFieldEditable.company)">
                添加公司
              </Button>
            </div>

            <!-- 已选公司表格 -->
            <div class="company-table-container">
              <Table border :columns="selectedCompaniesColumns" :data="finalSelectedCompanies" height="400"></Table>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <!-- 提交后加个loading -->
          <Button type="primary" @click="submitCampaign" :disabled="isAllFieldsDisabled"
            :loading="submitLoading">提交</Button>
          <Button @click="callBackPage" style="margin-left: 16px">返回</Button>
        </div>
      </div>
    </Form>

    <!-- 参与公司弹窗 -->
    <Modal title="参与公司编辑" v-model="modalVisible" @on-ok="handleOk" @on-cancel="handleCancel" width="900"
      :mask-closable="false" @keydown.enter.native.prevent>
      <Form ref="searchForm" :model="searchForm" :label-width="80" style="display: flex;">
        <FormItem label="公司名称">
          <Input v-model="searchForm.name" placeholder="请输入公司名称" clearable class="uniform-input" />
        </FormItem>
        <FormItem :label-width="30">
          <Button style="margin: 0 2px" type="info" @click="searchCompanies(1)" :loading="searchCompaniesLoading"
            icon="ios-search">搜索</Button>
        </FormItem>
      </Form>

      <div style="margin: 0 20px">
        <Checkbox style="margin-bottom: 10px;" border v-model="selectAll" @on-change="handleAllChange"
          :disabled="companiesData.length === 0">全选</Checkbox>
        <Table border ref="selection" :columns="companiesColumns" height="400" :ellipsis="true" :data="companiesData"
          @on-selection-change="handleRowChange" @on-select-cancel="cancelPackage"
          @on-select-all-cancel="cancelPackageAll" :loading="tableLoading">
        </Table>
        <Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator
          @on-change="searchCompanies" style="margin: 15px 0" />
      </div>
    </Modal>
  </Card>
</template>

<script>
import { addCampaign, getRuleDetails, updateCampaign, mktChannelPage, getMktCorp } from '@/api/marketingActivity/index';
import { formatDateTime } from "@/libs/tools";
import { re } from 'mathjs';

export default {
  data () {
    const validateDeposit = (rule, value, callback) => {
      if (!value && value !== 0) {
        return callback(new Error('预存款不能为空'));
      }
      const numValue = parseFloat(value);
      if (numValue <= 0) {
        return callback(new Error('预存款必须大于0'));
      }

      // 获取当前编辑的索引
      const currentIndex = this.campaign.ruleList.findIndex(item => item.deposit === numValue);

      // 检查前一个值
      if (currentIndex > 0 && numValue <= this.campaign.ruleList[currentIndex - 1].deposit) {
        return callback(new Error('预存款需要递增'));
      }

      // 检查后一个值
      if (currentIndex < this.campaign.ruleList.length - 1 && numValue >= this.campaign.ruleList[currentIndex + 1].deposit) {
        return callback(new Error('预存款需要递增'));
      }

      callback();
    };

    const validateRebateRate = (rule, value, callback) => {
      if (!value && value !== 0) {
        return callback(new Error('返还比例不能为空'));
      }
      const numValue = parseFloat(value);
      if (numValue <= 0) {
        return callback(new Error('返还比例必须大于0'));
      }
      if (numValue > 100) {
        return callback(new Error('返还比例不能大于100'));
      }

      // 获取当前编辑的索引
      const currentIndex = this.campaign.ruleList.findIndex(item => item.rebateRate === numValue);

      // 检查前一个值
      if (currentIndex > 0 && numValue <= this.campaign.ruleList[currentIndex - 1].rebateRate) {
        return callback(new Error('返还比例必须递增'));
      }

      // 检查后一个值
      if (currentIndex < this.campaign.ruleList.length - 1 && numValue >= this.campaign.ruleList[currentIndex + 1].rebateRate) {
        return callback(new Error('返还比例必须递增'));
      }

      callback();
    };

    // 新增：立即返还规则的预存款验证（非必填）
    const validatePredepositOptional = (rule, value, callback) => {
      if (value === null || value === undefined || value === '') {
        return callback(); // 非必填，允许为空
      }
      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        return callback(new Error('预存款必须为数字'));
      }
      if (numValue <= 0) {
        return callback(new Error('预存款必须大于0'));
      }
      callback();
    };

    // 新增：立即返还规则的返还比例验证（非必填）
    const validateDistributionReturnRatioOptional = (rule, value, callback) => {
      if (value === null || value === undefined || value === '') {
        return callback(); // 非必填，允许为空
      }
      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        return callback(new Error('返还比例必须为数字'));
      }
      if (numValue < 1 || numValue > 100) {
        return callback(new Error('返还比例必须在1到100之间'));
      }
      callback();
    };

    const validateGrowthRate = (rule, value, callback) => {
      if (!value && value !== 0) {
        return callback(new Error('环比增幅不能为空'));
      }
      const numValue = parseFloat(value);
      if (numValue <= 0) {
        return callback(new Error('环比增幅必须大于0'));
      }
      if (numValue > 100) {
        return callback(new Error('环比增幅不能大于100'));
      }
      const index = this.campaign.ruleList.findIndex(ruleItem => ruleItem.growthRate === numValue);
      if (index > 0 && numValue <= this.campaign.ruleList[index - 1].growthRate) {
        callback(new Error('当前环比增幅必须大于上一级环比增幅'));
      } else {
        callback();
      }
    };

    const validateTotalAmountRefund = (rule, value, callback) => {
      if (!value && value !== 0) {
        return callback(new Error('返还金总额不能为空'));
      }
      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        return callback(new Error('返还金总额必须为数字'));
      }

      // 添加前八后二的校验
      const pattern = /^([1-9]\d{0,7})(\.\d{0,2})?$/;
      if (!pattern.test(value)) {
        return callback(new Error('最高支持8位整数和2位小数的正数'));
      }

      if (numValue <= 0) {
        return callback(new Error('返还金总额必须大于0'));
      }
      callback();
    };

    const validateRestitutionFundsTime = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('返还金有效期不能为空'));
      }

      const endTime = this.campaign.endTime || (this.campaign.activityTimeRange && this.campaign.activityTimeRange[1]);
      if (endTime) {
        const endTimestamp = new Date(endTime).getTime();
        const validTimestamp = new Date(value).getTime();

        if (validTimestamp <= endTimestamp) {
          return callback(new Error('返还金有效期必须晚于活动结束时间'));
        }
      }

      callback();
    };
    const validateEffectiveTime = (rule, value, callback) => {
      // 如果没有值，则通过 (因为是非必填)
      if (value === null || value === undefined || value === '') {
        // 只有当立即返还区域被激活时，effectiveTime才是必填的
        if (this.isImmediateReturnSectionActivelyFilled) {
          return callback(new Error('返还金生效期不能为空'));
        }
        return callback();
      }

      // 格式校验
      const dateValue = new Date(value);
      if (dateValue.toString() === 'Invalid Date') {
        return callback(new Error('返还金生效期格式不正确'));
      }

      // 只有在代销模式下才进行时间范围限制
      if (this.campaign.cooperationMode === '1') {
        dateValue.setHours(0, 0, 0, 0);

        const activityEndTime = this.campaign.activityTimeRange && this.campaign.activityTimeRange[1];
        if (activityEndTime) {
          const activityEndDate = new Date(activityEndTime);
          activityEndDate.setHours(0, 0, 0, 0);

          if (dateValue < activityEndDate) {
            return callback(new Error('活动结束时间—返还金有效期'));
          }
        }
      }
      callback();
    };

    // 新增：立即返还规则的预存款验证
    const validateImmediatePredeposit = (rule, value, callback) => {
      try {
        const index = Number(rule.field && rule.field.split && rule.field.split(".")[1]);
        console.log('【立即返还-预存款校验】index:', index, 'field:', rule.field, 'list:', this.campaign.returnImmediatelyList);
        if (isNaN(index) || !this.campaign.returnImmediatelyList || !this.campaign.returnImmediatelyList[index]) {
          // index异常，直接通过
          return callback();
        }
        const currentItem = this.campaign.returnImmediatelyList[index];
        const hasPredeposit = (value !== null && value !== '');
        const hasReturnRatio = (currentItem.distributionReturnRatio !== null && currentItem.distributionReturnRatio !== '');

        if (hasPredeposit || hasReturnRatio) {
          // 如果预存款和返还比例有一个被填写，则两者都必填
          if (!hasPredeposit) {
            return callback(new Error('预存款不能为空'));
          }
        }

        if (hasPredeposit) {
          const numValue = parseFloat(value);
          if (isNaN(numValue)) {
            return callback(new Error('预存款必须为数字'));
          }
          if (numValue <= 0) {
            return callback(new Error('预存款必须大于0'));
          }
          // 递增校验 for predeposit
          if (index > 0) {
            const prevItem = this.campaign.returnImmediatelyList[index - 1];
            if (prevItem && prevItem.predeposit !== null && prevItem.predeposit !== '' && numValue <= parseFloat(prevItem.predeposit)) {
              return callback(new Error('预存款需要递增'));
            }
          }
          if (index < this.campaign.returnImmediatelyList.length - 1) {
            const nextItem = this.campaign.returnImmediatelyList[index + 1];
            if (nextItem && nextItem.predeposit !== null && nextItem.predeposit !== '' && numValue >= parseFloat(nextItem.predeposit)) {
              return callback(new Error('预存款需要递增'));
            }
          }
        }
        callback();
      } catch (e) {
        console.error('【立即返还-预存款校验异常】', e);
        callback();
      }
    };

    // 新增：立即返还规则的返还比例验证
    const validateImmediateDistributionReturnRatio = (rule, value, callback) => {
      try {
        const index = Number(rule.field && rule.field.split && rule.field.split(".")[1]);
        console.log('【立即返还-返还比例校验】index:', index, 'field:', rule.field, 'list:', this.campaign.returnImmediatelyList);
        if (isNaN(index) || !this.campaign.returnImmediatelyList || !this.campaign.returnImmediatelyList[index]) {
          // index异常，直接通过
          return callback();
        }
        const currentItem = this.campaign.returnImmediatelyList[index];
        const hasPredeposit = (currentItem.predeposit !== null && currentItem.predeposit !== '');
        const hasReturnRatio = (value !== null && value !== '');

        if (hasPredeposit || hasReturnRatio) {
          // 如果预存款和返还比例有一个被填写，则两者都必填
          if (!hasReturnRatio) {
            return callback(new Error('返还比例不能为空'));
          }
        }

        if (hasReturnRatio) {
          const numValue = parseFloat(value);
          if (isNaN(numValue)) {
            return callback(new Error('返还比例必须为数字'));
          }
          if (numValue < 1 || numValue > 100) {
            return callback(new Error('返还比例必须在1到100之间'));
          }
          // 递增校验 for distributionReturnRatio
          if (index > 0) {
            const prevItem = this.campaign.returnImmediatelyList[index - 1];
            if (prevItem && prevItem.distributionReturnRatio !== null && prevItem.distributionReturnRatio !== '' && numValue <= parseFloat(prevItem.distributionReturnRatio)) {
              return callback(new Error('返还比例需要递增'));
            }
          }
          if (index < this.campaign.returnImmediatelyList.length - 1) {
            const nextItem = this.campaign.returnImmediatelyList[index + 1];
            if (nextItem && nextItem.distributionReturnRatio !== null && nextItem.distributionReturnRatio !== '' && numValue >= parseFloat(nextItem.distributionReturnRatio)) {
              return callback(new Error('返还比例需要递增'));
            }
          }
        }
        callback();
      } catch (e) {
        console.error('【立即返还-返还比例校验异常】', e);
        callback();
      }
    };

    return {
      mcId: null, // 活动ID，用于区分新增/修改
      searchForm: {
        name: ''
      },
      campaign: {
        name: '',
        cooperationMode: "1",
        activityTimeRange: [], // 新增：活动时间区间
        sequentialTimeRange: [], // 新增：环比时间区间
        startTime: '',
        endTime: '',
        sequentialStartTime: '',
        sequentialEndTime: '',
        totalAmountRefund: 0,
        restitutionFundsTime: '',
        effectiveTime: '', // 返还金生效期
        campaignStatus: '1',
        ruleList: [
          { returnType: "1", deposit: 1, rebateRate: 1, growthRate: 1 }
        ],
        returnImmediatelyList: [
          { returnType: "2", predeposit: null, distributionReturnRatio: null }
        ], //代销立即返还规则
        selectedCompanies: []
      },
      formRules: {
        name: [
          { required: true, message: '活动名称不能为空', trigger: 'blur' }
        ],
        cooperationMode: [
          { required: true, message: '合作模式不能为空', trigger: 'change' }
        ],
        activityTimeRange: [
          { required: true, type: 'array', message: '活动时间不能为空', trigger: 'change' },
          { validator: this.validateActivityTimeRange, trigger: 'change' }
        ],
        sequentialTimeRange: [
          { required: true, type: 'array', message: '环比时间不能为空', trigger: 'change' },
          { validator: this.validateSequentialTimeRange, trigger: 'change' }
        ],
        startTime: [
          { required: true, type: 'date', message: '开始时间不能为空', trigger: 'change' },
          { validator: this.validateStartTime, trigger: 'change' }
        ],
        endTime: [
          { required: true, type: 'date', message: '结束时间不能为空', trigger: 'change' },
          { validator: this.validateEndTime, trigger: 'change' }
        ],
        sequentialStartTime: [
          { required: true, type: 'date', message: '环比开始时间不能为空', trigger: 'change' },
          { validator: this.validateSequentialStartTime, trigger: 'change' }
        ],
        sequentialEndTime: [
          { required: true, type: 'date', message: '环比结束时间不能为空', trigger: 'change' },
          { validator: this.validateSequentialEndTime, trigger: 'change' }
        ],
        restitutionFundsTime: [
          { required: true, type: 'date', message: '返还金有效期不能为空', trigger: 'change' },
          { validator: validateRestitutionFundsTime, trigger: 'change' }
        ],
        effectiveTime: [
          { validator: validateEffectiveTime, trigger: 'change' }
        ],
        totalAmountRefund: [
          { validator: validateTotalAmountRefund, trigger: 'blur' }
        ],
        deposit: [
          { validator: validateDeposit, trigger: 'blur' }
        ],
        rebateRate: [
          { validator: validateRebateRate, trigger: 'blur' }
        ],
        growthRate: [
          { validator: validateGrowthRate, trigger: 'blur' }
        ],
        predeposit: [ // 立即返还规则的预存款校验
          { validator: validateImmediatePredeposit, trigger: 'blur' }
        ],
        distributionReturnRatio: [ // 立即返还规则的返还比例校验
          { validator: validateImmediateDistributionReturnRatio, trigger: 'blur' }
        ]
      },
      modalVisible: false,
      companiesData: [],
      selectedCompaniesColumns: [
        {
          title: '公司名称',
          key: 'companyName',
          align: 'center',
          tooltip: true,
        },
        {
          title: '渠道商简称',
          key: 'corpName',
          align: 'center',
          tooltip: true,
        },
        {
          title: '操作',
          width: 100,
          align: 'center',
          render: (h, { row, index }) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'error',
                  size: 'small',
                  icon: 'md-close',
                  disabled: (row.isInitial && this.originalStatus !== '0') || !this.isFieldEditable || (this.isFieldEditable !== true && !this.isFieldEditable.company)
                },
                on: {
                  click: () => this.removePackage(index)
                }
              })
            ]);
          }
        }
      ],
      companiesColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: "公司名称",
          key: "companyName",
          minWidth: 150,
          align: "center",
          tooltip: true
        },
        {
          title: "渠道商简称",
          key: "corpName",
          minWidth: 150,
          align: "center",
          tooltip: true
        }
      ],
      selectedCompanies: [],
      searchCompaniesLoading: false,
      spinShow: false,
      selectAll: false,
      currentPageSelected: false,
      page: 1,
      pageSize: 10,
      total: 0,
      totalCount: 0,
      tableLoading: false,
      filteredData: [], // 新增：存储过滤后的完整数据
      // 活动状态选项
      activityStatusOptions: [
        { value: '0', label: '待开始' },
        { value: '1', label: '已开始' },
        { value: '2', label: '已结束' },
        { value: '3', label: '已作废' },
        { value: '4', label: '提前结束' },
        { value: '5', label: '重新结算中' }
      ],
      // 原始活动状态（用于判断状态变更）
      originalStatus: '',
      originalEndTime: null, // 添加原始结束时间
      datePickerOptions: {
        // disabledDate: (date) => {
        //   // 如果是已开始状态
        //   if (this.originalStatus === '1') {
        //     // 使用originalEndTime来判断
        //     if (this.originalEndTime) {
        //       return date < new Date(this.originalEndTime);
        //     }
        //   }
        //   //若是新建，开始时间要比今天大
        //   if(!this.mcId){
        //     return date<new Date()
        //   }
        //   return false;
        // }
      },
      submitLoading: false,
      initialSelectedCompanies: [], // 新增：存储初始已选中的公司
      finalSelectedCompanies: [], // 新增：最终需要提交的公司
    };
  },
  computed: {
    // 为返还金生效期日期选择器生成选项
    effectiveTimeDatePickerOptions () {
      return {
        disabledDate: (date) => {
          const activityEndTime = this.campaign.activityTimeRange && this.campaign.activityTimeRange[1];
          const restitutionFundsTime = this.campaign.restitutionFundsTime;
          let minDate = null, maxDate = null;
          if (activityEndTime) {
            minDate = new Date(activityEndTime);
            minDate.setDate(minDate.getDate() + 1);
            minDate.setHours(0, 0, 0, 0);
          }
          if (restitutionFundsTime) {
            maxDate = new Date(restitutionFundsTime);
            maxDate.setHours(0, 0, 0, 0);
          }
          // 只能选 >= 活动结束日期 && <= 返还金有效期
          if (minDate && maxDate) {
            return date < minDate || date > maxDate;
          }
          if (minDate) {
            return date < minDate;
          }
          if (maxDate) {
            return date > maxDate;
          }
          return false;
        }
      };
    },
    // 判断字段是否可编辑（只针对原始数据，后续改变活动状态时，不影响原有状态）
    isFieldEditable () {
      // 状态说明：活动状态: 0-待开始/1-已开始/2-已结束/3-已作废/4-提前结束/5-重新结算中
      const status = this.originalStatus;

      // 新增时全部可编辑
      if (!this.mcId) return true;

      // 根据不同状态返回可编辑字段
      switch (status) {
        case '0': // 待开始：全部允许修改
          return true;

        case '1': // 已开始：仅允许修改状态为已作废、提前结束，允许延长结束时间和修改总返还金额
          //已开始状态下，代销模式下可以修改参与公司
          return {
            campaignStatus: true, // 可以修改状态
            endTime: true, // 可以延长结束时间
            activityTimeRange: true, // 允许修改活动时间范围（但只能延长结束时间）
            totalAmountRefund: this.campaign.cooperationMode === '1', // 代销模式下可以修改返还金总额
            name: false,
            cooperationMode: false,
            startTime: false,
            sequentialTimeRange: false,
            sequentialStartTime: false,
            sequentialEndTime: false,
            restitutionFundsTime: false,
            ruleList: false, // 累计返还规则在已开始状态下不可编辑
            returnImmediatelyList: false, // 立即返还规则在已开始状态下不可编辑
            company: this.campaign.cooperationMode === '1' ? true : false
          };
        case '2': // 已结束：不允许修改
        case '3': // 已作废：不允许修改
        case '4': // 提前结束：不允许修改
        case '5': // 重新结算中：不允许修改
          return {
            name: false,
            cooperationMode: false,
            activityTimeRange: false,
            sequentialTimeRange: false,
            startTime: false,
            endTime: false,
            sequentialStartTime: false,
            sequentialEndTime: false,
            totalAmountRefund: false,
            restitutionFundsTime: false,
            campaignStatus: false,
            ruleList: false,
            returnImmediatelyList: false, // 立即返还规则在已结束、作废等状态下不可编辑
            company: false
          };

        default:
          return false;
      }
    },

    // 获取可选的状态选项
    availableStatusOptions () {
      if (!this.mcId) return this.activityStatusOptions; // 新增时显示所有选项

      const currentStatus = this.originalStatus;

      // 已开始状态
      if (currentStatus === '1') {
        // A2Z模式下，活动状态只能改到已作废
        if (this.campaign.cooperationMode === '2') {
          return this.activityStatusOptions.filter(option =>
            ['1', '3'].includes(option.value)
          );
        } else {
          // 代销模式下，活动状态可以改到已作废或提前结束
          return this.activityStatusOptions.filter(option =>
            ['1', '3', '4'].includes(option.value)
          );
        }
      }

      // 待开始状态
      if (currentStatus === '0') {
        return this.activityStatusOptions.filter(option =>
          ['0', '3'].includes(option.value)
        );
      }

      // 其他状态只显示当前状态
      return this.activityStatusOptions.filter(option =>
        option.value === currentStatus
      );
    },

    // 判断是否所有字段都不可编辑
    isAllFieldsDisabled () {
      if (!this.isFieldEditable) return true;
      if (typeof this.isFieldEditable === 'boolean') return !this.isFieldEditable;
      return Object.values(this.isFieldEditable).every(value => !value);
    },
    // 判断立即返还规则部分是否活跃（用于星标显示）
    isImmediateReturnSectionActivelyFilled() {
      if (this.campaign.cooperationMode !== '1') {
        return false;
      }
      return !!this.campaign.effectiveTime ||
             this.campaign.returnImmediatelyList.some(item =>
               (item.predeposit !== null && item.predeposit !== '') ||
               (item.distributionReturnRatio !== null && item.distributionReturnRatio !== '')
             );
    }
  },
  methods: {
    /**
     * 获取参与公司列表数据
     * @param {number} page - 页码
     * @returns {Promise<void>}
     */
    async searchCompanies (page) {
      try {
        if (!Array.isArray(this.companiesData)) {
          this.companiesData = [];
        }

        if (page === 1) {
          this.page = 1;
        }

        this.searchCompaniesLoading = true;
        this.tableLoading = true;

        const queryParams = {
          companyName: this.searchForm.name.trim() || '',
          pageNum: this.page,
          pageSize: this.pageSize,
          cooperationMode: this.campaign.cooperationMode
        };

        const companyListRes = await mktChannelPage(queryParams);

        if (companyListRes?.code !== '0000' || !companyListRes?.data) {
          throw new Error(companyListRes?.message || '获取公司列表失败');
        }

        const records = Array.isArray(companyListRes.data) ? companyListRes.data : [];

        // 处理数据，为初始已选中的公司添加 _disabled 标记
        this.companiesData = records.map(item => ({
          ...item,
          _disabled: this.initialSelectedCompanies.some(initial => initial.corpId === item.corpId)&&this.originalStatus!=='0'
        }));

        this.total = parseInt(companyListRes.count || 0);

        if (this.page === 1 && !this.searchForm.name) {
          this.totalCount = this.total;
        }

        await this.$nextTick();
        this.updatePageData(true);

      } catch (error) {
        const errorMsg = error.message || '获取公司列表失败';
        this.$Message.error(errorMsg);
        console.error('获取公司列表错误:', error);
        this.companiesData = [];
        this.total = 0;
        this.totalCount = 0;
      } finally {
        this.searchCompaniesLoading = false;
        this.tableLoading = false;
      }
    },

    /**
     * 获取已选择的公司列表
     * @returns {Promise<void>}
     */
    async getSelectedCompanies () {
      if (!this.mcId) return;

      try {
        const selectedCorpsRes = await getMktCorp({ mcId: this.mcId });

        if (selectedCorpsRes?.code !== '0000' || !selectedCorpsRes?.data) {
          throw new Error(selectedCorpsRes?.message || '获取已选公司失败');
        }

        // 确保返回的数据是数组
        const companies = Array.isArray(selectedCorpsRes.data) ?
          selectedCorpsRes.data.map(item => ({
            ...item,
            corpId: item.corpId || item.id,
            companyName: item.companyName || item.nameCn,
            isInitial: true // 标记为初始已选中的公司
          })) : [];

        // 分别存储初始已选公司和当前选中的公司
        this.initialSelectedCompanies = [...companies];
        this.selectedCompanies = [...companies];
        this.finalSelectedCompanies = [...companies];

      } catch (error) {
        console.error('获取已选公司失败:', error);
        this.$Message.error('获取已选公司失败');
        this.selectedCompanies = [];
        this.initialSelectedCompanies = [];
        this.finalSelectedCompanies = [];
      }
    },

    // 初始化方法
    async init () {
      // 从路由参数获取活动ID
      this.mcId = this.$route.query.mcId;
      if (this.mcId) {
        await this.getActivityDetail(this.mcId);
        await this.getSelectedCompanies(); // 获取已选公司列表
      }
      // await this.searchCompanies(1);
    },

    updatePageData (isFromSearch = false) {
      this.$nextTick(() => {
        if (this.$refs.selection) {
          const selectionRef = this.$refs.selection;

          // 移除选择change事件监听器
          if (isFromSearch) {
            const oldVm = selectionRef.$children[0];
            const oldHandlers = oldVm.$listeners['on-selection-change'];
            oldVm.$off('on-selection-change');
          }

          // 更新当前页的选中状态
          this.companiesData.forEach((item, index) => {
            if (this.selectedCompanies.some(selected => selected.corpId === item.corpId)) {
              selectionRef.toggleSelect(index);
            }
          });

          // 检查当前页是否全选
          const isCurrentPageAllSelected = this.companiesData.every(item =>
            this.selectedCompanies.some(selected => selected.corpId === item.corpId)
          );


          // 更新表头的选择框状态
          if (isCurrentPageAllSelected && this.total > 0) {
            selectionRef.selectAll(true);
          }

          // 如果是来自搜索，重新绑定事件监听器
          if (isFromSearch) {
            const oldVm = selectionRef.$children[0];
            oldVm.$on('on-selection-change', this.handleRowChange);
          }
          // 更新全选状态:列表数据为分页数据，列表搜索时，全选状态需根据总数据量来判断

          this.selectAll = this.selectedCompanies.length === this.totalCount;

        }
      });
    },

    handleAllChange (status) {
      this.selectAll = status; // 立即更新全选状态

      if (status) {
        this.searchCompaniesLoading = true;
        //请求接口获取所有公司数据
        mktChannelPage({
          companyName: this.searchForm.name,
          pageNum: -1,
          pageSize: -1,
          cooperationMode: this.campaign.cooperationMode
        }).then(res => {
          if (res.code == '0000') {
            // 创建一个Map来存储搜索出来的公司数据
            let searchMap = new Map(
              res.data.map(item => [item.corpId, item])
            );
            // 将初始选择的公司数据从searchMap中移除
            this.initialSelectedCompanies.forEach(item => {
              searchMap.delete(item.corpId);
            });
            //将之前临时选中的数据从searchMap中移除
            this.selectedCompanies.forEach(item => {
              searchMap.delete(item.corpId);
            });

            // 使用Map处理合并逻辑，确保数据唯一性
            const resultMap = new Map();

            // 先添加初始选中的公司
            this.initialSelectedCompanies.forEach(item => {
              resultMap.set(item.corpId, item);
            });

            // 添加之前临时选中的公司
            this.selectedCompanies.forEach(item => {
              resultMap.set(item.corpId, item);
            });

            // 添加其余搜索出来的公司
            Array.from(searchMap.values()).forEach(item => {
              resultMap.set(item.corpId, item);
            });

            // 更新选中的公司列表
            this.selectedCompanies = Array.from(resultMap.values());

            // 更新当前页的选中状态
            this.$nextTick(() => {
              if (this.$refs.selection) {
                this.$refs.selection.selectAll(true);
              }
            });
          }
        }).catch(error => {
          this.$Message.error('获取公司列表失败');
        }).finally(() => {
          this.searchCompaniesLoading = false;
        });
      } else {
        // 取消全选时，保留初始选中的公司
        this.selectedCompanies = this.initialSelectedCompanies;
        this.$nextTick(() => {
          if (this.$refs.selection) {
            this.$refs.selection.selectAll(false);
          }
        });
      }
    },

    handleRowChange (selection) {
      console.log(selection, "选中列表");
      //将选中的数据列表保存到selectedCompanies中，需要根据已有数据来判断是否存在，存在则不添加，不存在则添加
      selection.forEach(item => {
        if (!this.selectedCompanies.some(selected => selected.corpId === item.corpId)) {
          // 检查是否是初始选中的公司
          const isInitial = this.initialSelectedCompanies.some(initial => initial.corpId === item.corpId);
          this.selectedCompanies.push({ ...item, isInitial });
        }
      });
      this.selectAll = this.selectedCompanies.length === this.total;
    },
    removePackage (index) {
      const removedCompany = this.selectedCompanies[index];
      if (!removedCompany) return;

      // 如果是初始已选中的公司，不允许删除
      if (removedCompany.isInitial&&this.originalStatus!=='0') {
        this.$Message.warning('初始已选中的公司不能删除');
        return;
      }

      this.selectedCompanies.splice(index, 1);
      this.finalSelectedCompanies.splice(index, 1);

      // 更新表格中的选中状态
      const tableRowIndex = this.companiesData.findIndex(item => item.corpId === removedCompany.corpId);
      if (tableRowIndex > -1) {
        this.$nextTick(() => {
            this.$refs.selection.toggleSelect(tableRowIndex);
        });
      }

      // 更新全局全选状态
      this.selectAll = this.selectedCompanies.length === this.totalCount;
    },
    cancelPackage (selection, row) {
      const index = this.selectedCompanies.findIndex(item => item.corpId === row.corpId);
      if (index > -1) {
        this.selectedCompanies.splice(index, 1);
      }
      // 更新全选状态
      this.selectAll = this.selectedCompanies.length === this.totalCount;
    },
    cancelPackageAll () {
      // 获取当前页的公司ID列表
      const currentPageCorpIds = this.companiesData.map(item => item.corpId);

      // 从已选列表中移除当前页的所有公司
      this.selectedCompanies = this.selectedCompanies.filter(
        item => !currentPageCorpIds.includes(item.corpId)
      );

      // 更新全选状态
      this.selectAll = this.selectedCompanies.length === this.totalCount;
    },
    addRule () {
      const lastRule = this.campaign.ruleList[this.campaign.ruleList.length - 1];
      const newDeposit = lastRule.deposit + 1;
      let newRebateRate = 0;
      let newGrowthRate = 0;
      if (this.campaign.cooperationMode == 1) {
        newRebateRate = Math.min(lastRule.rebateRate + 1, 100);
        this.campaign.ruleList.push({ deposit: newDeposit, rebateRate: newRebateRate });
      } else {
        newGrowthRate = Math.min(lastRule.growthRate + 1, 100);
        newRebateRate = Math.min(lastRule.rebateRate + 1, 100);
        this.campaign.ruleList.push({ rebateRate: newRebateRate, growthRate: newGrowthRate });
      }
    },
    removeRule (index) {
      if (this.campaign.ruleList.length > 1) {
        this.campaign.ruleList.splice(index, 1);
      } else {
        this.$Message.warning('至少需要保留一条返还规则');
      }
    },
    addRule2 () {
      // 动态添加立即返还规则，预存款和返还比例都不是必填，不需要默认值
      this.campaign.returnImmediatelyList.push({ returnType: "2", predeposit: null, distributionReturnRatio: null });
    },
    removeRule2 (index) {
      // 允许删除所有立即返还规则
      this.campaign.returnImmediatelyList.splice(index, 1);
    },
    editCompany () {
      this.modalVisible = true;
      // 使用主表单中保存的选择状态
      this.selectedCompanies = [...(this.finalSelectedCompanies || [])];
      // 确保初始已选公司列表也被正确恢复
      this.initialSelectedCompanies = [...(this.initialSelectedCompanies || [])];
      this.page = 1;
      this.searchForm.name = '';
      this.searchCompanies(1);
    },
    handleOk () {
      // 合并初始已选公司和新增的公司
      this.campaign.selectedCompanies = [...this.selectedCompanies];
      this.finalSelectedCompanies = [...this.selectedCompanies];
      this.modalVisible = false;
      this.page = 1;
      this.searchForm.name = '';
    },
    handleCancel () {
      this.modalVisible = false;
      this.page = 1;
      this.searchForm.name = '';
    },
    submitCampaign () {
      this.$refs.campaignForm.validate(async (valid) => {
        if (valid && this.validateStatusChange()) {
          // 新增：代销模式下立即返还规则校验
          if (this.campaign.cooperationMode === '1' && this.campaign.effectiveTime) {
            const hasValidImmediateRule = this.campaign.returnImmediatelyList.some(item =>
              (item.predeposit !== null && item.predeposit !== '') &&
              (item.distributionReturnRatio !== null && item.distributionReturnRatio !== '')
            );

            if (!hasValidImmediateRule) {
              this.submitLoading = false;
              this.$Message['warning']({
                background: true,
                content: '您已填写返还金生效期，请至少填写一组立即返还规则的预存款和返还比例信息',
                duration: 5
              });
              return false; // 阻止表单提交
            }
          }

          // 移除了额外的立即返还规则的手动校验，因为现在由新的 validateEffectiveTime, validateImmediatePredeposit, validateImmediateDistributionReturnRatio 负责实时校验。

          try {
            //增加点击后的loading
            this.submitLoading = true;
            const formData = this.formatSubmitData();
            const api = this.mcId ? updateCampaign : addCampaign;
            const res = await api(formData);
            this.submitLoading = false;
            if (res.code == '0000') {
              this.$Message.success(this.mcId ? '修改成功' : '新增成功');
              this.$router.push({
                name: 'marketingActivityIndex',
              });
            } else {
              // this.$Message.error(res.message || (this.mcId ? '修改失败' : '新增失败'));
            }
          } catch (error) {
            this.submitLoading = false;
            this.$Message.error(this.mcId ? '修改失败' : '新增失败');
          }
        } else {
          this.submitLoading = false;
          this.$Message.error('表单验证失败，请检查所有字段');
          return false;
        }
      });
    },
    resetForm () {
      this.$refs.campaignForm.resetFields();
      if (this.campaign.cooperationMode == 1) {
        this.campaign.ruleList = [{ deposit: 1, rebateRate: 1 }];
      } else {
        this.campaign.ruleList = [{ deposit: 1, growthRate: 1 }];
      }
    },
    callBackPage () {
      this.$router.push({
        name: 'marketingActivityIndex',
      });
    },
    // 获取活动详情
    async getActivityDetail (mcId) {
      try {
        const res = await getRuleDetails({ mcId });
        if (res.code === '0000') {
          const detail = res.data;
          this.campaign.name = detail.campaignName;
          this.campaign.cooperationMode = detail.cooperationMode;

          // 设置活动时间区间
          this.campaign.activityTimeRange = [
            new Date(detail.startTime),
            new Date(detail.endTime)
          ];
          this.campaign.startTime = new Date(detail.startTime);
          this.campaign.endTime = new Date(detail.endTime);

          // 保存原始结束时间
          this.originalEndTime = new Date(detail.endTime);

          if (detail.cooperationMode === '2') {
            // 设置环比时间区间
            this.campaign.sequentialTimeRange = [
              new Date(detail.sequentialStartTime),
              new Date(detail.sequentialEndTime)
            ];
            this.campaign.sequentialStartTime = new Date(detail.sequentialStartTime);
            this.campaign.sequentialEndTime = new Date(detail.sequentialEndTime);
          }

          this.campaign.restitutionFundsTime = new Date(detail.restitutionFundsTime);
          // 将字符串转换为数字
          this.campaign.totalAmountRefund = parseFloat(detail.totalAmountRefund) || 0;
          this.campaign.effectiveTime = detail.effectiveTime ? new Date(detail.effectiveTime) : ''; // 获取返还金生效期

          // 处理规则数据，区分累计返还和立即返还
          this.campaign.ruleList = [];
          this.campaign.returnImmediatelyList = [];

          if (detail.campaignRuleList && detail.campaignRuleList.length > 0) {
            // 用于只获取一次立即返还的 effectiveTime
            let effectiveTimeSet = false;

            detail.campaignRuleList.forEach(rule => {
              if (rule.returnType === '1' || rule.returnType === null) { // 累计返还
                const newRule = {
                  rebateRate: parseFloat(rule.returnRatio) || 0,
                  id: rule.id,
                  returnType: "1"
                };
                if (detail.cooperationMode === '1') { // 代销模式
                  newRule.deposit = parseFloat(rule.amountDeposited) || 0;
                } else if (detail.cooperationMode === '2') { // A2Z模式
                  newRule.growthRate = parseFloat(rule.sequentialGrowthRate) || 0;
                }
                this.campaign.ruleList.push(newRule);
              } else if (rule.returnType === '2') { // 立即返还
                this.campaign.returnImmediatelyList.push({
                  predeposit: rule.amountDeposited !== null ? parseFloat(rule.amountDeposited) : null, // 从 amountDeposited 获取
                  distributionReturnRatio: rule.returnRatio !== null ? parseFloat(rule.returnRatio) : null, // 从 returnRatio 获取
                  id: rule.id,
                  returnType: "2"
                });
                // 只需要从第一个立即返还规则中获取 effectiveTime
                if (!effectiveTimeSet && rule.effectiveTime) {
                  this.campaign.effectiveTime = new Date(rule.effectiveTime);
                  effectiveTimeSet = true;
                }
              }
            });
          }
          // 如果加载后立即返还列表为空，则初始化一个空项，方便用户添加
          if (this.campaign.cooperationMode === '1' && this.campaign.returnImmediatelyList.length === 0) {
            this.campaign.returnImmediatelyList.push({ returnType: "2", predeposit: null, distributionReturnRatio: null });
          }
          // 如果加载后累计返还列表为空，则初始化一个空项，方便用户添加
          if (this.campaign.cooperationMode === '1' && this.campaign.ruleList.length === 0) {
            this.campaign.ruleList.push({ returnType: "1", deposit: 1, rebateRate: 1 });
          }
          // 如果加载后累计返还列表为空，则初始化一个空项，方便用户添加（A2Z）
          if (this.campaign.cooperationMode === '2' && this.campaign.ruleList.length === 0) {
            this.campaign.ruleList.push({ returnType: "1", growthRate: 1, rebateRate: 1 });
          }


          // 处理公司数据
          this.campaign.selectedCompanies = detail.company ? detail.company.map(corp => ({
            id: corp.corpId,
            nameCn: corp.companyName
          })) : [];

          this.campaign.campaignStatus = detail.campaignStatus;
          this.originalStatus = detail.campaignStatus; // 保存原始状态
        }
      } catch (error) {
        this.$Message.error('获取活动详情失败');
      }
    },

    // 格式化提交数据
    formatSubmitData () {
      // 初始化 ruleList 数组
      const formattedRuleList = [];

      // 1. 处理累计返还规则 (returnType: 1)
      this.campaign.ruleList.forEach(rule => {
        formattedRuleList.push({
          returnType: "1", // 累计返还类型
          amountDeposited: this.campaign.cooperationMode === '1' ? parseFloat(rule.deposit) : undefined,
          sequentialGrowthRate: this.campaign.cooperationMode === '2' ? parseFloat(rule.growthRate) : undefined,
          returnRatio: parseFloat(rule.rebateRate),
          id: rule.id,
        });
      });

      // 2. 处理立即返还规则 (returnType: 2)
      if (this.campaign.cooperationMode === '1' && this.campaign.returnImmediatelyList && this.campaign.returnImmediatelyList.length > 0) {
        this.campaign.returnImmediatelyList.forEach(rule2 => {
          // 只有当预存款或返还比例有值时才添加到提交数据中
          if ((rule2.predeposit !== null && rule2.predeposit !== '') || (rule2.distributionReturnRatio !== null && rule2.distributionReturnRatio !== '')) {
            formattedRuleList.push({
              returnType: "2", // 立即返还类型
              amountDeposited: parseFloat(rule2.predeposit) || undefined, // 从 predeposit 赋值
              returnRatio: parseFloat(rule2.distributionReturnRatio) || undefined, // 从 distributionReturnRatio 赋值
              effectiveTime: this.campaign.effectiveTime ? formatDateTime(this.campaign.effectiveTime, false) : undefined, // 添加返还金生效期，格式为 00:00:00
              id: rule2.id // 如果是修改，可能有id
            });
          }
        });
      }

      const formData = {
        campaignName: this.campaign.name.trim(),
        cooperationMode: this.campaign.cooperationMode,
        startTime: formatDateTime(this.campaign.startTime),
        endTime: formatDateTime(this.campaign.endTime, true),
        restitutionFundsTime: formatDateTime(this.campaign.restitutionFundsTime, true),
        companyList: this.selectedCompanies,
        ruleList: formattedRuleList, // 合并后的规则列表
        campaignStatus: this.campaign.campaignStatus
      };

      if (this.campaign.cooperationMode === '2') {
        formData.sequentialStartTime = formatDateTime(this.campaign.sequentialStartTime);
        formData.sequentialEndTime = formatDateTime(this.campaign.sequentialEndTime, true);
      }

      if (this.campaign.cooperationMode === '1') {
        formData.totalAmountRefund = parseFloat(this.campaign.totalAmountRefund);
      }

      if (this.mcId) {
        formData.id = this.mcId;
      }

      return formData;
    },
    handleCooperationModeChange () {

      //模式切换后,将form表单初始化
      this.campaign.ruleList = [
        // 确保初始化时，累计返还规则有默认值
        this.campaign.cooperationMode === '1'
          ? { returnType: "1", deposit: 1, rebateRate: 1 }
          : { returnType: "1", growthRate: 1, rebateRate: 1 }
      ];
      // 立即返还规则也要初始化
      this.campaign.returnImmediatelyList = [
        { returnType: "2", predeposit: null, distributionReturnRatio: null }
      ];
      this.campaign.effectiveTime = ''; // 清空返还金生效期
    },
    // 日期格式化
    formatDate (date) {
      if (!date) return '';
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} 00:00:00`;
    },
    // 表单提交前的验证
    validateStatusChange () {
      const { campaignStatus, endTime } = this.campaign;

      // 如果是已开始状态延长结束时间
      if (this.originalStatus === '1') {
        const newEndTime = new Date(endTime);
        const originalEndTime = new Date(this.campaign.activityTimeRange[1]);
        if (newEndTime < originalEndTime) {
          this.$Message.error('已开始状态只能延长结束时间，不能缩短');
          return false;
        }
      }

      return true;
    },

    // 验证环比开始时间
    validateSequentialStartTime (rule, value, callback) {
      if (!value) {
        return callback();
      }
      if (this.campaign.cooperationMode === '2') {
        if (this.campaign.sequentialEndTime && value >= this.campaign.sequentialEndTime) {
          return callback(new Error('环比开始时间必须早于环比结束时间'));
        }
      }
      callback();
    },

    // 验证环比结束时间
    validateSequentialEndTime (rule, value, callback) {
      if (!value) {
        return callback();
      }
      if (this.campaign.cooperationMode === '2') {
        if (this.campaign.sequentialStartTime && value <= this.campaign.sequentialStartTime) {
          return callback(new Error('环比结束时间必须晚于环比开始时间'));
        }
        if (this.campaign.startTime && value >= this.campaign.startTime) {
          return callback(new Error('环比结束时间必须早于活动开始时间'));
        }
      }
      callback();
    },

    // 验证活动开始时间
    validateStartTime (rule, value, callback) {
      if (!value) {
        return callback();
      }

      if (this.campaign.endTime && value >= this.campaign.endTime) {
        return callback(new Error('活动开始时间必须早于结束时间'));
      }

      if (this.campaign.cooperationMode === '2') {
        if (this.campaign.sequentialEndTime && value <= this.campaign.sequentialEndTime) {
          return callback(new Error('活动开始时间必须晚于环比结束时间'));
        }
      }
      callback();
    },

    // 验证活动结束时间
    validateEndTime (rule, value, callback) {
      if (!value) {
        return callback();
      }

      if (this.campaign.startTime && value <= this.campaign.startTime) {
        return callback(new Error('活动结束时间必须晚于开始时间'));
      }

      if (this.campaign.cooperationMode === '2') {
        if (this.campaign.sequentialEndTime && value <= this.campaign.sequentialEndTime) {
          return callback(new Error('活动结束时间必须晚于环比结束时间'));
        }
      }
      callback();
    },

    // 处理活动时间变化
    handleActivityTimeChange (dates) {
      if (dates && dates.length === 2) {
        const newStartTime = dates[0];
        const newEndTime = dates[1];

        // 同步更新 activityTimeRange, startTime, endTime
        if (this.originalStatus === '1') {
          // 如果是已开始状态，只允许延长结束时间，开始时间不变
          const originalStartTime = this.campaign.startTime;
          this.campaign.startTime = originalStartTime;
          this.campaign.endTime = newEndTime;
          this.campaign.activityTimeRange = [originalStartTime, newEndTime];
        } else {
          this.campaign.startTime = newStartTime;
          this.campaign.endTime = newEndTime;
          this.campaign.activityTimeRange = [newStartTime, newEndTime];
        }

        // 立即检查返还金生效期的有效性，并触发校验
        if (this.campaign.effectiveTime) {
          const effectiveTimeDate = new Date(this.campaign.effectiveTime);
          const today = new Date();
          today.setHours(0, 0, 0, 0); // Reset to start of day for accurate comparison
          effectiveTimeDate.setHours(0, 0, 0, 0);

          const activityEndTime = this.campaign.activityTimeRange && this.campaign.activityTimeRange[1]; // Use the just updated activityTimeRange
          const activityEndDate = new Date(activityEndTime);
          activityEndDate.setHours(0, 0, 0, 0);

          let isEffectiveTimeValid = true;
          if (effectiveTimeDate < today || effectiveTimeDate > activityEndDate) {
            isEffectiveTimeValid = false;
          }

          this.$nextTick(() => {
            // 清除之前的验证状态，然后重新触发验证
            this.$refs.campaignForm.validateField('effectiveTime');
            this.$refs.campaignForm.validateField('restitutionFundsTime');
          });
        }
      } else {
        // 如果活动起止时间被清空，也清空返还金生效期
        this.campaign.startTime = '';
        this.campaign.endTime = '';
        this.campaign.activityTimeRange = [];
        this.campaign.effectiveTime = null;
        this.$nextTick(() => {
            // this.$refs.campaignForm.clearValidate('effectiveTime'); // 移除此行
        });
      }
    },

    // 处理环比时间变化
    handleSequentialTimeChange (dates) {
      if (dates && dates.length === 2) {
        this.campaign.sequentialStartTime = dates[0];
        this.campaign.sequentialEndTime = dates[1];
      } else {
        this.campaign.sequentialStartTime = '';
        this.campaign.sequentialEndTime = '';
      }
    },

    // 验证活动时间范围
    validateActivityTimeRange (rule, value, callback) {
      if (!value || value.length !== 2) {
        return callback(new Error('请选择完整的活动时间范围'));
      }

      const [startTime, endTime] = value;
      const startTimestamp = new Date(startTime).getTime();
      const endTimestamp = new Date(endTime).getTime();

      if (startTimestamp >= endTimestamp) {
        return callback(new Error('活动开始时间必须早于结束时间'));
      }

      if (this.campaign.cooperationMode === '2' && this.campaign.sequentialTimeRange?.length === 2) {
        const sequentialEndTime = this.campaign.sequentialTimeRange[1];
        const sequentialEndTimestamp = new Date(sequentialEndTime).getTime();

        // 将时间调整到当天的开始（00:00:00）
        const startDate = new Date(startTime);
        const sequentialEndDate = new Date(sequentialEndTime);
        const startDayTimestamp = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate()).getTime();
        const sequentialEndDayTimestamp = new Date(sequentialEndDate.getFullYear(), sequentialEndDate.getMonth(), sequentialEndDate.getDate()).getTime();

        if (startDayTimestamp <= sequentialEndDayTimestamp) {
          return callback(new Error('活动时间必须晚于环比结束时间'));
        }
      }

      callback();
    },

    // 验证环比时间范围
    validateSequentialTimeRange (rule, value, callback) {
      if (!value || value.length !== 2) {
        return callback(new Error('请选择完整的环比时间范围'));
      }

      const [startTime, endTime] = value;
      const startTimestamp = new Date(startTime).getTime();
      const endTimestamp = new Date(endTime).getTime();

      if (startTimestamp >= endTimestamp) {
        return callback(new Error('环比开始时间必须早于环比结束时间'));
      }

      if (this.campaign.activityTimeRange?.length === 2) {
        const activityStartTime = this.campaign.activityTimeRange[0];

        // 将时间调整到当天的开始（00:00:00）
        const endDate = new Date(endTime);
        const activityStartDate = new Date(activityStartTime);
        const endDayTimestamp = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate()).getTime();
        const activityStartDayTimestamp = new Date(activityStartDate.getFullYear(), activityStartDate.getMonth(), activityStartDate.getDate()).getTime();

        if (endDayTimestamp >= activityStartDayTimestamp) {
          return callback(new Error('环比时间必须早于活动开始时间'));
        }
      }

      callback();
    },

  }, // 组件创建时加载数据
  mounted () {
    this.init();
  },
  // 监听ruleDetails
  watch: {
    ruleDetails (newVal) {
      console.log(newVal);
      this.loading = true;
      if (newVal && newVal.campaignRuleList) {
        this.cumulativeRules = newVal.campaignRuleList.filter(
          (rule) => rule.returnType === null || rule.returnType === '1'
        );
        this.immediateRules = newVal.campaignRuleList.filter(
          (rule) => rule.returnType === '2'
        );
      }
    },
    // 新增：监听返还金生效期变化，清理和重新验证立即返还规则的校验状态
    'campaign.effectiveTime': {
      handler(newVal, oldVal) {
        if (this.campaign.cooperationMode === '1' && this.$refs.campaignForm) {
          this.campaign.returnImmediatelyList.forEach((item, index) => {
            const predepositProp = `returnImmediatelyList.${index}.predeposit`;
            const distributionReturnRatioProp = `returnImmediatelyList.${index}.distributionReturnRatio`;

            if (newVal && ((item.predeposit !== null && item.predeposit !== '') || (item.distributionReturnRatio !== null && item.distributionReturnRatio !== ''))) {
              this.$refs.campaignForm.validateField(predepositProp);
              this.$refs.campaignForm.validateField(distributionReturnRatioProp);
            } else if (!newVal && oldVal) { // 如果effectiveTime从有值变为无值，强制清空相关字段的校验
                this.$refs.campaignForm.validateField(predepositProp); // 再次触发校验，使其显示必填错误
                this.$refs.campaignForm.validateField(distributionReturnRatioProp); // 再次触发校验，使其显示必填错误
            }
          });
        }
      },
      deep: true // 深度监听对象内部属性的变化
    },
    // 新增：监听isImmediateReturnSectionActivelyFilled的变化，以触发effectiveTime的校验
    isImmediateReturnSectionActivelyFilled: {
      handler(newVal) {
        if (this.$refs.campaignForm) {
          if (newVal) {
            // 当立即返还区域活跃时，强制校验effectiveTime
            this.$refs.campaignForm.validateField('effectiveTime');
          } else {
            // 当立即返还区域不活跃时，清除effectiveTime的校验（如果它不是必填的）
            // 注意：iView没有clearValidate，这里依赖于数据重置或再次校验来清除
            this.campaign.effectiveTime = null; // 清空数据，通常会清除校验状态
            // 或者再次触发一次校验，如果它现在是非必填的，就会通过
            this.$refs.campaignForm.validateField('effectiveTime');
          }
        }
      },
      immediate: true // 立即执行一次，以处理初始状态
    }
  },
};
</script>

<style scoped>
.card-wrapper {
  margin: 24px;
  background: #fff;
}

.form-wrapper {
  padding: 24px;
}

.form-section {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ccc;
  color: #00aaff;
}

.section-content {
  margin-bottom: 32px;
  padding: 0 16px;
}

.rules-section {
  margin-bottom: 16px;
}


.rules-section :deep(.ivu-form-item-error-tip) {
  margin-left: 120px;
}

.rules-section .input-wrapper {
  margin-bottom: 18px;
}

.rule-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.aligned-form-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 32px;
}

.rule-title {
  min-width: 80px;
  display: inline-block;
  text-align: right;
  margin-right: 10px;
}

.input-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
}

.uniform-input {
  width: 100%;
}
.uniform-input {
  width: 100%;
}

.unit-label {
  margin-left: 15px;
  margin-right: 15px;
  color: #515a6e;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 18px;
}


.amount-input-group {
  display: flex;
  align-items: center;
}

.company-section {
  margin-top: 16px;
}

.company-header {
  display: flex;
  /* justify-content: flex-end; */
  margin-bottom: 16px;
}

.add-company-button {
  margin-right: 16px;
}

.company-table-container {
  margin-top: 16px;
}

.form-actions {
  margin-top: 32px;
  text-align: center;
  border-top: 1px solid #e8eaec;
  padding-top: 24px;
}

.custom-textarea {
  border: 1px solid #dcdee2;
  border-radius: 4px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 10px;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  margin-bottom: 5px;
}

.remove-button {
  margin-left: 10px;
}

.spinBox {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1;
}
</style>
