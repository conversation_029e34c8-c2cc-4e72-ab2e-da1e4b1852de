<template>
	<!-- 运营商管理 -->
	<Card>
		<div style="width: 100%;margin-top: 50px; margin: auto;">
			<div style="display: flex;width: 100%;align-items:center;">
				<Button style="margin: 0 2px;margin-left: 20px;" type="primary" icon="md-add"
					v-has="'add'" @click="addSupplier()">添加供应商</Button>
			</div>
			<!-- 表格 -->
			<Table :columns="columns" :data="data" style="width:100%;margin-top: 50px;" :loading="loading">
				<template slot-scope="{ row, index }" slot="action">
					<Button type="primary" size="small" style="margin-right: 5px" v-has="'update'" @click="updateSupplier(row)">编辑</Button>
				</template>
			</Table>
			<!-- 分页 -->
			<div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px; ">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
		</div>
		<!-- 添加供应商 -->
		<Modal v-model="addmodel" title="添加供应商" :mask-closable="false" @on-cancel="cancelModal" :width="400">
			<Form ref="formmodel" :model="formmodel" :rules="rules" :label-width="120">
				<div>
          <FormItem label="供应商公司名称" prop="supplierShortenName">
            <Input v-model="formmodel.supplierShortenName" clearable @on-blur="changeCode('1', formmodel.supplierShortenName)" style="width: 200px"/>
          </FormItem>
          <FormItem label="供应商编码" prop="supplierShortenCode">
          	<Input v-model="formmodel.supplierShortenCode" :disabled="codeDisabled" clearable style="width: 200px"/>
          </FormItem>
					<FormItem label="供应商名称" prop="supplierName">
						<Input v-model="formmodel.supplierName" clearable style="width: 200px" />
					</FormItem>
					<FormItem label="APN(简中)" prop="apnZh">
						<Input v-model="formmodel.apnZh" clearable style="width: 200px;" />
					</FormItem>
					<FormItem label="APN(繁中)" prop="apnTw">
						<Input v-model="formmodel.apnTw" clearable style="width: 200px;" />
					</FormItem>
					<FormItem label="APN(英文)" prop="apnEn">
						<Input v-model="formmodel.apnEn" clearable style="width: 200px;" />
					</FormItem>
					<FormItem label="免实名地区">
						<Select v-model="formmodel.mccList" multiple filterable clearable placeholder="请选择免实名地区" style="width: 200px">
							<Option v-for="item in regionOptions" :value="item.mcc" :key="item.mcc">{{ item.countryEN }}</Option>
						</Select>
					</FormItem>
				</div>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">返回</Button>
				<Button type="primary" :loading="addLoading" @click="addBesure">确定</Button>
			</div>
		</Modal>
		<!-- 修改供应商 -->
		<Modal v-model="updateSupplierModel" title="修改供应商" :mask-closable="false" @on-cancel="cancelModal" :width="400">
			<Form ref="updatemodel" :model="updatemodel" :rules="rules" :label-width="120">
				<div>
          <FormItem label="供应商公司名称" prop="supplierShortenName">
            <Input v-model="updatemodel.supplierShortenName" @on-blur="changeCode('2', updatemodel.supplierShortenName)" style="width: 200px" clearable/>
          </FormItem>
          <FormItem label="供应商编码" prop="supplierShortenCode">
          	<Input v-model="updatemodel.supplierShortenCode" :disabled="codeDisabled" style="width: 200px" clearable/>
          </FormItem>
					<FormItem label="供应商名称" prop="supplierName">
						<Input v-model="updatemodel.supplierName" style="width: 200px" readonly />
					</FormItem>
					<FormItem label="APN(简中)" prop="apnZh">
						<Input v-model="updatemodel.apnZh" clearable style="width: 200px;" />
					</FormItem>
					<FormItem label="APN(繁中)" prop="apnTw">
						<Input v-model="updatemodel.apnTw" clearable style="width: 200px;" />
					</FormItem>
					<FormItem label="APN(英文)" prop="apnEn">
						<Input v-model="updatemodel.apnEn" clearable style="width: 200px;" />
					</FormItem>
					<FormItem label="免实名地区">
						<Select v-model="updatemodel.mccList" multiple filterable clearable placeholder="请选择免实名地区" style="width: 200px">
							<Option v-for="item in regionOptions" :value="item.mcc" :key="item.mcc">{{ item.countryEN }}</Option>
						</Select>
					</FormItem>
				</div>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">返回</Button>
				<Button type="primary" :loading="updateLoading" @click="updateBesure">确定</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		supplier,
		addSupplierList,
		updateSupplierList,
    getSupplierShortenCode,
		getRegionList
	} from '@/api/ResourceSupplier'
	export default {
		data() {
			return {
				loading: false,
				total: 0,
				currentPage: 1,
				addmodel: false,
				updateSupplierModel: false,
				addLoading: false,
				updateLoading: false,
        codeDisabled: false,
        supplierShortenList: [],
        // mock 国家/地区数据
        regionOptions: [],
				columns: [{
						title: '供应商公司名称',
						key: 'supplierShortenName',
						align: 'center'
					},
          {
          		title: '供应商编码',
          		key: 'supplierShortenCode',
          		align: 'center'
          	},
          {
						title: '供应商名称',
						key: 'supplierName',
						align: 'center'
					},
					{
						title: 'APN(简中)',
						key: 'apnZh',
						align: 'center'
					},
					{
						title: 'APN(繁中)',
						key: 'apnTw',
						align: 'center'
					},
					{
						title: 'APN(英文)',
						key: 'apnEn',
						align: 'center',
					},
					{
						title: '免实名地区',
						key: 'mccList',
						align: 'center',
						ellipsis: true,
						tooltip: true,
						// render: (h, params) => {
						// 	// 展示为逗号分隔的英文，mccLsit:[{mcc: '123', countryEN: 'xxx'}]
						// 	return h('span', params.row.countryInfo ? params.row.countryInfo.map(item => item.countryEN).join(', ') : '')
						// }
					},
					{
						title: '操作',
						slot: 'action',
						align: 'center'
					}
				],
				data: [],
				formmodel: {
					supplierName: '',
					apnZh: '',
					apnTw: '',
					apnEn: '',
          supplierShortenName: '',
          supplierShortenCode: '',
          mccList: [],
				},
				updatemodel: {
					supplierName: '',
					apnZh: '',
					apnTw: '',
					apnEn: '',
					supplierId: '',
          supplierShortenId: '',
          supplierShortenName: '',
          supplierShortenCode: '',
          mccList: [],
				},
				rules: {
          supplierShortenName: [{
						required: true,
						message: "请输入供应商公司名称",
						trigger: "blur",
					}],
          supplierShortenCode: [{
						required: true,
						message: "请输入供应商编码",
						trigger: "blur",
					}],
					supplierName: [{
						required: true,
						message: "请输入供应商名称",
						trigger: "blur",
					},{
						min:0,
						max:100,
						message: '输入100位以内字符',
						trigger: 'blur'
					},
					],
					apnZh: [{
							required: true,
							message: "请输入APN(简中)",
							trigger: "blur",
						}
					],
					apnTw: [{
							required: true,
							message: "请输入APN(繁中)",
							trigger: "blur",
						}
					],
					apnEn: [{
							required: true,
							message: "请输入APN(英文)",
							trigger: "blur",
						},
					]
				},
			}
		},
		mounted() {
			this.goPageFirst(1)
		},
		methods: {
			goPageFirst(page) {
				this.loading = true
				var _this = this
				let pageNum = page
				let pageSize = 10
				supplier({
					pageNum,
					pageSize,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.page = page
						this.total = Number(res.count)
						let template = res.data.map(item => {
							return {
								...item,
								mccList: item.countryInfo ? item.countryInfo.map(item => item.countryEN).join(', ') : ''
							}
						})
						this.data = template
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			// 添加
			addSupplier(){
        this.getSupplierShortenCode()
        this.fetchRegionOptions()
				this.addmodel = true
			},
			addBesure(){
				this.$refs.formmodel.validate(valid => {
					if (valid) {
						const formData = JSON.parse(JSON.stringify(this.formmodel));
						addSupplierList(formData).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提醒',
									desc: '添加成功！'
								})
								this.goPageFirst(1)
								this.currentPage=1
								this.cancelModal()
								this.addLoading = false
								this.addmodel = false
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.cancelModal()
							this.addLoading = false
						})
					}
				})
			},
			// 修改
			updateSupplier(row) {
        this.getSupplierShortenCode()
        this.fetchRegionOptions()
				this.updateSupplierModel = true
				this.updatemodel = {
					supplierName: row.supplierName,
					apnZh: row.apnZh,
					apnTw: row.apnTw,
					apnEn: row.apnEn,
					supplierId: row.supplierId,
          supplierShortenId: row.supplierShortenId,
          supplierShortenName: row.supplierShortenName,
          supplierShortenCode: row.supplierShortenCode,
          mccList: row.countryInfo ? row.countryInfo.map(item => item.mcc) : [],
				}
			},
			updateBesure() {
				this.$refs.updatemodel.validate(valid => {
					if (valid) {
						const formData = JSON.parse(JSON.stringify(this.updatemodel));
						updateSupplierList(formData).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提醒',
									desc: '修改成功！'
								})
								this.goPageFirst(1)
								this.currentPage=1
								this.cancelModal()
								this.addLoading = false
								this.addmodel = false
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.cancelModal()
							this.addLoading = false
						})
					}
				})
			},
			cancelModal(){
        this.codeDisabled = false
				this.addmodel = false
				this.updateSupplierModel = false
				this.$refs.formmodel.resetFields()
				this.$refs.updatemodel.resetFields()
				this.formmodel.mccList = []
				this.updatemodel.mccList = []
			},
      //编辑简称时回填编码
      changeCode(type, data) {
        //匹配简称找到对应的编码
        if (this.supplierShortenList.hasOwnProperty(data)) {
          //新增
          if (type == '1') {
            this.codeDisabled = true
            this.formmodel.supplierShortenCode = this.supplierShortenList[data]
          } else {
            this.codeDisabled = true
            this.updatemodel.supplierShortenCode = this.supplierShortenList[data]
          }
        } else {
          //编辑
          this.codeDisabled = false
          if (type == '1') {
            this.formmodel.supplierShortenCode = ''
          } else {
            this.updatemodel.supplierShortenCode = this.updatemodel.supplierShortenCode ? this.updatemodel.supplierShortenCode : ''
          }
        }
      },
      getSupplierShortenCode(type, data) {
        getSupplierShortenCode().then(res => {
        	if (res && res.code == '0000') {
            this.supplierShortenList = res.data
        	} else {
        		throw res
        	}
        }).catch((err) => {
        	console.log(err)
        }).finally(() => {
        })
      },
      fetchRegionOptions() {
        getRegionList().then(res => {
          if (res && res.code === '0000') {
            this.regionOptions = res.data
          }
        })
      },
    }
	}
</script>

<style>
</style>
