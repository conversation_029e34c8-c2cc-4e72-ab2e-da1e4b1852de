<template>
  <!-- 运营商管理 -->
  <Card>
    <div style="width: 100%;margin-top: 50px; margin: auto;">
      <div style="display: flex;width: 100%;align-items:center;">
        <span style="font-weight:bold;">国家名称</span>
        <Input v-model="country " placeholder="请输入国家名称" prop="showTitle" clearable
          style="width: 200px;margin-left: 10px;" />&nbsp;&nbsp;
        <span style="font-weight:bold;">大洲名称</span>
        <Input v-model="continent" placeholder="请输入大洲名称" prop="showTitle" clearable
          style="width: 200px;margin-left: 10px;" />
        <Button v-has="'search'" type="primary" icon="md-search" size="large" style="margin-left: 20px;"
          :loading="searchLoading" @click="search()">搜索</Button>
        <Button v-has="'add'" type="success" icon="md-add" size="large" style="margin-left: 20px;"
          @click="address()">新增国家</Button>
      </div>
      <!-- 表格 -->
      <Table :columns="columns12" :data="talbedata" style="width:100%;margin-top: 50px;" :loading="loading">
        <template slot-scope="{ row, index }" slot="Operators">
          <a @click="viewOperators(row)">查看更多</a>
        </template>
        <template slot-scope="{ row, index }" slot="action">
          <Button v-has="'update'" type="primary" size="small" style="margin-right: 5px"
            @click="updaterow(row)">修改</Button>
          <Button v-has="'delete'" type="error" size="small" style="margin-right: 5px" :loading="deleteLoading"
            @click="remove(row)">删除</Button>
        </template>
      </Table>
      <!-- 分页 -->
      <div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px; ">
        <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
      </div>
      <!-- 添加国家 -->
      <Modal v-model="addmodel" title="添加国家" :mask-closable="false" @on-cancel="cancelModal" :width="'95%'"
        style="padding: 10px; box-sizing: border-box; max-height: 90vh; overflow: auto;">
        <div style="width: 100%; overflow-x: auto;">
          <RadioGroup v-model="addPreSupplier" @on-change="noRepeat">
            <Form ref="formmodel" :model="formmodel" :rules="rules" :label-width="110" style="width: 90%;">
              <FormItem label="漫游是否已开通" prop="openRoaming">
                <Select v-model="formmodel.openRoaming" placeholder="请选择" clearable
                  style="width: 200px ;margin-right: 10px;">
                  <Option :value="item.id" v-for="(item,index3) in openList" :key="index3">{{item.value}}
                  </Option>
                </Select>
              </FormItem>
              <div style="display: flex;">
                <FormItem label="国家名称" prop="countryCn">
                  <Input v-model="formmodel.countryCn" placeholder="简体名称" clearable style="width: 200px" />
                </FormItem>
                <FormItem prop="countryTw" style="margin-left: 30px;" :label-width="0">
                  <Input v-model="formmodel.countryTw" placeholder="繁体名称" clearable style="width: 200px" />
                </FormItem>
                <FormItem prop="countryEn" style="margin-left: 30px;" :label-width="0">
                  <Input v-model="formmodel.countryEn" placeholder="英文名称" clearable style="width: 200px" />
                </FormItem>
              </div>
              <div style="display: flex;">
                <FormItem label="所属大洲名称" prop="continentCn">
                  <Input v-model="formmodel.continentCn" placeholder="简体名称" clearable style="width: 200px" />
                </FormItem>
                <FormItem prop="continentTw" style="margin-left: 30px;" :label-width="0">
                  <Input v-model="formmodel.continentTw" placeholder="繁体名称" clearable style="width: 200px" />
                </FormItem>
                <FormItem prop="continentEn" style="margin-left: 30px;" :label-width="0">
                  <Input v-model="formmodel.continentEn" placeholder="英文名称" clearable style="width: 200px" />
                </FormItem>
              </div>
              <FormItem label="MCC" prop="mcc">
                <Input v-model="formmodel.mcc" placeholder="请输入MCC" prop="showTitle" clearable
                  :onkeyup="formmodel.mcc = formmodel.mcc.replace(/\s+/g,'')" style="width: 200px" />
              </FormItem>
              <FormItem label="是否为热门国家" prop="hotcountry">
                <Select v-model="formmodel.hotcountry" :filterable="true" clearable placeholder="请选择国家"
                  style="width: 200px" @on-change="getProviders($event)">
                  <Option :value="item.id" v-for="(item,index2) in hotcountryList" :key="index2">{{item.value}}</Option>
                </Select>
              </FormItem>
              <FormItem label="上传图片" :rules="[{required: true, message: '请上传图片', trigger: 'blur' },]">
                <div style='display: flex;flex-direction: column;width: 200px;height: 100px;'>
                  <Upload type="drag" action="#" ref="upload" :before-upload="handleUpload" :on-preview="handlePreview"
                    :show-upload-list="false" v-if="formmodel.picture==null">
                    <div style="padding: 20px;height: 100px; width: 100%;">
                      <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                      <p>上传图片</p>
                    </div>
                  </Upload>
                  <div style='display: flex;flex-direction: column;width: 100%;height: 100%;position: relative;' v-else>
                    <Icon type="md-close-circle" color="#ff3300" size="22" class="mediaShowDelSty"
                      @click="cancelSelected()"></Icon>
                    <img :src="pictureUrl" width="100%" height="100%" @click="pictureShowFlag=true"></img>
                  </div>
                </div>
              </FormItem>
              <div class="section-box" style="margin-top: 30px;">
                <div v-for="(item, index) in formmodel.GTList" :key="index">
                  <FormItem :label="'GT码 '" :prop="'GTList.' + index+ '.gtcode'"
                    :rules="[{required: true, message: '请输入GT码', trigger: 'blur' },]">
                    <div class="gt-input-group">
                      <Input v-model="item.gtcode" placeholder="请输入GT码" clearable
                        :onkeyup="item.gtcode = item.gtcode.replace(/\s+/g,'')" style="width: 200px" />
                      <Icon type="ios-close-circle-outline" size="30" style="cursor: pointer;" color="red"
                        @click="removegt(index)" />
                    </div>
                  </FormItem>
                </div>
                <FormItem style="margin-bottom: 20px;">
                  <Button type="primary" ghost @click="addgt()">新增GT码</Button>
                </FormItem>
              </div>
              <div class="section-box">
                <div class="mnc-table">
                  <div class="mnc-row header">
                    <div class="mnc-cell" style="width: 700px;"></div>
                    <div class="mnc-cell" style="width: 60px;"></div>
                    <div class="mnc-cell" style="width: 115px;">
                      <FormItem :label-width="30">
                        <Radio :label="formmodel.MNCList[0].suppliers[0].supplierId">
                          H卡
                        </Radio>
                      </FormItem>
                    </div>
                    <div v-for="i in 9" :key="i" class="mnc-cell" style="width: 120px;">
                      <FormItem :label-width="0" style="display: flex; flex-direction: column;">
                        <div style="display: flex; justify-content: center;">
                          <Radio :label="formmodel.MNCList[0].suppliers[i].supplierId"
                            :disabled="!formmodel.MNCList[0].suppliers[i].supplierId"
                            v-model="formmodel.MNCList[0].suppliers[i].choose"
                            >
                            V{{i}}卡
                          </Radio>
                        </div>
                        <Select filterable @on-clear="clearSupplierId(0,i)"
                          v-model="formmodel.MNCList[0].suppliers[i].supplierId"
                          :clearable="true" style="width: 100px;" transfer
                          @on-change="(value) => handleSupplierChange(value, i)">
                          <Option v-for="inx in availableProviders(formmodel.MNCList[0].suppliers[i].supplierId)"
                            :value="inx.supplierId" :key="inx.supplierId" :title="inx.supplierName">{{inx.supplierName.length > 20 ? inx.supplierName.substring(0,20) + "…" : inx.supplierName}}
                          </Option>
                        </Select>
                      </FormItem>
                    </div>
                  </div>
                  <div v-for="(item, indexMCN) in formmodel.MNCList" :key="indexMCN" class="mnc-row">
                    <FormItem label="MNC" :prop="'MNCList.' + indexMCN+ '.mnc'" :rules="rules.mnc">
                      <Input v-model="item.mnc" placeholder="请输入MNC" clearable
                        :onkeyup="item.mnc = item.mnc.replace(/\s+/g,'')" style="width: 150px" />
                    </FormItem>
                    <FormItem label="运营商名称" :prop="'MNCList.' + indexMCN+ '.operatorId'" :rules="rules.operatorId"
                      :label-width='90'>
                      <Select v-model="item.operatorId" placeholder="请输入运营商名称" :disabled="!item.mnc" clearable
                        filterable style="width: 150px" transfer @on-change="() => updateSupportNetType()">
                        <Option v-for="(item3,index3) in operatorIdList" :value="item3.id" :key="item3.id"
                          :title="item3.id">{{item3.operatorName}}</Option>
                      </Select>
                    </FormItem>
                    <FormItem label="TADIG" :prop="'MNCList.' + indexMCN+ '.tadig'" :rules="rules.tadigRules"
                      :label-width='60'>
                      <Input v-model="item.tadig" placeholder="请输入TADIG" maxlength="64" clearable
                        :disabled="!item.operatorId" v-model.trim="item.tadig" style="width: 150px" />
                    </FormItem>
                    <FormItem :label-width="0" style="margin: 0 10px; cursor: pointer;">
                      <Icon type="ios-close-circle-outline" size="30" color="red" @click="removemnc(indexMCN)" />
                    </FormItem>
                    <FormItem :label-width="5">
                      <Select v-model="item.suppliers[0].status" style="width: 100px;" transfer
                        :disabled="!item.operatorId"
                        :class="{'status-1': item.suppliers[0].status === 1, 'status-2': item.suppliers[0].status === 2, 'status-3': item.suppliers[0].status === 3}"
                        @on-change="(val) => changeSupplierStatus(indexMCN, 0, val)">
                        <Option v-for="status in mncStatusOptions" :value="status.value" :key="status.value"
                          :class="{'status-1': status.value === 1, 'status-2': status.value === 2, 'status-3': status.value === 3}">
                          {{ status.label }}
                        </Option>
                      </Select>
                    </FormItem>
                    <FormItem :label-width="20" v-for="i in 9" :key="i">
                      <Select v-model="item.suppliers[i].status" style="width: 100px;" transfer
                        :disabled="!item.operatorId"
                        :class="{'status-1': item.suppliers[i].status === 1, 'status-2': item.suppliers[i].status === 2, 'status-3': item.suppliers[i].status === 3}"
                        @on-change="(val) => changeSupplierStatus(indexMCN, i, val)">
                        <Option v-for="status in mncStatusOptions" :value="status.value" :key="status.value"
                          :class="{'status-1': status.value === 1, 'status-2': status.value === 2, 'status-3': status.value === 3}">
                          {{ status.label }}
                        </Option>
                      </Select>
                    </FormItem>
                  </div>
                </div>
                <FormItem>
                  <Button type="primary" ghost @click="addmnc()">新增MNC</Button>
                </FormItem>
              </div>
              <div class="section-box">
                <div v-for="(itemNet, indexNet) in formmodel.supportNetType" :key="indexNet" class="mnc-row">
                  <FormItem label="运营商名称" :prop="'supportNetType.' + indexNet + '.operatorId'">
                    <Select v-model="itemNet.operatorId" disabled style="width: 150px" transfer>
                      <Option v-for="netItem in operatorIdList" :value="netItem.id" :key="netItem.id"
                        :title="netItem.id">
                        {{netItem.operatorName}}
                      </Option>
                    </Select>
                  </FormItem>
                  <FormItem label="网络类型" :prop="'supportNetType.' + indexNet + '.supportNetType'"
                    :rules="[{required: true, type: 'array', min: 1, message: '请选择网络类型', trigger: 'change'}]">
                    <CheckboxGroup v-model="itemNet.supportNetType"
                      @on-change="(value) => handleNetworkTypeChange(value, indexNet)">
                      <Checkbox label="2">2G</Checkbox>&nbsp;&nbsp;&nbsp;&nbsp;
                      <Checkbox label="3">3G</Checkbox>&nbsp;&nbsp;&nbsp;&nbsp;
                      <Checkbox label="4">4G</Checkbox>&nbsp;&nbsp;&nbsp;&nbsp;
                      <Checkbox label="5">5G</Checkbox>
                    </CheckboxGroup>
                  </FormItem>
                </div>
              </div>
            </Form>
          </RadioGroup>
        </div>
        <div slot="footer" style="width: 100%; display: flex;justify-content:center; margin: 30px 0;">
          <Button @click="cancelModal">取消</Button>&nbsp;&nbsp;
          <Button type="primary" :loading="addLoading" @click="addsave">确定</Button>
        </div>
      </Modal>
      <!-- 修改 -->
      <Modal v-model="upmodel" title="修改国家" :mask-closable="false" @on-cancel="updatecancelModal" :width="'95%'"
        style="padding: 10px; box-sizing: border-box; max-height: 90vh; overflow-y: auto;">
        <div style="width: 100%; overflow-x: auto;">
          <RadioGroup v-model="upadatePreSupplier" @on-change="noRepeats">
            <Form ref="upform" :model="upform" :rules="upformrules" :label-width="110" style="width: 90%;">
              <FormItem label="漫游是否已开通" prop="openRoaming">
                <Select v-model="upform.openRoaming" placeholder="请选择" clearable
                  style="width: 200px ;margin-right: 10px;">
                  <Option :value="item.id" v-for="(item,indexOpen) in openList" :key="indexOpen">{{item.value}}
                  </Option>
                </Select>
              </FormItem>
              <div style="display: flex;">
                <FormItem label="国家名称" prop="countryCn">
                  <Input v-model="upform.countryCn" placeholder="简体名称" clearable style="width: 200px" />
                </FormItem>
                <FormItem prop="countryTw" style="margin-left: 30px;" :label-width="0">
                  <Input v-model="upform.countryTw" placeholder="繁体名称" clearable style="width: 200px" />
                </FormItem>
                <FormItem prop="countryEn" style="margin-left: 30px;" :label-width="0">
                  <Input v-model="upform.countryEn" placeholder="英文名称" clearable style="width: 200px" />
                </FormItem>
              </div>
              <div style="display: flex;">
                <FormItem label="所属大洲名称" prop="continentCn">
                  <Input v-model="upform.continentCn" placeholder="简体名称" clearable style="width: 200px" />
                </FormItem>
                <FormItem prop="continentTw" style="margin-left: 30px;" :label-width="0">
                  <Input v-model="upform.continentTw" placeholder="繁体名称" clearable style="width: 200px" />
                </FormItem>
                <FormItem prop="continentEn" style="margin-left: 30px;" :label-width="0">
                  <Input v-model="upform.continentEn" placeholder="英文名称" clearable style="width: 200px" />
                </FormItem>
              </div>
              <FormItem label="MCC" prop="mcc">
                <Input v-model="upform.mcc" placeholder="请输入MCC" prop="showTitle" clearable style="width: 200px" />
              </FormItem>
              <FormItem label="是否为热门国家" :rules="[{required: true, message: '请选择是否为热门国家', trigger: 'blur' },]">
                <Select v-model="hotCountry" placeholder="请选择" style="width: 200px ;margin-right: 10px;"
                  @on-change="getProviders($event)">
                  <Option :value="item.id" v-for="(item,index6) in hotcountryList" :key="index6">{{item.value}}</Option>
                </Select>
              </FormItem>
              <FormItem label="上传图片" :rules="[{required: true, message: '请上传图片', trigger: 'blur' },]">
                <div style='display: flex;flex-direction: column;width: 200px;height: 100px;'>
                  <Upload type="drag" action="#" ref="upload" :before-upload="handleUpload" :on-preview="handlePreview"
                    :show-upload-list="false" v-if="upform.imagePath==null">
                    <div style="padding: 20px;height: 100px; width: 100%;">
                      <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                      <p>上传图片</p>
                    </div>
                  </Upload>
                  <div style='display: flex;flex-direction: column;width: 100%;height: 100%;position: relative;' v-else>
                    <Icon type="md-close-circle" color="#ff3300" size="22" class="mediaShowDelSty"
                      @click="cancelSelected()"></Icon>
                    <img :src="pictureUrl" width="100%" height="100%" @click="pictureShowFlag=true"></img>
                  </div>
                </div>
              </FormItem>
              <div class="section-box" style="margin-top: 30px;">
                <div v-for="(item, indexGt) in upform.gt" :key="indexGt">
                  <FormItem :label="'GT码 ' " :prop="'gt.' + indexGt+ '.gtCode'"
                    :rules="[{required: true, message: '请输入GT码', trigger: 'blur' },]">
                    <div>
                      <Input v-model="item.gtCode" placeholder="请输入GT码" clearable
                        :onkeyup="item.gtCode = item.gtCode.replace(/\s+/g,'')" style="width: 200px" />
                      <Icon type="ios-close-circle-outline" size="30" color="red" style="cursor: pointer;"
                        @click="upformremovegt(indexGt)" />
                    </div>
                  </FormItem>
                </div>
                <FormItem style="margin-bottom: 20px;">
                  <Button type="primary" size="small" @click="upformaddgt()">新增GT码</Button>
                </FormItem>
              </div>
              <div class="section-box">
                <div class="mnc-table">
                  <div class="mnc-row header">
                    <div class="mnc-cell" style="width: 700px;"></div>
                    <div class="mnc-cell" style="width: 60px;"></div>
                    <div class="mnc-cell" style="width: 115px;">
                      <FormItem :label-width="30">
                        <Radio :label="upform.mnc[0].suppliers[0].supplierId">
                          H卡
                        </Radio>
                      </FormItem>
                    </div>
                    <div v-for="i in 9" :key="i" class="mnc-cell" style="width: 120px;">
                      <FormItem :label-width="0" style="display: flex; flex-direction: column;">
                        <div style="display: flex; justify-content: center;">
                          <Radio :label="upform.mnc[0].suppliers[i].supplierId"
                            :disabled="!upform.mnc[0].suppliers[i].supplierId"
                            v-model="upform.mnc[0].suppliers[i].choose"
                            >
                            V{{i}}卡
                          </Radio>
                        </div>
                        <Select filterable @on-clear="clearSupplierId(0,i)"
                          v-model="upform.mnc[0].suppliers[i].supplierId"
                          :clearable="true" style="width: 100px;" transfer
                          @on-change="(value) => handleSupplierChangeUpdate(value, i)">
                          <Option v-for="inx in availableProviderss(upform.mnc[0].suppliers[i].supplierId)"
                            :value="inx.supplierId" :key="inx.supplierId" :title="inx.supplierName">{{inx.supplierName.length > 20 ? inx.supplierName.substring(0,20) + "…" : inx.supplierName}}
                          </Option>
                        </Select>
                      </FormItem>
                    </div>
                  </div>
                  <div v-for="(item, indexMCN) in upform.mnc" :key="indexMCN" class="mnc-row">
                    <FormItem label="MNC" :prop="'mnc.' + indexMCN+ '.mnc'" :rules="upformrules.mnc">
                      <Input v-model="item.mnc" placeholder="请输入MNC" clearable
                        :onkeyup="item.mnc = item.mnc.replace(/\s+/g,'')" style="width: 150px" />
                    </FormItem>
                    <FormItem label="运营商名称" :prop="'mnc.' + indexMCN+ '.operatorId'" :rules="upformrules.operatorId"
                      :label-width='90'>
                      <Select v-model="item.operatorId" placeholder="请输入运营商名称" :disabled="!item.mnc" clearable
                        filterable style="width: 150px" transfer @on-change="() => updateSupportNetTypeUpdate()">
                        <Option v-for="(item3,index3) in operatorIdList" :value="item3.id" :key="item3.id"
                          :title="item3.id">{{item3.operatorName}}</Option>
                      </Select>
                    </FormItem>
                    <FormItem label="TADIG" :prop="'mnc.' + indexMCN+ '.tadig'" :rules="upformrules.tadigRules"
                      :label-width='60'>
                      <Input v-model="item.tadig" placeholder="请输入TADIG" maxlength="64" clearable
                        :disabled="!item.operatorId" v-model.trim="item.tadig" style="width: 150px" />
                    </FormItem>
                    <FormItem :label-width="0" style="margin: 0 10px; cursor: pointer;">
                      <Icon type="ios-close-circle-outline" size="30" color="red" @click="upformremovemnc(indexMCN)" />
                    </FormItem>
                    <FormItem :label-width="5">
                      <Select v-model="item.suppliers[0].status" style="width: 100px;" transfer
                        :disabled="!item.operatorId"
                        :class="{'status-1': item.suppliers[0].status === 1, 'status-2': item.suppliers[0].status === 2, 'status-3': item.suppliers[0].status === 3}"
                        @on-change="(val) => changeSupplierStatusUpdate(indexMCN, 0, val)">
                        <Option v-for="status in mncStatusOptions" :value="status.value" :key="status.value"
                          :class="{'status-1': status.value === 1, 'status-2': status.value === 2, 'status-3': status.value === 3}">
                          {{ status.label }}
                        </Option>
                      </Select>
                    </FormItem>
                    <FormItem :label-width="20" v-for="i in 9" :key="i">
                      <Select v-model="item.suppliers[i].status" style="width: 100px;" transfer
                        :disabled="!item.operatorId"
                        :class="{'status-1': item.suppliers[i].status === 1, 'status-2': item.suppliers[i].status === 2, 'status-3': item.suppliers[i].status === 3}"
                        @on-change="(val) => changeSupplierStatusUpdate(indexMCN, i, val)">
                        <Option v-for="status in mncStatusOptions" :value="status.value" :key="status.value"
                          :class="{'status-1': status.value === 1, 'status-2': status.value === 2, 'status-3': status.value === 3}">
                          {{ status.label }}
                        </Option>
                      </Select>
                    </FormItem>
                  </div>
                </div>
                <FormItem>
                  <Button type="primary" size="small" @click="upformaddmnc()">新增MNC</Button>
                </FormItem>
              </div>
              <div class="section-box">
                <div v-for="(itemNet, indexNet) in upform.supportNetType" :key="indexNet" class="mnc-row">
                  <FormItem label="运营商名称" :prop="'supportNetType.' + indexNet + '.operatorId'">
                    <Select v-model="itemNet.operatorId" disabled style="width: 150px" transfer>
                      <Option v-for="netItem in operatorIdList" :value="netItem.id" :key="netItem.id"
                        :title="netItem.id">
                        {{netItem.operatorName}}
                      </Option>
                    </Select>
                  </FormItem>
                  <FormItem label="网络类型" :prop="'supportNetType.' + indexNet + '.supportNetType'"
                    :rules="[{required: true, type: 'array', min: 1, message: '请至少选择一种网络类型', trigger: 'change'}]">
                    <CheckboxGroup v-model="itemNet.supportNetType"
                      @on-change="(value) => handleNetworkTypeChange(value, indexNet)">
                      <Checkbox label="2">2G</Checkbox>&nbsp;&nbsp;&nbsp;&nbsp;
                      <Checkbox label="3">3G</Checkbox>&nbsp;&nbsp;&nbsp;&nbsp;
                      <Checkbox label="4">4G</Checkbox>&nbsp;&nbsp;&nbsp;&nbsp;
                      <Checkbox label="5">5G</Checkbox>
                    </CheckboxGroup>
                  </FormItem>
                </div>
              </div>
            </Form>
          </RadioGroup>
        </div>
        <div slot="footer" style="width: 100%; display: flex;justify-content:center; margin: 30px 0;">
          <Button @click="updatecancelModal">取消</Button>&nbsp;&nbsp;
          <Button type="primary" :loading="updateLoading" @click="updatesave">确定</Button>
        </div>
      </Modal>
      <Modal title="View Image" v-model="visible">
        <img :src="'https://o5wwk8baw.qnssl.com/' + imgName + '/large'" v-if="visible" style="width: 100%">
      </Modal>
      <!-- 图片预览-->
      <Modal title="封面预览" v-model="pictureShowFlag" :footer-hide="true" width="532px">
        <div style="display: flex;justify-content: center;align-items: center;width: 500px;">
          <img :src="pictureUrl" width="100%">
        </div>
      </Modal>
      <!-- Gt码查看更多 -->
      <Modal title="查看更多" v-model="model1">
        <div v-for="(item,index7) in gtList" :value="item.gtCode" :key="index7">
          <span style="margin-top: 20px;">{{item.gtCode}}</span>
        </div>
      </Modal>
      <!-- 运营商查看更多 -->
      <Modal title="查看更多" v-model="model2">
        <div v-for="(item,index8) in operatorList" :value="item.operatorName" :key="index8">
          <span style="margin-top: 20px;">{{item.operatorName}}</span>
        </div>
      </Modal>
    </div>
  </Card>
</template>

<script>
  import {
    opsearch,
    addop,
    updateop,
    deleteop,
    getOperators
  } from '@/api/operators'
  import {
    supplier,
  } from '@/api/ResourceSupplier'
  export default {
    computed: {
      // 资源供应商重复选择
      availableProviders() {
        return (val) => {
          let newList = JSON.parse(JSON.stringify(this.providers));
          const arr = this.formmodel.MNCList[0].suppliers.map(item => {
            return (item = item.supplierId);
          });
          newList = newList.filter(item => {
            if (val == item.supplierId) {
              return item;
            } else {
              if (arr.indexOf(item.supplierId) == -1) {
                return item;
              }
            }
          });
          return newList;
        };
      },
      availableProviderss() {
        return (val) => {
          let newList = JSON.parse(JSON.stringify(this.providers));
          const arr = this.upform.mnc[0].suppliers.map(item => {
            return (item = item.supplierId);
          });
          newList = newList.filter(item => {
            if (val == item.supplierId) {
              return item;
            } else {
              if (arr.indexOf(item.supplierId) == -1) {
                return item;
              }
            }
          });
          return newList;
        };
      },
      // 新增国家的网络类型计算属性
      formmodelSupportNetType() {
        const enabledOperators = new Set();

        this.formmodel.MNCList.forEach(mncItem => {
          const isOperatorEnabled = mncItem.suppliers.some(supplier =>
            [1, 2].includes(supplier.status) && mncItem.operatorId
          );

          if (isOperatorEnabled && mncItem.operatorId) {
            enabledOperators.add(mncItem.operatorId);
          }
        });

        const currentOperatorIds = this.formmodel.supportNetType.map(item => item.operatorId);

        // 添加新增的运营商
        Array.from(enabledOperators).forEach(operatorId => {
          if (!currentOperatorIds.includes(operatorId)) {
            this.$set(this.formmodel.supportNetType, this.formmodel.supportNetType.length, {
              operatorId: operatorId,
              supportNetType: []
            });
          }
        });

        // 移除不再启用的运营商
        this.formmodel.supportNetType = this.formmodel.supportNetType.filter(item =>
          enabledOperators.has(item.operatorId)
        );

        return this.formmodel.supportNetType;
      },
      // 修改国家的网络类型计算属性
      upformSupportNetType() {
        if (this.upform.supportNetType && this.upform.supportNetType.length > 0) {
          return this.upform.supportNetType;
        }

        const enabledOperators = new Set();

        this.upform.mnc.forEach(mncItem => {
          const isOperatorEnabled = mncItem.suppliers.some(supplier =>
            [1, 2].includes(supplier.status) && mncItem.operatorId
          );

          if (isOperatorEnabled && mncItem.operatorId) {
            enabledOperators.add(mncItem.operatorId);
          }
        });

        const existingNetworkTypes = new Map();

        if (this.upform.supportNetTypeDTOS && this.upform.supportNetTypeDTOS.length > 0) {
          this.upform.supportNetTypeDTOS.forEach(item => {
            try {
              const supportNetType = JSON.parse(item.supportNetType);
              existingNetworkTypes.set(item.operatorId, supportNetType);
            } catch (e) {
              console.error('解析supportNetType失败:', e);
            }
          });
        }

        this.upform.supportNetType.forEach(item => {
          if (item.supportNetType && item.supportNetType.length > 0) {
            existingNetworkTypes.set(item.operatorId, item.supportNetType);
          }
        });

        this.upform.supportNetType = Array.from(enabledOperators).map(operatorId => ({
          operatorId: operatorId,
          supportNetType: existingNetworkTypes.get(operatorId) || []
        }));

        return this.upform.supportNetType;
      },
    },
    data() {
      var checkEnglish = (rule, value, callback) => {
        //  校验英文的正则
        // let reg = /[a-zA-z]$/;
        var reg = /^[0-9a-zA-Z\/\(\)\,\.\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;

        if (reg.test(value) == false) {
          callback(new Error("不能中文输入！"));
        } else {
          //校验通过
          callback();
        }
      };
      // 新增国家
      var checkMNC = (rule, value, callback) => {
        const matches = rule.field.match(/MNCList\.(\d+)\.mnc/);
        const currentIndex = parseInt(matches[1], 10); // 提取索引并转换为整数

        // 检查 mnc 和 operatorId 的组合是否已存在（不包括当前正在验证的这一项）
        const seenPairs = new Set();
        this.formmodel.MNCList.forEach((detail, index) => {
          if (index !== currentIndex) { // 排除当前正在验证的这一项
            const key = `${detail.mnc}-${detail.operatorId}`;
            seenPairs.add(key);
          }
        });

        // 检查当前项是否与已存在的组合重复
        const currentKey = `${value}-${this.formmodel.MNCList[currentIndex].operatorId}`;
        if (seenPairs.has(currentKey)) {
          callback(new Error('MNC + 运营商 的组合必须唯一'));
        } else {
          callback(); // 没有错误
        }
      };
      var checkOperatorId = (rule, value, callback) => {
        const matches = rule.field.match(/MNCList\.(\d+)\.operatorId/);
        const currentIndex = parseInt(matches[1], 10); // 提取索引并转换为整数

        // 检查 mnc 和 operatorId 的组合是否已存在（不包括当前正在验证的这一项）
        const seenPairs = new Set();
        this.formmodel.MNCList.forEach((detail, index) => {
          if (index !== currentIndex) { // 排除当前正在验证的这一项
            const key = `${detail.mnc}-${detail.operatorId}`;
            seenPairs.add(key);
          }
        });

        // 检查当前项是否与已存在的组合重复
        const currentKey = `${this.formmodel.MNCList[currentIndex].mnc}-${value}`;
        if (seenPairs.has(currentKey)) {
          callback(new Error('MNC + 运营商 的组合必须唯一'));
        } else {
          callback(); // 没有错误
        }
      };
      var checkAddTadig = (rule, value, callback) => {
        // 使用正则表达式检查输入值是否只包含英文、数字或它们的组合
        const isValidInput = /^[a-zA-Z0-9]+$/.test(value);

        if (value) {
          if (!isValidInput) {
            // 如果输入值包含特殊符号，则直接报错
            callback(new Error('TADIG仅支持英文和数字'));
          }
        } else {
          callback();
        }

        const matches = rule.field.match(/MNCList\.(\d+)\.tadig/);
        const currentIndex = parseInt(matches[1], 10);
        // 从 MNCList 中找到所有其他项（排除当前索引项）
        const otherItems = this.formmodel.MNCList.filter((_, index) => index !== currentIndex);
        // 查找是否存在相同的 TADIG
        const foundItem = otherItems.find(item => item.tadig === value);
        if (foundItem) {
          // 如果找到相同的 TADIG，则检查运营商是否相同
          if (foundItem.operatorId === this.formmodel.MNCList[currentIndex].operatorId) {
            // 如果运营商相同，则校验通过
            callback();
          } else {
            // 如果运营商不同，则报错
            callback(new Error('TADIG只能对应一个运营商,请修改TADIG'));
          }
        } else {
          // 如果没有找到相同的 TADIG，则校验通过
          callback();
        }
      };
      // 修改国家
      var checkUpdateMNC = (rule, value, callback) => {
        const matches = rule.field.match(/mnc\.(\d+)\.mnc/);
        const currentIndex = parseInt(matches[1], 10); // 提取索引并转换为整数

        // 检查 mnc 和 operatorId 的组合是否已存在（不包括当前正在验证的这一项）
        const seenPairs = new Set();
        this.upform.mnc.forEach((detail, index) => {
          if (index !== currentIndex) { // 排除当前正在验证的这一项
            const key = `${detail.mnc}-${detail.operatorId}`;
            seenPairs.add(key);
          }
        });

        // 检查当前项是否与已存在的组合重复
        const currentKey = `${value}-${this.upform.mnc[currentIndex].operatorId}`;
        if (seenPairs.has(currentKey)) {
          callback(new Error('MNC + 运营商 的组合必须唯一'));
        } else {
          callback(); // 没有错误
        }
      };
      var checkUpdateOperatorId = (rule, value, callback) => {
        const matches = rule.field.match(/mnc\.(\d+)\.operatorId/);
        const currentIndex = parseInt(matches[1], 10); // 提取索引并转换为整数

        // 检查 mnc 和 operatorId 的组合是否已存在（不包括当前正在验证的这一项）
        const seenPairs = new Set();
        this.upform.mnc.forEach((detail, index) => {
          if (index !== currentIndex) { // 排除当前正在验证的这一项
            const key = `${detail.mnc}-${detail.operatorId}`;
            seenPairs.add(key);
          }
        });

        // 检查当前项是否与已存在的组合重复
        const currentKey = `${this.upform.mnc[currentIndex].mnc}-${value}`;
        if (seenPairs.has(currentKey)) {
          callback(new Error('MNC + 运营商 的组合必须唯一'));
        } else {
          callback(); // 没有错误
        }
      };
      var checkUpdateTadig = (rule, value, callback) => {
        // 使用正则表达式检查输入值是否只包含英文、数字或它们的组合
        const isValidInput = /^[a-zA-Z0-9]+$/.test(value);

        if (value) {
          if (!isValidInput) {
            // 如果输入值包含特殊符号，则直接报错
            callback(new Error('TADIG仅支持英文和数字'));
          }
        } else {
          callback();
        }

        const matches = rule.field.match(/mnc\.(\d+)\.tadig/);
        const currentIndex = parseInt(matches[1], 10);
        // 从 MNCList 中找到所有其他项（排除当前索引项）
        const otherItems = this.upform.mnc.filter((_, index) => index !== currentIndex);
        // 查找是否存在相同的 TADIG
        const foundItem = otherItems.find(item => item.tadig === value);
        if (foundItem) {
          // 如果找到相同的 TADIG，则检查运营商是否相同
          if (foundItem.operatorId === this.upform.mnc[currentIndex].operatorId) {
            // 如果运营商相同，则校验通过
            callback();
          } else {
            // 如果运营商不同，则报错
            callback(new Error('TADIG只能对应一个运营商,请修改TADIG'));
          }
        } else {
          // 如果没有找到相同的 TADIG，则校验通过
          callback();
        }
      };
      return {
        excludeProvides: [],
        pictureShowFlag: false,
        pictureUrl: '',
        pictureflag: false,
        hotcountryList: [{
            id: 1,
            value: '否'
          },
          {
            id: 2,
            value: '是'
          }
        ],
        openList: [{
            id: "1",
            value: '是'
          },
          {
            id: "2",
            value: '否'
          }
        ],
        operatorIdList: [],
        providers: [],
        addPreSupplier: null, //新增-首选供应商
        upadatePreSupplier: null, //修改-首选供应商
        supplierIdlx: 'default', //单选
        supplierIdlxs: 'default', //单选
        formmodel: {
          GTList: [{
            index: 0,
            gtcode: "",
          }],
          MNCList: [{
            id: 0,
            index: 0,
            mnc: "",
            operatorId: "",
            tadig: "",
            suppliers: [{
              supplierId: 'default',
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }],
          }],
          supplierId: "",
          openRoaming: '',
          countryCn: '',
          countryTw: '',
          countryEn: '',
          continentCn: '',
          continentTw: '',
          continentEn: '',
          mcc: '',
          gtcode: '',
          mnc: '',
          operatorName: '',
          hotcountry: '',
          supportNetType: [{
            operatorId: '',
            supportNetType: [],
          }]
        },
        upform: {
          gt: [{
            index: 0,
            gtCode: "",
          }],
          mnc: [{
            id: 0,
            index: 0,
            mnc: "",
            operatorId: "",
            tadig: "",
            suppliers: [{
              supplierId: 'default',
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }],
          }],
          supportNetType: [{
            operatorId: '',
            supportNetType: [],
          }],
          openRoaming: '',
          countryCn: '',
          countryTw: '',
          countryEn: '',
          continentCn: '',
          continentTw: '',
          continentEn: '',
          mcc: '',
          hotcountry: '',
        },
        country: '',
        countryId: '',
        continent: '',
        form: {},
        file: null,
        uploadUrl: '',
        loading: false,
        searchLoading: false,
        addLoading: false,
        updateLoading: false,
        deleteLoading: false,
        total: 0,
        currentPage: 1,
        addmodel: false,
        upmodel: false,
        index: 0,
        model1: false,
        model2: false,
        columns12: [{
            title: '国家名称',
            key: 'countryCn',
            align: 'center',
            minWidth: 100,
            tooltip: true,
          },
          {
            title: '所属大洲',
            key: 'continentCn',
            align: 'center',
            minWidth: 100,
            tooltip: true,
          },
          {
            title: 'MCC',
            key: 'mcc',
            align: 'center',
            minWidth: 100,
            tooltip: true,
          },
          {
            title: '是否为热门国家',
            key: 'hotcountry',
            align: 'center',
            minWidth: 120,
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              const text = row.hotCountry == 2 ? '是' : '否'
              return h('label', text)
            }
          },
          {
            title: '运营商',
            slot: 'Operators',
            align: 'center',
            minWidth: 120,
            render: (h, params) => {
              const row = params.row
              // 使用Set去重
              const uniqueOperators = new Set()
              row.mnc.forEach((value) => {
                uniqueOperators.add(value.operatorName)
              })
              // 将Set转换为数组并连接
              const text = Array.from(uniqueOperators).join('、 ')
              return h('div', [
                h('Tooltip', {
                  props: {
                    placement: 'bottom',
                    transfer: true,
                    content: text,
                    maxWidth: 300
                  },
                  style: {
                    width: '100%',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }
                }, text)
              ]);
            }
          },
          {
            title: '操作',
            slot: 'action',
            align: 'center',
            minWidth: 150,
          }
        ],
        talbedata: [],
        defaultList: [],
        imgName: '',
        visible: false,
        uploadList: [],
        hotCountry: '',
        gtList: [],
        operatorList: [],
        rules: {
          openRoaming: [{
            required: true,
            message: "请选择是否开通",
            trigger: "blur",
          }],
          countryCn: [{
            required: true,
            message: "请输入简体名称",
            trigger: "blur",
          }, {
            min: 0,
            max: 100,
            message: '输入100位以内字符',
            trigger: 'blur'
          }, ],
          countryTw: [{
            required: true,
            message: "请输入繁体名称",
            trigger: "blur",
          }, {
            min: 0,
            max: 100,
            message: '输入100位以内字符',
            trigger: 'blur'
          }, ],
          countryEn: [{
              required: true,
              message: "请输入英文名称",
              trigger: "blur",
            },
            {
              min: 0,
              max: 100,
              message: '输入100位以内字符',
              trigger: 'blur'
            },
            {
              required: true,
              validator: checkEnglish,
              trigger: 'blur'
            },
          ],
          continentCn: [{
            required: true,
            message: "请输入简体名称",
            trigger: "blur",
          }, {
            min: 0,
            max: 100,
            message: '输入100位以内字符',
            trigger: 'blur'
          }, ],
          continentTw: [{
            required: true,
            message: "请输入繁体名称",
            trigger: "blur",
          }, {
            min: 0,
            max: 100,
            message: '输入100位以内字符',
            trigger: 'blur'
          }, ],
          continentEn: [{
              required: true,
              message: "请输入英文名称",
              trigger: "blur",
            }, {
              min: 0,
              max: 100,
              message: '输入100位以内字符',
              trigger: 'blur'
            },
            {
              required: true,
              validator: checkEnglish,
              trigger: 'blur'
            },
          ],
          mcc: [{
              required: true,
              message: "请输入MCC",
              trigger: "blur",
            },
            // {
            // 	pattern: /^[0-9]*$/,
            // 	message: '请输入纯数字号码'
            // },
            {
              min: 0,
              max: 50,
              message: '输入50位以内的号码',
              trigger: 'blur'
            },
          ],
          gtcode: [{
            required: true,
            message: "请输入...",
            trigger: "blur",
          }, {
            min: 0,
            max: 25,
            message: '输入25位以内的字符',
            trigger: 'blur'
          }, ],
          mnc: [{
            required: true,
            message: "请输入MCC",
            trigger: "blur",
          }, {
            min: 0,
            max: 25,
            message: '输入25位以内的字符',
            trigger: 'blur'
          }, ],
          operatorName: [{
            required: true,
            message: "请输入...",
            trigger: "blur",
          }],
          apn: [{
            required: true,
            message: "请输入...",
            trigger: "blur",
          }, ],
          hotcountry: [{
            required: true,
            message: "请选择是否为热门国家",
            trigger: "blur",
            pattern: /.+/
          }],
          mnc: [{
            required: true,
            message: '请输入MNC码',
          }, {
            pattern: /^[0-9]*$/,
            message: '请输入纯数字号码',
          }, {
            validator: checkMNC,
          }],
          operatorId: [{
            required: true,
            message: '请输入运营商名称',
          }, {
            validator: checkOperatorId,
          }],
          tadigRules: [{
            validator: checkAddTadig,
          }]
        },
        upformrules: {
          openRoaming: [{
            required: true,
            message: "请选择是否开通",
            trigger: 'blur'
          }],
          countryCn: [{
            required: true,
            message: "请输入简体名称",
            trigger: "blur",
          }, {
            min: 0,
            max: 100,
            message: '输入100位以内字符',
            trigger: 'blur'
          }, ],
          countryTw: [{
            required: true,
            message: "请输入繁体名称",
            trigger: "blur",
          }, {
            min: 0,
            max: 100,
            message: '输入100位以内字符',
            trigger: 'blur'
          }, ],
          countryEn: [{
              required: true,
              message: "请输入英文名称",
              trigger: "blur",
            },
            {
              min: 0,
              max: 100,
              message: '输入100位以内字符',
              trigger: 'blur'
            },
            {
              required: true,
              validator: checkEnglish,
              trigger: 'blur'
            },
          ],
          continentCn: [{
            required: true,
            message: "请输入简体名称",
            trigger: "blur",
          }, {
            min: 0,
            max: 100,
            message: '输入100位以内字符',
            trigger: 'blur'
          }, ],
          continentTw: [{
            required: true,
            message: "请输入繁体名称",
            trigger: "blur",
          }, {
            min: 0,
            max: 100,
            message: '输入100位以内字符',
            trigger: 'blur'
          }, ],
          continentEn: [{
              required: true,
              message: "请输入英文名称",
              trigger: "blur",
            }, {
              min: 0,
              max: 100,
              message: '输入100位以内字符',
              trigger: 'blur'
            },
            {
              required: true,
              validator: checkEnglish,
              trigger: 'blur'
            },
          ],
          mcc: [{
              required: true,
              message: "请输入MCC",
              trigger: "blur",
            },
            {
              min: 0,
              max: 50,
              message: '输入50位以内的号码',
              trigger: 'blur'
            },
          ],
          gtcode: [{
            required: true,
            message: "请输入...",
            trigger: "blur",
          }, {
            min: 0,
            max: 25,
            message: '输入25位以内的字符',
            trigger: 'blur'
          }, ],
          mnc: [{
            required: true,
            message: "请输入MCC",
            trigger: "blur",
          }, {
            min: 0,
            max: 25,
            message: '输入25位以内的字符',
            trigger: 'blur'
          }, ],
          operatorName: [{
            required: true,
            message: "请输入...",
            trigger: "blur",
          }],
          apn: [{
            required: true,
            message: "请输入...",
            trigger: "blur",
          }, ],
          hotcountry: [{
            required: true,
            message: "请选择是否为热门国家",
          }],
          addgt: [{
            type: 'array',
            required: true,
            message: '请添加gt码',
            trigger: 'blur,change'
          }],
          mnc: [{
            required: true,
            message: '请输入MNC码',
          }, {
            pattern: /^[0-9]*$/,
            message: '请输入纯数字号码',
          }, {
            validator: checkUpdateMNC,
          }],
          operatorId: [{
            required: true,
            message: '请输入运营商名称',
          }, {
            validator: checkUpdateOperatorId,
          }],
          tadigRules: [{
            validator: checkUpdateTadig,
          }],
        },
        id: 0,
        mncStatusOptions: [{
            value: 1,
            label: '主用',
            color: '#2d8cf0' // 改为蓝色
          },
          {
            value: 2,
            label: '备用',
            color: '#19be6b' // 改为绿色
          },
          {
            value: 3,
            label: '未启用',
            color: '#ed4014'
          }
        ],
        activeOperators: [], // 存放有主用/备用的运营商
      }
    },
    created() {
      this.updateSupportNetType();
    },
    mounted() {
      this.goPageFirst(1)
      this.getSupplier()
      this.getOperators()
    },
    methods: {
      goPageFirst(page) {
        this.loading = true
        var _this = this
        let pageNum = page
        let pageSize = 10
        let country = this.country
        let continent = this.continent
        opsearch({
          pageNum,
          pageSize,
          country,
          continent
        }).then(res => {
          if (res.code == '0000') {
            _this.loading = false
            this.searchLoading = false
            this.page = page
            this.total = res.data.count
            this.talbedata = res.data.list
          }
        }).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.loading = false
          this.searchLoading = false
        })
      },
      goPage(page) {
        this.goPageFirst(page)
      },
      search() {
        this.searchLoading = true
        this.goPageFirst(1)
        this.currentPage = this.page
      },
      getGTcode(row) {
        this.gtList = row.gt
        this.model1 = true
      },
      viewOperators(row) {
        this.operatorList = row.mnc
        this.model2 = true
      },
      // 打开添加国家按钮
      address() {
        this.formmodel.picture = null
        // this.$refs.formmodel.resetFields()
        this.addmodel = true
      },
      getProviders(id) {
        this.hotCountry = id
      },
      // 添加国家
      addsave() {
        if (!this.formmodel.picture) {
          this.$Message.warning('请选择需要上传的文件')
          return
        } else {
          // 判断是否选择gt码或mnc组
          if (this.formmodel.GTList.length < 1) {
            this.$Message.warning('请添加至少一组gt码')
            return
          }
          if (this.formmodel.MNCList.length < 1) {
            this.$Message.warning('请添加至少一组mnc')
            return
          }

          // 检查是否有首选供应商
          if (!this.addPreSupplier) {
            this.$Message.warning('请勾选首选供应商')
            return
          }

          // 对supplier数据进行处理，使用深拷贝
          let mncList = JSON.parse(JSON.stringify(this.formmodel.MNCList)).map((mnc, index) => {
            // 获取第一行的supplierId列表
            const firstRowSupplierIds = this.formmodel.MNCList[0].suppliers.map(s => s.supplierId);

            // 创建新的suppliers数组，保持与第一行相同的supplierId
            let suppliers = firstRowSupplierIds.map((supplierId, i) => {
              return {
                supplierId: supplierId,
                isEnable: mnc.suppliers[i]?.status || 3
              };
            });

            // 过滤掉supplierId为null的对象
            suppliers = suppliers.filter(supplier => supplier.supplierId !== null);

            return {
              id: mnc.id,
              index: mnc.index,
              mnc: mnc.mnc,
              operatorId: mnc.operatorId,
              suppliers: suppliers
            };
          });

          // 检查首选供应商是否仍然存在
          const preferredSupplierExists = mncList.some(mnc =>
            mnc.suppliers.some(s => s.supplierId === this.addPreSupplier)
          );

          if (!preferredSupplierExists) {
            this.$Message.warning('请重新选择首选供应商')
            return
          }

          // 检查首选供应商是否有主用MNC
          let hasPreferredPrimary = false;
          let hasHCardPrimary = false;

          mncList.forEach(mcn => {
            mcn.suppliers.forEach(s => {
              // 检查首选供应商是否有主用MNC
              if (s.supplierId == this.addPreSupplier && s.isEnable == 1) {
                hasPreferredPrimary = true;
              }
              // 检查H卡是否有主用MNC
              if (s.supplierId === 'default' && s.isEnable == 1) {
                hasHCardPrimary = true;
              }
            })
          })

          if (!hasPreferredPrimary) {
            this.$Message.warning('首选供应商至少主用一个MNC')
            return
          }
          if (!hasHCardPrimary) {
            this.$Message.warning('H卡列至少主用一个MNC')
            return
          }
          this.$refs.formmodel.validate(valid => {
            if (valid) {

              const opreTadigRelations = this.formmodel.MNCList.map(mnc => ({
                id: mnc.id,
                index: mnc.index,
                mnc: mnc.mnc,
                operatorId: mnc.operatorId,
                tadig: mnc.tadig
              }));
              let formData = new FormData()
              formData.append('gt', JSON.stringify(this.formmodel.GTList))
              formData.append('mncSupplierMap', JSON.stringify(mncList))
              formData.append('preferredSupplier', this.addPreSupplier)
              formData.append('openRoaming', this.formmodel.openRoaming)
              formData.append('countryCn', this.formmodel.countryCn)
              formData.append('countryTw', this.formmodel.countryTw)
              formData.append('countryEn', this.formmodel.countryEn)
              formData.append('continentCn', this.formmodel.continentCn)
              formData.append('continentTw', this.formmodel.continentTw)
              formData.append('continentEn', this.formmodel.continentEn)
              formData.append('mcc', this.formmodel.mcc)
              formData.append('file', this.formmodel.picture)
              formData.append('hotCountry', this.hotCountry)
              formData.append('opreTadigRelations', JSON.stringify(opreTadigRelations))

              // 转换supportNetType的格式
              const formattedNetworkType = this.formmodel.supportNetType.map(item => ({
                operatorId: item.operatorId,
                supportNetType: item.supportNetType || []
              }));
              formData.append('supportNetType', JSON.stringify(formattedNetworkType))
              this.addLoading = true
              addop(formData).then(res => {
                if (res && res.code == '0000') {
                  this.$Notice.success({
                    title: '操作提醒',
                    desc: '添加成功！'
                  })
                  this.goPageFirst(1)
                  this.currentPage = 1
                  this.addmodel = false
                } else {
                  throw res
                }
              }).catch((err) => {
                console.log(err)
              }).finally(() => {
                this.addLoading = false
                this.cancelModal()
              })
            }
          })
        }
      },
      // 修改国家
      updatesave() {
        if (!this.upform.imagePath) {
          this.$Message.warning('请选择需要上传的文件')
          return
        } else {
          // 判断是否选择gt码或mnc组
          if (this.upform.gt.length < 1) {
            this.$Message.warning('请添加至少一组gt码')
            return
          }
          if (this.upform.mnc.length < 1) {
            this.$Message.warning('请添加至少一组mnc')
            return
          }

          // 检查是否有首选供应商
          if (!this.upadatePreSupplier) {
            this.$Message.warning('请勾选首选供应商')
            return
          }

          // 检查首选供应商是否被正确勾选
          const isPreferredSupplierSelected = this.upform.mnc.some(mnc =>
            mnc.suppliers.some(supplier =>
              supplier.supplierId === this.upadatePreSupplier && supplier.choose === true
            )
          );

          if (!isPreferredSupplierSelected) {
            this.$Message.warning('请重新选择首选供应商')
            return
          }

          // 对supplier数据进行处理，使用深拷贝
          let mncList = JSON.parse(JSON.stringify(this.upform.mnc)).map((mnc, index) => {
            // 获取第一行的supplierId列表
            const firstRowSupplierIds = this.upform.mnc[0].suppliers.map(s => s.supplierId);

            // 创建新的suppliers数组，保持与第一行相同的supplierId
            let suppliers = firstRowSupplierIds.map((supplierId, i) => {
              return {
                supplierId: supplierId,
                isEnable: mnc.suppliers[i]?.status || 3
              };
            });

            // 过滤掉supplierId为null的对象
            suppliers = suppliers.filter(supplier => supplier.supplierId !== null);

            return {
              id: mnc.id,
              index: mnc.index,
              mnc: mnc.mnc,
              operatorId: mnc.operatorId,
              suppliers: suppliers
            };
          });

          // 检查首选供应商是否有主用MNC
          let hasPreferredPrimary = false;
          let hasHCardPrimary = false;

          mncList.forEach(mcn => {
            mcn.suppliers.forEach(s => {
              // 检查首选供应商是否有主用MNC
              if (s.supplierId === this.upadatePreSupplier && s.isEnable === 1) {
                hasPreferredPrimary = true;
              }
              // 检查H卡是否有主用MNC
              if (s.supplierId === 'default' && s.isEnable === 1) {
                hasHCardPrimary = true;
              }
            })
          })

          if (!hasPreferredPrimary) {
            this.$Message.warning('首选供应商至少主用一个MNC')
            return
          }
          if (!hasHCardPrimary) {
            this.$Message.warning('H卡列至少主用一个MNC')
            return
          }

          // 在提交前先触发表单校验
          this.$refs.upform.validate(valid => {
            if (valid) {
              const opreTadigRelations = this.upform.mnc.map(mnc => ({
                id: mnc.id,
                index: mnc.index,
                mnc: mnc.mnc,
                operatorId: mnc.operatorId,
                tadig: mnc.tadig
              }));

              let formData = new FormData()
              formData.append('mncSupplierMap', JSON.stringify(mncList))
              formData.append('preferredSupplier', this.upadatePreSupplier)
              formData.append('id', this.countryId)
              formData.append('gt', JSON.stringify(this.upform.gt))
              formData.append('openRoaming', this.upform.openRoaming)
              formData.append('countryCn', this.upform.countryCn)
              formData.append('countryTw', this.upform.countryTw)
              formData.append('countryEn', this.upform.countryEn)
              formData.append('continentCn', this.upform.continentCn)
              formData.append('continentTw', this.upform.continentTw)
              formData.append('continentEn', this.upform.continentEn)
              formData.append('mcc', this.upform.mcc)
              formData.append('hotCountry', this.hotCountry)
              formData.append('opreTadigRelations', JSON.stringify(opreTadigRelations))

              // 转换supportNetType的格式
              const formattedNetworkType = this.upform.supportNetType.map(item => ({
                operatorId: item.operatorId,
                supportNetType: item.supportNetType || []
              }));
              formData.append('supportNetType', JSON.stringify(formattedNetworkType))

              this.updateLoading = true
              if (this.pictureflag === true) {
                formData.append('file', this.upform.imagePath)
              }
              updateop(formData).then(res => {
                if (res && res.code == '0000') {
                  this.$Notice.success({
                    title: '操作提醒',
                    desc: '修改成功！'
                  })
                  this.upmodel = false
                  this.pictureflag = false
                  this.goPageFirst(1)
                  this.currentPage = 1
                } else {
                  throw res
                }
              }).catch((err) => {
                console.log(err)
              }).finally(() => {
                this.updateLoading = false
                this.updatecancelModal()
              })
            }
          })
        }
      },
      //清空首选供应商
      clearSupplierId(mncIndex, sindex) {
        const currentSupplier = this.formmodel.MNCList[mncIndex].suppliers[sindex].supplierId;
        const currentSupplier2 = this.upform.mnc[mncIndex].suppliers[sindex].supplierId;

        // 清除供应商ID
        this.formmodel.MNCList[mncIndex].suppliers[sindex].supplierId = null;
        this.upform.mnc[mncIndex].suppliers[sindex].supplierId = null;

        // 清除对应V卡的选中状态
        this.formmodel.MNCList[mncIndex].suppliers[sindex].choose = false;
        this.upform.mnc[mncIndex].suppliers[sindex].choose = false;

        // 只有当清除的是当前首选供应商时，才清除首选供应商状态
        if (this.addPreSupplier === currentSupplier) {
          this.addPreSupplier = null;
          this.supplierIdlx = null;
          // 清除所有行的首选供应商状态
          this.formmodel.MNCList.forEach(mnc => {
            mnc.suppliers.forEach(supplier => {
              this.$set(supplier, 'choose', false);
            });
          });
          // 实时更新网络类型
          this.updateSupportNetType();
        }
        if (this.upadatePreSupplier === currentSupplier2) {
          this.upadatePreSupplier = null;
          this.supplierIdlxs = null;
          // 清除所有行的首选供应商状态
          this.upform.mnc.forEach(mnc => {
            mnc.suppliers.forEach(supplier => {
              this.$set(supplier, 'choose', false);
            });
          });
          // 实时更新网络类型
          this.updateSupportNetTypeUpdate();
        }

        // 更新 Radio 组件的选中状态
        this.$nextTick(() => {
          if (this.addPreSupplier === null) {
            this.supplierIdlx = null;
          }
          if (this.upadatePreSupplier === null) {
            this.supplierIdlxs = null;
          }
        });
      },
      cancelModal() {
        this.addmodel = false
        // 重置表单数据
        this.formmodel = {
          GTList: [{
            index: 0,
            gtcode: "",
          }],
          MNCList: [{
            id: 0,
            index: 0,
            mnc: "",
            operatorId: "",
            tadig: "",
            suppliers: [{
              supplierId: 'default',
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }, {
              supplierId: null,
              status: 3,
            }],
          }],
          supplierId: "",
          openRoaming: '',
          countryCn: '',
          countryTw: '',
          countryEn: '',
          continentCn: '',
          continentTw: '',
          continentEn: '',
          mcc: '',
          gtcode: '',
          mnc: '',
          operatorName: '',
          hotcountry: '',
          supportNetType: [{
            operatorId: '',
            supportNetType: [],
          }]
        }
        this.pictureUrl = ''
        this.deleteLoading = false
        this.pictureflag = false
        this.hotCountry = ""
        this.countryId = ''
        this.supplierIdlx = ''
        this.addPreSupplier = ''
        this.index = 0
        this.id = 0
        this.$refs.formmodel.resetFields()
      },
      updatecancelModal() {
        this.upmodel = false
        this.$refs.upform.resetFields()
        this.goPageFirst(1)
        this.currentPage = 1
        this.upform.picture = null
        this.pictureUrl = ''
        this.pictureflag = false
        this.upform.openRoaming = ''
        this.upform.countryCn = ''
        this.upform.countryTw = ''
        this.upform.countryEn = ''
        this.upform.continentCn = ''
        this.upform.continentTw = ''
        this.upform.continentEn = ''
        this.upform.mcc = ''
        this.hotCountry = '' //热门国家
        this.countryId = ''
        this.supplierIdlxs = '' //首先供应商
        this.upadatePreSupplier = ""
        this.upform.supplierId = ''
        this.upform.gt.forEach((i) => {
          i.index = 0
          i.gtcode = ""
        })
        this.upform.mnc.forEach((i) => {
          i.index = 0
          i.id = 0
          i.operatorId = ""
          i.mnc = ""
          i.suppliers.forEach((item) => {
            item.choose = false
            item.supplierId = ""
          })
        })
      },
      // 添加gt码
      addgt() {
        this.index++;
        this.formmodel.GTList.push({
          index: this.index,
          gtcode: "",
        });
      },
      // 添加gt码
      upformaddgt() {
        this.index++;
        this.upform.gt.push({
          index: this.index,
          gtCode: "",
        });
      },
      // 删除gt码
      upformremovegt(index) {
        if (this.upform.gt.length > 1) {
          this.upform.gt.splice(index, 1);
          this.index--;
        } else {
          this.$Message.warning('至少保留一个GT码');
        }
      },
      // 删除gt码
      removegt(index) {
        if (this.formmodel.GTList.length > 1) {
          this.formmodel.GTList.splice(index, 1);
          this.index--;
        } else {
          this.$Message.warning('至少保留一个GT码');
        }
      },
      noRepeat(e) {
        this.formmodel.supplierId = e;

        // 先清除所有供应商的choose状态
        this.formmodel.MNCList.forEach(mnc => {
          mnc.suppliers.forEach(supplier => {
            this.$set(supplier, 'choose', false);
          });
        });

        // 设置当前选中的供应商
        if (e) {
          // 设置首选供应商
          this.addPreSupplier = e;

          // 找到选中供应商在所有行中的位置
          this.formmodel.MNCList.forEach(mnc => {
            mnc.suppliers.forEach((supplier, index) => {
              if (supplier.supplierId === e) {
                this.$set(supplier, 'choose', true);
              }
            });
          });
        } else {
          this.addPreSupplier = null;
        }

        // 实时更新网络类型
        this.updateSupportNetType();
      },
      noRepeats(e) {
        this.upform.supplierId = e;

        // 清除所有供应商的choose状态
        this.upform.mnc.forEach(mnc => {
          mnc.suppliers.forEach(supplier => {
            this.$set(supplier, 'choose', false);
          });
        });

        // 如果选择了供应商，设置其choose状态为true
        if (e) {
          this.upform.mnc.forEach(mnc => {
            mnc.suppliers.forEach(supplier => {
              if (supplier.supplierId === e) {
                this.$set(supplier, 'choose', true);
              }
            });
          });
          this.upadatePreSupplier = e;
        } else {
          this.upadatePreSupplier = null;
        }

        // 实时更新网络类型
        this.$nextTick(() => {
          this.updateSupportNetTypeUpdate();
        });
      },
      //获取资源供应商
      getSupplier(supplierId) {
        this.supplierIdlx = supplierId
        this.supplierIdlxs = supplierId
        this.upadatePreSupplier = supplierId
        this.addPreSupplier = supplierId
        let pageNum = 1
        let pageSize = -1
        supplier({
          pageNum,
          pageSize,
        }).then(res => {
          if (res.code == '0000') {
            this.providers = res.data
          }
        }).catch((err) => {
          console.error(err)
        })
      },
      // 添加MNC
      addmnc() {
        this.index++;
        this.id++;
        let suppliers = [{
            supplierId: 'default',
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          }
        ];
        this.formmodel.MNCList.push({
          id: this.id,
          index: this.index,
          mnc: "",
          operatorId: "",
          suppliers: suppliers
        });
        // 实时更新网络类型
        this.updateSupportNetType();
      },
      // 添加MNC
      upformaddmnc() {
        this.index++;
        this.id++;
        let suppliers = [{
            supplierId: 'default',
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          },
          {
            supplierId: null,
            status: 3,
          }
        ];
        this.upform.mnc.push({
          id: this.id,
          index: this.index,
          mnc: "",
          operatorId: "",
          suppliers: suppliers
        });
        // 实时更新网络类型
        this.updateSupportNetTypeUpdate();
      },
      // 删除mnc
      removemnc(index) {
        if (this.formmodel.MNCList.length > 1) {
          this.formmodel.MNCList.splice(index, 1);
          this.index--;
          this.id--;
          // 立即更新网络类型
          this.updateSupportNetType();
        } else {
          this.$Message.warning('至少保留一个MNC');
        }
      },
      // 删除mnc
      upformremovemnc(index) {
        if (this.upform.mnc.length > 1) {
          this.upform.mnc.splice(index, 1);
          this.index--;
          this.id--;
          // 立即更新网络类型
          this.updateSupportNetTypeUpdate();
        } else {
          this.$Message.warning('至少保留一个MNC');
        }
      },
      // 点击修改国家
      updaterow(row) {
        // 使用 Vue.set 来保持响应式
        this.$set(this, 'upform', {
          ...row,
          supportNetType: [],
          supportNetTypeDTOS: row.supportNetTypeDTOS
        });

        this.supplierIdlxs = row.preferredSupplierId;
        this.upadatePreSupplier = row.preferredSupplierId;
        // 添加这一行,确保choose状态被正确设置
        this.noRepeats(row.preferredSupplierId);
        this.hotCountry = parseInt(row.hotCountry) === "" ? 1 : parseInt(row.hotCountry);
        this.upform.supplierId = row.preferredSupplierId;
        this.countryId = row.id;
        this.pictureUrl = row.imagePath;
        this.upform.imagePath = row.imagePath;

        // 处理网络类型回显
        if (row.supportNetTypeDTOS && row.supportNetTypeDTOS.length > 0) {
          this.upform.supportNetType = row.supportNetTypeDTOS.map(item => {
            let supportNetType = [];
            try {
              // 确保supportNetType是数组
              supportNetType = Array.isArray(item.supportNetType) ?
                item.supportNetType :
                JSON.parse(item.supportNetType);
            } catch (e) {
              console.error('解析supportNetType失败:', e);
            }
            return {
              operatorId: item.operatorId,
              supportNetType: supportNetType
            };
          });
        }

        if (row.gt == null || row.gt.length < 1) {
          this.upform.gt = [{
            index: this.index,
            gtCode: ""
          }];
        }

        // 处理MNC数据
        row.mnc.forEach((item, index) => {
          this.$set(this.upform.mnc, index, {
            ...item,
            suppliers: []
          });

          // 处理H卡
          const hCard = item.suppliers.find(s => s.supplierId === 'default');
          this.$set(this.upform.mnc[index].suppliers, 0, {
            supplierId: 'default',
            status: hCard?.isEnable ? Number(hCard.isEnable) : 3,
            choose: 'default' === row.preferredSupplierId // 添加choose状态的设置
          });

          // 处理V卡
          const vCards = item.suppliers.filter(s => s.supplierId !== 'default');
          vCards.forEach((value, sindex) => {
            this.$set(this.upform.mnc[index].suppliers, sindex + 1, {
              supplierId: value.supplierId,
              status: value.isEnable ? Number(value.isEnable) : 3,
              choose: value.supplierId === row.preferredSupplierId // 根据是否是首选供应商设置 choose 状态
            });
          });

          // 补充剩余的suppliers
          const remainingCount = 10 - this.upform.mnc[index].suppliers.length;
          for (let i = 0; i < remainingCount; i++) {
            this.$set(this.upform.mnc[index].suppliers, this.upform.mnc[index].suppliers.length, {
              supplierId: null,
              status: 3,
              choose: false // 初始化 choose 状态为 false
            });
          }
        });

        this.index = this.upform.mnc.length;
        this.upmodel = true;
      },
      //删除
      remove(row) {
        this.$Modal.confirm({
          title: '确认删除该项？',
          onOk: () => {
            let mcc = row.mcc
            deleteop({
              mcc
            }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提醒',
                  desc: '删除成功！'
                })
                this.deletemodel = false
                this.goPageFirst(1)
                this.currentPage = 1
              } else {
                throw res
              }
            }).catch((err) => {
              console.log(err)
            }).finally(() => {
              this.loading = false
            })

          }
        });
      },
      handlePreview(file) {
        this.formmodel.picture = file;
      },
      //上传图片赋值
      handleUpload(file) {
        if (!/^.+(\.jpeg|\.png|\.jpg)$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式不正确',
            desc: '文件 ' + file.name + ' 格式不正确，请上传jpeg,png,jpg格式文件。'
          })
        } else {
          this.formmodel.picture = file;
          this.upform.imagePath = file;
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => {
            const _base64 = reader.result;
            this.pictureUrl = _base64; //将_base64赋值给图片的src，实现图片预览
          }
          this.formmodel.file = file
          this.upform.imagePath = file;
          this.pictureflag = true
        }

        return false;
      },
      //上传图片删除
      cancelSelected() {
        this.formmodel.picture = null;
        this.upform.imagePath = null;
        this.pictureUrl = '';
      },
      //获取运营商
      getOperators() {
        getOperators({}).then(res => {
          if (res.code === '0000') {
            this.operatorIdList = res.data
          }
        }).catch((err) => {
          console.log(err)
        })
      },
      validateMncStatus() {
        let hasPrimary = false;
        let hasPrimaryH = false;
        this.activeOperators = [];

        // 检查是否有主用MNC
        this.form.mncs.forEach(mnc => {
          if (mnc.status === 1) {
            hasPrimary = true;
            this.activeOperators.push(mnc.operator_id);
          }
          if (mnc.status === 1 || mnc.status === 2) {
            this.activeOperators.push(mnc.operator_id);
          }
          if (mnc.card_type === 'h' && mnc.status === 1) {
            hasPrimaryH = true;
          }
        });

        if (!hasPrimary) {
          this.$Message.error('至少需要设置一个主用MNC');
          return false;
        }

        if (!hasPrimaryH) {
          this.$Message.error('H卡中至少需要设置一个主用MNC');
          return false;
        }

        return true;
      },

      changeSupplierStatus(indexMCN, indexSupplier, value) {
        // 更新状态
        this.formmodel.MNCList[indexMCN].suppliers[indexSupplier].status = value;
        // 立即更新网络类型
        this.updateSupportNetType();
      },

      updateSupportNetType() {
        // 获取第一行的供应商ID列表和状态
        const firstRowSuppliers = this.formmodel.MNCList[0]?.suppliers || [];

        // 从第二行开始遍历,确保每行都有相同的供应商结构
        for(let i = 1; i < this.formmodel.MNCList.length; i++) {
          const mncItem = this.formmodel.MNCList[i];
          // 创建新的suppliers数组,只复制supplierId和choose状态
          let suppliers = firstRowSuppliers.map((supplier, index) => {
            return {
              supplierId: supplier.supplierId,
              status: mncItem.suppliers[index]?.status || 3, // 保持原有的status值
              choose: supplier.choose
            };
          });
          // 使用Vue.set确保响应式更新
          this.$set(mncItem, 'suppliers', suppliers);
        }

        // 收集所有符合条件的运营商ID
        const enabledOperators = new Set();
        this.formmodel.MNCList.forEach(mncItem => {
          if (!mncItem.operatorId) return;

          // 检查该运营商是否有任何供应商处于主用/备用状态
          const hasActiveSupplier = mncItem.suppliers.some(supplier => {
            // 检查是否为主用/备用状态
            const isActiveStatus = [1, 2].includes(supplier.status);
            // 检查该供应商是否为首选供应商
            const isPreferredSupplier = supplier.supplierId === this.addPreSupplier;

            return isActiveStatus && isPreferredSupplier;
          });

          if (hasActiveSupplier) {
            enabledOperators.add(mncItem.operatorId);
          }
        });

        // 获取当前已存在的网络类型配置
        const existingNetworkTypes = new Map();
        this.formmodel.supportNetType.forEach(item => {
          if (item.supportNetType && item.supportNetType.length > 0) {
            existingNetworkTypes.set(item.operatorId, item.supportNetType);
          }
        });

        // 更新supportNetType
        const newSupportNetType = Array.from(enabledOperators).map(operatorId => {
          return {
            operatorId: operatorId,
            supportNetType: existingNetworkTypes.get(operatorId) || []
          };
        });

        // 使用Vue.set更新整个数组以保持响应式
        this.$set(this.formmodel, 'supportNetType', newSupportNetType);
      },

      changeSupplierStatusUpdate(indexMCN, indexSupplier, value) {
        // 更新状态
        this.$set(this.upform.mnc[indexMCN].suppliers[indexSupplier], 'status', value);

        // 立即更新网络类型
        this.updateSupportNetTypeUpdate();
      },

      updateSupportNetTypeUpdate() {
        // 获取第一行的供应商ID列表
        const firstRowSupplierIds = this.upform.mnc[0]?.suppliers.map(s => s.supplierId) || [];

        // 从第二行开始遍历,确保每行都有相同的供应商结构
        for(let i = 1; i < this.upform.mnc.length; i++) {
          const mncItem = this.upform.mnc[i];
          // 创建新的suppliers数组,保持与第一行相同的supplierId
          let suppliers = firstRowSupplierIds.map((supplierId, index) => {
            return {
              supplierId: supplierId,
              status: mncItem.suppliers[index]?.status || 3,
              choose: mncItem.suppliers[index]?.choose || false
            };
          });
          // 使用Vue.set确保响应式更新
          this.$set(mncItem, 'suppliers', suppliers);
        }

        // 收集所有符合条件的运营商ID
        const enabledOperators = new Set();
        this.upform.mnc.forEach(mncItem => {
          const isOperatorEnabled = mncItem.suppliers.some(supplier => {
            // 检查是否为主用/备用状态
            const isActiveStatus = [1, 2].includes(supplier.status);
            // 检查该供应商是否为首选供应商
            const isPreferredSupplier = supplier.supplierId === this.upadatePreSupplier;

            return isActiveStatus && isPreferredSupplier && mncItem.operatorId;
          });

          if (isOperatorEnabled && mncItem.operatorId) {
            enabledOperators.add(mncItem.operatorId);
          }
        });

        // 获取当前已存在的网络类型配置
        const existingNetworkTypes = new Map();

        // 首先从supportNetTypeDTOS中获取原有的网络类型配置
        if (this.upform.supportNetTypeDTOS && this.upform.supportNetTypeDTOS.length > 0) {
          this.upform.supportNetTypeDTOS.forEach(item => {
            try {
              const supportNetType = JSON.parse(item.supportNetType);
              existingNetworkTypes.set(item.operatorId, supportNetType);
            } catch (e) {
              console.error('解析supportNetType失败:', e);
            }
          });
        }

        // 然后从当前的supportNetType中获取已修改的网络类型配置
        this.upform.supportNetType.forEach(item => {
          if (item.supportNetType && item.supportNetType.length > 0) {
            existingNetworkTypes.set(item.operatorId, item.supportNetType);
          }
        });

        // 更新supportNetType
        const newSupportNetType = Array.from(enabledOperators).map(operatorId => {
          return {
            operatorId: operatorId,
            supportNetType: existingNetworkTypes.get(operatorId) || []
          };
        });

        // 使用Vue.set更新整个数组以保持响应式
        this.$set(this.upform, 'supportNetType', newSupportNetType);
      },

      handleNetworkTypeChange(value, index) {
        if (!this.upform.supportNetType || !this.upform.supportNetType[index]) {
          return;
        }

        this.$set(this.upform.supportNetType[index], 'supportNetType', value);

        this.$nextTick(() => {
          this.$refs.upform.validateField(`supportNetType.${index}.supportNetType`);
        });
      },

      // 添加新的方法处理供应商选择变化
      handleSupplierChange(value, index) {
        // 如果当前修改的是首选供应商
        if (this.formmodel.MNCList[0].suppliers[index].choose) {
          // 更新首选供应商的值
          this.addPreSupplier = value;

          // 同步更新其他行相同位置的供应商ID
          for(let i = 1; i < this.formmodel.MNCList.length; i++) {
            this.$set(this.formmodel.MNCList[i].suppliers[index], 'supplierId', value);
          }
        }

        // 更新网络类型
        this.$nextTick(() => {
          this.updateSupportNetType();
        });
      },
      // 添加新的方法处理修改modal中的供应商选择变化
      handleSupplierChangeUpdate(value, index) {
        // 如果当前修改的是首选供应商
        if (this.upform.mnc[0].suppliers[index].choose) {
          // 更新首选供应商的值
          this.upadatePreSupplier = value;

          // 同步更新其他行相同位置的供应商ID
          for(let i = 1; i < this.upform.mnc.length; i++) {
            this.$set(this.upform.mnc[i].suppliers[index], 'supplierId', value);
          }
        }

        // 更新网络类型
        this.$nextTick(() => {
          this.updateSupportNetTypeUpdate();
        });
      },
    }
  }
</script>

<style lang="less" scoped>
  .demo-upload-list {
    display: inline-block;
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    border: 1px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
    margin-right: 4px;
  }

  .demo-upload-list img {
    width: 100%;
    height: 100%;
  }

  .demo-upload-list-cover {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, .6);
  }

  .demo-upload-list:hover .demo-upload-list-cover {
    display: block;
  }

  .demo-upload-list-cover i {
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    margin: 0 2px;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .ivu-modal-body {
    overflow-y: auto;
    max-height: 70vh;
  }

  .ivu-form {
    width: 100%;
  }

  .ivu-form-item {
    margin-bottom: 24px;
  }

  .ivu-form-item-label {
    text-align: left;
    padding-right: 10px;
  }

  .ivu-form-item-content {
    margin-left: 0;
  }

  .section-box {
    border: 2px dashed rgba(0, 0, 0, 0.1);
    margin: 20px 0;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.02);
    padding: 16px 0;
    min-width: 2000px;
  }

  .gt-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .mnc-table {
    width: 100%;
    white-space: nowrap;
  }

  .mnc-row {
    display: flex;
    margin-bottom: 20px;
    white-space: nowrap;
  }

  .mnc-row.header {
    font-weight: bold;
  }

  .mnc-cell {
    flex-shrink: 0;
    padding: 0 10px;
    display: flex;
    flex-direction: column;
    /* 改为纵向排列 */
    justify-content: flex-start;
    /* 顶部对齐 */
  }

  .mnc-cell .ivu-form-item {
    margin-bottom: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    /* 改为纵向排列 */
    align-items: flex-start;
    /* 左对齐 */
  }

  .mnc-cell .ivu-form-item-label {
    width: auto;
    text-align: right;
    padding-right: 10px;
    background: none;
    white-space: nowrap;
    line-height: 32px;
  }

  .mnc-cell .ivu-form-item-content {
    margin-left: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    /* 改为纵向排列 */
    align-items: center;
    /* 水平居中 */
  }

  /* MNC区域单元格宽度 */
  .mnc-cell:nth-child(1) {
    width: 700px;
  }

  /* MNC 运营商名称 TADIG */
  .mnc-cell:nth-child(2) {
    width: 120px;
  }

  /* 删除按钮 */
  .mnc-cell:nth-child(3) {
    width: 80px;
  }

  /* H卡 */
  .mnc-cell:nth-child(n+6) {
    width: 120px;
  }

  /* V1-V9卡 */

  /* 下拉框样式 */
  .mnc-cell .ivu-select {
    width: 100%;
  }

  .mnc-cell .ivu-select-selection {
    display: flex;
    align-items: center;
    height: 32px;
  }

  .mnc-cell .ivu-select-arrow {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
  }

  /* 状态选择器样式 */
  .mnc-cell .ivu-select-single .ivu-select-selection {
    height: 32px;
    line-height: 32px;
  }

  .mnc-cell .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
  .mnc-cell .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
    height: 32px;
    line-height: 32px;
  }

  /* 删除按钮样式 */
  .mnc-cell .ivu-icon {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .status-1 {
    color: #2d8cf0 !important;
  }

  .status-2 {
    color: #19be6b !important;
  }

  .status-3 {
    color: #ed4014 !important;
  }

  .status-4 {
    color: #c5c8ce !important;
  }

  /* 状态选择器样式 */
  .ivu-select-selection {
    &.status-1 {
      border-color: #2d8cf0 !important;
      color: #2d8cf0 !important;
    }

    &.status-2 {
      border-color: #19be6b !important;
      color: #19be6b !important;
    }

    &.status-3 {
      border-color: #ed4014 !important;
      color: #ed4014 !important;
    }

    &.status-4 {
      border-color: #c5c8ce !important;
      color: #c5c8ce !important;
    }
  }

  /* 下拉框选项样式 */
  .ivu-select-dropdown {
    .ivu-select-item {
      &.status-1 {
        color: #2d8cf0 !important;
      }

      &.status-2 {
        color: #19be6b !important;
      }

      &.status-3 {
        color: #ed4014 !important;
      }

      &.status-4 {
        color: #c5c8ce !important;
      }
    }
  }

  /* 状态文本样式 */
  .status-1 {
    color: #2d8cf0 !important;
  }

  .status-2 {
    color: #19be6b !important;
  }

  .status-3 {
    color: #ed4014 !important;
  }

  /* 添加全局样式，确保Tooltip气泡显示在最上层 */
  :global(.ivu-tooltip-popper) {
    z-index: 10000 !important;
  }
</style>
