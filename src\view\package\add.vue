<template>
	<Card style="width: 100%;padding: 16px;">
		<div style="display: flex;justify-content: center;margin: 20px 0;">
			<Form ref="formObj" :model="formObj" :label-width="150" :rules="ruleAddValidate">
				<Row>
					<Col span="24">
					<FormItem label="套餐名称(简中)" prop="nameCn">
						<Input v-model="formObj.nameCn" :maxlength="100" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入套餐名称(简中)"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24">
					<FormItem label="套餐名称(繁中)" prop="nameTw">
						<Input v-model="formObj.nameTw" :maxlength="100" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入套餐名稱(繁中)"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24">
					<FormItem label="套餐名称(英文)" prop="nameEn">
						<Input v-model="formObj.nameEn" :maxlength="100" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="Please enter package name (EN)"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24">
					<FormItem label="套餐描述(简中)" prop="descCn">
						<Input v-model="formObj.descCn" :maxlength="4000" :readonly="typeFlag=='Info'" type="textarea" :rows="3"
						 placeholder="请输入套餐描述(简中)"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24">
					<FormItem label="套餐描述(繁中)" prop="descTw">
						<Input v-model="formObj.descTw" :maxlength="4000" :readonly="typeFlag=='Info'" type="textarea" :rows="3"
						 placeholder="请输入套餐描述(繁中)"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24">
					<FormItem label="套餐描述(英文)" prop="descEn">
						<Input v-model="formObj.descEn" :maxlength="4000" :readonly="typeFlag=='Info'" type="textarea" :rows="3"
						 placeholder="Please enter package description (EN)"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="周期类型" prop="periodUnit">
						<Select v-model="formObj.periodUnit" class="inputSty" placeholder="请选择周期类型" :disabled="typeFlag=='Info'"
						 :clearable="typeFlag!='Info'">
							<Option v-for="item in periodUnitList" :value="item.value" :key="item.value">{{ item.label }}</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="持续周期" prop="keepPeriod">
						<Input v-model="formObj.keepPeriod" :maxlength="11" class="inputSty" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入持续周期"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="套餐购买有效期(天)" prop="effectiveDay">
						<Input v-model="formObj.effectiveDay" :maxlength="11" class="inputSty" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入套餐购买有效期(天)"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="套餐封面" prop="picture">
						<div style='display: flex;flex-direction: column;width: 200px;height: 100px;' v-if="typeFlag!='Info'">
							<Upload type="drag" accept="image/*" action="#" ref="upload" :before-upload="handleUpload" :show-upload-list="false"
							 v-if="pictureUrl == ''">
								<div style="padding: 20px;height: 100px; width: 100%;">
									<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
									<p>上传套餐封面图片</p>
								</div>
							</Upload>
							<div style='display: flex;flex-direction: column;width: 100%;height: 100%;border: 1px dashed #dcdee2;border-radius: 4px;position: relative;'
							 v-else>
								<Icon type="md-close-circle" color="#ff3300" size="22" class="mediaShowDelSty" @click="cancelSelected()"></Icon>
								<img style="object-fit: contain;" :src="pictureUrl" width="100%" height="100%" @click="pictureShowFlag=true"></img>
							</div>
						</div>
						<div style='display: flex;flex-direction: column;width: 200px;height: 100px;' v-else>
							<div style='display: flex;flex-direction: column;width: 100%;height: 100%;border: 1px dashed #dcdee2;border-radius: 4px;position: relative;'>
								<img style="object-fit: contain;" :src="pictureUrl" width="100%" height="100%" @click="pictureShowFlag=true"></img>
							</div>
						</div>
					</FormItem>
					</Col>
					<Col span="12" v-if="typeFlag!='Add'">
					<FormItem label="套餐状态" prop="status">
						<Select v-model="formObj.status" class="inputSty" placeholder="请选择套餐状态" :disabled="true">
							<Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="终端厂商套餐" prop="isTerminal">
						<Select v-model="formObj.isTerminal" @on-change="isTerminalChange" class="inputSty" placeholder="是否为终端厂商套餐"
						 :disabled="typeFlag=='Info'" :clearable="typeFlag!='Info'">
							<Option value="1">是</Option>
							<Option value="2">否</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="12" v-if="formObj.isTerminal=='1'">
					<FormItem label="选择厂商" prop="corpId">
						<Select placeholder="请选择厂商" v-model="formObj.corpId" :disabled="typeFlag=='Info'" :clearable="typeFlag!='Info'">
							<Option v-for="(item,index) in corpIdList" :value="item.corpId" :key="index">{{ item.corpName }}</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="促销套餐" prop="isPromotion">
						<Select v-model="formObj.isPromotion" class="inputSty" placeholder="是否为促销套餐" :disabled="typeFlag=='Info'"
						 :clearable="typeFlag!='Info'">
							<Option value="1">是</Option>
							<Option value="2">否</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="促销限购份数" v-if="formObj.isPromotion=='1'" prop="saleLimit">
						<Input v-model="formObj.saleLimit" :maxlength="11" class="inputSty" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入促销限购份数"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="流量限制类型" prop="flowLimitType">
						<Select v-model="formObj.flowLimitType" class="inputSty" placeholder="请选择流量限制类型" :disabled="typeFlag=='Info'"
						 :clearable="typeFlag!='Info'" @on-change="getcontrolLogic">
							<Option :value="1">周期内限量</Option>
							<Option :value="2">按周期类型重置</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="达量后控制逻辑" prop="controlLogic">
						<Select v-model="formObj.controlLogic" class="inputSty" placeholder="请选择达量后控制逻辑" :disabled="typeFlag=='Info'"
						 :clearable="formObj.flowLimitType!=2 && !(formObj.isTerminal==='1'&&formObj.flowLimitType===1)">
							<Option v-if="!(formObj.isTerminal==='1'&&formObj.flowLimitType===1) " :value="1">达量限速</Option>
							<Option v-if="formObj.flowLimitType===1" :value="2">达量释放</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<div v-show="formObj.isTerminal == '1'">
					<Row>
					<Col span="12">
					<FormItem label="流量上限" prop="flowLimitSum">
						<Input v-model="formObj.flowLimitSum" :maxlength="8" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入流量上限">
						<Select slot="append" v-model="flowLimitUnit" :disabled="typeFlag=='Info'" style="width: 80px;">
							<Option value="1">GB</Option>
							<Option value="2">MB</Option>
						</Select>
						</Input>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="签约业务(高速)" prop="signBizId">
						<Input v-model="formObj.signBizId" :maxlength="50" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="签约业务(高速)">
						</Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="签约业务(低速)" prop="limitSignBizId">
						<Input v-model="formObj.limitSignBizId" :maxlength="50" class="inputSty" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入签约业务(低速)"></Input>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="签约业务(限速)" prop="slowSignBizId">
						<Input v-model="formObj.slowSignBizId" :maxlength="50" class="inputSty" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入签约业务(限速)"></Input>
					</FormItem>
					</Col>
				</Row>
				</div>
				<Row v-show="formObj.isTerminal == '2'">
					<Col span="12">
					<FormItem label="是否支持热点" prop="isSupportedHotspots">
						<Select v-model="formObj.isSupportedHotspots" class="inputSty" placeholder="请选择是否支持热点" :disabled="typeFlag=='Info'"
							:clearable="typeFlag!='Info'" @on-change="getcountryList(formObj.isSupportedHotspots)"
						>
							<Option value="1">是</Option>
							<Option value="2">否</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span='12'>
					<FormItem label="允许订购开始时间" prop="startTime">
						<DatePicker type="datetime" format="yyyy/MM/dd HH:mm:ss" class="inputSty" placeholder="请选择开始时间" :disabled="typeFlag=='Info'"
						 :clearable="typeFlag!='Info'" v-model="formObj.startTime" @on-change="changeTime"></DatePicker>
					</FormItem>
					</Col>
					<Col span='12'>
					<FormItem label="允许订购结束时间" prop="endTime">
						<DatePicker type="datetime" format="yyyy/MM/dd HH:mm:ss" class="inputSty" placeholder="请选择结束时间" :disabled="typeFlag=='Info'"
						 :clearable="typeFlag!='Info'" v-model="formObj.endTime" @on-change="changeTime"></DatePicker>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="计费激活流量限额" prop="billFlowLimit">
						<Input v-model="formObj.billFlowLimit" :maxlength="11" class="inputSty" :readonly="typeFlag=='Info'" placeholder="请输入计费激活流量限额">
						<span slot="append">MB</span>
						</Input>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="套餐价格(人民币)" prop="cny">
						<Input v-model="formObj.cny" :maxlength="13" class="inputSty" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入套餐价格(人民币)"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="套餐价格(港币)" prop="hkd">
						<Input v-model="formObj.hkd" :maxlength="13" class="inputSty" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入套餐价格(港币)"></Input>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="套餐价格(美元)" prop="usd">
						<Input v-model="formObj.usd" :maxlength="13" class="inputSty" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入套餐价格(美元)"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row v-show="formObj.isTerminal == '2'">
					<Col span="12">
					<FormItem label="卡池绑定方式" prop="bindCardPoolType">
						<Select filterable v-model="formObj.bindCardPoolType" class="inputSty" placeholder="请选择" :disabled="typeFlag=='Info'"
						 :clearable="typeFlag!='Info'" @on-change="changeBindCardpoolType"  @on-clear="clearBindCardpoolType">
							<Option value='1'>关联卡池</Option>
							<Option value='2'>国家卡池关联组</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<Row v-show="formObj.bindCardPoolType == '2'">
					<Col span="12">
					<FormItem label="选择国家卡池关联组" prop="groupId" :rules="formObj.bindCardPoolType == '2' ?
          	ruleAddValidate.groupId : [{required: false}]">
						<Select v-model="formObj.groupId" placeholder="选择国家卡池关联组" :disabled="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 :filterable="true" @on-change="changeGroupId(formObj.groupId)" @on-clear="clearGroupId">
							<Option :title="item.groupName" v-for="(item,index) in groupIdtList" :value="item.groupId" :key="item.groupId">{{ item.groupName.length > 30 ? item.groupName.substring(0,30) + "…" : item.groupName }}</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<Row v-show="formObj.isTerminal == '1'">
					<Col span="24">
					<FormItem label="选择国家/地区" prop="mccList" :rules="formObj.isTerminal == '1' ?
          	ruleAddValidate.mccList : [{required: false}]">
						<Select v-model="formObj.mccList" multiple placeholder="请选择国家/地区" :disabled="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 :filterable="true" @on-change="mccListChange">
							<Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<Row v-show="formObj.isTerminal == '2'">
					<Col span="24">
					<FormItem v-show="formObj.bindCardPoolType == '1'" label="选择国家/地区" prop="mccList" :rules="formObj.isTerminal == '2' && formObj.bindCardPoolType == '1' ?
          	ruleAddValidate.mccList : [{required: false}]">
						<Select v-model="formObj.mccList" multiple placeholder="请选择国家/地区" :disabled="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 :filterable="true" @on-change="mccListChange">
							<Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
						</Select>
					</FormItem>
					<FormItem v-show="formObj.bindCardPoolType == '2' && formObj.groupId" label="选择国家/地区" prop="mccList" :rules="formObj.bindCardPoolType == '2' ?
          	ruleAddValidate.mccList : [{required: false}]">
						<Select v-model="formObj.mccList" multiple placeholder="请选择国家/地区" :disabled="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 :filterable="true" @on-change="mccListChange">
							<Option v-for="(value,key) in continentList1" :value="key" :key="key">{{ value }}</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
        <div v-show="formObj.isTerminal == '2'">
          <Row v-for="(item, index1) in formObj.packageConsumptionStr" :key="index1" style="display: flex; align-items: center; margin-bottom: 24px;">
              <Col span="12">
              <FormItem label="用量值" :prop="'packageConsumptionStr.' + index1+ '.consumption'"
                :rules="formObj.isTerminal == '2' ? ruleAddValidate.consumption : [{required: false}]" :label-width="150" style="margin-bottom: 0px;">
                <Input v-model="item.consumption" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
                  placeholder="请输入用量值" style="width: 200px;">
                  <Select slot="append" v-model="item.unit" style="width: 70px;" :disabled="typeFlag=='Info'">
                    <Option v-for="unitItem in unitList" :value="unitItem.value" :key="unitItem.value">
                      {{ unitItem.label }}
                    </Option>
                  </Select>
                </Input>
              </FormItem>
              </Col>
              <Col span="12" style="display: flex; align-items: center;">
              <FormItem label="选择模板" :prop="'packageConsumptionStr.' + index1+ '.upccTemplateId'" :label-width="150" style="margin-right: 10px; margin-bottom: 0px;"
                :rules="formObj.isTerminal == '2' ?  ruleAddValidate.upccTemplateId : [{required: false}]">
                <Select filterable v-model="item.upccTemplateId" style="width: 150px;" placeholder="选择模板"
                  :disabled="typeFlag=='Info'" :clearable="typeFlag!='Info'">
                  <Option :title="i.templateDesc" v-for="i in TemplateList" :value="i.templateId"
                   :key="i.templateId">{{i.templateName.length > 30 ? i.templateName.substring(0,30) + "…" : i.templateName}}</Option>
                </Select>
              </FormItem>
              <Button type="error" size="small" style="margin-bottom: 0px; margin-top: 0px;"
                @click="removeTemplate(index1)" v-if="index1 != 0" >删除</Button>
              </Col>
          </Row>
          <div style="margin-top: 0px; margin-bottom: 10px; text-align: right;">
            <Button size="small" type="primary" @click="addTemplate">添加</Button>
          </div>
        </div>
				<Row v-show="formObj.isTerminal == '2'">
					<Col span="12">
					<FormItem label="无上限模板" prop="noLimitTemplateId">
						<Select filterable v-model="formObj.noLimitTemplateId" class="inputSty" placeholder="选择模板" :disabled="typeFlag=='Info'"
						 :clearable="typeFlag!='Info'">
						  <Option :title="l.templateDesc" v-for="l in TemplateList" :value="l.templateId" :key="l.templateId">{{l.templateName.length > 30 ? l.templateName.substring(0,30) + "…" : l.templateName}}</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<Row v-show="formObj.bindCardPoolType == '1' || formObj.isTerminal == '1'">
					<Col span="24">
					<FormItem label="关联卡池" prop="cardPool" :rules="formObj.bindCardPoolType == '1' || formObj.isTerminal == '1' ?
          	ruleAddValidate.cardPool : [{required: false}]">
						<Button type="dashed" class="inputSty" long @click="loadCardPoolView(formObj.mccList)" :disabled="formObj.mccList.length==0">点击查看</Button>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="是否支持加油包" prop="hasRefuelPackage">
						<i-switch v-model="formObj.hasRefuelPackage" size="large" :disabled="typeFlag=='Info'" @on-change="fuelPackaChange">
							<span slot="open">是</span>
							<span slot="close">否</span>
						</i-switch>
					</FormItem>
					</Col>
					<Col span="12">
						<FormItem  prop="selectionTypes">
							<Button  v-if="formObj.hasRefuelPackage" type="dashed" class="inputSty" long @click="RefuelPackageList">加油包列表</Button>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
						<FormItem label="允许中国激活" prop="supportChina" v-if="formObj.isTerminal=='2'">
							<i-switch v-model="formObj.supportChina" size="large" :disabled="typeFlag=='Info'" >
								<span slot="open">是</span>
								<span slot="close">否</span>
							</i-switch>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24" v-if="formObj.isTerminal=='2'">
					<FormItem label="套餐扣费模式" prop="deductionModel">
						<Select v-model="formObj.deductionModel" placeholder="请选择套餐扣费模式" @on-change="deductionModelChange"
						 :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'" style="width: 200px;">
							<Option value="1">标准模式</Option>
							<Option value="2">绑定模式</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24" v-if="formObj.isTerminal=='2' && formObj.deductionModel == '2'">
					<FormItem label="扣费URL" prop="deductionUrl">
						<Input v-model="formObj.deductionUrl" :maxlength="100" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'"
						 placeholder="请输入扣费URL"></Input>
					</FormItem>
					</Col>
				</Row>
				<Row v-if="formObj.isTerminal == '2' && formObj.deductionModel == '1'">
					<Col span="24">
					<FormItem label="是否支持定向流量" prop="isSupportDirect">
						<Select v-model="formObj.isSupportDirect" class="inputSty" placeholder="请选择是否支持定向流量" :disabled="typeFlag=='Info'"
						 :clearable="typeFlag!='Info'" @on-change="changeDirect($event)">
							<Option value="1">是</Option>
							<Option value="2">否</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<div v-if="formObj.isTerminal == '2' && formObj.deductionModel == '1'">
					<directApp :typeFlag="typeFlag" :isSupportDirect="formObj.isSupportDirect" ref="procedureEdit" @onDirectApp="onDirectApp" :notClick="null" :packageId="null"></directApp>
				</div>
				<!-- 加油包列表弹窗 -->
				<Modal title="添加加油包"  v-model="addRefuelModel"  @on-cancel="cancelModal" :mask-closable="false"
				 width="1000px">
					<Form ref="searchObj" :model="searchObj" :label-width="80" inline style="font-weight:bold;">
						<FormItem label="加油包名称">
							<Input type="text" v-model="searchObj.gaspackname" clearable placeholder="加油包名称"></Input>
						</FormItem>
						<FormItem label="加油包ID">
							<Input type="text" v-model="searchObj.gaspacknameid" clearable placeholder="加油包ID"></Input>
						</FormItem>
						<FormItem>
							<Button type="primary" :loading="searchObjloading" @click="search">搜索</Button>
						</FormItem>
					</Form>
					<Table :columns="Unitedcolumns" :data="Uniteddata" style="width:100%;margin-top: 40px;"
					 @on-selection-change="handleRowChange"
					 @on-select-cancel="cancelPackage"
					 @on-select-all-cancel="cancelPackageAll"
					 :loading="Unitedloading">
					</Table>
					<div style="margin-top:15px">
						<Page :total="Unitedtotal" :current.sync="UnitedcurrentPage" show-total show-elevator @on-change="UnitedgoPage" />
					</div>
					<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
						<Button style="margin-left: 8px" @click="cancelModal">取消</Button>
						<Button type="primary" @click="Confirm">确定</Button>
					</div>
				</Modal>
				<div style="text-align: center;" v-if="typeFlag!='Info'">
					<Button type="primary" @click="submit" :loading="submitFlag" v-preventReClick>提交</Button>
					<Button style="margin-left: 8px" @click="reset">重置</Button>
				</div>
				<div style="text-align: center;" v-if="typeFlag=='Info'">
					<Button style="width: 100px;" @click="reback">返回</Button>
				</div>
			</Form>
		</div>
		<!-- 图片预览-->
		<Modal title="封面预览" v-model="pictureShowFlag" :footer-hide="true" width="532px">
			<div style="display: flex;justify-content: center;align-items: center;width: 500px;">
				<img style="object-fit: contain;" :src="pictureUrl" width="100%">
			</div>
		</Modal>
		<!-- 卡池树 -->
		<Drawer title="关联卡池管理" v-model="drawer" width="350" :mask-closable="false" :styles="styles" @on-close="drawerClose">
			<Button type="success" size="small" style="margin: 0 15px" v-if="typeFlag!='Info'" @click="cardPoolEdit">编辑</Button>
			<Tree :data="cardPoolTree" ref="cardPool" class="demo-tree-render" :empty-text="emptyText"></Tree>
			<div class="demo-drawer-footer" v-if="typeFlag!='Info'">
				<Button style="margin-right: 8px" @click="drawerClose">取消</Button>
				<Button type="primary" @click="toSetCardPool()">确定</Button>
			</div>
		</Drawer>
		<Modal title="卡池编辑" v-model="cardPoolEditFlag" @on-cancel="cardPoolEditConfirm" :mask-closable="false" width="730px">
			<div style="padding: 0 16px;">
				<Form ref="cpEditForm" :model="filterSearchObj" inline style="font-weight:bold;">
					<FormItem>
						<Input type="text" v-model="filterSearchObj.cpName" clearable placeholder="卡池名称"></Input>
					</FormItem>
					<FormItem>
						<Input type="text" v-model="filterSearchObj.sName" clearable placeholder="供应商名称"></Input>
					</FormItem>
					<FormItem>
						<Input type="text" v-model="filterSearchObj.cName" clearable placeholder="国家/地区名称"></Input>
					</FormItem>
					<FormItem>
						<Button type="primary" :loading="cardPoolEditTreeLoad" @click="doCPTreeFilter">搜索</Button>
					</FormItem>
				</Form>
				<div class="demo-spin-article">
					<div style="height: 295px; overflow-y: auto;">
						<Tree :data="cardPoolEditTree" ref="cardPool" class="demo-tree-render" :empty-text="emptyText"></Tree>
					</div>
					<Spin size="large" fix v-if="cardPoolEditTreeLoad"></Spin>
				</div>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<!-- <Button style="margin-left: 8px" @click="cardPoolEditCancle">取消</Button> -->
				<Button type="primary" @click="cardPoolEditConfirm">确定</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		mapMutations
	} from 'vuex'
	import {
		opsearchAll,
	} from '@/api/operators';
	import {
		addPackage,
		getRefuelList,
		getTemplate,
		countryCardPoolGroup,
		addPhoto,
	} from '@/api/package/package';
	import {
		cardPoolRatio,
	} from '@/api/productMngr/cardPool';
	import {
		getPage
	} from '@/api/customer/manufacturer';
	import{getCardPoolMccList} from'@/api/associationGroup/cardPoolMccGroup.js';
	import directApp from './components/directApp.vue'
	const math = require('mathjs');
	export default {
		components: {
			directApp
		},
		data() {
			const positiveNum = (rule, value, callback) => {
				var str = /^(([1-9]\d{0,9})|0)(\.\d{1,2})?$/;
				return str.test(value);
			};
			const integerNum = (rule, value, callback) => {
				var str = /^[0-9]\d*$/;
				return str.test(value);
			};
			const billFlowLimitRule = (rule, value, callback) => {
				if (value) {
					var str = /^[1-9]\d*$/;
					return str.test(value);
				} else {
                    callback();
                }
			};
			// 将用量值转换为MB
			const convertConsumptionToMB = (value, unit) => {
				if (!value) return 0;
				const numValue = parseFloat(value);
				switch (unit) {
					case 'TB':
						return numValue * 1024 * 1024; // TB to MB
					case 'GB':
						return numValue * 1024; // GB to MB
					case 'MB':
						return numValue; // MB to MB
					default:
						return numValue;
				}
			};
			// 流量值要比前面的流量值大
			const validateConsumption = (rule, value, callback) => {
				// 自动去除前后空格
				if (typeof value === 'string') {
					value = value.trim();
				}
				// 使用正则表达式从 rule.field 中提取数字索引
				const matches = rule.field.match(/packageConsumptionStr\.(\d+)\.consumption/);
				if (!matches || matches.length !== 2) {
					console.error('无法从 rule.field 中提取有效的索引:', rule.field);
					callback(new Error('无法提取有效的索引'));
					return;
				}
				const index = parseInt(matches[1], 10); // 提取索引并转换为整数
				// 确保 packageConsumptionStr 是一个数组，并且索引有效
				const packageConsumptionStr = this.formObj.packageConsumptionStr;
				if (!Array.isArray(packageConsumptionStr) || index < 0 || index >= packageConsumptionStr.length) {
					console.error('索引超出范围或 packageConsumptionStr 不是数组');
					callback(new Error('索引无效或数据结构错误'));
					return;
				}
				// 获取当前元素的 consumption 和 unit 属性
				const currentItem = packageConsumptionStr[index];
				const currentConsumption = currentItem.consumption;
				const currentUnit = currentItem.unit;
				if (currentConsumption === undefined || currentConsumption === null || currentConsumption === '') {
					callback(new Error('请输入用量值'));
					return;
				}
				if (currentUnit === undefined || currentUnit === null || currentUnit === '') {
					callback(new Error('请选择单位'));
					return;
				}
				// 校验用量值格式 1-10位正整数
				var integerRegex = /^[1-9]\d{0,9}$/;
				if (!integerRegex.test(currentConsumption)) {
					callback(new Error('请输入1-10位整数数字'));
					return;
				}
				// 用BigInt防止精度丢失，统一转MB
				let mbValue;
				if (currentUnit === 'MB') {
					mbValue = BigInt(currentConsumption);
				} else if (currentUnit === 'GB') {
					mbValue = BigInt(currentConsumption) * 1024n;
				} else if (currentUnit === 'TB') {
					mbValue = BigInt(currentConsumption) * 1024n * 1024n;
				} else {
					mbValue = BigInt(currentConsumption);
				}
				const maxMB = 9999999999n;
				let maxValueStr = '';
				if (currentUnit === 'MB') {
					maxValueStr = '9999999999MB';
				} else if (currentUnit === 'GB') {
					maxValueStr = '9765624GB';
				} else if (currentUnit === 'TB') {
					maxValueStr = '9536TB';
				} else {
					maxValueStr = '9999999999MB';
				}
				if (mbValue > maxMB) {
					callback(new Error('用量值超过最大限制 ' + maxValueStr));
					return;
				}
				if (mbValue < 10n) {
					callback(new Error('用量值不能小于10MB'));
					return;
				}
				// 检查前一个值是否存在且当前值不小于前一个值
				const preIndex = index - 1;
				let preConsumptionMB = (preIndex >= 0) ? (() => {
					const preItem = packageConsumptionStr[preIndex];
					if (!preItem || !preItem.consumption || !preItem.unit) return null;
					if (preItem.unit === 'MB') return BigInt(preItem.consumption);
					if (preItem.unit === 'GB') return BigInt(preItem.consumption) * 1024n;
					if (preItem.unit === 'TB') return BigInt(preItem.consumption) * 1024n * 1024n;
					return BigInt(preItem.consumption);
				})() : null;
				if (preConsumptionMB !== null && (mbValue <= preConsumptionMB)) {
					callback(new Error('用量值逻辑不正确，每档的用量值需大于上一档次的用量值'));
				} else {
					callback();
				}
			};
			return {
				addRefuelModel: false, //加油包弹窗标识
				searchObjloading: false,
				Unitedloading:false,
				selection: [], //多选
				searchObj: {
					gaspackname: '',
					gaspacknameid: '',
				},
				Unitedcolumns: [{
					type: 'selection',
					width: 60,
					align: 'center'
				}, {
					title: "加油包ID",
					key: 'id',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				},
				{
					title: "加油包名称(简体中文)",
					key: 'nameCn',
					minWidth: 180,
					align: 'center',
					tooltip: true,
				}, {
					title: "加油包价格(人民币)",
					key: 'cny',
					minWidth: 180,
					align: 'center',
					tooltip: true,
				}, {
					title: "加油包价格(港币)",
					key: 'hkd',
					minWidth: 180,
					align: 'center',
					tooltip: true,
				}, {
					title: "加油包价格(美元)",
					key: 'usd',
					minWidth: 180,
					align: 'center',
					tooltip: true,
				}],
				Unitedtotal: 0,
				UnitedcurrentPage: 1,
				Unitedpage: 0,
				Uniteddata: [],
				submitFlag: false,
				en: /^[0-9a-zA-Z\/\(\)\,\.\:\<\>\"\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/,
				mccListTemp: '',
				usageType: '1', //默认普通卡池
				drawer: false, //抽屉标识
				emptyText: '未查询到任何卡池数据',
				styles: {
					height: 'calc(100% - 55px)',
					overflow: 'auto',
					paddingBottom: '53px',
					position: 'static'
				},
				continentList: [],
				continentList1: [],
				statusList: [{
						label: '待上架',
						value: '1'
					},
					{
						label: '正常',
						value: '2'
					},
					{
						label: '下架',
						value: '3'
					}
				],
				cardPoolList: [],
				typeFlag: 'Add',
				formObj: {
					nameCn: '',
					nameTw: '',
					nameEn: '',
					descCn: '',
					descTw: '',
					descEn: '',
					cny: '',
					hkd: '',
					usd: '',
					mccList: [],
					periodUnit: '', //周期类型
					keepPeriod: '', //持续周期
					effectiveDay: '', //套餐订购以后的有效期，单位为天
					isTerminal: '', //是否是终端厂商套餐
					corpId: '', //厂商
					status: '', //套餐状态
					startTime: '', //创建时间yyyy/mm/dd hh:mm:ss
					endTime: '', //修改时间yyyy/mm/dd hh:mm:ss
					isPromotion: '', //是否是促销套餐
					saleLimit: '', //限制购买数量
					billFlowLimit: '',
					cardPool: [], //关联卡池
					hasRefuelPackage: false, //是否支持加油包
					refuelList: [], //加油包
					picture: null, //上传图片
					flowLimitType: '', //流量限制类型
					controlLogic: '', //达量后控制逻辑
					selectionTypes: [], //多选类型
					supportChina:false,//允许中国激活
					deductionModel: '',//套餐扣费模式
					deductionUrl: '',//扣费URL
					flowLimitSum: '', //流量上限
					signBizId: '', //高速
					limitSignBizId: '',//低速
					slowSignBizId: '',//限速
					isSupportedHotspots: '',//是否支持热点
					packageConsumptionStr :[{
						consumption: "",
						upccTemplateId: "",
						unit: "MB",
					}],
					groupId: "",//选择国家卡池
					noLimitTemplateId: '', // 无上限模板
					bindCardPoolType: '',//卡池绑定方式
					isSupportDirect: '',//是否支持定向流量
					directAppInfos: [{
						directType: '',//定向使用逻辑
						appConsumption: [{
							index1: 1,
							consumption: '',//流量值
							upccTemplateId:[],//选择UPCC模板值
						}],
						isUsePackage: '',//是否继续使用通用模板
						noLimitTemplateId: [],//定向限速板/定向免流限速模版/定向免流继续使用模板
					}], //是定向应用数据
				},
				flowLimitUnit: "1", //流量上限单位
				pictureUrl: '',
				pictureShowFlag: false,
				corpIdList: [], //厂商列表
				changedIsTerminal: [
					//是终端厂商
					"flowLimitSum", //流量上限
					"signBizId", //高速
					"limitSignBizId", //低速
					"slowSignBizId", //限速
					"corpId", //厂商名
					//非终端厂商
					"isSupportedHotspots", //是否支持热点
					"noLimitTemplateId",//无上限模板
					"bindCardPoolType", //卡池绑定方式
				],
				ruleAddValidate: {
					nameCn: [{
						required: true,
						type: 'string',
						message: '套餐名称(简中)不能为空',
					}],
					nameEn: [
						{
							validator: (rule, value, cb) => {
								var regex = /^[0-9a-zA-Z\/\(\)\,\.\:\"\<\>\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;
								return regex.test(value) || value == '';
							},
							message: 'Package name (EN) format error',
						}
					],
					descCn: [{
						required: true,
						type: 'string',
						message: '套餐描述(简中)不能为空',
					}],
					descEn: [
						{
							validator: (rule, value, cb) => {
								var regex = /^[0-9a-zA-Z\/\(\)\,\.\:\"\<\>\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;
								return regex.test(value) || value == '';
							},
							message: 'Package description (EN) format error',
						}
					],
					periodUnit: [{
						required: true,
						type: 'string',
						message: '周期类型不能为空',
					}],
					keepPeriod: [{
							required: true,
							message: '持续周期不能为空',
						}, {
							validator: integerNum,
							message: '持续周期格式错误'
						},
						{
							validator: (rule, value, cb) => {
								return Number(2147483647) >= Number(value);
							},
							message: '持续周期数值过大',
						}
					],
					effectiveDay: [{
							required: true,
							message: '购买有效期(天)不能为空',
						}, {
							validator: integerNum,
							message: '购买有效期(天)格式错误'
						},
						{
							validator: (rule, value, cb) => {
								return Number(2147483647) >= Number(value);
							},
							message: '购买有效期(天)数值过大',
						}
					],
					corpId: [{
						required: true,
						type: 'string',
						message: '厂商不能为空',
					}],
					status: [],
					isTerminal: [{
						required: true,
						type: 'string',
						message: '是否为厂商套餐不能为空',
					}],
					isPromotion: [{
						required: true,
						type: 'string',
						message: '是否为促销套餐不能为空',
					}],
					signBizId: [{
						required: true,
						type: 'string',
						message: '签约业务(高速)不能为空',
					}],
					limitSignBizId: [{
						required: true,
						type: 'string',
						message: '签约业务(低速)不能为空',
					}],
					slowSignBizId: [{
						required: true,
						type: 'string',
						message: '签约业务(限速)不能为空',
					}],
					isSupportedHotspots: [{
						required: true,
						message: '是否支持热点不能为空',
					}],
					noLimitTemplateId: [{
						required: true,
						type: 'string',
						message: '无上限模板不能为空',
					}],
					startTime: [{
						required: true,
						type: 'date',
						message: '开始时间不能为空',
					}],
					endTime: [{
						required: true,
						type: 'date',
						message: '结束时间不能为空',
					}],
					saleLimit: [{
							required: true,
							message: '促销限购份数不能为空'
						}, {
							validator: integerNum,
							message: '促销限购份数格式错误'
						},
						{
							validator: (rule, value, cb) => {
								return Number(2147483647) >= Number(value);
							},
							message: '促销限购份数数值过大',
						}
					],
					flowLimitType: [{
						required: true,
						message: '请选择流量限制类型'
					}],
					flowLimitSum: [
						{
							required: true,
							message: '请输入流量上限'
						},
						{
							validator: (rule, value, cb) => {
								if (this.formObj.isTerminal === '1') {
									if (!value) {
										return cb(new Error('请输入流量上限'));
									}
									var str = /^[1-9]\d*$/;
									if (!str.test(value)) {
										return cb(new Error('请输入正整数'));
									}
								}
								cb();
							}
						}
					],
					controlLogic: [{
						required: true,
						message: '请选择达量后控制逻辑'
					}],
					cny: [{
						required: true,
						message: '套餐价格(人民币)不能为空',
					}, {
						validator: (rule, value, cb) => {
							var str = /^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value);
						},
						message: '最高支持8位整数和2位小数正数或零',
					}],
					hkd: [{
						required: true,
						message: '套餐价格(港币)不能为空',
					}, {
						validator: (rule, value, cb) => {
							var str = /^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value);
						},
						message: '最高支持8位整数和2位小数正数或零',
					}],
					usd: [{
						required: true,
						message: '套餐价格(美元)不能为空',
					}, {
						validator: (rule, value, cb) => {
							var str = /^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value);
						},
						message: '最高支持8位整数和2位小数正数或零',
					}],
					mccList: [{
						required: true,
						type: 'array',
						message: '支持国家/地区不能为空',
					}],
					billFlowLimit: [{
							// validator: (rule, value, cb) => {
							// 	var str = /^[1-9]\d*$/;
							// 	return str.test(value);
							// },
							validator: billFlowLimitRule,
							message: '激活流量限额大于0的正整数',
						},
						{
							validator: (rule, value, cb) => {
								return Number(2147483647) >= Number(value);
							},
							message: '激活流量限额数值过大',
						}
					],
					bindCardPoolType: [{
							required: true,
							message: '卡池绑定方式不能为空',
						}
					],
					groupId: [{
							required: true,
							message: '国家卡池关联组不能为空',
						}
					],
					cardPool: [
						// {
						//   required: true,
						//   type: 'array',
						//   message: '关联卡池不能为空',
						// },
						{
							validator: (rule, value, cb) => {
								var len = this.formObj.cardPool.length;
								return len > 0;
							},
							message: '关联卡池不能为空',
						}
					],
					picture: [
						// {
						//   required: true,
						//   type: 'object',
						//   message: '封面图片不能为空',
						// },
						{
							required: true,
							validator: (rule, value, cb) => {

								return this.pictureUrl != '' || this.formObj.picture != null;
							},
							message: '封面图片不能为空',
						}
						// {
						//   required: true,
						//   message: '封面图片不能为空',
						//   trigger: 'change',
						//   validator: (rule, value, cb) => {

						//     if( this.pictureUrl == '' || this.formObj.picture == null ) {
						//       return cb(new Error('封面图片不能为空'))
						// 		}else {
						//       callback()
						//     }
						//},
						// }
					],
					selectionTypes: [
						{
							validator: (rule, value, cb) => {
								var len = this.formObj.selectionTypes.length;
								return len > 0 || !this.formObj.hasRefuelPackage;
							},
							message: '加油包列表不能空',
						}
					],
					supportChina:[{
							required: true,
							message: '请选择允许中国激活',
						},
					],
					deductionModel:[{
						required: true,
						type: 'string',
						message: '请选择套餐扣费模式',
					}],
					isSupportDirect: [{
						required: true,
						type: 'string',
						message: '是否支持定向流向不能为空',
					}],
					consumption: [
						{ required: true, message: '用量值不能为空'},
						{ validator: validateConsumption },
					],
					upccTemplateId: [{
						required: true,
						type: 'string',
						message: '选择模板不能为空',
					}],
				},
				periodUnitList: [{
						value: '1',
						label: '24小时'
					}, {
						value: '2',
						label: '自然日'
					},
					{
						value: '3',
						label: '自然月'
					},
					{
						value: '4',
						label: '自然年'
					}
				],
				cardPoolEditFlag: false, //卡池编辑框标识
				cardPoolTree: [], //比例树
				cardPoolEditTree: [], //编辑树
				cardPoolEditTreeLoad: false, //编辑树加载标识
				filterPool: [], //筛选后展示集合
				filterTempPool: [], //筛选展示临时集合:取消时使用
				totalPool: [], //所有比例数据集合
				totalTempPool: [], //所有比例数据集合:取消时使用
				cpcrvList: [],
				firstLoad: false, //用于区分国家/地区是否首次加载
				filterSearchObj: {
					'cpName': '',
					'sName': '',
					'cName': ''
				}, //条件筛选对线
				localMap: new Map(),
				TemplateList: [],
				groupIdtList: [],
				refuelIDLists: [],//加油包列表确定后的数据
				appInfos:[],//应用列表
				directTemplateList: {}, //定向模板列表
				selectedOptions: [],
				isVaild: true,
				unitList: [
					{ value: 'MB', label: 'MB' },
					{ value: 'GB', label: 'GB' },
					{ value: 'TB', label: 'TB' },
				],
			}
		},
		created() {
			this.$nextTick(() => {
				this.changedIsTerminal.forEach((element) => {
					this.$set(this.ruleAddValidate[element][0], 'required', false);
				});
			})
		},
		computed: {
		},
		methods: {
			...mapMutations([
				'closeTag'
			]),
			//调用定向应用子组件方法
			onDirectApp() {
				this.formObj.directAppInfos = this.$refs['procedureEdit'].childMethod();
				//转换定向流量数据格式传给后端
				const directAppInfos = [];
				for (const directAppInfo of this.formObj.directAppInfos) {
				    const appDetailInfos = [];
				    directAppInfo.appId.forEach((appId, i) => {
						if (directAppInfo.appConsumption) {
							const appConsumption = directAppInfo.appConsumption.map((consumptionData) => {
								return {
									consumption: parseInt(consumptionData.consumption),
									upccTemplateId: consumptionData.upccTemplateId[i]
								};
							});
							const noLimitTemplateIdList = directAppInfo.noLimitTemplateId;
							const appDetailInfo = {
								appConsumption : directAppInfo.directType == '2' ? appConsumption : [],
								appId,
								noLimitTemplateId: noLimitTemplateIdList[i]
							};
							appDetailInfos.push(appDetailInfo);
						}
				    });

				    const directAppInfoData = {
						appDetailInfos,
						directType: directAppInfo.directType,
						isUsePackage: directAppInfo.isUsePackage
				    };

					directAppInfos.push(directAppInfoData);
				}
				this.formObj.directAppInfos = directAppInfos
			},
			//提交
			async submit() {
        console.log(this.formObj.packageConsumptionStr,"packageConsumptionStr")
				// 是否终端厂商的改变  校验也随之改变
				if(this.formObj.isTerminal == '1'){
					this.changedIsTerminal.forEach(element => {
						this.ruleAddValidate['isSupportedHotspots'][0].required = false;
						this.ruleAddValidate['noLimitTemplateId'][0].required = false;
						this.ruleAddValidate['bindCardPoolType'][0].required = false;
						this.ruleAddValidate['corpId'][0].required = true;
						// this.ruleAddValidate['flowLimitSum'][0].required = true;
						this.ruleAddValidate['signBizId'][0].required = true;
						this.ruleAddValidate['limitSignBizId'][0].required = true;
						this.ruleAddValidate['slowSignBizId'][0].required = true;
					})
				} else {
					this.changedIsTerminal.forEach(element => {
						this.ruleAddValidate['isSupportedHotspots'][0].required = true;
						this.ruleAddValidate['noLimitTemplateId'][0].required = true;
						this.ruleAddValidate['bindCardPoolType'][0].required = true;
						this.ruleAddValidate['corpId'][0].required = false;
						this.ruleAddValidate['flowLimitSum'] = []
						this.ruleAddValidate['signBizId'][0].required = false;
						this.ruleAddValidate['limitSignBizId'][0].required = false;
						this.ruleAddValidate['slowSignBizId'][0].required = false;
					})
				}

        if (this.formObj.isTerminal == '2') {
        	if (this.formObj.isSupportDirect == '1' && this.formObj.deductionModel == '1') {
        		//获取定向应用子组件的校验结果
        		this.isVaild = await this.$refs['procedureEdit'].childSubmit();
        		//将定向应用数据转换格式给后端
        		this.onDirectApp()
        	}
        }

				// 等待DOM更新完成
				await this.$nextTick();
				// 执行表单验证
				this.submitFlag = true;

				try {
					const isValidForm = await new Promise(resolve => {
						this.$refs["formObj"].validate((valid) => {
							resolve(valid);
						});
					});

					if (!isValidForm || !this.isVaild) {
						return;
					}

					var data = Object.assign({}, this.formObj);
					var refuelList = [];
					if (data.hasRefuelPackage) {
						this.formObj.selectionTypes.forEach((value, index) => {
							refuelList.push(value.id)
						})
					}
					// 添加对 packageConsumptionStr 长度的校验
					if (data.isTerminal === '2' && (!data.packageConsumptionStr || data.packageConsumptionStr.length === 0)) {
						this.$Notice.error({
							title: '操作提示',
							desc: '请至少填写一组用量值、选择模板信息'
						});
						this.submitFlag = false;
						return;
					}
					if (data.startTime > data.endTime) {
						this.$Notice.error({
							title: '操作提示',
							desc: '操作失败,允许订购时间有误'
						})
						return;
					}
					//相关价格前端以元为单位显示，需要转换成分为单位
					data.cny = Number(math.format(Number(data.cny) * 100, 12));
					data.hkd = Number(math.format(Number(data.hkd) * 100, 12));
					data.usd = Number(math.format(Number(data.usd) * 100, 12));
					var type = this.typeFlag;
					var formData = new FormData();

					let cpcrvListStr;
					if (this.formObj.isTerminal == '1') {
						cpcrvListStr = this.cpcrvList
					} else {
						if (this.formObj.bindCardPoolType == '1') {
							cpcrvListStr = this.cpcrvList
						}
					}

					let image = '';
					if (data.picture != null) { // Only call addPhoto if there's a picture
						formData.append('file', data.picture); //封面
						const resPhoto = await addPhoto(formData);
						if (resPhoto && resPhoto.code == '0000') {
							image = resPhoto.data;
						} else {
							this.$Notice.error({
								title: '操作提示',
								desc: resPhoto.msg || '图片上传失败'
							});
							return; // Exit the function on failure
						}
					} else if (this.typeFlag !== 'Add') { // For Edit, if no new picture, use existing coverUrl
						image = this.pictureUrl; // Use existing pictureUrl if no new upload
					}

					//新增
					if ('Add' == type) {
						let directAppInfosList = data.isSupportDirect == '1' ? data.directAppInfos : []
						const resPackage = await addPackage({
							coverPath: image,
							nameCn: data.nameCn,
							nameTw: data.nameTw,
							nameEn: data.nameEn,
							descCn: data.descCn,
							descTw: data.descTw,
							descEn: data.descEn,
							cny: data.cny,
							hkd: data.hkd,
							usd: data.usd,
							mccList: data.mccList,
							periodUnit: data.periodUnit,
							keepPeriod: data.keepPeriod,
							effectiveDay: data.effectiveDay,
							isTerminal: data.isTerminal,
							corpId: data.isTerminal == '1' ? data.corpId : '',
							status: data.status,
							startTime: data.startTime,
							endTime: data.endTime,
							flowLimitType: data.flowLimitType,
							flowLimitUnit: this.flowLimitUnit,
							controlLogic: data.controlLogic,
							isPromotion: data.isPromotion,
							saleLimit: data.isPromotion == '1' ? data.saleLimit : '',
							billFlowLimit: data.billFlowLimit,
							billFlowLimitUnit: 2,
							refuelList: refuelList,
							supportRefuel: data.hasRefuelPackage ? 1:2,
							supportChina: data.supportChina ? 1:2,
							deductionUrl: data.deductionUrl,
							flowLimitSum: data.isTerminal == '1' ? data.flowLimitSum : undefined,
							signBizId: data.isTerminal == '1' ? data.signBizId : undefined,
							slowSignBizId: data.isTerminal == '1' ? data.slowSignBizId : undefined,
							limitSignBizId: data.isTerminal == '1' ? data.limitSignBizId : undefined,
							cpcrvList: cpcrvListStr,
							deductionModel: data.isTerminal == '1' ? 1 : data.deductionModel,
							bindCardPoolType: data.isTerminal == '2' ? data.bindCardPoolType : undefined,
							// 根据单位将 consumption 值统一转换为 MB 再传给后端
							packageConsumptions: data.isTerminal == '2' ? data.packageConsumptionStr.map(item => ({
								consumption: this.convertToMB(item.consumption, item.unit), // 将用量值转换为MB
								unit: item.unit, // 单位保持不变
								upccTemplateId: item.upccTemplateId
							})) : undefined,
							noLimitTemplateId: data.isTerminal == '2' ? data.noLimitTemplateId : undefined,
							isSupportedHotspots: data.isTerminal == '2' ? data.isSupportedHotspots : undefined,
							groupId: data.isTerminal == '2' && data.bindCardPoolType == '2' ? data.groupId : undefined,
							isSupportDirect: data.isTerminal == '2' && data.deductionModel == '1' ? data.isSupportDirect : '2',
							directAppInfos: directAppInfosList
						});

						if (resPackage && resPackage.code == '0000') {
							this.$Notice.success({
								title: '操作提示',
								desc: '操作成功'
							});
							setTimeout(() => {
								this.reback();
							}, 1500);
						} else {
							this.$Notice.error({
								title: '操作提示',
								desc: resPackage.msg || '套餐创建失败'
							});
						}
					}
				} catch (err) {
					console.error('提交操作发生错误:', err);
				} finally {
					this.submitFlag = false;
				}
			},
			//重置
			reset() {
				this.formObj = {
					nameCn: '',
					nameTw: '',
					nameEn: '',
					descCn: '',
					descTw: '',
					descEn: '',
					cny: '',
					hkd: '',
					usd: '',
					mccList: [],
					periodUnit: '', //周期类型
					keepPeriod: '', //持续周期
					effectiveDay: '', //套餐订购以后的有效期，单位为天
					isTerminal: '', //是否是终端厂商套餐
					corpId: '', //厂商
					status: '', //套餐状态
					startTime: '', //创建时间yyyy/mm/dd hh:mm:ss
					endTime: '', //修改时间yyyy/mm/dd hh:mm:ss
					isPromotion: '', //是否是促销套餐
					saleLimit: '', //限制购买数量
					billFlowLimit: '',
					cardPool: [], //关联卡池
					hasRefuelPackage: false, //是否支持加油包
					refuelList: [], //加油包
					picture: null, //上传图片
					flowLimitType: '', //流量限制类型
					controlLogic: '', //达量控制逻辑
					selectionTypes: [], //多选类型
					deductionModel: '',//套餐扣费模式
					deductionUrl: '',//扣费URL
					packageConsumptionStr: [{
						consumption: "",
						upccTemplateId: "",
						unit: "MB",
					}],
					bindCardPoolType: 1,//卡池绑定方式
					noLimitTemplateId: "", //无上限模板
					groupId: "", //国家卡池关联组
					flowLimitSum: '', //流量上限
					slowSignBizId: '', //签约业务(限速)。用户激活套餐为HIMS
					limitSignBizId: '', //签约业务(低速)。用户激活套餐需要下发
					appId: [],//应用列表
					isSupportDirect: '',//是否支持定向流量
					directAppInfos: [{
						directType: '',//定向使用逻辑
						appConsumption: [{
							index1: 1,
							consumption: '',//流量值
							upccTemplateId:[],//选择UPCC模板值
						}],
						isUsePackage: '',//是否继续使用通用模板
						noLimitTemplateId: [],//定向限速板/定向免流限速模版/定向免流继续使用模板
					}],
				}
				this.$refs["formObj"].resetFields();
				this.pictureUrl = ''
			},
			//国家/地区
			getLocalList() {
			  opsearchAll().then(res => {
			    if (res && res.code == '0000') {
			      var list = res.data;
			      this.continentList = list;
			      this.continentList.sort(function(str1, str2) {
			        return str1.countryEn.localeCompare(str2.countryEn);
			      });
			      var localMap = new Map();
			      list.map((local, index) => {
			        localMap.set(local.mcc, local.countryEn);
			      });
			      this.localMap = localMap;
			    } else {
			      throw res
			    }
			  }).catch((err) => {
			    console.error(err);
			  }).finally(() => {

			  })
			},
			//国家/地区 2
			getLocalList2() {
				getCardPoolMccList({
					groupId: this.formObj.groupId,
          isSupportedHotspots: '',
				}).then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.continentList1 = list;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					let flag = true;
					this.formObj.selectionTypes.map((item, index) => {
					   if(value.id===item.id){
						   flag=false
					   }
					});
					//判断重复
					if(flag){
						this.formObj.selectionTypes.push(value);
					}
				});
			},
			// 取消选择套餐包
			cancelPackage(selection, row) {
				this.formObj.selectionTypes.forEach((value,index)=>{
					if(value.id===row.id){
						 this.formObj.selectionTypes.splice(index,1);
					}
				})
			},
			// 取消全选选择套餐包
			cancelPackageAll(selection, row) {
				this.formObj.selectionTypes=[]
			},
			//获取可订购加油包列表
			getRefuelList(page){
				this.Unitedloading=true
				getRefuelList({
					pageNum:page,
					pageSize:10,
					refuelID:this.searchObj.gaspacknameid,
					refuelName:this.searchObj.gaspackname
				}).then(res => {
					if (res && res.code == '0000') {
						this.Uniteddata=res.data
						this.Unitedtotal=res.count
						this.UnitedcurrentPage = page
						//回显
						this.formObj.selectionTypes.forEach(item=>{
						   res.data.forEach(element=>{
							 if(element.id==item.id){
							   this.$set(element,'_checked',true)
							 }
						   })
						})
						this.addRefuelModel = true
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
					this.Unitedloading=false
				   this.searchObjloading=false
				})
			},
			RefuelPackageList() {
				this.getRefuelList(1)
			},
			//是否支持加油包切换清空加油包选项
			fuelPackaChange(){
				this.formObj.selectionTypes=[]
			},
			// 扣费模式为标准模式时，清除扣费URL的值，以免提交
			deductionModelChange(){
				this.formObj.deductionUrl = ''
			},
			search(){
				this.searchObjloading=true
				this.getRefuelList(1)
			},
			UnitedgoPage(page){
				this.getRefuelList(page)
			},
			//添加加油包
			addRefuelPackage() {
				var len = this.formObj.refuelList.length;
				var lastObj = this.formObj.refuelList[len - 1];
				if (len != 0 && (lastObj.nameCn == '' || lastObj.cny == '' || lastObj.hkd ==
						'' || lastObj.hkd == '')) {
					this.$Message.error('请完善上条加油包信息')
					return;
				}
				this.formObj.refuelList.push({
					nameCn: '',
					nameTw: '',
					nameEn: '',
					flowValue: '',
					flowUnit: '',
					cny: '',
					hkd: '',
					usd: ''
				});
			},
			//删除加油包
			delRefuelPackageBtn(index) {
				this.formObj.refuelList.splice(index, 1);
			},
			//上传图片赋值
			handleUpload(file) {
				var type = file.type;
				if (type.indexOf('image') != -1) {
					this.formObj.picture = file;
					this.pictureUrl = '';
					const reader = new FileReader();
					reader.readAsDataURL(file);
					reader.onload = () => {
						const _base64 = reader.result;
						this.pictureUrl = _base64; //将_base64赋值给图片的src，实现图片预览
					}
					this.$refs['formObj'].validateField('picture');
				} else {
					this.$Notice.error({
						title: '操作提示',
						desc: '仅支持图片格式文件'
					})
				}
				return false;
			},
			//上传图片删除
			cancelSelected() {
				this.formObj.picture = null;
				this.pictureUrl = '';
				this.$refs['formObj'].validateField('picture');
			},
			//单日周期时默认选择达量控制逻辑
			getcontrolLogic() {
				this.formObj.controlLogic = this.formObj.flowLimitType === 2 ? 1 :
					this.formObj.isTerminal === '1' && this.formObj.flowLimitType === 1 ? 2 : null
			},
			//套餐状态动态变化
			changeTime() {
				var start = this.formObj.startTime;
				var end = this.formObj.endTime;
				if (start == '' || end == '' || start > end) {
					this.formObj.status = '';
				} else if (start > new Date()) {
					this.formObj.status = '1';
				} else if (start < new Date() && end > new Date()) {
					this.formObj.status = '2';
				} else if (end < new Date()) {
					this.formObj.status = '3';
				}
			},
			//时间转换
			dateToStr(datetime) {
				var dateTime = new Date(datetime);
				var year = dateTime.getFullYear();
				var month = dateTime.getMonth() + 1; //js从0开始取
				var date = dateTime.getDate();
				var hour = dateTime.getHours();
				var minutes = dateTime.getMinutes();
				var second = dateTime.getSeconds();
				if (month < 10) {
					month = "0" + month;
				}
				if (date < 10) {
					date = "0" + date;
				}
				if (hour < 10) {
					hour = "0" + hour;
				}
				if (minutes < 10) {
					minutes = "0" + minutes;
				}
				if (second < 10) {
					second = "0" + second;
				}
				return year + "-" + month + "-" + date + " " + hour + ":" + minutes + ":" + second;
			},
			//国家卡池关联组
			groupIdListChange() {
				countryCardPoolGroup({
					isSupportedHotspots: this.formObj.isSupportedHotspots
				}).then(res => {
					if (res && res.code == '0000') {
						this.groupIdtList = res.data;
					} else {
						throw res
					}
				}).catch((err) => {})
			},
			//国家/地区选择变更
			mccListChange(e) {
				if (!this.firstLoad) {
					this.mccListTemp = '';
					this.formObj.cardPool = [];
					//清空总数据集合
					this.totalPool = [];
					this.cpcrvList = [];
				} else {
					this.firstLoad = false;
				}
			},
			//卡池编辑窗口
			cardPoolEdit() {
				this.filterSearchObj = {
					'cpName': '',
					'sName': '',
					'cName': ''
				};
				//展示列表不影响实际值
				// this.totalPool = this.totalTempPool.concat();
				//筛选比例集合-筛选所有信息 -> filterPool
				this.filterRateList("", "", "", "all", "edit");
				this.drawer = false;
				this.cardPoolEditFlag = true;
			},
			//搜索
			doCPTreeFilter() {
				this.cardPoolEditTreeLoad = true;
				var this_ = this;
				//前一次过滤数据汇总至->this.totalPool
				this.saveTreeIntoTotalPool();
				//根据过滤条件进行过滤
				this.filterRateList(this.filterSearchObj.cpName, this.filterSearchObj.sName, this.filterSearchObj.cName,
					"all",
					"edit");
				setTimeout(function() {
					this_.cardPoolEditTreeLoad = false;
				}, 500);
			},
			//存储值 搜索/编辑保存 进行前一次过滤数据汇总
			saveTreeIntoTotalPool() {
				//filterPool --> into --> totalPool
				if (this.totalPool.length > 0) {
					//优化函数->组装数据
					//获取树值做逻辑判断
					var filterMap = new Map();
					this.filterPool.map((filterParent, index) => {
						filterParent.children.map((filterChild, index) => {
							if (filterChild.rate != null && filterChild.rate != 0) {
								//添加新的key-value
								//key:卡池id+国家/地区mcc value:比例值
								filterMap.set(filterParent.id + filterChild.mcc, filterChild.rate);
							}
						});
					});
					//判断赋值
					this.totalPool.map((totalParent, index) => {
						totalParent.children.map((totalChild, index) => {
							if (filterMap.has(totalParent.id + totalChild.mcc)) {
								totalChild.rate = filterMap.get(totalParent.id + totalChild.mcc);
							}
						});
					});
				}
			},
			//编辑确认
			cardPoolEditConfirm() {
				//前一次过滤数据汇总至->this.totalPool
				// this.saveTreeIntoTotalPool();
				//临时值更新
				// this.totalTempPool = this.totalPool.concat();
				// this.filterTempPool = this.filterPool.concat();
				this.filterRateList("", "", "", "filled", "show");
				this.cardPoolEditFlag = false;
				this.drawer = true;
			},
			//编辑取消
			cardPoolEditCancle() {
				//总值复原至原值
				// this.totalPool = this.totalTempPool.concat();
				//过滤树复原至原值
				// this.filterPool = this.filterTempPool.concat();
				this.filterRateList("", "", "", "filled", "show");
				this.cardPoolEditFlag = false;
				this.drawer = true;
			},
			//加油包列表确认
			Confirm() {
				this.addRefuelModel = false
				this.searchObj.gaspackname = ""
				this.searchObj.gaspacknameid = ""
				//获取确定后的数据
				this.refuelIDLists = JSON.parse(JSON.stringify(this.formObj.selectionTypes))
			},
			//加油包取消
			cancelModal() {
				this.addRefuelModel = false
				this.searchObj.gaspackname = ""
				this.searchObj.gaspacknameid = ""
				//如果一开始取消，加油包数据则等于初始数据，如果有确定后改过数据，那么再点取消，展示的应该是确定后的数据
				if (this.refuelIDLists.length == 0) {
					this.formObj.selectionTypes = []
				} else {
					this.formObj.selectionTypes = JSON.parse(JSON.stringify(this.refuelIDLists))
				}
			},
			//加载卡池树数据
			loadTreeData(cpObj) {
				let pushArr = [];
				let len = cpObj.length;
				try {
					for (var i = 0; i < len; i++) {
						let index = i;
						let cp = cpObj[index];
						let obj = {
							title: cp.poolName + "-(" + cp.supplierName + ")",
							id: cp.poolId,
							poolName: cp.poolName,
							supplierName: cp.supplierName,
							expand: true,
							children: []
						};
						if (cp.regionList && cp.regionList.length > 0) {
							for (var n = 0; n < cp.regionList.length; n++) {
								let nIndex = n;
								let region = cp.regionList[nIndex];
								obj.children.push({
									expand: true,
									poolId: cp.poolId,
									poolName: cp.poolName,
									supplierName: cp.supplierName,
									countryCn: region.countryCn,
									countryTw: region.countryTw,
									countryEn: region.countryEn,
									mcc: region.mcc,
									rate: region.rate,
									render: (h, {
										root,
										node,
										data
									}) => {
										return h('div', {
											style: {
												display: 'flex',
												width: '100%',
												height: '25px',
												flexDirection: 'row',
												alignItems: 'center'
											}
										}, [
											h('Tooltip', {
												props: {
													placement: 'left',
													content: cp.regionList[nIndex].countryEn
												},
												style: {
													width: '100px',
													display: 'inline-block',
												}
											}, [
												h('div', {
													style: {
														width: '100px',
														height: '25px',
														display: 'inline-block',
														overflow: 'hidden',
														textOverflow: 'ellipsis',
														whiteSpace: 'nowrap',
														lineHeight: '30px'
													}
												}, cp.regionList[nIndex].countryEn + '：')
											]),
											h('input', {
												domProps: {
													type: 'Number',
													value: cp.regionList[nIndex].rate == undefined ? null : cp.regionList[nIndex]
														.rate,
													placeholder: '请输入分配比列(%)',
													max: 100,
													min: 0,
													disabled: this.typeFlag == 'Info'
												},
												style: {
													width: '150px',
													height: '20px',
													// float: 'left',
													textAlign: 'center',
													border: '#ccc 1px solid',
													borderRadius: '5px',
													mozBorderRadius: '5px',
													webkitBorderRadius: '5px',
													marginLeft: '8px'
												},
												on: {
													input: (e) => {
														var v = e.target.value;
														cpObj[index].regionList[nIndex].rate = v;
														obj.children[nIndex].rate = v;
													}
												}
											})
										]);
									},
								})
							}
						}
						pushArr.push(obj);
					}
					//设置总数据集合
					this.totalPool = pushArr;
				} catch (e) {
					//设置总数据集合
					this.totalPool = [];
				}
			},
			//筛选比例集合
			filterRateList(cpName, sName, cName, type, operateType) {
				//cpName-卡池名称
				//sName-供应商名称
				//cName-国家/地区名称
				//type-过滤方式 all/filled
				var cardPoolList = [];
				if (this.totalPool.length > 0) {
					this.totalPool.map((cp, index) => {
						//匹配卡池名称
						var cpNameFlag = cp.poolName != null ? cp.poolName.indexOf(cpName) != -1 : false;
						//匹配供应商名称
						var sNameFlag = cp.supplierName != null ? cp.supplierName.indexOf(sName) != -1 : false;
						var parent = {
							title: cp.title,
							id: cp.id,
							poolName: cp.poolName,
							supplierName: cp.supplierName,
							expand: true,
							children: []
						};
						//满足卡池名称&供应商名称匹配
						if (cpNameFlag && sNameFlag) {
							cp.children.map((obj, index) => {
								//匹配国家/地区名称
								var cNameFlag = obj.countryEn != null ? obj.countryEn.indexOf(cName) != -1 : false;
								if (cNameFlag) {
									if ('all' == type) {
										parent.children.push(obj);
									}
									if ('filled' == type && obj.rate != null && obj.rate != "") {
										parent.children.push(obj);
									}
								}
							});
						}
						if (parent.children.length > 0) {
							cardPoolList.push(parent);
						}
					});
				}
				if (cardPoolList.length != 0) {
					if ('edit' == operateType) {
						this.cardPoolEditTree = [{
							title: '关联卡池',
							expand: true,
							children: []
						}];
						this.cardPoolEditTree[0].children = cardPoolList.concat();
						this.filterPool = cardPoolList.concat();
						this.$forceUpdate();
					}
					if ('show' == operateType) {
						this.cardPoolTree = [{
							title: '关联卡池',
							expand: true,
							children: []
						}];
						this.cardPoolTree[0].children = cardPoolList.concat();
						this.filterPool = cardPoolList.concat();
						this.$forceUpdate();
					}
				} else {
					this.cardPoolEditTree = [];
					this.cardPoolTree = [];
					this.filterPool = [];
				}
			},
			//构建提交比例集合-初始化
			loadTotalRateList() {
				var cardPoolList = [];
				this.totalPool.map((cardPool, index) => {
					cardPool.children.map((rateObj, index) => {
						if (rateObj.rate != null) {
							cardPoolList.push({
								poolId: rateObj.poolId,
								poolName: rateObj.poolName,
								mcc: rateObj.mcc,
								rate: String(rateObj.rate)
							});
						}
					});
				});
				this.cpcrvList = cardPoolList;
			},
			//初次加载
			firstLoadCardPool(countryList) {
				var id = this.formObj.id;
				if (this.mccListTemp != JSON.stringify(countryList)) {
					this.mccListTemp = JSON.stringify(countryList);
					cardPoolRatio({
						usageType: this.formObj.isTerminal == '1' ? '3' : '1',
						mccList: countryList,
						packageId: id == undefined ? null : id
					}).then(res => {
						if (res && res.code == '0000') {
							var data = res.data;
							//加载卡池集合数据
							this.formObj.cardPool = data.data;
							//排序
							this.formObj.cardPool.sort(function(str1, str2) {
								return str1.poolName.localeCompare(str2.poolName);
							});
							//加载卡池集合数据->totalPool
							this.loadTreeData(this.formObj.cardPool);
							//筛选比例集合-筛选已填信息->过滤totalPool
							this.filterRateList("", "", "", "filled", "show");
							//构建提交比例集合-初始化->构建提交数组cpcrvList
							this.loadTotalRateList();
						} else {
							throw res
						}
					}).catch((err) => {

					}).finally(() => {

					})
				} else {
					//加载卡池集合数据->totalPool
					this.loadTreeData(this.formObj.cardPool);
					//筛选比例集合-筛选已填信息->过滤totalPool
					this.filterRateList("", "", "", "filled", "show");
					//构建提交比例集合-初始化->构建提交数组cpcrvList
					this.loadTotalRateList();
				}
			},
			//加载卡池信息-编辑信息初始化/查看关联卡池信息
			loadCardPoolView(countryList) {
				var id = this.formObj.id;
				if (this.mccListTemp != JSON.stringify(countryList)) {
					this.mccListTemp = JSON.stringify(countryList);
					cardPoolRatio({
						usageType: this.formObj.isTerminal == '1' ? '3' : '1',
						mccList: countryList,
						packageId: id == undefined ? null : id
					}).then(res => {
						if (res && res.code == '0000') {
							var data = res.data;
							//加载卡池集合数据
							this.formObj.cardPool = data.data;
							//排序
							this.formObj.cardPool.sort(function(str1, str2) {
								return str1.poolName.localeCompare(str2.poolName);
							});
							//加载卡池集合数据->totalPool
							this.loadTreeData(this.formObj.cardPool);
							//筛选比例集合-筛选已填信息->过滤totalPool
							this.filterRateList("", "", "", "filled", "show");
							//临时保存暂存数据->用于取消恢复
							// this.totalTempPool = this.totalPool.concat();
							// this.filterTempPool = this.filterPool.concat();
							//构建提交比例集合(非首次加载不用构建)
							//弹出抽屉
							this.drawer = true;
						} else {
							throw res
						}
					}).catch((err) => {

					}).finally(() => {

					})
				} else {
					//加载卡池集合数据->totalPool
					this.loadTreeData(this.formObj.cardPool);
					//筛选比例集合-筛选已填信息->过滤totalPool
					this.filterRateList("", "", "", "filled", "show");
					//临时保存暂存数据->用于取消恢复
					// this.totalTempPool = this.totalPool.concat();
					// this.filterTempPool = this.filterPool.concat();
					//构建提交比例集合(非首次加载不用构建)
					//弹出抽屉
					this.drawer = true;
				}
			},
			//抽屉关闭
			drawerClose() {
				if (this.typeFlag != 'Info') {
					this.mccListTemp = '';
					this.formObj.cardPool = [];
					this.cpcrvList = [];
          this.$refs['formObj'].validateField('cardPool');
				}
				this.drawer = false;
			},
			//卡池提交
			toSetCardPool() {
				var cardPoolList = [];
				var poolMccList = [];
				if (this.totalPool.length > 0) {
					//获取树值做逻辑判断
					this.totalPool.map((cardPool, index) => {
						cardPool.children.map((rateObj, index) => {
							if (rateObj.rate != null && rateObj.rate != 0) {
								cardPoolList.push({
									poolId: rateObj.poolId,
									poolName: rateObj.poolName,
									mcc: rateObj.mcc,
									rate: String(rateObj.rate)
								});
								poolMccList.push(rateObj.mcc);
							}
						});
					});
				}


				//参数校验
				var reg = new RegExp("^(\\d|[0-9]\\d|100)$");
				var submitFlag = true;
				for (var i = 0; i < cardPoolList.length; i++) {
					//限制0-100整数输入
					if (!reg.test(cardPoolList[i].rate) && cardPoolList[i].rate != '') {
						this.$Notice.warning({
							title: '操作提示',
							desc: '分配比输入错误(仅支持0-100)'
						});
						submitFlag = false;
						return false;
					}
				}

				//分组
				var groups = [];
				cardPoolList.map((item, index) => {
					var mcc = item.mcc;
					if (!groups[mcc]) {
						groups[mcc] = [];
					}
					groups[mcc].push({
						key: index,
						value: Number(item.rate)
					});
				});
				//判断比例值 唯一默认100%
				for (var num in groups) {
					var group = groups[num];
					if (group.length == 1) {
						cardPoolList[group[0].key].rate = '100';
					} else {
						var total = 0;
						group.map((item, index) => {
							total = total + item.value;
						});
						if (total != 100) {
							var name = this.localMap.has(num) ? this.localMap.get(num) : '各国家';
							this.$Notice.warning({
								title: '操作提示',
								desc: name + '分配比需满足100%'
							})
							submitFlag = false;
							//跳出group循环
							return false;
						}
					}

					//跳出groups循环
					if (!submitFlag) {
						return false;
					}
				}
				//勾选国家
				var mccList = this.formObj.mccList;
				//判断所选国家是否匹配完全
				for (var j = 0; j < mccList.length; j++) {
					if (poolMccList.indexOf(mccList[j]) == -1) {
						var name = this.localMap.has(mccList[j]) ? this.localMap.get(mccList[j]) : '存在国家/地区';
						this.$Notice.warning({
							title: '操作提示',
							desc: name + '未分配比例'
						})
						submitFlag = false;
						return false;
					}
				}

				if (!submitFlag) {
					return false;
				} else {
					//提交逻辑
					this.cpcrvList = cardPoolList;
          this.$refs['formObj'].validateField('cardPool');
					this.drawer = false;
				}
			},
			isTerminalChange(e) {
				if ('1' == e) {
					//清除非终端厂商的缓存
					this.formObj.isSupportedHotspots = ""
					this.formObj.noLimitTemplateId = ""
					this.formObj.packageConsumptionStr = [{
						consumption: "",
						upccTemplateId: "",
						unit: "MB",
					}]
					this.formObj.mccList = []
					this.formObj.bindCardPoolType = ""
					this.formObj.flowLimitSum = ""
					this.getCompanyList();
				} else {
					//清除终端厂商的缓存
					this.formObj.flowLimitSum = ""
					this.formObj.signBizId = ""
					this.formObj.limitSignBizId = ""
					this.formObj.slowSignBizId = ""
				}
				this.formObj.controlLogic = this.formObj.flowLimitType === 2 ? 1 :
				this.formObj.isTerminal === '1' && this.formObj.flowLimitType === 1 ? 2 : null
				this.mccListTemp = '';
				this.formObj.cardPool = [];
				this.cpcrvList = [];
				this.formObj.deductionModel = ''; //清除套餐扣费模式
				this.formObj.deductionUrl = ''; //清除套餐扣费URL
			},
			//获取厂商集合
			getCompanyList() {
				getPage({
					pageNumber: 1,
					pageSize: -1,
					corpType: '7'
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						this.corpIdList = data.records;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//返回
			reback() {
				this.$router.push({
					name: 'packageIndex'
				})
			},
			//删除卡池绑定方式——清空国家、国家卡池关联组
			clearBindCardpoolType(){
				this.formObj.mccList = []
				this.formObj.groupId = ""
			},
			clearGroupId(){
				this.formObj.mccList = []
			},
			//卡池绑定方式发生变化时
			changeBindCardpoolType() {
				if(this.formObj.bindCardPoolType == '1'){
					// 切换到关联卡池时
					// 1. 清空国家卡池关联组相关数据
					this.formObj.groupId = "";
					this.continentList1 = [];

					// 2. 清空国家/地区选择
					this.formObj.mccList = [];

					// 3. 获取关联卡池的国家/地区列表
					this.getLocalList();
				}
				if (this.formObj.bindCardPoolType == '2') {
					// 切换到国家卡池关联组时
					// 1. 清空关联卡池相关数据
					this.formObj.cardPool = [];
					this.cpcrvList = [];
					this.totalPool = [];
					this.filterPool = [];
					this.cardPoolTree = [];
					this.cardPoolEditTree = [];

					// 2. 清空国家/地区选择
					this.formObj.mccList = [];
				}
			},
			// 根据是否支持热点值获取模板
			getcountryList(value){
				if(value){
					// 重新获取模板列表
					this.getSelectTemplate(value);
					// 重置已选择的模板
					this.formObj.packageConsumptionStr.forEach(item => {
						item.upccTemplateId = "";
					});
					// 重置无上限模板
					this.formObj.noLimitTemplateId = "";
				}
			},
			//当国家卡池有值时  调选择国家/地区接口
			changeGroupId(value){
				this.formObj.mccList = []
				if (value) {
					this.getLocalList2()
				}
			},
			//选择模板查询、无上限模板查询
			getSelectTemplate(isSupportedHotspots) {
				getTemplate({
					isSupportedHotspots: isSupportedHotspots
				}).then(res => {
					if (res && res.code == '0000') {
						this.TemplateList = res.data;
					} else {
						throw res
					}
				}).catch((err) => {})
			},
			//删除模板
			removeTemplate(index) {
				this.formObj.packageConsumptionStr.splice(index, 1);
			},
			//添加模板
			addTemplate() {
				this.formObj.packageConsumptionStr.push({
					consumption: "",
					upccTemplateId: "",
					unit: "MB",
				});
			},
			//改变是否支持定向流量值时清空数据
			changeDirect(data) {},
			// 将用量值转换为MB
			convertToMB(value, unit) {
				if (!value) return 0;
				const numValue = parseFloat(value);
				switch (unit) {
					case 'TB':
						return numValue * 1024 * 1024; // TB to MB
					case 'GB':
						return numValue * 1024; // GB to MB
					case 'MB':
						return numValue;
					default:
						return numValue;
				}
			},
		},
		mounted() {
			this.groupIdListChange()
			this.getLocalList()
			try {
				if (this.$route.query.package != null) {
					this.firstLoad = true;
					var packageInfo = JSON.parse(decodeURIComponent(this.$route.query.package));
					this.typeFlag = packageInfo.type;
					packageInfo.startTime = this.dateToStr(packageInfo.startTime);
					packageInfo.endTime = this.dateToStr(packageInfo.endTime);
					if (packageInfo.isTerminal == '1') {
						this.getCompanyList();
					}
					this.formObj = Object.assign({}, packageInfo);
					this.formObj.picture = null;
					this.firstLoadCardPool(packageInfo.mccList);
					this.pictureUrl = packageInfo.coverUrl;
				}
			} catch (e) {
			}
		},
	}
</script>

<style scoped="scoped">
	.inputSty {
		width: 200px;
	}

	.mediaShowDelSty {
		position: absolute;
		right: -11px;
		top: -11px;
		z-index: 996;
		background-color: #ffffff;
		border-radius: 50%;
	}

	.demo-drawer-footer {
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		border-top: 1px solid #e8e8e8;
		padding: 10px 16px;
		text-align: right;
		background: #fff;
	}

	.demo-spin-article {
		height: 300px;
		width: 100%;
		display: inline-block;
		position: relative;
		/* border: 1px solid #eee; */
	}

	::v-deep .ivu-select{
	  position:relative;
	}

	::v-deep .ivu-select .ivu-select-dropdown{
	  width: 100% !important;
	  min-width:auto !important;
	}
</style>

