<template>
  <Card>

    <!-- 步骤内容区域 -->
    <div class="step-content">
      <!-- 步骤1：基本信息配置 -->
      <CardPoolSwitchStep1 v-if="currentStep === 0" :wizard-data="wizardData" :loading="submitLoading"
        @form-change="handleFormChange" @next-step="handleNextStep" @cancel="handleCancel" />

      <!-- 步骤2：剩余套餐处理 -->
      <CardPoolSwitchStep2 v-if="currentStep === 1" :task-info="taskInfo" :wizard-data="wizardData"
        @prev-step="handlePrevStep" @finish="handleFinish" @back-to-list="handleBackToList" />

      <!-- 步骤3：完成页面 -->
      <div v-if="currentStep === 2" class="completion-step">
        <div class="completion-content">
          <Icon type="ios-checkmark-circle" size="64" color="#19be6b" />
          <h2>卡池切换任务创建成功！</h2>
          <p>任务ID: {{ taskInfo.taskId }}</p>
          <p>规则名称: {{ taskInfo.ruleName }}</p>
          <div class="completion-actions">
            <Button type="primary" @click="handleBackToList">返回列表</Button>
            <Button @click="handleViewDetails" style="margin-left: 8px;">查看详情</Button>
          </div>
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
import CardPoolSwitchStep1 from './components/CardPoolSwitchStep1.vue';
import CardPoolSwitchStep2 from './components/CardPoolSwitchStep2.vue';
import { createCardPoolSwitchTask } from '@/api/package/cardPoolSwitch';

export default {
  name: 'CardPoolSwitchWizard',
  components: {
    CardPoolSwitchStep1,
    CardPoolSwitchStep2
  },
  data () {
    return {
      currentStep: 0, // 当前步骤：0-基本信息，1-剩余套餐，2-完成
      submitLoading: false,

      // 向导数据 - 在父组件中统一管理
      wizardData: {
        // 基本信息
        ruleName: '',
        originalSupplier: '',
        targetCountries: [],
        backups: [
          {
            supplier: "",
            pool: "",
            percentage: null,
            loadingPools: false,
            poolList: [], // 每个备用项都有独立的空数组
            poolListChecked: false, // 标记卡池列表是否已检查完成
            _uid: Date.now(), // 添加唯一标识}
          }
        ]
      },

      // 任务信息
      taskInfo: {
        taskId: '',
        ruleName: '',
        standardPackageCount: 0
      }
    };
  },
  methods: {
    // 处理表单数据变更
    handleFormChange (formData) {
      this.wizardData = { ...this.wizardData, ...formData };
    },

    // 处理下一步
    async handleNextStep (formData) {
      this.wizardData = { ...this.wizardData, ...formData };

      try {
        this.submitLoading = true;

        // 准备提交数据
        const payload = {
          ruleName: this.wizardData.ruleName,
          originalSupplierId: this.wizardData.originalSupplier,
          targetCountries: this.wizardData.targetCountries,
          newSuppliers: this.wizardData.backups.map(supplier => ({
            supplierId: supplier.supplier,
            poolId: supplier.pool,
            percentage: this.wizardData.backups.length === 1 ? 100 : supplier.percentage
          }))
        };

        console.log('提交数据:', payload);


        // 调用API创建卡池切换任务
        // const res = await createCardPoolSwitchTask(payload);
        // if (res.code === '0000') {
        // 设置任务信息
        // this.taskInfo = {
        //   taskId: res.data.taskId || 'task_' + Date.now(),
        //   ruleName: this.wizardData.ruleName,
        //   standardPackageCount: res.data.standardPackageCount || 15
        // };

        // 进入下一步
        this.currentStep = 1;
        this.$Message.success('基本配置保存成功，进入剩余套餐处理');
        // } else {
        //   this.$Message.error(res.msg || '创建失败');
        // }
      } catch (error) {
        console.error('创建卡池切换任务失败:', error);
        this.$Message.error('创建失败，请重试');
      } finally {
        this.submitLoading = false;
      }
    },

    // 处理上一步
    handlePrevStep () {
      if (this.currentStep > 0) {
        this.currentStep--;
      }
    },

    // 处理完成
    handleFinish () {
      this.currentStep = 2;
    },

    // 取消操作
    handleCancel () {
      this.$Modal.confirm({
        title: '确认取消',
        content: '确定要取消创建卡池切换任务吗？未保存的数据将丢失。',
        onOk: () => {
          this.handleBackToList();
        }
      });
    },

    // 返回列表
    handleBackToList () {
      this.$router.push({ name: 'cardPoolSwitchList' });
    },

    // 查看详情
    handleViewDetails () {
      this.$Message.info('查看详情功能待开发');
      // 可以跳转到详情页面
      // this.$router.push({ 
      //   name: 'cardPoolSwitchDetail', 
      //   params: { id: this.taskInfo.taskId } 
      // });
    }
  }
};
</script>

<style scoped>
.steps-container {
  margin-bottom: 30px;
  padding: 20px 0;
}

.step-content {
  min-height: 400px;
}

.completion-step {
  text-align: center;
  padding: 60px 20px;
}

.completion-content h2 {
  margin: 20px 0 10px 0;
  color: #333;
}

.completion-content p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.completion-actions {
  margin-top: 30px;
}
</style>
