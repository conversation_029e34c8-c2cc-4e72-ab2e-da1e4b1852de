<template>
  <div class="step1-container">
    <Form ref="formRef" :model="formData" :rules="ruleValidate" :label-width="120">
      <Row>
        <Col span="12">
        <FormItem label="规则名称" prop="ruleName">
          <Input v-model="formData.ruleName" placeholder="请输入规则名称" maxlength="100" style="width: 400px" />
        </FormItem>
        </Col>
        <Col span="12">
        <FormItem label="原资源供应商" prop="originalSupplier">
          <Select v-model="formData.originalSupplier" placeholder="请选择" clearable filterable style="width: 400px">
            <Option v-for="supplier in supplierList" :value="supplier.supplierId" :key="supplier.supplierId">{{ supplier.supplierName }}</Option>
          </Select>
        </FormItem>
        </Col>
      </Row>

      <!-- 目标国家选择 -->
      <FormItem label="目标国家" prop="targetCountries" :required="true" :show-message="false">
        <TargetCountrySelector v-model="formData.targetCountries" :all-countries="filteredCountries"
          :continent-list="continentList" :current-continent="selectedContinent" :check-all="countryCheckAll"
          :indeterminate="countryIndeterminate" placeholder="请选择目标国家" :show-error="targetCountryError"
          :error-message="'目标国家不能为空'" @continent-change="handleContinentChange"
          @selection-change="handleSelectionChange" @check-all="handleCheckAll"
          @input="triggerTargetCountriesValidation" />
        <!-- 加载状态 -->
        <Spin v-if="loadingCountries || loadingContinents" fix size="large"></Spin>
      </FormItem>

      <div v-for="(backup, index) in formData.backups" :key="index">
        <Row :gutter="16">
          <Col span="6">
          <FormItem label="新资源供应商" :prop="`backups.${index}.supplier`" :rules="ruleValidate.newSuppliers">
            <Select filterable clearable v-model="backup.supplier" placeholder="请选择"
              @on-change="(val) => handleBackupSupplierChange(index, val)" remote
              :remote-method="(query) => remoteSearchSupplier(query, index)" :loading="loadingBackupSuppliers"
              @on-open-change="
                (open) => handleOpenBackupSupplierSelect(index, open)
              ">
              <Option v-if="loadingBackupSuppliers" value="" disabled>加载中</Option>
              <Option v-for="supplier in availableBackupSuppliers(index)" :value="supplier.supplierId"
                :key="supplier.supplierId">{{ supplier.supplierName }}</Option>
              <Option v-if="
                !loadingBackupSuppliers &&
                backupSupplierListChecked &&
                backupSupplierList.length === 0
              " value="" disabled>无数据</Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="7">
          <FormItem label="备用卡池" :prop="`backups.${index}.pool`" :rules="ruleValidate.backupPool">
            <Select v-model="backup.pool" placeholder="请选择" clearable filterable remote
              :remote-method="(query) => remoteSearchPool(query, index)" :loading="backup.loadingPools" @on-open-change="
                (open) => open && handleOpenCardPoolSelect(index)
              " class="backups-pool-select">
              <Option v-if="backup.loadingPools" value="" disabled>加载中</Option>
              <Option v-for="poolItem in backup.poolList" :value="poolItem.poolId" :key="poolItem.poolId">{{
                poolItem.poolName }}</Option>
              <Option v-if="
                !backup.loadingPools &&
                backup.poolListChecked &&
                backup.poolList &&
                backup.poolList.length === 0
              " value="" disabled>无数据</Option>
            </Select>
          </FormItem>
          </Col>
          <Col span="5">
          <FormItem label="比例" :prop="`backups.${index}.percentage`" :rules="ruleValidate.percentage" :label-width="60">
            <InputNumber :min="0" :max="100" v-model="backup.percentage" placeholder="比例" style="width: 80%"
              @on-change="(value) => handlePercentageChange(index, value)">
            </InputNumber>
            %
          </FormItem>
          </Col>
          <Col span="6">
          <FormItem :label-width="0" v-if="formData.backups.length > 1">
            <Button type="error" size="small" @click="removeBackup(index)" icon="ios-remove" title="移除此备用供应商"></Button>
          </FormItem>
          <FormItem :label-width="0" v-if="index === formData.backups.length - 1">
            <Button type="dashed" size="small" @click="addBackup" icon="ios-add" title="添加备用供应商"></Button>
          </FormItem>
          </Col>
        </Row>
      </div>

      <Divider />

      <div style="text-align: center">
        <Button type="primary" @click="handleNext" :loading="loading">下一步</Button>
        <Button @click="handleCancel" style="margin-left: 8px">取消</Button>
      </div>
    </Form>
  </div>
</template>

<script>
import TargetCountrySelector from "@/components/TargetCountrySelector.vue";
// 导入新增的 API 函数
import {
  getSuppliers,
  getCountries,
  getContinents,
  saveSupplierFaultRule,
  getCardPoolsByMcc,
  getSuppliersByMccList,
} from "@/api/server/faultHandling";

export default {
  name: "CardPoolSwitchStep1",
  components: {
    TargetCountrySelector,
  },
  props: {
    wizardData: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    // 自定义百分比总和验证器
    const validatePercentageSum = (rule, value, callback) => {
      // 不管有几条备用资源供应商，都要验证百分比总和为100%
      const totalPercentage = this.formData.backups.reduce((sum, backup) => {
        // 确保 backup.percentage 是数字
        return sum + (Number(backup.percentage) || 0);
      }, 0);

      // 使用浮点数比较时注意精度问题，可以允许微小误差
      if (Math.abs(totalPercentage - 100) > 0.01) {
        callback(new Error("比例总和必须等于100%"));
      } else {
        callback();
      }
    };

    return {
      submitLoading: false,
      formData: {
        ruleName: "",
        originalSupplier: "",
        targetCountries: [], // 存储选中的国家 MCC
        backups: [
          this.createNewBackupItem(), // 确保初始化时创建独立的备用项
        ],
      },
      // 目标国家选择器相关状态
      selectedContinent: "all", // 当前选中的大洲
      countryCheckAll: false, // 全选状态
      countryIndeterminate: false, // 不确定状态

      allCountries: [], // 原始国家列表 (存储所有已加载的国家数据，用于排序)
      filteredCountries: [], // 传递给子组件的国家列表 (包含 mcc)
      continentList: [],
      supplierList: [], // 全部供应商列表 - 用于故障资源供应商选择
      backupSupplierList: [], // 基于目标国家获取的备用供应商列表
      loadingCountries: false,
      loadingContinents: false,
      loadingBackupSuppliers: false, // 加载备用供应商的状态
      ruleValidate: {
        ruleName: [
          { required: true, message: "规则名称不能为空", trigger: "blur" },
        ],
        originalSupplier: [
          {
            required: true,
            message: "故障资源供应商不能为空",
            trigger: "change",
          },
        ],
        targetCountries: [
          // 验证 formData.targetCountries (MCC 数组) 是否为空
          {
            required: true,
            type: "array",
            min: 1,
            message: "目标国家不能为空",
            trigger: "change",
          },
        ],
        newSuppliers: [
          {
            required: true,
            message: "备用资源供应商不能为空",
            trigger: "change",
          },
        ],
        backupPool: [
          { required: true, message: "备用卡池不能为空", trigger: "change" },
        ],
        percentage: [
          {
            required: true,
            type: "number",
            message: "比例不能为空",
            trigger: "blur",
          },
          {
            type: "number",
            min: 0.01,
            max: 100,
            message: "比例必须在 0 到 100 之间",
            trigger: "blur",
          },
          { validator: validatePercentageSum, trigger: "blur" }, // 应用总和校验
        ],
      },
      // 增加缓存相关数据
      supplierCache: {
        mccKey: "", // 缓存的MCC列表key
        isLoading: false, // 是否正在加载
        noDataKeys: [], // 记录已确认没有数据的key列表
      },
      poolCache: {
        noDataKeys: [], // 记录已确认没有数据的key列表
      }, // 卡池缓存 {supplierId: {isLoading, lastMccKey}}
      backupSupplierListChecked: false, // 标记备用供应商列表是否已检查完成
      targetCountryError: false, // 新增的 targetCountryError 状态变量
    };
  },
  computed: {
    availableBackupSuppliers () {
      return (index) => {
        return this.backupSupplierList;
      };
    },
    // 获取当前视图中的MCC列表
    currentViewCountryMccs () {
      return this.filteredCountries
        .map((country) => country.mcc)
        .filter((mcc) => mcc != null);
    },
  },
  watch: {
    formData: {
      handler (newVal) {
        // 避免循环更新：只有当数据真正不同时才发出事件
        if (JSON.stringify(this.formData) !== JSON.stringify(newVal)) {
          this.$emit("form-change", newVal);
        }
      },
      deep: true,
    },
// 修改监听目标国家变化的逻辑
    'formData.targetCountries': {
      handler(newVal, oldVal) {
        if (!oldVal || !newVal) return;
        if (newVal.length > 0 && Array.isArray(newVal)) {
          const sortedVal = this.sortTargetCountriesByEn(newVal);
          if (JSON.stringify(sortedVal) !== JSON.stringify(newVal)) {
            this.formData.targetCountries = sortedVal;
            return;
          }
        }

      }
    }
  },
  mounted () {
    this.fetchInitialData();
    this.formData = {...this.wizardData}
  },
  methods: {
    async fetchInitialData () {
      this.loadingContinents = true;
      this.loadingCountries = true; // Also set loading for initial country fetch

      const continentPromise = getContinents()
        .then((res) => {
          if (res.code == "0000" && Array.isArray(res.data)) {
            this.continentList = res.data;
          } else {
            this.continentList = [];
          }
        })
        .catch((err) => {
          console.error("获取大洲列表失败:", err);
          this.continentList = [];
        })
        .finally(() => {
          this.loadingContinents = false;
        });

      const supplierPromise = getSuppliers()
        .then((res) => {
          if (res.code == "0000" && Array.isArray(res.data)) {
            // API 返回数据直接是数组
            this.supplierList = res.data;
          } else {
            this.supplierList = [];
          }
        })
        .catch((err) => {
          console.error("获取供应商列表失败:", err);
          this.supplierList = [];
        });

      await Promise.all([continentPromise, supplierPromise]);

      // Fetch initial countries for 'all'
      await this.fetchCountries(this.selectedContinent); // Wait for initial countries
    },

    // 获取基于目标国家的供应商列表
    async fetchSuppliersByMcc () {
      // 如果目标国家为空，不执行操作
      if (
        !this.formData.targetCountries ||
        this.formData.targetCountries.length === 0
      ) {
        this.backupSupplierList = []; // 清空备用供应商列表
        this.backupSupplierListChecked = false; // 重置检查标记
        return;
      }

      const mccKey = this.getMccCacheKey();

      // 检查当前mccKey是否已经请求过且确认没有数据
      if (
        this.supplierCache.noDataKeys &&
        this.supplierCache.noDataKeys.includes(mccKey)
      ) {
        console.log("此目标国家组合已确认没有可用的备用供应商，跳过请求");
        this.backupSupplierList = [];
        this.loadingBackupSuppliers = false;
        this.backupSupplierListChecked = true; // 设置检查标记为true
        return;
      }

      this.supplierCache.isLoading = true;
      this.loadingBackupSuppliers = true; // 设置加载状态为true
      this.backupSupplierListChecked = false; // 重置检查标记

      try {
        console.log("获取目标国家对应的备用供应商，请求参数:", {
          mccList: this.formData.targetCountries,
        });
        const res = await getSuppliersByMccList({
          mccList: this.formData.targetCountries,
        });

        if (res.code === "0000" && Array.isArray(res.data)) {
          this.backupSupplierList = res.data;
          console.log("获取备用供应商成功:", this.backupSupplierList);

          // 更新缓存key
          this.supplierCache.mccKey = mccKey;

          // 如果返回的数据为空，记录到noDataKeys中
          if (res.data.length === 0) {
            if (!this.supplierCache.noDataKeys) {
              this.supplierCache.noDataKeys = [];
            }
            if (!this.supplierCache.noDataKeys.includes(mccKey)) {
              this.supplierCache.noDataKeys.push(mccKey);
            }
            console.log("此目标国家组合没有可用的备用供应商，已记录");
          }

          // 检查当前选中的备用供应商是否在新列表中
          this.checkAndUpdateBackupSuppliers();
          this.backupSupplierListChecked = true; // 设置检查标记为true
        } else {
          console.warn("获取备用供应商列表失败:", res);
          this.backupSupplierList = [];
          // this.$Message.warning('操作失败');
          this.backupSupplierListChecked = true; // 设置检查标记为true
        }
      } catch (err) {
        console.error("获取目标国家对应的备用供应商失败:", err);
        this.backupSupplierList = [];
        // this.$Message.error('操作失败');
        this.backupSupplierListChecked = true; // 设置检查标记为true
      } finally {
        this.loadingBackupSuppliers = false; // 设置加载状态为false
        this.supplierCache.isLoading = false;
      }
    },

    // 检查并更新备用供应商选择
    checkAndUpdateBackupSuppliers () {
      // 如果没有备用供应商列表数据，直接返回
      if (this.backupSupplierList.length === 0) return;

      // 获取当前可用的备用供应商ID列表
      const availableSupplierIds = this.backupSupplierList.map(
        (s) => s.supplierId
      );

      // 遍历所有备用项，检查选择的供应商是否在新列表中
      this.formData.backups.forEach((backup, index) => {
        if (
          backup.supplier &&
          !availableSupplierIds.includes(backup.supplier)
        ) {
          // 如果当前选择的供应商不在新列表中，清空该备用项的供应商和卡池选择
          this.$set(backup, "supplier", "");
          this.$set(backup, "pool", "");
          this.$set(backup, "poolList", []);
        } else if (backup.supplier) {
          // 如果供应商有效，保留现有选择，只更新卡池列表
          // 确保每个已选择供应商的卡池列表能够被正确加载
          this.fetchBackupPools(index);
        }
      });
    },

    async fetchCountries (continentEn = "all") {
      this.loadingCountries = true;
      this.filteredCountries = []; // Clear old data
      try {
        const res = await getCountries({
          continent: continentEn === "all" ? "" : continentEn,
        });
        if (res.code == "0000" && Array.isArray(res.data)) {
          // 按英文名排序
          const sortByEn = (arr) => {
            return arr.slice().sort((a, b) => {
              return (a.countryEn || "").localeCompare(b.countryEn || "");
            });
          };
          // 过滤掉没有mcc的国家
          let filtered = res.data.filter((c) => c.mcc != null);
          // 排序
          this.filteredCountries = sortByEn(filtered);
          // 保存到allCountries，用于排序查找
          if (!this.allCountries) {
            this.allCountries = [];
          }
          // 将新的国家数据合并到allCountries，避免重复
          filtered.forEach((country) => {
            if (!this.allCountries.some((c) => c.mcc === country.mcc)) {
              this.allCountries.push(country);
            }
          });

          const missingMccCount =
            res.data.length - this.filteredCountries.length;
          if (missingMccCount > 0) {
            console.warn(`${missingMccCount} 个国家缺少 MCC，已被过滤。`);
          }

          // 确保当前已选国家也按英文名排序
          if (
            this.formData.targetCountries &&
            this.formData.targetCountries.length > 0
          ) {
            this.formData.targetCountries = this.sortTargetCountriesByEn(
              this.formData.targetCountries
            );
          }

          // 加载新国家列表后更新全选状态
          this.updateCheckAllStatus();
        } else {
          this.filteredCountries = [];
          // this.$Message.warning('操作失败');
        }
      } catch (err) {
        this.filteredCountries = [];
        this.$Message.error("操作失败");
      } finally {
        this.loadingCountries = false;
        // 移除初始验证目标国家
      }
    },

    // 处理大洲切换
    handleContinentChange (continentEn) {
      console.log("接收到大洲变更事件:", continentEn);
      if (this.selectedContinent !== continentEn) {
        this.selectedContinent = continentEn;
        this.fetchCountries(continentEn);
      }
    },

    // 按英文名排序目标国家mcc数组
    sortTargetCountriesByEn (targetCountries) {
      // 收集所有国家数据以便查找英文名
      const allCountriesMap = {};

      // 合并所有可能包含国家数据的数组
      const allCountrySources = [
        ...(this.filteredCountries || []),
        ...(this.allCountries || []),
      ];

      // 创建mcc->country映射
      allCountrySources.forEach((country) => {
        if (country && country.mcc) {
          // 如果已存在同样的mcc，保留有英文名的或第一个
          if (
            !allCountriesMap[country.mcc] ||
            (!allCountriesMap[country.mcc].countryEn && country.countryEn)
          ) {
            allCountriesMap[country.mcc] = country;
          }
        }
      });

      console.log("排序前:", JSON.stringify(targetCountries));

      // 根据英文名排序
      const result = targetCountries.slice().sort((a, b) => {
        const aCountry = allCountriesMap[a] || {};
        const bCountry = allCountriesMap[b] || {};
        const aName = aCountry.countryEn || "";
        const bName = bCountry.countryEn || "";
        console.log(`比较: ${aName} vs ${bName}`);
        return aName.localeCompare(bName);
      });

      console.log("排序后:", JSON.stringify(result));
      return result;
    },

    // 处理选择变更
    handleSelectionChange (selectedMccs, currentViewMccs) {
      // 只保留当前选中的国家，不再强制保留 outsideSelections
      let newTargetCountries = [...new Set(selectedMccs)];
      // 重要：总是对结果进行排序
      newTargetCountries = this.sortTargetCountriesByEn(newTargetCountries);
      // 检查是否有实际内容变化（非排序变化）
      const oldSet = new Set(this.formData.targetCountries);
      const newSet = new Set(newTargetCountries);
      const hasAddedCountry = newTargetCountries.some(
        (mcc) => !oldSet.has(mcc)
      );
      const hasRemovedCountry = this.formData.targetCountries.some(
        (mcc) => !newSet.has(mcc)
      );
      const hasContentChanged = hasAddedCountry || hasRemovedCountry;
      // 更新数据
      this.formData.targetCountries = newTargetCountries;
      // 更新全选状态
      this.updateCheckAllStatus();
      // 如果目标国家有实际内容变化，重新获取备用供应商列表
      if (hasContentChanged) {
        console.log("目标国家内容发生变化，重置资源供应商和卡池");
        // 清空相关缓存和数据
        this.backupSupplierList = [];
        this.supplierCache.mccKey = "";
        this.supplierCache.noDataKeys = []; // 清空无数据缓存
        this.poolCache = {
          noDataKeys: [], // 重置卡池缓存，但保留noDataKeys结构
        };
        // 重置所有备用项
        this.formData.backups.forEach((backup, index) => {
          if (backup) {
            this.$set(backup, "supplier", "");
            this.$set(backup, "pool", "");
            this.$set(backup, "poolList", []);
          }
        });
        // 如果目标国家不为空，重新获取备用供应商列表
        if (this.formData.targetCountries.length > 0) {
          this.fetchSuppliersByMcc();
        }
      }
      // 添加表单验证触发，用户交互后应显示错误
      this.$nextTick(() => {
        this.triggerTargetCountriesValidation(true);
      });
    },

    // 处理全选/取消全选
    handleCheckAll ({ currentMccs, isCurrentlyAllSelected }) {
      const oldSelection = [...this.formData.targetCountries];
      if (isCurrentlyAllSelected) {
        this.formData.targetCountries = this.formData.targetCountries.filter(
          (mcc) => !currentMccs.includes(mcc)
        );
      } else {
        const mccsToAdd = currentMccs.filter(
          (mcc) => !this.formData.targetCountries.includes(mcc)
        );
        this.formData.targetCountries = [
          ...this.formData.targetCountries,
          ...mccsToAdd,
        ];
      }
      // 排序
      this.formData.targetCountries = this.sortTargetCountriesByEn(
        this.formData.targetCountries
      );
      this.updateCheckAllStatus();

      // 检查是否有实际内容变化
      const newSelection = this.formData.targetCountries;
      const oldSet = new Set(oldSelection);
      const newSet = new Set(newSelection);
      const hasAddedCountry = newSelection.some((mcc) => !oldSet.has(mcc));
      const hasRemovedCountry = oldSelection.some((mcc) => !newSet.has(mcc));
      const hasContentChanged = hasAddedCountry || hasRemovedCountry;

      if (hasContentChanged) {
        console.log("全选/取消全选导致目标国家变化，重置备用资源供应商和卡池");
        // 清空相关缓存和数据
        this.backupSupplierList = [];
        this.supplierCache.mccKey = "";
        this.supplierCache.noDataKeys = []; // 清空无数据缓存
        this.poolCache = {
          noDataKeys: [], // 重置卡池缓存，但保留noDataKeys结构
        };

        // 重置所有备用项
        this.formData.backups.forEach((backup, index) => {
          if (backup) {
            this.$set(backup, "supplier", "");
            this.$set(backup, "pool", "");
            this.$set(backup, "poolList", []);
          }
        });

        // 如果目标国家不为空，重新获取备用供应商列表
        if (this.formData.targetCountries.length > 0) {
          this.fetchSuppliersByMcc();
        }
      }

      this.$nextTick(() => {
        this.triggerTargetCountriesValidation(true);
      });
    },

    // 更新全选状态
    updateCheckAllStatus () {
      const currentMccs = this.currentViewCountryMccs;
      if (!currentMccs || currentMccs.length === 0) {
        this.countryIndeterminate = false;
        this.countryCheckAll = false;
        return;
      }

      // 检查当前视图中选中的数量
      const selectedCountInView = this.formData.targetCountries.filter((mcc) =>
        currentMccs.includes(mcc)
      ).length;

      if (selectedCountInView === 0) {
        this.countryIndeterminate = false;
        this.countryCheckAll = false;
      } else if (selectedCountInView === currentMccs.length) {
        this.countryIndeterminate = false;
        this.countryCheckAll = true;
      } else {
        this.countryIndeterminate = true;
        this.countryCheckAll = false;
      }
    },

    // 故障供应商变更
    handleFaultSupplierChange (value) {
      console.log("故障供应商变更:", value);
    },

    // 备用供应商变更
    handleBackupSupplierChange (index, supplierId) {
      console.log(`备用供应商 ${index + 1} 变更:`, supplierId);
      if (this.formData.targetCountries.length === 0) {
        this.$Message.warning("请先选择目标国家");
        return;
      }

      const backup = this.formData.backups[index];
      if (!backup) return;

      // 清空当前备用卡池选择和列表
      this.$set(backup, "pool", "");
      this.$set(backup, "poolList", []);

      // 如果供应商已选，则加载卡池
      if (supplierId) {
        this.fetchBackupPools(index);
      }

      // 如果存在多个备用项，重新验证所有百分比
      if (this.formData.backups.length > 1) {
        this.$nextTick(() => {
          this.formData.backups.forEach((b, i) => {
            if (this.$refs.formRef) {
              this.$refs.formRef.validateField(`backups.${i}.percentage`);
            }
          });
        });
      }
    },

    // 添加百分比变更处理方法
    handlePercentageChange (index, value) {
      console.log(`备用项 ${index + 1} 百分比变更:`, value);
      // 如果存在多个备用项，重新验证所有百分比
      if (this.formData.backups.length > 1) {
        this.$nextTick(() => {
          this.formData.backups.forEach((b, i) => {
            this.$refs.formRef.validateField(`backups.${i}.percentage`);
          });
        });
      }
    },

    // 处理打开卡池选择器
    handleOpenCardPoolSelect (index) {
      const backup = this.formData.backups[index];
      if (!backup) return;

      if (!backup.supplier) {
        return;
      }

      if (this.formData.targetCountries.length === 0) {
        this.$Message.warning("请先选择目标国家");
        return;
      }

      const supplierId = backup.supplier;
      const mccKey = this.getMccCacheKey();

      // 生成唯一的缓存键，包含供应商ID和索引
      const cacheKey = `${supplierId}_${index}`;

      // 生成用于检查是否已确认无数据的键
      const noDataKey = `${supplierId}_${mccKey}`;

      // 检查当前supplier+mcc组合是否已确认没有数据
      if (
        this.poolCache.noDataKeys &&
        this.poolCache.noDataKeys.includes(noDataKey)
      ) {
        console.log(
          `备用供应商 ${supplierId} 在当前目标国家下已确认没有可用的卡池，跳过请求`
        );
        this.$set(this.formData.backups[index], "poolList", []);
        this.$set(backup, "poolListChecked", true); // 设置检查标记为true
        return;
      }

      // 初始化供应商缓存 - 为每个索引位置创建独立缓存
      if (!this.poolCache[cacheKey]) {
        this.$set(this.poolCache, cacheKey, {
          lastMccKey: "",
          isLoading: false,
        });
      }

      // 如果已有卡池列表且缓存key一致，不重复请求
      if (
        backup.poolList &&
        backup.poolList.length > 0 &&
        this.poolCache[cacheKey].lastMccKey === mccKey
      ) {
        console.log(
          `使用备用 ${index + 1} 供应商 ${supplierId} 的卡池列表缓存`
        );
        this.$set(backup, "poolListChecked", true); // 设置检查标记为true
        return;
      }

      // 如果正在加载，不重复请求
      if (this.poolCache[cacheKey].isLoading) {
        console.log(
          `备用 ${index + 1
          } 供应商 ${supplierId} 的卡池列表正在加载中，跳过请求`
        );
        return;
      }

      console.log(
        `为备用 ${index + 1} (供应商: ${backup.supplier}) 获取卡池列表...`
      );
      this.fetchBackupPools(index);
    },

    // 获取备用卡池
    fetchBackupPools (index) {
      const backup = this.formData.backups[index];
      if (!backup || !backup.supplier) {
        if (backup) {
          this.$set(backup, "poolList", []);
          this.$set(backup, "poolListChecked", false); // 重置检查标记
        }
        return;
      }

      const supplierId = backup.supplier;
      const mccKey = this.getMccCacheKey();

      // 生成唯一的缓存键，包含供应商ID和索引
      const cacheKey = `${supplierId}_${index}`;

      // 生成用于检查是否已确认无数据的键
      const noDataKey = `${supplierId}_${mccKey}`;

      // 初始化供应商缓存 - 为每个索引位置创建独立缓存
      if (!this.poolCache[cacheKey]) {
        this.$set(this.poolCache, cacheKey, {
          lastMccKey: "",
          isLoading: false,
        });
      }

      // 检查当前supplier+mcc组合是否已确认没有数据
      if (
        this.poolCache.noDataKeys &&
        this.poolCache.noDataKeys.includes(noDataKey)
      ) {
        console.log(
          `备用供应商 ${supplierId} 在当前目标国家下已确认没有可用的卡池，跳过请求`
        );
        this.$set(this.formData.backups[index], "poolList", []);
        this.$set(backup, "poolListChecked", true); // 设置检查标记为true
        return;
      }

      this.$set(this.poolCache[cacheKey], "isLoading", true);
      this.$set(backup, "loadingPools", true); // 设置当前备用项的加载状态
      this.$set(backup, "poolListChecked", false); // 重置检查标记

      // 使用实际API获取卡池列表
      getCardPoolsByMcc({
        mccList: this.formData.targetCountries,
        supplierId: backup.supplier,
      })
        .then((res) => {
          if (res.code === "0000" && Array.isArray(res.data)) {
            // 明确只更新当前索引的备用卡池列表，确保数据隔离
            this.$set(this.formData.backups[index], "poolList", res.data);
            console.log(`备用 ${index + 1} 卡池列表加载完成:`, res.data);

            // 如果返回的数据为空，记录到noDataKeys中
            if (res.data.length === 0) {
              if (!this.poolCache.noDataKeys) {
                this.poolCache.noDataKeys = [];
              }
              if (!this.poolCache.noDataKeys.includes(noDataKey)) {
                this.poolCache.noDataKeys.push(noDataKey);
                console.log(
                  `备用供应商 ${supplierId} 在当前目标国家下没有可用的卡池，已记录`
                );
              }
            }

            // 更新缓存
            if (this.poolCache[cacheKey]) {
              this.$set(this.poolCache[cacheKey], "lastMccKey", mccKey);
            }
            this.$set(backup, "poolListChecked", true); // 设置检查标记为true
          } else {
            this.$set(this.formData.backups[index], "poolList", []);
            this.$set(backup, "poolListChecked", true); // 设置检查标记为true
          }
        })
        .catch((err) => {
          console.error(`获取备用 ${index + 1} 卡池列表失败:`, err);
          this.$set(this.formData.backups[index], "poolList", []);
          this.$set(backup, "poolListChecked", true); // 设置检查标记为true
        })
        .finally(() => {
          if (this.formData.backups[index]) {
            this.$set(this.formData.backups[index], "loadingPools", false); // 清除加载状态
          }
          if (this.poolCache[cacheKey]) {
            this.$set(this.poolCache[cacheKey], "isLoading", false);
          }
        });
    },

    // 生成目标国家缓存key
    getMccCacheKey () {
      return this.formData.targetCountries.sort().join(",");
    },

    // 清空备用卡池
    clearBackupPools () {
      this.formData.backups.forEach((backup, index) => {
        this.$set(backup, "pool", "");
        this.$set(backup, "poolList", []);
      });
    },

    // 创建新的备用项
    createNewBackupItem () {
      return {
        supplier: "",
        pool: "",
        percentage: null,
        loadingPools: false,
        poolList: [], // 每个备用项都有独立的空数组
        poolListChecked: false, // 标记卡池列表是否已检查完成
        _uid: Date.now(), // 添加唯一标识
      };
    },

    // 添加备用
    addBackup () {
      // 使用工厂方法创建新备用项，确保有独立的数据引用
      this.formData.backups.push(this.createNewBackupItem());
      if (this.formData.backups.length > 1) {
        this.$nextTick(() => {
          this.$refs.formRef.validateField("backups.0.percentage");
        });
      }
    },

    // 移除备用
    removeBackup (index) {
      this.formData.backups.splice(index, 1);
      if (this.formData.backups.length === 1) {
        const firstBackup = this.formData.backups[0];
        firstBackup.percentage = null;
        this.$nextTick(() => {
          this.$refs.formRef.validateField(
            "backups.0.percentage",
            (errorMessage) => {
              // 如果只剩一个，比例为空是允许的
            }
          );
        });
      } else if (this.formData.backups.length > 0) {
        this.$nextTick(() => {
          this.$refs.formRef.validateField("backups.0.percentage");
        });
      }
      this.$nextTick(() => {
        this.formData.backups.forEach((b, i) => {
          if (b.supplier) {
            this.$refs.formRef.validateField(`backups.${i}.supplier`);
          }
        });
      });
    },

    // 下一步
    handleNext () {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 额外验证 - 检查是否有重复的供应商+卡池组合
          const supplierPoolPairs = new Set();
          let hasDuplicate = false;
          for (let i = 0; i < this.formData.backups.length; i++) {
            const backup = this.formData.backups[i];
            if (backup.supplier && backup.pool) {
              const pairString = `${backup.supplier}-${backup.pool}`;
              if (supplierPoolPairs.has(pairString)) {
                hasDuplicate = true;
                break;
              }
              supplierPoolPairs.add(pairString);
            }
          }
          if (hasDuplicate) {
            this.$Message.error("备用供应商和备用卡池不能重复选择");
            return;
          }
          // 百分比验证
          if (this.formData.backups.length > 1) {
            const totalPercentage = this.formData.backups.reduce(
              (sum, backup) => {
                return sum + (Number(backup.percentage) || 0);
              },
              0
            );
            if (Math.abs(totalPercentage - 100) > 0.01) {
              this.$Message.error("比例总和必须等于100%");
              return;
            }
          }
          this.$emit("next-step", this.formData);
        }
      });
    },

    // 取消
    handleCancel () {
      this.$emit("cancel");
    },
    // 修改打开备用供应商选择框事件处理，添加缓存机制
    handleOpenBackupSupplierSelect (index, open) {
      // 只在打开下拉框时才处理
      if (!open) return;

      // 如果没有目标国家，不请求
      if (
        !this.formData.targetCountries ||
        this.formData.targetCountries.length === 0
      ) {
        this.backupSupplierListChecked = false; // 重置检查标记
        return;
      }

      const mccKey = this.getMccCacheKey();

      // 检查当前mccKey是否已经请求过且确认没有数据
      if (
        this.supplierCache.noDataKeys &&
        this.supplierCache.noDataKeys.includes(mccKey)
      ) {
        console.log("此目标国家组合已确认没有可用的备用供应商，跳过请求");
        this.backupSupplierList = [];
        this.backupSupplierListChecked = true; // 设置检查标记为true
        return;
      }

      // 如果已经有缓存且缓存key与当前一致，不重复请求
      if (
        this.backupSupplierList.length > 0 &&
        this.supplierCache.mccKey === mccKey
      ) {
        console.log("使用备用供应商列表缓存");
        this.backupSupplierListChecked = true; // 设置检查标记为true
        return;
      }

      // 如果正在加载，不重复请求
      if (this.supplierCache.isLoading) {
        console.log("备用供应商列表正在加载中，跳过请求");
        return;
      }

      // 设置加载状态并加载数据
      this.loadingBackupSuppliers = true;
      this.fetchSuppliersByMcc();
    },

    // 添加触发目标国家验证的方法
    triggerTargetCountriesValidation (forceShowError) {
      // 确保formRef已经初始化
      if (this.$refs.formRef) {
        this.$nextTick(() => {
          this.$refs.formRef.validateField(
            "targetCountries",
            (errorMessage) => {
              // 只有在强制显示错误或已经显示过错误的情况下才更新错误状态
              if (forceShowError || this.targetCountryError) {
                this.targetCountryError = !!errorMessage;
              }
            }
          );
        });
      }
    },

    // 删除已选国家
    removeSelectedCountry (mcc) {
      this.formData.targetCountries = this.formData.targetCountries.filter(
        (item) => item !== mcc
      );
      // 重新排序
      this.formData.targetCountries = this.sortTargetCountriesByEn(
        this.formData.targetCountries
      );
      // 触发联动
      this.handleSelectionChange(
        this.formData.targetCountries,
        this.currentViewCountryMccs
      );
    },

    // 添加远程搜索供应商方法
    remoteSearchSupplier (query, index) {
      // 如果没有目标国家，不执行操作
      if (
        !this.formData.targetCountries ||
        this.formData.targetCountries.length === 0
      ) {
        return;
      }

      // 如果列表为空或者正在加载新数据，则加载数据
      if (
        this.backupSupplierList.length === 0 &&
        !this.loadingBackupSuppliers
      ) {
        this.fetchSuppliersByMcc();
        return;
      }

      // 如果已有数据，则根据查询词过滤
      // 注意：这里不需要真的过滤，因为iView的Select组件会自己处理过滤
      // 这个方法主要是为了加载数据和处理loading状态
    },

    // 添加远程搜索卡池方法
    remoteSearchPool (query, index) {
      const backup = this.formData.backups[index];
      if (!backup || !backup.supplier) {
        return;
      }

      // 如果列表为空或者正在加载新数据，则加载数据
      if (backup.poolList.length === 0 && !backup.loadingPools) {
        this.fetchBackupPools(index);
        return;
      }

      // 注意：与供应商类似，这里不需要真的过滤
    },
  },
};
</script>

<style scoped>
.step1-container {
  padding: 20px 0;
}

.ivu-form-item {
  margin-bottom: 22px;
}

.ivu-form-item-content>.ivu-input-number {
  vertical-align: middle;
}
</style>
