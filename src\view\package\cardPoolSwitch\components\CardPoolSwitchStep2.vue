<template>
  <div class="step2-container">
    <div class="page-header">
      <h3>剩余未切换套餐</h3>
    </div>

    <div style="margin-bottom: 20px;">
      <Checkbox v-model="selectAll" @on-change="handleSelectAll" :indeterminate="indeterminate">
        全选剩余套餐
      </Checkbox>
      <span style="margin-left: 20px; color: #666;">
        已选择 {{ selectedPackages.length }} / {{ remainingPackages.length }} 个套餐
      </span>
    </div>

    <!-- 表格区域 -->
    <Table ref="table" :columns="columns" :data="remainingPackages" :loading="tableLoading"
      @on-selection-change="handleSelectionChange" border :ellipsis="true" style="margin-bottom: 30px;">
    </Table>

    <!-- 操作按钮区域 -->
    <div class="form-actions">
      <Button @click="handlePrevStep" style="margin-right: 15px;">上一步</Button>
      <Button type="primary" @click="handleContinueSwitch" :disabled="selectedPackages.length === 0"
        :loading="switchLoading" style="margin-right: 15px;">
        确认切换
      </Button>
      <Button type="default" @click="handleFinish" :loading="finishLoading">
        返回
      </Button>
    </div>

    <!-- 进度提示Modal -->
    <Modal v-model="progressModal" title="切换进度" :closable="false" :mask-closable="false" width="500">
      <div style="text-align: center;">
        <Progress :percent="switchProgress" :stroke-width="10" status="active" />
        <p style="margin-top: 15px;">{{ progressText }}</p>
      </div>
      <div slot="footer">
        <Button v-if="switchProgress === 100" type="primary" @click="closeProgressModal">
          完成
        </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'CardPoolSwitchStep2',
  props: {
    taskInfo: {
      type: Object,
      required: true
    },
    wizardData: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      remainingPackages: [],
      selectedPackages: [],
      selectAll: false,
      indeterminate: false,
      tableLoading: false,
      switchLoading: false,
      finishLoading: false,
      progressModal: false,
      switchProgress: 0,
      progressText: '',
      columns: [
        { type: 'selection', width: 60, align: 'center' },
        { title: '套餐ID', key: 'id', width: 150 },
        { title: '套餐名称', key: 'packageName', minWidth: 200, ellipsis: true, tooltip: true },
        { title: '套餐描述', key: 'descCn', minWidth: 200, ellipsis: true, tooltip: true }
      ]
    };
  },
  mounted () {
    this.loadRemainingPackages();
  },
  methods: {
    // 加载剩余套餐数据
    loadRemainingPackages () {
      this.tableLoading = true;

      // Mock数据
      setTimeout(() => {
        this.remainingPackages = [
          {
            id: 'PKG001',
            packageName: '企业专享流量套餐',
            descCn: '企业套餐',
            currentCardPool: 'Pool-A-001',
            targetCardPool: 'Pool-B-001',
            status: 'pending',
            createTime: '2024-01-15 10:30:00'
          },
          {
            id: 'PKG002',
            packageName: '高级无限流量套餐',
            descCn: '高级套餐',
            currentCardPool: 'Pool-A-002',
            targetCardPool: 'Pool-B-002',
            status: 'pending',
            createTime: '2024-01-15 10:31:00'
          },
          {
            id: 'PKG003',
            packageName: '定制化IoT套餐',
            descCn: '定制套餐',
            currentCardPool: 'Pool-A-003',
            targetCardPool: 'Pool-B-003',
            status: 'pending',
            createTime: '2024-01-15 10:32:00'
          },
          {
            id: 'PKG004',
            packageName: '临时访问套餐',
            descCn: '临时套餐',
            currentCardPool: 'Pool-A-004',
            targetCardPool: 'Pool-B-004',
            status: 'pending',
            createTime: '2024-01-15 10:33:00'
          },
          {
            id: 'PKG005',
            packageName: '测试环境套餐',
            descCn: '测试套餐',
            currentCardPool: 'Pool-A-005',
            targetCardPool: 'Pool-B-005',
            status: 'pending',
            createTime: '2024-01-15 10:34:00'
          }
        ];
        this.tableLoading = false;
      }, 1000);
    },

    // 处理表格选择变化
    handleSelectionChange (selection) {
      this.selectedPackages = selection;
      this.updateSelectAllStatus();
    },

    // 处理全选
    handleSelectAll (checked) {
      this.$refs.table && this.$refs.table.selectAll(checked);
    },

    // 更新全选状态
    updateSelectAllStatus () {
      const selectedCount = this.selectedPackages.length;
      const totalCount = this.remainingPackages.length;

      this.selectAll = selectedCount === totalCount && totalCount > 0;
      this.indeterminate = selectedCount > 0 && selectedCount < totalCount;
    },

    // 获取状态颜色
    getStatusColor (status) {
      const colorMap = {
        'pending': 'default',
        'switching': 'processing',
        'completed': 'success',
        'failed': 'error'
      };
      return colorMap[status] || 'default';
    },

    // 获取状态文本
    getStatusText (status) {
      const textMap = {
        'pending': '待切换',
        'switching': '切换中',
        'completed': '已完成',
        'failed': '切换失败'
      };
      return textMap[status] || '未知';
    },

    // 继续切换选中套餐
    handleContinueSwitch () {
      if (this.selectedPackages.length === 0) {
        this.$Message.warning('请选择要切换的套餐');
        return;
      }

      this.$Modal.confirm({
        title: '确认切换',
        content: `确认切换选中的 ${this.selectedPackages.length} 个套餐吗？`,
        onOk: () => {
          this.startSwitchProgress();
        }
      });
    },

    // 开始切换进度
    startSwitchProgress () {
      this.progressModal = true;
      this.switchProgress = 0;
      this.progressText = '正在准备切换...';

      const totalPackages = this.selectedPackages.length;
      let currentIndex = 0;

      const switchInterval = setInterval(() => {
        if (currentIndex < totalPackages) {
          this.switchProgress = Math.round(((currentIndex + 1) / totalPackages) * 100);
          this.progressText = `正在切换第 ${currentIndex + 1} 个套餐: ${this.selectedPackages[currentIndex].packageName}`;
          currentIndex++;
        } else {
          clearInterval(switchInterval);
          this.progressText = '切换完成！';

          // 更新套餐状态
          this.selectedPackages.forEach(pkg => {
            const index = this.remainingPackages.findIndex(p => p.id === pkg.id);
            if (index !== -1) {
              this.remainingPackages[index].status = 'completed';
            }
          });

          this.selectedPackages = [];
          this.updateSelectAllStatus();
        }
      }, 800);
    },

    // 关闭进度Modal
    closeProgressModal () {
      this.progressModal = false;
      this.$Message.success('套餐切换完成！');
    },

    // 上一步
    handlePrevStep () {
      this.$emit('prev-step');
    },

    // 结束流程
    handleFinish () {
      const pendingCount = this.remainingPackages.filter(pkg => pkg.status === 'pending').length;

      let content = '确认结束卡池切换流程吗？';

      this.$Modal.confirm({
        title: '确认结束',
        content: content,
        onOk: () => {
          this.finishLoading = true;
          this.$router.go(-1)
        }
      });
    }
  }
};
</script>

<style scoped>
.step2-container {
  padding: 20px 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h3 {
  margin-bottom: 10px;
  color: #333;
}

.page-description {
  color: #666;
  margin-top: 10px;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e8eaec;
}

.ivu-alert {
  margin-bottom: 20px;
}

.ivu-table-wrapper {
  border: 1px solid #e8eaec;
  border-radius: 4px;
}

.ivu-progress-outer {
  margin: 20px 0;
}
</style>
