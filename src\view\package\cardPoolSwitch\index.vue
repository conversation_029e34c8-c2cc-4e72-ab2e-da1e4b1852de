<template>
  <Card style="width: 100%; padding: 16px;">
    <!-- 1. 查询条件区域 -->
    <Form ref="searchObj" :model="searchObj" :label-width="100">
      <Row :gutter="16">
        <Col span="6"> <!-- 第一行，span=8 -->
        <FormItem label="规则名称" prop="ruleName">
          <Input v-model="searchObj.ruleName" placeholder="请输入规则名称" clearable style="width: 100%" />
        </FormItem>
        </Col>
        <Col span="6"> <!-- 第一行，span=8 -->
        <FormItem label="原资源供应商" prop="faultSupplier">
          <Select v-model="searchObj.faultSupplier" placeholder="请选择" clearable filterable style="width: 100%">
            <Option v-for="supplier in supplierList" :value="supplier.supplierId" :key="supplier.supplierId">{{
              supplier.supplierName }}</Option>
          </Select>
        </FormItem>
        </Col>
        <Col span="6"> <!-- 第一行，span=8 -->
        <FormItem label="目标国家" prop="targetCountries">
          <Select v-model="searchObj.targetCountries" multiple filterable placeholder="请选择" clearable
            style="width: 100%">
            <Option v-for="country in countryList" :value="country.mcc" :key="country.mcc">{{ country.countryEn }}
            </Option>
          </Select>
        </FormItem>
        </Col>
      </Row>
      <Row :gutter="16">
        <Col span="6"> <!-- 第二行，span=6 -->
        <FormItem label="新资源供应商" prop="backupSupplier">
          <Select v-model="searchObj.backupSupplier" placeholder="请选择" clearable filterable style="width: 100%">
            <Option v-for="supplier in supplierList" :value="supplier.supplierId" :key="supplier.supplierId">{{
              supplier.supplierName }}</Option>
          </Select>
        </FormItem>
        </Col>
        <Col span="6"> <!-- 第二行，span=6 -->
        <FormItem label="新卡池" prop="backupPool">
          <Input v-model="searchObj.backupPool" placeholder="请输入备用卡池名称" clearable style="width: 100%" />
        </FormItem>
        </Col>
        <Col span="6"> <!-- 第二行，span=6 -->
        <FormItem label="故障状态" prop="status">
          <Select v-model="searchObj.status" placeholder="请选择" clearable style="width: 100%">
            <Option value="0">待执行</Option>
            <Option value="1">切换中</Option>
            <Option value="2">已完成</Option>
            <Option value="3">已失败</Option>
          </Select>
        </FormItem>
        </Col>
        <Col span="6"> <!-- 第二行，按钮 span=6, 调整对齐和上边距 -->
        <FormItem :label-width="10">
          <Button type="primary" @click="handleSearch" icon="ios-search">查询</Button>
          <Button type="info" @click="addRecord" icon="ios-add" style="margin-left: 8px">新建</Button>
        </FormItem>
        </Col>
      </Row>
    </Form>
    <!-- 表格区域 -->
    <Table :columns="columns" :data="tableData" :loading="tableLoading" border :ellipsis="true"
      style="margin-top: 20px;">
      <template slot-scope="{ row }" slot="backupSuppliers">
        <!-- 分拆显示：备用供应商 -->
        <div v-for="(supplier, index) in row.standbySupplierNames" :key="'sup_' + index"
          style="line-height: 1.8; min-height: 24px; /* 保持最小行高对齐 */"> <!-- 增加 key 前缀避免潜在冲突 -->
          <span>{{ supplier }}</span>
        </div>
      </template>
      <template slot-scope="{ row }" slot="backupPools">
        <!-- 分拆显示：备用卡池 -->
        <div v-for="(pool, index) in row.newCardPools" :key="'pool_' + index" style="line-height: 1.8; min-height: 24px;">
          <a @click="showPoolDetails(pool)">
            {{ pool.poolName }}<span v-if="pool.rate !== undefined && row.newCardPools.length > 1">（{{ pool.rate
            }}%）</span>
          </a>
        </div>
      </template>
      <template slot-scope="{ row }" slot="status">
        <Tag :color="getStatusColor(row.status)">{{ getStatusText(row.status) }}</Tag>
      </template>
      <template slot-scope="{ row }" slot="action">
        <Button type="primary" size="small" style="margin-right: 5px" @click="viewDetail(row)">详情</Button>
        <Button type="success" size="small" style="margin-right: 5px" v-if="row.status === '0'"
          @click="executeTask(row)">执行</Button>
        <Button type="error" size="small" v-if="['0', '1'].includes(row.status)" @click="cancelTask(row)">取消</Button>
      </template>
      <template slot-scope="{ row }" slot="newCardPools">
        <div v-for="(pool, index) in row.newCardPools" :key="'pool_' + index"
          style="line-height: 1.8; min-height: 24px;">
          <a @click="showPoolDetails(pool)">
            {{ pool.poolName }}<span v-if="pool.rate !== undefined && row.newCardPools.length > 1">（{{ pool.rate
            }}%）</span>
          </a>
        </div>
      </template>
    </Table>

    <!-- 分页区域 -->
    <Page :total="total" show-total show-elevator :page-size="searchObj.pageSize" :current.sync="searchObj.pageNum"
      @on-change="loadByPage" @on-page-size-change="handleSizeChange" style="margin: 15px 0; text-align: right;" />
    <PoolDetail :visible="poolDetailModalVisible" :currentPoolDetails="currentPoolDetails" :loading="poolDetailLoading"
      :detilCorpname="detilCorpname" :activeCollapsePanel="activeCollapsePanel" @close="handlePoolDetailClose"
      @collapse-change="handlePoolCollapseChange" />
  </Card>
</template>

<script>
import PoolDetail from '@/components/PoolDetail.vue'
import {getCardPoolById} from '@/api/server/faultHandling';
export default {
  name: 'CardPoolSwitchList',
  components: { PoolDetail },
  data () {
    return {
      // 搜索条件对象
      searchObj: {
        ruleName: '',
        faultSupplier: '',
        targetCountries: [],
        backupSupplier: '',
        backupPool: '',
        status: '',
        pageNum: 1,
        pageSize: 10
      },
      // 表格数据
      tableLoading: false,
      tableData: [],
      total: 0,
      // 下拉选项数据
      supplierList: [],
      countryList: [],
      columns: [
        { title: '规则名称', key: 'ruleName', minWidth: 150, ellipsis: true, tooltip: true },
        { title: '原资源供应商', key: 'originalSupplierName', minWidth: 120, ellipsis: true, tooltip: true },
        { title: '目标国家', key: 'targetCountries', minWidth: 200, ellipsis: true, tooltip: true },
        { title: '新资源供应商', key: 'newSupplierName', minWidth: 120, ellipsis: true, tooltip: true },
        { title: '新卡池', slot: 'newCardPools', minWidth: 200, ellipsis: true, tooltip: true },
        { title: '切换状态', slot: 'status', width: 100, align: 'center' },
        { title: '切换完成时间', key: 'createTime', width: 160, align: 'center' },
        { title: '操作', slot: 'action', width: 180, align: 'center' }
      ],
      poolDetailModalVisible: false,
      currentPoolDetails: null,
      poolDetailLoading: false,
      detilCorpname: '',
      activeCollapsePanel: '',
    }
  },
  mounted () {
    this.initData();
    this.loadData();
  },
  methods: {
    // 初始化数据
    initData () {
      this.loadSupplierList();
      this.loadCountryList();
    },

    // 加载供应商列表
    loadSupplierList () {
      // Mock数据
      this.supplierList = [
        { supplierId: '1', supplierName: 'Supplier A' },
        { supplierId: '2', supplierName: 'Supplier B' },
        { supplierId: '3', supplierName: 'Supplier C' }
      ];
    },

    // 加载国家列表
    loadCountryList () {
      // Mock数据
      this.countryList = [
        { mcc: '460', countryEn: 'China' },
        { mcc: '454', countryEn: 'Hong Kong' },
        { mcc: '310', countryEn: 'United States' },
        { mcc: '262', countryEn: 'Germany' },
        { mcc: '208', countryEn: 'France' }
      ];
    },

    // 加载数据
    loadData () {
      this.tableLoading = true;

      // Mock数据 - 实际项目中这里应该调用API
      setTimeout(() => {
        this.tableData = [
          {
            "id": "1944660968383354066",
            "ruleName": "V->H故障测试",
            "faultSupplierName": "MTT",
            "faultSupplierId": "5",
            "countryEn": [
              "China"
            ],
            "standbySupplierNames": [
              "CMHK"
            ],
            "newCardPools": [
              {
                "poolId": "C2507141522453829369",
                "poolName": "cmhk-H卡池",
                "rate": 100
              }
            ],
            "faultStatus": "1",
            "faultStartTime": "2025-07-14 15:31:20",
            "faultRestoreTime": "2025-07-14 16:23:09",
            "mccs": null
          },
          {
            "id": "1928018074264309890",
            "ruleName": "ttt",
            "faultSupplierName": "BICS",
            "faultSupplierId": "2",
            "countryEn": [
              "China"
            ],
            "standbySupplierNames": [
              "MTT"
            ],
            "newCardPools": [
              {
                "poolId": "C2212021600184114077",
                "poolName": "V卡继承卡池1",
                "rate": 100
              }
            ],
            "faultStatus": "1",
            "faultStartTime": "2025-05-29 17:18:24",
            "faultRestoreTime": "2025-05-29 17:18:38",
            "mccs": null
          },
          {
            "id": "1925132857869636977",
            "ruleName": "qqqqqqq",
            "faultSupplierName": "MTT",
            "faultSupplierId": "5",
            "countryEn": [
              "China",
              "China"
            ],
            "standbySupplierNames": [
              "BICS-Telus"
            ],
            "newCardPools": [
              {
                "poolId": "904e5429e46642b28ae568f775397111",
                "poolName": "普卡v3测试1123",
                "rate": 100
              }
            ],
            "faultStatus": "1",
            "faultStartTime": "2025-05-21 18:13:35",
            "faultRestoreTime": null,
            "mccs": null
          },
          {
            "id": "123",
            "ruleName": "激活测试勿动",
            "faultSupplierName": "MTT",
            "faultSupplierId": "5",
            "countryEn": [
              "China"
            ],
            "standbySupplierNames": [
              "BICS-Telus"
            ],
            "newCardPools": [
              {
                "poolId": "C2312051000575823149",
                "poolName": "V卡继承卡池1_复制",
                "rate": 100
              }
            ],
            "faultStatus": "1",
            "faultStartTime": "2025-05-21 15:42:59",
            "faultRestoreTime": null,
            "mccs": null
          },
          {
            "id": "1925024572894123377",
            "ruleName": "all",
            "faultSupplierName": "BICS",
            "faultSupplierId": "2",
            "countryEn": [
              "435dgfdg",
              "abc",
              "Afghanistan",
              "Albania",
              "Algeria",
              "Angola",
              "Anguilla",
              "Antigua and Barbuda",
              "Argentina",
              "Armenia",
              "Aruba",
              "Australia",
              "Austria",
              "Azerbaijan",
              "Bahamas",
              "Bahrain",
              "Bangladesh",
              "Barbados",
              "Belarus",
              "Belgium",
              "Belize",
              "Benin",
              "Bermuda",
              "Bhutan",
              "Bolivia",
              "Bosnia and Herzegovina",
              "Brazil",
              "British Virgin Islands",
              "Brunei",
              "Bulgaria",
              "Cambodia",
              "Cameroon",
              "Canada",
              "Cape Verde",
              "Cayman Islands",
              "Centrafrique",
              "Chile",
              "China",
              "China",
              "Colombia",
              "Costa Rica",
              "Croatia",
              "Cuba",
              "Curacao & Bonaire",
              "Cyprus",
              "Czech Republic",
              "Democratic Republic of Congo",
              "Denmark",
              "Dominica",
              "Dominican Republic",
              "Ecuador",
              "Egypt",
              "El Salvador",
              "Estonia",
              "Faroe Islands",
              "Fiji",
              "Finland",
              "France",
              "French Guiana",
              "French Polynesia",
              "Gabon",
              "Gambia",
              "Georgia",
              "Germany",
              "Ghana",
              "Gibraltar",
              "Greece",
              "Greenland",
              "Grenada",
              "Guam",
              "guang",
              "guang-1",
              "Guatemala",
              "Guyana",
              "Haiti",
              "Honduras",
              "Hong Kong, China",
              "Hungary",
              "Iceland",
              "Iceland",
              "India",
              "Indonesia",
              "Iran",
              "Ireland",
              "Isle of Man",
              "Israel",
              "Italy",
              "Ivory Coast (Cote d'Ivoire)",
              "Jamaica and Caribbean Countries",
              "janpen",
              "Janpen",
              "Japan",
              "Jersey",
              "Jordan",
              "Kazakhstan",
              "Kenya",
              "Kosovo",
              "Kuwait",
              "Kyrgyzstan",
              "Laos",
              "Latvia",
              "lcx-11",
              "lcx-222",
              "lcx-555",
              "lcx-test666",
              "lcxtest-777",
              "Liberia",
              "Lithuania",
              "Luxembourg",
              "Macau, China",
              "Macedonia",
              "Madagascar",
              "Malawi",
              "Malaysia",
              "Maldives",
              "Mali",
              "Malta",
              "Martinique",
              "Mauritius",
              "Mexico",
              "Moldova",
              "Monaco",
              "Mongolia",
              "Montenegro",
              "Montserrat",
              "Morocco",
              "Mozambique",
              "Myanmar",
              "Nepal",
              "Netherlands",
              "New Zealand",
              "Nicaragua",
              "Niger",
              "Norway",
              "Oman",
              "Pakistan",
              "Panama",
              "Papua New Guinea",
              "Paraguay",
              "Peru",
              "Philippines",
              "Poland",
              "Portugal",
              "Puerto Rico",
              "Qatar",
              "ret",
              "Reunion",
              "Romania",
              "Russia",
              "Rwanda",
              "Saudi Arabia",
              "Serbia",
              "Seychelles",
              "Sierra Leone",
              "Singapore",
              "Slovakia",
              "Slovenia",
              "South Africa",
              "South Korea",
              "Spain",
              "Sri Lanka",
              "St. Kitts/Nevis",
              "St. Lucia",
              "St. Vincent",
              "Sudan",
              "Suriname",
              "Swaziland",
              "Sweden",
              "Switzerland",
              "Taiwan, China",
              "Tajikistan",
              "Tanzania",
              "Tchad",
              "Thailand",
              "Tonga",
              "Trinidad and Tobago",
              "Tunisia",
              "Turkey",
              "Turks and Caicos",
              "UAE",
              "Uganda",
              "UK",
              "Ukraine",
              "Uruguay",
              "USA",
              "Uzbekistan",
              "Vanuatu",
              "Venezuela",
              "Vietnam",
              "Yemen",
              "yhdceshi",
              "yuche",
              "yuche-10",
              "yuche-11",
              "yuche-2",
              "yuche-2",
              "yuche-3",
              "Zambia",
              "Zimbabwe"
            ],
            "standbySupplierNames": [
              "MTT"
            ],
            "newCardPools": [
              {
                "poolId": "C2312051000575823149",
                "poolName": "V卡继承卡池1_复制",
                "rate": 100
              }
            ],
            "faultStatus": "1",
            "faultStartTime": "2025-05-21 11:03:18",
            "faultRestoreTime": "2025-05-21 15:20:43",
            "mccs": null
          },
          {
            "id": "1924775375494952401",
            "ruleName": "123456789",
            "faultSupplierName": "MTT",
            "faultSupplierId": "5",
            "countryEn": [
              "China"
            ],
            "standbySupplierNames": [
              "BICS-Telus",
              "BICS-Telus"
            ],
            "newCardPools": [
              {
                "poolId": "C2212021600184114077",
                "poolName": "V卡继承卡池1",
                "rate": 50
              },
              {
                "poolId": "C2312051000575823149",
                "poolName": "V卡继承卡池1_复制",
                "rate": 50
              }
            ],
            "faultStatus": "1",
            "faultStartTime": "2025-05-20 18:33:05",
            "faultRestoreTime": null,
            "mccs": null
          },
          {
            "id": "1922953178702189937",
            "ruleName": "test55",
            "faultSupplierName": "Orange",
            "faultSupplierId": "3",
            "countryEn": [
              "China",
              "China"
            ],
            "standbySupplierNames": [
              "MTT"
            ],
            "newCardPools": [
              {
                "poolId": "C2312051000575823149",
                "poolName": "V卡继承卡池1_复制",
                "rate": 100
              }
            ],
            "faultStatus": "1",
            "faultStartTime": "2025-05-15 17:52:19",
            "faultRestoreTime": "2025-05-15 17:58:36",
            "mccs": null
          },
          {
            "id": "1920386137575163473",
            "ruleName": "规则4",
            "faultSupplierName": "Orange",
            "faultSupplierId": "3",
            "countryEn": [
              "435dgfdg",
              "abc",
              "Afghanistan",
              "Albania",
              "Algeria",
              "Angola",
              "Anguilla",
              "Antigua and Barbuda",
              "Argentina",
              "Armenia",
              "Aruba",
              "Australia",
              "Austria",
              "Azerbaijan",
              "Bahamas",
              "Bahrain",
              "Bangladesh",
              "Barbados",
              "Belarus",
              "Belgium",
              "Belize",
              "Benin",
              "Bermuda",
              "Bhutan",
              "Bolivia",
              "Bosnia and Herzegovina",
              "Brazil",
              "British Virgin Islands",
              "Brunei",
              "Bulgaria",
              "Cambodia",
              "Cameroon",
              "Canada",
              "Cape Verde",
              "Cayman Islands",
              "Centrafrique",
              "Chile",
              "China",
              "China",
              "Colombia",
              "Costa Rica",
              "Croatia",
              "Cuba",
              "Curacao & Bonaire",
              "Cyprus",
              "Czech Republic",
              "Democratic Republic of Congo",
              "Denmark",
              "Dominica",
              "Dominican Republic",
              "Ecuador",
              "Egypt",
              "El Salvador",
              "Estonia",
              "Faroe Islands",
              "Fiji",
              "Finland",
              "France",
              "French Guiana",
              "French Polynesia",
              "Gabon",
              "Gambia",
              "Georgia",
              "Germany",
              "Ghana",
              "Gibraltar",
              "Greece",
              "Greenland",
              "Grenada",
              "Guam",
              "guang",
              "guang-1",
              "Guatemala",
              "Guyana",
              "Haiti",
              "Honduras",
              "Hong Kong, China",
              "Hungary",
              "Iceland",
              "Iceland",
              "India",
              "Indonesia",
              "Iran",
              "Ireland",
              "Isle of Man",
              "Israel",
              "Italy",
              "Ivory Coast (Cote d'Ivoire)",
              "Jamaica and Caribbean Countries",
              "janpen",
              "Janpen",
              "Japan",
              "Jersey",
              "Jordan",
              "Kazakhstan",
              "Kenya",
              "Kosovo",
              "Kuwait",
              "Kyrgyzstan",
              "Laos",
              "Latvia",
              "lcx-11",
              "lcx-222",
              "lcx-555",
              "Liberia",
              "Lithuania",
              "Luxembourg",
              "Macau, China",
              "Macedonia",
              "Madagascar",
              "Malawi",
              "Malaysia",
              "Maldives",
              "Mali",
              "Malta",
              "Martinique",
              "Mauritius",
              "Mexico",
              "Moldova",
              "Monaco",
              "Mongolia",
              "Montenegro",
              "Montserrat",
              "Morocco",
              "Mozambique",
              "Myanmar",
              "Nepal",
              "Netherlands",
              "New Zealand",
              "Nicaragua",
              "Niger",
              "Norway",
              "Oman",
              "Pakistan",
              "Panama",
              "Papua New Guinea",
              "Paraguay",
              "Peru",
              "Philippines",
              "Poland",
              "Portugal",
              "Puerto Rico",
              "Qatar",
              "Reunion",
              "Romania",
              "Russia",
              "Rwanda",
              "Saudi Arabia",
              "Serbia",
              "Seychelles",
              "Sierra Leone",
              "Singapore",
              "Slovakia",
              "Slovenia",
              "South Africa",
              "South Korea",
              "Spain",
              "Sri Lanka",
              "St. Kitts/Nevis",
              "St. Lucia",
              "St. Vincent",
              "Sudan",
              "Suriname",
              "Swaziland",
              "Sweden",
              "Switzerland",
              "Taiwan, China",
              "Tajikistan",
              "Tanzania",
              "Tchad",
              "Thailand",
              "Tonga",
              "Trinidad and Tobago",
              "Tunisia",
              "Turkey",
              "Turks and Caicos",
              "UAE",
              "Uganda",
              "UK",
              "Ukraine",
              "Uruguay",
              "USA",
              "Uzbekistan",
              "Vanuatu",
              "Venezuela",
              "Vietnam",
              "Yemen",
              "yhdceshi",
              "yuche",
              "yuche-10",
              "yuche-11",
              "yuche-2",
              "yuche-2",
              "yuche-3",
              "Zambia",
              "Zimbabwe"
            ],
            "standbySupplierNames": [
              "BICS-Telus",
              "BICS",
              "Orange"
            ],
            "newCardPools": [
              {
                "poolId": "C2203011006079999065",
                "poolName": "巫学钢测试卡池",
                "rate": 98
              },
              {
                "poolId": "904e5429e46642b28ae568f775397171",
                "poolName": "卡池122",
                "rate": 1
              },
              {
                "poolId": "C2201261341364776919",
                "poolName": "张松林香港卡池11",
                "rate": 1
              }
            ],
            "faultStatus": "1",
            "faultStartTime": "2025-05-08 15:51:49",
            "faultRestoreTime": "2025-05-08 15:52:09",
            "mccs": null
          },
          {
            "id": "1920034470858102129",
            "ruleName": "утром",
            "faultSupplierName": "Orange",
            "faultSupplierId": "3",
            "countryEn": [
              "Israel",
              "China",
              "China"
            ],
            "standbySupplierNames": [
              "BICS-Telus",
              "MTT"
            ],
            "newCardPools": [
              {
                "poolId": "C2312061045238311836",
                "poolName": "阈值测试",
                "rate": 50
              },
              {
                "poolId": "C2312051000575823149",
                "poolName": "V卡继承卡池1_复制",
                "rate": 50
              }
            ],
            "faultStatus": "1",
            "faultStartTime": "2025-05-07 16:34:25",
            "faultRestoreTime": null,
            "mccs": null
          },
          {
            "id": "1920023971890234961",
            "ruleName": "规则3",
            "faultSupplierName": "BICS",
            "faultSupplierId": "2",
            "countryEn": [
              "Albania",
              "Canada"
            ],
            "standbySupplierNames": [
              "BICS-Telus"
            ],
            "newCardPools": [
              {
                "poolId": "C2208031544572674598",
                "poolName": "wyq11",
                "rate": 100
              }
            ],
            "faultStatus": "1",
            "faultStartTime": "2025-05-07 15:52:42",
            "faultRestoreTime": "2025-05-20 18:23:37",
            "mccs": null
          }
        ]
        this.total = 3;
        this.tableLoading = false;
      }, 1000);
    },

    // 分页处理
    loadByPage (page) {
      this.searchObj.pageNum = page;
      this.loadData();
    },

    // 搜索
    handleSearch () {
      this.searchObj.pageNum = 1; // 重置为第一页
      this.loadData();
    },

    // 重置搜索条件
    resetSearch () {
      this.$refs.searchObj.resetFields();
      this.searchObj.pageNum = 1;
      this.loadData();
    },

    // 页大小改变
    handleSizeChange (size) {
      this.searchObj.pageSize = size;
      this.searchObj.pageNum = 1;
      this.loadData();
    },

    // 获取状态颜色
    getStatusColor (status) {
      const colorMap = {
        '0': 'default',
        '1': 'processing',
        '2': 'success',
        '3': 'error'
      };
      return colorMap[status] || 'default';
    },

    // 获取状态文本
    getStatusText (status) {
      const textMap = {
        '0': '待执行',
        '1': '执行中',
        '2': '已完成',
        '3': '已失败'
      };
      return textMap[status] || '未知';
    },

    // 新增记录
    addRecord () {
      this.$router.push({
        name: 'cardPoolSwitchAdd'
      });
    },

    // 查看详情
    viewDetail (row) {
      this.$Message.info(`查看详情功能待开发: ${row.ruleName}`);
    },

    // 执行任务
    executeTask (row) {
      this.$Modal.confirm({
        title: '确认执行',
        content: `确认执行卡池切换任务"${row.ruleName}"吗？`,
        onOk: () => {
          this.$Message.success('任务执行成功');
          this.loadData();
        }
      });
    },

    // 取消任务
    cancelTask (row) {
      this.$Modal.confirm({
        title: '确认取消',
        content: `确认取消卡池切换任务"${row.ruleName}"吗？`,
        onOk: () => {
          this.$Message.success('任务取消成功');
          this.loadData();
        }
      });
    },
    showPoolDetails (pool) {
      this.currentPoolDetails = null;
      this.detilCorpname = '';
      this.poolDetailModalVisible = true;
      this.poolDetailLoading = true;
      this.activeCollapsePanel = '';
      // 模拟API请求，实际应调用 getCardPoolById
      let data = {
        poolId: pool.poolId,
        page: 1,
        pageSize: 9999
      }
      getCardPoolById(data)
        .then(res => {
          if (res.code === '0000' && res.data) {
            this.currentPoolDetails = res.data.data[0];
            if (this.currentPoolDetails.corpId) {
              this.getCompanyList(this.currentPoolDetails.usageType, this.currentPoolDetails.corpId);
            }
          } else {
            this.$Message.warning('操作失败' + ': ' + (res.msg || '未知错误'));
            this.currentPoolDetails = null;
          }
        })
        .catch(err => {
          this.$Message.error('操作失败');
          this.currentPoolDetails = null;
        })
        .finally(() => {
          this.poolDetailLoading = false;
        });
    },
    handlePoolDetailClose () {
      this.poolDetailModalVisible = false;
      this.currentPoolDetails = null;
      this.detilCorpname = '';
      this.activeCollapsePanel = '';
    },
    handlePoolCollapseChange (e) {
      this.activeCollapsePanel = e;
      // 可在此处处理套餐懒加载逻辑
    },
    //获取厂商集合
    getCompanyList (e, corpId) {
      console.log("获取厂商名称getCompanyList", e, corpId)
      //8-线下(前端线下为2) 7-线上(前端线上为3)
      getPage({
        pageNumber: 1,
        pageSize: -1,
        corpType: e == '2' ? '8' : '7'
      }).then(res => {
        if (res && res.code == '0000') {
          var data = res.data.records;
          this.corpIdDetails = data; // 保存厂商数据
          data.forEach((item) => {
            if (item.corpId === corpId) {
              this.detilCorpname = item.corpName;
              console.log("找到匹配厂商:", item.corpName);
            }
          });
          // 如果没找到匹配的厂商
          if (!this.detilCorpname) {
            console.log("未找到匹配厂商，corpId:", corpId);
            this.detilCorpname = "未知厂商";
          }
        } else {
          console.error("获取厂商列表失败:", res);
          this.detilCorpname = "未知厂商";
        }
      }).catch((err) => {
        console.error("获取厂商列表异常:", err);
        this.detilCorpname = "未知厂商";
      });
    },
  }
}
</script>

<style scoped>
/* 页面样式按照项目规范 */
</style>
