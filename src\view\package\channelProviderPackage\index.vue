<template>
	<!-- 渠道商套餐管理 -->
	<Card>
		<!-- 搜索条件 -->
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">套餐ID:</span>
				<Input v-model="packageId" clearable placeholder="请输入模板ID" style="width: 210px;" />
			</div>
			<div class="search_box">
				<span class="search_box_label">套餐名称:</span>
				<Input v-model="packageName" clearable placeholder="清输入模板名称" style="width: 210px;" />
			</div>
			<div class="search_box">
				<span class="search_box_label">支持国家:</span>
				<Select v-model="mcc" clearable placeholder="请选择国家/地区" filterable style="width: 210px;">
					<Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}
					</Option>
				</Select>
			</div>
			<div class="search_box">
				<span class="search_box_label">渠道商:</span>
				<Select v-model="corpId" clearable placeholder="请选择渠道商" filterable style="width: 210px;">
					<Option :value="item.corpId" v-for="(item,index) in corpList" :key="index">{{item.corpName}}
					</Option>
				</Select>
			</div>
			<div class="search_box">
				<span class="search_box_label">审核状态:</span>
				<Select v-model="auditStatus" clearable placeholder="请选择审核状态" filterable style="width: 210px;">
					<Option value="1">通过</Option>
					<Option value="2">待审核</Option>
					<Option value="3">不通过</Option>
				</Select>
			</div>
			<div style="width: 110px; display: flex;justify-content: center; margin-bottom: 20px;">
				<Button type="primary" icon="md-search" :loading="searchloading" v-has="'search'"
					@click="searchOne()">搜索</Button>
			</div>
			<div style="width: 160px; display: flex;justify-content: center; margin-bottom: 20px;">
				<Button type="error" icon="md-cloud-download" :loading="unapprovedloading" v-has="'unapproved'"
					@click="exportUnapproved()">未审批记录导出</Button>
			</div>
			<div style="width: 155px; display: flex;justify-content: center; margin-bottom: 20px;">
				<Button type="info" icon="md-brush" v-has="'fileApproval'"
					@click="batchFileApproval()">批量文件审批</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table ref="selection" :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="info">
				<Button type="info" ghost size="small" v-has="'detail'" @click="details(row)">详情</Button>
			</template>
			<template slot-scope="{ row, index }" slot="action">
				<div v-show="[1,4,5].includes(+row.auditStatus)">
					<Button type="primary" ghost size="small" style="margin-right: 10px" v-has="'examine'"
						@click="examine(2,row.id)">通过</Button>
					<Button type="error" ghost size="small" v-has="'examine'" @click="examine(3,row.id)">不通过</Button>
				</div>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 详情弹窗 -->
		<Modal v-model="modal1" title="套餐详情" @on-cancel="cancelModal" :mask-closable="false" width="600px">
			<Form ref="formObj" :model="formObj" :label-width="140">
				<FormItem label="套餐名称:" prop="nameCn">
					<Input v-model="formObj.nameCn" type="textarea" :rows="5" :maxlength="500"
						readonly placeholder="最多支持500字符" style="width: 350px;" />
				</FormItem>
				<FormItem label="套餐描述:" prop="descCn">
					<Input v-model="formObj.descCn" type="textarea" :rows="5" :maxlength="4000"
						readonly placeholder="最多可支持4000字符"
						style="width: 350px;" />
				</FormItem>
				<FormItem label="周期类型" prop="periodUnit">
					<Select v-model="formObj.periodUnit" filterable disabled style="width: 350px;"
						placeholder="请选择周期类型">
						<Option v-for="item in periodUnitList" :value="item.value" :key="item.value">{{ item.label }}
						</Option>
					</Select>
				</FormItem>
				<FormItem label="持续周期:" prop="keepPeriod">
					<Input v-model="formObj.keepPeriod" :maxlength="11" readonly
						placeholder="请选择持续周期" style="width: 350px;" />
				</FormItem>
				<FormItem label="套餐购买有效期:" prop="effectiveDay">
					<Input v-model="formObj.effectiveDay" :maxlength="11" readonly
						placeholder="请输入套餐购买有效期" style="width: 350px;">
					<span slot="append">天</span>
					</Input>
				</FormItem>
				<FormItem label="流量限制类型:" prop="flowLimitType">
					<Select v-model="formObj.flowLimitType" filterable
						disabled style="width: 350px;"
						placeholder="请选择流量限制类型">
						<Option :value="1">周期内限量</Option>
						<Option :value="2">按周期类型重置</Option>
					</Select>
				</FormItem>
				<FormItem label="控制逻辑:" prop="controlLogic">
					<Select v-model="formObj.controlLogic" filterable disabled
						style="width: 350px;" placeholder="请选择控制逻辑">
						<Option v-if="formObj.flowLimitType===1||formObj.flowLimitType===2 ||!formObj.flowLimitType"
							:value="1">达量限速</Option>
						<Option v-if="formObj.flowLimitType===1|| !formObj.flowLimitType" :value="2">达量释放</Option>
					</Select>
				</FormItem>
				<FormItem label="是否支持热点:" prop="isSupportedHotspots">
					<Select v-model="formObj.isSupportedHotspots" filterable
						disabled style="width: 350px;"
						placeholder="请选择是否支持热点">
						<Option :value="1">是</Option>
						<Option :value="2">否</Option>
					</Select>
				</FormItem>
				<FormItem label="选择国家/地区:" prop="mccList"
					v-show="formObj.isSupportedHotspots">
					<Tooltip :content="formObj.mccList" placement="top" max-width="300">
						<Input v-model="formObj.mccList" style="width: 350px;" readonly placeholder="请选择国家/地区" class="ellipsis-input"></Input>
					</Tooltip>
				</FormItem>
				<div v-show="formObj.mccList.length>0" v-for="(item,index) in formObj.combinationList" :key="index">
					<FormItem label="用量值:" :prop="'combinationList.' + index+ '.displayConsumption'">
						<Input v-model="item.displayConsumption" readonly placeholder="请选择用量值" style="width: 350px;"></Input>
					</FormItem>
					<FormItem label="速度模板:"
						:prop="'combinationList.' + index+ '.templateName'">
						<Input v-model="item.templateName" style="width: 350px;" readonly placeholder="请选择速度模板"></Input>
					</FormItem>
				</div>
				<FormItem label="无上限模板:" prop="noLimitTemplateName">
					<Input v-model="formObj.noLimitTemplateName" style="width: 350px;" readonly placeholder="请选择无上限模板"></Input>
				</FormItem>
				<FormItem label="是否支持加油包" prop="hasRefuelPackage">
					<i-switch v-model="formObj.hasRefuelPackage" size="large" disabled>
						<span slot="open">是</span>
						<span slot="close">否</span>
					</i-switch>
				</FormItem>
				<FormItem label="绑定加油包" prop="selectionTypes" v-if="formObj.hasRefuelPackage">
					<Button type="dashed" class="inputSty" style="width: 350px;" @click="RefuelPackageList">加油包列表</Button>
				</FormItem>

				<FormItem v-if="appInfos.length != 0" label="是否支持定向流量" prop="isSupportDirect">
					<Select v-model="formObj.isSupportDirect" class="inputSty" placeholder="请选择是否支持定向流量" disabled
						  @on-change="changeDirect($event)" style="width: 350px;">
						<Option value="1">是</Option>
						<Option value="2">否</Option>
					</Select>
				</FormItem>
				<div v-for="(fitem, findex) in formObj.directAppInfos" :key="findex">
					<Row v-if="formObj.isSupportDirect == '1'" type="flex" justify="start" align="middle">
						<Col span="24">
						<FormItem label="选择应用" :prop="'directAppInfos.' + findex + '.appId'">
							<Select v-model="fitem.appId" multiple filterable placeholder="请选择应用" disabled
								 @on-change="changeAppId($event)" style="width: 350px;">
								<Option v-for="(item2,index2) in appInfos" :value="item2.id" :key="item2.id">{{ item2.appName }}</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
					<Row v-if="formObj.isSupportDirect == '1'">
						<Col span="24">
						<FormItem label="定向使用逻辑" :prop="'directAppInfos.' + findex + '.directType'">
							<RadioGroup v-model="fitem.directType" @on-change="changeLogic($event)">
								<Radio label="1" disabled>限速</Radio>
								<Radio label="2" disabled>免流</Radio>
							</RadioGroup>
						</FormItem>
						</Col>
					</Row>
					<div v-if="formObj.isSupportDirect == '1' && fitem.directType == '2'" v-for="(item1, index1) in fitem.appConsumption" :key="index1">
						<Row>
							<Col span="24">
							<FormItem label="流量值" :prop="'directAppInfos.' + findex + '.appConsumption.' + index1 + '.consumption'">
								<Input v-model.trim="item1.consumption" readonly style="width: 350px;"
								placeholder="请输入流量值"><span slot="append">MB</span></Input>
							</FormItem>
							</Col>
						</Row>
						<Row type="flex" justify="start" align="middle">
							<Col span="24">
							<FormItem label="选择UPCC模板" :prop="'directAppInfos.' + findex + '.appConsumption.' + index1 + '.upccTemplateId'">
								<div v-for="(uitem, uindex) in fitem.appId"  style="display: flex; justify-content: flex-statrt; align-items: flex-start;">
									<Select v-model="item1.upccTemplateId[uindex]" filterable placeholder="请选择UPCC模板" style="margin-bottom: 20px;width: 350px;"
									 :key="uitem" disabled>
										<Option v-for="bitem in directTemplateList[uitem]" :value="bitem.upccTemplateId" :key="bitem.upccTemplateId">{{bitem.templateName}}</Option>
									</Select>
								</div>
							</FormItem>
							</Col>
						</Row>
					</div>
					<Row v-if="formObj.isSupportDirect == '1' && fitem.directType == '2'">
						<Col span="12">
						<FormItem label="是否继续使用通用模板" :prop="'directAppInfos.' + findex + '.isUsePackage'">
							<Select v-model="fitem.isUsePackage" placeholder="请选择是否继续使用通用模板" disabled
							 @on-change="changeUsePackage($event)" style="width: 350px;">
								<Option value="1">是</Option>
								<Option value="2">否</Option>
							</Select>
						</FormItem>
						</Col>
					</Row>
					<Row v-if="formObj.isSupportDirect == '1'">
						<Col span="12" v-if="fitem.directType == '1'">
						<FormItem :label="'定向限速模板'" :prop="'directAppInfos.' + findex + '.noLimitTemplateId'">
							<div v-for="(ditem, dindex) in fitem.appId">
								<Select v-model="fitem.noLimitTemplateId[dindex]" filterable placeholder="请选择定向限速模板" disabled
									:clearable="typeFlag!='info'" style="margin-bottom: 20px; width: 350px;" >
									<Option v-for="item3 in directTemplateList[ditem]" :value="item3.upccTemplateId" :key="item3.upccTemplateId">{{ item3.templateName }}</Option>
								</Select>
							</div>
						</FormItem>
						</Col>
						<Col span="12" v-if="fitem.isUsePackage && fitem.directType == '2'">
							<FormItem :label="fitem.isUsePackage == '2' ? '定向免流限速模板' : '定向免流继续使用模板'" :prop="'directAppInfos.' + findex + '.noLimitTemplateId'"
							 >
								<div v-for="(ditem, dindex) in fitem.appId">
									<Select v-model="fitem.noLimitTemplateId[dindex]" :placeholder="fitem.isUsePackage == '2' ? '请选择定向免流限速模板' : '请选择定向免流继续使用模板'"
									 disabled style="margin-bottom: 20px; width: 350px;">
										<Option v-for="item5 in directTemplateList[ditem]" :value="item5.upccTemplateId" :key="item5.upccTemplateId">{{ item5.templateName }}</Option>
									</Select>
								</div>
							</FormItem>
						</Col>
					</Row>
					<div style="margin-bottom: 30px;"></div>
				</div>

			</Form>
			<div slot="footer" class="footer_wrap">
				<Button @click="cancelModal">取消</Button>
			</div>
		</Modal>
		<Modal v-model="modal2" title="确认执行审核不通过？" @on-cancel="cancelModal" :mask-closable="false" width="500px">
			<Form ref="formItemReason" :model="formItemReason" :rules="ruleValidate" @submit.native.prevent>
				<FormItem prop="reasonText">
					<Input v-model="formItemReason.reasonText" maxlength="300" placeholder="请输入不通过原因……"></Input>
				</FormItem>
			</Form>
			<div slot="footer" style="text-align: right;">
				<Button style="margin-left: 8px" @click="cancelModal">取消</Button>
				<Button type="primary" @click="confirm">确定</Button>
			</div>
		</Modal>
		<!-- 加油包列表弹窗 -->
		<Modal title="添加加油包" v-model="addRefuelModel" :footer-hide="true" @on-cancel="cancelRefuelModal"
			:mask-closable="false" width="1000px">
			<Table :columns="Unitedcolumns" :data="Uniteddata" style="width:100%;"
				@on-selection-change="handleRowChange" @on-select-cancel="cancelPackage"
				@on-select-all-cancel="cancelPackageAll" :loading="Unitedloading">
			</Table>
			<div style="margin-top:15px">
				<Page :total="Unitedtotal" :current.sync="UnitedcurrentPage" show-total show-elevator
					@on-change="UnitedgoPage" />
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button style="margin-left: 8px" @click="cancelRefuelModal">取消</Button>
			</div>
		</Modal>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					<FormItem label="你本次导出任务ID为:">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem label="你本次导出的文件名为:">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="Goto">立即前往</Button>
			</div>
		</Modal>
		<!-- 批量文件审批弹窗 -->
		<Modal title="批量文件审批" v-model="fileApprovalflag" :footer-hide="true" :mask-closable="false" width="550px" @on-cancel="fileCancelModal">
			<div style="padding: 0 16px;">
				<Form ref="fileApprovalObj" :model="fileApprovalObj" :rules="ruleobj">
					<FormItem label="上传文件审批" style="font-size: 14px;font-weight: bold;" prop="file">
						<Upload type="drag" v-model="fileApprovalObj.file" :action="uploadUrl" :on-success="fileSuccess"
							:on-error="handleError" :before-upload="handleBeforeUpload" :on-progress="fileUploading"
							style="width: 400px; margin-top: 50px;margin-left: 50px;">
							<div style="padding: 20px 0">
								<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
								<p>点击或拖拽文件上传</p>
							</div>
						</Upload>
						<div style="width: 450px;">
							<Alert type="warning" style="padding: 8px 10px;margin-top: 10px;margin-left: 50px;">注：可使用“未审批记录导出”文件进行上传审批！</Alert>
						</div>
						<ul class="ivu-upload-list" v-if="file" style="width: 400px;margin-left: 50px;">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />{{file.name}}
								</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" @click="removeFile"></i>
							</li>
						</ul>

					</FormItem>
					<div style="width: 100%; display: flex;justify-content: center;margin-top: 50px;">
						<Button @click="fileCancelModal">返回</Button>&nbsp;&nbsp;&nbsp;&nbsp;
						<Button type="primary" :loading="importLoading" @click="fileConfirm">确定</Button>
					</div>
				</Form>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import PackageModel from '../../../components/package/packagemodel.vue'
	import {
		opsearchAll,
	} from '@/api/operators';
	import {
		getCorpList
	} from "@/api/product/package/batch";
	import {
		getList,
		examine,
		exportUnapprovedFile,
		fileConfirmApproval
	} from '@/api/package/channelPackage'
	import {addItem,updateItem,getUpccList} from '@/api/channel/package.js'
	import{getDetailsRefuelList,packageGetDirectional,deatilGetDirect} from '@/api/package/package.js'
	export default {
		components: {
			PackageModel
		},
		data() {
			// 自定义验证 判断上传文件 file, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (!this.file) {
					callback(new Error('请上传文件'))
				} else {
					callback()
				}
			}
			return {
				total: 0,
				currentPage: 1,
				page: 0,
				packageId: "", //套餐ID
				packageName: "", //套餐名称
				mcc: "", //支持国家
				auditStatus: "", //审核状态
				corpId: "", //渠道商
				corpIds: "", //渠道商
				types: "",
				ids: "",
				continentList: [],
				corpList: [],
				loading: false,
				searchloading: false, //查询加载
				unapprovedloading: false,
				Unitedloading: false,
				data: [], //表格列表
				columns: [{
					title: "渠道商",
					key: 'corpName',
					minWidth: 120,
					align: 'center',
					tooltip: true
				}, {
					title: "套餐ID",
					key: 'id',
					minWidth: 120,
					align: 'center',
					tooltip: true
				}, {
					title: "套餐名称",
					key: 'nameCn',
					minWidth: 120,
					align: 'center',
					tooltip: true,
					// render: (h, params) => {
					// 	const row = params.row
					// 	var text = row.nameCn
					// 	let length = text === "" || text === null ? 0 : text.length
					// 	if(length > 20){
					// 		let content = ""
					// 		for(let i=0;i<=row.nameCn.length;){
					// 			content=content+text.slice(i, i+17)+','
					// 			i=i+18
					// 		}
					// 		text = text.substring(0, 20) + "..."
					// 		return h('div', [h('Tooltip', {
					// 			props: {
					// 				placement: 'bottom',
					// 				transfer: true //是否将弹层放置于 body 内
					// 			},
					// 			style: {
					// 				cursor: 'pointer',
					//             },
					// 		},
					// 		[ //这个中括号表示是Tooltip标签的子标签
					// 			text, //表格列显示文字
					//            h('label', {
					// 				slot: 'content',
					// 				style: {
					// 					whiteSpace: 'normal'
					// 				},
					// 				domProps: {
					// 					innerHTML: content.replace(/\,/g, "</br>")
					// 				},
					// 			}, )
					// 		])
					// 	  ]);
					// 	}else {
					// 		return h('label', text)
					// 	}
					// }
				}, {
					title: "详情",
					slot: 'info',
					minWidth: 120,
					align: 'center',
				}, {
					title: '审核状态',
					key: 'auditStatus',
					align: 'center',
					minWidth: 120,
					render: (h, params) => {
						const row = params.row
						const color = row.auditStatus == '1' ? '#2b85e4' : row.auditStatus == '2' ? '#19be6b' :
							row.auditStatus == '3' ? '#ff0000' : row.auditStatus == '4' ? '#ffa554' :
							row.auditStatus == '5' ? '#ff0000' : '';
						const text = row.auditStatus == '1' ? '新建待审核' : row.auditStatus == '2' ? '通过' :
							row.auditStatus == '3' ? '不通过' : row.auditStatus == '4' ? '修改待审批' :
							row.auditStatus == '5' ? '删除待审批' : ''
						return h('label', {
							style: {
								color: color
							}
						}, text)
					}
				}, {
					title: "审核操作",
					slot: 'action',
					minWidth: 180,
					align: 'center',
					fixed: 'right'
				}, ],
				typeFlag: "",
				title: '', //弹窗标识
				// ——————————**详情**————————————
				modal1:false,
				modal2:false,
				addRefuelModel: false, //加油包弹窗标识
				exportModal: false,//导出弹框标识
				fileApprovalflag: false, //批量文件审批弹窗
				searchObjloading: false,
				importLoading: false,
				formObj:{
					nameCn:'',//套餐名称
					descCn:'',//套餐描述
					periodUnit:'',//周期类型
					keepPeriod:'',//持续周期
					effectiveDay:'',//套餐有效期
					flowLimitType:'',//流量限制类型
					controlLogic:'',//控制逻辑
					isSupportedHotspots:'',//是否支持热点
					mccList:'',//国家/地区
					templateName:'',//无上限模板
					combinationList:[{
						displayConsumption:'',//用量值
						templateName:'',//速度模板
					}],//用量值与速度模板组合列表
					noLimitTemplateName: "",//无上限模板
					groupId: "",
					hasRefuelPackage: false, //是否支持加油包
					refuelList: [], //加油包
					directAppInfos:[
						{
							index: 1,
							appId: [],//应用多选框选中的值
							directType: '',//定向使用逻辑
							appConsumption: [{
								index1: 1,
								consumption: '',//流量值
								upccTemplateId: [],//选择UPCC模板值
							}],
							isUsePackage: '',//是否继续使用通用模板
							noLimitTemplateId: [],//定向限速板/定向免流限速模版/定向免流继续使用模板
						}
					],
				},
				periodUnitList: [{
						value: '1',
						label: "24小时",
					},
          {
						value: '2',
						label: '自然日'
					},
					{
						value: '3',
						label: '自然月'
					},
					{
						value: '4',
						label: '自然年'
					}
				],
				Unitedtotal: 0,
				UnitedcurrentPage: 1,
				Unitedpage: 0,
				Uniteddata: [],
				Unitedcolumns: [{
					type: 'selection',
					width: 60,
					align: 'center'
				}, {
					title: "加油包ID",
					key: 'id',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				},
				{
					title: "加油包名称",
					key: 'nameCn',
					minWidth: 180,
					align: 'center',
					tooltip: true,
				}, {
					title: "加油包流量值(MB)",
					key: 'flowValue',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}],
				formItemReason: {
					reasonText: ""
				},
				ruleValidate: {
					reasonText: [{ required: true, message: '原因不能为空' }]
				},
				refuelIDList: [],
				searchObj: {
					gaspackname: '',
					gaspacknameid: '',
				},
				taskId:'',
				taskName:'',
				file: null,
				uploadList: [], //下载
				uploadUrl: '', //删除弹窗 上传地址
				updateuploadUrl: '', //修改弹窗 上传地址
				index: 1,
				index1: 1,
				appInfos: [],//应用列表
				directTemplateList: {}, //定向模板列表
				fileApprovalObj: {
					file: ''
				},
				ruleobj: {
					file: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
				},
			}
		},
		mounted() {
			this.goPageFirst(1)
			this.getLocalList1();
			this.getCorpList();
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				getList({
					page: page,
					pageSize: 10,
					isNeedAuth: true,
					isNeedMcc: true,
					list: true,
					packageId: this.packageId,
					packageNameCn: this.packageName,
					mcc: this.mcc,
					corpId: this.corpId,
					authStatus: this.auditStatus,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						let List = []
						// 判断审核表authObj是否有值，有值优先展示
						res.data.data.map((value, index) => {
							if (value.authObj) {
								List.push(value.authObj)
							} else {
								List.push(value)
							}
						})
						this.data = List;
						this.total = res.data.total
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			searchOne: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			exportUnapproved: function() {
				this.unapprovedloading = true
				exportUnapprovedFile({
					userId: this.$store.state.user.userId,
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.taskId
					this.taskName = res.data.taskName
					this.unapprovedloading = false
				}).catch((err) => {
					console.error(err)
					this.unapprovedloading = false
				}).finally(() => {})
			},
			batchFileApproval: function() {
				this.fileApprovalflag = true
			},
			// 批量删除 文件下载提示
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			handleBeforeUpload(file, fileList) {
				if (!/^.+(\.xlsx)$/.test(file.name)) {
					this.$Notice.warning({
						title: "文件格式不正确",
						desc: file.name + "格式不正确，请上传.xlsx格式文件"
					})
				} else {
					this.file = file,
					this.uploadList = fileList
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			removeFile() {
				this.file = ''
			},
			//导入文件提交
			fileConfirm: function() {
				this.$refs["fileApprovalObj"].validate(valid => {
					if (valid) {
						this.importLoading = true
						let formData = new FormData()
						formData.append('file', this.file)
						fileConfirmApproval(formData).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.goPageFirst(1)
								this.file = ''
								this.$refs['fileApprovalObj'].resetFields()
								this.fileApprovalflag = false
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						}).finally(() => {
							this.importLoading = false
						})
					}
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			//获取国家/地区
			getLocalList1() {
				opsearchAll().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.continentList = list;
						this.continentList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
						var localMap = new Map();
						list.map((local, index) => {
							localMap.set(local.mcc, local.countryEn);
						});
						this.localMap = localMap;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//获取渠道商
			getCorpList() {
				getCorpList({
					"type": 1,
					"status": 1,
					"checkStatus": 2
				}).then(res => {
					if (res && res.code == '0000') {
						this.corpList = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//审核
			examine(type, id) {
				if (type == 2) {
					this.$Modal.confirm({
						title: '确认执行审核通过？',
						onOk: () => {
							examine({
							id: id,
							auditStatus: type
						}).then(res => {
								if (res && res.code == '0000') {
									this.$Notice.success({
										title: '操作提示',
										desc: '操作成功'
									})
									this.goPageFirst(this.page)
								} else {
									throw res
								}
							}).catch((err) => {})
						}
					});
				} else {
					this.modal2 = true
					this.types = type
					this.ids = id
				}

			},
			confirm() {
				let noPassMessage = this.formItemReason.reasonText
				this.$refs.formItemReason.validate((valid) => {
					if (valid) {
						examine({
							id: this.ids,
							auditStatus: this.types,
							noPassMessage: noPassMessage
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.$refs['formItemReason'].resetFields()
								this.modal2 = false
								this.goPageFirst(this.page)
							} else {
								throw res
							}
						}).catch((err) => {})
					}
				})
			},
			// 查看详情
			details(row) {
				this.packageGetDirectional(row.corpId)
				this.modal1 = true
				this.refuelIDList = row.refuelIDList
				this.formObj.groupId= row.groupId
				this.corpIds= row.corpId
				this.formObj.id=row.id
				this.formObj.nameCn=row.nameCn
				this.formObj.descCn=row.descCn
				this.formObj.periodUnit=row.periodUnit
				this.formObj.effectiveDay=row.effectiveDay
				this.formObj.keepPeriod=row.keepPeriod
				this.formObj.noLimitTemplateName= row.noLimitTemplateName.length>35 ? row.noLimitTemplateName.substring(0,35) + "…" : row.noLimitTemplateName
				this.formObj.flowLimitType=Number(row.flowLimitType)
				this.formObj.controlLogic=Number(row.controlLogic)
				this.formObj.isSupportedHotspots=Number(row.isSupportedHotspots)
				this.formObj.templateName=row.noLimitTemplateId
				this.formObj.combinationList=[]
				let list = []
				Object.keys(row.mccMap).map((key)=>{
					list.push(row.mccMap[key])
				})
				this.formObj.mccList = list.join(" ,")
				row.packageConsumptions.map((item)=>{
					this.formObj.combinationList.push({
						consumption:item.consumption,//用量值
						displayConsumption:item.displayConsumption, //用量值名称
						upccTemplateId:item.upccTemplateId,//速度模板
						templateName:item.templateName.length>35 ? item.templateName.substring(0,35) + "…" : item.templateName,//速度模板
					})
				})
				this.formObj.hasRefuelPackage = row.refuelIDList.length > 0 ? true : false; //是否支持加油包
				this.formObj.selectionTypes = row.refuelIDList;//加油包列表
				this.formObj.isSupportDirect = row.isSupportDirect
				this.setData()
			},
			// 取消
			cancelModal(){
				this.modal1=false
				this.modal2= false
				this.formObj.controlLogic=""
				this.formObj.combinationList=[{
					displayConsumption:'',//用量值
					templateName:'',//速度模板
				}]
				this.$refs['formItemReason'].resetFields()
				this.exportModal=false

			},
			//关闭上传批量文件审核弹窗
			fileCancelModal() {
				this.fileApprovalflag = false
				this.file = ''
				this.$refs['fileApprovalObj'].resetFields()
			},
			RefuelPackageList() {
				this.getDetailsRefuelList(1)
			},
			search() {
				this.searchObjloading = true
				this.getDetailsRefuelList(1)
			},
			//获取可订购加油包列表
			getDetailsRefuelList(page) {
				this.Unitedloading = true
				getDetailsRefuelList({
					pageNum: page,
					pageSize: 10,
					refuelID: this.searchObj.gaspacknameid,
					refuelName: this.searchObj.gaspackname,
					packageID: this.formObj.id,
				}).then(res => {
					if (res && res.code == '0000') {
						this.Uniteddata = res.data
						this.Unitedtotal = res.count
						this.UnitedcurrentPage = page
						//回显
						this.formObj.selectionTypes.forEach(item => {
							res.data.forEach(element => {
								if (element.id == item.id) {
									this.$set(element, '_checked', true)
									this.$set(element, '_disabled', true)
								}
							})
						})
						this.addRefuelModel = true
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
					this.Unitedloading = false
					this.searchObjloading = false
				})
			},
			UnitedgoPage(page) {
				this.getDetailsRefuelList(page)
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					let flag = true;
					this.formObj.selectionTypes.map((item, index) => {
						if (value.id === item.id) {
							flag = false
						}
					});
					//判断重复
					if (flag) {
						this.formObj.selectionTypes.push(value);
					}
				});
			},
			// 取消选择套餐包
			cancelPackage(selection, row) {
				this.formObj.selectionTypes.forEach((value, index) => {
					if (value.id === row.id) {
						this.formObj.selectionTypes.splice(index, 1);
					}
				})
			},
			// 取消全选选择套餐包
			cancelPackageAll(selection, row) {
				this.formObj.selectionTypes = []
			},
			cancelRefuelModal() {
				this.addRefuelModel = false
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportModal = false
			},
			//获取定向应用和模板数据源接口
			packageGetDirectional(id) {
				packageGetDirectional({
					corpId: id
				}).then(res => {
					if (res && res.code == '0000') {
						this.appInfos = res.data
						this.appInfos.map((item,index)=>{
							this.directTemplateList[item.id] = item.appUpccInfo
						})
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			// 定向应用信息初始化
			setData() {
				deatilGetDirect({
					packageId: this.formObj.id
				}).then(res => {
					if (res && res.code == '0000') {
						let transformedData = [];

						res.data.forEach((item, index) => {
						    let transformedItem = {
						        index: index + 1,
						        appId: [],
						        directType: item.directType,
						        noLimitTemplateId: item.appDetailInfos.map(detail => detail.noLimitTemplateId),
								isUsePackage: item.isUsePackage
						    };

						    item.appDetailInfos.forEach(appDetail => {
						        transformedItem.appId.push(appDetail.appId);

						        if (appDetail.appConsumption.length > 0) {
						            appDetail.appConsumption.forEach(consumption => {
						                if (!transformedItem.appConsumption) {
						                    transformedItem.appConsumption = [];
						                }

						                let existingConsumption = transformedItem.appConsumption.find(c => c.consumption === consumption.consumption);
						                if (existingConsumption) {
						                    existingConsumption.upccTemplateId.push(consumption.upccTemplateId);
						                } else {
						                    transformedItem.appConsumption.push({
						                        index1: transformedItem.index,
						                        consumption: consumption.consumption,
						                        upccTemplateId: [consumption.upccTemplateId],
												isUsePackage: transformedItem.isUsePackage,
												noLimitTemplateId: transformedItem.noLimitTemplateId,
						                    });
						                }
						            });
						        }
						    });

						    transformedData.push(transformedItem);
						});

						this.formObj.directAppInfos = transformedData

					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
		}
	}
</script>

<style scoped="scoped">
	.search_head_i {
		margin-top: 30px;
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 90px;
	}

	.footer_wrap {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
