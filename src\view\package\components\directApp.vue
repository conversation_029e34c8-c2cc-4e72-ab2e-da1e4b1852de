<template>
	<div>
		<Form ref="procedureEdit" :model="formObj" :label-width="150" :rules="ruleAddValidate">
		<div v-for="(fitem, findex) in formObj.directAppInfos" :key="fitem.index">
			<Row type="flex" justify="start" align="middle" v-if="isSupportDirect == '1'" >
				<Col span="24">
				<FormItem label="选择应用" :prop="'directAppInfos.' + findex + '.appId'" :rules="ruleAddValidate.appId">
					<Select v-model="fitem.appId" multiple filterable placeholder="请选择应用" :disabled="typeFlag=='Info' || notClick == true"
						clearable @on-change="changeAppId($event,findex)" style="width: 400px;" >
						<Option v-for="(item2,index2) in choseAppInfos(fitem.appId)" :value="item2.id" :key="item2.id" :disabled="!fitem.appId.includes(item2.id) && appTotal == true">{{ item2.appName }}</Option>
					</Select>
					<Button :disabled="typeFlag=='Info' || notClick == true" v-if="findex != 0" size="small" type="error" style="margin-left: 20px; width: 80px;" @click="deleteApp(findex)">删除应用</Button>
					<Button :disabled="typeFlag=='Info' || notClick == true || appTotal == true" v-if="findex == (formObj.directAppInfos.length-1)" size="small" type="primary" style="margin-left: 20px; width: 80px;" @click="addApp()">添加应用</Button>
				</FormItem>
				</Col>
			</Row>
			<Row v-if="isSupportDirect == '1'">
				<Col span="24">
				<FormItem label="定向使用逻辑" :prop="'directAppInfos.' + findex + '.directType'" :rules="ruleAddValidate.directType">
					<RadioGroup v-model="fitem.directType" @on-change="changeLogic(findex)">
						<Radio label="1" :disabled="typeFlag=='Info' || notClick == true">限速</Radio>
						<Radio label="2" :disabled="typeFlag=='Info' || notClick == true">免流</Radio>
					</RadioGroup>
				</FormItem>
				</Col>
			</Row>
			<div v-if="isSupportDirect == '1' && fitem.directType == '2'" v-for="(item1, index1) in fitem.appConsumption" :key="index1">
				<Row style="margin-bottom: 5px;">
					<Col span="16">
					<FormItem label="流量值" :prop="'directAppInfos.' + findex + '.appConsumption.' + index1 + '.consumption'" :rules="ruleAddValidate.consumption">
						<Input type="number" v-model.trim="item1.consumption" :readonly="typeFlag=='Info'" :clearable="typeFlag!='Info'" :disabled="notClick == true"
						placeholder="请输入流量值" style="width: 300px;"><span slot="append">MB</span></Input>
					</FormItem>
					</Col>
					<Col span="4" style="padding: 6px 0px 0px 20px;" v-if="index1 != 0">
						<Button size="small" type="error" ghost style="width: 80px;" @click="delFlowValue(findex,index1)" :disabled="typeFlag=='Info' || notClick == true">删除流量值</Button>
					</Col>
					<Col span="4" style="padding: 6px 0px 0px 10px;" v-if="index1 == (fitem.appConsumption.length - 1)">
						<Button size="small" type="info" ghost style="width: 80px;" @click="addFlowValue(findex)" :disabled="typeFlag=='Info' || notClick == true">添加流量值</Button>
					</Col>
				</Row>
				<Row type="flex" justify="start" align="middle">
					<Col span="24">
					<FormItem label="选择UPCC模板" :prop="'directAppInfos.' + findex + '.appConsumption.' + index1 + '.upccTemplateId'" :rules="ruleAddValidate.upccTemplateId">
						<div v-for="(uitem, uindex) in fitem.appId"  style="display: flex; justify-content: flex-statrt; align-items: flex-start;">
							<Select v-model="item1.upccTemplateId[uindex]" filterable placeholder="请选择UPCC模板" style="margin-bottom: 20px;width: 300px;"
							:clearable="typeFlag!='Info'" :disabled="typeFlag=='Info' || notClick == true" :key="uitem">
								<Option v-for="bitem in directTemplateList[uitem]" :value="bitem.upccTemplateId" :key="bitem.upccTemplateId">{{bitem.templateName}}</Option>
							</Select>
						</div>
					</FormItem>
					</Col>
				</Row> 
			</div>
			<Row v-if="isSupportDirect == '1' && fitem.directType == '2'">
				<Col span="12">
				<FormItem label="是否继续使用通用流量" :prop="'directAppInfos.' + findex + '.isUsePackage'" :rules="ruleAddValidate.isUsePackage">
					<Select v-model="fitem.isUsePackage" placeholder="请选择是否继续使用通用流量" :disabled="typeFlag=='Info' || notClick == true"
					:clearable="typeFlag!='Info'" @on-change="changeUsePackage($event)" style="width: 300px;">
						<Option value="1">是</Option>
						<Option value="2">否</Option>
					</Select>
				</FormItem>
				</Col>
			</Row>
			<Row v-if="isSupportDirect == '1'">
				<Col span="12" v-if="fitem.directType == '1'">
				<FormItem :label="'定向限速模板'" :prop="'directAppInfos.' + findex + '.noLimitTemplateId'" :rules="ruleAddValidate.noLimitTemplateId">
					<div v-for="(ditem, dindex) in fitem.appId">
						<Select v-model="fitem.noLimitTemplateId[dindex]" filterable placeholder="请选择定向限速模板" :disabled="typeFlag=='Info' || notClick == true"
							:clearable="typeFlag!='Info'" style="margin-bottom: 20px;width: 300px;">
							<Option v-for="item3 in directTemplateList[ditem]" :value="item3.upccTemplateId" :key="item3.upccTemplateId">{{ item3.templateName }}</Option>
						</Select>
					</div>
				</FormItem>
				</Col>
				<Col span="12" v-if="fitem.isUsePackage && fitem.directType == '2'">
					<FormItem :label="fitem.isUsePackage == '2' ? '定向免流限速模板' : '定向免流继续使用模板'" :prop="'directAppInfos.' + findex + '.noLimitTemplateId'"
					  :rules="ruleAddValidate.noLimitTemplateId">
						<div v-for="(ditem, dindex) in fitem.appId">
							<Select v-model="fitem.noLimitTemplateId[dindex]" :placeholder="fitem.isUsePackage == '2' ? '请选择定向免流限速模板' : '请选择定向免流继续使用模板'"
							 :disabled="typeFlag=='Info' || notClick == true" :clearable="typeFlag!='Info'" style="margin-bottom: 20px;width: 300px;">
								<Option v-for="item5 in directTemplateList[ditem]" :value="item5.upccTemplateId" :key="item5.upccTemplateId">{{ item5.templateName }}</Option>
							</Select>
						</div>
					</FormItem>
				</Col>
			</Row>
			<div style="margin-bottom: 30px;"></div>
		</div>
		</Form>
	</div>
</template>

<script>
	import {
		packageGetDirectional,
		deatilGetDirect,
	} from '@/api/package/package';
	export default {
		props: {
			isSupportDirect: {
				type: String,
				default: ''
			},
			typeFlag: {
				type: String,
				default: ''
			},
			packageId: {
				type: String,
				default: ''
			},
			notClick: {
				type: Boolean,
				default: ''
			}
		},
		data() {
			// upcc模板
			const validateUpcc = (rule, value, callback) => {
				let matches = rule.field.match(/\d+/g); //使用正则表达式匹配所有数字  
				let directIndex = matches[0]; //得到对应的应用组
				let appIndex = matches[1]; //得到对应的应用组
				let upcc = this.formObj.directAppInfos[directIndex].appConsumption[appIndex].upccTemplateId;
				let appIds = this.formObj.directAppInfos[directIndex].appId;
				
				//判断用户点击upcc后删除产生的undefined或者null
				let filteredArr = upcc.filter(item => item !== undefined && item !== null && item !== '');
				let isValid = upcc.every(item => item !== null && item !== '' && item !== undefined);
				
				
				if (upcc.length == 0) {
					callback(new Error(rule.message));
				} else if (upcc.length < appIds.length) {
					callback(new Error(rule.message));
				} else if (!isValid) {
					callback(new Error(rule.message));
				} else if (upcc.length != filteredArr.length) {
					callback(new Error(rule.message));
				} else {
					callback();
				}
			};
			// 定向限速模板/定向免流限速模板/定向免流继续使用模板
			const validateSpeedLimit = (rule, value, callback) => {
				let matches = rule.field.match(/\d+/g); //使用正则表达式匹配所有数字 
				let directIndex = matches[0]; //得到对应的应用组的定向限速模板
				let speedLimit = this.formObj.directAppInfos[directIndex].noLimitTemplateId;
				let appIds = this.formObj.directAppInfos[directIndex].appId;
				//判断用户点击定向限速模板后删除产生的undefined或者null
				let filteredArr = speedLimit.filter(item => item !== undefined && item !== null && item !== '');
				let isValid = filteredArr.every(item => item !== null && item !== '' && item !== undefined);
				
				let length = appIds.length
				let noLimitTemplateIds = speedLimit.slice(0, length)
				let filteredArrs = noLimitTemplateIds.filter(item => item !== undefined && item !== null && item !== '');
				//判断定向限速模板/定向免流限速模板/定向免流继续使用模板
				let templateType = this.formObj.directAppInfos[directIndex].directType
				let isUsePackage = this.formObj.directAppInfos[directIndex].isUsePackage
				let message;
				if (templateType == '1') {
					message = '定向限速模板不能为空！'
				} else {
					if (isUsePackage == '1') {
						message = '定向免流继续使用模板不能为空！'
					} else {
						message = '定向免流限速模板不能为空！'
					}
				}
				if (speedLimit.length == 0) {  
					// 模板为空时
					callback(new Error(message));
				} else if (speedLimit.length < appIds.length) {
					// 模板长度和应用长度不一致时
					callback(new Error(message));
				} else if (!isValid ) {
					// 从上选到下，然后又删除
					callback(new Error(message));
				} else if (noLimitTemplateIds.length != filteredArrs.length) {
					// 从最后开始选，空留上面的；多并发选择了又删，删了又选
					callback(new Error(message));
				} else {
					callback();
				}
			};
			// 流量值不可以重复
			const validateRepeat = (rule, value, callback) => {
				let matches = rule.field.match(/\d+/g); //使用正则表达式匹配所有数字
				let directIndex = matches[0]; //得到对应的应用组
				let consumptionList = [];
				if (this.formObj.directAppInfos[directIndex].appConsumption.length > 1) {
					this.formObj.directAppInfos[directIndex].appConsumption.forEach((item,index) => {
						consumptionList.push(item.consumption)
					})
					consumptionList = consumptionList.map(String);  
					let set = new Set();  
					let duplicates = [];  
					for (let i = 0; i < consumptionList.length; i++) {  
						if (!set.has(consumptionList[i])) {  
							set.add(consumptionList[i]);  
						} else {  
							duplicates.push(consumptionList[i]);  
						}  
					}
					if (duplicates.includes(value)) {
						callback(new Error(rule.message));
					} else {
						callback();
					}
				} else {
					callback();
				}
			};
			// 流量值要比前面的流量值大
			const validateLarger = (rule, value, callback) => {
				let matches = rule.field.match(/\d+/g); //使用正则表达式匹配所有数字
				let directIndex = matches[0]; //得到对应的应用组
				let cIndex = matches[1]; //得到对应的流量值下标
				let arrList = this.formObj.directAppInfos[directIndex].appConsumption[cIndex].consumption
				if (this.formObj.directAppInfos[directIndex].appConsumption.length > 1) {
					let arrList1 =  (cIndex > 0) ? this.formObj.directAppInfos[directIndex].appConsumption[cIndex-1].consumption : null
					if (arrList1 && (Number(arrList) < Number(arrList1))) {
						callback(new Error(rule.message));
					} else {
						callback();
					}
				} else {
					callback();
				}	
			};
			return {
				formObj: {
					directAppInfos:[
						{
							index: 1,
							appId: [],//应用多选框选中的值
							directType: '',//定向使用逻辑
							appConsumption: [{
								index1: 1,
								consumption: '',//流量值
								upccTemplateId: [],//选择UPCC模板值
							}],
							isUsePackage: '',//是否继续使用通用流量
							noLimitTemplateId: [],//定向限速板/定向免流限速模版/定向免流继续使用模板
						}
					],
				},	
				index: 1,
				index1: 1,
				upccIndex: '',
				upccChange: '',
				appTotal: false,
				initialized: true, // 添加一个标志位
				selectedValues: [], // 当前选中的upcc值  
				oldValue: [], // 上一次选中的upcc值  
				appInfos: [],//应用列表
				directTemplateList: {}, //定向模板列表
				selectedOptions: [],
				ruleAddValidate: {
					directType: [{
						required: true,
						message: '定向使用逻辑不能为空',
					}],
					appId: [{
						required: true,
						type: 'array',
						message: '选择应用不能为空',
					}],
					isUsePackage: [{
						required: true,
						message: '是否继续使用通用流量不能为空',
					}],
					consumption: [{
						required: true,
						message: '流量值不能为空',
					}, {
						validator: (rule, value, cb) => {
							var str = /^[1-9]\d*$/;
							return str.test(value);
						},
						message: '请输入正整数',
					}, {
						validator: validateRepeat, 
						message:'流量值不能重复',
					}, {
						validator: validateLarger, 
						message:'流量值逻辑不正确，每档的流量值需大于上一档次的流量值',
					}],
					upccTemplateId: [{
						required: true,
						validator: validateUpcc,
						message: '选择UPCC模版不能为空',
						// trigger: 'blur',
					}],
					noLimitTemplateId: [{
						required: true,
						validator: validateSpeedLimit,
						// trigger: 'blur',
					}]
				}
			}
		},
		computed: {
			// 应用不能重复选择，去掉已经选择的选项
			choseAppInfos() {
				// 把已经选中的选项过滤掉
				return (val) => {
					let newList = JSON.parse(JSON.stringify(this.appInfos));
					//处理appId数据，返回一个新数组arr
					//arr数组就相当于所有Select选中的数据集合（没有选中的为''，不影响判断），只要在这个集合里面，其他的下拉框就不应该有这个选项
					const arr = [];
					this.formObj.directAppInfos.forEach(item => {
						item.appId.map(i => {
							arr.push(i) 
							return arr
						})
					});
					//过滤出newList里面需要显示的数据
					newList = newList.filter(item => {
						//当前下拉框的选中的数据需要显示
						//val就是当前下拉框选中的值
						if (val.includes(item.id)) {
							return item;
						} else {
							//再判断在arr这个数组中是不是有这个数据，如果不在，说明是需要显示的
							if (arr.indexOf(item.id) == -1) {
								return item;
							}
						}
					});
					return newList;
				}
			},
		},
		watch: {  
		    // 监听 upccChange 的变化  
		  //   upccChange(newVal, oldVal) {
				// if (this.initialized) {
				// 	// 执行一些异步或复杂操作
				// 	// setTimeout(() => {  
				// 		let upccIndex = this.upccIndex
				// 		let missingIndex = ''
				// 		if (newVal.length < oldVal.length) {
				// 			function findMissingIndex(arr1, arr2) {  
				// 				// 遍历arr1数组  
				// 				for (let i = 0; i < arr1.length; i++) {  
				// 					// 检查arr1中的当前元素是否存在于arr2中  
				// 					if (arr2.indexOf(arr1[i]) === -1) {  
				// 					// 如果不存在，返回当前元素在arr1中的下标  
				// 					return i;  
				// 					}  
				// 				}  
				// 				// 如果没有找到缺失的元素，则返回-1表示没有缺失  
				// 				return -1;  
				// 			} 
				// 			missingIndex = findMissingIndex(oldVal,newVal)
				// 			let arrList = this.formObj.directAppInfos[upccIndex].appConsumption
				// 			// 每删除一个应用,就删除对应的upcc模板
				// 			arrList.forEach((i) => {
				// 				i.upccTemplateId.splice(missingIndex, 1)
				// 			})
				// 		}
				// 	// }, 3000);
				// } else {
				// 	return
				// }
			 //  } 
		},
		methods: {
			//添加应用
			addApp() {
				this.index++;
				this.formObj.directAppInfos.push({
				    index: this.index,
				    appId: [],//应用列表
					directType: '',//定向使用逻辑
					appConsumption: [{
						index1: 1,
						consumption: '',//流量值
						upccTemplateId: [],//选择UPCC模板值
					}],
					isUsePackage: '',//是否继续使用通用流量
					noLimitTemplateId: [],//定向限速板/定向免流限速模版/定向免流继续使用模板
				});
			},
			//刪除应用
			deleteApp(index) {
				this.formObj.directAppInfos.splice(index, 1)
				this.index--;
				
				//删除应用后重新计算应用总数
				let arr = [];
				this.formObj.directAppInfos.forEach(item => {
					item.appId.map(i => {
						arr.push(i) 
						return arr
					})
				});
				if (arr.length >= '9') {
					this.appTotal = true
				} else {
					this.appTotal = false
				}
			},
			//删除流量值
			delFlowValue(findex,index) {
				let directAppInfos = this.formObj.directAppInfos[findex]
				directAppInfos.appConsumption.splice(index, 1);
				this.index1--;
				
			},
			//添加流量值
			addFlowValue(findex) {
				let directAppInfos = this.formObj.directAppInfos[findex]
				this.index1++;
				directAppInfos.appConsumption.push({
					index1: this.index1,
					consumption: '',//流量值
					upccTemplateId:[],//选择UPCC模板值
				})
			},
			//改变定向流量逻辑时清空数据
			changeLogic(findex) {
				
				let directAppInfos = this.formObj.directAppInfos[findex]
				directAppInfos.appConsumption= [{
					index1: this.index1,
					consumption: '',//流量值
					upccTemplateId:[],//选择UPCC模板值
				}]
				directAppInfos.isUsePackage =''
				directAppInfos.noLimitTemplateId = []
				
			},
			//改变是否继续使用通用模版时清除数据
			changeUsePackage(e) {
				// this.formObj.directAppInfos.forEach((i) => {
				// 	i.noLimitTemplateId = []
				// })
			},
			//计算应用总数
			changeAppId(e,findex) {
				let arr = [];
				this.formObj.directAppInfos.forEach(item => {
					item.appId.map(i => {
						arr.push(i) 
						return arr
					})
				});
				if (arr.length >= '9') {
					this.appTotal = true
				} else {
					this.appTotal = false
				}
				
				this.upccChange = e
				this.upccIndex = findex
				// 如果appId一样 查看upcc
				
				if (this.initialized) { 
					console.error(this.initialized,"处理数据，初始化已经完成")
					if(this.formObj.directAppInfos[findex].appId == e) {
						const oldValue = this.oldValue
						this.oldValue = e //更新旧值为当前值，为下一次变化做准备
						if (e.length < oldValue.length) {
							function findMissingIndex(arr1, arr2) {  
								// 遍历arr1数组  
								for (let i = 0; i < arr1.length; i++) {  
									// 检查arr1中的当前元素是否存在于arr2中  
									if (arr2.indexOf(arr1[i]) === -1) {  
									// 如果不存在，返回当前元素在arr1中的下标  
									return i;  
									}  
								}  
								// 如果没有找到缺失的元素，则返回-1表示没有缺失  
								return -1;  
							} 
							let missingIndex = ''
							missingIndex = findMissingIndex(oldValue,e)
							let arrList = this.formObj.directAppInfos[findex].appConsumption
							// 每删除一个应用,就删除对应的upcc模板
							arrList.forEach((i) => {
								i.upccTemplateId.splice(missingIndex, 1)
							})
						}
					}
					
				} else {
					console.error(this.initialized,"不处理")
					return
				}
			},
			//获取定向应用和模板数据源接口
			packageGetDirectional() {
				packageGetDirectional().then(res => {
					if (res && res.code == '0000') {
						this.appInfos = res.data
						this.appInfos.map((item,index)=>{
							this.directTemplateList[item.id] = item.appUpccInfo
						})
						this.initialized = false
					} else {
						throw res
					}
				}).catch((err) => {
			
				}).finally(() => {
					this.initialized = true
				})
			},
			childMethod() { 
				return this.formObj.directAppInfos	
			},
			//校验数据不能为空
			childSubmit() {
				return new Promise((resolve, reject) => {  
				    // 假设这里是一些异步操作，例如网络请求  
				    // 成功时调用 resolve，失败时调用 reject  
					this.$refs["procedureEdit"].validate((valid) => {
						resolve(valid);
					})
				});
			},
			
			// 定向应用信息初始化
			setData(id) {
				//不可以执行
				deatilGetDirect({
					packageId: id
				}).then(res => {
					if (res && res.code == '0000') {
						let transformedData = [];
						res.data.forEach((item, index) => {
							
						    let transformedItem = {
						        index: index + 1,
						        appId: [],
						        directType: item.directType,
						        noLimitTemplateId: item.appDetailInfos.map(detail => detail.noLimitTemplateId),
								isUsePackage: item.isUsePackage
						    };
						
						    item.appDetailInfos.forEach(appDetail => {
						        transformedItem.appId.push(appDetail.appId);
						
						        if (appDetail.appConsumption.length > 0) {
						            appDetail.appConsumption.forEach(consumption => {
										consumption.consumption = consumption.consumption.toString()
						                if (!transformedItem.appConsumption) {
						                    transformedItem.appConsumption = [];
						                }
						                let existingConsumption = transformedItem.appConsumption.find(c => c.consumption === consumption.consumption);
						                if (existingConsumption) {
						                    existingConsumption.upccTemplateId.push(consumption.upccTemplateId);
						                } else {
						                    transformedItem.appConsumption.push({
						                        index1: transformedItem.index,
						                        consumption: consumption.consumption,
						                        upccTemplateId: [consumption.upccTemplateId],
						                    });
						                }
						            });
						        } else {
									transformedItem.appConsumption= [{
										index1: this.index1,
										consumption: '',
										upccTemplateId: [],
									}]
								}
						    });
						
						    transformedData.push(transformedItem);
						});
						
						this.formObj.directAppInfos = transformedData
						this.initialized = false
					} else {
						throw res
					}
				}).catch((err) => {
							
				}).finally(() => {
					this.initialized = true
				})
			},
		},
		mounted() {
			this.packageGetDirectional()
			if (this.typeFlag != 'Add') {
				this.$nextTick(() => {
					if (this.isSupportDirect == '1') {
						this.setData(this.packageId)
					}
				})
			}
		},
	}
	
</script>

<style>
</style>