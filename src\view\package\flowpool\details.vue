<template>
	<!-- 新建流量池 -->
	<Card style="width: 100%;padiing: 16px;">
		<div style="display: flex;justify-content: center;margin: 20px 0;">
			<Form ref="formObj" :model="formObj" :label-width="150" >
				<Row>
					<Col span="24">
					<FormItem label="流量池名称(简中)" prop="flowPoolName">
						<Input v-model='formObj.flowPoolName'  :readonly="true" placeholder="请输入流量池名称(简中)" :clearable="false" style="width: 600px;" />
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24">
					<FormItem label="流量池名称(繁中)" prop="nameTw">
						<Input v-model='formObj.nameTw' placeholder="請輸入流量池名稱(繁中)" :readonly="true" :clearable="false" style="width: 600px;" />
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24">
					<FormItem label="流量池名称(英文)" prop="nameEn">
						<Input v-model='formObj.nameEn' placeholder="Please enter the name of the flow pool (English)" :readonly="true" :clearable="false" style="width: 600px;" />
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="选择客户" prop="corpId">
						<Select v-model="formObj.corpId" filterable placeholder="请选择客户" :readonly="true" :clearable="false" style="width: 200px;" disabled>
							<Option :value="item.corpId" v-for="(item,index) in corpList" :key="index">{{item.corpName}}</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="流量池ID" >
						<Input v-model='formObj.flowpoolid'   style="width: 200px;" :readonly="true"/>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="支持国家" prop="mccList">
						<Select v-model="formObj.mccList" multiple placeholder="请选择国家/地区" :disabled="typeFlag=='Info'" :readonly="true" :clearable="false"
						 :filterable="true" @on-change="mccListChange" style="width: 200px;" disabled>
							<Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="关联卡池" prop="cardPool">
						<Button  type="dashed" class="inputSty" long @click="loadCardPoolView(formObj.mccList)"
						 style="width: 200px;" :disabled="formObj.mccList.length==0">点击查看</Button>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="流量池总量" prop="flowPoolTotal">
						<Input v-model='formObj.flowPoolTotal' placeholder="请输入流量池总量" :readonly="true" :clearable="false" style="width: 200px;">
						<span slot="append">GB</span>
						</Input>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="高速签约模板" prop="upccHignSignId">
						<Input v-model='formObj.upccHignSignId' placeholder="请输入高速签约模板" :readonly="true" :clearable="false" style="width: 200px;" />
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="低速签约模板" prop="upccLowerSignId">
						<Input v-model='formObj.upccLowerSignId' placeholder="请输入低速签约模板" :readonly="true" :clearable="false" style="width: 200px;" />
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="限速签约模板" prop="upccLimitsSignId">
						<Input v-model='formObj.upccLimitsSignId' placeholder="请输入限速签约模板" :readonly="true" :clearable="false" style="width: 200px;" />
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="重置周期类型" prop="cycleType">
						<Select filterable v-model="formObj.cycleType" :readonly="true" :clearable="false" placeholder="请选择重置周期类型" style="width: 200px ;" disabled >
							<Option :value="1">24小时</Option>
							<Option :value="2">自然日</Option>
							<Option :value="3">自然月</Option>
							<Option :value="4">自然年</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="重置周期数" prop="cycleNum">
						<Input v-model='formObj.cycleNum' placeholder="请输入重置周期数" :readonly="true" :clearable="false" style="width: 200px;" />
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24">
					<FormItem label="控制逻辑" prop="controlLogic">
						<Select filterable v-model="formObj.controlLogic" :readonly="true" :clearable="false" placeholder="请选择控制逻辑" style="width: 200px ;" disabled>
							<Option :value='1'>达量限速</Option>
							<Option :value='2'>达量停用</Option>
							<Option :value='3'>达量继续使用</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="有效日期" prop="startTime">
						<DatePicker type="datetime" format="yyyy/MM/dd HH:mm:ss" class="inputSty" placeholder="请选择开始时间" v-model="formObj.startTime"
						 @on-change="changestartTime" style="width: 200px;" :readonly="true" :clearable="false" disabled></DatePicker>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem prop="endTime">
						<DatePicker type="datetime" format="yyyy/MM/dd HH:mm:ss" class="inputSty" placeholder="请选择结束时间" v-model="formObj.endTime"
						 @on-change="changeendTime" style="width: 200px;" :readonly="true" :clearable="false" disabled></DatePicker>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
						<FormItem label="上架状态">
							<Select filterable v-model="formObj.shelfstatus"  :readonly="true"   placeholder="请选择上架状态" style="width: 200px ;margin-right: 10px;" disabled>
							  <Option  :value="1" >上架</Option>
							  <Option  :value="2" >下架</Option>
							</Select>
						</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="12">
					<FormItem label="流量池价格" prop="flowPoolPrice">
						<Input v-model='formObj.flowPoolPrice' placeholder="请输入流量池价格" :readonly="true" :clearable="false" style="width: 200px;">
						<span slot="append">元</span>
						</Input>
					</FormItem>
					</Col>
					<Col span="12">
					<FormItem label="超量后价格" prop="flowPoolExtraPrice">
						<Input v-model='formObj.flowPoolExtraPrice' placeholder="请输入超量后价格" :readonly="true" :clearable="false" style="width: 200px;">
						<span slot="append">元/GB</span>
						</Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24">
					<FormItem label="用量提醒阈值" prop="alarmThreshold">
						<Input v-model='formObj.alarmThreshold' placeholder="百分比" :readonly="true" :clearable="false" style="width: 200px;" />
					</FormItem>
					</Col>
				</Row>
			</Form>
		</div>
		<div style="display: flex;justify-content: center;margin: 20px 0;">
			<Button style="margin: 0 4px" @click="back">返回</Button>
			<!-- <Button type="primary" @click="Confirm" :loading="loading">确定</Button> -->
		</div>
		<!-- 卡池树 -->
		<Drawer title="关联卡池管理" v-model="drawer" width="350" :mask-closable="false" :styles="styles" @on-close="drawerClose">
			<!-- <Button type="success" size="small" style="margin: 0 15px" v-if="typeFlag!='Info'" @click="cardPoolEdit">编辑</Button> -->
			<Tree :data="cardPoolTree" ref="cardPool" class="demo-tree-render" :empty-text="emptyText"></Tree>
			<div class="demo-drawer-footer" v-if="typeFlag!='Info'">
				<Button style="margin-right: 8px" @click="drawerClose">取消</Button>
				<!-- <Button type="primary" @click="toSetCardPool()">确定</Button> -->
			</div>
		</Drawer>
		<!-- 卡池编辑弹框 -->
		<Modal title="卡池编辑" v-model="cardPoolEditFlag" @on-cancel="cardPoolEditConfirm" :mask-closable="false" width="730px">
			<div style="padding: 0 16px;">
				<Form ref="cpEditForm" :model="filterSearchObj" inline style="font-weight:bold;">
					<FormItem>
						<Input type="text" v-model="filterSearchObj.cpName" clearable placeholder="卡池名称" disabled></Input>
					</FormItem>
					<FormItem>
						<Input type="text" v-model="filterSearchObj.sName" clearable placeholder="供应商名称" disabled></Input>
					</FormItem>
					<FormItem>
						<Input type="text" v-model="filterSearchObj.cName" clearable placeholder="国家/地区名称" disabled></Input>
					</FormItem>
					<FormItem>
						<Button type="primary" :loading="cardPoolEditTreeLoad" @click="doCPTreeFilter">搜索</Button>
					</FormItem>
				</Form>
				<div class="demo-spin-article">
					<div style="height: 295px; overflow-y: auto;">
						<Tree :data="cardPoolEditTree" ref="cardPool" class="demo-tree-render" :empty-text="emptyText"></Tree>
					</div>
					<Spin size="large" fix v-if="cardPoolEditTreeLoad"></Spin>
				</div>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button type="primary"  @click="cardPoolEditConfirm">确定</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		opsearchAll,
	} from '@/api/operators';
	import {
		addflow,
		getcardpool
	} from "@/api/package/flowpool";
	import {
		getCorpList
	} from '@/api/product/package/batch';
	export default {
		data() {
			const validateDate = (rule, value, callback) => {
				let endDate = this.formObj.endTime == "" ? 0 : this.formObj.endTime;
				let startDate = this.formObj.startTime == "" ? 0 : this.formObj.startTime;
				if ((rule.field === "startDate") || rule.field === "startTime") {
					if (endDate != 0 && startDate > endDate) {
						callback(new Error("开始时间不能大于结束时间"));
					} else {
						callback();
					}
				} else {
					if (startDate != 0 && startDate > endDate) {
						callback(new Error("结束时间不能小于开始时间"));
					} else {
						callback();
					}
				}
			}
			return {
				loading: false,
				formObj: {
					flowPoolName: '', //流量池名称
					flowPoolId:'',
					nameTw: '', //流量池繁体
					nameEn: '', //流量池英文
					corpId: '', //客户id
					mccList: [], //支持国家
					cardPool: [], //选择卡池
					flowPoolTotal: '', //流量池总量
					upccHignSignId: '', //高速签约模板
					upccLowerSignId: '', //低速签约模板
					upccLimitsSignId: '', //限速签约模板
					cycleType: '', //重置周期类型
					cycleNum: '', //重置周期数
					controlLogic: '', //控制逻辑
					startTime: '', //有效日期开始时间
					endTime: '', //有效日期结束时间
					flowPoolPrice: '', //流量池价格
					flowPoolExtraPrice: '', //超量后价格
					alarmThreshold: '' ,//用量提醒阈值
					fcpList:[]
				},
				drawer: false, //卡池树标识
				cardPoolTree: [], //比例树
				cardPoolEditFlag: false, //卡池编辑弹框标识
				cardPoolEditTreeLoad: false, //编辑树加载标识
				cardPoolEditTree: [], //编辑树
				continentList: [], //国家List
				corpList: [], //客户List
				typeFlag: 'Add',
				emptyText: '未查询到任何卡池数据',
				filterPool: [], //筛选后展示集合
				filterTempPool: [], //筛选展示临时集合:取消时使用
				totalPool: [], //所有比例数据集合
				totalTempPool: [], //所有比例数据集合:取消时使用
				cpcrvList: [],
				firstLoad: false, //用于区分国家/地区是否首次加载
				filterSearchObj: {
					'cpName': '',
					'sName': '',
					'cName': ''
				}, //条件筛选对线
				localMap: new Map(),
				styles: {
					height: 'calc(100% - 55px)',
					overflow: 'auto',
					paddingBottom: '53px',
					position: 'static'
				},
				ruleAddValidate: {
					flowPoolName: [{
						required: true,
						type: 'string',
						message: '流量池名称(简中)不能为空',
					}],
					nameTw: [{
						required: true,
						type: 'string',
						message: '流量池名称(繁中)不能为空',
					}],
					nameEn: [{
						required: true,
						validator: (rule, value, cb) => {
							var regex = /^[0-9a-zA-Z\/\(\)\,\.\:\"\<\>\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;
							return regex.test(value);

						},
						message: 'Please enter the name of the flow pool (English)',
					}],
					corpId: [{
						required: true,
						type: 'string',
						message: '请选择客户',
					}],
					mccList: [{
						required: true,
						type: 'array',
						message: '支持国家/地区不能为空',
					}],
					cardPool: [{
						required: true,
						validator: (rule, value, cb) => {
							var len = this.formObj.cardPool.length;
							return len > 0;
						},
						message: '关联卡池不能为空',
					}],
					flowPoolTotal: [{
						required: true,
						message: '流量池总量不能为空',
					}, {
						validator: (rule, value, cb) => {
							var str = /^[0-9]\d*$/;
							return str.test(value);
						},
						message: '流量池总量格式错误',
					}],
					upccHignSignId: [{
						required: true,
						type: 'string',
						message: '高速签约模板不能为空',
					}],
					upccLowerSignId: [{
						required: true,
						type: 'string',
						message: '低速签约模板不能为空',
					}],
					upccLimitsSignId: [{
						required: true,
						type: 'string',
						message: '限速签约模板不能为空',
					}],
					cycleType: [{
						required: true,
						message: '请选择重置周期',
					}],
					cycleNum: [{
						required: true,
						type: 'string',
						message: '重置周期数不能为空',
					}],
					controlLogic: [{
						required: true,
						message: '请选择控制逻辑',
					}],
					startTime: [{
						required: true,
						type: 'date',
						message: '开始时间不能为空',
					}, {
						validator: validateDate,
					}],
					endTime: [{
						required: true,
						type: 'date',
						message: '结束时间不能为空',
					}, {
						validator: validateDate,
					}],
					flowPoolPrice: [{
						required: true,
						type: 'string',
						message: '流量池价格不能为空',
					}, {
						validator: (rule, value, cb) => {
							var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value);
						},
						message: '最高支持8位整数和2位小数的正数或零',
					}],
					flowPoolExtraPrice: [{
						required: true,
						type: 'string',
						message: '超量后价格不能为空',
					}, {
						validator: (rule, value, cb) => {
							var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value);
						},
						message: '最高支持8位整数和2位小数的正数或零',
					}],
					alarmThreshold: [
						{ required: true, message: '请输入用量提醒阈值', trigger: 'change',},
						{
							validator: (rule, value, cb) => {
								var str = /^[1-9]\d*$/;
								return str.test(value);
							},
							message: '请输入正整数',
						}
					],
				}
			}
		},
		mounted() {
			// 保存上一页返回数据
			localStorage.setItem("searchObj", decodeURIComponent(this.$route.query.searchObj))
			var list=JSON.parse(decodeURIComponent(this.$route.query.details))
			this.firstLoad = true;
			this.formObj.flowPoolName=list.flowPoolName
			this.formObj.flowPoolId=list.flowPoolId
			this.formObj.nameTw=list.nameTw
			this.formObj.nameEn=list.nameEn
			this.formObj.corpId=list.corpId
			this.formObj.mccList=list.supportMcc
			this.formObj.flowPoolTotal=list.flowPoolTotal
			this.formObj.upccHignSignId=list.upccHignSignId
			this.formObj.upccLowerSignId=list.upccLowerSignId
			this.formObj.upccLimitsSignId=list.upccLimitsSignId
			this.formObj.cycleType=Number(list.cycleType) 
			this.formObj.cycleNum=list.cycleNum.toString()
			this.formObj.controlLogic=Number(list.controlLogic)
			this.formObj.startTime=list.startTime
			this.formObj.endTime=list.endTime
			this.formObj.flowPoolPrice=list.flowPoolPrice.toString()
			this.formObj.flowPoolExtraPrice=list.flowPoolExtraPrice.toString()
			this.formObj.alarmThreshold=list.alarmThreshold.toString()
			this.formObj.flowpoolid=list.flowPoolId
			this.formObj.shelfstatus=Number(list.shelfStatus)
			this.getLocalList();
			this.getCorpList();
			this.firstLoadCardPool(this.formObj.mccList)
		},
		methods: {
			changestartTime: function(date) {
				// this.formObj.startTime=date
			},
			changeendTime: function(date) {
				// this.formObj.endTime=date
			},
			back: function() {
				this.$router.push({
					path: '/trafficPool',
				})
			},
			Confirm: function() {
				this.$refs["formObj"].validate((valid) => {
					if (valid) {
						this.loading = true
						this.formObj.fcpList=this.formObj.cardPool
						getPage(this.formObj).then(res => {
							if (res.code == '0000') {
								this.$Notice.success({
								  title: '操作提示',
								  desc: '操作成功'
								})
								this.loading = false
								this.$router.push({
									path: '/trafficPool',
								})
							}
						}).catch((err) => {
							console.error(err)
						}).finally(() => {
							this.loading = false
						})
					}
				})
			},
			//国家/地区选择变更
			mccListChange(e) {
				if (!this.firstLoad) {
					this.mccListTemp = '';
					this.formObj.cardPool = [];
					//清空总数据集合
					this.totalPool = [];
					this.cpcrvList = [];
					this.$refs['formObj'].validateField('cardPool');
				} else {
					this.firstLoad = false;
				}
			},
			/**
			 * *********************选择卡池*****************************
			 */
			//加载卡池信息-编辑信息初始化/查看关联卡池信息
			loadCardPoolView(countryList) {
				var id = this.formObj.flowPoolId;
				if (this.mccListTemp != JSON.stringify(countryList)) {
					this.mccListTemp = JSON.stringify(countryList);
					getcardpool({
						isGetAll:true,
						mcc : countryList,
						flowPoolId : id == undefined ? null : id
					}).then(res => {
						if (res && res.code == '0000'&& res.data.data) {
							var data = res.data;
							//加载卡池集合数据
							this.formObj.cardPool = data.data;
							//排序
							this.formObj.cardPool.sort(function(str1, str2) {
								return str1.poolName.localeCompare(str2.poolName);
							});
							//加载卡池集合数据->totalPool
							this.loadTreeData(this.formObj.cardPool);
							//筛选比例集合-筛选已填信息->过滤totalPool
							this.filterRateList("", "", "", "filled", "show");
							//临时保存暂存数据->用于取消恢复
							// this.totalTempPool = this.totalPool.concat();
							// this.filterTempPool = this.filterPool.concat();
							//构建提交比例集合(非首次加载不用构建)
							
						} else {
							throw res
						}
					}).catch((err) => {

					}).finally(() => {

					})
				} else {
					//加载卡池集合数据->totalPool
					this.loadTreeData(this.formObj.cardPool);
					//筛选比例集合-筛选已填信息->过滤totalPool
					this.filterRateList("", "", "", "filled", "show");
					//临时保存暂存数据->用于取消恢复
					// this.totalTempPool = this.totalPool.concat();
					// this.filterTempPool = this.filterPool.concat();
					//构建提交比例集合(非首次加载不用构建)
					//弹出抽屉
					// this.drawer = true;
				}
				//弹出抽屉
				this.drawer = true;
			},
			//加载卡池树数据
			loadTreeData(cpObj) {
				let pushArr = [];
				let len = cpObj.length;
				try {
					for (var i = 0; i < len; i++) {
						let index = i;
						let cp = cpObj[index];
						let obj = {
							title: cp.poolName + "-(" + cp.supplierName + ")",
							id: cp.poolId,
							poolName: cp.poolName,
							supplierName: cp.supplierName,
							expand: true,
							children: []
						};
						if (cp.regionList && cp.regionList.length > 0) {
							for (var n = 0; n < cp.regionList.length; n++) {
								let nIndex = n;
								let region = cp.regionList[nIndex];
								obj.children.push({
									expand: true,
									poolId: cp.poolId,
									poolName: cp.poolName,
									supplierName: cp.supplierName,
									countryCn: region.countryCn,
									countryTw: region.countryTw,
									countryEn: region.countryEn,
									mcc: region.mcc,
									rate: region.rate,
									render: (h, {
										root,
										node,
										data
									}) => {
										return h('div', {
											style: {
												display: 'flex',
												width: '100%',
												height: '25px',
												flexDirection: 'row',
												alignItems: 'center'
											}
										}, [
											h('Tooltip', {
												props: {
													placement: 'left',
													content: cp.regionList[nIndex].countryEn
												},
												style: {
													width: '100px',
													display: 'inline-block',
												}
											}, [
												h('div', {
													style: {
														width: '100px',
														height: '25px',
														display: 'inline-block',
														overflow: 'hidden',
														textOverflow: 'ellipsis',
														whiteSpace: 'nowrap',
														lineHeight: '30px'
													}
												}, cp.regionList[nIndex].countryEn + '：')
											]),
											h('input', {
												domProps: {
													type: 'Number',
													value: cp.regionList[nIndex].rate == undefined ? null : cp.regionList[nIndex]
														.rate,
													placeholder: '请输入分配比列(%)',
													max: 100,
													min: 0,
													disabled: true
												},
												style: {
													width: '150px',
													height: '20px',
													// float: 'left',
													textAlign: 'center',
													border: '#ccc 1px solid',
													borderRadius: '5px',
													mozBorderRadius: '5px',
													webkitBorderRadius: '5px',
													marginLeft: '8px'
												},
												on: {
													input: (e) => {
														var v = e.target.value;
														cpObj[index].regionList[nIndex].rate = v;
														obj.children[nIndex].rate = v;
													}
												}
											})
										]);
									},
								})
							}
						}
						pushArr.push(obj);
					}
					//设置总数据集合
					this.totalPool = pushArr;
				} catch (e) {
					//设置总数据集合
					this.totalPool = [];
				}
			},
			//搜索
			doCPTreeFilter() {
				this.cardPoolEditTreeLoad = true;
				var this_ = this;
				//前一次过滤数据汇总至->this.totalPool
				this.saveTreeIntoTotalPool();
				//根据过滤条件进行过滤
				this.filterRateList(this.filterSearchObj.cpName, this.filterSearchObj.sName, this.filterSearchObj.cName,
					"all",
					"edit");
				setTimeout(function() {
					this_.cardPoolEditTreeLoad = false;
				}, 500);
			},
			//卡池编辑窗口
			cardPoolEdit() {
				this.filterSearchObj = {
					'cpName': '',
					'sName': '',
					'cName': ''
				};
				//展示列表不影响实际值
				// this.totalPool = this.totalTempPool.concat();
				//筛选比例集合-筛选所有信息 -> filterPool
				this.filterRateList("", "", "", "all", "edit");
				this.drawer = false;
				this.cardPoolEditFlag = true;
			},
			//抽屉关闭
			drawerClose() {
				if (this.typeFlag != 'Info') {
					this.mccListTemp = '';
					this.formObj.cardPool = [];
					this.cpcrvList = [];
					this.$refs['formObj'].validateField('cardPool');
				}
				this.drawer = false;
			},
			//存储值 搜索/编辑保存 进行前一次过滤数据汇总
			saveTreeIntoTotalPool() {
				//filterPool --> into --> totalPool
				if (this.totalPool.length > 0) {
					//优化函数->组装数据
					//获取树值做逻辑判断
					var filterMap = new Map();
					this.filterPool.map((filterParent, index) => {
						filterParent.children.map((filterChild, index) => {
							if (filterChild.rate != null && filterChild.rate != 0) {
								//添加新的key-value
								//key:卡池id+国家/地区mcc value:比例值
								filterMap.set(filterParent.id + filterChild.mcc, filterChild.rate);
							}
						});
					});
					//判断赋值
					this.totalPool.map((totalParent, index) => {
						totalParent.children.map((totalChild, index) => {
							if (filterMap.has(totalParent.id + totalChild.mcc)) {
								totalChild.rate = filterMap.get(totalParent.id + totalChild.mcc);
							}
						});
					});
				}
			},
			//筛选比例集合
			filterRateList(cpName, sName, cName, type, operateType) {
				//cpName-卡池名称
				//sName-供应商名称
				//cName-国家/地区名称
				//type-过滤方式 all/filled
				var cardPoolList = [];
				if (this.totalPool.length > 0) {
					this.totalPool.map((cp, index) => {
						//匹配卡池名称
						var cpNameFlag = cp.poolName != null ? cp.poolName.indexOf(cpName) != -1 : false;
						//匹配供应商名称
						var sNameFlag = cp.supplierName != null ? cp.supplierName.indexOf(sName) != -1 : false;
						var parent = {
							title: cp.title,
							id: cp.id,
							poolName: cp.poolName,
							supplierName: cp.supplierName,
							expand: true,
							children: []
						};
						//满足卡池名称&供应商名称匹配
						if (cpNameFlag && sNameFlag) {
							cp.children.map((obj, index) => {
								//匹配国家/地区名称
								var cNameFlag = obj.countryEn != null ? obj.countryEn.indexOf(cName) != -1 : false;
								if (cNameFlag) {
									if ('all' == type) {
										parent.children.push(obj);
									}
									if ('filled' == type && obj.rate != null && obj.rate != "") {
										parent.children.push(obj);
									}
								}
							});
						}
						if (parent.children.length > 0) {
							cardPoolList.push(parent);
						}
					});
				}
				if (cardPoolList.length != 0) {
					if ('edit' == operateType) {
						this.cardPoolEditTree = [{
							title: '关联卡池',
							expand: true,
							children: []
						}];
						this.cardPoolEditTree[0].children = cardPoolList.concat();
						this.filterPool = cardPoolList.concat();
						this.$forceUpdate();
					}
					if ('show' == operateType) {
						this.cardPoolTree = [{
							title: '关联卡池',
							expand: true,
							children: []
						}];
						this.cardPoolTree[0].children = cardPoolList.concat();
						this.filterPool = cardPoolList.concat();
						this.$forceUpdate();
					}
				} else {
					this.cardPoolEditTree = [];
					this.cardPoolTree = [];
					this.filterPool = [];
				}
			},
			//构建提交比例集合-初始化
			loadTotalRateList() {
				var cardPoolList = [];
				this.totalPool.map((cardPool, index) => {
					cardPool.children.map((rateObj, index) => {
						if (rateObj.rate != null) {
							cardPoolList.push({
								poolId: rateObj.poolId,
								poolName: rateObj.poolName,
								mcc: rateObj.mcc,
								rate: String(rateObj.rate)
							});
						}
					});
				});
				this.cpcrvList = cardPoolList;
			},
			//初次加载
			firstLoadCardPool(countryList) {
				var id = this.formObj.flowPoolId;
				if (this.mccListTemp != JSON.stringify(countryList)) {
					this.mccListTemp = JSON.stringify(countryList);
					getcardpool({
						isGetAll:false,
						mcc : countryList,
						flowPoolId : id == undefined ? null : id
					}).then(res => {
						if (res && res.code == '0000') {
							var data = res.data;
							//加载卡池集合数据
							this.formObj.cardPool = data.data;
							//排序
							this.formObj.cardPool.sort(function(str1, str2) {
								return str1.poolName.localeCompare(str2.poolName);
							});
							//加载卡池集合数据->totalPool
							this.loadTreeData(this.formObj.cardPool);
							//筛选比例集合-筛选已填信息->过滤totalPool
							this.filterRateList("", "", "", "filled", "show");
							//构建提交比例集合-初始化->构建提交数组cpcrvList
							this.loadTotalRateList();
						} else {
							throw res
						}
					}).catch((err) => {

					}).finally(() => {

					})
				} else {
					//加载卡池集合数据->totalPool
					this.loadTreeData(this.formObj.cardPool);
					//筛选比例集合-筛选已填信息->过滤totalPool
					this.filterRateList("", "", "", "filled", "show");
					//构建提交比例集合-初始化->构建提交数组cpcrvList
					this.loadTotalRateList();
				}
			},
			//卡池提交
			toSetCardPool() {
				var cardPoolList = [];
				var poolMccList = [];
				if (this.totalPool.length > 0) {
					//获取树值做逻辑判断
					this.totalPool.map((cardPool, index) => {
						cardPool.children.map((rateObj, index) => {
							if (rateObj.rate != null && rateObj.rate != 0) {
								cardPoolList.push({
									poolId: rateObj.poolId,
									poolName: rateObj.poolName,
									mcc: rateObj.mcc,
									rate: String(rateObj.rate)
								});
								poolMccList.push(rateObj.mcc);
							}
						});
					});
				}


				//参数校验
				var reg = new RegExp("^(\\d|[0-9]\\d|100)$");
				var submitFlag = true;
				for (var i = 0; i < cardPoolList.length; i++) {
					//限制0-100整数输入
					if (!reg.test(cardPoolList[i].rate) && cardPoolList[i].rate != '') {
						this.$Notice.warning({
							title: '操作提示',
							desc: '分配比输入错误(仅支持0-100)'
						});
						submitFlag = false;
						return false;
					}
				}

				//分组
				var groups = [];
				cardPoolList.map((item, index) => {
					var mcc = item.mcc;
					if (!groups[mcc]) {
						groups[mcc] = [];
					}
					groups[mcc].push({
						key: index,
						value: Number(item.rate)
					});
				});
				//判断比例值 唯一默认100%
				for (var num in groups) {
					var group = groups[num];
					if (group.length == 1) {
						cardPoolList[group[0].key].rate = '100';
					} else {
						var total = 0;
						group.map((item, index) => {
							total = total + item.value;
						});
						if (total != 100) {
							var name = this.localMap.has(num) ? this.localMap.get(num) : '各国家';
							this.$Notice.warning({
								title: '操作提示',
								desc: name + '分配比需满足100%'
							})
							submitFlag = false;
							//跳出group循环
							return false;
						}
					}

					//跳出groups循环
					if (!submitFlag) {
						return false;
					}
				}
				//勾选国家
				var mccList = this.formObj.mccList;
				//判断所选国家是否匹配完全
				for (var j = 0; j < mccList.length; j++) {
					if (poolMccList.indexOf(mccList[j]) == -1) {
						var name = this.localMap.has(mccList[j]) ? this.localMap.get(mccList[j]) : '存在国家/地区';
						this.$Notice.warning({
							title: '操作提示',
							desc: name + '未分配比例'
						})
						submitFlag = false;
						return false;
					}
				}

				if (!submitFlag) {
					return false;
				} else {
					//提交逻辑
					this.cpcrvList = cardPoolList;
					this.$refs['formObj'].validateField('cardPool');
					this.drawer = false;
				}
			},
			//编辑确认
			cardPoolEditConfirm: function() {
				this.filterRateList("", "", "", "filled", "show");
				this.cardPoolEditFlag = false;
				this.drawer = true;
			},
			/**
			 * ********************初始加载数据******************************
			 */
			//国家/地区
			getLocalList() {
				opsearchAll().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.continentList = list;
						this.continentList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
						var localMap = new Map();
						list.map((local, index) => {
							localMap.set(local.mcc, local.countryEn);
						});
						this.localMap = localMap;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//获取渠道集合
			getCorpList() {
			  getCorpList({
				// "type": 1,
				"status": 1,
				"checkStatus": 2,
				"types":[1,3,4,7,8,9]
			  }).then(res => {
				if (res && res.code == '0000') {
				  this.corpList = res.data;
				} else {
				  throw res
				}
			  }).catch((err) => {
			
			  }).finally(() => {
			
			  })
			},
		}

	}
</script>

<style>
	.demo-drawer-footer {
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		border-top: 1px solid #e8e8e8;
		padding: 10px 16px;
		text-align: right;
		background: #fff;
	}
</style>
