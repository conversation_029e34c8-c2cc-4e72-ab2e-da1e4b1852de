<template>
	<!-- 流量池管理 -->
	<Card>
		<div class="search_head_i">
			<div class="search_box">
			  <span class="search_box_label">流量池名称:</span>
			  <Input v-model='searchObj.flowpoolname' placeholder="请输入流量池名称" clearable style="width: 200px;" />
			</div>
			<div class="search_box">
			  <span class="search_box_label">流量池ID:</span>
			  <Input v-model='searchObj.flowpoolid' placeholder="请输入流量池ID" clearable style="width: 200px;" />
			</div>
			<div class="search_box">
			  <span class="search_box_label">使用状态:</span>
			  <Select filterable v-model="searchObj.usestatus"  :clearable="true"  placeholder="请选择使用状态" style="width: 200px ;margin-right: 10px;">
			    <Option  :value="1" >正常</Option>
			    <Option  :value="2" >限速</Option>
			    <Option  :value="3" >停用</Option>
			  </Select>
			</div>
			<div class="search_box">
			  <span class="search_box_label">上架状态:</span>
			  <Select filterable v-model="searchObj.shelfstatus"  :clearable="true"  placeholder="请选择上架状态" style="width: 200px ;margin-right: 10px;">
			    <Option  :value="1" >上架</Option>
			    <Option  :value="2" >下架</Option>
			  </Select>
			</div>
			<div class="search_box">
			  <span class="search_box_label">客户名称:</span>
			  <Input v-model='searchObj.corpname' placeholder="请输入客户名称" clearable style="width: 200px;" />
			</div>
			<div class="search_box">
			  <Button  v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search()">搜索</Button>
			  <Button  v-has="'export'" style="margin: 0 2px;margin-left: 20px;" icon="ios-cloud-download-outline" type="success" :loading="downloading" @click="exportFile" >
			    导出
			  </Button>
			  <Button v-has="'add'" style="margin: 0 2px;margin-left: 20px;"  type="warning" icon="md-add" @click="addpool()">新建流量池</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'view'" type="primary" size="small" style="margin-right: 3px;" @click="Details(row)">详情</Button>
				<Button v-has="'update'" type="info" size="small" style="margin-right: 3px;" @click="Update(row)">修改</Button>
				<Button v-has="'delete'" type="error" disabled size="small" style="margin-right: 3px;" @click="Delete">删除</Button>
				<Button v-has="'copy'" type="warning" size="small" style="margin-right:3px;" @click="Copy(row)">复制</Button>
			</template>
			<template slot-scope="{ row, index }" slot="operation">
				<Button :disabled="![1,4,5].includes(+row.authStatus)" v-has="'pass'" type="success" size="small" style="margin-right: 3px;" @click="Operation(true,row.flowPoolId)">通过</Button>
				<Button :disabled="![1,4,5].includes(+row.authStatus)" v-has="'fail'" type="error" size="small" style="margin-right: 3px;" @click="Operation(false,row.flowPoolId)">不通过</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div  style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					<FormItem label="你本次导出任务ID为:">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem label="你本次导出的文件名为:">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
				</Form>
			</div>
		
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="Goto">立即前往</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		getPage,
		exportflow,
		review,
		updateflow,
		copyflow,
		getcardpool
	} from "@/api/package/flowpool";
	export default{
		data(){
			return{
				total: 0,
				currentPage: 1,
				page: 0,
				loading:false,
				searchloading:false,
				downloading:false,
				exportModal:false,//导出弹框标识
				taskId:'',
				taskName:'',
				form:{},
				searchObj:{
					flowpoolname:'',
					flowpoolid:'',
					usestatus:'',
					shelfstatus:'',
					corpname:'',
				},
				columns:[{
					title: "流量池名称",
					key: 'flowPoolName',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				},{
					title: "流量池ID",
					key: 'flowPoolId',
					minWidth: 200,
					align: 'center'
				},{
					title: "客户名称",
					key: 'corpName',
					minWidth: 120,
					align: 'center'
				},{
					title: "使用状态",
					key: 'useStatus',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  const text = row.useStatus==='1' ? "正常":row.useStatus==='2' ? "限速"
					  :row.useStatus==='3' ? "停用":"";
					  return h('label', text);
					}
				},{
					title: "上架状态",
					key: 'shelfStatus',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  const text = row.shelfStatus==='1' ? "上架":row.shelfStatus==='2' ? "下架":"";
					  return h('label', text);
					}
				},{
					title: "总流量(GB)",
					key: 'flowPoolTotal',
					minWidth: 120,
					align: 'center'
				},{
					title: "操作",
					slot: 'action',
					minWidth: 250,
					align: 'center'
				},{
					title: "审批状态",
					key: 'authStatus',
					minWidth: 120,
					align: 'center',
					render: (h, params) => {
					  const row = params.row;
					  const color = row.authStatus == '1'?'#2b85e4':row.authStatus == '2' ? '#19be6b'
					  : row.authStatus == '3' ? '#ff0000' : row.authStatus == '4' ? '#ffa554'
					  : row.authStatus == '5' ? '#ff0000' :'';
					  const text = row.authStatus==='1' ? "新建待审批":row.authStatus==='2' ? "通过"
					  :row.authStatus==='3' ? "新建审批不通过":row.authStatus==='4' ? "修改待审批"
					  :row.authStatus==='5' ? "删除待审批":"";
					 return h('label', {
					   style: {
					     color: color
					   }
					 }, text)
					}
				},{
					title: "审批操作",
					slot: 'operation',
					minWidth: 200,
					align: 'center'
				},
				],
				data:[]
			}
		},
		mounted() {
			//缓存数据
			let searchObj = JSON.parse(localStorage.getItem("searchObj")) === null ? '' : JSON.parse(localStorage.getItem(
				"searchObj"))
			if (searchObj) {
				this.searchObj.flowpoolname = searchObj.flowpoolname === undefined ? "" : searchObj.flowpoolname
				this.searchObj.flowpoolid = searchObj.flowpoolid === undefined ? "" : searchObj.flowpoolid
				this.searchObj.usestatus = searchObj.usestatus === undefined ? "" : searchObj.usestatus
				this.searchObj.shelfstatus = searchObj.shelfstatus === undefined ? "" : searchObj.shelfstatus
				this.searchObj.corpname = searchObj.corpname === undefined ? "" : searchObj.corpname
			}
			this.goPageFirst(1)
			//清除缓存
			localStorage.removeItem("searchObj")
		},
		methods:{
			goPageFirst:function(page){
				this.loading = true
				var _this = this
				getPage({
					corpName:this.searchObj.corpname ,
					flowPoolId:this.searchObj.flowpoolid,
					flowPoolName:this.searchObj.flowpoolname ,
					pageNum: page,
					pageSize: 10,
					shelfStatus:this.searchObj.shelfstatus,
					useStatus:this.searchObj.usestatus
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						this.data = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			goPage:function(page){
				this.goPageFirst(page)
			},
			search:function(){
				this.searchloading = true
				this.goPageFirst(1)
			},
			exportFile:function(){
				this.downloading = true
				exportflow({
					corpName:this.searchObj.corpname ,
					flowPoolId:this.searchObj.flowpoolid,
					flowPoolName:this.searchObj.flowpoolname ,
					pageNum: -1,
					pageSize: -1,
					shelfStatus:this.searchObj.shelfstatus,
					useStatus:this.searchObj.usestatus,
					userId:this.$store.state.user.userId
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.taskId
					this.taskName = res.data.taskName
					this.downloading = false
				}).catch(() => this.downloading = false)
			},
			addpool:function(){
				this.$router.push({
				  path:'/addPool',
				  query: {
				    searchObj: encodeURIComponent(JSON.stringify(this.searchObj)),
				  }
				})
			},
			Details:function(row){
				this.$router.push({
				  path:'/detailsPool',
				  query: {
				    searchObj: encodeURIComponent(JSON.stringify(this.searchObj)),
					details:encodeURIComponent(JSON.stringify(row))
				  }
				})
			},
			Update:function(row){
				this.$router.push({
				  path:'/editPool',
				  query: {
				    searchObj: encodeURIComponent(JSON.stringify(this.searchObj)),
					details:encodeURIComponent(JSON.stringify(row))
				  }
				})
			},
			Delete:function(){
				
			},
			Copy:function(row){
				this.$router.push({
				  path:'/copyPool',
				  query: {
				    searchObj: encodeURIComponent(JSON.stringify(this.searchObj)),
					details:encodeURIComponent(JSON.stringify(row))
				  }
				})
			},
			cancelModal:function(){
				this.exportModal=false
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportModal = false
			},
			Operation:function(checkStatus,flowPoolId){
				let title=checkStatus===true ?'确认审批通过?':'确认审批不通过?'
				this.$Modal.confirm({
					title: title,
					onOk: () => {
						review({
							authStatus: checkStatus,
							flowPoolId:flowPoolId
						}).then(res => {
							if (res && res.code == '0000') {
								this.goPageFirst(1)
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						})
					}
				});
			}
			
			
		}
	}
</script>

<style>
	.search_head_i {
	  width: 100%;
	  display: flex;
	  justify-content: flex-start;
	  align-items: center;
	  flex-wrap: wrap;
	}
	.search_box {
	  width: 340px;
	  padding: 0 5px;
	  display: flex;
	  justify-content: flex-start;
	  align-items: center;
	  flex-direction: row;
	  margin-bottom: 20px;
	}
	.search_box_label {
	  font-weight: bold;
	  text-align: center;
	  margin: 0 10px;
	  width: 105px;
	}
</style>
