<template>
	<!-- 加油包管理 -->
	<Card style="width: 100%;padding: 16px;">
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">加油包名称:</span>
				<Input v-model='searchObj.gaspackname' placeholder="请输入加油包名称" clearable style="width: 200px;" />
			</div>
			<div class="search_box">
				<span class="search_box_label">加油包ID:</span>
				<Input v-model='searchObj.gaspacknameid' placeholder="请输入加油包ID" clearable style="width: 200px;" />
			</div>
			<div class="search_box">
				<span class="search_box_label">是否允许订购:</span>
				<Select filterable v-model="searchObj.allowsub" :clearable="true" placeholder="请选择使用状态" style="width: 200px ;margin-right: 10px;">
					<Option :value="1">是</Option>
					<Option :value="2">否</Option>
				</Select>
			</div>
			<div class="search_box">
				<Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search()">搜索</Button>
				<Button v-has="'add'" style="margin: 0 2px;margin-left: 20px;" type="success" icon="md-add" @click="addfuelpack()">新建加油包</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="showpackage">
				<Button v-has="'view'"  type="primary" size="small" style="margin-right: 3px;" @click="showpackage(row)">点击查看</Button>
			</template>
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'update'"  :disabled="[5].includes(+row.authStatus)" type="info" size="small" style="margin-right: 3px;" @click="Update(row)">编辑</Button>
				<Button v-has="'delete'"  :disabled="[4,5].includes(+row.authStatus)" type="error" size="small" style="margin-right: 3px;" @click="Delete(row.id)">删除</Button>
				<Button v-has="'copy'"  type="warning" size="small" style="margin-right:3px;" @click="Copy(row)">复制</Button>
			</template>
			<template slot-scope="{ row, index }" slot="authaction">
				<Button v-has="'pass'" :disabled="![1,4,5].includes(+row.authStatus)" type="success" size="small" style="margin-right: 3px;"
				 @click="Operation(true,row.id)">通过</Button>
				<Button v-has="'fail'" :disabled="![1,4,5].includes(+row.authStatus)" type="error" size="small" style="margin-right: 3px;"
				 @click="Operation(false,row.id)">不通过</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 查看关联套餐 -->
		<Modal title="查看关联套餐" v-model="showModal" :mask-closable="true" @on-cancel="cancelModal">
			<Table :columns="Unitedcolumns" :data="Uniteddata" style="width:100%;" :loading="Unitedloading">
			</Table>
			<!-- 分页 -->
			<div style="margin-top:15px">
				<Page :total="Unitedtotal" :current.sync="UnitedcurrentPage" show-total show-elevator @on-change="UnitedgoPage" />
			</div>
		
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">返回</Button>
			</div>
		</Modal>
		<!-- 新建/修改/复制加油 -->
		<Modal :title="title" v-model="fuelPackModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form ref="fuelPackObj" :model="fuelPackObj"  :rules="rule" 
				label-position="left" :label-width="150" 
				style=" align-items: center;justify-content:center;">
					<FormItem label="加油包名称(简中)" prop="nameCn">
						<Input v-model="fuelPackObj.nameCn"  placeholder="请输入加油包名称(简中)" style="width: 200px;"></Input>
					</FormItem>
					<FormItem label="加油包名称(繁中)" prop="nameTw">
						<Input v-model="fuelPackObj.nameTw"  placeholder="请输入加油包名称(繁中)" style="width: 200px;"></Input>
					</FormItem>
					<FormItem label="加油包名称(英文)" prop="nameEn">
						<Input v-model="fuelPackObj.nameEn"  placeholder="请输入加油包名称(英文)" style="width: 200px;"></Input>
					</FormItem>
					<FormItem label="加油包单价(人民币)" prop="cny">
						<Input v-model="fuelPackObj.cny"  placeholder="请输入加油包名称(人民币)" style="width: 200px;"></Input>
					</FormItem>
					<FormItem label="加油包单价(港币)" prop="hkd">
						<Input v-model="fuelPackObj.hkd"  placeholder="请输入加油包名称(港币)" style="width: 200px;"></Input>
					</FormItem>
					<FormItem label="加油包单价(美元)" prop="usd">
						<Input v-model="fuelPackObj.usd"  placeholder="请输入加油包名称(美元)" style="width: 200px;"></Input>
					</FormItem>
					<FormItem label="加油包流量单位" prop="flowUnit">
						<Select filterable v-model="fuelPackObj.flowUnit" :clearable="true" placeholder="请选择使用状态" style="margin-right: 10px;width: 200px;">
							<Option value="2">GB</Option>
							<Option value="1">MB</Option>
						</Select>
					</FormItem>
					<FormItem label="加油包流量值" prop="flowValue">
						<Input v-model="fuelPackObj.flowValue"  placeholder="请输入加油包流量值" style="width: 200px;"></Input>
					</FormItem>
					<FormItem label="是否允许订购" prop="allowSub">
						<Select filterable v-model="fuelPackObj.allowSub" :clearable="true" placeholder="请选择使用状态" style="margin-right: 10px;width: 200px;">
							<Option value="1">是</Option>
							<Option value="2">否</Option>
						</Select>
					</FormItem>
				</Form>
			</div>
		
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" :loading="submitloading" @click="submit">确定</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		getPage,
		addfuelPack,
		updatefuelPack,
		deletefuelPack,
		auditfuelPack,
		selectRefuekPackage
	} from "@/api/package/fuelPack";
	export default {
		data() {
			return {
				searchObj: {
					gaspackname: '',
					gaspacknameid: '',
					allowsub: ''
				},
				fuelPackObj:{
					nameCn:'',
					nameTw:'',
					nameEn:'',
					cny:'',
					hkd:'',
					usd:'',
					flowUnit:'',
					flowValue:'',
					allowSub:''
				},
				refuelId :'',
				total: 0,
				currentPage: 1,
				page: 0,
				Unitedtotal: 0,
				UnitedcurrentPage: 1,
				Unitedpage: 0,
				showModal:false,
				fuelPackModal:false,
				loading: false,
				Unitedloading:false,
				searchloading: false,
				submitloading:false,
				title:'',
				columns: [{
						title: "加油包ID",
						key: 'id',
						minWidth: 200,
						align: 'center',
						tooltip: true,
					},
					{
					title: "加油包名称(简体中文)",
					key: 'nameCn',
					minWidth: 200,
					align: 'center',
					tooltip: true,
				},{
					title: "加油包名称(繁体中文)",
					key: 'nameTw',
					minWidth: 200,
					align: 'center',
					tooltip: true,
				},{
					title: "加油包名称(英文)",
					key: 'nameEn',
					minWidth: 200,
					align: 'center',
					tooltip: true,
				},{
					title: "加油包价格(人民币)",
					key: 'cny',
					minWidth: 200,
					align: 'center',
					tooltip: true,
				},{
					title: "加油包价格(港币)",
					key: 'hkd',
					minWidth: 200,
					align: 'center',
					tooltip: true,
				},{
					title: "加油包价格(美元)",
					key: 'usd',
					minWidth: 200,
					align: 'center',
					tooltip: true,
				},{
					title: "加油包流量单位",
					key: 'flowUnit',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
					  const row = params.row;
					  const text = row.flowUnit==='1' ? "MB":row.flowUnit==='2' ? "GB":"";
					  return h('label', text);
					}
				},{
					title: "加油包流量值",
					key: 'flowValue',
					minWidth: 140,
					align: 'center',
					tooltip: true,
				},{
					title: "是否允许订购",
					key: 'allowSub',
					minWidth: 140,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
					  const row = params.row;
					  const text = row.allowSub==='1' ? "是":row.allowSub==='2' ? "否":"";
					  return h('label', text);
					}
				},{
					title: "关联套餐",
					slot: 'showpackage',
					minWidth: 120,
					align: 'center',
					tooltip: true,
					fixed: 'right',
				},{
					title: "操作",
					slot: 'action',
					minWidth: 200,
					align: 'center',
					tooltip: true,
					fixed: 'right',
				},{
					title: "审批状态",
					key: 'authStatus',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					fixed: 'right',
					render: (h, params) => {
					  const row = params.row;
					  const color = row.authStatus == '1'?'#2b85e4':row.authStatus == '2' ? '#19be6b'
					  : row.authStatus == '3' ? '#ff0000' : row.authStatus == '4' ? '#ffa554'
					  : row.authStatus == '5' ? '#ff0000' :'';
					  const text = row.authStatus==='1' ? "新建待审批"
					  :row.authStatus==='2' ? "通过"
					  :row.authStatus==='3' ? "新建审批不通过"
					  :row.authStatus==='4' ? "修改待审批"
					  :row.authStatus==='5' ? "删除待审批":"";
					  return h('label', {
					    style: {
					      color: color
					    }
					  }, text)
					  
					}
				},{
					title: "审批操作",
					slot: 'authaction',
					minWidth: 200,
					align: 'center',
					tooltip: true,
					fixed: 'right',
				},
				],
				Unitedcolumns:[{
					title: "套餐名称",
					key: 'nameCn',
					minWidth: 200,
					align: 'center',
					tooltip: true,
				},{
					title: "套餐ID",
					key: 'id',
					minWidth: 200,
					align: 'center',
					tooltip: true,
				}],
				Uniteddata:[],
				data: [],
				rule:{
					nameCn: [{
						required: true,
						type: 'string',
						message: '加油包名称(简中)不能为空',
					}],
					nameTw: [{
						required: true,
						type: 'string',
						message: '加油包名称(繁中)不能为空',
					}],
					nameEn: [{
						required: true,
						type: 'string',
						message: '加油包名称(英文)不能为空',
					},{
						validator: (rule, value, cb) => {
							var regex = /^[0-9a-zA-Z\/\(\)\,\.\:\"\<\>\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;
							return regex.test(value) || value == '';
						},
						message: 'Please enter English',
					}
					],
					cny: [{
						required: true,
						type: 'string',
						message: '加油包单价(人民币)不能为空',
					},{
						validator: (rule, value, cb) => {
							var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value);
						},
						message: '最高支持8位整数和2位小数正数或零',
					}
					],
					hkd: [{
						required: true,
						type: 'string',
						message: '加油包单价(港币)不能为空',
					},{
						validator: (rule, value, cb) => {
							var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value);
						},
						message: '最高支持8位整数和2位小数正数或零',
					}
					],
					usd: [{
						required: true,
						type: 'string',
						message: '加油包单价(美元)不能为空',
					},{
						validator: (rule, value, cb) => {
							var str = /^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;
							return str.test(value);
						},
						message: '最高支持8位整数和2位小数正数或零',
					}
					],
					flowUnit: [{
						required: true,
						message: '请选择加油包流量单位',
					}],
					flowValue: [{
						required: true,
						type: 'string',
						message: '加油包流量值不能为空',
					},{
						validator: (rule, value, cb) => {
							var str = /^[1-9]\d*$/;
							return str.test(value);
						},
						message: '请输入正整数',
					}
					],
					allowSub: [{
						required: true,
						message: '请选择是否允许订购',
					}],
				}

			}
		},
		mounted() {
			this.goPageFirst(1)
		},
		methods: {
			goPageFirst:function(page){
				this.loading = true
				var _this = this
				getPage({
					pageNo : page,
					pageSize : 10,
					allowSub:this.searchObj.allowsub,
					id:this.searchObj.gaspacknameid,
					nameCn:this.searchObj.gaspackname
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						let List = []
						// 循环遍历data
						res.data.records.map((value, index) => {
							if (value.authObj) {
								List.push(value.authObj)
							} else {
								List.push(value)
							}
						})
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.data.total
						this.data = List
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			goPage:function(page){
				this.goPageFirst(page)
			},
			UnitedgoPageFirst:function(page){
				this.Unitedloading = true
				var _this = this
				selectRefuekPackage({
					pageNo : page,
					pageSize : 10,
					refuelId:this.refuelId
				}).then(res => {
					if (res.code == '0000') {
						_this.Unitedloading = false
						this.Unitedpage = page
						this.UnitedcurrentPage = page
						this.Unitedtotal = res.count
						this.Uniteddata = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.Unitedloading = false
				})
			},
			UnitedgoPage:function(page){
				this.UnitedgoPageFirst(page)
			},
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			cancelModal:function(){
				this.showModal=false
				this.fuelPackModal=false
				this.$refs['fuelPackObj'].resetFields()
			},
			addfuelpack: function() {
				this.title="新建加油包"
				this.fuelPackModal=true
			},
			showpackage:function(row){
				this.showModal=true
				this.refuelId=row.id
				this.UnitedgoPageFirst(1)
			},
			Update:function(row){
				this.title="修改加油包"
				this.fuelPackModal=true
				this.fuelPackObj.id=Object.assign({}, row).id
				this.fuelPackObj.nameCn=Object.assign({}, row).nameCn
				this.fuelPackObj.nameTw=Object.assign({}, row).nameTw
				this.fuelPackObj.nameEn=Object.assign({}, row).nameEn
				this.fuelPackObj.cny=Object.assign({}, row).cny.toString()
				this.fuelPackObj.hkd=Object.assign({}, row).hkd.toString()
				this.fuelPackObj.usd=Object.assign({}, row).usd.toString()
				this.fuelPackObj.flowValue=Object.assign({}, row).flowValue.toString()
				this.fuelPackObj.flowUnit=Object.assign({}, row).flowUnit
				this.fuelPackObj.allowSub=Object.assign({}, row).allowSub
			},
			Delete:function(id){
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						deletefuelPack({
							id: id
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
				
						})
					}
				});
			},
			Copy:function(row){
				this.title="复制加油包"
				this.fuelPackModal=true
				this.fuelPackObj.id=Object.assign({}, row).id
				this.fuelPackObj.nameCn=Object.assign({}, row).nameCn
				this.fuelPackObj.nameTw=Object.assign({}, row).nameTw
				this.fuelPackObj.nameEn=Object.assign({}, row).nameEn
				this.fuelPackObj.cny=Object.assign({}, row).cny.toString()
				this.fuelPackObj.hkd=Object.assign({}, row).hkd.toString()
				this.fuelPackObj.usd=Object.assign({}, row).usd.toString()
				this.fuelPackObj.flowValue=Object.assign({}, row).flowValue.toString()
				this.fuelPackObj.flowUnit=Object.assign({}, row).flowUnit
				this.fuelPackObj.allowSub=Object.assign({}, row).allowSub
			},
			Operation:function(checkStatus,id){
				let title=checkStatus===true ?'确认审批通过?':'确认审批不通过?'
				this.$Modal.confirm({
					title: title,
					onOk: () => {
						auditfuelPack({
							authStatus: checkStatus,
							id:id
						}).then(res => {
							if (res && res.code == '0000') {
								this.goPageFirst(1)
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						})
					}
				});
			},
			//提交
			submit:function(){
				this.$refs["fuelPackObj"].validate((valid) => {
					if (valid) {
						this.submitloading=true
						if(this.title==="新建加油包" ||this.title==="复制加油包"){
							addfuelPack(this.fuelPackObj).then(res => {
								if (res.code == '0000') {
									this.$Notice.success({
										title: '操作提示',
										desc: '操作成功'
									})
									this.fuelPackModal=false
									this.$refs['fuelPackObj'].resetFields()
									this.goPageFirst(1)
								}
							}).catch((err) => {
								console.error(err)
							}).finally(() => {
								this.submitloading=false
							})
						}else if(this.title==="修改加油包"){
							updatefuelPack(this.fuelPackObj).then(res => {
								if (res.code == '0000') {
									this.$Notice.success({
										title: '操作提示',
										desc: '操作成功'
									})
									this.fuelPackModal=false
									this.$refs['fuelPackObj'].resetFields()
									this.goPageFirst(1)
								}
							}).catch((err) => {
								console.error(err)
							}).finally(() => {
								this.submitloading=false
							})
						}
					}
				})
				
			}
			
		}
	}
</script>

<style>
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		width: 340px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		margin: 0 10px;
		width: 105px;
	}
</style>
