<template>
	<Card style="width: 100%;padding: 16px;">
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">套餐名称</span>
				<Input v-model='searchObj.packageName' placeholder="请输入套餐名称" clearable style="width: 200px;" />
			</div>
			<div class="search_box">
				<span class="search_box_label">套餐ID</span>
				<Input v-model='searchObj.packageId' placeholder="请输入套餐ID" clearable style="width: 200px;" />
			</div>
			<div class="search_box">
				<span class="search_box_label">国家/地区</span>
				<Select v-model="searchObj.country" filterable style="width: 200px;" placeholder="请选择国家/地区"
					:clearable="true">
					<Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
				</Select>
			</div>
			<div class="search_box">
				<span class="search_box_label">流量限制类型</span>
				<Select v-model="searchObj.flowLimitType" filterable style="width: 200px;" placeholder="请选择流量限制类型"
					:clearable="true">
					<Option v-for="(item,index) in flowLimitTypeList" :value="item.value" :key="index">{{ item.label }}
					</Option>
				</Select>
			</div>
			<div class="search_box">
				<span class="search_box_label">套餐状态</span>
				<Select v-model="searchObj.status" filterable style="width: 200px;" placeholder="请选择套餐状态"
					:clearable="true">
					<Option v-for="(item,index) in statusList" :value="item.value" :key="index">{{ item.label }}
					</Option>
				</Select>
			</div>
			<div class="search_box">
				<span class="search_box_label_flag">是否终端厂商选择框</span>
				<Select v-model="searchObj.isTerminal" filterable style="width: 200px;" placeholder="是否终端厂商选择框"
					:clearable="true">
					<Option v-for="(item,index) in isTerminalList" :value="item.value" :key="index">{{ item.label }}
					</Option>
				</Select>
			</div>
			<div class="search_box">
				<span class="search_box_label">卡池ID</span>
				<Input v-model='searchObj.poolId' placeholder="请输入卡池ID" clearable style="width: 200px;" />
			</div>
			<div class="search_box">
				<span class="search_box_label">资源供应商</span>
				<Select v-model="searchObj.supplierId" filterable style="width: 200px;" placeholder="请选择资源供应商"
					:clearable="true">
					<Option v-for="(item,index) in supplierList" :value="item.supplierId" :key="index">{{ item.supplierName }}
					</Option>
				</Select>
			</div>
			<!-- "style="margin-top: 30px;display: flex;justify-content: flex-start;align-items: center;" -->
			<div class="search_box" style="flex-wrap: wrap;width: 100%;">
				<Button style="margin: 0 4px" type="primary" v-preventReClick @click="searchPackage"
					:loading="tableLoading">
					<Icon type="ios-search" />&nbsp;搜索
				</Button>
				<Button style="margin: 0 4px" type="info" @click="packageAdd" v-has="'add'">
					<div style="display: flex;align-items: center;">
						<Icon type="md-add" />&nbsp;新增
					</div>
				</Button>
				<Button style="margin: 0 4px" type="success" @click="packageBatchEdit" v-has="'batchUpdate'">
					<div style="display: flex;align-items: center;">
						<Icon type="md-create" />&nbsp;批量编辑
					</div>
				</Button>
				<Button style="margin: 0 4px" type="error" @click="deleteList" v-has="'batchDelete'">
					<div style="display: flex;align-items: center;">
						<Icon type="ios-trash" />&nbsp;批量删除
					</div>
				</Button>
				<Button style="margin: 0 4px" type="error" @click="allDeleteList" v-has="'batch_delete'">
					<div style="display: flex;align-items: center;">
						<Icon type="ios-trash" />&nbsp;批量删除套餐覆盖国家
					</div>
				</Button>
				<Button style="margin: 0 4px" type="success" @click="allPackageBatchEdit" v-has="'batch_updata'">
					<div style="display: flex;align-items: center;">
						<Icon type="md-create" />&nbsp;批量修改套餐覆盖国家
					</div>
				</Button>
				<Button style="margin: 0 4px" type="warning" @click="queryExport" v-has="'export'" :loading="downloading">
					<div style="display: flex;align-items: center;">
						<Icon type="ios-cloud-download-outline" />&nbsp;导出
					</div>
				</Button>
				<Button style="margin: 0 4px" type="info" @click="countryExport"
				 v-has="'country_export'" :loading="countryloading" icon="ios-cloud-download-outline">
					套餐覆盖国家导出
				</Button>
				<Button style="margin: 0 4px" type="primary" @click="goCardPoolSwitch"
				 icon="md-swap">
					一键切换卡池
				</Button>
				 <!-- v-has="'cardpool_switch'" -->
			</div>
		</div>
		<div>
			<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="tableLoading"
				@on-selection-change="handleRowChange" @on-select-cancel="cancelPackage" @on-select-all-cancel="cancelPackageAll">
				<template slot-scope="{ row, index }" slot="action">
					<Button type="primary" size="small" style="margin-right: 5px" v-has="'view'"
						@click="packageCommon(row,'Info')">详情</Button>
					<Button type="success" size="small" style="margin-right: 5px" v-has="'update'"
						@click="packageCommon(row,'Update')">编辑</Button>
					<Button type="warning" size="small" style="margin-right: 5px" v-has="'copy'"
						@click="packageCommon(row,'Copy')">复制</Button>
					<Button type="error" v-if="row.auditStatus==='4'" disabled size="small" v-has="'delete'" @click="packageDel(row.id)">删除</Button>
					<Button type="error" v-else size="small" v-has="'delete'" @click="packageDel(row.id)">删除</Button>
				</template>
				<template slot-scope="{ row, index }" slot="approval">
					<div v-if="[1,4,5].includes(+row.auditStatus)">
						<Button type="primary" size="small" style="margin-right: 5px" v-has="'check'"
							@click="examine(2,row.id)">通过</Button>
						<Button type="error" size="small" v-has="'check'" @click="examine(3,row.id)">不通过</Button>
					</div>
					<div v-else>
						<Button type="primary" size="small" style="margin-right: 5px" v-has="'check'"
							disabled>通过</Button>
						<Button type="error" size="small" v-has="'check'" disabled>不通过</Button>
					</div>
				</template>
			</Table>
			<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator
				@on-change="loadByPage" style="margin: 15px 0;" />
		</div>
		<!-- 弹出框-->
		<Modal title="套餐批量编辑" v-model="batchEditFlag" :footer-hide="true" :mask-closable="false" width="550px" @on-cancel="cancelModal">
			<div style="padding: 0 16px;">
				<Form ref="batchEditObj" :model="batchEditObj" :label-width="140" :rules="ruleBatchEditValidate">
					<div v-for="(item, index) in batchEditObj.packageConsumptions" :key="index" style="margin-bottom: 30px;"> <!-- Outer wrapper for each group -->
            <!-- Row 1: Consumption + Unit -->
            <div style="display: flex; align-items: center; margin-bottom: 25px;">
                <FormItem label="用量值" :prop="'packageConsumptions.' + index + '.consumption'"
                          :rules="consumptionRules(index)" style="margin-bottom: 0;">
                    <Input v-model="item.consumption" placeholder="请输入用量值" clearable style="width: 200px;"></Input>
                </FormItem>
                <FormItem label="" :prop="'packageConsumptions.' + index + '.unit'"
                          :rules="unitRules(index)" :label-width="0" style="margin-bottom: 0;">
                    <Select v-model="item.unit" filterable style="width: 85px;" :clearable="false">
                        <Option v-for="unitItem in unitList" :value="unitItem.value" :key="unitItem.value">{{ unitItem.label }}</Option>
                    </Select>
                </FormItem>
            </div>
            <!-- Row 2: Template + Delete Button -->
            <div style="display: flex; align-items: center;">
                <FormItem label="选择模板" :prop="'packageConsumptions.' + index + '.upccTemplateId'" style="margin-bottom: 0;">
                    <Select filterable v-model="item.upccTemplateId" placeholder="下拉选择" clearable style="width: 295px">
                        <Option :title="i.templateDesc" v-for="i in TemplateList" :value="i.templateId" :key="i.templateId">{{i.templateName.length > 25 ? i.templateName.substring(0,25) + "…" : i.templateName}}</Option>
                    </Select>
                </FormItem>
                <Button type="error" size="small" @click="removeConsumption(index)" style="margin-left: 10px;">删除</Button>
            </div>
            <!-- Separator -->
            <Divider v-if="index < batchEditObj.packageConsumptions.length - 1" style="margin: 30px 0;" />
					</div>
					<FormItem style="margin-top: 15px; display: flex; justify-content: flex-end;">
						<Button  type="primary" size="small" @click="addConsumption()">添加</Button>
					</FormItem>
					<FormItem label="套餐购买有效期(天)" prop="effectiveDay">
						<Input v-model="batchEditObj.effectiveDay" :maxlength="11" placeholder="请输入套餐购买有效期(天)"></Input>
					</FormItem>
					<FormItem label="套餐描述(简体中文)" prop="descCn">
						<Input v-model="batchEditObj.descCn" maxlength="4000" type="textarea" :rows="3"
							placeholder="请输入套餐描述(简中)"></Input>
					</FormItem>
					<FormItem label="套餐描述(繁体中文)" prop="descTw">
						<Input v-model="batchEditObj.descTw" maxlength="4000" type="textarea" :rows="3"
							placeholder="请输入套餐描述(繁中)"></Input>
					</FormItem>
					<FormItem label="套餐描述(英文)" prop="descEn">
						<Input v-model="batchEditObj.descEn" maxlength="4000" type="textarea" :rows="3"
							placeholder="Please enter package description (EN)"></Input>
					</FormItem>
					<FormItem label="关联加油包" >
						<Button  type="dashed" class="inputSty" long @click="RefuelPackageList">加油包列表</Button>
					</FormItem>
				</Form>
				<div style="text-align: center;">
					<Button type="primary" @click="submit" v-has="'batchUpdate'" v-preventReClick
						:loading="submitFlag">提交</Button>
					<Button style="margin-left: 8px" @click="reset">重置</Button>
				</div>
			</div>
		</Modal>

		<Modal title="批量删除套餐覆盖国家" v-model="deleteflag" :footer-hide="true" :mask-closable="false" width="900px" @on-cancel="cancelModal">
			<div style="padding: 0 16px;">
				<Form ref="formobj" :model="formobj" :rules="ruleobj">
					<FormItem label="上传套餐及国家列表" style="font-size: 14px;font-weight: bold;" prop="file">
						<Upload type="drag" v-model="formobj.file" :action="uploadUrl" :on-success="fileSuccess"
							:on-error="handleError" :before-upload="handleBeforeUpload" :on-progress="fileUploading"
							style="width: 500px; margin-top: 50px;margin-left: 50px;">
							<div style="padding: 20px 0">
								<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
								<p>点击或拖拽文件上传</p>
							</div>
						</Upload>
						<div style="width: 500px;margin-left: 50px;">
							<Button type="primary" :loading="downloading" icon="ios-download"
								@click="downloadFile">下载模板文件</Button>
						</div>
						<ul class="ivu-upload-list" v-if="file" style="width: 500px;margin-left: 50px;">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />{{file.name}}
								</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" @click="removeFile"></i>
							</li>
						</ul>
						<div style="width: 100%; display: flex;margin-left: 50px;margin-top: 100px;">
							<Button @click="cancelModal">返回</Button>&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="primary" :loading="importLoading" @click="delconfirmbatch">确定</Button>
						</div>
					</FormItem>
				</Form>
				<H1>历史任务查看</H1>
				<Table :columns="columnsTask" :data="taskdata" style="width:100%; margin-top: 20px;" :loading="loading">
					<template slot-scope="{ row, index }" slot="originFile">
						<Button v-if="row.originFile" type="primary" @click="exportfile(row,1)">点击下载</Button>
					</template>
					<template slot-scope="{ row, index }" slot="successFile">
						<Button v-if="row.successFile" type="success" @click="exportfile(row,2)">点击下载</Button>
					</template>
					<template slot-scope="{ row, index }" slot="failFile" >
						<Button v-if="row.failFile" type="error" @click="exportfile(row,3)">点击下载</Button>
					</template>
					<template slot-scope="{ row, index }" slot="approval">
						<div v-if="[1,4,5].includes(+row.authStatus)">
							<Button type="primary" size="small" style="margin-right: 5px" v-has="'check'"
								@click="delExamine(true,row.id)">通过</Button>
							<Button type="error" size="small" v-has="'check'" @click="delExamine(false,row.id)">不通过</Button>
						</div>
						<div v-else>
							<Button type="primary" size="small" style="margin-right: 5px" v-has="'check'"
								disabled>通过</Button>
							<Button type="error" size="small" v-has="'check'" disabled>不通过</Button>
						</div>
					</template>
				</Table>

				<!-- 分页 -->
				<div style="margin-top: 15px;">
					<Page :total="totalTask" :page-size="pageSize" :current.sync="pageTask" show-total show-elevator
						@on-change="TaskgoPage" style="margin: 15px 0;" />
				</div>
			</div>
		</Modal>
		<!-- 模板文件table -->
		<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>

		<Modal title="批量修改套餐覆盖国家" v-model="updateflag" :footer-hide="true" :mask-closable="false" width="900px" @on-cancel="cancelModal" >
			<div style="padding: 0 16px;">
				<Form ref="updateFormobj" :model="updateFormobj" :rules="updateruleobj">
					<FormItem label="上传套餐及国家列表" style="font-size: 14px;font-weight: bold;" prop="updateFile">
						<Upload type="drag" v-model="updateFormobj.updateFile" :action="updateuploadUrl" :on-success="updatefileSuccess"
							:on-error="updatehandleError" :before-upload="updatehandleBeforeUpload" :on-progress="updatefileUploading"
							style="width: 500px; margin-top: 50px;margin-left: 50px;">
							<div style="padding: 20px 0">
								<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
								<p>点击或拖拽文件上传</p>
							</div>
						</Upload>
						<div style="width: 500px;margin-left: 50px;">
							<Button type="primary" :loading="downloading" icon="ios-download"
								@click="updateDownloadFile">下载模板文件</Button>
						</div>
						<ul class="ivu-upload-list" v-if="updateFile" style="width: 500px;margin-left: 50px;">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />{{updateFile.name}}
								</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" @click="updateRemoveFile"></i>
							</li>
						</ul>
						<div style="width: 100%; display: flex;margin-left: 50px;margin-top: 100px;">
							<Button @click="cancelModal">返回</Button>&nbsp;&nbsp;&nbsp;&nbsp;
							<Button type="primary" :loading="importLoading" @click="updateconfirmbatch">确定</Button>
						</div>
					</FormItem>
				</Form>
				<H1>历史任务查看</H1>
				<Table :columns="updateColumnsTask" :data="updateTaskdata" style="width:100%; margin-to: 20px;" :loading="loading" >
					<template slot-scope="{ row, index }" slot="originFile">
						<Button v-if="row.originFile" type="primary" @click="exportfile(row,1)">点击下载</Button>
					</template>
					<template slot-scope="{ row, index }" slot="successFile">
						<Button v-if="row.successFile" type="success" @click="exportfile(row,2)">点击下载</Button>
					</template>
					<template slot-scope="{ row, index }" slot="failFile" >
						<Button v-if="row.failFile" type="error" @click="exportfile(row,3)">点击下载</Button>
					</template>
					<template slot-scope="{ row, index }" slot="approval">
						<div v-if="[1,4,5].includes(+row.authStatus)">
							<Button type="primary" size="small" style="margin-right: 5px" v-has="'check'"
								@click="updateExamine(true,row.id)">通过</Button>
							<Button type="error" size="small" v-has="'check'" @click="updateExamine(false,row.id)">不通过</Button>
						</div>
						<div v-else>
							<Button type="primary" size="small" style="margin-right: 5px" v-has="'check'"
								disabled>通过</Button>
							<Button type="error" size="small" v-has="'check'" disabled>不通过</Button>
						</div>
					</template>
				</Table>
				<!-- 分页 -->
				<div style="margin-top: 15px;">
					<Page :total="totalTaskUpdate" :page-size="pageSize" :current.sync="pageTaskUpdate" show-total show-elevator
						@on-change="TaskgoPageUpdate" style="margin: 15px 0;" />
				</div>
			</div>
		</Modal>
		<!-- 模板文件table -->
		<Table :columns="updateModelColumns" :data="updateModelData" ref="updateModelTable" v-show="false"></Table>

		<a ref="downloadLink" style="display: none"></a>
		<!-- 卡池树 -->
		<Drawer title="关联卡池管理" v-model="drawer" width="350" :mask-closable="false" :styles="styles"
			@on-close="drawerClose">
			<Button type="success" size="small" style="margin: 0 15px" @click="cardPoolEdit">编辑</Button>
			<Tree :data="cardPoolTree" ref="cardPool" class="demo-tree-render" :empty-text="emptyText"></Tree>
			<div class="demo-drawer-footer">
				<Button style="margin-right: 8px" @click="drawerClose">取消</Button>
				<Button type="primary" @click="toSetCardPool()">确定</Button>
			</div>
		</Drawer>

		<!-- 卡池编辑 -->
		<Modal title="卡池编辑" v-model="cardPoolEditFlag" @on-cancel="cardPoolEditConfirm" :mask-closable="false"
			width="730px">
			<div style="padding: 0 16px;">
				<Form ref="cpEditForm" :model="filterSearchObj" inline style="font-weight:bold;">
					<FormItem>
						<Input type="text" v-model="filterSearchObj.cpName" clearable placeholder="卡池名称"></Input>
					</FormItem>
					<FormItem>
						<Input type="text" v-model="filterSearchObj.sName" clearable placeholder="供应商名称"></Input>
					</FormItem>
					<FormItem>
						<Input type="text" v-model="filterSearchObj.cName" clearable placeholder="国家/地区名称"></Input>
					</FormItem>
					<FormItem>
						<Button type="primary" :loading="cardPoolEditTreeLoad" @click="doCPTreeFilter">搜索</Button>
					</FormItem>
				</Form>
				<div class="demo-spin-article">
					<div style="height: 295px; overflow-y: auto;">
						<Tree :data="cardPoolEditTree" ref="cardPool" class="demo-tree-render" :empty-text="emptyText">
						</Tree>
					</div>
					<Spin size="large" fix v-if="cardPoolEditTreeLoad"></Spin>
				</div>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<!-- <Button style="margin-left: 8px" @click="cardPoolEditCancle">取消</Button> -->
				<Button type="primary" @click="cardPoolEditConfirm">确定</Button>
			</div>
		</Modal>

		<!-- 加油包列表弹窗 -->
		<Modal title="添加加油包"  v-model="addRefuelModel"  @on-cancel="cancelModal" :mask-closable="false"
		 width="1000px">
			<Form ref="RefuelObj" :model="RefuelObj" :label-width="80" inline style="font-weight:bold;">
				<FormItem label="加油包名称">
					<Input type="text" v-model="RefuelObj.gaspackname" clearable placeholder="加油包名称"></Input>
				</FormItem>
				<FormItem label="加油包ID">
					<Input type="text" v-model="RefuelObj.gaspacknameid" clearable placeholder="加油包ID"></Input>
				</FormItem>
				<FormItem>
					<Button type="primary" :loading="searchObjloading" @click="search">搜索</Button>
				</FormItem>
			</Form>
			<Table :columns="Unitedcolumns" :data="Uniteddata" style="width:100%;margin-top: 40px;"
			 @on-selection-change="RowChange"
			 @on-select-cancel="cancelUnited"
			 @on-select-all-cancel="cancelUnitedAll"
			 :loading="Unitedloading">
			</Table>
			<div style="margin-top:15px">
				<Page :total="Unitedtotal" :current.sync="UnitedcurrentPage" show-total show-elevator @on-change="UnitedgoPage" />
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button style="margin-left: 8px" @click="cancelModal">取消</Button>
				<Button type="primary" @click="Confirm">确定</Button>
			</div>
		</Modal>

		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					<FormItem label="你本次导出任务ID为:">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem label="你本次导出的文件名为:">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" @click="Goto">立即前往</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		opsearchAll,
	} from '@/api/operators';
	import {
		getPackageList,
		checkPackage,
		batchDelete,
		allBatchDelete,
		allBatchUpdate,
		batchUpdatePackage,
		batchQueryTasks,
		batchQueryDownload,
		allCheckPackage,
		getRefuelList,
		exportflow,
		exportPackageCountryList,
		getConsumption,
		getTemplate,
	} from '@/api/package/package';
	import {
		cardPoolRatio,
	} from '@/api/productMngr/cardPool';
	import {
		supplier,
	} from '@/api/ResourceSupplier'
	import { index } from 'mathjs';
	const math = require('mathjs');
	export default {
		components: {},
		data() {
			// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (this.uploadList && this.uploadList.length === 0) {
					callback(new Error('请上传文件'))
				} else {
					callback()
				}
			}
			//保留五位小数
			const positiveNum = (rule, value, callback) => {
				var str = /^(([1-9]\d{0,13})|0)(\.\d{1,4})?$/;
				return str.test(value);
			};
			//非负整数
			const nonnegativeInteger = (rule, value, callback) => {
				var str = /^[0-9]\d*$/;
				return str.test(value);
			};
			return {
				addRefuelModel: false, //加油包弹窗标识
				exportModal: false,//导出弹框标识
				searchObjloading: false,
				Unitedloading:false,
				selection: [], //多选
				supplierList:[],//资源供应商列表
				RefuelObj: {
					gaspackname: '',
					gaspacknameid: '',
				},
				Unitedcolumns: [{
					type: 'selection',
					width: 60,
					align: 'center'
				}, {
					title: "加油包ID",
					key: 'id',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				},
				{
					title: "加油包名称(简体中文)",
					key: 'nameCn',
					minWidth: 180,
					align: 'center',
					tooltip: true,
				}, {
					title: "加油包价格(人民币)",
					key: 'cny',
					minWidth: 180,
					align: 'center',
					tooltip: true,
				}, {
					title: "加油包价格(港币)",
					key: 'hkd',
					minWidth: 180,
					align: 'center',
					tooltip: true,
				}, {
					title: "加油包价格(美元)",
					key: 'usd',
					minWidth: 180,
					align: 'center',
					tooltip: true,
				}],
				Unitedtotal: 0,
				UnitedcurrentPage: 1,
				Unitedpage: 0,
				Uniteddata: [],
				pageNo: '',
				id: '',
				fileType : '',
				Taskpage: 0,
				continentList: [],
				searchObj: {
					'packageName': '', //套餐名称
					'country': '', //搜索国家/地区
					'status': '', //套餐状态
					'packageId': '', //套餐ID
					'poolId':'',//卡池ID
					'supplierId':'',//供应商ID
				},
				tableData: [], //套餐列表信息
				selection: [], //多选
				selectionIds: [], //多选ids
				selectionList: [], //翻页勾选List
				selectionTypes: [], //多选类型(是否为终端厂商套餐)
				uploadList: [], //下载
				loading: false,
				tableLoading: false,
				importLoading: false,
				downloading: false,
				countryloading:false,
				total: 0,
				pageSize: 10,
				page: 1,
				totalTask: 0, //删除弹窗总条数
				pageTask: 1, //删除弹窗 当前页数
				totalTaskUpdate: 0, //修改弹窗总条数
				pageTaskUpdate: 1, //修改弹窗 当前页数
				submitFlag: false,
				batchEditFlag: false,
				deleteflag: false,
				updateflag: false,
				packageCountryList: '',
				batchEditObj: {
					'flowLimitUnit': 1, //默认流量上限单位是GB
					'effectiveDay': '', //套餐订购后有效期
					'descCn': '', //套餐描述（简体中文）
					'descTw': '', //套餐描述（繁字中文）
					'descEn': '', //套餐描述（英文）
					'selectionTypes':[],//加油包列表
					packageConsumptions: [{
						index: 0,
						consumption: "",//用量值
						unit: "MB", //用量单位
						upccTemplateId: "",//选择模板
					}]
				},
				formobj: {},
				updateFormobj: {},
				statusList: [
					// {
					//   value: '1',
					//   label: "待上架"
					// },
					{
						value: '2',
						label: "正常"
					},
					{
						value: '3',
						label: "下架"
					},
					// {
					//   value: '4',
					//   label: "删除/注销"
					// }
				],
				isTerminalList: [{
						value: '1',
						label: "是"
					},
					{
						value: '2',
						label: "否"
					}
				],
				flowLimitTypeList: [{
						value: '1',
						label: "周期内限量"
					},
					{
						value: '2',
						label: "按周期类型重置"
					}
				],
				columns: [{
						type: 'selection',
						width: 60,
						align: 'center'
					},
					{
						title: '套餐ID',
						key: 'id',
						sortable: true,
						align: 'center',
						tooltip: true,
						minWidth: 170,
					},
					{
						title: '套餐名称(简中)',
						key: 'nameCn',
						align: 'center',
						tooltip: true,
						minWidth: 130,
					},
					{
						title: '套餐价格(人民币)',
						key: 'cny',
						align: 'center',
						minWidth: 130,
						// render: (h, params) => {
						//   const row = params.row;
						//   const text = Number(math.format(Number(row.cny) / 100, 14)).toFixed(4);
						//   return h('label', text);
						// }
					},
					{
						title: '套餐价格(港币)',
						key: 'hkd',
						align: 'center',
						minWidth: 120,
						// render: (h, params) => {
						//   const row = params.row;
						//   const text = Number(math.format(Number(row.hkd) / 100, 14)).toFixed(4);
						//   return h('label', text);
						// }
					},
					{
						title: '套餐价格(美元)',
						key: 'usd',
						align: 'center',
						minWidth: 120,
						// render: (h, params) => {
						//   const row = params.row;
						//   const text = Number(math.format(Number(row.usd) / 100, 14)).toFixed(4);
						//   return h('label', text);
						// }
					},
					{
						title: '套餐状态',
						key: 'status',
						align: 'center',
						minWidth: 90,
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.status) {
								case "1":
									text = "待上架";
									break;
								case "2":
									text = "正常";
									break;
								case "3":
									text = "下架";
									break;
								case "4":
									text = "删除/注销";
									break;
								default:
									text = "未知状态";
							}
							return h('label', text);
						}
					},
					{
						title: '终端厂商套餐',
						key: 'isTerminal',
						align: 'center',
						tooltip: true,
						minWidth: 110,
						render: (h, params) => {
							const row = params.row;
							const text = row.isTerminal == '1' ? '是' : row.isTerminal == '2' ? '否' : '未知';
							return h('label', text)
						}
					},
					{
						title: '流量限制类型',
						key: 'flowLimitType',
						align: 'center',
						tooltip: true,
						minWidth: 110,
						render: (h, params) => {
							const row = params.row;
							const text = row.flowLimitType == '1' ? '周期内限量' : row
								.flowLimitType == '2' ? '按周期类型重置' : '';
							return h('label', text)
						}
					}
				],
				columnsTask: [{
						title: '批量操作时间',
						key: 'createTime',
						minWidth: 150,
						align: 'center',
					},
					{
						title: '处理状态',
						key: 'taskStatus',
						minWidth: 90,
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.taskStatus) {
								case "1":
									text = "未处理";
									break;
								case "2":
									text = "处理中";
									break;
								case "3":
									text = "已完成";
									break;
							}
							return h('label', text);
						}
					}, {
						title: '原始文件',
						slot: 'originFile',
						minWidth: 100,
						align: 'center',
					}, {
						title: '成功文件',
						slot: 'successFile',
						minWidth: 100,
						align: 'center',
					}, {
						title: '失败文件',
						slot: 'failFile',
						minWidth: 100,
						align: 'center',
					}
				],
				updateColumnsTask: [{
						title: '批量操作时间',
						key: 'createTime',
						minWidth: 150,
						align: 'center',
					},
					{
						title: '处理状态',
						key: 'taskStatus',
						minWidth: 90,
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.taskStatus) {
								case "1":
									text = "未处理";
									break;
								case "2":
									text = "处理中";
									break;
								case "3":
									text = "已完成";
									break;
							}
							return h('label', text);
						}
					}, {
						title: '原始文件',
						slot: 'originFile',
						minWidth: 100,
						align: 'center',
					}, {
						title: '成功文件',
						slot: 'successFile',
						minWidth: 100,
						align: 'center',
					}, {
						title: '失败文件',
						slot: 'failFile',
						minWidth: 100,
						align: 'center',
					}
				],
				taskdata: [],
				updateTaskdata: [],
				uploadUrl: '', //删除弹窗 上传地址
				updateuploadUrl: '', //修改弹窗 上传地址
				file: null,
				updateFile: null,
				modelData: [{
					'package_id': '********',
					'country': '********',
				}, ],
				modelColumns: [{
						title: 'package_id',
						key: 'package_id',
					}, // 列名根据需要添加
					{
						title: 'country',
						key: 'country'
					} // 列名根据需要添加
				],
				updateModelData: [{
					'package_id': '********',
					'country': '********',
					'poolId': '********',
					'rate[多个卡池，在同一行多次填写。poolId、rate...]': '********'
				}, ],
				updateModelColumns: [{
						title: 'package_id',
						key: 'package_id',
					}, // 列名根据需要添加
					{
						title: 'country',
						key: 'country'
					}, // 列名根据需要添加
					{
						title: 'poolId',
						key: 'poolId'
					},
					{
						title: 'rate[多个卡池，在同一行多次填写。poolId、rate...]',
						key: 'rate[多个卡池，在同一行多次填写。poolId、rate...]'
					}
				],
				ruleBatchEditValidate: {
					effectiveDay: [{
						validator: (rule, value, cb) => {
							var str = /^[0-9]\d*$/;
							return str.test(value) || value == '';
						},
						message: '购买有效期(天)格式错误',
						trigger: 'blur'
					}, {
						validator: (rule, value, cb) => {
							return Number(2147483647) >= Number(value) || value == '';
						},
						message: '购买有效期(天)数值过大',
						trigger: 'blur'
					}],
					cny: [{
						validator: (rule, value, cb) => {
							var str = /^(([1-9]\d{0,9})|0)(\.\d{1,2})?$/;
							return str.test(value) || value == '';
						},
						message: '套餐价格(人民币)格式错误',
						trigger: 'blur'
					}],
					hkd: [{
						validator: (rule, value, cb) => {
							var str = /^(([1-9]\d{0,9})|0)(\.\d{1,2})?$/;
							return str.test(value) || value == '';
						},
						message: '套餐价格(港币)格式错误',
						trigger: 'blur'
					}],
					usd: [{
						validator: (rule, value, cb) => {
							var str = /^(([1-9]\d{0,9})|0)(\.\d{1,2})?$/;
							return str.test(value) || value == '';
						},
						message: '套餐价格(美元)格式错误',
						trigger: 'blur'
					}],
					descEn: [{
						validator: (rule, value, cb) => {
							var regex =
								/^[0-9a-zA-Z\/\(\)\,\.\:\"\<\>\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;
							return regex.test(value) || value == '';
						},
						message: 'Package description (EN) format error',
					}],
					mccList: [],
					cardPool: [{
						validator: (rule, value, cb) => {
							return this.batchEditObj.mccList.length == 0 || (this.batchEditObj.mccList.length >
								0 && this.cpcrvList
								.length > 0);
						},
						message: '关联卡池不能为空',
						trigger: 'blur change'
					}],
				},
				drawer: false, //抽屉标识
				emptyText: '未查询到任何卡池数据',
				styles: {
					height: 'calc(100% - 55px)',
					overflow: 'auto',
					paddingBottom: '53px',
					position: 'static'
				},
				mccListTemp: '',
				cardPoolEditFlag: false, //卡池编辑框标识
				cardPoolTree: [], //比例树
				cardPoolEditTree: [], //编辑树
				cardPoolEditTreeLoad: false, //编辑树加载标识
				filterPool: [], //筛选后展示集合
				filterTempPool: [], //筛选展示临时集合:取消时使用
				totalPool: [], //所有比例数据集合
				totalTempPool: [], //所有比例数据集合:取消时使用
				cpcrvList: [],
				filterSearchObj: {
					'cpName': '',
					'sName': '',
					'cName': ''
				}, //条件筛选对线
				ruleobj: {
					file: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
				},
				updateruleobj: {
					updateFile: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
				},
				localMap: new Map(),
				taskId:'',
				taskName:'',
				index: 0,
				TemplateList: [], //速度模板列表
				isSupportedHotspots: [], //是否支持热点
				isSupportedHotspotsList: [],
				unitList: [{
					value: 'MB',
					label: 'MB'
				}, {
					value: 'GB',
					label: 'GB'
				}, {
					value: 'TB',
					label: 'TB'
				}],
			}
		},
		computed: {
		},
		methods: {
			// 用量值规则
			consumptionRules (index) {
				return [{
					validator: (rule, value, callback) => {
						value = String(value || '').trim();

						const currentItem = this.batchEditObj.packageConsumptions[index];
						const currentConsumption = value;
						const currentUnit = currentItem.unit;

						if (!currentConsumption) {
							callback();
							return;
						}

						// Validate consumption format (1-10 digits integer)
						const integerRegex = /^[1-9]\d{0,9}$/;
						if (!integerRegex.test(currentConsumption)) {
							callback(new Error('请输入1-10位整数数字'));
							return;
						}

						// Convert to BigInt MB for range checks
						let currentConsumptionMB;
						try {
							let baseValue = BigInt(currentConsumption);
							switch (currentUnit) {
								case 'TB':
									currentConsumptionMB = baseValue * 1024n * 1024n;
									break;
								case 'GB':
									currentConsumptionMB = baseValue * 1024n;
									break;
								case 'MB':
									currentConsumptionMB = baseValue;
									break;
								default:
									// This should ideally not be reached if currentUnit is checked above
									callback(new Error('无效的单位'));
									return;
							}
						} catch (e) {
							callback(new Error('用量值转换失败')); // Should not happen with regex check
							return;
						}

						const minMB = 10n;
						const maxMB = 9999999999n; // 10位最大值

						// Check if consumption is within the allowed MB range
						if (currentConsumptionMB < minMB) {
							callback(new Error('用量值不能小于 ' + minMB + 'MB'));
							return;
						}
						if (currentConsumptionMB > maxMB) {
							let maxValueStr;
							if (currentUnit === 'MB') {
								maxValueStr = maxMB + 'MB';
							} else if (currentUnit === 'GB') {
								maxValueStr = (maxMB / 1024n) + 'GB';
							} else if (currentUnit === 'TB') {
								maxValueStr = (maxMB / (1024n * 1024n)) + 'TB';
							}
							callback(new Error('用量值超过最大限制 ' + maxValueStr));
							return;
						}

						callback(); // All checks passed for this validator
					},
					trigger: ['blur', 'change']
				}, {
					// Second validator: Uniqueness
					validator: (rule, value, callback) => {
						// Only validate if both consumption and unit are present
						const currentItem = this.batchEditObj.packageConsumptions[index];
						if (!currentItem.consumption || !currentItem.unit) { // Use consumption.trim() for check? No, the `value` is already trimmed.
							callback(); // If incomplete, don't validate uniqueness
							return;
						}
						const currentConsumptionTrimmed = currentItem.consumption.trim();
						const currentUnit = currentItem.unit;

						let currentConsumptionMB;
						try {
							currentConsumptionMB = BigInt(currentConsumptionTrimmed);
							switch (currentUnit) {
								case 'TB':
									currentConsumptionMB *= 1024n * 1024n;
									break;
								case 'GB':
									currentConsumptionMB *= 1024n;
									break;
							}
						} catch (e) {
							callback(new Error('用量值转换失败')); // Should not happen if first validator passed
							return;
						}

						const otherConsumptions = this.batchEditObj.packageConsumptions.filter((item, i) => i !== index);
						const isUnique = otherConsumptions.every(item => {
							if (!item.consumption || !item.unit) {
								return true; // Don't block if other item is incomplete
							}
							const otherConsumptionTrimmed = item.consumption.trim();
							const otherUnit = item.unit;

							let otherConsumptionMB;
							try {
								otherConsumptionMB = BigInt(otherConsumptionTrimmed);
								switch (otherUnit) {
									case 'TB':
										otherConsumptionMB *= 1024n * 1024n;
										break;
									case 'GB':
										otherConsumptionMB *= 1024n;
										break;
								}
							} catch (e) {
								console.error('转换其他用量值失败:', e);
								return true; // If other value conversion fails, don't block uniqueness
							}

							return currentConsumptionMB !== otherConsumptionMB;
						});
						if (!isUnique) {
							return callback(new Error('用量值+单位组合必须唯一'));
						}
						callback();
                        this.$nextTick(() => {
                            this.$refs.batchEditObj.validateField('packageConsumptions.' + index + '.unit');
                        });
					},
					trigger: ['blur', 'change']
				}, {
					// Third validator: Increment
					validator: (rule, value, callback) => {
						// Only validate if both consumption and unit are present
						const currentItem = this.batchEditObj.packageConsumptions[index];
						if (!currentItem.consumption || !currentItem.unit) {
							callback(); // If incomplete, don't validate increment
							return;
						}
						const currentConsumptionTrimmed = currentItem.consumption.trim();
						const currentUnit = currentItem.unit;

						let currentConsumptionMB;
						try {
							currentConsumptionMB = BigInt(currentConsumptionTrimmed);
							switch (currentUnit) {
								case 'TB':
									currentConsumptionMB *= 1024n * 1024n;
									break;
								case 'GB':
									currentConsumptionMB *= 1024n;
									break;
							}
						} catch (e) {
							callback(new Error('用量值转换失败')); // Should not happen if first validator passed
							return;
						}

						if (index > 0) {
							const prevItem = this.batchEditObj.packageConsumptions[index - 1];
							// Only compare if previous item is also complete
							if (prevItem && prevItem.consumption && prevItem.unit) {
                                const prevConsumptionTrimmed = prevItem.consumption.trim();
                                const prevUnit = prevItem.unit;
								let prevConsumptionMB;
								try {
									prevConsumptionMB = BigInt(prevConsumptionTrimmed);
									switch (prevUnit) {
										case 'TB':
											prevConsumptionMB *= 1024n * 1024n;
											break;
										case 'GB':
											prevConsumptionMB *= 1024n;
											break;
									}
								} catch (e) {
									console.error('转换前一个用量值失败:', e);
									callback(); // If prev value conversion fails, don't block increment check
									return;
								}

								if (prevConsumptionMB !== null && (currentConsumptionMB <= prevConsumptionMB)) {
									return callback(new Error('用量值逻辑不正确，每档的用量值需大于上一档次的用量值'));
								}
							}
						}
						callback();
					},
					trigger: ['blur', 'change']
				}]
			},
			// 单位规则
			unitRules (index) {
				return [{
					validator: (rule, value, callback) => {
						const consumption = this.batchEditObj.packageConsumptions[index].consumption;
						if (consumption) { // Only validate uniqueness and increment if consumption value exists
							const currentConsumptionMB = this.convertToMB(consumption, value);
							const otherConsumptions = this.batchEditObj.packageConsumptions.filter((item, i) => i !== index);
							const isUnique = otherConsumptions.every(item => {
								// Only compare if the other item has a valid consumption and unit
								if (item.consumption === '' || item.consumption === null || item.unit === '' || item.unit === null) {
									return true; // Don't block if other item is incomplete
								}
								const otherConsumptionMB = this.convertToMB(item.consumption, item.unit);
								return currentConsumptionMB !== otherConsumptionMB;
							});
							if (!isUnique) {
								return callback(new Error('用量值+单位组合必须唯一'));
							}
						}
						callback();
                        this.$nextTick(() => {
                            this.$refs.batchEditObj.validateField('packageConsumptions.' + index + '.consumption');
                        });
					},
					trigger: 'change'
				}]
			},
			// 辅助函数：将用量转换为MB
			convertToMB(value, unit) {
				if (value === '' || value === null || isNaN(Number(value))) return 0;
				let num = Number(value);
				switch (unit) {
					case 'MB':
						return num;
					case 'GB':
						return num * 1024;
					case 'TB':
						return num * 1024 * 1024;
					default:
						return 0;
				}
			},
			//表格初始化
			init() {
				this.columns.splice(10, 0, {
					title: '审批状态',
					key: 'auditStatus',
					align: 'center',
					tooltip: true,
					minWidth: 100,
					render: (h, params) => {
						const row = params.row
						const color = row.auditStatus == '1' ? '#2b85e4' : row.auditStatus == '2' ? '#19be6b' :
							row.auditStatus == '3' ? '#ff0000' : row.auditStatus == '4' ? '#ffa554' :
							row.auditStatus == '5' ? '#ff0000' : '';
						const text = row.auditStatus == '1' ? '新建待审核' : row.auditStatus == '2' ? '通过' :
							row.auditStatus == '3' ? '不通过' : row.auditStatus == '4' ? '修改待审批' :
							row.auditStatus == '5' ? '删除待审批' : ''
						return h('label', {
							style: {
								color: color
							}
						}, text)
					}
				});
				this.columnsTask.splice(6, 0, {
					title: '审批状态',
					key: 'authStatus',
					align: 'center',
					tooltip: true,
					minWidth: 100,
					render: (h, params) => {
						const row = params.row
						const color = row.authStatus == '1' ? '#2b85e4' : row.authStatus == '2' ? '#19be6b' :
							row.authStatus == '3' ? '#ff0000' : '';
						const text = row.authStatus == '1' ? '待审核' : row.authStatus == '2' ? '通过' :
							row.authStatus == '3' ? '不通过' : ''
						return h('label', {
							style: {
								color: color
							}
						}, text)
					}
				});
				this.updateColumnsTask.splice(6, 0, {
					title: '审批状态',
					key: 'authStatus',
					align: 'center',
					tooltip: true,
					minWidth: 100,
					render: (h, params) => {
						const row = params.row
						const color = row.authStatus == '1' ? '#2b85e4' : row.authStatus == '2' ? '#19be6b' :
							row.authStatus == '3' ? '#ff0000' : '';
						const text = row.authStatus == '1' ? '待审核' : row.authStatus == '2' ? '通过' :
							row.authStatus == '3' ? '不通过' : ''
						return h('label', {
							style: {
								color: color
							}
						}, text)
					}
				});
				var action = ['view', 'update', 'copy', 'delete'];
				var approval = ['check'];
				var btnPriv = this.$route.meta.permTypes;
				var actionMixed = action.filter(function(val) {
					return btnPriv.indexOf(val) > -1
				});
				var approvalMixed = approval.filter(function(val) {
					return btnPriv.indexOf(val) > -1
				});
				if (actionMixed.length > 0) {
					var width = 30 + 50 * actionMixed.length;
					this.columns.splice(9, 0, {
						title: '操作',
						slot: 'action',
						width: width,
						align: 'center'
					});
				}
				if (approvalMixed.length > 0) {
					this.columns.push({
						title: '审批操作',
						slot: 'approval',
						align: 'center',
						width: 140,
					});
				}
				if (approvalMixed.length > 0) {
					this.columnsTask.push({
						title: '操作',
						slot: 'approval',
						align: 'center',
						width: 140,
					});
				}
				if (approvalMixed.length > 0) {
					this.updateColumnsTask.push({
						title: '操作',
						slot: 'approval',
						align: 'center',
						width: 140,
					});
				}
				//获取国家/地区信息
				this.getLocalList();
				//获取资源供应商
				this.getsupplier();
				//加载初始信息
				this.loadByPage(1);
			},
			//搜索 表格数据加载
			loadByPage(page) {
				this.page = page;
				this.tableLoading = true;
				var searchCondition = {
					packageNameCn: this.searchObj.packageName.replace(/\s/g, ''),
					mcc: this.searchObj.country,
					status: this.searchObj.status,
					isNeedMcc: true,
					list: true,
					page: page,
					isNeedAuth: true,
					pageSize: this.pageSize,
					packageId: this.searchObj.packageId,
					isTerminal: this.searchObj.isTerminal,
					flowLimitType: this.searchObj.flowLimitType,
					supplierId:this.searchObj.supplierId,
					poolId:this.searchObj.poolId,
				};
				getPackageList(searchCondition).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						let List = []
						// 循环遍历data
						data.data.map((value, index) => {
							if (value.authObj) {
								List.push(value.authObj)
							} else {
								List.push(value)
							}
						})
						//回显
						this.selectionList.forEach(item => {
							List.forEach(element => {
								if (element.id == item.id) {
									this.$set(element, '_checked', true)
								}
							})
						})
						this.total = Number(data.total);
						this.tableData = List;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
					this.tableLoading = false;
				})
			},
			//搜索
			searchPackage() {
				// 资源供应商与国家/地区结合使用
				if(this.searchObj.supplierId && !this.searchObj.country){
					this.$Message.warning('还需选择【国家/地区】一起作为搜索条件')
					return
				}
				this.loadByPage(1);
			},
			//国家/地区
			getLocalList() {
				opsearchAll().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.continentList = list;
						this.continentList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
						var localMap = new Map();
						list.map((local, index) => {
							localMap.set(local.mcc, local.countryEn);
						});
						this.localMap = localMap;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//多选
			RowChange(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					let flag = true;
					this.batchEditObj.selectionTypes.map((item, index) => {
					   if(value.id===item.id){
						   flag=false
					   }
					});
					//判断重复
					if(flag){
						this.batchEditObj.selectionTypes.push(value);
					}
				});
			},
			// 取消选择套餐包
			cancelUnited(selection, row) {
				this.batchEditObj.selectionTypes.forEach((value,index)=>{
					if(value.id===row.id){
						 this.batchEditObj.selectionTypes.splice(index,1);
					}
				})
			},
			// 取消全选选择套餐包
			cancelUnitedAll(selection, row) {
				this.batchEditObj.selectionTypes=[]
			},
			//获取可订购加油包列表
			getRefuelList(page){
				this.Unitedloading=true
				getRefuelList({
					pageNum:page,
					pageSize:10,
					refuelID:this.RefuelObj.gaspacknameid,
					refuelName:this.RefuelObj.gaspackname
				}).then(res => {
					if (res && res.code == '0000') {
						this.Uniteddata=res.data
						this.Unitedtotal=res.count
						this.UnitedcurrentPage = page
						//回显
						this.batchEditObj.selectionTypes.forEach(item=>{
						   res.data.forEach(element=>{
							 if(element.id==item.id){
							   this.$set(element,'_checked',true)
							 }
						   })
						})
						this.addRefuelModel = true
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
					this.Unitedloading=false
				   this.searchObjloading=false
				})
			},
			RefuelPackageList() {
				this.getRefuelList(1)
			},
			search(){
				this.searchObjloading=true
				this.getRefuelList(1)
			},
			UnitedgoPage(page){
				this.getRefuelList(page)
			},
			//加油包列表确认
			Confirm() {
				this.addRefuelModel = false
			},
			//新增
			packageAdd() {
				this.$router.push({
					name: 'packageAdd'
				})
			},
			//一键切换卡池
			goCardPoolSwitch() {
				this.$router.push({
					name: 'cardPoolSwitchList'
				})
			},
			//提交
			submit() {
				this.$refs["batchEditObj"].validate((valid) => {
					if (valid) {
						var batchEdit = Object.assign({}, this.batchEditObj);
						batchEdit.cpcrvList = this.cpcrvList;
						var ids=[]
						this.selectionList.forEach((value, index) => {
							ids.push(value.id)
						})
						batchEdit.ids = ids;
						this.submitFlag = true;
						//组装加油包id
						batchEdit.refuelListStr=[]
						this.batchEditObj.selectionTypes.forEach((value, index) => {
							batchEdit.refuelListStr.push(value.id)
						})
						batchEdit.refuelListStr=JSON.stringify(batchEdit.refuelListStr)

						// 在此处执行严格的预提交校验
						if (!this.validateAllPackageConsumptionsForSubmission()) {
							this.submitFlag = false;
							return; // 校验失败，阻止提交，错误信息已在 validateAllPackageConsumptionsForSubmission 中显示
						}

						// 过滤掉不完整或数值无效的分组（此步骤在严格校验后，作为额外的清理）
						batchEdit.packageConsumptions = batchEdit.packageConsumptions.filter(item => {
							const consumption = String(item.consumption || '').trim();
							const unit = item.unit;
							const templateId = item.upccTemplateId;

							// 必须是完整的条目：用量值、单位和模板ID都必须存在且非空
							if (!consumption || !unit || !templateId) {
								return false; // 不完整的条目，过滤掉
							}

							// 必须通过数值校验（在 validateAllPackageConsumptionsForSubmission 已检查，此处是双重保障）
							if (!this.isConsumptionValueValid(consumption, unit)) {
								return false; // 数值无效，过滤掉
							}

							return true; // 通过所有检查，保留此条目
						});

						// 将用量值根据单位转换为MB，并转换为Number类型以适应后端API
						batchEdit.packageConsumptions = batchEdit.packageConsumptions.map(item => {
							const baseValue = BigInt(item.consumption.trim()); // 使用trim()去除空格
							let consumptionMB;
							switch (item.unit) {
								case 'TB':
									consumptionMB = baseValue * 1024n * 1024n;
									break;
								case 'GB':
									consumptionMB = baseValue * 1024n;
									break;
								case 'MB':
									consumptionMB = baseValue;
									break;
								default:
									consumptionMB = 0n; // Default or error handling
							}
							return { consumption: Number(consumptionMB), unit: item.unit, upccTemplateId: item.upccTemplateId };
						});

						 batchEdit.packageConsumptions.map((v,index)=> {
							// 将模板对应的 是否支持热点 属性去重后传给接口
							this.isSupportedHotspots.map((v1, index1)=>{
								if (v1.templateId == v.upccTemplateId) {
									this.isSupportedHotspotsList.push(this.isSupportedHotspots[index1].supportHotspot)
								}
							})
						})
						batchEdit.supportHotspot = [...new Set(this.isSupportedHotspotsList)]

						batchUpdatePackage(batchEdit).then(res => {
							if (res && res.code === '0000') {
								setTimeout(() => {
									this.batchEditFlag = false;
									this.submitFlag = false;
									this.$Notice.success({
										title: '操作提示',
										desc: '操作成功'
									});
									this.selectionList = [];
									this.batchEditObj.selectionTypes=[];
									this.isSupportedHotspotsList= [] //清除是否支持热点数组
									this.batchEditObj.packageConsumptions = [{ index: 0, consumption: "", unit: "MB", upccTemplateId: "" }]; // 重置用量值和模板
									this.index = 0; // 重置index
									this.loadByPage(this.page);
								}, 1500);
							} else {
								this.submitFlag = false;
								throw res
								this.isSupportedHotspotsList= [] //清除是否支持热点数组
								this.batchEditObj.packageConsumptions = [{ index: 0, consumption: "", unit: "MB", upccTemplateId: "" }]; // 重置用量值和模板
								this.index = 0; // 重置index
							}
						}).catch((err) => {
							this.submitFlag = false;
							this.isSupportedHotspotsList= [] //清除是否支持热点数组
							this.batchEditObj.packageConsumptions = [{ index: 0, consumption: "", unit: "MB", upccTemplateId: "" }]; // 重置用量值和模板
							this.index = 0; // 重置index
						})
					}
				})
			},
			reset() {
				this.$refs["batchEditObj"].resetFields();
				this.batchEditObj.packageConsumptions = [{
					index: 0,
					consumption: "",
					unit: "MB",
					upccTemplateId: "",
				}]
				this.batchEditObj.selectionTypes=[]
			},
			//详情
			//编辑
			//复制
			packageCommon(row, type) {
				// var obj = Object.assign({}, row);
				var obj =row
				obj.hasRefuelPackage = obj.refuelIDList.length > 0 ? true : false;
				obj.type = type;
				this.$router.push({
					name: 'package' + type,
					query: {
						package: encodeURIComponent(JSON.stringify(obj))
					}
				})
			},
			//审核通过/不通过
			examine(type, id) {
				this.$Modal.confirm({
					title: type == 2 ? '确认执行审核通过？' : '确认执行审核不通过？',
					onOk: () => {
						checkPackage(id, type).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.loadByPage(this.page);
							} else {
								throw res
							}
						}).catch((err) => {
						})
					}
				});
			},
			//删除
			packageDel(id) {
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						batchDelete({
							packageIdList: [id]
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								if (this.tableData.length == 1 && this.page > 1) {
									this.loadByPage(this.page - 1);
								} else {
									this.loadByPage(this.page);
								}
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				selection.map((value, index) => {
					let flag = true;
					this.selectionList.map((item, index) => {
						if (value.id === item.id) {
							flag = false
						}
					});
					//判断重复
					if (flag) {
						this.selectionList.push(value);
					}
				});
			},
			// 取消选择套餐包
			cancelPackage(selection, row) {
				this.selectionList.forEach((value, index) => {
					if (value.id === row.id) {
						this.selectionList.splice(index, 1);
					}
				})
			},
			// 取消全选选择套餐包
			cancelPackageAll(selection, row) {
				this.selectionList = []
			},
			// 导出
			queryExport:function(){
				// 资源供应商与国家/地区结合使用
				if(this.searchObj.supplierId && !this.searchObj.country){
					this.$Message.warning('还需选择【国家/地区】一起作为搜索条件')
					return
				}
				this.downloading = true
				exportflow({
					packageNameCn: this.searchObj.packageName.replace(/\s/g, ''),
					mcc: this.searchObj.country,
					status: this.searchObj.status,
					list: true,
					isExport: true,
					page: this.page,
					pageSize: this.pageSize,
					packageId: this.searchObj.packageId,
					isTerminal: this.searchObj.isTerminal,
					flowLimitType: this.searchObj.flowLimitType,
					supplierId:this.searchObj.supplierId,
					poolId:this.searchObj.poolId,
					userId: this.$store.state.user.userId,//查询权限
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.id
					this.taskName = res.data.fileName
					this.downloading = false
				}).catch(() => this.downloading = false)
			},
			//套餐覆盖国家导出
			countryExport:function(){
				var len = this.selectionList.length;
				let ids=[]
				this.selectionList.map((value,index) => {
					ids.push(value.id)
				})
				if (len < 1) {
					this.$Message.warning('请勾选套餐！')
					return
				}
				this.countryloading=true
				exportPackageCountryList({
					packageIds: ids,
					userId: this.$store.state.user.userId,
				}).then((res) => {
					this.exportModal = true
					this.taskId = res.data.id
					this.taskName = res.data.fileName
					this.countryloading = false
				}).catch(() => this.countryloading = false)
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportModal = false
			},
			//批量删除
			deleteList() {
				var len = this.selectionList.length;
				if (len < 1) {
					this.$Message.warning('请至少选择一条记录')
					return
				}
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						let ids=[]
						this.selectionList.map((value,index) => {
							ids.push(value.id)
						})
						batchDelete({
							packageIdList: ids
						}).then(res => {
							if (res && res.code == '0000') {
								this.selectionList = [];
								if (this.tableData.length = len && this.page > 1) {
									this.loadByPage(this.page - 1);
								} else {
									this.loadByPage(this.page);
								}
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						})
					}
				});
			},
			//批量编辑
			packageBatchEdit() {
				var typeLen = 0;
				var item="";
				var len = this.selectionList.length;
				//判断类型是否一致
				this.selectionList.map((value,index) => {
					if(value.isTerminal!=item){
						typeLen=typeLen+1
					}
					item=value.isTerminal

				})
				if (len < 1) {
					this.$Message.warning('请至少选择一条记录');
					return
				} else if (typeLen > 1) {
					this.$Message.warning('选择套餐类型不一致');
				} else if (this.selectionList[0].isTerminal != '1' && this.selectionList[0].isTerminal != '2') {
					this.$Message.warning('选择套餐类型错误');
				} else if (this.selectionList[0].isTerminal == '1') {
					this.$Message.warning('终端厂商不允许批量编辑');
				} else {
					this.$refs["batchEditObj"].resetFields();
					this.batchEditFlag = true;
				}
				this.getSpeedTemplate()
			},
			//批量删套餐覆盖国家 弹窗
			allDeleteList() {
				this.deleteflag = true
				this.batchQueryTasks1(1)
			},
			// 批量编辑套餐覆盖国家 弹窗
			allPackageBatchEdit() {
				this.updateflag = true
				this.batchQueryTasks2(1)
			},
			// 批量删除 文件下载提示
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			// 批量修改 文件下载提示
			updatefileSuccess(response, updateFile, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			// 批量删除 上传文件错误
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			// 批量修改 上传文件错误
			updatehandleError(res, updateFile) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			// 批量删除 csv文件
			handleBeforeUpload(file, fileList) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: "文件格式不正确",
						desc: file.name + "格式不正确，请上传.csv格式文件"
					})
				} else {
					this.file = file,
					this.uploadList = fileList
				}
				return false
			},
			// 批量修改 csv文件
			updatehandleBeforeUpload(updateFile, fileList) {
				if (!/^.+(\.csv)$/.test(updateFile.name)) {
					this.$Notice.warning({
						title: "文件格式不正确",
						desc: updateFile.name + "格式不正确，请上传.csv格式文件"
					})
				} else {
					// if (updateFile.size > 5 * 1024 * 1024) {
					// 	this.$Notice.warning({
					// 		title: "文件大小超过限制",
					// 		desc: updateFile.name + "超过了最大限制范围5MB"
					// 	})
					// } else {
						this.updateFile = updateFile,
						this.uploadList = fileList
					// }
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			removeFile() {
				this.file = ''
			},
			// 批量删除 历史任务查看
			batchQueryTasks1: function(page) {
				var _this = this
				batchQueryTasks({
					pageNo: page,
					pageSize: this.pageSize,
					type: 2,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.pageTask = page
						this.totalTask = res.data.total
						this.taskdata = res.data.records
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
				})
			},
			// 批量修改历史任务查看
			batchQueryTasks2: function(page) {
				var _this = this
				batchQueryTasks({
					pageNo: page,
					pageSize: this.pageSize,
					type: 1,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.pageTaskUpdate = page
						this.totalTaskUpdate = res.data.total
						this.updateTaskdata = res.data.records
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
				})
			},
			// 跳转页码
			TaskgoPage: function(page) {
				this.batchQueryTasks1(page)
			},
			TaskgoPageUpdate: function(page){
				this.batchQueryTasks2(page)
			},

			updatefileUploading(event,updateFile, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			updateRemoveFile() {
				this.updateFile = ''
			},
			//批量删除套餐国家 导入文件确定
			delconfirmbatch: function() {
				this.$refs["formobj"].validate(valid => {
					if (valid) {
						this.importLoading = true
						let formData = new FormData()
						formData.append('file', this.file)
						formData.append('type', 2)
						allBatchDelete(formData).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.batchQueryTasks1(1)
								this.file = ''
								this.$refs['formobj'].resetFields()
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						}).finally(() => {
							this.importLoading = false
						})
					}
				})
			},
			//批量修改套餐国家 导入文件确定
			updateconfirmbatch: function() {
				this.$refs["updateFormobj"].validate(valid => {
					if (valid) {
						this.importLoading = true
						let formData = new FormData()
						formData.append('file', this.updateFile)
						formData.append('type', 1)
						allBatchUpdate(formData).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.batchQueryTasks2(1)
								this.updateFile = ''
								this.$refs['updateFormobj'].resetFields()
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						}).finally(() => {
							this.importLoading = false
						})
					}
				})
			},
			//模板下载
			downloadFile: function() {
				this.$refs.modelTable.exportCsv({
					filename: "批量删除套餐覆盖国家模板",
					// type:'xlsx',
					columns: this.modelColumns,
					data: this.modelData
				})
			},
			//模板下载
			updateDownloadFile: function() {
				this.$refs.updateModelTable.exportCsv({
					filename: "批量修改套餐覆盖国家模板",
					// type:'xlsx',
					columns: this.updateModelColumns,
					data: this.updateModelData
				})
			},
			//返回
			cancelModal: function() {
				this.deleteflag = false
				this.updateflag = false
				this.file = ''
				this.updateFile = ''
				this.$refs['formobj'].resetFields()
				this.$refs['updateFormobj'].resetFields()
				this.addRefuelModel = false
				this.exportModal=false
				this.isSupportedHotspotsList= []
				this.batchEditObj.packageConsumptions = [{ index: 0, consumption: "", unit: "MB", upccTemplateId: "" }]; // 重置用量值和模板
				this.index = 0; // 重置index
			},
			//批量删除 下载文件
			exportfile: function(row, fileType) {
				this.exporting = true
				batchQueryDownload({
					id: row.id,
					fileType: fileType
				}).then(res => {
					const content = res.data
					let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[1]) //获取到Content-Disposition;filename  并解码
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content, fileName)
					}
				}).catch(err => this.exporting = false)
			},
			//批量删除 审核通过/不通过
			delExamine(type, id) {
				this.$Modal.confirm({
					title: type == true ? '确认执行审核通过？' : '确认执行审核不通过？',
					onOk: () => {
						allCheckPackage({
							authStatus:type,
							id:id
							}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.batchQueryTasks1(1)
							} else {
								throw res
							}
						}).catch((err) => {
						})
					}
				});
			},
			//批量修改 审核通过/不通过
			updateExamine(type, id) {
				this.$Modal.confirm({
					title: type == true ? '确认执行审核通过？' : '确认执行审核不通过？',
					onOk: () => {
						allCheckPackage({
							authStatus:type,
							id:id
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.batchQueryTasks2(1)
							} else {
								throw res
							}
						}).catch((err) => {
						})
					}
				});
			},
			//国家/地区选择变更
			mccListChange(e) {
				this.batchEditObj.cardPool = [];
				this.cpcrvList = [];
				this.mccListTemp = ''
				this.$refs['batchEditObj'].validateField('cardPool');
			},
			//卡池编辑窗口
			cardPoolEdit() {
				this.filterSearchObj = {
					'cpName': '',
					'sName': '',
					'cName': ''
				};
				// this.totalPool = this.totalTempPool.concat();
				//筛选比例集合-筛选所有信息 -> filterPool
				this.filterRateList("", "", "", "all", "edit");
				this.drawer = false;
				this.cardPoolEditFlag = true;
			},
			//搜索
			doCPTreeFilter() {
				this.cardPoolEditTreeLoad = true;
				var this_ = this;
				//前一次过滤数据汇总至->this.totalPool
				this.saveTreeIntoTotalPool();
				//根据过滤条件进行过滤
				this.filterRateList(this.filterSearchObj.cpName, this.filterSearchObj.sName, this.filterSearchObj.cName,
					"all",
					"edit");
				setTimeout(function() {
					this_.cardPoolEditTreeLoad = false;
				}, 500);
			},
			//存储值 搜索/编辑保存 进行前一次过滤数据汇总
			saveTreeIntoTotalPool() {
				//filterPool --> into --> totalPool
				if (this.totalPool.length > 0) {
					//优化函数->组装数据
					//获取树值做逻辑判断
					var filterMap = new Map();
					this.filterPool.map((filterParent, index) => {
						filterParent.children.map((filterChild, index) => {
							if (filterChild.rate != null && filterChild.rate != 0) {
								//添加新的key-value
								//key:卡池id+国家/地区mcc value:比例值
								filterMap.set(filterParent.id + filterChild.mcc, filterChild.rate);
							}
						});
					});
					//判断赋值
					this.totalPool.map((totalParent, index) => {
						totalParent.children.map((totalChild, index) => {
							if (filterMap.has(totalParent.id + totalChild.mcc)) {
								totalChild.rate = filterMap.get(totalParent.id + totalChild.mcc);
							}
						});
					});
				}
			},
			//编辑确认
			cardPoolEditConfirm() {
				this.filterRateList("", "", "", "filled", "show");
				this.cardPoolEditFlag = false;
				this.drawer = true;
			},
			//关联卡池
			loadCardPoolView(countryList) {
				if (this.mccListTemp == JSON.stringify(countryList)) {
					//加载卡池集合数据->totalPool
					this.loadTreeData(this.batchEditObj.cardPool);
					//筛选比例集合-筛选已填信息->过滤totalPool
					this.filterRateList("", "", "", "filled", "show");
					this.batchEditFlag = false;
					//弹出抽屉
					this.drawer = true;
				} else {
					this.mccListTemp = JSON.stringify(countryList);
					cardPoolRatio({
						usageType: this.selectionList[0].isTerminal == '1' ? '3' : '1',
						mccList: this.batchEditObj.mccList,
						packageId: null,
					}).then(res => {
						if (res && res.code == '0000') {
							var data = res.data;
							//加载卡池集合数据
							this.batchEditObj.cardPool = data.data;
							this.batchEditObj.cardPool.sort(function(str1, str2) {
								return str1.poolName.localeCompare(str2.poolName);
							});
							//加载卡池集合数据->totalPool
							this.loadTreeData(this.batchEditObj.cardPool);
							//筛选比例集合-筛选已填信息->过滤totalPool
							this.filterRateList("", "", "", "filled", "show");
							this.batchEditFlag = false;
							//弹出抽屉
							this.drawer = true;
						} else {
							throw res
						}
					}).catch((err) => {

					}).finally(() => {

					})
				}
			},
			//筛选比例集合
			filterRateList(cpName, sName, cName, type, operateType) {
				//cpName-卡池名称
				//sName-供应商名称
				//cName-国家/地区名称
				//type-过滤方式 all/filled
				var cardPoolList = [];
				if (this.totalPool.length > 0) {
					this.totalPool.map((cp, index) => {
						//匹配卡池名称
						var cpNameFlag = cp.poolName != null ? cp.poolName.indexOf(cpName) != -1 : false;
						//匹配供应商名称
						var sNameFlag = cp.supplierName != null ? cp.supplierName.indexOf(sName) != -1 : false;
						var parent = {
							title: cp.title,
							id: cp.id,
							poolName: cp.poolName,
							supplierName: cp.supplierName,
							expand: true,
							children: []
						};
						//满足卡池名称&供应商名称匹配
						if (cpNameFlag && sNameFlag) {
							cp.children.map((obj, index) => {
								//匹配国家/地区名称
								var cNameFlag = obj.countryEn != null ? obj.countryEn.indexOf(cName) != -
									1 : false;
								if (cNameFlag) {
									if ('all' == type) {
										parent.children.push(obj);
									}
									if ('filled' == type && obj.rate != null && obj.rate != "") {
										parent.children.push(obj);
									}
								}
							});
						}
						if (parent.children.length > 0) {
							cardPoolList.push(parent);
						}
					});
				}
				if (cardPoolList.length != 0) {
					if ('edit' == operateType) {
						this.cardPoolEditTree = [{
							title: '关联卡池',
							expand: true,
							children: []
						}];
						this.cardPoolEditTree[0].children = cardPoolList.concat();
						this.filterPool = cardPoolList.concat();
						this.$forceUpdate();
					}
					if ('show' == operateType) {
						this.cardPoolTree = [{
							title: '关联卡池',
							expand: true,
							children: []
						}];
						this.cardPoolTree[0].children = cardPoolList.concat();
						this.filterPool = cardPoolList.concat();
						this.$forceUpdate();
					}
				} else {
					this.cardPoolEditTree = [];
					this.cardPoolTree = [];
					this.filterPool = [];
				}
			},
			//加载卡池树数据
			loadTreeData(cpObj) {
				let pushArr = [];
				let len = cpObj.length;
				try {
					for (var i = 0; i < len; i++) {
						let index = i;
						let cp = cpObj[index];
						let obj = {
							title: cp.poolName + "-(" + cp.supplierName + ")",
							id: cp.poolId,
							poolName: cp.poolName,
							supplierName: cp.supplierName,
							expand: true,
							children: []
						};
						if (cp.regionList && cp.regionList.length > 0) {
							for (var n = 0; n < cp.regionList.length; n++) {
								let nIndex = n;
								let region = cp.regionList[nIndex];
								obj.children.push({
									expand: true,
									poolId: cp.poolId,
									poolName: cp.poolName,
									supplierName: cp.supplierName,
									countryCn: region.countryCn,
									countryTw: region.countryTw,
									countryEn: region.countryEn,
									mcc: region.mcc,
									rate: region.rate,
									render: (h, {
										root,
										node,
										data
									}) => {
										return h('div', {
											style: {
												display: 'flex',
												width: '100%',
												height: '25px',
												flexDirection: 'row',
												alignItems: 'center'
											}
										}, [
											h('Tooltip', {
												props: {
													placement: 'left',
													content: cp.regionList[nIndex].countryEn
												},
												style: {
													width: '100px',
													display: 'inline-block',
												}
											}, [
												h('div', {
													style: {
														width: '100px',
														height: '25px',
														display: 'inline-block',
														overflow: 'hidden',
														textOverflow: 'ellipsis',
														whiteSpace: 'nowrap',
														lineHeight: '30px'
													}
												}, cp.regionList[nIndex].countryEn + '：')
											]),
											h('input', {
												domProps: {
													type: 'Number',
													value: cp.regionList[nIndex].rate ==
														undefined ? null : cp.regionList[nIndex]
														.rate,
													placeholder: '请输入分配比列(%)',
													max: 100,
													min: 0
												},
												style: {
													width: '150px',
													height: '20px',
													// float: 'left',
													textAlign: 'center',
													border: '#ccc 1px solid',
													borderRadius: '5px',
													mozBorderRadius: '5px',
													webkitBorderRadius: '5px',
													marginLeft: '8px'
												},
												on: {
													input: (e) => {
														var v = e.target.value;
														cpObj[index].regionList[nIndex].rate =
															v;
														obj.children[nIndex].rate = v;
													}
												}
											})
										]);
									},
								})
							}
						}
						pushArr.push(obj);
					}
					//设置总数据集合
					this.totalPool = pushArr;
				} catch (e) {
					//设置总数据集合
					this.totalPool = [];
				}
			},
			//抽屉关闭
			drawerClose() {
				this.batchEditObj.cardPool = [];
				this.cpcrvList = [];
				this.mccListTemp = ''
				this.$refs['batchEditObj'].validateField('cardPool');
				this.drawer = false;
				this.batchEditFlag = true;
			},
			// 获取资源供应商
			getsupplier(){
				supplier({
					pageNum:-1,
					pageSize:-1,
				}).then(res => {
					if (res.code == '0000') {
						this.supplierList = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				})
			},
			//卡池提交
			toSetCardPool() {
				var cardPoolList = [];
				var poolMccList = [];
				if (this.totalPool.length > 0) {
					//获取树值做逻辑判断
					this.totalPool.map((cardPool, index) => {
						cardPool.children.map((rateObj, index) => {
							if (rateObj.rate != null && rateObj.rate != 0) {
								cardPoolList.push({
									poolId: rateObj.poolId,
									poolName: rateObj.poolName,
									mcc: rateObj.mcc,
									rate: String(rateObj.rate)
								});
								poolMccList.push(rateObj.mcc);
							}
						});
					});
				}

				//参数校验
				var reg = new RegExp("^(\\d|[0-9]\\d|100)$");
				var submitFlag = true;
				for (var i = 0; i < cardPoolList.length; i++) {
					//限制0-100整数输入
					if (!reg.test(cardPoolList[i].rate) && cardPoolList[i].rate != '') {
						this.$Notice.warning({
							title: '操作提示',
							desc: '分配比输入错误(仅支持0-100)'
						});
						submitFlag = false;
						return false;
					}
				}

				//分组
				var groups = [];
				cardPoolList.map((item, index) => {
					var mcc = item.mcc;
					if (!groups[mcc]) {
						groups[mcc] = [];
					}
					groups[mcc].push({
						key: index,
						value: Number(item.rate)
					});
				});
				//判断比例值 唯一默认100%
				for (var num in groups) {
					var group = groups[num];
					if (group.length == 1) {
						cardPoolList[group[0].key].rate = '100';
					} else {
						var total = 0;
						group.map((item, index) => {
							total = total + item.value;
						});
						if (total != 100) {
							var name = this.localMap.has(num) ? this.localMap.get(num) : '各国家';
							this.$Notice.warning({
								title: '操作提示',
								desc: name + '分配比需满足100%'
							})
							submitFlag = false;
							//跳出group循环
							return false;
						}
					}

					//跳出groups循环
					if (!submitFlag) {
						return false;
					}
				}
				//勾选国家
				var mccList = this.batchEditObj.mccList;
				//判断所选国家是否匹配完全
				for (var j = 0; j < mccList.length; j++) {
					if (poolMccList.indexOf(mccList[j]) == -1) {
						var name = this.localMap.has(mccList[j]) ? this.localMap.get(mccList[j]) : '存在国家/地区';
						this.$Notice.warning({
							title: '操作提示',
							desc: name + '未分配比例'
						})
						submitFlag = false;
						return false;
					}
				}

				if (!submitFlag) {
					return false;
				} else {
					//提交逻辑
					this.cpcrvList = cardPoolList;
					this.$refs['batchEditObj'].validateField('cardPool');
					this.drawer = false;
					this.batchEditFlag = true;
				}
			},
			//速度模板查询
			getSpeedTemplate(){
				getTemplate().then(res => {
					if (res && res.code == '0000') {
						this.TemplateList = res.data;
						this.isSupportedHotspots = this.TemplateList
					} else {
						throw res
					}
				}).catch((err) => {})
			},
			//删除用量值
			removeConsumption(index){
				this.batchEditObj.packageConsumptions.splice(index, 1);
				this.index--;
			},
			//添加用量值
			addConsumption(){
				this.index++
				this.batchEditObj.packageConsumptions.push({
					index: this.index,
					consumption: "",
					unit: "MB",
					upccTemplateId: "",
				});
			},
			// 新增辅助函数：检查用量值的数值是否有效
			isConsumptionValueValid(consumption, unit) {
				consumption = String(consumption || '').trim();

				if (!consumption || !unit) {
					return false; // 用量值或单位为空，视为无效
				}

				const integerRegex = /^[1-9]\d{0,9}$/;
				if (!integerRegex.test(consumption)) {
					return false; // 格式不正确
				}

				let consumptionMB;
				try {
					let baseValue = BigInt(consumption);
					switch (unit) {
						case 'TB':
							consumptionMB = baseValue * 1024n * 1024n;
							break;
						case 'GB':
							consumptionMB = baseValue * 1024n;
							break;
						case 'MB':
							consumptionMB = baseValue;
							break;
						default:
							return false; // 无效单位
					}
				} catch (e) {
					return false; // 转换失败
				}

				const minMB = 10n;
				const maxMB = 9999999999n;

				if (consumptionMB < minMB || consumptionMB > maxMB) {
					return false; // 超出范围
				}

				return true;
			},
			// 新增辅助函数：在提交前对所有用量值进行严格校验
			validateAllPackageConsumptionsForSubmission() {
				// 不再对 packageConsumptions 进行排序，直接在原始顺序上进行校验

				// 用于记录已经存在的用量值+单位的组合（转换为MB），以便进行唯一性检查
				const existingConsumptionsMB = new Set();

				for (let i = 0; i < this.batchEditObj.packageConsumptions.length; i++) {
					const currentItem = this.batchEditObj.packageConsumptions[i];
					const consumption = String(currentItem.consumption || '').trim();
					const unit = currentItem.unit;
					const templateId = currentItem.upccTemplateId;

					// 第一遍遍历：进行基础格式、范围和完整性检查（用量值、单位、模板都必须存在或都为空）
					if (consumption) {
						// 1. 检查用量值的数值合法性 (format, min/max range)
						if (!this.isConsumptionValueValid(consumption, unit)) {
							this.$Notice.error({
								title: '提交校验失败',
								desc: `第 ${i + 1} 行用量值 ${consumption}${unit || ''} 不符合要求（请输入1-10位整数数字，最小10MB，最大9999999999MB），请修正后再提交。`
							});
							return false; // 数值无效，阻止提交
						}

						// 2. 如果用量值数值合法，则其单位和模板都必须存在
						if (!unit) {
							this.$Notice.error({
								title: '提交校验失败',
								desc: `第 ${i + 1} 行用量值 ${consumption} 缺少单位，请选择。`
							});
							return false;
						}
						if (!templateId) {
							this.$Notice.error({
								title: '提交校验失败',
								desc: `第 ${i + 1} 行用量值 ${consumption}${unit} 缺少模板，请选择。`
							});
							return false;
						}

						// 3. 检查用量值+单位组合的唯一性
						const currentConsumptionMB = this.convertToMB(consumption, unit);
						if (existingConsumptionsMB.has(currentConsumptionMB)) {
							this.$Notice.error({
								title: '提交校验失败',
								desc: `用量值 ${consumption}${unit} 组合重复，请修正后再提交。`
							});
							return false;
						}
						existingConsumptionsMB.add(currentConsumptionMB);

						// 4. 检查用量值的递增逻辑 (与上一行比较)
						if (i > 0) {
							const prevItem = this.batchEditObj.packageConsumptions[i - 1];
							const prevConsumption = String(prevItem.consumption || '').trim();
							const prevUnit = prevItem.unit;

							// 仅在前一个有效时进行比较
							if (prevConsumption && prevUnit && prevItem.upccTemplateId) {
								const prevConsumptionMB = this.convertToMB(prevConsumption, prevUnit);
								if (currentConsumptionMB <= prevConsumptionMB) {
									this.$Notice.error({
										title: '提交校验失败',
										desc: `用量值逻辑不正确，第 ${i + 1} 行用量值 ${consumption}${unit} 必须大于上一档次的用量 ${prevConsumption}${prevUnit}。请修正后再提交。`
									});
									return false;
								}
							}
						}

					} else {
						// 如果用量值为空，但单位或模板不为空，则认为是不完整的行
						if (unit || templateId) {
							this.$Notice.error({
								title: '提交校验失败',
								desc: `存在用量值为空但单位或模板不为空的行，请填写完整或删除该行。`
							});
							return false;
						}
					}
				}

				return true; // 所有严格校验都通过
			},
		},
		mounted() {
			this.init();
		}
	}
</script>

<style scoped="scoped">
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		margin: 0 10px;
		width: 105px;
	}

	.search_box_label_flag {
		font-weight: bold;
		text-align: center;
		margin: 0 10px;
		width: 150px;
	}

	.search_box {
		width: 340px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.demo-drawer-footer {
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		border-top: 1px solid #e8e8e8;
		padding: 10px 16px;
		text-align: right;
		background: #fff;
	}

	.demo-spin-article {
		height: 300px;
		width: 100%;
		display: inline-block;
		position: relative;
		/* border: 1px solid #eee; */
	}
	.inputSty {
		width: 200px;
	}
</style>
