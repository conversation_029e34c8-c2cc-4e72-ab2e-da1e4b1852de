<template>
	<!-- 定向应用管理 -->
	<Card>
		<!-- 搜索条件 -->
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">应用名称:</span>
				<Input v-model.trim="appName" clearable placeholder="请输入应用名称" style="width: 250px;" />
			</div>
			<div style="width: 110px; display: flex;justify-content: center; margin-bottom: 20px;">
				<Button type="primary" icon="md-search" :loading="searchloading" v-has="'search'"
					@click="searchOne()">搜索</Button>
			</div>
			<div style="width: 110px; display: flex;justify-content: center; margin-bottom: 20px;">
				<Button type="info" icon="md-add" v-has="'add'" @click="addItem">新增</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table :columns="columns" :data="data" style="width:100%; margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button type="primary" ghost style="margin-right: 20px" v-has="'update'" @click="updateItem(row)">修改</Button>
				<Button type="error" ghost v-has="'delete'" @click="delItem(row)">删除</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 新增/编辑弹窗 -->
		<Modal v-model="modal1" :title="title" @on-cancel="cancelModal" :mask-closable="false" width="670px">
			<Form ref="formObj" :model="formObj"  :rules="ruleAddValidate" :label-width="130" style="padding: 10px;">
				<FormItem label="应用名称:" prop="appName">
					<Input v-model.trim="formObj.appName" clearable placeholder="请输入应用名称" style="width: 350px;" />
				</FormItem>
				<FormItem label="RG值" prop="rg">
					<Input v-model.trim="formObj.rg" maxlength="10" clearable placeholder="请输入RG值" style="width: 350px;" />
				</FormItem>
				<div style="display: flex; flex-wrap: nowrap;">
					<div>
						<div v-for="(item,index) in formObj.highSpeedUpccTemplate" :key="index" style="display: flex;">
							<FormItem label="UPCC签约模板" :prop="'highSpeedUpccTemplate.' + index+ '.templateId'"
							:rules="{required: true, message: 'UPCC模板不能为空', trigger: 'blur'}">
								<Select filterable v-model="item.templateId" style="width: 350px;" placeholder="请选择UPCC签约模板" clearable>
									<Option v-for="(item1, index1) in choseUpccTemplateId(item.templateId)" :title="item1.templateName" :value="item1.templateId" :key="item1.templateId">{{item1.templateName.length > 40 ? item1.templateName.substring(0,40) : item1.templateName}}</Option>
								</Select>
							</FormItem>
							<Button type="error" size="small" style="margin-left: 10px; margin-top: 5px; width: 40px; height: 25px;" @click="removeTemplate(index)"  v-if="(typeFlag == 'Add' && index != 0) || (typeFlag == 'Update' && index != (formObj.highSpeedUpccTemplate.length -1))">删除</Button>
						</div>
					</div>
					<div>
						<Button type="info" size="small" style="margin-left: 10px; margin-top: 5px;" @click="addTemplate">增加</Button>
					</div>
				</div>
				<FormItem label="低速UPCC签约模板" prop="lowSpeedUpccTemplate">
					<Select filterable v-model="formObj.lowSpeedUpccTemplate" style="width: 350px;" placeholder="请选择低速UPCC签约模板" clearable>
						<Option v-for="(item2,index2) in upccTemplateList" :title="item2.templateName" :value="item2.templateId" :key="item2.templateId">{{item2.templateName.length > 40 ? item2.templateName.substring(0,40) : item2.templateName}}</Option>
					</Select>
				</FormItem>
			</Form>
			<div slot="footer" class="footer_wrap">
				<Button icon="ios-arrow-back" @click="cancelModal">返回</Button>
				<Button type="primary" :loading="submitLoading" @click="submit">确定</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		getDirectionalApp,
		newDirectionalApp,
		updateDirectionalApp,
		delItem
	} from '@/api/package/targetedApplications'
	import {upccList} from '@/api/package/upcc'
	export default {
		data() {
			return {
				total: 0,
				currentPage: 1,
				page: 0,
				appName: "", //应用名称
				loading: false,
				searchloading: false, //查询加载
				data: [], //表格列表
				columns: [{
					title: "应用名称",
					key: 'appName',
					minWidth: 150,
					align: 'center',
					tooltip: true
				}, {
					title: "RG值",
					key: 'rg',
					minWidth: 150,
					align: 'center',
					tooltip: true
				}, {
					title: "UPCC签约模板",
					key: 'highSpeedUpccTemplate',
					minWidth: 250,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row;
						let text = ""
						if (row.highSpeedUpccTemplate != null) {
							text = row.highSpeedUpccTemplate[0].templateName
						}
						if (row.highSpeedUpccTemplate !=null && row.highSpeedUpccTemplate.length > 1) {
							return h('div', [
								h('Tooltip', {
										props: {
											placement: 'bottom',
											transfer: true,
										},
										style: {
											cursor: 'pointer',
										}
									},
									[
										h('span', {
											style: {
												display: "block",
											}
										}, row.highSpeedUpccTemplate[0].templateName),
										h('span', {
											style: {
												display: "block",
											}
										}, row.highSpeedUpccTemplate[1].templateName),
										h('span', {
											style: {
												display: "block",
											}
										}, "……"),
										h('ul', {
											slot: 'content',
											style: {
												whiteSpace: 'normal',
												wordBreak: 'break-all',//超出隐藏
												listStyle: 'none',//去掉无序小黑点
											},
										}, this.data[params.index].highSpeedUpccTemplate.map(
										item => {
											return h('li', item.templateName)
										}))
									]
								)
							])
						} else {
							text = text;
							return h('label', text)
						};
					}
				},  {
					title: "操作",
					slot: 'action',
					minWidth: 180,
					align: 'center',
					fixed: 'right'
				}, ],
				// ——————————**新增/编辑**————————————
				modal1: false,
				submitLoading: false,
				typeFlag: "",
				title: "",
				index: 0,
				formObj: {
					appName: "",
					rg: "",
					highSpeedUpccTemplate: [{
						index: 0,
						templateId: ''
					}],
					lowSpeedUpccTemplate: ''
				},
				id: '',
				upccTemplateList: [],//选择UPCC模板列表
				ruleAddValidate: {
					appName: [{
						required: true,
						type: 'string',
						message: '应用名称不能为空',
					}],
					rg: [{
						required: true,
						message: 'RG值不能为空',
					}, {
						pattern: /^[0-9]\d*$/,
						message: '请输入纯数字',
						trigger: 'blur'
					},],
					lowSpeedUpccTemplate: [{
						required: true,
						type: 'string',
						message: '低速UPCC签约模板不能为空',
					}]
				}
			}
		},

		mounted() {
			this.goPageFirst(1)
			this.getUpccTemplateList()
		},
		computed: {
			// UPCC模板不能重复选择，去掉已经选择的选项
			choseUpccTemplateId() {
				return (val) => {
					// 将option的显示进行深拷贝
					let newList = JSON.parse(JSON.stringify(this.upccTemplateList));
					// 处理highSpeedUpccTemplate数据，返回一个arr
					// arr数组相当于所有selected选中的数据集合
					const arr = this.formObj.highSpeedUpccTemplate.map(item => {
						// 将{templateId: '1'}变为【'1'】,方便indexOf
						return (item = item.templateId);
					});
					//过滤出newList里面展示的数据
					newList = newList.filter(item => {
						if (val == item.templateId) {
							return item;
						} else {
							if (arr.indexOf(item.templateId) == -1) {
								return item;
							}
						}
					});
					return newList;
				};
			},
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				getDirectionalApp({
					pageNum: page,
					pageSize: 10,
					appName: this.appName,
					corpId: this.$store.state.user.userId
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.data.total
						this.data = res.data.records
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			searchOne: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			// ————————————————————————————****新增/修改应用****————————————————————————
			//新增应用
			addItem() {
				this.typeFlag= 'Add'
				this.title= "新增应用"
				this.modal1= true
			},
			//修改应用
			updateItem(row) {
				this.typeFlag= 'Update'
				this.title= "修改应用"
				this.formObj = JSON.parse(JSON.stringify(row))
				this.modal1= true
				this.id = row.id
				let newArr = JSON.parse(JSON.stringify(row.highSpeedUpccTemplate))
				newArr.forEach((i)=>{
					delete i.rate
					delete i.unit
					delete i.templateName
				})
				this.$set(this.formObj, "highSpeedUpccTemplate", newArr);
			},
			//删除应用
			delItem(row) {
				let data = {
					id: row.id,
					appName: row.appName
				}
				this.$Modal.confirm({
				  title: '确认删除？',
				  onOk: () => {
				    delItem(data).then(res => {
				      if (res && res.code == '0000') {
				        this.$Notice.success({
				          title: '操作提示',
				          desc: '操作成功'
				        })
				        this.goPageFirst(1)
				      } else {
				        throw res
				      }
				    }).catch((err) => {
				      this.$Notice.error({
				        title: '操作提示',
				        desc: '操作失败'
				      })
				    })
				  }
				});
			},
			//删除UPCC模板
			removeTemplate(index) {
				this.formObj.highSpeedUpccTemplate.splice(index, 1);
				this.index--;
			},
			//增加UPCC模板
			addTemplate() {
				this.index++;
				if (this.formObj.highSpeedUpccTemplate.length < this.upccTemplateList.length) {
					this.formObj.highSpeedUpccTemplate.push({
						index: this.index,
						templateId: "",
					});
				} else {
					this.$Notice.open({
						title: '操作提醒：',
						desc: '不能再添加了 ！',
					});
				}
			},
			// 取消
			cancelModal() {
				this.modal1= false
				this.$refs['formObj'].resetFields()
				this.formObj.highSpeedUpccTemplate= [{
					index: 0,
					templateId: ''
				}]
				this.id = ''
			},
			//提交upcc模板
			submit() {
				this.$refs["formObj"].validate((valid) => {
					if (valid) {
						this.submitLoading = true;
						let arr = []
						this.formObj.highSpeedUpccTemplate.forEach((i)=>{
							arr.push(i.templateId)
						})
						let fun = this.typeFlag == 'Add' ? newDirectionalApp : updateDirectionalApp
						fun({
							"id": this.id,
							"appName": this.formObj.appName,
							"rg": Number(this.formObj.rg),
							"highSpeedUpccTemplate": arr,
							"lowSpeedUpccTemplate": this.formObj.lowSpeedUpccTemplate
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								});
								setTimeout(() => {
									this.submitLoading = false;
									this.cancelModal();
									this.goPageFirst(1)
								}, 1500);
							} else {
								this.submitLoading = false;
								throw res
							}
						}).catch((err) => {
							this.submitLoading = false;
						})
					}
				})
			},
			//UPCC模板查询
			getUpccTemplateList() {
				upccList({
					pageNum: -1,
					pageSize: -1,
				}).then(res => {
					if (res && res.code == '0000') {
						this.upccTemplateList = res.data.records;
					} else {
						throw res
					}
				}).catch((err) => {})
			},
		}
	}
</script>

<style scoped="scoped">
	.search_head_i {
		margin-top: 30px;
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 90px;
	}

	.footer_wrap {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
