<template>
	<!-- UPCC速度模板管理 -->
	<Card>
		<!-- 搜索条件 -->
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">模板ID:</span>
				<Input v-model="templateId" clearable placeholder="请输入模板ID" style="width: 200px;" />
			</div>
			<div class="search_box">
				<span class="search_box_label">模板名称:</span>
				<Input v-model="templateName" clearable placeholder="清输入模板名称" style="width: 200px;" />
			</div>
			<div class="search_box">
				<span class="search_box_label">模板描述:</span>
				<Poptip trigger="focus">
					<Input v-model="templateDesc" clearable placeholder="请输入模板描述" style="width: 200px" />
					<template #content>
						<div>{{ formatTemplateDesc }}</div>
					</template>
				</Poptip>
			</div>
			<div class="search_box">
				<span class="search_box_label">是否支持热点:</span>
				<Select v-model="supportHotspot" :clearable="true" style="width: 200px;">
					<Option value="1">是</Option>
					<Option value="2">否</Option>
				</Select>
			</div>
			<div class="search_box" style="width: 120px;padding-left: 20px;">
				<Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search()">搜索</Button>
				&nbsp;&nbsp;&nbsp;&nbsp;<Button v-has="'add'" type="info" icon="md-add" :loading="addloading"
					style="margin-left: 10px;" @click="addUpcc()">新增</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table ref="selection" :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'update'" type="primary" ghost style="margin-right: 10px;" @click="updateUpcc(row)">编辑</Button>
				<Button v-has="'delete'" type="error" ghost style="margin-right: 10px;" @click="deleteUpcc(row)">删除</Button>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 新增/编辑模板 -->
		<Modal :title="title" v-model="upccModal" :mask-closable="true" @on-cancel="cancelModal" width="500px">
			<Form ref="upccform" :model="upccform" :rules="rule" :label-width="120" style="font-size: 600;">
				<FormItem label="模板名称" prop="templateName">
					<Input v-model="upccform.templateName" placeholder="请输入模板名称" maxlength="1000" clearable
						style="width: 300px"></Input>
				</FormItem>
				<FormItem label="模板描述" prop="templateDesc">
					<Input v-model="upccform.templateDesc" placeholder="请输入模板描述" maxlength="3000" type="textarea"
						:rows="5" clearable style="width: 300px"></Input>
				</FormItem>
				<FormItem label="签约模板ID" prop="signId">
					<Input v-model="upccform.signId" placeholder="请输入模板ID" clearable
						style="width: 300px"></Input>
				</FormItem>
				<FormItem label="是否支持热点" prop="supportHotspot">
					<Select v-model="upccform.supportHotspot" :disabled="title == name2" style="width: 300px;" clearable>
						<Option value="1">是</Option>
						<Option value="2">否</Option>
					</Select>
				</FormItem>
				<FormItem label="模板速率" prop="rate">
					<Input v-model="upccform.rate" placeholder="请输入模板速率" maxlength="4" clearable style="width: 300px">
						<Select v-model="upccform.unit" slot="append" style="width: 70px">
						    <Option value="1">Kb/s</Option>
						    <Option value="2">Mb/s</Option>
						</Select>
					</Input>
				</FormItem>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">返回</Button>
				<Button type="primary" @click="besure" :loading="besureLoading">确定</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		upccList,
		del,
		addUpccList,
		updateUpccList
	} from '@/api/package/upcc'
	export default {
		data() {
			return {
				total: 0,
				currentPage: 1,
				page: 0,
				templateId: "", //模板ID
				templateName: "", //模板名称
				templateDesc: "", //模板描述
				supportHotspot: "", //是否支持热点
				title: "",
				name1: "新增UPCC模板",
				name2: "修改UPCC模板",
				upccform: {
					templateName: "",
					templateDesc: "",
					signId: "",
					supportHotspot: "",
					rate: "",
					unit: "2",
				},
				rule: {
					templateName: [{
						required: true,
						message: "模板名称不可为空",
						trigger: 'change',
					},{
						pattern: /^[^\s]+(\s+[^\s]+)*$/,
						trigger: "blur",
						message: '模板名称有空格',
					}],
					templateDesc: [{
						required: true,
						message: "模板描述不可为空",
						trigger: 'blur',
					}],
					signId: [{
						required: true,
						message: "签约模板ID不可为空",
						trigger: 'blur',
					},{
						pattern: /^[^\s]+(\s+[^\s]+)*$/,
						trigger: "blur",
						message: '签约模板ID有空格',
					}],
					supportHotspot: [{
						required: true,
						message: "是否支持热点不可为空",
						trigger: 'blur',
					}],
					rate: [{
						required: true,
						message: "模板速率不可为空",
					},{
						pattern: /^[0-9]+$/, 
						message: '请输入非负纯数字', 
						trigger: 'blur' 
					}],
				},
				loading: false,
				searchloading: false, //查询加载
				addloading: false, //新增加载
				besureLoading: false, //确定加载
				upccModal: false, //新增/编辑对话框
				data: [], //表格列表
				columns: [{
					title: "模板ID",
					key: 'templateId',
					minWidth: 120,
					align: 'center',
					tooltip: true
				}, {
					title: "模板名称",
					key: 'templateName',
					minWidth: 220,
					align: 'center',
					tooltip: true
				}, {
					title: "模板描述",
					key: 'templateDesc',
					minWidth: 220,
					align: 'center',
					render: (h, params) => {
						const row = params.row;
						let text = row.templateDesc === "" ? "" : row.templateDesc;
						if (text.length > 20) {
							text = text.substring(0, 20) + "...";
							return h('div', [h('Tooltip', {
									props: {
										placement: 'bottom',
										transfer: true
									},
									style: {
										cursor: 'pointer',
									},
								},
								[
									text,
									h('label', {
											slot: 'content',
											style: {
												whiteSpace: 'normal',
												wordBreak: 'break-all' //超出隐藏
											},
										},
										row.templateDesc
									)
								])]);
						} else {
							text = text;
							return h('label', text);
						}
					}
				}, {
					title: "是否支持热点",
					key: 'supportHotspot',
					minWidth: 110,
					align: 'center',
					render: (h, params) => {
						const row = params.row;
						const text = row.supportHotspot == '1' ? '是' : row.supportHotspot == '2' ? '否' : '';
						return h('label', text)
					}
				}, {
					title: "签约模板ID",
					key: 'signId',
					minWidth: 150,
					align: 'center',
					render: (h, params) => {
						const row = params.row
						var text = row.signId
						let length = text === "" || text === null ? 0 : text.length
						if(length > 20){
							let content = ""
							for(let i=0;i<=row.signId.length;){
								content=content+text.slice(i, i+17)+','
								i=i+18
							}
							text = text.substring(0, 20) + "..."
							return h('div', [h('Tooltip', {
								props: {
									placement: 'bottom',
									transfer: true //是否将弹层放置于 body 内
								},
								style: {
									cursor: 'pointer',
					            },
							},
							[ //这个中括号表示是Tooltip标签的子标签
								text, //表格列显示文字
					           h('label', {
									slot: 'content',
									style: {
										whiteSpace: 'normal'
									},
									domProps: {
										innerHTML: content.replace(/\,/g, "</br>")
									},
								}, )
							])
						  ]);
						}else {
							return h('label', text)
						}
					}
				}, {
					title: "模板速率",
					key: 'rate',
					minWidth: 100,
					align: 'center',
					render: (h, params) => {
						const row = params.row;
						const text = row.unit == '1' ? row.rate + 'Kb/s' : row.rate + 'Mb/s'
						return h('label', text)
					}
				}, {
					title: "操作",
					slot: 'action',
					minWidth: 180,
					align: 'center',
					fixed: 'right'
				}, ]
			}
		},
		mounted() {
			this.goPageFirst(1)
		},
		computed: {
			formatTemplateDesc() {
				if (this.templateDesc === '') {
					return '请输入模板描述';
				} else {
					return this.templateDesc;
				}
			}
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				upccList({
					pageNum: page,
					pageSize: 10,
					templateId: this.templateId,
					templateName: this.templateName,
					templateDesc: this.templateDesc,
					supportHotspot: this.supportHotspot
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.data = res.data.records
						this.total = res.data.total
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			//新增upcc模板
			addUpcc() {
				this.title = this.name1
				this.upccModal = true
			},
			//修改upcc模板
			updateUpcc(row) {
				this.title = this.name2
				this.upccModal = true
				this.upccform.templateName = row.templateName
				this.upccform.templateDesc = row.templateDesc
				this.upccform.signId = row.signId
				this.upccform.supportHotspot = row.supportHotspot
				this.upccform.rate = row.rate
				this.upccform.unit = row.unit
				this.templateIds = row.templateId
			},
			//删除upcc模板
			deleteUpcc(row) {
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						del({
							templateId: row.templateId,
							signId: row.signId,
							templateName: row.templateName,
							templateDesc: row.templateDesc,
							supportHotspot: row.supportHotspot,
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.page = 1
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.error(err)
						})
					}
				});
			},
			besure() {
				this.$refs["upccform"].validate(valid => {
					if (valid) {
						if (this.title == this.name1) {
							this.besureLoading = true
							addUpccList({
								templateName: this.upccform.templateName,
								templateDesc: this.upccform.templateDesc,
								signId: this.upccform.signId,
								supportHotspot: this.upccform.supportHotspot,
								rate: this.upccform.rate,
								unit: this.upccform.unit,
							}).then(res => {
								if (res.code === '0000') {
									let data = res.data
									this.$Notice.success({
										title: "操作提示",
										desc: '操作成功',
									})
									this.besureLoading = false
									this.goPageFirst(1)
									this.upccModal = false
									this.cancelModal()
								}
							}).catch((error) => {
								this.besureLoading = false
								console.log(error)
							}).finally(() => {
								this.besureLoading = false
							})
						} else {
							//修改upcc模板
							this.besureLoading = true
							updateUpccList({
								templateName: this.upccform.templateName,
								templateDesc: this.upccform.templateDesc,
								templateId: this.templateIds,
								signId: this.upccform.signId,
								supportHotspot: this.upccform.supportHotspot,
								rate: this.upccform.rate,
								unit: this.upccform.unit,
							}).then(res => {
								if (res.code === '0000') {
									let data = res.data
									this.$Notice.success({
										title: "操作提示",
										desc: '操作成功',
									})
									this.besureLoading = false
									this.goPageFirst(1)
									this.upccModal = false
									this.cancelModal()
								}
							}).catch((error) => {
								this.besureLoading = false
								console.log(error)
							}).finally(() => {
								this.besureLoading = false
							})
						}
					}
				})
			},
			cancelModal() {
				this.upccModal = false
				this.upccform.templateName = ""
				this.upccform.templateDesc = ""
				this.upccform.signId = ""
				this.upccform.supportHotspot = ""
				this.upccform.rate = ""
				this.upccform.unit = "2"
				this.$refs.upccform.resetFields()
			},
		}
	}
</script>

<style scoped="scoped">
	.search_head_i {
		margin-top: 30px;
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 100px;
	}

	.footer_wrap {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
