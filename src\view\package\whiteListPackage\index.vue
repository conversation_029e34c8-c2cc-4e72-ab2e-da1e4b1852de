<template>
  <!-- 白名单套餐管理 -->
  <Card>
    <!-- 搜索条件 -->
    <div class="search_head_i">
      <div class="search_box">
        <span class="search_box_label">渠道商简称:</span>
        <Select v-model="corpId" filterable placeholder="请选择渠道商简称" clearable style="width: 300px;">
          <Option :value="item.corpId" v-for="(item,index) in corpList" :key="index">{{item.corpName}}</Option>
        </Select>
      </div>
      <div class="search_box" style="width: 120px;padding-left: 20px;">
        <Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search">搜索</Button>
        &nbsp;&nbsp;&nbsp;&nbsp;
        <Button v-has="'add'" type="info" icon="md-add" style="margin-left: 10px;" @click="addPackage">新增</Button>
      </div>
    </div>
    <!-- 表格 -->
    <Table ref="selection" :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
      <template slot-scope="{ row, index }" slot="details">
        <Button v-has="'showItem'" type="info" ghost @click="showItem(row)">查看详情</Button>
      </template>
      <template slot-scope="{ row, index }" slot="action">
        <Button v-has="'update'" type="primary" ghost style="margin-right: 10px;"
          @click="updatePackage(row)">编辑</Button>
        <Button v-has="'delete'" type="error" ghost @click="deletePackage(row)">删除</Button>
      </template>
    </Table>
    <!-- 分页 -->
    <div style="margin-top:15px">
      <Page :total="total" :current.sync="currentPage" :page-size="pageSize" show-total show-elevator
        @on-change="goPage" />
    </div>
    <!-- 新增/编辑/删除规则 -->
    <Modal :title="title" v-model="modal" :mask-closable="false" @on-cancel="cancelModal" width="900px">
      <Form ref="formObj" :model="formObj" :rules="rule" style="font-size: 600;padding: 20px;">
        <FormItem label="渠道商简称" prop="corpId" :label-width="90" style="margin-bottom: 50px;">
          <Select v-model="formObj.corpId" :disabled="funcType == '2'" filterable placeholder="请选择渠道商简称" clearable
            style="width: 300px;">
            <Option :value="item.corpId" v-for="(item,index) in corpList" :key="index">{{item.corpName}}</Option>
          </Select>
        </FormItem>
        <Tabs type="card" value="name1" @on-click="choseTab">
          <TabPane label="号段编辑" name="name1">
            <div>
              <Row :gutter="24">
                <div v-for="(item2, index) in formObj.numberSegmentDTO" :key="index" class="rule-item">
                  <Col span="7">
                  <FormItem :prop="'numberSegmentDTO.' + index + '.beginIccid'"
                    :rules="tabName == 'name1' ? rule.beginIccid : [{required: false}]"
                    style="display: flex;justify-content: flex-start;">
                    <div style="text-align: center; font-weight: bold;" v-show="index == 0">
                      <span>起始号码</span>
                    </div>
                    <Input v-model="item2.beginIccid" type="number" clearable placeholder="请输入起始号码"
                      style="width: 200px;"></Input>
                  </FormItem>
                  </Col>
                  <Col span="7">
                  <FormItem :prop="'numberSegmentDTO.' + index + '.endIccid'"
                    :rules="tabName == 'name1' ? rule.endIccid : [{required: false}]"
                    style="display: flex;justify-content: flex-start;">
                    <div style="text-align: center; font-weight: bold;" v-show="index == 0">
                      <span>结束号码</span>
                    </div>
                    <Input v-model="item2.endIccid" type="number" clearable placeholder="请输入结束号码"
                      style="width: 200px;"></Input>
                  </FormItem>
                  </Col>
                  <Col span="7">
                  <FormItem :prop="'numberSegmentDTO.' + index + '.packageId'"
                    :rules="tabName == 'name1' ? rule.packageId : [{required: false}]"
                    style="display: flex;justify-content: flex-start;">
                    <div style="text-align: center; font-weight: bold;" v-if="index == 0">
                      <span>白名单套餐ID</span>
                    </div>
                    <Input v-model="item2.packageId" clearable placeholder="请输入白名单套餐ID" style="width: 200px;"></Input>
                  </FormItem>
                  </Col>
                  <Col span="3">
                  <FormItem>
                    <Button type="error" ghost @click="removeRule(index)" :class="index == 0 ? 'delbox1' : ''">
                      删除</Button>
                  </FormItem>
                  </Col>
                </div>
              </Row>
              <Row :gutter="24">
                <Col span="3" offset="21">
                <Button type="success" ghost @click="addRule">
                  添加
                </Button>
                </Col>
              </Row>
            </div>
          </TabPane>
          <TabPane label="文件新增" name="name2">
            <div style="display: flex;justify-content: center;margin-top: 50px;">
              <FormItem label="文件" style="width:510px;" prop="file">
                <Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/" :action="uploadUrl"
                  :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
                  :on-progress="fileUploading">
                  <div style="padding: 20px 0">
                    <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                    <p>点击或拖拽文件上传</p>
                  </div>
                </Upload>
                <ul class="ivu-upload-list" v-if="file" style="width: 100%;">
                  <li class="ivu-upload-list-file ivu-upload-list-file-finish">
                    <span>
                      <Icon type="ios-folder" />{{file.name}}
                    </span>
                    <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
                  </li>
                </ul>
                <div style="width: 100%;">
                  <Button type="primary" @click="downloadTemplate" :loading="downloadFileLoading">模板下载</Button>
                  <Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
                </div>
              </FormItem>
            </div>
          </TabPane>
          <TabPane label="文件删除" name="name3" v-if="funcType == '2'">
            <div style="display: flex;justify-content: center;margin-top: 50px;">
              <FormItem label="文件" style="width:510px;" prop="deleteFile">
                <Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/" :action="uploadUrl"
                  :on-success="fileSuccess" :on-error="handleDeleteError" :before-upload="handleDeleteBeforeUpload"
                  :on-progress="fileUploading">
                  <div style="padding: 20px 0">
                    <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                    <p>点击或拖拽文件上传</p>
                  </div>
                </Upload>
                <ul class="ivu-upload-list" v-if="deleteFile" style="width: 100%;">
                  <li class="ivu-upload-list-file ivu-upload-list-file-finish">
                    <span>
                      <Icon type="ios-folder" />{{deleteFile.name}}
                    </span>
                    <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style=""
                      @click="removeDeleteFile"></i>
                  </li>
                </ul>
                <div style="width: 100%;">
                  <Button type="primary" @click="downloadTemplate" :loading="downloadFileLoading">模板下载</Button>
                  <Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
                </div>
              </FormItem>
            </div>
          </TabPane>
        </Tabs>
      </Form>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
        <Button type="primary" @click="besure" :loading="besureLoading">确定</Button>
        <Button @click="cancelModal">返回</Button>
      </div>
    </Modal>
    <!-- 详情 -->
    <Modal title="查看详情" v-model="detailModal" :mask-closable="false" @on-cancel="cancelModal" width="800px">
      <div style="padding: 0 16px">
        <div style="margin: 20px;">
          <span class="search_box_label">渠道商简称:</span>&nbsp;&nbsp;&nbsp;&nbsp;{{corpName}}
        </div>
        <div style="margin: 20px;">
          <span class="search_box_label">按号码查询:</span>&nbsp;&nbsp;&nbsp;&nbsp;
          <Input v-model.trim="iccid" type="number" clearable placeholder="请输入ICCID"
            style="width: 300px;"></Input>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <Button type="primary" icon="md-search" :loading="searchIccidloading" @click="searchIccid">搜索</Button>
        </div>
        <div style="margin: 20px 0">
          <Table :columns="columnsType == '1' ? iccidColumns1 : iccidColumns2" :data="iccidData" :ellipsis="true"
            :loading="iccidLoading"></Table>
        </div>
      </div>
      <div slot="footer" style="display: flex;justify-content:center;">
        <Button @click="cancelModal">返回</Button>
      </div>
    </Modal>
    <a ref="downloadLink" style="display: none"></a>
  </Card>
</template>

<script>
  import {
    whitePackageList,
    addWhitePackageNumber,
    updateWhitePackageNumber,
    addWhitePackageFile,
    downloadTemplate,
    deleteWhitePackage,
    whitePackageDetail,
  } from '@/api/package/whiteListPackage'
  import {
    getCorpList
  } from '@/api/product/package/batch';
  export default {
    data() {
      // 统一的异步校验空格方法
      const validateNoSpaces = (rule, value, callback) => {
        // 检查是否为空（包括空字符串、null、undefined）
        if (!value || value.trim() === '') {
          callback(new Error(rule.message || '该字段不能为空'));
        }
        // 检查是否仅为空格（允许包含空格但必须有其他字符）
        else if (/^\s+$/.test(value)) {
          callback(new Error(rule.message || '该字段不能仅为空格'));
        }
        // 通过验证
        else {
          callback();
        }
      };
      const validateNumberLength = (rule, value, callback) => {
        const numStr = value.toString();
        if (numStr.length < 13 || numStr.length > 20) {
          return callback(new Error('请输入13-20位数字'));
        }
        callback();
      };
      const validateSameLengthBegin = (rule, value, callback, form, field) => {
        const matches = rule.field.match(/numberSegmentDTO\.(\d+)\.beginIccid/);
        const currentIndex = parseInt(matches[1], 10); // 提取索引并转换为整数
        const otherValue = form.numberSegmentDTO[currentIndex].endIccid;

        // 检查长度是否相同
        if (value && otherValue && value.toString().length !== otherValue.toString().length) {
          return callback(new Error('起始号码和结束号码位数必须相同'));
        }

        // 检查beginIccid是否小于等于endIccid
        if (value && otherValue && value > otherValue) {
          return callback(new Error('起始号码必须小于或等于结束号码'));
        }

        // 检查号码段是否与其他号码段交叉
        if (value) {
          const currentBegin = value;
          const currentEnd = otherValue;

          // 检查与前面号码段的交叉
          for (let i = 0; i < currentIndex; i++) {
            const prevBegin = form.numberSegmentDTO[i].beginIccid;
            const prevEnd = form.numberSegmentDTO[i].endIccid;

            if (prevBegin && prevEnd) {
              if ((currentBegin >= prevBegin && currentBegin <= prevEnd) ||
                  (currentEnd >= prevBegin && currentEnd <= prevEnd) ||
                  (currentBegin <= prevBegin && currentEnd >= prevEnd)) {
                return callback(new Error(`当前号码段与第${i + 1}组号码段存在交叉`));
              }
            }
          }

          // 检查与后面号码段的交叉
          for (let i = currentIndex + 1; i < form.numberSegmentDTO.length; i++) {
            const nextBegin = form.numberSegmentDTO[i].beginIccid;
            const nextEnd = form.numberSegmentDTO[i].endIccid;

            if (nextBegin && nextEnd) {
              if ((currentBegin >= nextBegin && currentBegin <= nextEnd) ||
                  (currentEnd >= nextBegin && currentEnd <= nextEnd) ||
                  (currentBegin <= nextBegin && currentEnd >= nextEnd)) {
                return callback(new Error(`当前号码段与第${i + 1}组号码段存在交叉`));
              }
            }
          }
        }

        callback();
      };
      const validateSameLengthEnd = (rule, value, callback, form, field) => {
        const matches = rule.field.match(/numberSegmentDTO\.(\d+)\.endIccid/);
        const currentIndex = parseInt(matches[1], 10); // 提取索引并转换为整数
        const otherValue = form.numberSegmentDTO[currentIndex].beginIccid;

        // 检查长度是否相同
        if (value && otherValue && value.toString().length !== otherValue.toString().length) {
          return callback(new Error('起始号码和结束号码位数必须相同'));
        }

        // 检查endIccid是否大于等于beginIccid
        if (value && otherValue && value < otherValue) {
          return callback(new Error('结束号码必须大于或等于起始号码'));
        }

        // 检查号码段是否与其他号码段交叉
        if (value) {
          const currentBegin = otherValue;
          const currentEnd = value;

          // 检查与前面号码段的交叉
          for (let i = 0; i < currentIndex; i++) {
            const prevBegin = form.numberSegmentDTO[i].beginIccid;
            const prevEnd = form.numberSegmentDTO[i].endIccid;

            if (prevBegin && prevEnd) {
              if ((currentBegin >= prevBegin && currentBegin <= prevEnd) ||
                  (currentEnd >= prevBegin && currentEnd <= prevEnd) ||
                  (currentBegin <= prevBegin && currentEnd >= prevEnd)) {
                return callback(new Error(`当前号码段与第${i + 1}组号码段存在交叉`));
              }
            }
          }

          // 检查与后面号码段的交叉
          for (let i = currentIndex + 1; i < form.numberSegmentDTO.length; i++) {
            const nextBegin = form.numberSegmentDTO[i].beginIccid;
            const nextEnd = form.numberSegmentDTO[i].endIccid;

            if (nextBegin && nextEnd) {
              if ((currentBegin >= nextBegin && currentBegin <= nextEnd) ||
                  (currentEnd >= nextBegin && currentEnd <= nextEnd) ||
                  (currentBegin <= nextBegin && currentEnd >= nextEnd)) {
                return callback(new Error(`当前号码段与第${i + 1}组号码段存在交叉`));
              }
            }
          }
        }

        callback();
      };
      return {
        total: 0,
        currentPage: 1,
        page: 0,
        pageSize: 10,
        file: null,
        deleteFile: null,
        corpId: "",
        corpName: "",
        cropName: "",
        uploadUrl: "",
        corpList: [],
        title: "",
        funcType: "",
        columnsType: '1',
        iccid: "",
        tabName: "name1",
        message: "请上传.xlsx文件，大小限制为10MB以内。",
        name1: "numberSegmentDTO",
        name2: "fileAdd",
        name3: "fileDelete",
        formObj: {
          corpId: "",
          numberSegmentDTO: [{
            beginIccid: "",
            endIccid: "",
            packageId: "",
          }],
        },
        rule: {
          corpId: [{
            required: true,
            message: "渠道商简称不能为空",
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.funcType === '2') {
                callback();
              } else {
                validateNoSpaces(rule, value, callback);
              }
            }
          }],
          beginIccid: [{
              validator: validateNoSpaces,
              message: '起始号码不能为空或仅为空格',
            }, {
              validator: (rule, value, callback) => {
                validateNumberLength(rule, value, callback);
              },
            },
            {
              validator: (rule, value, callback) => {
                validateSameLengthBegin(rule, value, callback, this.formObj, 'beginIccid');
              },
            }
          ],
          endIccid: [{
              validator: validateNoSpaces,
              message: '结束号码不能为空或仅为空格',
            }, {
              validator: (rule, value, callback) => {
                validateNumberLength(rule, value, callback);
              },
            },
            {
              validator: (rule, value, callback) => {
                validateSameLengthEnd(rule, value, callback, this.formObj, 'endIccid');
              },
            }
          ],
          packageId: [{
            validator: validateNoSpaces,
            message: '白名单套餐ID不能为空或仅为空格',
          }],
        },
        modal: false, //新增/编辑对话框
        detailModal: false,
        loading: false,
        iccidLoading: false,
        searchloading: false, //查询加载
        searchIccidloading: false,
        besureLoading: false, //确定加载
        downloadFileLoading: false,
        data: [], //表格列表
        detailSearchItem: {
          corpId: "",
          iccid: "",
        },
        iccidData: [],
        columns: [{
            title: "渠道商简称",
            key: 'corpName',
            minWidth: 200,
            align: 'center',
            tooltip: true
          },
          {
            title: "查看详情",
            slot: 'details',
            minWidth: 100,
            align: 'center',
            fixed: 'right'
          },
          {
            title: "操作",
            slot: 'action',
            minWidth: 200,
            align: 'center',
            fixed: 'right'
          },
        ],
        iccidColumns1: [{
            title: "起始号码",
            key: 'beginIccid',
            minWidth: 200,
            align: 'center',
            tooltip: true
          },
          {
            title: "结束号码",
            key: 'endIccid',
            minWidth: 100,
            align: 'center',
            tooltip: true
          },
          {
            title: "白名单套餐ID",
            key: 'packageId',
            minWidth: 200,
            align: 'center',
            tooltip: true
          },
        ],
        iccidColumns2: [{
            title: "ICCID",
            key: 'iccid',
            minWidth: 200,
            align: 'center',
            tooltip: true
          },
          {
            title: "白名单套餐ID",
            key: 'packageId',
            minWidth: 200,
            align: 'center',
            tooltip: true
          },
        ]
      }
    },
    mounted() {
      this.goPageFirst(1)
      this.getCorpList()
    },
    computed: {},
    methods: {
      // 首页列表
      goPageFirst: function(page) {
        this.loading = true
        whitePackageList({
          corpId: this.corpId ? this.corpId : "",
          pageSize: 10,
          pageNum: page,
        }).then(res => {
          if (res.code == '0000') {
            this.page = page
            this.currentPage = page
            this.data = res.data.records
            this.total = Number(res.data.totalCount)
          }
        }).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.loading = false
          this.searchloading = false
        })
      },
      search: function() {
        this.searchloading = true
        this.goPageFirst(1)
      },
      goPage(page) {
        this.goPageFirst(page)
      },
      //下载模板文件
      downloadTemplate() {
        this.downloadFileLoading = true
        downloadTemplate({
          "templateType": this.tabName == 'name2' ? "add" : "delete",
        }).then(res => {
          const content = res.data
          let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[
            1])) //获取到Content-Disposition;filename  并解码
          if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
            const link = this.$refs.downloadLink // 创建a标签
            let url = URL.createObjectURL(content)
            link.download = fileName
            link.href = url
            link.click() // 执行下载
            URL.revokeObjectURL(url) // 释放url
          } else { // 其他浏览器
            navigator.msSaveBlob(content, fileName)
          }
        }).catch(err =>
          console.error(err)
        ).finally(() => {
          this.downloadFileLoading = false
        })
      },
      // 新增/修改/删除
      async besure() {
        console.log(this.formObj, "this.formObj");

        try {
          // 处理号段新增/修改/删除
          if (this.tabName === 'name1') {
            // 号段
            const valid = await this.$refs.formObj.validate();
            if (!valid) return;

            this.besureLoading = true;
            const apiCall = this.funcType === '1' ?
              () => addWhitePackageNumber({
                corpId: this.formObj.corpId,
                numberSegmentDTO: this.formObj.numberSegmentDTO,
              }) :
              () => updateWhitePackageNumber({
                corpId: this.formObj.corpId,
                numberSegmentDTO: this.formObj.numberSegmentDTO,
              });

            const res = await apiCall();
            if (res.code === '0000') {
              this.showSuccessMessage();
              this.resetFormAndCloseModal();
            }
          }
          // 处理文件上传
          else {
            const file = this.tabName === 'name2' ? this.file : this.deleteFile;
            if (!file) {
              this.$Message.warning('请选择需要上传的文件');
              return;
            }
            this.besureLoading = true;
            const formData = new FormData();
            formData.append('file', file);
            formData.append('corpId', this.formObj.corpId);
            formData.append('type', this.tabName === 'name2' ? 'fileAdd' : 'fileDelete');

            const res = await addWhitePackageFile(formData);
            if (res.code === '0000') {
              this.showSuccessMessage();
              this.resetFormAndCloseModal();
            }
          }
        } catch (error) {
          console.error(error);
        } finally {
          this.besureLoading = false;
        }
      },
      // 显示成功提示
      showSuccessMessage() {
        this.$Notice.success({
          title: "操作提示",
          desc: '操作成功',
        });
      },
      // 重置表单并关闭弹窗
      resetFormAndCloseModal() {
        this.goPageFirst(1);
        this.modal = false;
        this.cancelModal();
      },
      // 删除
      deletePackage(row) {
        this.$Modal.confirm({
          title: '确认删除？',
          onOk: () => {
            deleteWhitePackage({
              corpId: row.corpId,
            }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.page = 1
                this.goPageFirst(1)
              } else {
                throw res
              }
            }).catch((err) => {
              console.error(err)
            })
          }
        });
      },
      // 详情列表
      goDetailsFirst(row) {
        this.iccidLoading = true
        whitePackageDetail({
          corpId: row ? row.corpId : this.corpId,
          iccid: this.iccid,
        }).then(res => {
          if (res.code == '0000') {
            if (this.iccid) {
              this.columnsType = '2'
              //编辑回显号段
              if (this.funcType == '2') {
                //如果iccidPackageIdList为空，则显示初始化的数据，否则显示回显的数据
                if (res.data.iccidPackageIdList.length == 0) {
                  this.formObj.numberSegmentDTO = [{
                    beginIccid: "",
                    endIccid: "",
                    packageId: "",
                  }]
                } else {
                  this.formObj.numberSegmentDTO = res.data.iccidPackageIdList
                }
              }
              this.iccidData = res.data.iccidPackageIdList
            } else {
              this.columnsType = '1'
              this.iccidData = res.data.numberSegments
              if (this.funcType == '2') {
                //如果numberSegments为空，则显示初始化的数据，否则显示回显的数据
                if (res.data.numberSegments.length == 0) {
                  this.formObj.numberSegmentDTO = [{
                    beginIccid: "",
                    endIccid: "",
                    packageId: "",
                  }]
                } else {
                  this.formObj.numberSegmentDTO = res.data.numberSegments
                }
              }
            }
          }
        }).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.iccidLoading = false
          this.searchIccidloading = false
        })
      },
      searchIccid() {
        this.searchIccidloading = true
        this.goDetailsFirst(this.detailSearchItem)
      },
      choseTab(name) {
        this.tabName = name
      },
      //新增
      addPackage() {
        this.title = "新增体验套餐规则"
        this.funcType = '1'
        // 清空formObj
        this.formObj = {
          numberSegmentDTO: [{
            beginIccid: "",
            endIccid: "",
            packageId: "",
          }],
        }
        this.modal = true
      },
      //修改
      updatePackage(row) {
        this.title = "修改体验套餐规则"
        this.funcType = '2'
        this.modal = true
        this.formObj.corpId = row.corpId
        this.goDetailsFirst(row)
      },
      //查看详情
      showItem(row) {
        this.detailSearchItem = row
        this.corpName = row.corpName
        //请求详情
        this.goDetailsFirst(row)
        this.detailModal = true
      },
      addRule(index) {
        this.index++;
        this.formObj.numberSegmentDTO.push({
          beginIccid: '',
          endIccid: '',
          packageId: '',
          index: this.index,
        });
      },
      removeRule(index) {
        if (this.formObj.numberSegmentDTO.length > 1) {
          this.formObj.numberSegmentDTO.splice(index, 1);
        } else {
          this.$Message.warning('至少需要保留一条返还规则');
        }
      },
      handleBeforeUpload(file) {
        if (!/^.+(\.xlsx)$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式不正确',
            desc: '文件 ' + file.name + ' 格式不正确，请上传.xlsx。'
          })
        } else {
          if (file.size > 10 * 1024 * 1024) {
            this.$Notice.warning({
              title: '文件大小超过限制',
              desc: '文件 ' + file.name + '超过了最大限制范围10MB'
            })
          } else {
            this.file = file
          }
        }
        return false
      },
      handleDeleteBeforeUpload(file) {
        if (!/^.+(\.xlsx)$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式不正确',
            desc: '文件 ' + file.name + ' 格式不正确，请上传.xlsx。'
          })
        } else {
          if (file.size > 10 * 1024 * 1024) {
            this.$Notice.warning({
              title: '文件大小超过限制',
              desc: '文件 ' + file.name + '超过了最大限制范围10MB'
            })
          } else {
            this.deleteFile = file
          }
        }
        return false
      },
      fileUploading(event, file, fileList) {
        this.message = '文件上传中、待进度条消失后再操作'
      },
      fileSuccess(response, file, fileList) {
        this.message = '请先下载模板文件，并按格式填写后上传'
      },
      handleError(res, file) {
        var v = this
        setTimeout(function() {
          v.uploading = false;
          v.$Notice.warning({
            title: '错误提示',
            desc: "上传失败！"
          });
        }, 3000);
      },
      handleDeleteError(res, file) {
        var v = this
        setTimeout(function() {
          v.uploading = false;
          v.$Notice.warning({
            title: '错误提示',
            desc: "上传失败！"
          });
        }, 3000);
      },
      removeFile() {
        this.file = ''
      },
      removeDeleteFile() {
        this.deleteFile = ''
      },
      cancelModal() {
        this.modal = false
        this.$refs.formObj.resetFields()
        this.detailModal = false
        this.iccid = ''
        this.file = null
        this.deleteFile = null
        this.searchIccidloading = false
      },
      //获取渠道集合
      getCorpList() {
        getCorpList({
          // "type": 1,
          "status": 1,
          "checkStatus": 2,
          "types": [1, 3, 4, 7, 8, 9]
        }).then(res => {
          if (res && res.code == '0000') {
            this.corpList = res.data;
          } else {
            throw res
          }
        }).catch((err) => {

        }).finally(() => {

        })
      },
    }
  }
</script>

<style scoped="scoped">
  .search_head_i {
    margin-top: 30px;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
  }

  .search_box {
    width: 300px;
    padding: 0 5px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: row;
    margin-bottom: 20px;
  }

  .search_box_label {
    font-weight: bold;
    text-align: center;
    width: 150px;
  }

  .footer_wrap {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .rule-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .delbox1 {
    margin-top: 30px;
  }

  /* 去掉input为number的上下箭头 */
  /deep/ input::-webkit-outer-spin-button,
  /deep/ input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }

  /deep/ input[type="number"] {
    -moz-appearance: textfield;
  }
</style>
