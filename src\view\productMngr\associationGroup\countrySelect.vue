<template>
  <div class="country-select-page">
    <Card>
      <template #title>
        <strong> {{cardpoolTitle}} </strong>
      </template>
      <Form ref="formObj" :model="formObj" :label-width="90" :rules="ruleValidate" inline @submit.native.prevent>
        <FormItem label="关联组名称" prop="groupName">
          <Input v-model="formObj.groupName" :maxlength="500" :clearable="true" placeholder="最大支持500字符"
            :disabled="pageType == '4'" style="width: 300px;" @keydown.enter.native.prevent />
        </FormItem>
        <FormItem label="国家/地区">
          <Select v-model="formObj.selectedMcc" style="width: 200px;" filterable clearable>
            <Option v-for="item in countryList" :key="item.mcc" :value="item.mcc">{{ item.countryEn + '（' + item.countryCn + '）'}}</Option>
          </Select>
        </FormItem>
        <FormItem v-show="pageType === '4'" label="卡池名称">
          <Input v-model.trim="formObj.searchCardPool" placeholder="请输入卡池名称" clearable style="width: 200px;"
            @keydown.enter.native.prevent />
        </FormItem>
        <FormItem :label-width="30">
          <Button type="primary" @click="searchGroupPoolList">搜索</Button>
        </FormItem>
        <FormItem :label-width="30">
          <Button type="primary" ghost icon="md-add" v-show="pageType != '4'" @click="addCountryModal">新增国家</Button>
        </FormItem>
      </Form>

      <Table border :columns="countryColumns" :data="paginatedCountries" :loading="loading">
        <template slot-scope="{ row }" slot="countryCn">
          <span>{{ row.countryDTOS.map(item => item.countryCn).join('、') }}</span>
        </template>
        <template slot-scope="{ row }" slot="poolDetails">
          <div>
            <div v-for="(pool, index) in row.mccCardPoolDetailDTOS" :key="index">
              {{ pool.poolName }} {{ pool.rate }}%
            </div>
          </div>
        </template>
        <template slot-scope="{ row }" slot="action">
          <Button type="info" ghost @click="showModal('edit', row)" :disabled="pageType == '4'">编辑</Button>
          <Button type="error" ghost style="margin-left: 10px;" @click="handleDeleteCountry(row)"
            :disabled="pageType == '4'">删除</Button>
        </template>
      </Table>

      <div class="pagination-wrapper">
        <Page :total="filteredCountries.length" :current="currentPage" :page-size="pageSize" show-total show-elevator
          @on-change="handlePageChange" />
      </div>

      <div class="footer-textarea">
        <Button @click="handleBack">返回</Button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button v-show="pageType != '4'" type="primary" @click="handleConfirm" :loading="submitting">确定</Button>
      </div>
    </Card>

    <!-- 新增/编辑 Modal -->
    <Modal v-model="showCountryModal" :title="modalTitle" :mask-closable="false" width="85%" class="country-modal"
      @on-cancel="handleModalCancel">
      <Form :label-width="90" @submit.native.prevent>
        <FormItem label="关联组名称">
          <Input v-model.trim="groupName" disabled style="width: 300px;" />
        </FormItem>
      </Form>
      <Tabs v-model="activeTabName">
        <!-- 选择国家/地区页签 -->
        <TabPane label="选择国家/地区" name="name1">
          <div style="width:100%; display: flex;justify-content: flex-start">
            <div style="width: 80%;">
              <Form inline style="margin: 16px 0;" @submit.native.prevent>
                <FormItem label="国家/地区" :label-width="70">
                  <Select
                    v-model="countrySearchEn"
                    filterable
                    clearable
                    placeholder="请输入国家英文名"
                    style="width: 220px; margin-right: 8px;"
                    :transfer="true"
                  >
                    <Option
                      v-for="item in countryData"
                      :key="item.mcc"
                      :value="item.countryEn"
                    >{{ item.countryEn }}</Option>
                  </Select>
                </FormItem>
                <FormItem :label-width="30">
                  <Button type="info" ghost @click="handleCountrySearch">搜索</Button>
                </FormItem>
              </Form>
              <Table border :columns="modalCountryColumns" :data="paginatedCountryData" :loading="loading"
                ref="countryTable">
                <template slot-scope="{ row }" slot="delete">
                  <Button type="error" ghost :disabled="pageType == '4'" @click="clearCountry(row.id)">删除</Button>
                </template>
              </Table>
              <div class="pagination-wrapper" style="margin-top: 20px;">
                <Page :total="countrySearchList.length" :current="modalCurrentPage" :page-size="modalPageSize" show-total
                  show-elevator @on-change="handleModalPageChange" />
              </div>
            </div>
            <div
              style="width: 20%; height: 200px; display: flex;flex-direction: column;justify-content: flex-start; align-items: center; padding-top: 90px;">
              <Button type="primary" ghost :disabled="pageType == '4'" @click="addCountry"
                style="width: 100px; margin-bottom: 30px;">添加国家</Button>
              <Button type="success" ghost :disabled="pageType == '4'" @click="batchAddCountry"
                style="width: 100px; margin-bottom: 30px;">批量添加国家</Button>
              <Button type="warning" ghost :disabled="pageType == '4'" @click="clearAllCountry"
                style="width: 100px;">清空国家</Button>
            </div>
          </div>
        </TabPane>

        <!-- 选择卡池页签 -->
        <TabPane label="选择卡池" name="name2">
          <div style="width: 100%; display: flex; justify-content: flex-start;">
            <div style="width: 60%; overflow-x: auto; padding-right: 50px;">
              <Form inline style="margin: 16px 0;" @submit.native.prevent>
                <FormItem label="卡池名称" :label-width="60">
                  <Input v-model.trim="searchCardPool" placeholder="请输入卡池名称" clearable style="width: 200px"
                    @keydown.enter.native.prevent />
                </FormItem>
                <FormItem :label-width="30">
                  <Button type="info" ghost @click="searchPools" :loading="poolLoading">搜索</Button>
                </FormItem>
              </Form>
              <Table border ref="poolTable" :columns="poolColumns" :data="poolData" :loading="poolLoading"
                @on-selection-change="handlePoolSelection" @on-select-all="handlePoolSelectAll" size="small"
                :disabled-hover="pageType === '4'">
              </Table>
              <div style="margin-top: 30px;">
                <Page :total="poolTotal" :current="poolCurrentPage" :page-size="poolPageSize" show-total show-elevator
                  @on-change="handlePoolPageChange" />
              </div>
            </div>
            <div style="width: 40%; overflow-x: auto;">
              <Form inline style="margin: 16px 0;">
                <FormItem label="已选卡池">
                </FormItem>
              </Form>
              <div style="max-height: 435px; overflow-y: auto;">
                <Table border :columns="selectedPoolColumns" :data="selectedPools" size="small" :sticky="true">
                  <template slot-scope="{ row }" slot="rate">
                    <Input v-model="row.rate" type="number" :min="0" :max="100" style="width: 100%"
                      @on-change="handleRateChange(row)" :disabled="pageType == '4'" clearable>
                    <span slot="append">%</span>
                    </Input>
                  </template>
                  <template slot-scope="{ row, index }" slot="action">
                    <Button type="error" ghost :disabled="pageType == '4'"
                      @click="handleRemovePool(row, index)">删除</Button>
                  </template>
                </Table>
              </div>
              <div style="margin-top: 30px; text-align: left; color: #515a6e; font-size: 12px;">
                共 <span style="color: dodgerblue;">{{selectedPools.length}}</span> 条
              </div>
            </div>
          </div>
        </TabPane>
      </Tabs>
      <div slot="footer" class="footer-textarea">
        <Button @click="handleModalCancel">取消</Button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button type="primary" v-show="pageType != '4'" @click="handleModalConfirm"
          :disabled="poolLoading || !isPoolDataReady">确定</Button>
      </div>
    </Modal>

    <!-- 添加国家 -->
    <Modal title="添加国家/地区" v-model="modal1" :footer-hide="true" :mask-closable="false" @on-cancel="closeModal1"
      width="500px">
      <Form ref="mccObj" :model="mccObj" :rules="mccValidate" style="padding: 0 5%;" @submit.native.prevent>
        <FormItem prop="country">
          <Select v-model="mccObj.country" multiple filterable style="margin-top: 20px;" @keydown.enter.native.prevent>
            <Option v-for="mcc in countryList" :value="mcc.mcc" :key="mcc.mcc" :disabled="existingMccs.has(mcc.mcc)">
              {{ mcc.countryEn + '（' + mcc.countryCn + '）'}}
            </Option>
          </Select>
        </FormItem>
      </Form>
      <div class="footer-textarea">
        <Button type="primary" @click="sumbitModal1">确定</Button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button @click="closeModal1">返回</Button>
      </div>
    </Modal>

    <!-- 批量添加国家 -->
    <batch-add ref="batchAddModal" :continents-data="continentsData" :selected-countries="countryData"
      :disabled-mccs="Array.from(existingMccs)" @on-close="closeBatchAddModal" @on-confirm="handleBatchAddConfirm" />
  </div>
</template>

<script>
  import {
    getContinentMcc,
  } from '@/api/sms/areaWelcome';
  import {
    opsearchAll
  } from '@/api/operators'
  import {
    getCardPoolinfoBymccNew
  } from '@/api/productMngr/cardPool'
  import {
    addcardPoolMccGroup,
    updatecardPoolMccGroup,
    getCardPoolGroupDetailNew
  } from '@/api/associationGroup/cardPoolMccGroup.js'
  import BatchAdd from '@/components/country/batchAdd.vue'
  export default {
    name: 'CountrySelect',
    components: {
      BatchAdd
    },
    data() {
      // 自定义验证 判断上传文件 mccLists 的长度, 这样就和普通输入框表现一致了
      const validateUpload = (rule, value, callback) => {
        if (this.formObj.mccLists && this.formObj.mccLists.length < 1) {
          callback(new Error('请选择卡池'))
        } else {
          callback()
        }
      }
      return {
        cardpoolTitle: '',
        pageType: this.$route.query.flag || '1',
        groupName: "",
        searchCardPool: '',
        modalTitle: '',
        modalType: 'add',
        searchCountry: '',
        activeTabName: 'name1',
        pendingTabSwitch: '', // 标记即将切换的页签
        modalData: null,
        countryTotal: 0,
        countryCurrentPage: 1,
        currentPage: 1,
        pageSize: 10,
        total: 0,
        modalCurrentPage: 1,
        modalTotal: 0,
        poolCurrentPage: 1,
        poolPageSize: 10,
        poolTotal: 0,
        modalPageSize: 10,
        showCountryModal: false,
        modal1: false,
        loading: false,
        poolLoading: false,
        submitting: false,
        formObj: {
          groupName: '', //关联组名称
          cardpoolList: [], //卡池列表
          mccLists: [], //国家地区列表
          selectedMcc: "", //国家下拉框
          searchCardPool: "", //卡池输入框
        },
        ruleValidate: {
          groupName: [{
            required: true,
            message: "关联组名称不能为空",
          }],
          mccLists: [{
            required: true,
            validator: validateUpload,
            trigger: 'change',
          }, ]
        },
        mccObj: {
          country: '',
        },
        mccValidate: {
          country: [{
            required: true,
            message: "请选择适用国家/地区",
          }, ],
        },
        countryColumns: [{
            title: "国家",
            slot: 'countryCn',
            minWidth: 200,
            align: 'center',
            render: (h, params) => {
              const countries = params.row.countryDTOS.map(item => item.countryCn);
              const displayText = countries.join('、');

              return h('div', [
                h('Tooltip', {
                  props: {
                    placement: 'bottom',
                    transfer: true,
                    content: displayText,
                    maxWidth: 300
                  },
                  style: {
                  width: '100%',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }
              }, displayText)
              ]);
            }
          },
          {
            title: "卡池",
            slot: 'poolDetails',
            minWidth: 300,
            align: 'center',
            render: (h, params) => {
              const row = params.row;
              const pools = row.mccCardPoolDetailDTOS || [];

              // 创建一个包含所有卡池信息的容器
              return h('div', {
                style: {
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  maxHeight: '120px', // 限制最大高度为3行
                  overflowY: 'auto', // 添加垂直滚动条
                  padding: '10px 0',
                  width: '100%'
                }
              }, pools.map((pool, index) => {
                return h('div', {
                  style: {
                    display: 'flex',
                    alignItems: 'flex-start', // 改为flex-start以保持左对齐
                    marginBottom: '5px',
                    padding: '5px 10px',
                    backgroundColor: '#f8f8f9',
                    borderRadius: '5px',
                    width: '100%',
                    boxSizing: 'border-box'
                  }
                }, [
                  h('span', {
                    style: {
                      color: '#2d8cf0',
                      fontWeight: 'bold',
                      marginRight: '10px',
                      minWidth: '40px',
                      flexShrink: 0 // 防止比例被压缩
                    }
                  }, pool.rate + '%'),
                  h('span', {
                    style: {
                      color: '#515a6e',
                      textAlign: 'left', // 确保文本左对齐
                      wordBreak: 'break-word', // 允许单词换行
                      whiteSpace: 'normal' // 允许文本换行
                    }
                  }, pool.poolName)
                ]);
              }));
            }
          },
          // 在详情页面不显示操作列
          ...(this.pageType !== '4' ? [{
            title: "操作",
            slot: 'action',
            width: 200,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('Button', {
                  props: {
                    type: 'info',
                    ghost: true,
                    disabled: this.pageType === '4'
                  },
                  on: {
                    click: () => {
                      this.showModal('edit', params.row);
                    }
                  }
                }, '编辑'),
                h('Button', {
                  props: {
                    type: 'error',
                    ghost: true,
                    disabled: this.pageType === '4'
                  },
                  style: {
                    marginLeft: '10px'
                  },
                  on: {
                    click: () => {
                      this.handleDeleteCountry(params.row);
                    }
                  }
                }, '删除')
              ]);
            }
          }] : [])
        ],
        modalCountryColumns: [{
            title: '国家/地区名称（中文）',
            key: 'countryCn',
            minWidth: 170,
            align: 'center',
            tooltip: true,
          },
          {
            title: '国家/地区名称（英文）',
            key: 'countryEn',
            minWidth: 170,
            align: 'center',
            tooltip: true,
          },
          {
            title: '所属大洲（中文）',
            key: 'continentCn',
            minWidth: 140,
            align: 'center',
            tooltip: true,
          },
          {
            title: '所属大洲（英文）',
            key: 'continentEn',
            minWidth: 140,
            align: 'center',
            tooltip: true,
          },
          {
            title: '操作',
            slot: 'delete',
            minWidth: 100,
            align: 'center',
            fixed: 'right'
          }
        ],
        poolColumns: [{
            type: 'selection',
            width: 60,
            align: 'center',
            tooltip: true,
            fixed: 'left',
            disabled: (row) => this.pageType === '4' // 在详情页禁用选择
          },
          {
            title: '卡池ID',
            key: 'poolId',
            minWidth: 180,
            align: 'center',
            tooltip: true,
          },
          {
            title: '卡池名称',
            key: 'poolName',
            minWidth: 150,
            align: 'center',
            tooltip: true,
          },
          {
            title: '供应商',
            key: 'supplierName',
            minWidth: 100,
            align: 'center',
            tooltip: true,
          },
          {
            title: '支持国家/地区',
            key: 'countrys',
            minWidth: 200,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const countries = params.row.countrys || [];
              const displayText = countries.join('、');
              return h('div', [
                h('Tooltip', {
                  props: {
                    placement: 'bottom',
                    transfer: true,
                    content: displayText,
                    maxWidth: 300
                  },
                  style: {
                    width: '100%',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }
                }, displayText)
              ]);
            }
          },
          {
            title: '卡池类型',
            key: 'poolType',
            minWidth: 130,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              return h('span', '全球卡普通卡池');
            }
          }
        ],
        selectedPoolColumns: [{
            title: '卡池ID',
            key: 'poolId',
            minWidth: 180,
            align: 'center',
            tooltip: true,
          },
          {
            title: '卡池名称',
            key: 'poolName',
            minWidth: 200,
            align: 'center',
            tooltip: true,
          },
          {
            title: '卡池占比',
            slot: 'rate',
            minWidth: 140,
            align: 'center',
            fixed: 'right'
          },
          {
            title: '操作',
            slot: 'action',
            width: 100,
            align: 'center',
            fixed: 'right'
          }
        ],
        countryList: [],
        selectedCountries: [],
        countryData: [],
        poolData: [],
        selectedPools: [],
        tempSelectedCountries: [],
        tempSelectedPools: [],
        userModifiedRates: new Set(),
        continentsData: {}, // 接口返回的大洲数据
        allPoolsData: [], // 存储所有卡池数据
        poolSelectionMap: new Map(),
        poolRateMap: new Map(),
        countryPoolMap: new Map(), // 国家与卡池的映射关系
        poolCountryMap: new Map(), // 卡池与国家的映射关系
        isPoolDataReady: true, // 添加卡池数据就绪状态
        selectedMcc: '',
        lastCountryMccs: '', // 添加一个变量存储上次的国家MCC
        countrySearchEn: '', // 新增：国家英文名搜索框绑定
        countrySearchList: [], // 新增：表格临时展示用
        allCountriesData: [],// 新增：存储所有国家数据的原始副本
        selectedCountries: [],// 修改：使用allCountriesData作为数据源
        initialDataLoaded: false,// 新增：标记是否已加载初始数据
        filteredCountries: [], // 改为普通数据属性，存储过滤后的国家数据
        searchMcc: '', // 新增：存储搜索的MCC
        searchCardPool: '', // 新增：存储搜索的卡池名称
        tempSelectedCountries: [], // 存储临时国家数据
        tempSelectedPools: [],    // 存储临时卡池数据
      }
    },
    mounted() {
      this.loading = true; // 页面加载时显示 loading
      this.cardpoolTitle = this.$route.query.cardpoolTitle;
      this.pageType = this.$route.query.flag || '1';
      this.getContinentMcc();

      // 编辑/复制/详情模式需要加载初始数据
      if (this.pageType !== '1') {
        let groupId = this.$route.query.groupId;
        let mccs = this.$route.query.mccs;
        this.formObj.groupName = this.$route.query.groupName;
      } else {
        // 新增模式标记初始数据已加载（使用空数组）
        this.initialDataLoaded = true;
        this.allCountriesData = [];
      }
    },
    watch: {
      'allCountriesData': {
        handler() {
          this.applyFrontendFilters();
        },
        deep: true
      },
      'selectedCountries': {
        handler() {
          if (this.pageType === '4') {
            this.applyFrontendFilters();
          }
        },
        deep: true
      }
    },
    computed: {
      paginatedCountries() {
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        return this.filteredCountries.slice(start, end);
      },
      // 修改：分页基于countrySearchList
      paginatedCountryData() {
        const start = (this.modalCurrentPage - 1) * this.modalPageSize;
        const end = start + this.modalPageSize;
        return this.countrySearchList.slice(start, end);
      },
      paginatedPoolData() {
        const start = (this.poolCurrentPage - 1) * this.poolPageSize;
        const end = start + this.poolPageSize;
        return this.poolData.slice(start, end);
      },
      existingMccs() {
        const mccs = new Set();
        // 遍历所有国家数据，排除当前正在编辑的国家组（如果是编辑模式）
        this.allCountriesData.forEach(countryGroup => {
          if (countryGroup.countryDTOS) {
            countryGroup.countryDTOS.forEach(item => {
              if (item.mcc) {
                // 如果是编辑模式，且当前编辑的国家组包含这个国家，则跳过
                if (this.modalType === 'edit' && this.modalData &&
                    this.modalData.countryDTOS.some(c => c.mcc === item.mcc)) {
                  return;
                }
                mccs.add(item.mcc);
              }
            });
          }
        });
        return mccs;
      },
    },
    methods: {
      // 添加重置modal1状态的方法
      resetModal1State() {
        this.modal1 = false;
        if (this.$refs.mccObj) {
          this.$refs.mccObj.resetFields();
        }
      },

      // 添加重置batchAddModal状态的方法
      resetBatchAddModalState() {
        this.$refs.batchAddModal.modal = false;
      },

      // 添加重置主modal状态的方法
      resetMainModalState() {
        this.showCountryModal = false;
        this.modalType = 'add';
        this.modalTitle = '新增国家';
        this.modalData = null;
        this.modalCurrentPage = 1;
        this.searchCountry = '';
        this.poolCurrentPage = 1;
        this.searchCardPool = '';
        // 不再重置 countryData 和 selectedPools，由取消逻辑处理
      },

      // 修改handleModalCancel方法
      handleModalCancel() {
        // 恢复之前的状态
        if (this.modalType === 'add') {
          this.countryData = [...this.tempSelectedCountries];
          this.selectedPools = [...this.tempSelectedPools];
          this.tempSelectedCountries = [];
          this.tempSelectedPools = [];

          // 重新应用过滤
          this.applyFrontendFilters();
        }

        this.resetMainModalState();
      },

      // 修改closeModal1方法
      closeModal1() {
        this.resetModal1State();
      },

      // 修改closeBatchAddModal方法
      closeBatchAddModal() {
        this.resetBatchAddModalState();
      },

      // 修改sumbitModal1方法
      sumbitModal1() {
        this.$refs["mccObj"].validate((valid) => {
            if (valid) {
              // 验证是否有重复国家
              const selectedMccs = new Set(this.mccObj.country);
              const duplicateMccs = Array.from(selectedMccs).filter(mcc => this.existingMccs.has(mcc));

              if (duplicateMccs.length > 0) {
                const duplicateCountries = this.countryList
                  .filter(c => duplicateMccs.includes(c.mcc))
                  .map(c => c.countryEn)
                  .join(', ');
                this.$Message.error(`以下国家已存在: ${duplicateCountries}`);
                return;
              }

              // 存放根据mcc找到的国家对象
              const selectedCountries = this.mccObj.country.map(mcc => {
                return this.countryList.find(c => c.mcc === mcc);
              }).filter(country => country !== undefined);

              // 直接更新countryData
              this.countryData = selectedCountries;
              // 只关闭添加国家modal
              this.resetModal1State();
              // 触发卡池列表加载
              this.loadPoolData(1, true);
              // 添加国家成功后，恢复全部展示
              this.countrySearchList = [...this.countryData];
              this.countrySearchEn = '';
            } else {
              return false;
            }
          });
      },

      // 修改handleBatchAddConfirm方法
      handleBatchAddConfirm(selectedCountries) {
        // 过滤掉已选的国家（包括existingMccs中的国家）
        const filteredCountries = selectedCountries.filter(country =>
          !this.existingMccs.has(country.mcc)
        );

        if (filteredCountries.length === 0) {
          this.$Message.warning('所选国家已全部存在于列表中');
          return;
        }

        // 格式化数据
        const formattedCountries = filteredCountries.map(country => ({
          id: country.id,
          countryCn: country.countryCn,
          countryTw: country.countryTw,
          countryEn: country.countryEn,
          continentCn: country.continentCn,
          continentTw: country.continentTw,
          continentEn: country.continentEn,
          mcc: country.mcc,
          imageUrl: country.imageUrl,
          createTime: country.createTime,
          updateTime: country.updateTime,
          hotCountry: country.hotCountry
        }));

        // 直接更新countryData
        this.countryData = formattedCountries;
        // 只关闭批量添加国家modal
        this.resetBatchAddModalState();
        // 触发卡池列表加载
        this.loadPoolData(1, true);
        // 批量添加国家成功后，恢复全部展示
        this.countrySearchList = [...this.countryData];
        this.countrySearchEn = '';
      },

      // handleModalConfirm 方法
      handleModalConfirm() {
        // 添加卡池数据就绪检查
        if (!this.isPoolDataReady) {
          this.$Message.warning('卡池数据正在加载中，请稍候...');
          return;
        }

        // 校验国家选择
        if (this.countryData.length === 0) {
          this.$Message.error('请至少选择一个国家');
          return;
        }

        // 校验卡池选择
        if (this.selectedPools.length === 0) {
          this.$Message.error('请至少选择一个卡池');
          return;
        }

        // 校验卡池占比是否已填写
        const hasEmptyRate = this.selectedPools.some(pool => {
          const rate = pool.rate;
          return rate === undefined || rate === null || rate.toString().trim() === '';
        });
        if (hasEmptyRate) {
          this.$Message.error('请填写所有卡池占比');
          return;
        }

        // 校验卡池占比是否为整数
        const hasDecimalRate = this.selectedPools.some(pool => {
          const rate = parseFloat(pool.rate);
          return isNaN(rate) || !Number.isInteger(rate);
        });
        if (hasDecimalRate) {
          this.$Message.error('卡池占比必须是整数');
          return;
        }

        // 校验卡池占比总和是否为100%
        const total = this.selectedPools.reduce((sum, pool) => {
          const rate = parseFloat(pool.rate);
          return sum + (isNaN(rate) ? 0 : rate);
        }, 0);
        if (Math.abs(total - 100) > 0.01) {
          this.$Message.error('卡池占比总和必须为100%');
          return;
        }

        // 校验卡池比例
        const hasInvalidRate = this.selectedPools.some(pool => {
          const rate = parseFloat(pool.rate);
          return isNaN(rate) || rate < 1 || rate > 100;
        });
        if (hasInvalidRate) {
          this.$Message.error('卡池比例必须在1-100之间');
          return;
        }

        // 格式化数据
        const formattedData = {
          uniqueId: this.modalType === 'edit' ? this.modalData.uniqueId :
              `country_${Date.now()}_${this.allCountriesData.length}`,
          relationId: this.modalType === 'edit' ? this.modalData.relationId : null,
          mccs: null,
          countryDTOS: this.countryData.map(country => ({
            id: country.id,
            countryCn: country.countryCn,
            countryTw: country.countryTw,
            countryEn: country.countryEn,
            continentCn: country.continentCn,
            continentTw: country.continentTw,
            continentEn: country.continentEn,
            mcc: country.mcc,
            imageUrl: country.imageUrl,
            createTime: country.createTime,
            updateTime: country.updateTime,
            hotCountry: country.hotCountry
          })),
          mccCardPoolDetailDTOS: this.selectedPools.map(pool => ({
            id: null,
            relationId: this.modalType === 'edit' ? this.modalData.relationId : null,
            poolId: pool.poolId,
            poolName: pool.poolName,
            rate: Number(pool.rate),
            supplierId: pool.supplierId,
            mccs: pool.mccs
          }))
        };

        // 更新数据 - 操作allCountriesData
        if (this.modalType === 'edit') {
          const index = this.allCountriesData.findIndex(
            item => item.uniqueId === this.modalData.uniqueId
          );
          if (index > -1) {
            this.allCountriesData.splice(index, 1, formattedData);
          }
        } else {
          this.allCountriesData.push(formattedData);
        }

        this.$Message.success(this.modalType === 'add' ? '添加成功' : '编辑成功');

        // 关闭modal
        this.showCountryModal = false;

        // 强制更新过滤数据
        this.applyFrontendFilters();

        // 重置分页到第一页
        this.currentPage = 1;
      },

      // 恢复 handleConfirm 方法
      handleConfirm() {
        // 校验关联组名称
        if (!this.formObj.groupName || this.formObj.groupName.trim() === '') {
          this.$Message.error('关联组名称不能为空或仅为空格');
          return;
        }

        // 校验是否有数据
        if (this.allCountriesData.length === 0) {
          this.$Message.error('请至少添加一个国家');
          return;
        }

        // 格式化数据 - 使用allCountriesData中的所有最新数据
        const formattedData = {
          groupId: this.pageType == '2' ? this.$route.query.groupId : undefined,
          groupName: this.formObj.groupName,
          mccCardPoolRelationVos: this.allCountriesData.map(country => {
            // 获取该国家下的所有卡池
            const cardPools = country.mccCardPoolDetailDTOS || [];

            // 获取该国家下的所有 mcc
            const mccs = country.countryDTOS.map(item => item.mcc);

            // 为每个卡池创建一个对象
            return {
              mcc: mccs,
              mccCardPoolDetailVos: cardPools.map(pool => ({
                poolId: pool.poolId,
                rate: Number(pool.rate),
                mccs: mccs
              }))
            };
          })
        };

        // 调用接口提交数据
        this.submitting = true;
          let func = this.pageType == '2' ? updatecardPoolMccGroup : addcardPoolMccGroup;
          func(formattedData).then(res => {
            if (res && res.code === '0000') {
              this.$Notice.success({
                title: '操作提示',
                desc: '操作成功'
              });

              // 如果是编辑模式，重新加载数据
              if (this.pageType === '2') {
                this.getCardPoolGroupDetailNew(this.$route.query.groupId, this.$route.query.mccs);
              } else {
                // 新增模式清空数据
                this.allCountriesData = [];
                this.filteredCountries = [];
              }

              // 返回列表页
              this.handleBack();
            }
          }).catch(error => {
            console.error('保存失败:', error);
          }).finally(() => {
            this.submitting = false;
          });
      },

      // 新增国家
      addCountryModal() {
        // 保存当前状态
        this.tempSelectedCountries = [...this.countryData];
        this.tempSelectedPools = [...this.selectedPools];

        // 重置Modal数据
        this.resetMainModalState();

        // 设置Modal显示
        this.modalType = 'add';
        this.modalTitle = '新增国家';
        this.showCountryModal = true;
        this.groupName = this.formObj.groupName;
        this.activeTabName = 'name1';

        // 初始化数据
        this.countryData = [];
        this.selectedPools = [];
        this.countrySearchList = [];
        this.countrySearchEn = '';
      },

      showModal(type, row) {
        // 重置所有状态
        this.countryData = [];
        this.selectedPools = [];
        this.poolSelectionMap = new Map();
        this.poolRateMap = new Map();
        this.modalCurrentPage = 1;
        this.searchCountry = '';
        this.poolData = [];
        this.poolCurrentPage = 1;
        this.searchCardPool = '';
        this.allPoolsData = [];
        this.isPoolDataReady = true;
        this.activeTabName = 'name1';

        // 设置modal相关数据
        this.modalType = type;
        this.modalTitle = type === 'add' ? '新增国家' : '编辑国家';
        this.showCountryModal = true;
        this.modalData = row;
        this.groupName = this.formObj.groupName;

        if (type === 'edit' && row) {
          // 设置国家数据
          this.countryData = JSON.parse(JSON.stringify(row.countryDTOS || []));

          // 设置卡池数据
          if (row.mccCardPoolDetailDTOS && row.mccCardPoolDetailDTOS.length > 0) {
            this.selectedPools = JSON.parse(JSON.stringify(row.mccCardPoolDetailDTOS));

            // 初始化选中状态和比例
            this.selectedPools.forEach(pool => {
              if (pool && pool.poolId) {
                this.poolSelectionMap.set(pool.poolId, true);
                // 保持原有的rate值
                const rate = pool.rate !== undefined ? pool.rate : 0;
                this.poolRateMap.set(pool.poolId, rate);
              }
            });
          }

          // 详情页不自动加载卡池数据
          if (this.pageType !== '4') {
            this.$nextTick(() => {
              this.loadPoolData(1, true);
              // 新增：初始化搜索列表
              this.countrySearchList = [...this.countryData];
              this.countrySearchEn = '';
            });
          } else {
            // 详情页初始化搜索列表
            this.countrySearchList = [...this.countryData];
            this.countrySearchEn = '';
          }
        } else {
          // 新增：初始化搜索列表
          this.countrySearchList = [...this.countryData];
          this.countrySearchEn = '';
        }
      },

      // 修改卡池加载方法
      async loadPoolData(page, keepSelection = false) {
        // 详情页不自动加载卡池数据
        if (this.pageType === '4') {
          return;
        }

        if (this.countryData.length === 0) {
          this.allPoolsData = [];
          this.selectedPools = [];
          this.poolSelectionMap.clear();
          this.poolRateMap.clear();
          this.updateCurrentPageData(1);
          this.isPoolDataReady = true;
          return;
        }

        this.poolLoading = true;
        this.isPoolDataReady = false;

        try {
          const params = {
            poolName: this.searchCardPool,
            num: -1,
            size: -1
          };

          // 如果是详情页，使用选中的MCC
          if (this.pageType === '4') {
            params.mcc = this.selectedMcc;
          } else {
            params.mcc = this.countryData.map(country => country.mcc).toString();
          }

          // 检查国家是否发生变化
          const currentMccs = params.mcc;
          const hasCountryChanged = currentMccs !== this.lastCountryMccs;
          this.lastCountryMccs = currentMccs;

          const res = await getCardPoolinfoBymccNew(params);

          if (res && res.code === '0000') {
            // 获取所有卡池数据
            this.allPoolsData = res.data.records || [];

            // 只有当国家发生变化时才筛选已选卡池
            if (hasCountryChanged && this.selectedPools.length > 0) {
              // 获取已选卡池的ID列表
              const selectedPoolIds = this.selectedPools.map(pool => pool.poolId);

              // 更新selectedPools，只保留在allPoolsData中存在的卡池
              this.selectedPools = this.selectedPools.filter(pool =>
                this.allPoolsData.some(p => p.poolId === pool.poolId)
              );

              // 更新poolRateMap
              this.selectedPools.forEach(pool => {
                if (pool && pool.poolId) {
                  this.poolRateMap.set(pool.poolId, pool.rate);
                }
              });
            }

            this.updateCurrentPageData(page);
            this.isPoolDataReady = true;
          } else {
            this.isPoolDataReady = true;
            this.$Message.error(res.msg || '加载卡池数据失败');
          }
        } catch (error) {
          console.error('加载卡池数据失败:', error);
          this.$Message.error('加载卡池数据失败，请重试');
          this.isPoolDataReady = true;
        } finally {
          this.poolLoading = false;
        }
      },

      manualSearchPools() {
        if (this.pageType === '4' && this.selectedMcc) {
          this.poolLoading = true;
          this.isPoolDataReady = false;

          const params = {
            poolName: this.searchCardPool,
            num: -1,
            size: -1,
            mcc: this.selectedMcc
          };

          getCardPoolinfoBymccNew(params).then(res => {
            if (res && res.code === '0000') {
              this.allPoolsData = res.data.records || [];
              this.updateCurrentPageData(1);
            } else {
              this.$Message.error(res.msg || '加载卡池数据失败');
            }
          }).catch(error => {
            console.error('加载卡池数据失败:', error);
            this.$Message.error('加载卡池数据失败，请重试');
          }).finally(() => {
            this.poolLoading = false;
            this.isPoolDataReady = true;
          });
        }
      },

      // 修改 updateCurrentPageData 方法
      updateCurrentPageData(page) {
        if (!this.allPoolsData || this.allPoolsData.length === 0) {
          this.poolData = [];
          this.poolTotal = 0;
          this.poolCurrentPage = 1;
          return;
        }

        const start = (page - 1) * this.poolPageSize;
        const end = start + this.poolPageSize;

        // 获取当前页数据
        const currentPageData = this.allPoolsData.slice(start, end);

        // 处理选中状态和比例
        currentPageData.forEach(pool => {
          const selectedPool = this.selectedPools.find(selected => selected.poolId === pool.poolId);
          if (selectedPool) {
            // 保持原有的rate值
            pool.rate = selectedPool.rate;
            // 设置选中状态
            this.$set(pool, '_checked', true);
          }
        });

        // 更新数据
        this.poolData = currentPageData;
        this.poolTotal = this.allPoolsData.length;
        this.poolCurrentPage = page;

        // 在下一个tick中设置选中状态
        this.$nextTick(() => {
          if (this.$refs.poolTable) {
            this.$refs.poolTable.selectAll(false);
            this.poolData.forEach((pool, index) => {
              if (pool._checked) {
                this.$refs.poolTable.toggleSelect(index);
              }
            });
          }
        });
      },

      handlePoolPageChange(page) {
        this.updateCurrentPageData(page);
      },

      handlePoolSelection(selection) {
        if (this.pageType === '4') return;

        // 获取当前页所有卡池的ID
        const currentPagePoolIds = this.poolData.map(pool => pool.poolId);

        // 保留不在当前页的已选卡池
        const otherPageSelectedPools = this.selectedPools.filter(pool =>
          !currentPagePoolIds.includes(pool.poolId)
        );

        // 合并当前页选中的卡池和其他页的已选卡池
        this.selectedPools = [
          ...otherPageSelectedPools,
          ...selection.map(pool => ({
            ...pool,
            rate: this.poolRateMap.get(pool.poolId) || '' // 使用已保存的rate值或空字符串
          }))
        ];

        // 更新poolSelectionMap
        this.poolData.forEach(pool => {
          this.poolSelectionMap.set(pool.poolId, selection.some(selected => selected.poolId === pool.poolId));
        });

        // 处理卡池占比
        this.handlePoolRateUpdate();
      },

      handlePoolSelectAll(selection) {
        if (this.pageType === '4') return;

        // 获取当前页所有卡池的ID
        const currentPagePoolIds = this.poolData.map(pool => pool.poolId);

        if (selection.length > 0) {
          // 全选 - 保留其他页的已选卡池，添加当前页所有卡池
          const otherPageSelectedPools = this.selectedPools.filter(pool =>
            !currentPagePoolIds.includes(pool.poolId)
          );

          this.selectedPools = [
            ...otherPageSelectedPools,
            ...this.poolData.map(pool => ({
              ...pool,
              rate: this.poolRateMap.get(pool.poolId) || '' // 使用已保存的rate值或空字符串
            }))
          ];

          // 更新poolSelectionMap
          this.poolData.forEach(pool => {
            this.poolSelectionMap.set(pool.poolId, true);
          });
        } else {
          // 取消全选 - 只保留其他页的已选卡池
          this.selectedPools = this.selectedPools.filter(pool =>
            !currentPagePoolIds.includes(pool.poolId)
          );

          // 更新poolSelectionMap
          this.poolData.forEach(pool => {
            this.poolSelectionMap.set(pool.poolId, false);
          });
        }

        // 处理卡池占比
        this.handlePoolRateUpdate();
      },

      // 新增国家
      addCountry() {
        this.modal1 = true;
          // 只回显已选国家，并过滤掉已经存在于existingMccs中的国家
          this.mccObj.country = this.countryData
            .map(item => item.mcc)
            .filter(mcc => !this.existingMccs.has(mcc));
      },

      // 批量添加国家
      batchAddCountry() {
        this.$refs.batchAddModal.modal = true;
        // 不动countrySearchList
      },

      // 清空国家
      clearAllCountry() {
        this.countryData = [];
        this.selectedPools = [];
        this.modalCurrentPage = 1;
        this.searchCountry = '';
        this.poolData = [];
        this.poolCurrentPage = 1;
        this.searchCardPool = '';
        this.tempSelectedCountries = [];
        this.tempSelectedPools = [];
        // 触发卡池列表加载
        this.loadPoolData(1, true);
        // 清空后同步countrySearchList
        this.countrySearchList = [...this.countryData];
        this.countrySearchEn = '';
      },

      // 删除国家
      handleDeleteCountry(row) {
        const sourceData = this.pageType === '4' ? this.selectedCountries : this.allCountriesData;
        const index = sourceData.findIndex(item => item.uniqueId === row.uniqueId);

        if (index > -1) {
          sourceData.splice(index, 1);
        }

        // 立即更新过滤数据
        this.applyFrontendFilters();

        // 计算当前页
        const totalPages = Math.ceil(this.filteredCountries.length / this.pageSize);
        if (this.currentPage > totalPages) {
          this.currentPage = Math.max(1, totalPages);
        }
      },

      getContinentMcc() {
        getContinentMcc().then(res => {
          if (res && res.code == '0000') {
            this.continentsData = res.data

            // 提取所有国家数据并放入一个数组
            let countries = [];
            for (let continent in res.data) {
              if (res.data.hasOwnProperty(continent)) {
                countries = countries.concat(res.data[continent]);
              }
            }

            // 根据国家英文名称的首字符对数组进行排序
            countries.sort((a, b) => {
              const charA = a.countryEn.charAt(0).toLowerCase();
              const charB = b.countryEn.charAt(0).toLowerCase();
              if (charA < charB) return -1;
              if (charA > charB) return 1;
              return 0;
            });
            this.countryList = countries

            // 如果是编辑模式，加载详情数据
            if (this.pageType != '1') {
              let groupId = this.$route.query.groupId;
              let mccs = this.$route.query.mccs;
              this.formObj.groupName = this.$route.query.groupName;
              this.getCardPoolGroupDetailNew(groupId, mccs);
            }
          } else {
            throw res
          }
        }).catch((err) => {
          console.error('获取国家列表失败:', err);
          this.$Message.error('获取国家列表失败，请重试');
        }).finally(() => {
          this.loading = false;
        })
      },

      // 搜索卡池
      searchPools() {
        this.poolCurrentPage = 1;
        if (this.pageType === '4') {
          // 详情页使用手动搜索
          this.manualSearchPools();
        } else {
          // 原有搜索逻辑
          this.loadPoolData(1, true);
        }
      },

      // 修改卡池占比变化处理方法
      handleRateChange(row) {
        // 更新selectedPools中对应卡池的rate值
        const index = this.selectedPools.findIndex(pool => pool.poolId === row.poolId);
        if (index > -1) {
          this.$set(this.selectedPools[index], 'rate', row.rate);
        }

        // 更新poolRateMap
        this.poolRateMap.set(row.poolId, row.rate);

        // 处理卡池占比
        this.handlePoolRateUpdate();
      },

      // 处理移除卡池
      handleRemovePool(row, index) {
        // 从selectedPools中移除指定poolId的卡池
        const poolIndex = this.selectedPools.findIndex(pool => pool.poolId === row.poolId);
        if (poolIndex > -1) {
          this.selectedPools.splice(poolIndex, 1);
        }

        // 更新poolSelectionMap，只更新被删除的卡池状态
        this.poolSelectionMap.set(row.poolId, false);

        // 更新poolRateMap，只删除被删除的卡池的比例
        this.poolRateMap.delete(row.poolId);

        // 处理卡池占比
        this.handlePoolRateUpdate();

        // 更新当前页的选中状态
        this.$nextTick(() => {
          if (this.$refs.poolTable) {
            // 只更新被删除卡池的选中状态
            this.poolData.forEach(pool => {
              if (pool.poolId === row.poolId) {
                this.$set(pool, '_checked', false);
              }
            });
          }
        });
      },

      handlePageChange(page) {
        this.currentPage = page;
      },

      handleModalPageChange(page) {
        this.modalCurrentPage = page;
      },

      handleBack() {
        // 清除localStorage数据
        localStorage.removeItem('countrySelectFormData');
        localStorage.removeItem('countrySelectResult');
        this.$router.push({
          name: 'associationGroup'
        });
      },

      // 详情页查询关联组详情
      searchGroupPoolList() {
        this.currentPage = 1;
        this.applyFrontendFilters();
      },

      getCardPoolGroupDetailNew(groupId, mccs) {
        this.loading = true;
        getCardPoolGroupDetailNew({
          num: -1,
          size: -1,
          groupId: groupId,
          mccs: mccs,
          mcc: this.formObj.selectedMcc,
          poolName: this.pageType == '4' ? this.formObj.searchCardPool : undefined,
        }).then(res => {
          if (res.code == '0000') {
            // 根据页面类型决定存储到哪个数组
            if (this.pageType === '4') {
              this.selectedCountries = res.data.records.map((record, index) => ({
                uniqueId: `country_${Date.now()}_${index}`,
                relationId: record.relationId,
                countryDTOS: record.countryDTOS,
                mccCardPoolDetailDTOS: record.mccCardPoolDetailDTOS
              }));
            } else {
              this.allCountriesData = res.data.records.map((record, index) => ({
                uniqueId: `country_${Date.now()}_${index}`,
                relationId: record.relationId,
                countryDTOS: record.countryDTOS,
                mccCardPoolDetailDTOS: record.mccCardPoolDetailDTOS
              }));
            }

            // 应用过滤
            this.applyFrontendFilters();
          }
        }).catch((err) => {
          console.error(err);
        }).finally(() => {
          this.loading = false;
        });
      },

      // 修改卡池占比处理方法
      handlePoolRateUpdate() {
        // 如果只有一个卡池且没有设置比例，设置为100%
        if (this.selectedPools.length === 1 && (!this.selectedPools[0].rate || this.selectedPools[0].rate.toString()
            .trim() === '')) {
          this.selectedPools[0].rate = 100;
          return;
        }

        // 如果之前只有一个卡池且比例为100%，现在添加了新的卡池，重置所有比例为空字符串
        if (this.selectedPools.length > 1) {
          const hasSinglePoolWith100Rate = this.selectedPools.length === 2 &&
            this.selectedPools.some(pool => pool.rate === 100);

          if (hasSinglePoolWith100Rate) {
            this.selectedPools.forEach(pool => {
              pool.rate = '';
            });
          }
        }
      },

      // 新增：国家英文名搜索
      handleCountrySearch() {
        const keyword = (this.countrySearchEn || '').trim().toLowerCase();

        // 创建临时副本用于搜索
        const searchSource = [...this.countryData];

        if (!keyword) {
          this.countrySearchList = searchSource;
        } else {
          this.countrySearchList = searchSource.filter(
            c => (c.countryEn || '').toLowerCase().includes(keyword)
          );
        }
        this.modalCurrentPage = 1;

        // 详情页国家搜索后可能需要清空卡池数据
        if (this.pageType === '4') {
          this.poolData = [];
          this.allPoolsData = [];
        }
      },

      // 修改清空国家方法
      clearCountry(id) {
        // 原始数据中删除
        const index = this.countryData.findIndex(item => item.id === id);
        if (index > -1) {
          this.countryData.splice(index, 1);
        }

        // 搜索列表中删除
        const searchIndex = this.countrySearchList.findIndex(item => item.id === id);
        if (searchIndex > -1) {
          this.countrySearchList.splice(searchIndex, 1);
        }

        // 触发卡池列表加载
        this.loadPoolData(1, true);
        // 删除后同步countrySearchList
        this.countrySearchList = [...this.countryData];
        this.countrySearchEn = '';
      },

      applyFrontendFilters() {
        this.loading = true;
        this.$nextTick(() => {
          const mccFilter = this.formObj.selectedMcc;
          const cardPoolFilter = this.formObj.searchCardPool?.toLowerCase().trim() || '';
          const sourceData = this.pageType === '4' ? this.selectedCountries : this.allCountriesData;

          this.filteredCountries = sourceData.filter(group => {
            const hasMcc = mccFilter
              ? group.countryDTOS.some(country => country.mcc === mccFilter)
              : true;

            const hasCardPool = cardPoolFilter
              ? group.mccCardPoolDetailDTOS.some(pool =>
                  pool.poolName.toLowerCase().includes(cardPoolFilter)
                )
              : true;

            return hasMcc && hasCardPool;
          });

          // 确保分页重置到第一页
          this.currentPage = 1;
          this.loading = false;
        });
      },
    },
  }
</script>

<style scoped>
  /* 添加自定义样式 */
  .country-select-page {
    .form-container {
      margin-bottom: 20px;
    }

    .country-container {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      .country-table {
        width: 80%;
        min-width: 0;

        :deep(.ivu-table-wrapper) {
          overflow-x: auto;
        }
      }

      .action-buttons {
        width: 20%;
        display: flex;
        flex-direction: column;
        gap: 10px;
        justify-content: flex-start;
        padding-top: 20px;
      }
    }

    .card-pool-container {
      .search-bar {
        margin-bottom: 20px;
        display: flex;
        gap: 10px;
      }

      .pool-tables {
        display: flex;
        gap: 20px;

        .pool-list {
          width: 60%;
          min-width: 0;

          :deep(.ivu-table-wrapper) {
            overflow-x: auto;
          }
        }

        .selected-pools {
          width: 40%;
          min-width: 0;

          :deep(.ivu-table-wrapper) {
            overflow-x: auto;
          }
        }
      }
    }

    .pagination-wrapper {
      margin-top: 20px;
      text-align: left;
    }

    .footer {
      margin-top: 20px;
      text-align: center;

      button+button {
        margin-left: 10px;
      }
    }

    .country-modal {
      :deep(.ivu-modal) {
        width: 80% !important;
        max-width: 1400px;
      }

      :deep(.ivu-modal-body) {
        max-height: calc(80vh - 150px);
        overflow-y: auto;
        padding: 16px 24px;

        .ivu-tabs-tabpane {
          display: flex !important;
          gap: 20px;
          margin-top: 16px;

          &[data-tab="country"] {
            >div:first-child {
              width: 80%;
              min-width: 0;
            }

            >div:last-child {
              width: 20%;
              min-width: 0;
              padding-top: 20px;

              .button-group {
                display: flex;
                flex-direction: column;
                gap: 10px;
              }
            }
          }

          &[data-tab="cardPool"] {
            >div:first-child {
              width: 60%;
              min-width: 0;

              .search-bar {
                margin-bottom: 16px;
                display: flex;
                gap: 10px;
              }
            }

            >div:last-child {
              width: 40%;
              min-width: 0;
            }
          }

          :deep(.ivu-table-wrapper) {
            overflow-x: auto;
            border: 1px solid #dcdee2;
            border-radius: 4px;
          }
        }
      }
    }
  }

  .footer-textarea {
    display: flex;
    justify-content: center;
    margin: 30px 0;
  }

  .selected-pools-table {
    height: 400px;
    overflow-y: auto;
  }

  /* 去掉input为number的上下箭头 */
  :deep(input::-webkit-outer-spin-button),
  :deep(input::-webkit-inner-spin-button) {
    -webkit-appearance: none;
  }

  :deep(input[type="number"]) {
    -moz-appearance: textfield;
  }

  .country-select-modal {
    :deep(.ivu-modal) {
      .ivu-modal-content {
        height: 600px;

        .ivu-modal-body {
          height: calc(100% - 100px);
          padding: 16px;
          display: flex;
          flex-direction: column;
        }
      }
    }
  }
</style>
