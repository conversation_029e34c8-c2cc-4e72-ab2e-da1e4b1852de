<template>
	<!-- 卡池详情 -->
	<Card>
<!-- 		<div style="display: flex;justify-content: flex-start;align-items: center;margin-top: 10px;margin-left: 10px;">
			<Button  @click="back">
				<Icon type="ios-arrow-back" />&nbsp;返回
			</Button>
		</div> -->
		<!-- 编辑搜索 搜索条件-->
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">国家/地区</span>
				<Select v-model="searchObj.mcc"  filterable
				style="width: 500px;"
				:clearable="true"
				multiple
				placeholder="请选择国家/地区"
				>
					<Option v-for="item in countryList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
				</Select>
			</div>
			<div class="search_box">
				<span class="search_box_label">资源供应商</span>
				<Select v-model="searchObj.supplierId"
					class="i-Select"
					style="width: 200px;"
					multiple
					placeholder="请选择资源供应商"
					>
					<Option  v-for="item in supplierList"
					:value="item.supplierId"
					:key="item.supplierId">
					{{item.supplierName.length > 11 ? item.supplierName.substring(0,11) + "…" : item.supplierName}}
					</Option>
				</Select>
			</div>
			<div class="search_box">
				<span class="search_box_label">卡池</span>
				<Input v-model="searchObj.poolName" :clearable="true" placeholder="请输入卡池名称" style="width: 200px;"/>
			</div>
			<div class="search_box">
				<Button   type="primary" icon="md-search" :loading="searchloading" @click="search()">搜索</Button>&nbsp;
			    <Button  @click="back">
			    	<Icon type="ios-arrow-back" />&nbsp;返回
			    </Button>
			</div>
		</div>

		<Scroll  :height="800"  :on-reach-bottom="handleReachBottom" style="text-align: left;width:100%;">
			<!-- 表格 -->
			<Table
			:columns="columns"
			:data="tableDataList"
			style="width:100%;margin-top: 40px;"
			:loading="loading">
				<template slot-scope="{ row, index }" slot="detail">
					<Button  type="warning" ghost style="margin-right: 10px;" @click="showdetail(row)">点击查看详情</Button>
				</template>
				<template slot-scope="{ row, index }" slot="cardPoolDetails">
					<Scroll  :height="70" style="text-align: left;width:100%;">
						<div v-for="(item,count) in row.cardPoolDetails" :key="count">
							<span>{{item.poolName}}</span>
							<span>({{item.rate}}%)</span>
						</div>
					</Scroll>
				</template>
				<template slot-scope="{ row, index }" slot="action">
					<Button  type="primary" ghost style="margin-right: 10px;" @click="update(row)">编辑</Button>
					<Button  type="warning" ghost style="margin-right: 10px;" @click="copy(row)">复制</Button>
					<Button  type="error" ghost style="margin-right: 10px;" @click="delItem(row)">删除</Button>
				</template>
			</Table>
		</Scroll>
	</Card>
</template>

<script>
	import{getCardPoolGroupDetail} from '@/api/associationGroup/cardPoolMccGroup.js'
	import {supplier} from '@/api/ResourceSupplier'
	import {opsearchAll} from '@/api/operators';
	export default{
		data(){
			return{
				loading: false,
				searchloading:false,//搜索加载
				groupId:'',//关联组id
				data:[],
				tableDataList: [],
				// 懒加载分页
				pageIndex: 1,
				pageSize: 10,
				pageCount: 0,
				countryList:[],//国家地区列表
				supplierList:[],//资源供应商列表
				searchObj:{
					"mcc": [],
					"poolName":  '',
					"supplierId":  [],
				},//查询搜索条件
				columns:[
					{
						title: "国家/地区",
						key: 'countryEn',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "资源供应商",
						key: 'supplierId',
						minWidth: 120,
						align: 'center',
						render:(h,params) =>{
							const row=params.row
							let list=this.supplierList.filter((item)=> item.supplierId===row.supplierId)
							var text=list.length>0 ? list[0].supplierName:""
							return h('label',text)
						}
					},
					{
						title: "卡池",
						slot: 'cardPoolDetails',
						minWidth: 120,
						align: 'center',
					},
					{
						title: "仅支持热点",
						key: 'isOnlySupportedHotspots',
						minWidth: 120,
						align: 'center',
						render: (h, params) => {
							const row = params.row
							var text = row.isOnlySupportedHotspots==='1' ? "是"
							:row.isOnlySupportedHotspots==='2' ? "否" :""
							return h('label', text)
						}
					},
					{
						title: "流量上限(MB)",
						key: 'consumption',
						minWidth: 120,
						align: 'center'
					},
					{
						title: "备注",
						key: 'remarks',
						minWidth: 120,
						align: 'center',
						tooltip: true,
						render: (h, params) => {
							const row = params.row
							var text = row.remarks
							let length = text === "" || text === null ? 0 : text.length
							if(length > 20){
								let content = ""
								for(let i=0;i<=row.remarks.length;){
									content=content+text.slice(i, i+17)+','
									i=i+18
								}
								text = text.substring(0, 20) + "..."
								return h('div', [h('Tooltip', {
									props: {
										placement: 'bottom',
										transfer: true //是否将弹层放置于 body 内
									},
									style: {
										cursor: 'pointer',
						            },
								},
								[ //这个中括号表示是Tooltip标签的子标签
									text, //表格列显示文字
						           h('label', {
										slot: 'content',
										style: {
											whiteSpace: 'normal'
										},
										domProps: {
											innerHTML: content.replace(/\,/g, "</br>")
										},
									}, )
								])
							  ]);
							}else {
								return h('label', text)
							}
						}
					},
				]
			}
		},
		mounted() {
			// 保存上一页返回数据
			localStorage.setItem("searchObj", decodeURIComponent(this.$route.query.searchObj))
			let list=JSON.parse(decodeURIComponent(this.$route.query.obj))
			this.groupId=list.groupId
			this.getLocalList()
			this.getsupplier()
			this.goPageFirst(1)
		},
		methods:{
			back(){
				this.$router.go(-1)
			},
			// 搜索按钮查询
			search(){
				this.pageIndex= 1
				this.pageCount= 0
				this.searchloading=true
				this.goPageFirst(1)
			},
			// 滚动条拉到底部,在加载数据
			handleReachBottom(){
				return new Promise(resolve => {
					setTimeout(() => {
						let _this = this
						_this.pageIndex++;
						if (_this.pageIndex > Math.ceil(_this.pageCount / _this.pageSize)) {
						  return;
						}
						_this.loadMoreTableData(_this.pageIndex);
						resolve();
					}, 2000);
				});
			},
			// 懒加载数据
			loadMoreTableData(pageIndex){
				let _this = this
				//需要执行的代码
				if (_this.pageCount <= _this.pageSize) {
					_this.tableDataList = _this.data;
				}else{
					let pageIndexs = Math.ceil(_this.pageCount / _this.pageSize);
					if (pageIndex > pageIndexs) {
						pageIndex = pageIndexs;
					}
					let start = (pageIndex - 1) * _this.pageSize;
					let end = pageIndex * _this.pageSize;
					_this.tableDataList = _this.tableDataList.concat(_this.data.slice(start, end));
				
				}
			},
			/**
			*
			* ----------------------------------初始化信息-----------------------------------
			*
			*/
			goPageFirst(pageIndex) {
				this.loading = true
				var _this = this
				let obj={
				  "groupId": this.groupId,
				  "mcc": this.searchObj.mcc,
				  "poolName": this.searchObj.poolName,
				  "supplierId": this.searchObj.supplierId
				}
				getCardPoolGroupDetail(obj).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						_this.data = res.data
						_this.pageCount = _this.data.length;
						_this.tableDataList=[]
						this.loadMoreTableData(1)
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchloading=false
				})
			},
			//国家/地区
			getLocalList() {
				opsearchAll().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.countryList = list;
						this.countryList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
						var localMap = new Map();
						list.map((local, index) => {
							localMap.set(local.mcc, local.countryEn);
						});
						this.localMap = localMap;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			// 获取资源供应商
			getsupplier(){
				supplier({
					pageNum:-1,
					pageSize:-1,
				}).then(res => {
					if (res.code == '0000') {
						this.supplierList = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				})
			},
		}
	}
</script>

<style scoped="scoped">
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}
	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}
	.search_box_label {
		font-weight: bold;
		text-align: center;
		width:150px;
	}
</style>
