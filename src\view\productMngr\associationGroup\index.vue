<template>
  <!-- 国家卡池关联组首页 -->
  <Card>
    <!-- 搜索条件 -->
    <div class="search_head_i">
      <div class="search_box">
        <span class="search_box_label">关联组名称</span>
        <Input v-model="groupName" :clearable="true" placeholder="请输入关联组名称" style="width: 200px;" />
      </div>
      <div class="search_box">
        <span class="search_box_label">国家/地区</span>
        <Select v-model="mcc" filterable style="width: 200px;" placeholder="请选择国家/地区" :clearable="true">
          <Option v-for="item in countryList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
        </Select>
      </div>
      <div class="search_box">
        <Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search()">搜索</Button>
        <Button v-has="'add'" style="margin: 0 20px" type="info" @click="add">
          <div style="display: flex;align-items: center;">
            <Icon type="md-add" />&nbsp;新增
          </div>
        </Button>
      </div>
    </div>
    <!-- 表格 -->
    <Table :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
      <template slot-scope="{ row, index }" slot="detail">
        <Button v-has="'info'" type="warning" ghost style="margin-right: 10px;" @click="showdetail(row)">点击查看详情</Button>
      </template>
      <template slot-scope="{ row, index }" slot="action">
        <Button v-has="'update'" type="primary" ghost style="margin-right: 10px;" :loading="uploading"
          @click="update(row)">编辑</Button>
        <Button v-has="'copy'" type="warning" ghost style="margin-right: 10px;" :loading="copyloading"
          @click="copy(row)">复制</Button>
        <Button v-has="'delete'" type="error" ghost style="margin-right: 10px;" @click="delItem(row)">删除</Button>
      </template>
    </Table>
    <!-- 分页 -->
    <div style="margin-top:15px">
      <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
    </div>
  </Card>
</template>

<script>
  import {
    opsearchAll
  } from '@/api/operators';
  import {
    addcardPoolMccGroup,
    getCardPoolGroup,
    updatecardPoolMccGroup,
    getCardPoolGroupDetail,
    batchDelete,
  } from '@/api/associationGroup/cardPoolMccGroup.js'
  export default {
    data() {
      return {
        total: 0,
        currentPage: 1,
        page: 0,
        loading: false,
        searchloading: false,
        submitloading: false,
        choseloading: false, //选择卡池加载
        mcc: '', //国家/地区
        groupName: '', //关联组名称
        countryList: [], //国家地区列表
        data: [], //表格列表
        cardpoolmodel: false, //卡池弹窗
        uploading: false, //编辑按钮加载
        copyloading: false, //复制按钮加载
        cardpoolTitle: '新增关联组',
        flag: '',
        poolIdList: [], //卡池列表
        tableDataList: [], //全局已选卡池列表
        overallDataList: [], //全局已选卡池列表
        columns: [{
            title: "关联组名称",
            key: 'groupName',
            minWidth: 120,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              var text = row.groupName
              let length = text === "" || text === null ? 0 : text.length
              if (length > 20) {
                let content = ""
                for (let i = 0; i <= row.groupName.length;) {
                  content = content + text.slice(i, i + 17) + ','
                  i = i + 18
                }
                text = text.substring(0, 20) + "..."
                return h('div', [h('Tooltip', {
                    props: {
                      placement: 'bottom',
                      transfer: true //是否将弹层放置于 body 内
                    },
                    style: {
                      cursor: 'pointer',
                    },
                  },
                  [ //这个中括号表示是Tooltip标签的子标签
                    text, //表格列显示文字
                    h('label', {
                      slot: 'content',
                      style: {
                        whiteSpace: 'normal'
                      },
                      domProps: {
                        innerHTML: content.replace(/\,/g, "</br>")
                      },
                    }, )
                  ])]);
              } else {
                return h('label', text)
              }
            }
          },
          {
            title: "国家/地区",
            key: 'countryEn',
            minWidth: 120,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row
              var text = ''
              let i = 0
              let countryEn = []
              for (let key in row.countryEn) {
                countryEn.push(row.countryEn[key])
              }
              // 排序
              countryEn = countryEn.sort(function(str1, str2) {
                return str1.localeCompare(str2);
              });

              for (let key in countryEn) {
                i++
                if (i > 50) {
                  text = text + '.........'
                  break
                } else {
                  text = text + countryEn[key] + '、'
                }
              }

              let length = text === "" || text === null ? 0 : text.length
              if (length > 20) {
                let mcc = text.replace(/\、/g, "</br>")
                text = text.substring(0, 20) + "..."
                return h('div', [h('Tooltip', {
                    props: {
                      placement: 'bottom',
                      transfer: true //是否将弹层放置于 body 内
                    },
                    style: {
                      cursor: 'pointer',
                    },
                  },
                  [ //这个中括号表示是Tooltip标签的子标签
                    text, //表格列显示文字
                    h('label', {
                      slot: 'content',
                      style: {
                        whiteSpace: 'normal',
                      },
                      domProps: {
                        innerHTML: mcc.slice(0, mcc.length - 1)
                      },
                    }, )
                  ])]);
              } else {
                text = text;
                return h('label', text.slice(0, text.length - 1))
              }
            }
          },
          {
            title: "卡池详情",
            slot: 'detail',
            minWidth: 120,
            align: 'center'
          },
          {
            title: "操作",
            slot: 'action',
            minWidth: 250,
            align: 'center',
            fixed: 'right'
          },
        ],
      }
    },
    mounted() {
      // 获取缓存的搜索条件
      let searchObj = JSON.parse(localStorage.getItem("searchObj")) || {};
      if (searchObj) {
        this.groupName = searchObj.groupName || "";
        this.mcc = searchObj.mcc || "";
        this.currentPage = searchObj.currentPage || 1;
      }
      // 加载数据
      this.goPageFirst(this.currentPage);
      this.getLocalList();
    },
    methods: {
      goPageFirst(page) {
        this.loading = true
        var _this = this
        getCardPoolGroup({
          num: page,
          size: 10,
          groupName: this.groupName,
          mcc: this.mcc
        }).then(res => {
          if (res.code == '0000') {
            _this.loading = false
            this.searchLoading = false
            this.page = page
            this.currentPage = page
            this.total = Number(res.data.totalCount)
            this.data = res.data.records
          }
        }).catch((err) => {
          console.error(err)
        }).finally(() => {
          this.loading = false
          this.searchLoading = false
        })
      },
      search() {
        this.searchLoading = true
        this.goPageFirst(1)
      },
      goPage(page) {
        this.goPageFirst(page)
      },
      // 清除
      cancelModal() {
        localStorage.removeItem("cardpoolList")
        localStorage.removeItem("mccLists")
        localStorage.removeItem("tableDataList")
        this.cardpoolmodel = false
        this.$nextTick(() => {
          this.$refs['formObj'].resetFields()
        })
      },
      // 新增国家弹窗
      add() {
        // 保存搜索条件
        const searchObj = {
          groupName: this.groupName,
          mcc: this.mcc,
          currentPage: this.currentPage
        };
        localStorage.setItem("searchObj", JSON.stringify(searchObj));

        this.$router.push({
          name: 'addCardPool',
          query: {
            cardpoolTitle: "新增关联组",
            flag: '1',
          }
        })
      },
      //弹出选择卡池弹窗
      chosecardPool() {
        this.choseloading = true
        Promise.all([
          this.$refs.cardPool.show(this.flag, this.formObj.cardpoolList, this.formObj.mccLists)
        ]).then(res => {
          //需要执行的代码
          this.choseloading = false
        })
      },
      //跳转至详情界面
      showdetail(row) {
        // 保存搜索条件
        const searchObj = {
          groupName: this.groupName,
          mcc: this.mcc,
          currentPage: this.currentPage
        };
        localStorage.setItem("searchObj", JSON.stringify(searchObj));

        this.$router.push({
          name: 'cardPooldetails',
          query: {
            cardpoolTitle: "关联组详情",
            flag: '4',
            groupName: row.groupName,
            groupId: row.groupId,
            mccs: Object.keys(row.countryEn)
          }
        })
      },
      // 编辑
      update(row) {
        // 保存搜索条件
        const searchObj = {
          groupName: this.groupName,
          mcc: this.mcc,
          currentPage: this.currentPage
        };
        localStorage.setItem("searchObj", JSON.stringify(searchObj));

        this.$router.push({
          name: 'updateCardPool',
          query: {
            cardpoolTitle: "编辑关联组",
            flag: '2',
            groupName: row.groupName,
            groupId: row.groupId,
            mccs: Object.keys(row.countryEn)
          }
        })
      },
      // 复制
      copy(row) {
        // 保存搜索条件
        const searchObj = {
          groupName: this.groupName,
          mcc: this.mcc,
          currentPage: this.currentPage
        };
        localStorage.setItem("searchObj", JSON.stringify(searchObj));

        this.$router.push({
          name: 'copyCardPool',
          query: {
            cardpoolTitle: "复制关联组",
            flag: '3',
            groupName: row.groupName,
            groupId: row.groupId,
            mccs: Object.keys(row.countryEn)
          }
        })
      },
      // 删除
      delItem(row) {
        this.$Modal.confirm({
          title: '确认删除？',
          onOk: () => {
            batchDelete({
              groupIds: [row.groupId]
            }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirst(1)
              } else {
                throw res
              }
            }).catch((err) => {

            })
          }
        });
      },
      // 提交
      submit() {
        this.$refs["formObj"].validate((valid) => {
          if (valid) {
            let mccCardPoolRelationVos = []
            if (JSON.parse(localStorage.getItem("cardpoolList")) != null) {
              this.tableDataList = JSON.parse(localStorage.getItem("cardpoolList"))
            } else {
              this.tableDataList = this.overallDataList
            }
            this.tableDataList.map((item) => {
              if (!item.mccCardPoolDetailVos || item.mccCardPoolDetailVos.length < 1) {
                return
              }
              let obj = {
                consumption: item.consumption, //用量
                isOnlySupportedHotspots: item.isOnlySupportedHotspots, //是否只支持热点
                mcc: item.mcc, //国家码
                mccCardPoolDetailVos: item.mccCardPoolDetailVos, //卡池信息集合
                remarks: item.remarks, //备注,
                supplierId: item.supplierId, //供应商Id
              }
              mccCardPoolRelationVos.push(obj)
            })
            this.submitloading = true
            if (this.flag === 1 || this.flag === 3) { //新增
              addcardPoolMccGroup({
                groupName: this.formObj.groupName,
                mccCardPoolRelationVos: mccCardPoolRelationVos
              }).then(res => {
                if (res && res.code == '0000') {
                  this.$Notice.success({
                    title: "操作提示",
                    desc: "新增成功!",
                  })
                  this.cancelModal()
                  this.goPageFirst(1)
                } else {
                  throw res
                }
              }).catch((err) => {
                console.error(err)
              }).finally(() => {
                this.submitloading = false
              })
            } else { //修改
              updatecardPoolMccGroup({
                groupId: this.formObj.groupId,
                groupName: this.formObj.groupName,
                mccCardPoolRelationVos: mccCardPoolRelationVos
              }).then(res => {
                if (res && res.code == '0000') {
                  this.$Notice.success({
                    title: "操作提示",
                    desc: "编辑成功!",
                  })
                  this.cancelModal()
                  this.goPageFirst(1)
                } else {
                  throw res
                }
              }).catch((err) => {
                console.error(err)
              }).finally(() => {
                this.submitloading = false
              })
            }
          }
        })
      },
      //国家/地区
      getLocalList() {
        opsearchAll().then(res => {
          if (res && res.code == '0000') {
            var list = res.data;
            this.countryList = list;
            this.countryList.sort(function(str1, str2) {
              return str1.countryEn.localeCompare(str2.countryEn);
            });
            var localMap = new Map();
            list.map((local, index) => {
              localMap.set(local.mcc, local.countryEn);
            });
            this.localMap = localMap;
          } else {
            throw res
          }
        }).catch((err) => {

        }).finally(() => {

        })
      },
      goCountryPage(page) {
        this.countryCurrentPage = page;
        this.loading = true;
        this.countryTableData = [];
        this.countryTotal = 0;
        this.getLocalList();
      },
    }
  }
</script>

<style scoped="scoped">
  .search_head_i {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
  }

  .search_box {
    width: 300px;
    padding: 0 5px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: row;
    margin-bottom: 20px;
  }

  .search_box_label {
    font-weight: bold;
    text-align: center;
    width: 85px;
  }

  .footer_wrap {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
