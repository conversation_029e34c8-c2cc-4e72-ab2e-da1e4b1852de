<template>
	<div>
		<Card>
			<div class="search_head_i">
				<div class="search_box">
					<span class="search_box_label">卡池ID</span>
					<Input placeholder="请输入卡池ID" v-model="searchCondition.poolId" clearable style="width: 200px" />
				</div>
				<div class="search_box">
					<span class="search_box_label">卡池名称</span>
					<Input placeholder="请输入卡池名称" v-model="searchCondition.poolName" clearable style="width: 200px" />
				</div>
				<div class="search_box">
					<span class="search_box_label">供应商</span>
					<Input placeholder="请输入供应商名称" v-model="searchCondition.supplierName" clearable
						style="width: 200px" />
				</div>
			</div>
			<div class="search_head_i">
				<div class="search_box">
					<span class="search_box_label">国家/地区</span>
					<Select v-model="searchCondition.mcc" clearable placeholder="请选择国家/地区" style="width:200px"
						:filterable="true">
						<Option v-for="item in localList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
					</Select>
				</div>
				<div class="search_box">
					<span class="search_box_label">卡池类型</span>
					<Select v-model="searchCondition.usageType" clearable placeholder="请选择主卡类型" style="width:200px">
						<Option :value="item.value" v-for="(item,index) in usageTypes" :key="index">{{item.label}}
						</Option>
					</Select>
				</div>
				<div class="search_box">
					<Button style="margin: 0 4px" v-preventReClick type="primary" @click="search()" :loading="loading">
						<Icon type="ios-search" />&nbsp;搜索
					</Button>
					<Button style="margin: 0 4px" type="info" @click="addCardPool" v-has="'add'">
						<div style="display: flex;align-items: center;">
							<Icon type="md-add" />&nbsp;新建卡池
						</div>
					</Button>
				</div>
			</div>
			<div style="margin-top:20px">
				<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row, index }" slot="action">
						<Button type="primary" size="small" style="margin-right: 5px" v-has="'view'"
							@click="cardPoolDetails(row)">详情</Button>
						<Button type="success" size="small" style="margin-right: 5px" v-has="'update'"
							@click="updateCardPool(row)">修改</Button>
						<Button type="warning" size="small" style="margin-right: 5px" v-has="'copy'"
							@click="copyCardPool(row.poolId)">复制</Button>
						<Button type="error" size="small" style="margin-right: 5px" v-has="'delete'"
							@click="delCardPool(row.poolId)">删除</Button>
						<Button type="info" size="small" style="margin-right: 5px" v-has="'vimsiManage'"
							@click="toVimsi(row)">VIMSI管理</Button>
						<Button type="success" size="small" v-has="'export'" v-preventReClick
							@click="exportCardPool(row.poolId,row.poolName)">导出</Button>
					</template>
				</Table>
			</div>
			<div class="table-botton" style="margin-top:15px">
				<Page :total="total" :current.sync="currentPage" :page-size="pageSize" show-total show-elevator
					@on-change="goPage" style="margin: 10px 0;" />
			</div>
		</Card>
		<!-- 卡池新建/编辑/复制-->
		<Modal v-model="operateFlag" :title="operateTitle" :footer-hide="true" :mask-closable="false" width="530px">
			<div class="modal_content">
				<Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="130"
					:label-height="100" inline style="font-weight:bold;">
					<FormItem label="卡池类型" prop="usageType" style="width:420px">
						<Select v-model="formValidate.usageType" ref="usageType" clearable placeholder="请选择卡池类型"
							@on-change="typeChange">
							<Option :value="item.value" v-for="(item,index) in usageTypes" :key="index">{{item.label}}
							</Option>
						</Select>
					</FormItem>
					<div v-if="formValidate.usageType != ''&& formValidate.usageType != undefined">
						<FormItem label="卡池名称" prop="poolName" style="width:420px">
							<Input placeholder="请输入卡池名称" :maxlength="40" v-model="formValidate.poolName" clearable />
						</FormItem>
						<FormItem label="支持的国家/地区" prop="mcc" style="width:420px">
							<Select v-model="formValidate.mcc" placeholder="请选择支持的国家/地区" :filterable="true" multiple>
								<Option v-for="item in localList" :value="item.mcc" :key="item.id">{{ item.countryEn }}
								</Option>
							</Select>
						</FormItem>
						<FormItem label="供应商" prop="supplierId" style="width:420px">
							<Select v-model="formValidate.supplierId" clearable placeholder="请选择供应商">
								<Option :value="item.supplierId" v-for="(item,index) in providers" :key="index">
									{{item.supplierName}}
								</Option>
							</Select>
						</FormItem>
						<FormItem label="TPLID" prop="tplId" style="width:420px">
							<Input placeholder="请输入TPLID" :maxlength="50" v-model="formValidate.tplId" clearable />
						</FormItem>
						<!-- 普通卡池 -->
						<div v-if="formValidate.usageType == '1'">
							<FormItem label="支持HIMSI-4G上网" prop="isSupportHimsi" style="width:420px">
								<Select v-model="formValidate.isSupportHimsi" clearable placeholder="请选择是否支持HIMSI-4G上网">
									<Option value="1">是</Option>
									<Option value="2">否</Option>
								</Select>
							</FormItem>
						</div>
						<!-- 终端线上/下卡池 -->
						<div v-else>
							<div v-if="formValidate.usageType == '2'">
								<FormItem label="选择厂商" prop="corpId" style="width:420px">
									<Select v-model="formValidate.corpId" clearable placeholder="请选择厂商"
										@on-change="getPackageList">
										<Option :value="item.corpId" v-for="(item,index) in corpIds" :key="index">
											{{item.corpName}}
										</Option>
									</Select>
								</FormItem>
								<FormItem label="选择套餐" prop="packageId" style="width:420px">
									<Select v-model="formValidate.packageId"
										:disabled="formValidate.corpId=='' || formValidate.corpId==undefined" clearable
										placeholder="请选择套餐">
										<Option v-for="(item,index) in packages" :value="item.packageId" :key="index">
											{{item.packageName}}
										</Option>
									</Select>
								</FormItem>
								<FormItem label="套餐计算周期类型" prop="periodUnit" style="width:420px">
									<Select v-model="formValidate.periodUnit" clearable placeholder="请选择套餐计算周期类型">
										<Option :value="item.value" v-for="(item,index) in periodUnits" :key="index">
											{{item.label}}
										</Option>
									</Select>
								</FormItem>
								<FormItem label="持续周期数" prop="keepPeriod" style="width:420px">
									<Input placeholder="请输入持续周期数(非负整数)" :maxlength="11"
										v-model="formValidate.keepPeriod" clearable />
								</FormItem>
								<FormItem label="到期后是否重置" prop="isExpireReset" style="width:420px">
									<Select v-model="formValidate.isExpireReset" clearable placeholder="请选择到期后是否重置">
										<Option value="1">是</Option>
										<Option value="2">否</Option>
									</Select>
								</FormItem>
							</div>
							<div v-if="formValidate.usageType != '1'">
								<FormItem label="是否动态开户" prop="isOpenAccount" style="width:420px">
									<Select v-model="formValidate.isOpenAccount" clearable placeholder="请选择是否动态开户">
										<Option value="1">是</Option>
										<Option value="2">否</Option>
									</Select>
								</FormItem>
							</div>
						</div>
						<div v-if="formValidate.usageType != '2'">
							<FormItem label="是否动态签约" prop="isSignUpcc" style="width:420px">
								<Select v-model="formValidate.isSignUpcc" clearable placeholder="请选择是否动态签约">
									<Option value="1">是</Option>
									<Option value="2">否</Option>
								</Select>
							</FormItem>
						</div>
						<!-- 不支持HIMSI 4G上网||终端线上/下卡池 -->
						<div v-if="formValidate.isSupportHimsi == '2' || formValidate.usageType !='1'">
							<FormItem label="VIMSI冻结周期" prop="vimsiFreezeDay" style="width:420px">
								<Input placeholder="请输入VIMSI冻结周期(非负整数)" :maxlength="11"
									v-model="formValidate.vimsiFreezeDay">
								<span slot="append">分</span>
								</Input>
							</FormItem>
							<FormItem label="阈值" prop="alarmThreshold" style="width:420px">
								<Input placeholder="请输入阈值(0-100)" :maxlength="11" v-model="formValidate.alarmThreshold"
									clearable />
							</FormItem>
						</div>
					</div>
				</Form>
				<div style="text-align: center;margin: 4px 0;">
					<Button type="primary" @click="submit" v-has="['add','update','copy']" v-preventReClick
						:loading="submitFlag">提交</Button>
					<Button style="margin-left: 8px" @click="operateFlag = false">取消</Button>
				</div>
			</div>
		</Modal>
		<!-- 卡池详情模态框 -->
		<Modal v-model="detailsFlag" title="卡池详情" :footer-hide="true" :mask-closable="false" width="900px">
			<div class="modal_content">
				<div class="box">
					<span style="">卡池类型：&nbsp;&nbsp;{{details.usageType == "1" ? "全球卡普通卡池" : details.usageType == "2" ? "终端线下卡池" : details.usageType == "3" ? "终端线上卡池" : ""}}</span>
				</div>
				<div class="box">
					<span style="">卡池名称：&nbsp;&nbsp;{{details.poolName}}</span>
				</div>
				<div class="box">
					<span style="">支持国家/地区：&nbsp;&nbsp;{{details.mccsCn}}</span>
				</div>
				<div class="box">
					<span style="">供应商：&nbsp;&nbsp;{{details.supplierName}}</span>
				</div>
				<div class="box">
					<span style="">TPLID：&nbsp;&nbsp;{{details.tplId}}</span>
				</div>
				<div class="box" v-if="details.usageType != '2'">
					<span style="">是否动态签约：&nbsp;&nbsp;{{details.isSignUpcc == "1" ? "是" : "否" }}</span>
				</div>
				<div class="box" v-if="details.usageType != '1'">
					<span style="">是否动态开户：&nbsp;&nbsp;{{details.isOpenAccount == "1" ? "是" : "否" }}</span>
				</div>
				<!-- 全球卡普通卡池 -->
				<div class="box" v-if="details.usageType == '1'">
					<span style="">支持HIMSI-4G上网：&nbsp;&nbsp;{{details.isSupportHimsi == "1" ? "是" : "否" }}</span>
				</div>			
				<!-- 终端线下卡池 -->
				<div class="box" v-if="details.usageType == '2'">
					<span style="">厂商：&nbsp;&nbsp;{{detilCorpname}}</span>
				</div>
				<div class="box" v-if="details.usageType == '2'">
					<span style="">套餐：&nbsp;&nbsp;{{details.packageName}}</span>
				</div>
				<div class="box" v-if="details.usageType == '2'">
					<span style="">套餐计算周期类型：&nbsp;&nbsp;{{details.periodUnit == '1' ? '24小时' : details.periodUnit == '2' ?
					 '自然日' : details.periodUnit == '3' ? '自然月' : details.periodUnit == '4' ? '自然年' : ''}}</span>
				</div>
				<div class="box" v-if="details.usageType == '2'">
					<span style="">持续周期数：&nbsp;&nbsp;{{details.keepPeriod}}</span>
				</div>
				<div class="box" v-if="details.usageType == '2'">
					<span style="">到期后是否重置：&nbsp;&nbsp;{{details.isExpireReset == '1' ? '是' : '否'}}</span>
				</div>
				<!-- 全球卡普通卡时，支持HIMSI-4g上网时不展示 -->
				<div class="box" v-if="details.isSupportHimsi != '1'">
					<span style="">VIMSI冻结周期：&nbsp;&nbsp;{{details.vimsiFreezeDay}}</span>
				</div>
				<div class="box" v-if="details.isSupportHimsi != '1'">
					<span style="">告警阈值：&nbsp;&nbsp;{{details.alarmThreshold}}</span>
				</div>
				<div style="color:#878787;line-height: 30px;background-color: #f7f7f7;">
					<Collapse v-model="activeCollapsePanel" @on-change="handleCollapseChange">
						<Panel name="1">
							已绑定套餐
							<div slot="content"
								:style="{'height': details.packageList && details.packageList.length > 5 ? '150px' : '100%','overflowX': details.packageList && details.packageList.length > 5 ? 'auto' : 'visible'}">
								<!-- !details.packageList 则显示loadding,!details.packageList.length 则显示暂未绑定套餐 -->
								<div v-if="!details.packageList || !details.packageList.length">
									<div v-if="!details.packageList">	
										<div style="display: flex;justify-content: center;">
											<Icon type="ios-loading" size="large"></Icon>
										</div>
									</div>
									<div v-else style="display: flex;justify-content: center;">
										<div>暂未绑定套餐</div>
									</div>
								</div>
								<div v-for="(item,index) in details.packageList" :key="index" v-else>
									<Row>
										<Col span="10">
										<div
											style="overflow: hidden; text-overflow:ellipsis; white-space: nowrap;text-align: left;">
											套餐id：&nbsp;&nbsp;{{item.packageId}}</div>
										</Col>
										<Col span="8">
										<div
											style="overflow: hidden; text-overflow:ellipsis; white-space: nowrap;text-align: left;">
											套餐名称：&nbsp;&nbsp;{{item.nameCn}}</div>
										</Col>
										<Col span="6">
										<div style="overflow: hidden; text-overflow:ellipsis; white-space: nowrap;text-align: left;">
											绑定时间：&nbsp;&nbsp;{{item.createTime}}
										</div>
										</Col>
									</Row>
								</div>
							</div>
						</Panel>
					</Collapse>
				</div>
			</div>
		</Modal>
	</div>
</template>

<script>
	import {
		getCardPoolList,
		addCardPool,
		updateCardPool,
		copyCardPool,
		exportCardPool,
		delCardPool
	} from '@/api/productMngr/cardPool';
	import {
		supplierList,
	} from '@/api/tel/supplier';
	import {
		opsearchAll,
	} from '@/api/operators';
	import {
		getPackageByCorpId,
		getPackageList
	} from '@/api/package/package';
	import {
		getPage
	} from '@/api/customer/manufacturer';
	import {
		getCardPoolPackageList
	} from '@/api/server/faultHandling';
	export default {
		components: {},
		data() {
			//非负整数
			const nonnegativeInteger = (rule, value, callback) => {
				var str = /^[0-9]\d*$/;
				return str.test(value);
			};
			//正整数
			const positiveInteger = (rule, value, callback) => {
				var str = /^[1-9]\d*$/;
				return str.test(value);
			};
			return {
				submitFlag: false,
				formValidate: {
					usageType: '', //卡池类型
					// poolId: '', //卡池ID 编辑状态添加
					poolName: '', //卡池名称
					mcc: [], //国家ID
					supplierId: null, //供应商标识
					tplId: '', //TPLID
					isSupportHimsi: '', //支持HIMSI 4G上网
					corpId: '', //厂商ID
					packageId: '', //套餐ID
					packageName: '', //套餐名称
					settleRule: '', //结算ID
					periodUnit: '', //套餐计算周期类型
					keepPeriod: '', //持续周期
					isOpenAccount: '', //是否支持动态开户
					isSignUpcc: '', //是否UPCC动态签约
					isExpireReset: '', //到期后是否重置
					vimsiFreezeDay: '', //VIMSI的冻结周期
					alarmThreshold: '', //阈值
				},
				formValidateSave: '', //临时存储变量
				ruleValidate: {
					usageType: [{
						required: true,
						message: '请选择卡池类型',
						trigger: 'change',
					}],
					poolName: [{
							validator: (rule, value, cb) => {
								return value.replace(/\s/g, '') != '';
							},
							required: true,
							message: '请输入卡池名称',
							type: 'string',
						},
						{
							validator: (rule, value, cb) => {
								var str = value.replace(/\s/g, '');
								return str.length <= 40;
							},
							message: '卡池名称过长',
						}
					],
					mcc: [{
						required: true,
						type: 'array',
						message: '请选择国家/地区',
					}],
					supplierId: [{
						required: true,
						message: '请选择供应商',
						trigger: 'change',
					}],
					tplId: [{
						required: true,
						message: '请输入TPLID',
						type: 'string',
					}],
					isSupportHimsi: [{
						required: true,
						message: '请选择是否支持HIMSI-4G上网',
						trigger: 'change',
					}],
					corpId: [{
						required: true,
						type: 'string',
						message: '请选择厂商',
					}],
					packageId: [{
						required: true,
						type: 'string',
						message: '请选择套餐',
					}],
					periodUnit: [{
						required: true,
						type: 'string',
						message: '请选择套餐计算周期类型',
						trigger: 'change',
					}],
					keepPeriod: [{
							required: true,
							message: '请输入持续周期数',
						},
						{
							validator: nonnegativeInteger,
							message: '持续周期数格式错误',
						},
						{
							validator: (rule, value, cb) => {
								return Number(**********) >= Number(value);
							},
							message: '持续周期数数值过大',
						}
					],
					isOpenAccount: [{
						required: true,
						type: 'string',
						message: '请选择是否支持动态开户',
						trigger: 'change',
					}],
					isSignUpcc: [{
						required: true,
						type: 'string',
						message: '请选择是否支持动态签约',
						trigger: 'change',
					}],
					isExpireReset: [{
						required: true,
						type: 'string',
						message: '请选择到期后是否重置',
						trigger: 'change',
					}],
					vimsiFreezeDay: [{
							required: true,
							message: '请输入VIMSI的冻结周期',
						},
						{
							validator: nonnegativeInteger,
							message: '冻结周期格式错误',
						},
						{
							validator: (rule, value, cb) => {
								return Number(**********) >= Number(value);
							},
							message: '冻结周期数值过大',
						}
					],
					alarmThreshold: [{
							required: true,
							message: '请输入阈值',
						},
						{
							validator: nonnegativeInteger,
							message: '阈值格式错误',
						},
						{
							validator: (rule, value, cb) => {
								return Number(100) >= Number(value);
							},
							message: '阈值数值要求0-100',
						}
					],
				},
				loading: false,
				currentPage: 1,
				total: 0,
				pageSize: 10,
				columns: [],
				tableData: [],
				searchCondition: {
					poolId: '',
					poolName: '',
					supplierName: '',
					mcc: '',
					usageType: ''
				},
				details: {
					vimsiFreezeDay: '', //VIMSI的冻结周期
					alarmThreshold: '', //阈值
					tplId: '', //TPLID
					packageList: [], //套餐集合
					usageType: '', //卡池类型
					poolName: '', //卡池名称
					mccsCn: [], //支持国家/地区
					supplierName: '', //供应商
					isOpenAccount: '', //是否动态开户
					isSignUpcc: '', //是否动态签约
					isSupportHimsi: '', // 支持HIMSI-4G上网
					corpName: '', //厂商
					corpId: '',
					packageName: '', //套餐名
					periodUnit: '', //套餐计算周期类型
					keepPeriod: '', //持续周期数
					isExpireReset: '', //到期后是否重置
				},
				detilCorpname: '',
				corpIdDetails: [], //详情corpId集合
				detailsFlag: false, //详情-modal框
				operateFlag: false, //新增/编辑/复制-modal框
				operateTitle: '新建卡池', //新增/编辑/复制-modal框标题
				operateType: 'Add', ////新增/编辑/复制-类型
				localList: [], //国家/地区集合
				corpIds: [], //厂商集合
				providers: [], //供应商集合
				packages: [], //套餐集合
				usageTypes: [{
						label: '全球卡普通卡池',
						value: '1'
					},
					{
						label: '终端线下卡池',
						value: '2'
					},
					{
						label: '终端线上卡池',
						value: '3'
					}
				],
				periodUnits: [{
						label: '24小时',
						value: '1'
					},
					{
						label: '自然日',
						value: '2'
					},
					{
						label: '自然月',
						value: '3'
					},
					{
						label: '自然年',
						value: '4'
					}
				],
				activeCollapsePanel: '',// 默认展关闭套餐面板
			}
		},
		methods: {
			// 页面初始化
			init() {
				this.columns = [{
						title: '卡池ID',
						key: 'poolId',
						align: 'center',
						tooltip: true
					},
					{
						title: '卡池名称',
						key: 'poolName',
						align: 'center',
						tooltip: true
					},
					{
						title: '供应商',
						key: 'supplierName',
						align: 'center',
						tooltip: true
					},
					{
						title: '支持国家/地区',
						key: 'mccsCn',
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							let text = row.mccsCn != '' && row.mccsCn != null ? row.mccsCn.toString() : '未知';
							if (text.length > 8) { //进行截取列显示字数
								text = text.substring(0, 8) + "...";
								return h('div', [h('Tooltip', {
										props: {
											placement: 'bottom',
											transfer: true //是否将弹层放置于 body 内
										},
										style: {
											cursor: 'pointer',
										}
									},
									[ //这个中括号表示是Tooltip标签的子标签
										text, //表格列显示文字
										h('label', {
												slot: 'content',
												style: {
													whiteSpace: 'normal'
												}
											},
											row.mccsCn.toString()
										)
									])]);
							} else {
								text = text;
								return h('label', text)
							}
						}
					},
					{
						title: '卡池类型',
						key: 'usageType',
						align: 'center',
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const color = row.usageType == '1' ? '#19be6b' : row.usageType == '2' ? '#ff0000' : row
								.usageType ==
								'3' ? '#2b85e4' : '#ff9900';
							const text = row.usageType == '1' ? '全球卡普通卡池' : row.usageType == '2' ? '终端线下卡池' : row
								.usageType ==
								'3' ? '终端线上卡池' : '未知';
							return h('label', {
								style: {
									color: color
								}
							}, text)
						}
					},
					{
						title: '总数',
						key: 'total',
						align: 'center'
					},
					{
						title: '已使用',
						key: 'used',
						align: 'center'
					},
					{
						title: '使用占比',
						key: 'percent',
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							const text = row.total == 0 ? '无' : ((row.used / row.total) * 100).toFixed(2) + '%';
							return h('label', {}, text)
						}
					}
				];
				var action = ['delete', 'update', 'copy', 'view', 'export', 'vimsiManage'];
				var btnPriv = this.$route.meta.permTypes;
				var actionMixed = action.filter(function(val) {
					return btnPriv.indexOf(val) > -1
				});
				if (actionMixed.length > 0) {
					var width = 60 + 60 * actionMixed.length;
					this.columns.push({
						title: '操作',
						slot: 'action',
						width: width,
						align: 'center'
					});
				}
				//临时存储对象
				this.formValidateSave = JSON.stringify(this.formValidate);
				//获取国家/地区信息
				this.getLocalList();
				//加载初始信息
				this.goPageFirst(1);
			},
			// 页面加载
			goPageFirst(page) {
				this.currentPage = page;
				this.loading = true;
				var searchCondition = {
					poolId: this.searchCondition.poolId.replace(/\s/g, ''),
					poolName: this.searchCondition.poolName.replace(/\s/g, ''),
					supplierName: this.searchCondition.supplierName.replace(/\s/g, ''),
					mcc: this.searchCondition.mcc,
					poolType: this.searchCondition.usageType,
					page: page,
					pageSize: this.pageSize
				};
				getCardPoolList(searchCondition).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						this.total = data.total;
						this.tableData = data.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
					this.loading = false;
				})
			},
			// 分页跳转
			goPage(page) {
				this.goPageFirst(page)
			},
			// 条件查询 currentPage置1
			search() {
				this.goPageFirst(1);
			},
			//卡池详情
			cardPoolDetails(data) {
				let corpId = data.corpId ? data.corpId : null
				if(data.usageType == '2' || data.usageType == '3') {
					this.getCompanyList(data.usageType,corpId);
				}
				this.details = {
					vimsiFreezeDay: data.vimsiFreezeDay, //VIMSI的冻结周期
					alarmThreshold: data.alarmThreshold, //阈值
					tplId: data.tplId, //TPLID
					packageList: data.packageList, //套餐集合
					usageType: data.usageType, //卡池类型
					poolName: data.poolName, //卡池名称
					mccsCn:data.mccsCn != null ? data.mccsCn.toString() : '', //支持国家/地区
					supplierName: data.supplierName, //供应商
					isOpenAccount: data.isOpenAccount, //是否动态开户
					isSignUpcc: data.isSignUpcc, //是否动态签约
					isSupportHimsi: data.isSupportHimsi, // 支持HIMSI-4G上网
					// corpName: this.detilCorpname, //厂商
					packageName: data.packageName, //套餐名
					periodUnit: data.periodUnit, //套餐计算周期类型
					keepPeriod: data.keepPeriod, //持续周期数
					isExpireReset: data.isExpireReset, //到期后是否重置
					poolId: data.poolId
				};	
				this.activeCollapsePanel = ''; // 确保每次关闭
				this.detailsFlag = true;
			},
			getCardPoolPackageList(){
				getCardPoolPackageList({
					poolId: this.details.poolId
				}).then(res => {
					console.log("套餐列表:", res)
					this.details.packageList = res.data;
				}).catch(err => {
					console.log("套餐列表获取失败:", err)
				})
			},
			handleCollapseChange (e) {
				//如果打开，则获取套餐列表
				if (e.includes('1')) {
					//如果套餐数据没有，则获取套餐数据
					if (!this.details.packageList) {
						this.getCardPoolPackageList();
					}
				}else{
					console.log("关闭套餐面板")
				}

			},
			//卡池新增
			addCardPool() {
				this.$refs['formValidate'].resetFields();
				this.$refs.usageType.clearSingleSelect();
				this.operateType = 'Add';
				this.operateTitle = '卡池新建';
				//表单初始化
				this.formValidate = Object.assign({}, JSON.parse(this.formValidateSave));
				this.operateFlag = true;
			},
			//卡池编辑
			updateCardPool(data) {
				this.$refs['formValidate'].resetFields();
				this.operateType = 'Update';
				this.operateTitle = '卡池编辑';
				//构建国家列表
				var mcc = [];
				if (data.mcc == null) {
					this.$Notice.error({
						title: '操作提示',
						desc: '支持的国家/地区信息获取失败'
					})
				} else {
					data.mcc.map((item, index) => {
						mcc.push(item.mcc);
					});
				}
				this.typeChange(data.usageType);
				// this.formValidate.usageType = data.usageType;
				// this.getPackageList(data.corpId);

				//前端线下为2-->加载结算规则表套餐
				this.packages = [];
				this.formValidate.packageId = '';
				//加载套餐列表
				if ('2' === data.usageType && data.corpId != undefined) {
					getPackageByCorpId(data.corpId).then(res => {
						if (res && res.code == '0000') {
							var data1 = res.data;
							this.packages = data1;
							//表单赋值
							this.formValidate = Object.assign({}, data);
							this.formValidate.mcc = mcc;
							this.operateFlag = true;
						} else {
							throw res
						}
					}).catch((err) => {

					}).finally(() => {

					})
				} else {
					//表单赋值
					this.formValidate = Object.assign({}, data);
					this.formValidate.mcc = mcc;
					this.operateFlag = true;
				}
			},
			// 卡池复制
			copyCardPool(id) {
				this.$Modal.confirm({
					title: '确认复制？',
					onOk: () => {
						copyCardPool(id).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.goPageFirst(this.currentPage);
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			// 卡池删除
			delCardPool(id) {
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						delCardPool(id).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								if (this.tableData.length == 1 && this.currentPage > 1) {
									this.goPageFirst(this.currentPage - 1);
								} else {
									this.goPageFirst(this.currentPage);
								}
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			//卡池信息导出
			exportCardPool(id, name) {
				//导出操作
				exportCardPool(id).then(res => {
					const content = res.data;
					const blob = new Blob([content]); // 构造一个blob对象来处理数据
					// 获取当前时间
					var date = new Date();
					var y = date.getFullYear();
					var m = date.getMonth() + 1;
					var d = date.getDate();
					var time = y + "-" + m + "-" + d
					const fileName = time + '.xlsx' // 导出文件名
					
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = document.createElement('a'); // 创建a标签
						link.download = fileName; // a标签添加属性
						link.style.display = 'none';
						link.href = URL.createObjectURL(blob);
						document.body.appendChild(link);
						link.click(); // 执行下载
						URL.revokeObjectURL(link.href); // 释放url
						document.body.removeChild(link); // 释放标签
					} else { // 其他浏览器
						navigator.msSaveBlob(blob, fileName);
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//表单提交
			submit() {
				this.$refs['formValidate'].validate((valid) => {
					if (valid) {
						var form = this.formValidate;
						var type = form.usageType;
						var isSupportHimsi = form.isSupportHimsi;
						var data = {
							usageType: form.usageType, //卡池类型
							poolName: form.poolName.replace(/\s/g, ''), //卡池名称
							mcc: form.mcc, //国家ID
							supplierId: form.supplierId, //供应商标识
							tplid: form.tplId, //TPLID
							isSupportHimsi: type == '1' ? form.isSupportHimsi : '2', //支持HIMSI 4G上网
							corpId: type != '2' ? null : form.corpId, //厂商ID
							periodUnit: type != '2' ? null : form.periodUnit, //套餐计算周期类型
							keepPeriod: type != '2' ? null : form.keepPeriod, //持续周期
							isExpireReset: type != '2' ? null : form.isExpireReset, //到期后是否重置
							isOpenAccount: type == '1' ? '1' : form.isOpenAccount, //是否支持HSS动态开户
							isSignUpcc: type == '2' ? null : form.isSignUpcc, //是否支持UPCC动态签约
							vimsiFreezeDay: type == '1' && isSupportHimsi == '1' ? null : form
								.vimsiFreezeDay, //VIMSI的冻结周期
							alarmThreshold: type == '1' && isSupportHimsi == '1' ? null : form
								.alarmThreshold, //阈值
						};
						var providers = this.providers;
						for (var i = 0; i < providers.length; i++) {
							if (form.supplierId == providers[i].supplierId) {
								data.supplierName = providers[i].supplierName;
								break;
							}
						}
						if ('2' == form.usageType) {
							var packages = this.packages;
							for (var i = 0; i < packages.length; i++) {
								if (form.packageId == packages[i].packageId) {
									data.packageId = form.packageId;
									data.packageName = packages[i].packageName;
									data.settleRule = packages[i].settleRule;
									break;
								}
							}
						}
						//新增
						if ('Add' == this.operateType) {
							this.submitFlag = true;
							addCardPool(data).then(res => {
								if (res && res.code == '0000') {
									setTimeout(() => {
										this.$Notice.success({
											title: '操作提示',
											desc: '操作成功'
										});
										this.goPageFirst(this.currentPage);
										this.operateFlag = false;
										this.submitFlag = false;
									}, 1500);
								} else {
									this.submitFlag = false;
									throw res
								}
							}).catch((err) => {
								this.submitFlag = false;
							});
						}
						//编辑
						if ('Update' == this.operateType) {
							data.poolId = form.poolId;
							this.submitFlag = true;
							updateCardPool(data).then(res => {
								if (res && res.code == '0000') {
									setTimeout(() => {
										this.$Notice.success({
											title: '操作提示',
											desc: '操作成功'
										});
										this.goPageFirst(this.currentPage);
										this.operateFlag = false;
										this.submitFlag = false;
									}, 1500);
								} else {
									this.submitFlag = false;
									throw res
								}
							}).catch((err) => {
								this.submitFlag = false;
							})
						}
					}
				})
			},
			//表单重置
			reset(name) {
				this.$refs[name].resetFields();
			},
			//国家/地区
			getLocalList() {
				opsearchAll().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.localList = list;
						this.localList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//卡池类型切换
			typeChange(e) {
				if ('2' == e || '3' == e) {
					//终端线上/下卡池加载厂商
					this.getCompanyList(e,null);
				}
			},
			//获取厂商集合
			getCompanyList(e,corpId) {
				//8-线下(前端线下为2) 7-线上(前端线上为3)
				getPage({
					pageNumber: 1,
					pageSize: -1,
					corpType: e == '2' ? '8' : '7'
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						this.corpIds = data.records;
						this.corpIdDetails = data.records; 
						this.corpIdDetails.map((item,index) => {
							if (item.corpId === corpId) {
								this.detilCorpname = this.corpIdDetails[index].corpName
							}
						})
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//获取套餐集合
			getPackageList(e) {
				//前端线下为2-->加载结算规则表套餐
				var type = this.formValidate.usageType;
				this.packages = [];
				this.formValidate.packageId = '';
				//加载套餐列表
				if ('2' === type && e != undefined) {
					getPackageByCorpId(e).then(res => {
						if (res && res.code == '0000') {
							var data = res.data;
							this.packages = data;
						} else {
							throw res
						}
					}).catch((err) => {

					}).finally(() => {

					})
				}
			},
			//获取供应商集合
			getProviderList() {
				supplierList().then(res => {
					if (res && res.code == '0000') {
						this.providers = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//VIMSI管理跳转
			toVimsi(row) {
				if (row.isSupportHimsi == '1') {
					this.$Notice.error({
						title: '操作提示',
						desc: 'H卡上网卡池不支持VIMSI管理'
					});
					return false;
				}
				var obj = {
					p: row.poolId,
					s: row.supplierId
				};
				this.$router.push({
					name: 'vimsi',
					query: {
						p: encodeURIComponent(JSON.stringify(obj))
					}
				})
			},
		},
		mounted() {
			this.init();
			// this.getCompanyList(null,null);
		},
		watch: {
			operateFlag(newValue, oldValue) {
				//操作模态框拉起加载供应商信息
				if (newValue) {
					//获取供应商列表
					this.getProviderList();
				}
			}
		},
	}
</script>
<style scoped="scoped">
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.modal_content {
		padding: 0 16px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 85px;
	}

	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.box {
		padding: 0 10px;
		color: #878787;
		line-height: 38px;
		background-color: #f7f7f7;
		border: 1px solid #dcdee2;
		border-bottom: none;
	}
</style>
