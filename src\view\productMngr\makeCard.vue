<template>
  <div>
    <Card>
      <div class="search_head">
        <span style="font-weight:bold;">选择供应商：</span>&nbsp;&nbsp;
        <Select v-model="provider" placeholder="下拉选择供应商" style="width:300px" @on-change="getProviders">
            <Option :value="item.value" v-for="(item,index) in providers"  :key="index">{{item.label}}</Option>
        </Select>&nbsp;&nbsp;
        <span style="font-weight:bold;">选择卡类型：</span>&nbsp;&nbsp;
        <Select v-model="cardType" placeholder="下拉选择卡类型" style="width:250px" @on-change="getProviders">
            <Option :value="item.value" v-for="(item,index) in cardTypes"  :key="index">{{item.label}}</Option>
        </Select>&nbsp;&nbsp;
        <span style="font-weight:bold;">选择日期：</span>&nbsp;&nbsp;
        <Row>
            <Col span="12">
                <DatePicker v-model="timeRangeArray" type="daterange" format="yyyyMMdd" placement="bottom-end" placeholder="选择日期范围" style="width: 320px" @on-change="handleDateChange"
          @on-clear="hanldeDateClear"></DatePicker>
            </Col>
        </Row>&nbsp;&nbsp;
        <Button  type="primary" icon="md-search" @click="search()">搜索</Button>&nbsp;&nbsp;
        <Button v-has="'add'" icon="md-add" type="success" @click="editCard()">填写表单</Button>&nbsp;&nbsp;
      </div>
      <div style="margin-top:20px">
        <Table  :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
          <template slot-scope="{ row, index }" slot="action">
            <Button v-has="'export'" type="success" size="small" style="margin-right: 10px" @click="download(row)">下载</Button>
            <Button v-has="'delete'" type="error" size="small" style="margin-right: 10px" @click="remove(row)">删除</Button>
          </template>
        </Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" style="margin: 10px 0;" />
      </div>
    </Card>
    <Modal
      v-model="modal1"
      title="填写表单"
      @on-ok="ok"
      @on-cancel="cancel"
      width="600px">
      <div class="search_head">
        <Form ref="formValidate1" :model="formValidate1" :rules="ruleValidate1" :label-width="160" :label-height="100" inline style="font-weight:bold;">
          <FormItem label="供应商" prop="provider" >
              <Select v-model="formValidate1.provider" placeholder="下拉选择供应商" style="width:250px" @on-change="getProviders">
                  <Option :value="item.value" v-for="(item,index) in providers"  :key="index">{{item.label}}</Option>
              </Select>
          </FormItem>
          <FormItem label="卡类型" prop="cardType" >
              <Select v-model="formValidate1.cardType" placeholder="下拉选择卡类型" style="width:250px" @on-change="getProviders">
                  <Option :value="item.value" v-for="(item,index) in cardTypes"  :key="index">{{item.label}}</Option>
              </Select>
          </FormItem>
          <FormItem label="Customer" prop="customer" >
              <Input placeholder="Customer..." v-model="formValidate1.customer" clearable style="width: 250px" />&nbsp;&nbsp;
              <span style="color:#c5c8ce;">客户名</span>
          </FormItem>
          <FormItem label="Quantity" prop="quantity" >
              <Input placeholder="Quantity..." v-model="formValidate1.quantity" clearable style="width: 250px" />&nbsp;&nbsp;
              <span style="color:#c5c8ce;">需分配主IMSI的数量</span>
          </FormItem>
          <FormItem label="Type" prop="type" >
              <Input placeholder="Type..." v-model="formValidate1.type" clearable style="width: 250px" />&nbsp;&nbsp;
          </FormItem>
          <FormItem label="Batch" prop="batch" >
              <Input placeholder="Batch..." v-model="formValidate1.batch" clearable style="width: 250px" />&nbsp;&nbsp;
          </FormItem>
          <FormItem label="Transport_key" prop="transport_key" >
              <Input placeholder="Transport_key..." v-model="formValidate1.transport_key" clearable style="width: 250px" />&nbsp;&nbsp;
              <span style="color:#c5c8ce;">传输key</span>
          </FormItem>
          <FormItem label="OP_key" prop="OP_key" >
              <Input placeholder="OP_key..." v-model="formValidate1.OP_key" clearable style="width: 250px" />&nbsp;&nbsp;
              <span style="color:#c5c8ce;">OPC</span>
          </FormItem>
          <FormItem label="OTA_Transport_Key" prop="OTA_Transport_Key" >
              <Input placeholder="OTA_Transport_Key..." v-model="formValidate1.OTA_Transport_Key" clearable style="width: 250px" />&nbsp;&nbsp;
              <span style="color:#c5c8ce;">目标OTA</span>
          </FormItem>
          <FormItem label="Address1" prop="address1" >
              <Input placeholder="Address1..." v-model="formValidate1.address1" clearable style="width: 250px" />&nbsp;&nbsp;
              <span style="color:#c5c8ce;">地址1</span>
          </FormItem>
          <FormItem label="Address2" prop="address2" >
              <Input placeholder="Address2..." v-model="formValidate1.address2" clearable style="width: 250px" />&nbsp;&nbsp;
              <span style="color:#c5c8ce;">地址2</span>
          </FormItem>
          <FormItem label="Address3" prop="address3" >
              <Input placeholder="Address3..." v-model="formValidate1.address3" clearable style="width: 250px" />&nbsp;&nbsp;
              <span style="color:#c5c8ce;">地址3</span>
          </FormItem>
          <FormItem label="Address4" prop="address4" >
              <Input placeholder="Address4..." v-model="formValidate1.address4" clearable style="width: 250px" />&nbsp;&nbsp;
              <span style="color:#c5c8ce;">地址4</span>
          </FormItem>
          <FormItem label="Product Code" prop="productCode" >
              <Input placeholder="Product Code..." v-model="formValidate1.productCode" clearable style="width: 250px" />&nbsp;&nbsp;
              <span style="color:#c5c8ce;">产品编码</span>
          </FormItem>
          <FormItem label="PO_Ref" prop="PO_Ref" >
              <Input placeholder="PO_Ref..." v-model="formValidate1.PO_Ref" clearable style="width: 250px" />&nbsp;&nbsp;
          </FormItem>
          <FormItem label="Artwork" prop="artwork" >
              <Input placeholder="Artwork..." v-model="formValidate1.artwork" clearable style="width: 250px" />&nbsp;&nbsp;
          </FormItem>
          <FormItem label="VAR_OUT" prop="varOut" >
              <Input placeholder="VAR_OUT..." v-model="formValidate1.varOut" clearable style="width: 250px" />&nbsp;&nbsp;
          </FormItem>
          <FormItem label="Roaming Exclusive List" prop="roamingExclusiveList" >
              <Input placeholder="Roaming Exclusive List..." v-model="formValidate1.roamingExclusiveList" clearable style="width: 250px" />&nbsp;&nbsp;
              <span style="color:#c5c8ce;">贴片卡填写，可为空</span>
          </FormItem>
        </Form>
      </div>
    </Modal>
  </div>
</template>

<script>

export default {
  components: {
  },
  data () {
    return {
      formValidate: {
        file: '',
        provider: ''
      },
      ruleValidate: {
        file: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ],
        provider: [
          { required: true, message: '请选择供应商', trigger: 'blur' }
        ]
      },
      formValidate1: {
        provider: '',
        cardType: '',
        customer: 'China Mobile International Ltd',
        quantity: '',
        type: 'PLUG-IN',
        batch: '',
        Transport_key: '36',
        OP_key: '36',
        OTA_Transport_Key: 'NA',
        address1: 'China Mobile International Ltd',
        address2: '30/F Tower 1 Kowloon Commerce Ctr',
        address3: '51 Kwai Cheong Road',
        address4: 'Kwai Chung, NT, Hong Kong',
        productCode: 'CMI0000000000000001',
        PO_Ref: 'CMIGTO0000000000002',
        artwork: 'V1053539A_U1058946A',
        varOut: 'PIN1/PUK1/PIN2/PUK2/ADM/ACCESS_CONTROL',
        roamingExclusiveList: ''
      },
      ruleValidate1: {
        provider: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        cardType: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        customer: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        batch: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        Transport_key: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        OP_key: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        OTA_Transport_Key: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        address1: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        address2: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        address3: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        address4: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        productCode: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        PO_Ref: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        artwork: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        varOut: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
        roamingExclusiveList: [
          { required: true, message: '请输入VIMSI号码', trigger: 'blur' }
        ],
      },
      // 表头信息
      columns: [
        {
          title: '文件名',
          key: 'fileName',
          align: 'center'
        },
        {
          title: '供应商',
          key: 'supply',
          align: 'center'
        },
        {
          title: '时间',
          key: 'time',
          align: 'center'
        },
        {
          title: '操作',
          slot: 'action',
          width: 300,
          align: 'center'
        }
      ],
      cardTypes: [
        {
          label: '卡类型1',
          value: 0
        },
        {
          label: '卡类型2',
          value: 1
        }
      ],
      providers: [
        {
          label: '供应商1',
          value: 0
        },
        {
          label: '供应商2',
          value: 1
        }
      ],
      statuses: [
        {
          label: '状态1',
          value: 0
        },
        {
          label: '状态2',
          value: 1
        }
      ],
      tableData: [],
      loading: false,
      currentPage: 1,
      total: 0,
      vimsiCondition: '',
      vimsiChoosed: '',
      ids: [],
      modal1: false,
      status: '',
      provider:'',
      cardType:'',
      timeRangeArray: [],
      searchBeginTime: '',
      searchEndTime: '',
    }
  },
  watch: {
    '$route': 'reload'
  },
  computed: {},
  methods: {
    // 页面加载
    goPageFirst (page) {
      this.loading = true
      let table = [
        {
          fileName: '文件1',
          supply: '供应商1',
          time: '2021-02-28 15:30:28',
          status: 0
        },
        {
          fileName: '文件2',
          supply: '供应商2',
          time: '2021-02-28 15:30:28',
          status: 1
        },
        {
          fileName: '文件3',
          supply: '供应商3',
          time: '2021-02-28 15:30:28',
          status: 2
        },
        {
          fileName: '文件4',
          supply: '供应商4',
          time: '2021-02-28 15:30:28',
          status: 3
        },
        {
          fileName: '文件5',
          supply: '供应商5',
          time: '2021-02-28 15:30:28',
          status: 4
        }
      ]
      this.tableData = table
      this.total = 5
      this.loading = false
    },
    // 查询按钮，指定号码查询
    // 分页跳转
    goPage (page) {
      this.goPageFirst(page)
    },
    // 勾选按钮
    handleRowChange (selection) {
      this.selection = selection
      this.ids = []
      selection.map((value, index) => {
        this.ids.push(value.fallbackId)
      })
    },
    hanldeDateClear() {
      this.searchBeginTime = ''
      this.searchEndTime = ''
    },
    handleDateChange(dateArr) {
      let beginDate = this.timeRangeArray[0] || ''
      let endDate = this.timeRangeArray[1] || ''
      if (beginDate == '' || endDate == '') {
        return
      }
      [this.searchBeginTime, this.searchEndTime] = dateArr
    },
    error (nodesc) {
      this.$Notice.error({
        title: '出错啦',
        desc: nodesc ? '' : '服务器内部错误'
      })
    },
    search () {
      this.goPageFirst(1)
    },
    delteNumber (id) {
      this.$Modal.confirm({
        title: '确认删除？',
        onOk: () => {
          this.$Notice.success({
            title: '成功',
            desc: '操作成功'
          })
          this.goPageFirst(this.page)
        }
      })
    },
    editCard () {
      this.modal1 = true
    },
    updateBatch () {
      this.modal3 = true
    },
    download(){

    },
    remove(){

    },
    getProviders () {

    },
    getStatus () {

    },
    ok(){
      this.formValidate1 = {}
    },
    cancel(){
      this.formValidate1 = {}
    }
  },
  mounted () {
    this.goPageFirst(1)
  }
}
</script>
<style>

  .search_head {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items:center;
    margin-bottom: 20px;
  }
  .search_head_label {
    margin-top: 20px;
    font-size: 17px;
  }
</style>
