<template>
	<div>
		<Card>
			<Form ref="formObj" :model="formObj" :rules="formObjRule" class="search_head">
				<FormItem label="MSISDN:" class="search_head_label">
					<Input placeholder="输入MSISDN号码..." v-model.trim="msisdnCondition" clearable style="width: 220px" />
				</FormItem>
				<FormItem label="IMSI:" class="search_head_label">
					<Input placeholder="输入IMSI号码..." v-model.trim="imsiCondition" clearable style="width: 220px" />
				</FormItem>
				<FormItem label="ICCID:" class="search_head_label">
					<Input placeholder="输入ICCID号码..." v-model.trim="iccidCondition" clearable style="width: 220px" />
				</FormItem>
				<FormItem label="起始ICCID:" prop="startICCID" class="search_head_label" :rules="formObj.endICCID ?
						formObjRule.startICCID : [{required: false}] ">
					<Input placeholder="输入起始ICCID号码..." v-model.trim="formObj.startICCID" clearable
						style="width: 220px" />
				</FormItem>
				<FormItem label="结束ICCID:" prop="endICCID" class="search_head_label" :rules="formObj.startICCID ?
						formObjRule.endICCID : [{required: false}] ">
					<Input placeholder="输入结束ICCID号码..." v-model.trim="formObj.endICCID" clearable
						style="width: 220px" />
				</FormItem>
				<FormItem label="卡类型:" class="search_head_label">
					<Select v-model="cardTypeCondition" placeholder="下拉选择卡类型" style="width:200px"
						@on-change="getProviders" clearable>
						<Option :value="item.value" v-for="(item,index) in cardTypes" :key="index">{{item.label}}
						</Option>
					</Select>
				</FormItem>
				<FormItem label="主卡形态:" class="search_head_label">
					<Select v-model="cardShapeCondition" placeholder="下拉选择主卡形态" style="width:200px"
						@on-change="getProviders" clearable>
						<Option :value="item.value" v-for="(item,index) in cardShapes" :key="index">{{item.label}}
						</Option>
					</Select>
				</FormItem>
				<FormItem label="选择时间段:" class="search_head_label">
					<DatePicker v-model="timeRangeArray" type="daterange" format="yyyy-MM-dd" placement="bottom-end"
						placeholder="选择日期范围" style="width: 250px" @on-change="handleDateChange"
						@on-clear="hanldeDateClear"></DatePicker>
				</FormItem>
				<FormItem label="所属渠道:" class="search_head_label">
					<Select v-model="corpId" clearable placeholder="选择所属渠道" filterable @on-change="getcorpId($event)"
						style="width:200px">
						<Option :value="item.corpId" v-for="(item,index) in corpList" :key="index">{{item.corpName}}
						</Option>
					</Select>
				</FormItem>
				<FormItem label="出库状态:" class="search_head_label">
					<Select v-model="isStoreOut" clearable placeholder="选择出库状态" filterable style="width:200px">
						<Option :value="item.value" v-for="(item,index) in isStoreOutList" :key="index">{{item.label}}
						</Option>
					</Select>
				</FormItem>
			</Form>
			<div>
				<a ref="downloadLink" style="display: none"></a>
				<Button type="primary" icon="md-search" :loading="searchLoading"
					@click="search()">搜索</Button>&nbsp;&nbsp;
				<Button v-has="'add'" icon="md-add" type="success" @click="add()">导入</Button>&nbsp;&nbsp;
				<Button v-has="'export'" icon="md-add" type="error" :loading="exportloading"
					@click="out()">导出</Button>&nbsp;&nbsp;
				<Button v-has="'batchUpdate'" icon="md-add" type="warning"
					@click="updateBatch()">批量修改</Button>&nbsp;&nbsp;
				<Button v-has="'updateExpire'" icon="md-add" type="info"
					@click="updateTime()">修改过期时间</Button>&nbsp;&nbsp;
				<Button v-has="'importRecord'" icon="md-add" type="success"
					@click="recordView()">导入记录查看</Button>&nbsp;&nbsp;
			</div>
			<div style="margin-top:20px">
				<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading"
					@on-selection-change="handleRowChange">
					<template slot-scope="{ row, index }" slot="action">
						<Button v-has="'view'" type="success" size="small" style="margin-right: 10px"
							@click="getMore(row)">详情</Button>
						<Button v-has="'update'" type="error" size="small" style="margin-right: 10px"
							@click="update(row)">编辑</Button>
					</template>
				</Table>
			</div>
			<div class="table-botton" style="margin-top:15px">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage"
					style="margin: 10px 0;" />
			</div>
		</Card>
		<Modal v-model="modal1" title="主卡导入" :mask-closable="false" @on-cancel="cancelModal" width="650px">
			<div class="search_head" style="margin: 20px 0;">
				<Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="150"
					:label-height="100" inline style="font-weight:bold;">
					<FormItem label="目标OTA" prop="ota" style="width:510px">
						<Select v-model="formValidate.ota" filterable placeholder="目标OTA" clearable>
							<Option :value="item.id" v-for="(item,index) in otas" :key="index">{{item.name}}</Option>
						</Select>
					</FormItem>
					<FormItem label="短信模板" prop="sms" style="width:510px">
						<Select v-model.trim="formValidate.sms" filterable placeholder="下拉选择短信模板" clearable>
							<Option :value="item.id" v-for="(item,index) in smsAll" :key="index">{{item.templateName}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="卡片类型" prop="cardType" style="width:510px">
						<Select v-model="formValidate.cardType" placeholder="下拉选择卡片类型" clearable>
							<Option :value="item.value" v-for="(item,index) in cardTypes" :key="index">{{item.label}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="TPLID" prop="tplid" style="width:510px">
						<Input placeholder="TPLID..." v-model="formValidate.tplid" clearable />
					</FormItem>
					<FormItem label="主卡形态" prop="cardShape" style="width:510px">
						<Select v-model="formValidate.cardShape" placeholder="下拉选择主卡形态" clearable
							@on-change="clearxlsFileCache">
							<Option :value="item.value" v-for="(item,index) in cardShapes" :key="index">{{item.label}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="选择合作商" prop="provId" style="width:510px" v-if="formValidate.cardType == '2'">
						<Select v-model="formValidate.provId" filterable placeholder="下拉选择合作商"
							@on-change="getProvs($event)" clearable>
							<Option :value="item.corpId" v-for="(item,index) in provs" :key="index">{{item.corpName}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="绑定套餐" prop="packageId" style="width:510px" v-if="formValidate.cardType == '2'">
						<CheckboxGroup v-model="formValidate.packageId" style="display: flex;">
							<li v-for="(item,index) in cpackages" :key="index" style="list-style: none;">
								<Checkbox :label="item.packageId">{{ item.packageName }}</Checkbox>
							</li>
						</CheckboxGroup>
					</FormItem>
					<FormItem label="是否需要实名制" prop="realName" style="width:510px">
						<Select v-model="formValidate.realName" clearable @on-change="isgetrealName($event)">
							<Option v-for="(item,index) in realNameOrNot" :value="item.value" :key="index">
								{{item.label}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="实名制规则" prop="realNameCity" style="width:510px" v-if="formValidate.realName === 0">
						<Select v-model="formValidate.realNameCity" filterable clearable>
							<Option v-for="item in realNameCitys" :value="item.groupId" :key="item.groupId">{{item.groupName}}</Option>
						</Select>
					</FormItem>
					<FormItem label="是否支持GTP动态路由" prop="supportGtpRoute" style="width:510px">
						<Select v-model="formValidate.supportGtpRoute" placeholder="请选择是否支持GTP动态路由"
							clearable @on-change="changeRoute($event)">
							<Option :value="1">是</Option>
							<Option :value="2">否</Option>
						</Select>
					</FormItem>
					<FormItem label="路由ID" prop="routeId" :rules="formValidate.supportGtpRoute == '1' ? ruleValidate.routeId : [{required: false}]"
					 style="width:510px" v-if="formValidate.supportGtpRoute=='1'">
						<Input placeholder="请输入路由ID" v-model="formValidate.routeId" clearable />
					</FormItem>
					<FormItem label="动态开户UPCC签约模板" prop="openUpccSignId" style="width:510px">
						<Input placeholder="请输入动态开户UPCC签约模板" maxlength="32" v-model.trim="formValidate.openUpccSignId" clearable />
					</FormItem>
					<FormItem label="套餐到期UPCC签约模板" prop="expireUpccSignId" style="width:510px">
						<Input placeholder="请输入套餐到期UPCC签约模板" maxlength="32" v-model.trim="formValidate.expireUpccSignId" clearable />
					</FormItem>
					<FormItem label="adm文件" style="width:510px" prop="admfile">
						<Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/" :action="uploadUrl"
							:on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
							:on-progress="fileUploading" v-model="formValidate.admfile">
							<div style="padding: 20px 0">
								<Icon type="ios-cloud-upload" size="30" style="color: #3399ff"></Icon>
								<p>点击或拖拽文件上传</p>
							</div>
						</Upload>
						<ul class="ivu-upload-list" v-if="formValidate.admfile">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span><i class="ivu-icon ivu-icon-ios-stats"></i>{{formValidate.admfile.name}}</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style=""
									@click="admremoveFile"></i>
							</li>
						</ul>
					</FormItem>
					<FormItem label="sdb文件" style="width:510px" prop="sdbfile">
						<Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/"
							:on-success="sdbfileSuccess" :before-upload="sdbhandleBeforeUpload"
							:on-progress="sdbfileUploading" show-upload-list v-model="formValidate.sdbfile">
							<div style="padding: 20px 0">
								<Icon type="ios-cloud-upload" size="30" style="color: #3399ff"></Icon>
								<p>点击或拖拽文件上传</p>
							</div>
						</Upload>
						<ul class="ivu-upload-list" v-if="formValidate.sdbfile">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span><i class="ivu-icon ivu-icon-ios-stats"></i>{{formValidate.sdbfile.name}}</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style=""
									@click="sdbremoveFile"></i>
							</li>
						</ul>
					</FormItem>
					<FormItem label="xlsx文件" style="width:510px" prop="xlsxfile" v-if="formValidate.cardShape == '2'"
						:rules="formValidate.cardShape == '2' ? ruleValidate.xlsxfile : [{required: false}]">
						<Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/"
							:on-success="xlsxfileSuccess" :before-upload="xlsxhandleBeforeUpload"
							:on-progress="xlsxfileUploading" show-upload-list v-model="formValidate.xlsxfile">
							<div style="padding: 20px 0">
								<Icon type="ios-cloud-upload" size="30" style="color: #3399ff"></Icon>
								<p>点击或拖拽文件上传</p>
							</div>
						</Upload>
						<ul class="ivu-upload-list" v-if="formValidate.xlsxfile">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span><i class="ivu-icon ivu-icon-ios-stats"></i>{{formValidate.xlsxfile.name}}</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style=""
									@click="xlsxremoveFile"></i>
							</li>
						</ul>
					</FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" :loading="importLoading" @click="handleUpload">确定</Button>
			</div>
		</Modal>
		<Modal v-model="modal2" title="修改单个主卡" :mask-closable="false" @on-cancel="modifycancelModal" width="620px">
			<div class="search_head">
				<Form ref="choosed" :model="choosed" :rules="ruleValidate2" :label-width="150" :label-height="100"
					inline style="font-weight:bold;">
					<FormItem label="MSISDN:" style="width:510px">
						<span>{{choosed.msisdn}}</span>&nbsp;&nbsp;
					</FormItem>
					<FormItem label="IMSI:" style="width:510px">
						<span>{{choosed.imsi}}</span>&nbsp;&nbsp;
					</FormItem>
					<FormItem label="ICCID:" style="width:510px">
						<span>{{choosed.iccid}}</span>&nbsp;&nbsp;
					</FormItem>
					<FormItem label="短信模板" prop="templateId" style="width:510px">
						<Select v-model="choosed.templateId" filterable placeholder="下拉选择短信模板">
							<Option :value="item.id" v-for="(item,index) in smsAll" :key="index">{{item.templateName}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="卡片类型" prop="type" style="width:510px">
						<Select ref="store" v-model="choosed.type" placeholder="下拉选择卡片类型" clearable>
							<Option :value="item.value" v-for="(item,index) in cardTypes" :key="index">{{item.label}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="TPLID" prop="tplid" style="width:510px">
						<Input placeholder="TPLID..." v-model="choosed.tplid" clearable />
					</FormItem>
					<FormItem label="状态修改" prop="status" style="width:510px">
						<Select v-model="choosed.status" placeholder="下拉选择状态" @on-change="getStatus" clearable>
							<Option :value="item.value" v-for="(item,index) in statuses" :key="index">{{item.label}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="选择合作商" prop="corpId" style="width:510px" v-if="choosed.type == '2'">
						<Select v-model="choosed.corpId" filterable placeholder="下拉选择合作商" @on-change="getProvs($event)"
							clearable>
							<Option :value="item.corpId" v-for="(item,index) in provs" :key="index">{{item.corpName}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="绑定套餐" prop="packageId" v-if="choosed.type == '2'" style="width:510px">
						<CheckboxGroup v-model="choosed.packageId" style="display: flex;"
							@on-change="getcpackages($event)">
							<li v-for="(item,index) in cpackages" :key="index" style="list-style: none;">
								<Checkbox :label="item.packageId">{{ item.packageName }}</Checkbox>
							</li>
						</CheckboxGroup>
					</FormItem>
					<FormItem label="是否需要实名制" prop="realName" style="width:510px">
						<Select v-model="choosed.realName" @on-change="getName($event)">
							<Option v-for="(item,index) in realNameOrNot" :value="item.value" :key="index">
								{{item.label}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="实名制规则" prop="realNameid" style="width:510px" v-if="realNameflg === 0">
						<Select v-model="choosed.realNameid" filterable>
							<Option v-for="item in realNameCitys" :value="item.groupId" :key="item.groupId">{{item.groupName}}</Option>
						</Select>
					</FormItem>
					<FormItem label="路由ID" prop="routeId" style="width:510px" v-if="modifyRoute == '1'">
						<Input placeholder="请输入路由ID" v-model="choosed.routeId" clearable />
					</FormItem>
					<FormItem label="动态开户UPCC签约模板" prop="openUpccSignId" style="width:510px">
						<Input maxlength="32" placeholder="请输入动态开户UPCC签约模板" v-model.trim="choosed.openUpccSignId" clearable />
					</FormItem>
					<FormItem label="套餐到期UPCC签约模板" prop="expireUpccSignId" style="width:510px">
						<Input maxlength="32" placeholder="请输入套餐到期UPCC签约模板" v-model.trim="choosed.expireUpccSignId" clearable />
					</FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="modifycancelModal">取消</Button>
				<Button type="primary" :loading="updateLoading" @click="modifyhandleUpload">确定</Button>
			</div>
		</Modal>
		<Modal v-model="modal3" title="批量修改主卡" :mask-closable="false" @on-cancel="BatchcancelModal" width="620px">
			<div class="search_head">
				<Form ref="formValidate1" :model="formValidate1" :rules="ruleValidate1" :label-width="150"
					:label-height="100" inline style="font-weight:bold;">
					<FormItem label="ICCID文件" style="width:510px"
						:rules="[{required: true, message: '请上传文件', trigger: 'blur' },]">
						<Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/"
							:on-success="BatchfileSuccess" :on-error="BatchhandleError"
							:before-upload="BatchhandleBeforeUpload" :on-progress="BatchfileUploading">
							<div style="padding: 20px 0">
								<Icon type="ios-cloud-upload" size="30" style="color: #3399ff"></Icon>
								<p>点击或拖拽文件上传</p>
							</div>
						</Upload>
						<ul class="ivu-upload-list" v-if="formValidate1.iccidFile">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span><i class="ivu-icon ivu-icon-ios-stats"></i>{{formValidate1.iccidFile.name}}</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style=""
									@click="BatchremoveFile"></i>
							</li>
						</ul>
					</FormItem>
					<FormItem>
						<Button type="primary" :loading="downloading" icon="ios-download"
							@click="downloadFile">{{$t('buymeal.Download')}}</Button>
						<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;margin-left: 100px;">
							{{message1}}
						</Alert>
					</FormItem>
					<FormItem label="短信模板" style="width:510px">
						<Select v-model="formValidate1.templateid" filterable placeholder="下拉选择短信模板" clearable>
							<Option :value="item.id" v-for="(item,index) in smsAll" :key="index">{{item.templateName}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="卡片类型" style="width:510px">
						<Select v-model="formValidate1.type" placeholder="下拉选择卡片类型" clearable>
							<Option :value="item.value" v-for="(item,index) in cardTypes" :key="index">{{item.label}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="TPLID" prop="tplid" style="width:510px">
						<Input placeholder="TPLID..." v-model="formValidate1.tplid" clearable />
					</FormItem>
					<FormItem label="状态修改" style="width:510px">
						<Select v-model="formValidate1.status" placeholder="下拉选择状态" clearable>
							<Option :value="item.value" v-for="(item,index) in statuses" :key="index">{{item.label}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="选择合作商" prop="provId" style="width:510px" v-if="formValidate1.type == 2">
						<Select v-model="formValidate1.provId" filterable placeholder="下拉选择合作商"
							@on-change="getProvs($event)" clearable>
							<Option :value="item.corpId" v-for="(item,index) in provs" :key="index">{{item.corpName}}</Option>
						</Select>
					</FormItem>
					<FormItem label="绑定套餐" prop="packageId" v-if="formValidate1.type == 2" style="width:510px">
						<CheckboxGroup v-model="formValidate1.packageId" style="display: flex;">
							<li v-for="(item,index) in cpackages" :key="index" style="list-style: none;">
								<Checkbox :label="item.packageId">{{ item.packageName }}</Checkbox>
							</li>
						</CheckboxGroup>
					</FormItem>

					<FormItem label="是否需要实名制" style="width:510px">
						<Select v-model="formValidate1.realName" clearable>
							<Option v-for="(item,index) in realNameOrNot" :value="item.value" :key="index">
								{{item.label}}
							</Option>
						</Select>
					</FormItem>
					<FormItem label="实名制规则" prop="realNameCity" style="width:510px" v-if="formValidate1.realName === 0">
						<Select v-model="formValidate1.realNameCity" filterable clearable>
							<Option v-for="item in realNameCitys" :value="item.groupId" :key="item.groupId">{{item.groupName}}</Option>
						</Select>
					</FormItem>
					<FormItem label="路由ID" prop="routeId" style="width:510px" >
						<Input placeholder="请输入路由ID" v-model="formValidate1.routeId" clearable />
					</FormItem>
					<FormItem label="动态开户UPCC签约模板" prop="openUpccSignId" style="width:510px">
						<Input maxlength="32" placeholder="请输入动态开户UPCC签约模板" v-model.trim="formValidate1.openUpccSignId" clearable />
					</FormItem>
					<FormItem label="套餐到期UPCC签约模板" prop="expireUpccSignId" style="width:510px">
						<Input maxlength="32" placeholder="请输入套餐到期UPCC签约模板" v-model.trim="formValidate1.expireUpccSignId" clearable />
					</FormItem>
				</Form>
				<!-- 模板文件table -->
				<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>

			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="BatchcancelModal">取消</Button>
				<Button type="primary" :loading="batchLoading" @click="BatchhandleUpload">确定</Button>
			</div>
		</Modal>
		<Modal v-model="modal4" title="导出" @on-ok="ok" @on-cancel="cancelModal">
			<span style="font-weight:bold;">请确认是否导出？</span>&nbsp;&nbsp;
		</Modal>
		<Modal v-model="modal5" title="主卡详情" @on-ok="ok" @on-cancel="cancelModal" width="620px">
			<div class="search_head" style="font-weight:bold;">
				<span>MSISDN:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{more.msisdn}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span style="font-weight:bold;">ICCID:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{more.iccid}}</span>&nbsp;&nbsp;<br>
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span style="font-weight:bold;">IMSI:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{more.imsi}}</span>&nbsp;&nbsp;<br>
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span style="font-weight:bold;">验证码:</span>&nbsp;&nbsp;
				<span>{{more.pin2}}</span>&nbsp;&nbsp;<br>
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>PUK:</span>&nbsp;&nbsp;
				<span>{{more.puk1}}</span>&nbsp;&nbsp;<br>
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>output file:</span>&nbsp;&nbsp;<br>
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>ADM:</span>&nbsp;&nbsp;
				<span>{{more.fileNameAdm}}</span>&nbsp;&nbsp;<br>
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>SDB:</span>&nbsp;&nbsp;
				<span>{{more.fileNameSdb}}</span>&nbsp;&nbsp;<br>
			</div>
		</Modal>
		<Modal v-model="modal6" title="修改主卡过期时间" :mask-closable="false" @on-cancel="updatecancelModal" width="620px">
			<div class="search_head" style="font-weight:bold;">
				<Form ref="formValidate3" :model="formValidate3" :rules="ruleValidate3" :label-width="140"
					:label-height="100" inline style="font-weight:bold;">
					<FormItem label="主卡过期时间:" prop="outTime" style="width:510px">
						<Input placeholder="主卡过期时间..." v-model="formValidate3.outTime" clearable style="width:300px">
						<span slot="append">月</span>
						</Input>

					</FormItem>
				</Form>
			</div>
			<div class="search_head" style="font-weight:bold;margin-left: 50px;">
				<Checkbox v-model="single">刷新存量未过期卡</Checkbox>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="updatecancelModal">取消</Button>
				<Button type="primary" :loading="updatetimeLoading" @click="updatehandleUpload">确定</Button>
			</div>
		</Modal>
		<Modal v-model="modal7" title="主卡导入记录查看" :mask-closable="false" :footer-hide="true" width="1285px"
			:loading="recordLoading">
			<div class="search_head">
				<Button icon="ios-arrow-back" @click="back">返回</Button> &nbsp;&nbsp;&nbsp;
			</div>
			<Table :columns="taskColumns" :data="taskData" :ellipsis="true" :loading="taskloading">
				<template slot-scope="{ row, index }" slot="successFilePath">
					<Button v-has="'download'" v-if="row.status === 1 || row.successNum === 0" disabled type="success"
						@click="exportfile(row.taskId,1)">点击下载</Button>
					<Button v-has="'download'" v-else type="success" @click="exportfile(row.taskId,1)">点击下载</Button>
				</template>
				<template slot-scope="{ row, index }" slot="failFilePath">
					<Button v-has="'download'" v-if="row.status === 1 || row.failNum === 0" disabled type="error"
						@click="exportfile(row.taskId,2)">点击下载</Button>
					<Button v-has="'download'" v-else type="error" @click="exportfile(row.taskId,2)">点击下载</Button>
				</template>
				<template slot-scope="{ row, index }" slot="detail">
					<Button v-has="'info'" type="info" v-if="row.status === 1" disabled
						@click="recordInfo(row)">点击查看</Button>
					<Button v-has="'info'" type="info" v-else @click="recordInfo(row)">点击查看</Button>
				</template>
			</Table>
			<!-- 分页 -->
			<Page :total="recordTotal" :current.sync="currentRecordPage" show-total show-elevator
				@on-change="goRecordPage" style="margin: 15px 0;" />
		</Modal>
		<Modal v-model="modal8" title="详情" :mask-closable="false" :footer-hide="true" width="600px"
			style="padding: 30px;">
			<div class="search_head" style="font-weight:bold;">
				<span>目标OTA:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.ota}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>短信模板:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.smsTemplateName}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>卡片类型:</span>&nbsp;&nbsp;
				<span
					style="font-weight:bold;">{{ info.type === 1 ? '普通卡' : info.type === 2 ? '省移动' : info.type === 3 ? '后付费' : info.type === 4 ? '联合发卡' : ''}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>TPLID:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.tplid}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>主卡形态:</span>&nbsp;&nbsp;
				<span
					style="font-weight:bold;">{{ info.cardForm === 1 ? '普通卡(实体卡)' : info.cardForm === 2 ? 'Esim卡' : info.cardForm === 3 ? '贴片卡' : info.cardForm === 4 ? 'IMSI号' : '' }}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>是否需要实名制:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;" v-if="info.realname">是</span>
				<span style="font-weight:bold;" v-else>否</span>
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>实名制规则:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.realname}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>是否支持GTP动态路由:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.supportGtpRoute}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;" v-if="info.supportGtpRoute=='是'">
				<span>路由ID:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.routeId}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>动态开户UPCC签约模板:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.openUpccSignId}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>套餐到期UPCC签约模板:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.expireUpccSignId}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>adm文件:</span>&nbsp;&nbsp;
				<Button class="button" v-has="'download'" @click="exportfile(taskId,4)">点击下载</Button>
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>sdb文件:</span>&nbsp;&nbsp;
				<Button class="button" v-has="'download'" @click="exportfile(taskId,3)">点击下载</Button>
			</div>
			<div class="search_head" style="font-weight:bold;" v-if="info.esimPathSdb">
				<span>esim文件:</span>&nbsp;&nbsp;
				<Button class="button" v-has="'download'" @click="exportfile(taskId,5)">点击下载</Button>
			</div>
			<div style="display: flex;align-items: center;justify-content: center;">
				<Button icon="ios-arrow-back" size="large" @click="reback">返回</Button>
			</div>
		</Modal>
		<a ref="downloadLink" style="display: none"></a>
	</div>
</template>

<script>
	import {
		Pagesearch,
		cardUp,
		cardsearch,
		cardexport,
		importfile,
		getchannel,
    getrealNameGroups,
		getotalist,
		gettemplate,
		BatchUpdate,
		Update,
		UpdateTime,
		Updatesearch,
		getExpireTime,
		updateExpireTimeBatch,
		exportFile,
		getRecordPage
	} from '@/api/mastercdr'
	import {
		getCorpList
	} from '@/api/product/package/batch';
	import {
		searchRule
	} from '@/api/realname/rule'
	export default {
		components: {},
		data() {
			// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (!value) {
					callback(new Error(this.$t('请上传文件')))
				} else {
					callback()
				}
			}
			// 校验是否为纯数字并且是月份
			const checkNumber = (rule, value, callback) => {
				let reg = /^[0-9]\d*$/;
				if (reg.test(value)) {
					if (value >= 1 && value <= 60) {
						callback();
					} else {
						callback(new Error("请输入1到60月"));
					}
				} else {
					callback(new Error("请输入纯数字"));
				}
			};
			const checkroutingID = (rule, value, callback) => {
				if (value > **********) {
					callback(new Error("超过了最大限制范围**********"));
				} else {
					callback();
				}
			};
			return {
				corpList: [],
				corpId: '',
				isStoreOut: '',
				corpName: '',
				modelData: [{
					'iccid': '********',
				}, ],
				modelColumns: [{
						title: 'iccid',
						key: 'iccid'
					}, // 列名根据需要添加
				],
				formValidate: {
					ota: '',
					sms: '',
					warehouse: '',
					cardType: '',
					tplid: '',
					cardShape: '',
					packageId: [],
					provId: '',
					realName: '',
					realNameCity: '',
					admfile: null,
					sdbfile: null,
					xlsxfile: null,
					supportGtpRoute: '',
					routeId: '',
					openUpccSignId: '',
					expireUpccSignId: '',
				},
				packageIdList: [],
				realNameflg: '',
				filePathAdm: '',
				filePathSdb: '',
				esimPathSdb: '',
				ruleValidate: {
					ota: [{
						required: true,
						message: '请填写目标OTA',
					}],
					sms: [{
						required: true,
						message: '请选择短信模板',
					}],
					warehouse: [{
						required: true,
						message: '请选择仓库',
					}],
					cardType: [{
						required: true,
						message: '请选择卡片类型',
					}],
					tplid: [{
						required: true,
						message: '请填写TPLID',
						trigger: 'blur',
					}, {
						min: 0,
						max: 20,
						message: '输入20位以内的数字',
						trigger: 'blur'
					}, {
						pattern: /^[0-9]\d*$/,
						message: '请输入纯数字',
						trigger: 'blur'
					}, ],
					cardShape: [{
						required: true,
						message: '请选择主卡形态',
					}],
					packageId: [{
						required: true,
						message: '请选择套餐',
					}],
					provId: [{
						required: true,
						message: '请选择合作商',
						trigger: 'change'
					}],
					realName: [{
						required: true,
						message: '请选择是否需要实名制',
					}],
					realNameCity: [{
						required: true,
						message: '请选择实名制规则',
					}],
					supportGtpRoute: [{
						required: true,
						message: '请选择是否支持GTP动态路由'
					}],
					routeId: [{
						required: true,
						message: '请输入路由ID'
					}, {
						pattern: /^[-]?\d+$/,
						message: '请输入整数',
						trigger: 'blur',
					}, {
						validator: checkroutingID,
						trigger: "blur"
					} ],
					admfile: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
					sdbfile: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
					xlsxfile: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
				},
				formValidate1: {
					iccidFile: '',
					sms: '',
					warehouse: '',
					cardType: '',
					tplid: '',
					channel: '',
					status: '',
					provId: '',
					packageId: [],
					realName: '',
					realNameCity: '',
					type: '',
					templateid: '',
				},
				ruleValidate1: {
					iccidFile: [{
						required: true,
						message: '请选择供应商',
						trigger: 'blur'
					}],
					ota: [{
						required: true,
						message: '请填写目标OTA',
						trigger: 'change'
					}],
					templateid: [{
						required: true,
						message: '请选择短信模板',
					}],
					channel: [{
						required: true,
						message: '请选择渠道商',
					}],
					type: [{
						required: true,
						message: '请选择卡片类型',
					}],
					tplid: [{
						required: false,
						message: '请填写TPLID',
						trigger: 'blur',
					}, {
						min: 0,
						max: 20,
						message: '输入20位以内的数字',
						trigger: 'blur'
					}, {
						pattern: /^[0-9]\d*$/,
						message: '请输入纯数字',
						trigger: 'blur'
					}, ],
					cardShape: [{
						required: true,
						message: '请选择主卡形态',
					}],
					packageId: [{
						required: true,
						message: '请选择套餐',
					}],
					provId: [{
						required: true,
						message: '请选择合作商',
						trigger: 'change'
					}],
					realName: [{
						required: true,
						message: '请选择是否需要实名制',
					}],
					realNameCity: [{
						required: true,
						message: '请选择实名制规则',
					}],
					status: [{
						required: true,
						message: '请选择修改后状态',
					}],
					routeId: [{
						pattern: /^[-]?\d+$/,
						message: '请输入整数',
						trigger: 'blur',
					}, {
						validator: checkroutingID,
						trigger: "blur"
					} ],
				},
				formValidate2: {
					status: ''
				},
				ruleValidate2: {
					iccidFile: [{
						required: true,
						message: '请选择供应商',
						trigger: 'blur'
					}],
					ota: [{
						required: true,
						message: '请填写目标OTA',
					}],
					templateId: [{
						required: true,
						message: '请选择短信模板',
					}],
					channelId: [{
						required: true,
						message: '请选择渠道商',
					}],
					type: [{
						required: true,
						message: '请选择卡片类型',
					}],
					tplid: [{
						required: true,
						message: '请填写TPLID',
						trigger: 'blur'
					}, {
						min: 0,
						max: 20,
						message: '输入20位以内的数字',
						trigger: 'blur'
					}, {
						pattern: /^[0-9]\d*$/,
						message: '请输入纯数字',
						trigger: 'blur'
					}, ],
					cardShape: [{
						required: true,
						message: '请选择主卡形态',
					}],
					packageId: [{
						type: 'array',
						required: true,
						message: '请选择套餐',
					}],
					corpId: [{
						required: true,
						message: '请选择合作商',
						// trigger: 'change'
					}],
					realName: [{
						required: true,
						message: '请选择是否需要实名制',
					}],
					realNameid: [{
						required: true,
						message: '请选择实名制规则',
						// trigger: 'blur,change'
					}],
					status: [{
						required: true,
						message: '请选择修改后状态',
					}],
					routeId: [{
						required: true,
						message: '请输入路由ID'
					}, {
						pattern: /^[-]?\d+$/,
						message: '请输入整数',
						trigger: 'blur',
					}, {
						validator: checkroutingID,
						trigger: "blur"
					} ],
				},
				choosed: {
					msisdn: '',
					imsi: '',
					iccid: '',
					sms: '',
					warehouse: '',
					cardType: '',
					tplid: '',
					channel: '',
					status: '',
					packageId: [],
					corpId: '',
					realName: '',
					realNameCity: '',
					templateId: '',
					routeId: '',
					openUpccSignId: '',
					expireUpccSignId: ''
				},
				formValidate3: {},
				ruleValidate3: {
					outTime: [{
							required: true,
							message: '请输入主卡过期时间',
							trigger: 'change'
						},
						{
							validator: checkNumber,
							trigger: "blur"
						}
					]
				},
				more: {},
				// 表头信息
				columns: [{
						title: 'MSISDN',
						key: 'msisdn',
						align: 'center',
						width: '180px',
						fixed: 'left',
					},
					{
						title: 'IMSI',
						key: 'imsi',
						align: 'center',
						width: '180px',
						fixed: 'left',
					},
					{
						title: 'ICCID',
						key: 'iccid',
						align: 'center',
						width: '180px',
						fixed: 'left',
					},
					{
						title: '卡片类型',
						key: 'type',
						align: 'center',
						width: '100px',
						tooltip: true,
						render: (h, params) => {
							const row = params.row
							const color = row.type == 1 ? '#19be6b' : row.type == 2 ? '#27A1FF' : row.type == 2 ?
								'#ff0000' : '#5555ff'
							const text = row.type == 1 ? '普通卡' : row.type == 2 ? '省移动' : row.type == 3 ? '后付费' :
								row.type == 4 ? '联合发卡' : ''
							return h('label', {
								style: {
									color: color
								}
							}, text)
						}
					},
					{
						title: '主卡形态',
						key: 'cardForm',
						align: 'center',
						width: '150px',
						tooltip: true,
						render: (h, params) => {
							const row = params.row
							const color = row.cardForm == 1 ? '#19be6b' : row.cardForm == 2 ? '#27A1FF' : '#ff0000'
							const text = row.cardForm == 1 ? '普通卡（实体卡）' : row.cardForm == 2 ? 'Esim卡' : row
								.cardForm == 3 ? '贴片卡' : row.cardForm == 4 ? 'IMSI号' : ''
							return h('label', {
								style: {
									color: color
								}
							}, text)
						}
					},
					{
						title: '合作模式',
						key: 'cooperationMode',
						align: 'center',
						width: '100px',
						tooltip: true,
						render: (h, params) => {
							const row = params.row
							var text = row.cooperationMode == 1 ? "代销" : row.cooperationMode == 2 ? "A~Z" : ''
							return h('label', text)
						}
					},
					{
						title: '仓库',
						key: 'storeName',
						align: 'center',
						width: '200px',
						tooltip: true,
					},
					{
						title: '渠道商',
						key: 'channelName',
						align: 'center',
						width: '200px',
						tooltip: true,
					},
					{
						title: '出库状态',
						key: 'isStoreOut',
						align: 'center',
						width: '100px',
						render: (h, params) => {
							const row = params.row
							var text = row.isStoreOut == 1 ? "未出库" : row.isStoreOut == 2 ? "已出库" : ''
							return h('label', text)
						}
					},
					{
						title: '入库时间',
						key: 'createTime',
						align: 'center',
						width: '160px',
						tooltip: true,
					},
					{
						title: '出库时间',
						key: 'outTime',
						align: 'center',
						width: '160px',
						tooltip: true,
					},
					{
						title: '目标OTA',
						key: 'otaName',
						align: 'center',
						width: '200px',
						tooltip: true,
					},
					{
						title: 'TPLID',
						key: 'tplid',
						align: 'center',
						width: '150px',
						tooltip: true,
					},
					{
						title: '短信模板',
						key: 'templateName',
						align: 'center',
						width: '200px',
						tooltip: true,
					},
					{
						title: '实名认证状态',
						key: 'authStatus',
						align: 'center',
						width: '120px',
						tooltip: true,
						render: (h, params) => {
              const row = params.row;
              let lastTent = "..."
              let text;
              var manyAuthStatus;
              if (this.tableData[params.index].realNameInfos && this.tableData[params.index].realNameInfos.length > 0) {
                manyAuthStatus = this.tableData[params.index].realNameInfos.map(function(info) {
                  text = info.authStatus === "1" ? "待认证" : info.authStatus === "2" ? "认证中" :
                  	info.authStatus === "3" ? "认证通过" : info.authStatus === "4" ? "认证失败" :
                  	info.authStatus === "5" ? "证件已过期" : info.realnameId === null ? "无需认证" : "未认证"
                  return text;
                });
              }
              if (!manyAuthStatus || manyAuthStatus.length === 0) {
                  return h('span', '');
                }

                if (manyAuthStatus.length === 1) {
                  return h('span', manyAuthStatus[0]);
                }

                if (manyAuthStatus.length === 2) {
                  // 直接显示两个元素，不需要 Tooltip
                  return h('div', [
                    h('div', manyAuthStatus[0]),
                    h('div', manyAuthStatus[1]),
                  ]);
                }
                // 当有超过两个元素时，使用 Tooltip 显示前两个元素，并在 Tooltip 中显示所有元素
                return h('div', [
                  h('Tooltip', {
                    props: {
                      placement: 'bottom',
                      transfer: true,
                    },
                    style: {
                      cursor: 'pointer',
                    },
                  }, [
                    h('span', {
                      style: {
                        display: 'block',
                      },
                    }, manyAuthStatus[0]),
                    h('span', {}, manyAuthStatus[1]),
                    h('div', {}, lastTent),
                    h('ul', {
                      slot: 'content',
                      style: {
                        listStyleType: 'none',
                        whiteSpace: 'normal',
                        wordBreak: 'break-all' ,//超出隐藏
                      },
                    },
                      manyAuthStatus.map(item => { // 从第三个元素开始映射
                        return h('li', item);
                      })),
                    ]),
                  ]);
              }
					},
					{
						title: '证件有效日期',
						key: 'certificateExpirationTime',
						align: 'center',
						width: '160px',
						tooltip: true,
            render: (h, params) => {
              const row = params.row;
              let lastTent = "..."
              var manyCertificateExpirationTime;

              if (this.tableData[params.index].realNameInfos && this.tableData[params.index].realNameInfos.length > 0) {
                manyCertificateExpirationTime = this.tableData[params.index].realNameInfos.map(function(info) {
                    return info.certificateExpirationTime;
                });
              }

              if (!manyCertificateExpirationTime || manyCertificateExpirationTime.length === 0) {
                return h('span', '');
              }

              if (manyCertificateExpirationTime.length === 1) {
                return h('span', manyCertificateExpirationTime[0] || '--');
              }

              if (manyCertificateExpirationTime.length === 2) {
                // 直接显示两个元素，不需要 Tooltip
                return h('div', [
                  h('div', manyCertificateExpirationTime[0] || '--'),
                  h('div', manyCertificateExpirationTime[1] || '--'),
                ]);
              }

              // 当有超过两个元素时，使用 Tooltip 显示前两个元素，并在 Tooltip 中显示所有元素
              return h('div', [
                h('Tooltip', {
                  props: {
                    placement: 'bottom',
                    transfer: true,
                  },
                  style: {
                    cursor: 'pointer',
                  },
                }, [
                  h('span', {
                    style: {
                      display: 'block',
                    },
                  }, manyCertificateExpirationTime[0] || '--'),
                  h('span', {}, manyCertificateExpirationTime[1] || '--'),
                  h('div', {}, lastTent),
                  h('ul', {
                    slot: 'content',
                    style: {
                      listStyleType: 'none',
                      whiteSpace: 'normal',
                      wordBreak: 'break-all' ,//超出隐藏
                    },
                  },
                    manyCertificateExpirationTime.map(item => { // 从第三个元素开始映射
                      return h('li', item || "--");
                    })),
                  ]),
                ]);
            }
					},
					{
						title: '实名制规则名称',
						key: 'ruleName',
						align: 'center',
						width: '200px',
						tooltip: true,
            render: (h, params) => {
              const row = params.row;
              let lastTent = "..."
              var manyRuleName;

              if (this.tableData[params.index].realNameInfos && this.tableData[params.index].realNameInfos.length > 0) {
                manyRuleName = this.tableData[params.index].realNameInfos.map(function(info) {
                    return info.ruleName;
                });
              }

              if (!manyRuleName || manyRuleName.length === 0) {
                return h('span', '');
              }

              if (manyRuleName.length === 1) {
                return h('span', manyRuleName[0]);
              }

              if (manyRuleName.length === 2) {
                // 直接显示两个元素，不需要 Tooltip
                return h('div', [
                  h('div', manyRuleName[0]),
                  h('div', manyRuleName[1]),
                ]);
              }
              // 当有超过两个元素时，使用 Tooltip 显示前两个元素，并在 Tooltip 中显示所有元素
              return h('div', [
                h('Tooltip', {
                  props: {
                    placement: 'bottom',
                    transfer: true,
                  },
                  style: {
                    cursor: 'pointer',
                  },
                }, [
                  h('span', {
                    style: {
                      display: 'block',
                    },
                  }, manyRuleName[0]),
                  h('span', {}, manyRuleName[1]),
                  h('div', {}, lastTent),
                  h('ul', {
                    slot: 'content',
                    style: {
                      listStyleType: 'none',
                      whiteSpace: 'normal',
                      wordBreak: 'break-all' ,//超出隐藏
                    },
                  },
                    manyRuleName.map(item => { // 从第三个元素开始映射
                      return h('li', item);
                    })),
                  ]),
                ]);
            }
					},
					{
						title: '是否支持GTP动态路由',
						key: 'supportGtpRoute',
						align: 'center',
						width: '160px',
						tooltip: true,
						render: (h, params) => {
							const row = params.row
							var text = row.supportGtpRoute === "1" ? "是" : row.supportGtpRoute === "2" ? "否" : ""
							return h('label', text)
						}
					},
					{
						title: '路由ID',
						key: 'routeId',
						align: 'center',
						width: '150px',
						tooltip: true,
					},
					{
						title: '动态开户UPCC签约模板',
						key: 'openUpccSignId',
						align: 'center',
						width: '170px',
						tooltip: true
					},
					{
						title: '套餐到期UPCC签约模板',
						key: 'expireUpccSignId',
						align: 'center',
						width: '170px',
						tooltip: true,
					},
					{
						title: '操作',
						slot: 'action',
						align: 'center',
						width: '150px',
						fixed: 'right',
					}
				],
				taskColumns: [{
						title: '导入时间',
						key: 'beginTime',
						align: 'center',
						width: '150px',
					},
					{
						title: '完成时间',
						key: 'endTime',
						align: 'center',
						width: '150px',
					},
					{
						title: '处理状态',
						key: 'status',
						align: 'center',
						width: '120px',
						render: (h, params) => {
							const row = params.row
							var text = row.status === 1 ? "处理中" : row.status === 2 ? "已完成" : row.status === 3 ?
								"任务失败" : ''
							return h('label', text)
						}
					},
					{
						title: '导入号码总数量',
						key: 'total',
						align: 'center',
						width: '120px',
					},
					{
						title: '导入成功数量',
						key: 'successNum',
						align: 'center',
						width: '120px',
					},
					{
						title: '导入失败数量',
						key: 'failNum',
						align: 'center',
						width: '120px',
					},
					{
						title: '下载导入成功号码列表',
						slot: 'successFilePath',
						align: 'center',
						width: '160px',
					},
					{
						title: '下载导入失败号码列表',
						slot: 'failFilePath',
						align: 'center',
						width: '160px',
					},
					{
						title: '详情',
						slot: 'detail',
						align: 'center',
						width: '150px',
					},
				],
				taskData: [],
				info: {},
				otas: [],
				smsAll: [{
					id: 82,
					templateName: '阳阳'
				}],
				cpackages: [],
				cardTypes: [{
						label: '普通卡',
						value: 1
					},
					{
						label: '省移动',
						value: 2
					},
					{
						label: '后付费',
						value: 3
					},
					{
						label: '联合发卡',
						value: 4
					},
				],
				cardShapes: [{
						label: '普通卡（实体卡）',
						value: 1
					},
					{
						label: 'Esim卡',
						value: 2
					},
					{
						label: '贴片卡',
						value: 3
					},
					{
						label: 'IMSI号',
						value: 4
					},
				],
				isStoreOutList: [{
					label: '未出库',
					value: 1
				}, {
					label: '已出库',
					value: 2
				}, ],
				packages: [{
						label: '套餐1',
						value: 0
					},
					{
						label: '套餐2',
						value: 1
					}
				],
				provs: [],
				statuses: [{
						label: '正常',
						value: 1
					},
					{
						label: '暂停',
						value: 2
					},
					{
						label: '注销',
						value: 3
					}
				],
				channels: [{
						label: '渠道商1',
						value: 0
					},
					{
						label: '渠道商2',
						value: 1
					}
				],
				realNameOrNot: [{
						label: '是',
						value: 0
					},
					{
						label: '否',
						value: 1
					}
				],
				realNameCitys: [],
				warehouses: [{
						label: '仓库1',
						value: 0
					},
					{
						label: '仓库2',
						value: 1
					}
				],
				tableData: [],
        tableDatas: [
          {
            "realNameInfos": [
              {
                "ruleName": "规则1",
                "authStatus": '1',
                "certificateExpirationTime": 1
              },
              {
                "ruleName": "规则2",
                "authStatus": '2',
                "certificateExpirationTime": 2
              },
              {
                "ruleName": "规则3",
                "authStatus": '3',
                "certificateExpirationTime": 3
              }
            ]
          },
          {
            "realNameInfos": [
              {
                "ruleName": "规则1",
                "authStatus": '4',
                "certificateExpirationTime": 1
              },
              {
                "ruleName": "规则2",
                "authStatus": '5',
                "certificateExpirationTime": 2
              },
            ]
          },
          {
            "realNameInfos": [
              {
                "ruleName": "规则1",
                "authStatus": '6',
                "certificateExpirationTime": 1
              },
            ]
          },
        ],
				taskData: [],
				loading: false,
				searchLoading: false,
				importLoading: false,
				updateLoading: false,
				batchLoading: false,
				updatetimeLoading: false,
				detailsLoading: false,
				recordLoading: false,
				taskloading: false,
				exportloading: false,
				currentPage: 1,
				total: 0,
				currentRecordPage: 1,
				recordTotal: 0,
				msisdnCondition: '',
				imsiCondition: '',
				iccidCondition: '',
				cardTypeCondition: '',
				cardShapeCondition: '',
				modifyRoute: '',
				ids: [],
				modal1: false,
				modal2: false,
				modal3: false,
				modal4: false,
				modal5: false,
				modal6: false,
				modal7: false,
				modal8: false,
				status: '',
				downloading: false,
				message: '文件仅支持xls、xlsx格式文件,大小不能超过5MB',
				message1: '文件仅支持csv格式文件',
				uploadUrl: '',
				timeRangeArray: [],
				searchBeginTime: '',
				searchEndTime: '',
				outTime: null,
				single: false,
				taskId: '',
				formObj: {
					startICCID: '',
					endICCID: '',
				},
				formObjRule: {
					startICCID: [{
						required: true,
						type: "string",
						message: "起始ICCID不能为空",
					}, {
						min: 19,
						max: 20,
						message: '请输入19或者20位',
						trigger: 'blur'
					}],
					endICCID: [{
						required: true,
						type: "string",
						message: "结束ICCID不能为空",
					}, {
						min: 19,
						max: 20,
						message: '请输入19或者20位',
						trigger: 'blur'
					}],
				}
			}
		},
		watch: {
			'$route': 'reload'
		},
		computed: {},
		methods: {
			// 页面加载
			goPageFirst(page) {
				this.loading = true
				var _this = this
				let current = page
				let size = 10
				let cardForm = this.cardShapeCondition
				let iccid = this.iccidCondition
				let startICCID = this.formObj.startICCID
				let endICCID = this.formObj.endICCID
				let imsi = this.imsiCondition
				let msisdn = this.msisdnCondition
				let type = this.cardTypeCondition
				let startTime = this.searchBeginTime
				let endTime = this.searchEndTime
				if (startTime) {
					startTime = this.searchBeginTime + " 00:00:00"
				}
				if (endTime) {
					endTime = this.searchEndTime + " 00:00:00"
				}
				let corpId = this.corpId
				let corpName = this.corpName
				let isStoreOut = this.isStoreOut
				let isNeedMcc = false
				Pagesearch({
					current,
					size,
					cardForm,
					endTime,
					startTime,
					iccid,
					startICCID,
					endICCID,
					imsi,
					msisdn,
					type,
					isNeedMcc,
					corpId,
					corpName,
					isStoreOut
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchLoading = false
						this.page = page
						this.total = res.count
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchLoading = false
				})
			},
			// 分页跳转
			goPage(page) {
				this.goPageFirst(page)
			},
			// 查询按钮，指定号码查询
			search() {
				var a = this.formObj.startICCID.substring(0,13)
				var b = this.formObj.endICCID.substring(0,13)
				var c = this.formObj.startICCID.length
				var d = this.formObj.endICCID.length
				this.$refs["formObj"].validate((valid) => {
					if (valid) {
						if (c == d) {
							if (a != b) {
								this.$Message.error({
									content: '请保持起始ICCID与结束ICCID前13位数相同！',
									duration: 3
								})
								return
							}
						} else {
							this.$Message.error({
								content: '请保持起始ICCID与结束ICCID位数相同！',
								duration: 3
							})
							return
						}
						this.searchLoading = true
						this.currentPage = 1
						this.goPageFirst(1)
					}
				})
			},
			//模板下载
			downloadFile: function() {
				this.$refs.modelTable.exportCsv({
					filename: '批量文件模板',
					// type:'xlsx',
					columns: this.modelColumns,
					data: this.modelData
				})
			},
			// 选择合作商并选择套餐
			getProvs(id) {
				this.formValidate1.packageId = []
				this.formValidate.packageId = []
				this.choosed.packageId = []
				this.packageIdList = []
				this.cpackages = []

				this.provs.forEach((value) => {
					if (value.corpId === id) {
						this.cpackages = value.cpackages
					}
				})

			},
			// 不需要实名制时，清除实名制规则缓存
			isgetrealName(data) {
				this.formValidate.realName = data
				if (this.formValidate.realName === 1) {
					this.formValidate.realNameCity = ''
				}
			},
			// 获取套餐id
			getcpackages(id) {
				this.packageIdList.push(id.toString())
			},
			// 获取所属渠道
			getcorpId(id) {
				this.corpList.forEach((value) => {
					if (value.corpId === id) {
						this.corpName = value.corpName
					}
				})
			},
			getName(id) {
				this.realNameflg = id
			},
			// 勾选按钮
			handleRowChange(selection) {
				this.selection = selection
				this.ids = []
				selection.map((value, index) => {
					this.ids.push(value.fallbackId)
				})
			},
			hanldeDateClear() {
				this.searchBeginTime = ''
				this.searchEndTime = ''
			},
			handleDateChange(dateArr) {
				let beginDate = this.timeRangeArray[0] || ''
				let endDate = this.timeRangeArray[1] || ''
				if (beginDate == '' || endDate == '') {
					return
				}
				[this.searchBeginTime, this.searchEndTime] = dateArr
			},
			sdbremoveFile() {
				this.formValidate.sdbfile = ''
			},
			admremoveFile() {
				this.formValidate.admfile = ''
			},
			xlsxremoveFile() {
				this.formValidate.xlsxfile = ''
			},
			BatchremoveFile() {
				this.formValidate1.iccidFile = ''
			},
			handleBeforeUpload(file) {
				if (!/^.+(\.adm|\.ADM)$/.test(file.name)) {
					this.$Notice.warning({
						title: '文件格式不正确',
						desc: '文件 ' + file.name + ' 格式不正确，请上传.adm格式文件。'
					})
				} else {
					// 上传adm文件
					let formData = new FormData()
					formData.append('file', file)
					importfile(formData).then(res => {
						if (res.code === '0000') {
							this.$Notice.success({
								title: '操作成功',
								desc: '成功上传adm文件'
							})
							this.formValidate.admfile = file
							this.filePathAdm = res.data
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err)
					})
				}
				return false
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			sdbhandleBeforeUpload(file) {
				if (!/.sdb$/.test(file.name)) {
					this.$Notice.warning({
						title: '文件格式不正确',
						desc: '文件 ' + file.name + ' 格式不正确，请上传.sdb格式文件。'
					})
				} else {
					// 上传sdb文件
					let formData = new FormData()
					formData.append('file', file)
					importfile(formData).then(res => {
						if (res.code === '0000') {
							this.$Notice.success({
								title: '操作成功',
								desc: '成功上传sdb文件'
							})
							this.formValidate.sdbfile = file
							this.filePathSdb = res.data
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err)
					})
				}
				return false
			},
			sdbfileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			sdbfileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			xlsxhandleBeforeUpload(file) {
				if (!/^.+(\.xls|\.xlsx)$/.test(file.name)) {
					this.$Notice.warning({
						title: '文件格式不正确',
						desc: '文件 ' + file.name + ' 格式不正确，请上传.xlsx和.xls格式文件。'
					})
				} else {
					// 上传xlsx文件
					let formData = new FormData()
					formData.append('file', file)
					importfile(formData).then(res => {
						if (res.code === '0000') {
							this.$Notice.success({
								title: '操作成功',
								desc: '成功上传xlsx文件'
							})
							this.formValidate.xlsxfile = file
							this.esimPathSdb = res.data
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err)
					})
				}
				return false
			},
			xlsxfileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			xlsxfileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			BatchhandleBeforeUpload(file) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: '文件格式不正确',
						desc: '文件 ' + file.name + '格式不正确，请上传.csv格式文件。'
					})
				} else {
					this.formValidate1.iccidFile = file
				}
				return false
			},
			BatchhandleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			BatchfileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			BatchfileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			// 批量修改
			BatchhandleUpload() {
				if (!this.formValidate1.iccidFile) {
					this.$Message.warning('请选择需要上传的文件')
					return
				} else {
					this.$refs.formValidate1.validate(valid => {
						if (valid) {
							let formData = new FormData()
							formData.append('corpId', this.formValidate1.provId == undefined ? '' : this
								.formValidate1.provId)
							formData.append('packages', this.formValidate1.packageId == undefined ? '' : this
								.formValidate1.packageId)
							formData.append('smsId', this.formValidate1.templateid == undefined ? '' : this
								.formValidate1.templateid)
							formData.append('tplid', this.formValidate1.tplid == undefined ? '' : this
								.formValidate1.tplid)
							formData.append('type', this.formValidate1.type == undefined ? '' : this.formValidate1
								.type)
							formData.append('file', this.formValidate1.iccidFile)
							formData.append('status', this.formValidate1.status == undefined ? '' : this
								.formValidate1.status)
							formData.append('routeId', this.formValidate1.routeId == undefined ? '' : this
								.formValidate1.routeId)
							if (this.formValidate1.openUpccSignId) {
								formData.append('openUpccSignId', this.formValidate1.openUpccSignId == undefined ? '' : this
									.formValidate1.openUpccSignId)
							}
							if (this.formValidate1.expireUpccSignId) {
								formData.append('expireUpccSignId', this.formValidate1.expireUpccSignId == undefined ? '' : this
									.formValidate1.expireUpccSignId)
							}
							if (this.formValidate1.realName === 0 || this.formValidate1.realName === 1) {
								formData.append('updateRealNameFlag', 1)
							} else {
								formData.append('updateRealNameFlag', 2)
							}
							if (this.formValidate1.realName === 0) {
								formData.append('realnameId', this.formValidate1.realNameCity)
							}
							// 若没有套餐信息，则提示不让导入
							if (this.formValidate1.type === 2 && this.formValidate1.packageId === "") {
								this.$Modal.confirm({
									title: '提示',
									content: '若没有套餐信息，不可以导入'
								})
								return
							}
							this.batchLoading = true
							BatchUpdate(formData).then(res => {
								if (res.code === '0000') {
									let data = res.data
									this.$Notice.success({
										title: '操作成功',
										desc: '操作成功'
									})
									this.batchLoading = false
									this.BatchcancelModal()
									this.currentPage = 1
									this.goPageFirst(1)
								}
							}).catch((err) => {
								this.batchLoading = false
								this.BatchcancelModal()
								console.log(err)
							})
						}
					})

				}
			},
			//主卡导入-提交
			handleUpload() {
				if (!this.formValidate.sdbfile || !this.formValidate.admfile) {
					this.$Message.warning('请选择需要上传的文件')
					return
				} else {
					if (this.formValidate.cardShape == '2' && !this.formValidate.xlsxfile) {
						this.$Message.warning('请选择需要上传的文件')
						return
					} else {
						this.$refs.formValidate.validate(valid => {
							if (valid) {
								let cardForm = this.formValidate.cardShape
								let corpId = this.formValidate.provId
								let filePathAdm = this.filePathAdm
								let filePathSdb = this.filePathSdb
								let esimPathSdb = this.esimPathSdb
								let ota = this.formValidate.ota
								let packages = this.formValidate.packageId
								let realnameId = this.formValidate.realNameCity
								let smsId = this.formValidate.sms
								let tplid = this.formValidate.tplid
								let type = this.formValidate.cardType
								let supportGtpRoute = this.formValidate.supportGtpRoute
								let routeId = !this.formValidate.routeId ? undefined : this.formValidate.routeId
								let openUpccSignId = !this.formValidate.openUpccSignId ? undefined : this.formValidate.openUpccSignId
								let expireUpccSignId = !this.formValidate.expireUpccSignId ? undefined : this.formValidate.expireUpccSignId
								// 若没有套餐信息，则提示不让导入
								if (type === 2 && packages === "") {
									this.$Modal.confirm({
										title: '提示',
										content: '若没有套餐信息，不可以导入'
									})
									return
								}
								this.importLoading = true
								cardUp({
									cardForm,
									corpId,
									filePathAdm,
									filePathSdb,
									esimPathSdb,
									ota,
									realnameId,
									packages,
									smsId,
									tplid,
									type,
									supportGtpRoute,
									routeId,
									openUpccSignId,
									expireUpccSignId
								}).then(res => {
									if (res.code === '0000') {
										let data = res.data
										this.$Notice.success({
											title: '操作成功',
											desc: '操作成功'
										})
										this.importLoading = false
										this.cancelModal()
										this.currentPage = 1
										this.goPageFirst(1)
									}
								}).catch((err) => {
									this.importLoading = false
									this.cancelModal()
									console.log(err)
								})
							}
						})
					}
				}
			},
			// 修改过期时间
			updatehandleUpload() {
				this.$refs.formValidate3.validate(valid => {
					if (valid) {
						this.updatetimeLoading = true
						let expireNum = this.formValidate3.outTime
						let refresh = this.single
						updateExpireTimeBatch({
							expireNum,
							refresh
						}).then(res => {
							if (res.code === '0000') {
								let data = res.data
								this.$Notice.success({
									title: '操作提示',
									desc: '正在处理中，稍后通过邮件通知结果！'
								})
								this.updatetimeLoading = false
								this.updatecancelModal()
								this.currentPage = 1
								this.goPageFirst(1)
							}
						}).catch((err) => {
							this.updatetimeLoading = false
							this.updatecancelModal()
							console.log(err)
						})
					}
				})
			},
			// 单个修改
			modifyhandleUpload() {
				this.$refs.choosed.validate(valid => {
					if (valid) {
						// 判断是否选择套餐
						if (this.choosed.packageId < 1) {
							this.$Modal.warning({
								title: "请选择套餐",
							});
							return
						}
						let corpId = this.choosed.corpId //合作运营商id
						let id = this.choosed.imsi
						let iccid = this.choosed.iccid
						let packages = this.choosed.packageId //套餐id
						// let packages = this.packageIdList
						let smsId = this.choosed.templateId //短信模板id
						let status = this.choosed.status //状态修改
						let tplid = this.choosed.tplid //tplid
						let type = this.choosed.type //卡片类型
						let realnameId = null
						if (this.realNameflg === 0) {
							realnameId = this.choosed.realNameid //实名制国家id
						}
						let routeId = !this.choosed.routeId ? undefined : this.choosed.routeId
						let openUpccSignId = !this.choosed.openUpccSignId ? undefined : this.choosed.openUpccSignId
						let expireUpccSignId = !this.choosed.expireUpccSignId ? undefined : this.choosed.expireUpccSignId
						this.updateLoading = true
						Update({
							iccid,
							corpId,
							id,
							packages,
							realnameId,
							smsId,
							status,
							tplid,
							type,
							routeId,
							openUpccSignId,
							expireUpccSignId
						}).then(res => {
							if (res.code === '0000') {
								let data = res.data
								this.$Notice.success({
									title: '操作成功',
									desc: '操作成功'
								})
								this.updateLoading = false
								this.modifycancelModal()
								this.currentPage = 1
								this.goPageFirst(1)
							}
						}).catch((err) => {
							this.updateLoading = false
							this.modifycancelModal()
							console.log(err)
						}).finally(() => {
							this.updateLoading = false
							this.modifycancelModal()
						})
					}
				})
			},
			error(nodesc) {
				this.$Notice.error({
					title: '出错啦',
					desc: nodesc ? '' : '服务器内部错误'
				})
			},
			delteNumber(id) {
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						this.$Notice.success({
							title: '成功',
							desc: '操作成功'
						})
						this.currentPage = 1
						this.goPageFirst(1)
					}
				})
			},
			add() {
				this.modal1 = true
			},
			// 导出
			out() {
				var a = this.formObj.startICCID.substring(0,13)
				var b = this.formObj.endICCID.substring(0,13)
				var c = this.formObj.startICCID.length
				var d = this.formObj.endICCID.length
				this.$refs["formObj"].validate((valid) => {
					if (valid) {
						if (c == d) {
							if (a != b) {
								this.$Message.error({
									content: '请保持起始ICCID与结束ICCID前13位数相同！',
									duration: 3
								})
								return
							}
						} else {
							this.$Message.error({
								content: '请保持起始ICCID与结束ICCID位数相同！',
								duration: 3
							})
							return
						}
						this.$Modal.confirm({
							title: '确认导出？',
							onOk: () => {
								let current = this.page
								let size = 10
								let cardForm = this.cardShapeCondition
								let endTime = this.searchEndTime
								let startTime = this.searchBeginTime
								let iccid = this.iccidCondition
								let startICCID = this.formObj.startICCID
								let endICCID = this.formObj.endICCID
								let imsi = this.imsiCondition
								let msisdn = this.msisdnCondition
								let type = this.cardTypeCondition
								if (startTime) {
									startTime = this.searchBeginTime + " 00:00:00"
								}
								if (endTime) {
									endTime = this.searchEndTime + " 00:00:00"
								}
								let corpId = this.corpId
								let corpName = this.corpName
								let isStoreOut = this.isStoreOut

								let isFilterExpired = true
								this.exportloading = true
								let userId = this.$store.state.user.userId
								cardexport({
									current,
									size,
									cardForm,
									endTime,
									startTime,
									iccid,
									startICCID,
									endICCID,
									imsi,
									msisdn,
									type,
									isFilterExpired,
									corpId,
									corpName,
									userId,
								}).then(res => {
									if (res.code === "0000") {
										this.$Notice.success({
											title: "操作提示",
											desc: res.data,
										});
										this.exportloading = false
									}
								}).catch(err => {
									this.exportloading = false
									console.error(err);
								}
								)
							}
						})
					}
				})

			},
			getMore(item) {
				this.detailsLoading = true
				let imsi = item.imsi
				cardsearch(imsi).then(res => {
					if (res.code === '0000') {
						this.detailsLoading = false
						this.more = res.data
						this.modal5 = true
					}
				}).catch((err) => {
					console.log(err)
				})

			},
			update(item) {
				// 判断是否出库，如出库则提示不允许修改
				// if (item.isStoreOut === '2') {
				// 	this.$Notice.error({
				// 		title: '提示',
				// 		desc: '该卡已出库，不允许修改'
				// 	})
				// } else {
				// 查询接口
				let imsi = item.imsi
				//判断改卡是否支持gtp动态路由，支持路由ID能修改，不支持不能修改
				this.modifyRoute = item.supportGtpRoute
				Updatesearch(imsi).then(res => {
					if (res.code === '0000') {
						this.modal2 = true
						// 表单赋值
						this.choosed = Object.assign({}, item);
						this.choosed.templateId = ""
						this.smsAll.forEach((value) => {
							if (value.id === item.templateId) {
								this.choosed.templateId = item.templateId
							}
						})
						this.choosed.type = parseInt(this.choosed.type)
						this.choosed.status = parseInt(this.choosed.status)
            // 如果实名制规则接口返回数据为空,“是否需要实名制规则”默认为否
						if (this.realNameCitys.length < 1) {
							this.choosed.realName = 1
						}
						this.realNameCitys.forEach((value) => {
							if (value.groupId === res.data.realNameId) {
								this.choosed.realNameid = res.data.realNameId //实名制国家id
								this.choosed.realName = 0
								this.realNameflg = 0
							} else {
								if (res.data.realNameId) {
									this.choosed.realName = 0
									this.realNameflg = 0
								} else {
									this.choosed.realName = 1
									this.realNameflg = 1
								}
							}
						})
						this.provs.forEach((value) => {
							if (value.corpId === res.data.corpId) {
								this.choosed.corpId = res.data.corpId
							} else {
								this.choosed.corpid = ""
							}
						})
						if (res.data.packages != null) {
							this.provs.forEach((value) => {
								if (value.corpId === this.choosed.corpId) {
									this.cpackages = value.cpackages
								}
							})
							this.choosed.packageId = res.data.packages //套餐id
						}

					}
				}).catch((err) => {
					console.log(err)
				})
				// }

			},
			clearxlsFileCache() {
				if (this.cardShapes != '2') {
					this.formValidate.xlsxfile = null
					this.esimPathSdb = ''
				}
			},
			updateBatch() {
				this.modal3 = true
			},
			updateTime() {
				this.modal6 = true
				this.getExpireTime()
			},
			recordView() {
				this.modal7 = true
				this.goRecodePageFirst(1)
			},
			// 导入记录查看列表
			goRecodePageFirst: function(page) {
				this.loading = true
				var _this = this
				getRecordPage({
					size: 10,
					current: page,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.recordLoading = false
						this.currentRecordPage = page
						this.recordTotal = res.count
						this.taskData = res.data
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					_this.loading = false
					this.recordLoading = false
				})
			},
			goRecordPage(page) {
				this.goRecodePageFirst(page)
			},
			back() {
				this.modal7 = false
			},
			reback() {
				this.modal8 = false
			},
			recordInfo(data) {
				let taskId = data.taskId
				this.info = {
					ota: data.detail.ota,
					smsTemplateName: data.detail.smsTemplateName,
					type: data.detail.type,
					tplid: data.detail.tplid,
					cardForm: data.detail.cardForm,
					realname: data.detail.realnames ? data.detail.realnames.join(", ") : '',
					esimPathSdb: data.detail.esimPathSdb,
					supportGtpRoute: data.detail.supportGtpRoute == '1' ? "是" : data.detail.supportGtpRoute == '2' ? "否" : '',
					routeId: data.detail.routeId,
					openUpccSignId: data.detail.openUpccSignId,
					expireUpccSignId: data.detail.expireUpccSignId,
				}
				this.taskId = taskId
				this.modal8 = true
			},
			// 导入记录查看 文件下载
			exportfile(taskId, fileType) {
				this.taskloading = true
				var _this = this
				exportFile({
					taskId,
					fileType
				}).then(res => {
					const content = res.data
					let fileName = decodeURI(escape(res.headers['content-disposition'].match(/=(.*)$/)[1]))
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content, fileName)
					}
				}).finally(() => {
					this.taskloading = false
				})
			},
			getCbMore(id) {
				this.$router.push({
					path: '/corp/addcb',
					query: {
						cbId: id
					}
				})
			},
			getProviders() {

			},
			getStatus() {

			},
			ok() {
				this.formValidate1 = {}
			},
			cancelModal() {
				this.modal1 = false
				this.formValidate.ota = ''
				this.formValidate.sms = ''
				this.formValidate.cardType = ''
				this.formValidate.tplid = ''
				this.formValidate.cardShape = ''
				this.formValidate.provId = ''
				this.formValidate.packageId = []
				this.formValidate.realName = ''
				this.formValidate.sdbfile = ''
				this.formValidate.admfile = ''
				this.formValidate.xlsxfile = ''
				this.formValidate.routeId = ''
				this.$refs.formValidate.resetFields()
				this.cpackages = {}
			},
			// 取消批量修改
			BatchcancelModal() {
				this.modal3 = false
				this.formValidate1.iccidFile = ''
				this.$refs.formValidate1.resetFields()
				this.formValidate1.templateid = ''
				this.formValidate1.type = ''
				this.formValidate1.tplid = ''
				this.formValidate1.status = ''
				this.formValidate1.provId = ''
				this.formValidate1.realName = ''
				this.formValidate1.realNameCity = ''
				this.formValidate1.packageId = []
				this.cpackages = {}
			},
			// 取消修改时间
			updatecancelModal() {
				this.modal6 = false
				this.$refs.formValidate3.resetFields()
				this.single = false
			},
			// 取消单个修改
			modifycancelModal() {
				this.modal2 = false
				this.$refs.choosed.resetFields()
				// 清空select绑定值
				this.$refs.store.clearSingleSelect()
				this.updateLoading = false
				this.cpackages = {}
			},
			// 获取合作运营商
			getchannel() {
				getchannel().then(res => {
					if (res.code === '0000') {
						this.provs = res.data.filter(obj => obj.cpackages.length !== 0)
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			// 获取实名制国家
			getrealNameGroups() {
				getrealNameGroups().then(res => {
					if (res.code === '0000') {
						this.realNameCitys = res.data
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			//获取渠道集合
			getCorpList() {
				getCorpList({
					// "type": 1,
					"status": 1,
					"checkStatus": 2,
					"types": [1, 3, 4, 5]
				}).then(res => {
					if (res && res.code == '0000') {
						this.corpList = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			// 获取目标OTA
			getotalist() {
				getotalist().then(res => {
					if (res.code === '0000') {
						this.otas = res.data
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			// 获取短信模板
			gettemplate() {
				gettemplate().then(res => {
					if (res.code === '0000') {
						var list = res.data;
						this.smsAll = list;
						this.smsAll.sort(function(str1, str2) {
							return str1.templateName.localeCompare(str2.templateName);
						});
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			// 获取过期时间
			getExpireTime() {
				getExpireTime().then(res => {
					if (res.code === '0000') {
						// this.outTime = res.data;
						this.$refs.formValidate3.resetFields()
						this.formValidate3 = {
							outTime: res.data.toString()
						}
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			//切换支不支持路由ID
			changeRoute(data) {
				if (data == '2') {
					this.formValidate.routeId = ''
				}
			},
		},
		mounted() {
			this.goPageFirst(1)
			this.getrealNameGroups()
			this.getotalist()
			this.gettemplate()
			this.getchannel()
			this.getExpireTime()
			this.getCorpList()
		}
	}
</script>
<style>
	.search_head {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 20px;
		flex-wrap: wrap;
	}

	.search_head_box {
		margin-bottom: 20px;
		margin-right: 20px;
	}

	.search_head_label {
		font-weight: bold;
		display: flex;
		justify-content: flex-start;
		margin-right: 20px;
	}

	.button {
		font-weight: bolder;
		color: darkturquoise;
		border: none;
		outline: none;
	}

	#space {
		/* height: 30px;
		line-height: 30px; */
		font-size: 12px;
		white-space: pre-line;
		list-style: none;
	}
</style>
