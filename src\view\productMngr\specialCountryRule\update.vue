<template>
  <Card class="card-wrapper">
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80" class="form-wrapper">
      <div class="form-section">
        <!-- 基本信息 -->
        <div class="section-header">基本信息</div>
        <div class="section-content">
          <Row :gutter="24">
            <Col span="8">
              <FormItem label="规则名称" prop="groupName">
                <Input v-model="formValidate.groupName" placeholder="请输入规则名称" :maxlength="200" class="uniform-input" />
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="国家" prop="mcc">
                <Select
                  v-model="formValidate.mcc"
                  placeholder="请选择国家"
                  filterable clearable
                  class="uniform-select"
                  :disabled="isEdit"
                >
                  <Option
                    v-for="(item, index) in countryList"
                    :key="index"
                    :value="String(item.mcc)"
                    :label="item.countryEn"
                    style="width:300px"
                    class="select-option"
                  >
                    {{item.countryEn}}
                  </Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem label="是否生效" prop="status">
                <i-switch v-model="formValidate.status" />
              </FormItem>
            </Col>
          </Row>
        </div>

        <!-- 卡池信息 -->
        <div class="section-header">卡池信息</div>
        <div class="section-content">
          <div class="card-pool-table">
            <Table :columns="cardPoolColumns" border :data="formValidate.details">
              <template slot="supplier" slot-scope="{ row, index }">
                <div class="supplier-select-wrapper" v-if="row.isDefault">-</div>
                <div class="supplier-select-wrapper" v-else>
                  <FormItem :prop="'details.' + index + '.supplierId'" :rules="[
                    { required: true, message: '供应商不能为空', trigger: 'change' }
                  ]" :label-width="0">
                    <Select
                      v-model="row.supplierId"
                      placeholder="请选择供应商"
                      filterable
                      clearable
                      transfer
                      class="supplier-select-select"
                      @on-change="(value) => handleSupplierChange(value, index)"
                    >
                      <Option :value="item.supplierId" v-for="(item, pIndex) in supplierList" :key="pIndex">{{item.supplierName}}</Option>
                    </Select>
                  </FormItem>
                </div>
              </template>
              <template slot="cardPool" slot-scope="{ row, index }">
                <div v-if="row.isDefault" style="font-weight:bold;">默认</div>
                <div v-else class="pool-container">
                  <div class="pool-row">
                    <div class="pool-select">
                      <FormItem :prop="'details.' + index + '.cardPools'" :rules="cardPoolRules" :label-width="0">
                        <Select
                          v-model="row.selectedPoolIds"
                          placeholder="请选择卡池名称"
                          multiple
                          filterable
                          clearable
                          transfer
                          class="pool-select-select"
                          @on-change="(value) => handleCardPoolChange(value, index)"
                          @on-open-change="(status) => handleCardPoolSelectOpen(status, index)"
                        >
                          <Option
                            v-for="(item, pIndex) in row.availableCardPools"
                            :key="pIndex"
                            :value="item.poolId"
                            :label="item.poolName"
                            class="select-option"
                          >
                            {{item.poolName}}
                          </Option>
                        </Select>
                      </FormItem>
                    </div>
                    <div class="pool-percentages" v-if="row.selectedPoolIds && row.selectedPoolIds.length > 0">
                      <div v-for="(poolId, pIndex) in row.selectedPoolIds" :key="pIndex" class="percentage-item">
                        <InputNumber
                          v-model="row.selectedRates[pIndex]"
                          :min="0"
                          :max="100"
                          class="percentage-input"
                          size="small"
                          @on-change="(value) => handleRateChange(value, index, pIndex)"
                        />
                        <span class="percentage-symbol">%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <template slot="remarkCn" slot-scope="{ row, index }">
                <FormItem :prop="'details.' + index + '.remarkCn'" :rules="[
                  { required: true, message: row.isDefault ? '默认项中文备注不能为空' : '中文备注不能为空', trigger: 'blur' },
                  { validator: (rule, value, callback) => {
                    if (value && value.trim() === '') {
                      callback('中文备注不能为空');
                    } else {
                      callback();
                    }
                  }, trigger: 'blur' }
                ]" :label-width="0">
                  <Input
                    v-model="formValidate.details[index].remarkCn"
                    type="textarea"
                    :maxlength="1000"
                    :rows="2"
                    placeholder="请输入中文备注"
                  />
                </FormItem>
              </template>
              <template slot="remarkEn" slot-scope="{ row, index }">
                <FormItem :prop="'details.' + index + '.remarkEn'" :rules="[
                  { required: true, message: row.isDefault ? '默认项英文备注不能为空' : '英文备注不能为空', trigger: 'blur' },
                  { validator: (rule, value, callback) => {
                    if (value && value.trim() === '') {
                      callback('英文备注不能为空');
                    } else {
                      callback();
                    }
                  }, trigger: 'blur' }
                ]" :label-width="0">
                  <Input
                    v-model="formValidate.details[index].remarkEn"
                    type="textarea"
                    :maxlength="1000"
                    :rows="2"
                    placeholder="请输入英文备注"
                  />
                </FormItem>
              </template>
              <template slot="action" slot-scope="{ row, index }">
                <Button
                  v-if="!row.isDefault && formValidate.details.filter(item => !item.isDefault).length >= 2"
                  type="error"
                  size="small"
                  @click="removeCardPoolRow(index)"
                >
                  删除
                </Button>
              </template>
            </Table>
            <div class="add-row">
              <Button type="primary" @click="addCardPoolRow">
                 添加
              </Button>
            </div>
          </div>
        </div>

        <!-- 渠道商信息 -->
        <div class="section-header">渠道商信息</div>
        <div class="section-content channel-section">
          <Row>
            <Col span="24">
              <DualTableSelect
                ref="dualTableSelect"
                v-model="formValidate.corps"
                :source-columns="channelColumns"
                :source-data="channelList"
                :selected-columns="selectedChannelColumns"
                :selected-data="selectedChannelData"
                :total="totalChannels"
                :current="currentPage"
                :page-size="pageSize"
                :loading="loading"
                :check-all="computedCheckAll"
                :indeterminate="computedIndeterminate"
                search-placeholder="请输入渠道商名称搜索"
                @on-search="handleSearch"
                @on-check-all="handleCheckAll"
                @on-page-change="onPageChange"
                @on-select="onSelect"
                @on-select-cancel="onSelectCancel"
                @on-select-all="onSelectAll"
                @on-select-all-cancel="onSelectAllCancel"
                @on-remove="handleRemoveChannel"
              >
                <template slot="selected-header">
                  <div>
                    <div class="custom-selected-header" style="height: 32px;line-height: 32px;">
                      已选渠道商
                    </div>
                  </div>
                </template>
              </DualTableSelect>
            </Col>
          </Row>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <Button type="primary" @click="handleSubmit" :loading="isSubmitting">确认</Button>
          <Button style="margin-left: 8px" @click="handleCancel">返回</Button>
        </div>
      </div>
    </Form>
  </Card>
</template>

<script>
import {
  getChannelList,
  getCardPoolInfoByMcc,
  getCountryList,
  createSpecialCountryRule,
  updateSpecialCountryRule,
  getSpecialCountryRuleDetail,
  getSupplierList
} from '@/api/channel/specialCountryRule'
import DualTableSelect from '@/components/dual-table-select/DualTableSelect.vue'

/**
 * 特殊国家规则配置页面
 * 用于创建和编辑特定国家的卡池分配规则
 * 包含基本信息配置、卡池信息配置和渠道商信息配置三个主要模块
 */
export default {
  name: 'SpecialCountryRuleUpdate',
  components: {
    DualTableSelect // 双表格选择组件，用于渠道商选择
  },
  data() {
    return {
      // 是否为编辑模式
      isEdit: false,

      // 表单数据对象
      formValidate: {
        groupName: '',
        countryEn: '',
        mcc: '',
        status: 0,
        details: [
          this.getDefaultDetail(),
          this.getEmptyDetail()
        ],
        corps: []
      },

      // 表单验证规则
      ruleValidate: {
        groupName: [
          { required: true, message: '规则名称不能为空', trigger: 'blur' },
          { type: 'string', max: 200, message: '规则名称最多支持200字符', trigger: 'blur' },
          { validator: (rule, value, callback) => {
            if (value && value.trim() === '') {
              callback('规则名称不能为空');
            } else {
              callback();
            }
          }, trigger: 'blur' }
        ],
        mcc: [
          { required: true, message: '请选择国家', trigger: 'change' }
        ]
      },

      // 基础数据列表
      countryList: [], // 国家列表
      cardPoolList: [], // 卡池列表
      channelList: [], // 渠道商列表
      selectedChannelData: [], // 已选渠道商数据
      supplierList: [], // 供应商列表

      // 渠道商搜索和分页相关
      searchChannel: '', // 搜索关键词
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页条数
      totalChannels: 0, // 总条数

      // 选择状态控制
      checkAll: false, // 全选状态
      indeterminate: false, // 半选状态
      loading: false, // 加载状态

      // 表格列配置
      cardPoolColumns: [
        {
          title: '供应商',
          slot: 'supplier',
          width: 230,
          align: 'center',
        },
        {
          title: '卡池名称及分配比例',
          slot: 'cardPool',
          width: 430,
          align: 'center',
        },
        {
          title: '备注中文',
          slot: 'remarkCn',
          minWidth: 350
        },
        {
          title: '备注英文',
          slot: 'remarkEn',
          minWidth: 350
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 80
        }
      ],

      // 渠道商列表列配置
      channelColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '渠道商简称',
          key: 'corpName',
          align: 'center',
          width: 200,
          ellipsis: true,
          tooltip: true
        },
        {
          title: '渠道商公司名称',
          key: 'companyName',
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '国家卡池关联组',
          key: 'relationGroupNames',
          align: 'center',
          width: 280,
          ellipsis: true,
          tooltip: true,
          render: (h, params) => this.renderRelationGroupNames(h, params)
        }
      ],

      // 已选渠道商列表列配置
      selectedChannelColumns: [
        {
          title: '渠道商简称',
          key: 'corpName',
          align: 'center',
          width: 200,
          ellipsis: true,
          tooltip: true
        },
        {
          title: '渠道商公司名称',
          key: 'companyName',
          align: 'center',
          ellipsis: true,
          tooltip: true
        },
        {
          title: '国家卡池关联组',
          key: 'relationGroupNames',
          align: 'center',
          width: 280,
          ellipsis: true,
          tooltip: true,
          render: (h, params) => this.renderRelationGroupNames(h, params)
        },
        {
          title: '操作',
          slot: 'action',
          width: 80,
          align: 'center'
        }
      ],

      // 卡池选择验证规则
      cardPoolRules: {
        type: 'array',
        required: true,
        message: '请选择卡池',
        trigger: 'change'
      },

      // 防抖定时器
      debounceTimer: null,

      // 缓存已请求过的MCC对应的卡池信息
      cardPoolCache: {},

      // 防止重复请求的控制变量
      isLoadingCardPools: false,
      //防止重复请求提交
      isSubmitting: false,

      // 新增：保存当前搜索条件下所有渠道商ID
      currentSearchAllIds: []
    }
  },

  watch: {
    // 监听国家选择变化
    "formValidate.mcc": {
      handler(newVal, oldVal) {
        // 如果新值为空，只清空选中信息
        if (!newVal) {
          this.formValidate.details.forEach((row, index) => {
            this.$set(row, 'selectedPoolIds', []);
            this.$set(row, 'selectedRates', []);
            this.$set(row, 'cardPools', []);
          });
          return;
        }

        // 只有在国家实际发生变化时才执行清空逻辑
        if (oldVal && newVal !== oldVal) {
          // 遍历所有行，清空卡池相关信息，保留供应商选择
          this.formValidate.details.forEach((row, index) => {
            // 清空已选择的卡池和比例
            this.$set(row, 'selectedPoolIds', []);
            this.$set(row, 'selectedRates', []);
            this.$set(row, 'cardPools', []); // 清空当前行的cardPools数据
            // 清空当前行的可用卡池列表
            this.$set(row, 'availableCardPools', []);

          });
        } else if (!oldVal && newVal && this.isEdit) {

        }
        // 如果是新增模式，且MCC从无值变为有值，初始化details
         if (!oldVal && newVal && !this.isEdit && (!this.formValidate.details || this.formValidate.details.length === 0)) {
            this.formValidate.details = [
              this.getDefaultDetail(),
              this.getEmptyDetail()
            ];
         }
      },
    },

    // 监听countryList变化
    countryList: {
      handler(newVal) {
        if (this.isEdit && this.formValidate.mcc && newVal.length > 0) {
          const mccValue = this.convertMccToString(this.formValidate.mcc);
          const country = newVal.find(item => String(item.mcc) === mccValue);

          if (country) {
            // 确保类型一致性
             this.$set(this.formValidate, 'mcc', String(country.mcc));
            this.$forceUpdate();
          }
        }
      },
      immediate: true
    }
  },

  created() {
    this.isEdit = this.$route.name === 'specialCountryRuleEdit'
    this.init()
  },

  methods: {
    /**
     * 页面初始化
     * 加载国家列表、详情数据（编辑模式）和渠道商列表
     */
    async init() {
      // 获取供应商列表
      await this.getSupplierList()
      // 先获取国家列表，在编辑和新增都需要
      await this.getCountryList()

      // 根据编辑状态决定后续动作
      if (this.isEdit) {
        const ruleId = this.$route.query.ruleId || this.$route.params.ruleId;
        if (ruleId) {
          // 编辑模式：获取详情数据
          await this.getDetail(ruleId)
          // 卡池列表会在详情加载后根据国家获取
        } else {
          this.$Message.error('缺少规则ID')
          this.$router.push('/specialCountryRule')
        }
      }

      // 获取渠道商列表（编辑模式时，已选渠道商会根据详情中的corps单独获取）
      if (!this.isEdit) {
        await this.getChannels()
      }
    },

    /**
     * 获取默认的卡池详情对象（不可删除，卡池名称显示"默认"）
     * @returns {Object} 默认卡池详情对象
     */
    getDefaultDetail() {
      return {
        detailId: '',
        cardPools: [{ poolId: 'default', rate: 100, poolName: '默认', supplierId: null }], // 默认卡池信息
        remarkCn: '',
        remarkEn: '',
        selectedPoolIds: ['default'],
        selectedRates: [100],
        isDefault: true,
        supplierId: null, // 默认行supplierId为null
        availableCardPools: [{ poolId: 'default', rate: 100, poolName: '默认', supplierId: null }] // 默认行可用卡池为默认
      }
    },

    /**
     * 获取空的卡池详情对象（普通项）
     * @returns {Object} 卡池详情对象
     */
    getEmptyDetail() {
      return {
        detailId: '',
        cardPools: [],
        remarkCn: '',
        remarkEn: '',
        selectedPoolIds: [],
        selectedRates: [],
        isDefault: false,
        supplierId: '', // 新增行supplierId初始为空字符串
        availableCardPools: [] // 新增行可用卡池初始为空
      }
    },

    /**
     * 渲染关联组名称
     * 处理长文本显示和tooltip
     */
    renderRelationGroupNames(h, params) {
      // 确保relationGroupNames存在且是对象
      const relationGroupNames = params.row.relationGroupNames ?
        (typeof params.row.relationGroupNames === 'object' ?
          Object.values(params.row.relationGroupNames) :
          [params.row.relationGroupNames]) :
        [];

      const content = relationGroupNames.filter(Boolean).join('、');

      //需要判断当前内容是否存在，为空的话择直接返回
      if(content && content.length > 0){
        return h('Tooltip', {
          props: {
            content,
            maxWidth: 300,
            transfer: true
          }
        }, [
          h('span', {
            style: {
              display: 'inline-block',
              width: '240px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }
          }, content)
        ])
      } else {
        return h('div', '-')
      }
    },

    /**
     * API调用方法
     */
    async getCountryList() {
      try {
        const res = await getCountryList()
        if (res.code === '0000') {
          this.countryList = res.data
        }
      } catch (error) {
        this.$Message.error('获取国家列表失败')
      }
    },

    async getDetail(ruleId) {
      try {
        const res = await getSpecialCountryRuleDetail({"groupId":ruleId, current:-1, size:-1})
        if (res.code === '0000' && res.data && res.data.length > 0) {
          const detailData = res.data[0];

          // 确保countryList已加载
          if (this.countryList.length === 0) {
            await this.getCountryList();
          }

          // 先处理详情数据
          await this.handleDetailData(detailData)
        } else {
          this.$Message.error('获取详情失败：数据为空')
        }
      } catch (error) {
        this.$Message.error('获取详情失败：' + (error.message || '未知错误'))
      }
    },

    async getCardPools(supplierId, index, options = {}) {
      if (!this.formValidate.mcc) return

      const cacheKey = `${this.formValidate.mcc}_${supplierId || ''}`;
      const assignToAllRows = (data) => {
        this.formValidate.details.forEach((row, idx) => {
          if (row.supplierId === supplierId && !row.isDefault) {
            this.$set(this.formValidate.details[idx], 'availableCardPools', data);
            // 反显逻辑
            this.$nextTick(() => {
              if (
                row.selectedPoolIds &&
                row.selectedPoolIds.length > 0 &&
                row.availableCardPools &&
                row.availableCardPools.length > 0
              ) {
                const validIds = row.selectedPoolIds.filter(pid =>
                  row.availableCardPools.some(opt => opt.poolId === pid)
                );
                this.$set(row, 'selectedPoolIds', validIds);
              }
            });
          }
        });
      };

      if (this.cardPoolCache[cacheKey]) {
        assignToAllRows(this.cardPoolCache[cacheKey]);
        return;
      }
      if (this.isLoadingCardPools === cacheKey) return
      this.isLoadingCardPools = cacheKey

      let data = {
        mcc: this.formValidate.mcc,
        supplierId: supplierId || ''
      }

      try {
        const res = await getCardPoolInfoByMcc(data);
        if (res.code === '0000') {
          this.cardPoolCache[cacheKey] = res.data;
          assignToAllRows(res.data);
          // 初始化时 selectedPoolIds 为空，自动同步
          this.formValidate.details.forEach((row, idx) => {
            if (
              row.supplierId === supplierId &&
              !row.isDefault &&
              (!row.selectedPoolIds || row.selectedPoolIds.length === 0) &&
              row.cardPools &&
              row.cardPools.length > 0
            ) {
              const selectedPoolIds = row.cardPools.map(pool => pool.poolId);
              this.$set(row, 'selectedPoolIds', selectedPoolIds);
              const selectedRates = row.cardPools.map(pool => pool.rate);
              this.$set(row, 'selectedRates', selectedRates);
            }
          });
        } else if (!options.silent) {
          this.$Message.error('获取卡池列表失败');
        } else {
          console.warn('获取卡池列表失败', res);
        }
      } catch (error) {
        if (!options.silent) {
          this.$Message.error('获取卡池列表失败');
        } else {
          console.warn('获取卡池列表失败', error);
        }
      } finally {
        if (this.isLoadingCardPools === cacheKey) {
          this.isLoadingCardPools = false;
        }
      }
    },

    async getChannels() {
      this.loading = true
      try {
        const res = await getChannelList({
          corpName: this.searchChannel,
          current: this.currentPage,
          size: this.pageSize,
          cooperationMode: "2"
        })

        if (res.code === '0000') {
          this.handleChannelData(res)
          // 新增：每次分页/搜索后，获取所有匹配ID
          await this.updateCurrentSearchAllIds();
        }
      } catch (error) {
        this.$Message.error('获取渠道商列表失败')
      } finally {
        this.loading = false
      }
    },

    /**
     * 数据处理方法
     */
    async handleDetailData(data) {
      const mccValue = this.convertMccToString(data.mcc);
      // 处理表单数据
      this.formValidate = {
        groupId: data.groupId,
        groupName: data.groupName,
        countryEn: data.countryEn,
        mcc: mccValue,
        status: data.status =='1' ? true : false,
        corps: data.corps || [],
        details: {}
      }
      // 确保在下一个tick更新
      this.$nextTick(() => {
        if (this.formValidate.mcc) {
          this.$set(this.formValidate, 'mcc', mccValue);
        }
      });
      this.formValidate.details = await this.processDetails(data.details)
      this.getSelectedChannelData(data.corps)
    },

    /**
     * 处理卡池详情数据
     * @param {Array} details 后端返回的详情数据
     * @returns {Array} 处理后的详情数据
     */
    async processDetails(details) {
      let defaultRow = null;
      let otherRows = [];
      if (details && details.length > 0) {
        details.forEach(detail => {
          const isDefault = detail.cardPools?.some(pool => pool.poolId === 'default');
          const selectedPoolIds = [];
          const selectedRates = [];
          detail.cardPools?.forEach(pool => {
            if (pool.poolId !== 'default') {
              selectedPoolIds.push(pool.poolId);
              selectedRates.push(pool.rate);
            }
          });
          const rowData = {
            detailId: detail.detailId || '',
            cardPools: detail.cardPools || [],
            remarkCn: detail.remarkCn || '',
            remarkEn: detail.remarkEn || '',
            selectedPoolIds,
            selectedRates,
            supplierId: isDefault ? null : (detail.cardPools?.[0]?.supplierId || ''),
            isDefault,
            availableCardPools: []
          };
          if (isDefault) {
            defaultRow = rowData;
          } else {
            otherRows.push(rowData);
          }
        });
      }
      if (!defaultRow) {
        defaultRow = this.getDefaultDetail();
      }
      this.$set(this.formValidate, 'details', [defaultRow, ...otherRows]);

      // 用 Promise.all 等待所有 getCardPools 完成，确保 availableCardPools 数据加载完毕再渲染
      const promises = this.formValidate.details.map((row, index) => {
        if (!row.isDefault && row.supplierId) {
          return this.getCardPools(row.supplierId, index, { silent: true });
        }
        return Promise.resolve(); // 返回一个已解决的 Promise，以便 Promise.all 正常工作
      });
      await Promise.all(promises);

      return [defaultRow, ...otherRows];
    },

    /**
     * 处理渠道商列表数据
     * 设置选中状态并更新UI
     */
    handleChannelData(res) {
      this.channelList = res.data.map(item => ({
        ...item,
        _checked: this.formValidate.corps.includes(item.corpId)
      }))
      this.totalChannels = JSON.parse(res.count)
      // 新增：保存当前搜索条件下所有渠道商ID
      this.currentSearchAllIds = (res.data || []).map(item => item.corpId);
      this.updateCheckAllStatus()
    },

    /**
     * 卡池相关方法
     */
    handleCardPoolChange(value, index) {
      const row = this.formValidate.details[index]
      row.selectedPoolIds = value

      // 处理分配比例
      const newRates = new Array(value.length).fill(0)
      value.forEach((poolId, idx) => {
        const oldIdx = row.selectedPoolIds.indexOf(poolId)
        if (oldIdx !== -1 && oldIdx < row.selectedRates.length) {
          newRates[idx] = row.selectedRates[oldIdx]
        }
      })

      // 使用$set更新selectedRates确保响应式
      this.$set(row, 'selectedRates', newRates)
      this.updateCardPoolsStructure(index)
    },

    /**
     * 更新卡池数据结构
     * @param {Number} index 行索引
     */
    updateCardPoolsStructure(index) {
      const row = this.formValidate.details[index]
      const cardPools = row.selectedPoolIds.map((poolId, idx) => ({
        poolId,
        rate: row.selectedRates[idx] || 0
      }))
      // 使用$set更新cardPools确保响应式
      this.$set(row, 'cardPools', cardPools)
    },

    /**
     * 渠道商选择相关方法
     */
    handleSearch(keyword) {
      this.searchChannel = keyword
      this.currentPage = 1
      this.getChannels()
    },

    /**
     * 处理全选/取消全选操作（由DualTableSelect组件的@on-check-all触发）
     * @param {Boolean} checked 全选复选框的状态
     */
    handleCheckAll(checked) {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
      }

      this.debounceTimer = setTimeout(async () => {
        this.loading = true
        try {
          // 获取所有匹配当前搜索条件的渠道商
          const res = await getChannelList({
            corpName: this.searchChannel,
            current: -1, // 获取所有页
            size: -1,    // 获取所有数据
            cooperationMode: "2"
          })

          if (res.code !== '0000') {
            throw new Error(res.msg || '获取渠道商列表失败');
          }

          const allMatchingChannels = res.data || [];
          const allMatchingChannelIds = allMatchingChannels.map(item => item.corpId);

          if (checked) {
            // === 处理"全选"逻辑：追加 ===
            let currentSelectedCorps = [...this.formValidate.corps];
            let currentSelectedData = [...this.selectedChannelData];
            // 使用Map提高查找效率
            const currentSelectedDataMap = new Map(currentSelectedData.map(item => [item.corpId, item]));

            allMatchingChannels.forEach(item => {
              // 如果当前项未被选中，则添加
              if (!currentSelectedCorps.includes(item.corpId)) {
                currentSelectedCorps.push(item.corpId);
                // 确保数据列表也不重复添加
                if (!currentSelectedDataMap.has(item.corpId)) {
                  currentSelectedData.push({
                    corpId: item.corpId,
                    corpName: item.corpName,
                    companyName: item.companyName,
                    relationGroupNames: item.relationGroupNames || {}
                  });
                  currentSelectedDataMap.set(item.corpId, item); // 加入Map
                }
              }
            });

            // 更新vue data
            this.formValidate.corps = currentSelectedCorps;
            this.selectedChannelData = currentSelectedData;

            // 更新当前页UI为全选状态
            this.updateSelectionStatus(true);

          } else {
            // === 处理"取消全选"逻辑：仅移除匹配项 ===
            // 从选中ID列表中移除所有匹配当前搜索条件的ID
            this.formValidate.corps = this.formValidate.corps.filter(corpId =>
              !allMatchingChannelIds.includes(corpId)
            );

            // 从选中数据列表中移除所有匹配当前搜索条件的数据项
            this.selectedChannelData = this.selectedChannelData.filter(item =>
              !allMatchingChannelIds.includes(item.corpId)
            );

            // 更新当前页UI为全不选状态
            this.updateSelectionStatus(false);
          }

        } catch (error) {
          this.$Message.error('处理全选/取消全选操作失败: ' + error.message)
        } finally {
          this.loading = false
          // 重新计算并更新顶层全选/半选状态
          this.updateCheckAllStatus();
        }
      }, 300) // 300ms防抖
    },

    /**
     * 更新选中状态UI
     * @param {Boolean} checked 是否选中
     */
    updateSelectionStatus(checked) {
      this.checkAll = checked
      this.indeterminate = false

      if (this.channelList?.length > 0) {
        this.channelList.forEach(item => {
          item._checked = checked
        })

        this.$nextTick(() => {
          if (this.$refs.dualTableSelect?.$refs.sourceTable) {
            this.$refs.dualTableSelect.$refs.sourceTable.selectAll(checked)
          }
          this.$forceUpdate()
        })
      }
    },

    /**
     * 表单操作方法
     */
    async handleSubmit() {
      this.$refs.formValidate.validate(async valid => {
        if (!valid || !this.validateRates()) return
        if (this.isSubmitting) return
        this.isSubmitting = true
        try {
          const submitData = this.prepareSubmitData()
          const res = await this.submitData(submitData)

          if (res?.code === '0000') {
            this.$Message.success('操作成功')
            this.$router.push('/specialCountryRule')
          }
          } catch (error) {
            this.$Message.error('操作失败')
        } finally {
          this.isSubmitting = false
        }
      })
    },

    /**
     * 验证卡池分配比例
     * @returns {Boolean} 验证结果
     */
    validateRates() {
      let isValid = true
      this.formValidate.details.forEach((row, index) => {
        this.updateCardPoolsStructure(index)

        if (row.selectedPoolIds?.length > 0) {
          // 检查是否有为0或负数的分配比例
          const hasZeroOrNegativeRate = row.selectedRates.some(rate => Number(rate) <= 0)
          if (hasZeroOrNegativeRate) {
            isValid = false
            this.$Message.error(`第${index + 1}行的卡池分配比例不能为负数或0`)
            return
          }

          const sum = row.selectedRates.reduce((acc, rate) => acc + (Number(rate) || 0), 0)
          if (Math.abs(sum - 100) > 0.01) {
            isValid = false
            this.$Message.error(`第${index + 1}行的卡池分配比例之和必须为100%，当前为${sum}%`)
          }
        }
      })
      return isValid
    },

    /**
     * 准备提交数据
     * @returns {Object} 提交的数据对象
     */
    prepareSubmitData() {
      // 确保所有卡池结构更新
      this.formValidate.details.forEach((row, index) => {
        this.updateCardPoolsStructure(index);
      });

      return {
        ...(this.isEdit ? { groupId: this.formValidate.groupId } : {}),
        groupName: this.formValidate.groupName ? this.formValidate.groupName.trim() : '',
        countryEn: this.countryList.find(item => item.mcc === this.formValidate.mcc)?.countryEn || '',
        mcc: this.formValidate.mcc,
        status: this.formValidate.status ? 1 : 0, // 转换为数字
        corps: this.formValidate.corps,
        details: this.formValidate.details.map(row => {
          if (row.isDefault) {
            return {
              detailId: row.detailId || '',
              cardPools: [{ poolId: 'default', rate: 100 }],
              remarkCn: row.remarkCn ? row.remarkCn.trim() : '',
              remarkEn: row.remarkEn ? row.remarkEn.trim() : '',
              supplierId: row.supplierId || ''
            }
          } else {
            return {
              detailId: row.detailId || '',
              cardPools: row.cardPools || [],
              remarkCn: row.remarkCn ? row.remarkCn.trim() : '',
              remarkEn: row.remarkEn ? row.remarkEn.trim() : '',
              supplierId: row.supplierId || ''
            }
          }
        })
      }
    },

    /**
     * 提交数据到服务器
     * @param {Object} data 提交的数据
     * @returns {Promise} 请求结果
     */
    async submitData(data) {
      return this.isEdit ?
        await updateSpecialCountryRule(data) :
        await createSpecialCountryRule(data)
    },

    handleReset() {
      this.$refs.formValidate.resetFields()
      this.formValidate.corps = []
      this.selectedChannelData = []
      this.checkAll = false
      this.indeterminate = false
      this.getChannels()
    },

    handleCancel() {
      this.$router.push('/specialCountryRule')
    },

    handleRateChange(value, index, pIndex) {
      // 确保使用Vue的$set方法更新数组，以保证响应式
      const row = this.formValidate.details[index];
      // 使用$set更新selectedRates数组中的值
      this.$set(row.selectedRates, pIndex, value);
      // 更新卡池结构
      this.updateCardPoolsStructure(index);
    },

    removeCardPoolRow(index) {
      if (this.formValidate.details.length > 1 && !this.formValidate.details[index].isDefault) {
        this.formValidate.details.forEach((row, idx) => {
          if (idx !== index) {
            this.updateCardPoolsStructure(idx)
          }
        })
        this.formValidate.details.splice(index, 1)
      }
    },

    addCardPoolRow() {
      this.formValidate.details.forEach((row, index) => {
        this.updateCardPoolsStructure(index)
      })
      const newRow = this.getEmptyDetail()
      this.formValidate.details.push(newRow)
    },

    onPageChange(page) {
      this.currentPage = page
      this.getChannels()
    },

    onSelect(selection, row) {
      this.updateSelectedForCurrentPage(selection)
    },

    onSelectCancel(selection, row) {
      this.updateSelectedForCurrentPage(selection)
    },

    onSelectAll(selection) {
      this.updateSelectedForCurrentPage(selection)
    },

    onSelectAllCancel(selection) {
      this.updateSelectedForCurrentPage(selection)
    },

    updateSelectedForCurrentPage(selection) {
      // 获取当前页所有项的ID
      const currentPageIds = this.channelList.map(item => item.corpId);

      // 从已选中数据中过滤掉当前页的数据
      let filteredCorps = this.formValidate.corps.filter(corpId =>
        !currentPageIds.includes(corpId)
      );

      // 从当前选择中获取的ID
      const currentSelectedIds = selection.map(item => item.corpId);

      // 合并当前页选择和其他页选择
      this.formValidate.corps = [...filteredCorps, ...currentSelectedIds];

      // 更新右侧显示的数据
      // 首先过滤掉当前页的数据
      let filteredData = this.selectedChannelData.filter(item =>
        !currentPageIds.includes(item.corpId)
      );

      // 合并当前页选择和其他页选择的数据
      this.selectedChannelData = [...filteredData, ...selection];

      // 检查是否全部选中
      this.isAllSelected = this.formValidate.corps.length === this.totalChannels;

      // 更新全选状态
      this.updateCheckAllStatus();
    },
    updateCheckAllStatus() {
      if (this.isAllSelected) {
        this.checkAll = true
        this.indeterminate = false
        return
      }

      // 获取当前页面选中的数量
      const currentPageSelected = this.channelList.filter(
        item => this.formValidate.corps.includes(item.corpId)
      ).length

      // 获取已选总数
      const totalSelected = this.formValidate.corps.length

      // 更新全选和部分选中状态
      if (this.totalChannels === 0) {
        // 没有数据时
        this.checkAll = false
        this.indeterminate = false
      } else if (totalSelected === 0) {
        // 完全未选中
        this.checkAll = false
        this.indeterminate = false
      } else if (totalSelected === this.totalChannels) {
        // 全部选中
        this.checkAll = true
        this.indeterminate = false
        this.isAllSelected = true
      } else {
        // 部分选中
        this.checkAll = false
        this.indeterminate = true
      }
    },
    handleRemoveChannel(row) {
      const index = this.formValidate.corps.indexOf(row.corpId)
      if (index > -1) {
        // 从选中值中移除
        this.formValidate.corps.splice(index, 1)

        // 从右侧表格数据中移除
        const dataIndex = this.selectedChannelData.findIndex(item => item.corpId === row.corpId)
        if (dataIndex > -1) {
          this.selectedChannelData.splice(dataIndex, 1)
        }

        // 更新左侧表格数据选中状态
        for (let i = 0; i < this.channelList.length; i++) {
          if (this.channelList[i].corpId === row.corpId) {
            // 对于被移除的项，设置为未选中
            this.$set(this.channelList[i], '_checked', false)
          } else {
            // 确保其他项的选中状态与formValidate.corps保持一致
            this.$set(this.channelList[i], '_checked', this.formValidate.corps.includes(this.channelList[i].corpId))
          }
        }
      }
      // 检查是否全部选中
      this.isAllSelected = this.formValidate.corps.length === this.totalChannels;
      // 更新全选状态
      this.updateCheckAllStatus()
    },

    getPoolLabel(poolId) {
      const pool = this.cardPoolList.find(item => item.poolId === poolId)
      return pool ? pool.poolName : ''
    },

    getSelectedChannelData(corpIds) {
      console.log('corpIds',corpIds)
      if (!corpIds || corpIds.length === 0) {
        // 如果没有已选渠道商，直接获取渠道商列表
        console.log('没有已选渠道商，直接获取渠道商列表')
        this.getChannels();
        return;
      }

      this.loading = true
      getChannelList({
        cooperationMode: "2",
        corpIds: corpIds,
        current: -1,
        size: -1
      }).then(res => {
        if (res.code === '0000' && res.data) {
          this.selectedChannelData = res.data.map(item => ({
            corpId: item.corpId,
            corpName: item.corpName,
            companyName: item.companyName,
            relationGroupNames: item.relationGroupNames || {}
          }))

          // 编辑模式下也需要获取全部渠道商列表用于左侧表格
          if (this.isEdit) {
            this.getChannels()
          }

          this.updateCheckAllStatus()
        } else {
          this.$Message.error(res.msg || '获取已选渠道商失败')
        }
      }).catch(err => {
        this.$Message.error('获取已选渠道商失败')
        console.error('获取已选渠道商失败:', err)
      }).finally(() => {
        this.loading = false
      })
    },

    /**
     * 确保MCC值为字符串类型
     * 因为Select组件需要字符串类型进行比较
     */
    convertMccToString(mcc) {
      if (mcc === null || mcc === undefined) return '';
      return String(mcc);
    },

    // 新增：获取当前搜索条件下所有渠道商ID
    async updateCurrentSearchAllIds() {
      const res = await getChannelList({
        corpName: this.searchChannel,
        current: -1,
        size: -1,
        cooperationMode: "2"
      });
      if (res.code === '0000') {
        this.currentSearchAllIds = (res.data || []).map(item => item.corpId);
      } else {
        this.currentSearchAllIds = [];
      }
    },

    // 获取供应商列表
    async getSupplierList() {


      try {
        const res = await getSupplierList()
        if (res.code === '0000') {
          this.supplierList = res.data || []
        }
      } catch (error) {
        this.$Message.error('获取供应商列表失败')
      }
    },

    // 处理供应商变更
    handleSupplierChange(value, index) {
      const row = this.formValidate.details[index]
      this.$set(row, 'supplierId', value)
      // 清空已选择的卡池和比例
      this.$set(row, 'selectedPoolIds', [])
      this.$set(row, 'selectedRates', [])
      this.$set(row, 'cardPools', []) // 清空当前行的cardPools数据
      // 清空当前行的可用卡池列表
      this.$set(row, 'availableCardPools', [])

      // 不再这里立即获取卡池，延迟到点击卡池选择时
      // if (value) {
      //     this.getCardPools(value, index)
      // }
    },

    // 新增处理卡池选择框打开事件的方法
    handleCardPoolSelectOpen(status, index) {
      if (status) {
        const row = this.formValidate.details[index];
        if (row.supplierId) {
          this.getCardPools(row.supplierId, index); // 用户操作时有提示
        }
      }
    },
  },
  computed: {
    computedCheckAll() {
      if (!this.currentSearchAllIds || this.currentSearchAllIds.length === 0) return false;
      const selectedSet = new Set(this.formValidate.corps);
      return this.currentSearchAllIds.every(id => selectedSet.has(id));
    },
    computedIndeterminate() {
      if (!this.currentSearchAllIds || this.currentSearchAllIds.length === 0) return false;
      const selectedSet = new Set(this.formValidate.corps);
      const checkedCount = this.currentSearchAllIds.filter(id => selectedSet.has(id)).length;
      return checkedCount > 0 && checkedCount < this.currentSearchAllIds.length;
    }
  },
}
</script>
<style>

/* ivu-tag */
.pool-select-select .ivu-tag {
  display: block;
  margin: 3px 4px 4px 0;
  }

</style>
<style lang="less" scoped>
  .ivu-select-dropdown .ivu-select-item{
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: none !important;
    word-break: break-all;
    width: 300px !important;
  }
  .ivu-select-multiple .ivu-select-item-selected:after{
    right: 0px !important;
  }
  .ivu-select-dropdown .ivu-select-item:hover{
    white-space: normal !important;
    overflow: visible !important;
    text-overflow: clip !important;
    max-width: none !important;
    word-break: break-all;
  }
  .pool-select-select{
    width: 300px;
  }
.pool-select-select .ivu-select-dropdown .ivu-select-item {
  max-width: 300px !important;
  /* 强制英文单词换行 */
}

.pool-select-select .ivu-select-dropdown .ivu-select-item:hover {
  /* 确保英文单词换行 */
  max-width: 300px !important;
}

.pool-select-select .ivu-select-dropdown {
  min-width: 200px !important;
  /* 设置下拉菜单的最小宽度 */
}
.supplier-select-select{
  width: 200px
}

.card-wrapper {
  margin: 24px;
  background: #fff;
}

.form-wrapper {
  padding: 24px;
}

.form-section {
  // max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ccc;
  color: #00aaff;
}

.section-content {
  margin-bottom: 32px;
  padding: 0 16px;
}


.ivu-tag {
  margin: 4px;
}

.form-actions {
  margin-top: 32px;
  text-align: center;
  border-top: 1px solid #e8eaec;
  padding-top: 24px;
}

.card-pool-table .ivu-form-item {
  margin-bottom: 8px;
  vertical-align: middle;
  padding:12px 0;
}

.card-pool-table {
  width: 100%;
  position: relative;

  .pool-container {
    width: 100%;
    padding: 4px 0;
    position: relative;

    .pool-row {
      display: flex;
      width: 100%;
      gap: 16px;

      .pool-select {
        width: 75%;
        position: relative;
      }

      .pool-percentages {
        width: 25%;
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .percentage-item {
          display: flex;
          align-items: center;
          margin:3px 4px 3px 0;
          width: 100%;

          .percentage-input {
            width: 100%
          }

          .percentage-symbol {
            color: #515a6e;
            margin-right: 8px;
          }
        }
      }
    }
  }

  .add-row {
    margin-top: 16px;
    text-align: right;
    padding-right: 16px;
  }
}

// 移除其他不需要的样式
.channel-select,
.channel-selected {
  .select-header {
    display: none;
  }

  .select-content {
    padding: 0;
  }
}
.ivu-select-selection{
  div{
    display: flex;
    flex-direction: column;
  }
}

</style>
