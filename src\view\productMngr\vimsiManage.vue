<template>
	<div>
		<Card>
			<div class="search_head_i">
				<div class="search_box">
					<span class="search_box_label">VIMSI</span>
					<Input placeholder="请输入VIMSI" v-model="searchCondition.vimsi" clearable style="width: 200px" />
				</div>
				<div class="search_box">
					<span class="search_box_label">MSISDN</span>
					<Input placeholder="请输入MSISDN" v-model="searchCondition.msisdn" clearable style="width: 200px" />
				</div>
				<div class="search_box">
					<span class="search_box_label">制卡IMSI</span>
					<Input placeholder="请输入制卡IMSI" v-model="searchCondition.madeImsi" clearable style="width: 200px" />
				</div>
				<div class="search_box">
					<span class="search_box_label">VIMSI状态</span>
					<Select v-model="searchCondition.status" clearable placeholder="请选择VIMSI状态" style="width:200px">
						<Option :value="item.value" v-for="(item,index) in statuseList" :key="index">{{item.label}}
						</Option>
					</Select>
				</div>
				<div style="width: 430px; padding: 0 5px; display: flex; justify-content: flex-start; align-items: center; margin-bottom: 20px;">
					<span class="search_box_label2">是否需要GTP PROXY指定号码</span>
					<Select v-model="searchCondition.reqGtpProxy" placeholder="是否需要GTP PROXY指定号码"
						clearable style="width:200px">
						<Option :value="1">是</Option>
						<Option :value="2">否</Option>
					</Select>
				</div>
			</div>
			<div class="search_head_i">
				<div class="search_box">
					<Button style="margin: 0 15px" type="primary" :loading="loading" v-preventReClick @click="search()">
						<Icon type="ios-search" />&nbsp;搜索
					</Button>
					<Button style="margin: 0 15px" type="info" @click="vimsiImport" v-has="'add'">
						<div style="display: flex;align-items: center;">
							<Icon type="md-add" />&nbsp;VIMSI导入
						</div>
					</Button>
					<Button style="margin: 0 15px" type="error" @click="removeBatch" v-has="'batchDelete'">
						<div style="display: flex;align-items: center;">
							<Icon type="ios-trash" />&nbsp;批量删除
						</div>
					</Button>
					<Button style="margin: 0 15px" type="success" @click="recordView()" v-has="'importRecord'">
						<div style="display: flex;align-items: center;">
							<Icon type="md-add" />&nbsp;导入记录查看
						</div>
					</Button>
				</div>
			</div>
			<div style="margin-top:20px">
				<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row, index }" slot="action">
						<Button type="primary" v-has="'stop'" size="small" style="margin-right: 10px"
							v-if="row.status=='1'" @click="stop(row.id)">暂停</Button>
						<Button type="primary" v-has="'stop'" size="small" style="margin-right: 10px" v-else
							disabled>暂停</Button>
						<Button type="success" v-has="'active'" size="small" style="margin-right: 10px"
							v-if="row.status=='4'||row.status=='5'" @click="active(row.id)">激活</Button>
						<Button type="success" v-has="'active'" size="small" style="margin-right: 10px" v-else
							disabled>激活</Button>
						<Button type="error" v-has="'delete'" size="small" style="margin-right: 10px"
							@click="remove(row.id,row.status)">删除</Button>
					</template>
				</Table>
			</div>
			<div class="table-botton" style="margin-top:15px">
				<Page :total="total" :current.sync="currentPage" :page-size="pageSize" show-total show-elevator
					@on-change="goPage" style="margin: 10px 0;" />
			</div>
		</Card>

		<!-- VIMSI导入模态框 -->
		<Modal v-model="importFlag" title="VIMSI导入" :footer-hide="true" :mask-closable="false" width="530px">
			<div class="modal_content">
				<Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="130"
					:label-height="100" inline style="font-weight:bold;">
					<FormItem label="VIMSI数量" prop="vimsiNum" style="width:420px">
						<Input placeholder="请输入VIMSI数量" :maxlength="11" v-model="formValidate.vimsiNum" clearable />
					</FormItem>
					<FormItem label="供应商" prop="supplierId" style="width:420px">
						<Select v-model="formValidate.supplierId" @on-change="queryCardPoolList" :filterable="true"
							clearable placeholder="请选择供应商" :disabled="cardPoodIntoFlag">
							<Option :value="item.supplierId" v-for="(item,index) in providers" :key="index">{{item.supplierName}}</Option>
						</Select>
					</FormItem>
					<FormItem label="是否需要GTP PROXY指定号码" prop="reqGtpProxy" style="width:420px">
						<Select v-model="formValidate.reqGtpProxy" placeholder="是否需要GTP PROXY指定号码"
							clearable>
							<Option :value="1">是</Option>
							<Option :value="2">否</Option>
						</Select>
					</FormItem>
					<FormItem label="卡池" prop="cardPool" style="width:420px">
						<Select v-model="formValidate.cardPool" @on-change="cardPoolSelect" :filterable="true" clearable
							placeholder="请选择卡池"
							:disabled="formValidate.supplierId=='' || formValidate.supplierId==undefined || cardPoodIntoFlag">
							<Option
								style="width: 270px; overflow: hidden; text-overflow:ellipsis; white-space: nowrap;text-align: left;"
								:value="item.poolId" v-for="(item,index) in cardPoolList" :key="index">{{item.poolName}}</Option>
						</Select>
					</FormItem>
					<FormItem label="K4SNO" prop="K4SNO" style="width:420px">
						<Input placeholder="请输入K4SNO" :maxlength="30" v-model="formValidate.K4SNO" clearable />
					</FormItem>
					<FormItem label="OPSNO" prop="OPSNO" style="width:420px">
						<Input placeholder="请输入OPSNO" :maxlength="30" v-model="formValidate.OPSNO" clearable />
					</FormItem>
					<FormItem label="KI" prop="ki" style="width:420px">
						<Select v-model="formValidate.ki" clearable placeholder="请选择是否需要KI">
							<Option value="1">是</Option>
							<Option value="2">否</Option>
						</Select>
					</FormItem>
					<FormItem label="ICCID" style="width:420px">
						<Select v-model="formValidate.iccid" clearable placeholder="请选择是否需要ICCID">
							<Option value="1">是</Option>
							<Option value="2">否</Option>
						</Select>
					</FormItem>
					<FormItem label="OPC" style="width:420px">
						<Select v-model="formValidate.opc" clearable placeholder="请选择是否需要OPC">
							<Option value="1">是</Option>
							<Option value="2">否</Option>
						</Select>
					</FormItem>
					<FormItem label="签约业务ID" prop="businessId" style="width:420px">
						<Input placeholder="请输入签约业务ID" :maxlength="50" v-model="formValidate.businessId" clearable />
					</FormItem>
				</Form>
				<div style="text-align: center;margin: 4px 0;">
					<Button type="primary" @click="submit" v-has="'add'" v-preventReClick
						:loading="submitFlag">提交</Button>
					<Button style="margin-left: 8px" @click="importFlag = false">取消</Button>
				</div>
			</div>
		</Modal>

		<!-- 批量删除模态框 -->
		<Modal v-model="batchDelFlag" title="VIMSI批量删除" :footer-hide="true" :mask-closable="false" width="530px">
			<div class="modal_content">
				<Form ref="formBatch" :model="formBatch" :rules="ruleBatch" :label-width="130" :label-height="100"
					inline style="font-weight:bold;">
					<FormItem label="VIMSI起始号码" prop="beginNum" style="width:420px">
						<Input placeholder="请输入VIMSI起始号码" v-model="formBatch.beginNum" clearable />
					</FormItem>
					<FormItem label="VIMSI结束号码" prop="endNum" style="width:420px">
						<Input placeholder="请输入VIMSI结束号码" v-model="formBatch.endNum" clearable />
					</FormItem>
				</Form>
				<div style="text-align: center;margin: 4px 0;">
					<Button type="primary" @click="batchSubmit" v-has="'batchDelete'" :loading="deleteloading"
						v-preventReClick>提交</Button>
					<Button style="margin-left: 8px" @click="batchDelFlag = false">取消</Button>
				</div>
			</div>
		</Modal>

		<!-- 导入记录查看 -->
		<Modal v-model="recoedViewFlag" title="导入记录查看" :mask-closable="false" :footer-hide="true" width="1285px" :loading="recordLoading">
			<div class="search_head">
				<Button icon="ios-arrow-back" @click="back">返回</Button> &nbsp;&nbsp;&nbsp;
			</div>
			<Table :columns="taskColumns" :data="taskData" :ellipsis="true" :loading="taskloading">
				<template slot-scope="{ row, index }" slot="successFileUrl">
					<Button v-has="'download'" v-if="row.taskStatus === '1' || row.successNum === 0" disabled type="success"
						@click="exportfile(row,1)">点击下载</Button>
					<Button v-has="'download'" v-else type="success" @click="exportfile(row,1)">点击下载</Button>
				</template>
				<template slot-scope="{ row, index }" slot="failFileUrl">
					<Button v-has="'download'" v-if="row.taskStatus === '1'  || row.failNum === 0" disabled type="error"
						@click="exportfile(row,2)">点击下载</Button>
					<Button v-has="'download'" v-else type="error" @click="exportfile(row,2)">点击下载</Button>
				</template>
				<template slot-scope="{ row, index }" slot="detail">
					<Button v-has="'info'" type="info" v-if="row.taskStatus === '1'" disabled
						@click="recordInfo(row)">点击查看</Button>
					<Button v-has="'info'" type="info" v-else @click="recordInfo(row)">点击查看</Button>
				</template>
			</Table>
			<!-- 分页 -->
			<Page :total="recordTotal" :current.sync="currentRecordPage" show-total show-elevator
				@on-change="goRecordPage" style="margin: 15px 0;" />
		</Modal>
		<!-- 导入记录详情查看 -->
		<Modal v-model="infoFlag" title="详情" :mask-closable="false" :footer-hide="true" width="600px" style="padding: 30px;">
			<div class="search_head" style="font-weight:bold;">
				<span>VIMSI数量:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.importNum}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>供应商:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.supplierName}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>卡池:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.cardpoolName}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>K4SNO:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.k4sno}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>OPSNO:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.opsno}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>是否需要KI:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{ info.needKi == '1' ? '是' : info.needKi == '2' ? '否' : ''}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>是否需要ICCID:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.needIccid == '1' ? '是' : info.needIccid == '2' ? '否' : ''}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>是否需要OPC:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.needOpc == '1' ? '是' : info.needOpc == '2' ? '否' : ''}}</span>&nbsp;&nbsp;
			</div>
			<div class="search_head" style="font-weight:bold;">
				<span>签约业务ID:</span>&nbsp;&nbsp;
				<span style="font-weight:bold;">{{info.signId}}</span>&nbsp;&nbsp;
			</div>
			<div style="display: flex;align-items: center;justify-content: center;">
				<Button icon="ios-arrow-back" size="large" @click="reback">返回</Button>
			</div>
		</Modal>
		<a ref="downloadLink" style="display: none"></a>
	</div>
</template>

<script>
	import {
		getVCardList,
		addVCard,
		batchDelVCard,
		statusVCard,
		delVCard,
		getRecordPage,
		exportFile
	} from '@/api/productMngr/vimsi';
	import {
		queryCardPoolList
	} from '@/api/productMngr/cardPool';
	import {
		supplierList,
	} from '@/api/tel/supplier';
	import config from '@/config/index.js';
	let keytype = config.keytype;
	export default {
		components: {},
		data() {
			//非负整数
			const nonnegativeInteger = (rule, value, callback) => {
				var str = /^[0-9]\d*$/;
				return str.test(value);
			};
			//正整数
			const positiveInteger = (rule, value, callback) => {
				var str = /^[1-9]\d*$/;
				return str.test(value);
			};
			return {
				deleteloading: false,
				cardPoodIntoFlag: false,
				submitFlag: false,
				providers: [], //供应商集合
				cardPoolList: [], //卡池集合
				searchCondition: {
					vimsi: '',
					msisdn: '',
					madeImsi: '',
					status: '',
					reqGtpProxy:'',
				},
				importFlag: false,
				formValidate: {
					vimsiNum: '',
					supplierId: '',
					reqGtpProxy: '',
					cardPool: '',
					iccid: '',
					K4SNO: '',
					OPSNO: '',
					ki: '',
					opc: '',
					businessId: '',
				},
				formValidateSave: '', //临时存储变量
				batchDelFlag: false,
				formBatch: {
					beginNum: '',
					endNum: '',
				},
				formBatchSave: '', //临时存储变量
				recoedViewFlag: false,
				infoFlag: false,
				ruleValidate: {
					vimsiNum: [{
							required: true,
							type: 'string',
							message: '请输入导入VIMSI数量',
						},
						{
							validator: nonnegativeInteger,
							message: 'VIMSI数量格式错误',
						},
						{
							validator: (rule, value, cb) => {
								return 0 != Number(value);
							},
							message: 'VIMSI数量不能为0',
						},
						{
							validator: (rule, value, cb) => {
								return Number(2147483647) >= Number(value);
							},
							message: 'VIMSI数量数值过大',
						}
					],
					supplierId: [{
						required: true,
						message: '请选择供应商',
						trigger: 'change',
					}],
					reqGtpProxy:  [{
						required: true,
						message: '请选是否需要GTP PROXY指定号码',
					}],
					cardPool: [{
						required: true,
						message: '请选择卡池',
						trigger: 'change',
					}],
					K4SNO: [
						// {
						//   required: true,
						//   type: 'string',
						//   message: '请输入K4SNO',
						// },
						// {
						//   validator: nonnegativeInteger,
						//   message: 'K4SNO格式错误(仅支持数字)',
						// }
						{
							validator: (rule, value, cb) => {
								var str = /^[0-9]\d*$/;
								return str.test(value) || value == '';
							},
							message: 'K4SNO格式错误(仅支持数字)',
						}
					],
					OPSNO: [
						// {
						//   required: true,
						//   type: 'string',
						//   message: '请输入OPSNO',
						// },
						// {
						//   validator: nonnegativeInteger,
						//   message: 'OPSNO格式错误(仅支持数字)',
						// }
						{
							validator: (rule, value, cb) => {
								var str = /^[0-9]\d*$/;
								return str.test(value) || value == '';
							},
							message: 'OPSNO格式错误(仅支持数字)',
						}
					],
					ki: [{
						validator: (rule, value, cb) => {
							//若为非普卡 则必选KI
							return (this.cardPoolType != '1' && value != '' && value != undefined) || this
								.cardPoolType == '1';
						},
						message: '终端卡池此项必填',
					}],
					iccid: [{
						required: true,
						message: '请选择是否需要ICCID',
						trigger: 'change',
					}],
					opc: [{
						required: true,
						message: '请选择是否需要OPC',
						trigger: 'change',
					}],
					businessId: [{
							validator: (rule, value, cb) => {
								//终端线下 则必填
								return (this.cardPoolType == '2' && this.isExpireReset == '1' &&
										value != '' && value != undefined) || this.cardPoolType != '2' ||
									(this.cardPoolType == '2' && this.isExpireReset == '2');
							},
							message: '到期后重置卡池此项必填',
						},
						// {
						//   validator: (rule, value, cb) => {
						//     var str = /^[1-9]\d*$/;
						//     return str.test(value) || value == '';
						//   },
						//   message: '签约业务ID格式错误',
						// },
					],
				},
				ruleBatch: {
					beginNum: [{
							required: true,
							type: 'string',
							message: '请输入VIMSI起始号码',
						},
						{
							validator: nonnegativeInteger,
							message: 'VIMSI号码格式错误',
						}
					],
					endNum: [{
							required: true,
							type: 'string',
							message: '请输入VIMSI结束号码',
						},
						{
							validator: nonnegativeInteger,
							message: 'VIMSI号码格式错误',
						}
					],
				},
				tableData: [],
				loading: false,
				currentPage: 1,
				total: 0,
				pageSize: 10,
				columns: [],
				statuseList: [{
						label: '待分配',
						value: '1'
					},
					{
						label: '已分配',
						value: '2'
					},
					// {
					//   label: '使用中',
					//   value: '3'
					// },
					{
						label: '暂停态',
						value: '4'
					},
					{
						label: '已冻结',
						value: '5'
					}
				],
				cardPoolType: '1',
				cardPoolStatus: '1',
				isExpireReset: '2',
				taskColumns: [{
						title: '导入时间',
						key: 'createTime',
						align: 'center',
						width: '150px',
					},
					{
						title: '完成时间',
						key: 'finishTime',
						align: 'center',
						width: '150px',
					},
					{
						title: '处理状态',
						key: 'taskStatus',
						align: 'center',
						width: '120px',
						render: (h, params) => {
							const row = params.row
							var text = row.taskStatus === '1' ? "处理中" : row.taskStatus === '2' ? "已完成" : ''
							return h('label', text)
						}
					},
					{
						title: '导入号码总数量',
						key: 'importNum',
						align: 'center',
						width: '120px',
					},
					{
						title: '导入成功数量',
						key: 'successNum',
						align: 'center',
						width: '120px',
					},
					{
						title: '导入失败数量',
						key: 'failNum',
						align: 'center',
						width: '120px',
					},
					{
						title: '下载导入成功号码列表',
						slot: 'successFileUrl',
						align: 'center',
						width: '160px',
					},
					{
						title: '下载导入失败号码列表',
						slot: 'failFileUrl',
						align: 'center',
						width: '160px',
					},
					{
						title: '详情',
						slot: 'detail',
						align: 'center',
						width: '150px',
					},
				],
				taskData: [],
				recordTotal: 0,
				currentRecordPage: 1,
				pageSize: 10,
				recordLoading: false,
        taskloading: false,
				info: {},
			}
		},
		methods: {
			// 页面加载
			goPageFirst(page) {
				this.currentPage = page;
				this.loading = true;
				var searchCondition = {
					poolId: this.searchCondition.poolId,
					imsi: this.searchCondition.vimsi.replace(/\s/g, ''),
					msisdn: this.searchCondition.msisdn.replace(/\s/g, ''),
					madeImsi: this.searchCondition.madeImsi.replace(/\s/g, ''),
					reqGtpProxy: this.searchCondition.reqGtpProxy,
					status: this.searchCondition.status,
					page: page,
					pageSize: this.pageSize
				};
				getVCardList(searchCondition).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						this.total = data.total;
						this.tableData = data.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
					this.loading = false;
				})
			},
			// 查询按钮,指定号码查询
			// 分页跳转
			goPage(page) {
				this.goPageFirst(page);
			},
			// 条件查询 currentPage置1
			search() {
				this.goPageFirst(1);
			},
			//信息初始化
			init() {
				//临时存储对象
				this.formValidateSave = JSON.stringify(this.formValidate);
				this.formBatchSave = JSON.stringify(this.formBatch);
				this.columns = [{
						title: 'VIMSI',
						key: 'imsi',
						align: 'center',
						minWidth: 130,
						tooltip: true
					},
					{
						title: 'MSISDN',
						key: 'msisdn',
						align: 'center',
						minWidth: 130,
						tooltip: true
					},
					{
						title: '制卡IMSI',
						key: 'madeImsi',
						align: 'center',
						minWidth: 130,
						tooltip: true
					},
					{
						title: '卡池ID',
						key: 'poolId',
						align: 'center',
						tooltip: true,
						minWidth: 200,
					},
					{
						title: '卡池名称',
						key: 'poolName',
						align: 'center',
						tooltip: true,
						minWidth: 120,
					},
					{
						title: 'VIMSI状态',
						key: 'status',
						align: 'center',
						minWidth: 100,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const color = row.status == '1' ? '#2d8cf0' : row.status == '2' ? '#808695' : row
								.status == '3' ?
								'#19be6b' :
								row.status == '4' ? '#ff9900' : row.status == '5' ? '#ed4014' : '#515a6e';
							const text = row.status == '1' ? '待分配' : row.status == '2' ? '已分配' : row.status ==
								'3' ? '使用中' : row.status == '4' ? '暂停态' : row.status == '5' ? '已冻结' : '未知';
							return h('label', {
								style: {
									color: color
								}
							}, text)
						}
					},
					{
						title: '是否需要GTP PROXY指定号码',
						key: 'reqGtpProxy',
						align: 'center',
						minWidth: 210,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							var text = row.reqGtpProxy == '1' ? "是" : row.reqGtpProxy == '2' ? "否" : '';
							return h('label', text)
						}
					}
				];
				var action = ['delete', 'active', 'stop'];
				var btnPriv = this.$route.meta.permTypes;
				var actionMixed = action.filter(function(val) {
					return btnPriv.indexOf(val) > -1
				});
				if (actionMixed.length > 0) {
					var width = 50 + 60 * actionMixed.length;
					this.columns.push({
						title: '操作',
						slot: 'action',
						width: width,
						align: 'center',
						fixed: 'right',
					});
				}
				this.goPageFirst(1);
			},
			//vimsi导入
			vimsiImport() {
				this.$refs['formValidate'].resetFields();
				//表单初始化
				var obj = JSON.parse(this.formValidateSave);
				this.getProviderList();
				// this.queryCardPoolList(obj.supplierId);
				this.formValidate = Object.assign({}, obj);
				if (this.cardPoodIntoFlag) {
					var sid = this.searchCondition.supplierId;
					var cid = this.searchCondition.poolId;
					this.formValidate.supplierId = sid;
					this.queryCardPoolList(sid);
					setTimeout(() => {
						this.formValidate.cardPool = cid;
						this.cardPoolSelect(cid);
					}, 1000);
				}
				this.importFlag = true;
			},
			//提交
			submit() {
				this.$refs['formValidate'].validate((valid) => {
					if (valid) {
						var data = this.formValidate;
						var obj = {
							vimsiNum: Number(data.vimsiNum),
							reqGtpProxy: data.reqGtpProxy,
							cardPool: data.cardPool,
							k4sno: data.K4SNO,
							opsno: data.OPSNO,
							keytype: keytype,
							isNeedICCID: data.iccid == '1' ? true : false,
							isNeedKI: data.ki == '1' ? true : false,
							isNeedOPC: data.opc == '1' ? true : false,
							supplierId: data.supplierId,
							upccBusinessId: data.businessId,
							poolStatus: this.cardPoolStatus,
						};
						this.submitFlag = true;
						addVCard(obj).then(res => {
							if (res && res.code == '0000') {
								setTimeout(() => {
									this.$Notice.success({
										title: '操作提示',
										desc: '操作成功'
									});
									this.importFlag = false;
									this.submitFlag = false;
									this.goPageFirst(this.currentPage);
								}, 1500);
							} else {
								this.submitFlag = false;
								throw res
							}
						}).catch((err) => {
							this.submitFlag = false;
						})
					}
				})
			},
			//批量删除
			batchSubmit() {
				this.$refs['formBatch'].validate((valid) => {
					if (valid) {
						this.deleteloading = true
						var poolId = this.searchCondition.poolId == undefined ? '' : this.searchCondition.poolId;
						var data = {
							poolId: poolId == undefined ? null : poolId,
							startImsi: this.formBatch.beginNum,
							endImsi: this.formBatch.endNum
						};
						batchDelVCard(poolId, this.formBatch.beginNum, this.formBatch.endNum).then(res => {
							if (res && res.code == '0000') {
								setTimeout(() => {
									this.$Notice.success({
										title: '操作提示',
										desc: '操作成功'
									});
									this.deleteloading = false
									this.batchDelFlag = false;
									this.goPageFirst(1);
								}, 1500);
							} else {
								throw res
							}
						}).catch((err) => {}).finally(() => {
                this.deleteloading = false
                this.batchDelFlag = false
              })
					}
				})
			},
			//批量删除
			removeBatch() {
				this.$refs['formBatch'].resetFields();
				//表单初始化
				this.formBatch = Object.assign({}, JSON.parse(this.formBatchSave));
				this.batchDelFlag = true;
			},
			//暂停
			stop(id) {
				this.$Modal.confirm({
					title: '确认暂停？',
					onOk: () => {
						statusVCard(id, "4").then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.goPageFirst(this.currentPage);
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			//激活
			active(id) {
				this.$Modal.confirm({
					title: '确认激活？',
					onOk: () => {
						statusVCard(id, "1").then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.goPageFirst(this.currentPage);
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			//删除
			remove(id, status) {
				if (status == '2') {
					this.$Notice.error({
						title: '操作提示',
						desc: '已分配状态不能删除'
					})
					return false;
				}
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						delVCard(id).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								if (this.tableData.length == 1 && this.currentPage > 1) {
									this.goPageFirst(this.currentPage - 1);
								} else {
									this.goPageFirst(this.currentPage);
								}
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			//获取供应商集合
			getProviderList() {
				supplierList().then(res => {
					if (res && res.code == '0000') {
						this.providers = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			//获取卡池集合
			queryCardPoolList(e) {
				this.formValidate.cardPool = '';
				if (e != undefined) {
					queryCardPoolList({
						supplierId: e,
						isSupportHimsi: "2"
					}).then(res => {
						if (res && res.code == '0000') {
							var data = res.data;
							this.cardPoolList = data;
						} else {
							throw res
						}
					}).catch((err) => {

					}).finally(() => {
						this.loading = false;
					})
				}
			},
			//卡池变更
			cardPoolSelect(e) {
				if (e != undefined) {
					var cardPoolList = this.cardPoolList;
					for (var i = 0; i < cardPoolList.length; i++) {
						if (e == cardPoolList[i].poolId) {
							this.isExpireReset = cardPoolList[i].isExpireReset;
							this.cardPoolType = cardPoolList[i].usageType;
							this.cardPoolStatus = cardPoolList[i].status;
						}
					}
				} else {
					this.isExpireReset = '2';
					this.cardPoolType = '1';
					this.cardPoolStatus = '1';
				}
				this.$refs['formValidate'].validateField('ki');
				this.$refs['formValidate'].validateField('businessId');
			},
			// 导入记录查看
			recordView() {
				this.recoedViewFlag = true
				this.goRecodePageFirst(1)
			},
			back() {
				this.recoedViewFlag = false
			},

			// 导入记录查看列表
			goRecodePageFirst: function(page) {
				this.loading = true
				var _this = this
				getRecordPage({
					pageSize: this.pageSize,
					pageNum: page,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.recordLoading = false
						var data = res.data
						this.currentRecordPage = page
						this.recordTotal = data.total
						this.taskData = data.records
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					_this.loading = false
					this.recordLoading = false
				})
			},
			goRecordPage(page) {
				this.goRecodePageFirst(page)
			},

			// 下载导入号码列表
			exportfile:function(row, type) {
				this.taskloading = true
				var _this = this
				exportFile({
					id: row.id,
					type: type
				}).then(res => {
					const content = res.data
					let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[
						1]) //获取到Content-Disposition;filename  并解码
					console.log(fileName)
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content, fileName)
					}
				}).finally(() => {
					this.taskloading = false
				})
			},
			// 主卡导入记录详情
			recordInfo(data){
				this.info = {
					importNum : data.importNum,
					supplierName : data.supplierName,
					cardpoolName : data.cardpoolName,
					k4sno : data.k4sno,
					opsno : data.opsno,
					needKi : data.needKi,
					needIccid : data.needIccid,
					needOpc : data.needOpc,
					signId : data.signId,
				}
				this.infoFlag = true
			},
			reback() {
				this.infoFlag = false
			},

		},

		mounted() {
			try {
				var poolCard = JSON.parse(decodeURIComponent(this.$route.query.p));
				this.cardPoodIntoFlag = true;
				this.searchCondition.poolId = poolCard.p;
				this.searchCondition.supplierId = poolCard.s;
			} catch (e) {

			}
			this.init();
		},
		watch: {
			importFlag(newValue, oldValue) {
				//操作模态框拉起加载供应商信息
				// if (newValue) {
				//   this.getProviderList();
				//   this.queryCardPoolList();
				// }
			}
		},
	}
</script>
<style scoped="scoped">
	.search_head {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 20px;
	}

	.modal_content {
		padding: 0 16px;
	}

	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		margin: 0 10px;
		width: 80px;
	}
	
	.search_box_label2 {
		font-weight: bold;
		text-align: center;
		margin: 0 10px;
		width: 350px;
	}

	.search_box {
		width: 280px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}
</style>
