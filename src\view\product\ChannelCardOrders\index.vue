<template>
	<!-- 渠道商白卡订单管理 -->
	<Card>
		<!-- 搜索条件 -->
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">渠道商</span>
				<Select v-model="corpId" clearable placeholder="请选择渠道商" filterable>
					<Option :value="item.corpId" v-for="(item,index) in corpList" :key="index" :title="item.corpName">{{item.corpName}}</Option>
				</Select>
			</div>
			<div class="search_box">
				<span class="search_box_label">订单状态</span>
				<Select v-model="orderStatus" clearable placeholder="请选择订单状态" filterable>
					<Option :value="1">已下单</Option>
					<Option :value="2">已取消</Option>
					<Option :value="3">待付款</Option>
					<Option :value="4">付款已确认</Option>
					<Option :value="5">待发货</Option>
					<Option :value="6">发货中</Option>
					<Option :value="7">已发货</Option>
					<Option :value="8">发货失败</Option>
					<Option :value="9">付款未到账</Option>
					<Option :value="10">付款待确认</Option>
					<Option :value="11">回滚中</Option>
					<Option :value="12">回滚成功</Option>
					<Option :value="13">回滚失败</Option>
				</Select>
			</div>
			<div class="search_box">
				<span class="search_box_label">主卡形态</span>
				<Select v-model="cardForms" clearable placeholder="请选择主卡形态" filterable>
					<Option :value="1">普通卡(实体卡)</Option>
					<Option :value="2">Esim卡</Option>
					<Option :value="4">IMSI</Option>
				</Select>
			</div>
			<div class="search_box">
				<span style="font-weight: bold;text-align: center;width: 180px;">CMLINK/定制卡</span>
				<Select v-model="chargingMode" clearable placeholder="请选择" filterable>
					<Option :value="2">CMLINK卡</Option>
					<Option :value="1">定制卡</Option>
				</Select>
			</div>
			<div class="search_box">
				<Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search()">搜索</Button>
			</div>
					<!-- 增加一个导出 -->
			<div class="search_box">
				<Button v-has="'export'" icon="ios-cloud-download-outline" type="success" :loading="exportLoading" @click="exportList()">导出</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table ref="selection" :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<div style="padding: 10px 5px 5px 0 ;">
					<Button v-has="'confirm'" v-show="(row.orderStatus == '1' && row.chargingMode == '2') || (row.orderStatus == '4' && row.chargingMode == '1')" type="success" ghost
						size="small" style="margin: 0 5px 5px 0" @click="orderConfirmation(row)">订单确认</Button>
					<Button v-has="'cancel'" v-show="[1,3,4].includes(+row.orderStatus)" type="error" ghost size="small"
						style="margin: 0 5px 5px 0" @click="orderCancellation(row.orderId)">订单取消</Button>
					<Button v-has="'createinvoice'" type="info" ghost size="small" style="margin: 0 5px 5px 0"
						v-if="row.orderStatus == '1' && row.chargingMode == '1'" @click="showInvoiceView(row, '1')">生成Invoice</Button>
					<Button v-has="'createinvoiceAgain'" type="info" ghost size="small" style="margin: 0 5px 5px 0"
          	v-if="row.orderStatus == '3' && row.chargingMode == '1'" @click="showInvoiceView(row, '2')">重新生成Invoice</Button>
					<Button v-has="'downloadinvoice'"
						v-show="![1,2].includes(+row.orderStatus) && row.chargingMode == '1'" type="warning" ghost
						size="small" style="margin: 0 5px 5px 0"
						@click="downloadFile('1',row.orderId)">下载Invoice</Button>
					<Button v-has="'paymentProof'"
						v-show="![1,2,3].includes(+row.orderStatus) && row.chargingMode == '1'" type="primary" ghost
						size="small" style="margin: 0 5px 5px 0" @click="downloadFile('2',row.orderId)">下载付款证明</Button>
					<Button v-has="'downloadFailFile'" v-if="row.deliverFailPath" type="error" ghost size="small"
						style="margin: 0 5px 5px 0" @click="downloadFile('3',row.orderId)">下载失败文件</Button>
					<Button v-has="'delivery'" v-if="row.orderStatus == '5'||row.orderStatus == '12'" type="success" ghost size="small"
						style="margin: 0 5px 5px 0" @click="delivery(row)">发货</Button>
          <Button v-has="'rollbackOrder'" v-if="row.orderStatus == '7'||row.orderStatus == '13'" type="error" ghost size="small"
          	style="margin: 0 5px 5px 0" @click="rollbackOrder(row)">回滚</Button>
					<Button v-has="'downloadDeliverFlie'" v-if="row.orderStatus == '7'&&row.deliverCardfilePath" type="success"
						ghost size="small" style="margin: 0 5px 5px 0" @click="downloadFile('4',row.orderId)">下载发货列表</Button>
				</div>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 发货 -->
		<Modal v-model="deliveryFlag" title="发货" :footer-hide="true" :mask-closable="false" width="450px"
			@on-cancel="cancelModal">
			<div>
				<Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="100" :label-height="100"
					inline style="font-weight:bold;">
					<FormItem label="合作模式" prop="cooperationMode" style="width:400px">
						<Select v-model="formValidate.cooperationMode" disabled>
							<Option value="1">代销</Option>
							<Option value="2">A2Z</Option>
						</Select>
					</FormItem>
					<FormItem label="短信模板" prop="smsTemplate" style="width:400px">
						<Select v-model="formValidate.smsTemplate" filterable clearable placeholder="下拉选择短信模板">
							<Option v-for="(item2,index2) in smsAll" :value="item2.templateId" :key="item2.templateId"
								:title="item2.templateName">{{item2.templateName}}</Option>
						</Select>
					</FormItem>
					<FormItem label="语言" prop="language" style="width:400px">
						<Select v-model="formValidate.language" clearable placeholder="请选择语言">
							<Option v-for="item in languageList" :value="item.value" :key="item.value">{{ item.label}}</Option>
						</Select>
					</FormItem>
					<FormItem label="是否新加坡卡" prop="singaporeCard" style="width:400px">
						<Select v-model="formValidate.singaporeCard" :clearable="true" placeholder="请选择是否为新加坡卡">
							<Option value="1">是</Option>
							<Option value="2">否</Option>
						</Select>
					</FormItem>
					<FormItem label="卡号列表" prop="file">
						<div style="display: flex;">
							<Upload v-model="formValidate.file" :action="uploadUrl" :on-success="fileSuccess" :on-error="handleError"
								:before-upload="handleBeforeUpload" ref="upload" :on-progress="fileUploading">
								<Button icon="ios-cloud-upload-outline">上传ICCID列表</Button>
							</Upload>
							<div style="width: 250px; margin-left: 45px;">
								<Button type="info" icon="ios-download" @click="downloadTemplate">下载模版文件</Button>
							</div>
						</div>
						<ul class="ivu-upload-list" v-if="file" style="width: 310px;">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />{{file.name}}
								</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" @click="removeFile"></i>
							</li>
						</ul>
					</FormItem>
					<FormItem label="物流公司" prop="logisticCompany" style="width:400px" :rules="cardForm == '1' ?
						ruleValidate.logisticCompany : [{required: false}]">
						<Input placeholder="请输入物流公司" v-model="formValidate.logisticCompany" clearable />
					</FormItem>
					<FormItem label="物流编号" prop="logistic" style="width:400px" :rules="cardForm == '1' ?
						ruleValidate.logistic : [{required: false}]">
						<Input placeholder="请输入物流编号" v-model="formValidate.logistic" clearable />
					</FormItem>
					<FormItem label="订单批次" prop="orderBatch" style="width:400px">
						<Input placeholder="请输入订单批次" v-model="formValidate.orderBatch" clearable />
					</FormItem>
				</Form>
				<div style="text-align: center;margin: 4px 0;">
					<Button style="margin-right: 30px" @click="cancelModal">返回</Button>
					<Button v-has="'deliverySubmit'" type="primary" @click="submit" v-preventReClick
						:loading="submitLoading">确定</Button>
				</div>
			</div>
		</Modal>
		<!-- 订单确认弹窗 -->
		<Modal v-model="orderModal" title="订单确认" :footer-hide="true" :mask-closable="false" width="450px"
			@on-cancel="cancelModal">
			<Spin size="large" fix v-if="spinShow"></Spin>
			<Form ref="formValidate2" :model="formValidate2" :rules="ruleValidate2" :label-width="120" :label-height="100"
				inline style="font-weight:bold;">
				<FormItem label="流量计费规则" prop="a2zRuleId" style="width:400px" v-if="orderBIndCooperationMode == '2'">
					<Select filterable clearable v-model="formValidate2.a2zRuleId" placeholder="请选择流量计费规则">
						<Option v-for="item3 in a2zRuleList" :value="item3.charging" :key="item3.charging">{{ item3.chargingName }}
						</Option>
					</Select>
				</FormItem>
				<FormItem label="国家卡池关联组" prop="groupId" style="width:400px"
					v-if="orderBIndCooperationMode == '2' && allowCreatePackage == '1'">
					<Select filterable clearable v-model="formValidate2.groupId" placeholder="请选择国家卡池关联组">
						<Option v-for="item1 in groupIdList" :value="item1.groupId" :key="item1.groupId" :title="item1.groupName">{{
							item1.groupName.length > 30 ? item1.groupName.substring(0,30) + "…" : item1.groupName }}</Option>
					</Select>
				</FormItem>
				<FormItem label="IMSI费规则" prop="imsiFee" style="width:400px" v-if="cardForm == '4'">
					<Select v-model="formValidate2.imsiFee" filterable clearable placeholder="请选择IMSI费规则">
						<Option v-for="item2 in imsiFeeList" :value="item2.imsi" :key="item2.imsi">{{ item2.imsiName}}</Option>
					</Select>
				</FormItem>
			</Form>
			<div style="text-align: center;margin: 4px 0;">
				<Button style="margin-right: 30px" @click="cancelModal">返回</Button>
				<Button type="primary" @click="submitOrder" v-preventReClick :loading="submitOrderLoading">确定</Button>
			</div>
		</Modal>
		<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
		<!-- 生成invoice -->
		<Modal v-model="invoiceModel" title="生成发票预览" @on-ok="createInvoice" @on-cancel="cancelInvoice" width="800px"
			:styles="{top: '10px'}">
			<Card width="750px">
				<invoiceCard ref="dataForm" :customerName="invoiceInfo.customerName" :InvoiceNo="invoiceInfo.InvoiceNo"
					:address="invoiceInfo.address" :columns="invoiceColumns" :FileTitle="invoiceInfo.FileTitle"
					:data="invoiceInfo.data" :InvoiceDate="invoiceInfo.InvoiceDate" @getdesc='getdesc'
					:InvoiceDescValue="invoiceInfo.InvoiceDesc" :currency="invoiceInfo.currency" :invoiceForm.sync="invoiceForm"
					:quantityValue="quantityValue" />
			</Card>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelInvoice">取消</Button>
				<Button type="primary" :loading="Invoiceloading" @click="createInvoice">生成Invoice</Button>
			</div>
		</Modal>
		<a ref="downloadLink" style="display: none"></a>
	</Card>
</template>

<script>
	import invoiceCard from '@/components/invoice/invoiceCard'
	import {
		getCorpList
	} from "@/api/product/package/batch";
	import {
		getList,
		shippingMethods,
		comfirmOrder,
		cancelOrder,
		downloadFile,
		createInvoice,
    createInvoiceAgain,
		createInvoiceNo,
    InfoOrder,
		rollbackOrder,
		blankCardOrderExport
	} from "@/api/product/whiteCardOrders";
  import {
  	getChannelSmsTemplate,
  } from '@/api/channel.js'
	import mixin from '@/mixin/validate'
	export default {
		mixins: [mixin],
		components: {
			invoiceCard
		},
		data() {
			// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (this.uploadList && this.uploadList.length === 0) {
					callback(new Error("请上传文件"))
				} else {
					callback()
				}
			}
			return {
				total: 0,
				currentPage: 1,
				corpId: "",
				orderStatus: "",
				cardForms: "",
				chargingMode: "",
				cardForm: "", //主卡形态
				orderId: "", //订单ID
				uploadUrl: '',
				file: null,
				id: "",
				address: '',
				orderUserId: "",
				InvoiceAmount: "",
				quantityValue: 0,
				invoiceNo: "",
        invoiceType: "",
        orderBIndCooperationMode: "",
        orderBIndId: "",
        allowCreatePackage: "",
				default: "*All the above listed prices do not include any Tax. \n\nPayment Instruction\n\nPlease remit payment to beneficiary China Mobile International Limited by telegraph transfer.\n\nBeneficiary Name: China Mobile International Limited\nBeneficiary Address: Level 30, Tower 1, Kowloon Commerce Centre, No. 51 Kwai Cheong Road, Kwai Chung. N.T. Hong Kon\nBank Name: The Hongkong and Shanghai Banking Corporation Limited\nBank Address: HSBC Main Building, 1 Queen's Road Central, Hong Kong\nSwift Code: HSBCHKHHHKH\nAccount No.:**********-838\n\n*Please quote our Invoice No. with your payment instructions to the bank upon remittance. \n*Please email remittance <NAME_EMAIL> for update of your account.\n\nThis computer generated document requires no signature.",
				loading: false,
				searchloading: false, //查询加载
				submitLoading: false, //发货提交
				invoiceModel: false, //Invoice弹窗
				Invoiceloading: false,
				deliveryFlag: false, //发货弹窗
        orderModal: false, //订单确认弹窗
        submitOrderLoading: false,
        spinShow: false,
				corpList: [], //渠道商列表
				smsAll: [], //模版列表
				languageList: [{
						value: '1',
						label: '繁体中文'
					},
					{
						value: '2',
						label: '英文'
					},
					{
						value: '3',
						label: '简体中文'
					}
				],
				uploadList: [],
        a2zRuleList: [], //流量计费规则列表
        groupIdList: [], //国家卡池关联组列表
        imsiFeeList: [], //imsi费规则列表
				data: [], //表格列表
				columns: [{
					title: "订单ID",
					key: 'orderId',
					minWidth: 180,
					align: 'center',
					tooltip: true,
				}, {
					title: "订单时间",
					key: 'createTime',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.createTime) {
							var time = new Date(row.createTime)
							const y = time.getFullYear() // 可返回一个表示年份的 4 位数字
							const m = time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time
								.getMonth() +
								1 // 可返回表示月份的数字。返回值是 0（一月） 到 11（十二月） 之间的一个整数 // 注意： 一月为 0, 二月为 1, 以此类推。
							const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate()
							const h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
							const min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
							const seconds = time.getSeconds() < 10 ? '0' + time.getSeconds() : time
								.getSeconds()
							text = y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + seconds
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: "渠道商",
					key: 'corpName',
					minWidth: 150,
					align: 'center',
					tooltip: true
				}, {
					title: "主卡形态",
					key: 'cardForm',
					minWidth: 140,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text = row.cardForm == '1' ? '普通卡（实体卡）' : row.cardForm == '2' ? 'Esim卡' : row.cardForm == '4' ? 'IMSI' : ''
						return h('label', text)
					},
				}, {
					title: "卡片类型",
					key: 'cooperationMode',
					minWidth: 135,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text = row.cooperationMode == '1' ? "代销" : row.cooperationMode == '2' ? "A~Z" : ''
						return h('label', text)
					},
				}, {
					title: "CMLINK/定制卡",
					key: 'chargingMode',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text = row.chargingMode == 1 ? "定制卡" : row
							.chargingMode == 2 ? "CMLINK卡" : ''
						return h('label', text)
					},
				}, {
					title: "卡片数量",
					key: 'count',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: '订单状态',
					key: 'orderStatus',
					align: 'center',
					minWidth: 100,
					render: (h, params) => {
					  const row = params.row;
						const color = row.orderStatus === '1' ? '#2b85e4' :
													row.orderStatus === '2' ? '#00aa00' :
													row.orderStatus === '3' ? '#ff0000' :
													row.orderStatus === '4' ? '#69e457' :
													row.orderStatus === '5' ? '#e47a49' :
													row.orderStatus === '6' ? '#dd74e4' :
													row.orderStatus === '7' ? '#24cbe4' :
													row.orderStatus === '8' ? '#7009e4' :
													row.orderStatus === '9' ? '#e4b809' :
													row.orderStatus === '10' ? '#ff9900' :
													row.orderStatus === '11' ? '#e4e424' : // 新增：回滚中
													row.orderStatus === '12' ? '#24e424' : // 新增：回滚成功
													row.orderStatus === '13' ? '#e42424' : // 新增：存在回滚失败
													'';

						const text = row.orderStatus === '1' ? '已下单' :
													row.orderStatus === '2' ? '已取消' :
													row.orderStatus === '3' ? '待付款' :
													row.orderStatus === '4' ? '付款已确认' :
													row.orderStatus === '5' ? '待发货' :
													row.orderStatus === '6' ? '发货中' :
													row.orderStatus === '7' ? '已发货' :
													row.orderStatus === '8' ? '发货失败' :
													row.orderStatus === '9' ? '付款未到账' :
													row.orderStatus === '10' ? '付款待确认' :
													row.orderStatus === '11' ? '回滚中' : // 新增
													row.orderStatus === '12' ? '回滚成功' : // 新增
													row.orderStatus === '13' ? '存在回滚失败' : // 新增
													'';

						return h('label', {
								style: {
										color: color
								}
						}, text);
					}
				}, {
					title: "国家",
					key: 'countryName',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: "邮编",
					key: 'postcode',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: "收货地址",
					key: 'address',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: "收件人",
					key: 'addressee',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: "联系电话",
					key: 'phoneNumber',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: "物流公司",
					key: 'logisticCompany',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: "物流编号",
					key: 'logistic',
					minWidth: 130,
					align: 'center',
					tooltip: true,
				}, {
					title: "订单批次",
					key: 'orderBatch',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: "生成Invoice时间",
					key: 'invoiceTime',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.invoiceTime) {
							var time = new Date(row.invoiceTime)
							const y = time.getFullYear() // 可返回一个表示年份的 4 位数字
							const m = time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time
								.getMonth() +
								1 // 可返回表示月份的数字。返回值是 0（一月） 到 11（十二月） 之间的一个整数 // 注意： 一月为 0, 二月为 1, 以此类推。
							const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate()
							const h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
							const min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
							const seconds = time.getSeconds() < 10 ? '0' + time.getSeconds() : time
								.getSeconds()
							text = y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + seconds
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: "上传付款证明时间",
					key: 'paymentProofsTime',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.paymentProofsTime) {
							var time = new Date(row.paymentProofsTime)
							const y = time.getFullYear() // 可返回一个表示年份的 4 位数字
							const m = time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time
								.getMonth() +
								1 // 可返回表示月份的数字。返回值是 0（一月） 到 11（十二月） 之间的一个整数 // 注意： 一月为 0, 二月为 1, 以此类推。
							const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate()
							const h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
							const min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
							const seconds = time.getSeconds() < 10 ? '0' + time.getSeconds() : time
								.getSeconds()
							text = y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + seconds
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: "订单确认时间",
					key: 'confirmTime',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.confirmTime) {
							var time = new Date(row.confirmTime)
							const y = time.getFullYear() // 可返回一个表示年份的 4 位数字
							const m = time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time
								.getMonth() +
								1 // 可返回表示月份的数字。返回值是 0（一月） 到 11（十二月） 之间的一个整数 // 注意： 一月为 0, 二月为 1, 以此类推。
							const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate()
							const h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
							const min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
							const seconds = time.getSeconds() < 10 ? '0' + time.getSeconds() : time
								.getSeconds()
							text = y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + seconds
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: "发货时间",
					key: 'deliverTime',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.deliverTime) {
							var time = new Date(row.deliverTime)
							const y = time.getFullYear() // 可返回一个表示年份的 4 位数字
							const m = time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time
								.getMonth() +
								1 // 可返回表示月份的数字。返回值是 0（一月） 到 11（十二月） 之间的一个整数 // 注意： 一月为 0, 二月为 1, 以此类推。
							const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate()
							const h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
							const min = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
							const seconds = time.getSeconds() < 10 ? '0' + time.getSeconds() : time
								.getSeconds()
							text = y + '-' + m + '-' + d + ' ' + h + ':' + min + ':' + seconds
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: "短信模板",
					key: 'templateName',
					minWidth: 160,
					align: 'center',
					tooltip: true,
				}, {
					title: "语言",
					key: 'language',
					minWidth: 160,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text =
              row.language == 1 ? "繁体中文" :
              row.language == 2 ? "英文" :
              row.language == 3 ? "简体中文" : ""
						return h('label', text)
					},
				}, {
					title: "产品包装",
					key: 'cardPackage',
					minWidth: 160,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text = row.cardPackage == 1 ? "裸卡" :
             row.cardPackage == 2 ? "包装卡" : ""
						return h('label', text)
					},
				}, {
					title: "操作",
					slot: 'action',
					minWidth: 330,
					align: 'center',
					fixed: 'right'
				}, ],
				formValidate: {
					cooperationMode: "",
					smsTemplate: "",
					language: "",
					singaporeCard: "",
					file: "",
					logisticCompany: "",
					logistic: "",
					orderBatch: ""
				},
				formValidate2: {
          a2zRuleId: "",
          groupId: "",
          imsiFee: ""
        },
        ruleValidate: {
					smsTemplate: [{
						required: true,
						message: "短信模板不能为空",
					}],
					language: [{
						required: true,
						message: "语言不能为空",
					}],
					singaporeCard: [{
						required: true,
						message: "是否新加坡卡不能为空",
					}],
					file: [{
						required: true,
						validator: validateUpload,
						trigger: 'change',
					}],
					logisticCompany: [{
						required: true,
						message: "物流公司不能为空",
					}],
					logistic: [{
						required: true,
						message: "物流编号不能为空",
					}],
					orderBatch: [{
						required: true,
						message: "订单批次不能为空",
					}, {
            // 新的校验规则，检查是否包含&符号
            validator: (rule, value, callback) => {
              if (value && value.includes('&')) {
                callback(new Error('订单批次不能包含&符号'));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },],
				},
				ruleValidate2: {
          a2zRuleId: [{
          	required: true,
          	message: "计费规则不能为空",
          }],
          groupId: [{
          	required: true,
          	message: "国家卡池关联组不能为空",
          }],
          imsiFee: [{
          	required: true,
          	message: "IMSI费不能为空",
          }],
        },
        modelData: [{
					'ICCID': '********',
				}, ],
				modelColumns: [{
						title: 'ICCID',
						key: 'ICCID'
					}, // 列名根据需要添加
				],
				/**
				 * ---------------生成发票相关----------------
				 */
				invoiceForm: {
					productName: 'GDS-SIM card fees',
					listedPrice: ''
				},
				invoiceInfo: {
					customerName: "",
					InvoiceNo: "",
					address: "",
					InvoiceDate: "",
					currency: "",
					InvoiceDesc: "*All the above listed prices do not include any Tax. \n\nPayment Instruction\n\nPlease remit payment to beneficiary China Mobile International Limited by telegraph transfer.\n\nBeneficiary Name: China Mobile International Limited\nBeneficiary Address: Level 30, Tower 1, Kowloon Commerce Centre, No. 51 Kwai Cheong Road, Kwai Chung. N.T. Hong Kon\nBank Name: The Hongkong and Shanghai Banking Corporation Limited\nBank Address: HSBC Main Building, 1 Queen's Road Central, Hong Kong\nSwift Code: HSBCHKHHHKH\nAccount No.:**********-838\n\n*Please quote our Invoice No. with your payment instructions to the bank upon remittance. \n*Please email remittance <NAME_EMAIL> for update of your account.\n\nThis computer generated document requires no signature.",
					data: [{
						description: '',
						listedPrice: '',
						quantity: '',
						amount: '',
					}]
				},
				invoiceColumns: [{
						title: 'Product Name',
						align: 'center',
						width: 230,
						slot: 'description',
						renderHeader: (h, params) => {
							return h('div', [
								h('span', {
									style: {
										color: 'red'
									}
								}, '*'),
								h('span', ' Product Name'),
							])
						}
					},
					{
						title: 'Listed Price',
						align: 'center',
						width: 240,
						slot: 'listedPrice',
						renderHeader: (h, params) => {
							return h('div', [
								h('span', {
									style: {
										color: 'red'
									}
								}, '*'),
								h('span', ' Listed Price'),
							])
						}
					},
					{
						title: 'Quantity',
						align: 'center',
						width: 131,
						key: 'quantity',
						render: (h, params) => {
							const row = params.row;
							const text = row.quantity
							//千分位
							const formatter = new Intl.NumberFormat('en-US', {
								style: 'decimal',
								minimumFractionDigits: 0,
								maximumFractionDigits: 0
							});
							const formattedText = formatter.format(parseFloat(text));
					
							return h('label', formattedText)
						}
					},
					{
						title: 'Amount',
						align: 'center',
						width: 131,
						slot: 'amount',
					},
				],
				exportLoading: false
			}
		},
		mounted() {
			this.getCorpList();
			this.goPageFirst(1)
		},
		methods: {
			goPageFirst: function(page) {
        this.data = []
				this.loading = true
				var _this = this
				getList({
					pageNum: page,
					pageSize: 10,
					orderUserId: this.corpId,
					status: this.orderStatus,
					cardForms: this.cardForms,
					chargingMode: this.chargingMode
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.data = res.data;
						this.total = res.count
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			//导出
			exportList () {
				console.log('导出')
				this.exportLoading = true
				blankCardOrderExport({
					pageNum: -1,
					pageSize: -1,
					orderUserId: this.corpId,
					status: this.orderStatus,
					cardForms: this.cardForms,
					chargingMode: this.chargingMode
				}).then(res => {
					this.exportLoading = false
					const content = res.data;
					//下载文件
					let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[1]); //获取到Content-Disposition;filename  并解码
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content, fileName)
					}
					this.$Notice.success({
						title: "操作提示",
						desc: "导出成功！",
					});
					
				}).catch(err => {
					this.exportLoading = false
					console.error(err)
				})
			},
			//订单取消
			orderCancellation(id) {
				this.$Modal.confirm({
					title: "订单取消？",
					onOk: () => {
						cancelOrder(id).then((res) => {
							if (res.code === "0000") {
								this.$Notice.success({
									title: "操作提示",
									desc: "订单取消成功！",
								});
							}
							this.goPageFirst(1);
						});
					},
				});
			},
      //订单确认
      orderConfirmation(row) {
        if (row.cooperationMode == '1' && row.cardForm != '4') {
          this.$Modal.confirm({
          	title: "订单确认？",
          	onOk: () => {
          		comfirmOrder({
          		  id: row.orderId,
          		  freeimsiId: ""
          		}).then(res => {
          			if (res && res.code == '0000') {
          				this.$Notice.success({
          					title: "操作提醒",
          					desc: "操作成功"
          				})
          			} else {
          				throw res
          			}
          		}).catch((err) => {
          			console.error(err)
          		}).finally(() => {
                this.goPageFirst(1)
          		})
          	},
          });
        } else {
          this.orderModal = true
          this.spinShow = true
          this.orderBIndCooperationMode = row.cooperationMode
          this.orderBIndId = row.orderId
          this.cardForm = row.cardForm
          this.allowCreatePackage = row.allowCreatePackage
          this.getInfoOrder(row.orderUserId, row.cooperationMode)
        }
      },
      // 订单绑定提交
      submitOrder() {
        this.$refs["formValidate2"].validate((valid) => {
        	if (valid) {
            let data = {
              id: this.orderBIndId,
              freeimsiId: this.formValidate2.imsiFee
            }
            let data2 = {
              id: this.orderBIndId,
              ruleId: this.formValidate2.a2zRuleId,
              groupId: this.formValidate2.groupId ? this.formValidate2.groupId : "",
              freeimsiId: this.formValidate2.imsiFee
            }
            let resultData = this.orderBIndCooperationMode == '2' ? data2 : data
        		this.submitOrderLoading = true
        		comfirmOrder(resultData).then(res => {
        			if (res && res.code == '0000') {
        				this.$Notice.success({
        					title: "操作提醒",
        					desc: "操作成功"
        				})
        				this.submitOrderLoading = false
        				this.cancelModal()
        			} else {
        				throw res
        			}
        		}).catch((err) => {
        			console.error(err)

        		}).finally(() => {
              this.submitOrderLoading = false
              this.goPageFirst(1)
        		})
        	}
        });
      },
			//1、下载Invoice  2、下载付款证明  3、下载失败文件
			downloadFile(type, id) {
				downloadFile({
					id: id,
					type: type
				}).then(res => {
					const content = res.data
					let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content,fileName)
					}
				}).catch()
			},
			//回滚
			rollbackOrder (data) {
				this.$Modal.confirm({
					title: '提示',
					content: '确定执行回滚操作吗？',
					onOk: () => {
						rollbackOrder({
							orderId: data.orderId,
							retry: data.orderStatus == '13' ? "1" : ""
						}).then(res => {
							if (res && res.code == '0000') {
								//提示并刷新列表
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
							} else {
								throw res
							}
						}).catch((err) => {
							console.error(err)

						}).finally(() => {
							this.goPageFirst(1)
						})
					},
					onCancel: () => {

					}
				});

			},
			//获取发票说明
			getdesc(data) {
				this.invoiceInfo.InvoiceDesc = data
			},
			//生成Invoice、重新生成invoice
			showInvoiceView(row, type) {
        this.invoiceType = type
				this.orderId = row.orderId
				this.orderUserId = row.orderUserId
				this.id = row.id //设置选中行主键
				this.createInvoiceNo(row.orderId, row.orderUserId)
			},
			//生成发票
			createInvoice: function() {
				if (this.invoiceInfo.InvoiceDesc) {
					this.$refs.dataForm.$refs.invoiceForm.validate((valid) => {
						if (valid) {
              let func = this.invoiceType == '1' ? createInvoice : createInvoiceAgain
							this.Invoiceloading = true
							//TODO 生成发票参数设置
							func({
								id: this.orderId,
								corpId: this.orderUserId,
								desc: this.invoiceInfo.InvoiceDesc,
								productName: this.invoiceForm.productName,
								listedPrice: this.invoiceForm.listedPrice,
								invoiceNo: this.invoiceNo
							}).then(res => {
								if (res && res.code == '0000') {
									this.$Notice.success({
										title: '操作提示',
										desc: '发票生成成功'
									})
									this.$refs.dataForm.$refs.invoiceForm.resetFields()
									this.invoiceModel = false
									this.Invoiceloading = false
									//刷新页面数据
									this.goPageFirst(1)
								} else {
									throw res
								}
							}).catch((err) => {
								this.Invoiceloading = false
								console.log(err)
							})
						}
					})
				} else {
					this.$Message.error("发票说明不能为空");
				}
			},
			/**
			 * 生成票号
			 */
			createInvoiceNo: function(orderId, corpId) {
				createInvoiceNo({
					orderId: orderId,
					corpId: corpId
				}, ).then(res => {
					if (res && res.code == '0000') {
						this.quantityValue = res.data.quantity
						this.invoiceNo = res.data.invoiceNo
						const now = new Date();
						const year = now.getFullYear(); // 年
						const month = now.getMonth() + 1 < 10 ? '0' + (now.getMonth() + 1) : now.getMonth() +
						1; // 月
						const date = now.getDate() < 10 ? '0' + now.getDate() : now.getDate(); // 日
						this.invoiceInfo = {
							customerName: res.data.companyName,
							address: res.data.companyAddress,
							currency: res.data.currency,
							InvoiceNo: res.data.invoiceNo,
							InvoiceDate: year + '-' + month + '-' + date,
							InvoiceDesc: this.default,
							data: [{
								quantity: res.data.quantity,
							}]
						}
						this.invoiceForm = {
							productName: "GDS-SIM card fees", //账单名称
						}
						this.invoiceModel = true
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			/**
			 * 生成发票预览退出
			 */
			cancelInvoice: function() {
				this.id = null
				this.invoiceModel = false
				this.invoiceInfo = []
				this.$refs.dataForm.$refs.invoiceForm.resetFields()
			},
			//发货弹窗
			delivery(row) {
        this.getChannelSmsTemplate(row.orderUserId, row.cooperationMode)
				this.formValidate.cooperationMode = row.cooperationMode
				this.formValidate.language = row.language
        this.formValidate.smsTemplate = row.templateId.toString()
        this.cardForm = row.cardForm
        this.orderId = row.orderId
				this.deliveryFlag = true
			},
			cancelModal() {
				this.$refs["formValidate"].resetFields();
				this.file = null;
				this.deliveryFlag = false
        this.formValidate2.a2zRuleId = ''
        this.formValidate2.groupId = ''
        this.formValidate2.imsiFee = ''
        this.$refs["formValidate2"].resetFields();
        this.orderModal = false
			},
			//发货 提交
			submit() {
				this.$refs["formValidate"].validate((valid) => {
					if (valid) {
						this.submitLoading = true
						let formData = new FormData()
						formData.append('id', this.orderId)
						formData.append('iccidFile', this.file)
						formData.append('smsTemplate', this.formValidate.smsTemplate)
						formData.append('language', this.formValidate.language)
						formData.append('singaporeCard', this.formValidate.singaporeCard)
						formData.append('logisticCompany', this.formValidate.logisticCompany)
						formData.append('logistic', this.formValidate.logistic)
						formData.append('orderBatch', this.formValidate.orderBatch)
						shippingMethods(formData).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: "操作提醒",
									desc: "操作成功"
								})
								this.submitLoading = false
								this.uploadList = []
								this.cancelModal()
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.error(err)
							this.submitLoading = false
							this.goPageFirst(1)
						}).finally(() => {
						})
					}
				});
			},
			//文件上传
			fileSuccess(response, file, fileList) {
				this.message = "请先下载模板文件，并按格式填写后上传"
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: "错误提示",
						desc: "上传失败!"
					});
				}, 3000);
			},
			handleBeforeUpload(file, fileList) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: "文件格式不正确",
						desc: "文件 " + file.name + " 格式不正确，请上传.csv格式文件"
					})
				} else {
					this.file = file
					this.uploadList = fileList
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = "文件上传中、待进度条消失后再操作"
			},
			removeFile() {
				this.file = ''
			},
			//模板下载
			downloadTemplate: function() {
				this.$refs.modelTable.exportCsv({
					filename: "ICCID列表",
					columns: this.modelColumns,
					data: this.modelData
				})
			},

			/*——————————————————————— 初始化信息 ————————————————————————*/
			//获取渠道商
			getCorpList() {
				getCorpList({
					"type": 1,
					"status": 1,
					"checkStatus": 2
				}).then(res => {
					if (res && res.code == '0000') {
						this.corpList = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
      // 获取短信模板
      getChannelSmsTemplate(corpId, cooperationMode) {
      	getChannelSmsTemplate({
          corpId: corpId,
          cooperationMode: cooperationMode
        }).then(res => {
      		if (res.code === '0000') {
      			this.smsAll = res.data;
      		}
      	}).catch((err) => {
      		console.log(err)
      	}).finally(() => {

      	})
      },
      // 获取关联信息
      getInfoOrder(corpId,cooperationMode) {
      	InfoOrder({
          corpId: corpId,
          cooperationMode: cooperationMode
        }).then(res => {
      		if (res.code === '0000') {

            // 直接将对象转换为数组
            this.a2zRuleList = Object.entries(res.data.chargings).map(([charging, chargingName]) => ({
              charging,
              chargingName
            }));

            // 将groupIds对象转换为数组
            this.groupIdList = Object.entries(res.data.groupIds).map(([groupId, groupName]) => ({
              groupId,
              groupName
            }));

            this.imsiFeeList = Object.entries(res.data.freeImsiList).map(([imsi, imsiName]) => ({
              imsi,
              imsiName
            }));
            if (this.a2zRuleList.length == 1) {
             this.formValidate2.a2zRuleId = this.a2zRuleList[0].charging
            }
            if (this.allowCreatePackage == '1' && this.groupIdList.length == 1) {
              this.formValidate2.groupId = this.groupIdList[0].groupId
            }
            if (this.cardForm == '4' && this.imsiFeeList.length == 1) {
             this.formValidate2.imsiFee = this.imsiFeeList[0].imsi
            }
      		} else {
            throw res
          }
      	}).catch((err) => {
      		console.log(err)
      	}).finally(() => {
          this.spinShow = false
				})
      },
		}
	}
</script>

<style scoped="scoped">
	.search_head_i {
		margin-top: 25px;
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		padding: 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 110px;
	}
</style>
