<template>
	<Card style="width: 100%; padiing: 16px">
		<div style="padding: 0 16px">
			<Form ref="searchObj" :model="searchObj" class="box">
				<FormItem>
					<Input v-model='searchObj.iccid' placeholder="请输入ICCID" clearable class="recordBtnSty" />
				</FormItem>
				<FormItem>
					<Input v-model='searchObj.orderUniqueId' placeholder="请输入订单ID" clearable class="recordBtnSty" />
				</FormItem>
				<FormItem>
					<DatePicker type="month" format="yyyy-MM" placeholder="选择月份" clearable @on-change="selectTime"
						class="recordBtnSty"></DatePicker>
				</FormItem>
				<FormItem>
					<Select v-model="searchObj.orderStatus" placeholder="选择订单状态" clearable class="recordBtnSty">
						<Option v-for="item in orderStatusList" :value="item.value" :key="item.value">
							{{ item.label }}
						</Option>
					</Select>
				</FormItem>
				<FormItem>
					<Select v-model="searchObj.corpId" placeholder="选择渠道商" filterable @on-change="selectChannel" clearable class="recordBtnSty">
						<Option v-for="item in corpList" :value="item.corpId" :key="item.corpId">{{ item.corpName }}
						</Option>
					</Select>
				</FormItem>
				<FormItem>
					<Button type="primary" v-has="'search'" icon="md-search" @click="getPageFrist(0)" :loading="searchLoading">&nbsp;查询</Button>
				</FormItem>
				<FormItem>
					<Button type="success"v-has="'orderRecord'" @click="exportOrderRecord" >&nbsp;导出订单记录</Button>
				</FormItem>
			</Form>
			<div style="margin: 20px 0">
				<Table :columns="Columns" :data="tableData" :ellipsis="true" :loading="tableLoading">
					<template slot-scope="{ row, index }" slot="orderInfo">
						<a style="color: #55aaff" @click="showOrderInfo(row.orderId)">查看详情</a>
					</template>

					<template slot-scope="{ row, index }" slot="action">
						<Button size="small" v-has="'unsubscribe'" v-if="(row.orderStatus == '2' && row.orderType == '2') ||
							((row.orderStatus == '1' || row.orderStatus == '2') && row.orderType == '3') ||
							(row.orderStatus == '2' && row.orderType == '7')" type="error" @click="unsubscribeAll(row)"
							class="actionstyle">退订</Button>

						<Button type="info" v-has="'check'" v-if="row.orderStatus == '4'" size="small" class="actionstyle"
							@click="examine(row.orderId, '2')">通过</Button>

						<Button type="primary" v-has="'check'" v-if="row.orderStatus == '4'" size="small" class="actionstyle"
							@click="examine(row.orderId, '3')">不通过</Button>
					</template>
				</Table>
				<Page :total="total" :page-size="pageSize" :current.sync="page" show-total show-elevator
					@on-change="getPageFrist" style="margin: 15px 0" />
			</div>
		</div>
		
		<!-- 子单详情弹出框-->
		<Modal title="子订单详情记录" v-model="packageRecordDetailsFlage" :footer-hide="true" :mask-closable="false"
			width="1200px">
			<div style="margin: 20px 0">
				<Table :columns="packageDetailsColumns" :data="packageDetailsTableData" :ellipsis="true"
					:loading="packageTableDetailsLoading">
					<template slot-scope="{ row, index }" slot="action1">
						<Button size="small" v-has="'unsubscribe'"
							v-if="(row.orderStatus == '2' && row.orderType == '2') ||
							((row.orderStatus == '1' || row.orderStatus == '2') && row.orderType == '3') 
							&& row.orderType != '7'
							"type="error" @click="unsubscribeModal(row.id)">退订</Button>

						<Button type="info" v-has="'check'" v-if="row.orderStatus == '4'&& row.orderType != '7'" size="small"
							style="margin-right: 5px" @click="examineModal(row.id, '2')">通过</Button>

						<Button type="primary" v-has="'check'" v-if="row.orderStatus == '4'&& row.orderType != '7'" size="small"
							style="margin-right: 5px" @click="examineModal(row.id, '3')">不通过</Button>
					</template>
				</Table>
				<Page :total="packageDetailsTotal" :page-size="packageDetailsPageSize"
					:current.sync="packageDetailsPage" show-total show-elevator @on-change="getPurchaseRecordsDetails"
					style="margin: 15px 0" />
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		getOrderList,
		downLoadData,
		unsubscribeBatch,
		examineOrderBatch,
		getDetailsList,
		unsubscribe,
		examineOrder,
		getCorpList
	} from "@/api/product/channelOrderManager";
	export default {
		components: {},
		data() {
			return {
				tableLoading: false,
				searchLoading: false,
				packageTableDetailsLoading: false,
				packageRecordDetailsFlage: false,
				searchObj: {
					iccid: "",
					orderUniqueId: "",
					searchMonth: "", //月份
					orderStatus: "", //订单状态
					corpId: "", //渠道商
				},
				orderStatusList: [{
						label: "待发货",
						value: "1",
					},
					{
						label: "已完成",
						value: "2",
					},
					{
						label: "已退订",
						value: "3",
					},
					{
						label: "激活退订待审批",
						value: "4",
					},
					{
						label: "部分退订",
						value: "5",
					},
					{
						label: "部分发货",
						value: "6",
					},
					{
						label: "已回收",
						value: "7",
					},
					{
						label: "部分回收",
						value: "8",
					},
					{
						label: "复合状态",
						value: "9",
					},
				],
				tableData: [],
				Columns: [],
				packageDetailsTableData: [],
				packageDetailsColumns: [{
						title: "订单编号",
						key: "id",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "ICCID",
						key: "iccid",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "购买套餐",
						key: "nameEn",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "订单状态",
						key: "orderStatus",
						minWidth: 150,
						align: "center",
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.orderStatus) {
								case "1":
									text = "待发货";
									break;
								case "2":
									text = "已完成";
									break;
								case "3":
									text = "已退订/已回滚";
									break;
								case "4":
									text = "激活退订待审批";
									break;
								case "5":
									text = "已回收";
									break;
								default:
									text = "未知状态";
							}
							return h("label", text);
						},
					},
				
					{
						title: "金额",
						key: "amount",
						align: "center",
						minWidth: 100,
						tooltip: true,
						render: (h, params) => {
							return h("span", params.row.amount);
						},
					},
					{
						title: "币种",
						key: "currencyCode",
						align: "center",
						minWidth: 100,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							//156 CNY,840 美元, 344 港币
							const text =
								row.currencyCode == "156" ?
								"人民币" :
								row.currencyCode == "840" ?
								"美元" :
								row.currencyCode == "344" ?
								"港币" :
								"获取失败";
							return h("label", text);
						},
					},
					{
						title: "操作",
						slot: "action1",
						width: 200,
						fixed: "right",
						align: "center",
					},
				],
				corpList: [],
				total: 0,
				pageSize: 10,
				page: 1,
				packageDetailsTotal: 0,
				packageDetailsPageSize: 10,
				packageDetailsPage: 1,
			}
		},
		methods: {
			//加载表列信息
			loadColumns() {
				// zh-CN  en-US
				let lang = this.$i18n.locale;
				let Obj = {
					"zh-CN": "packageName",
					"en-US": 'nameEn'
				}
				var defaultColumns = [{
						title: "订单详情",
						width: 100,
						slot: "orderInfo",
						align: "center",
					},
					{
						title: "渠道商",
						key: "user",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "订单日期",
						key: "orderDate",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "订单编号",
						key: "orderUniqueId",
						align: "center",
						minWidth: 170,
						tooltip: true,
					},
					{
						title: "ICCID",
						key: "iccid",
						align: "center",
						minWidth: 170,
						tooltip: true,
					},
					{
						title: "购买套餐",
						key: Obj[lang],
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "购买渠道",
						key: "orderChannel",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "订单状态",
						key: "orderStatus",
						align: "center",
						minWidth: 150,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.orderStatus) {
								case "1":
									text = "待发货";
									break;
								case "2":
									text = "已完成";
									break;
								case "3":
									text = "已退订";
									break;
								case "4":
									text = "激活退订待审批";
									break;
								case "5":
									text = "部分退订";
									break;
								case "6":
									text = "部分发货";
									break;
								case "7":
									text = "已回收";
									break;
								case "8":
									text = "部分回收";
									break;
								case "9":
									text = "复合状态";
									break;
								default:
									text = "未知状态";
							}
							return h("label", text);
						},
					},
					{
						title: "购买份数",
						key: "count",
						align: "center",
						minWidth: 150,
						tooltip: true,
					},
					{
						title: "金额",
						key: "amount",
						align: "center",
						minWidth: 100,
						tooltip: true,
						render: (h, params) => {
							return h("span", params.row.amount);
						},
					},
					{
						title: "币种",
						key: "currencyCode",
						align: "center",
						minWidth: 100,
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							//156 CNY,840 美元, 344 港币
							const text =
								row.currencyCode == "156" ?
								"人民币" :
								row.currencyCode == "840" ?
								"美元" :
								row.currencyCode == "344" ?
								"港币" :
								"--";
							return h("label", text);
						},
					},
					{
						title: "操作",
						slot: "action",
						width: 200,
						fixed: "right",
						align: "center",
					},
				];

				this.Columns = defaultColumns;
			},
			getPageFrist(page) {
				this.page = page
				if (page === 0) {
					this.page = 1;
				}
				if (this.searchObj.corpId || this.searchObj.iccid || this.searchObj.orderUniqueId || 
					this.searchObj.orderStatus || this.searchObj.searchMonth) {
					this.tableLoading = true;
					this.searchLoading = true;
					getOrderList({
						orderUserId: this.searchObj.corpId,//渠道商
						pageNumber: page,
						pageSize: 10,
						iccid: this.searchObj.iccid,
						orderUniqueId: this.searchObj.orderUniqueId,
						orderStatus: this.searchObj.orderStatus, //订单状态
						startTime: this.searchObj.searchMonth, 
					}).then((res) => {
						if (res.code === "0000") {
							this.tableData = res.data.records;
							this.total = res.data.totalCount;
						} else {
							throw res
						}
						this.searchLoading = false;
						this.tableLoading = false;
					}).catch((err) => {
						this.searchLoading = false;
						this.tableLoading = false;
					});
				} else {
					this.$Notice.warning({
						title: '操作提示',
						desc: '至少选择一项搜索条件！'
					});
					this.tableData = [];
					this.total = 0;
					return;
				}			
			},
			// 选择月份
			selectTime(date) {
				this.searchObj.searchMonth = date;
			},
			// 选择渠道商
			selectChannel(){
				getCorpList().then(res => {
					if (res.code == '0000') {
						this.corpList = res.data
					}
				}).catch((err) => {
					console.error(err)
				})
			},
			//总单退订
			unsubscribeAll(row) {
				let id = row.orderId
				this.$Modal.confirm({
					title: "确认全部退订？",
					onOk: () => {
						unsubscribeBatch(id)
							.then((res) => {
								if (res && res.code == "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.getPageFrist(0);
								} else {
									throw res;
								}
							}).catch((err) => {});
					},
				});
			},
			//总订单审核
			examine(id, type) {
				this.$Modal.confirm({
					title: type == "2" ? "确认执行通过操作？" : "确认执行不通过操作？",
					onOk: () => {
						examineOrderBatch({
								id: id,
								status: type,
							})
							.then((res) => {
								if (res && res.code == "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.getPageFrist(this.page);
								} else {
									throw res;
								}
							})
							.catch((err) => {});
					},
				});
			},
			// 导出订单记录
			exportOrderRecord() {
				if (this.searchObj.corpId || this.searchObj.iccid || this.searchObj.orderUniqueId ||
					this.searchObj.orderStatus || this.searchObj.searchMonth) {
					downLoadData({
						startTime: this.searchObj.searchMonth,
						orderUserId: this.searchObj.corpId,
						iccid: this.searchObj.iccid,
						orderUniqueId: this.searchObj.orderUniqueId,
						orderStatus: this.searchObj.orderStatus, //订单状态
						pageNumber: -1,
						pageSize: -1,
					}).then((res) => {
						if (res) {
							this.fileDownload("订单记录", "xlsx", res.data);
						}
					});
				} else {
					this.$Notice.warning({
						title: '操作提示',
						desc: '至少选择一项搜索条件进行导出！'
					});
					return;
				}
			},
			// 下载文件
			fileDownload(name, extra, data) {
				const content = data;
				const fileName = name + "." + extra; // 导出文件名
				if ("download" in document.createElement("a")) {
					// 支持a标签download的浏览器
					const link = document.createElement("a"); // 创建a标签
					let url = URL.createObjectURL(content);
					link.download = fileName;
					link.href = url;
					link.click(); // 执行下载
					URL.revokeObjectURL(url); // 释放url
				} else {
					// 其他浏览器
					navigator.msSaveBlob(content, fileName);
				}
				this.$Notice.success({
					title: "操作提示",
					desc: "操作成功",
				});
			},
			//子订单详情
			showOrderInfo(id) {
				this.packageRecordDetailsFlage = true;
				this.recordDetailId = id;
				this.getPurchaseRecordsDetails(0, id);
			},
			// 子订单列表
			getPurchaseRecordsDetails(page, id) {
				if (page === 0) {
					this.packageDetailsPage = 1;
				}
				this.packageTableDetailsLoading = true;			
				getDetailsList({
					orderId: id || this.recordDetailId,
					pageNumber: page,
					pageSize: 10,
				}).then((res) => {
					if (res.code === "0000") {
						this.packageDetailsTableData = res.data.records;
						this.packageDetailsTotal = res.data.totalCount;
					}
					this.packageTableDetailsLoading = false;
					this.packageRecordDetailsFlage = true;
				});
			
				//根据所选月份进行条件查询
			},
			//子订单退订
			unsubscribeModal(id) {
				this.$Modal.confirm({
					title: "确认退订？",
					onOk: () => {
						unsubscribe(id)
							.then((res) => {
								if (res && res.code == "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
			
									if (this.packageDetailsTableData.length === 1) {
										this.packageDetailsTableData = [];
										this.packageRecordDetailsFlage = false;
										this.getPageFrist(0);
									} else {
										this.getPurchaseRecordsDetails(0, this.recordDetailId);
									}
			
			
								} else {
									throw res;
								}
							})
							.catch((err) => {});
					},
				});
			},
			// 子订单审核
			examineModal(id, type) {
				this.$Modal.confirm({
					title: type == "2" ? "确认执行通过操作？" : "确认执行不通过操作？",
					onOk: () => {
						examineOrder({
								id: id,
								status: type,
							})
							.then((res) => {
								if (res && res.code == "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.getPurchaseRecordsDetails(0);
								} else {
									throw res;
								}
							})
							.catch((err) => {});
					},
				});
			},
		},
		mounted() {
			this.loadColumns()
			this.selectChannel()
		}
	}
</script>

<style>
	.box {
		display: flex;
		justify-content: space-around;
		flex-wrap: wrap;
	}

	.recordBtnSty {
		width: 200px;
	}

	.butbox {
		display: flex;
		align-items: center;
		justify-content: space-evenly;
		flex-wrap: nowrap;
	}

	.actionstyle {
		margin-right: 5px
	}
</style>
