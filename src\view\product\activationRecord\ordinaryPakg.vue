<template>

	<!--  全球卡普通套餐  -->
	<div>
		<Card>
			<div class="search_head">
				<DatePicker v-model="timeRangeArray" v-has="'search'" @on-change="handleDateChange" :editable="false" type="daterange"
				 placeholder="选择时间段" clearable style="width: 200px ;margin: 0 10px 0 0;" @on-clear="hanldeDateClear"></DatePicker>
				<Button v-has="'search'" type="primary" icon="md-search" @click="searchByCondition()" style="margin-right: 10px;"
				 :loading="loading">搜索</Button>
				<Button v-has="'export'" type="success" :loading="downloading" icon="ios-download" @click="downLoad()">导出</Button>
			</div>

			<div style="margin-top:20px">
				<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading"></Table>
			</div>

			<div class="table-botton" style="margin-top:15px">
				<Page :total="total" :current.sync="page" show-total show-elevator @on-change="goPage" />
			</div>

		</Card>

	</div>
</template>

<script>
	import {
		searchOrdinaryList,
		exportOrdinary
	} from '@/api/product/activeStat'

	export default {
		data() {
			return {
				downloading: false,
				columns: [{
						title: '卡号',
						key: 'imsi',
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						align: 'center'
					},
					{
						title: '套餐名称',
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						key: 'packageName',
						align: 'center'
					},
					{
						title: '套餐ID',
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						key: 'packageId',
						align: 'center'
					},
					{
						title: '订购时间',
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						key: 'orderDate',
						align: 'center'
					},
					{
						title: '激活时间',
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						key: 'activeTime',
						align: 'center'
					},
					{
						title: '过期时间',
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						key: 'expireTime',
						align: 'center'
					},
					{
						title: '套餐激活国家',
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						key: 'mcc',
						align: 'center'
					},
					{
						title: '套餐激活方式',
						key: 'activeType',
						align: 'center',
						// render: (h, params) => {
						//   const row = params.row
						//   const color = row.activeCategory == 1 ? '#19be6b' : row.status == 2 ? '#ff0000' : '#515a6e'
						//   const text = row.activeCategory == 1 ? 'H' : row.status == 2 ? 'v' : ''
						//   return h('label', {
						//     style: {
						//       color: color
						//     }
						//   }, text)
						// }
					},
					{
						title: '使用的VIMSI',
						key: 'firstActiveNumber',
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						align: 'center'
					}
				],
				tableData: [],
				loading: false,
				currentPage: 1,
				page: 1,
				total: 0,
				timeRangeArray: [],
				searchBeginTime: '',
				searchEndTime: '',
			}
		},
		computed: {

		},
		methods: {
			// 获取列表
			goPageFirst: function(page) {
				this.page = page
				this.loading = true
				var form = {
					page: page,
					pageSize: 10,
					startDate: this.searchBeginTime,
					endDate: this.searchEndTime,
				}
				searchOrdinaryList(form).then(res => {
					if (res && res.code == '0000') {
						this.tableData = res.data
						this.total = res.count
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					this.loading = false
				})
			},
			hanldeDateClear() {
				this.searchBeginTime = ''
				this.searchEndTime = ''
			},
			handleDateChange(dateArr) {
				let beginDate = this.timeRangeArray[0] || ''
				let endDate = this.timeRangeArray[1] || ''
				if (beginDate == '' || endDate == '') {
					return
				}
				[this.searchBeginTime, this.searchEndTime] = dateArr
			},
			//操作栏导出
			downLoad(name) {
				this.downloading = true
				exportOrdinary({
					startDate: this.searchBeginTime,
					endDate: this.searchEndTime,
					page: 1,
					pageSize: -1,
				}).then(res => {
					const content = res.data
					let fileName = '全球卡普通套餐统计.csv'
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = document.createElement('a') // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content, fileName)
					}
					this.downloading = false
				}).catch(err => {
					this.downloading = false
				})
			},
			searchByCondition: function() {
				this.goPageFirst(1)
			},
			// 分页跳转
			goPage(page) {
				this.goPageFirst(page)
			}
		},
		mounted() {
			this.goPageFirst(1)
		},
		watch: {}
	}
</script>
<style>
	.search_head {
		width: 100%;
		display: flex;
		text-align: center;
		align-items: center;
		justify-content: flex-start;
	}
</style>
