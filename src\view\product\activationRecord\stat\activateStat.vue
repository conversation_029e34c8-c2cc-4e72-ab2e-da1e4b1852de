<template>
  <!--  激活统计  -->
  <div>
    <Card>
      <div class="search_head" >
		<Form ref="formInline" :label-width="90" :model="formInline" :rules="ruleInline" inline>
		  <FormItem label="查询时间段:"  prop="timeRangeArray" >
			  <DatePicker v-model="formInline.timeRangeArray" v-has="'search'" @on-change="handleDateChange" :editable="false" type="daterange" placeholder="选择时间段"
				clearable style="width: 200px ;margin: 0 10px 0 0;" @on-clear="hanldeDateClear"></DatePicker>
		  </FormItem>
		  <FormItem label="查询地区名称:"  prop="localName">
			  <Input clearable v-has="'search'" v-model="formInline.localName" placeholder="输入地区名称..." style="width: 200px ;margin-right: 10px;" />
			  <Button v-has="'search'" type="primary" icon="md-search" @click="searchByCondition('formInline')"
			    style="margin-right: 10px;" :loading="loading">搜索</Button>
				<Button v-has="'export'" type="success" :loading="downloading" icon="ios-download"
					@click="downLoad('formInline')">导出</Button>
		  </FormItem>
		</Form>
      </div>
      <div >
        <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading"></Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="total" :current.sync="page" show-total show-elevator @on-change="goPage" />
      </div>
    </Card>
  </div>
</template>

<script>
  import {
    searchActiStatList,
	exportActiveStat
  } from '@/api/product/activeStat'

  export default {
    data() {
      return {
		formInline: {
		  timeRangeArray: [],
		  localName: ''
		},
		ruleInline: {
			timeRangeArray: [
				{ type: 'array',required: true, message: '请选择时间', trigger: 'blur',
					fields: {
						0: {type: 'date', required: true, message: '请选择开始日期'},
						1: {type: 'date', required: true, message: '请选择结束日期'}
					}
				}
			],
		},
        downloading: false,
        columns: [{
            title: '开始时间',
            key: 'startDate',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          },
          {
            title: '结束时间',
            key: 'endDate',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          },
          {
            title: '地区',
            key: 'mccName',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          },
          {
            title: '销量',
            key: 'countNum',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          }
        ],
        tableData: [],
        loading: false,
        page: 1,
        total: 0,
		searchBeginTime: '',
		searchEndTime: '',
      }
    },
    computed: {

    },
    methods: {
      // 获取列表
      goPageFirst: function(page) {
        this.page = page
        this.loading = true
        var data = {
          page: page,
          pageSize: 10,
          // localName: this.localName.replace(/\s/g, ''),
		  mccName: this.formInline.localName,
          startDate: this.searchBeginTime,
          endDate: this.searchEndTime,
        }
        searchActiStatList(data).then(res => {
          if (res && res.code == '0000') {
            this.tableData = res.data
            this.total = res.count
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
      hanldeDateClear() {
        this.searchBeginTime = ''
        this.searchEndTime = ''
      },
      handleDateChange(dateArr) {
        let beginDate = this.formInline.timeRangeArray[0] || ''
        let endDate = this.formInline.timeRangeArray[1] || ''
        if (beginDate == '' || endDate == '') {
      	return
        }
        [this.searchBeginTime, this.searchEndTime] = dateArr
      },
      //操作栏导出
      downLoad(name) {
        this.$refs[name].validate((valid) => {
        	if (valid) {
            this.downloading = true
            exportActiveStat({
				startDate: this.searchBeginTime,
				endDate: this.searchEndTime,
				mccName: this.formInline.localName,
				page: 1,
				pageSize: -1,
            }).then(res => {
              const content =  res.data
                let fileName = '销售统计.csv'
                if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
                  const link = document.createElement('a') // 创建a标签
                  let url = URL.createObjectURL(content)
                  link.download = fileName
                  link.href = url
                  link.click() // 执行下载
                  URL.revokeObjectURL(url) // 释放url
                } else { // 其他浏览器
                  navigator.msSaveBlob(content, fileName)
                }
            this.downloading = false
              }).catch(err => {
            this.downloading = false
              })
        } else {
          this.$Message.error('参数校验不通过');
        }})
      },
      searchByCondition: function(name) {
		this.$refs[name].validate((valid) => {
		  if (valid) {
			  this.goPageFirst(1)
		  } else {
			  this.$Message.error('参数校验不通过');
		  }
		})
      },
      // 分页跳转
      goPage(page) {
        this.goPageFirst(page)
      }
    },
    mounted() {
    },
    watch: {}
  }
</script>
<style>
  .search_head {
    width: 100%;
    display: flex;
    text-align: center;
    align-items: center;
    justify-content: flex-start;
  }
</style>
