<template>
  <!--  按是否可用激活统计  -->
  <div>
    <Card>
      <div class="search_head">
		  <Form ref="formInline" :label-width="90" :model="formInline" :rules="ruleInline" inline>
		    <FormItem label="查询时间:"  prop="timeRangeArray" >
		  	  <DatePicker v-has="'search'" v-model="formInline.timeRangeArray" type="date" @on-change="getTime" clearable placeholder="选择查询时间"
		  	    style="width: 200px;margin: 0 10px 0 0;"></DatePicker>
				<Button v-has="'search'" type="primary" icon="md-search" @click="searchByCondition('formInline')"
			style="margin-right: 10px;" :loading="loading">搜索</Button>
				<Tooltip content="导出使用过仍有可用套餐卡号列表" placement="bottom">
						<Button v-has="'exportUsedAndAvailabe'" type="success" :loading="usedDownloading" icon="ios-download"
						  @click="downloadFile('1')">导出使用过仍有可用套餐卡号列表</Button>&nbsp;&nbsp;
				</Tooltip>
				<Tooltip content="导出使用过无可用套餐卡号列表" placement="bottom">
				<Button v-has="'exportUsedAndUnAvailabe'" type="success" :loading="unusedDownloading" icon="ios-download"
					@click="downloadFile('2')">导出使用过无可用套餐卡号列表</Button>
				</Tooltip>
		    </FormItem>
		  </Form>
	  </div>
      <div >
        <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
        </Table>
      </div>
    </Card>

  </div>
</template>

<script>
  import {
    searchUsedStat,
    downloadByType,
  } from '@/api/product/activeStat'
  export default {
    data() {
      return {
		formInline: {
			timeRangeArray: '',
		},
		ruleInline: {
			timeRangeArray: [
			  { required: true, message: '请选择时间', trigger: 'change', pattern: /.+/ }
			],
		},
		usedDownloading: false,
        unusedDownloading: false,
        columns: [{
            title: '时间',
            key: 'statDate',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          },
          {
            title: '使用过仍有可用套餐',
            key: 'availablePackageNum',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          },
          {
            title: '使用过但无可用套餐',
            key: 'noAvailablePackageNum',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          },
          // {
          //   title: '截止月底仍有未激活套餐用户数',
          //   slot: 'userCount',
          //   tooltip: true,
          //   tooltipTheme: 'light',
          //   tooltipMaxWidth: 600,
          //   align: 'center'
          // },
        ],
        tableData: [],
        details: {},
        loading: false,
        endTime: null,
      }
    },
    computed: {

    },
    methods: {
      // 获取列表
      goPageFirst: function() {
        this.loading = true
        var data = {
          statDate: this.endTime,
        }
        searchUsedStat(data).then(res => {
          if (res && res.code == '0000') {
            this.tableData = res.data
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
      getTime: function(times, type) {
        this.endTime = times
      },
      //导出
      downloadFile(t) { //type 1 仍有可用 2 无可用
		this.$refs['formInline'].validate((valid) => {
			if (valid) {
				if(t === '1'){
					this.usedDownloading = true
				}else if(t === '2'){
					this.unusedDownloading = true
				}else{
					return
				}
			  downloadByType({
			    statDate: this.endTime,
			    type: t
			  }).then(res => {
				const content =  res.data
			    // 获取当前时间
			    let date = new Date();
			    let y = date.getFullYear();
			    let m = date.getMonth() + 1;
			    let d = date.getDate();
			    // let H = Da.getHours();
			    let time = y + "-" + m + "-" + d
			    let fileName = ''
				if(t === '1'){
					fileName = '仍有可用套餐卡号列表-'+ time + '.csv'
				}else if(t === '2'){
					fileName = '无可用套餐卡号列表-'+ time + '.csv'
				}
			    if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
			      const link = document.createElement('a') // 创建a标签
			      let url = URL.createObjectURL(content)
			      link.download = fileName
			      link.href = url
			      link.click() // 执行下载
			      URL.revokeObjectURL(url) // 释放url
			    } else { // 其他浏览器
			      navigator.msSaveBlob(content, fileName)
			    }
			    this.usedDownloading = false
				this.unusedDownloading = false
			  }).catch(err => {
				  this.usedDownloading = false
				  this.unusedDownloading = false
			  })
			} else {
			  this.$Message.error('参数校验不通过');
			}
		})
      },
      //操作栏导出
      downLoad(row) {
        this.downloading = true
        downLoadUserFile({
          info:row
        }).then(res => {
          const content = res.data
          const fileName = '话单.xlsx' // 导出文件名
          if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
            const link = this.$refs.downloadLink // 创建a标签
            let url = URL.createObjectURL(content)
            link.download = fileName
            link.href = url
            link.click() // 执行下载
            URL.revokeObjectURL(url) // 释放url
          } else { // 其他浏览器
            navigator.msSaveBlob(content, fileName)
          }
          this.downloading = false
        }).catch(err => this.downloading = false)
      },


      searchByCondition: function(name) {
        this.$refs[name].validate((valid) => {
          if (valid) {
        	  this.goPageFirst()
          } else {
        	  this.$Message.error('参数校验不通过');
          }
        })
      },
    },
    mounted() {
    },
    watch: {}
  }
</script>
<style>
  .search_head {
    width: 100%;
    display: flex;
    text-align: center;
    align-items: center;
    justify-content: flex-start;
  }
</style>
