<template>
  <!--  按是否可用激活统计  -->
  <div>
    <Card>
      <div class="search_head">
		  <Form ref="formInline" :label-width="90" :model="formInline" :rules="ruleInline" inline>
		    <FormItem label="查询时间:"  prop="timeRangeArray" >
				<DatePicker v-model="formInline.timeRangeArray" v-has="'search'" type="month" @on-change="getTime" clearable placeholder="选择查询时间"
				style="width: 200px;margin: 0 10px 0 0;"></DatePicker>
				<Button v-has="'search'" type="primary" icon="md-search" @click="searchByCondition('formInline')"
				  style="margin-right: 10px;" :loading="loading">搜索</Button>
			  <Button v-has="'export'" type="success" :loading="downloading" icon="ios-download"
				@click="downLoad">导出</Button>
		    </FormItem>
		  </Form>
      </div>
      <div >
        <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
        </Table>
      </div>
    </Card>

  </div>
</template>

<script>
  import {
    searchUnactiveStat,
    exportUnactiveStat,
  } from '@/api/product/activeStat'
  export default {
    data() {
      return {
		formInline: {
			timeRangeArray: '',
		},
		ruleInline: {
			timeRangeArray: [
			  { required: true, message: '请选择时间', trigger: 'change', pattern: /.+/ }
			],
		},
        downloading: false,
        columns: [{
            title: '时间',
            key: 'statTime',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          },
          {
            title: '截止月底仍有未激活套餐用户数',
            key: 'countNum',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          },
        ],
        tableData: [],
        details: {},
        loading: false,
        page: 1,
        endTime: null,
        total: 0
      }
    },
    computed: {

    },
    methods: {
      // 获取列表
      goPageFirst: function(page) {
        this.loading = true
        var data = {
          statDate: this.endTime,
        }
        searchUnactiveStat(data).then(res => {
          if (res && res.code == '0000') {
            this.tableData = res.data
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
      getTime: function(times, type) {
        this.endTime = times
      },
      //操作栏导出
      downLoad() {
        this.$refs['formInline'].validate((valid) => {
        	if (valid) {
            this.downloading = true
            exportUnactiveStat({
              statDate: this.endTime,
            }).then(res => {
              const content =  res.data
                // 获取当前时间
                let date = new Date();
                let y = date.getFullYear();
                let m = date.getMonth() + 1;
                let d = date.getDate();
                // let H = Da.getHours();
                let time = y + "-" + m + "-" + d
                let fileName = '未激活套餐统计-'+ time + '.csv'
                if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
                  const link = document.createElement('a') // 创建a标签
                  let url = URL.createObjectURL(content)
                  link.download = fileName
                  link.href = url
                  link.click() // 执行下载
                  URL.revokeObjectURL(url) // 释放url
                } else { // 其他浏览器
                  navigator.msSaveBlob(content, fileName)
                }
          this.downloading = false
              }).catch(err => {
          this.downloading = false
              })
        } else {
          this.$Message.error('参数校验不通过');
        }})
      },
      searchByCondition: function(name) {
        this.$refs[name].validate((valid) => {
          if (valid) {
        	  this.goPageFirst(1)
          } else {
        	  this.$Message.error('参数校验不通过');
          }
        })
      },
      // 分页跳转
      goPage(page) {
        this.goPageFirst(page)
      }
    },
    mounted() {
    },
    watch: {}
  }
</script>
<style>
  .search_head {
    width: 100%;
    display: flex;
    text-align: center;
    align-items: center;
    justify-content: flex-start;
  }
</style>
