<template>
	<!--  库存管理  -->
	<div>
		<Card>
			<div class="search_head">
				<span class="input_notice">仓库名称：</span>&nbsp;&nbsp;
				<Input clearable v-has="'search'" v-model="warehouse" placeholder="输入仓库名称..." style="width: 200px ;margin-right: 10px;" />
				<Button v-has="'search'" :loading="searchLoading" icon="md-search" type="primary" @click="searchByCondition()" style="margin-right: 10px;">搜索</Button>
				<Button v-has="'add'" icon="md-add" type="success" @click="showAddModal(null,'add')" style="margin-right: 10px;">新增仓库</Button>
				<Button v-has="'cardTransfer'" icon="md-card" type="info" @click="showTransfersModal()">卡片调拨</Button>
			</div>
			<div style="margin-top:20px">
				<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row, index }" slot="action">
						<Button type="text" style="color: #2F54EB;" v-has="'update'" size="small" @click="showAddModal(row,'update')">修改</Button>
					</template>
					<template slot-scope="{ row, index }" slot="downloadAction">
						<row style="padding: 5px 5px">
							<Col span="4">
							<Button :loading="row.exAllLoading" type="text" style="color: #2F54EB;" v-has="'exportAll'" size="small" @click="downLoadFile(row,'all')">总库存</Button>
							</Col>
							<Col span="5">
							<Button :loading="row.exEntityLoading" type="text" style="color: #2F54EB;" v-has="'exportEntity'" size="small" @click="downLoadFile(row,'entity')">实体卡库存</Button>
							</Col>
							<Col span="5">
							<Button :loading="row.exFilmLoading" type="text" style="color: #2F54EB;" v-has="'exportFilm'" size="small" @click="downLoadFile(row,'film')">贴膜卡库存</Button>
							</Col>
							<Col span="5">
							<Button :loading="row.exEsimLoading" type="text" style="color: #2F54EB;" v-has="'exportEsim'" size="small" @click="downLoadFile(row,'esim')">ESIM卡库存</Button>
							</Col>
							<Col span="5">
							<Button :loading="imsiLoading" type="text" style="color: #2F54EB;" v-has="'exportImsi'" size="small" 
							@click="downLoadFile(row,'imsi')">IMSI号库存</Button>
							</Col>
						</row>
					</template>
				</Table>
			</div>
			<div class="table-botton" style="margin-top:15px">
				<Page :total="total" :current.sync="page" show-total show-elevator @on-change="goPage" />
			</div>
		</Card>
		<!-- 卡片调拨 -->
		<Modal title="卡片调拨" v-model="transfersModal" :mask-closable="false" @on-cancel="cancelUpload">
			<Upload type="drag" :action="uploadUrl" :on-success="fileSuccess" :before-upload="handleBeforeUpload" :on-progress="fileUploading">
				<div style="padding: 20px 0">
					<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
					<p>点击或拖拽号码文件上传</p>
				</div>
			</Upload>
			<ul class="ivu-upload-list" v-if="file">
				<li class="ivu-upload-list-file ivu-upload-list-file-finish">
					<span><i class="ivu-icon ivu-icon-ios-stats"></i>{{file.name}}</span>
					<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
				</li>
			</ul>
			<div style="width: 100%;padding: 10px 0;">
				<Button type="primary" :loading="downloading" icon="ios-download" @click="downloadTempFile">下载模板文件</Button>
				<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
			</div>
			<div style="width: 100%;margin-top: 10px;">
				<Form v-if="transfersModal" ref="uploadForm" :model="modalData" :rules="rules" @keydown.enter.native="handleUpload">
					<FormItem prop="intoInventory">
						<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
							<span style="width: 2%;color: red;">*</span><span style="width: 30%;">选择转入仓库：</span>
							<Select filterable v-model="modalData.intoInventory" :clearable="true" placeholder="选择转入仓库..." style="">
								<Option v-for="item in inventoryList" :value="item.storeId" :key="item.storeId">{{ item.storeName }}</Option>
							</Select>
						</div>
					</FormItem>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelUpload">取消</Button>
				<Button type="primary" :loading="uploading" @click="handleUpload">确定</Button>
			</div>
			<a ref="downloadLink" style="display: none"></a>
		</Modal>

		<Modal :title="title" v-model="addModal" :mask-closable="false" @on-cancel="cancelModal">
			<Form v-if="addModal" ref="editForm" :model="addModalData" :rules="addRules" >
				<FormItem prop="storeName">
					<div class="input_modal">
						<span style="width: 2%;color: red;">*</span><span style="width: 20%;">仓库名称：</span>
						<Input v-model="addModalData.storeName" placeholder="请输入仓库名称..." style="width: 80%;" />
					</div>
				</FormItem>
				<FormItem prop="mccList">
					<div class="input_modal">
						<span style="width: 2%;color: red;">*</span><span style="width: 20%;">覆盖国家/地区：</span>
						<Select filterable v-model="addModalData.mccList"  placeholder="请选择国家/地区" class="inputSty" multiple style="width: 80%;">
						  <Option v-for="item in continentList" :value="item.mcc" :key="item.id">{{ item.countryEn }}</Option>
						</Select>
					</div>
				</FormItem>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button v-if="type === 'add'" type="primary" @click="addInventory">确定</Button>
				<Button v-if="type === 'update'" type="primary" @click="editInventory">确定</Button>
			</div>
		</Modal>
		<!-- 卡片调拨模板文件table -->
		<Table
		    :columns="modelColumns"
			:data="modelData"
		    ref="modelTable"
		    v-show="false"
		></Table>

		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelExportModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
					<FormItem label="你本次导出任务ID为:">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem label="你本次导出的文件名为:">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
				</Form>
			</div>
		
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelExportModal">取消</Button>
				<Button type="primary" @click="Goto">立即前往</Button>
			</div>
		</Modal>
	</div>
</template>

<script>
	import {
		downLoadFile,
		editInventory,
		addInventory,
		getWarehouseList,
		uploadFile,
		getCountryList,
		getAllInventory,
	} from '@/api/product/inventory';
	export default {
		props: {
			intoInventoryRules: {
				default: () => {
					return [{
						required: true,
						message: '请选择调入仓库',
						trigger: 'change'
					}]
				}
			}
		},
		data() {
			var validateCountryId = (rule, value, callback) => {
				if (!value || value.length == 0) {
					callback(new Error('请选择国家/地区'));
				} else {
					callback();
				}
			};
			var validateInventoryName = (rule, value, callback) => {
				if (!value || value.length == 0) {
					callback(new Error('请设置仓库名称'));
				} else {
					callback();
				}
			};
			return {
				type: '',
				inventoryList: [],
				addModalData: {
					storeName: '',
					mccList: []
				},
				addRules: {
					mccList: [{
						type: 'array',
						validator: validateCountryId,
						required: true,
						trigger: 'blur'
					}],
					storeName: [{
						validator: validateInventoryName,
						required: true,
						trigger: 'blur'
					}]
				},
				warehouse: '', //仓库名称
				intoModal: false,
				addModal: false,
				continentList: [],
				// 列表数据
				tableData: [],
				columns: [{
						title: '仓库名称',
						key: 'storeName',
						align: 'center'
					},
					{
						title: '当前总库存量',
						key: 'totle',
						align: 'center'
					},
					{
						title: '实体卡库存',
						key: 'entity',
						align: 'center'
					},
					{
						title: '贴膜卡库存',
						key: 'padPasting',
						align: 'center'
					},
					{
						title: 'ESIM卡库存',
						key: 'esim',
						align: 'center'
					},
					{
						title: 'IMSI号库存',
						key: 'imsiNum',
						align: 'center'
					},
					{
						title: '覆盖国家',
						key: 'coverCountry',
						align: 'center',
						render: (h, params) => {
						  const row = params.row;
						  let text = row.coverCountry != '' && row.coverCountry != null ? row.coverCountry.toString() : '未知';
						  if (text.length > 8) { //进行截取列显示字数
						    text = text.substring(0, 8) + "...";
						    return h('div', [h('Tooltip', {
						        props: {
						          placement: 'bottom',
						          transfer: true //是否将弹层放置于 body 内
						        },
						        style: {
						          cursor: 'pointer',
						        }
						      },
						      [ //这个中括号表示是Tooltip标签的子标签
						        text, //表格列显示文字
						        h('label', {
						            slot: 'content',
						            style: {
						              whiteSpace: 'normal'
						            }
						          },
						          row.coverCountry.toString()
						        )
						      ])]);
						  } else {
						    text = text;
						    return h('label', text)
						  }
						}
					},
					{
						title: '操作',
						slot: 'action',
						align: 'center'
					},
					{
						title: '导出',
						width: 500,
						slot: 'downloadAction',
						align: 'center'
					}
				],
				currentPage: 1,
				page: 1,
				total: 0,
				transfersModal: false,
				file: null,
				uploadUrl: '',
				message: '文件仅支持csv格式文件,大小不能超过5MB',
				modalData: {
					intoInventory: '',
				},
				modelData:[
					{
						'iccid': '********'
					},
				],
				modelColumns: [
					{title:'iccid号码',key:'iccid'}// 列名根据需要添加
				],
				searchLoading: false,
				uploading: false,
				downloading: false,
				loading: false,
				title: '新增仓库',
				imsiLoading: false, //导出IMSI号loading
				exportModal:false,//导出弹框标识
				taskId:'',
				taskName:'',
			}
		},
		computed: {
			rules() {
				return {
					intoInventory: this.intoInventoryRules,
				}
			},
		},
		methods: {
			// 获取列表
			goPageFirst: function(page) {
				this.page = page
				this.loading = true
				var params = {
					storeName: this.warehouse,
					pageNum: page,
					pageSize: 10
				}
				getWarehouseList(params).then(res => {
					if (res && res.code == '0000') {
						this.tableData = res.data.storeInfoList
						this.total = res.data.count
					} else {
						throw res
					}
				}).catch((err) => {
					this.loading = false
					this.searchLoading = false
					console.log(err)
				}).finally(() => {
					this.loading = false
					this.searchLoading = false
				})
			},

			showTransfersModal: function() {
				this.getAllInventory()
				this.modalData = {
						intoInventory: '',
					},
				this.file = null
				this.transfersModal = true
			},
			showAddModal: function(row, type) {
				this.getLocalList()
				if (type === 'add') {
					this.type = 'add'
					this.addModalData = {
						storeName: '',
						mccList: ''
					}
				} else {
					this.type = 'update'
					this.title = '编辑仓库'
					this.addModalData = {
						storeName: row.storeName,
						mccList: row.mccList,
						storeId: row.storeId
					}
				}

				this.addModal = true
			},
			getAllInventory(){
				getAllInventory().then(res => {
					if (res && res.code == '0000') {
						this.inventoryList = res.data
					} else {
						throw res
					}
			    }).catch((err) => {
			    })
			},
			getLocalList(){
			  getCountryList().then(res => {
			    if (res && res.code == '0000') {
			      var list = res.data;
			      this.continentList = list;
			      this.continentList.sort(function(str1, str2) {
			        return str1.countryEn.localeCompare(str2.countryEn);
			      });
			    } else {
			      throw res
			    }
			  }).catch((err) => {
			  })
			},
			cancelModal: function() {
				this.addModal = false
			},
			showUploadLoadNumModal: function(row) {
				this.file = null
				this.intoModal = true
			},
			// 下载卡片调拨模板文件
			downloadTempFile() {
				this.$refs.modelTable.exportCsv({
					filename:'卡片调拨模板文件',
					// type:'xlsx',
					columns:this.modelColumns,
					data:this.modelData
				})
			},
			removeFile() {
				this.file = ''
			},
			handleBeforeUpload(file) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: '文件格式不正确',
						desc: '文件 ' + file.name + ' 格式不正确，请上传.csv格式文件。'
					})
				} else {
					if (file.size > 5 * 1024 * 1024) {
						this.$Notice.warning({
							title: '文件大小超过限制',
							desc: '文件 ' + file.name + '超过了最大限制范围5MB'
						})
					} else {
						this.file = file
					}
				}
				return false
			},
			downLoadFile: function(row,type) {
				this.$Modal.confirm({
				  title: '确认导出？',
				  onOk: () => {
					var t = ''
					var name = ''
					if( type === 'all' ){
						row.exAllLoading = true
						t = '5'
						var name = '总库存'
					}else if( type === 'entity' ){
						row.exEntityLoading = true
						t = '1'
						var name = '实体卡库存'
					}else if( type === 'film' ){
						row.exFilmLoading = true
						t = '3'
						var name = '贴膜卡库存'
					}else if( type === 'esim' ){
						row.exEsimLoading = true
						t = '2'
						var name = 'ESIM卡库存'
					}else if(type === 'imsi'){
						row.exEsimLoading = true
						t = '4'
						var name = 'IMSI卡库存'
					}
					downLoadFile({
						userId: this.$store.state.user.userId,
						storeId: row.storeId,
						cardForm: t
					}).then(res => {
						this.exportModal = true
						this.taskId = res.data.id
						this.taskName = res.data.fileName
					}).catch(err => {})
				  }
				});
			},
			
			cancelExportModal:function(){
				this.exportModal=false
			},
			//跳转至下载列表
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportModal = false
			},
			searchByCondition: function() {
				this.searchLoading = true
				this.goPageFirst(1)
			},
			//新增仓库
			addInventory: function() {
				this.$refs.editForm.validate((valid) => {
					if (valid) {
						addInventory({
							mcc: this.addModalData.mccList,
							storeName: this.addModalData.storeName
						}).then(res => {
							if (res && res.code == '0000') {
								this.success('新增仓库成功')
								this.addModal = false,
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.loading = false
						})
					}
				})
			},
			//修改仓库
			editInventory: function() {
				this.$refs.editForm.validate((valid) => {
					if (valid) {
						editInventory({
							mcc: this.addModalData.mccList,
							storeId: this.addModalData.storeId,
							storeName: this.addModalData.storeName
						}).then(res => {
							if (res && res.code == '0000') {
								this.success('修改仓库信息成功')
								this.addModal = false,
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.loading = false
						})
					}
				})
			},
			// 分页跳转
			goPage(page) {
				this.goPageFirst(page)
			},
			cancelUpload() {
				this.file = ''
				this.intoModal = false
				this.transfersModal = false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			//卡片调拨上传
			handleUpload() {
				if (!this.file) {
					this.$Message.warning('请选择需要上传的文件')
					return
				}
				this.$refs.uploadForm.validate((valid) => {
					if (valid) {
						this.uploading = true
						let formData = new FormData()
						formData.append('multipartFile', this.file)
						formData.append('storeId', this.modalData.intoInventory)
						uploadFile(formData).then(res => {
							if (res.code === '0000') {
								this.success('操作成功')
								this.cancelUpload()
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.uploading = false
						})
					}
				})
			},
			success: function(desc) {
				this.$Notice.success({
					title: '操作成功',
					desc: desc
				})
			}
		},
		mounted() {
			this.goPageFirst(1)
		},
		watch: {}
	}
</script>
<style>
	.input_notice {
	  font-size: 15px;
	  font-weight:bold;
	}
	.search_head {
	  width: 100%;
	  display: flex;
	  justify-content: flex-start;
	  align-items:center;
	  margin-bottom: 20px;
	}

	.demo-drawer-footer {
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		border-top: 1px solid #e8e8e8;
		padding: 10px 16px;
		text-align: right;
		background: #fff;
	}

	.input_modal {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		flex-wrap: nowrap;
		width: 100%;
	}

	.errorMsg {
		width: 80%;
		margin-left: 20%;
	}
</style>
