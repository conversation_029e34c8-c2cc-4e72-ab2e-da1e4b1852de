<template>
  <div>
    <Card>
      <Button type="success" v-has="'delayPackage'" @click="doDelay()" style="margin-right: 10px;">未激活套餐延期</Button>
      <div style="margin-top:20px">
        <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
          <template slot-scope="{ row, index }" slot="fileAction">
            <a @click="downLoadFile(row)"  v-has="'download'" style="margin-right: 10px;">下载文件</a>
          </template>
          <template slot-scope="{ row, index }" slot="status">
            {{row.status === '0'?'待审核':(row.status === '1'?'审核通过':'审核未通过')}}
          </template>
          <template slot-scope="{ row, index }" slot="action">
            <Button :loading="row.checkPassLoading" v-has="'check'" v-if="row.status == '1' " size="small" type="info" @click="doPass(row,'2')"
              style="margin-right: 10px;">通过</Button>
            <Button :loading="row.checkUnPassLoading" v-has="'check'" v-if="row.status == '1' " size="small" type="warning" @click="doPass(row,'3')"
              style="margin-right: 10px;">不通过</Button>
          </template>
        </Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="total" :current.sync="page" show-total show-elevator @on-change="goPage" />
      </div>
    </Card>
    <Modal title="套餐延期" v-model="delayModal" :mask-closable="false" @on-cancel="cancelUpload">
      <Upload type="drag" :action="uploadUrl" :on-success="fileSuccess" :before-upload="handleBeforeUpload"
        :on-progress="fileUploading">
        <div style="padding: 20px 0">
          <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
          <p>点击或拖拽文件上传</p>
        </div>
      </Upload>
      <ul class="ivu-upload-list" v-if="file">
        <li class="ivu-upload-list-file ivu-upload-list-file-finish">
          <span><i class="ivu-icon ivu-icon-ios-stats"></i>{{file.name}}</span>
          <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
        </li>
      </ul>
	  <div style="width: 100%;padding: 10px 0;">
		<Button type="primary" :loading="downloading" icon="ios-download" @click="downloadTempFile">下载模板文件</Button>
		<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
	  </div>
      <div style="width: 100%;margin-top: 10px;">
		  <Form ref="modalData" :model="modalData" :rules="rules" @keydown.enter.native="handleUpload">
			<FormItem label="延期时间" prop="delayTime">
				<DatePicker :options="options3" v-model="modalData.delayTime" @on-change="getDelayTime" type="date" :editable="false" show-week-numbers placeholder="选择延期时间..."
				  style="width: 85%"></DatePicker>
			</FormItem>
		  </Form>
      </div>

      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
        <Button @click="cancelUpload">取消</Button>
        <Button type="primary" :loading="uploading" @click="handleUpload">延期</Button>
      </div>
      <a ref="downloadLink" style="display: none"></a>
    </Modal>
	<!-- 套餐延期模板文件table -->
	<Table
		:columns="modelColumns"
		:data="modelData"
		ref="modelTable"
		v-show="false"
	></Table>
  </div>
</template>

<script>
  import {
    getPackageList, //查询列表
    doPass, //审核   0通过   1不通过
	packageDelay
  } from '@/api/product/delay/audit';
  export default {

    data() {
      return {
		options3: {
		  disabledDate (date) {
			  return date && date.valueOf() < (Date.now() + 1) ;
		  }
		},
        delayModal: false,
        // 列表数据
        tableData: [],
        columns: [{
            title: '延期文件',
            slot: 'fileAction',
            align: 'center'
          },
          {
            title: '延期日期',
            key: 'delayDate',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          },
          {
            title: '延期状态',
            slot: 'status',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center',
			render: (h, params) => {
			  const row = params.row
			  const color = row.status == 1 ? '#19be6b' : row.status == 2 ? '#ff0000' : row.status == 3 ? '#27A1FF' : row.status == 4 ? '#ff9900': '#515a6e'
			  const text = row.status == 1 ? '待审核' : row.status == 2 ? '通过' : row.status == 3 ? '不通过' : row.status == 4 ? '执行中' : row.status == 5 ? '延期失败' : '其他'
			  return h('label', {
			    style: {
			      color: color
			    }
			  }, text)
			}
          },

          {
            title: '审核操作',
            slot: 'action',
            align: 'center'
          },
        ],
		modalData: {
			delayTime: '',
		},
		rules: {
			delayTime: [
			  { required: true, message: '请选择延期时间',trigger: 'change', pattern: /.+/},
			],
		},
        // 表内容详情数据
        infoData: [{
            ICCID: '453543',
            IMSI: '45323',
            packageName: '新年套餐',
            createTime: '2019-01-01 00:00:00',
            expirationTime: '2022-01-01 00:00:00',
            currency: '人民币',
            amount: '400',
          },
          {
            ICCID: '23432442',
            IMSI: '434234',
            packageName: '元旦套餐',
            createTime: '2019-01-01 00:00:00',
            expirationTime: '2022-01-01 00:00:00',
            currency: '人民币',
            amount: '400',
          }
        ],
        page: 1,
        total: 0,
        loading: false,
        infoMoadl: false,

        file: null,
        uploadUrl: '',
        downloading: false,
        delayTime: '',
        uploading: false,
        message: '文件仅支持csv格式文件,大小不能超过5MB',
		modelData:[],
		modelColumns: [
			{title:'iccid号码',key:'iccid'},
			{title:'HIMSI',key:'himsi'},
			{title:'套餐ID',key:'packageId'},
			{title:'未激活套餐名称',key:'name'},
			{title:'购买时间',key:'buyTime'},
			{title:'过期时间（延期前的原过期时间）',key:'expireTime'},
			{title:'币种',key:'currency'},
			{title:'金额',key:'price'},
		],
      }
    },
    computed: {

    },
    methods: {
		// 下载卡片调拨模板文件
		downloadTempFile: function() {
			this.$refs.modelTable.exportCsv({
				filename:'套餐延期文件模板',
				// type:'xlsx',
				columns:this.modelColumns,
				data:this.modelData
			})
		},
      getDelayTime: function(e, f) {
        this.delayTime = e
      },
      doDelay: function() {
        this.file = null
        this.delayTime = ''
        this.delayModal = true
      },
      // 获取列表
      goPageFirst: function(page) {
        this.page = page
        this.loading = true
        getPackageList({
			pageNum: page,
			pageSize: 10
		}).then(res => {
          if (res && res.code == '0000') {
            this.tableData = res.data
            this.total = res.count
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
		if (this.tableData.length) {
		  this.tableData.map(item => {
		    this.$set(item, 'checkPassLoading', false)
		    this.$set(item, 'checkUnPassLoading', false)
		    return item
		  })
		}
      },
      doPass: function(row, type) {//审核   2通过   3不通过
		  this.$Modal.confirm({
			title: '确认审核？',
			onOk: () => {
				if(type === '2'){
					row.checkPassLoading = true
				}else if(type === '3'){
					row.checkUnPassLoading = true
				}else{
					return
				}
				doPass(
				  row.taskId,
				  type
				 ).then(res => {
				  if (res && res.code == '0000') {
				    this.success('审批状态已更新')
				    this.goPageFirst(this.page)
				  } else {
				    throw res
				  }
				}).catch((err) => {
				  console.log(err)
				}).finally(() => {
				  this.loading = false
				})
			}
		  });
      },
      removeFile() {
        this.file = ''
      },
      handleBeforeUpload(file) {
        if (!/^.+(\.csv)$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式不正确',
            desc: '文件 ' + file.name + ' 格式不正确，请上传.csv。'
          })
        } else {
          if (file.size > 5 * 1024 * 1024) {
            this.$Notice.warning({
              title: '文件大小超过限制',
              desc: '文件 ' + file.name + '超过了最大限制范围5MB'
            })
          } else {
            this.file = file
          }
        }
        return false
      },
      //导出
      downLoadFile: function(row) {
		const url = row.sourceFilePath
		// const url = 'http://10.1.60.109:5041/cmi/file/test.csv'
		const fileName = row.taskName + '.csv' // 导出文件名
		if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
			const link = this.$refs.downloadLink // 创建a标签
			link.download = fileName
			link.href = url
			link.click() // 执行下载
		} else { // 其他浏览器
			navigator.msSaveBlob(content, fileName)
		}
      },
      searchByCondition: function() {
        this.goPageFirst(1)
      },
      // 分页跳转
      goPage(page) {
        this.goPageFirst(page)
      },
      cancelUpload() {
        this.delayModal = false
		this.$refs['modalData'].resetFields();
      },
      fileUploading(event, file, fileList) {
        this.message = '文件上传中、待进度条消失后再操作'
      },
      fileSuccess(response, file, fileList) {
        this.message = '请先下载模板文件，并按格式填写后上传'
      },
      handleUpload() {
        if (!this.file) {
          this.$Message.warning('请选择需要上传的文件')
          return
        }
        if (!this.delayTime) {
          this.$Message.warning('请选择延期时间')
          return
        }
        this.uploading = true
        let formData = new FormData()
        formData.append('file', this.file)
        formData.append('delayDate', this.delayTime)
        packageDelay(formData).then(res => {
          if (res.code === '0000') {
            this.$Notice.success({
              title: '操作成功',
            })
          this.uploading = false
          this.cancelUpload()
          this.goPageFirst(1)
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.uploading = false
        })
      },
      success: function(desc) {
        this.$Notice.success({
          title: '操作成功',
          desc: desc
        })
      },

    },
    mounted() {
      this.goPageFirst(1)
    },
    watch: {}
  }
</script>
<style scoped>
  .search_head {
    width: 100%;
    display: flex;
    justify-content: flex-start;
  }


  .input_modal {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    width: 100%;
  }
</style>
