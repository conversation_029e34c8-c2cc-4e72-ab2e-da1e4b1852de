<template>
  <!--  未激活套餐查询  -->
  <div>
    <Card style="">
      <div style="width: 100%;display: flex;flex-wrap: nowrap;justify-content: center;">
        <div style="width: 25%; display: flex;flex-wrap: wrap;">
          <div style="width: 100%;display: flex;flex-wrap: wrap; height: 230px;">
            <Upload type="drag" :action="uploadUrl" :on-success="fileSuccess" :before-upload="handleBeforeUpload"
              :on-progress="fileUploading" style="width: 90%;">
              <div style="padding: 20px 0">
                <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
                <p>点击或拖拽文件上传</p>
              </div>
            </Upload>
            <ul class="ivu-upload-list" v-if="file">
              <li class="ivu-upload-list-file ivu-upload-list-file-finish">
                <span><i class="ivu-icon ivu-icon-ios-stats"></i>{{file.name}}</span>
                <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
              </li>
            </ul>
            <div style="margin-top: 10px; display: flex; height: 60px; justify-content: space-around; width: 90%;">
              <Tooltip content="下载查询模板" placement="bottom">
                <div style="width: 80px;display: flex;flex-wrap: wrap; align-items: center;justify-content: center;">
                  <Button v-has="'download'" style="width: 50px;height: 50px;" icon="ios-download-outline" type="warning" shape="circle"
                    @click="uploadLoadTemplate"></Button>
                </div>
              </Tooltip>
              <Tooltip content="查询未激活套餐" placement="bottom">
                <Button :loading="loading" v-has="'search'" style="width: 50px;height: 50px;" icon="ios-search" type="info" shape="circle"
                  @click="searchByCondition"></Button>
              </Tooltip>
              <!-- <Tooltip content="导出未激活套餐" placement="bottom">
                <Button :loading="exportLoading" v-has="'export'" style="width: 50px;height: 50px;" icon="ios-cloud-download-outline" type="success"
                  shape="circle" @click="downLoadOverdueFile"></Button>
              </Tooltip> -->
            </div>
            <a ref="downloadLink" style="display: none"></a>
          </div>
        </div>
        <div style="width: 75%;display: flex;flex-wrap: wrap;">
          <Badge :count="0" :overflow-count="99999" :show-zero="false" class-name="demo-badge-alone">
            <H2 style="padding: 0 0 10px 0;">未激活套餐查询结果</H2>
            <!-- <span style="margin-right: 20px;">上传：{{totalCount}}条</span><span style="color: #55aaff;">
              未激活：{{total}}条</span> -->
          </Badge>
          <div style="width: 100%;">
            <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading"
              max-height="600">
			  <template slot-scope="{ row, index }" slot="taskFileName" >
			    <a  type="primary" size="small" @click="downloadFile(row,0)">{{row.taskFileName}}</a>
			  </template>
			   <template slot-scope="{ row, index }" slot="resultFileName" v-if="row.status==='1'">
			     <a  type="primary" size="small" @click="downloadFile(row,1)">{{row.resultFileName}}</a>
			   </template>
			</Table>
			  <div class="table-botton" style="margin-top:15px">
			  	<Page :total="total" :current.sync="page" show-total show-elevator @on-change="goPage" />
			  </div>
          </div>
        </div>
      </div>
    </Card>
	
	<!-- 卡片调拨模板文件table -->
	<Table
		:columns="modelColumns"
		:data="modelData"
		ref="modelTable"
		v-show="false"
	></Table>
  </div>
</template>

<script>
  import {
    handlerUpload,
    downLoadOverdueFile,
	downloadfile,
	getFileList
  } from '@/api/product/delay/pakgDelaySearch';
  export default {
    data() {
      return {
        // 列表数据
        tableData: [],
        columns: [{
            title: '查询时间',
            key: 'createTime',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          },
          {
            title: '查询文件',
			slot: 'taskFileName',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
            align: 'center'
          },
          {
            title: '查询状态',
            key: 'status',
            align: 'center',
            tooltip: true,
            tooltipTheme: 'light',
            tooltipMaxWidth: 600,
			render: (h, params) => {
				const row = params.row;
				const text = row.status == '0' ? '查询中' : row.status == '1' ? '成功' : row.status ==
				  '2' ? '失败' : ' ';
				return h('label', text);
			  },
          },
		  {
		    title: '文件下载',
		    slot: 'resultFileName',
		    align: 'center',
			tooltip: true,
			tooltipTheme: 'light',
			tooltipMaxWidth: 600,
		  }
        ],
        loading: false,
        page: 1,
        total: 0,
        file: null,
        uploadUrl: '',
        exportLoading: false,
        uploading: false,
        message: '文件仅支持csv格式文件,大小不能超过5MB',
		modelData:[
			{
				'iccid': '********'
			},
		],
		modelColumns: [
			{title:'iccid号码',key:'iccid'}// 列名根据需要添加
		],
      }
    },
    computed: {

    },
    methods: {
		// 分页跳转
		goPage(page) {
			getFileList({
				pageNum:page,
				pageSize:10
			}).then(res => {
				if (res.code === '0000') {
					this.total = res.count
					this.tableData = res.data
					this.uploading = false
					this.loading = false
				}
			})
			// this.handlerUpload(page)
		},
		searchByCondition: function() {
			this.handlerUpload(1)
		},
      //模板下载
      uploadLoadTemplate: function() {
		this.$refs.modelTable.exportCsv({
			filename:'未激活套餐查询文件模板',
			// type:'xlsx',
			columns:this.modelColumns,
			data:this.modelData
		})
      },
      removeFile() {
        this.file = ''
      },
      handleBeforeUpload(file) {
        if (!/^.+(\.csv)$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式不正确',
            desc: '文件 ' + file.name + ' 格式不正确，请上传.csv格式文件。'
          })
        } else {
          if (file.size > 5 * 1024 * 1024) {
            this.$Notice.warning({
              title: '文件大小超过限制',
              desc: '文件 ' + file.name + '超过了最大限制范围5MB'
            })
          } else {
            this.file = file
          }
        }
        return false
      },
      exportExcel() {
        let key
        let title
        key = this.excelKey
        title = this.excelTitle
        let filename = '延期数据'
        const params = {
          title: title,
          key: key,
          data: this.tableData,
          autoWidth: true,
          filename: filename
        }
        excel.export_array_to_excel(params)
      },
      //导出过期数据
      downLoadOverdueFile: function() {
		if (!this.file) {
		  this.$Message.warning('请选择需要上传的文件')
		  return
		}
		this.exportLoading = true
		let formData = new FormData()
		formData.append('file', this.file)
        downLoadOverdueFile(formData).then(res => {
          const content = res.data
          const fileName = '未激活套餐.csv' // 导出文件名
          if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
            const link = this.$refs.downloadLink // 创建a标签
            let url = URL.createObjectURL(content)
            link.download = fileName
            link.href = url
            link.click() // 执行下载
            URL.revokeObjectURL(url) // 释放url
          } else { // 其他浏览器
            navigator.msSaveBlob(content, fileName)
          }
		  this.exportLoading = false
        }).catch(err => {this.exportLoading = false})
      },
	  // 下载文件
	  downloadFile(row,type){
		  let id =row.id
		  downloadfile(id,type).then(res => {
		  	const content = res.data
			var fileName =''
			if(type===0){
				fileName = row.taskFileName+'.csv' // 导出文件名
			}else{
				fileName = row.resultFileName+'.csv' // 导出文件名
			}
		  	if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
		  		const link = this.$refs.downloadLink // 创建a标签
		  		let url = URL.createObjectURL(content)
		  		link.download = fileName
		  		link.href = url
		  		link.click() // 执行下载
		  		URL.revokeObjectURL(url) // 释放url
		  	} else { // 其他浏览器
		  		navigator.msSaveBlob(content,fileName)
		  	}
		  })
	  },
      fileUploading(event, file, fileList) {
        this.message = '文件上传中、待进度条消失后再操作'
      },
      fileSuccess(response, file, fileList) {
        this.message = '请先下载模板文件，并按格式填写后上传'
      },
      //上传
      handlerUpload(page) {
        if (!this.file) {
          this.$Message.warning('请选择需要上传的文件')
          return
        }
		this.page = page
        this.uploading = true
        let formData = new FormData()
        formData.append('file', this.file)
		formData.append('pageNum', page)
		formData.append('pageSize', 10)
        handlerUpload(formData).then(res => {
          if (res.code === '0000') {
			this.goPage(1)
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.uploading = false
		  this.loading = false
        })
      },
      success: function(desc) {
        this.$Notice.success({
          title: '操作成功',
          desc: desc
        })
      }
    },
    mounted() {
		this.goPage(1)
	},
    watch: {}
  }
</script>
<style>
  .demo-badge-alone {
    background: #5cb85c !important;
  }

  .demo-button {
    width: 50px;
    height: 50px;
    border-radius: 50px;
    display: inline-block;
  }
</style>
