<template>
	<!--  套餐批量配置  -->
	<div>
		<Card>
			<div class="search_head_i">
				<div class="search_box">
					<span class="search_box_label">任务名称</span>
					<Input placeholder="输入任务名称" v-model="searchCondition.taskName" clearable style="width: 150px" />
				</div>
				<div class="search_box">
					<span class="search_box_label">所属渠道</span>
					<Select v-model="searchCondition.corpId" clearable filterable placeholder="选择所属渠道" style="width:200px">
						<Option :value="item.corpId" v-for="(item,index) in corpList" :key="index">{{item.corpName}}
						</Option>
					</Select>
				</div>
				<div class="search_box">
					<Button style="margin: 0 4px" v-preventReClick type="primary" @click="searchByCondition()"
						:loading="loading">
						<Icon type="ios-search" />&nbsp;搜索
					</Button>
					<Button style="margin: 0 4px" type="info" @click="showBatchModal" v-has="'addBatch'">
						<div style="display: flex;align-items: center;">
							<Icon type="md-add" />&nbsp;批量配置
						</div>
					</Button>
				</div>
			</div>
			<div style="margin-top:20px">
				<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row, index }" slot="action">
						<div>
							<Button v-if="row.taskStatus=='2' || row.taskStatus=='5' || row.taskStatus=='6'" v-has="'download'"
								style="margin-right: 5px" size="small" type="success"
								@click="downLoadFile(row.taskId,'1')">成功文件</Button>
							<Button v-else v-has="'download'" style="margin-right: 5px" disabled size="small"
								type="success">成功文件</Button>
							<Button v-if="row.taskStatus=='2' || row.taskStatus=='5' || row.taskStatus=='6'" v-has="'download'"
								style="margin-right: 5px" size="small" type="error"
								@click="downLoadFile(row.taskId,'2')">失败文件</Button>
							<Button v-else v-has="'download'" style="margin-right: 5px" disabled size="small"
								type="error">失败文件</Button>
							<Button v-if="row.taskStatus=='2' || row.taskStatus=='6'" v-has="'back'" style="margin-right: 5px" size="small"
								type="warning" @click="reback(row.taskId)">回滚</Button>
							<Button v-else v-has="'back'" disabled style="margin-right: 5px" size="small"
								type="warning">回滚</Button>
							<Button v-if="row.taskStatus=='5' || row.taskStatus=='6'" v-has="'download'" style="margin-right: 5px" size="small"
								type="success" @click="downLoadFile(row.taskId,'3')">回滚成功文件</Button>
							<Button v-else v-has="'download'" style="margin-right: 5px" disabled size="small"
								type="success">回滚成功文件</Button>
							<Button v-if="row.taskStatus=='5' || row.taskStatus=='6'" v-has="'download'" style="margin-right: 5px" size="small"
								type="error" @click="downLoadFile(row.taskId,'4')">回滚失败文件</Button>
							<Button v-else v-has="'download'" style="margin-right: 5px" disabled size="small"
								type="error">回滚失败文件</Button>
						</div>
					</template>
				</Table>
			</div>
			<div class="table-botton" style="margin-top:15px">
				<Page :total="total" :current.sync="currentPage" :page-size="pageSize" show-total show-elevator
					@on-change="goPage" style="margin: 10px 0;" />
			</div>
		</Card>

		<Modal title="批量配置" v-model="batchModal" :footer-hide="true" :mask-closable="false" @on-cancel="cancelUpload">
			<Tabs type="card" value="name1" @on-click="choseTab">
				<TabPane label="页面配置" name="name1">
					<div style="width: 100%;margin: 10px 0; margin-bottom: 58px;" v-show="tabName === 'name1'">
						<Form ref="pagFormValidate" :model="pageData" :rules="pagRuleValidate">
							<FormItem prop="taskName">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">任务名称</span>
									<Input placeholder="请输入任务名称" :maxlength="20" v-model="pageData.taskName"
										clearable />
								</div>
							</FormItem>
							<FormItem prop="iccid">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">ICCID</span>
									<Input placeholder="请输入ICCID" :maxlength="20" v-model="pageData.iccid"
										clearable />
								</div>
							</FormItem>
							<FormItem prop="quantity">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">数量</span>
									<Input placeholder="请输入数量" v-model="pageData.quantity"
										clearable />
								</div>
							</FormItem>
							<FormItem prop="packageId">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">套餐ID</span>
									<Input placeholder="请输入套餐ID" v-model="pageData.packageId" clearable/>
								</div>
							</FormItem>
							<FormItem prop="effectiveDay">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">有效期</span>
									<Input placeholder="请输入有效期" v-model="pageData.effectiveDay"
										clearable />
								</div>
							</FormItem>
							<FormItem prop="currency">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">币种</span>
									<Select v-model="pageData.currency" :clearable="true" placeholder="请选择币种">
										<Option value='人民币'>人民币</Option>
										<Option value='美元'>美元</Option>
										<Option value='港币'>港币</Option>
									</Select>
								</div>
							</FormItem>
							<FormItem prop="amount">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">金额</span>
									<Input placeholder="请输入金额" v-model="pageData.amount"
										clearable />
								</div>
							</FormItem>
							<FormItem prop="channel">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">售卖渠道</span>
									<Select v-model="pageData.channel" :clearable="true" placeholder="请选择种售卖渠道">
										<Option value='批量售卖'>批量售卖</Option>
										<Option value='推广活动'>推广活动</Option>
										<Option value='测试渠道'>测试渠道</Option>
									</Select>
								</div>
							</FormItem>
							<FormItem prop="language">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">语言</span>
									<Select v-model="pageData.language" :clearable="true" placeholder="请选择语言">
										<Option value='简体中文'>简体中文</Option>
										<Option value='繁体中文'>繁体中文</Option>
										<Option value='英文'>英文</Option>
									</Select>
								</div>
							</FormItem>
							<FormItem prop="corpId">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">渠道商</span>
									<Select v-model="pageData.corpId" :clearable="true" placeholder="请选择渠道商"
									    @on-change="getmodes"
										@on-clear="modesclear"
										:filterable="true">
										<Option :value="item.corpId" v-for="(item,index) in corpList" :key="index">{{item.corpName}}</Option></Select>
								</div>
							</FormItem>
							<FormItem prop="isSingapore">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">是否新加坡卡</span>
									<Select v-model="pageData.isSingapore" :clearable="true" placeholder="请选择是否为新加坡卡">
										<Option value="1">是</Option>
										<Option value="2">否</Option>
									</Select>
								</div>
							</FormItem>
							<FormItem prop="activeAt">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">套餐激活日期</span>
									<Input placeholder="请输入套餐激活日期" v-model="pageData.activeAt"
										clearable />
								</div>
							</FormItem>
							<FormItem prop="orderBatch">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">输入订单批次</span>
									<Input placeholder="请输入输入订单批次" :maxlength="200" v-model="pageData.orderBatch"
										clearable />
								</div>
							</FormItem>
							<FormItem prop="cooperationMode">
								<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
									<span style="width: 30%;font-weight:bold;">合作模式：</span>
									<Select v-model="pageData.cooperationMode" :clearable="true"
										placeholder="请选择合作模式" placement="top-start">
										<Option :value="item.value" v-for="(item,index) in modeList" :key="index">{{item.name}}
										</Option>
									</Select>
								</div>
							</FormItem>
              <FormItem prop="remark" :rules="isNeedCorpList.includes(pageData.corpId) ? pagRuleValidate.remark : [{required: false}]">
              	<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
              		<span style="width: 30%;font-weight:bold;">备注：</span>
              		<Input placeholder="请输入输入备注" :maxlength="200" v-model="pageData.remark"
              			clearable />
              	</div>
              </FormItem>
						</Form>
					</div>
				</TabPane>
				<TabPane label="文件配置" name="name2">
					<div v-show="tabName === 'name2'" style="padding: 10px; margin-bottom: 25px;">
						<div>
							<Upload type="drag" :action="uploadUrl" :on-success="fileSuccess"
								:before-upload="handleBeforeUpload" :on-progress="fileUploading">
								<div style="padding: 20px 0">
									<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
									<p>点击或拖拽文件上传</p>
								</div>
							</Upload>
							<ul class="ivu-upload-list" v-if="modalData.file">
								<li class="ivu-upload-list-file ivu-upload-list-file-finish">
									<span><i class="ivu-icon ivu-icon-ios-stats"></i>{{modalData.file.name}}</span>
									<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style=""
										@click="removeFile"></i>
								</li>
							</ul>
						</div>
						<div style="width: 100%;padding: 10px 0;">
							<Button type="primary" :loading="downloading" icon="ios-download"
								@click="downloadTempFile">下载模板文件</Button>
							<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
						</div>
						<div style="width: 100%;margin: 10px 0;">
							<Form ref="formValidate" :model="modalData" :rules="ruleValidate">
								<FormItem prop="taskName">
									<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
										<span style="width: 30%;font-weight:bold;">输入任务名称：</span>
										<Input placeholder="请输入任务名称" :maxlength="20" v-model="modalData.taskName"
											clearable />
									</div>
								</FormItem>
								<FormItem prop="corpId">
									<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
										<span style="width: 30%;font-weight:bold;">选择所属渠道：</span>
										<Select transfer v-model="modalData.corpId" :clearable="true" placeholder="请选择所属渠道"
											@on-change="modalgetmodes"
											@on-clear="modalmodesclear"
											:filterable="true">
											<Option :value="item.corpId" v-for="(item,index) in corpList" :key="index">{{item.corpName}}
											</Option>
										</Select>
									</div>
								</FormItem>
								<FormItem prop="isSingapore">
									<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
										<span style="width: 30%;font-weight:bold;">是否新加坡卡：</span>
										<Select v-model="modalData.isSingapore" :clearable="true"
											placeholder="请选择是否为新加坡卡">
											<Option value="1">是新加坡卡</Option>
											<Option value="2">非新加坡卡</Option>
										</Select>
									</div>
								</FormItem>
								<FormItem prop="orderBatch">
									<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
										<span style="width: 30%;font-weight:bold;">输入订单批次：</span>
										<Input placeholder="请输入输入订单批次" :maxlength="200" v-model="modalData.orderBatch"
											clearable />
									</div>
								</FormItem>
								<FormItem prop="cooperationMode">
									<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
										<span style="width: 30%;font-weight:bold;">合作模式：</span>
										<Select v-model="modalData.cooperationMode" :clearable="true"
											placeholder="请选择合作模式" placement="top-start">
											<Option :value="item.value" v-for="(item,index) in modalmodeList" :key="index">{{item.name}}
											</Option>
										</Select>
									</div>
								</FormItem>
                <FormItem prop="remark" :rules="isNeedCorpList.includes(modalData.corpId) ? ruleValidate.remark : [{required: false}]">
                	<div style="display: flex;flex-wrap: nowrap;width: 100%; align-items: center;">
                		<span style="width: 30%;font-weight:bold;">备注：</span>
                		<Input placeholder="请输入输入备注" :maxlength="200" v-model="modalData.remark"
                			clearable />
                	</div>
                </FormItem>
							</Form>
						</div>
					</div>
				</TabPane>
			</Tabs>
			<div style="text-align: center;margin: 4px 0;">
				<Button type="primary" @click="handleUpload" v-has="'addBatch'" v-preventReClick
					:loading="uploading">提交</Button>
				<Button style="margin-left: 8px" @click="cancelUpload">取消</Button>
			</div>
		</Modal>
		<!-- 卡片调拨模板文件table -->
		<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
	</div>
</template>

<script>
	import excel from '@/libs/excel';
	import {
		getTaskList,
		downloadTask,
		doReback,
		addPageTask,
		addTask,
		getCorpList,
    getIsNeedRemark,
	} from '@/api/product/package/batch';
	const math = require('mathjs');
	export default {
		data() {
			return {
				searchCondition: {
					taskName: '',
					corpId: ''
				},
				corpList: [],
        isNeedCorpList: [],
				loading: false,
				currentPage: 1,
				pageSize: 10,
				total: 0,
				columns: [],
				tableData: [],
				pageData: {
					taskName: '',
					iccid: '',
					quantity: '',
					packageId: '',
					effectiveDay: '',
					currency: '',
					amount: '',
					channel: '',
					language: '',
					corpId: '',
					isSingapore: '',
					activeAt: '',
					orderBatch: '',
					cooperationMode: '',
          remark: '',
				},
				modalData: {
					file: null,
					taskName: '',
					corpId: '',
					isSingapore: '',
					orderBatch: '',
					cooperationMode: '',
          remark: '',
				},
				downloading: false,
				message: '文件仅支持csv格式文件,大小不能超过5MB',
				ruleValidate: {
					taskName: [{
						required: true,
						message: '请输入任务名称',
						type: 'string',
					}],
					isSingapore: [{
						required: true,
						message: '请选择是否为新加坡卡',
						trigger: 'change'
					}],
					orderBatch: [{
						required: true,
						message: '请输入订单批次',
						trigger: 'change'
					}],
					cooperationMode: [{
						required: true,
						message: '请选择合作模式',
					}],
          remark: [{
          	required: true,
          	message: '备注不能为空',
          }],
				},
				changePackageId: [
					"effectiveDay"
				],
				pagRuleValidate: {
					taskName: [{
						required: true,
						message: '任务名称不能为空',
						type: 'string',
					}],
					iccid: [{
						required: true,
						message: 'iccid不能为空',
						type: 'string',
					}],
					quantity: [{
						required: true,
						message: '数量不能为空',
						type: 'string',
					},{
						pattern: /^(\+?[1-9]\d{0,5}|\+?999999)$/,
						trigger: "blur",
						message: '取值1-999999之间的正整数',
					}],
					effectiveDay: [{
						required: true,
						message: '有效期不能为空',
						type: 'string',
					}],
					currency: [{
						required: true,
						message: '币种不能为空',
						type: 'string',
					}],
					amount: [{
						required: true,
						message: '金额不能为空',
						type: 'string',
					},{
						pattern: /^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/,
						trigger: "blur",
						message: '金额最高支持8位整数和2位小数正数或零',
					}],
					channel: [{
						required: true,
						message: '售卖渠道不能为空',
						type: 'string',
					}],
					language: [{
						required: true,
						message: '语言不能为空',
						type: 'string',
					}],
					corpId: [{
						required: true,
						message: '渠道商不能为空',
						type: 'string',
					}],
					isSingapore: [{
						required: true,
						message: '是否是新加坡卡不能为空',
						type: 'string',
					}],
					activeAt: [{
						pattern: /^\d{4}\/\d{2}\/\d{2}$/,
						trigger: "blur",
						message: '日期格式为yyyy/mm/dd',
					}],
					orderBatch: [{
						required: true,
						message: '订单批次不能为空',
						type: 'string',
					}],
					cooperationMode: [{
						required: true,
						message: '合作模式不能为空',
					}],
          remark: [{
						required: true,
						message: '备注不能为空',
					}],
				},
				batchModal: false,
				uploadUrl: '',
				uploading: false,
				modelColumns: [{
						title: 'ICCID',
						key: 'iccid'
					},
					{
						title: '套餐ID',
						key: 'packageId'
					},
					{
						title: '套餐有效期',
						key: 'effectiveDay'
					},
					{
						title: '销售渠道[批量售卖/推广活动/测试渠道]',
						key: 'corpName'
					},
					{
						title: '币种[请填写：人民币、港币、美元]',
						key: 'currencyCode'
					},
					{
						title: '金额',
						key: 'amount'
					},
					{
						title: '语言[请填写：简体中文、繁体中文、英文]',
						key: 'language'
					},
					{
						title: '套餐激活日期[YYYY/MM/DD]',
						key: 'date'
					},
				],
				modelData: [],
				tabName: 'name1',
				modeList:[{
					value:1,
					name:'代销'
				},{
					value:2,
					name:'A2Z'
				}
				],//合作模式选项
				modalmodeList:[{
					value:1,
					name:'代销'
				},{
					value:2,
					name:'A2Z'
				}
				],//合作模式选项

			}
		},
		created() {
			this.$nextTick(() => {
				this.changePackageId.forEach((element) => {
					this.$set(this.pagRuleValidate[element][0], 'required', false);
				});
			})
		},
		methods: {
			// 页面初始化
			init() {
				this.columns = [{
						title: '任务名称',
						key: 'taskName',
						align: 'center',
						tooltip: true,
						minWidth: 120
					},
					{
						title: '所属渠道',
						key: 'corpName',
						align: 'center',
						tooltip: true,
						minWidth: 120
					},
					{
						title: '是否新加坡卡',
						key: 'isSingapore',
						align: 'center',
						width: 110,
						render: (h, params) => {
							const row = params.row;
							const text = row.isSingapore == '1' ? '是' : row.isSingapore == '2' ? '否' : '未知';
							return h('label', text)
						}
					},
					{
						title: '账户名',
						key: 'username',
						align: 'center',
						minWidth: 120,
						tooltip: true
					},
					{
						title: '金额(人民币)',
						align: 'center',
						minWidth: 110,
						render: (h, params) => {
							const row = params.row;
							const map = new Map(Object.entries(row.amount));
							var cny = map.get("156");
							// var text = cny == null ? '-' : Number(math.format(Number(cny) / 100, 14)).toFixed(4);
							var text = cny == null ? '-' : cny;
							return h('label', text);
						},
					},
					{
						title: '金额(港币)',
						align: 'center',
						minWidth: 110,
						render: (h, params) => {
							const row = params.row;
							const map = new Map(Object.entries(row.amount));
							var hkd = map.get("344");
							// var text = hkd == null ? '-' : Number(math.format(Number(hkd) / 100, 14)).toFixed(4);
							var text = hkd == null ? '-' : hkd;
							return h('label', text);
						}
					},
					{
						title: '金额(美元)',
						align: 'center',
						minWidth: 110,
						render: (h, params) => {
							const row = params.row;
							const map = new Map(Object.entries(row.amount));
							var usd = map.get("840");
							// var text = usd == null ? '-' : Number(math.format(Number(usd) / 100, 14)).toFixed(4);
							var text = usd == null ? '-' : usd;
							return h('label', text);
						}
					},
					{
						title: '处理状态',
						key: 'taskStatus',
						align: 'center',
						width: 100,
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.taskStatus) {
								case "1":
									text = "处理中";
									break;
								case "2":
									text = "已完成";
									break;
								case "4":
									text = "回滚中";
									break;
								case "5":
									text = "已回滚";
									break;
                case "6":
                	text = "可回滚";
                	break;
								default:
									text = "未知状态";
							}
							return h('label', text);
						}
					},
					{
						title: '任务总数',
						key: 'sourceFileCount',
						tooltip: true,
						width: 100,
						align: 'center'
					},
					{
						title: '成功条数',
						key: 'successFileCount',
						tooltip: true,
						width: 100,
						align: 'center'
					},
					{
						title: '失败条数',
						key: 'failFileCount',
						tooltip: true,
						width: 100,
						align: 'center'
					},
					{
						title: '创建时间',
						key: 'createTime',
						tooltip: true,
						minWidth: 150,
						align: 'center'
					},
					{
						title: '完成时间',
						key: 'completeTime',
						tooltip: true,
						minWidth: 150,
						align: 'center'
					}
				];
				// var action = ["download", "back"];
				var btnPriv = this.$route.meta.permTypes;
				var width = 0;
				btnPriv.filter(function(val) {
					if ("download".indexOf(val) > -1) {
						width = width + 330;
					}
					if ("back".indexOf(val) > -1) {
						width = width + 75;
					}
				});
				if (width > 0) {
					this.columns.push({
						title: '操作',
						slot: 'action',
						width: width,
						fixed: 'right',
						align: 'center'
					});
				}
				// this.columns.push({
				//   title: '操作',
				//   width: 150,
				//   fixed: "right",
				//   slot: 'action',
				//   align: 'center'
				// });
				//获取渠道集合
				this.getCorpList();
        this.getIsNeedRemark();
				//加载初始信息
				this.goPageFirst(1);
			},
			// 条件查询
			searchByCondition() {
				this.goPageFirst(1);
			},
			// 页面加载
			goPageFirst(page) {
				this.currentPage = page;
				this.loading = true;
				var searchCondition = {
					taskName: this.searchCondition.taskName.replace(/\s/g, ''),
					corpId: this.searchCondition.corpId,
					current: page,
					size: this.pageSize
				};
				getTaskList(searchCondition).then(res => {
					if (res && res.code == '0000') {
						this.total = res.count;
						this.tableData = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {
					this.loading = false;
				})
			},
			// 分页跳转
			goPage(page) {
				this.goPageFirst(page);
			},
			// 根据渠道商支持的合作模式，展示对应合作模式
			getmodes(){
				this.corpList.map((item)=>{
					if(item.corpId===this.pageData.corpId){
						if(item.modes.includes('1') && item.modes.includes('2')){//A2Z模式,代销模式通知支持
							this.modeList=[{
								value:1,
								name:'代销'
							},{
								value:2,
								name:'A2Z'
							}]
						}else if(item.modes.includes('2')){//A2Z模式
							this.modeList=[{
								value:2,
								name:'A2Z'
							}]
							this.pageData.cooperationMode=2
						}else if(item.modes.includes('1')){//代销模式
							this.modeList=[{
								value:1,
								name:'代销'
							}]
							this.pageData.cooperationMode=1
						}
					}
				})
			},
			modesclear(){
				this.pageData.cooperationMode=''
				this.modeList=[{
					value:1,
					name:'代销'
				},{
					value:2,
					name:'A2Z'
				}
				]
			},
			// 根据渠道商支持的合作模式，展示对应合作模式
			modalgetmodes(){
				this.corpList.map((item)=>{
					if(item.corpId===this.modalData.corpId){
						if(item.modes.includes('1') && item.modes.includes('2')){//A2Z模式,代销模式通知支持
							this.modalmodeList=[{
								value:1,
								name:'代销'
							},{
								value:2,
								name:'A2Z'
							}]
						}else if(item.modes.includes('2')){//A2Z模式
							this.modalmodeList=[{
								value:2,
								name:'A2Z'
							}]
							this.modalData.cooperationMode=2
						}else if(item.modes.includes('1')){//代销模式
							this.modalmodeList=[{
								value:1,
								name:'代销'
							}]
							this.modalData.cooperationMode=1
						}
					}
				})
			},
			modalmodesclear(){
				this.modalData.cooperationMode=''
				this.modalmodeList=[{
					value:1,
					name:'代销'
				},{
					value:2,
					name:'A2Z'
				}
				]
			},

			getCorpList() {
				getCorpList({
					"type": 1,
					"status": 1,
					"checkStatus": 2
				}).then(res => {
					if (res && res.code == '0000') {
						this.corpList = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			getIsNeedRemark() {
        getIsNeedRemark({
        }).then(res => {
        	if (res && res.code == '0000') {
        		this.isNeedCorpList = res.data;
        	} else {
        		throw res
        	}
        }).catch((err) => {

        }).finally(() => {

        })
      },
      //批量配置
			showBatchModal() {
				this.$refs['formValidate'].resetFields();
				this.$refs['pagFormValidate'].resetFields();
				this.modalData = {
					file: null,
					taskName: '',
					corpId: '',
					isSingapore: '',
					orderBatch: '',
					cooperationMode: '',
          remark: '',
				};
				this.pageData = {
					taskName: '',
					iccid: '',
					quantity: '',
					packageId: '',
					effectiveDay: '',
					currency: '',
					amount: '',
					channel: '',
					language: '',
					corpId: '',
					isSingapore: '',
					activeAt: '',
					orderBatch: '',
					cooperationMode: '',
          remark: '',
				};
				this.batchModal = true;
			},
			//删除临时文件
			removeFile() {
				this.modalData.file = '';
			},
			//文件临时上传
			handleBeforeUpload(file) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: '文件格式错误',
						desc: '文件格式错误，请上传.csv格式文件'
					})
				} else {
					if (file.size > 5 * 1024 * 1024) {
						this.$Notice.warning({
							title: '文件大小超过限制',
							desc: '文件超过了最大限制范围5MB'
						})
					} else {
						this.modalData.file = file;
					}
				}
				return false
			},
			//文件上传时
			fileUploading(event, file, fileList) {
				this.message = '文件上传中,待进度条消失后再操作'
			},
			//文件上传成功时
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件,并按格式填写后上传'
			},
			//取消
			cancelUpload() {
				this.batchModal = false;
				this.modeList=[{
					value:1,
					name:'代销'
				},{
					value:2,
					name:'A2Z'
				}],
				this.modalmodeList=[{
					value:1,
					name:'代销'
				},{
					value:2,
					name:'A2Z'
				}]
			},
			// 选择标签
			choseTab(name) {
				this.tabName = name
			},
			//发送
			async handleUpload() {
				if (this.tabName === 'name1') {
					//套餐id填入后，有效期也必填
					if(this.pageData.packageId){
						this.changePackageId.forEach(element => {
							this.pagRuleValidate['effectiveDay'][0].required = true;
						})
						this.$refs["pagFormValidate"].validate();
					} else {
						this.changePackageId.forEach(element => {
							this.pagRuleValidate['effectiveDay'][0].required = false;
						})
						this.$refs["pagFormValidate"].validate();
					}

					this.$refs.pagFormValidate.validate(valid => {
						if (valid) {
							this.uploading = true;
							//body参数
							addPageTask({
								activeAt: this.pageData.activeAt,
								amount: this.pageData.amount,
								channel: this.pageData.channel,
								corpId: this.pageData.corpId,
								currency: this.pageData.currency,
								effectiveDay: this.pageData.effectiveDay,
								iccid: this.pageData.iccid,
								isSingapore: this.pageData.isSingapore,
								language: this.pageData.language,
								quantity: this.pageData.quantity,
								taskName: this.pageData.taskName,
								orderBatch: this.pageData.orderBatch,
								cooperationMode: this.pageData.cooperationMode,
								username: this.$store.state.user.userName,
								operatorId: this.$store.state.user.userId,
								packageId: this.pageData.packageId,
                remark: this.pageData.remark,
							}).then(res => {
								if (res.code === '0000') {
									var data = res.data
									this.$Notice.success({
										title: '操作提示',
										desc: res.msg
									})
									this.cancelUpload();
									this.goPageFirst(1);
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.uploading = false;
							})
						}
					})
				} else {
					if (!this.modalData.file) {
						this.$Message.warning('请选择需要上传的文件')
						return false;
					}
					this.$refs.formValidate.validate((valid) => {
						if (valid) {
							this.uploading = true;
							var formData = new FormData();
							if (this.modalData.corpId != undefined) {
								formData.append('corpId', this.modalData.corpId);
							}
							formData.append('isSingapore', this.modalData.isSingapore);
							formData.append('orderBatch', this.modalData.orderBatch);
							formData.append('cooperationMode', this.modalData.cooperationMode);
							formData.append('operatorId', this.$store.state.user.userId);
							formData.append('username', this.$store.state.user.userName);
							formData.append('file', this.modalData.file);
							formData.append('taskName', this.modalData.taskName);
							formData.append('remark', this.modalData.remark);

							addTask(formData).then(res => {
								if (res.code === '0000') {
									var data = res.data
									this.$Notice.success({
										title: '操作提示',
										desc: res.msg
									})
									this.cancelUpload();
									this.goPageFirst(1);
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.uploading = false;
							})
						}
					})
				}
			},
			//文件下载
			downLoadFile(id, type) {
				//导出操作
				downloadTask(id, type).then(res => {
					const content = res.data;
					const blob = new Blob([content]); // 构造一个blob对象来处理数据
					var typeName = "";
					switch (type) {
						case "1":
							typeName = "成功";
							break;
						case "2":
							typeName = "失败";
							break;
						case "3":
							typeName = "回滚成功";
							break;
						case "4":
							typeName = "回滚失败";
							break;
						default:
							typeName = "";
					}
					const fileName = '任务执行' + typeName + '信息' + '.csv' // 导出文件名
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = document.createElement('a'); // 创建a标签
						link.download = fileName; // a标签添加属性
						link.style.display = 'none';
						link.href = URL.createObjectURL(blob);
						document.body.appendChild(link);
						link.click(); // 执行下载
						URL.revokeObjectURL(link.href); // 释放url
						document.body.removeChild(link); // 释放标签
					} else { // 其他浏览器
						navigator.msSaveBlob(blob, fileName);
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {

				})
			},
			// 下载模板文件
			downloadTempFile() {
				this.$refs.modelTable.exportCsv({
					filename: '套餐批量配置',
					columns: this.modelColumns,
					data: this.modelData
				})
			},
			//回滚
			reback(id) {
				this.$Modal.confirm({
					title: '确认回滚？',
					onOk: () => {
						doReback(id).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: res.msg
								})
								this.goPageFirst(this.currentPage);
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						})
					}
				});
			},
		},
		mounted() {
			//信息初始化
			this.init();
		},
		watch: {},
	}
</script>
<style scoped="scoped">
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 85px;
	}

	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.demo-drawer-footer {
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		border-top: 1px solid #e8e8e8;
		padding: 10px 16px;
		text-align: right;
		background: #fff;
	}

	.input_modal {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		flex-wrap: nowrap;
		width: 100%;
	}

	.errorMsg {
		width: 80%;
		margin-left: 20%;
	}
</style>
