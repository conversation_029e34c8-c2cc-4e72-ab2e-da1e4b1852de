<template>
  <!--  套餐单个配置  -->
  <div>
    <Card>
      <div class="search_head">
        <Form ref="form" :label-width="60" :model="form" :rules="rule" inline>
          <FormItem label="MSISDN：" prop="MSISDN">
            <Input
              v-model="form.MSISDN"
              placeholder="输入MSISDN..."
              clearable
              style="width: 200px; margin-right: 20px"
            />
          </FormItem>

          <FormItem label="ICCID：" prop="MSISDN">
            <Input
              v-model="form.ICCID"
              placeholder="输入ICCID..."
              clearable
              style="width: 200px; margin-right: 20px"
            />
          </FormItem>

          <FormItem label="IMSI：" prop="MSISDN">
            <Input
              v-model="form.IMSI"
              placeholder="输入IMSI..."
              clearable
              style="width: 200px; margin-right: 20px"
            />
          </FormItem>

          <Button
            v-preventReClick
            icon="md-search"
            type="primary"
            :loading="loading"
            @click="searchByCondition()"
            >搜索</Button
          >
        </Form>
      </div>
      <div style="margin-top: 20px">
        <Table
          :columns="columns"
          :data="tableData"
          :ellipsis="true"
          :loading="loading"
          no-data-text="暂无数据"
        >
          <template slot-scope="{ row, index }" slot="package">
            <a v-has="'viewPackage'" href="#" @click="showBoughtPackages(row)"
              >查看套餐</a
            >
          </template>
          <template slot-scope="{ row, index }" slot="action">
            <Button
              v-has="'add'"
              v-preventReClick
              type="primary"
              size="small"
              style="margin-right: 10px"
              @click="showPackages(row)"
              >添加</Button
            >
            <Button
              v-has="'delete'"
              v-preventReClick
              type="error"
              size="small"
              @click="deleteWarning(row, 1)"
              >全部解绑</Button
            >
          </template>
        </Table>
      </div>
      <div class="table-botton" style="margin-top: 15px">
        <Page
          :total="total"
          :current.sync="currentPage"
          show-total
          show-elevator
          @on-change="goPage"
        />
      </div>
    </Card>

    <!-- 添加套餐 -->
    <Modal
      title="添加套餐"
      v-model="editModal"
      width="60%"
      :mask-closable="false"
      @on-cancel="cancelModal"
      :footer-hide="true"
    >
      <div class="search_head">
        <span style="font-weight: bold">套餐名称(简体)：</span
        ><Input
          v-model="addPackageName"
          placeholder="输入套餐名称..."
          clearable
          style="width: 200px; margin-right: 20px"
        />
        <span style="font-weight: bold">国家/地区：</span>
        <Select
          v-model="addCountry"
          filterable
          style="width: 200px; margin-right: 20px"
          placeholder="请选择国家/地区"
          :clearable="true"
        >
          <Option
            v-for="item in continentList"
            :value="item.mcc"
            :key="item.id"
            >{{ item.countryEn }}</Option
          >
        </Select>
        <Button
          v-preventReClick
          icon="md-search"
          type="primary"
          @click="getAllPackageList(0)"
          >搜索</Button
        >
      </div>
      <div style="margin-top: 15px">
        <Table
          ref="selection"
          :columns="columnsT"

          :data="tableDataModal"
          :ellipsis="true"
          :loading="loading2"
          max-height="500"
        >
          <template slot-scope="{ row, index }" slot="delete">
            <Button
              v-preventReClick
              type="success"
              size="small"
              @click="add(row)"
              >新增</Button
            >
          </template>
        </Table>
      </div>
      <div class="table-botton" style="margin-top: 15px">
        <Page
          :total="totalModal"
          :current.sync="currentPageModal"
          :page-size="6"
          show-total
          show-elevator
          @on-change="goPageModal"
        />
      </div>
    </Modal>

    <!-- 已购买套餐详情 -->
    <Modal
      title="已购买套餐详情"
      v-model="boughtModal"
      width="60%"
      :mask-closable="false"
      @on-cancel="cancelModal"
      :footer-hide="true"
    >
      <div style="margin-top: 5px">
        <Table
          ref="selection"
          :columns="boughtColumns"
          :data="boughtTableData"
          :ellipsis="true"
          :loading="loading1"
          max-height="500"
        >
          <template slot-scope="{ row, index }" slot="delete">
            <Button
              v-if="row.packageStatus == '1' "
              v-has="'unbundling'"
              v-preventReClick
              type="error"
              size="small"
              @click="deleteWarning(row, 2)"
              >解绑</Button
            >
          </template>
        </Table>
      </div>
      <div class="table-botton" style="margin-top: 15px">
        <Page
          :total="totalModal"
          :current.sync="currentPageModal"
          :page-size="10"
          show-total
          show-elevator
          @on-change="goPageModal"
        />
      </div>
    </Modal>
	<!-- 套餐生效时间修改 -->
	<!-- <Modal
	  title="选择套餐生效日期"
	  v-model="updateTimeModal"
	  width="18%"
	  :mask-closable="false"
	  @on-cancel="cancelTimeModal"
	>
	  <div style="margin-top: 5px">
		<Form ref="updateTimeObj" :model="updateTimeObj"  :rules="updateTimerule"
		label-position="left" :label-width="80"
		style=" align-items: center;justify-content:center;">
			<FormItem label="选择日期:" prop="date">
				<DatePicker format="yyyy/MM/dd" v-model="updateTimeObj.date" type="date" placement="bottom-start" placeholder="请选择日期"  :editable="true" @on-change="handleDateChange"></DatePicker>  
			</FormItem>
		</Form>
	  </div>
	  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
	  	<Button @click="cancelTimeModal">取消</Button>
	  	<Button type="primary" :loading="updateTimeloading" @click="submit">确定</Button>
	  </div>
	</Modal> -->
  </div>
</template>

<script>
import {
  getindividualList,
  getPackageList,
  addPackage,
  deletePackage,
  getAllPackageList
} from "@/api/product/package/individual";
import { opsearch } from "@/api/operators";
const mathjs = require("mathjs");
export default {
  data() {
    const checkSearch = (rule, value, cb) => {
      if (this.form.MSISDN || this.form.ICCID || this.form.IMSI) {
        return cb();
      } else {
        return cb(false);
      }
    };

    return {
      addCountry: "",
      addPackageName: "",
      continentList: [],
	  updateTimeObj:{},
      // 列表数据
      form: {
        MSISDN: "",
        ICCID: "",
        IMSI: ""
      },
      rule: {
        MSISDN: [{ validator: checkSearch, message: "至少填写一项" }]
      },
	  updateTimerule:{
		date: [{ required: true,type: 'date', message: "请选择日期" }]
	  },
      editModal: false,
      boughtModal: false,
	  // updateTimeModal:false,
	  updateTimeloading:false,
      boughtTableData: [],
      tableData: [],
      tableDataModal: [],
      columns: [
        {
          title: "MSISDN",
          key: "msisdn",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center"
        },
        {
          title: "ICCID",
          key: "iccid",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center"
        },
        {
          title: "IMSI",
          key: "imsi",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center"
        }
      ],
      public: [
        {
          title: "操作",
          slot: "action",
          align: "center"
        }
      ],
      viewPackage: [
        {
          title: "已购买套餐",
          slot: "package",
          align: "center"
        }
      ],
      publicCloum: {
        type: "selection",
        width: 60,
        align: "center"
      },
      boughtColumns: [
        {
          title: "套餐ID",
          key: "id",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center"
        },
        {
          title: "套餐名称",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          key: "nameCn",
          align: "center"
        },
        {
          title: "套餐价格",
          key: "hkd",
          align: "center",
          render: (h, params) => {
            const row = params.row;
            return h("label", 0);
          }
        },
        {
          title: "币种",
          key: "currencyCode",
          align: "center",
          render: (h, params) => {
            const row = params.row;
            //156 CNY,840 美元, 344 港币
            const text =
              row.currencyCode == "156"
                ? "人民币"
                : row.currencyCode == "840"
                ? "美元"
                : row.currencyCode == "344"
                ? "港币"
                : "获取失败";
            return h("label", text);
          }
        },
        {
          title: "国家/地区",
          key: "countrys",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center"
        },
        {
          title: "购买时间",
          key: "orderDate",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center"
        }
      ],
      unbundlingClum: [
        {
          title: "操作",
          slot: "delete",
          align: "center"
        }
      ],
      columnsT: [
        {
          title: "套餐ID",
          key: "id",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center"
        },
        {
          title: "套餐名称",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          key: "nameCn",
          align: "center"
        },

        {
          title: "国家/地区",
          key: "countrys",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center"
        },
                {
          title: "套餐价格(港币)",
          key: "hkd",
          align: "center",
          render: (h, params) => {
            const row = params.row;
            return h("label", row.hkd);
          }
        },
        {
          title: "操作",
          slot: "delete",
          align: "center"
        }
      ],
      loading: false,
      currentPage: 1,
      row: null,
      type: 0,
      currentPageModal: 1,
      page: 0,
      modalPage: 0,
      total: 0,
      totalModal: 0,
      loading1: false,
      loading2: false,
      currentCardForm:'',
	  date:''
    };
  },
  computed: {},
  methods: {
    cancelModal: function() {
      this.editModal = false;
	  // this.updateTimeModal=false
      this.boughtTableData = [];
      this.tableDataModal = [];
      this.currentPageModal = 1;
    },
	//套餐生效时间弹窗取消
	// cancelTimeModal:function(){
	// 	this.updateTimeModal=false
	// 	this.$refs['updateTimeObj'].resetFields()
	// },
    // 获取z主卡列表
    goPageFirst: function(page) {
      let kongge = /\s/g;
      		if(kongge.test(this.form.MSISDN) == true || kongge.test(this.form.ICCID) == true || kongge.test(this.form.IMSI) == true) {
      			this.$Notice.success({
      				title: "错误",
      				desc: "搜索项有空格"
      			});
      		}else{
      			if (page === 0) {
      			  this.currentPage = 1;
      			}

      			this.page = page;
      			this.loading = true;
      			getindividualList({
      			  msisdn: this.form.MSISDN.replace(/\s/g, ""),
      			  iccid: this.form.ICCID.replace(/\s/g, ""),
      			  imsi: this.form.IMSI.replace(/\s/g, ""),
      			  status: 1, //1正常 2-暂停 3-注销
      			  current: page+1,
      			  size: 10
      			})
      			  .then(res => {
      			    this.tableData = res.data;
      			    this.total = res.count;
      			  })
      			  .catch(err => {
      			    console.log(err);
      			  })
      			  .finally(() => {
      			    this.loading = false;
      			  });
      		}
    },
    //已购买套餐详情列表
    showBoughtPackages: function(row) {
      this.type = 1;
      this.row = row;
      this.currentCardForm = row.cardForm;
      this.getPackageList(0);
      this.boughtModal = true;
    },
    //套餐详情列表
    showPackages: function(row) {
      this.addPackageName = "";
      this.addCountry = "";
      this.row = row;
      this.type = 2;
      this.editModal = true;
      this.getAllPackageList(0);
    },
    searchByCondition: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.goPageFirst(0);
        }
      });
    },

    //获取所有套餐列表
    getAllPackageList: function(page) {
      this.modalPage = page;
      this.loading2 = true;
      getAllPackageList({
        mcc: this.addCountry,
        nameCn: this.addPackageName,
        status: 2, // 套餐状态 1待上架 2 正常 3 下架
        auditStatus: 2, //审批状态 1待审核 2通过 3不通过
        isTerminal: 2,
        current: page,
        size: 6
      })
        .then(res => {
          res.data.map(function(item) {
            item.countrys = item.countrys.toString();
          });
          this.tableDataModal = res.data;
          this.totalModal = res.count;
        })
        .catch(err => {
          console.log(err);
        })
        .finally(() => {
          this.loading2 = false;
        });
    },
    //将套餐添加到号码
    add: function(row) {
	  //当套餐计费激活方式为方式一，则弹出日期选择框
		// if(row.activationMode==='1'){
		// 	this.updateTimeModal=true
		// 	this.updateTimeObj.imsi=this.row.imsi
		// 	this.updateTimeObj.packageId=row.id
		// }else{
			addPackage({
			  imsi: this.row.imsi,
			  packageId: row.id
			})
			  .then(res => {
			    this.$Notice.success({
			      title: "操作成功",
			      desc: "已成功添加套餐"
			    });
			  })
			  .catch(err => {
			    console.log(err);
			  })
			  .finally(() => {
			    this.loading = false;
			  });
		// }
    },
	handleDateChange(date) {
		this.date=date
	},
	//套餐生效日期新增套餐
	submit:function(){
		this.$refs["updateTimeObj"].validate((valid) => {
			if (valid) {
				this.updateTimeloading=true
				addPackage({
				  activitionTime:this.date,
				  imsi: this.updateTimeObj.imsi,
				  packageId: this.updateTimeObj.packageId
				}).then(res => {
				    this.$Notice.success({
				      title: "操作成功",
				      desc: "已成功添加套餐"
				    });
				  })
				  .catch(err => {
				    console.log(err);
				  })
				  .finally(() => {
					// this.cancelTimeModal()
				    this.loading = false;
					this.updateTimeloading=false
				  });
			}
		})

	},
    //获取已有套餐详情列表
    getPackageList: function(page) {
      this.modalPage = page;
      this.loading1 = true;
      getPackageList({
        current: page,
        iccid: this.row.iccid,
        orderChannel: 110,
        size: 10
      })
        .then(res => {
          res.data.map(function(item) {
            item.countrys = item.countrys.toString();
          });
          this.boughtTableData = res.data;
          this.totalModal = res.count;
        })
        .catch(err => {})
        .finally(() => {
          this.loading1 = false;
        });
    },
    //国家/地区
    getLocalList() {
      opsearch({
        pageNum: 1,
        pageSize: 9999
      })
        .then(res => {
          if (res && res.code == "0000") {
            var list = res.data.list;
            this.continentList = list;
            this.continentList.sort(function(str1, str2) {
              return str1.countryEn.localeCompare(str2.countryEn);
            });

          } else {
            throw res;
          }
        })
        .catch(err => {})
        .finally(() => {});
    },
    // 分页跳转
    goPage(page) {
      this.goPageFirst(page);
    },
    //模态框分页
    goPageModal(page) {
      this.modalPage = page;
      if (this.type == 1) {
        this.getPackageList(page);
      } else {
        this.getAllPackageList(page);
      }
    },
    deleteWarning: function(params, type) {
      this.$Notice.warning({
        title: "操作提醒",
        name: "delete",
        render: h => {
          return h("div", [
            "即将删除当前选中项的套餐信息,请您确认！",
            h("br"),
            h("div", [
              h(
                "Button",
                {
                  props: {
                    type: "dashed",
                    size: "small"
                  },
                  style: {
                    marginTop: "10px",
                    marginLeft: "130px"
                  },
                  on: {
                    click: () => {
                      this.$Notice.close("delete");
                    }
                  }
                },
                "取消"
              ),
              h(
                "Button",
                {
                  props: {
                    type: "error",
                    size: "small"
                  },
                  style: {
                    marginTop: "10px",
                    marginLeft: "10px"
                  },
                  on: {
                    click: () => {
                      this.$Notice.close("delete");
                      if (type == 1) {
                        console.log("批量删除");
                        this.deleteAll(params.iccid);
                      }
                      if (type == 2) {
                        this.deleteOne(params.packageUniqueId);
                      }
                    }
                  }
                },
                "删除"
              )
            ])
          ]);
        },
        duration: 0
      });
    },
    //单个删除
    deleteOne: function(id) {
      console.log(id);
      deletePackage({
        iccid: this.row.iccid, //当前条主键
        puid: id,
        type: "1"
      })
        .then(res => {

          if (res.code === "0000") {

            this.$Notice.success({
              title: "操作成功",
              desc: "已成功解绑套餐"
            });

          if(res.data &&  res.data.length){
                   this.$Notice.success({
              title: "操作成功",
              desc: res.data.join("，") + "暂未解绑套餐"
            });
          }

          } else {
            this.$Notice.success({
              title: "操作成功",
              desc:res.data? res.data.join("，") + "暂未解绑套餐":''
            });
          }
          this.getPackageList(this.modalPage);
        })
        .catch(err => {})
        .finally(() => {
          this.loading = false;
        });
    },
    //全部删除
    deleteAll: function(id) {
      deletePackage({
        iccid: id, //当前条主键
        type: "2"
      })
        .then(res => {
          this.$Notice.success({
            title: "操作成功",
            desc: "已成功删除套餐"
          });

        })
        .catch(err => {})
        .finally(() => {
          this.loading = false;
        });
    }
  },
  mounted() {
    // this.goPageFirst(0)
    //获取国家/地区信息
    this.getLocalList();
  },
  beforeMount: function() {
    var btnPriv = this.$route.meta.permTypes;
    if (btnPriv.includes("viewPackage")) {
      this.columns = this.columns.concat(this.viewPackage);
    }
    if (btnPriv.includes("add") || btnPriv.includes("delete")) {
      this.columns = this.columns.concat(this.public);
    }
    if (btnPriv.includes("unbundling")) {
      this.boughtColumns = this.boughtColumns.concat(this.unbundlingClum);
    }
  },
  watch: {}
};
</script>
<style>
.search_head {
  width: 100%;
  display: flex;
  align-items: center;
  /* text-align: center; */
  justify-content: flex-start;
}

.demo-drawer-footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px 16px;
  text-align: right;
  background: #fff;
}

.input_modal {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  width: 100%;
}

.errorMsg {
  width: 80%;
  margin-left: 20%;
}
</style>
