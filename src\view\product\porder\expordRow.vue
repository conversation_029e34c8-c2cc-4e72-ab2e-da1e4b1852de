<!-- 展开项 -->

<style scoped>
  .expand-row {
    /* margin-bottom: 16px; */
  }
</style>
<template>
  <div>
    <Row class="expand-row">
      <Col span="6" v-if="row.orderNum">
      <span class="expand-key">订单号： </span>
      <span class="expand-value">{{ row.orderNum }}</span>
      <Tooltip content="复制" placement="top-start">
        <a href="#" @click="coypVal(row.orderNum)" style="margin-left: 5px;">
          <Icon type="md-copy" size="15" />
        </a>
      </Tooltip>
      </Col>
      <Col span="6" v-if="row.deliveryICCID">
      <span class="expand-key">发货ICCID：</span>
      <span class="expand-value">{{ row.deliveryICCID }}</span>
      <Tooltip content="复制" placement="top-start">
        <a href="#" @click="coypVal(row.deliveryICCID)" style="margin-left: 5px;">
          <Icon type="md-copy" size="15" />
        </a>
      </Tooltip>

      </Col>
      <Col span="6" v-if="row.bindICCID">
      <span class="expand-key">绑定ICCID： </span>
      <span class="expand-value">{{ row.bindICCID }}</span>
      <Tooltip content="复制" placement="top-start">
        <a href="#" @click="coypVal(row.bindICCID)" style="margin-left: 5px;">
          <Icon type="md-copy" size="15" />
        </a>
      </Tooltip>
      </Col>
      <Col span="6" v-if="row.BillsLadingNo">
      <span class="expand-key">货运单号： </span>
      <span class="expand-value">{{ row.BillsLadingNo }}</span>
      <Tooltip content="复制" placement="top-start">
        <a href="#" @click="coypVal(row.BillsLadingNo)" style="margin-left: 5px;">
          <Icon type="md-copy" size="15" />
        </a>
      </Tooltip>
      </Col>

    </Row>
  </div>
</template>
<script>
  export default {
    props: {
      row: Object
    },
    methods: {
      // 获取列表
      coypVal: function(value) {
        try {
          let Url2 = value;
          let oInput = document.createElement('input');
          oInput.value = Url2;
          document.body.appendChild(oInput);
          oInput.select();
          document.execCommand("Copy");
          oInput.className = 'oInput';
          oInput.style.display = 'none';
          this.$Message.success('复制成功');
        } catch (e) {
          this.$Message.error('复制功能暂不可使用，请手动复制');
        }

      },
    }
  };
</script>
