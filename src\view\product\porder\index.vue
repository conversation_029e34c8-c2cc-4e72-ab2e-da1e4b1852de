<template>
  <!--  个人订单管理  -->
  <div>
    <Card>
      <div class="search_head_i">
        <div class="search_box">
          <span class="search_box_label">订单编号</span>
          <Input placeholder="请输入订单编号" v-model="searchCondition.orderUniqueId" clearable style="width: 200px" />
        </div>
        <div class="search_box">
          <span class="search_box_label">订单状态</span>
          <Select v-model="searchCondition.orderStatus" clearable placeholder="请选择订单状态" style="width:200px">
            <Option :value="item.value" v-for="(item,index) in statusList" :key="index">{{item.label}}</Option>
          </Select>
        </div>
        <div class="search_box">
          <span class="search_box_label">第三方订单号</span>
          <Input placeholder="请输入第三方订单号" v-model="searchCondition.thirdOrderId" clearable style="width: 200px" />
        </div>
        <div class="search_box">
          <span class="search_box_label">商品名称</span>
          <Input placeholder="请输入商品名称" v-model="searchCondition.orderName" clearable style="width: 200px" />
        </div>
        <div class="search_box">
          <span class="search_box_label">订单生成时间</span>
          <DatePicker v-model="timeRangeArray" @on-change="handleDateChange" :editable="false" type="daterange" placeholder="请选择订单生成时间"
            clearable style="width: 200px ;" @on-clear="hanldeDateClear"></DatePicker>
        </div>
        <div class="search_box">
          <span class="search_box_label">用户名称</span>
          <Input placeholder="请输入用户名称" v-model="searchCondition.orderUserName" clearable style="width: 200px" />
        </div>
        <div class="search_box">
          <span class="search_box_label">是否大额订单</span>
          <Select v-model="searchCondition.isBigOrder" clearable placeholder="请选择是否大额订单" style="width:200px">
            <Option value="1">是</Option>
            <Option value="2">否</Option>
          </Select>
        </div>
        <div class="search_box">
          <Button style="margin: 0 4px" v-preventReClick type="primary" @click="searchByCondition" :loading="loading">
            <Icon type="ios-search" />&nbsp;搜索
          </Button>
          <Button style="margin: 0 4px" type="success" v-has="'export'" @click="exportData" icon="md-cloud-download" :loading="exportLoading">导出</Button>
          <Button style="margin: 0 4px" type="info" v-has="'shipBatch'" @click="showBatchModal">
            <div style="display: flex;align-items: center;">
              <Icon type="md-add" />&nbsp;批量发货</div>
          </Button>
          <Button style="margin: 0 4px" type="warning" v-has="'apply'" :disabled="isSuperManger == '1'" @click="viewPermission">
            <div style="display: flex;align-items: center;">
              <Icon type="md-unlock" />&nbsp;申请查看权限</div>
          </Button>
        </div>
      </div>

      <!-- 数据列表 -->
      <div style="margin-top:20px">
        <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
          <template slot-scope="{ row, index }" slot="orderInfo">
            <a v-has="'view'" style="color: #55aaff;" @click="showOrderInfo(row.orderId)">查看详情</a>
          </template>
          <template slot-scope="{ row, index }" slot="h5OrderInfo">
            <a v-has="'view'" disabled v-if="row.orderUserType != '1'">H5订单信息</a>
            <a v-has="'view'" @click="showH5OrderInfo(row.orderId)" v-else>H5订单信息</a>
          </template>
          <template slot-scope="{ row, index }" slot="unsubscribeCheck">
            <Button v-has="'check'" ghost type="success" size="small" style="margin-right: 5px" @click="examine(row.orderId,'2',)"
              v-if="row.orderStatus == '4'">通过</Button>
            <Button v-has="'check'" ghost type="success" size="small" style="margin-right: 5px" disabled v-else>通过</Button>

            <Button v-has="'check'" ghost type="error" size="small" style="margin-right: 5px" @click="examine(row.orderId,'3',)"
              v-if="row.orderStatus == '4'">不通过</Button>
            <Button v-has="'check'" ghost type="error" size="small" style="margin-right: 5px" disabled v-else>不通过</Button>
          </template>
          <template slot-scope="{ row, index }" slot="action">
            <Button v-has="'untie'" ghost size="small" type="warning" @click="unbundling(row)" style="margin-right: 5px;"
              v-if="(row.orderStatus == '2' && row.orderType == '3' && row.cardForm != '2' && !row.bigOrder) ||
              (['2', '6'].includes(row.orderStatus) && row.orderType == '3' && row.bigOrder)">解绑</Button>
            <Button v-has="'untie'" ghost size="small" type="warning" disabled style="margin-right: 5px;" v-else>解绑</Button>

            <Button v-has="'ship'" ghost size="small" type="info" @click="deliveryAllMoadl(row)" style="margin-right: 5px;"
              v-if="(row.orderStatus == '1' && row.orderType == '3' && !row.bigOrder) ||
              (['1', '6', '9'].includes(row.orderStatus) && row.orderType == '3' && row.bigOrder)">发货</Button>
            <Button v-has="'ship'" ghost size="small" type="info" disabled style="margin-right: 5px;" v-else>发货</Button>

            <Button v-has="'unsubscribe'" ghost size="small" type="error" @click="unsubscribeAll(row)" style="margin-right: 5px;"
              v-if="(row.orderStatus == '2' && row.orderType == '7' && !row.bigOrder) ||
              (row.orderStatus == '2' && row.orderType == '2' && !row.bigOrder) ||
              ((row.orderStatus == '1'||row.orderStatus == '2') && row.orderType == '3' && !row.bigOrder) ||
              (['1', '2', '5', '9'].includes(row.orderStatus) && row.orderType == '3' && row.bigOrder)">退订</Button>
            <Button v-has="'unsubscribe'" ghost size="small" type="error" disabled style="margin-right: 5px;" v-else>退订</Button>
            <Button v-has="'reorder'" size="small" ghost type="primary" :disabled="!(row.orderStatus == '11' && row.orderType == '3' && row.bigOrder)"
             style="margin-right: 5px;" @click="reorderAgain(row.orderUniqueId)">再次订购</Button>
          </template>
        </Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="total" :current.sync="currentPage" :page-size="pageSize" show-total show-elevator @on-change="goPage"
          style="margin: 10px 0;" />
      </div>
    </Card>

    <!-- 批量发货 -->
    <Modal title="请上传批量发货文件" v-model="batchModal" :mask-closable="false" @on-cancel="cancelUpload">
      <div>
        <Upload type="drag" :action="''" :on-success="fileSuccess" :before-upload="handleBeforeUpload" :on-progress="fileUploading">
          <div style="padding: 20px 0">
            <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
            <p>点击或拖拽文件上传</p>
          </div>
        </Upload>
        <ul class="ivu-upload-list" v-if="file">
          <li class="ivu-upload-list-file ivu-upload-list-file-finish">
            <span><i class="ivu-icon ivu-icon-ios-stats"></i>{{file.name}}</span>
            <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
          </li>
        </ul>
      </div>
      <div style="width: 100%;padding: 10px 0; text-align: center;">
        <!-- <Button type="primary" :loading="downloading" icon="ios-download" @click="downloadTempFile">下载模板文件</Button> -->
        <Alert type="warning">{{message}}</Alert>
      </div>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
        <Button style="margin-left: 8px" @click="cancelUpload">取消</Button>
        <Button type="primary" @click="deliveryBatch" v-preventReClick :loading="uploading">确定</Button>
      </div>
    </Modal>

    <!-- 总单发货 -->
    <Modal title="请填写发货信息" v-model="deliverFlag" :mask-closable="false" width="530px" @on-cancel="deliverCancel">
      <div class="modal_content">
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="120" :label-height="100"
          inline style="font-weight:bold;">
          <!-- <Input hide v-model="formValidate.orderId" /> -->
          <FormItem label="货运单号" prop="logistic" style="width:420px">
            <Input placeholder="请输入货运单号" :maxlength="100" v-model="formValidate.logistic" clearable />
          </FormItem>
          <FormItem label="ICCID" prop="iccidList" style="width:420px" v-if="!bigOrder">
            <Input v-model="formValidate.iccidList" wrap="hard" :autosize="{ minRows: 1, maxRows: 3 }" :rows="2"
              :maxlength="4000" clearable type="textarea" placeholder="请输入ICCID,多个以'|'分隔" />
          </FormItem>
          <FormItem label="上传ICCID文件" style="font-size: 14px;font-weight: bold;" prop="file" v-if="bigOrder">
          	<div>
          	  <Upload type="drag" :action="''" :on-success="fileSuccess" :before-upload="handleBigOrderBeforeUpload" :on-progress="fileUploading" style=" width: 290px;">
          	    <div style="padding: 20px 0;">
          	      <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
          	      <p>点击或拖拽文件上传</p>
          	    </div>
          	  </Upload>
          	  <ul class="ivu-upload-list" v-if="file">
          	    <li class="ivu-upload-list-file ivu-upload-list-file-finish">
          	      <span><i class="ivu-icon ivu-icon-ios-stats"></i>{{file.name}}</span>
          	      <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
          	    </li>
          	  </ul>
          	</div>
          	<div style="width: 300px; display: flex;flex-wrap: wrap">
          	  <Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{deliverMessage}}</Alert>
              <Button type="primary" icon="ios-download" @click="downloadTempFile">下载模板文件</Button>
          	</div>
          </FormItem>
          <FormItem label="物流公司" prop="logisticCompany" style="width:420px">
            <Input placeholder="请输入物流公司" :maxlength="100" v-model="formValidate.logisticCompany" clearable />
          </FormItem>
          <FormItem label="地址" prop="address" style="width:420px">
            <!-- country|province|city|mailing -->
            <!-- <Input placeholder="请输入地址" :maxlength="255" v-model="formValidate.address" clearable /> -->
            <Input v-model="formValidate.address.country" wrap="hard" :autosize="{ minRows: 1, maxRows: 3 }" :rows="1"
              :maxlength="255" clearable type="textarea" placeholder="请输入国家/地区" />
            <Input v-model="formValidate.address.province" wrap="hard" :autosize="{ minRows: 1, maxRows: 3 }" :rows="1"
              :maxlength="255" clearable type="textarea" placeholder="请输入省份" />
            <Input v-model="formValidate.address.city" wrap="hard" :autosize="{ minRows: 1, maxRows: 3 }" :rows="1"
              :maxlength="255" clearable type="textarea" placeholder="请输入城市地址" />
            <Input v-model="formValidate.address.mailing" wrap="hard" :autosize="{ minRows: 1, maxRows: 3 }" :rows="1"
              :maxlength="255" clearable type="textarea" placeholder="请输入邮寄地址" />
          </FormItem>
          <FormItem label="邮政编码" prop="postCode" style="width:420px">
            <Input placeholder="请输入邮政编码" :maxlength="20" v-model="formValidate.postCode" clearable />
          </FormItem>
        </Form>
      </div>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
        <Button @click="deliverCancel">取消</Button>
        <Button type="primary" @click="deliverySubmit" v-preventReClick :loading="submitFlag">确定</Button>
      </div>
    </Modal>

    <!-- 订单详情 -->
    <Modal title="订单详情" v-model="orderInfoModal" :fullscreen="false" :mask-closable="false" @on-cancel="orderInfoModalCancel"
      width="80%" :footer-hide="true" style="z-index: 100;">
      <orderInfo v-if="orderInfoModal" :id="orderId" />
    </Modal>

    <!-- H5订单详情 -->
    <Modal title="H5订单详情" v-model="h5orderInfoModal" :fullscreen="false" :mask-closable="false" @on-cancel="h5orderInfoCancel"
      width="60%" :footer-hide="true">
      <h5orderInfo v-if="h5orderInfoModal" :ordersObj="h5Obj" />
    </Modal>

    <!-- 大额订单详情 -->
    <Modal title="大额订单详情" v-model="largeOrderModal" :fullscreen="false" :mask-closable="false" @on-cancel="largeOrderModalCancel"
      width="40%" :footer-hide="true" style="z-index: 100;">
      <largeOrderInfo v-if="largeOrderModal" :id="orderId" />
    </Modal>

    <!-- 申请查看权限 -->
    <Modal title="金锁模式申请" v-model="lockApplicationModal" :fullscreen="false" :mask-closable="false" @on-cancel="lockApplicationCancel"
	  width="500px" :footer-hide="true">

    <lockApplication v-if="lockApplicationModal" :page="1" :type="1" @lockApplicationCancel="lockApplicationCancel"
		  @goPageFirst="goPageFirst(1)"/>
	</Modal>

    <!-- 卡片模板文件table -->
    <Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
  </div>
</template>

<script>
  const math = require('mathjs');
  import orderInfo from './modal/orderInfo.vue';
  import h5orderInfo from './modal/h5orderInfo.vue';
  import lockApplication from '@/components/apply-permission/lockApplication.vue';
  import largeOrderInfo from './modal/largeOrderInfo.vue';
  import {
    getOrderList,
    deliveryUploadBatch,
    unbundlingBatch,
    recyclingBatch,
    deliveryBatch,
    unsubscribeBatch,
    examineOrderBatch,
    downLoadData,
    getOrderDetail,
    reorderAgain,
    deliverBigOrder,
    unsubscribeBigOrder,
    unbundlingBigOrder
  } from '@/api/product/porder/index';
  export default {
    components: {
      orderInfo,
      h5orderInfo,
      lockApplication,
      largeOrderInfo
    },
    data() {
      // 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
      const validateUpload = (rule, value, callback) => {
      	if (this.uploadList && this.uploadList.length === 0) {
      		callback(new Error("请上传文件"))
      	} else {
      		callback()
      	}
      }
      return {
        statusList: [{
            value: '1',
            label: '待发货'
          },
          {
            value: '2',
            label: '已完成'
          },
          {
            value: '3',
            label: '已退订'
          },
          {
            value: '4',
            label: '激活退订待审批'
          },
          {
            value: '5',
            label: '部分退订'
          },
          {
            value: '6',
            label: '部分发货'
          },
          // {
          //   value: '7',
          //   label: '已回收'
          // },
          // {
          //   value: '8',
          //   label: '部分回收'
          // },
          {
            value: '9',
            label: '复合状态'
          },
          {
            value: '10',
            label: '订单生成中'
          },
          {
            value: '11',
            label: '订单处理失败'
          },
          {
            value: '12',
            label: '发货处理中'
          },
          {
            value: '13',
            label: '退订处理中'
          },
          {
            value: '14',
            label: '解绑中'
          }
        ],
        timeRangeArray: [],
        searchCondition: {
          orderUniqueId: '',
          orderStatus: '',
          thirdOrderId: '',
          orderName: '',
          startTime: '',
          endTime: '',
          orderUserName: '',
          isBigOrder: null,
        },
        loading: false, //表格加载load标识
        exportLoading: false, //下载loading
        currentPage: 1, //表格当前页面
        pageSize: 10, //表格加载大小
        total: 0, //表格数据总量
        columns: [], //表格标题
        tableData: [], //表格数据
        batchModal: false, //批量发布文件模态框标识
        file: null, //批量发布文件
        downloading: false, //模板下载load标识
        uploading: false, //批量发布文件上传load标识
        message: '文件请使用导出模板,仅支持csv格式文件,大小不能超过5MB',
        deliverMessage: '仅支持csv格式文件,大小不能超过5MB',
        deliverFlag: false, //单个发货模态框标识
        submitFlag: false, //单个发货提交load标识
        formValidate: {
          orderId: '',
          logistic: '',
          iccidList: '',
          logisticCompany: '',
          address: {
            address: '',
            country: '',
            province: '',
            city: '',
            mailing: ''
          },
          postCode: '',
          file: '',
        },
        ruleValidate: {
          logistic: [{
            required: true,
            type: 'string',
            message: '请输入货运单号',
          }],
          iccidList: [{
            required: true,
            type: 'string',
            message: '请输入ICCID',
          }],
          logisticCompany: [{
            required: true,
            type: 'string',
            message: '请输入物流公司',
          }],
          address: [
          // {
          //   required: true,
          //   type: 'string',
          //   message: '请输入地址',
          // },
          {
            validator: (rule, value, cb) => {
              return value.country != null && value.country != '';
            },
            message: '请输入国家/地区',
          },
          // {
          //   validator: (rule, value, cb) => {
          //     return value.province != null && value.province != '';
          //   },
          //   message: '请输入省份',
          // },
          // {
          //   validator: (rule, value, cb) => {
          //     return value.city != null && value.city != '';
          //   },
          //   message: '请输入城市地址',
          // },
          {
            validator: (rule, value, cb) => {
              return value.mailing != null && value.mailing != '';
            },
            message: '请输入邮寄地址',
          }
          ],
          file: [{
          	required: true,
          	validator: validateUpload,
          	trigger: 'change',
          }],
        },
        orderId: '', //总订单id
        orderInfoModal: false,
        h5orderInfoModal: false,
        lockApplicationModal: false,
        largeOrderModal: false,
        h5Obj: null,
        modelColumns: [
          {
            title: 'ICCID',
            key: 'iccid'
          }
        ],
        modelData: [],
        uploadList: [], //下载
        isSuperManger: "",
        bigOrder: false,
      }
    },
    computed: {

    },
    methods: {
      // 页面初始化
      init() {
        this.columns = [{
            title: '订单详情',
            width: 100,
            slot: 'orderInfo',
            align: 'center'
          },
          {
            title: '商品名称',
            tooltip: true,
            width: 150,
            key: 'orderName',
            align: 'center'
          },
          {
            title: '订单编号',
            key: 'orderUniqueId',
            tooltip: true,
            width: 180,
            align: 'center'
          },
          {
            title: '订购类型',
            key: 'orderType',
            tooltip: true,
            width: 140,
            align: 'center',
            render: (h, params) => {
              const row = params.row;
              var text = "";
              var color = "";
              switch (row.orderType) {
                case "1":
                  text = "卡";
                  color = '#3943ff';
                  break;
                case "2":
                  text = "套餐";
                  color = '#00aa00';
                  break;
                case "3":
                  text = "卡+套餐";
                  color = '#1d9dff';
                  break;
                case "4":
                  text = "终端线下卡池套餐";
                  color = '#4f4f4f';
                  break;
                case "5":
                  text = "流量池套餐";
                  color = '#ff3056';
                  break;
                case "6":
                  text = "终端厂商套餐";
                  color = '#9822ff';
                  break;
                case "7":
                  text = "加油包";
                  color = '#ff10ac';
                  break;
                default:
                  text = "未知类型";
              }
              return h('label', {
                style: {
                  color: color
                }
              }, text)
            }
          },
          {
            title: '订购状态',
            width: 140,
            key: 'orderStatus',
            align: 'center',
            render: (h, params) => {
              const row = params.row;
              var text = "";
              var color = "";
              switch (row.orderStatus) {
                case "1":
                  text = "待发货";
                  color = '#aaaa10';
                  break;
                case "2":
                  text = "已完成";
                  color = '#00aa00';
                  break;
                case "3":
                  text = "已退订";
                  color = '#130bff';
                  break;
                case "4":
                  text = "激活退订待审批";
                  color = '#ff10ac';
                  break;
                case "5":
                  text = "部分退订";
                  color = '#a407ff';
                  break;
                case "6":
                  text = "部分发货";
                  color = '#07ffeb';
                  break;
                // case "7":
                //   text = "已回收";
                //   break;
                // case "8":
                //   text = "部分回收";
                //   break;
                case "9":
                  text = "复合状态";
                  color = '#ff862f';
                  break;
                case "10":
                  text = "订单生成中";
                  color = '#309bff';
                  break;
                case "11":
                  text = "订单处理失败";
                  color = '#ff0b1b';
                  break;
                case "12":
                  text = "发货处理中";
                  color = '#ffcb81';
                  break;
                case "13":
                  text = "退订处理中";
                  color = '#9cff5a';
                  break;
                case "14":
                  text = "解绑中";
                  color = '#55557f';
                  break;
                default:
                  text = "未知状态";
              }
              return h('label', {
                style: {
                  color: color
                }
              }, text)
            }
          },
          {
            title: '订单生成时间',
            key: 'createTime',
            tooltip: true,
            width: 150,
            align: 'center'
          },
          {
            title: '订单更新时间',
            key: 'updateTime',
            tooltip: true,
            width: 150,
            align: 'center'
          },
          {
            title: '购买数量',
            width: 100,
            key: 'count',
            align: 'center'
          },
          {
            title: '币种',
            key: 'currencyCode',
            width: 100,
            align: 'center',
            render: (h, params) => {
              const row = params.row;
              var text = "";
              switch (row.currencyCode) {
                case "156":
                  text = "人民币";
                  break;
                case "840":
                  text = "美元";
                  break;
                case "344":
                  text = "港币";
                  break;
                default:
                  text = "未知";
              }
              return h('label', text);
            },
          },
          {
            title: '金额',
            key: 'amount',
            width: 100,
            align: 'center',
            // render: (h, params) => {
            //   const row = params.row;
            //   const text = Number(math.format(Number(row.amount) / 100, 14)).toFixed(4);
            //   return h('label', text);
            // }
          },
          {
            title: 'ICCID',
            key: 'iccid',
            minWidth: 180,
            align: 'center',
            tooltip: true,
            render: (h, params) => {
              const row = params.row;
              if (row.bigOrder) {
                //已发货展示按钮，未发货不展示按钮
                if (row.orderStatus == '1') {
                  return h('label', '');
                } else {
                  return h('div', [
                      h('a', {
                          on: {
                            click: () => {
                                this.showIccid(row.orderId)
                            }
                          }
                      },
                      "查看详情"),
                  ]);
                }
              } else {
                return h('label', row.iccid);
              }
            }
          },
          {
            title: '第三方订单号',
            key: 'thirdOrderId',
            width: 150,
            tooltip: true,
            align: 'center',
          },
          {
            title: '联系人号码',
            key: 'phoneNumber',
            tooltip: true,
            width: 150,
            align: 'center'
          },
          {
            title: '收件人',
            key: 'addressee',
            tooltip: true,
            width: 150,
            align: 'center'
          },
          {
            title: '收件地址',
            key: 'address',
            tooltip: true,
            width: 250,
            align: 'center'
          },
          {
            title: '物流公司',
            key: 'logisticCompany',
            tooltip: true,
            width: 150,
            align: 'center'
          },
          {
            title: '货运单号',
            key: 'logistic',
            tooltip: true,
            width: 150,
            align: 'center'
          },
          {
            title: '用户',
            key: 'orderUserName',
            tooltip: true,
            width: 150,
            align: 'center'
          },
        ];
        var action = ["untie", "unsubscribe", "ship", "reorder"];
        var approval = ['check'];
        var btnPriv = this.$route.meta.permTypes;
        var actionMixed = action.filter(function(val) {
          return btnPriv.indexOf(val) > -1
        });
        var approvalMixed = approval.filter(function(val) {
          return btnPriv.indexOf(val) > -1
        });
        if (actionMixed.length > 0) {
          var width = 0;
          if(actionMixed.length == 1){
            width = 70;
          } else if(actionMixed.length == 2){
            width = 140;
          } else if(actionMixed.length == 3){
            width = 175;
          } else if(actionMixed.length == 4){
            width = 240;
          }
          this.columns.push({
            title: '操作',
            slot: 'action',
            width: width,
            fixed: 'right',
            align: 'center'
          });
        }
        if (approvalMixed.length > 0) {
          this.columns.push({
            title: '退订审批',
            width: 140,
            fixed: 'right',
            slot: 'unsubscribeCheck',
            align: 'center'
          });
        }
        this.columns.push({
          title: 'H5订单信息详情',
          width: 125,
          slot: 'h5OrderInfo',
          fixed: 'right',
          align: 'center'
        });
        //加载初始信息
        this.goPageFirst(1);
      },
      // 分页跳转
      goPage(page) {
        this.goPageFirst(page);
      },
      // 条件查询
      searchByCondition() {
        this.goPageFirst(1);
      },
      //页面加载
      goPageFirst(page) {
        this.currentPage = page;
        this.loading = true;
        var searchCondition = {
          orderUniqueId: this.searchCondition.orderUniqueId.replace(/\s/g, ''),
          orderStatus: this.searchCondition.orderStatus,
          thirdOrderId: this.searchCondition.thirdOrderId.replace(/\s/g, ''),
          orderName: this.searchCondition.orderName.replace(/\s/g, ''),
          startTime: this.searchCondition.startTime,
          endTime: this.searchCondition.endTime,
          orderUserName: this.searchCondition.orderUserName.replace(/\s/g, ''),
          isBigOrder: this.searchCondition.isBigOrder,
          orderUserType: '1',
          pageNumber: page,
          pageSize: this.pageSize
        };
        getOrderList(searchCondition).then(res => {
          if (res && res.code == '0000') {
            var data = res.data;
            this.total = data.totalCount;
            this.tableData = data.records;
          } else {
            throw res
          }
        }).catch((err) => {

        }).finally(() => {
          this.loading = false;
        })
      },
      //查询导出
      exportData() {
        var searchCondition = {
          orderUniqueId: this.searchCondition.orderUniqueId.replace(/\s/g, ''),
          orderStatus: this.searchCondition.orderStatus,
          thirdOrderId: this.searchCondition.thirdOrderId.replace(/\s/g, ''),
          orderName: this.searchCondition.orderName.replace(/\s/g, ''),
          startTime: this.searchCondition.startTime,
          endTime: this.searchCondition.endTime,
          orderUserName: this.searchCondition.orderUserName.replace(/\s/g, ''),
          isBigOrder: this.searchCondition.isBigOrder,
          orderUserType: '1',
          pageNumber: 1,
          pageSize: -1
        };
        this.exportLoading = true
        downLoadData(searchCondition).then(res => {
          const content = res.data;
          const blob = new Blob([content]); // 构造一个blob对象来处理数据
          const fileName = '总订单导出列表' + '.csv' // 导出文件名
          if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
            const link = document.createElement('a'); // 创建a标签
            link.download = fileName; // a标签添加属性
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            document.body.appendChild(link);
            link.click(); // 执行下载
            URL.revokeObjectURL(link.href); // 释放url
            document.body.removeChild(link); // 释放标签
          } else { // 其他浏览器
            navigator.msSaveBlob(blob, fileName);
          }
        }).catch((err) => {
          console.log(err);
        }).finally(() => {
          this.exportLoading = false
        })
      },
      //解绑
      unbundling(row) {
        let func = row.bigOrder ? unbundlingBigOrder : unbundlingBatch
        this.$Modal.confirm({
          title: '确认解绑？',
          onOk: () => {
            func(row.orderId).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirst(this.currentPage);
              } else {
                throw res
              }
            }).catch((err) => {

            })
          }
        });
      },
      //回收
      recycling(id) {
        this.$Modal.confirm({
          title: '确认回收？',
          onOk: () => {
            recyclingBatch(id).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirst(this.currentPage);
              } else {
                throw res
              }
            }).catch((err) => {

            })
          }
        });
      },
      //总单发货
      deliveryAllMoadl(data) {
        this.bigOrder = data.bigOrder
        this.$refs['formValidate'].resetFields();
        var address = {
            address: '',
            country: '',
            province: '',
            city: '',
            mailing: ''
        };
        if(data.address != null){
          var str = data.address.split("|");
          address = {
              address: '',
              country: str[0],
              province: str[1],
              city: str[2],
              mailing: str[3],
          };
        }
        this.formValidate = {
          orderId: data.orderId,
          logistic: data.logistic,
          iccidList: data.iccid,
          logisticCompany: data.logisticCompany,
          address: address,
          postCode: data.postCode,
          orderUniqueId: data.orderUniqueId,
        };
        this.deliverFlag = true;
      },
      //总单退订
      unsubscribeAll(row) {
        this.$Modal.confirm({
          title: '确认全部退订？',
          onOk: () => {
            if (row.bigOrder) {
              unsubscribeBigOrder({
                orderId: row.orderUniqueId,
                interWeb: true,
              }).then(res => {
                if (res && res.code == '0000') {
                  this.$Notice.success({
                    title: '操作提示',
                    desc: '操作成功'
                  })
                  this.goPageFirst(this.currentPage);
                } else {
                  throw res
                }
              }).catch((err) => {

              })
            } else {
              unsubscribeBatch(row.orderId).then(res => {
                if (res && res.code == '0000') {
                  this.$Notice.success({
                    title: '操作提示',
                    desc: '操作成功'
                  })
                  this.goPageFirst(this.currentPage);
                } else {
                  throw res
                }
              }).catch((err) => {

              })
            }
          }
        });
      },
      //再次订购
      reorderAgain(id) {
        this.$Modal.confirm({
          title: '确认再次订购？',
          onOk: () => {
            reorderAgain({
              orderUniqueId: id
            }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirst(this.currentPage);
              } else {
                throw res
              }
            }).catch((err) => {

            })
          }
        });
      },
      //审核
      examine(id, type) {
        this.$Modal.confirm({
          title: type == '2' ? '确认执行通过操作？' : '确认执行不通过操作？',
          onOk: () => {
            examineOrderBatch({
              'id': id,
              'status': type
            }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirst(this.currentPage);
              } else {
                throw res
              }
            }).catch((err) => {

            })
          }
        });
      },
      //打卡批量发货模态框
      showBatchModal() {
        this.file = null;
        this.batchModal = true;
      },
      //申请查看权限
      viewPermission() {
        this.lockApplicationModal = true
      },
      //文件临时上传
      handleBeforeUpload(file, fileList) {
        if (!/^.+(\.csv)$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式错误',
            desc: '文件格式错误，请上传csv格式文件'
          })
        } else {
          if (file.size > 5 * 1024 * 1024) {
            this.$Notice.warning({
              title: '文件大小超过限制',
              desc: '文件超过了最大限制范围5MB'
            })
          } else {
            this.file = file;
            this.uploadList = fileList
          }
        }
        return false
      },
      handleBigOrderBeforeUpload(file, fileList) {
        if (!/^.+(\.csv)$/.test(file.name)) {
          this.$Notice.warning({
            title: '文件格式错误',
            desc: '文件格式错误，请上传csv格式文件'
          })
        } else {
          this.file = file;
          this.uploadList = fileList
        }
        return false
      },
      //文件上传时
      fileUploading(event, file, fileList) {
        this.message = '文件上传中,待进度条消失后再操作'
        this.deliverMessage = '文件上传中,待进度条消失后再操作'
      },
      //文件上传成功时
      fileSuccess(response, file, fileList) {
        this.message = '请先下载模板文件,并按格式填写后上传'
        this.deliverMessage = '请先下载模板文件,并按格式填写后上传'
      },
      //批量发货上传
      removeFile() {
        this.file = '';
      },
      //下载模板
      downloadTempFile() {
        this.$refs.modelTable.exportCsv({
          filename: 'iccid',
          columns: this.modelColumns,
          data: this.modelData
        })
      },
      //取消
      cancelUpload() {
        this.batchModal = false;
      },
      //单个取消
      deliverCancel() {
        this.$refs['formValidate'].resetFields();
        this.deliverFlag = false;
        this.file = null;
        this.uploadList = []
      },
      //总单发货
      deliverySubmit() {
        this.$refs.formValidate.validate((valid) => {
          if (valid) {
            this.submitFlag = true;
            var data = Object.assign({}, this.formValidate);
            var address = data.address.country.concat("|",data.address.province,"|",data.address.city,"|",data.address.mailing);
            var resultData;
            //大额订单
            if (this.bigOrder) {
              resultData = new FormData();
              resultData.append('file', this.file);
              resultData.append('address', address);
              resultData.append('logistic', data.logistic);
              resultData.append('logisticCompany', data.logisticCompany);
              resultData.append('orderId', data.orderId);
              resultData.append('postCode', data.postCode);
              resultData.append('orderUniqueId', data.orderUniqueId);
              deliverBigOrder(resultData).then(res => {
                if (res.code === '0000') {
                  var data = res.data;
                  this.$Notice.success({
                    title: '操作提示',
                    desc: '操作成功'
                  })
                  this.deliverFlag = false;
                  this.goPageFirst(1);
                } else {
                  throw res
                }
              }).catch((err) => {
                console.log(err)
              }).finally(() => {
                this.submitFlag = false;
                this.deliverFlag=false;
                this.deliverCancel();
              })
            } else {
              //小单
              resultData = {
                address: address,
                iccidList: data.iccidList,
                logistic: data.logistic,
                logisticCompany: data.logisticCompany,
                orderId: data.orderId,
                postCode: data.postCode
              };

              deliveryBatch(resultData, data.orderId).then(res => {
                if (res.code === '0000') {
                  var data = res.data;
                  this.$Notice.success({
                    title: '操作提示',
                    desc: '操作成功'
                  })
                  this.deliverFlag = false;
                  this.goPageFirst(1);
                } else {
                  throw res
                }
              }).catch((err) => {
                console.log(err)
              }).finally(() => {
                this.submitFlag = false;
                this.deliverFlag=false;
                this.deliverCancel();
              })
            }
          }
        })
      },
      //批量发货发送
      deliveryBatch() {
        if (!this.file) {
          this.$Message.warning('请选择需要上传的文件')
          return false;
        } else {
          this.uploading = true;
          var formData = new FormData();
          formData.append('file', this.file);
          deliveryUploadBatch(formData).then(res => {
            if (res.code === '0000') {
              var data = res.data
              this.$Notice.success({
                title: '操作提示',
                desc: '操作成功'
              })
              this.cancelUpload();
              this.goPageFirst(1);
            } else {
              throw res
            }
          }).catch((err) => {
            console.log(err)
          }).finally(() => {
            this.uploading = false;
          })
        }
      },
      //订单详情
      showOrderInfo(id) {
        this.orderId = id;
        this.orderInfoModal = true;
      },
      showIccid(id) {
        this.orderId = id
        this.largeOrderModal = true
      },
      //H5订单信息
      showH5OrderInfo(id) {
        this.getH5OrderInfoById(id);
        // this.getH5OrderInfoById('594771099632271360');
      },
      getH5OrderInfoById(id) {
        getOrderDetail({
          orderId: id
        }).then(res => {
          if (res && res.code == '0000') {
            var data = res.data;
            if (data.orders != null && data.orders.length > 0) {
              this.h5Obj = data.orders[0];
              this.h5orderInfoModal = true;
            } else{
              this.h5orderInfoModal = true;
            }
          } else {
            throw res
          }
        }).catch((err) => {

        }).finally(() => {

        })
      },
      hanldeDateClear() {
        this.searchCondition.startTime = '';
        this.searchCondition.endTime = '';
      },
      handleDateChange(date) {
        var startTime = this.timeRangeArray[0] || '';
        var endTime = this.timeRangeArray[1] || '';
        if (startTime == '' || endTime == '') {
          return
        }
        [this.searchCondition.startTime, this.searchCondition.endTime] = date;
      },
      h5orderInfoCancel() {
        this.h5Obj = null;
        this.h5orderInfoModal = false;
      },
      lockApplicationCancel() {
        this.lockApplicationModal = false
      },
      orderInfoModalCancel() {
        this.orderInfoModal = false;
        this.goPageFirst(this.currentPage);
      },
      largeOrderModalCancel() {
        this.largeOrderModal = false
      },
    },
    mounted() {
      // this.showH5OrderInfo("123");
      this.isSuperManger = this.$store.state.user.roleId
      this.init();
    },
    watch: {}
  }
</script>
<style scoped="scoped">
  .search_head_i {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
  }

  .search_box_label {
    font-weight: bold;
    text-align: center;
    width: 95px;
  }

  .search_box {
    width: 300px;
    padding: 0 5px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: row;
    margin-bottom: 20px;
  }

  .modal_content {
    padding: 0 16px;
  }

  .expand-row {
    margin-bottom: 16px;
  }

  .search_head {
    width: 100%;
    display: flex;
    justify-content: flex-start;
  }

  .demo-drawer-footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    background: #fff;
  }

  .input_modal {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    width: 100%;
  }

  .errorMsg {
    width: 80%;
    margin-left: 20%;
  }

</style>

<style>

  .ivu-tooltip-content .ivu-tooltip-inner{
    max-height: 600px;
    overflow-y: auto;
  }
</style>
