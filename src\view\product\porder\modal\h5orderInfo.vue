<!-- H5订单详情 -->
<template>
  <div style="height: 650px; overflow-y: auto;">
    <Form ref="searchForm" :label-width="120">
      <Row>
        <Col span="8">
        <FormItem label="订单ID">
          <Input v-model='orders.uuid' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="订单编号">
          <Input v-model='orders.orderNumber' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="交易币种">
          <Input v-model='orders.orderCurrency' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="订单状态">
          <Input v-model='orders.orderType' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="支付时间">
          <DatePicker v-model='orders.payTime' type="datetime" readonly style="width: 200px"></DatePicker>
        </FormItem>
        <FormItem label="订单生成时间">
          <DatePicker v-model='orders.orderTime' type="datetime" readonly style="width: 200px"></DatePicker>
        </FormItem>
        <FormItem label="是否包含硬卡">
          <Select v-model="orders.cardType" disabled style="width: 200px;">
            <Option value="0">不包含</Option>
            <Option value="1">包含</Option>
            <Option value="2">esim</Option>
          </Select>
        </FormItem>
        <FormItem label="支付方式">
          <Input v-model='orders.methods' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="银行卡号">
          <Input v-model='orders.cardNo' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="购买渠道">
          <Input v-model='orders.buyType' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="套餐ID">
          <Input v-model='orders.productId' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="套餐名字">
          <Input v-model='orders.productName' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="套餐价格">
          <Input v-model='orders.productAmount' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="套餐图片">
          <Button type="dashed" class="inputSty" long @click="pictureShowFlag = true" style="width: 200px;" v-if="orders.productPic != null && orders.productPic != ''">查看图片</Button>
          <Button type="dashed" class="inputSty" long style="width: 200px;" disabled v-else>图片不存在</Button>
        </FormItem>
        <FormItem label="套餐介绍">
          <Input v-model='orders.productDetial' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="eSim">
          <Input v-model='orders.iccd' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="物流编号">
          <Input v-model='orders.logisticsNo' readonly style="width: 200px;" />
        </FormItem>
        </Col>
        <Col span="8">

        <FormItem label="物流公司">
          <Input v-model='orders.logisticsCom' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="套餐数量">
          <Input v-model='orders.productNum' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="收货人ID">
          <Input v-model='orders.personId' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="支付订单ID">
          <Input v-model='orders.payId' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="订单状态描述">
          <Input v-model='orders.payResult' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="登录帐号">
          <Input v-model='orders.loginUser' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="登录信息">
          <Input v-model='orders.isTemporary' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="是否激活">
          <Input v-model='orders.isActivate' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="同步错误信息">
          <Input v-model='orders.snyErro' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="同步成功回传订单号">
          <Input v-model='orders.synOrderid' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="退款失败原因">
          <Input v-model='orders.backPayreason' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="订单提交语言">
          <Input v-model='orders.orderLanguage' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="退款时间">
          <Input v-model='orders.backDate' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="结算币种">
          <Input v-model='orders.orderPayType' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="商品原价">
          <Input v-model='orders.oldPrice' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="商品折扣">
          <Input v-model='orders.acount' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="电话头">
          <Input v-model='orders.billingPhoneTel' readonly style="width: 200px;" />
        </FormItem>
        </Col>

        <Col span="8">
        <FormItem label="唯一ID">
          <Input v-model='consignee.uuid' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="是否设置为注册账号">
          <Input v-model='consignee.lsLogonNo' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="消费者的账单国家">
          <Input v-model='consignee.billingCountry' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="消费者的电话">
          <Input v-model='consignee.billingPhone' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="省份">
          <Input v-model='consignee.billingProvince' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="消费者的城市">
          <Input v-model='consignee.billingCity' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="消费者的详细地址">
          <Input v-model='consignee.billingAddress' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="消费者的姓">
          <Input v-model='consignee.billingFirstName' readonly style="width: 200px;" />
        </FormItem>
        <FormItem label="消费者的邮箱">
          <Input v-model='consignee.billingEmail' readonly style="width: 200px;" />
        </FormItem>
        </Col>
      </Row>
    </Form>

    <!-- 图片预览-->
    <Modal title="封面预览" v-model="pictureShowFlag" :footer-hide="true" width="532px">
      <div style="display: flex;justify-content: center;align-items: center;width: 500px;">
        <img style="object-fit: contain;" :src="orders.productPic" width="100%">
      </div>
    </Modal>
  </div>
</template>

<script>
  import {
    getOrderDetail
  } from '@/api/product/porder/index';
  export default {
    props: {
      ordersObj: Object
    },
    data() {
      return {
        pictureShowFlag: false,
        orders: {
          uuid: '', //订单id
          orderNumber: '', //订单编号
          orderCurrency: '', //订单号的交易币种
          orderType: '', //订单状态
          payTime: '', //支付时间
          orderTime: '', //订单生成时间
          cardType: '', //是否包含硬卡 0：不包含 1：包含 2: esim
          methods: '', //支付方式
          cardNo: '', //银行卡号
          buyType: '', //购买渠道
          productId: '', //套餐ID
          productName: '', //套餐名字
          productAmount: '', //套餐价格
          productPic: '', //套餐图片
          productDetial: '', //套餐介绍
          iccd: '', //eSim
          logisticsNo: '', //物流编号
          logisticsCom: '', //物流公司
          productNum: '', //套餐数量
          personId: '', //收货人ID
          payId: '', //支付订单ID
          payResult: '', //订单状态描述
          loginUser: '', //登录帐号
          isTemporary: '', //登录信息
          isActivate: '', //是否激活
          snyErro: '', //同步错误信息
          synOrderid: '', //同步成功回传订单号
          backPayreason: '', //退款失败原因
          orderLanguage: '', //订单提交语言
          backDate: '', //退款时间
          orderPayType: '', //结算币种
          oldPrice: '', //商品原价
          acount: '', //商品折扣
          billingPhoneTel: '', //电话头
          consignee: null
        },
        consignee: {
          uuid: '', //唯一ID
          lsLogonNo: '', //是否设置为注册账号
          billingCountry: '', //消费者的账单国家
          billingPhone: '', //消费者的电话
          billingProvince: '', //省份
          billingCity: '', //消费者的城市
          billingAddress: '', //消费者的详细地址
          billingFirstName: '', //消费者的姓
          billingEmail: '', //消费者的邮箱
        },
      }
    },
    methods: {
      //信息初始化
      init() {
        // this.getH5OrderInfoById();
      },
      getH5OrderInfoById() {
        getOrderDetail({
          orderId: this.oId
        }).then(res => {
          if (res && res.code == '0000') {
            var data = res.obj;
            if (data.orders != null && data.orders.length > 0) {
              this.orders = data.orders[0];
              this.consignee = this.orders.consignee;
            } else {
              this.$Notice.error({
                title: '操作提示',
                desc: 'H5订单详情获取失败'
              });
            }
          } else {
            this.$Notice.error({
              title: '操作提示',
              desc: 'H5订单详情获取失败'
            });
            throw res
          }
        }).catch((err) => {

        }).finally(() => {

        })
      }
    },
    mounted() {
      if(this.ordersObj != null){
        this.orders = this.ordersObj;
        this.consignee = this.orders.consignee;
      }
    },
    watch: {}
  }
</script>
<style scoped="scoped">

</style>
