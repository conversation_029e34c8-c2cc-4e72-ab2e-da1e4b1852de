<!-- 大额订单详情-->
<template>
  <div>
    <div style="padding: 5px 0">
      <div class="search_head_i">
        <div class="search_box">
          <span class="search_box_label">ICCID</span>
          <Input placeholder="请输入ICCID" v-model.trim="iccid" clearable style="width: 200px" />
        </div>
        <div class="search_box">
          <Button style="margin: 0 4px" v-preventReClick type="primary" @click="search" :loading="searchLoading">
            <Icon type="ios-search" />&nbsp;搜索
          </Button>
        </div>
      </div>
        <Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading"> </Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="total" :current.sync="currentPage" :page-size="pageSize" show-total show-elevator
          @on-change="goPage" style="margin: 10px 0;" />
      </div>
    </div>
  </div>
</template>

<script>
  import {
    getDetailsList,
  } from '@/api/product/porder/index';
  export default {
    props: {
      id: String,
    },

    data() {
      return {
        iccid: '',
        columns: [
          {
            title: 'ICCID',
            key: 'iccid',
            tooltip: true,
            minWidth: 200,
            align: 'center'
          },
        ],
        tableData: [],
        searchLoading: false,
        loading: false,
        total: 0,
        currentPage: 1,
        pageSize: 10,
      }
    },
    methods: {
      search() {
        this.searchLoading = true
        this.goPageFirst(1)
      },
      goPageFirst(page) {
        this.currentPage = page;
        this.loading = true;
        var searchCondition = {
          orderId: this.id,
          pageNumber: page,
          pageSize: this.pageSize,
          iccid: this.iccid,
          onlyIccid: true,
        };
        getDetailsList(searchCondition).then(res => {
          if (res && res.code == '0000') {
            var data = res.data;
            this.total = data.totalCount;
            this.tableData = data.records;
          } else {
            throw res
          }
        }).catch((err) => {

        }).finally(() => {
          this.loading = false;
          this.searchLoading = false;
        })
      },
      goPage(page) {
        this.goPageFirst(page);
      },
    },
    mounted() {
      this.goPageFirst(1)
    },
  }
</script>
<style scoped>
  .search_head_i {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
  }

  .search_box_label {
    font-weight: bold;
    text-align: center;
    width: 95px;
  }

  .search_box {
    width: 300px;
    padding: 0 5px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: row;
    margin-bottom: 20px;
  }
</style>
