<!-- 订单详情 -->
<template>
  <div>
    <div style="padding: 5px 0">
      <div class="search_head_i">
        <div class="search_box">
          <span class="search_box_label">ICCID</span>
          <Input placeholder="请输入ICCID" v-model.trim="iccid" clearable style="width: 200px" />
        </div>
        <div class="search_box">
          <span class="search_box_label">订单状态</span>
          <Select v-model="status" clearable placeholder="请选择订单状态" style="width:200px">
            <Option :value="item.value" v-for="(item,index) in statusList" :key="index">{{item.label}}</Option>
          </Select>
        </div>
        <div class="search_box">
          <Button style="margin: 0 4px" v-preventReClick type="primary" @click="search" :loading="loading">
            <Icon type="ios-search" />&nbsp;搜索
          </Button>
        </div>
      </div>
      <Table ref="selection" :columns="columnsModal" :data="tableDataModal" :ellipsis="true" :loading="loadingModal"
        max-height="500">
        <template slot-scope="{ row, index }" slot="action">
          <Button v-has="'untie'" size="small" type="warning" @click="unbundlingModal(row.id)" style="margin-right: 5px;"
            v-if="row.orderStatus == '2' && row.orderType == '3' && row.cardForm != '2'">解绑</Button>
          <Button v-has="'untie'" size="small" type="warning" disabled style="margin-right: 5px;" v-else>解绑</Button>

<!--          <Button v-has="'recycle'" size="small" type="success" @click="recyclingModal(row.id)" style="margin-right: 5px;"
            v-if="row.orderStatus == '3' && row.orderType == '3' && row.iccid != '' && row.iccid != null && row.cardForm != '2'">回收</Button>
          <Button v-has="'recycle'" size="small" type="success" disabled style="margin-right: 5px;" v-else>回收</Button> -->

          <Button v-has="'ship'" size="small" type="info" @click="deliveryModal(row)" style="margin-right: 5px;" v-if="row.orderStatus == '1' && row.orderType == '3'">发货</Button>
          <Button v-has="'ship'" size="small" type="info" disabled style="margin-right: 5px;" v-else>发货</Button>

          <Button v-has="'unsubscribe'" size="small" type="error" @click="unsubscribeModal(row.id)" style="margin-right: 5px;"
            v-if="(row.orderStatus == '2' && row.orderType == '2')||((row.orderStatus == '1'||row.orderStatus == '2') && row.orderType == '3') && row.orderType != '7'">退订</Button>
          <Button v-has="'unsubscribe'" size="small" type="error" disabled style="margin-right: 5px;" v-else>退订</Button>

          <Button v-has="'check'" type="primary" size="small" style="margin-right: 5px" @click="examineModal(row.id,'2')"
            v-if="row.orderStatus == '4'&& row.orderType != '7'">通过</Button>
          <Button v-has="'check'" type="primary" size="small" style="margin-right: 5px" disabled v-else>通过</Button>

          <Button v-has="'check'" type="primary" size="small" style="margin-right: 5px" @click="examineModal(row.id,'3')"
            v-if="row.orderStatus == '4'&& row.orderType != '7'">不通过</Button>
          <Button v-has="'check'" type="primary" size="small" style="margin-right: 5px" disabled v-else>不通过</Button>
        </template>
      </Table>
    </div>
    <div class="table-botton" style="margin-top:15px">
      <Page :total="totalModal" :current.sync="currentPageModal" :page-size="pageSizeModal" show-total show-elevator
        @on-change="goPageModal" style="margin: 10px 0;" />
    </div>

    <!-- 单个发货 -->
    <Modal title="请填写发货信息" v-model="deliverModal" :mask-closable="false" width="530px" @on-cancel="deliverCancelModal">
      <div class="modal_content">
        <Form ref="formValidateModal" :model="formValidateModal" :rules="ruleValidateModal" :label-width="130"
          :label-height="100" inline style="font-weight:bold;">
          <!-- <Input hide v-model="formValidateModal.orderId" /> -->
          <FormItem label="货运单号" prop="logistic" style="width:420px">
            <Input placeholder="请输入货运单号" :maxlength="100" v-model="formValidateModal.logistic" clearable />
          </FormItem>
          <FormItem label="ICCID" prop="iccidList" style="width:420px">
            <Input v-model="formValidateModal.iccidList" wrap="hard" :autosize="{ minRows: 1, maxRows: 3 }" :rows="2"
              :maxlength="4000" clearable type="textarea" placeholder="请输入ICCID" />
          </FormItem>
          <FormItem label="物流公司" prop="logisticCompany" style="width:420px">
            <Input placeholder="请输入物流公司" :maxlength="100" v-model="formValidateModal.logisticCompany" clearable />
          </FormItem>
          <!-- <FormItem label="联系地址" prop="address" style="width:420px">
            <Input placeholder="请输入联系地址" :maxlength="255" v-model="formValidateModal.address" clearable />
          </FormItem> -->
          <FormItem label="地址" prop="address" style="width:420px">
            <Input v-model="formValidateModal.address.country" wrap="hard" :autosize="{ minRows: 1, maxRows: 3 }" :rows="1"
              :maxlength="255" clearable type="textarea" placeholder="请输入国家/地区" />
            <Input v-model="formValidateModal.address.province" wrap="hard" :autosize="{ minRows: 1, maxRows: 3 }" :rows="1"
              :maxlength="255" clearable type="textarea" placeholder="请输入省份" />
            <Input v-model="formValidateModal.address.city" wrap="hard" :autosize="{ minRows: 1, maxRows: 3 }" :rows="1"
              :maxlength="255" clearable type="textarea" placeholder="请输入城市地址" />
            <Input v-model="formValidateModal.address.mailing" wrap="hard" :autosize="{ minRows: 1, maxRows: 3 }" :rows="1"
              :maxlength="255" clearable type="textarea" placeholder="请输入邮寄地址" />
          </FormItem>
          <FormItem label="邮政编码" prop="postCode" style="width:420px">
            <Input placeholder="请输入邮政编码" :maxlength="20" v-model="formValidateModal.postCode" clearable />
          </FormItem>
        </Form>
      </div>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
        <Button @click="deliverCancelModal">取消</Button>
        <Button type="primary" @click="deliverySubmitModal" v-preventReClick :loading="submitFlagModal">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  const math = require('mathjs');
  import {
    getDetailsList,
    unbundling,
    recycling,
    delivery,
    unsubscribe,
    examineOrder
  } from '@/api/product/porder/index';
  export default {
    props: {
      id: String,
    },
    data() {
      return {
        iccid: '',
        status: '',
        columnsModal: [],
        tableDataModal: [],
        loadingModal: false,
        totalModal: 0,
        currentPageModal: 1,
        pageSizeModal: 10,
        deliverModal: false,
        formValidateModal: {
          orderId: '',
          logistic: '',
          iccidList: '',
          logisticCompany: '',
          address: {
            address: '',
            country: '',
            province: '',
            city: '',
            mailing: ''
          },
          postCode: ''
        },
        ruleValidateModal: {
          logistic: [{
            required: true,
            type: 'string',
            message: '请输入货运单号',
          }],
          iccidList: [{
            required: true,
            type: 'string',
            message: '请输入ICCID',
          }],
          logisticCompany: [{
            required: true,
            type: 'string',
            message: '请输入物流公司',
          }],
          address: [
          // {
          //   required: true,
          //   type: 'string',
          //   message: '请输入地址',
          // },
          {
            validator: (rule, value, cb) => {
              return value.country != null && value.country != '';
            },
            message: '请输入国家/地区',
          },
          // {
          //   validator: (rule, value, cb) => {
          //     return value.province != null && value.province != '';
          //   },
          //   message: '请输入省份',
          // },
          // {
          //   validator: (rule, value, cb) => {
          //     return value.city != null && value.city != '';
          //   },
          //   message: '请输入城市地址',
          // },
          {
            validator: (rule, value, cb) => {
              return value.mailing != null && value.mailing != '';
            },
            message: '请输入邮寄地址',
          }
          ],
        },
        submitFlagModal: false,
        statusList: [{
            value: '1',
            label: '待发货'
          },
          {
            value: '2',
            label: '已完成'
          },
          {
            value: '3',
            label: '已退订/已回滚'
          },
          {
            value: '4',
            label: '激活退订待审批'
          },
          // {
          //   value: '5',
          //   label: '部分退订'
          // },
          // {
          //   value: '6',
          //   label: '部分发货'
          // },
        ]
      }
    },
    methods: {
      //信息初始化
      init() {
        this.columnsModal = [{
            title: '订单编号',
            key: 'id',
            minWidth: 170,
            tooltip: true,
            align: 'center'
          },
          {
            title: 'ICCID',
            key: 'iccid',
            tooltip: true,
            align: 'center',
            minWidth: 150,
          },
          {
            title: '物流编号',
            key: 'logistic',
            tooltip: true,
            align: 'center',
            minWidth: 150,
          },
          {
            title: '订购类型',
            key: 'orderType',
            align: 'center',
            minWidth: 150,
            render: (h, params) => {
              const row = params.row;
              var text = "";
              var color = "";
              switch (row.orderType) {
                case "1":
                  text = "卡";
                  color = '#3943ff';
                  break;
                case "2":
                  text = "套餐";
                  color = '#00aa00';
                  break;
                case "3":
                  text = "卡+套餐";
                  color = '#1d9dff';
                  break;
                case "4":
                  text = "终端线下卡池套餐";
                  color = '#4f4f4f';
                  break;
                case "5":
                  text = "流量池套餐";
                  color = '#ff3056';
                  break;
                case "6":
                  text = "终端厂商套餐";
                  color = '#9822ff';
                  break;
                case "7":
                  text = "加油包";
                  color = '#ff10ac';
                  break;
                default:
                  text = "未知类型";
              }
              return h('label', {
                style: {
                  color: color
                }
              }, text)
            }
          },
          {
            title: '订单状态',
            key: 'orderStatus',
            align: 'center',
            minWidth: 150,
            render: (h, params) => {
              const row = params.row;
              var text = "";
              var color = "";
              switch (row.orderStatus) {
                case "1":
                  text = "待发货";
                  color = '#aaaa10';
                  break;
                case "2":
                  text = "已完成";
                  color = '#00aa00';
                  break;
                case "3":
                  text = "已退订/已回滚";
                  color = '#130bff';
                  break;
                case "4":
                  text = "激活退订待审批";
                  color = '#ff10ac';
                  break;
                // case "5":
                //   text = "已回收";
                //   break;
                default:
                  text = "未知状态";
              }
              return h('label', {
                style: {
                  color: color
                }
              }, text)
            }
          },
          {
            title: '币种',
            key: 'currencyCode',
            align: 'center',
            minWidth: 100,
            render: (h, params) => {
              const row = params.row;
              var text = "";
              switch (row.currencyCode) {
                case "156":
                  text = "人民币";
                  break;
                case "840":
                  text = "美元";
                  break;
                case "344":
                  text = "港币";
                  break;
                default:
                  text = "未知";
              }
              return h('label', text);
            },
          },
          {
            title: '金额',
            key: 'amount',
            align: 'center',
            minWidth: 100,
            // render: (h, params) => {
            //   const row = params.row;
            //   const text = Number(math.format(Number(row.amount) / 100, 14)).toFixed(4);
            //   return h('label', text);
            // }
          }
        ];
        this.columnsModal.push({
          title: '操作',
          width: 370,
          fixed: 'right',
          slot: 'action',
          align: 'center'
        });
        this.goPageFirstModal(1);
      },
      search() {
        this.goPageFirstModal(1)
      },
      goPageFirstModal(page) {
        this.currentPageModal = page;
        this.loadingModal = true;
        var searchCondition = {
          orderId: this.id,
          pageNumber: page,
          pageSize: this.pageSizeModal,
          iccid: this.iccid,
          status: this.status,
          onlyIccid: false,
        };
        getDetailsList(searchCondition).then(res => {
          if (res && res.code == '0000') {
            var data = res.data;
            this.totalModal = data.totalCount;
            this.tableDataModal = data.records;
          } else {
            throw res
          }
        }).catch((err) => {

        }).finally(() => {
          this.loadingModal = false;
        })
      },
      goPageModal(page) {
        this.goPageFirstModal(page);
      },
      //解绑
      unbundlingModal(id) {
        this.$Modal.confirm({
          title: '确认解绑？',
          onOk: () => {
            unbundling(id).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirstModal(this.currentPageModal);
              } else {
                throw res
              }
            }).catch((err) => {

            })
          }
        });
      },
      //回收
      recyclingModal(id) {
        this.$Modal.confirm({
          title: '确认回收？',
          onOk: () => {
            recycling(id).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirstModal(this.currentPageModal);
              } else {
                throw res
              }
            }).catch((err) => {

            })
          }
        });
      },
      //子单发货
      deliveryModal(data) {
        this.$refs['formValidateModal'].resetFields();
        var address = {
            address: '',
            country: '',
            province: '',
            city: '',
            mailing: ''
        };
        if(data.address != null){
          var str = data.address.split("|");
          address = {
              address: '',
              country: str[0],
              province: str[1],
              city: str[2],
              mailing: str[3],
          };
        }
        this.formValidateModal = {
          orderId: data.id,
          logistic: data.logistic,
          iccidList: data.iccid,
          logisticCompany: data.logisticCompany,
          address: address,
          postCode: data.postCode
        };
        this.deliverModal = true;
      },
      //退订
      unsubscribeModal(id) {
        this.$Modal.confirm({
          title: '确认退订？',
          onOk: () => {
            unsubscribe(id).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirstModal(this.currentPageModal);
              } else {
                throw res
              }
            }).catch((err) => {

            })
          }
        });
      },
      //单个取消
      deliverCancelModal() {
        this.$refs['formValidateModal'].resetFields();
        this.deliverModal = false;
      },
      //单个发货
      deliverySubmitModal() {
        this.$refs.formValidateModal.validate((valid) => {
          if (valid) {
            this.submitFlagModal = true;
            var data = Object.assign({}, this.formValidateModal);
            var address = data.address.country.concat("|",data.address.province,"|",data.address.city,"|",data.address.mailing);
            var form = {
              address: address,
              iccidList: data.iccidList,
              logistic: data.logistic,
              logisticCompany: data.logisticCompany,
              orderId: data.orderId,
              postCode: data.postCode
            };
            delivery(form, form.orderId).then(res => {
              if (res.code === '0000') {
                var data = res.data;
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.deliverModal = false;
                this.goPageFirstModal(1);
              } else {
                throw res
              }
            }).catch((err) => {
              console.log(err)
            }).finally(() => {
              this.submitFlagModal = false;
			  this.deliverModal=false
            })
          }
        })
      },
      examineModal(id, type) {
        this.$Modal.confirm({
          title: type == '2' ? '确认执行通过操作？' : '确认执行不通过操作？',
          onOk: () => {
            examineOrder({
              'id': id,
              'status': type
            }).then(res => {
              if (res && res.code == '0000') {
                this.$Notice.success({
                  title: '操作提示',
                  desc: '操作成功'
                })
                this.goPageFirstModal(this.currentPageModal);
              } else {
                throw res
              }
            }).catch((err) => {

            })
          }
        });
      },
    },
    mounted() {
      this.init();
    },
    watch: {}
  }
</script>
<style scoped>
  .input_modal {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    width: 100%;
  }
  .search_head_i {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
  }
  .search_box {
    width: 300px;
    padding: 0 5px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: row;
    margin-bottom: 20px;
  }
  .search_box_label {
    font-weight: bold;
    text-align: center;
    width: 95px;
  }
</style>
