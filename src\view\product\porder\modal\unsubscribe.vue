<!-- 退订 -->
<template>
  <div>
    <Form ref="editForm" :model="tdModel" :rules="rules" @keydown.enter.native="delivery">
      <FormItem>
        <div class="input_modal">
          <span style="width: 120px;">购买数量：</span>
          <Input v-model="tdModel.total" readonly placeholder="0" style="width: 300px;" />
        </div>
      </FormItem>
      <FormItem v-if="['0','2'].indexOf(tdModel.status) > -1 ">
        <div class="input_modal">
          <span style="width: 120px;">可退订数量：</span>
          <Input v-model="tdModel.MaxUnsubscribe" readonly placeholder="可退订数量" style="width: 300px;" />
        </div>
      </FormItem>
      <FormItem prop="unsubscribeCount" v-if=" ['0','2'].indexOf(tdModel.status) > -1 ">
        <div class="input_modal">
          <span style="width: 10px;color: red;">*</span><span style="width: 110px;">退订数量：</span>
          <Input v-model="tdModel.unsubscribeCount" type="number" placeholder="退订数量" style="width: 300px;" />
        </div>
      </FormItem>


      <FormItem prop="numList" v-if="['3','5'].indexOf(tdModel.status) > -1">
        <div class="input_modal">
          <span style="width: 10px;color: red;">*</span><span style="width: 110px;">可退订卡号列表：</span>
          <Select v-model="tdModel.numList" placeholder="请选择退订卡号" multiple style="width: 300px;">
            <Option v-for="item in tdModel.list" :value="item.key" :key="item.key">{{ item.value }}</Option>
          </Select>
        </div>
      </FormItem>

    </Form>
    <div slot="footer" style="width: 100%; padding: 8px 0 8px 0; display: flex;align-items: center;justify-content:flex-end;">
      <Button style="width:40%" @click="cancelModal">取消</Button>
      <Button type="primary" @click="ok" style="margin-left: 50px;width:40%">确定</Button>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      tdModel: Object,
    },

    data() {
      var validateCount = (rule, value, callback) => {
        if (!value || value == 0) {
          callback(new Error('请设置退订数量'));
        }
        if (value > this.tdModel.MaxUnsubscribe) {
          callback(new Error('退订数量超过限制'));
        } else {
          callback();
        }
      };
      var validateList = (rule, value, callback) => {
        if (!value || value.length == 0) {
          callback(new Error('请选择退订卡号'));
        }
       else {
          callback();
        }
      };
      return {
        rules: {
          unsubscribeCount: [{
            validator: ['0','2'].indexOf(this.tdModel.status) > -1 ? validateCount : true,
            trigger: 'blur'
          }],
          numList:[{
            validator: ['3','5'].indexOf(this.tdModel.status) > -1 ? validateList : true,
            trigger: 'blur'
          }]
        },
      }
    },
    methods: {
      cancelModal: function() {
        this.$emit('unsubscribeCancel', 1)
      },
      ok: function() {
        this.$refs.editForm.validate((valid) => {
          if (valid) {
            console.log(this.tdModel)
            this.$emit('unsubscribeOk', this.tdModel)
          }
        })
      }
    },
    mounted() {
    },
    watch: {}
  }
</script>
<style>
  .input_modal {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    width: 100%;
  }
</style>
