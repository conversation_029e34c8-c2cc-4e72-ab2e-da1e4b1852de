<template>
	<!--  实名制人工认证  -->
	<Card id="card">
		<!-- 搜索条件 -->
		<Form ref="form" :label-width="90" :model="form" :rules="rule" inline>
			<FormItem label="认证时间">
				<DatePicker format="yyyy-MM-dd" v-model="form.date" @on-change="handleDateChange" type="daterange"
					placeholder="选择时间段"></DatePicker>
			</FormItem>
			<FormItem label="ICCID">
				<Input v-model="form.iccid" placeholder="请输入ICCID" :clearable="true" class="inputSty" />
			</FormItem>
			<FormItem label="订单ID">
				<Input v-model="form.orderId" placeholder="请输入订单ID" :clearable="true" class="inputSty" />
			</FormItem>
			<FormItem label="证件ID">
				<Input v-model="form.certificatesId" placeholder="请输入证件ID" :clearable="true" class="inputSty" />
			</FormItem>
			<FormItem>
				<Button v-has="'search'" type="primary" icon="md-search" @click="search()" :loading="searchloading"
					style="margin-right: 10px;">搜索</Button>
			</FormItem>
		</Form>
		<!-- 表格 -->
		<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading" type:html>
			<template slot-scope="{ row, index }" slot="fileName">
				<Button v-if="row.fileName===null ||row.fileName===''" disabled v-has="'showImg'" type="info" ghost
					@click="showimg(row)" style="margin-right: 10px;">点击查看</Button>
				<Button v-else v-has="'showImg'" type="info" ghost @click="showimg(row)"
					style="margin-right: 10px;">点击查看</Button>
			</template>
			<template slot-scope="{ row, index }" slot="action">
				<Button v-if="row.authStatus==='1' || row.errorDesc==='5'" disabled v-has="'certified'" type="success"
					ghost @click="Review(row,1)" style="margin-right: 10px;">认证通过</Button>
				<Button v-else v-has="'certified'" type="success" ghost @click="Review(row,1)"
					style="margin-right: 10px;">认证通过</Button>
				<Button v-has="'unsubscribe'" type="warning" ghost @click="Review(row,3)"
					style="margin-right: 10px;">退订</Button>
				<Button v-has="'recertification'" type="info" ghost @click="Review(row,2)"
					style="margin-right: 10px;">重新认证</Button>
				<Button v-has="'delete'" type="error" ghost @click="DeleteHuman(row.authId)"
					style="margin-right: 10px;">删除</Button>
			</template>
		</Table>
		<div class="table-botton" style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" :page-size="10" show-total show-elevator
				@on-change="goPage" />
		</div>
		<!-- 图片查看弹窗 -->
		<Modal title="图片查看" v-model="imgModel" :footer-hide="true" :mask-closable="false" id="img" width="600px" @on-cancel="cancelModal">
			<div style="width: 500px;margin: 4px 30px;">
				<img :src="pictureUrl" width="100%">
			</div>
			<div class="twobox">
				<div class="onebox">
					<h2 style="margin: 10px 3px; margin-left: 10px;">OCR识别结果</h2>
					<Form ref="info" :model="info" :label-width="80">
						<FormItem label="证件ID" :title="info.ocrNumber" style="font-size: large;" prop="ocrNumber" class="inputstyle">
							<Input v-model='info.ocrNumber' readonly placeholder="请输入证件ID">
							</Input>
						</FormItem>
						<FormItem label="证件类型" style="font-size: large;" prop="ocrCertificatesType" class="inputstyle">
							<Select disabled v-model="info.ocrCertificatesType" placeholder="请输入证件类型"  class="inputstyle">
								<Option disabled :value="item.value" v-for="(item,index) in ocrCertificatesTypeList" :key="index">{{item.label}}</Option>
							</Select>
						</FormItem>
						<FormItem label="证件有效期" style="font-size: large;" prop="ocrExpireDate" class="inputstyle">
							<Input v-model='info.ocrExpireDate' readonly placeholder="请输入证件有效期">
							</Input>
						</FormItem>
						<FormItem label="出生年月日" style="font-size: large;" prop="ocrBirthDate" class="inputstyle">
							<Input v-model='info.ocrBirthDate' readonly placeholder="请输入出生年月日">
							</Input>
						</FormItem>
						<FormItem label="姓名(中文)" :title="info.inputNameCh" style="font-size: large;" prop="inputNameCh" class="inputstyle">
							<Input v-model='info.inputNameCh' readonly placeholder="请输入姓名(中文)">
							</Input>
						</FormItem>
						<FormItem label="姓名(英文)" :title="info.inputName" style="font-size: large;" prop="inputName" class="inputstyle">
							<Input v-model='info.inputName' readonly placeholder="请输入姓名(英文)">
							</Input>
						</FormItem>
						<FormItem label="护照国家" :title="info.ocrCountryCode" style="font-size: large;" prop="ocrCountryCode" class="inputstyle">
							<Input v-model='info.ocrCountryCode' readonly placeholder="请输入护照国家">
							</Input>
						</FormItem>
					</Form>
				</div>
				<div class="onebox">
					<h2 style="margin: 10px 5px; margin-left: 10px;">用户认证信息</h2>
					<Form ref="ruleList" :model="ruleList" :rules="listRule" :label-width="80">
						<FormItem label="证件ID" :title="ruleList.certificatesId" style="font-size: large;" prop="ocrNumber" class="inputstyle">
							<Input v-model='ruleList.certificatesId' placeholder="请输入证件ID" :clearable="true" width="200px">
							</Input>
						</FormItem>
						<FormItem label="证件类型" style="font-size: large;" prop="certificatesType" class="inputstyle">
							<Select  v-model="ruleList.certificatesType" placeholder="请输入证件类型" :clearable="true"  class="inputstyle">
								<Option :value="item.value" v-for="(item,index) in certificatesTypeList" :key="index">{{item.label}}</Option>
							</Select>
						</FormItem>
						<FormItem style="font-size: large; height: 18.89px">
						</FormItem>
						<FormItem label="出生年月日" style="font-size: large;" prop="dateOfBirth" class="inputstyle">
							<Input v-model='ruleList.dateOfBirth' placeholder="格式:20300101" :clearable="true" >
							</Input>
						</FormItem>
						
						<FormItem label="姓名(中文)" :title="ruleList.inputNameCh" style="font-size: large;" prop="inputNameCh" class="inputstyle">
							<Input v-model='ruleList.inputNameCh' placeholder="请输入姓名(中文)" :clearable="true">
							</Input>
						</FormItem>
						
						<FormItem label="姓名(英文)" :title="ruleList.inputName" style="font-size: large;" prop="inputName" class="inputstyle">
							<Input v-model='ruleList.inputName' placeholder="请输入姓名(英文)" :clearable="true">
							</Input>
						</FormItem>
						<FormItem label="护照国家" :title="ruleList.passportCountry" style="font-size: large;" prop="ocrCountryCode" class="inputstyle">
							<Input v-model='ruleList.passportCountry' placeholder="请输入护照国家" :clearable="true">
							</Input>
						</FormItem>
						<FormItem label="手机号码" :title="ruleList.phoneNumber" style="font-size: large;" prop="phoneNumber" class="inputstyle">
							<Input v-model='ruleList.phoneNumber' placeholder="请输入手机号码" :clearable="true">
							</Input>
						</FormItem>
						<FormItem label="邮箱" :title="ruleList.email" style="font-size: large;" prop="email" class="inputstyle">
							<Input v-model='ruleList.email' placeholder="请输入邮箱" :clearable="true">
							</Input>
						</FormItem>
					</Form>
				</div>
			</div>
			<div style="display: flex;align-items: center;justify-content: center;">
				<Button size="large" @click="cancelModal">返回</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button v-has="'update'" size="large" type="primary" @click="update" :loading="updateLoading">确定</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	function stop() {
		return false;
	}
	import {
		pageSearch,
		Review,
		filedownload,
		DeleteHuman,
		authenUpdate
	} from '@/api/realname/certification'
	export default {
		data() {
			var validateEmail = (rule, value, callback) => {
				if (value && value.indexOf("@") == -1) {
					callback(new Error("请输入有效的邮箱地址"));
				} else {
					callback();
				}
			};
			return {
				info: {}, //图片查看详情
				form: {},
				imgModel: false,
				pictureUrl: '',
				tableData: [],
				certificatesTypeList: [
					{
						value: '1',
						label: "护照",
					},
					{
						value: '2',
						label: "港澳通行证",
					},
					{
						value: '3',
						label: "香港身份证",
					},
					{
						value: '4',
						label: "澳门身份证",
					}
				],
				ocrCertificatesTypeList: [
					{
						value: '1',
						label: "护照",
					},
					{
						value: '2',
						label: "港澳通行证",
					},
					{
						value: '3',
						label: "香港身份证",
					},
					{
						value: '4',
						label: "澳门身份证",
					}
				],
				ruleList: {
					authId: "",
					certificatesId: "",
					dateOfBirth: "",
					certificatesType: "",
					inputNameCh: "",
					inputName: "",
					// ocrExpireDate: "",
					passportCountry: "",
					phoneNumber: "",
					email: ""
				},
				total: 0,
				loading: false,
				searchloading: false,
				updateLoading: false,
				currentPage: 1,
				page: 1,
				startTime: '',
				endTime: '',
				columns: [{
						title: '认证时间',
						key: 'authTime',
						align: 'center',
						minWidth: 200,
						fixed: 'left',
					},
					{
						title: 'ICCID',
						key: 'iccid',
						align: 'center',
						minWidth: 200,
						tooltip: true,
						fixed: 'left',
						width: 200,
						render: (h, params) => {
							const row = params.row;
							let text = '';
							if (row.iccidList) {
								text = row.iccidList
							} else {
								text = row.iccid
							}
							let length = text === "" || text === null ? 0 : text.length
							if (length > 20) { //进行截取列显示字数
						  let iccid = text.replace(/\|/g, "</br>")
								text = text.substring(0, 20) + "..."
								return h('div', [h('Tooltip', {
										props: {
											placement: 'bottom',
											transfer: true //是否将弹层放置于 body 内
						 			},
										style: {
											cursor: 'pointer',
						   	},
									},
									[ //这个中括号表示是Tooltip标签的子标签
										text, //表格列显示文字
						  		h('label', {
											slot: 'content',
											style: {
												whiteSpace: 'normal'
											},
											domProps: {
												innerHTML: iccid
											},
										}, )
									])]);
							} else {
								text = text;
								return h('label', text)
							}
						}
					},
					{
						title: '订单ID',
						key: 'orderid',
						align: 'center',
						minWidth: 200,
						render: (h, params) => {
							const row = params.row
							var text = ""
							if (row.authObj === '2') {
								text = row.orderUniqueId
							} else if (row.authObj === '1') {
								row.orderuuids.map((value, id) => {
									if (text === "") {
										text = text + '' + value
									} else {
										text = text + '|' + value
									}
								})
							}
							return h('label', text)
						}
					},
					{
						title: '证件ID',
						key: 'certificatesId',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '证件类型',
						key: 'certificatesType',
						align: 'center',
						minWidth: 200,
						render: (h, params) => {
							const row = params.row
							var text = row.certificatesType === '1' ? "护照" : row.certificatesType === '2' ?
								"港澳通行证" :
								row.certificatesType === '3' ? "香港身份证" : row.certificatesType === '4' ? "澳门身份证" :
								"未知"
							return h('label', text)
						}
					},
					{
						title: '护照国家',
						key: 'passportCountry',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '出生年月日',
						key: 'dateOfBirth',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '姓名（中文）',
						key: 'inputNameCh',
						align: 'center',
						minWidth: 200,
						sortable: true,
						tooltip: true,
					},
					{
						title: '姓名（英文）',
						key: 'inputName',
						align: 'center',
						minWidth: 200,
						sortable: true,
						tooltip: true,
					},
					{
						title: '手机号码',
						key: 'phoneNumber',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '邮箱',
						key: 'email',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '认证国家/地区',
						key: 'ruleName',
						align: 'center',
						minWidth: 200,
						sortable: true,
						tooltip: true,
					},
					{
						title: '图片查看',
						slot: 'fileName',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '认证失败原因',
						key: 'errorDesc',
						align: 'center',
						minWidth: 200,
						render: (h, params) => {
							const row = params.row
							var text = row.errorDesc === '1' ? "姓名校验不一致" : row.errorDesc === '2' ? "证件已过期" :
								row.errorDesc === '3' ? "证件ID校验不一致" : row.errorDesc === '4' ? "未满16周岁" :
								row.errorDesc === '5' ? "超过一证X号" :
								row.errorDesc === '0' ? "OCR识别异常" : "未知"
							return h('label', text)
						}
					},
					{
						title: '认证状态',
						key: 'authStatus',
						align: 'center',
						minWidth: 200,
						render: (h, params) => {
							const row = params.row
							var text = row.authStatus === "1" ? "待认证" : row.authStatus === "2" ? "认证中" :
								row.authStatus === "3" ? "认证通过" : row.authStatus === "4" ? "认证失败" :
								row.authStatus === "5" ? "证件已过期" : "未知"
							return h('label', text)
						}
					},
					{
						title: '使用状态',
						key: 'useStatus',
						align: 'center',
						minWidth: 200,
						render: (h, params) => {
							const row = params.row
							var text = row.useStatus === "1" ? "处理中" : row.useStatus === "2" ? "在用" :
								row.useStatus === "3" ? "备份" : "未知"
							return h('label', text)
						}
					},
					{
						title: '认证次数',
						key: 'authNum',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '处理状态',
						key: 'isRepeat',
						align: 'center',
						minWidth: 200,
						render: (h, params) => {
							const row = params.row
							var text = row.isRepeat === '1' ? "未重新认证过" : row.isRepeat === '2' ? "已重新认证过" : "未知"
							return h('label', text)
						}
					},
					{
						title: '操作',
						slot: 'action',
						align: 'center',
						fixed: 'right',
						minWidth: 380,
					}
				],
				rule: {
					date: [{
						type: 'array',
						required: true,
						message: '请选择时间',
						trigger: 'blur',
						fields: {
							0: {
								type: 'date',
								required: true,
								message: '请选择开始日期'
							},
							1: {
								type: 'date',
								required: true,
								message: '请选择结束日期'
							}
						}
					}],
				},
				listRule: {
					email: [
						{
							validator: validateEmail,
							trigger: "blur",
						},
						{
							type: "email",
							trigger: "blur",
							message: "联系人邮箱格式错误",
						},
					],
				}
			}
		},
		computed: {

		},
		mounted() {
			document.getElementById("card").oncontextmenu = stop
			document.getElementById("card").oncopy = stop
			this.goPageFirst(1)
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				pageSearch({
					authBeginDate: this.startTime === "" ? null : this.startTime + ' 00:00:00',
					authEndDate: this.endTime === "" ? null : this.endTime + ' 23:59:59',
					certificatesId: this.form.certificatesId,
					iccid: this.form.iccid,
					orderId: this.form.orderId,
					pageNumber: page,
					pageSize: 10,
				}).then(res => {
					if (res.code === '0000') {
						this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						this.tableData = res.data
						// this.tableData.map((value, index) => {
						// 	if (value.iccidList){
						// 		let iccid=value.iccidList.replace(/\|/g,"</br>")
						// 		console.log(iccid)
						// 		this.tableData[index].iccid = value.iccidList.replace(/\|/g,"</br>")
						// 	}else {
						// 		this.tableData[index].iccid = value.iccid
						// 	}
						// })
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchloading = false
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			search() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			handleDateChange(date) {
				if (Array.isArray(date)) {
					this.startTime = date[0];
					this.endTime = date[1];
				}
			},
			//查看图片
			showimg(row) {
				this.imgModel = true
				filedownload({
					// fileName:"www.png",
					// type:"ocr"
					fileName: row.fileName,
					type: "ocr"
				}).then(res => {
					document.getElementById("img").oncontextmenu = stop
					document.getElementById("img").oncopy = stop
					const blob = new Blob([res.data])
					this.pictureUrl = window.URL.createObjectURL(blob)
				})
				let ocrNumberFlag;
				if(row.ocrNumber) {
					if(row.ocrNumber.length > 10) {
						ocrNumberFlag = row.ocrNumber.substring(0,10)
					} else {
						ocrNumberFlag = row.ocrNumber
					}
				}else {
					ocrNumberFlag = null
				}
				var str = row.ocrName ? row.ocrName : ""
				var a = str.indexOf('|') //截取出特殊字符
				var ocrNameCh = str.substring(0,a) //中文名
				var ocrNameEn =str.substring(a+1,str.length) //英文名
				this.info = {
					ocrNumber: row.ocrNumber,
					ocrBirthDate: row.ocrBirthDate,
					ocrCertificatesType: row.ocrCertificatesType,
					inputNameCh: ocrNameCh,
					inputName: ocrNameEn,
					ocrExpireDate: row.ocrExpireDate,
					ocrCountryCode: row.ocrCountryCode,
				}	
				var reg = new RegExp("-","g");
				var ocrExpireDate = row.ocrExpireDate ? row.ocrExpireDate.replace(reg, "") : "";
				this.ruleList ={
					authId: row.authId,
					certificatesId: row.certificatesId,
					dateOfBirth: row.dateOfBirth,
					certificatesType: row.certificatesType,
					inputNameCh: row.inputNameCh,
					inputName: row.inputName,
					// ocrExpireDate: ocrExpireDate,
					passportCountry: row.passportCountry,
					email: row.email,
					phoneNumber: row.phoneNumber,
				}
			},
			//认证审核
			Review(row, id) {
				// 【1、通过  2、重新认证  3、退订 】
				let desc = ""
				if (id === 1) {
					desc = '确认通过？'
				} else if (id === 2) {
					desc = row.isRepeat === "2" ? '已进行过重新认证，是否需要再次发起？' : row.isRepeat === "1" ? '确认重新认证？' : ""
				} else {
					desc = '确认退订？'
				}
				this.$Modal.confirm({
					title: desc,
					onOk: () => {
						Review({
							authId: row.authId,
							result: id
						}).then(res => {
							if (res.code === "0000") {
								this.$Notice.success({
									title: "提示",
									desc: "操作成功"
								})
								this.goPageFirst(1)
							} else {
								this.goPageFirst(1)
							}
						}).catch((err) => {
							this.goPageFirst(1)
						});
					},
				})
			},
			// 认证删除
			DeleteHuman(id) {
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						DeleteHuman({
							authID: id,
						}).then(res => {
							if (res && res.code === "0000") {
								this.goPageFirst(1)
								this.$Notice.success({
									title: "提示",
									desc: '操作成功'
								})
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						})
					},
				})
			},
			// 关闭图片查看弹窗
			cancelModal() {
				this.imgModel = false
				this.pictureUrl = ''
				this.info= {}
				this.$refs['ruleList'].resetFields()
			},
			// 人工认证
			update() {
				this.$refs["ruleList"].validate((valid) => {
					if (valid) {
						this.submitLoading = true
						let ruleList = JSON.parse(JSON.stringify(this.ruleList));
						authenUpdate(ruleList).then(res => {
							if (res.code === "0000") {
								this.$Notice.success({
									title: "提示",
									desc: '操作成功'
								})
								this.submitLoading = false
								this.updateModal = false
								this.goPageFirst(1)
								this.cancelModal()
							}
						}).catch((err) => {
							console.error(err)
						}).finally(() => {
							this.submitLoading = false
							// this.cancelModal()
						});
					}
				})
			},
		}
	}
</script>

<style>
	.inputSty {
		width: 200px;
	}
	.search_head {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 20px;
	}
	
	.twobox {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		padding: 6px;
	}
	
	.onebox {
		width: 50%;
		margin: 0 15px 15px 0;
	}
	
	.rowbox {
		line-height: 22px;
		font-size: 12px;
		color: #515a6e;
		font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
		padding: 10px 12px 10px 0px;
	} 
	
	.inputstyle {
		margin-bottom: 10px;
		
	}
	
	.ellipsisSty {
		overflow: hidden;
		text-overflow:ellipsis;
		white-space: nowrap;text-align: left; 
		cursor: pointer;
	}
	
</style>
