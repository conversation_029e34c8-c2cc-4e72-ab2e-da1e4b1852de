<template>
	<!--  认证信息查看 -->
	<Card id="card">
		<!-- 搜索条件 -->
		<Form ref="form" :label-width="70" :model="form" :rules="rule" inline style="width: 90%;">
			<Row :gutter="10">
				<Col span="7">
				<FormItem label="ICCID">
					<Input v-model.trim="form.iccid" placeholder="请输入ICCID" :clearable="true" class="inputSty" />
				</FormItem>
				</Col>
				<Col span="7">
				<FormItem label="MSISDN">
					<Input v-model.trim="form.msisdn" placeholder="请输入MSISDN" :clearable="true" class="inputSty" />
				</FormItem>
				</Col>
				<Col span="10">
				<FormItem label="IMSI">
					<Input v-model.trim="form.imsi" placeholder="请输入IMSI" :clearable="true" class="inputSty" />
				</FormItem>
				</Col>
				<Col span="7">
				<FormItem label="认证状态">
					<Select v-model="form.authStatus" placeholder="请选择认证状态" :clearable="true" class="inputSty">
						<Option :value="1">认证通过</Option>
						<Option :value="2">非认证通过</Option>
					</Select>
				</FormItem>
				</Col>
				<Col span="7">
				<!-- 增加证件ID搜索 -->
				<FormItem label="证件ID">
					<Input v-model.trim="form.certificatesId" placeholder="请输入证件ID" :clearable="true" class="inputSty" />
				</FormItem>
				</Col>
				<Col span="10">
				<FormItem>
					<Button v-has="'search'" type="primary" icon="md-search" @click="search()" :loading="searchloading"
						style="margin-right: 10px;">搜索</Button>
					<Button v-has="'export'" type="success" :loading="downloading" icon="ios-download" @click="downloadFile()"
						style="margin-right: 10px;">导出</Button>
					<Button v-has="'apply'" :disabled="isSuperManger == '1'" type="warning" icon="md-unlock"
						@click="viewPermission">申请查看权限</Button>
				</FormItem>
			</Col>
		</Row>
		</Form>
		<!-- 表格 -->
		<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading" type:html>
			<template slot-scope="{ row, index }" slot="cancelAuth">
				<Button v-has="'cancelAuthentication'" type="primary" :disabled="!!row.cancelTime" ghost @click="cancelAuthInfo(row)" style="margin-right: 10px;">取消实名认证信息</Button>
			</template>
			<template slot-scope="{ row, index }" slot="fileName">
				<Button v-has="'showImg'" type="info" v-if="row.authStatus === '3' && row.needDesensitization == false && row.fileName" ghost @click="showimg(row)" style="margin-right: 10px;">点击查看</Button>
				<Button v-has="'showImg'" type="info" v-else disabled ghost style="margin-right: 10px;">点击查看</Button>
			</template>
		</Table>
		<div class="table-botton" style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" :page-size="10" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 导出弹窗 -->
		<Modal title="导出" v-model="exportModel" :mask-closable="true" @on-cancel="cancelModal" :width="440">
			<div>
				<Form ref="ruleList" :model="ruleList" label-position="left" :rules="rule" :label-width="100" style=" align-items: center;justify-content:center;">
					<FormItem label="导出范围:" style="font-size: large;" prop="exporttype">
						<Select  v-model="ruleList.exporttype" placeholder="下拉选择导出范围"  style="width: 260px;">
							<Option :value="item.value" v-for="(item,index) in typeList" :key="index">{{item.label}}</Option>
						</Select>
					</FormItem>
					<FormItem label="发送邮箱:" style="font-size: large;" prop="email" v-if="ruleList.exporttype===2||ruleList.exporttype===1">
						<Select filterable v-model="ruleList.email" placeholder="下拉选择邮箱" clearable style="width: 260px;">
							<Option :value="item.email" v-for="(item,index) in emailList" :key="index">{{item.email}}</Option>
						</Select>
					</FormItem>
					<FormItem label="上传号码列表" prop="file" v-if="ruleList.exporttype===2">
						<div style="display: flex;">
							<Upload v-model="ruleList.file" :action="uploadUrl" :on-success="fileSuccess" :on-error="handleError"
							 :before-upload="handleBeforeUpload" ref="upload" :on-progress="fileUploading">
								<Button icon="ios-cloud-upload-outline">点击上传</Button>
							</Upload>
							<div style="width: 500px;margin-left: 50px;">
								<Button type="primary" icon="ios-download" @click="downloadTemplate">下载模板</Button>
							</div>
						</div>
						<ul class="ivu-upload-list" v-if="file">
							<li class="ivu-upload-list-file ivu-upload-list-file-finish">
								<span>
									<Icon type="ios-folder" />{{file.name}}</span>
								<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
							</li>
						</ul>
					</FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">返回</Button>
				<Button type="primary" :loading="exportloading" @click="Confirm">确定</Button>
			</div>
		</Modal>
		<!-- 模板文件table -->
		<Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
		<!-- 图片查看弹窗 -->
		<Modal title="图片查看" v-model="imgModel" :footer-hide="true" width="532px" id="img"  @on-cancel="cancelModal">
			<div style="display: flex;justify-content: center;align-items: center;width: 500px;">
				<img :src="pictureUrl" width="100%">
			</div>
		</Modal>
		<!-- 申请查看权限 -->
		<Modal title="金锁模式申请" v-model="lockApplicationModal" :fullscreen="false" :mask-closable="false" @on-cancel="lockApplicationCancel"
		  width="500px" :footer-hide="true">
		  <lockApplication v-if="lockApplicationModal" :page="2" :type="2" @lockApplicationCancel="lockApplicationCancel"
		   @goPageFirst="goPageFirst(1)"/>
		</Modal>
	</Card>
</template>

<script>
	function stop() {
		return false;
	}
	import lockApplication from '@/components/apply-permission/lockApplication.vue';
	import {
		SearchList,
		exportFile,
		getemail,
		filedownload,
		cancelAuthenticationInfo
	} from '@/api/realname/certification'
	export default {
		components: {
		  lockApplication
		},
		data() {
			// 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
			const validateUpload = (rule, value, callback) => {
				if (this.uploadList && this.uploadList.length === 0) {
					callback(new Error('请上传文件'))
				} else {
					callback()
				}
			}
			return {
				form: {},
				exportModel: false,
				imgModel: false,
				lockApplicationModal: false,
				exportloading: false,
				pictureUrl: '',
				uploadUrl: '',
				isSuperManger: "",
				tableData: [],
				ruleList: {
					exporttype:'',
					email:'',
					file:''
				},
				file:null,
				total: 0,
				loading: false,
				searchloading: false,
				downloading: false,
				currentPage: 1,
				page: 1,
				emailList: [],
				uploadList:[],
				typeList: [{
						value: 1,
						label: "全量导出",
					},
					{
						value: 2,
						label: "按文件导出",
					}
				],
				/**
				 * 模板下载
				 */
				modelData: [{
					'iccid': '********',
				}],
				modelColumns: [{
					title: 'iccid',
					key: 'iccid'
				}],
				columns: [{
						title: 'ICCID',
						key: 'iccid',
						align: 'center',
						minWidth: 200,
					},
					{
						title: 'MSISDN',
						key: 'msisdn',
						align: 'center',
						minWidth: 200,
					},
					{
						title: 'IMSI',
						key: 'imsi',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '姓名（中文）',
						key: 'inputNameCh',
						align: 'center',
						minWidth: 150,
						sortable: true,
						tooltip: true,
					},
					{
						title: '姓名（英文）',
						key: 'inputName',
						align: 'center',
						minWidth: 150,
						sortable: true,
						tooltip: true,
					},
					{
						title: '证件类型',
						key: 'certificatesType',
						align: 'center',
						minWidth: 100,
						render: (h, params) => {
							const row = params.row
							var text =row.certificatesType==='1' ? "护照":row.certificatesType==='2' ? "港澳通行证"
							:row.certificatesType==='3' ? "香港身份证" : row.certificatesType==='4' ? "澳门身份证":"未知"
							return h('label',text)
						}
					},
					{
						title: '护照国家',
						key: 'passportCountry',
						align: 'center',
						minWidth: 100,
					},
					{
						title: '证件号',
						key: 'certificatesId',
						align: 'center',
						minWidth: 150,
					},
					{
						title: '出生年月日',
						key: 'dateOfBirth',
						align: 'center',
						minWidth: 150,
					},
					{
						title: '认证时间',
						key: 'authTime',
						align: 'center',
						minWidth: 150,
					},
					{
						title: '认证国家',
						key: 'ruleName',
						align: 'center',
						minWidth: 120,
						sortable: true,
						tooltip: true,
					},
					{
						title: '认证状态',
						key: 'authstatus',
						align: 'center',
						minWidth: 120,
						render: (h,params) => {
							const row = params.row
							var text =row.authStatus==="1" ? "待认证":row.authStatus==="2" ? "认证中"
							:row.authStatus==="3" ? "认证通过":row.authStatus==="4" ? "认证失败"
							:row.authStatus==="5" ? "证件已过期" : row.authStatus==="6" ? "已取消" : "未知"
							return h('label',text)
						}
					},
					{
						title: '取消时间',
						key: 'cancelTime',
						align: 'center',
						minWidth: 150,
					},
					{
						title: '操作',
						slot: 'cancelAuth',
						align: 'center',
						minWidth: 160,
					},
					{
						title: '图片查看',
						slot: 'fileName',
						align: 'center',
						minWidth: 120,
					},
				],
				rule: {
					file: [{
						required: true,
						validator: validateUpload,
						// message: '请上传文件',
						trigger: 'change',
					}],
					exporttype: [{
						required: true,
						message: '请选择导出范围',
					}],
					email: [{
						required: true,
						message: '请选择邮箱',
					}],
				},
			}
		},
		computed: {
		},
		mounted() {
			this.isSuperManger = this.$store.state.user.roleId
			document.getElementById("card").oncontextmenu = stop
			// console.error(document.getElementById("card"))
			document.getElementById("card").oncopy = stop
			// this.goPageFirst(1)
			this.getemail()
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				SearchList({
					imsi:this.form.imsi,
					msisdn:this.form.msisdn,
					iccid:this.form.iccid,
					authStatus:this.form.authStatus,
					certificatesId:this.form.certificatesId,
					pageNumber:page,
					pageSize:10,
				}).then(res => {
					if (res.code === '0000') {
						this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchloading = false
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			search() {
				//校验必选（查询条件三选一）
				if(this.form.iccid||this.form.imsi||this.form.msisdn||this.form.authStatus||this.form.certificatesId){
					this.searchloading = true
					this.goPageFirst(1)
				}else{
					this.$Notice.warning({
						title:'提示',
						desc:'查询条件必选一个'
					})
				}
			},
			downloadFile() {
				this.exportModel = true
			},
			viewPermission() {
				this.lockApplicationModal = true
			},
			cancelModal() {
				this.file=""
				this.exportModel = false
				this.uploadList=[]
				this.$refs.ruleList.resetFields()
				this.pictureUrl = ''
			},
			lockApplicationCancel() {
				this.lockApplicationModal = false
			},
			Confirm() {
				this.$refs["ruleList"].validate((valid) => {
					if (valid) {
						this.exportloading = true
						let formData = new FormData()
						formData.append('file', this.file)
						exportFile(formData,this.ruleList.exporttype,this.ruleList.email).then(res => {
							if (res.code === "0000") {
								this.$Notice.success({
									title:'提示',
									desc:'异步导出任务创建成功，会下发至指定邮箱，数据量较大生成较慢，请稍后'
								})
								// this.goPageFirst(1)
								this.cancelModal()
							}
						}).catch((err) => {
							console.error(err)
						}).finally(() => {
							this.exportModel = false
							this.exportloading = false
							this.cancelModal()
						});
					}
				})
			},
			/**
			 * 文件上传
			 */
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			handleBeforeUpload(file, fileList) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: "文件格式不正确",
						desc: '文件 ' + file.name + ' 格式不正确，请上传csv格式文件。'
					})
				} else {
					this.file = file
					this.uploadList = fileList
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			removeFile() {
				this.file = ''
			},
			//模板下载
			downloadTemplate: function() {
				this.$refs.modelTable.exportCsv({
					filename: "ICCID",
					type:'csv',
					columns: this.modelColumns,
					data: this.modelData
				})
			},
			getemail: function(){
				getemail().then(res => {
					if (res.code === "0000") {
						this.emailList=res.data
					}
				})
			},
			//取消实名认证
			cancelAuthInfo(row){
				cancelAuthenticationInfo({
					authID: row.authId
				}).then(res => {
					if (res.code === '0000') {
						this.loading = true
						this.$Notice.success({
							title: "操作提醒",
							desc: "操作成功"
						})
						this.goPageFirst(1)
						this.loading = false
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
				})
			},
			showimg(row){
				this.imgModel = true
				filedownload({
					fileName: row.fileName,
					type: "ocr"
				}).then(res => {
					document.getElementById("img").oncontextmenu = stop
					document.getElementById("img").oncopy = stop
					const blob = new Blob([res.data])
					this.pictureUrl = window.URL.createObjectURL(blob)
				})
				let ocrNumberFlag;
				if(row.ocrNumber) {
					if(row.ocrNumber.length > 10) {
						ocrNumberFlag = row.ocrNumber.substring(0,10)
					} else {
						ocrNumberFlag = row.ocrNumber
					}
				}else {
					ocrNumberFlag = null
				}
			},
		}
	}
</script>

<style>
	.inputSty {
		width: 220px;
	}
</style>
