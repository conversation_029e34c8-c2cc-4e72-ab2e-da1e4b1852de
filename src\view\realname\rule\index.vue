<template>
	<!--  规则管理  -->
	<Card>
		<!-- 搜索条件 -->
		<Form ref="form" :label-width="60" :model="form" inline>
			<FormItem label="规则ID">
				<Input v-model="form.ruleId" placeholder="请输入规则ID" :clearable="true" class="inputSty" />
			</FormItem>
			<FormItem label="规则名称">
				<Input v-model="form.rulename" placeholder="请输入规则名称" :clearable="true" class="inputSty" />
			</FormItem>
			<FormItem label="规则编码">
				<Input v-model="form.rulecode" placeholder="请输入规则编码" :clearable="true" class="inputSty" />
			</FormItem>
			<FormItem>
				<Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search()" style="margin-right: 10px;">搜索</Button>
			</FormItem>
		</Form>

		<!-- 表格 -->
		<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'update'" type="info" ghost @click="update(row)" style="margin-right: 10px;">修改</Button>
				<Button v-has="'view'" type="warning" ghost @click="showView(row)" style="margin-right: 10px;">详情</Button>
			</template>
		</Table>
		<div class="table-botton" style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" :page-size="10" show-total show-elevator @on-change="goPage" />
		</div>
		<!-- 修改弹窗 -->
		<Modal title="规则修改" v-model="updateModal" :mask-closable="true" @on-cancel="cancelModal">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form ref="ruleList" :model="ruleList" label-position="left" :rules="rule" :label-width="150" style=" align-items: center;justify-content:center;">
					<FormItem label="规则ID:" style="font-size: large;">
						<span style="font-weight: bold;">{{ruleList.id}}</span>
					</FormItem>
					<FormItem label="规则名称:" style="font-size: large;" prop="name">
						<Input v-model='ruleList.name' placeholder="请输入规则名称" :clearable="true" style="width: 190px;margin-right: 10px;">
						</Input>
					</FormItem>
					<FormItem label="规则编码:" style="font-size: large;" prop="ruleCode">
						<Input v-model='ruleList.ruleCode' placeholder="请输入规则编码" :clearable="true" style="width: 190px;margin-right: 10px;">
						</Input>
					</FormItem>
					<FormItem label="订单认证url:" style="font-size: large;" prop="orderUrl">
						<Input v-model='ruleList.orderUrl' placeholder="请输入订单认证url" :clearable="true" style="width: 190px;margin-right: 10px;">
						</Input>
					</FormItem>
					<FormItem label="卡认证url:" style="font-size: large;" prop="cardUrl">
						<Input v-model='ruleList.cardUrl' placeholder="请输入卡认证url" :clearable="true" style="width: 190px;margin-right: 10px;">
						</Input>
					</FormItem>
					<FormItem label="一证绑定卡数量:" style="font-size: large;" prop="bindNum">
						<Input v-model='ruleList.bindNum' placeholder="请输入一证绑定卡数量" :clearable="true" style="width: 190px;margin-right: 10px;">
						</Input>
					</FormItem>
          <FormItem label="实名制有效期:" style="font-size: large;" prop="deadline">
          	<Select  v-model="ruleList.deadline" placeholder="请选择实名制有效期" :clearable="true" style="width: 190px;margin-right: 10px;">
          		<Option value="1">长期有效</Option>
          		<Option value="2">单次套餐生效</Option>
          	</Select>
          </FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">返回</Button>
				<Button type="primary" :loading="updateloading" @click="updateConfirm">确定</Button>
			</div>
		</Modal>
	</Card>
</template>

<script>
	import {
		searchRule,
		updateRule
	} from '@/api/realname/rule'
	export default {
		data() {
			const validatePositiveNum = (rule, value, callback) => {
				var str = /^[0-9]*$/
				if (!value || str.test(value)) {
					callback();
				} else {
					callback(new Error(rule.message));
				}
			};
			return {
				form: {
					ruleId: '',
					rulename: '',
					rulecode: '',
				},
				tableData: [],
				ruleList: {
					id: '',
					name: '',
					ruleCode: '',
					orderurl: '',
					cardUrl: '',
					bindNum: '',
				},
				total: 0,
				loading: false,
				currentPage: 1,
				page: 1,
				searchloading: false,
				updateModal: false,
				updateloading: false,
				columns: [{
						title: '规则ID',
						key: 'id',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '规则名称',
						key: 'name',
						align: 'center',
						minWidth: 200,
						sortable: true,
						tooltip: true,
					},
					{
						title: '规则编码',
						key: 'ruleCode',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '订单认证url',
						key: 'orderUrl',
						tooltip: true,
						align: 'center',
						minWidth: 200,
					},
					{
						title: '卡认证url',
						key: 'cardUrl',
						tooltip: true,
						align: 'center',
						minWidth: 200,
					},
					{
						title: '一证绑定卡数量',
						key: 'bindNum',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '操作',
						slot: 'action',
						align: 'center',
						minWidth: 200,
					}
				],
				rule: {
					name: [{
							required: true,
							message: '规则名称不能为空',
							trigger: 'blur'
						},
						// { validator: validateNum,message: "最高支持8位整数和2位小数的正负数"},
					],
					ruleCode: [{
							required: true,
							message: '规则编码不能为空',
							trigger: 'blur'
						},
						{
							max: 50,
							min: 0,
							message: '长度不能超过50'
						},
					],
					orderUrl: [{
						required: true,
						message: '订单认证url不能为空',
						trigger: 'blur'
					}, ],
					cardUrl: [{
						required: true,
						message: '卡认证url不能为空',
						trigger: 'blur'
					}, ],
					bindNum: [{
						required: true,
						message: '一证绑定卡数量不能为空',
						trigger: 'blur'
					}, {
						validator: validatePositiveNum,
						message: "请输入纯数字",
					}, ],
          deadline: [{
            required: true,
            message: '实名制有效期不能为空',
            trigger: 'blur'
          }]
				},
			}
		},
		computed: {

		},
		mounted() {
			//缓存数据
			let ruleList = JSON.parse(localStorage.getItem("ruleList")) === null ? '' : JSON.parse(localStorage.getItem(
				"ruleList"))
			if (ruleList) {
				this.form.ruleId = ruleList.ruleId === undefined ? "" : ruleList.ruleId
				this.form.rulename = ruleList.rulename === undefined ? "" : ruleList.rulename
				this.form.rulecode = ruleList.rulecode === undefined ? "" : ruleList.rulecode
			}
			this.goPageFirst(1)
			//清除缓存
			localStorage.removeItem("ruleList")
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				let pageNo = page
				let pageSize = 10
				searchRule(pageNo, pageSize, {
					ruleId: this.form.ruleId,
					ruleName: this.form.rulename,
					ruleCode: this.form.rulecode,
				}).then(res => {
					if (res.code === '0000') {
						this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.data.total
						this.tableData = res.data.records
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchloading = false
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			search() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			update(row) {
				this.ruleList = Object.assign({}, row);
				this.ruleList.bindNum = row.bindNum.toString()
				this.updateModal = true
			},
			showView(row) {
				this.$router.push({
					path: '/ruleDetail',
					query: {
						ruleDetail: encodeURIComponent(JSON.stringify(row)),
						ruleList: encodeURIComponent(JSON.stringify(this.form)),
					}
				})
			},
			cancelModal() {
				this.updateModal = false
				setTimeout(() => {
					this.$refs.ruleList.resetFields()
				}, 100)
			},
			updateConfirm() {
				this.$refs["ruleList"].validate((valid) => {
					if (valid) {
						let id = this.ruleList.id
						this.updateloading = true
						this.ruleList.updateTime = null
						updateRule(id, this.ruleList).then(res => {
							if (res.code === "0000") {
                this.$Notice.success({
                	title: '操作提示',
                	desc: '修改成功'
                })
								this.updateModal = false
								this.updateloading = false
								this.goPageFirst(1)
								this.cancelModal()
							}
						}).catch((err) => {
							console.error(err)
						}).finally(() => {
							this.updateloading = false
							this.cancelModal()
						});
					}
				})
			}
		}

	}
</script>

<style>
	.inputSty {
		width: 200px;
	}
</style>
