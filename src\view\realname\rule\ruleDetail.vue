<template>
	<!--  规则详情 -->
	<Card>
		<!-- 搜索条件 -->
		<Form ref="form" :label-width="90" :model="form" inline>
			<FormItem label="规则ID:">
				<span style="font-weight: bold;">{{form.id}}</span>
			</FormItem>
			<FormItem label="规则名称:">
				<span style="font-weight: bold;">{{form.name}}</span>
			</FormItem>
		</Form>

		<!-- 表格 -->
		<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<Button v-has="'Delete'" type="error" @click="Delete(row)" style="margin-right: 10px;">删除</Button>
			</template>
		</Table>
		<div class="table-botton" style="display: flex;align-items: center;justify-content:center; margin-top: 20px;">
			<Button v-has="'add'" icon="md-add" type="success" @click="add()" style="margin-right: 10px;">添加</Button>&nbsp;&nbsp;
			<Button @click="back()">返回</Button>
		</div>

		<!-- 新增弹窗 -->
		<Modal title="添加" v-model="addModal" :mask-closable="true" @on-cancel="cancelModal" :width="350">
			<Form ref="ruleList" :label-width="90" :model="ruleList" :rules="rule" inline>
				<FormItem label="国家/地区：" prop="localId">
					<Select filterable v-model="ruleList.localId" placeholder="请选择国家/地区" :clearable="true" class="inputSty">
						<Option v-for="item in localList" :value="item.mcc" :key="item.id">{{ item.countryCn }}</Option>
					</Select>
				</FormItem>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">返回</Button>
				<Button type="primary" :loading="addloading" @click="Confirm">确定</Button>
			</div>
		</Modal>

	</Card>
</template>

<script>
	import {
		searchDetail,
		addRule,
		delRule
	} from '@/api/realname/rule'
	import {
		opsearch
	} from '@/api/channel.js'
	export default {
		data() {
			return {
				form: {},
				ruleList: {},
				tableData: [],
				localList: [],
				localId: '',
				loading: false,
				addloading: false,
				total: 0,
				currentPage: 1,
				page: 1,
				addModal: false,
				columns: [{
						title: 'MCC',
						key: 'mcc',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '国家/地区中文名称',
						key: 'countryCn',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '国家/地区英文名称',
						key: 'countryEn',
						align: 'center',
						minWidth: 200,
					},
					{
						title: '操作',
						slot: 'action',
						align: 'center',
						minWidth: 200,
					}
				],
				rule: {
					localId: [{
						required: true,
						message: '请选择国家/地区',
						trigger: 'change'
					}, ],
				},

			}
		},
		computed: {

		},
		mounted() {
			// 保存上一页返回数据
			localStorage.setItem("ruleList", decodeURIComponent(this.$route.query.ruleList))
			this.form = JSON.parse(decodeURIComponent(this.$route.query.ruleDetail));
			this.goPageFirst(1)
			this.getLocalList()
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				let id = this.form.id
				searchDetail(id).then(res => {
					if (res.code === '0000') {
						this.loading = false
						this.page = page
						this.total = res.count
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
				})
			},
			goPage() {

			},
			add() {
				this.addModal = true
			},
			back() {
				this.$router.push({
					path: '/rule',
				})
			},
			Confirm() {
				this.$refs["ruleList"].validate((valid) => {
					if (valid) {
						let id = this.form.id
						let mcc = this.ruleList.localId
						this.addloading = true
						addRule(id, mcc).then(res => {
							if (res.code === "0000") {
								this.$Notice.success({
									title: '提示',
									desc: '添加成功'
								})
								this.goPageFirst(1)
								this.cancelModal()
								this.addloading = false
							}
						}).catch((err) => {
							console.error(err)
						}).finally(() => {
							this.cancelModal()
							this.addloading = false
						});
					}
				})
			},
			Delete(row) {
				this.$Modal.confirm({
					title: '确定删除？',
					onOk: () => {
						let id = this.form.id
						let mcc = row.mcc
						delRule(id, mcc).then(res => {
							if (res.code === "0000") {
								this.$Notice.success({
									title: '提示',
									desc: '删除成功'
								})
								this.goPageFirst(1)
								this.cancelModal()
							}
						});
					},
				})
			},
			cancelModal() {
				this.addModal = false
				this.$refs.ruleList.resetFields()
				this.ruleList = {}
			},
			//国家/地区
			getLocalList() {
				opsearch().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.localList = list;
						this.localList.sort(function(str1, str2) {
							return str1.countryCn.localeCompare(str2.countryCn);
						});
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
		}
	}
</script>

<style>
	.inputSty {
		width: 200px;
	}
</style>
