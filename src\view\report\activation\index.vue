<template>
  <!-- 套餐激活报表 -->
  <Card>
    <div style="display: flex; width: 100%">
      <Form ref="form" :label-width="0" :model="form" :rules="rule" inline>
        <FormItem prop="dimension">
          <Select
            v-model="form.dimension"
            :clearable="true"
            style="width: 200px; text-align: left; margin: 0 10px"
            placeholder="请选择统计维度"
            @on-change="
              date = '';
              resetField(['startDate', 'endDate']);
            "
          >
            <Option
              v-for="(type, typeIndex) in cycleList"
              :value="type.id"
              :key="typeIndex"
              >{{ type.value }}</Option
            >
          </Select>
        </FormItem>
        <FormItem prop="cardform">
          <Select
            v-model="form.cardform"
            :clearable="true"
            style="width: 200px; text-align: left; margin: 0 10px"
            placeholder="请选择卡类别"
          >
            <Option
              v-for="(type, typeIndex) in typeList"
              :value="type.id"
              :key="typeIndex"
              >{{ type.value }}</Option
            >
          </Select>
        </FormItem>
        <FormItem v-if="form.dimension != '2'" prop="startDate">
          <DatePicker
            format="yyyyMMdd"
            v-model="date"
            v-has="'search'"
            @on-change="checkDatePicker"
            :editable="false"
            type="daterange"
            placeholder="选择时间段"
            clearable
          ></DatePicker>
        </FormItem>
        <FormItem v-if="form.dimension == '2'" prop="startDate">
          <DatePicker
            format="yyyyMM"
            @on-change="checkDatePicker($event, 1)"
            type="month"
            placement="bottom-start"
            placeholder="请选择开始月份"
            :editable="false"
          ></DatePicker
          >  
        </FormItem>
        <FormItem v-if="form.dimension == '2'" prop="endDate">
          <DatePicker
            format="yyyyMM"
            @on-change="checkDatePicker($event, 2)"
            type="month"
            placement="bottom-start"
            placeholder="请选择结束月份"
            :editable="false"
          ></DatePicker>
        </FormItem>

        <FormItem>
          <Button
            v-has="'search'"
            type="primary"
            icon="md-search"
            size="large"
            @click="search()"
            >搜索</Button
          >&nbsp;&nbsp;
          <Button
            v-has="'export'"
            type="success"
            icon="ios-cloud-download-outline"
            size="large"
            style="margin-left: 20px"
            @click="exportTable()"
            >导出</Button
          >
        </FormItem>
      </Form>
    </div>
    <!-- 表格 -->
    <Table
      :columns="columns12"
      :data="data"
      style="width: 100%; margin-top: 50px"
      :loading="loading"
    >
    </Table>

    <!-- 分页 -->
    <div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px">
      <Page
        :total="total"
        :current.sync="currentPage"
        show-total
        show-elevator
        @on-change="goPage"
      />
    </div>

    <Table
      v-if="data1.length"
      :columns="columns12"
      :data="data1"
      style="width: 100%; margin-top: 50px"
      :loading="loading"
    >
    </Table>
  </Card>
</template>

<script>
import {
  StatActivereportPageList,
  StatActivereportDetailDownload,
} from "@/api/report";
import mixin from '@/mixin/common'

export default {
  mixins:[mixin],
  data() {

    return {
      date: "",
      loading: false,
      form: {
        cardform: "",
        dimension: "",
        endDate: "",
        startDate: "",
      },
      rule: {
        startDate: [
          {
            required: true,
            message: "请选择时间",
          },
         
        ],
        endDate: [
          {
            required: true,
            message: "请选择时间",
          },
         
        ],
        dimension: [
          {
            required: true,
            message: "请选择维度",
          },
        ],
      },
      total: 0,
      currentPage: 1,
      cycleList: [
        {
          id: 1,
          value: "日",
        },
        {
          id: 2,
          value: "月",
        },
      ],
      typeList: [
        {
          id: 1,
          value: "普通卡（实体卡）",
        },
        {
          id: 2,
          value: "Esim卡",
        },
        {
          id: 3,
          value: "贴片卡",
        },
		{
		  id: 4,
		  value: "IMSI号",
		},
      ],

      columns12: [
        {
          title: "卡类型",
          key: "cardform",
          align: "center",
		  minWidth: 80,
          render: (h, params) => {
            const obj = {
              1: "普通卡（实体卡）",
              2: "Esim卡",
              3: "贴片卡",
              4: "IMSI号",
            };
            return h(
              "span",
              params.row.cardform ? obj[params.row.cardform] :params.row.cardform===""?row.cardform:"合计"
            );
          },
        },
        {
          title: "时间",
          key: "statTime",
          align: "center",
		  minWidth: 80,
        },
           {
          title: "本期激活数",
          key: "totalActiveNum",
          align: "center",
		  minWidth: 80,
        },
        {
          title: "H-IMSI激活数",
          key: "himisactivenum",
          align: "center",
		  minWidth: 120,
        },
        {
          title: "V-IMSI激活已切换数",
          key: "vimisactivechagenum",
          align: "center",
		  minWidth: 120,
        },
        {
          title: "V-IMSI激活未切换数",
          key: "vimisactiveunchagenum",
          align: "center",
		  minWidth: 120,
        },
		{
		  title: "加油包激活数",
		  key: "totalActiveNumRefuel",
		  align: "center",
		  minWidth: 80,
		},
        {
          title: "港币收入",
          key: "hkdIncome",
          align: "center",
		  minWidth: 80,
        },
        {
          title: "人民币收入",
          key: "cnyincome",
          align: "center",
		  minWidth: 80,
        },
        {
          title: "美元收入 ",
          key: "usdIncome",
          align: "center",
		  minWidth: 80,
        },
        {
          title: "总收入(单位:港币)",
          key: "totalincome",
          align: "center",
		  minWidth: 80,
        },
      ],
      data: [],
      data1: [], //合计
      rules: {},
    };
  },
  created(){
    this.rule.startDate.push( { validator: this.validateDate, trigger: "change" })
    this.rule.endDate.push( { validator: this.validateDate, trigger: "change" })
  },
  mounted() {
    console.log();

    // this.goPageFirst(0);
  },
  methods: {
    resetField(arr) {
      this.$refs["form"].fields.forEach((element) => {
        if (arr.includes(element.prop)) {
          element.resetField();
        }
      });
    },

    checkDatePicker(date, type) {
      if (Array.isArray(date)) {
        this.form.startDate = date[0];
        this.form.endDate = date[1];
      } else {
        if (type === 1) {
          this.form.startDate = date;
        } else {
          this.form.endDate = date;
        }
      }
    },
    goPageFirst(page) {
      if (page === 0) {
        this.currentPage = 1;
      }
      var _this = this;
      let pageSize = 10;
      let pageNum = this.currentPage;

      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.loading = true;

          StatActivereportPageList({
            pageNum,
            pageSize,
            ...this.form,
          })
            .then((res) => {
              if (res.code == "0000") {
                _this.loading = false;
                this.page = page;
                this.total = res.data.total;
                this.data = res.data.record;
                this.data1 = res.data.records1[0] ? res.data.records1 : [];
              }
            })
            .catch((err) => {
              console.error(err);
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    goPage(page) {
      this.goPageFirst(page);
    },
    // 搜索
    search() {
      this.goPageFirst(0);
    },
    // 导出
    exportTable() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          StatActivereportDetailDownload({
            ...this.form,
          })
            .then((res) => {
              const content = res.data;
              const fileName = "套餐激活数.csv"; // 导出文件名
              if ("download" in document.createElement("a")) {
                // 支持a标签download的浏览器
                const link = document.createElement("a"); // 创建a标签
                let url = URL.createObjectURL(content);
                link.download = fileName;
                link.href = url;
                link.click(); // 执行下载
                URL.revokeObjectURL(url); // 释放url
              } else {
                // 其他浏览器
                navigator.msSaveBlob(content, fileName);
              }
            })
            .catch(() => (this.downloading = false));
        }
      });
    },
    details(row) {
      this.$router.push({
        path: "/channel/detailsList",
      });
    },
  },
};
</script>

<style></style>
