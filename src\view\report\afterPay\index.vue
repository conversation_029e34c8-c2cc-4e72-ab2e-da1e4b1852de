<template>
  <!-- 后付费报表 -->

  <Card>
    <div style="display: flex; width: 100%">
      <Form ref="form" :label-width="0" :model="form" :rules="rule" inline>
        <FormItem prop="packagename">
          <Input
            v-model="form.packagename"
            placeholder="输入套餐名称"
            clearable
            style="width: 200px; text-align: left; margin: 0 10px"
          />
        </FormItem>
        <FormItem prop="corpname">
          <Input
            v-model="form.corpname"
            placeholder="输入渠道名称"
            clearable
            style="width: 200px; text-align: left; margin: 0 10px"
          />
        </FormItem>
        <FormItem prop="dimension">
          <Select
            v-model="form.dimension"
            :clearable="true"
            style="width: 200px; text-align: left; margin: 0 10px"
            placeholder="请选择统计维度"
            @on-change="
              date = '';
              resetField(['startDate', 'endDate']);
            "
          >
            <Option
              v-for="(type, typeIndex) in cycleList"
              :value="type.id"
              :key="typeIndex"
              >{{ type.value }}</Option
            >
          </Select>
        </FormItem>
        <FormItem prop="endDate">
          <FormItem v-if="form.dimension != '2'" prop="startDate">
            <DatePicker
              format="yyyyMMdd"
              v-model="date"
              v-has="'search'"
              @on-change="checkDatePicker"
              :editable="false"
              type="daterange"
              placeholder="选择时间段"
              clearable
            ></DatePicker>
          </FormItem>
          <FormItem v-if="form.dimension == '2'" prop="startDate">
            <DatePicker
              format="yyyyMM"
              @on-change="checkDatePicker($event, 1)"
              type="month"
              placement="bottom-start"
              placeholder="请选择开始月份"
              :editable="false"
            ></DatePicker>
          </FormItem>
          <FormItem v-if="form.dimension == '2'" prop="endDate">
            <DatePicker
              format="yyyyMM"
              @on-change="checkDatePicker($event, 2)"
              type="month"
              placement="bottom-start"
              placeholder="请选择结束月份"
              :editable="false"
            ></DatePicker>
          </FormItem>
        </FormItem>
        <FormItem>
          <Button type="primary" icon="md-search" size="large" @click="search()"
            >搜索</Button
          >
          <Button
            type="success"
            icon="ios-cloud-download-outline"
            size="large"
            style="margin-left: 20px"
            @click="exportTable()"
            >导出</Button
          >
        </FormItem>
      </Form>
    </div>
    <!-- 表格 -->
    <Table
      :columns="columns12"
      :data="data"
      style="width: 100%; margin-top: 50px"
      :loading="loading"
    >
    </Table>
    <!-- 分页 -->
    <div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px">
      <Page
        :total="total"
        :current.sync="currentPage"
        show-total
        show-elevator
        @on-change="goPage"
      />
    </div>

    <Table
      v-if="data1.length"
      :columns="columns12"
      :data="data1"
      style="width: 100%; margin-top: 50px"
      :loading="loading"
    >
    </Table>
  </Card>
</template>

<script>
import {
  StatPostpaidsettlePageList,
  StatPostpaidsettleDetailDownload
} from "@/api/report";
import mixin from '@/mixin/common'

export default {
  mixins:[mixin],
  data() {
    return {
      date: "",
      loading: false,
      form: {
        corpname: "",
        dimension: "",
        endDate: "",
        packagename: "",
        startDate: ""
      },
      rule: {
        startDate: [
          {
            required: true,
            message: "请选择时间"
          }
        ],
        endDate: [
          {
            required: true,
            message: "请选择时间"
          }
        ],
        dimension: [
          {
            required: true,
            message: "请选择维度"
          }
        ]
      },
      total: 0,
      currentPage: 1,
      cycleList: [
        {
          id: 1,
          value: "日"
        },
        {
          id: 2,
          value: "月"
        }
      ],
      typeList: [
        { value: "1", label: "普通卡（实体卡）" },
        { value: "2", label: "Esim卡" },
        { value: "3", label: "贴片卡" }
      ],
      sellList: [
        { value: "102", label: "API" },
        { value: "103", label: "官网（H5）" },
        { value: "104", label: "北京移动" },
        { value: "105", label: "批量售卖" },
        { value: "106", label: "推广活动" },
        { value: "110", label: "测试渠道" },
        { value: "111", label: "合作发卡" },
        { value: "112", label: "后付费发卡" },
        { value: "113", label: "WEB" },
        { value: "114", label: "流量池WEB" }
      ],

      columns12: [
        {
          title: "渠道名称",
          key: "corpname",
          align: "center",
          render: (h, params) => {
            return h("span", params.row.corpname === '-'?"合计":params.row.corpname);
          }
        },

        {
          title: "套餐名称",
          key: "packagename",
          align: "center"
        },
        {
          title: "货币",
          key: "currencycode",
          align: "center",
          render: (h, params) => {
            const row = params.row;
            //156 CNY,840 美元, 344 港币
            const text =
              row.currencycode == "156"
                ? "人民币"
                : row.currencycode == "840"
                ? "美元"
                : row.currencycode == "344"
                ? "港币"
                : "-";
            return h("label", text);
          }
        },
        {
          title: "时间",
          key: "statTime",
          align: "center"
        },

        {
          title: "使用量",
          key: "usevolume",
          align: "center"
        },
        {
          title: "当前货币收入",
          key: "salesincome",
          align: "center"
        },
        {
          title: "港币收入",
          key: "hkdincome",
          align: "center"
        }
      ],
      data: [],
      data1: [],
      rules: {}
    };
  },
    created(){
    this.rule.startDate.push( { validator: this.validateDate, trigger: "change" })
    this.rule.endDate.push( { validator: this.validateDate, trigger: "change" })
  },
  mounted() {
    // this.goPageFirst(0);
  },
  methods: {
    resetField(arr) {
      this.$refs["form"].fields.forEach(element => {
        console.log(element.prop);

        if (arr.includes(element.prop)) {
          element.resetField();
        }
      });
    },

    checkDatePicker(date, type) {
      if (Array.isArray(date)) {
        this.form.startDate = date[0];
        this.form.endDate = date[1];
      } else {
        if (type === 1) {
          this.form.startDate = date;
        } else {
          this.form.endDate = date;
        }
      }
    },

    goPageFirst(page) {
      if (page === 0) {
        this.currentPage = 1;
      }
      var _this = this;
      let pageNum = this.currentPage;
      let pageSize = 10;

      this.$refs["form"].validate(valid => {
        if (valid) {
          this.loading = true;

          StatPostpaidsettlePageList({
            pageNum,
            pageSize,
            ...this.form
          })
            .then(res => {
              if (res.code == "0000") {
                _this.loading = false;
                this.page = page;
                this.total = res.data.total;
                this.data = res.data.record;

                if (res.data.records1[0]) {
                  let resObj = res.data.records1[0];
                  let obj = {};
                  for (const key in resObj) {
                    if (Object.hasOwnProperty.call(resObj, key)) {

                      const element = resObj[key];
                      obj[key] = element || "-";
                    }
                  }
                  this.data1 = [obj];
                } else {
                  this.data1 = [];
                }
              }
            })
            .catch(err => {
              console.error(err);
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          this.$Message.error("参数校验不通过");
        }
      });
    },
    goPage(page) {
      this.goPageFirst(page);
    },
    // 搜索
    search() {
      this.goPageFirst(0);
    },
    // 导出
    exportTable() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          StatPostpaidsettleDetailDownload({ ...this.form })
            .then(res => {
              const content = res.data;
              const fileName = "后付费报表.csv"; // 导出文件名
              if ("download" in document.createElement("a")) {
                // 支持a标签download的浏览器
                const link = document.createElement("a"); // 创建a标签
                let url = URL.createObjectURL(content);
                link.download = fileName;
                link.href = url;
                link.click(); // 执行下载
                URL.revokeObjectURL(url); // 释放url
              } else {
                // 其他浏览器
                navigator.msSaveBlob(content, fileName);
              }
            })
            .catch(() => (this.downloading = false));
        }
      });
    },
    details(row) {
      this.$router.push({
        path: "/channel/detailsList"
      });
    }
  }
};
</script>

<style></style>
