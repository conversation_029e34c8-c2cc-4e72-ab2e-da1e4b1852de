<!-- 展开项 -->

<style scoped>
	.expand-row {
		/* margin-bottom: 16px; */
	}
</style>
<template>
	<div>
		<Row class="expand-row">
			<Col span="5">
			<span class="expand-key">激活数： </span>
			<span class="expand-value">{{ row.allcount }}</span>
			</Col>
			<Col span="5">
			<span class="expand-key">激活收入(单位:美元)：</span>
			<span class="expand-value">{{ row.my }}</span>
			</Col>
			<Col span="5">
			<span class="expand-key">激活收入(单位:人民币)： </span>
			<span class="expand-value">{{ row.rmb }}</span>
			</Col>
			<Col span="5">
			<span class="expand-key">激活收入(单位:港币)： </span>
			<span class="expand-value">{{ row.gb }}</span>

			</Col>
			<Col span="4">
			<span class="expand-key">激活总收入(单位:港币)： </span>
			<span class="expand-value">{{ row.allgb }}</span>
			</Col>

			<Col span="5">
			<span class="expand-key">过期数： </span>
			<span class="expand-value">{{ row.cardStatus }}</span>
			</Col>
			<Col span="5">
			<span class="expand-key">过期收入(单位:美元)：</span>
			<span class="expand-value">{{ row.cardType }}</span>
			</Col>
			<Col span="5">
			<span class="expand-key">过期收入(单位:人民币)： </span>
			<span class="expand-value">{{ row.smsTemp }}</span>
			</Col>
			<Col span="5">
			<span class="expand-key">过期收入(单位:港币)： </span>
			<span class="expand-value">{{ row.activeType }}</span>

			</Col>
			<Col span="4">
			<span class="expand-key">过期总收入(单位:港币)： </span>
			<span class="expand-value">{{ row.overdueTime }}</span>
			</Col>
			
			<Col span="5">
			<span class="expand-key">退款收入（美元）负数：</span>
			<span class="expand-value">{{ row.cardStatus }}</span>
			</Col>
			<Col span="5">
			<span class="expand-key">退款收入（人民币）负数：</span>
			<span class="expand-value">{{ row.cardType }}</span>
			</Col>
			<Col span="5">
			<span class="expand-key">退款收入（港币）负数：</span>
			<span class="expand-value">{{ row.smsTemp }}</span>
			</Col>
			<Col span="5">
			<span class="expand-key">退款总收入（港币)：</span>
			<span class="expand-value">{{ row.activeType }}</span>
			</Col>
		</Row>
	</div>
</template>
<script>
	export default {
		props: {
			row: Object
		},
		methods: {
			// 获取列表
			coypVal: function(value) {
				try {
					let Url2 = value;
					let oInput = document.createElement('input');
					oInput.value = Url2;
					document.body.appendChild(oInput);
					oInput.select();
					document.execCommand("Copy");
					oInput.className = 'oInput';
					oInput.style.display = 'none';
					this.$Message.success('复制成功');
				} catch (e) {
					this.$Message.error('复制功能暂不可使用，请手动复制');
				}

			},
		}
	};
</script>
