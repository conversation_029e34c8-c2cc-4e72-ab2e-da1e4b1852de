<template>
  <!-- 套餐分析报表 -->
  <Card>
    <div style="display: flex; width: 100%">
      <Form ref="form" :label-width="0" :model="form" :rules="rule" inline>
        <FormItem prop="cropName">

     <Input
            v-model="form.corpName"
            placeholder="请输入销售主体"
            prop="showTitle"
            clearable
            style="width: 150px"
          />

     
        </FormItem>
        <FormItem prop="type">
          <Select
            v-model="form.type"
            :clearable="true"
            style="width: 200px; text-align: left; margin: 0 10px"
            placeholder="请选择统计维度"
            @on-change="
              date = '';
       resetField(['startTime','endTime'])
            "
          >
            <Option
              v-for="(type, typeIndex) in cycleList"
              :value="type.id"
              :key="typeIndex"
              >{{ type.value }}</Option
            >
          </Select>
        </FormItem>
        <FormItem prop="endTime">
    <FormItem v-if="form.type != '2'" prop="startTime">
          <DatePicker
            format="yyyyMMdd"
            v-model="date"
            v-has="'search'"
            @on-change="checkDatePicker"
            :editable="false"
            type="daterange"
            placeholder="选择时间段"
            clearable
          ></DatePicker>
        </FormItem>
        <FormItem v-if="form.type == '2'" prop="startTime">
          <DatePicker
            format="yyyyMM"
            @on-change="checkDatePicker($event, 1)"
            type="month"
            placement="bottom-start"
            placeholder="请选择开始月份"
            :editable="false"
          ></DatePicker
          >  
        </FormItem>
        <FormItem v-if="form.type == '2'" prop="endTime">
          <DatePicker
            format="yyyyMM"
            @on-change="checkDatePicker($event, 2)"
            type="month"
            placement="bottom-start"
            placeholder="请选择结束月份"
            :editable="false"
          ></DatePicker>
        </FormItem>
        </FormItem>
        <FormItem>
          <Button
            v-has="'search'"
            type="primary"
            icon="md-search"
            size="large"
            @click="search()"
            >搜索</Button
          >
          <Button
            v-has="'export'"
            type="success"
            icon="ios-cloud-download-outline"
            size="large"
            style="margin-left: 20px"
            @click="exportTable()"
            >导出</Button
          >
        </FormItem>
      </Form>
    </div>
    <!-- 表格 -->
    <Table
      :columns="columns12"
      :data="page.showList"
      :ellipsis="true"
      border
      style="margin-top: 50px"
      :loading="loading"
    >
    </Table>
    <!-- 分页 -->
    <div style="margin-left: 38%; margin-top: 100px; margin-bottom: 10px">
      <Page
        show-total
        show-elevator
        @on-change="slicePage"
        :total="page.allPieces"
        :page-size="page.pagePieces"
      />
    </div>

    <Table
      :columns="columns13"
      v-if="data1.length"
      :data="data1"
      :ellipsis="true"
      border
      style="margin: 100px auto"
      :loading="loading"
    >
    </Table>
  </Card>
</template>

<script>
import {
  StatReportPackageAnalysisSearch,
  StatReportPackageAnalysisExport
} from "@/api/report";
import expandRow from "./expordRow.vue";
import mixin from '@/mixin/common'

export default {
  mixins:[mixin],
  components: {
    expandRow
  },
  data() {
    return {
      date: "",
      page: {
        pagePieces: 10,
        allPieces: 0,
        showList: []
      },
      form: {
        cropName: "",
        endTime: "",
        startTime: "",
        type: ""
      },
      rule: {
        startTime: [
          {
            required: true,
            message: "请选择时间"
          }
        ],
        endTime: [
          {
            required: true,
            message: "请选择时间"
          }
        ],
        type: [
          {
            required: true,
            message: "请选择维度"
          }
        ]
      },
      total: 0,
      currentPage: 1,
      loading: false,
      cycleList: [
        {
          id: 1,
          value: "日"
        },
        {
          id: 2,
          value: "月"
        }
      ],
      typeList: [
        { value: "1", label: "普通卡（实体卡）" },
        { value: "2", label: "Esim卡" },
        { value: "3", label: "贴片卡" }
      ],
      sellList: [
        { value: "102", label: "API" },
        { value: "103", label: "官网（H5）" },
        { value: "104", label: "北京移动" },
        { value: "105", label: "批量售卖" },
        { value: "106", label: "推广活动" },
        { value: "110", label: "测试渠道" },
        { value: "111", label: "合作发卡" },
        { value: "112", label: "后付费发卡" },
        { value: "113", label: "WEB" },
        { value: "114", label: "流量池WEB" }
      ],

      columns12: [
        // {
        // 	type: 'expand',
        // 	title: '更多',
        // 	align: 'left',
        // 	width: 80,
        // 	render: (h, params) => {
        // 		return h(expandRow, {
        // 			props: {
        // 				row: params.row
        // 			}
        // 		})
        // 	}
        // },
        {
          title: "套餐id",
          key: "packageId",
          align: "center",
          height: "",
          width: 80
        },
        {
          title: "套餐名称",
          key: "packageName",
          align: "center",
          width: 100
        },
        {
          title: "销售主体",
          key: "corpName",
          align: "center",
          width: 150,
       
        },
        // {
        // 	title: '渠道类型',
        // 	key: 'state',
        // 	align: 'center',
        // 	width: 100
        // },
        {
          title: "时间",
          key: "statTime",
          align: "center",
          width: 150
        },

        {
          title: "销售数",
          key: "salesVolume",
          align: "center",
          width: 80
        },
        {
          title: "销售收入(单位:美元)",
          key: "salesUsdIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
              params.row.salesUsdIncome
              // this.$moneyCover(params.row.salesUsdIncome, 1 / 100)
            );
          }
        },
        {
          title: "销售收入(单位:人民币)",
          key: "salesCnyIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
              params.row.salesCnyIncome
              // this.$moneyCover(params.row.salesCnyIncome, 1 / 100)
            );
          }
        },
        {
          title: "销售收入(单位:港币)",
          key: "salesHkdIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
              params.row.salesHkdIncome
              // this.$moneyCover(params.row.salesHkdIncome, 1 / 100)
            );
          }
        },
        {
          title: "销售总收入(单位:港币)",
          key: "salesTotalIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
            params.row.salesTotalIncome
              // this.$moneyCover(params.row.salesTotalIncome, 1 / 100)
            );
          }
        },
        {
          title: "激活数",
          key: "activeVolume",
          align: "center",
          width: 80
        },
        {
          title: "激活收入(单位:美元)",
          key: "activeUsdIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
              params.row.activeUsdIncome
              // this.$moneyCover(params.row.activeUsdIncome, 1 / 100)
            );
          }
        },
        {
          title: "激活收入(单位:人民币)",
          key: "activeCnyIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
              params.row.activeCnyIncome
              // this.$moneyCover(params.row.activeCnyIncome, 1 / 100)
            );
          }
        },
        {
          title: "激活收入(单位:港币)",
          key: "activeHkdIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
              params.row.activeHkdIncome
              // this.$moneyCover(params.row.activeHkdIncome, 1 / 100)
            );
          }
        },
        {
          title: "激活总收入(单位:港币)",
          key: "activeTotalIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
              params.row.activeTotalIncome
              // this.$moneyCover(params.row.activeTotalIncome, 1 / 100)
            );
          }
        },
        {
          title: "过期数",
          key: "expireVolume",
          align: "center",
          width: 80
        },
        {
          title: "过期收入(单位:美元)",
          key: "expireUsdIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
             params.row.expireUsdIncome
              // this.$moneyCover(params.row.expireUsdIncome, 1 / 100)
            );
          }
        },
        {
          title: "过期收入(单位:人民币)",
          key: "expireCnyIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
              params.row.expireCnyIncome
              // this.$moneyCover(params.row.expireCnyIncome, 1 / 100)
            );
          }
        },
        {
          title: "过期收入(单位:港币)",
          key: "expireHkdIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
          params.row.expireHkdIncome
              // this.$moneyCover(params.row.expireHkdIncome, 1 / 100)
            );
          }
        },
        {
          title: "过期总收入(单位:港币)",
          key: "expireTotalIncome",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h(
              "span",
             params.row.expireTotalIncome
              // this.$moneyCover(params.row.expireTotalIncome, 1 / 100)
            );
          }
        },
        {
          title: "总收入（港币）",
          key: "totalSum",
          align: "center",
          width: 150,
          render: (h, params) => {
            return h("span", params.row.totalSum)
            // return h("span", this.$moneyCover(params.row.totalSum, 1 / 100));
          }
        }
      ],

      columns13: [
        {
          title: "套餐id",
          key: "packageId",
          align: "center",
          height: "",
          width: 80,
          render: (h, params) => {
            return h("span", "合计");
          }
        },

        {
          title: "销售数",
          key: "sumSalesVolume",
          align: "center",
          width: 80
        },
        {
          title: "销售收入(单位:美元)",
          key: "sumSalesUsdIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumSalesUsdIncome
            );
          }
        },
        {
          title: "销售收入(单位:人民币)",
          key: "sumSalesCnyIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumSalesCnyIncome
            );
          }
        },
        {
          title: "销售收入(单位:港币)",
          key: "sumSalesHkdIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumSalesHkdIncome
            );
          }
        },
        {
          title: "销售总收入(单位:港币)",
          key: "sumSalesTotalIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumSalesTotalIncome
            );
          }
        },
        {
          title: "激活数",
          key: "sumActiveVolume",
          align: "center",
          width: 80
        },
        {
          title: "激活收入(单位:美元)",
          key: "sumActiveUsdIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumActiveUsdIncome
            );
          }
        },
        {
          title: "激活收入(单位:人民币)",
          key: "sumActiveCnyIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumActiveCnyIncome
            );
          }
        },
        {
          title: "激活收入(单位:港币)",
          key: "sumActiveHkdIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumActiveHkdIncome
            );
          }
        },
        {
          title: "激活总收入(单位:港币)",
          key: "sumActiveTotalIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumActiveTotalIncome
            );
          }
        },
        {
          title: "过期数",
          key: "sumExpireVolume",
          align: "center",
          width: 80
        },
        {
          title: "过期收入(单位:美元)",
          key: "sumExpireUsdIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumExpireUsdIncome
            );
          }
        },
        {
          title: "过期收入(单位:人民币)",
          key: "sumExpireCnyIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumExpireCnyIncome
            );
          }
        },
        {
          title: "过期收入(单位:港币)",
          key: "sumExpireHkdIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumExpireHkdIncome
            );
          }
        },
        {
          title: "过期总收入(单位:港币)",
          key: "sumExpireTotalIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h(
              "span",
             params.row.sumExpireTotalIncome
            );
          }
        },
        {
          title: "总收入（港币）",
          key: "totalIncome",
          align: "center",
          width: 100,
          render: (h, params) => {
            return h("span",params.row.totalIncome);
          }
        }
      ],
      data: [],
      data1: [],
      list: [],
      rules: {}
    };
  },
    created(){
    this.rule.startTime.push( { validator: this.validateDate, trigger: "change" })
    this.rule.endTime.push( { validator: this.validateDate, trigger: "change" })
  },
  mounted() {
    // this.goPageFirst(0);
  },
  methods: {
    resetField (arr){
  this.$refs['form'].fields.forEach(element => {

    if(arr.includes(element.prop)  ){
      element.resetField();
    }
    
  });
  },
  
    checkDatePicker(date, type) {
      if (Array.isArray(date)) {
        this.form.startTime = date[0];
        this.form.endTime = date[1];
      } else {
        if (type === 1) {
          this.form.startTime = date;
        } else {
          this.form.endTime = date;
        }
      }
    },

    slicePage(page) {
      // 开始节点
      let startPage = page * this.page.pagePieces - this.page.pagePieces;
      // 结束节点
      let endPage = page * this.page.pagePieces;
      // 获取n页的数据
      this.page.showList = this.list.slice(startPage, endPage);
    },

    goPageFirst(page) {
      if (page === 0) {
        this.currentPage = 1;
      }
      var _this = this;
      let pageNum = this.currentPage;
      let pageSize = 10;

      this.$refs["form"].validate(valid => {
        if (valid) {
          this.loading = true;

          StatReportPackageAnalysisSearch({
            pageNum,
            pageSize,
            ...this.form
          })
            .then(res => {
              if (res.code == "0000") {
                _this.loading = false;
                this.data1 = [res.data.sumAnalyzeDataDTO];
                this.list =
                  res.data.analyzeDayList || res.data.analyzeMonthList;
                this.page.allPieces = this.list.length;
                this.page.showList = this.list.slice(0, this.page.pagePieces);
              }
            })
            .catch(err => {
              if (err.code === "1000") {
                this.page = {
                  pagePieces: 10,
                  allPieces: 0,
                  showList: []
                };
                this.data1 = [];
              }
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          this.$Message.error("参数校验不通过");
        }
      });
    },
    goPage(page) {
      this.goPageFirst(page);
    },
    // 搜索
    search() {
      this.goPageFirst(0);
    },
    // 导出
    exportTable() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          StatReportPackageAnalysisExport({
            ...this.form
          })
            .then(res => {
              const content = res.data;
              const fileName = "套餐分析报表.csv"; // 导出文件名
              if ("download" in document.createElement("a")) {
                // 支持a标签download的浏览器
                const link = document.createElement("a"); // 创建a标签
                let url = URL.createObjectURL(content);
                link.download = fileName;
                link.href = url;
                link.click(); // 执行下载
                URL.revokeObjectURL(url); // 释放url
              } else {
                // 其他浏览器
                navigator.msSaveBlob(content, fileName);
              }
            })
            .catch(() => (this.downloading = false));
        }
      });
    },
    details(row) {
      this.$router.push({
        path: "/channel/detailsList"
      });
    }
  }
};
</script>

<style></style>
