<template>
  <!-- 卡销售报表 -->
  <Card>
    <div style="display: flex; width: 100%">
      <Form ref="form" :label-width="0" :model="form" :rules="rule" inline>
        <FormItem prop="saleChannel">
          <Select
            v-model="form.saleChannel"
            :clearable="true"
            style="width: 200px; text-align: left; margin: 0 10px"
            placeholder="请选择销售渠道"
          >
            <Option
              v-for="(type, typeIndex) in sellList"
              :value="type.value"
              :key="typeIndex"
              >{{ type.label }}</Option
            >
          </Select>
        </FormItem>
        <FormItem prop="unit">
          <Select
            v-model="form.unit"
            :clearable="true"
            @on-change="
              date = '';
              resetField(['startTime','endTime'])
          
            "
            style="width: 200px; text-align: left; margin: 0 10px"
            placeholder="请选择统计维度"
          >
            <Option
              v-for="(type, typeIndex) in cycleList"
              :value="type.id"
              :key="typeIndex"
              >{{ type.value }}</Option
            >
          </Select>
        </FormItem>
        <FormItem prop="cardForm">
          <Select
            v-model="form.cardForm"
            :clearable="true"
            style="width: 200px; text-align: left; margin: 0 10px"
            placeholder="请选择卡类别"
          >
            <Option
              v-for="(type, typeIndex) in typeList"
              :value="type.value"
              :key="typeIndex"
              >{{ type.label }}</Option
            >
          </Select>
        </FormItem>
        <FormItem prop="endTime">
        <FormItem v-if="form.unit != '1'" prop="startTime">
          <DatePicker
            format="yyyyMMdd"
            v-model="date"
            v-has="'search'"
            @on-change="checkDatePicker"
            :editable="false"
            type="daterange"
            placeholder="选择时间段"
            clearable
          ></DatePicker>
        </FormItem>
        <FormItem v-if="form.unit == '1'" prop="startTime">
          <DatePicker
            format="yyyyMM"
            @on-change="checkDatePicker($event, 1)"
            type="month"
            placement="bottom-start"
            placeholder="请选择开始月份"
            :editable="false"
          ></DatePicker
          >  
        </FormItem>
        <FormItem v-if="form.unit == '1'" prop="endTime">
          <DatePicker
            format="yyyyMM"
            @on-change="checkDatePicker($event, 2)"
            type="month"
            placement="bottom-start"
            placeholder="请选择结束月份"
            :editable="false"
          ></DatePicker>
        </FormItem>
        </FormItem>
        <FormItem>
          <Button
            v-has="'search'"
            type="primary"
            icon="md-search"
            size="large"
            @click="search()"
            >搜索</Button
          >
          <Button
            v-has="'export'"
            type="success"
            icon="ios-cloud-download-outline"
            size="large"
            style="margin-left: 20px"
            @click="exportTable()"
            >导出</Button
          >
        </FormItem>
      </Form>
    </div>
    <!-- 表格 -->
    <Table
      :columns="columns12"
      :data="data"
      style="width: 100%; margin-top: 50px"
      :loading="loading"
    >
    </Table>

    <!-- 分页 -->
    <div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px">
      <Page
        :total="total"
        :current.sync="currentPage"
        show-total
        show-elevator
        @on-change="goPage"
      />
    </div>

    <Table
      v-if="data1.length"
      :columns="columns12"
      :data="data1"
      style="width: 100%; margin-top: 50px"
      :loading="loading"
    >
    </Table>
  </Card>
</template>

<script>
import { StatCardReport, StatCardReportExport } from "@/api/report";
import mixin from '@/mixin/common'

export default {
  mixins:[mixin],
  data() {
    return {
      date: "",
      loading: false,
      form: {
        cardForm: null,
        endTime: "",
        saleChannel: null,
        startTime: "",
        unit: ""
      },
      rule: {
        startTime: [
          {
            required: true,
            message: "请选择时间"
          }
        ],
        endTime: [
          {
            required: true,
            message: "请选择时间"
          }
        ],
        unit: [
          {
            required: true,
            message: "请选择维度"
          }
        ]
      },
      total: 0,
      currentPage: 1,
      cycleList: [
        {
          id: 2,
          value: "日"
        },
        {
          id: 1,
          value: "月"
        }
      ],
      typeList: [
        { value: "1", label: "普通卡（实体卡）" },
        { value: "2", label: "Esim卡" },
        { value: "3", label: "贴片卡" },
        { value: "4", label: "IMSI号" }
      ],
      sellList: [
        { value: "102", label: "API" },
        { value: "103", label: "官网（H5）" },
        { value: "104", label: "北京移动" },
        { value: "105", label: "批量售卖" },
        { value: "106", label: "推广活动" },
        { value: "110", label: "测试渠道" },
        { value: "111", label: "合作发卡" },
        { value: "112", label: "后付费发卡" },
        { value: "113", label: "WEB" },
        { value: "114", label: "流量池WEB" },
      ],

      columns12: [
        {
          title: "卡类型",
          key: "cardForm",
          align: "center",
          render: (h, params) => {
            const obj = {
              1: "普通卡（实体卡）",
              2: "Esim卡",
              3: "贴片卡",
			  4: "IMSI号"
            };
            return h(
              "span",
              params.row.cardForm ? obj[params.row.cardForm] : "合计"
            );
          }
        },
        {
          title: "销售渠道",
          key: "salesChannel",
          align: "center",
                render: (h, params) => {
            const obj = {
              102: "API",
              103: "官网（H5）",
              104: "北京移动",
              105: "批量售卖",
              106: "推广活动",
              110: "测试渠道",
              111: "合作发卡",
              112: "后付费发卡",
              113: "WEB",
              114: "流量池WEB",
          
            };
            return h(
              "span",
               obj[params.row.salesChannel]
            );
          }
        },
        {
          title: "时间",
          key: "statTime",
          align: "center"
        },
        {
          title: "本期销量",
          key: "salesVolume",
          align: "center"
        },
        {
          title: "港币收入",
          key: "hkdIncome",
          align: "center",
            render: (h, params) => {
            return h(
              "span",
              params.row.hkdIncome.toFixed(6)
            );
          }
        },
        {
          title: "人民币收入",
          key: "cnyIncome",
          align: "center",
            render: (h, params) => {
            return h(
              "span",
              params.row.cnyIncome.toFixed(6)
            );
          }
        },
        {
          title: "美元收入",
          key: "usdIncome",
          align: "center",
            render: (h, params) => {
            return h(
              "span",
              params.row.usdIncome.toFixed(6)
            );
          }
        },
        {
          title: "总收入(单位港币)",
          key: "totalIncome",
          align: "center",
            render: (h, params) => {
            return h(
              "span",
              params.row.totalIncome+''
            );
          }
        }
      ],
      data: [],
      data1: []
    };
  },
    created(){
    this.rule.startTime.push( { validator: this.validateDate, trigger: "change" })
    this.rule.endTime.push( { validator: this.validateDate, trigger: "change" })
  },
  mounted() {
    // this.goPageFirst(0);
  },
  methods: {
    resetField (arr){
  this.$refs['form'].fields.forEach(element => {

    if(arr.includes(element.prop)  ){
      element.resetField();
    }
    
  });
  },
       checkDatePicker(date, type) {
      if (Array.isArray(date)) {
        this.form.startTime = date[0];
        this.form.endTime = date[1];
      } else {
        if (type === 1) {
          this.form.startTime = date;
        } else {
          this.form.endTime = date;
        }
      }
    },

    goPageFirst(page) {
      if (page === 0) {
        this.currentPage = 1;
      }
      var _this = this;
      let pageNum = this.currentPage;
      let pageSize = 10;

      this.$refs["form"].validate(valid => {
        if (valid) {
          this.loading = true;
          StatCardReport({
            pageNum,
            pageSize,
            ...this.form
          })
            .then(res => {
              if (res.code == "0000") {
                _this.loading = false;
                this.page = page;
                this.total = res.data.total;
                this.data = res.data.record[0].cardSaleDTO;
                if(res.data.record[0].cardSaleTotal){
                this.data1 =[res.data.record[0].cardSaleTotal ];
                }else{
                   this.data1 = [];
                }
              }
            })
            .catch(err => {
              console.error(err);
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          this.$Message.error("参数校验不通过");
        }
      });
    },
    goPage(page) {
      this.goPageFirst(page);
    },
    // 搜索
    search() {
      this.goPageFirst(0);
    },
    // 导出
    exportTable() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          StatCardReportExport({
            ...this.form
          })
            .then(res => {
              const content = res.data;
              const fileName = "卡销售报表.csv"; // 导出文件名
              if ("download" in document.createElement("a")) {
                // 支持a标签download的浏览器
                const link = document.createElement("a"); // 创建a标签
                let url = URL.createObjectURL(content);
                link.download = fileName;
                link.href = url;
                link.click(); // 执行下载
                URL.revokeObjectURL(url); // 释放url
              } else {
                // 其他浏览器
                navigator.msSaveBlob(content, fileName);
              }
            })
            .catch(() => (this.downloading = false));
        }
      });
    },
    details(row) {
      this.$router.push({
        path: "/channel/detailsList"
      });
    }
  }
};
</script>

<style></style>
