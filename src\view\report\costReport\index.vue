<template>
  <!-- 成本报表 -->
  <Card>
    <Form ref="form" :label-width="90" :model="form" :rules="rule" inline>
      <FormItem label="资源供应商:" prop="supplierId">
        <Select v-model="form.supplierId" style="width: 200px;" filterable clearable placeholder="请选择资源供应商">
          <Option v-for="item in supplierList" :value="item.supplierId" :key="item.supplierId">{{item.supplierName}}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="统计维度:"  prop="dimension" >
        <Select @on-change="changeDimension" filterable v-model="form.dimension" placeholder="下拉选择统计维度" clearable>
      	  <Option :value="item.value" v-for="(item,index) in cycleList"  :key="index">{{item.label}}</Option>
        </Select>
      </FormItem>
      <FormItem v-if="form.dimension === '1'" label="时间段:"  prop="timeRangeArray">
      <DatePicker format="yyyyMMdd" v-model="form.timeRangeArray" @on-change="handleDateChange" :editable="false" type="daterange" placeholder="选择时间段"
        clearable style="width: 200px ;margin: 0 10px 0 0;" @on-clear="hanldeDateClear"></DatePicker>
      </FormItem>
      <FormItem v-if="form.dimension === '2'" label="开始月份:"  prop="dateStart">
      <DatePicker
        format="yyyyMM"
        v-model="form.dateStart"
        type="month"
        placement="bottom-start"
        placeholder="请选择开始月份"
        @on-change="handleChangeBeginMonth"
        :editable="false"
      ></DatePicker>  
      </FormItem>
      <FormItem v-if="form.dimension === '2'" label="结束月份:"  prop="dateEnd">
      <DatePicker
        format="yyyyMM"
        v-model="form.dateEnd"
        type="month"
        placement="bottom-start"
        placeholder="请选择结束月份"
        @on-change="handleChangeEndMonth"
        :editable="false"
      ></DatePicker>
      </FormItem>
      <Button type="primary" icon="md-search" :loading="searchLoading" @click="search('form')" v-has="'search'">搜索</Button>&nbsp;&nbsp;
      <Button type="success" icon="ios-cloud-download-outline" style="margin-left: 20px" @click="exportTable()"
        :loading="exportLoading"  v-has="'export'">导出</Button>
    </Form>
    <!-- 表格 -->
    <Table :columns="columns" :data="data" style="width: 100%; margin-top: 30px;" :loading="loading">
    </Table>
    <!-- 分页 -->
    <div style="margin-left: 38%; margin-top: 50px; margin-bottom: 160px">
      <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
    </div>
  </Card>
</template>

<script>
  import {
    getCostReport,
    costReportDownload,
  } from "@/api/report";
  import {
    supplier
  } from '@/api/ResourceSupplier'
  export default {
    data() {
      return {
        date: "",
        loading: false,
        searchLoading: false,
        exportLoading: false,
        supplierList: [],
        form: {
          supplierId: "",
          dimension: "",
          timeRangeArray: [],
          dateStart: '',
          dateEnd: ''
        },
        total: 0,
        currentPage: 1,
        cycleList: [
        	{
        	  value: "1",
        	  label: "日",
        	},
        	{
        	  value: "2",
        	  label: "月",
        	},
        ],
        columns: [{
            title: "时间",
            key: "statDate",
            align: "center",
            minWidth: 150,
            tooltip: true,
            render: (h, params) => {
            	const row = params.row;
            	var text = "";
            	if (row.statDate.length === 6) {
            	  // 6位长度，YYYYMM -> yyyy-mm
            	  const year = row.statDate.substring(0, 4);
            	  const month = row.statDate.substring(4, 6);
            	  text = `${year}-${month.padStart(2, '0')}`;
            	} else {
            	  // 8位长度，YYYYMMDD -> YYYY-MM-DD
            	  const year = row.statDate.substring(0, 4);
            	  const month = row.statDate.substring(4, 6);
            	  const day = row.statDate.substring(6, 8);
            	  text = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
              }
            	return h("label", text);
            },
          },
          {
            title: "供应商",
            key: "supplierName",
            align: "center",
            minWidth: 150,
            tooltip: true,
          },
          {
          	title: "计费方式",
            key: "costType",
          	align: "center",
          	minWidth: 120,
          	tooltip: true,
          },
          {
            title: "国家",
            key: "countryName",
            align: "center",
            minWidth: 150,
            tooltip: true,
          },
          {
            title: "运营商",
            key: "operatorName",
            align: "center",
            minWidth: 150,
            tooltip: true,
          },
          {
            title: "单价/总价",
            key: "price",
            align: "center",
            minWidth: 150,
            tooltip: true,
          },
          {
            title: "流量(MB)",
            key: "flowByte",
            align: "center",
            minWidth: 150,
            tooltip: true,
          },
          {
          	title: "计费流量上限",
            key: "upperLimit",
          	align: "center",
          	minWidth: 150,
          	tooltip: true,
          },
          {
            title: "币种",
            key: "currency",
            align: "center",
            minWidth: 120,
            tooltip: true,
          },
          {
            title: "当前币种总价",
            key: "currentAmount",
            align: "center",
            minWidth: 120,
            tooltip: true,
          },
          {
            title: "港币总价",
            key: "hkdAmount",
            align: "center",
            minWidth: 120,
            tooltip: true,
          },
          {
            title: "美元总价",
            key: "usdAmount",
            align: "center",
            minWidth: 120,
            tooltip: true,
          },
        ],
        data: [],
        rule: {
          dimension: [{ required: true, message: '请选择维度', trigger: 'blur'}],
          timeRangeArray: [
          	{ type: 'array',required: true, message: '请选择时间', trigger: 'blur',
          	  fields: {
          			0: {type: 'date', required: true, message: '请选择开始日期'},
          			1: {type: 'date', required: true, message: '请选择结束日期'}
          		}
          	}
          ],
          dateStart: [{ type: 'date',required: true, message: '请选择开始月份', trigger: 'blur'}],
          dateEnd: [{ type: 'date',required: true, message: '请选择结束月份', trigger: 'blur'}],
        },
        searchBeginTime: '',
        searchEndTime: '',
      };
    },
    mounted() {
      this.getsupplier()
    },
    methods: {
      //切换维度
      changeDimension(){
        this.form.timeRangeArray = ''
        this.form.dateStart = ''
        this.form.dateEnd = ''
      },
      // 获取开始月份
      handleChangeBeginMonth(month) {
      	this.searchBeginTime = month;
      },
      // 获取结束月份
      handleChangeEndMonth(month) {
      	this.searchEndTime = month;
      },
      hanldeDateClear() {
      	this.searchBeginTime = ''
      	this.searchEndTime = ''
      },
      handleDateChange(dateArr) {
      	let beginDate = this.form.timeRangeArray[0] || ''
      	let endDate = this.form.timeRangeArray[1] || ''
      	if (beginDate == '' || endDate == '') {
      	return
      	}
      	[this.searchBeginTime, this.searchEndTime] = dateArr
      },
      checkDatePicker(date) {
      	this.form.startDate = date[0];
      	this.form.endDate = date[1];
      },
      // 获取某年某月的最后一天
      appendLastDayOfMonth(yearMonth) {
        const year = parseInt(yearMonth.slice(0, 4), 10);
        const month = parseInt(yearMonth.slice(4), 10) - 1;
        const date = new Date(year, month + 1, 0); // 直接设置为下个月的第0天来获取本月的最后一天
        return date.getFullYear().toString().padStart(4, '0') + (date.getMonth() + 1).toString().padStart(2, '0') +
        date.getDate().toString().padStart(2, '0').slice(0, 2);

      },
      goPageFirst(page) {
        this.searchLoading = true;
        this.loading = true;
        var _this = this;
        let pageSize = 10;
        getCostReport({
            size: 10,
            current: page,
            supplierId: _this.form.supplierId,
            dimension: _this.form.dimension,
            dateStart: _this.form.dimension == '1' ? _this.searchBeginTime : _this.searchBeginTime + '01',
            dateEnd: _this.form.dimension == '1' ? _this.searchEndTime : this.appendLastDayOfMonth(_this.searchEndTime)
          }).then((res) => {
            if (res.code == "0000") {
              _this.currentPage = page
              _this.loading = false;
              _this.searchLoading = false;
              _this.data = res.data;
              _this.total = Number(res.count);
            }
          })
          .catch((err) => {
            console.error(err);
            _this.data = [];
            _this.total = 0
          })
          .finally(() => {
            _this.loading = false;
            _this.searchLoading = false;
          });
      },
      goPage(page) {
        this.goPageFirst(page);
      },
      // 搜索
      search(name) {
        if (this.searchBeginTime > this.searchEndTime) {
          this.$Message.warning('开始时间不能大于结束时间')
          return
        }
        this.$refs[name].validate((valid) => {
          if (valid) {
            this.goPageFirst(1)
          }
        })
      },
      // 导出
      exportTable() {
        if (this.searchBeginTime > this.searchEndTime) {
          this.$Message.warning('开始时间不能大于结束时间')
          return
        }
        this.exportLoading = true
        var _this = this
        costReportDownload({
            supplierId: _this.form.supplierId,
            dimension: _this.form.dimension,
            dateStart: _this.form.dimension == '1' ? _this.searchBeginTime : _this.searchBeginTime + '01',
            dateEnd: _this.form.dimension == '1' ? _this.searchEndTime : this.appendLastDayOfMonth(_this.searchEndTime)
          }).then((res) => {
            const content = res.data;
            let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
            if ("download" in document.createElement("a")) {
              // 支持a标签download的浏览器
              const link = document.createElement('a'); // 创建a标签
              let url = URL.createObjectURL(content);
              link.download = fileName;
              link.href = url;
              link.click(); // 执行下载
              URL.revokeObjectURL(url); // 释放url
              this.exportLoading = false
            } else {
              // 其他浏览器
              navigator.msSaveBlob(content, fileName);
            }
          })
          .catch(() => (this.exportLoading = false));
      },
      // 获取资源供应商
      getsupplier() {
        supplier({
          pageNum: -1,
          pageSize: -1,
        }).then(res => {
          if (res.code == '0000') {
            this.supplierList = res.data
          }
        }).catch((err) => {
          console.error(err)
        }).finally(() => {})
      },
    },
  };
</script>

<style>
</style>
