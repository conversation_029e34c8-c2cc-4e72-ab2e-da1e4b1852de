<template>
  <!-- ESIM Download报表 -->
  <Card>
    <Form ref="form" :label-width="90" :model="form" :rules="rule" inline>
      <FormItem label="统计维度:"  prop="dimension" >
        <Select @on-change="changeDimension" filterable v-model="form.dimension" placeholder="下拉选择统计维度" clearable>
      	  <Option :value="item.value" v-for="(item,index) in cycleList"  :key="index">{{item.label}}</Option>
        </Select>
      </FormItem>
      <FormItem v-if="form.dimension === '1'" label="时间段:"  prop="timeRangeArray">
      <DatePicker format="yyyyMMdd" v-model="form.timeRangeArray" @on-change="handleDateChange" :editable="false" type="daterange" placeholder="选择时间段"
        clearable style="width: 200px ;margin: 0 10px 0 0;" @on-clear="hanldeDateClear"></DatePicker>
      </FormItem>
      <FormItem v-if="form.dimension === '2'" label="开始月份:"  prop="dateStart">
      <DatePicker
        format="yyyyMM"
        v-model="form.dateStart"
        type="month"
        placement="bottom-start"
        placeholder="请选择开始月份"
        @on-change="handleChangeBeginMonth"
        :editable="false"
      ></DatePicker>  
      </FormItem>
      <FormItem v-if="form.dimension === '2'" label="结束月份:"  prop="dateEnd">
      <DatePicker
        format="yyyyMM"
        v-model="form.dateEnd"
        type="month"
        placement="bottom-start"
        placeholder="请选择结束月份"
        @on-change="handleChangeEndMonth"
        :editable="false"
      ></DatePicker>
      </FormItem>&nbsp;&nbsp;&nbsp;&nbsp;
       <!-- v-has="'search'"  v-has="'export'" -->
      <Button type="primary" icon="md-search" :loading="searchLoading" @click="search('form')" v-has="'search'">搜索</Button>&nbsp;&nbsp;
      <Button type="success" icon="ios-cloud-download-outline" style="margin-left: 20px" @click="exportTable()"
        v-has="'export'">导出</Button>
    </Form>
    <!-- 表格 -->
    <Table :columns="columns" :data="data" style="width: 100%; margin-top: 30px;" :loading="loading">
    </Table>
    <!-- 分页 -->
    <div style="margin-left: 38%; margin-top: 50px; margin-bottom: 160px">
      <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
    </div>
    <!-- 导出提示 -->
    <Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
    	<div style="align-items: center;justify-content:center;display: flex;">
    		<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
    			<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
    			<FormItem label="你本次导出任务ID为:">
    				<span style="width: 100px;">{{taskId}}</span>
    			</FormItem>
    			<FormItem label="你本次导出的文件名为:">
    				<span>{{taskName}}</span>
    			</FormItem>
    			<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
    		</Form>
    	</div>

    	<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
    		<Button @click="cancelModal">取消</Button>
    		<Button type="primary" @click="Goto">立即前往</Button>
    	</div>
    </Modal>
  </Card>
</template>

<script>
  import {
    getEsimReport,
    esimReportDownload,
  } from "@/api/report";
  export default {
    data() {
      return {
        date: "",
        searchBeginTime: '',
        searchEndTime: '',
        taskId: '',
        taskName: '',
        loading: false,
        searchLoading: false,
        exportModal: false,
        form: {
          dimension: "",
          timeRangeArray: [],
          dateStart: '',
          dateEnd: ''
        },
        total: 0,
        currentPage: 1,
        cycleList: [
        	{
        	  value: "1",
        	  label: "日",
        	},
        	{
        	  value: "2",
        	  label: "月",
        	},
        ],
        columns: [{
            title: "时间",
            key: "date",
            align: "center",
            minWidth: 150,
            tooltip: true,
          },
          {
            title: "首次下载",
            key: "downloadNum",
            align: "center",
            minWidth: 150,
            tooltip: true,
          },
          {
          	title: "首次安装",
            key: "installNum",
          	align: "center",
          	minWidth: 120,
          	tooltip: true,
          },
          {
            title: "首次激活",
            key: "activeNum",
            align: "center",
            minWidth: 150,
            tooltip: true,
          },
        ],
        data: [],
        rule: {
          dimension: [{ required: true, message: '请选择维度', trigger: 'blur'}],
          timeRangeArray: [
          	{ type: 'array',required: true, message: '请选择时间', trigger: 'blur',
          	  fields: {
          			0: {type: 'date', required: true, message: '请选择开始日期'},
          			1: {type: 'date', required: true, message: '请选择结束日期'}
          		}
          	}
          ],
          dateStart: [{ type: 'date',required: true, message: '请选择开始月份', trigger: 'blur'}],
          dateEnd: [{ type: 'date',required: true, message: '请选择结束月份', trigger: 'blur'}],
        },
      };
    },
    mounted() {
    },
    methods: {
      //切换维度
      changeDimension(){
        this.form.timeRangeArray = ''
        this.form.dateStart = ''
        this.form.dateEnd = ''
      },
      // 获取开始月份
      handleChangeBeginMonth(month) {
      	this.searchBeginTime = month;
      },
      // 获取结束月份
      handleChangeEndMonth(month) {
      	this.searchEndTime = month;
      },
      hanldeDateClear() {
      	this.searchBeginTime = ''
      	this.searchEndTime = ''
      },
      handleDateChange(dateArr) {
      	let beginDate = this.form.timeRangeArray[0] || ''
      	let endDate = this.form.timeRangeArray[1] || ''
      	if (beginDate == '' || endDate == '') {
      	return
      	}
      	[this.searchBeginTime, this.searchEndTime] = dateArr
      },
      checkDatePicker(date) {
      	this.form.startDate = date[0];
      	this.form.endDate = date[1];
      },
      //处理时间格式
      reformatDateWithRegex(dateStr) {
        if (dateStr.length === 6) {
          // 对于YYYYMM格式，使用简单的替换
          return dateStr.replace(/(\d{4})(\d{2})/, '$1-$2');
        } else if (dateStr.length === 8) {
          // 对于YYYYMMDD格式，使用原始的正则表达式
          return dateStr.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3');
        }
      },
      goPageFirst(page) {
        this.loading = true;
        var _this = this;
        getEsimReport({
            pageNum: page,
            pageSize: 10,
            beginDate: this.reformatDateWithRegex(_this.searchBeginTime),
            endDate: this.reformatDateWithRegex(_this.searchEndTime)
          }).then((res) => {
            if (res.code == "0000") {
              _this.currentPage = page
              _this.loading = false;
              _this.searchLoading = false;
              _this.data = res.data;
              _this.total = Number(res.count);
            }
          })
          .catch((err) => {
            console.error(err);
            _this.data = [];
            _this.total = 0
          })
          .finally(() => {
            _this.loading = false;
            _this.searchLoading = false;
          });
      },
      goPage(page) {
        this.goPageFirst(page);
      },
      // 搜索
      search(name) {
        if (this.searchBeginTime > this.searchEndTime) {
          this.$Message.warning('开始时间不能大于结束时间')
          return
        }
        this.$refs[name].validate((valid) => {
          if (valid) {
            this.searchLoading = true;
            this.goPageFirst(1)
          }
        })
      },
      // 导出
      exportTable() {
        if (this.searchBeginTime > this.searchEndTime) {
          this.$Message.warning('开始时间不能大于结束时间')
          return
        }
        this.$refs['form'].validate((valid) => {
          if (valid) {
            var _this = this
            esimReportDownload({
                corpId: this.$store.state.user.userId,
                beginDate: this.reformatDateWithRegex(_this.searchBeginTime),
                endDate: this.reformatDateWithRegex(_this.searchEndTime)
              }).then((res) => {
                if (res.code == '0000') {
                  this.exportModal = true
                  this.taskId = res.data.taskId
                  this.taskName = res.data.taskName
                }
              })
              .catch();
          }
        })
      },
      Goto() {
      	this.$router.push({
      	  path: '/taskList',
      	  query: {
      	    taskId: encodeURIComponent(this.taskId),
      	    fileName: encodeURIComponent(this.taskName),
      	  }
      	});
      },
      cancelModal() {
        this.exportModal = false
      },
    },
  };
</script>

<style>
</style>

