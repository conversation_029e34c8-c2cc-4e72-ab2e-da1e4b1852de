<template>
  <!-- 汇率管理 -->
  <Card>
    <div style="display: flex">
      <!-- <span style="margin-top: 4px;">币种类型</span>&nbsp;&nbsp; -->

      <Form ref="form" :label-width="0" :model="form" :rules="rule" inline>
        <FormItem prop="currencyCode">
          <Select v-model="form.currencyCode" style="width: 200px; text-align: left; margin: 0 10px" :clearable="true"
            placeholder="请选择币种类型">
            <Option v-for="(type, typeIndex) in typeList" :value="type.id" :key="typeIndex">{{ type.value }}</Option>
          </Select>
        </FormItem>
        <FormItem prop="date">
          <DatePicker type="month" placeholder="请选择月份" format="yyyyMM" style="width: 200px"
            @on-change="checkDatePicker"></DatePicker>&nbsp;&nbsp;
        </FormItem>

        <FormItem>
          <Button v-has="'search'" type="primary" icon="md-search" size="large" @click="search()">搜索</Button>
          <Button v-has="'export'" type="success" icon="ios-cloud-download-outline" size="large"
            style="margin-left: 20px" @click="exportTable()">导出</Button>

          <Button type="success" icon="ios-cloud-upload-outline" size="large" style="margin-left: 20px"
            @click="openModal">导入</Button>
        </FormItem>
      </Form>
    </div>
    <!-- 表格 -->
    <Table :columns="columns12" :data="data" style="width: 100%; margin-top: 50px" :loading="loading">
    </Table>
    <!-- 分页 -->
    <div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px">
      <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
    </div>


    <Modal title="汇率导入" v-model="importExchangeFlag" :footer-hide="true" :mask-closable="false"
      @on-cancel="cancelModal">
      <div style="padding: 0 16px">
        <Form ref="editObj" :model="editObj" :label-width="100" :rules="ruleEditValidate">

          <FormItem label="汇率导入月份" prop="month">
            <DatePicker type="month" v-model="editObj.month" @on-change="checkMonth" format="yyyyMM" placeholder="请选择月份"
              style="width: 100%"></DatePicker>
          </FormItem>
          <FormItem label="美元兑港币" prop="UsdExchangeHkd">
            <Input v-model="editObj.UsdExchangeHkd" :clearable="true" maxlength="13" placeholder="请输入汇率"></Input>
          </FormItem>
          <FormItem label="人民币兑港币" prop="CnyExchangeHkd">
            <Input v-model="editObj.CnyExchangeHkd" :clearable="true" maxlength="13" placeholder="请输入汇率"></Input>
          </FormItem>
          <FormItem label="人民币兑美元" prop="CnyExchangeUsd">
            <Input v-model="editObj.CnyExchangeUsd" :clearable="true" maxlength="13" placeholder="请输入汇率"></Input>
          </FormItem>
          <FormItem label="欧元兑港币" prop="EurExchangeHkd">
            <Input v-model="editObj.EurExchangeHkd" :clearable="true" maxlength="13" placeholder="请输入汇率"></Input>
          </FormItem>
          <FormItem label="欧元兑美元" prop="EurExchangeUsd">
            <Input v-model="editObj.EurExchangeUsd" :clearable="true" maxlength="13" placeholder="请输入汇率"></Input>
          </FormItem>
        </Form>
        <div style="text-align: center">
          <Button type="primary" @click="importSubmit">提交</Button>
          <Button style="margin-left: 8px" @click="reset('editObj')">重置</Button>
        </div>
      </div>
    </Modal>

  </Card>
</template>

<script>
  import {
    StatRate,
    StatRateExport,
    importRate
  } from "@/api/report";
  export default {
    data() {
      return {
        form: {
          currencyCode: null,
          date: "",
        },
        rule: {
          date: [{
            required: true,
            message: "请选择时间",
          }, ],
        },
        importExchangeFlag: false,
        editObj: {
          month: "",
          UsdExchangeHkd: "",
          CnyExchangeHkd: "",
          CnyExchangeUsd: "",
          EurExchangeHkd: "",
          EurExchangeUsd: "",
        },
        formmodel: {},
        total: 0,
        currentPage: 1,
        page: 0,
        updatemodel: false,
        typeList: [{
            id: 1,
            value: "人民币兑港币",
          },

          {
            id: 2,
            value: "美元兑港币",
          },
          {
            id: 3,
            value: "人民币兑美元",
          },
          {
            id: 4,
            value: "欧元兑港币",
          },
          {
            id: 5,
            value: "欧元兑美元",
          },
        ],
        loading: false,
        columns12: [{
            title: "月份",
            key: "rateTime",
            align: "center",
          },
          {
            title: "币种",
            key: "money",
            align: "center",
            render: (h, params) => {

              return h('span', params.row.sourceCurrencyCode + '兑' + params.row.targetCurrencyCode)

            }
          },
          {
            title: "汇率",
            key: "rate",
            align: "center",
          },
        ],
        ruleEditValidate: {
          month: [{
              required: true,
              message: "月份不能为空"
            },

          ],
          UsdExchangeHkd: [{
              required: true,
              message: "汇率金额不能为空"
            },
            {
              pattern: /^(([1-9]\d{0,7})|0)(\.\d{0,4})?$/,
              message: "汇率金额最高支持8位整数和4位小数正数或零"
            }
          ],
          CnyExchangeHkd: [{
              required: true,
              message: "汇率金额不能为空"
            },
            {
              pattern: /^(([1-9]\d{0,7})|0)(\.\d{0,4})?$/,
              message: "汇率金额最高支持8位整数和4位小数正数或零"
            }
          ],
          CnyExchangeUsd: [{
              required: true,
              message: "汇率金额不能为空"
            },
            {
              pattern: /^(([1-9]\d{0,7})|0)(\.\d{0,4})?$/,
              message: "汇率金额最高支持8位整数和4位小数正数或零"
            }
          ],
          EurExchangeHkd: [{
              required: true,
              message: "汇率金额不能为空"
            },
            {
              pattern: /^(([1-9]\d{0,7})|0)(\.\d{0,4})?$/,
              message: "汇率金额最高支持8位整数和4位小数正数或零"
            }
          ],
          EurExchangeUsd: [{
              required: true,
              message: "汇率金额不能为空"
            },
            {
              pattern: /^(([1-9]\d{0,7})|0)(\.\d{0,4})?$/,
              message: "汇率金额最高支持8位整数和4位小数正数或零"
            }
          ],
        },
        data: [

        ],
      };
    },

    mounted() {
      // this.goPageFirst(0)
    },
    methods: {

      checkMonth(date) {
        this.editObj.month = date;
      },
      checkDatePicker(date) {
        this.form.date = date;
      },
      openModal() {
        this.importExchangeFlag = true;
      },

      cancelModal() {
        this.importExchangeFlag = false;
      },

      reset(name) {
        this.$refs[name].resetFields();
      },
      importSubmit() {
        this.$refs["editObj"].validate(valid => {
          if (valid) {


            let params = {
              "rateVOList": [{
                  "rate": this.editObj.UsdExchangeHkd,
                  "rateTime": this.editObj.month,
                  "sourceCurrencyCode": "840",
                  "targetCurrencyCode": "344"
                },
                {
                  "rate": this.editObj.CnyExchangeHkd,
                  "rateTime": this.editObj.month,
                  "sourceCurrencyCode": "156",
                  "targetCurrencyCode": "344"
                },
                {
                  "rate": this.editObj.CnyExchangeUsd,
                  "rateTime": this.editObj.month,
                  "sourceCurrencyCode": "156",
                  "targetCurrencyCode": "840"
                },
                {
                  "rate": this.editObj.EurExchangeHkd,
                  "rateTime": this.editObj.month,
                  "sourceCurrencyCode": "978",
                  "targetCurrencyCode": "344"
                },
                {
                  "rate": this.editObj.EurExchangeUsd,
                  "rateTime": this.editObj.month,
                  "sourceCurrencyCode": "978",
                  "targetCurrencyCode": "840"
                }
              ]
            }

            importRate(params).then(res => {
              if (res.code === "0000") {
                this.$Notice.success({
                  title: "操作提示",
                  desc: "操作成功"
                });
                this.reset('editObj')
                this.cancelModal();
              }
            })

            // StatOfflineImport(this.editObj).then(res => {
            //   if (res.code === "0000") {
            //     this.$Notice.success({
            //       title: "操作提示",
            //       desc: "操作成功"
            //     });
            //   }

            //   this.reset("editObj");
            //   this.date1 = "";
            //   this.importModalFlag = false;
            // });
          }
        });
      },

      goPageFirst(page) {
        if (page === 0) {
          this.currentPage = 1;
        }

        var _this = this;
        let pageNum = this.currentPage;
        let pageSize = 10;

        this.$refs["form"].validate((valid) => {
          if (valid) {
            this.loading = true;
            //计算来源币种与目标币种
            let sourceCurrencyCode = this.form.currencyCode === 1 ? 156 : this.form.currencyCode === 2 ? 840 : this
              .form.currencyCode === 3 ? 156 : this.form.currencyCode === 4 ? 978 : this.form.currencyCode === 5 ? 978 : null //来源币种

            let targetCurrencyCode = this.form.currencyCode === 1 ? 344 : this.form.currencyCode === 2 ? 344 : this
              .form.currencyCode === 3 ? 840 : this.form.currencyCode === 4 ? 344 : this.form.currencyCode === 5 ? 840 : null //目标币种
            let date = this.form.date
            StatRate({
                pageNum,
                pageSize,
                sourceCurrencyCode,
                targetCurrencyCode,
                date
              })
              .then((res) => {
                if (res.code == "0000") {
                  _this.loading = false;
                  this.page = page;
                  this.total = res.data.total;
                  this.data = res.data.records
                }
              })
              .catch((err) => {
                console.error(err);
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            this.$Message.error("参数校验不通过");
          }
        });
      },
      goPage(page) {
        this.goPageFirst(page);
      },
      search() {
        this.goPageFirst(0);
      },
      exportTable() {
        this.$refs["form"].validate((valid) => {
          if (valid) {


            StatRateExport({
                ...this.form,
              })
              .then((res) => {
                const content = res.data;
                const fileName = "汇率管理.csv"; // 导出文件名
                if ("download" in document.createElement("a")) {
                  // 支持a标签download的浏览器
                  const link = document.createElement("a"); // 创建a标签
                  let url = URL.createObjectURL(content);
                  link.download = fileName;
                  link.href = url;
                  link.click(); // 执行下载
                  URL.revokeObjectURL(url); // 释放url
                } else {
                  // 其他浏览器
                  navigator.msSaveBlob(content, fileName);
                }
              })
              .catch(() => (this.downloading = false));
          }
        })
      },
      cancel() {
        this.updatemodel = false;
      },

      update(row, id) {
        this.updatemodel = true;
      },
    },
  };
</script>

<style>
</style>
