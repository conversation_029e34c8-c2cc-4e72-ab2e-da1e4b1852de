<template>
  <!-- 线下收入报表 -->

  <Card>
    <div style="display: flex; width: 100%">
      <Form ref="form" :label-width="0" :model="form" :rules="rule" inline>
        <FormItem prop="feeName">
          <Input
            v-model="form.feeName"
            placeholder="输入费用名称"
            prop="showTitle"
            clearable
            style="width: 150px"
          />
        </FormItem>
        <FormItem prop="currencyCode">
          <Select
            v-model="form.currencyCode"
            style="width: 200px"
            clearable
            placeholder="请选择币种"
            @on-select="choose($event)"
          >
            <Option
              v-for="(type, typeIndex) in typeList"
              :value="type.id"
              :key="typeIndex"
              >{{ type.value }}</Option
            >
          </Select>
        </FormItem>
        <FormItem prop="type">
          <Select
            v-model="form.type"
            :clearable="true"
            style="width: 200px; text-align: left; margin: 0 10px"
            placeholder="请选择统计维度"
            @on-change="
              date = '';
               resetField(['startTime','endTime'])
            "
          >
            <Option
              v-for="(type, typeIndex) in cycleList"
              :value="type.id"
              :key="typeIndex"
              >{{ type.value }}</Option
            >
          </Select>
        </FormItem>
             <FormItem prop="endTime">
  <FormItem v-if="form.type != '2'" prop="startTime">
          <DatePicker
            format="yyyyMMdd"
            v-model="date"
            v-has="'search'"
            @on-change="checkDatePicker"
            :editable="false"
            type="daterange"
            placeholder="选择时间段"
            clearable
          ></DatePicker>
        </FormItem>
        <FormItem v-if="form.type == '2'" prop="startTime">
          <DatePicker
            format="yyyyMM"
            @on-change="checkDatePicker($event, 1)"
            type="month"
            placement="bottom-start"
            placeholder="请选择开始月份"
            :editable="false"
          ></DatePicker
          >  
        </FormItem>
        <FormItem v-if="form.type == '2'" prop="endTime">
          <DatePicker
            format="yyyyMM"
            @on-change="checkDatePicker($event, 2)"
            type="month"
            placement="bottom-start"
            placeholder="请选择结束月份"
            :editable="false"
          ></DatePicker>
        </FormItem>
        </FormItem>
        <FormItem>
          <Button type="primary" icon="md-search" size="large" @click="search()"
            >搜索</Button
          >
          <Button
            type="success"
            icon="ios-cloud-download-outline"
            size="large"
            style="margin-left: 20px"
            @click="exportTable()"
            >导出</Button
          >
        </FormItem>

        <FormItem>
          <Button
            type="success"
            icon="ios-cloud-upload-outline"
            size="large"
            style="margin-left: 20px"
            @click="openModal"
            >导入</Button
          >
        </FormItem>
      </Form>
    </div>
    <!-- 表格 -->
    <Table
      :columns="columns12"
      :data="data"
      style="width: 100%; margin-top: 50px"
      :loading="loading"
    >
    </Table>
    <!-- 分页 -->
    <div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px">
      <Page
        :total="total"
        :current.sync="currentPage"
        show-total
        show-elevator
        @on-change="goPage"
      />
    </div>

    <Table
     v-if="data1.length"
      :columns="columns12"
      :data="data1"
      style="width: 100%; margin-top: 50px"
      :loading="loading"
    >
    </Table>

    <Modal
      title="导入线下报表"
      v-model="importModalFlag"
      :footer-hide="true"
      :mask-closable="false"
      @on-cancel="cancelModal"
    >
      <div style="padding: 0 16px">
        <Form
          ref="editObj"
          :model="editObj"
          :label-width="100"
          :rules="ruleEditValidate"
        >
          <FormItem label="费用名称" prop="feeName">
            <Input
              v-model="editObj.feeName"
              :clearable="true"
              maxlength="50"
              placeholder="请输入费用名称"
            ></Input>
          </FormItem>

          <FormItem label="币种" prop="currencyCode">
            <Select
              v-model="editObj.currencyCode"
              placeholder="请选择币种"
              @on-select="choose($event)"
            >
              <Option
                v-for="(type, typeIndex) in typeList"
                :value="type.id"
                :key="typeIndex"
                >{{ type.value }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="费用产生时间" prop="feeTime">
            <DatePicker
              type="date"
              v-model="date1"
              format="yyyyMMdd"
              placeholder="请选择时间段"
              @on-change="changeTime"
              style="width: 100%"
            ></DatePicker>
          </FormItem>
          <FormItem label="金额" prop="amount">
            <Input
              v-model="editObj.amount"
              :clearable="true"
              maxlength="13"
              placeholder="请输入金额"
            ></Input>
          </FormItem>
        </Form>
        <div style="text-align: center">
          <Button type="primary" @click="importSubmit">提交</Button>
          <Button style="margin-left: 8px" @click="reset('editObj')"
            >重置</Button
          >
        </div>
      </div>
    </Modal>
  </Card>
</template>

<script>
import {
  StatOfflinePageList,
  StatOfflineExport,
  StatOfflineImport
} from "@/api/report";
import mixin from '@/mixin/common'

export default {
  mixins:[mixin],
  data() {
    return {
      date: "",
      date1:'',
      loading: false,
      form: {
        feeName: "",
        endTime: "",
        type: "",
        startTime: "",
        currencyCode: ""
      },
      rule: {
        startTime: [
          {
            required: true,
            message: "请选择时间"
          }
        ],
        endTime: [
          {
            required: true,
            message: "请选择时间"
          }
        ],
        type: [
          {
            required: true,
            message: "请选择维度"
          }
        ]
      },
      total: 0,
      currentPage: 1,
      cycleList: [
        {
          id: 1,
          value: "日"
        },
        {
          id: 2,
          value: "月"
        }
      ],
      typeList: [
        {
          id: 156,
          value: "人民币"
        },
        {
          id: 344,
          value: "港币"
        },
        {
          id: 840,
          value: "美元"
        }
      ],
      sellList: [
        { value: "102", label: "API" },
        { value: "103", label: "官网（H5）" },
        { value: "104", label: "北京移动" },
        { value: "105", label: "批量售卖" },
        { value: "106", label: "推广活动" },
        { value: "110", label: "测试渠道" },
        { value: "111", label: "合作发卡" },
        { value: "112", label: "后付费发卡" },
        { value: "113", label: "WEB" },
        { value: "114", label: "流量池WEB" },
      ],
      columns12: [
        {
          title: "费用名称",
          key: "feeName",
          align: "center"
        },
        {
          title: "产生时间",
          key: "feeTime",
          align: "center"
        },
        {
          title: "币种",
          key: "currencyCode",
          align: "center",
                             render: (h, params) => {
				const row = params.row;
				//156 CNY,840 美元, 344 港币
				const text = row.currencyCode == '156' ? '人民币' : row.currencyCode == '840' ? '美元' : row.currencyCode ==
				  '344' ? '港币' : '';
				return h('label', text);
			  },
        },
        {
          title: "金额",
          key: "salesIncome",
          align: "center",
             render: (h, params) => {
            return h(
              "span",
              params.row.salesIncome
            );
          }
        },
        {
          title: "汇总港币",
          key: "hkdIncome",
          align: "center",
           render: (h, params) => {
            return h(
              "span",
              params.row.hkdIncome
            );
          }
        }
      ],
      data: [],
      data1: [],
      editObj: {
        amount: "",
        currencyCode: "",
        feeName: "",
        feeTime: ""
      },
      ruleEditValidate: {
        amount: [
          {
            required: true,
            message: "时间不能为空"
          },
          {
            pattern: /^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/,
            message: "押金金额最高支持10位整数和2位小数正数或零"
          }
        ],
        currencyCode: [
          {
            required: true,
            message: "币种不能为空"
          }
        ],
        feeName: [
          {
            required: true,
            message: "费用名称不能为空"
          }
        ],
        feeTime: [
          {
            required: true,
            message: "费用产生时间不能为空"
          }
        ]
      },
      importModalFlag: false
    };

  },
    created(){
    this.rule.startTime.push( { validator: this.validateDate, trigger: "change" })
    this.rule.endTime.push( { validator: this.validateDate, trigger: "change" })
  },
  mounted() {
    // this.goPageFirst(0);
  },
  methods: {

changeTime(date){
 this.editObj.feeTime = date
},

    openModal() {
          this.importModalFlag = true;
    },

    cancelModal() {
      this.importModalFlag = false;
    },

resetField (arr){
  this.$refs['form'].fields.forEach(element => {

    if(arr.includes(element.prop)  ){
      element.resetField();
    }
    
  });
  },
    checkDatePicker(date, type) {
      if (Array.isArray(date)) {
        this.form.startTime = date[0];
        this.form.endTime = date[1];
      } else {
        if (type === 1) {
          this.form.startTime = date;
        } else {
          this.form.endTime = date;
        }
      }
    },

    goPageFirst(page) {
      if (page === 0) {
        this.currentPage = 1;
      }
      var _this = this;
      let pageNum = this.currentPage;
      let pageSize = 10;

      this.$refs["form"].validate(valid => {
        if (valid) {
          this.loading = true;
          StatOfflinePageList({
            page: pageNum,
            pageSize,
            ...this.form
          })
            .then(res => {
              if (res.code == "0000") {
                _this.loading = false;
                this.page = page;
                this.total = res.data.total;
                this.data =
                  res.data.offlineDayList || res.data.offlineMonthList;
                this.data1 = [
                  {
                    feeName: "合计",
                    hkdIncome: res.data.sum,
                  }
                ];
              }
            })
            .catch(err => {

            if(err.code === '1000'){
              this.data = this.data1  =[];
            }
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          this.$Message.error("参数校验不通过");
        }
      });
    },
    goPage(page) {
      this.goPageFirst(page);
    },
    // 搜索
    search() {
      this.goPageFirst(0);
    },
    reset(name) {
      this.$refs[name].resetFields();
    },
    importSubmit() {
      this.$refs["editObj"].validate(valid => {
        if (valid) {
          let formData = JSON.parse(JSON.stringify(this.editObj));

          formData.amount = this.$moneyCover(formData.amount, 100);
          //  formData.type = this.form.type;
          //  formData.startTime = this.form.startTime;
          //  formData.endTime = this.form.endTime;
          StatOfflineImport(formData).then(res => {
            if (res.code === "0000") {
              this.$Notice.success({
                title: "操作提示",
                desc: "操作成功"
              });
            }

            this.reset("editObj");
            this.date1 = "";
            this.importModalFlag = false;
          });
        }
      });
    },

    // 导出
    exportTable() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          StatOfflineExport({ ...this.form })
            .then(res => {
              const content = res.data;
              const fileName = "收入报表统计.csv"; // 导出文件名
              if ("download" in document.createElement("a")) {
                // 支持a标签download的浏览器
                const link = document.createElement("a"); // 创建a标签
                let url = URL.createObjectURL(content);
                link.download = fileName;
                link.href = url;
                link.click(); // 执行下载
                URL.revokeObjectURL(url); // 释放url
              } else {
                // 其他浏览器
                navigator.msSaveBlob(content, fileName);
              }
            })
            .catch(() => (this.downloading = false));
        }
      });
    },
    details(row) {
      this.$router.push({
        path: "/channel/detailsList"
      });
    }
  }
};
</script>

<style></style>
