<template>
  <!-- 重复使用报表 -->
  <Card>
    <div style="display: flex; width: 100%">
      <Form ref="form" :label-width="0" :model="form" :rules="rule" inline>
        <FormItem prop="activeNum">
          <Input
            v-model="form.activeNum"
            maxlength="5"
			 :clearable="true"
            placeholder="请输入次数"
          ></Input>
        </FormItem>

        <FormItem prop="type">
          <Select
            v-model="form.type"
            :clearable="true"
            style="width: 200px; text-align: left; margin: 0 10px"
            placeholder="请选择统计维度"
            @on-change="
              date = '';
                   resetField(['startTime','endTime'])
           
            "
          >
            <Option
              v-for="(type, typeIndex) in cycleList"
              :value="type.id"
              :key="typeIndex"
              >{{ type.value }}</Option
            >
          </Select>
        </FormItem>
       <FormItem v-if="form.type != '1'" prop="startTime">
          <DatePicker
            format="yyyy-MM-dd"
            v-model="date"
            v-has="'search'"
            @on-change="checkDatePicker"
            :editable="false"
            type="daterange"
            placeholder="选择时间段"
            clearable
          ></DatePicker>
        </FormItem>
        <FormItem v-if="form.type == '1'" prop="startTime">
          <DatePicker
            format="yyyy-MM"
            @on-change="checkDatePicker($event, 1)"
            type="month"
            placement="bottom-start"
            placeholder="请选择开始月份"
            :editable="false"
          ></DatePicker
          >  
        </FormItem>
        <FormItem v-if="form.type == '1'" prop="endTime">
          <DatePicker
            format="yyyy-MM"
            @on-change="checkDatePicker($event, 2)"
            type="month"
            placement="bottom-start"
            placeholder="请选择结束月份"
            :editable="false"
          ></DatePicker>
        </FormItem>
        <FormItem>
          <Button
            v-has="'search'"
            type="primary"
            icon="md-search"
            size="large"
            @click="search()"
            >搜索</Button
          >&nbsp;&nbsp;
          <Button
            v-has="'export'"
            type="success"
            icon="ios-cloud-download-outline"
            size="large"
            style="margin-left: 20px"
            @click="exportTable()"
            >导出</Button
          >
        </FormItem>
      </Form>
    </div>
    <!-- 表格 -->
    <Table
      :columns="columns12"
      :data="data"
      style="width: 100%; margin-top: 50px"
      :loading="loading"
    >


    </Table>

    <!-- 分页 -->
    <div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px">
      <Page
        :total="total"
        :current.sync="currentPage"
        show-total
        show-elevator
        @on-change="goPage"
      />
    </div>
  </Card>
</template>

<script>
import { countReuse, countReuseExport } from "@/api/report";
import mixin from '@/mixin/common'

export default {
  mixins:[mixin],
  data() {
    return {
      date: "",
      loading: false,
      form: {
        activeNum:"",
        endTime: "",
        startTime: "",
        type: ""
      },
      rule: {
          startTime: [
          {
            required: true,
            message: "请选择时间"
          }
        ],
        endTime: [
          {
            required: true,
            message: "请选择时间"
          }
        ],
        type: [
          {
            required: true,
            message: "请选择维度"
          }
        ]
      },
      total: 0,
      currentPage: 1,
      cycleList: [
        {
          id: 0,
          value: "日"
        },
        {
          id: 1,
          value: "月"
        }
      ],
      typeList: [
        {
          id: 1,
          value: "普通卡（实体卡）"
        },
        {
          id: 2,
          value: "Esim卡"
        },
        {
          id: 3,
          value: "贴片卡"
        }
      ],

      columns12: [
        {
          title: "HIMSI",
          key: "imsi",
          align: "center"
        },
        {
          title: "使用次数",
          key: "activeNum",
          align: "center"
        },
        {
          title: "套餐名称+激活时间",
          key: "packages",
          align: "center",
	          render: (h, params) => {
                            return h('Poptip', {
                                props: {
                                    trigger: 'hover',
                                    placement: 'bottom',
									transfer:true,
                                }
                            }, [
                                h('Tag', params.row.packages.length+'条记录'),
                                h('div', {
                                    slot: 'content'
                                }, [
                                    h('ul', this.data[params.index].packages.map((item) => {
                                        return h('li', {
                                            style: {
                                                textAlign: 'center',
                                                padding: '4px'
                                            }
                                        }, item.packageName+ ': '+item.activeTime )
                                    }))
                                ])
                            ]);
                        }
        }
      ],
      data: [],
      rules: {}
    };
  },
    created(){
    this.rule.startTime.push( { validator: this.validateDate, trigger: "change" })
    this.rule.endTime.push( { validator: this.validateDate, trigger: "change" })
  },
  mounted() {
    // this.goPageFirst(0);
  },
  methods: {

    resetField (arr){
  this.$refs['form'].fields.forEach(element => {

    if(arr.includes(element.prop)  ){
      element.resetField();
    }
    
  });
  },

       checkDatePicker(date, type) {
      if (Array.isArray(date)) {
        this.form.startTime = date[0];
        this.form.endTime = date[1];
      } else {
        if (type === 1) {
          this.form.startTime = date;
        } else {
          this.form.endTime = date;
        }
      }
    },
    goPageFirst(page) {
      if (page === 0) {
        this.currentPage = 1;
      }
      var _this = this;
      let size = 10;
      let current = this.currentPage;

      this.$refs["form"].validate(valid => {
        if (valid) {
          this.loading = true;

          countReuse({
            current,
            size,
            ...this.form
          })
            .then(res => {
              if (res.code == "0000") {
                _this.loading = false;
                this.page = page;
                this.total = res.count;
                this.data = res.data;
              }
            })
            .catch(err => {
              console.error(err);
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    goPage(page) {
      this.goPageFirst(page);
    },
    // 搜索
    search() {
      this.goPageFirst(0);
    },
    // 导出
    exportTable() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          countReuseExport({
            ...this.form
          })
            .then(res => {
              const content = res.data;
              const fileName = "重复使用报表.csv"; // 导出文件名
              if ("download" in document.createElement("a")) {
                // 支持a标签download的浏览器
                const link = document.createElement("a"); // 创建a标签
                let url = URL.createObjectURL(content);
                link.download = fileName;
                link.href = url;
                link.click(); // 执行下载
                URL.revokeObjectURL(url); // 释放url
              } else {
                // 其他浏览器
                navigator.msSaveBlob(content, fileName);
              }
            })
            .catch(() => (this.downloading = false));
        }
      });
    },
    details(row) {
      this.$router.push({
        path: "/channel/detailsList"
      });
    }
  }
};
</script>

<style></style>
