<template>
	<!-- 套餐订购报表 -->
	<Card>
		<Form ref="form" :label-width="90" :model="form" :rules="ruleInline" inline>
		  <FormItem label="销售主体:"  prop="corpName">
			<Input clearable v-model="form.corpName" placeholder="输入销售主体名称..." style="width: 200px ;margin-right: 10px;" />
		  </FormItem>
		  <FormItem label="统计维度:"  prop="dimension" >
			  <Select @on-change="changeDimension" filterable v-model="form.dimension" placeholder="下拉选择统计维度" clearable>
				  <Option :value="item.value" v-for="(item,index) in cycleList"  :key="index">{{item.label}}</Option>
			  </Select>
		  </FormItem>
		  <FormItem v-if="form.dimension === '1'" label="时间段:"  prop="timeRangeArray">
			<DatePicker format="yyyyMMdd" v-model="form.timeRangeArray" v-has="'search'" @on-change="handleDateChange" :editable="false" type="daterange" placeholder="选择时间段"
			clearable style="width: 200px ;margin: 0 10px 0 0;" @on-clear="hanldeDateClear"></DatePicker>
		  </FormItem>
		  <FormItem v-if="form.dimension === '2'" label="开始月份:"  prop="beginMonth">
			<DatePicker
			  format="yyyyMM"
			  v-model="form.beginMonth"
			  type="month"
			  placement="bottom-start"
			  placeholder="请选择开始月份"
			  @on-change="handleChangeBeginMonth"
			  :editable="false"
			></DatePicker>  
		  </FormItem>
		  <FormItem v-if="form.dimension === '2'" label="结束月份:"  prop="endMonth">
			<DatePicker
			  format="yyyyMM"
			  v-model="form.endMonth"
			  type="month"
			  placement="bottom-start"
			  placeholder="请选择结束月份"
			  @on-change="handleChangeEndMonth"
			  :editable="false"
			></DatePicker>
		  </FormItem>
		  <Button v-has="'search'" type="primary" icon="md-search" size="large" @click="search('form')">搜索</Button>&nbsp;&nbsp;
		  <Button v-has="'export'" type="success" icon="ios-cloud-download-outline" size="large" style="margin-left: 20px;" @click="exportTable()">导出</Button>
		</Form>
		<!-- 表格 -->
		<Table :columns="columns12" :data="data" style="width:100%;" :loading="loading">
		</Table>
		<div v-if="data2 !== null" style="margin-top: 20px;">
			<Table
			  :columns="columns13"
			  :data="data2"
			  style="width: 100%; "
			  :loading="loading"
			  :show-header="false"
			>
			</Table>
		</div>
		<!-- 分页 -->
		<div style="margin-left: 38%; margin-top: 100px; margin-bottom: 160px; ">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
	</Card>
</template>

<script>
	import {
		download,
		pageList,
	} from "@/api/stat/packageSale";
	export default {
		data() {
			return {
				form: {
					corpName: "",
					dimension: "",
					timeRangeArray: [],
					beginMonth: '',
					endMonth: ''
				},
				ruleInline: {
					dimension: [{ required: true, message: '请选择维度', trigger: 'blur'}],
					timeRangeArray: [
						{ type: 'array',required: true, message: '请选择时间', trigger: 'blur',
						  fields: {
								0: {type: 'date', required: true, message: '请选择开始日期'},
								1: {type: 'date', required: true, message: '请选择结束日期'}
							}
						}
					],
					beginMonth: [{ type: 'date',required: true, message: '请选择开始月份', trigger: 'blur'}],
					endMonth: [{ type: 'date',required: true, message: '请选择结束月份', trigger: 'blur'}],
				},
				total: 0,
				currentPage: 1,
				loading: false,
				cycleList: [
					{
					  value: "1",
					  label: "日",
					},
					{
					  value: "2",
					  label: "月",
					},
				],
				columns12: [{
						title: '销售主体',
						key: 'corpName',
						align: 'center',
					},
					{
						title: '时间',
						key: 'statTime',
						align: 'center'
					},
					{
						title: '本期套餐订购数',
						key: 'salesVolume',
						align: 'center'
					},
					{
						title: '本期加油包订购数',
						key: 'salesVolumeRefuel',
						align: 'center'
					},
					{
						title: '港币收入',
						key: 'hkdIncome',
						align: 'center'
					},
					{
						title: '人民币收入',
						key: 'cnyIncome',
						align: 'center'
					},
					{
						title: '美元收入',
						key: 'usdIncome',
						align: 'center'
					},
					{
						title: '总收入(币种:港币)',
						key: 'totalIncome',
						align: 'center'
					},
				],
				columns13: [{
						title: '销售主体',
						key: 'corpName',
						align: 'center'
					},
					{
						title: '时间',
						key: 'statTime',
						align: 'center'
					},
					{
						title: '本期套餐订购数',
						key: 'salesVolume',
						align: 'center'
					},
					{
						title: '本期加油包订购数',
						key: 'salesVolumeRefuel',
						align: 'center'
					},
					{
						title: '港币收入',
						key: 'hkdIncome',
						align: 'center'
					},
					{
						title: '人民币收入',
						key: 'cnyIncome',
						align: 'center'
					},
					{
						title: '美元收入',
						key: 'usdIncome',
						align: 'center'
					},
					{
						title: '总收入(币种:港币)',
						key: 'totalIncome',
						align: 'center'
					},
				],
				data: [],
				data2: null,
				rules: {},
				searchBeginTime: '',
			    searchEndTime: '',
			}
		},
		mounted() {
		},
		methods: {
			//切换维度
			changeDimension(){
			  this.form.timeRangeArray = ''
			  this.form.beginMonth = ''
			  this.form.endMonth = ''
			},
			// 获取开始月份
			handleChangeBeginMonth(month) {
				this.searchBeginTime = month;
			},
			// 获取结束月份
			handleChangeEndMonth(month) {
				this.searchEndTime = month;
			},
			hanldeDateClear() {
				this.searchBeginTime = ''
				this.searchEndTime = ''
			},
			handleDateChange(dateArr) {
				let beginDate = this.form.timeRangeArray[0] || ''
				let endDate = this.form.timeRangeArray[1] || ''
				if (beginDate == '' || endDate == '') {
				return
				}
				[this.searchBeginTime, this.searchEndTime] = dateArr
			},
			checkDatePicker(date) {
				this.form.startDate = date[0];
				this.form.endDate = date[1];
			},
			goPageFirst(page) {
				this.currentPage = page;
				this.loading = true;
				var _this = this;
				let pageSize = 10;
				pageList({
					page,
					pageSize,
					corpName: _this.form.corpName,
					type: _this.form.dimension,
					startTime: _this.searchBeginTime,
					endTime: _this.searchEndTime
				  }).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						_this.total = res.data.total
						if(_this.form.dimension === '1'){
							_this.data = res.data.packageSaleDayList;
						}
						if(_this.form.dimension === '2'){
							_this.data = res.data.packageSaleMonthList;
						}
						let sum = res.data.sum
						_this.data2 = [{
							'corpName': '合计',
							'statTime': '-',
							'salesVolume': sum.salesVolume,
							'salesVolumeRefuel':sum.salesVolumeRefuel,
							'hkdIncome': sum.hkdIncome,
							'cnyIncome': sum.cnyIncome,
							'usdIncome': sum.usdIncome,
							'totalIncome': sum.totalIncome,
						}]
					}
				}).catch((err) => {
					console.error(err)
          _this.data = _this.data2  =[];
          _this.total = 0
				}).finally(() => {
					_this.loading = false
				})
			},
			goPage(page) {
			  this.goPageFirst(page);
			},
			// 搜索
			search(name) {
        if (this.searchBeginTime > this.searchEndTime) {
          this.$Message.warning('开始时间不能大于结束时间')
          return
        }
			  this.$refs[name].validate((valid) => {
			    if (valid) {
			  	  this.goPageFirst(1)
			    } else {
			  	  this.$Message.error('参数校验不通过');
			    }
			  })
			},
			// 导出
			exportTable() {
				if (this.searchBeginTime > this.searchEndTime) {
				  this.$Message.warning('开始时间不能大于结束时间')
				  return
				}
				var _this = this
			  download({
					corpName: _this.form.corpName,
					type: _this.form.dimension,
					startTime: _this.searchBeginTime,
					endTime: _this.searchEndTime
			  }).then((res) => {
			      const content = res.data;
			      const fileName = "套餐订购报表"+ '.csv'; // 导出文件名
			      if ("download" in document.createElement("a")) {
			        // 支持a标签download的浏览器
			        const link = document.createElement('a'); // 创建a标签
			        let url = URL.createObjectURL(content);
			        link.download = fileName;
			        link.href = url;
			        link.click(); // 执行下载
			        URL.revokeObjectURL(url); // 释放url
			      } else {
			        // 其他浏览器
			        navigator.msSaveBlob(content, fileName);
			      }
			    })
			    .catch(() => (this.downloading = false));
			},

		}


	}
</script>

<style>
</style>
