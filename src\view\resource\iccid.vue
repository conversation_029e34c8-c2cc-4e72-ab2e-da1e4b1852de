<template>
  <div>
    <Card>
      <div class="search_head">
        <span style="font-weight:bold;">ICCID号码：</span>&nbsp;&nbsp;
        <Input placeholder="输入ICCID号码..." v-model="iccidCondition" clearable style="width: 200px" />&nbsp;&nbsp;
        <span style="font-weight:bold;">状态：</span>&nbsp;&nbsp;
        <Select filterable v-model="status" placeholder="下拉选择状态" style="width:300px" clearable>
            <Option :value="item.value" v-for="(item,index) in statuses"  :key="index">{{item.label}}</Option>
        </Select>&nbsp;&nbsp;
        <Button type="primary" icon="md-search" :loading="searchLoading" @click="search()">搜索</Button>&nbsp;&nbsp;
        <!-- <Button v-has="'add'" icon="md-add" type="success" @click="addIccid()">导入</Button>&nbsp;&nbsp;
        <Button v-has="'batchUpdate'" icon="md-add" type="warning" @click="updateBatch()">批量修改</Button>&nbsp;&nbsp;
        <Button v-has="'batchDelete'" icon="md-add" type="error" @click="deleteBatch()">批量删除</Button>&nbsp;&nbsp; -->
      </div>
      <div style="margin-top:20px">
        <Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
          <template slot-scope="{ row, index }" slot="action">
            <Button v-has="'update'" v-if="row.status == 1 || row.status == 6" type="success" size="small" style="margin-right: 10px" @click="update(row)">修改</Button>
            <Button v-has="'delete'" v-if="row.status == 1" type="error" size="small" :loading="row.delLoading" @click="deleteItem(row)">删除</Button>
          </template>
        </Table>
      </div>
      <div class="table-botton" style="margin-top:15px">
        <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" style="margin: 10px 0;" />
      </div>
    </Card>
    <Modal
      v-model="modal1"
      title="导入ICCID"
      :mask-closable="false"
      @on-cancel="cancel1"
      width="620px">
      <div class="search_head" style="margin: 50px 0px;">
        <Form ref="formValidate1" :model="formValidate1" :rules="ruleValidate1" :label-width="120" :label-height="100" inline style="font-weight:bold;">
          <FormItem label="ICCID起始号码" prop="begin" >
              <Input placeholder="ICCID起始号码..." v-model="formValidate1.begin" clearable style="width: 300px" />&nbsp;&nbsp;
          </FormItem>
          <FormItem label="ICCID结束号码" prop="end" >
              <Input placeholder="ICCID结束号码..." v-model="formValidate1.end" clearable style="width: 300px" />&nbsp;&nbsp;
          </FormItem>
        </Form>
      </div>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
        <Button @click="cancel1">取消</Button>
        <Button type="primary" :loading="addLoading" @click="add('formValidate1')">确定</Button>
      </div>
    </Modal>
    <Modal
      v-model="modal2"
      title="修改ICCID"
      :mask-closable="false"
      @on-cancel="cancel2">
      <div class="search_head">
        <Form ref="formValidate2" :model="formValidate2" :rules="ruleValidate2" :label-width="120" :label-height="100" inline style="font-weight:bold;">
          <FormItem label="ICCID号码" >
              <span>{{iccidChoosed.iccid}}</span>&nbsp;&nbsp;
          </FormItem>
          <FormItem label="状态" prop="status" >
              <Select filterable v-if="iccidChoosed.status == 1" v-model="formValidate2.status" placeholder="下拉选择状态" style="width:300px" clearable>
                  <Option :value="item.value" v-for="(item,index) in updateStatuses1"  :key="index">{{item.label}}</Option>
              </Select>
              <Select filterable v-else v-model="formValidate2.status" placeholder="下拉选择状态" style="width:300px" clearable>
                  <Option :value="item.value" v-for="(item,index) in updateStatuses2"  :key="index">{{item.label}}</Option>
              </Select>
          </FormItem>
        </Form>
      </div>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
        <Button @click="cancel2">取消</Button>
        <Button type="primary" :loading="updateLoading"  @click="updateIccid('formValidate2')">确定</Button>
      </div>
    </Modal>
    <Modal
      v-model="modal3"
      title="批量修改ICCID"
      :mask-closable="false"
      @on-cancel="cancel3">
      <div class="search_head">
        <Form ref="formValidate3" :model="formValidate3" :rules="ruleValidate3" :label-width="120" :label-height="100" inline style="font-weight:bold;">
          <FormItem label="ICCID起始号码" prop="begin" >
              <Input placeholder="ICCID起始号码..." v-model="formValidate3.begin" clearable style="width: 300px" />&nbsp;&nbsp;
          </FormItem>
          <FormItem label="ICCID结束号码" prop="end" >
              <Input placeholder="ICCID结束号码..." v-model="formValidate3.end" clearable style="width: 300px" />&nbsp;&nbsp;
          </FormItem>
          <FormItem label="状态" prop="status" >
              <Select filterable v-model="formValidate3.status" placeholder="下拉选择状态" style="width:300px" clearable>
                  <Option :value="item.value" v-for="(item,index) in updateStatuses1"  :key="index">{{item.label}}</Option>
              </Select>
          </FormItem>
        </Form>
      </div>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
        <Button @click="cancel3">取消</Button>
        <Button type="primary" :loading="updateBatchLoading"  @click="updateIccidBatch('formValidate3')">确定</Button>
      </div>
    </Modal>
    <Modal
      v-model="modal4"
      title="批量删除ICCID"
      :mask-closable="false"
      @on-cancel="cancel4">
      <div class="search_head">
        <Form ref="formValidate4" :model="formValidate4" :rules="ruleValidate4" :label-width="120" :label-height="100" inline style="font-weight:bold;">
          <FormItem label="ICCID起始号码" prop="begin" >
              <Input placeholder="ICCID起始号码..." v-model="formValidate4.begin" clearable style="width: 300px" />&nbsp;&nbsp;
          </FormItem>
          <FormItem label="ICCID结束号码" prop="end" >
              <Input placeholder="ICCID结束号码..." v-model="formValidate4.end" clearable style="width: 300px" />&nbsp;&nbsp;
          </FormItem>
        </Form>
      </div>
      <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
        <Button @click="cancel4">取消</Button>
        <Button type="primary" :loading="delBatchLoading"  @click="delIccidBatch('formValidate4')">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import {
  getPage,
  add,
  updateStatus,
  del,
  updateBatch,
  delBatch
} from '@/api/resoure/iccid';
export default {
  components: {
  },
  data () {
    // 校验是否为纯数字
    const checkNumber = (rule, value, callback) => {
      let reg = /^[0-9]\d*$/;
      if (reg.test(value)) {
        if (value.length > 30) {
          callback(new Error("请输入1-30位的纯数字"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入1-30位的纯数字"));
      }
    };
    return {
      formValidate1: {
        begin: '',
        end: '',
        provider: ''
      },
      ruleValidate1: {
        begin: [
          { required: true, message: '请输入ICCID起始号码', trigger: 'blur' },
          { validator: checkNumber,trigger: "blur" }
        ],
        end: [
          { required: true, message: '请输入ICCID结束号码', trigger: 'blur' },
          { validator: checkNumber,trigger: "blur" }
        ],
        provider: [
          { type:'number',required: true,min: 3, message: '请选择供应商', trigger: 'blur' }
        ]
      },
      formValidate2: {
        status: ''
      },
      ruleValidate2: {
        status: [
          { type:'number',required: true, message: '请选择修改后状态', trigger: 'blur' }
        ]
      },
      formValidate3: {
        begin: '',
        end: '',
        status: ''
      },
      ruleValidate3: {
        begin: [
          { required: true, message: '请输入ICCID起始号码', trigger: 'blur' },
          { validator: checkNumber,trigger: "blur" }
        ],
        end: [
          { required: true, message: '请输入ICCID结束号码', trigger: 'blur' },
          { validator: checkNumber,trigger: "blur" }
        ],
        status: [
          { type:'number',required: true, message: '请选择修改后状态', trigger: 'blur' }
        ]
      },
      formValidate4: {
        begin: '',
        end: '',
      },
      ruleValidate4: {
        begin: [
          { required: true, message: '请输入ICCID起始号码', trigger: 'blur' },
          { validator: checkNumber,trigger: "blur" }
        ],
        end: [
          { required: true, message: '请输入ICCID结束号码', trigger: 'blur' },
          { validator: checkNumber,trigger: "blur" }
        ],
      },
      // 表头信息
      columns: [
        {
          title: 'ICCID',
          key: 'iccid',
          align: 'center'
        },
        {
          title: '入库时间',
          key: 'createTime',
          align: 'center'
        },
        {
          title: '当前状态',
          key: 'status',
          align: 'center',
          render: (h, params) => {
            const row = params.row
            const color = row.status == 1 ? '#19be6b' : row.status == 2 ? '#ff0000' : row.status == 3 ? '#27A1FF' : row.status == 4 ? '#ff9900': row.status == 5 ? '#d75b0f' : row.status == 6 ? '#d518bc'  : '#515a6e'
            const text = row.status == 1 ? '已导入' : row.status == 2 ? '待分配' : row.status == 3 ? '已分配' : row.status == 4 ? '使用中' : row.status == 5 ? '已冻结' : row.status == 6 ? '留存' : '其他'
            return h('label', {
              style: {
                color: color
              }
            }, text)
          }
        },
        {
          title: '操作',
          slot: 'action',
          width: 300,
          align: 'center'
        }
      ],
      statuses: [
        // {
        //   label: '已导入',
        //   value: 1
        // },
        // {
        //   label: '待分配',
        //   value: 2
        // },
        {
          label: '已分配',
          value: 3
        },
        {
          label: '使用中',
          value: 4
        },
        {
          label: '已冻结',
          value: 5
        },
        // {
        //   label: '留存',
        //   value: 6
        // }
      ],
      updateStatuses1: [
        {
          label: '待分配',
          value: 2
        },
        {
          label: '留存',
          value: 6
        }
      ],
      updateStatuses2: [
        {
          label: '待分配',
          value: 2
        }
      ],
      tableData: [],
      loading: false,
      addLoading: false,
      searchLoading: false,
      updateLoading: false,
      updateBatchLoading: false,
      delBatchLoading: false,
      currentPage: 1,
      total: 0,
      iccidCondition: '',
      iccidChoosed: {},
      ids: [],
      modal1: false,
      modal2: false,
      modal3: false,
      modal4: false,
      status: '',
      selection: [], //多选
      selectionIds: [], //多选ids
    }
  },
  watch: {
    '$route': 'reload'
  },
  computed: {},
  methods: {
    // 页面加载
    goPageFirst (page) {
      this.loading = true
      let pageSize = 10
      let pageNumber = page
      getPage({
          iccid: this.iccidCondition,
          status: this.status,
      		pageNumber,
      		pageSize
      	}).then(res => {
        if (res && res.code == '0000') {
          this.tableData = res.data
          this.total = res.count
          this.loading = false
          this.searchLoading = false
          if (this.tableData.length) {
            this.tableData.map(item => {
              this.$set(item, 'delLoading', false)
              return item
            })
          }
        } else {
          throw res
        }
      }).catch((err) => {
        this.loading = false
        this.searchLoading = false
        if (this.tableData.length) {
          this.tableData.map(item => {
            this.$set(item, 'delLoading', false)
            return item
          })
        }
      })
    },
    // 查询按钮，指定号码查询
    // 分页跳转
    goPage (page) {
      this.currentPage = page
      this.goPageFirst(page)
    },
    error (nodesc) {
      this.$Notice.error({
        title: '出错啦',
        desc: nodesc ? '' : '服务器内部错误'
      })
    },
    search () {
      this.searchLoading = true
      this.currentPage = 1
      this.goPageFirst(1)
    },
    addIccid () {
      this.modal1 = true
    },
    add(name){
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.addLoading = true
          add({
              iccidEnd : this.formValidate1.end,
              iccidStart : this.formValidate1.begin,
            }).then(res => {
            if (res && res.code == '0000') {
              this.$Notice.success({
                title: '成功',
                desc: '操作成功'
              })
              this.currentPage = 1
              this.goPageFirst(1)
            } else {
              throw res
            }
          }).catch((err) => {
          })
          this.cancel1()
        }
      })
    },
    update(item){
      this.modal2 = true
      this.iccidChoosed = item
    },
    updateIccid(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.updateLoading = true
          updateStatus({
              iccid: this.iccidChoosed.iccid,
              status: this.formValidate2.status
            }).then(res => {
            if (res && res.code == '0000') {
              this.$Notice.success({
                title: '成功',
                desc: '操作成功'
              })
              this.currentPage = 1
              this.goPageFirst(1)
            } else {
              throw res
            }
          }).catch((err) => {
            this.currentPage = 1
            this.goPageFirst(1)
          })
          this.iccidChoosed = {}
          this.cancel2()
        }
      })
    },
    updateBatch () {
      this.modal3 = true
    },
    updateIccidBatch(name){
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.updateBatchLoading = true
          updateBatch({
              iccidEnd : this.formValidate3.end,
              iccidStart : this.formValidate3.begin,
              status: this.formValidate3.status
            }).then(res => {
            if (res && res.code == '0000') {
              this.$Notice.success({
                title: '成功',
                desc: '操作成功'
              })
              this.currentPage = 1
              this.goPageFirst(1)
            } else {
              throw res
            }
          }).catch((err) => {
          })
          this.cancel3()
        }
      })
    },
    deleteBatch(){
      this.modal4 = true
    },
    deleteItem(item){
      item.delLoading = true
      this.$Modal.confirm({
        title: '确认删除？',
        onOk: () => {
          del({
              iccid: item.iccid,
          	}).then(res => {
            if (res && res.code == '0000') {
              this.$Notice.success({
                title: '成功',
                desc: '操作成功'
              })
              this.currentPage = 1
              this.goPageFirst(1)
            } else {
              throw res
            }
          }).catch((err) => {
            this.currentPage = 1
            this.goPageFirst(1)
          })
        },
        onCancel: ()=>{
          item.delLoading = false
        }
      })
    },
    delIccidBatch(name){
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.delBatchLoading = true
          delBatch({
              iccidEnd: this.formValidate4.end,
              iccidStart: this.formValidate4.begin,
            }).then(res => {
            if (res && res.code == '0000') {
              this.$Notice.success({
                title: '成功',
                desc: '操作成功'
              })
              this.currentPage = 1
              this.goPageFirst(1)
            } else {
              throw res
            }
          }).catch((err) => {
          })
          this.cancel4()
        }
      })
    },
    cancel1(){
	    this.modal1 = false
      this.$refs.formValidate1.resetFields()
      this.addLoading = false
    },
    cancel2(){
	    this.modal2 = false
      this.$refs.formValidate2.resetFields()
      this.updateLoading = false
    },
    cancel3(){
      this.modal3 = false
      this.$refs.formValidate3.resetFields()
      this.updateBatchLoading = false
    },
    cancel4(){
      this.modal4 = false
      this.$refs.formValidate4.resetFields()
      this.delBatchLoading = false
    }
  },
  mounted () {
    this.goPageFirst(1)
  }
}
</script>
<style>

  .search_head {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items:center;
    margin-bottom: 20px;
  }
  .search_head_label {
    margin-top: 20px;
    font-size: 17px;
  }
</style>
