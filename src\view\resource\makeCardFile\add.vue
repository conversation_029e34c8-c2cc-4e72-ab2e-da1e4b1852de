<template>
	<!-- 新建制卡任务 -->
	<card style="width: 100%;padding: 20px; display: flex; justify-content: space-evenly">
		<Form ref="formObj" :model="formObj" :rules="ruleInfoValidate" :label-width="190" :label-height="100"
			style="font-weight:bold;">
			<div>
				<Row type="flex" justify="space-around">
					<Col :md="24" :lg="12">
					<FormItem label="选择供应商" prop="supplierId">
						<Select v-model="formObj.supplierId" placeholder="下拉选择供应商" style="width:330px" clearable
							@on-change="getProviders">
							<Option :value="item.supplierId" v-for="(item,index) in providers" :key="index">
								{{item.supplierName}}
							</Option>
						</Select>
					</FormItem>
					</Col>
					<Col :md="24" :lg="12">
					<FormItem label="选择卡类型" prop="makeCardType">
						<Select v-model="formObj.makeCardType" placeholder="下拉选择卡类型" style="width:330px" clearable>
							<Option :value="1">普通卡</Option>
							<Option :value="2">省移动</Option>
							<Option :value="3">欧洲卡</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				
				<Row justify="center">
					<Col :md="24" :lg="12">
					<FormItem label="是否使用连续资源下拉框" prop="isContinuous">
						<Select v-model="formObj.isContinuous" placeholder="是否连续资源" style="width:330px" clearable>
							<Option v-for="item in isContinuousList" :value="item.value" :key="item.value">
								{{ item.label }}
							</Option>
						</Select>
					</FormItem>
					</Col>
					<Col :md="24" :lg="12">
					<FormItem label="是否VIMSI制卡" prop="isVimsi">
						<Select v-model="formObj.isVimsi" placeholder="是否VIMSI制卡" style="width:330px"
							clearable>
							<Option :value="1">是</Option>
							<Option :value="2">否</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				<Row justify="center" style="margin-bottom: 30px;">
					<Col :md="24" :lg="12">
					<FormItem label="是否需要GTP PROXY指定号码" prop="gtpProxy">
						<Select v-model="formObj.gtpProxy" placeholder="是否需要GTP PROXY指定号码" style="width:330px"
							clearable>
							<Option :value="1">是</Option>
							<Option :value="2">否</Option>
						</Select>
					</FormItem>
					</Col>
				</Row>
				
				<Row justify="center">
					<Col :md="24" :lg="12">
					<FormItem label="任务名称" prop="taskName">
						<Input v-model="formObj.taskName" :maxlength="50" style="width: 330px" placeholder="请输入任务名称"
							clearable></Input>
					</FormItem>
					</Col>
					<Col :md="24" :lg="12">
					<FormItem label="Customer" prop="customer">
						<Input v-model="formObj.customer" :maxlength="50" style="width: 330px"
							:clearable="true"></Input>
					</FormItem>
					</Col>
				</Row>

				<Row>
					<Col :md="24" :lg="12">
					<FormItem label="Quantity" prop="quantity">
						<Input v-model="formObj.quantity" :maxlength="50" style="width: 330px"
							:clearable="true"></Input>
					</FormItem>
					</Col>
					<Col :md="24" :lg="12">
					<FormItem label="Type" prop="type">
						<Input v-model="formObj.type" :maxlength="50" style="width: 330px" :clearable="true"></Input>
					</FormItem>
					</Col>
				</Row>

				<Row>
					<Col :md="24" :lg="12">
					<FormItem label="Batch" prop="batch">
						<Input v-model="formObj.batch" :maxlength="50" style="width: 330px" :clearable="true"></Input>
					</FormItem>
					</Col>
					<Col :md="24" :lg="12">
					<FormItem label="Transport_key" prop="transportkey">
						<Input v-model="formObj.transportkey" :maxlength="50" style="width: 330px"
							:clearable="true"></Input>
					</FormItem>
					</Col>
				</Row>

				<Row>
					<Col :md="24" :lg="12">
					<FormItem label="OP_Key" prop="opKey">
						<Input v-model="formObj.opKey" :maxlength="50" style="width: 330px" :clearable="true"></Input>
					</FormItem>
					</Col>
					<Col :md="24" :lg="12">
					<FormItem label="OTA_Transport_Key" prop="otaTransportKey">
						<Input v-model="formObj.otaTransportKey" :maxlength="50" style="width: 330px"
							:clearable="true"></Input>
					</FormItem>
					</Col>
				</Row>

				<Row>
					<Col :md="24" :lg="12">
					<FormItem label="Address1" prop="address1">
						<Input v-model="formObj.address1" :maxlength="50" style="width: 330px"
							:clearable="true"></Input>
					</FormItem>
					</Col>
					<Col :md="24" :lg="12">
					<FormItem label="Address2" prop="address2">
						<Input v-model="formObj.address2" :maxlength="50" style="width: 330px"
							:clearable="true"></Input>
					</FormItem>
					</Col>
				</Row>

				<Row>
					<Col :md="24" :lg="12">
					<FormItem label="Address3" prop="address3">
						<Input v-model="formObj.address3" :maxlength="50" style="width: 330px"
							:clearable="true"></Input>
					</FormItem>
					</Col>
					<Col :md="24" :lg="12">
					<FormItem label="Address4" prop="address4">
						<Input v-model="formObj.address4" :maxlength="50" style="width: 330px"
							:clearable="true"></Input>
					</FormItem>
					</Col>
				</Row>

				<Row>
					<Col :md="24" :lg="12">
					<FormItem label="Product_Code" prop="productCode">
						<Input v-model="formObj.productCode" :maxlength="50" style="width: 330px"
							:clearable="true"></Input>
					</FormItem>
					</Col>
					<Col :md="24" :lg="12">
					<FormItem label="PO_Ref" prop="poRef">
						<Input v-model="formObj.poRef" :maxlength="50" style="width: 330px" :clearable="true"></Input>
					</FormItem>
					</Col>
				</Row>

				<Row>
					<Col :md="24" :lg="12">
					<FormItem label="Artwork" prop="artwork">
						<Input v-model="formObj.artwork" :maxlength="50" style="width: 330px" :clearable="true"></Input>
					</FormItem>
					</Col>
					<Col :md="24" :lg="12">
					<FormItem label="Roaming Exclusive List" prop="roamingExclusiveList">
						<Input v-model="formObj.roamingExclusiveList" placeholder="贴片卡填写，可为空" :maxlength="50"
							style="width: 330px" :clearable="true"></Input>
					</FormItem>
					</Col>
				</Row>

				<Row>
					<Col :md="24" :lg="12">
					<FormItem label="MSISDN" prop="msisdn" v-show="formObj.makeCardType != '3'">
						<Input v-model="formObj.msisdn" placeholder="如填写则从填写号码开始使用资源，为空则系统自动分配" style="width: 330px"
							:clearable="true"></Input>
					</FormItem>
					</Col>
					<Col :md="24" :lg="12">
					<FormItem label="IMSI" prop="imsi">
						<Input v-model="formObj.imsi" placeholder="如填写则从填写号码开始使用资源，为空则系统自动分配" style="width: 330px"
							:clearable="true"></Input>
					</FormItem>
					</Col>
				</Row>
			</div>

			<div style="margin-top: 30px;width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button type="primary" @click="makeCard"
					:loading="generateLoading">生成Inputfile</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<Button @click="back">返回</Button>
			</div>
		</Form>
	</card>

</template>

<script>
	import {
		getSupply
	} from '@/api/resoure/common';
	import {
		addTask,
		defaultContent,
	} from '@/api/resoure/makeCardFile';
	export default {
		data() {
			var validateQuantity = (rule, value, callback) => {
				var str = /^[0-9]*[1-9][0-9]*$/;
				if (str.test(value)) {
					callback();
				} else {
					callback(new Error("只支持正整数"));
				}
			};

			return {
				updatetimeLoading: false,
				generateLoading: false,
				providers: [],
				isContinuousList: [{
						value: 'true',
						label: '是'
					},
					{
						value: 'false',
						label: '否'
					},
				],
				isVimsiList: [{
						value: '1',
						label: '是'
					},
					{
						value: '0',
						label: '否'
					},
				],
				formObj: {
					supplierId: '',
					makeCardType: '',
					isContinuous: '',
					isVimsi: '',
					gtpProxy: '',
					taskName: '',
					customer: '',
					quantity: '',
					type: '',
					batch: '',
					transportkey: '',
					opKey: '',
					otaTransportKey: '',
					address1: '',
					address2: '',
					address3: '',
					address4: '',
					productCode: '',
					poRef: '',
					artwork: '',
					roamingExclusiveList: '',
					msisdn: '',
					imsi: '',

				},
				ruleInfoValidate: {
					supplierId: [{
						required: true,
						message: '请选择供应商',
					}, ],
					makeCardType: [{
						required: true,
						message: '请选择制卡类型',
					}, ],
					isContinuous: [{
						required: true,
						message: '请选择是否连续资源',
					}, ],
					isVimsi: [{
						required: true,
						message: '请选择是否vimsi制卡'
					}],
					gtpProxy: [{
						required: true,
						message: '请选择是否需要GTP PROXY指定号码'
					}],
					taskName: [{
						required: true,
						message: '请输入任务名称',
						trigger: 'blur'
					}],
					customer: [{
						required: true,
						message: '请输入客户名',
						trigger: 'blur'
					}],
					quantity: [{
						required: true,
						message: '请输入需分配主IMSI的数量',
					}, {
						validator: validateQuantity,
						trigger: 'blur'
					}],
					type: [{
						required: true,
						message: '请输入type',
						trigger: 'blur'
					}],
					batch: [{
						required: true,
						message: '请输入batch',
						trigger: 'blur'
					}],
					transportkey: [{
						required: true,
						message: '请输入transportKey',
						trigger: 'blur'
					}],
					opKey: [{
						required: true,
						message: '请输入opKey',
						trigger: 'blur'
					}],
					otaTransportKey: [{
						required: true,
						message: '请输入otaTransportKey',
						trigger: 'blur'
					}],
					address1: [{
						required: true,
						message: '请输入地址1',
						trigger: 'blur'
					}],
					address2: [{
						required: true,
						message: '请输入地址2',
						trigger: 'blur'
					}],
					address3: [{
						required: true,
						message: '请输入地址3',
						trigger: 'blur'
					}],
					address4: [{
						required: true,
						message: '请输入地址4',
						trigger: 'blur'
					}],
					productCode: [{
						required: true,
						message: '请输入产品编码',
						trigger: 'blur'
					}],
					poRef: [{
						required: true,
						message: '请输入poRef',
						trigger: 'blur'
					}],
					artwork: [{
						required: true,
						message: '请输入artwork',
						trigger: 'blur'
					}],

				},
			}
		},
		mounted() {
			// 获取供应商列表
			this.getProviders()
			// 默认模板
			// 新建任务时后端返回默认值模板
			defaultContent().then((res) => {
				if (res.code === "0000") {
					this.formObj.customer = res.data["customer"];
					this.formObj.address1 = res.data["address1"];
					this.formObj.address2 = res.data["address2"];
					this.formObj.address3 = res.data["address3"];
					this.formObj.address4 = res.data["address4"];
					this.formObj.artwork = res.data["artwork"];
					this.formObj.productCode = res.data["productCode"];
					this.formObj.poRef = res.data["poRef"];
					this.formObj.type = res.data["type"];
				} else {
					throw res
				}
			});
			// 保存上一页返回数据
			localStorage.setItem("searchObj", decodeURIComponent(this.$route.query.searchObj))
		},
		methods: {
			// 供应商
			getProviders() {
				getSupply().then(res => {
					if (res && res.code == '0000') {
						this.providers = res.data
					} else {
						throw res
					}
				}).catch((err) => {})
			},

			makeCard() {
				this.$refs["formObj"].validate((valid) => {
					if (valid) {
						this.generateLoading = true;
						const formData = JSON.parse(JSON.stringify(this.formObj));
						addTask(formData).then((res) => {
								if (res.code === "0000") {
									this.$Notice.success({
										title: "操作提示",
										desc: "操作成功",
									});
									this.$router.push({
										name: "makeCardFile_mngr"
									});
								}
							})
							.finally(() => {
								this.generateLoading = false;
							});
					}
				});
			},

			back() {
				this.$router.push({
					path: '/makeCardFile',
				})
			}
		},
	}
</script>

<style>
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}
	
	.search_box {
		width: 490px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
	}
	
</style>
