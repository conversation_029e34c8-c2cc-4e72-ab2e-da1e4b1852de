<template>
	<!-- 制卡文件管理 -->
	<div>
		<Card>
			<div class="search_head_i">
				<div class="search_box1">
					<span class="search_box_label">任务名称:</span>
					<Input v-model.trim='searchObj.taskName' placeholder="请输入任务名称" clearable style="width: 220px;" />
				</div>&nbsp;&nbsp;&nbsp;
				<div class="search_box1">
					<Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading"
						@click="search()">搜索</Button>&nbsp;&nbsp;&nbsp;
					<Button v-has="'add'" style="margin: 0 2px;margin-left: 20px;" type="warning" icon="md-add"
						@click="addTask()">新建制卡任务</Button>
				</div>
			</div>

			<div style="margin-top:20px">
				<Table :columns="columns" :data="data" style="width:100%; margin-to: 20px;" :loading="loading">
					<template slot-scope="{ row, index }" slot="rollbackSuccessFile">
						<a :loading="exporting" v-if="row.rollbackSuccessFile"
							@click="exportfile(row.id,1)">{{row.rollbackSuccessFile.slice(0,-4)}}</a>
					</template>
					<template slot-scope="{ row, index }" slot="rollbackFailFile">
						<a :loading="exporting" v-if="row.rollbackFailFile"
							@click="exportfile(row.id,2)">{{row.rollbackFailFile.slice(0,-4)}}</a>
					</template>
					<template slot-scope="{ row, index }" slot="action">
						<Button v-has="'view'" type="info" size="small" style="margin-right: 10px" @click="Info(row)"
							v-if="row.taskStatus === '2' || row.taskStatus === '3'" disabled>查看</Button>
						<Button v-has="'view'" type="info" size="small" style="margin-right: 10px" @click="Info(row)"
							v-else>查看</Button>

						<Button type="success" size="small" style="margin-right: 10px" v-has="'download'"
							@click="exportfile(row.id,3)" v-if="row.taskStatus === '2' || row.taskStatus === '3'"
							disabled>下载</Button>
						<Button type="success" size="small" style="margin-right: 10px" v-has="'download'"
							@click="exportfile(row.id,3)" v-else>下载</Button>

						<Button v-has="'rollback'" type="warning" size="small" @click="Reback(row.id)"
							v-if="row.taskStatus === '2' || row.taskStatus === '3'" disabled>回滚</Button>
						<Button v-has="'rollback'" type="warning" size="small" @click="Reback(row.id)"
							v-else>回滚</Button>
					</template>
				</Table>
				<!-- 分页 -->
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage"
					style="margin: 15px 0;" />
			</div>

			<Modal v-model="modal5" title="任务详情" @on-ok="ok" @on-cancel="cancelModal" width="620px">
				<div style="padding: 15px">
					<div style="font-weight:bold; height: 25px; line-height: 25px;">
						<span style="font-weight:bold;">任务名称:</span>&nbsp;&nbsp;
						<span style="font-weight:bold;">{{taskName}}</span>&nbsp;&nbsp;
					</div>
					<div style="font-weight:bold; height: 25px; line-height: 25px;">
						<span style="font-weight:bold;">制卡文件名:</span>&nbsp;&nbsp;
						<span v-model='taskName' style="font-weight:bold;">{{cardfileName}}</span>&nbsp;&nbsp;
					</div>
					<div style="font-weight:bold; height: 25px; line-height: 25px;">
						<span style="font-weight:bold;">是否需要GTP PROXY指定号码:</span>&nbsp;
						<span v-model='taskName' style="font-weight:bold;">{{gtpProxy}}</span>&nbsp;&nbsp;
					</div>
					<ul>
						<li id="space" v-for="(item,i) in items" :key="items.i">
							{{item}}
						</li>
					</ul>

					<div v-if="remind">
						<p style="font-weight: bold;color: firebrick;">内容超长，请下载查看！</p>
					</div>
				</div>

				<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
					<Button @click="cancelModal()">返回</Button>&nbsp;&nbsp;&nbsp;&nbsp;
					<Button type="primary" @click="exportfile(id,3)">下载</Button>
				</div>
			</Modal>
			<a ref="downloadLink" style="display: none"></a>
		</Card>
	</div>
</template>

<script>
	import excel from '@/libs/excel';
	import {
		getPage,
		downloadFile,
		doReback,
		cardFileView,

	} from '@/api/resoure/makeCardFile';
	export default {
		data() {
			return {
				total: 0,
				currentPage: 1,
				page: 1,
				id: '',
				cardfileName: '',
				gtpProxy: '',
				taskName: '',
				items: [],
				searchloading: false,
				loading: false,
				updatetimeLoading: false,
				exporting: false,
				remind: false,
				modal5: false,
				searchObj: {
					taskName: '', //任务名称
				},
				columns: [{
						title: '任务名称',
						key: 'taskName',
						minWidth: 120,
						align: 'center',
						tooltip: true
					},
					{
						title: '资源供应商',
						key: 'supplierrms',
						minWidth: 100,
						align: 'center',
					},
					{
						title: '制卡类型',
						key: 'cardType',
						minWidth: 90,
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.cardType) {
								case "1":
									text = "普通卡";
									break;
								case "2":
									text = "省移动";
									break;
								case "3":
									text = "欧洲卡";
                default:
                	text = "";
							}
							return h('label', text)
						}
					},
					{
						title: '执行状态',
						key: 'taskStatus',
						minWidth: 90,
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.taskStatus) {
								case "1":
									text = "成功";
									break;
								case "2":
									text = "失败";
									break;
								case "3":
									text = "制卡中";
									break;
								case "4":
									text = "回滚中";
                default:
                	text = "";
							}
							return h('label', text)
						}
					},
					{
						title: '任务开始时间',
						key: 'taskStartTime',
						minWidth: 150,
						align: 'center',
					},
					{
						title: '任务结束时间',
						key: 'taskEndTime',
						minWidth: 150,
						align: 'center',
					},
					{
						title: '制卡文件名',
						key: 'cardfileName',
						minWidth: 120,
						align: 'center',
						tooltip: true
					},
					{
						title: '任务失败原因',
						key: 'taskFailLog',
						width: 160,
						align: 'center',
						tooltip: true
					},
				],
				data: [],
			}
		},
		mounted() {
			//缓存数据
			let searchObj = JSON.parse(localStorage.getItem("searchObj")) === null ? '' : JSON.parse(localStorage.getItem(
				"searchObj"))
			if (searchObj) {
				this.searchObj.taskName = searchObj.taskName === undefined ? "" : searchObj.taskName
			}
			//清除缓存
			localStorage.removeItem("searchObj")
			this.init();
		},

		methods: {
			// 页面初始化
			init() {
				var rollbackSuccessFile = ['download'];
				var rollbackFailFile = ['download'];
				var action = ['view', 'download', 'rollback'];
				var btnPriv = this.$route.meta.permTypes;

				var successFileMixed = rollbackSuccessFile.filter(function(val) {
					return btnPriv.indexOf(val) > -1
				});
				var failFileMixed = rollbackFailFile.filter(function(val) {
					return btnPriv.indexOf(val) > -1
				});
				var actionMixed = action.filter(function(val) {
					return btnPriv.indexOf(val) > -1
				});
				if (successFileMixed.length > 0) {
					var width = 100 + 100 * successFileMixed.length;
					this.columns.splice(9, 0, {
						title: '回滚成功文件',
						slot: 'rollbackSuccessFile',
						width: width,
						align: 'center',
						tooltip: true
					});
				};
				if (successFileMixed.length > 0) {
					var width = 100 + 100 * successFileMixed.length;
					this.columns.splice(10, 0, {
						title: '回滚失败文件',
						slot: 'rollbackFailFile',
						width: width,
						align: 'center',
						tooltip: true
					});
				}
				if (actionMixed.length > 0) {
					var width = 40 + 50 * actionMixed.length;
					this.columns.splice(11, 0, {
						title: '操作',
						slot: 'action',
						width: width,
						align: 'center'
					});
				};

				//加载初始信息
				this.goPageFirst(1);
			},

			// 列表加载
			goPageFirst(page) {
				this.loading = true
				var _this = this
				getPage({
					pageSize: 10,
					pageNumber: page,
					taskName: this.searchObj.taskName,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.count
						this.data = res.data
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			// 搜索
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			// 加载表格数据
			goPage(page) {
				this.goPageFirst(page)
			},
			// 新建制卡任务
			addTask: function() {
				this.$router.push({
					path: '/addCard',
					query: {
						searchObj: encodeURIComponent(JSON.stringify(this.searchObj)),
					}
				})
			},
			// 下载成功/失败/原始文件
			exportfile(id, type) {
				this.exporting = true
				var _this = this
				downloadFile(id, type).then(res => {
					const content = res.data
					let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[
						1]) //获取到Content-Disposition;filename  并解码
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content, fileName)
					}
				}).catch(err => this.exporting = false)
			},
			ok() {

			},
			cancelModal() {
				this.modal5 = false
			},
			// 查看详情
			Info(row) {
				let id = row.id;
				let taskName = row.taskName;
				let cardfileName = row.cardfileName
				this.gtpProxy = row.gtpProxy == "1" ? "是" : row.gtpProxy == "2" ? "否" : ""
				cardFileView(id).then(res => {
					if (res.code === '0000') {
						this.modal5 = true
						this.id = id
						this.items = res.data
						this.taskName = taskName
						this.cardfileName = cardfileName
						if (this.items.length > 35) {
							this.remind = true
						}
					}

				}).catch((err) => {
					console.log(err)
				})
			},
			// 回滚
			Reback(id) {
				this.$Modal.confirm({
					title: '是否回滚？',
					onOk: () => {
						doReback(id).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: res.msg
								})
								this.goPageFirst(this.currentPage);
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						})
					}
				});
			},
		}
	}
</script>

<style>
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box1 {
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		margin: 0 10px;
		width: 70px;
	}

	ul li {
		list-style-type: none;
	}

	#space {
		height: 25px;
		line-height: 25px;
		font-size: 12px;
		white-space: pre-line;
		/* word-break: break-all;
		word-wrap: break-word; */
	}
</style>
