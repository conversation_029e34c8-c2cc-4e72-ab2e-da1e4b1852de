<template>
	<div>
		<Card>
			<div class="search_head">
				<span style="font-weight:bold;">MSISDN号码：</span>&nbsp;&nbsp;
				<Input placeholder="输入MSISDN号码..." v-model="msisdnCondition" clearable
					style="width: 200px" />&nbsp;&nbsp;
				<span style="font-weight:bold;">状态：</span>&nbsp;&nbsp;
				<Select filterable v-model="status" placeholder="下拉选择状态" style="width:300px" clearable>
					<Option :value="item.value" v-for="(item,index) in statuses" :key="index">{{item.label}}</Option>
				</Select>&nbsp;&nbsp;
				<Button type="primary" icon="md-search" :loading="searchLoading"
					@click="search()">搜索</Button>&nbsp;&nbsp;
				<Button v-has="'add'" icon="md-add" type="success" @click="addMsisdn()">导入</Button>&nbsp;&nbsp;
				<Button v-has="'batchUpdate'" icon="md-add" type="warning"
					@click="updateBatch()">批量修改</Button>&nbsp;&nbsp;
				<Button v-has="'batchDelete'" icon="md-add" type="error"
					@click="deleteBatch()">批量删除</Button>&nbsp;&nbsp;
				<Button v-has="'taskView'" icon="md-add" type="info"
					@click="taskView()">任务查看</Button>&nbsp;&nbsp;

			</div>
			<div style="margin-top:20px">
				<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row, index }" slot="action">
						<Button v-has="'update'"
							v-if="row.status == 1 || row.status == 2 || row.status == 5 || row.status == 6"
							type="success" size="small" style="margin-right: 10px" @click="update(row)">修改</Button>
						<Button v-has="'delete'"
							v-if="row.status == 1 || row.status == 2 || row.status == 5 || row.status == 6" type="error"
							size="small" :loading="row.delLoading" @click="deleteItem(row)">删除</Button>
					</template>
				</Table>
			</div>
			<div class="table-botton" style="margin-top:15px">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage"
					style="margin: 10px 0;" />
			</div>
		</Card>
		<Modal v-model="modal1" title="导入MSISDN" width="620px" :mask-closable="false" @on-cancel="cancel1">
			<div class="search_head" style="margin: 50px 0px;">
				<Form ref="formValidate1" :model="formValidate1" :rules="ruleValidate1" :label-width="180"
					:label-height="100" inline style="font-weight:bold;">
					<FormItem label="MSISDN起始号码" prop="begin">
						<Input placeholder="MSISDN起始号码..." v-model="formValidate1.begin" clearable
							style="width: 300px" />&nbsp;&nbsp;
					</FormItem>
					<FormItem label="MSISDN结束号码" prop="end">
						<Input placeholder="MSISDN结束号码..." v-model="formValidate1.end" clearable
							style="width: 300px" />&nbsp;&nbsp;
					</FormItem>
					<FormItem label="供应商" prop="provider">
						<Select filterable v-model="formValidate1.provider" placeholder="下拉选择供应商" style="width:300px" @on-change="getProviders"
						 clearable>
							<Option :value="item.supplierId" v-for="(item,index) in providers" :key="index">{{item.supplierName}}</Option>
						</Select>
					</FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancel1">取消</Button>
				<Button type="primary" :loading="addLoading" @click="add('formValidate1')">确定</Button>
			</div>
		</Modal>
		<Modal v-model="modal2" title="修改MSISDN" :mask-closable="false" @on-cancel="cancel2">
			<div class="search_head">
				<Form ref="formValidate2" :model="formValidate2" :rules="ruleValidate2" :label-width="120"
					:label-height="100" inline style="font-weight:bold;">
					<FormItem label="MSISDN号码">
						<span>{{msisdnChoosed.msisdn}}</span>&nbsp;&nbsp;
					</FormItem>
					<FormItem label="状态" prop="status">
						<Select filterable v-if="msisdnChoosed.status == 1" v-model="formValidate2.status"
							placeholder="下拉选择状态" style="width:300px" clearable>
							<Option :value="item.value" v-for="(item,index) in updateStatuses1" :key="index">
								{{item.label}}</Option>
						</Select>
						<Select filterable v-if="msisdnChoosed.status == 2" v-model="formValidate2.status"
							placeholder="下拉选择状态" style="width:300px" clearable>
							<Option :value="item.value" v-for="(item,index) in updateStatuses3" :key="index">
								{{item.label}}</Option>
						</Select>
						<Select filterable v-if="msisdnChoosed.status == 5" v-model="formValidate2.status"
							placeholder="下拉选择状态" style="width:300px" clearable>
							<Option :value="item.value" v-for="(item,index) in updateStatuses1" :key="index">
								{{item.label}}</Option>
						</Select>
						<Select filterable v-if="msisdnChoosed.status == 6" v-model="formValidate2.status"
							placeholder="下拉选择状态" style="width:300px" clearable>
							<Option :value="item.value" v-for="(item,index) in updateStatuses2" :key="index">
								{{item.label}}</Option>
						</Select>
					</FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancel2">取消</Button>
				<Button type="primary" :loading="updateLoading" @click="updateMsisdn('formValidate2')">确定</Button>
			</div>
		</Modal>
		<Modal v-model="modal3" title="批量修改MSISDN" :mask-closable="false" @on-cancel="cancel3">
			<div class="search_head">
				<Form ref="formValidate3" :model="formValidate3" :rules="ruleValidate3" :label-width="120"
					:label-height="100" inline style="font-weight:bold;">
					<FormItem label="MSISDN起始号码" prop="begin">
						<Input placeholder="MSISDN起始号码..." v-model="formValidate3.begin" clearable
							style="width: 300px" />&nbsp;&nbsp;
					</FormItem>
					<FormItem label="MSISDN结束号码" prop="end">
						<Input placeholder="MSISDN结束号码..." v-model="formValidate3.end" clearable
							style="width: 300px" />&nbsp;&nbsp;
					</FormItem>
          <FormItem label="状态" prop="status">
          	<Select filterable v-model="formValidate3.status" placeholder="下拉选择状态" style="width:300px" clearable>
          		<Option :value="item.value" v-for="(item,index) in updateStatuses1" :key="index">{{item.label}}</Option>
          	</Select>
          </FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancel3">取消</Button>
				<Button type="primary" :loading="updateBatchLoading"
					@click="updateMsisdnBatch('formValidate3')">确定</Button>
			</div>
		</Modal>
		<Modal v-model="modal4" title="批量删除MSISDN" :mask-closable="false" @on-cancel="cancel4" width="620px">
			<div class="search_head">
				<Form ref="formValidate4" :model="formValidate4" :rules="ruleValidate4" :label-width="120"
					:label-height="100" inline style="font-weight:bold;">
					<FormItem label="MSISDN起始号码" prop="begin">
						<Input placeholder="MSISDN起始号码..." v-model="formValidate4.begin" clearable
							style="width: 300px" />&nbsp;&nbsp;
					</FormItem>
					<FormItem label="MSISDN结束号码" prop="end">
						<Input placeholder="MSISDN结束号码..." v-model="formValidate4.end" clearable
							style="width: 300px" />&nbsp;&nbsp;
					</FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancel4">取消</Button>
				<Button type="primary" :loading="delBatchLoading" @click="delMsisdnBatch('formValidate4')">确定</Button>
			</div>
		</Modal>
		<Modal v-model="taskViewFlag" title="任务查看" :mask-closable="false" :footer-hide="true" width="1225px"
			:loading="recordLoading">
			<Table :columns="taskColumns" :data="taskData" :ellipsis="true" :loading="taskloading">
				<template slot-scope="{ row, index }" slot="successFileUrl">
					<Button v-has="'download'" v-if="row.taskStatus === '1' || row.successFileUrl === null" disabled type="success"
						@click="exportfile(row,3)">点击下载</Button>
					<Button v-has="'download'" v-else type="success" @click="exportfile(row,3)">点击下载</Button>
				</template>
				<template slot-scope="{ row, index }" slot="failFileUrl">
					<Button v-has="'download'" v-if="row.taskStatus === '1' || row.failFileUrl === null" disabled type="error"
						@click="exportfile(row,2)">点击下载</Button>
					<Button v-has="'download'" v-else type="error" @click="exportfile(row,2)">点击下载</Button>
				</template>
			</Table>
			<!-- 分页 -->
			<Page :total="recordTotal" :current.sync="currentRecordPage" show-total show-elevator
				@on-change="goRecordPage" style="margin: 15px 0;" />
		</Modal>
		<a ref="downloadLink" style="display: none"></a>
	</div>
</template>

<script>
	import {
		getSupply
	} from '@/api/resoure/common';
	import {
		getPage,
		add,
		updateStatus,
		del,
		updateBatch,
		delBatch,
		getRecordPage,
		exportFile
	} from '@/api/resoure/msisdn';
	export default {
		components: {},
		data() {
			// 校验是否为纯数字
			const checkNumber = (rule, value, callback) => {
				let reg = /^[0-9]\d*$/;
				if (reg.test(value)) {
					if (value.length > 30) {
						callback(new Error("请输入1-30位的纯数字"));
					} else {
						callback();
					}
				} else {
					callback(new Error("请输入1-30位的纯数字"));
				}
			};
			return {
				formValidate1: {
					begin: '',
					end: '',
					provider: ''
				},
				ruleValidate1: {
					begin: [{
							required: true,
							message: '请输入MSISDN起始号码',
							trigger: 'blur'
						},
						{
							validator: checkNumber,
							trigger: "blur"
						}
					],
					end: [{
							required: true,
							message: '请输入MSISDN结束号码',
							trigger: 'blur'
						},
						{
							validator: checkNumber,
							trigger: "blur"
						}
					],
					provider: [{
						required: true,
						message: '请选择供应商',
						trigger: 'blur'
					}]
				},
				formValidate2: {
					status: ''
				},
				ruleValidate2: {
					status: [{
						type: 'number',
						required: true,
						message: '请选择修改后状态',
						trigger: 'blur'
					}]
				},
				formValidate3: {
					begin: '',
					end: '',
					status: ''
				},
				ruleValidate3: {
					begin: [{
							required: true,
							message: '请输入MSISDN起始号码',
							trigger: 'blur'
						},
						{
							validator: checkNumber,
							trigger: "blur"
						}
					],
					end: [{
							required: true,
							message: '请输入MSISDN结束号码',
							trigger: 'blur'
						},
						{
							validator: checkNumber,
							trigger: "blur"
						}
					],
					status: [{
						type: 'number',
						required: true,
						message: '请选择修改后状态',
						trigger: 'blur'
					}]
				},
				formValidate4: {
					begin: '',
					end: '',
				},
				ruleValidate4: {
					begin: [{
							required: true,
							message: '请输入MSISDN起始号码',
							trigger: 'blur'
						},
						{
							validator: checkNumber,
							trigger: "blur"
						}
					],
					end: [{
							required: true,
							message: '请输入MSISDN结束号码',
							trigger: 'blur'
						},
						{
							validator: checkNumber,
							trigger: "blur"
						}
					],
				},
				// 表头信息
				columns: [{
						title: 'MSISDN',
						key: 'msisdn',
						align: 'center'
					},
					{
						title: '供应商',
						key: 'supplierName',
						align: 'center'
					},
					{
						title: '入库时间',
						key: 'createTime',
						align: 'center'
					},
					{
						title: '当前状态',
						key: 'status',
						align: 'center',
						render: (h, params) => {
							const row = params.row
							const color = row.status == 1 ? '#19be6b' : row.status == 2 ? '#ff0000' : row.status ==
								3 ? '#27A1FF' : row.status == 4 ? '#ff9900' : row.status == 5 ? '#d75b0f' : row
								.status == 6 ? '#d518bc' : '#515a6e'
							const text = row.status == 1 ? '已导入' : row.status == 2 ? '待分配' : row.status == 3 ?
								'已分配' : row.status == 4 ? '使用中' : row.status == 5 ? '已冻结' : row.status == 6 ?
								'留存' : '其他'
							return h('label', {
								style: {
									color: color
								}
							}, text)
						}
					},
					{
						title: '操作',
						slot: 'action',
						width: 300,
						align: 'center'
					}
				],
				taskColumns: [{
						title: '任务创建时间',
						key: 'createTime',
						align: 'center',
						width: '150px',	
					},
					{
						title: '处理状态',
						key: 'taskStatus',
						align: 'center',
						width: '100px',
						render: (h,params) => {
							const row = params.row
							var text =row.taskStatus === '1' ? "处理中" : row.taskStatus === '2' ? "已完成" : ''
							return h('label',text)
						}
					},
					{
						title: '开始号码',
						key: 'msisdnStart',
						align: 'center',
						width: '120px',
					},
					{
						title: '结束号码',
						key: 'msisdnEnd',
						align: 'center',
						width: '120px',
					},
					{
						title: '供应商',
						key: 'supplierrms',
						align: 'center',
						width: '100px',
					},
					{
						title: '导入总数量',
						key: 'importNum',
						align: 'center',
						width: '100px',
					},
					{
						title: '导入成功数量',
						key: 'successNum',
						align: 'center',
						width: '110px',
					},
					{
						title: '导入失败数量',
						key: 'failNum',
						align: 'center',
						width: '110px',
					},
					{
						title: '导入成功文件',
						slot: 'successFileUrl',
						align: 'center',
						width: '140px',
					},
					{
						title: '导入失败文件',
						slot: 'failFileUrl',
						align: 'center',
						width: '140px',
					},
				],
				taskData: [],
				providers: [],
				statuses: [{
						label: '已导入',
						value: 1
					},
					{
						label: '待分配',
						value: 2
					},
					{
						label: '已分配',
						value: 3
					},
					{
						label: '使用中',
						value: 4
					},
					{
						label: '已冻结',
						value: 5
					},
					{
						label: '留存',
						value: 6
					}
				],
				updateStatuses1: [{
						label: '待分配',
						value: 2
					},
					{
						label: '留存',
						value: 6
					}
				],
				updateStatuses2: [{
					label: '待分配',
					value: 2
				}],
				updateStatuses3: [{
					label: '留存',
					value: 6
				}],
				tableData: [],
				loading: false,
				addLoading: false,
				searchLoading: false,
				updateLoading: false,
				updateBatchLoading: false,
				delBatchLoading: false,
				recordLoading: false,
				taskloading: false,
				currentPage: 1,
				total: 0,
				currentRecordPage: 1,
				recordTotal: 0,
				msisdnCondition: '',
				msisdnChoosed: {},
				ids: [],
				modal1: false,
				modal2: false,
				modal3: false,
				modal4: false,
				taskViewFlag: false,
				status: '',
				selection: [], //多选
				selectionIds: [], //多选ids
			}
		},
		watch: {
			'$route': 'reload'
		},
		computed: {},
		methods: {
			// 页面加载
			goPageFirst(page) {
				this.loading = true
				let pageSize = 10
				let pageNumber = page
				getPage({
					msisdn: this.msisdnCondition,
					status: this.status,
					pageNumber,
					pageSize
				}).then(res => {
					if (res && res.code == '0000') {
						this.tableData = res.data
						this.total = res.count
						this.loading = false
						this.searchLoading = false
						if (this.tableData.length) {
							this.tableData.map(item => {
								this.$set(item, 'delLoading', false)
								return item
							})
						}
					} else {
						throw res
					}
				}).catch((err) => {
					this.loading = false
					this.searchLoading = false
					if (this.tableData.length) {
						this.tableData.map(item => {
							this.$set(item, 'delLoading', false)
							return item
						})
					}
				})
			},
			// 查询按钮，指定号码查询
			// 分页跳转
			goPage(page) {
				this.currentPage = page
				this.goPageFirst(page)
			},
			error(nodesc) {
				this.$Notice.error({
					title: '出错啦',
					desc: nodesc ? '' : '服务器内部错误'
				})
			},
			search() {
				this.searchLoading = true
				this.currentPage = 1
				this.goPageFirst(1)
			},
			addMsisdn() {
				this.modal1 = true
			},
			add(name) {
				this.$refs[name].validate((valid) => {
					if (valid) {
						this.addLoading = true
						add({
							phonenumEnd: this.formValidate1.end,
							phonenumStart: this.formValidate1.begin,
							supplierId: this.formValidate1.provider
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '成功',
									desc: '操作成功'
								})
								this.currentPage = 1
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {})
						this.cancel1()
					}
				})
			},
			update(item) {
				this.modal2 = true
				this.msisdnChoosed = item
			},
			updateMsisdn(name) {
				this.$refs[name].validate((valid) => {
					if (valid) {
						this.updateLoading = true
						updateStatus({
							msisdn: this.msisdnChoosed.msisdn,
							status: this.formValidate2.status
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '成功',
									desc: '操作成功'
								})
								this.currentPage = 1
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							this.currentPage = 1
							this.goPageFirst(1)
						})
						this.msisdnChoosed = ''
						this.cancel2()
					}
				})
			},
			updateBatch() {
				this.modal3 = true
			},
			updateMsisdnBatch(name) {
				this.$refs[name].validate((valid) => {
					if (valid) {
						this.updateBatchLoading = true
						updateBatch({
							phonenumEnd: this.formValidate3.end,
							phonenumStart: this.formValidate3.begin,
							status: this.formValidate3.status
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '成功',
									desc: '操作成功'
								})
								this.currentPage = 1
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {})
						this.cancel3()
					}
				})
			},
			deleteBatch() {
				this.modal4 = true
			},
			deleteItem(item) {
				item.delLoading = true
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						del({
							msisdn: item.msisdn,
							status: item.status
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '成功',
									desc: '操作成功'
								})
								this.currentPage = 1
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							this.currentPage = 1
							this.goPageFirst(1)
						})
					},
					onCancel: () => {
						item.delLoading = false
					}
				})
			},
			delMsisdnBatch(name) {
				this.$refs[name].validate((valid) => {
					if (valid) {
						this.delBatchLoading = true
						delBatch({
							phonenumEnd: this.formValidate4.end,
							phonenumStart: this.formValidate4.begin,
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '成功',
									desc: '操作成功'
								})
								this.currentPage = 1
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {})
						this.cancel4()
					}
				})
			},
			getProviders() {
				getSupply().then(res => {
					if (res && res.code == '0000') {
						this.providers = res.data
					} else {
						throw res
					}
				}).catch((err) => {})
			},
			cancel1() {
				this.modal1 = false
				this.$refs.formValidate1.resetFields()
				this.addLoading = false
			},
			cancel2() {
				this.modal2 = false
				this.$refs.formValidate2.resetFields()
				this.updateLoading = false
			},
			cancel3() {
				this.modal3 = false
				this.$refs.formValidate3.resetFields()
				this.updateBatchLoading = false
			},
			cancel4() {
				this.modal4 = false
				this.$refs.formValidate4.resetFields()
				this.delBatchLoading = false
			},
			taskView() {
				this.taskViewFlag = true
				this.goRecodePageFirst(1)
			},
			// 导入记录查看列表
			goRecodePageFirst: function(page) {
				this.loading = true
				var _this = this
				getRecordPage({
					pageSize: 10,
					pageNo: page,
					type: 1
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						var data = res.data
						this.currentRecordPage = page
						this.recordTotal = data.total
						this.taskData = data.records
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					_this.loading = false
					this.recordLoading = false
				})
			},
			goRecordPage(page) {
				this.goRecodePageFirst(page)
			},
			// 导入记录查看 文件下载
			exportfile:function(row, type) {
				this.taskloading = true
				var _this = this
				exportFile({
					id: row.id,
					type: type
				}).then(res => {
					const content = res.data
					let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[1]) //获取到Content-Disposition;filename  并解码
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content, fileName)
					}
				}).finally(() => {
					this.taskloading = false
				})
			},
		},
		mounted() {
			this.getProviders()
			this.goPageFirst(1)
		}
	}
</script>
<style>
	.search_head {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 20px;
	}

	.search_head_label {
		margin-top: 20px;
		font-size: 17px;
	}
</style>
