<template>
	<!-- OTA数据管理 -->
	<div>
		<Card>
			<div class="search_head_i">
				<div class="search_box1">
					<span class="search_box_label">任务名称:</span>
					<Input v-model.trim='searchObj.taskName' placeholder="请输入任务名称" clearable class="searchCondition" />
				</div>&nbsp;&nbsp;&nbsp;
        <div class="search_box1">
        	<span class="search_box_label">任务类型:</span>
        	<Select filterable v-model="searchObj.taskType" placeholder="请选择任务类型" clearable class="searchCondition">
        		<Option value='0'>删除OTA数据</Option>
        		<Option value='1'>新增OTA数据</Option>
        	</Select>
        </div>&nbsp;&nbsp;&nbsp;
        <div class="search_box1">
        	<span class="search_box_label">任务状态:</span>
        	<Select filterable v-model="searchObj.status" placeholder="请选择任务状态" clearable class="searchCondition">
        		<Option value='0'>待开始</Option>
        		<Option value='1'>处理中</Option>
        		<Option value='2'>已完成</Option>
        		<Option value='3'>失败</Option>
        	</Select>
        </div>&nbsp;&nbsp;&nbsp;
				<div class="search_box1">
					<Button type="primary" icon="md-search" :loading="searchloading" @click="search()" v-has="'search'">
            搜索
          </Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<Button type="warning" icon="md-add" @click="addTask()" v-has="'addTask'">新建任务</Button>
				</div>
			</div>
			<div style="margin-top:20px">
				<Table :columns="columns" :data="data" style="width:100%; margin-top: 20px;" :loading="loading">
          <template slot-scope="{ row, index }" slot="originFilePath">
            <Button v-has="'originFilePath'" type="info" ghost size="small" style="margin-right: 5px" v-if="row.originFilePath" @click="exportfile(row.id, '1')">
              点击下载
            </Button>
          </template>
          <template slot-scope="{ row, index }" slot="successedFilePath">
          	<Button v-has="'successedFilePath'" type="success" ghost size="small" style="margin-right: 5px" v-if="row.successedFilePath" @click="exportfile(row.id, '2')">
            点击下载
            </Button>
          </template>
          <template slot-scope="{ row, index }" slot="failedFilePath">
          	<Button v-has="'failedFilePath'" type="error" ghost size="small" v-if="row.failedFilePath" @click="exportfile(row.id, '3')">
            点击下载
            </Button>
          </template>
				</Table>
				<!-- 分页 -->
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage"
					style="margin: 15px 0;" />
			</div>
      <Table :columns="modelColumns" :data="modelData" ref="modelTable" v-show="false"></Table>
			<!-- 新建OTA数据管理任务 弹窗 -->
			<Modal title="新建OTA数据管理任务" v-model="addTaskModel" :mask-closable="false" width="600px" @on-cancel="cancelModal">
				<Form ref="formobj" :model="formobj" :rules="formobjRule" :label-width="120" :label-height="100"
				 inline style="font-weight:bold;">
					<FormItem label="任务名称" prop="taskName">
            <Input v-model.trim='formobj.taskName' maxlength="255" placeholder="请输入任务名称" clearable style="width: 400px;" />
					</FormItem>
          <FormItem label="任务类型" prop="taskType">
            <RadioGroup v-model="formobj.taskType" @on-change="changeType(formobj.taskType)">
              <Radio label="0">删除OTA数据</Radio>
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
              <Radio label="1">新增OTA数据</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="文件" prop="file" style="width:520px" v-if="formobj.taskType">
						<Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/"
						:action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
						:on-progress="fileUploading">
							<div style="padding: 20px 0">
								<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
								<p>点击或拖拽文件上传</p>
							</div>
						</Upload>
						<ul class="ivu-upload-list" v-if="file" style="width: 100%;">
						  <li class="ivu-upload-list-file ivu-upload-list-file-finish">
						    <span><Icon type="ios-folder" />{{file.name}}</span>
						    <i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
						  </li>
						</ul>
						<div style="width: 100%;">
							<Button type="primary" icon="ios-download" @click="downloadFile">下载模板文件</Button>
							<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
						</div>
					</FormItem>
				</Form>
			  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content: center;">
			  	<Button @click="cancelModal">返回</Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			  	<Button type="primary" :loading="uploadLoading" @click="handleUpload">确定</Button>
			  </div>
			</Modal>
			<!-- 导出提示 -->
			<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancelModal">
				<div style="align-items: center;justify-content:center;display: flex;">
					<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
						<h1 style="text-align: center;margin-bottom: 10px;">导出提示</h1>
						<FormItem label="你本次导出任务ID为:">
							<span style="width: 100px;">{{taskId}}</span>
						</FormItem>
						<FormItem label="你本次导出的文件名为:">
							<span>{{taskName}}</span>
						</FormItem>
						<span style="text-align: left;">请前往下载管理-下载列表查看及下载。</span>
					</Form>
				</div>

				<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
					<Button @click="cancelModal">取消</Button>
					<Button type="primary" @click="Goto">立即前往</Button>
				</div>
			</Modal>
      <a ref="downloadLink" style="display: none"></a>
		</Card>
	</div>
</template>

<script>
	import {
		getOtaDataPage,
    addTask,
    deleteTask,
		downloadFile,
    downloadTemplate
	} from '@/api/resoure/otaData';
	export default {
		data() {
      // 自定义验证 判断上传文件 fileList 的长度, 这样就和普通输入框表现一致了
      const validateUpload = (rule, value, callback) => {
      	if (this.uploadList && this.uploadList.length === 0) {
      		callback(new Error("请上传文件"))
      	} else {
      		callback()
      	}
      }
			return {
				total: 0,
				currentPage: 1,
				page: 1,
				searchloading: false,
				loading: false,
        uploadLoading: false,
        downloadFileLoading: false,
				addTaskModel: false,
        exportModal: false,//导出弹框标识
				searchObj: {
					taskName: '', //任务名称
          taskType: '', //任务类型
          status: '', //任务状态
				},
				columns: [{
						title: '任务名称',
						key: 'taskName',
						minWidth: 150,
						align: 'center',
						tooltip: true,
					},
					{
						title: '任务执行时间',
						key: 'createTime',
						minWidth: 160,
						align: 'center',
            tooltip: true
					},
          {
          	title: '任务结束时间',
          	key: 'updateTime',
          	minWidth: 160,
          	align: 'center',
            tooltip: true,
          },
					{
						title: '任务类型',
						key: 'taskType',
						minWidth: 150,
						align: 'center',
            tooltip: true,
						render: (h, params) => {
							const row = params.row;
							var text = "";
              var color = "";
							switch (row.taskType) {
								case '0':
									text = "删除OTA数据";
									color = '#ff2679';
									break;
								case '1':
									text = "新增OTA数据";
                  color = '#38aa19';
									break;
                default:
                	text = "";
                  color = '';
							}
							return h('label', {
                style: {
                  color: color
                }
              }, text)
						}
					},
					{
						title: '任务状态',
						key: 'status',
						minWidth: 150,
						align: 'center',
            tooltip: true,
						render: (h, params) => {
							const row = params.row;
							var text = "";
              var color = "";
							switch (row.status) {
								case 0:
									text = "待开始";
                  color = '#06b5ff';
									break;
								case 1:
									text = "处理中";
                  color = '#ffb70f';
									break;
								case 2:
									text = "已完成";
                  color = '#07ff8f';
									break;
                case 3:
                	text = "失败";
                  color = '#ff2167';
                	break;
                default:
                	text = "";
                  color = "";
							}
							return h('label', {
                style: {
                  color: color
                }
              }, text)
						}
					},
					{
						title: '号码总数',
						key: 'totalCount',
						minWidth: 150,
						align: 'center',
            tooltip: true,
					},
					{
						title: '成功数量',
						key: 'successCount',
						minWidth: 150,
						align: 'center',
            tooltip: true,
					},
          {
          	title: '失败数量',
          	key: 'failCount',
          	minWidth: 150,
          	align: 'center',
            tooltip: true,
          },
          {
          	title: '失败原因',
          	key: 'description',
          	minWidth: 150,
          	align: 'center',
            tooltip: true,
          },
          {
          	title: "源文件",
          	slot: "originFilePath",
          	width: 100,
          	align: "center",
            fixed: 'right',
          },
          {
          	title: "成功文件",
          	slot: "successedFilePath",
          	width: 100,
          	align: "center",
            fixed: 'right',
          },
          {
          	title: "失败文件",
          	slot: "failedFilePath",
          	width: 100,
          	align: "center",
            fixed: 'right',
          },
				],
				data: [],
        modelColumns: [{
					title: 'iccid',
					key: 'iccid',
				},],
        modelData: [{
          iccid: '',
        }],
        formobj: {
          taskName: '',
          type: '',
          file: '',
        },
        file: null,
        taskId: '',
        taskName: '',
        message: '',
        uploadUrl: '', //上传地址
        uploadList: [],
        formobjRule: {
          taskName: [{
            required: true,
            message: '请输入任务名称',
            trigger: 'blur'
          }],
          taskType: [{
            required: true,
            message: '请选择任务类型',
          }],
          file: [{
          	required: true,
          	validator: validateUpload,
          	trigger: 'change',
          }],
        }
      }
		},
		mounted() {
			this.goPageFirst(1);
		},

		methods: {
			// 列表加载
			goPageFirst(page) {
				this.loading = true
				var _this = this
				getOtaDataPage({
					pageSize: 10,
					pageNumber: page,
					taskName: this.searchObj.taskName,
					taskType: this.searchObj.taskType,
					status: this.searchObj.status,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = Number(res.count)
						this.data = res.data
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			// 搜索
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			// 加载表格数据
			goPage(page) {
				this.goPageFirst(page)
			},
			// 下载成功/失败/原始文件
			exportfile(id, type) {
        downloadFile({
        	taskId: id,
        	fileType: type,
        	userId: this.$store.state.user.userId,
        }).then((res) => {
        	this.exportModal = true
        	this.taskId = res.data.id
        	this.taskName = res.data.fileName
        }).catch()
			},
      // 新建任务
      addTask: function() {
      	this.addTaskModel = true
      },
			handleUpload() {
        this.$refs["formobj"].validate((valid) => {
        	if (valid) {
        		var formData = new FormData();
        		formData.append('taskName', this.formobj.taskName);
        		formData.append('taskType', this.formobj.taskType);
        		formData.append('file', this.file); //封面
        		this.uploadLoading = true
            let func = this.formobj.taskType == '1' ? addTask : deleteTask
        		func(formData).then((res) => {
        			if (res.code === "0000") {
        				this.$Notice.success({
        					title: "操作提醒",
        					desc: "任务已开始，请稍后查看！",
        				});
        				this.addTaskModel = false;
        				this.goPageFirst(1)
        				this.file = ''
        				this.$refs["formobj"].resetFields();
        			} else {
        				throw res
        			}
        		}).catch((err) => {
        		}).finally(() => {
              this.uploadLoading = false
        		})
        	}
        });
      },
      // 下载文件模板
      downloadFile() {
        if (this.formobj.taskType == '1') {
          this.downloadFileLoading = true
          downloadTemplate({
            "templateType": "add",
          }).then(res => {
          	const content = res.data
          	let fileName = decodeURIComponent(escape(res.headers['content-disposition'].match(/=(.*)$/)[1])) //获取到Content-Disposition;filename  并解码
          	if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
          		const link = this.$refs.downloadLink // 创建a标签
          		let url = URL.createObjectURL(content)
          		link.download = fileName
          		link.href = url
          		link.click() // 执行下载
          		URL.revokeObjectURL(url) // 释放url
          	} else { // 其他浏览器
          		navigator.msSaveBlob(content,fileName)
          	}
          }).catch(err =>
            console.error(err)
          ).finally(() => {
            this.downloadFileLoading = false
          })
        } else if (this.formobj.taskType == '0') {
          this.$refs.modelTable.exportCsv({
          	filename: "DeleteCards",
          	type:'csv',
          	columns: this.modelColumns,
          	data: this.modelData
          })
        }
      },
      Goto() {
        this.$router.push({
        	path: '/taskList',
        	query: {
        		taskId: encodeURIComponent(this.taskId),
        		fileName: encodeURIComponent(this.taskName),
        	}
        })
        this.exportModal = false
      },
      fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
      handleError(res, file) {
      	var v = this
      	setTimeout(function() {
      		v.uploading = false;
      		v.$Notice.warning({
      			title: '错误提示',
      			desc: "上传失败！"
      		});
      	}, 3000)
      },
      handleBeforeUpload(file, fileList) {
        let isValid = false; // 初始化文件有效性标志

        if (this.formobj.taskType === '1') {
          if (/^.+(\.zip)$/i.test(file.name)) {
            isValid = true;
          } else {
            this.$Notice.warning({
              title: '文件格式不正确',
              desc: '文件 ' + file.name + ' 格式不正确，请上传.zip文件。'
            });
          }
        } else if (this.formobj.taskType === '0') {
          if (/^.+(\.csv)$/i.test(file.name)) {
            isValid = true;
          } else {
            this.$Notice.warning({
              title: '文件格式不正确',
              desc: '文件 ' + file.name + ' 格式不正确，请上传.csv文件。'
            });
          }
        }

        // 只有在文件有效时才进行赋值
        if (isValid) {
          this.file = file;
          this.uploadList = fileList;
        }

        // 始终返回false以阻止自动上传
        return false;
      },
      fileUploading(event, file, fileList) {
      	this.message = '文件上传中、待进度条消失后再操作'
      },
      removeFile() {
      	this.file = ''
      },
      changeType(e) {
        this.file = null
        this.uploadList = []
        if (e == '1') {
          this.message = "填写好模板文件后，打包成zip压缩包进行上传"
        } else if (e == '0') {
          this.message = "请上传.csv文件"
        }
      },
      cancelModal() {
        this.file = null
        this.$refs["formobj"].resetFields();
        this.addTaskModel = false
        this.exportModal=false
			},
		}
	}
</script>

<style>
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

  .searchCondition {
    width: 200px;
  }

	.search_box1 {
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		margin: 0 10px;
		width: 70px;
	}

	ul li {
		list-style-type: none;
	}

	#space {
		height: 25px;
		line-height: 25px;
		font-size: 12px;
		white-space: pre-line;
		/* word-break: break-all;
		word-wrap: break-word; */
	}
</style>
