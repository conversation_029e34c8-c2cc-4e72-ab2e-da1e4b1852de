<template>
	<div>
		<Card>
			<div class="search_head">
				<div class="search_head_box">
					<span style="font-weight:bold;margin-top: 5px;">消息上报IMSI号码：</span>
					<Input placeholder="输入消息上报IMSI号码..." v-model="vimsiCondition" clearable :onkeyup="vimsiCondition = vimsiCondition.replace(/\s+/g,'')"style="width: 200px;" />
				</div>
				<div class="search_head_box">
					<span style="font-weight:bold;margin-top: 5px;">状态：</span>
					<Select v-model="status" placeholder="下拉选择状态" style="width:200px;" @on-change="getStatus" clearable>
						<Option :value="item.value" v-for="(item,index) in statuses" :key="index">{{item.label}}</Option>
					</Select>
				</div>
				<div class="search_head_box">
					<span style="font-weight:bold;margin-top: 5px;">供应商：</span>
					<Select v-model="provider" filterable placeholder="下拉选择供应商" style="width:200px;"  clearable>
						<Option :value="item.supplierId" v-for="(item,index) in providers" :key="item.id">{{item.supplierName}}</Option>
					</Select>
				</div>
				<div class="search_head_box">
					<span style="font-weight:bold;margin-top: 5px;">是否支持GTP PROXY：</span>
					<Select filterable v-model="supportGtpProxy" placeholder="下拉选择是否支持GTP PROXY" style="width:200px;" clearable>
						<Option :value="1">是</Option>
						<Option :value="2">否</Option>
					</Select>
				</div>
				<Button class="search_head_box" type="primary" icon="md-search" :loading="searchLoading" @click="search()">搜索</Button>
				<Button class="search_head_box" v-has="'add'" icon="md-add" type="success"  @click="addVimsi()">导入</Button>
				<Button class="search_head_box" v-has="'batchUpdate'" icon="md-add" type="warning"  @click="updateBatch()">批量修改</Button>
				<Button class="search_head_box" v-has="'batchDelete'" icon="md-add" type="error"  @click="deleteBatch()">批量删除</Button>
				<Button class="search_head_box" v-has="'taskView'" icon="md-add" type="info" @click="taskView()">任务查看</Button>
			</div>
			<div style="margin-top:30px">
				<Table @on-selection-change="handleRowChange" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row, index }" slot="action">
						<Button v-has="'update'" v-if="row.status == 1 || row.status == 5" type="success" size="small" style="margin-right: 10px"  @click="update(row)">修改</Button>
						<Button v-has="'delete'" v-if="row.status == 1 || row.status == 2 || row.status == 5" type="error" size="small" :loading="delteLoading" @click="deleteItem(row)">删除</Button>
					</template>
				</Table>
			</div>
			<div class="table-botton" style="margin-top:15px">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" style="margin: 10px 0;" />
			</div>
		</Card>
		<Modal v-model="modal1" title="导入消息上报IMSI" :mask-closable="false" @on-cancel="cancelModal" width="620px">
			<Tabs type="card" value="name1" @on-click="choseTab">
				<TabPane label="单个填写" name="name1">
					<div class="search_head" style="margin: 50px 0px 40px 0px;">
						<Form ref="formValidate1" :model="formValidate1" :rules="ruleValidate1" :label-width="170"
						 inline style="font-weight:bold;">
							<FormItem label="消息上报IMSI起始号码" prop="imsiStart">
								<Input placeholder="消息上报IMSI起始号码..." v-model="formValidate1.imsiStart" clearable style="width: 300px" />&nbsp;&nbsp;
							</FormItem>
							<FormItem label="消息上报IMSI结束号码" prop="imsiEnd">
								<Input placeholder="消息上报IMSI结束号码..." v-model="formValidate1.imsiEnd" clearable style="width: 300px" />&nbsp;&nbsp;
							</FormItem>
							<FormItem label="MSISDN起始号码" prop="phonenumStart">
								<Input placeholder="MSISDN起始号码..." v-model="formValidate1.phonenumStart" clearable style="width: 300px" />&nbsp;&nbsp;
							</FormItem>
							<FormItem label="MSISDN结束号码" prop="phonenumEnd">
								<Input placeholder="MSISDN结束号码..." v-model="formValidate1.phonenumEnd" clearable style="width: 300px" />&nbsp;&nbsp;
							</FormItem>
							<FormItem label="制卡 IMSI起始号码" >
								<Input placeholder="制卡 IMSI起始号码..." v-model="formValidate1.mappingimsiStart" clearable style="width: 300px" />&nbsp;&nbsp;
							</FormItem>
							<FormItem label="制卡 IMSI结束号码" >
								<Input placeholder="制卡 IMSI结束号码..." v-model="formValidate1.mappingimsiEnd" clearable style="width: 300px" />&nbsp;&nbsp;
							</FormItem>
							<FormItem label="供应商" prop="supplierId" >
								<Select v-model="formValidate1.supplierId" placement="top" filterable placeholder="下拉选择供应商" style="width:300px;" @on-change="getProviders" clearable>
									<Option :value="item.supplierId" v-for="(item,index) in providers" :key="item.id">{{item.supplierName}}</Option>
								</Select>
							</FormItem>
							<FormItem label="是否支持GTP Proxy" prop="supportGtpProxy" >
								<Select filterable v-model="formValidate1.supportGtpProxy" placeholder="下拉选择是否支持GTP PROXY" style="width:300px;" clearable>
									<Option :value="1">是</Option>
									<Option :value="2">否</Option>
								</Select>
							</FormItem>
						</Form>
					</div>
				</TabPane>
				<TabPane label="批量导入" name="name2">
					<div class="search_head" style="margin: 50px 0 0 0;">
						<Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="135" :label-height="100"
						 inline style="font-weight:bold;">
							<FormItem label="文件"  style="width:510px">
								<Upload multiple type="drag" action="//jsonplaceholder.typicode.com/posts/"
								:action="uploadUrl" :on-success="fileSuccess" :on-error="handleError" :before-upload="handleBeforeUpload"
								:on-progress="fileUploading"
								>
									<div style="padding: 20px 0">
										<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
										<p>点击或拖拽文件上传</p>
									</div>
								</Upload>
				                <ul class="ivu-upload-list" v-if="formValidate.file" style="width: 100%;">
				                	<li class="ivu-upload-list-file ivu-upload-list-file-finish">
				                		<span>
				                			<Icon type="ios-folder" />  {{formValidate.file.name}}</span>
				                		<i class="ivu-icon ivu-icon-ios-close ivu-upload-list-remove" style="" @click="removeFile"></i>
				                	</li>
				                </ul>
								<div style="width: 100%;">
									<Button type="primary" :loading="downloading" icon="ios-download" @click="downloadFile">下载模板文件</Button>
									<Alert type="warning" style="float:right;padding: 8px 10px 8px 10px;">{{message}}</Alert>
									<a ref="downloadLink" style="display: none"></a>
								</div>
							</FormItem>
							<!-- <FormItem label="供应商" prop="provider" >
							    <Select filterable v-model="formValidate1.provider" placeholder="下拉选择供应商" style="width:300px" @on-change="getProviders" clearable>
							        <Option :value="item.supplierId" v-for="(item,index) in providers"  :key="index">{{item.supplierName}}</Option>
							    </Select>
							</FormItem> -->
							<FormItem label="供应商" prop="supplierId" >
								<Select v-model="formValidate.supplierId" filterable placeholder="下拉选择供应商" style="width:300px;" @on-change="getProviders" clearable>
									<Option :value="item.supplierId" v-for="(item,index) in providers" :key="index">{{item.supplierName}}</Option>
								</Select>
							</FormItem>
							<FormItem label="是否支持GTP Proxy" prop="supportGtpProxy" >
								<Select filterable v-model="formValidate.supportGtpProxy" placeholder="下拉选择是否支持GTP PROXY" style="width:300px;" clearable>
									<Option :value="1">是</Option>
									<Option :value="2">否</Option>
								</Select>
							</FormItem>
						</Form>
					</div>
				</TabPane>
			</Tabs>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" :loading="addLoading" @click="handleUpload">确定</Button>
			</div>
		</Modal>
		<Modal v-model="modal2" title="修改消息上报IMSI" :mask-closable="false" @on-cancel="cancelModal">
			<div class="search_head">
				<Form ref="formValidate2" :model="formValidate2" :rules="ruleValidate2" :label-width="120" :label-height="100" inline style="font-weight:bold;">
				  <FormItem label="消息上报IMSI号码 :" >
				      <span>{{vimsiChoosed}}</span>&nbsp;&nbsp;
				  </FormItem>
				  <FormItem label="状态" prop="status" >
				      <Select v-if="modstatus == 1" v-model="formValidate2.status" placeholder="下拉选择状态" style="width:300px" clearable>
				          <Option :value="item.value" v-for="(item,index) in updateStatuses1"  :key="index">{{item.label}}</Option>
				      </Select>
				      <Select v-else v-model="formValidate2.status" placeholder="下拉选择状态" style="width:300px" clearable>
				          <Option :value="item.value" v-for="(item,index) in updateStatuses2"  :key="index">{{item.label}}</Option>
				      </Select>
				  </FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary"  :loading="updateLoading" @click="ok()">确定</Button>
			</div>
		</Modal>
		<Modal v-model="modal3" title="批量修改消息上报IMSI" :mask-closable="false" @on-cancel="cancelModal">
			<div class="search_head">
				<Form ref="formValidate3" :model="formValidate3" :rules="ruleValidate3" :label-width="150" :label-height="100"
				 inline style="font-weight:bold;">
					<FormItem label="消息上报IMSI起始号码" prop="phonenumStart">
						<Input placeholder="消息上报IMSI起始号码..." v-model="formValidate3.phonenumStart" clearable style="width: 300px" />&nbsp;&nbsp;
					</FormItem>
					<FormItem label="消息上报IMSI结束号码" prop="phonenumEnd">
						<Input placeholder="消息上报IMSI结束号码..." v-model="formValidate3.phonenumEnd" clearable style="width: 300px" />&nbsp;&nbsp;
					</FormItem>
					<FormItem label="状态"  prop="status">
						<Select v-model="formValidate3.status"  placeholder="下拉选择状态" style="width:300px" clearable>
							<Option :value="item.value" v-for="(item,index) in updateStatuses1" :key="item.value">{{item.label}}</Option>
						</Select>
					</FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary"  :loading="updateBatchLoading" @click="updateOk">确定</Button>
			</div>
		</Modal>
		<Modal v-model="modal4" title="批量删除消息上报IMSI" :mask-closable="false" @on-cancel="cancelModal">
			<div class="search_head">
				<Form ref="formValidate4" :model="formValidate4" :rules="ruleValidate4" :label-width="150" :label-height="100"
				 inline style="font-weight:bold;">
					<FormItem label="消息上报IMSI起始号码" prop="phonenumStart">
						<Input placeholder="消息上报IMSI起始号码..." v-model="formValidate4.phonenumStart" clearable style="width: 300px" />&nbsp;&nbsp;
					</FormItem>
					<FormItem label="消息上报IMSI结束号码" prop="phonenumEnd">
						<Input placeholder="消息上报IMSI结束号码..." v-model="formValidate4.phonenumEnd" clearable style="width: 300px" />&nbsp;&nbsp;
					</FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">取消</Button>
				<Button type="primary" :loading="delBatchLoading"  @click="deleteOk">确定</Button>
			</div>
		</Modal>
		<Modal v-model="taskViewFlag" title="任务查看" :mask-closable="false" :footer-hide="true" width="1200px"
			:loading="recordLoading">
			<Table :columns="taskColumns" :data="taskData" :ellipsis="true" :loading="taskloading">
				<template slot-scope="{ row, index }" slot="originFileUrl">
					<Button v-has="'download'" v-if=" row.taskStatus === '1'  ||  row.originFileUrl === null " disabled type="success"
						@click="exportfile(row,1)">点击下载</Button>
					<Button v-has="'download'" v-else type="info" @click="exportfile(row,1)">点击下载</Button>
				</template>
				<template slot-scope="{ row, index }" slot="successFileUrl">
					<Button v-has="'download'" v-if="row.taskStatus === '1' || row.successFileUrl === null" disabled type="success"
						@click="exportfile(row,3)">点击下载</Button>
					<Button v-has="'download'" v-else type="success" @click="exportfile(row,3)">点击下载</Button>
				</template>
				<template slot-scope="{ row, index }" slot="failFileUrl">
					<Button v-has="'download'" v-if="row.taskStatus === '1' || row.failFileUrl === null" disabled type="error"
						@click="exportfile(row,2)">点击下载</Button>
					<Button v-has="'download'" v-else type="error" @click="exportfile(row,2)">点击下载</Button>
				</template>
			</Table>
			<!-- 分页 -->
			<Page :total="recordTotal" :current.sync="currentRecordPage" show-total show-elevator
				@on-change="goRecordPage" style="margin: 15px 0;" />
		</Modal>
		<a ref="downloadLink" style="display: none"></a>
	</div>
</template>

<script>
	import {
		Pagesearch,
		batchUp,
		fileUp,
		updatebatch,
		deletebatch,
		updatesingle,
		deletesingle,
		supplierList,
		download,
		getRecordPage,
		exportFile

	} from '@/api/tel/supplier'
	export default {

		components: {},
		data() {
			// 校验是否为纯数字
			const checkNumber = (rule, value, callback) => {
				let reg = /^[0-9]\d*$/;
				if (reg.test(value)) {
					// if (value.length > 32) {
					// 	callback(new Error("参数长度超长"));
					// } else {
						callback();
					// }
				} else {
					callback(new Error("非纯数字格式"));
				}
			};
			// 校验三元组号码个数
			const checkLength1= (rule, value, callback) => {
				// 消息上报IMSI号码
				let imsiStart=parseInt(this.formValidate1.imsiStart)
				let imsiEnd=parseInt(this.formValidate1.imsiEnd)
				let imsilength=imsiEnd-imsiStart
				// MSISDN号码
				let phonenumStart=parseInt(this.formValidate1.phonenumStart)
				let phonenumEnd=parseInt(this.formValidate1.phonenumEnd)
				let msisdlength=phonenumEnd-phonenumStart
				// 制卡 IMSI号码
				let mappingimsiStart=parseInt(this.formValidate1.mappingimsiStart)
				let mappingimsiEnd=parseInt(this.formValidate1.mappingimsiEnd)
				let mappinglength=mappingimsiEnd-mappingimsiStart

				if(this.formValidate1.imsiStart=="" && this.formValidate1.mappingimsiStart==""){
					callback();
				}else if(msisdlength-imsilength===0 && this.formValidate1.imsiStart==""){
					callback();
				}else {
					if(this.formValidate1.mappingimsiStart==""){
						callback(new Error("1号段区间请输入"+imsilength+"个数"));
					}else if(msisdlength-mappinglength===0){
						callback();
					}else{
						callback(new Error("2号段区间请输入"+mappinglength+"个数"));
					}

				}




			};
			// 校验三元组号码个数
			const checkLength2= (rule, value, callback) => {
				// 消息上报IMSI号码
				let imsiStart=parseInt(this.formValidate1.imsiStart)
				let imsiEnd=parseInt(this.formValidate1.imsiEnd)
				let imsilength=imsiEnd-imsiStart
				// MSISDN号码
				let phonenumStart=parseInt(this.formValidate1.phonenumStart)
				let phonenumEnd=parseInt(this.formValidate1.phonenumEnd)
				let msisdlength=phonenumEnd-phonenumStart
				// 制卡 IMSI号码
				let mappingimsiStart=parseInt(this.formValidate1.mappingimsiStart)
				let mappingimsiEnd=parseInt(this.formValidate1.mappingimsiEnd)
				let mappinglength=mappingimsiEnd-mappingimsiStart

				if(this.formValidate1.imsiStart=="" && this.formValidate1.phonenumStart==""){
					callback();
				}else if(mappinglength-imsilength===0 && this.formValidate1.imsiStart==""){
					callback();
				}else {
					if(this.formValidate1.phonenumStart==""){
						callback(new Error("1号段区间请输入"+imsilength+"个数"));
					}else if(mappinglength-msisdlength===0){
						callback();
					}else{
						callback(new Error("2号段区间请输入"+msisdlength+"个数"));
					}

				}
			};

			return {
				formValidate: {
					file: null,
					provider: '',
					supplierId:'',
					supportGtpProxy: ''
				},
				provider:'', //供应商
				supportGtpProxy: '',
				tabid: 'name1',
				ruleValidate: {
					file: [{
						required: true,
						message: '请上传文件',
						trigger: 'blur'
					}],
					supplierId: [
					  { required: true, message: '请选择供应商',trigger: 'change' }
					],
					supportGtpProxy: [
					  { type:'number',required: true, message: '请选择是否支持GTP Proxy',trigger: 'change' }
					],
				},
				formValidate1: {
					imsiStart:'',
					imsiEnd:'',
					phonenumStart:'',
					phonenumEnd:'',
					mappingimsiStart:'',
					mappingimsiEnd:'',
					supplierId:'',
					supportGtpProxy: ''
				},
				ruleValidate1: {
					imsiStart: [{
						required: true,
						message: '请输入消息上报IMSI起始号码',
						trigger: 'blur'
					},
					{
						min:0,
						max:30,
						message: '请输入30位以内的纯数字IMSI起始号码',
						trigger: 'blur'
					},
					{
					  validator: checkNumber,
					  trigger: "blur"
					}
					],
					phonenumStart: [{
						required: true,
						message: '请输入MSISDN起始号码',
						trigger: 'blur'
					},
					{
						min:0,
						max:30,
						message: '请输入30位以内的纯数字MSISDN起始号码',
						trigger: 'blur'
					},
					{
					  validator: checkNumber,
					  trigger: "blur"
					}
					],
					mappingimsiStart: [
					// 	{
					// 	required: true,
					// 	message: '请输入制卡 IMSI起始号码',
					// 	trigger: 'blur'
					// },
					{
						min:0,
						max:30,
						message: '请输入30位以内的纯数字制卡 IMSI起始号码',
						trigger: 'blur'
					},
					{
					  validator: checkNumber,
					  trigger: "blur"
					}
					],
					imsiEnd: [{
						required: true,
						message: '请输入消息上报IMSI结束号码',
						trigger: 'blur'
					},{
						min:0,
						max:30,
						message: '请输入30位以内的纯数字IMSI起始号码',
						trigger: 'blur'
					},
					{
					  validator: checkNumber,
					  trigger: "blur"
					},
					],
					phonenumEnd: [{
						required: true,
						message: '请输入MSISDN结束号码',
						trigger: 'blur'
					},{
						min:0,
						max:30,
						message: '请输入30位以内的纯数字MSISDN起始号码',
						trigger: 'blur'
					},
					{
					  validator: checkNumber,
					  trigger: "blur"
					},
					// {
					// 	validator: checkLength1,
					// 	trigger: "blur"
					// }
					],
					mappingimsiEnd: [
					// 	{
					// 	required: true,
					// 	message: '请输入制卡 IMSI结束号码',
					// 	trigger: 'blur'
					// },
					{
						min:0,
						max:30,
						message: '请输入30位以内的纯数字制卡 IMSI起始号码',
						trigger: 'blur'
					},
					{
					  validator: checkNumber,
					  trigger: "blur"
					},
					// {
					// 	validator: checkLength2,
					// 	trigger: "blur"
					// }
					],
					supplierId: [{
						required: true,
						message: '请选择供应商',
					},
					],
					supportGtpProxy: [{
						required: true,
						message: '请选择是否支持GTP Proxy',
					},
					],
				},
				formValidate2: {
				  status: ''
				},
				ruleValidate2: {
				  status: [
				    { type:'number',required: true, message: '请选择修改后状态' }
				  ]
				},
				formValidate3: {
					phonenumEnd: '',
					phonenumStart: '',
					status: null
				},
				ruleValidate3: {
					phonenumEnd: [{
						required: true,
						message: '请输入消息上报IMSI结束号码',
						trigger: 'blur'
					},{
					  validator: checkNumber,
					  trigger: "blur"
					}
					],
					phonenumStart: [{
						required: true,
						message: '请输入消息上报IMSI起始号码',
						trigger: 'blur'
					},{
					  validator: checkNumber,
					  trigger: "blur"
					}
					],
					status: [{
						required: true,
						message: '请选择修改后状态',
					}],
				},
				formValidate4: {
					phonenumStart: '',
					phonenumEnd: '',
				},
				ruleValidate4: {
					phonenumStart: [{
						required: true,
						message: '请输入消息上报IMSI起始号码',
						trigger: 'blur'
					},{
					  validator: checkNumber,
					  trigger: "blur"
					}
					],
					phonenumEnd: [{
						required: true,
						message: '请输入消息上报IMSI结束号码',
						trigger: 'blur'
					},{
					  validator: checkNumber,
					  trigger: "blur"
					}
					],
				},
				updateStatuses1: [
				  {
				    label: '待分配',
				    value: 2
				  },
				  // {
				  //   label: '留存',
				  //   value: 6
				  // }
				],
				updateStatuses2: [
				  {
				    label: '待分配',
				    value: 2
				  }
				],
				// 表头信息
				columns: [{
						title: '消息上报IMSI',
						key: 'imsi',
						align: 'center'
					},
					{
						title: 'MSISDN',
						key: 'msisdn',
						align: 'center'
					},
					{
						title: '制卡 IMSI',
						key: 'mappingImsi',
						align: 'center'
					},
					{
						title: '供应商',
						key: 'supplierName',
						align: 'center'
					},
					{
						title: '是否支持GTP PROXY',
						key: 'supportGtpProxy',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row
							const color = row.supportGtpProxy == 1 ? '#2dbe37' : row.supportGtpProxy == 2 ? '#ff9900' : ''
							const text = row.supportGtpProxy == 1 ? '是' : row.supportGtpProxy == 2 ? '否' : ''
							return h('label', {
								style: {
									color: color
								}
							}, text)
						}
					},
					{
						title: '入库时间',
						key: 'createTime',
						align: 'center'
					},
					{
						title: '当前状态',
						key: 'status',
						align: 'center',
						render: (h, params) => {
							const row = params.row

							// const color = row.status == 1 ? '#19be6b' : row.status == 2 ? '#27A1FF' : row.status ==  3 ? '#ff9900' :
       //         row.status ==  7 ? '#ff0000' : row.status == 4 ? '#00FF00' : '#d518bc'
							// const text = row.status == 1 ? '已导入' : row.status == 2 ? '待分配' : row.status == 3 ? '已分配':
       //         row.status == 6 ? '留存' : row.status == 4 ? '使用中' : row.status == 5 ?'已冻结' : ''


              const color = row.status == 1 ? '#19be6b' : row.status == 2 ? '#ff0000' : row.status == 3 ? '#27A1FF' :
               row.status == 4 ? '#ff9900': row.status == 5 ? '#d75b0f': row.status == 6 ? '#d518bc'  : '#515a6e'
              const text = row.status == 1 ? '已导入' : row.status == 2 ? '待分配' : row.status == 3 ? '已分配' :
               row.status == 4 ? '使用中' : row.status == 5 ? '已冻结' : row.status == 6 ? '留存' : '其他'


              return h('label', {
								style: {
									color: color
								}
							}, text)
						}
					},
					{
						title: '操作',
						slot: 'action',
						width: 300,
						align: 'center'
					}
				],
				providers: [],
				statuses: [{
					label: '已导入',
					value: 1
				},
				{
					label: '待分配',
					value: 2
				},
				{
					label: '已分配',
					value: 3
				},
				{
					label: '使用中',
					value: 4
				},
				{
					label: '已冻结',
					value: 5
				},
				{
					label: '留存',
					value: 6
				}
				],
				taskColumns: [{
						title: '任务创建时间',
						key: 'createTime',
						align: 'center',
						width: '150px',
					},
					{
						title: '处理状态',
						key: 'taskStatus',
						align: 'center',
						width: '100px',
						render: (h,params) => {
							const row = params.row
							var text =row.taskStatus === '1' ? "处理中" : row.taskStatus === '2' ? "已完成" : ''
							return h('label',text)
						}
					},
					{
						title: '消息上报IMSI起始号码',
						key: 'imsiStart',
						align: 'center',
						width: '160px',
						tooltip: true
					},
					{
						title: '消息上报IMSI结束号码',
						key: 'imsiEnd',
						align: 'center',
						width: '160px',
						tooltip: true
					},
					{
						title: 'MSISDN起始号码',
						key: 'msisdnStart',
						align: 'center',
						width: '150px',
						tooltip: true
					},
					{
						title: 'MSISDN结束号码',
						key: 'msisdnEnd',
						align: 'center',
						width: '150px',
						tooltip: true
					},
					{
						title: '制卡IMSI起始号码',
						key: 'mappingimsiStart',
						align: 'center',
						width: '150px',
						tooltip: true
					},
					{
						title: '制卡IMSI结束号码',
						key: 'mappingimsiEnd',
						align: 'center',
						width: '150px',
						tooltip: true
					},
					{
						title: '批量导入原始文件',
						slot: 'originFileUrl',
						align: 'center',
						width: '140px',
					},
					{
						title: '供应商',
						key: 'supplierrms',
						align: 'center',
						width: '100px',
					},
					{
						title: '导入总数量',
						key: 'importNum',
						align: 'center',
						width: '100px',
					},
					{
						title: '导入成功数量',
						key: 'successNum',
						align: 'center',
						width: '110px',
					},
					{
						title: '导入失败数量',
						key: 'failNum',
						align: 'center',
						width: '110px',
					},
					{
						title: '导入成功文件',
						slot: 'successFileUrl',
						align: 'center',
						width: '140px',
					},
					{
						title: '导入失败文件',
						slot: 'failFileUrl',
						align: 'center',
						width: '140px',
					},
				],
				taskData: [],
				statusesList:[
					{
						label: '已导入',
						value: 1
					},
					{
						label: '待分配',
						value: 2
					},
					{
						label: '留存',
						value: 6
					}
				],
				tableData: [],
				loading: false,
				addLoading: false,
				searchLoading: false,
				updateBatchLoading: false,
				delBatchLoading: false,
				updateLoading:false,
				delteLoading:false,
				recordLoading: false,
        taskloading: false,
				currentPage: 1,
				total: 0,
				currentRecordPage: 1,
				recordTotal: 0,
				vimsiCondition: '',
				vimsiChoosed: '',
				updatestatus:'',
				ids: [],
				modal1: false,
				modal2: false,
				modal3: false,
				modal4: false,
				taskViewFlag: false,
				status: '',
				downloading: false,
				message: '文件仅支持csv格式文件,大小不能超过5MB',
				uploadUrl: '',
				selection: [], //多选
				selectionIds: [], //多选ids
				modstatus:''
			}
		},
		watch: {
			'$route': 'reload'
		},
		computed: {},
		methods: {
			// 页面加载
			goPageFirst(page) {
				this.loading = true
				var _this = this
				let pageNum = page
				let pageSize = 10
				let imsi=this.vimsiCondition
				let status=this.status
				let supplierId=this.provider
				Pagesearch({
					pageNum,
					pageSize,
					imsi,
					status,
					supplierId,
					supportGtpProxy: this.supportGtpProxy
				}).then(res => {
					if (res.code === '0000') {
						_this.loading = false
						this.searchLoading = false
						this.page = page
						this.total = res.count
						this.tableData = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchLoading = false
				})

			},
			// 查询按钮，指定号码查询
			// 分页跳转
			goPage(page) {
				this.page = page
				this.goPageFirst(page)
			},
			// 勾选按钮
			handleRowChange(selection) {
				this.selection = selection
				this.ids = []
				selection.map((value, index) => {
					this.ids.push(value.fallbackId)
				})
			},
			// 选择标签
			choseTab(name) {
				this.tabid = name
			},
			//下载模板文件
			downloadFile() {
				this.exporting = true
				download().then(res => {
					const content = res.data
					const fileName = 'template.csv' // 导出文件名
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content,fileName)
					}
				}).catch(err => this.exporting = false)
			},
			removeFile() {
				this.formValidate.file = ''
			},
			handleError(res, file) {
				var v = this
				setTimeout(function() {
					v.uploading = false;
					v.$Notice.warning({
						title: '错误提示',
						desc: "上传失败！"
					});
				}, 3000);
			},
			handleBeforeUpload(file) {
				if (!/^.+(\.csv)$/.test(file.name)) {
					this.$Notice.warning({
						title: '文件格式不正确',
						desc: '文件 ' + file.name + ' 格式不正确，请上传.csv格式文件。'
					})
				} else {
					this.formValidate.file = file
				}
				return false
			},
			fileUploading(event, file, fileList) {
				this.message = '文件上传中、待进度条消失后再操作'
			},
			fileSuccess(response, file, fileList) {
				this.message = '请先下载模板文件，并按格式填写后上传'
			},
			async handleUpload() {
				if (this.tabid === 'name1') {
					// 单个填写
					this.$refs.formValidate1.validate(valid => {
						if (valid) {
							this.addLoading=true
							// 消息上报IMSI号码
							let imsiStart=parseInt(this.formValidate1.imsiStart)
							let imsiEnd=parseInt(this.formValidate1.imsiEnd)
							let imsilength=imsiEnd-imsiStart
							// MSISDN号码
							let phonenumStart=parseInt(this.formValidate1.phonenumStart)
							let phonenumEnd=parseInt(this.formValidate1.phonenumEnd)
							let msisdlength=phonenumEnd-phonenumStart
							// 制卡 IMSI号码
							let mappingimsiStart=parseInt(this.formValidate1.mappingimsiStart)
							let mappingimsiEnd=parseInt(this.formValidate1.mappingimsiEnd)
							let mappinglength=mappingimsiEnd-mappingimsiStart
							// 非必填制卡 IMSI号码
							if(this.formValidate1.mappingimsiStart===""&&this.formValidate1.mappingimsiEnd===""){
								this.modal1=false
								if(imsilength===msisdlength){
									this.formValidate1.mappingimsiStart=this.formValidate1.imsiStart
									this.formValidate1.mappingimsiEnd=this.formValidate1.imsiEnd
									batchUp(this.formValidate1).then(res => {
										if (res.code === '0000') {
											let data = res.data
											this.$Notice.success({
												title: '操作成功',
												// desc: '成功添加了' + data.count + '条数据'
												desc: '表单数据已全部导入'
											})
											this.addLoading=false
											this.cancelModal()
											this.currentPage=1
											this.goPageFirst(1)
										} else {
											throw res
										}
									}).catch((err) => {
										console.log(err)
									}).finally(() => {
										this.addLoading=false
										this.cancelModal()
									})
								}else{
									this.addLoading=false
									this.$Modal.error({
										title: '提示',
										content: '号码个数不一致，请重新填写'
									})
								}
							}else{
								if(imsilength===msisdlength && imsilength===mappinglength){
									batchUp(this.formValidate1).then(res => {
										if (res.code === '0000') {
											let data = res.data
											this.$Notice.success({
												title: '操作成功',
												// desc: '成功添加了' + data.count + '条数据'
												desc: '表单数据已全部导入'
											})
											this.addLoading=false
											this.cancelModal()
											this.currentPage=1
											this.goPageFirst(1)
										} else {
											throw res
										}
									}).catch((err) => {
										console.log(err)
									}).finally(() => {
										this.addLoading=false
										this.cancelModal()
									})
								}else{
									this.addLoading=false
									this.$Modal.error({
										title: '提示',
										content: '号码个数不一致，请重新填写'
									})
								}
							}


						}
					})

				} else {
					// 批量导入
					if (!this.formValidate.file) {
						this.$Message.warning('请选择需要上传的文件')
						return
					} else {
						this.$refs.formValidate.validate(valid => {
							if (valid) {
								let formData = new FormData()
								formData.append('file', this.formValidate.file)
								formData.append('supplierId', this.formValidate.supplierId)
								formData.append('supportGtpProxy', this.formValidate.supportGtpProxy)
								fileUp(formData).then(res => {
									if (res.code === '0000') {
										this.$Notice.success({
											title: '操作成功',
											desc: '添加成功'
										})
										this.addLoading=false
										this.cancelModal()
										this.currentPage=1
										this.goPageFirst(1)
									} else {
										throw res
									}
								}).catch((err) => {
									console.log(err)
								}).finally(() => {
									this.addLoading=false
									this.cancelModal()
								})
							}
						})

					}
				}

			},
			cancelUpload() {
				this.formValidate = {}
				this.modal1 = false
			},
			search() {
				this.searchLoading = true
				this.currentPage=1
				this.goPageFirst(1)
			},
			//多选
			handleRowChange(selection) {
				this.selection = selection;
				this.selectionIds = [];
				selection.map((value, index) => {
					this.selectionIds.push(value.id);
				});
			},
			deleteBatch() {
				this.modal4 = true
			},
			addVimsi() {
				this.modal1 = true
			},
			update(item) {
				this.vimsiChoosed = item.imsi
				this.modstatus=parseInt(item.status)
				this.modal2 = true
			},
			deleteItem(item) {
				// 只能删除状态为“已导入”的号码。
				// if(item.status==="1"){
					this.$Modal.confirm({
						title: '确认删除？',
						onOk: () => {
							let imsi=item.imsi
							let msisdn=item.msisdn
							deletesingle({
								imsi,
								msisdn
							}).then(res => {
								if (res.code === '0000') {
									this.$Notice.success({
										title: '操作提示',
										desc: '删除成功'
									})
									this.cancelModal()
									this.currentPage=1
									this.goPageFirst(1)
								} else {
									throw res
								}
							}).catch((err) => {
								console.log(err)
							}).finally(() => {
								this.uploading = false
								this.cancelModal()
							})

						}
					});
				// }else{
				// 	this.$Modal.error({
				// 		title: "提示",
				// 		content: "只能删除状态为“已导入”的号码。"
				// 	});
				// }

			},
			updateBatch() {
				this.updateBatchLoading=false
				this.modal3 = true
			},
			getCbMore(id) {
				this.$router.push({
					path: '/corp/addcb',
					query: {
						cbId: id
					}
				})
			},
			getProviders() {

			},
			getStatus() {

			},
			getStatus3(status) {
			  this.formValidate3.status=status
			},

			// 单个修改
			ok() {
				this.$refs['formValidate2'].validate((valid) => {
				  if (valid) {
						this.updateLoading=true
						let imsi=this.vimsiChoosed
						let status=this.formValidate2.status
						updatesingle({
							imsi,
							status
						}).then(res => {
							if (res.code === '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '修改成功'
								})
								this.updateLoading=false
								this.cancelModal()
								this.currentPage=1
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.updateLoading=false
							this.cancelModal()
						})
					}
				})
			},
			// 批量修改
			updateOk() {
				this.$refs.formValidate3.validate(valid => {
					if (valid) {
						this.updateBatchLoading=true
						updatebatch(this.formValidate3).then(res => {
							if (res.code === '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '修改成功'
								})
								this.updateBatchLoading=false
								this.cancelModal()
								this.currentPage=1
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.updateBatchLoading=false
							this.cancelModal()
						})
					}
				})
			},
			// 批量删除
			deleteOk() {
				this.$refs.formValidate4.validate(valid => {
					if (valid) {
						this.delBatchLoading=true
						deletebatch(this.formValidate4).then(res => {
							if (res.code === '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.delBatchLoading=false
								this.cancelModal()
								this.currentPage=1
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {
							this.delBatchLoading=false
							this.cancelModal()
						})
					}
				})
			},
			cancelModal() {
				this.modal1 = false
				this.modal2 = false
				this.modal3 = false
				this.modal4 = false
				this.addLoading=false
				this.updateBatchLoading=false
				this.delBatchLoading=false
				this.updateLoading=false
				this.$refs.formValidate.resetFields()
				this.$refs.formValidate1.resetFields()
				this.$refs.formValidate2.resetFields()
				this.$refs.formValidate3.resetFields()
				this.$refs.formValidate4.resetFields()
				this.formValidate.supplierId=''
				this.formValidate1.supplierId=''
				this.formValidate1.mappingimsiStart=''
				this.formValidate1.mappingimsiEnd=''
				this.formValidate3.status=''
				this.formValidate.supportGtpProxy=''
				this.formValidate1.supportGtpProxy=''
				this.formValidate.file=null
			},
			// 获取供应商
			supplierList(){
				supplierList().then(res => {
					if (res.code === '0000') {
						this.providers=res.data
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {

				})
			},
			taskView() {
				this.taskViewFlag = true
				this.goRecodePageFirst(1)
			},
			// 导入记录查看列表
			goRecodePageFirst: function(page) {
				this.loading = true
				var _this = this
				getRecordPage({
					pageSize: 10,
					pageNo: page,
					type: 3
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.recordLoading = false
						var data = res.data
						this.currentRecordPage = page
						this.recordTotal = data.total
						this.taskData = data.records
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					_this.loading = false
					this.recordLoading = false
				})
			},
			goRecordPage(page) {
				this.goRecodePageFirst(page)
			},
			// 导入记录查看 文件下载
			exportfile:function(row, type) {
				this.taskloading = true
				var _this = this
				exportFile({
					id: row.id,
					type: type
				}).then(res => {
					const content = res.data
					let fileName = decodeURI(res.headers['content-disposition'].match(/=(.*)$/)[
						1]) //获取到Content-Disposition;filename  并解码
					if ('download' in document.createElement('a')) { // 支持a标签download的浏览器
						const link = this.$refs.downloadLink // 创建a标签
						let url = URL.createObjectURL(content)
						link.download = fileName
						link.href = url
						link.click() // 执行下载
						URL.revokeObjectURL(url) // 释放url
					} else { // 其他浏览器
						navigator.msSaveBlob(content, fileName)
					}
				}).finally(() => {
					this.taskloading = false
				})
			},
		},
		mounted() {
			this.goPageFirst(1)
			// 获取供应商
			this.supplierList()
		}
	}
</script>
<style>
	.search_head {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 20px;
		flex-wrap: wrap;
	}

	.search_head_label {
		margin-top: 20px;
		font-size: 17px;
	}
	
	.search_head_box {
		display: flex;
		justify-content: flex-start;
		margin: 20px 30px 0px 0;
	}
</style>
