<template>
  <Card>
    <!-- 搜索区域 -->
    <div class="search_head_i">
      <div class="search_box">
        <span class="search_box_label">渠道商简称</span>&nbsp;&nbsp;
        <Select v-model="searchForm.corpId" filterable clearable style="width: 200px" placeholder="请选择渠道商简称">
          <Option v-for="item in corpListFilter" :key="item.corpId" :value="item.corpId">{{ item.corpName }}</Option>
        </Select>
      </div>
      <div class="search_box">
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button v-has="'search'" type="primary" icon="md-search" :loading="searchLoading" @click="handleSearch">搜索</Button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button v-has="'add'" style="margin: 0 20px" type="info" icon="md-add" @click="handleAdd">新增</Button>
      </div>
    </div>

    <!-- 表格区域 -->
    <Table :columns="columns" :data="tableData" :loading="loading">
      <template slot-scope="{ row }" slot="action">
        <Button v-has="'edit'" type="success" size="small" ghost @click="handleEdit(row)">编辑</Button>
        <Button v-has="'delete'" :disabled="row.corpId == '-9999'" style="margin-left: 20px;" type="error" size="small" ghost @click="handleDelete(row)">删除</Button>
      </template>
    </Table>

    <!-- 分页 -->
    <div style="margin-top: 15px">
      <Page
        :total="pagination.total"
        :current="pagination.current"
        :page-size="pagination.pageSize"
        show-sizer
        show-total
        show-elevator
        @on-change="handlePageChange"
        @on-page-size-change="handlePageSizeChange"
      />
    </div>

    <!-- 新增/编辑弹窗 -->
    <Modal
      v-model="modalVisible"
      :title="modalTitle"
      @on-cancel="handleModalCancel"
      :mask-closable="false"
      width="800"
    >
      <Form ref="formRef" :model="formData" :rules="ruleValidate" :label-width="120" style="margin-bottom: 30px;">
        <!-- 新增时的表单 -->
        <template v-if="!formData.id">
          <!-- 默认地址配置 -->
          <Row type="flex" align="middle">
            <Col span="11">
              <FormItem label="渠道商默认地址"></FormItem>
            </Col>
            <Col span="13">
              <FormItem prop="defaultAddress" :label-width="128">
                <RadioGroup v-model="formData.defaultAddress" class="radio-group">
                  <Radio label='1'>ARCH</Radio>
                  <Radio label='2'>GTP Proxy</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
          </Row>
          <Divider />
          <!-- 动态地址配置 -->
          <div v-for="(item, index) in formData.addresses" :key="index">
            <Row type="flex" align="middle">
              <Col span="12">
                <FormItem label="渠道商简称" :prop="'addresses.' + index + '.corpId'" :rules="{ required: true, message: '请选择渠道商简称', trigger: 'change' }">
                  <Select v-model="item.corpId" filterable clearable placeholder="请选择渠道商简称" style="width: 200px">
                    <Option v-for="corp in availableCorps" :key="corp.corpId" :value="corp.corpId"
                     :disabled="isCorpSelected(corp.corpId, index)">{{ corp.corpName }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="10">
                <div class="radio-address-wrapper">
                  <FormItem :prop="'addresses.' + index + '.address'" :rules="{ required: true, message: '请选择GTP话单地址', trigger: 'change' }">
                    <RadioGroup v-model="item.address" class="radio-group">
                      <Radio label='1'>ARCH</Radio>
                      <Radio label='2'>GTP Proxy</Radio>
                    </RadioGroup>
                  </FormItem>
                </div>
              </Col>
              <Col span="2">
                <FormItem :label-width="0">
                  <Button type="error" ghost shape="circle" size="small" @click="removeAddress(index)" class="delete-btn" icon="md-remove"></Button>
                </FormItem>
              </Col>
            </Row>
          </div>
          <div class="add-btn-wrapper">
            <Button type="success" ghost shape="circle" size="small" @click="addAddress" icon="md-add"></Button>
          </div>
        </template>

        <!-- 编辑时的表单 -->
        <template v-else>
          <template v-if="isDefaultAddress">
            <Row type="flex" align="middle">
              <Col span="12">
                <FormItem label="渠道商默认地址">
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem prop="defaultAddress" :label-width="0" :rules="{ required: true, message: '请选择GTP话单地址', trigger: 'change' }">
                  <RadioGroup v-model="formData.defaultAddress" class="radio-group">
                    <Radio label='1'>ARCH</Radio>
                    <Radio label='2'>GTP Proxy</Radio>
                  </RadioGroup>
                </FormItem>
              </Col>
            </Row>
          </template>
          <template v-else>
            <Row type="flex" align="middle">
              <Col span="12">
                <FormItem label="渠道商简称" prop="corpId" :rules="{ required: true, message: '请选择渠道商简称', trigger: 'change' }">
                  <Select v-model="formData.corpId" placeholder="请选择渠道商简称" style="width: 200px" disabled>
                    <Option v-for="corp in corpList" :key="corp.corpId" :value="corp.corpId">{{ corp.corpName }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem prop="address" :rules="{ required: true, message: '请选择GTP话单地址', trigger: 'change' }" class="no-label-form-item">
                  <RadioGroup v-model="formData.address" class="radio-group">
                    <Radio label='1'>ARCH</Radio>
                    <Radio label='2'>GTP Proxy</Radio>
                  </RadioGroup>
                </FormItem>
              </Col>
            </Row>
          </template>
        </template>
      </Form>
      <div slot="footer" style="text-align: center;">
        <Button
          type="primary"
          :loading="submitLoading"
          @click="handleCustomOk"
          icon="md-checkmark"
        >确定</Button>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <Button @click="handleModalCancel" icon="ios-arrow-back">返回</Button>
      </div>
    </Modal>
  </Card>
</template>

<script>
import {
  getCorpList,
  getGtpConfigList,
  addGtpConfig,
  updateGtpConfig,
  deleteGtpConfig
} from '@/api/gtpConfig'

export default {
  name: 'GTPAddressConfig',
  data() {
    const validateAddress = (rule, value, callback) => {
      if (!this.formData.id && this.formData.addresses.length < 1) {
        callback(new Error('请至少配置一个渠道商地址'))
      } else {
        callback()
      }
    }

    return {
      // 搜索相关
      searchForm: {
        corpId: ''
      },
      searchLoading: false,
      // 表格相关
      loading: false,
      columns: [
        {
          title: '渠道商简称',
          key: 'corpName',
          minWidth: 150,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
          	const row = params.row
          	var text = row.corpId == "-9999" ? '默认' : row.corpName
          	return h('label', text);
          }
        },
        {
          title: '话单地址',
          key: 'address',
          minWidth: 150,
          tooltip: true,
          align: 'center',
          render: (h, params) => {
          	const row = params.row
            var color = row.address == 1 ? '#2b85e4' : row.address == 2 ? '#e4aa21' : ''
          	var text = row.address == 1 ? 'ARCH' : row.address == 2 ? 'GTP Proxy' : ''
          	return h('label', {
          		style: {
          			color: color
          		}
          	}, text);
          }
        },
        {
          title: '操作',
          slot: 'action',
          minWidth: 200,
          align: 'center'
        }
      ],
      tableData: [],
      // 分页相关
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },

      corpList: [], // 新增/编辑-渠道商列表
      corpListFilter: [], //首页渠道商列表
      // 弹窗相关
      modalVisible: false,
      modalTitle: 'GTP话单地址配置新增',
      isDefaultAddress: false,
      submitLoading: false, // 控制按钮加载状态
      formData: {
        id: "",
        defaultAddress: '',
        corpId: "",
        address: '',
        addresses: [
          {
            corpId: "",
            address: ''
          }
        ]
      },
      ruleValidate: {
        defaultAddress: [
          { required: true, message: '请选择默认地址', trigger: 'change' }
        ],
        corpId: [
          { required: true, message: '请选择渠道商简称', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请选择GTP话单地址', trigger: 'change' }
        ],
        addresses: [
          { required: true, type: 'array', min: 1, message: '请至少添加一个渠道商地址' }
        ]
      }
    }
  },

  computed: {
    availableCorps() {
      return this.corpList
    },
    canAddMore() {
      const selectedCorps = new Set(
        this.formData.addresses.map(item => item.corpId).filter(Boolean)
      )
      return this.corpList.length > selectedCorps.size
    }
  },

  mounted() {
    this.loadCorpList()
    this.loadTableData()
    this.loadFilterCorpList()
  },

  methods: {
    // 首页加载渠道商列表
    loadFilterCorpList() {
      getGtpConfigList({
        pageSize: -1,
        pageNum: -1
      }).then(res => {
    		if (res.code === "0000") {
          if (res.data[0].corpId == "-9999") {
            res.data[0].corpName = '默认'
          }
          this.corpListFilter = res.data
    		}
      })
    },

    // 新增\编辑 加载渠道商列表
    async loadCorpList() {
      try {
        const res = await getCorpList()
        if (res.code === '0000') {
          this.corpList = res.data
        }
      } catch (error) {
      }
    },

    // 加载表格数据
    async loadTableData() {
      this.loading = true
      try {
        const res = await getGtpConfigList({
          corpId: this.searchForm.corpId,
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.current
        })
        if (res.code === '0000') {
          this.tableData = res.data
          this.pagination.total = Number(res.count)
        }
      } catch (error) {
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadTableData()
    },

    // 分页
    handlePageChange(page) {
      this.pagination.current = page
      this.loadTableData()
    },

    handlePageSizeChange(pageSize) {
      this.pagination.pageSize = pageSize
      this.pagination.current = 1
      this.loadTableData()
    },

    // 新增
    handleAdd() {
      this.resetForm()
      this.modalTitle = 'GTP话单地址配置新增'
      this.modalVisible = true
      if (this.tableData[0].address) {
        this.formData.defaultAddress = this.tableData[0].address.toString()
      }
    },

    // 编辑
    handleEdit(row) {
      this.resetForm()
      this.modalTitle = 'GTP话单地址配置修改'
      if (row.corpId === '-9999') {
        this.isDefaultAddress = true
        this.formData.id = row.id
        this.formData.defaultAddress = row.address.toString()
      } else {
        this.formData.id = row.id
        this.formData.corpId = row.corpId
        this.formData.address = row.address.toString()
      }
      this.modalVisible = true
    },

    // 删除
    handleDelete(row) {
      this.$Modal.confirm({
        title: '确认删除？',
        onOk: async () => {
          try {
            const res = await deleteGtpConfig(row.id)
            if (res.code === '0000') {
              this.$Message.success('删除成功')
              this.loadTableData()
              this.loadFilterCorpList()
            }
          } catch (error) {
          }
        }
      })
    },

    // 表单提交
    async handleCustomOk() {
      // 1. 触发表单验证
      const valid = await this.$refs.formRef.validate();
      if (!valid) {
        return; // 验证失败，不关闭
      }

      // 2. 提交数据
      this.submitLoading = true; // 开启加载状态
      try {
        let submitData;
        if (!this.formData.id) {
          // 新增逻辑
          submitData = [
            { corpId: '-9999', address: this.formData.defaultAddress },
            ...this.formData.addresses.map(item => ({
              corpId: item.corpId,
              address: item.address
            }))
          ];
          await addGtpConfig(submitData);
        } else {
          // 编辑逻辑
          submitData = {
            id: this.formData.id,
            corpId: this.isDefaultAddress ? '-9999' : this.formData.corpId,
            address: this.isDefaultAddress ? this.formData.defaultAddress : this.formData.address
          };
          await updateGtpConfig(submitData);
        }

        // 3. 提交成功后的操作
        this.$Message.success("操作成功");
        this.modalVisible = false; // 手动关闭模态框
        this.loadTableData();
        this.loadFilterCorpList()
      } catch (error) {
      } finally {
        this.submitLoading = false; // 关闭加载状态
      }
    },

    // 取消表单
    handleModalCancel() {
      this.modalVisible = false
      this.resetForm()
    },

    // 重置表单
    resetForm() {
      this.formData = {
        id: '',
        defaultAddress: '',
        corpId: '',
        address: '',
        addresses: [
          {
            corpId: '',
            address: ''
          }
        ]
      }
      this.isDefaultAddress = false
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields()
      }
    },

    // 添加地址
    addAddress() {
      if (!this.canAddMore) {
        this.$Message.warning('渠道商简称已全部用尽，无剩余可选')
        return
      }
      this.formData.addresses.push({
        corpId: "",
        address: ''
      })
    },

    // 移除地址
    removeAddress(index) {
      if (this.formData.addresses.length <= 1) {
        this.$Message.warning('至少保留一组渠道商简称地址信息')
        return
      }
      this.formData.addresses.splice(index, 1)
    },

    // 判断渠道商是否已选择
    isCorpSelected(corpId, currentIndex) {
      return this.formData.addresses.some(
        (item, index) => index !== currentIndex && item.corpId === corpId
      )
    }
  }
}
</script>

<style lang="less" scoped>
.search_head_i {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
}

.search_box {
  width: 300px;
  padding: 0 5px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: row;
  margin-bottom: 40px;
}

.search_box_label {
  font-weight: bold;
  text-align: center;
  width: 85px;
}

/deep/ .ivu-form {
  .ivu-form-item {
    margin-bottom: 24px;

    .ivu-radio-group {
      display: flex;
      align-items: center;
      height: 32px;  // 统一高度
    }

    .ivu-radio-wrapper {
      margin-right: 24px;  // 单选框之间的间距
      height: 32px;  // 统一高度
      line-height: 32px;  // 文字垂直居中
    }
  }
}

/deep/ .ivu-divider {
  margin: 24px 0;
  background: #e8e8e8;
}

.radio-group-wrapper {
  display: flex;
  align-items: center;
  height: 32px;  // 统一高度

  .ivu-radio-group {
    margin-right: 16px;
  }

  .delete-btn {
    padding: 0 4px;
  }
}

.address-item {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  .ivu-row {
    display: flex;
    align-items: center;
  }
}

.add-btn-wrapper {
  margin-top: 24px;
  padding-right: 40px;
  text-align: right;
}

.radio-group {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
}

.radio-address-wrapper {
  display: flex;
  align-items: center;
  margin-left: -24px;

  .radio-group {
    margin-right: 16px;
  }
}

.no-label-form-item {
  /deep/ .ivu-form-item-label {
    display: none;
  }

  /deep/ .ivu-form-item-content {
    margin-left: 0 !important;
  }
}

/deep/ .ivu-radio-group {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
}

/deep/ .ivu-form-item {
  margin-bottom: 24px;

  &.no-label-form-item {
    margin-top: 5px;  // 微调对齐
  }
}
</style>
