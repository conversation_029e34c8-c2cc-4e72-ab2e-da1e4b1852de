<template>
  <Card style="width: 100%; padding: 16px">
    <!-- 1. 查询条件区域 -->
    <Form ref="searchForm" :model="filters" :label-width="100">
      <Row :gutter="16">
        <Col span="6"> <!-- 第一行，span=8 -->
        <FormItem label="规则名称" prop="ruleName">
          <Input v-model="filters.ruleName" placeholder="请输入规则名称" clearable style="width: 100%" />
        </FormItem>
        </Col>
        <Col span="6"> <!-- 第一行，span=8 -->
        <FormItem label="故障资源供应商" prop="faultSupplier">
          <Select v-model="filters.faultSupplier" placeholder="请选择" clearable filterable style="width: 100%">
            <Option v-for="supplier in supplierList" :value="supplier.supplierId" :key="supplier.supplierId">{{
              supplier.supplierName }}</Option>
          </Select>
        </FormItem>
        </Col>
        <Col span="6"> <!-- 第一行，span=8 -->
        <FormItem label="目标国家" prop="targetCountries">
          <Select v-model="filters.targetCountries" multiple filterable placeholder="请选择" clearable
            style="width: 100%">
            <Option v-for="country in countryList" :value="country.mcc" :key="country.mcc">{{country.countryEn}}</Option>
          </Select>
        </FormItem>
        </Col>
      </Row>
      <Row :gutter="16">
        <Col span="6"> <!-- 第二行，span=6 -->
        <FormItem label="备用资源供应商" prop="backupSupplier">
          <Select v-model="filters.backupSupplier" placeholder="请选择" clearable filterable style="width: 100%">
            <Option v-for="supplier in supplierList" :value="supplier.supplierId" :key="supplier.supplierId">{{
              supplier.supplierName }}</Option>
          </Select>
        </FormItem>
        </Col>
        <Col span="6"> <!-- 第二行，span=6 -->
        <FormItem label="备用卡池" prop="backupPool">
          <Input v-model="filters.backupPool" placeholder="请输入备用卡池名称" clearable style="width: 100%" />
        </FormItem>
        </Col>
        <Col span="6"> <!-- 第二行，span=6 -->
        <FormItem label="故障状态" prop="status">
          <Select v-model="filters.status" placeholder="请选择" clearable style="width: 100%">
            <Option value="0">故障中</Option>
            <Option value="1">已恢复</Option>
          </Select>
        </FormItem>
        </Col>
        <Col span="6"> <!-- 第二行，按钮 span=6, 调整对齐和上边距 -->
        <FormItem :label-width="10">
          <Button type="primary" @click="handleSearch" v-has="'search'" icon="ios-search">查询</Button>
          <Button type="info" @click="handleNew" v-has="'add'" icon="ios-add" style="margin-left: 8px">新建</Button>
        </FormItem>
        </Col>
      </Row>
    </Form>

    <!-- 2. 表格区域 -->
    <Table :columns="columns" :data="tableData" :loading="loading" :ellipsis="true" :tooltip="true"
      style="margin-top: 16px;">
      <template slot-scope="{ row }" slot="backupSuppliers">
        <!-- 分拆显示：备用供应商 -->
        <div v-for="(supplier, index) in row.standbySupplierNames" :key="'sup_' + index"
          style="line-height: 1.8; min-height: 24px; /* 保持最小行高对齐 */"> <!-- 增加 key 前缀避免潜在冲突 -->
          <span>{{ supplier }}</span>
        </div>
      </template>
      <template slot-scope="{ row }" slot="backupPools">
        <!-- 分拆显示：备用卡池 -->
        <div v-for="(pool, index) in row.cardPools" :key="'pool_' + index" style="line-height: 1.8; min-height: 24px;">
          <a @click="showPoolDetails(pool)">
            {{ pool.poolName }}<span v-if="pool.rate !== undefined&&row.cardPools.length>1">（{{ pool.rate }}%）</span>
          </a>
        </div>
      </template>
      <template slot-scope="{ row }" slot="status">
        <span>{{ row.faultStatus === '0' ? '故障中' : '已恢复' }}</span>
      </template>
      <template slot-scope="{ row }" slot="action">
        <Button type="success" v-has="'restore'" size="small" @click="handleRestore(row)" :disabled="row.faultStatus !== '0'"
          style="margin-right: 5px;">恢复</Button>
      </template>
    </Table>

    <!-- 3. 分页区域 -->
    <Page :total="pagination.total" show-total  show-sizer :page-size="pagination.pageSize"
      :current.sync="pagination.currentPage" @on-change="handleCurrentChange" @on-page-size-change="handleSizeChange"
      style="margin-top: 16px; text-align: right;" />

    <!-- 4. 卡池详情弹窗 -->
    <Modal v-model="poolDetailModalVisible" title="卡池详情" :footer-hide="true" :mask-closable="false" width="900px">
      <Spin size="large" fix v-if="poolDetailLoading"></Spin>
      <div v-if="!poolDetailLoading && currentPoolDetails" class="modal-content">
        <div class="box">
          <span style="">卡池类型：&nbsp;&nbsp;{{ currentPoolDetails.usageType == "1" ? '全球卡普通卡池' :
            currentPoolDetails.usageType
              == "2" ? '终端线下卡池' : currentPoolDetails.usageType == "3" ? '终端线上卡池' : ""}}</span>
        </div>
        <div class="box">
          <span style="">卡池名称：&nbsp;&nbsp;{{ currentPoolDetails.poolName }}</span>
        </div>
        <div class="box">
          <span style="">支持国家/地区：&nbsp;&nbsp;{{ currentPoolDetails.mccsCn ? currentPoolDetails.mccsCn.join(',') :
            ''}}</span>
        </div>
        <div class="box">
          <span style="">供应商：&nbsp;&nbsp;{{ currentPoolDetails.supplierName }}</span>
        </div>
        <div class="box">
          <span style="">TPLID：&nbsp;&nbsp;{{ currentPoolDetails.tplId }}</span>
        </div>
        <div class="box" v-if="currentPoolDetails.usageType != '2'">
          <span style="">是否动态签约：&nbsp;&nbsp;{{ currentPoolDetails.isSignUpcc == "1" ? '是' : '否' }}</span>
        </div>
        <div class="box" v-if="currentPoolDetails.usageType != '1'">
          <span style="">是否动态开户：&nbsp;&nbsp;{{ currentPoolDetails.isOpenAccount == "1" ? '是' : '否' }}</span>
        </div>
        <!-- 全球卡普通卡池 -->
        <div class="box" v-if="currentPoolDetails.usageType == '1'">
          <span style="">支持HIMSI-4G上网：&nbsp;&nbsp;{{ currentPoolDetails.isSupportHimsi == "1" ? '是' : '否' }}</span>
        </div>
        <!-- 终端线下卡池 -->
        <div class="box" v-if="currentPoolDetails.usageType == '2'">
          <span style="">厂商：&nbsp;&nbsp;{{ detilCorpname }}</span>
        </div>
        <div class="box" v-if="currentPoolDetails.usageType == '2'">
          <span style="">套餐名称：&nbsp;&nbsp;{{ currentPoolDetails.packageName || '暂无套餐名称' }}</span>
        </div>
        <div class="box" v-if="currentPoolDetails.usageType == '2'">
          <span style="">套餐计算周期类型：&nbsp;&nbsp;{{ currentPoolDetails.periodUnit == '1' ? '24小时' :
            currentPoolDetails.periodUnit == '2' ?
              '自然日' : currentPoolDetails.periodUnit == '3' ? '自然月' : currentPoolDetails.periodUnit == '4' ? '自然年' :
            ''}}</span>
        </div>
        <div class="box" v-if="currentPoolDetails.usageType == '2'">
          <span style="">持续周期数：&nbsp;&nbsp;{{ currentPoolDetails.keepPeriod }}</span>
        </div>  
        <div class="box" v-if="currentPoolDetails.usageType == '2'">
          <span style="">到期后是否重置：&nbsp;&nbsp;{{ currentPoolDetails.isExpireReset == '1' ? '是' : '否' }}</span>
        </div>
        <!-- 全球卡普通卡时，支持HIMSI-4g上网时不展示 -->
        <div class="box" v-if="currentPoolDetails.isSupportHimsi != '1'">
          <span style="">VIMSI冻结周期：&nbsp;&nbsp;{{ currentPoolDetails.vimsiFreezeDay }}</span>
        </div>
        <div class="box" v-if="currentPoolDetails.isSupportHimsi != '1'">
          <span style="">告警阈值：&nbsp;&nbsp;{{ currentPoolDetails.alarmThreshold }}</span>
        </div>
        <div style="color:#878787;line-height: 30px;background-color: #f7f7f7;">
          <Collapse v-model="activeCollapsePanel" @on-change="handleCollapseChange">
            <Panel name="1">
              已绑定套餐
              <div slot="content"
                :style="{ 'height': currentPoolDetails.packageList && currentPoolDetails.packageList.length > 5 ? '150px' : '100%', 'overflowY': currentPoolDetails.packageList && currentPoolDetails.packageList.length > 5 ? 'auto' : 'visible' }">
                <div v-if="!currentPoolDetails.packageList || currentPoolDetails.packageList.length === 0">
                  <div v-if="!currentPoolDetails.packageList">
                    <div style="display: flex;justify-content: center;">
                      <Icon type="ios-loading" size="large" style="margin-top: 100px;"></Icon>
                    </div>
                  </div>
                  <div v-else style="display: flex;justify-content: center;">
                    <div style="color: #999;">暂未绑定套餐</div>
                  </div>
                </div>
                <div v-for="(item, index) in currentPoolDetails.packageList" :key="index" v-else>
                  <Row>
                    <Col span="10">
                    <div style="overflow: hidden; text-overflow:ellipsis; white-space: nowrap;text-align: left;">
                      套餐ID：&nbsp;&nbsp;{{ item.packageId }}
                    </div>
                    </Col>
                    <Col span="8">
                    <div style="overflow: hidden; text-overflow:ellipsis; white-space: nowrap;text-align: left;">
                      套餐名称：&nbsp;&nbsp;{{ item.nameCn || '暂无名称' }}
                    </div>
                    </Col>
                    <Col span="6">
                    <div style="overflow: hidden; text-overflow:ellipsis; white-space: nowrap;text-align: left;">
                      绑定时间：&nbsp;&nbsp;{{ item.createTime | formatDateTime }}
                    </div>
                    </Col>
                  </Row>
                </div>
              </div>
            </Panel>
          </Collapse>
        </div>
      </div>
      <div v-if="!poolDetailLoading && !currentPoolDetails" style="text-align: center; padding: 20px;">
        无法加载卡池详情
      </div>
    </Modal>

  </Card>
</template>

<script>
import { getSuppliers, getTargetCountries, getCardPoolById, updateFaultStatus, getSupplierFaultRuleList, getCardPoolPackageList } from '@/api/server/faultHandling'; // 更新导入的API函数
import { getPage } from '@/api/customer/manufacturer';
export default {
  name: 'FaultHandling',
  data () {
    return {
      filters: {
        ruleName: '',
        faultSupplier: '',
        targetCountries: [],
        backupSupplier: '',
        backupPool: '',
        status: ''
      },
      loading: false, // 表格加载状态
      tableData: [], // 表格数据将从API获取
      supplierList: [], // 用于存储供应商列表
      countryList: [], // 用于存储国家列表
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      columns: [ // iView Table 列定义
        { title: '规则名称', key: 'ruleName', minWidth: 200, ellipsis: true, tooltip: true, align: 'center' },
        { title: '故障资源供应商', key: 'faultSupplierName', minWidth: 100, ellipsis: true, tooltip: true, align: 'center' },
        { title: '目标国家', key: 'targetCountries', width: 200, ellipsis: true, tooltip: true, align: 'center' },
        { title: '备用资源供应商', slot: 'backupSuppliers', minWidth: 100, ellipsis: true, tooltip: true, align: 'center' },
        { title: '备用卡池', slot: 'backupPools', minWidth: 250, ellipsis: true, tooltip: true, align: 'center' },
        { title: '故障状态', slot: 'status', width: 100, align: 'center' },
        { title: '故障开始时间', key: 'faultStartTime', width: 160, align: 'center' },
        { title: '故障恢复时间', key: 'faultRestoreTime', width: 160, align: 'center' },
        { title: '操作', slot: 'action', width: 150, align: 'center' }
      ],
      poolDetailModalVisible: false, // 卡池详情弹窗可见性
      currentPoolDetails: null, // 当前查看的卡池详情
      poolDetailLoading: false, // 卡池详情加载状态
      activeCollapsePanel: '',// 默认展关闭套餐面板
      localLanguage: this.$i18n.locale,
      detilCorpname: '', // 厂商名称
      corpIdDetails: [] // 存储厂商数据
    };
  },
  methods: {
    handleSearch () {
      this.pagination.currentPage = 1; // 查询时重置到第一页
      this.fetchData();
    },
    handleReset () {
      // this.$refs.searchForm.resetFields(); // 使用 iView Form 的重置方法，注意 Select 多选可能清空不掉，需要手动清空
      this.filters = { // 手动重置所有筛选条件
        ruleName: '',
        faultSupplier: '',
        targetCountries: [],
        backupSupplier: '',
        backupPool: '',
        status: ''
      };
      this.handleSearch();
    },
    handleNew () {
      // TODO: 跳转到新建故障处理规则的路由
      this.$router.push({ name: 'fault_add' }); // 示例路由名称
    },
    handleRestore (row) {
      console.log('恢复:', row);
      this.$Modal.confirm({
        title: '确认恢复',
        content: '<p>确认恢复该故障记录?</p>',
        onOk: () => {
          // 调用故障恢复API
          updateFaultStatus(row.id)
            .then(res => {
              if (res.code === '0000') {
                this.$Message.success('操作成功!');
                this.fetchData(); // 刷新列表
              } else {
                this.$Message.error('操作失败' + ': ' + (res.msg || '未知错误'));
              }
            })
            .catch(err => {
              console.error('恢复故障失败:', err);
              this.$Message.error('操作失败');
            });
        },
        onCancel: () => {
          this.$Message.info('取消');
        }
      });
    },
    handleDelete (row) {
      console.log('删除:', row);
      this.$Modal.confirm({
        title: '删除',
        content: '<p>此操作将永久删除该记录, 是否继续?</p>',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          // TODO: 调用API进行删除
          console.log('调用删除API, ID:', row.id);
          // --- API调用成功后 ---
          this.$Message.success('操作成功!');
          this.fetchData(); // 刷新列表
          // --- API调用失败后 ---
          // this.$Message.error('删除失败，请重试!');
        },
        onCancel: () => {
          this.$Message.info('取消');
        }
      });
    },
    showPoolDetails (pool) {
      console.log('请求显示卡池详情:', pool);
      this.currentPoolDetails = null; // 先清空旧数据
      this.detilCorpname = ''; // 清空厂商名称
      this.poolDetailModalVisible = true;
      this.poolDetailLoading = true;
      this.activeCollapsePanel = ''; // 确保每次关闭 
      let that = this

      // 调用API获取卡池详情
      let data = {
        poolId:pool.poolId,
        page:1,
        pageSize:9999
      }
      getCardPoolById(data)
        .then(res => {
          if (res.code === '0000' && res.data) {
            this.currentPoolDetails = res.data.data[0];
            console.log('获取到卡池详情:', res.data);
            // 如果有corpId，可以调用接口获取厂商名称
            if (this.currentPoolDetails.corpId) {
              console.log("获取厂商名称",this.currentPoolDetails.usageType, this.currentPoolDetails.corpId)
              // TODO: 实际项目中应该调用后端接口获取厂商名称
              that.getCompanyList(this.currentPoolDetails.usageType, this.currentPoolDetails.corpId);
            }
          } else {
            this.$Message.warning('操作失败' + ': ' + (res.msg || '未知错误'));
            this.currentPoolDetails = null;
          }
        })
        .catch(err => {
          console.error('获取卡池详情失败:', err);
          this.$Message.error('操作失败');
          this.currentPoolDetails = null;
        })
        .finally(() => {
          this.poolDetailLoading = false;
        });
    },
    getCardPoolPackageList(){
      getCardPoolPackageList({
        poolId: this.currentPoolDetails.poolId
      }).then(res => {
        console.log("套餐列表:", res)
        this.currentPoolDetails.packageList = res.data;
      }).catch(err => {
        console.log("套餐列表获取失败:", err)
      })
    },
    handleCollapseChange (e) {
      //如果打开，则获取套餐列表
      if (e.includes('1')) {
        //如果套餐数据没有，则获取套餐数据
        if (!this.currentPoolDetails.packageList) {
          this.getCardPoolPackageList();
        }
      }else{
        console.log("关闭套餐面板")
      }

    },
    //获取厂商集合
    getCompanyList (e, corpId) {
      console.log("获取厂商名称getCompanyList",e, corpId)
      //8-线下(前端线下为2) 7-线上(前端线上为3)
      getPage({
        pageNumber: 1,
        pageSize: -1,
        corpType: e == '2' ? '8' : '7'
      }).then(res => {
        if (res && res.code == '0000') {
          var data = res.data.records;
          this.corpIdDetails = data; // 保存厂商数据
          data.forEach((item) => {
            if (item.corpId === corpId) {
              this.detilCorpname = item.corpName;
              console.log("找到匹配厂商:", item.corpName);
            }
          });
          // 如果没找到匹配的厂商
          if (!this.detilCorpname) {
            console.log("未找到匹配厂商，corpId:", corpId);
            this.detilCorpname = "未知厂商";
          }
        } else {
          console.error("获取厂商列表失败:", res);
          this.detilCorpname = "未知厂商";
        }
      }).catch((err) => {
        console.error("获取厂商列表异常:", err);
        this.detilCorpname = "未知厂商";
      });
    },
    handleSizeChange (pageSize) {
      this.pagination.pageSize = pageSize;
      this.fetchData();
    },
    handleCurrentChange (currentPage) {
      this.pagination.currentPage = currentPage;
      this.fetchData();
    },
    fetchData () {
      console.log('获取数据 for page:', this.pagination.currentPage, 'size:', this.pagination.pageSize, 'filters:', this.filters);
      this.loading = true;
      // 准备请求参数
      const params = {
        pageNum: this.pagination.currentPage,
        pageSize: this.pagination.pageSize,
        ruleName: this.filters.ruleName || undefined,
        faultSupplierId: this.filters.faultSupplier || undefined,
        standbySupplierId: this.filters.backupSupplier || undefined,
        faultStatus: this.filters.status || undefined,
        mccs: this.filters.targetCountries.length > 0 ? this.filters.targetCountries : undefined,
        cardPoolName: this.filters.backupPool || undefined
      };

      // 调用API获取列表数据
      getSupplierFaultRuleList(params)
        .then(res => {
          if (res.code === '0000' && res.data) {
            this.tableData = res.data.records || [];
            //将tableData的countryEn字段从数字转成字符串，增加空值检查
            this.tableData.forEach(item => {
              item.targetCountries = item.countryEn ? item.countryEn.join(', ') : '';
            });

            this.pagination.total = res.data.total || 0;
            console.log('获取故障规则列表成功:', this.tableData);
          } else {
            this.tableData = [];
            this.pagination.total = 0;
            console.warn('获取故障规则列表失败:', res);
            this.$Message.warning('操作失败' + ': ' + (res.msg || '未知错误'));
          }
        })
        .catch(err => {
          console.error('获取故障规则列表失败:', err);
          this.tableData = [];
          this.pagination.total = 0;
          this.$Message.error('操作失败');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    fetchSuppliers () {
      console.log("获取供应商列表...");
      getSuppliers()
        .then(res => {
          if (res.code == '0000') { // 检查 res.data.data
            // 根据实际返回的数据结构调整
            // 使用 supplierId 作为 value, supplierName 作为 label
            this.supplierList = res.data;
            console.log("供应商列表获取成功: ", this.supplierList);
          } else {
            this.supplierList = []; // 清空以防出错
            console.warn("获取供应商列表成功，但数据为空或格式不符: ", res);
            this.$Message.warning('操作失败'); // 提示格式问题
          }
        })
        .catch(err => {
          console.error("获取供应商列表失败:", err);
          this.supplierList = []; // 清空以防出错
          this.$Message.error('操作失败');
        });
    },
    fetchCountries () {
      console.log("获取国家列表...");
      getTargetCountries()
        .then(res => {
          if (res.code === '0000') {
            this.countryList = res.data || [];
            console.log("目标国家列表获取成功: ", this.countryList);
          } else {
            this.countryList = []; // 清空以防出错
            console.warn("获取目标国家列表失败: ", res);
            this.$Message.warning('操作失败' + ': ' + (res.msg || '未知错误'));
          }
        })
        .catch(err => {
          console.error("获取目标国家列表失败:", err);
          this.countryList = []; // 清空以防出错
          this.$Message.error('操作失败');
        });
    }
  },
  filters: { // 添加过滤器用于格式化显示
    formatUsageType (value) {
      const types = { 
        '1': '全球卡普通卡池', 
        '2': '终端线下卡池', 
        '3': '终端线上卡池' 
      };
      return types[value] || '未知类型';
    },
    formatDateTime (value) {
      if (!value) return '';
      // 这里可以根据需要使用更复杂的日期格式化库，例如 moment.js 或 date-fns
      // 简单实现：
      try {
        const date = new Date(value);
        return date.toLocaleString(); // 使用本地化格式
      } catch (e) {
        return value; // 格式化失败则返回原始值
      }
    }
  },
  mounted () {
    this.fetchData(); // 页面加载时获取初始数据
    this.fetchSuppliers();
    this.fetchCountries();
  },
  watch: {
    'filters.targetCountries': function (val) {
      // 当国家列表选择变化时，将值转换为mccs参数
      this.filters.mccs = val;
    }
  }
};
</script>

<style scoped>
/* 可以添加一些微调样式 */
.ivu-form-item {
  margin-bottom: 16px;
  /* 调整查询项间距 */
}

a {
  cursor: pointer;
}

/* 详情弹窗内样式 */
.modal_content .box {
  line-height: 2.5;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
}

/* 表格内链接悬停效果 */
a:hover {
  color: #2d8cf0;
  /* iView 主题蓝色 */
  text-decoration: underline;
  /* 添加下划线 */
}

.modal_content {
		padding: 0 16px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 85px;
	}

	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}

	.box {
		padding: 0 10px;
		color: #878787;
		line-height: 38px;
		background-color: #f7f7f7;
		border: 1px solid #dcdee2;
		border-bottom: none;
	}
</style>