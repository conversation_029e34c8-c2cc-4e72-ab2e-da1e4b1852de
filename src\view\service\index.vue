<template>
	<div>
		<Card>
			<div class="search_head_i">
				<div class="search_box">
					<span class="search_box_label">MSISDN</span>
					<Input :placeholder="$t('support.MSISDNenter')" v-model.trim="msisdn" clearable style="width: 200px" />
				</div>
				<div class="search_box">
					<span class="search_box_label">ICCID</span>
					<Input :placeholder="$t('support.ICCIDenter')" v-model.trim="iccid" clearable style="width: 200px" />
				</div>
				<div class="search_box">
					<span class="search_box_label">IMSI</span>
					<Input :placeholder="$t('support.IMSIenter')" v-model.trim="imsi" clearable style="width: 200px" />
				</div>
				<div class="search_box">
					<Button style="margin: 0 4px" v-preventReClick type="primary" @click="search()" :loading="loading">
						<Icon type="ios-search" />&nbsp;{{$t('support.search')}}
					</Button>
				</div>
			</div>
			<!-- 表格 -->
			<div style="margin-top: 20px;display: flex;">
				<mailTable :tableData="tableData" :realNameInfoData="realNameInfoData" :tableStyle="{ width:'930px' }"></mailTable>
				<!-- 右侧按钮 -->
				<div style="margin-left: 80px;" v-if="this.ShowList != null">
					<Form>
						<FormItem>
							<router-link :to="{name:'location_package', query: {
      				  imsi: this.ShowList.imsi,
      				  mcc: this.ShowList.mcc,
					  backimsi:this.imsi,
					  backiccid:this.iccid,
					  backmsisdn:this.msisdn,
      				  localName: this.ShowList.local,
      				  localNameEn: this.ShowList.localEn
      				}}"
							 style="margin-right: 10px;" v-if="this.ShowList.mcc != null && this.ShowList.mcc != ''">
								<Button v-has="'currentPackage'" style="margin: 0 4px;width: 180px;" type="primary">
									{{$t('location_package')}}
								</Button>
							</router-link>
							<Button v-else v-has="'currentPackage'" style="margin: 0 4px;width: 180px;" disabled type="primary" @click="showLocalUpModal()">
								{{$t('location_package')}}
							</Button>
						</FormItem>

						<FormItem>
							<router-link :to="{name:'purchased_package', query: {
              type: this.ShowList.position,
      				imsi: this.ShowList.imsi,
					iccid: this.ShowList.iccid,
      				mcc: this.ShowList.mcc,
					backimsi:this.imsi,
					backiccid:this.iccid,
					backmsisdn:this.msisdn,
          isBak: this.ShowList.isBak,
      				}}">
								<Button v-has="'purchasedPackage'" style="margin: 0 4px;width: 180px;" type="primary">
									{{$t('sppurchased_package')}}
								</Button>
							</router-link>
						</FormItem>

						<FormItem>
							<Button v-has="'locationRecord'" style="margin: 0 4px;width: 180px;" type="primary" @click="showLocalUpModal(ShowList)">
								{{$t('support.Locationrecord')}}
							</Button>
						</FormItem>
						<FormItem>
							<Button v-has="'sendSMS'" style="margin: 0 4px;width: 180px;" type="primary" @click="showSmsModal(ShowList)">
								{{$t('support.SendSMS')}}
							</Button>
						</FormItem>
						<FormItem>
							<Button v-has="'view'" style="margin: 0 4px;width: 180px;" type="primary" @click="showTrafficModal(ShowList)">
								{{$t('support.Flowdetails')}}
							</Button>
						</FormItem>
						<FormItem v-if="this.ShowList.cardForm == '2'">
							<Button v-has="'esimInfo'" style="margin: 0 4px;width: 180px;" type="primary"  @click="showEsimModal(ShowList)">
								{{$t('support.EsimDetails')}}
							</Button>
						</FormItem>
            <FormItem>
            	<Button v-has="'replaceHIMSI'" style="margin: 0 4px;width: 180px;" type="primary"  @click="showHIMSIModal(ShowList)">
            		{{$t('support.replaceHIMSI')}}
            	</Button>
            </FormItem>
            <FormItem>
            	<Button v-has="'sessionQuery'" style="margin: 0 4px;width: 180px;" type="primary" @click="showSessionModal(ShowList)">
            		Session查询
            	</Button>
            </FormItem>
					</Form>
				</div>
			</div>
		</Card>

		<Modal :title="$t('support.Locationrecord')" v-model="localUpModal" :mask-closable="false" @on-cancel="cancelModal" width="850px">
			<update-record v-if="localUpModal" :imsi="localUpImsi" :iccid="ShowList.iccid" :local="localUpLocal" />
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">{{$t('support.close')}}</Button>
				<Button type="primary" v-has="'export'" @click="exportlocalUp">{{$t('stock.exporttb')}}</Button>
			</div>
		</Modal>

		<Modal :title="$t('support.SendSMS')" v-model="smsModal" :mask-closable="false" @on-cancel="cancelModal" width="500px" :footer-hide="true">
			<sms-modal v-if="smsModal" :row="row" @closeModal="cancelModal" />
		</Modal>

		<Modal :title="$t('support.usedetails')" v-model="trafficModal" :mask-closable="false" @on-cancel="cancelModal" width="60%">
			<traffic-detail v-if="trafficModal" :form="form" :imsi="trafficImsi" />
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<!-- <Button @click="cancelModal">关闭</Button> -->
				<!-- <Button type="primary" @click="exportTrafficFile" v-has="'export'">导出</Button> -->
			</div>
		</Modal>

		<Modal :title="$t('support.EsimDetails')" v-model="esimModal" :mask-closable="false" @on-cancel="cancelModal" width="30%">
			<esim-detail v-if="esimModal" :esimIccid="esimIccid"/>
			<div slot="footer" style="text-align: center;">
				<Button style="margin: 0 4px" @click="cancelModal">
					<Icon type="ios-arrow-back" />&nbsp;{{$t('support.back')}}
				</Button>
				<Button v-has="'qrCode'" type="primary" style="margin-left: 8px" :loading="qrloading" @click="generateQRCode">{{$t('support.generateQRCode')}}</Button>
			</div>
		</Modal>

    <Modal :title="$t('support.replaceHIMSI')" v-model="himsiModal" :mask-closable="false" @on-cancel="cancelModal" width="30%" :footer-hide="true">
    	<replace-HIMSI v-if="himsiModal" :himsiIccid="himsiIccid" />
    </Modal>

    <!-- esim二维码图片查看弹窗 -->
		<Modal :title="$t('support.qrPicture')" v-model="imgModel" :footer-hide="true" width="500px" id="img"  @on-cancel="imgCancelModal">
			<div style="display: flex;justify-content: center;align-items: center;width: 450px; ">
				<img :src="pictureUrl" width="90%">
			</div>
		</Modal>

		<Modal :title="$t('sessionInfo.sessionDetail')" v-model="sessionModal" :footer-hide="true" :mask-closable="false" @on-cancel="cancelModal" width="1400px">
			<session-detail v-if="sessionModal" :imsi="ShowList.imsi" :iccid="ShowList.iccid" @close="cancelModal" />
		</Modal>
	</div>
</template>

<script>
	import excel from '@/libs/excel';
	import {
		cardPageList,
	} from '@/api/mastercdr';
	// import expandRow from './modal/expordRow.vue';
	import UpdateRecord from './modal/updateRecordModal.vue';
	import SmsModal from './modal/smsModal.vue';
	import TrafficDetail from './modal/trafficDetaillModal.vue';
	import EsimDetail from './modal/esimDetail.vue';
	import LocalMeal from './modal/localMealModal.vue';
	import mailTable from './modal/tableComponent.vue';
	import replaceHIMSI from './modal/replaceHIMSI.vue';
	import {
		customerService
	} from '@/api/realname/certification'
	import {
		searchByLocal,
		//位置更新记录
		getUpdateRecordsH,
		//流量详情记录
		getTrafficDetail
	} from '@/api/server/card'
	import {
		getQrCode
	} from '@/api/aqCode';
	import SessionDetail from '@/components/sessionDetail/index.vue';

	export default {
		components: {
			// expandRow,
			SmsModal,
			TrafficDetail,
			UpdateRecord,
			LocalMeal,
			mailTable,
			EsimDetail,
      replaceHIMSI,
      SessionDetail,
		},
		provide() {
		  return {
        // 给子组件提供关闭父组件modal的方法
		    closeHIMSIModal: () => {
		      this.himsiModal = false;
		    }
		  };
		},
    data() {
			return {
				msisdn: '',
				imsi: '',
				iccid: '',
				tableData: [],
				formList: {},
        realNameInfoData: [],
				ModalFlag: false,
				localUpModal: false, //位置更新记录模态框
				localUpImsi: '',
				localUpLocal: '',
				trafficImsi: '',
				esimImsi: '',
				smsModal: false, //短信发送模态框
				trafficModal: false, //流量详情
				esimModal: false, //ESIM信息查询
				imgModel: false, //二维码图片
        himsiModal: false, //替换HIMSI
				row: null, //标记当前选中行
				cardId: null,
				loading: false,
				qrloading: false,
				currentPage: 1,
				startTime: null,
				endTime: null,
				total: 0,
				pageSize: 10,
				exportData: [],
				excelKey: [],
				excelTitle: [],
				ShowList: null,
				form: {
					imsi: "",
					iccid: "",
					msisdn: "",
				},
				pictureUrl: '',
				esimIccid: '',
				sessionModal: false, //会话详情
			}
		},
		computed: {

		},
		methods: {
			// 获取列表
			goPageFirst: function(page) {
				if (this.msisdn || this.iccid || this.imsi) {
					var msisdn = this.msisdn.replace(/\s/g, '');
					var iccid = this.iccid.replace(/\s/g, '');
					var imsi = this.imsi.replace(/\s/g, '');
					this.currentPage = page;
					this.loading = true;
					cardPageList({
						current: page,
						size: this.pageSize,
						msisdn: msisdn,
						iccid: iccid,
						imsi: imsi,
						isNeedMcc: true
					}).then(res => {
						if (res && res.code == '0000') {
							let List=[]
							if (res.data != null && res.data.length > 0) {
								this.ShowList = res.data[0];
								let text=""
								if(this.ShowList.realCountry != null &&this.ShowList.realCountry.length > 0){
									this.ShowList.realCountry.map((value, id) => {
										var country=this.$i18n.locale==='zh-CN' ? value.countryCN.toString():this.$i18n.locale==='en-US' ? value.countryEN.toString(): ''
										if (text === "") {
											text = text + '' + country
										} else {
											text = text + ', ' + country
										}
									})
								}
								//之前只有一个实名认证信息，传给子组建查看"实名制认证详情"
        //           this.formList = {
								// 	id: this.ShowList.certificatesId,
								// 	nameCh: this.ShowList.nameCh,
								// 	name: this.ShowList.name,
								// 	certificatesType: this.ShowList.certificatesType === '1' ? this.$t('support.Passport') : this.ShowList.certificatesType === '2' ? this.$t('support.Permit') :
								// 		this.ShowList.certificatesType === '3' ? this.$t('support.HKIdentityCard') : this.ShowList.certificatesType === '4' ? this.$t('support.MacauIdentityCard') : "",
								// 	passportCountry:this.ShowList.passportCountry,
								// 	errorDesc: this.ShowList.errorDesc === '0' ? this.$t('support.picturesAreNotSatisfied') : this.ShowList.errorDesc === '1' ? this.$t('support.nameIsInconsistent') : this.ShowList.errorDesc === '2' ? this.$t('support.certificateHasExpired') :
								// 		this.ShowList.errorDesc === '3' ? this.$t('support.IDIsInconsistent') : this.ShowList.errorDesc === '4' ? this.$t('support.sixteenyYearsOld') : "",
								// }
                //实名认证信息
                // 如果没有值，保留无需认证的展示
                let realEmptyData = [{
                  useStatus: null,
                  authStatus: null
                }]
                this.realNameInfoData = res.data[0].realNameInfos ? res.data[0].realNameInfos : realEmptyData
                let attributableChannel = ''
                let cooperationModel = ''
                if (res.data[0].isShowChannel) {
                  attributableChannel = this.ShowList.parentCorpName ? this.ShowList.parentCorpName : this.ShowList.channelName
                  cooperationModel = this.ShowList.cooperationMode ? ( this.ShowList.cooperationMode== '1' ? this.$t('support.distribution') : this.ShowList.cooperationMode == '2' ? this.$t('support.atoz'): this.$t('support.resourceCooperation')):""
                } else {
                  attributableChannel = ''
                  cooperationModel = ''
                }
								console.log(this.ShowList.whitePackageId)
								// 封装返回值
								this.tableData = [{
										key: 'MSISDN',
										value: this.ShowList.msisdn
									},
									{
										key: 'ICCID',
										value: this.ShowList.iccid
									},
									{
										key: 'IMSI',
										value: this.ShowList.imsi
									},
									{
										key: 'PIN',
										value: this.ShowList.pin2
									},
									{
										key: 'PUK',
										value: this.ShowList.puk1
									},
									{
										key: this.$t('support.cardtype'),
										value: this.ShowList.cardForm == '1' ? this.$t("stock.PhysicalSIM") : this.ShowList.cardForm == '2' ? this.$t("stock.eSIM") : this.ShowList
											.cardForm == '3' ? this.$t("stock.TSIM") : this.ShowList.cardForm == '4' ? this.$t("support.imsi") : '',
									},
									{
										key: this.$t('support.cardstate'),
										value: this.ShowList.status == '1' ? this.$t("order.Normal") : this.ShowList.status == '2' ? this.$t(
												"order.Terminated") : this.ShowList.status ==
											'3' ? this.$t("order.Suspend") : this.ShowList.status == '4' ? this.$t("order.Expired") : ' ',
									},
									{
										key: this.$t('support.template'),
										value: this.ShowList.templateName
									},
									{
										key: this.$t('support.Activation'),
										value: this.ShowList.activeType == '1' ? this.$t('support.Automatic') : this.ShowList.activeType == '2' ? this.$t('support.Manual') : ' '
									},
									{
										key: this.$t('support.SIMDate'),
										value: this.ShowList.expireTime
									},
									{
										key: this.$t('support.Targeting'),
										value: this.ShowList.position == '1' ? this.$t('support.Hcard') : this.ShowList.position == '2' ? this.$t('support.Vcard') : ' '
									},
									{
										key: this.$t('support.position'),
										value: this.$i18n.locale === 'zh-CN' ? this.ShowList.local : this.ShowList.localEn
									},
									{
										key: this.$t('stock.attributableChannel'),
										value: attributableChannel,
										permission: 'attributableChannel' // 此字段需要特定权限
									},
									{
										key: this.$t('support.cooperationModel'),
										value: cooperationModel,
										permission: 'cooperationModel' // 此字段需要特定权限
									},
                  // {
									// 	key: this.$t('stock.WhitelistPackageID'),
									// 	value: this.ShowList.whitePackageId
									// },
									// {},
								]
							} else {
                this.ShowList = null
								this.tableData = [];
                this.realNameInfoData = [];
								this.$Notice.error({
									title: this.$t("address.Operationreminder"),
									desc: this.$t("support.querycontent")
								})
							}
						} else {
							throw res
						}
					}).catch((err) => {
						console.log(err);
					}).finally(() => {
						this.loading = false;
					});
				}
			},
			//根据条件查询
			search: function() {
				if (this.msisdn || this.iccid || this.imsi) {
					sessionStorage.removeItem("pictureUrl") //搜索新的卡片时清除之前缓存的图片
					this.goPageFirst(1);
				} else {
					this.$Notice.warning({
						title: this.$t("address.Operationreminder"),
						desc: this.$t("support.searchcondition")
					})
					return false;
				}
			},
			//模态框关闭
			cancelModal: function() {
				this.localUpModal = false;
				this.trafficModal = false;
				this.smsModal = false;
				this.esimModal = false;
				this.sessionModal = false;
			},
			//二维码模态框关闭
			imgCancelModal() {
				this.imgModel = false;
				this.pictureUrl = "";
			},
			//位置更新记录导出
			exportlocalUp: function() {
				getUpdateRecordsH({
					imsi: this.localUpImsi,
					pageNumber: 1,
					pageSize: -1,
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						var content = data.records;
						content.map((value, index) => {
							return value.activeType == '1' ? value.activeType = this.$t('support.Automatic') : value.activeType == '2' ? value.activeType =
								this.$t('support.Manual') : value.activeType = ' ';
						});
						var key = ["reportTime", "mcc", "activeType"];
						var title = ["reportTime", "mcc", "activeType"];
						var filename = this.$t('support.Locationexport');
						var params = {
							title: title,
							key: key,
							data: content,
							autoWidth: true,
							filename: filename
						};
						excel.export_array_to_excel(params);
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {

				})
			},
			//导出流量详情
			exportTrafficFile: function() {
				getTrafficDetail({
					key: this.trafficImsi,
					pageNumber: 1,
					pageSize: -1,
					type: 1
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						var content = data.records;
						var key = ["useDate", "flowCount", "packageName"];
						var title = ["useDate", "flowCount(G)", "packageName"];
						var filename = this.$t('support.recordexport');
						var params = {
							title: title,
							key: key,
							data: content,
							autoWidth: true,
							filename: filename
						};
						excel.export_array_to_excel(params);
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {

				});
			},
			//流量详情
			showTrafficModal: function(row) {
				this.trafficImsi = row.imsi;
				this.form = {
						imsi: row.imsi,
						iccid: row.iccid,
						msisdn: row.msisdn,
					},
					this.trafficModal = true;
			},
			//esim信息
			showEsimModal: function(row) {
				this.esimIccid = row.iccid
				this.esimModal = true
			},
			//替换HIMSI
      showHIMSIModal: function (row) {
        this.himsiIccid = row.iccid
        this.himsiModal = true
      },
      // 已购买记录
			goPurchasedPackage: function(row) {
				this.$router.push({
					name: 'purchased_package',
					query: {
						MSISDN: row.MSISDN,
						ICCID: row.ICCID,
						IMSI: row.IMSI
					}
				})
			},
			// 位置更新记录
			showLocalUpModal: function(row) {
				this.localUpImsi = row.imsi;
				if (this.$i18n.locale === 'en-US') {
					this.localUpLocal = row.localEn;
				}else {
					this.localUpLocal = row.local;
				}
				this.localUpModal = true
			},
			// 当前位置套餐记录
			goLocationPackage: function(row) {
				this.$router.push({
					name: 'location_package',
					query: {
						MSISDN: row.MSISDN,
						ICCID: row.ICCID,
						IMSI: row.IMSI
					}
				})
			},
			showSmsModal: function(row) {
				this.row = row;
				this.smsModal = true;
			},
			generateQRCode() {
				this.qrloading = true
				if (sessionStorage.pictureUrl) {
					this.pictureUrl = JSON.parse(sessionStorage.getItem("pictureUrl"))
				} else {
					getQrCode({
						iccid: this.esimIccid
					}).then(res => {
						const blob = new Blob([res.data])
						this.pictureUrl = window.URL.createObjectURL(blob)
						sessionStorage.setItem("pictureUrl", JSON.stringify(this.pictureUrl))
						this.pictureUrl = JSON.parse(sessionStorage.getItem("pictureUrl"))
					})
				}
				document.getElementById("img").oncontextmenu = stop
				document.getElementById("img").oncopy = stop
				this.qrloading = false
				this.imgModel = true
			},
			showSessionModal(row) {
				this.sessionModal = true
			},
		},
		mounted() {
			this.msisdn = localStorage.getItem("MSISDN") === null ? '' : localStorage.getItem("MSISDN")
			this.iccid = localStorage.getItem("ICCID") === null ? '' : localStorage.getItem("ICCID")
			this.imsi = localStorage.getItem("IMSI") === null ? '' : localStorage.getItem("IMSI")
			localStorage.removeItem("IMSI")
			localStorage.removeItem("ICCID")
			localStorage.removeItem("MSISDN")
			this.goPageFirst(1);
		},

		watch: {}
	}
</script>

<style scoped="scoped">
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.modal_content {
		padding: 0 16px;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 85px;
	}

	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}
</style>
