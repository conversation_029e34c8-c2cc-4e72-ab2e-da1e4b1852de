<template>
	<!--  账户列表  -->
	<div>
		<Card>
			<div class="search_head_i">
				<div class="search_box">
					<Button style="margin: 0 4px" @click="reBack">
						<Icon type="ios-arrow-back" />&nbsp;{{$t('support.back')}}
					</Button>
				</div>
			</div>
			<div v-if="this.$i18n.locale === 'en-US'">
				<span style="font-weight:bold;">{{$t('support.position')}}：{{localNameEn}}</span>
			</div>
			<div v-else>
				<span style="font-weight:bold;">{{$t('support.position')}}：{{localName}}</span>
			</div>
			<div style="margin-top:10px">
				<Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading"
					max-height="500">
					<template slot-scope="{ row, index }" slot="action">
						<Button v-has="'active'" type="success" size="small" @click="activation(row)"
							v-if="row.packageStatus=='1'">{{$t('support.activation')}}</Button>
						<Button v-has="'active'" type="success" size="small" disabled
							v-else-if="row.packageStatus=='2'">{{$t('support.activated')}}</Button>
						<Button v-has="'active'" type="success" size="small" disabled
							v-else-if="row.packageStatus=='3'">{{$t('support.Used')}}</Button>
						<Button v-has="'active'" type="success" size="small" disabled
							v-else-if="row.packageStatus=='4'">{{$t('support.Activatedpending')}}</Button>
						<Button v-has="'active'" type="success" size="small" disabled
							v-else-if="row.packageStatus=='5'">{{$t('support.Expired')}}</Button>
						<Button v-has="'active'" type="success" size="small" @click="activation(row)"
							v-else-if="row.packageStatus=='6'">{{$t('support.Activating')}}</Button>
						<Button v-has="'active'" type="success" size="small" disabled v-else>" "</Button>
					</template>
				</Table>
			</div>
			<div style="margin-top:15px">
				<Page :total="total" :page-size="pageSize" :current.sync="pageP" show-total show-elevator
					@on-change="getLocalMeals" />
			</div>
		</Card>
	</div>
</template>

<script>
	import {
		getLocalMeals,
		//当前位置套餐激活
		doActivation,
	} from '@/api/server/card';
	const math = require('mathjs');
	export default {
		data() {
			return {
				localName: '',
				localNameEn: '',
				loading: false,
				pageP: 1,
				total: 0,
				pageSize: 10,
				columns: [],
				tableData: []
			}
		},
		methods: {
			//页面初始化
			init: function() {
				this.columns = [{
						title: this.$t('support.mealname'),
						key: 'packageName',
						align: 'center',
						minWidth: 150,
						render: (h, params) => {
							const row = params.row
							var text = this.$i18n.locale === 'zh-CN' ? row.packageName : this.$i18n.locale ===
								'en-US' ? row.packageNameEn : ' '
							return h('label', {
								style: {
									'word-break': 'break-word',
								}
							}, text)
						}
					},
					{
						title: this.$t('support.Ordernumber'),
						key: 'orderId',
						align: 'center',
						minWidth: 150,
						tooltip: true,
					},
					{
						title: this.$t('support.Activation_state'),
						key: 'packageStatus',
						align: 'center',
						minWidth: 130,
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.packageStatus) {
								case "1":
									text = this.$t('support.Unuse');
									break;
								case "2":
									text = this.$t('support.activated');
									break;
								case "3":
									text = this.$t('support.Used');
									break;
								case "4":
									text = this.$t('support.Activatedpending');
									break;
								case "5":
									text = this.$t('support.Expired');
									break;
								case "6":
									text = this.$t('support.Activating');
									break;
								default:
									text = "";
							}
							return h('label', text);
						},
					},
					{
						title: this.$t('support.Activationmethod'),
						key: 'activeType',
						align: 'center',
						minWidth: 130,
						render: (h, params) => {
							const row = params.row;
							const text = row.activeType == '1' ? this.$t('support.Automatic') : row
								.activeType == '2' ? this.$t('support.Manual') : '';
							return h('label', text)
						}
					},
					{
						title: this.$t('support.Periodtype'),
						key: 'periodUnit',
						align: 'center',
						minWidth: 130,
						render: (h, params) => {
							const row = params.row;
							const text = row.periodUnit === '1' ? this.$t("buymeal.hour") : (row.periodUnit ===
								'2' ? this.$t("buymeal.cday") :
								(row.periodUnit === '3' ? this.$t("buymeal.cmonth") : (row.periodUnit === '4' ? this.$t("buymeal.cyear") : ' ')));
							return h('label', text);
						},
					},
					{
						title: this.$t('support.Continuouscycle'),
						key: 'keepPeriod',
						align: 'center',
						minWidth: 130,
					},
					// {
					//   title: '套餐有效期',
					//   key: 'expireTime',
					//   align: 'center'
					// },
					{
						title: this.$t('support.meal_time'),
						key: 'expireTime',
						align: 'center',
						minWidth: 150,
					},
					{
						title: this.$t('deposit.mealprice'),
						key: 'price',
						align: 'center',
						minWidth: 120,
						// render: (h, params) => {
						//   const row = params.row;
						//   const text = Number(math.format(Number(row.price) / 100, 14));
						//   return h('label', text);
						// }
					},
					{
						title: this.$t('deposit.currency'),
						key: 'currencyCode',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.currencyCode) {
								case "156":
									text = this.$t('support.CNY');
									break;
								case "840":
									text = this.$t('support.USD');
									break;
								case "344":
									text = this.$t('support.HKD');
									break;
								default:
									text = "";
							}
							return h('label', text);
						},
					},
					{
						title: this.$t('support.Activatepackage'),
						slot: 'action',
						align: 'center',
						minWidth: 120,
					}
				];
				this.localName = this.$route.query.localName;
				this.localNameEn = this.$route.query.localNameEn;
				// 保存MSISDN，ICCID，IMSI
				localStorage.setItem("MSISDN", this.$route.query.backmsisdn)
				localStorage.setItem("ICCID", this.$route.query.backiccid)
				localStorage.setItem("IMSI", this.$route.query.backimsi)
				this.getLocalMeals(1);
			},
			getLocalMeals: function(page) {
				var mcc = this.$route.query.mcc;
				if (mcc == null || mcc == '') {
					this.$Notice.error({
						title: this.$t("address.Operationreminder"),
						desc: this.$t("support.obtained")
					});
					return false;
				};
				this.loading = true;
				this.pageP = page;
				getLocalMeals({
					imsi: this.$route.query.imsi,
					mcc: mcc,
					pageNumber: page,
					pageSize: this.pageSize,
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						this.tableData = data.records;
						this.total = data.totalCount;
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					this.loading = false
				})
			},
			//当前位置套餐激活
			activation: function(row) {
				doActivation({
					dataBundleId: row.packageId, //待激活套餐
					hImsi: this.$route.query.imsi,
					mcc: this.$route.query.mcc
				}).then(res => {
					if (res && res.code == '0000') {
						this.$Notice.success({
							title: this.$t("address.Operationreminder"),
							desc: this.$t("common.Successful")
						})
						this.getLocalMeals(this.pageP);
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {})
			},
			//查询当前位置套餐列表
			searchByCondition: function() {
				this.getLocalMeals(1);
			},
			reBack() {
				this.$router.push({
					name: 'service_index'
				})
			}
		},
		mounted: function() {
			this.init();
		},
		watch: {

		}
	};
</script>
<style scoped="scoped">
	.search_head {
		width: 100%;
		display: flex;
		text-align: center;
		align-items: center;
		justify-content: flex-start;
	}

	.search-btn {
		width: 100px !important;
	}

	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		width: 340px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}
</style>
