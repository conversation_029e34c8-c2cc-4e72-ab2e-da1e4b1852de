<!-- 展开项 -->

<style scoped>
  .expand-row {
    /* margin-bottom: 16px; */
  }
  .text{
    white-space:nowrap;
    text-overflow:ellipsis;
    overflow:hidden;
  }
</style>
<template>
  <div>
    <Row class="expand-row">
      <Col span="4" v-if="row.cardStatus">
      <span class="expand-key">卡片状态： </span>
      <span class="expand-value">{{ row.cardStatus }}</span>
      </Col>
      <Col span="4"  v-if="row.cardType">
      <span class="expand-key">卡片类型：</span>
      <span class="expand-value">{{ row.cardType }}</span>
      </Col>

      <Col span="4" v-if="row.activeType">
      <span class="expand-key">激活方式： </span>
      <span class="expand-value">{{ row.activeType }}</span>

      </Col>
      <Col span="4" v-if="row.overdueTime">
      <span class="expand-key">过期时间： </span>
      <span class="expand-value">{{ row.overdueTime }}</span>
      </Col>
      <Col span="8" class="text" v-if="row.smsTemp">
      <span class="expand-key">短信模板： </span>
      <span :title="row.smsTemp" class="expand-value ">{{ row.smsTemp }}</span>
      </Col>
    </Row>
  </div>
</template>
<script>
  export default {
    props: {
      row: Object
    },
    methods: {
      // 获取列表
      coypVal: function(value) {
        try {
          let Url2 = value;
          let oInput = document.createElement('input');
          oInput.value = Url2;
          document.body.appendChild(oInput);
          oInput.select();
          document.execCommand("Copy");
          oInput.className = 'oInput';
          oInput.style.display = 'none';
          this.$Message.success('复制成功');
        } catch (e) {
          this.$Message.error('复制功能暂不可使用，请手动复制');
        }

      },
    }
  };
</script>
