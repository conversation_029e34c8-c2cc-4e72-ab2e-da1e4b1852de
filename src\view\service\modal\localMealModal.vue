<!-- 流量 -->
<style scoped>
</style>
<template>
  <div>
    <div><span style="font-weight:bold;">当前位置：中国四川新希望大厦</span></div>
    <div style="margin-top:5px">
      <Table ref="selection" :columns="columns" :data="tableData" :ellipsis="true" :loading="loading" max-height="500">
        <template slot-scope="{ row, index }" slot="action">
          <Button type="success" size="small" disabled v-if="row.status=='1'">激活</Button>
          <Button type="success" size="small" @click="activation(row)" v-else>激活</Button>
        </template>
      </Table>
    </div>
    <div style="margin-top:15px">
      <Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="getLocalMeals" />
    </div>
  </div>
</template>
<script>
  import {
    getLocalMeals,
    //当前位置套餐激活
    doActivation,
  } from '@/api/server/card'
  export default {
    props: {
      cardId: String,
    },
    data() {
      return {
        page: 0,
        loading: false,
        currentPage: 1,
        total: 0,
        columns: [{
            title: '套餐名称',
            key: 'packageName',
            align: 'center'
          },
          {
            title: '激活状态',
            key: 'status',
            align: 'center',
            render: (h, params) => {
              const row = params.row;
              const text = row.status == '1' ? '激活中' : '未激活';
              return h('label', text);
            },
          },
          {
            title: '激活方式',
            key: 'actType',
            align: 'center'
          },
          {
            title: '周期类型',
            key: 'cycleType',
            align: 'center'
          },
          {
            title: '持续周期',
            key: 'cycle',
            align: 'center'
          },
          // {
          //   title: '套餐有效期',
          //   key: 'validity',
          //   align: 'center'
          // },
		 {
		   title: '套餐过期时间',
		   key: 'validity',
		   align: 'center'
		 },
          {
            title: '套餐价格',
            key: 'price',
            align: 'center'
          },
          {
            title: '币种',
            key: 'currency',
            align: 'center'
          },
          {
            title: '激活套餐',
            slot: 'action',
            align: 'center'
          },
        ],
        tableData: [{
            mealId: 'a9g69s6d971',
            packageName: '新年套餐',
            status: '1',
            actType: '自动',
            cycleType: '24小时',
            cycle: '7',
            validity: '2021-01-23 12:45:52',
            price: '50元',
            currency: '人民币'
          },
          {
            mealId: 'a9g69s6d972',
            packageName: '元旦套餐',
            status: '0',
            actType: '自动',
            cycleType: '每周',
            cycle: '4',
            validity: '2021-01-23 12:45:52',
            price: '50元',
            currency: '人民币'
          },
          {
            mealId: 'a9g69s6d972',
            packageName: '中秋套餐',
            status: '0',
            actType: '手动',
            cycleType: '每月',
            cycle: '1',
            validity: '2021-01-23 12:45:52',
            price: '50元',
            currency: '人民币'
          }
        ]
      }
    },
    methods: {
      getLocalMeals: function(page) {
        this.loading = true
        this.pageF = page
        // this.currentPage = page
        getLocalMeals({
          cardId: this.cardId,
          pageNumber: page,
          pageSize: 10,
        }).then(res => {
          if (res && res.code == '0000') {
            this.tableData = res.data
            this.total = res.total
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
      activation: function(row) {
        // this.$emit('doActivation', {
        //   cardId: this.cardId, //卡片
        //   actInfo: row //待激活套餐
        // })
        this.doActivation({
          cardId: this.cardId, //卡片
          actInfo: row //待激活套餐
        })
      },
      //当前位置套餐激活
      doActivation: function(e) {
        doActivation({
          cardId: e.cardId,
          mealId: e.actInfo.mealId
        }).then(res => {
          if (res && res.code == '0000') {
            this.$Notice.success({
              title: '操作成功',
              desc: '套餐激活成功'
            })
            this.getLocalMeals(this.page)
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loading = false
        })
      },
      // goPage: function(page) {
      //   this.getLocalMeals(page)
      // }
    },
    mounted: function() {
      // this.getLocalMeals(0)
    },
    watch: {
      cardId(newVal, oldVal) {
        this.getLocalMeals(0)
      }
    }
  };
</script>
<!--
 <Modal title="套餐详情" v-model="editModal" width="60%" :mask-closable="false" @on-cancel="cancelModal">
  <div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
    <Button @click="cancelModal">取消</Button>
    <Button type="primary" @click="cancelModal">确定</Button>
  </div>
    </Modal> -->
