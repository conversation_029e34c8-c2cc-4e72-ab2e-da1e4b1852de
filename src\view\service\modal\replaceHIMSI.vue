<template>
  <!-- 替换HIMSI -->
	<div style="padding: 16px;">
		<Form ref="himsiInfo" :model="himsiInfo" label-position="left" :label-width="105" style="font-size: 16px; margin-bottom: 40px;">
		    <FormItem label="ICCID">
		        <Input v-model.trim="himsiInfo.iccid" clearable></Input>
		    </FormItem>
		    <FormItem :label="$t('support.pakageId')">
		        <Input v-model.trim="himsiInfo.packageId" clearable></Input>
		    </FormItem>
		</Form>
    <Alert show-icon>
      An info prompt
      <template #desc>
        ICCID不填、Package Id填写：订购指定套餐<br>
        ICCID填写、Package Id不填：换卡继承旧卡所有套餐<br>
        ICCID填写、Package Id填写：换卡订购指定套餐
      </template>
    </Alert>
    <div style="text-align: center; margin-top: 30px;">
    	<Button style="margin: 0 4px" @click="cancelModal">
    		<Icon type="ios-arrow-back" />&nbsp;{{$t('support.back')}}
    	</Button>
      <Button style="margin: 0 4px" :loading="submitLoading" @click="submit">{{$t('address.determine')}}</Button>
    </div>
	</div>
</template>

<script>
	import {
		changeHIMSI
	} from '@/api/server/card.js';
	export default {
		props: ['himsiIccid'],
    inject: ['closeHIMSIModal'],
		data() {
			return {
        submitLoading: false,
				himsiInfo: {
					iccid: '',
					packageId: '',
					oldiccid: '',
				},
			}
		},
		methods: {
      // 返回
      cancelModal (){
        this.$refs["himsiInfo"].resetFields();
        this.closeHIMSIModal()
      },
			//提交
			submit: function() {
        if (this.himsiInfo.iccid || this.himsiInfo.packageId) {
          this.submitLoading = true
          changeHIMSI({
          	oldIccid: this.himsiIccid,
          	newIccid: this.himsiInfo.iccid,
          	packageId: this.himsiInfo.packageId
          }).then(res => {
          	if (res.code === "0000") {
          		this.$Notice.success({
          			title: this.$t('address.Operationreminder'),
          			desc: this.$t('common.Successful'),
          		});
          		this.cancelModal()
          	} else {
          		throw res
          	}
          }).catch((err) => {
          	console.error(err)
          }).finally(() => {
            this.submitLoading = false
          })
        } else {
          this.$Message['warning']({
            background: true,
            content: this.$t('flow.chooseOne')
          });
        }
			}
		},
	}
</script>

<style scoped="scoped">
</style>
