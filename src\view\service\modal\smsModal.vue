<!-- 下发短信 -->
<template>
  <div style="padding: 0 16px;">
    <Form ref="SMSFormValidate" :model="smsInfo" :rules="rules" :label-width="130" :label-height="100" inline style="font-weight:bold;">
      <FormItem :label="$t('support.sendingmethod')" prop="sendType" style="width:420px">
        <Select v-model="smsInfo.sendType" :placeholder="$t('support.chosesend')" clearable multiple style="width: 85%;">
          <Option v-for="(item,index) in sendTypeList" :value="item.key" :key="index">{{ item.value }}</Option>
        </Select>
      </FormItem>
      <FormItem :label="$t('support.template')" prop="tempId" style="width:420px">
        <Select v-model="smsInfo.tempId" :placeholder="$t('support.chosetemplate')" clearable style="width: 85%">
          <Option v-for="(item,index) in tempList" :value="item.id" :key="index">{{ item.templateName }}</Option>
        </Select>
      </FormItem>
      <FormItem :label="$t('support.phone')" v-if="smsInfo.sendType.indexOf('1') != -1" prop="phoneNum" style="width:420px">
        <Input v-model="smsInfo.phoneNum" clearable :placeholder="$t('support.phoneprompt')" style="width: 85%;" />
      </FormItem>
      <FormItem label="HIMSI" v-if="smsInfo.sendType.indexOf('2') != -1" prop="HIMSI" style="width:420px">
        <Input v-model="smsInfo.HIMSI" disabled placeholder="请输入HIMSI" style="width: 85%;" />
      </FormItem>
    </Form>
    <div style="text-align: center;margin: 4px 0;">
      <Button type="primary" @click="sendSms" v-preventReClick>{{$t('support.sending')}}</Button>
      <Button style="margin-left: 8px" @click="reSetInfo">{{$t('buymeal.Reset')}}</Button>
    </div>
  </div>
</template>
<script>
  import config from '@/config/index.js';
  let code = config.customerSMSType.code;
  let iccid1 = config.customerSMSType.iccid;
  import Vue from 'vue';
  import {
    getSmsTempList,
    sendSms
  } from '@/api/server/card'
  export default {
    props: {
      row: Object,
    },
    data() {
      //非负整数
      const nonnegativeInteger = (rule, value, callback) => {
        var str = /^[0-9]\d*$/;
        return str.test(value);
      };
      return {
        smsInfo: {
          HIMSI: '',
          phoneNum: '',
          sendType: ['1'], //默认手机号
          tempId: null
        },
        sendTypeList: [{
            key: '1',
            value: this.$t('support.phone')
          },
          {
            key: '2',
            value: 'HIMSI'
          }
        ],
        rules: {
          sendType: [{
            required: true,
            type: 'array',
            message: this.$t('support.Sendingempty'),
            trigger: 'change',
          }],
          tempId: [{
            required: true,
            message: this.$t('support.SMSempty'),
            trigger: 'change',
          }],
          phoneNum: [{
              required: true,
              message: this.$t('support.Phoneempty'),
              type: 'string',
            },
            {
              validator: nonnegativeInteger,
              message: this.$t('support.PhoneWrong'),
            }
          ],
          HIMSI: [{
            required: true,
            message: 'HIMSI不能为空',
            type: 'string',
          }],
        },
        tempList: [], //短信模板列表
      }
    },
    methods: {
      sendSms: function(type) {
        this.$refs['SMSFormValidate'].validate((valid) => {
          if (valid) {
            var typeList = this.smsInfo.sendType;
            var phones = [];
            for (var i = 0; i < typeList.length; i++) {
              if (typeList[i] == '1') {
                phones.push(this.smsInfo.phoneNum);
              }
              if (typeList[i] == '2') {
                // phones.push(this.smsInfo.HIMSI);
                phones.push(this.row.msisdn);
              }
            };
            //code
            var list = [this.row.pin2];
            var map = {};
            Vue.set(map, code, list);
            Vue.set(map, iccid1, [this.row.iccid]);
            var obj = {
              phones: phones,
              templateId: this.smsInfo.tempId,
              language: 0,
              params: map
            };
            sendSms(obj).then(res => {
              if (res && res.code == '0000') {
                this.$emit('closeModal', {
                  type: 'sms'
                })
                this.$Notice.success({
                  title: this.$t("address.Operationreminder"),
                  desc: this.$t("support.Sentsuccessfully")
                })
              } else {
                throw res
              }
            }).catch((err) => {
              console.log(err)
            }).finally(() => {});
          }
        })
      },
      //重置
      reSetInfo: function() {
        this.$refs['SMSFormValidate'].resetFields();
        this.smsInfo.HIMSI = this.row.imsi;
        this.smsInfo.phoneNum = '';
      },
      getSmsTempList: function() {
        getSmsTempList({}).then(res => {
          if (res && res.code == '0000') {
            this.tempList = res.data;
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {

        });
      }
    },
    mounted: function() {
      this.reSetInfo();
      this.getSmsTempList();
    },
    watch: {

    }
  };
</script>

<style scoped>
  .input_modal {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    width: 100%;
  }
</style>
