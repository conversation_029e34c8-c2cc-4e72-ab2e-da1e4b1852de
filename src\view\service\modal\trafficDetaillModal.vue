<!-- 流量详情 -->
<template>
  <div style="padding: 0 16px;">
    <div>
      <Table :columns="columnsF" :data="tableDataF" :ellipsis="true" :loading="loadingF" max-height="500"></Table>
    </div>
   <!-- <div class="table-botton" style="margin-top:15px">
      <Page :total="totalF" :current.sync="pageF" :page-size="pageSizeF" show-total show-elevator @on-change="loadTrafficDetail" />
    </div> -->
  </div>
</template>
<script>
  import {
    getTrafficDetail,
	getflowinfo
  } from '@/api/server/card'
  export default {
	props: {
	 form: {
	   imsi: "",
	   iccid: "",
	   msisdn: "",
	 },
	 imsi:"",
	},
    data() {
      return {
        pageF: 1,
        loadingF: false,
        totalF: 0,
        pageSizeF: 5,
        columnsF: [{
            title: this.$t('support.date'),
            key: 'date',
            align: 'center',
			render: (h, params) => {
			  const row = params.row;
			  const text = row.activeTime+"  -  "+row.expireTime
			  return h('label', text);
			},
          },
          {
            title: this.$t('support.used_flow'),
            key: 'flowByteTotal',
            align: 'center'
          },
          {
            title: this.$t('support.mealname'),
            key: 'packageName',
            align: 'center',
			render: (h, params) => {
				const row = params.row
				var text = this.$i18n.locale === 'zh-CN' ? row.packageName : this.$i18n.locale === 'en-US' ?
				row.packageNameEn : ''
				return h('label', text)
			}
          }
        ],
        tableDataF: []
      }
    },
    methods: {
      loadTrafficDetail: function(page) {
        this.loadingF = true;
        this.pageF = page;
        getflowinfo({
          // key: this.imsi,
		  iccid :this.form.iccid,
		  imsi :this.form.imsi,
		  msisdn :this.form.msisdn
          // pageNumber: page,
          // pageSize: this.pageSizeF,
          // type: 1
        }).then(res => {
          if (res && res.code == '0000') {
            var data = res.data;
            this.tableDataF = data;
            this.totalF = data.totalCount;
          } else {
            throw res
          }
        }).catch((err) => {
          console.log(err)
        }).finally(() => {
          this.loadingF = false
        })
      },
    },
    mounted: function() {
		this.loadTrafficDetail(1);
	},
    watch: {
      imsi(newVal, oldVal) {
        this.loadTrafficDetail(1);
      }
    }
  };
</script>

<style scoped>

</style>
