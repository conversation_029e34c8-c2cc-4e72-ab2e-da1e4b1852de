<!-- 位置更新记录 -->

<template>
	<div style="padding: 0 16px">
		<div>
			<span style="font-weight:bold;">{{$t('support.position')}}：{{localName}}</span>
		</div>
		<!-- <div v-if="this.$i18n.locale === 'en-US'">
		<span style="font-weight:bold;">{{$t('support.position')}}：{{localNameEn}}</span>
	</div>
	<div v-else>
		<span style="font-weight:bold;">{{$t('support.position')}}：{{localName}}</span>
	</div> -->
		</br>
		<div>
			<Table :columns="columnsU" :data="tableDataU" :ellipsis="true" :loading="loadingU" max-height="500"></Table>
		</div>
		<div class="table-botton" style="margin-top:15px">
			<Page :total="totalU" :current.sync="pageU" :page-size="pageSizeU" show-total show-elevator
				@on-change="loadRecords" />
		</div>
	</div>
</template>
<script>
	import {
		getUpdateRecordsH
	} from '@/api/server/card';
	export default {
		props: {
			imsi: String,
			local: String,
			localEn: String,
			iccid: String
		},
		data() {
			return {
				localName: '',
				localNameEn: '',
				pageU: 1,
				loadingU: false,
				totalU: 0,
				pageSizeU: 5,
				tempList: [],
				columnsU: [{
						title: this.$t('support.Report_time'),
						key: 'reportTime',
						align: 'center',
						minWidth: 50,
					},
					{
						title: this.$t('support.Report_address'),
						key: 'mcc',
						align: 'center',
						render: (h, params) => {
							const row = params.row
							var text;
							if (this.$i18n.locale === 'zh-CN'){
								if(row.mcc == "未知") {
									text = ""
								}else{
									text = row.mcc
								}
							}else{
								if(row.mccEn == "UNKNOW") {
									text = ""
								}else{
									text = row.mccEn
								}
							}
							return h('label', text)
						}
					},
					{
						title: this.$t('support.Activationmethod'),
						key: 'activeType',
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							const text = row.activeType == '1' ? this.$t('support.Automatic') : row.activeType ==
								'2' ? this.$t('support.Manual') : '-';
							return h('label', text)
						}
					},
					{
						title: this.$t('support.operator'),
						key: 'operatorName',
						align: 'center',
						tooltip: true,
						render: (h, params) => {
							const row = params.row;
							const text = row.operatorName == "unknown" ? "" : row.operatorName;
							return h('label', text)
						}
					},
					{
						title: this.$t('support.network'),
						key: 'netType',
						align: 'center',
						render: (h, params) => {
							const row = params.row;
							const text =
								row.netType == '1' ? "3G" :
								row.netType == '2' ? "2G" :
								row.netType == '3' ? "wifi" :
								row.netType == '4' ? "2G" :
								row.netType == '5' ? "3G" :
								row.netType == '6' ? "4G" :
								'';
							return h('label', text)
						}
					},
				],
				tableDataU: []
			}
		},
		methods: {
			loadRecords(page) {
				this.localName = this.local;
				this.localNameEn = this.localEn;
				this.loadingU = true;
				this.pageU = page;
				getUpdateRecordsH({
					imsi: this.imsi,
					pageNumber: page,
					pageSize: this.pageSizeU,
					iccid: this.iccid,
					expiredData: 1
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						this.tableDataU = data.records;
						this.totalU = data.totalCount;
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					this.loadingU = false
				})
			},
		},
		mounted: function() {
			this.loadRecords(1);
		},
		watch: {
			imsi(newVal, oldVal) {
				this.loadRecords(1);
			}
		}
	};
</script>


<style scoped>

</style>