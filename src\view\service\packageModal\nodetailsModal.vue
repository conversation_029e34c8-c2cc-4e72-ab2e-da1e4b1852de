<!-- 套餐详情 -->
<template>
	<div style="padding: 0 16px;">
		<Form ref="detailsObj" :model="detailsObj" :label-width="this.$i18n.locale==='zh-CN' ?150:140">
			<Row>
				<Col span="8">
				<FormItem v-if="this.$i18n.locale === 'en-US'" :label="$t('support.mealname')">
					<Input v-model="detailsObj.packageNameEn" readonly></Input>
				</FormItem>
				<FormItem v-else :label="$t('support.mealname')">
					<Input v-model="detailsObj.packageName" readonly></Input>
				</FormItem>
				</Col>
				<Col span="8">
				<FormItem :label="$t('deposit.mealId')">
					<Input v-model="detailsObj.packageId" readonly></Input>
				</FormItem>
				</Col>
				<Col span="8">
				<FormItem>
					<Button type="primary" :disabled="$route.query.isBak == true" @click="showMoreInfo">{{$t('support.informationQuery')}}</Button>
				</FormItem>
				</Col>
			</Row>
			<Row>
				<Col span="8">
				<FormItem :label="$t('support.Periodtype')">
					<Select v-model="detailsObj.periodUnit" disabled>
						<Option v-for="item in periodUnitList" :value="item.value" :key="item.value">{{ item.label }}
						</Option>
					</Select>
				</FormItem>
				</Col>
				<Col span="8">
				<FormItem :label="$t('support.Continuouscycle')">
					<Input v-model="detailsObj.keepPeriod" readonly></Input>
				</FormItem>
				</Col>
			</Row>
			<Row>
				<Col span="8">
				<FormItem :label="$t('support.time')">
					<Input v-model="detailsObj.expireTime" readonly></Input>
				</FormItem>
				</Col>
				<Col span="8">
				<FormItem :label="$t('deposit.mealprice')">
					<Input v-model="detailsObj.amount" readonly></Input>
				</FormItem>
				</Col>
				<Col span="8">
				<FormItem :label="$t('deposit.currency')">
					<Select v-model="detailsObj.currencyCode" disabled>
						<Option v-for="item in currencyList" :value="item.value" :key="item.value">{{ item.label }}
						</Option>
					</Select>
				</FormItem>
				</Col>
			</Row>
			<div v-if="obj.packageStatus != '1' && obj.packageStatus != '5'">
				<Row>
					<Col span="8">
					<FormItem :label="$t('order.ActivationTime')">
						<Input v-model="detailsObj.activeTime" readonly></Input>
					</FormItem>
					</Col>
					<Col span="8">
					<FormItem :label="$t('support.Activationtype')">
						<Select v-model="detailsObj.activeCategory" disabled>
							<Option v-for="item in activeCategoryList" :value="item.value" :key="item.value">
								{{ item.label }}</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="8">
					<FormItem :label="$t('support.Usedflow')">
						<Input v-model="detailsObj.usedFlowBytes" readonly>
						<!-- <span slot="append">MB</span> -->
						</Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="8">
					<FormItem v-if="this.$i18n.locale === 'en-US'" :label="$t('order.LocationUpdate')">
						<Input v-model="detailsObj.currentLocationEn" readonly></Input>
					</FormItem>
					<FormItem v-else :label="$t('order.LocationUpdate')">
						<Input v-model="detailsObj.currentLocation" readonly></Input>
					</FormItem>
					</Col>
					<Col span="8">
					<FormItem :label="$t('support.Report_time')">
						<Input v-model="detailsObj.reportTime" readonly></Input>
					</FormItem>
					</Col>
					<Col span="8">
					<FormItem :label="$t('support.DataUsedDay')">
						<Input v-model="detailsObj.todayUsedFlow" readonly>
						<!-- <span slot="append">MB</span> -->
						</Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="8">
					<FormItem :label="$t('support.DataRestrictionType')" v-if="obj.packageType!='3'">
						<Select v-model="detailsObj.flowLimitType" disabled :style="getStyle(1)">
							<Option value="1">{{$t('support.DataRestrictionCycle')}}</Option>
							<Option value="2">{{$t('support.DataRestrictionSingle')}}</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="8">
					<FormItem :label="$t('support.InternetStatus')">
						<Select v-model="detailsObj.surfStatus" disabled v-if="obj.packageType!='3'">
							<Option value="1">{{$t('support.Normal')}}</Option>
							<Option value="2">{{$t('support.RestrictedSpeed')}}</Option>
						</Select>
						<Select v-model="detailsObj.surfStatus" disabled v-else>
							<Option value="1">{{$t('flow.Normal')}}</Option>
							<Option value="2">{{$t('flow.Cardcycle')}}</Option>
							<Option value="3">{{$t('flow.Stopdatalimit')}}</Option>
							<Option value="4">{{$t('flow.Restrictedspeed')}}</Option>
							<Option value="5">{{$t('flow.Totallimitcard')}}</Option>
							<Option value="6">{{$t('flow.Datapoollimit')}}</Option>
							<Option value="7">{{$t('flow.stoppoollimit')}}</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="8">
					<FormItem :label="$t('support.hightDataCap')">
						<Input v-model="detailsObj.flowLimitSumStr" readonly>
						<!-- <span slot="append">MB</span> -->
						</Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="8">
					<FormItem :label="$t('support.ControlLogicLimit')">
						<Select v-model="detailsObj.controlLogic" disabled v-if="obj.packageType!='3'" :style="getStyle(2)">
							<Option value="1">{{$t('support.RestrictedSpeedLimit')}}</Option>
							<Option value="2">{{$t('support.ReleaseAfterLimit')}}</Option>
						</Select>
						<Select v-model="detailsObj.controlLogic" disabled v-else :style="getStyle(2)">
							<Option value="1">{{$t('flow.Continuelimit')}}</Option>
							<Option value="2">{{$t('flow.speedlimit')}}</Option>
							<Option value="3">{{$t('flow.Stoplimit')}}</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="8">
					<FormItem :label="$t('support.UsedAddonPack')">
						<Input v-model="detailsObj.refuelUsedFlow" readonly>
						<!-- <span slot="append">MB</span> -->
						</Input>
					</FormItem>
					</Col>
					<Col span="8">
					<FormItem :label="$t('support.ToppedAddonPack')">
						<Input v-model="detailsObj.refuelRechargeFlow" readonly>
						<!-- <span slot="append">MB</span> -->
						</Input>
					</FormItem>
					</Col>
				</Row>
				<Row v-show="detailsObj.isUsing">
					<Col span="8">
					<FormItem :label="$t('support.Available')">
						<Tooltip placement="top">
							<Input v-model="detailsObj.operatorName" readonly>
							</Input>
							<div slot="content" style="white-space: normal;">
								{{detailsObj.operatorNametip}}
							</div>
						</Tooltip>
					</FormItem>
					</Col>
					<Col span="8">
					<FormItem label="APN">
						<Tooltip placement="top">
							<Input v-if="$i18n.locale === 'zh-CN'" v-model="detailsObj.apnZh" readonly>
							</Input>
							<Input v-else v-model="detailsObj.apnEn" readonly>
							</Input>
							<div slot="content" v-if="$i18n.locale === 'zh-CN'" style="white-space: normal;">
								{{detailsObj.apnZhtip}}
							</div>
							<div slot="content" v-else style="white-space: normal;">
								{{detailsObj.apnEntip}}
							</div>
						</Tooltip>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="8" v-if="detailsObj.supportHotspot">
					<FormItem :label="$t('support.isSupportHot')">
						<Select v-model="detailsObj.supportHotspot" disabled>
							<Option value="1">{{$t('order.yes')}}</Option>
							<Option value="2">{{$t('order.no')}}</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="8" v-if="shouldShowUpccRate">
					<FormItem :label="$t('support.internetTemplateSpeed')">
						<Input v-model="detailsObj.upccRate" readonly></Input>
					</FormItem>
					</Col>
				</Row>
				<Row>
					<Col span="24">
					<Tabs :value="checked" @on-click="tagChange">
						<TabPane :label="$t('support.usedetails')" name="trafficInfo">
							<div v-if="checked == 'trafficInfo'">
								<div style="display: flex;width: 100%;">
									<Table style="display: flex;width: 100%;" :columns="columnsT" :data="tableData1" :ellipsis="true"
										:loading="loading" max-height="500">
									</Table>
									<div style="margin-left: 5px;">
										<div>
											<Button long @click="Flowdetails()">{{$t('support.Flowdetails')}}</Button>
										</div>
										<div style="margin-top: 25px;">
											<Button :disabled="$route.query.isBak == true" long @click="CDRdetailsBtn()">{{$t('support.cdrDetails')}}</Button>
										</div>
									</div>

								</div>
								<Page :total="total" :current.sync="currentpageT" :page-size="pageSize" show-total show-elevator
									@on-change="loadTraffic" style="margin: 15px 0;" />
							</div>
						</TabPane>
						<TabPane :label="$t('support.VIMSILocation')" name="vimsiInfo">
							<div v-if="checked == 'vimsiInfo'">
								<Table :columns="columnsV" :data="tableData" :ellipsis="true" :loading="loading"
									max-height="500">
								</Table>
								<Page :total="total" :current.sync="currentpageV" :page-size="pageSize" show-total
									show-elevator @on-change="loadVimsi" style="margin: 15px 0;" />
							</div>
						</TabPane>
						<TabPane :label="$t('support.IMSIdetails')" name="vimsiAllocation">
							<div v-if="checked == 'vimsiAllocation'">
								<Table :columns="columnsA" :data="tableData" :ellipsis="true" :loading="loading"
									max-height="500">
									<template slot-scope="{ row, index }" slot="flow">
										<a v-if="detailsObj.packageStatus == '2' || detailsObj.packageStatus == '6'"
											href="#" type="primary" size="small"
											@click="searchCardFlow('2',row.imsi)">{{$t('support.cardtraffic')}}</a>
										<a v-else href="#" type="primary" size="small" disabled="">{{$t('support.cardtraffic')}}</a>
									</template>
								</Table>
								<Page :total="total" :current.sync="currentpageA" :page-size="pageSize" show-total
									show-elevator @on-change="loadAllocation" style="margin: 15px 0;" />
							</div>
						</TabPane>
						<TabPane v-if="appTableData.length != 0" :label="$t('support.targetedAppDetails')" name="AppInfo">
							<div v-if="checked == 'AppInfo'">
								<Table border :columns="columnsApp" :data="appTableData" :ellipsis="true" :loading="loading"
									max-height="500" :span-method="handleSpan">
								</Table>
							</div>
						</TabPane>
					</Tabs>
					</Col>
				</Row>
			</div>
		</Form>
		<Spin size="large" fix v-if="spinShow"></Spin>
		<Modal :title="$t('support.cardflow')" v-model="cardFlowFlag" :footer-hide="true" :mask-closable="false" width="400px">
			<Form label-position="left" ref="cardFlow" :model="cardFlow" :label-width="130">
				<FormItem :label="$t('support.packageflow')">
					<Input v-model="cardFlow.totalFlow" style="width: 200px;" readonly></Input>
				</FormItem>
				<FormItem :label="$t('support.remainingflow')">
					<Input v-model="cardFlow.surplusFlow" style="width: 200px;" readonly></Input>
				</FormItem>
				<FormItem :label="$t('support.Usedtraffic')">
					<Input v-model="cardFlow.usedFlow" style="width: 200px;" readonly></Input>
				</FormItem>
			</Form>

		</Modal>
		<Modal :title="$t('support.cardflow')" v-model="cardFlowFlagEn" :footer-hide="true" :mask-closable="false"
			width="500px">
			<Form label-position="left" ref="cardFlow" :model="cardFlow" :label-width="270">
				<FormItem :label="$t('support.packageflow')">
					<Input v-model="cardFlow.totalFlow" style="width: 200px;" readonly></Input>
				</FormItem>
				<FormItem :label="$t('support.remainingflow')">
					<Input v-model="cardFlow.surplusFlow" style="width: 200px;" readonly></Input>
				</FormItem>
				<FormItem :label="$t('support.Usedtraffic')">
					<Input v-model="cardFlow.usedFlow" style="width: 200px;" readonly></Input>
				</FormItem>
			</Form>
		</Modal>
		<a ref="downloadLink" style="display: none"></a>
		<Modal :title="$t('support.Flowdetails')" v-model="flowdetailsFlag" :mask-closable="true" @on-cancel="cancelModal" width="900px">
			<Table :columns="columnFl" :data="tableDataFlow" :ellipsis="true" :loading="Flowloading" max-height="500">
			</Table>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelModal">{{$t('support.close')}}</Button>
				<Button type="primary" @click="exportFlow" :loading="downloading">{{$t('stock.exporttb')}}</Button>
			</div>
		</Modal>
		<!-- CDR详情 -->
		<Modal :title="$t('support.cdrDetails')" v-model="CDRdetailsFlag" :footer-hide="true" :mask-closable="false" width="1600" @on-cancel="cancelModal">
			<Form label-position="left" ref="cardFlow" :model="cardFlow" :label-width="70">
				<Row>
					<Col span="6">
					<FormItem :label="$t('support.coverageTime')">
						<Select v-model="CDRCoverageTime" clearable>
							<Option v-for="item in CDRCoverageTimeList" :value="item" :key="item">{{ item }}
							</Option>
						</Select>
					</FormItem>
					</Col>
					<Col span="4">
						<Button style="margin-left: 20px" type="primary" @click="CDRdetails()">{{$t('flow.select')}}</Button>
					</Col>
				</Row>
			</Form>
			<Table :columns="columnCDR" :data="tableDataCDR" :ellipsis="true" :loading="CDRloading" max-height="500">
			</Table>
			<Page :total="CDRtotal" :current.sync="CDRcurrentpage" :page-size="CDRpageSize" show-total show-elevator
				@on-change="CDRChange" style="margin: 15px 0;" />
		</Modal>
		<!-- 导出提示 -->
		<Modal v-model="exportModal" :mask-closable="true" @on-cancel="cancel">
			<div style="align-items: center;justify-content:center;display: flex;">
				<Form label-position="left" :label-width="150" style=" align-items: center;justify-content:center;">
					<h1 style="text-align: center;margin-bottom: 10px;">{{$t('exportMS')}}</h1>
					<FormItem :label="$t('exportID')">
						<span style="width: 100px;">{{taskId}}</span>
					</FormItem>
					<FormItem :label="$t('exportFlie')">
						<span>{{taskName}}</span>
					</FormItem>
					<span style="text-align: left;">{{$t('downloadResult')}}</span>
				</Form>
			</div>

			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancel">{{$t('common.cancel')}}</Button>
				<Button type="primary" @click="Goto">{{$t('Goto')}}</Button>
			</div>
		</Modal>
		<!-- 流量层级弹窗 -->
		<Modal :title="$t('flow.Details')" v-model="moreInfoFlag" :footer-hide="true" :mask-closable="false" width="900">
			<Form ref="tableDataMoreInfo" :model="tableDataMoreInfo" :label-width="this.$i18n.locale==='zh-CN' ?150:140">
				<div>
					<Row style="display: flex; flex-wrap: wrap; flex-direction: column;">
						<div v-for="(item, index1) in tableDataMoreInfo.consumptionList" :key="index1"
							style="display: flex; flex-wrap: wrap; margin-bottom: 10px;">
							<Col span="12">
							<FormItem :label="$t('support.usage2')">
								<Input :value="item.consumption" style="width: 200px;" readonly></Input>
							</FormItem>
							</Col>
							<Col span="12">
							<FormItem :label="$t('support.speedTemplate')">
								<Input :value="item.templateName" style="width: 200px;" readonly></Input>
							</FormItem>
							</Col>
						</div>
					</Row>
				</div>
				<Row>
					<div v-for="(item, index1) in tableDataMoreInfo.noLimitTemplate" :key="index1"
						style="display: flex; flex-wrap: wrap; margin-bottom: 10px;">
						<Col span="12">
						<FormItem :label="$t('support.unlimitedUsageTemplate')" prop="noLimitTemplateId">
							<Input style="width: 200px;" readonly :value="item.templateName"></Input>
						</FormItem>
						</Col>
					</div>
				</Row>
			</Form>
		</Modal>
	</div>
</template>

<script>
	import {
		getUpdateRecordsV,
		getTrafficDetail,
		getAllocationV,
		searchCardFlow,
		flowDetail,
		exportFlow,
		getAppInfo,
		getPurchasedDetail,
		getCDR,
		getConsumption,
		getCDRCoverageTime
	} from '@/api/server/card';
import { Button } from 'iview';
	const math = require('mathjs');
	export default {
		props: {
			obj: Object,
		},
		data() {
			return {
				cardFlowFlag: false, //流量查询标识
				cardFlowFlagEn: false,
				flowdetailsFlag: false,
				Flowloading: false,
				exportModal: false,
				downloading: false,
				taskId: '', //任务Id
				taskName: '', //任务名称
				Flowtotal: 0,
				FlowpageSize: 5,
				Flowpage: 1,
				CDRtotal: 0,
				CDRpageSize: 10,
				CDRpage: 1,
				CDRcurrentpage: 1,
				CDRCoverageTime:'',
				CDRCoverageTimeList:[],
				cardFlow: {
					totalFlow: '',
					surplusFlow: '',
					usedFlow: ''
				},
				detailsObj: {
					packageName: '', //套餐名称
					packageNameEn: '',//套餐名称英文
					periodUnit: '', //周期类型
					keepPeriod: '', //持续周期
					expireTime: '', //过期时间
					amount: '', //套餐价格
					currencyCode: '', //币种
					activeTime: '', //使用时间
					activeCategory: '', //激活类型
					currentLocation: '', //最新位置
					currentLocationEn: '',
					reportTime: '', //上报时间
          supportHotspot: '',//当前上网是否支持热点
          upccRate: '',//当前模板上网速度
				},
				periodUnitList: [{
						value: '1',
						label: this.$t("buymeal.hour")
					},
					{
						value: '2',
						label: this.$t("buymeal.day")
					},
					{
						value: '3',
						label: this.$t("buymeal.month")
					},
					{
						value: '4',
						label: this.$t("buymeal.year")
					}
				],
				activeCategoryList: [{
						value: '2',
						label: this.$t('support.Vcard')
					},
					{
						value: '1',
						label: this.$t('support.Hcard')
					}
				],
				currencyList: [{
						value: '156',
						label: this.$t('support.CNY')
					},
					{
						value: '840',
						label: this.$t('support.USD')
					},
					{
						value: '344',
						label: this.$t('support.HKD')
					}
				],
				checked: 'trafficInfo',
				total: 0,
				tableData: [],
				appTableData: [],
				tableData1: [],
				tableDataFlow: [],
				loading: false,
				pageSize: 5,
				currentpageT: 1,
				currentpageV: 1,
				currentpageA: 1,
				columnsT: [],
				columnsV: [],
				columnsA: [],
				columnFl: [],
				columnsApp: [],
				packageUniqueId: '',
				spinShow: false,
				moreInfoFlag:false,
				columnMoreInfo:[],
				tableDataMoreInfo:{
					noLimitTemplate:[],
					consumptionList:[]
				},
				MoreInfoLoading:false,
				CDRdetailsFlag:false,
				columnCDR:[{
						title: "himsi",
						key: 'himsi',
						align: 'center',
						width: 155,
						tooltip: true,
					},
					{
						title: "IMSI_with_usage",
						key: 'imsi',
						align: 'center',
						width: 155,
						tooltip: true,
					},
					{
						title: "start_time",
						key: 'startTime',
						align: 'center',
						width:160,
						tooltip: true,
					},
					{
						title: "data_vol_total_kb",
						key: 'dataVolTotal',
						align: 'center',
						width:150
					},
					{
						title: "msisdn",
						key: 'msisdn',
						align: 'center',

					},{
						title: "mcc",
						key: 'mcc',
						align: 'center',
						width: 80

					},{
						title: "mnc",
						key: 'mnc',
						align: 'center',
						width: 80

					},{
						title: "tapcode",
						key: 'tapcode',
						align: 'center',
						maxWidth: 100,
						tooltip: true,
					},{
						title: "sgsn_address",
						key: 'sgsnAddress',
						align: 'center',
						render: (h, params) => {
							return h('span', params.row.sgsnAddress ? params.row.sgsnAddress : '-')
						},
						width: 120,
						tooltip: true,
					},{
						title: "data_vol_uplink_kb ",
						key: 'dataVolUplink',
						align: 'center',
						width:165

					},{
						title: "data_vol_downlink_kb",
						key: 'dataVolDownlink',
						align: 'center',
						width:165
					},{
						title: "apn",
						key: 'apn',
						align: 'center',
						maxWidth: 90,
						tooltip: true,
					},],
				tableDataCDR:[],
				CDRloading:false,
			}
		},
		methods: {
			// 获取详情数据
			goDetailInfo: function(packageUniqueId) {
				this.spinShow = true
				getPurchasedDetail({
					pageNumber: -1,
					pageSize: -1,
					imsi: this.$route.query.imsi,
					iccid: this.$route.query.iccid,
					expiredData: 1,
					cooperationMode: sessionStorage.getItem("cooperationMode"),
					packageUniqueId: packageUniqueId,
				}).then(res => {
					if (res && res.code == '0000') {
						this.spinShow = false
						this.detailsObj = Object.assign({}, res.data.records[0]);
						// 超出长度省略展示
						this.detailsObj.operatorNametip=this.detailsObj.operatorName
						this.detailsObj.operatorName=this.detailsObj.operatorName && this.detailsObj.operatorName.length>20 ?
						this.detailsObj.operatorName.substring(0,20)+'...':this.detailsObj.operatorName

						this.detailsObj.apnEntip= this.detailsObj.apnEn
						this.detailsObj.apnEn=this.detailsObj.apnEn && this.detailsObj.apnEn.length>20 ?
						this.detailsObj.apnEn.substring(0,20)+'...':this.detailsObj.apnEn

						this.detailsObj.apnZhtip= this.detailsObj.apnZh
						this.detailsObj.apnZh=this.detailsObj.apnZh && this.detailsObj.apnZh.length>20 ?
						this.detailsObj.apnZh.substring(0,20)+'...':this.detailsObj.apnZh
					} else {
						throw res
					}
				}).catch((err) => {
					this.spinShow = false
					console.log(err)
				}).finally(() => {
					this.spinShow = false
				})
			},
			geiDetails(obj) {
				// obj.amount = Number(math.format(Number(obj.amount) / 100, 14)).toFixed(4);
				this.packageId = obj.packageId
				this.packageUniqueId = obj.packageUniqueId
				this.goDetailInfo(obj.packageUniqueId)
				this.$nextTick(()=>{
					this.loadApp()
				})
				this.getNewReportTime();
				if (obj.packageStatus != '1') {
					this.currentpageT = 1;
					this.currentpageV = 1;
					this.loadColumns();
					this.loadTraffic(this.currentpageT);
					this.checked = 'trafficInfo';
				}
			},
			getNewReportTime() {
				getUpdateRecordsV({
					imsi: this.$route.query.imsi,
					packageUniqueId: this.obj.packageUniqueId,
					pageNumber: 1,
					pageSize: 1,
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						if (data.records != null && data.records.length > 0) {
							var dateHoV = this.detailsObj.reportTime;
							var dateV = data.records[0].reportTime;
							this.detailsObj.reportTime = dateV >= dateHoV ? dateV : dateHoV;
						}
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {

				})
			},
			//查询流量详情
			Flowdetails() {
				let packageUniqueId = this.obj.packageUniqueId
				flowDetail(packageUniqueId).then(res => {
					if (res && res.code == '0000') {
						this.tableDataFlow = res.data;
						this.flowdetailsFlag = true
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			async CDRdetailsBtn () {
				await getCDRCoverageTime().then(res => {
					if (res && res.code == '0000') {
						this.CDRCoverageTimeList = res.data;
					}
				}).catch((err) => {
					console.log(err)
				})
				this.CDRdetails();
			},
			async	CDRdetails(){

				let data = 	{
					"coverHour": this.CDRCoverageTime,
					"packageUniqueId": this.obj.packageUniqueId,
					"pageSize": this.CDRpageSize,
					"pageNum": this.CDRcurrentpage
				}
				await	getCDR(data).then(res => {
					if (res && res.code == '0000') {
						this.tableDataCDR = res.data.records;
						this.CDRdetailsFlag = true;
						this.CDRtotal = Number(res.data.totalCount);
					}
				}).catch((err) => {
					console.log(err)
				})
			},
			CDRChange(page){
				this.CDRcurrentpage = page
				this.CDRdetails()
			},
			cancelModal() {
				this.flowdetailsFlag = false
        this.CDRCoverageTime = ''
			},
			cancel() {
				this.exportModal = false
			},
			//流量详情导出
			exportFlow() {
				var packageUniqueId = this.obj.packageUniqueId
				var userId = this.$store.state.user.userId
				this.downloading = true
				exportFlow({
					packageUniqueId,
					userId
				}).then(res => {
					if (res && res.code == '0000') {
						this.exportModal = true
						this.taskId = res.data.taskId
						this.taskName = res.data.taskName
					}
					this.downloading = false
				}).catch((err) => {
					this.downloading = false
					this.exportModal = false
				})
			},
			// 动态改变下拉框宽度
			getStyle(id) {
				if (id === 1 && this.$i18n.locale === 'en-US') {
					return {
						width: '240px',
					}
				}
				if (id === 2 && this.$i18n.locale === 'en-US') {
					return {
						width: '240px',
					}
				}
			},
			//定向应用上网详情-同应用组合并列——类型/总流量/当前上网状态
			handleSpan ({ row, column, rowIndex, columnIndex }) {
				if (columnIndex === 1 || columnIndex === 2 || columnIndex === 4) {
					let ststusX = row.mergeCol == 0 ? 0:row.mergeCol;
					let ststusY = row.mergeCol == 0 ? 0:1;
				    return [ststusX, ststusY];
				}

			},
			assembleData(data){
			    let groupIds = []
			    //筛选出不重复的 groupId值,将其放到 groupIds数组中
			    data.forEach(e => {
			        if(!groupIds.includes(e.groupId)){
			            groupIds.push(e.groupId)
			        }
			    })
			    let groupIdNums = []
			    //将groupIds数组中的 groupId值设置默认合并0个单元格,放到 groupIdNums中
			    groupIds.forEach(e => {
			        groupIdNums.push({groupId:e,num:0})
			    })
			    //计算每种 groupId值所在行需要合并的单元格数
			    data.forEach(e => {
			        groupIdNums.forEach(n => {
			            if(e.groupId == n.groupId){
			                n.num++
			            }
			        })
			    })
			    //将计算后的合并单元格数整合到 data中
			    data.forEach(e => {
			        groupIdNums.forEach(n => {
			            if(e.groupId == n.groupId){
			                if(groupIds.includes(e.groupId)){
			                    e.mergeCol = n.num
			                    //删除已经设置过的值(防止被合并的单元格进到这个 if 语句中)
			                    groupIds.splice(groupIds.indexOf(n.groupId),1)
			                    // groupIds.splice(groupIds.indexOf(n.totleFlow),1)

			                } else {
			                    //被合并的单元格设置为 0
			                    e.mergeCol = 0
			                }
			            }
			        })
			    })
			    //将整理后的数据交给表格渲染
			    this.appTableData = data
			},
			Goto() {
				this.$router.push({
					path: '/taskList',
					query: {
						taskId: encodeURIComponent(this.taskId),
						fileName: encodeURIComponent(this.taskName),
					}
				})
				this.exportModal = false
			},
			tagChange(name) {
				this.resetState();
				this.checked = name;
				if ('trafficInfo' === name) {
					this.loadTraffic(this.currentpageT);
				}
				if ('vimsiInfo' === name) {
					this.loadVimsi(this.currentpageV);
				}
				if ('vimsiAllocation' === name) {
					this.loadAllocation(this.currentpageA);
				}
			},
			loadColumns() {
				this.columnsT = [{
						title: 'IMSI',
						key: 'imsi',
						align: 'center',
						minWidth: 120
					}, {
						title: this.$t('support.Cardtype'),
						key: 'cardType',
						align: 'center',
						minWidth: 120,
						// render: (h, params) => {
						//   const row = params.row;
						//   const text = row.internetType == '1' ? 'H' : row.internetType == '2' ? 'V' : '未知';
						//   return h('label', text)
						// }
					},
					{
						title: this.$t('support.date'),
						key: 'statTime',
						align: 'center',
						minWidth: 130
					},
					{
						title: this.$t('support.used_flow'),
						key: 'flowByteTotal',
						align: 'center',
						minWidth: 120,
					}
				];
				this.columnsV = [{
						title: this.$t('support.VIMSIphone'),
						key: 'imsi',
						align: 'center',
					},
					{
						title: this.$t('support.TimeLocation'),
						key: 'reportTime',
						align: 'center',
					},
					{
						title: this.$t('support.Location'),
						key: 'mcc',
						align: 'center',
						render: (h, params) => {
							const row = params.row
							var text = this.$i18n.locale === 'zh-CN' ? row.mcc : this.$i18n.locale === 'en-US' ?
								row.mccEn : ''
							return h('label', text)
						}
					}
				];
				this.columnsA = [{
						title: this.$t('support.VIMSIphone'),
						key: 'imsi',
						align: 'center',
					},
					{
						title: this.$t('support.imsiType'),
						key: 'internetType',
						align: 'center',
						render: (h, params) => {
							const row = params.row
							var text =  row.internetType == '1' ? this.$t('support.Hcard') : row.internetType == '2' ? this.$t('support.Vcard') : ""
							return h('label', text)
						}
					},
					{
						title: this.$t('support.useCountry'),
						key: 'mcc',
						align: 'center',
						tooltip: true,
						render: (h, params) => {
							const row = params.row
							var text = this.$i18n.locale === 'zh-CN' ? row.mcc : this.$i18n.locale === 'en-US' ?
								row.mccEn : ''
							return h('label', text)
						}
					},
					{
						title: this.$t('support.imsiResource'),
						key: 'supplierName',
						align: 'center',
						tooltip: true,
						render: (h, params) => {
						  const row = params.row;
						  const text = row.supplierName ? row.supplierName : '\\';
						  return h('label', text)
						}
					},
				];
				this.columnsApp = [{
						title: this.$t('directionalApp.application'),
						key: 'appName',
						align: 'center',
					},
					{
						title: this.$t('deposit.charge_type'),
						key: 'type',
						align: 'center',
						render: (h, params) => {
							const row = params.row
							var text =  row.type == '1' ? this.$t('flow.Restricted') : row.type == '2' ? this.$t('directionalApp.freeFlow') : ""
							return h('label', text)
						}
					},
					{
						title: this.$t('flow.totalusage'),
						key: 'totleFlow',
						align: 'center',
					},
					{
						title: this.$t('support.Usedflow'),
						key: 'usedFlow',
						align: 'center',
					},
					{
						title: this.$t('fuelPack.onlinestatus'),
						key: 'surfStatus',
						align: 'center',
						render: (h, params) => {
							const row = params.row
							var text =  row.surfStatus == '1' ? this.$t('support.Normal') : row.surfStatus == '2' ? this.$t('flow.Restricted'): ""
							return h('label', text)
						}
					},
				];
				this.columnFl = [{
						title: 'IMSI',
						key: 'imsi',
						align: 'center',
						minWidth: 150
					},
					{
						title: this.$t('support.Cardtype'),
						key: 'cardType',
						align: 'center',
						minWidth: 120
					},
					{
						title: this.$t('support.date'),
						key: 'statTime',
						align: 'center',
						minWidth: 120
					},
					{
						title: this.$t('buymeal.Country'),
						key: 'countryCn',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row
							var text = this.$i18n.locale === 'zh-CN' ? row.countryCn.toString() : this.$i18n
								.locale === 'en-US' ? row.countryEn.toString() : ' '
							return h('label', text)
						}
					},
					{
						title: this.$t('support.operatorName'),
						key: 'operatorName',
						align: 'center',
						minWidth: 120
					},
					{
						title: this.$t('support.usageTotal'),
						key: 'flowByteTotal',
						align: 'center',
						minWidth: 120
					},
				]
			},
			//查询H/V卡流量
			searchCardFlow(type, imsi) {
				this.cardFlow = {
					totalFlow: '',
					surplusFlow: '',
					usedFlow: ''
				};
				;
				if (this.$i18n.locale === 'zh-CN') {
					this.cardFlowFlag = false;
				}
				if (this.$i18n.locale === 'en-US') {
					this.cardFlowFlagEn = false;
				}
				searchCardFlow({
					cardType: type,
					imsi: imsi,
					packageUniqueId: this.obj.packageUniqueId,
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						// this.cardFlow = data;
						this.cardFlow = {
							totalFlow: Number(Number(data.totalFlow) / 1024).toFixed(2),
							surplusFlow: Number(Number(data.surplusFlow) / 1024).toFixed(2),
							usedFlow: Number(Number(data.usedFlow) / 1024).toFixed(2)
						};
						if (this.$i18n.locale === 'zh-CN') {
							this.cardFlowFlag = true;
						}
						if (this.$i18n.locale === 'en-US') {
							this.cardFlowFlagEn = true;
						}
					} else {
						this.$Notice.error({
							title: this.$t("address.Operationreminder"),
							desc: res.msg
						})
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {

				})
			},
			loadTraffic(page) {
				this.currentpageT = page;
				this.loading = true;
				let packageUniqueId = this.obj.packageUniqueId
				let pageVO = {
					endTime: this.obj.expireTime,
					isPage: 0,
					pageNum: page,
					pageSize: this.pageSize,
					startTime: this.obj.activeTime
				}
				getTrafficDetail(packageUniqueId, pageVO).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						this.tableData1 = data;
						this.total = res.count;
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					this.loading = false;
				})
			},
			loadVimsi(page) {
				this.currentpageV = page;
				this.loading = true;
				getUpdateRecordsV({
					imsi: this.$route.query.imsi,
					packageUniqueId: this.obj.packageUniqueId,
					pageNumber: page,
					pageSize: this.pageSize,
					iccid: this.$route.query.iccid,
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						this.tableData = data.records;
						this.total = data.totalCount;
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					this.loading = false
				})
			},
			loadAllocation(page) {
				this.currentpageA = page;
				this.loading = true;
				getAllocationV({
					imsi: this.$route.query.imsi,
					packageUniqueId: this.obj.packageUniqueId,
					mcc: '460',
					pageNumber: page,
					pageSize: this.pageSize,
					iccid: this.$route.query.iccid,
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						this.tableData = data.records;
						this.total = data.totalCount;
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					this.loading = false
				})
			},
			loadApp() {
				this.loading = true;
				getAppInfo({
					imsi: this.$route.query.imsi,
					packageId: this.packageId,
					packageUniqueId: this.packageUniqueId,
				}).then(res => {
					if (res && res.code == '0000') {
						this.appTableData = res.data;
						this.assembleData(this.appTableData)
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					this.loading = false
				})
			},
			showMoreInfo(){
				getConsumption({
					packageUniqueId: this.packageUniqueId
				}).then(res => {
					if (res && res.code == '0000') {
						if(res.data.length>0){
							//取出非无限流量模板
							this.tableDataMoreInfo.consumptionList = res.data.filter((item) => {
								return item.consumption != '999999999999999999'
							})
							//根据数据consumption: "999999999999999999"，取出无限流量模板
							this.tableDataMoreInfo.noLimitTemplate = 	res.data.filter((item) => {
								return item.consumption == '999999999999999999'
							})
							this.moreInfoFlag = true
						}else{
							//提示没有数据
							this.$Message.info( this.$t("support.emptyData"))
						}

					}
				}).catch((err) => {
					console.log(err)
				})
			},
			// 添加重置状态的方法
			resetState() {
				this.currentpageT = 1;
				this.currentpageV = 1;
				this.currentpageA = 1;
				this.total = 0;
				this.tableData = [];
				this.tableData1 = [];
				this.checked = 'trafficInfo';
			},
		},
		mounted: function() {
		},
    computed: {
      shouldShowUpccRate() {
        return this.detailsObj.upccRate !== null && this.detailsObj.upccRate !== undefined && this.detailsObj.upccRate !== '';
      }
    },
		watch: {
			obj(newVal, oldVal) {
				this.geiDetails(newVal);
			}
		},
	};
</script>

<style scoped>
	.row-sty {
		padding: 0 10px;
		color: #878787;
		line-height: 38px;
		background-color: #f7f7f7;
		border: 1px solid #dcdee2;
		border-bottom: none;
	}
</style>
