<template>
	<div>
		<Card>
			<div class="search_head_i">
				<div class="search_box">
					<Button style="margin: 0 4px" @click="reBack">
						<Icon type="ios-arrow-back" />&nbsp;{{$t('support.back')}}
					</Button>
				</div>
			</div>
			<div>
				<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row, index }" slot="action">
						<a href="#" v-has="'view'" type="primary" size="small" style="margin-right: 10px"
							@click="packageModal(row)">{{$t('stock.details')}}</a>
						<a href="#" v-has="'recovery'" type="primary" size="small" style="margin-right: 10px"
							v-if="(row.packageStatus=='2'||row.packageStatus=='6')&& row.packageType!='3'"
							@click="recovery(row)">{{$t('support.Recycle')}}</a>
						<a href="#" v-has="'recovery'" type="primary" size="small" style="margin-right: 10px" disabled
							v-else>{{$t('support.Recycle')}}</a>
						<a type="primary" v-has="'change'" size="small" style="margin-right: 10px"
							@click="changeVIMSI(row)" v-if="row.packageStatus=='2'&&row.packageType!='3'">{{$t('support.ReplaceVIMSI')}}</a>
						<a href="#" v-has="'change'" type="primary" size="small" style="margin-right: 10px" disabled
							v-else>{{$t('support.ReplaceVIMSI')}}</a>
					</template>
					<template slot-scope="{ row, index }" slot="activeAt">
						<a :disabled="row.packageStatus !== '1'" href="#" type="primary" size="small"
							style="margin-right: 10px" @click="updateTime(row)">{{row.activeAt}}</a>
					</template>
				</Table>
			</div>
			<div class="table-botton" style="margin-top:15px">
				<Page :total="total" :page-size="pageSize" :current.sync="pageP" show-total show-elevator
					@on-change="goPage" />
			</div>
		</Card>
		<Modal :title="$t('packageInfo')" v-model="packageModalFlag" :footer-hide="true" :mask-closable="false" :width="this.$i18n.locale==='zh-CN' ?1000:1200">
			<noDetails :obj="obj"></noDetails>
		</Modal>
		<!-- 套餐生效时间修改 -->
		<Modal :title="$t('fuelPack.packageTtart')" v-model="updateTimeModal" width="18%" :mask-closable="false"
			@on-cancel="cancelTimeModal">
			<div style="margin-top: 5px">
				<Form ref="updateTimeObj" :model="updateTimeObj" :rules="updateTimerule" label-position="left"
					:label-width="80" style=" align-items: center;justify-content:center;">
					<FormItem :label="$t('fuelPack.SelectDate')+':'" prop="date">
						<DatePicker type="date" format="yyyy/MM/dd" v-model="updateTimeObj.date" @on-change="getTime"
							placement="bottom-start" :placeholder="$t('fuelPack.PleaseSelectDate')" :editable="true"></DatePicker>  
					</FormItem>
				</Form>
			</div>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:center;">
				<Button @click="cancelTimeModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" :loading="updateTimeloading" @click="submit">{{$t('common.determine')}}</Button>
			</div>
		</Modal>
	</div>
</template>

<script>
	import Details from './packageModal/detailsModal.vue'
	import noDetails from './packageModal/nodetailsModal.vue'
	import {
		searchPackageList,
		changeVIMSI,
		recoveryPackage,
		updateTime
	} from '@/api/server/card'
	const math = require('mathjs');
	export default {
		components: {
			Details,
			noDetails
		},
		data() {
			return {
				row: null,
				columns: [],
				action: [],
				tableData: [],
				updateTimeObj: {
					date: ''
				},
				loading: false,
				pageP: 1,
				total: 0,
				pageSize: 10,
				packageModalFlag: false,
				updateTimeModal: false,
				updateTimeloading: false,
				obj: {},
				type: '',
				updateTimerule: {
					date: [{
						type: 'date',
						required: true,
						message: this.$t('fuelPack.PleaseSelectDate')
					}]
				},
			}
		},
		computed: {

		},
		methods: {
			//页面初始化
			init: function() {
				// this.type = this.$route.query.type;
				this.columns = [{
						title: this.$t('support.mealname'),
						key: 'packageName',
						tooltip: true,
						align: 'center',
						minWidth: 150,
						render: (h, params) => {
							const row = params.row
							var text = this.$i18n.locale === 'zh-CN' ? row.packageName : this.$i18n.locale ===
								'en-US' ? row.packageNameEn : ' '
							return h('label', {
								style: {
									'word-break': 'break-word',
								}
							}, text)
						}
					},
					{
						title: this.$t('support.Packagestatus'),
						key: 'packageStatus',
						align: 'center',
						minWidth: 130,
						render: (h, params) => {
							const row = params.row;
							// [1.待使用 2.使用中 3.已使用 4.(未激活)已过期]
							// const text = row.packageStatus != '1' ? row.packageStatus == '-1' ? '已使用' : '未使用' : '使用中';
							var text = "";
							// 获取当前时间
							var timestamp= new Date().getTime();
							switch (row.packageStatus) {
								case "1":
									text = this.$t('support.Unuse');
									break;
								case "2":
									// 激活状态为2（已激活）或者6（激活中）时，判断是否过期，若过期 ，则展示为3已使用
									var date = new Date(row.expireTime);
									var time1 = date.getTime();
									if(timestamp > time1){
										text = this.$t('support.Used');
									}else{
										text = this.$t('support.activated');
									}
									break;
								case "3":
									text = this.$t('support.Used');
									break;
									// case "4":
									//   text = "已激活待计费";
									//   break;
								case "5":
									text = this.$t('support.Expired');
									break;
								case "6":
									// 激活状态为2（已激活）或者6（激活中）时，判断是否过期，若过期 ，则展示为3已使用
									var date = new Date(row.effectiveDay);
									var time1 = date.getTime();
									if(timestamp > time1){
										text = this.$t('support.Used');
									}else{
										text = this.$t('support.Activating');
									}
									break;
								default:
									text = " ";
							}
							text=(row.orderPackageStatus==="2" || row.orderPackageStatus==="3"
							|| row.orderPackageStatus==="5"|| row.orderPackageStatus==="6")
							&& row.orderStatus==="3" ? this.$t('support.Refunded') : text
							return h('label', text);
						},
					},
					{
						title: this.$t('support.Activationtype'),
						key: 'activeCategory',
						align: 'center',
						minWidth: 130,
						render: (h, params) => {
							const row = params.row;
							const text = row.activeCategory == '2' ? this.$t('support.Vcard') : row.activeCategory == '1' ? this.$t('support.Hcard') :
								'-';
							return h('label', text);
						},

					},
					{
						title: this.$t('support.Ordertime'),
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						key: 'orderTime',
						align: 'center',
						minWidth: 150
					},
					{
						title: this.$t('support.activeTime'),
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						key: 'activeTime',
						align: 'center',
						minWidth: 150
					},
					{
						title: this.$t('flow.expirationDate'),
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						key: 'expireTime',
						align: 'center',
						minWidth: 150
					},
					{
						title: this.$t('support.latestActivationDate'),
						key: 'effectiveDay',
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						align: 'center',
						minWidth: 160
					},
					{
						title: this.$t('support.Ordernumber'),
						tooltip: true,
						tooltipTheme: 'light',
						tooltipMaxWidth: 600,
						key: 'orderNumber',
						align: 'center',
						minWidth: 160
					},
					{
						title: this.$t('order.order_money'),
						key: 'amount',
						align: 'center',
						minWidth: 120,
						// render: (h, params) => {
						//   const row = params.row;
						//   const text = Number(math.format(Number(row.amount) / 100, 14)).toFixed(4);
						//   return h('label', text);
						// }
					},
					{
						title: this.$t('deposit.currency'),
						key: 'currencyCode',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							const row = params.row;
							var text = "";
							switch (row.currencyCode) {
								case "156":
									text = this.$t('support.CNY');
									break;
								case "840":
									text = this.$t('support.USD');
									break;
								case "344":
									text = this.$t('support.HKD');
									break;
								default:
									text = " ";
							}
							return h('label', text);
						},
					},
					{
						title: this.$t('support.Orderchannel'),
						key: 'orderChannel',
						align: 'center',
						minWidth: 120,
						render: (h, params) => {
							var row = params.row;
							var text = "";
							switch (row.orderChannel) {
								case "101":
									text = this.$t('order.WeChat');
									break;
								case "102":
									text = "API";
									break;
								case "103":
									text = this.$t('order.Website');
									break;
								case "104":
									text = this.$t('order.BeijingMobile');
									break;
								case "105":
									text = this.$t('order.BulkOrder');
									break;
								case "106":
									text = this.$t('order.Trial');
									break;
								case "110":
									text = this.$t('order.Testing');
									break;
								case "111":
									text = this.$t('order.issuance');
									break;
								case "112":
									text = this.$t('order.Postpaid');
									break;
								case "113":
									text = "WEB";
									break;
								case "114":
									text = this.$t('order.Datapool');
									break;
								case "115":
									text = this.$t('support.flowpoolApi');
									break;
								default:
									text = " ";
							}
							return h('label', text);
						},
					},
					{
						title: this.$t('support.DataRestrictionType'),
						key: 'flowLimitType',
						align: 'center',
						minWidth: 280,
						render: (h, params) => {
							const row = params.row;
							const text = row.flowLimitType === '1' ? this.$t('support.DataRestrictionCycle') :
								row.flowLimitType === '2' ? this.$t('support.DataRestrictionSingle') : ' ';
							return h('label', text);
						},
					},
					{
						title: this.$t('support.DataCap') + '(MB)',
						key: 'flowLimitSum',
						align: 'center',
						minWidth: 150,
					},
					{
						title: this.$t('fuelPack.onlinestatus'),
						key: 'surfStatus',
						align: 'center',
						minWidth: 250,
						render: (h, params) => {
							const row = params.row;
							let text = '';
							const surfStatus = row.surfStatus === '1' ? this.$t('support.Normal') :
								row.surfStatus === '2' ? this.$t('support.RestrictedSpeed') : ' ';
							const flowsurfStatus = row.surfStatus === '1' ? this.$t('flow.Normal') :
								row.surfStatus === '2' ? this.$t('flow.Cardcycle') :
								row.surfStatus === '3' ? this.$t('flow.Stopdatalimit') :
								row.surfStatus === '4' ? this.$t('flow.Restrictedspeed') :
								row.surfStatus === '5' ? this.$t('flow.Totallimitcard') :
								row.surfStatus === '6' ? this.$t('flow.Datapoollimit') :
								row.surfStatus === '7' ? this.$t('flow.stoppoollimit') : ' ';
							text = row.packageType != '3' ? surfStatus : flowsurfStatus
							let length = text === "" || text === null ? 0 : text.length
							if (length > 20) { //进行截取列显示字数
								let Status = text.replace(/\|/g, "</br>")
								text = text.substring(0, 20) + "..."
								return h('div', [h('Tooltip', {
										props: {
											placement: 'bottom',
											transfer: true //是否将弹层放置于 body 内
										},
										style: {
											cursor: 'pointer',
										},
									},
									[ //这个中括号表示是Tooltip标签的子标签
										text, //表格列显示文字
										h('label', {
											slot: 'content',
											style: {
												whiteSpace: 'normal'
											},
											domProps: {
												innerHTML: Status
											},
										}, )
									])]);
							} else {
								text = text;
								return h('label', text)
							}
						},
					},
					// {
					// 	title: this.$t('fuelPack.activationType'),
					// 	key: 'activationMode',
					// 	align: 'center',
					// 	minWidth: 250,
					// 	render: (h, params) => {
					// 		const row = params.row;
					// 		const text = row.activationMode === 3 ? this.$t('fuelPack.Activatelimit') :
					// 			row.activationMode === 1 ? this.$t('fuelPack.ActivateSpecific') :
					// 			row.activationMode === 2 ? this.$t('fuelPack.ActivateLU') : ' ';
					// 		return h('label', text);
					// 	},
					// },
					{
						title: this.$t('fuelPack.activationdate'),
						slot: 'activeAt',
						align: 'center',
						minWidth: 220,
						
					},
				];

				var action = ["view", "recovery", "change"];
				var btnPriv = this.$route.meta.permTypes;
				var actionMixed = action.filter(function(val) {
					return btnPriv.indexOf(val) > -1
				});
				if (actionMixed.length > 0) {
					var width = 100 + 60 * actionMixed.length;
					this.action.push({
						title: this.$t('support.action'),
						slot: 'action',
						align: 'center',
						width: width,
						fixed: 'right',
					});
					this.columns = this.columns.concat(this.action);
				}
				// 保存MSISDN，ICCID，IMSI
				localStorage.setItem("MSISDN", this.$route.query.backmsisdn)
				localStorage.setItem("ICCID", this.$route.query.backiccid)
				localStorage.setItem("IMSI", this.$route.query.backimsi)
				this.goPageFirst(1);
			},
			// 获取列表
			goPageFirst: function(page) {
				this.pageP = page;
				this.loading = true;
				searchPackageList({
					pageNumber: page,
					pageSize: this.pageSize,
					imsi: this.$route.query.imsi,
					iccid: this.$route.query.iccid,
					expiredData: 1
				}).then(res => {
					if (res && res.code == '0000') {
						var data = res.data;
						this.tableData = data.records;
						this.total = data.totalCount;
					} else {
						throw res
					}
				}).catch((err) => {
					console.log(err)
				}).finally(() => {
					this.loading = false
				})
			},
			//提前回收
			recovery(row) {
				var status = row.packageStatus;
				var expireTime = row.expireTime;
				var effectiveDay = row.effectiveDay;
				//已激活 expire_time < 当前时间，则提示不能提前回收，套餐已过期
				if ('2' == status && expireTime != null && expireTime != undefined && new Date(expireTime) < new Date()) {
					this.$Notice.error({
						title: this.$t("address.Operationreminder"),
						desc: this.$t("support.advance")
					})
					return false;
				}
				//激活中 effective_day < 当前时间，则提示不能提前回收，套餐已过期
				if ('6' == status && effectiveDay != null && effectiveDay != undefined && new Date(effectiveDay) <
					new Date()) {
					this.$Notice.error({
						title: this.$t("address.Operationreminder"),
						desc: this.$t("support.advance")
					})
					return false;
				}
				this.$Modal.confirm({
					title: this.$t('support.Termination'),
					onOk: () => {
						recoveryPackage({
							packageUniqueId: row.packageUniqueId
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t("address.Operationreminder"),
									desc: this.$t("common.Successful")
								})
								this.goPageFirst(this.pageP);
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {

						})
					}
				});
			},
			//更换VIMSI
			changeVIMSI(row) {
				this.$Modal.confirm({
					title: this.$t('support.replacement'),
					onOk: () => {
						var mcc = this.$route.query.mcc;
						if (row.packageStatus != '2') {
							this.$Notice.error({
								title: this.$t("address.Operationreminder"),
								desc: this.$t("support.operationFailed")
							})
							return false;
						}
						if (mcc == '' || mcc == null) {
							this.$Notice.error({
								title: this.$t("address.Operationreminder"),
								desc: this.$t("support.VoperationFailed")
							})
							return false;
						}
						changeVIMSI({
							mcc: mcc,
							packageId: row.packageId,
							packageUniqueId: row.packageUniqueId
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t("address.Operationreminder"),
									desc: this.$t("common.Successful")
								})
								this.goPageFirst(this.pageP);
							} else {
								throw res
							}
						}).catch((err) => {
							console.log(err)
						}).finally(() => {

						})
					}
				});
			},
			//根据使用情况弹出模态框
			packageModal(row) {
				this.obj = Object.assign({}, row);
				this.packageModalFlag = true;
			},
			//列表查询
			searchByCondition: function() {
				this.goPageFirst(1);
			},
			// 分页跳转
			goPage: function(page) {
				this.goPageFirst(page);
			},
			reBack() {
				this.$router.push({
					name: 'service_index'
				})
			},
			updateTime: function(row) {
				this.updateTimeObj.packageUniqueId = row.packageUniqueId
				this.updateTimeObj.date = row.activeAt
				this.updateTimeObj.newActiveTime = row.activeAt
				this.updateTimeModal = true
			},
			//套餐生效时间弹窗取消
			cancelTimeModal: function() {
				this.updateTimeModal = false
				this.$refs['updateTimeObj'].resetFields()
			},
			//获取时间
			getTime: function(date) {
				this.updateTimeObj.newActiveTime = date
			},
			//套餐生效日期
			submit: function() {
				this.$refs["updateTimeObj"].validate(valid => {
					if (valid) {
						this.updateTimeloading = true
						updateTime({
							packageUniqueId: this.updateTimeObj.packageUniqueId,
							newActiveTime: this.updateTimeObj.newActiveTime
						}).then(res => {
							if (res && res.code == '0000') {
								this.goPageFirst(this.pageP);
								this.$Notice.success({
									title: this.$t('common.Successful'),
									desc: this.$t('common.Successful')
								})
								this.cancelTimeModal()
							} else {
								throw res
							}
						}).catch((err) => {
							return false;
						}).finally(() => {
							this.updateTimeloading = false
						})
					}
				})
			},
		},
		mounted() {
			//信息初始化
			this.init();
		},
		watch: {}
	}
</script>
<style scoped="scoped">
	.search_head {
		width: 100%;
		display: flex;
		text-align: center;
		align-items: center;
		justify-content: flex-start;
	}

	.search-btn {
		width: 100px !important;
	}

	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		width: 340px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}
</style>
