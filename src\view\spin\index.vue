<template>
  <!-- 加载中 -->
  <div class="demo-spin-container">
    <Spin fix>
      <Icon type="ios-loading" size=100 class="demo-spin-icon-load"></Icon>
      <div>Loading</div>
    </Spin>
  </div>
</template>

<script>
</script>

<style scoped>
  .demo-spin-container {
    width: 100%;
    height: 100%;
    position: relative;
    border: 1px solid #eee;
  }

  .demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
  }

  @keyframes ani-demo-spin {
    from {
      transform: rotate(0deg);
    }

    50% {
      transform: rotate(180deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
</style>
