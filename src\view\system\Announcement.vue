<template>
	<!-- 公告管理 -->
	<Card>
		<div class="search_head">
			<Input v-model="notice" size="large" type="textarea" :autosize="{minRows: 12,maxRows: 20}" 
			placeholder="Enter Announcement..." clearable :border="false" maxlength="1000" style="input_Box"/>
		</div>
		<div class="button_Box">
			<Button v-has="'submit'" type="primary" size="large" ghost :loading="submitLoading" style="display: block;margin-right: 300px;" @click="submit">发布</Button>
			<Button type="warning" size="large" ghost @click="reset">重置</Button>
		</div>
	</Card>
</template>

<script>
	import {getAnnouncement,editAnnouncement} from '@/api/system/announcement.js';
	export default {
	    data () {
	        return {
	            value: '',
				notice: '',
				submitLoading: false,
	        }
	    },
		methods: {
			init: function() {
				getAnnouncement().then(res => {
				  if (res.code === '0000') {
				    this.notice = res.data.notice
				  } else {
				    throw resp
				  }
				}).catch(err => {})
			},
			submit: function()  {
				this.submitLoading = true
				editAnnouncement({notice: this.notice}).then(res => {
					if (res.code = '0000') {
						this.$Notice.success({
							title: '操作成功',
							desc: '发布公告成功!'
						})
						this.submitLoading = false
						this.init()
					} else {
						throw resp
					}
				}).catch(err => {
					this.submitLoading = false
				})
			},
			reset: function()  {
				this.notice = ''
			}
		},
		mounted() {
			this.init()
		}
	}
</script>

<style>
	.search_head {
	  width: 100%;
	  display: flex;
	  align-items: center;
	  justify-content: flex-start;
	  padding: 30px 40px;
	}
	.input_Box{
		font-size: 20px;
	}
	.button_Box {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.textarea.ivu-input {
		font-size: 22px !important;
	}
</style>