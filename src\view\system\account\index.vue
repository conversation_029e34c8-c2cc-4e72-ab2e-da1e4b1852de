<template>
	<!--  账户列表  -->
	<div>
		<Card>
			<div class="search_head">
				<span style="font-weight: bold">{{$t('sys.accountT')}}：</span><Input v-model.trim="username"
					:placeholder="$t('sys.wholesalerName')" clearable style="width: 200px; margin-right: 20px" />
				<span style="font-weight: bold">{{$t('sys.persona')}}：</span><Select v-model="roleType" :placeholder="$t('sys.roleType')"
					clearable style="width: 200px; margin-right: 20px">
					<Option v-for="item in roleListFirst" :value="item.id" :key="item.id">{{item.roleName}}</Option>
				</Select>
				<Button icon="md-search" type="primary" @click="searchByCondition()" v-preventReClick
					style="margin-right: 20px">{{ $t("common.search") }}</Button>
				<Button icon="md-add" v-has="'add'" type="success"
					@click="showUserModal(null, 0)">{{ $t("sys.add") }}</Button>
			</div>
			<div style="margin-top: 20px">
				<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row }" slot="username">
						{{ row.username }}
					</template>
					<template slot-scope="{ row }" slot="type">
						{{ row.roleName }}
					</template>
					<template slot-scope="{ row }" slot="status">
						{{ row.status === "1" ? $t('order.Normal') : $t('common.frozen') }}
					</template>
					<template slot-scope="{ row, index }" slot="action">
						<Button v-show="userId !== row.id" v-has="'edit'" type="primary" size="small"
							style="margin-right: 10px" @click="showUserModal(row, 1)">{{ $t("common.edit") }}</Button>

						<Button v-show="userId !== row.id" v-if="row.status !== '1'" v-preventReClick v-has="'unfreeze'"
							type="success" size="small" style="margin-right: 10px"
							@click="changeStatus(row)">{{ $t("common.thaw") }}</Button>
						<Button v-show="userId !== row.id" v-else v-has="'freeze'" v-preventReClick type="warning"
							size="small" style="margin-right: 10px"
							@click="changeStatus(row)">{{ $t("common.frozen") }}</Button>
						<Button v-show="userId !== row.id" v-has="'delete'" v-preventReClick type="error" size="small"
							@click="deleteWarning(row)">{{ $t("common.del") }}</Button>
					</template>
				</Table>
			</div>
			<div class="table-botton" style="margin-top: 15px">
				<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
			</div>
		</Card>

		<!-- 编辑模态框 -->
		<Modal :title="$t('support.edit2')" v-model="userModal" :mask-closable="false" @on-cancel="cancelModal">
			<Form v-if="userModal" ref="editForm" :model="modalData" :rules="rules"
				@keydown.enter.native="userAddOrEdit">
				<FormItem prop="username">
					<div class="input_modal">
						<span style="width: 2%; color: red">*</span><span
							style="width: 15%">{{ $t("sys.account") }}</span>
						<Input v-model.trim="modalData.username" :disabled="optType===1" :placeholder="$t('sys.paccount')"
							style="width: 83%" />
					</div>
				</FormItem>
				<FormItem prop="email">
					<div class="input_modal">
						<span style="width: 2%; color: red">*</span><span
							style="width: 15%">{{ $t("sys.enterEmail") }}</span>
						<Input v-model="modalData.email" :placeholder="$t('sys.inputEmail')" style="width: 82%" />
					</div>
				</FormItem>
				<FormItem v-if="optType == 0 || (optType == 1 && toRepwd)" prop="newPwd">
					<div class="input_modal">
						<span style="width: 2%; color: red">*</span><span
							style="width: 15%">{{ $t("sys.setPassword") }}</span>
						<Input type="password" password v-model="modalData.newPwd"
							:placeholder="$t('sys.enterPassword')" style="width: 82%" />
					</div>
				</FormItem>
				<FormItem v-if="optType == 0 || (optType == 1 && toRepwd)" prop="rePwd">
					<div class="input_modal">
						<span style="width: 2%; color: red">*</span><span
							style="width: 15%">{{ $t("sys.rePwd") }}</span>
						<Input type="password" password v-model="modalData.rePwd" :placeholder="$t('sys.prePwd')"
							style="width: 82%" />
					</div>
				</FormItem>
				<div v-if="optType == 0 || (optType == 1 && toRepwd)">
					<Alert type="warning" show-icon>{{$t('address.PwdRules')}}<a href="#" @click="showRules = true">{{$t('address.watch')}}</a>{{$t('address.more')}}
					</Alert>

					<div v-if="showRules" class="password-rule-notice">
						<Alert type="warning" closable @on-close="showRules = false" v-if="this.$i18n.locale==='zh-CN'">
							<text-view></text-view>
						</Alert>
						<Alert type="warning" closable @on-close="showRules = false" v-else>
							<texten-view></texten-view>
						</Alert>
					</div>
				</div>

				<FormItem prop="type" style="margin-top: 20px;">
					<div class="input_modal">
						<span style="width: 2%; color: red">*</span><span
							style="width: 15%">{{ $t("sys.role") }}：</span>
						<Select v-model="modalData.type" :placeholder="$t('sys.roleType')" style="width: 82%">
							<Option v-for="item in roleList" :value="item.id" :key="item.id">{{item.roleName}}
							</Option>
						</Select>
					</div>
				</FormItem>

				<FormItem prop="mobileNumber">
					<div class="input_modal">
						<span style="width: 2%; color: red">*</span><span
							style="width: 15%">{{ $t("sys.phone") }}:</span>
						<Input v-model="modalData.mobileNumber" :placeholder="$t('sys.enterPhone')"
							style="width: 82%" />
					</div>
				</FormItem>

        <FormItem prop="functionStatus" v-if="isCorp == '' || isCorp == 'undefined' || isCorp == 'null'">
        	<div class="input_modal">
            <span style="width: 2%; color: red">*</span><span
            	style="width: 15%">账号职能:</span>
            <RadioGroup v-model="modalData.functionStatus" @on-change="changeFunction">
              <Radio label="2">
                <Icon type="md-people"></Icon>
                  <span>销售人员</span>
              </Radio>
              <Radio label="1">
                <Icon type="ios-people"></Icon>
                <span>普通账号</span>
              </Radio>
              <Radio label="3">
                <Icon type="md-person"></Icon>
                <span>大区经理</span>
              </Radio>
            </RadioGroup>
        	</div>
        </FormItem>

        <FormItem prop="region" v-if="modalData.functionStatus == '2' || modalData.functionStatus == '3'" :rules="
        modalData.functionStatus == '2' ? rules.region2 : modalData.functionStatus == '3' ? rules.region3 :[{required: false}]">
        	<div class="input_modal">
        		<span style="width: 2%; color: red">*</span><span
        			style="width: 15%">归属大区:</span>
            <Select
              v-model="modalData.region"
              placeholder="请选择归属大区"
              clearable
              filterable
              :multiple="isMultiple"
              style="width: 82%"
            >
              <Option
                v-for="item in regionList"
                :value="item.id"
                :key="item.id"
                >{{ item.regionName }}</Option
              >
            </Select>
        	</div>
        </FormItem>

				<div v-if="optType == 1 && toRepwd" style="width: 100%; display: flex; justify-content: flex-end">
					<a href="#" @click="exitUpdatePassword">{{$t('sys.exitModifyPassword')}}</a>
				</div>
				<div v-if="optType == 1 && !toRepwd" style="width: 100%; display: flex; justify-content: flex-end">
					<a href="#" @click="toRepwd = true">{{$t('common.rePwd')}}</a>
				</div>
			</Form>
			<div slot="footer" style="width: 100%; display: flex; align-items: center; justify-content: flex-end;">
				<Button @click="cancelModal">{{ $t("common.cancel") }}</Button>
				<Button type="primary" @click="userAddOrEdit">{{$t("common.determine")}}</Button>
			</div>
		</Modal>
	</div>
</template>

<script>
	import i18n from "@/locale";
	import TextView from './text.vue';
	import TextenView from './textEn.vue';
	import publicData from "../../../libs/publicData.js";
	import {
		getRoleList
	} from "@/api/system/privilege";
	import {
		getAccountList,
		delOperator,
		addOperator,
		editOperator,
		changeUserStatus,
		sendSms,
    getRegionList
	} from "@/api/system/account";

	export default {
		components: {
			TextView,
			TextenView
		},
		data() {
			var validatePwd = (rule, value, callback) => {
				var pwpattent =
					/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/;
				if (pwpattent.test(value) == false) {
					callback(new Error(this.$t("address.reset")));
					return;
				}
				if (/(.)\1{2}/i.test(value)) {
					callback(new Error(this.$t("address.appear")));
					return;
				}
				var alphnumon = /((?:0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)|9(?=0)){2}\d)/;
				if (alphnumon.test(value)) {
					callback(new Error(this.$t("address.allowed"))); //字母或
					return;
				} else {
					callback();
				}
			};
			var validateEmail = (rule, value, callback) => {
				// var emailReg = /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;
				if (!value || value.indexOf("@") == -1) {
					callback(new Error(this.$t('address.emailaddress')));
				} else {
					callback();
				}
			};
			var validateType = (rule, value, callback) => {
				if (!value) {
					callback(new Error(this.$t('sys.pleaseSetRoles')));
				} else {
					callback();
				}
			};
			var validateMobileNumber = (rule, value, callback) => {
				if (!value) {
					return callback(new Error(this.$t('sys.phoneNoEmpty')));
				} else if (!/^[0-9]*$/.test(value)) {
					callback(this.$t('sys.onlyNumbers'));
				} else {
					callback();
				}
			};
			return {
				// 列表数据
				tableData: [],
				toRepwd: false,
				cooling: '',
				modalData: {
					username: "",
					type: "",
					memberId: "",
					mobileNumber: "",
          functionStatus: "",
          region: []
				},
				optType: 0,
				roleListFirst: [],
				roleList: [],
        regionList: [],
				roleType: null,
				username: "",
				userModal: false,
				columns: [{
						title: i18n.t("sys.accountT"),
						slot: "username",
						align: "center",
					},
					{
						title: this.$t('address.mailbox'),
						key: "email",
						align: "center",
					},
					{
						title: this.$t('sys.phone'),
						key: "mobileNumber",
						align: "center",
					},
					{
						title: i18n.t("sys.role"),
						slot: "type",
						align: "center",
					},
          {
          	title: "账号职能",
            key: "functionStatus",
          	align: "center",
            render: (h, params) => {
            	const row = params.row
            	const text = row.functionStatus == '1' ? "普通账号" : row.functionStatus == '2' ? "销售人员" : row.functionStatus == '3' ? "大区经理" : ""
            	return h('label', text)
            }
          },
					{
						title: i18n.t("sys.status"),
						slot: "status",
						align: "center",
					},

				],
				public: [{
					title: i18n.t("sys.opt"),
					slot: "action",
					align: "center",
				}, ],
				loading: false,
				currentPage: 1,
				page: 0,
				total: 0,
				rules: {
					newPwd: [{
						validator: validatePwd,
						trigger: "blur",
					}, ],
					rePwd: [{
						validator: validatePwd,
						trigger: "blur",
					}, ],
					email: [{
						validator: validateEmail,
						trigger: "blur",
					}, ],
					username: [{
						required: true,
						message: this.$t('sys.paccount'),
						trigger: 'blur'
					}, {
            // 新的校验规则，检查是否包含&符号
            validator: (rule, value, callback) => {
              if (value && value.includes('&')) {
                callback(new Error(this.$t('sys.userNameAmpersand')));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },],
					type: [{
						validator: validateType,
						trigger: "blur",
					}, ],
					mobileNumber: [{
							validator: validateMobileNumber,
							// required: true,
							// message: '手机号不能为空',
							trigger: "blur",
						},
						{
							max: 20,
							message: this.$t('sys.twentyDigits')
						},
					],
          functionStatus: [{
            required: true,
            message: '请选择账号职能',
            trigger: 'change'
          }],
          region2: [{
            required: true,
            message: '请选择归属大区',
            trigger: 'change'
          }],
          region3: [{
            type:'array',
            required: true,
            message: '请选择归属大区',
            trigger: 'change'
          }],
				},
				userId: "",
        isCorp: "",
				showRules: false,
        isMultiple: true,
			};
		},
		created() {
			this.userId = this.$store.state.user.userId;
      this.isCorp = sessionStorage.getItem('corpId')
		},
		computed: {},
		methods: {
			exitUpdatePassword() {
				this.toRepwd = false;
				this.resetField(['newPwd', ['rePwd']])
				delete this.modalData.newPwd;
				delete this.modalData.rePwd;
			},

			resetField(arr) {
				this.$refs["editForm"].fields.forEach((element) => {
					if (arr.includes(element.prop)) {
						element.resetField();
					}
				});
			},

			showUserModal: function(list, type) {
				this.optType = type;
				if (type == 0) {
					//新增
					this.modalData = {
						username: null,
						type: null,
						memberId: null,
						mobileNumber: null,
            functionStatus: '1',
            region: []
					};
				} else {
					//修改
					this.modalData = {
						type: list.roleId,
						status: list.status,
						id: list.id,
						username: list.username,
						newPwd: null,
						email: list.email,
						mobileNumber: list.mobileNumber,
            functionStatus: list.functionStatus,
            region: list.region
					};
          if (list.functionStatus == '2') {
            this.isMultiple = false
          } else {
            this.isMultiple = true
          }
				}
				this.getRoleList();
        this.getRegionList();
				this.userModal = true;
			},
			cancelModal: function() {
				this.userModal = false;
				this.showRules = false;
				this.toRepwd = false;
				delete this.modalData.newPwd;
				delete this.modalData.rePwd;

			},
			userAddOrEdit: function() {
				this.$refs.editForm.validate((valid) => {
					if (valid) {
						if (this.optType === 1) {
							// 修改
							this.edit();
						} else {
							this.add();
						}
					}
				});
			},
			add: function() {
				if (this.modalData.newPwd !== this.modalData.rePwd) {
					this.$Notice.warning({
						title: this.$t("address.Operationreminder"),
						desc: this.$t("sys.passwordsNotMatch"),
					});
					return;
				}
        let regionCopy;
        let regionList = [];
        if (this.modalData.functionStatus == '2') {
          regionList.push(this.modalData.region);
          regionCopy = regionList;
        } else if (this.modalData.functionStatus == '1') {
          regionCopy = []
        } else {
          regionCopy = this.modalData.region;
        }
        addOperator({
						userName: this.modalData.username,
						passwd: this.modalData.newPwd,
						email: this.modalData.email,
						role: this.modalData.type,
						mobileNumber: this.modalData.mobileNumber,
            functionStatus: this.modalData.functionStatus,
            region: regionCopy
					})
					.then((res) => {
						if (res && res.code == "0000") {
							this.$Notice.success({
								title: this.$t("address.Operationreminder"),
								desc: this.$t("sys.successAddedUser"),
							});
							this.userModal = false;
							this.goPageFirst(0);
						} else {
							throw res;
						}
					})
					.catch((err) => {
						console.log(err);
					})
					.finally(() => {
						// this.userModal = false
						this.loading = false;
					});
			},

			// 修改账户信息
			edit: function() {
				if (this.toRepwd) {
					if (this.modalData.newPwd !== this.modalData.rePwd) {
						this.$Notice.warning({
							title: this.$t("address.Operationreminder"),
							desc: this.$t("sys.passwordsNotMatch"),
						});
						return;
					}
				}
        let regionCopy;
        let regionList = [];
        if (this.modalData.functionStatus == '2') {
          regionList.push(this.modalData.region);
          regionCopy = regionList;
        } else if (this.modalData.functionStatus == '1') {
          regionCopy = []
        } else {
          regionCopy = this.modalData.region;
        }
				editOperator({
						id: this.modalData.id,
						userName: this.modalData.username,
						passwd: this.modalData.newPwd,
						email: this.modalData.email,
						status: this.modalData.status,
						role: this.modalData.type,
						mobileNumber: this.modalData.mobileNumber,
            functionStatus: this.modalData.functionStatus,
            region: regionCopy
					})
					.then((res) => {
						if (res && res.code == "0000") {
							this.$Notice.success({
								title: this.$t("address.Operationreminder"),
								desc: this.$t("sys.userInforupdated"),
							});

							this.userModal = false;
							this.toRepwd = false;
							this.currentPage = 1;
							this.goPageFirst(0);

						} else {
							throw res;
						}
					})
					.catch((err) => {
						console.log(err);
					})
					.finally(() => {
						// this.userModal = false
						this.loading = false;
					});
			},

			// 获取列表
			goPageFirst: function(page) {
				this.page = page;
				this.loading = true;
				var params = {
					userName: this.username,
					roleId: this.roleType,
					page: page,
					pageSize: 10,
				};
				if (page === 0) {
					this.currentPage = 1;
				}
				getAccountList(params)
					.then((res) => {
						if (res && res.code == "0000") {
							this.tableData = res.data.records;
							this.total = res.data.total;
						} else {
							throw res;
						}
					})
					.catch((err) => {
						console.log(err);
					})
					.finally(() => {
						this.loading = false;
					});
			},

			searchByCondition: function() {
				this.goPageFirst(0);
			},
			// 删除账户
			delete: function(memberId) {
				delOperator({
						id: memberId,
					})
					.then((res) => {
						if (res && res.code == "0000") {
							this.$Notice.success({
								title: this.$t('common.Successful'),
								desc: this.$t('sys.successDeleted'),
							});
							this.goPageFirst(this.page);
						} else {
							throw res;
						}
					})
					.catch((err) => {
						console.log(err);
					})
					.finally(() => {
						this.loading = false;
					});
			},

			changeStatus: function(row) {
				this.$Notice.warning({
					title: this.$t("address.Operationreminder"),
					name: "changeStatus",
					desc: "",
					render: (h) => {
						return h("div", [
							this.$t('sys.operationWill'),
							h(
								"span", {
									style: {
										marginRight: "10px",
										marginLeft: "10px",
										color: "#ED4014",
									},
								},
								(row.status == 1 ? this.$t('common.frozen') : this.$t('common.thaw')) + this.$t('sys.user') + row.username
							),
							this.$t('sys.continueExecution'),
							h("br"),
							h("div", [
								h(
									"Button", {
										props: {
											type: "dashed",
											size: "small",
										},
										style: {
											marginTop: "15px",
											marginLeft: "100px",
										},
										on: {
											click: () => {
												this.$Notice.close("changeStatus");
											},
										},
									},
									this.$t('common.cancel')
								),
								h(
									"Button", {
										props: {
											type: "info",
											size: "small",
										},
										style: {
											marginTop: "15px",
											marginLeft: "20px",
										},
										on: {
											click: () => {
												this.$Notice.close("changeStatus");
												this.doChangeStatus(row);
											},
										},
									},
									row.status == 1 ? this.$t('common.frozen') : this.$t('common.thaw')
								),
							]),
						]);
					},
					duration: 0,
				});
			},
			doChangeStatus: function(row) {
				editOperator({
						id: row.id,
						userName: row.username,
						email: row.email,
						status: row.status === "1" ? "2" : "1",
						role: row.roleId,
						mobileNumber: row.mobileNumber,
            functionStatus: row.functionStatus,
            region: row.region
					})
					.then((res) => {
						if (res.code === "0000") {
							this.$Notice.success({
								title: this.$t("common.Successful"),
								desc: this.$t("sys.userStatusUpdated"),
							});
							this.goPageFirst(this.page);
						} else {
							throw res;
						}
					})
					.catch((err) => {
						console.log(err);
					})
					.finally(() => {
						this.loading = false;
					});
			},

			// 分页跳转
			goPage(page) {
				this.goPageFirst(page);
			},
			//首页角色查询
			getRoleListFirstPage: function() {
				getRoleList({
					containChannel: true
				})
					.then((res) => {
						if (res.code === "0000") {
							this.roleListFirst = res.data;
						} else {
							throw res;
						}
					})
					.catch((err) => {
						console.log(err);
					})
					.finally(() => {});
			},
			//新增\编辑角色查询
			getRoleList: function() {
				getRoleList({})
					.then((res) => {
						if (res.code === "0000") {
							this.roleList = res.data;
						} else {
							throw res;
						}
					})
					.catch((err) => {
						console.log(err);
					})
					.finally(() => {});
			},
			error(nodesc) {
				this.$Notice.error({
					title: this.$t('sys.wrong'),
					desc: nodesc || this.$t('sys.Serverwrong'),
				});
			},
			cancel() {},
			deleteWarning: function(params) {
				this.$Notice.warning({
					title: this.$t("address.Operationreminder"),
					name: "delete",
					desc: this.$t("sys.confirmDeleteUser") + "？",
					render: (h) => {
						return h("div", [
							this.$t("sys.confirmDeleteUser") + "[",
							h(
								"span", {
									style: {
										marginRight: "10px",
										marginLeft: "10px",
										color: "#ff0000",
									},
								},
								params.username
							),
							"]？",
							h("br"),
							h("div", [
								h(
									"Button", {
										props: {
											type: "dashed",
											size: "small",
										},
										style: {
											marginTop: "10px",
											marginLeft: "130px",
										},
										on: {
											click: () => {
												this.$Notice.close("delete");
											},
										},
									},
									this.$t("common.cancel")
								),
								h(
									"Button", {
										props: {
											type: "error",
											size: "small",
										},
										style: {
											marginTop: "10px",
											marginLeft: "10px",
										},
										on: {
											click: () => {
												this.$Notice.close("delete");
												this.delete(params.id);
											},
										},
									},
									this.$t("common.del")
								),
							]),
						]);
					},
					duration: 0,
				});
			},
      changeFunction: function(){
        this.modalData.region= ''
        if (this.modalData.functionStatus == '2') {
          this.isMultiple = false
        } else {
          this.isMultiple = true
        }
      },
      // 归属大区下拉列表
      getRegionList: function() {
      	getRegionList({
      	})
      		.then((res) => {
      			if (res.code === "0000") {
      				this.regionList = res.data;
      			} else {
      				throw res;
      			}
      		})
      		.catch((err) => {
      			console.log(err);
      		})
      		.finally(() => {});
      },
    },
		beforeMount: function() {
			var btnPriv = this.$route.meta.permTypes;
			if (
				btnPriv.includes("edit") ||
				btnPriv.includes("delete") ||
				btnPriv.includes("freeze") ||
				btnPriv.includes("unfreeze")
			) {
				this.columns = this.columns.concat(this.public);
			}
		},
		mounted() {
			this.getRoleListFirstPage();
			this.goPageFirst(0);
      if (this.isCorp == '' || this.isCorp == 'undefined' || this.isCorp == 'null') {
        this.columns = this.columns
      } else {
        this.columns = this.columns.filter(column => column.key !== 'functionStatus');
      }
		},
		watch: {
			getLoading: function(value) {
				if (value == true) {
					this.loadmsg = this.$Message.loading({
						content: this.$t('sys.obtainPermissionData'),
						duration: 0,
					});
				} else {
					setTimeout(this.loadmsg, 1);
				}
			},

			setLoading: function(value) {
				if (value == true) {
					this.loadmsg = this.$Message.loading({
						content: this.$t('sys.Saving'),
						duration: 0,
					});
				} else {
					setTimeout(this.loadmsg, 1);
				}
			},
			// label(newVal, oldVal) {
			// 	if (newVal != oldVal) {
			// 		this.label = newVal
			// 		this.goPageFirst(0)
			// 	}
			// }
		},
	};
</script>
<style>
	.search_head {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}

	.demo-drawer-footer {
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		border-top: 1px solid #e8e8e8;
		padding: 10px 16px;
		text-align: right;
		background: #fff;
	}

	.input_modal {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		flex-wrap: nowrap;
		width: 100%;
	}

	.password-rule-notice {
		position: absolute;
		width: 100%;
		top: 0px;
		z-index: 111;
		left: 110%;
	}

	.errorMsg {
		width: 80%;
		margin-left: 20%;
	}
</style>
