<template>
  <!--  密码设置  -->
  <div>
    <Card>
      <div
        style="
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <div class="view_out">
          <Form
            ref="editForm"
            :model="userInfo"
            :rules="rules"
            @keydown.enter.native="edit"
          >
            <FormItem prop="account">
              <div class="view_line">
                <span style="width: 25%">{{ $t("sys.account") }}</span>
                <Input
                  style="width: 80%"
                  disabled
                  v-model="userInfo.account"
                  :placeholder="$t('sys.Enteraccount')"
                />
              </div>
            </FormItem>

            <FormItem prop="oldPwd">
              <div class="view_line">
                <span style="width: 25%">{{ $t("sys.oldPwd") }}</span>
                <Input
                  type="password"
                  style="width: 80%"
                  v-model="userInfo.oldPwd"
                  password
                  :placeholder="$t('sys.oldpassword')"
                />
              </div>
            </FormItem>
            <FormItem prop="newPwd">
              <div class="view_line">
                <span style="width: 25%">{{ $t("sys.newPwd") }}</span>
                <Input
                  type="password"
                  style="width: 80%"
                  v-model="userInfo.newPwd"
                  password
                  :placeholder="$t('sys.newpassword')"
                />
              </div>
            </FormItem>
            <FormItem prop="rePwd">
              <div class="view_line">
                <span style="width: 25%">{{ $t("sys.rePwd") }}</span>
                <Input
                  type="password"
                  style="width: 80%"
                  v-model="userInfo.rePwd"
                  password
                  :placeholder="$t('address.password_ok')"
                />
              </div>
            </FormItem>
            <Alert type="warning" show-icon
              >{{$t('address.PwdRules')}}<a
                href="#"
                @click="showRules = true"
                >{{$t('address.watch')}}</a
              >{{$t('address.more')}}</Alert
            >
            <FormItem>
              <div class="view_butt">
                <Button
                  v-has="'edit'"
                  v-preventReClick
                  class="searchBtn"
                  icon="md-refresh"
                  type="success"
                  @click="reset"
                  >{{ $t("buymeal.Reset") }}</Button
                >
                <Button
                  v-has="'edit'"
                  v-preventReClick
                  class="searchBtn"
                  icon="md-finger-print"
                  type="primary"
                  @click="edit"
                  style="margin-left: 55px"
                  >{{ $t("common.edit") }}</Button
                >
              </div>
            </FormItem>
          </Form>
        </div>
        <div v-if="showRules" class="view_right">
          <Alert type="warning" closable @on-close="showRules = false">
            <div v-if="this.$i18n.locale==='zh-CN'">
            	<text-view></text-view>
            </div>
            <div v-if="this.$i18n.locale==='en-US'">
            	<text-viewEn></text-viewEn>
            </div>
          </Alert>
        </div>
      </div>
    </Card>
  </div>
</template>

<script>
import TextView from "./text.vue";
import TextViewEn from './textEn.vue';
import i18n from "@/locale";
import { rePassword } from "@/api/system/account";

import { mapActions } from "vuex";
export default {
  name: "InfoExperience",
  components: {
    TextView,
	TextViewEn
  },
  data() {
    var validatePwd = (rule, value, callback) => {
      var pwpattent =
        /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/;
      if (pwpattent.test(value) == false) {
        callback(new Error(this.$t("address.reset")));
        return;
      }
      if (/(.)\1{2}/i.test(value)) {
        callback(new Error(this.$t("address.appear")));
        return;
      }
      var alphnumon =
        /((?:0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)|9(?=0)){2}\d)/;
      if (alphnumon.test(value)) {
        callback(new Error(this.$t("address.allowed"))); //字母或
        return;
      } else {
        callback();
      }
    };
    var validateAccount = (rule, value, callback) => {
      if (value) {
        callback();
      } else {
        callback(new Error(i18n.t("sys.accountNummMsg")));
      }
    };
    return {
      showRules: false,
      maxlength: 20,
      userInfo: {
        account: "",
        oldPwd: "",
        newPwd: "",
        rePwd: "",
      },
      rules: {
        oldPwd: [
          {
            required: true,
            message: this.$t('sys.poldPwd'),
            trigger: 'change'
          },
        ],
        newPwd: [
          {
            validator: validatePwd,
            trigger: "blur",
          },
        ],
        rePwd: [
          {
            validator: validatePwd,
            trigger: "blur",
          },
        ],
        account: [
          {
            validator: validateAccount,
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    ...mapActions(["handleLogOut"]),
    logout() {
      this.handleLogOut().then(() => {
        this.$router.push({
          name: "login",
        });
      });
    },

    resetField(arr) {

      this.$refs["editForm"].fields.forEach((element) => {
        if (arr.includes(element.prop)) {
          element.resetField();
        }
      });
    },

    reset: function () {
      this.resetField(['rePwd','newPwd','oldPwd']);
    },
    reUserInfo: function () {
      this.userInfo.account = this.$store.state.user.userName;
    },
    // 修改账户信息
    edit: function () {
      // 修改密码
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          if (this.userInfo.newPwd !== this.userInfo.rePwd) {
            this.$Notice.warning({
              title: this.$t("address.Operationreminder"),
              desc: this.$t("address.inconsistent") ,
            });
            return;
          }
          this.reSetUserInfo();
        }
      });
    },
    reSetUserInfo: function () {
      rePassword(
        {
          id: this.$store.state.user.userId,
          userName: this.userInfo.account,
          oldPasswd: this.userInfo.oldPwd.replace(/\s/g, ""),
          newPasswd: this.userInfo.newPwd.replace(/\s/g, ""),
        },
        this.$store.state.user.userId
      )
        .then((res) => {
          if (res && res.code == "0000") {
            this.$store.state.user.userName = this.userInfo.account;
            this.$Notice.success({
              title: this.$t("address.Operationreminder"),
              desc: this.$t("sys.successfully"),
            });
            var _this = this;
            setTimeout(function () {
              _this.logout();
            }, 1000);
          } else {
            throw res;
          }
        })
        .catch((err) => {});
    },
    checkPhone: function (phone) {
      if (!phone || phone === "") {
        return false;
      }
      if (
        !/^[0-9]*$/.test(phone.replace(/\s/g, "")) ||
        !/^\d{4,16}$/.test(phone.replace(/\s/g, ""))
      ) {
        return false;
      }
      return true;
    },
    checkPwd: function (pwd) {
      if (!pwd || pwd === "") {
        return false;
      }
      return true;
    },

    error(nodesc) {
      this.$Notice.error({
        title: this.$t("sys.wrong"),
        desc: nodesc || this.$t("sys.Serverwrong"),
      });
    },
    cancel() {},
  },
  mounted() {
    this.reUserInfo();
  },
  watch: {},
};
</script>
<style>
.view_out {
  width: 40%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.view_right {
  width: 600px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.view_line {
  display: flex;
  flex-wrap: nowrap;
  width: 500px;
  justify-content: center;
  align-items: center;
  /* padding: 13px 0; */
}

.view_butt {
  display: flex;
  flex-wrap: nowrap;
  /* width: 350px; */
  /* margin-bottom: 30px; */
  justify-content: space-between;
  /* padding: 10px; */
}

.view_a {
  display: flex;
  width: 350px;
  justify-content: flex-end;
  align-items: center;
  /* padding: 0px 0px 5px 0; */
}

.searchBtn {
  width: 60% !important;
}
</style>
