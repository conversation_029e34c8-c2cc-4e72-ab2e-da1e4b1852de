<template>
	<!-- 金锁模式审批管理 -->
	<Card>
		<!-- 搜索条件 -->
		<div class="search_head_i">
			<div class="search_box">
				<span class="search_box_label">申请账号:</span>
				<Input v-model.trim="accountNumber " placeholder="请输入申请账号" clearable
					style="width: 200px;" />
			</div>
			<div class="search_box">
				<span class="search_box_label">申请时间:</span>
				<DatePicker @on-change="getTime" format="yyyy-MM-dd" :editable="false" type="daterange"
					placeholder="请选择申请时间" clearable style="width: 200px; margin: 0 10px 0 0">
				</DatePicker>
			</div>
			<div class="search_box">
				<span class="search_box_label">审批结果:</span>
				<Select v-model="approveResultCode" clearable placeholder="选择审批结果" filterable style="width: 200px;">
					<Option value="1">待审批</Option>
					<Option value="2">通过</Option>
					<Option value="3">不通过</Option>
				</Select>
			</div>&nbsp;&nbsp;&nbsp;&nbsp;
			<div style="padding: 0 5px;">
				<Button v-has="'search'" type="primary" icon="md-search" :loading="searchloading" @click="search()">搜索</Button>
			</div>
		</div>
		<!-- 表格 -->
		<Table ref="selection" :columns="columns" :data="data" style="width:100%;margin-top: 40px;" :loading="loading">
			<template slot-scope="{ row, index }" slot="action">
				<div v-if="row.authStatus == '1'">
					<Button v-has="'pass'" type="primary" ghost size="small" style="margin-right: 10px"
						@click="examine(row.id, 2)">通过</Button>
					<Button v-has="'nopass'" type="error" ghost size="small" @click="examine(row.id, 3)">不通过</Button>
				</div>
			</template>
		</Table>
		<!-- 分页 -->
		<div style="margin-top:15px">
			<Page :total="total" :current.sync="currentPage" show-total show-elevator @on-change="goPage" />
		</div>
	</Card>
</template>

<script>
	import {
		getList,
		examine
	} from "@/api/system/lockModeApproval";
	export default {
		data() {
			return {
				total: 0,
				currentPage: 1,
				page: 0,
				accountNumber: "",
				startTime: "",
				endTime: "",
				approveResultCode: "", //审核结果
				loading: false,
				searchloading: false, //查询加载
				data: [], //表格列表
				columns: [{
					title: "申请账号",
					key: 'userName',
					minWidth: 120,
					align: 'center',
					tooltip: true
				}, {
					title: "申请时间",
					key: 'replyTime',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.replyTime) {
							var time = new Date(row.replyTime)
							text = time.getFullYear() + '年' + (time.getMonth() + 1) + '月' + time.getDate() +
								'日 ' + time.getHours().toString().padStart(2, "0") + ':' + time.getMinutes()
								.toString().padStart(2, "0") + ':' + time.getSeconds().toString().padStart(2,
									"0");
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: "审批时间",
					key: 'authTime',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						var text = ''
						if (row.authTime) {
							var time = new Date(row.authTime)
							text = time.getFullYear() + '年' + (time.getMonth() + 1) + '月' + time.getDate() +
								'日 ' + time.getHours().toString().padStart(2, "0") + ':' + time.getMinutes()
								.toString().padStart(2, "0") + ':' + time.getSeconds().toString().padStart(2,
									"0");
						} else {
							text = ""
						}
						return h('label', text)
					}
				}, {
					title: "审批账号",
					key: 'approver',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: "申请页面",
					key: 'pageNum',
					minWidth: 120,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text = row.pageNum == '1' ? '个人订单管理' : row.pageNum == '2' ? '认证信息' : ''
						return h('label', text)
					}
				}, {
					title: "申请原因",
					key: 'replyReason',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				}, {
					title: "浏览时间",
					key: 'availableTime',
					minWidth: 120,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const text = row.availableTime + "小时"
						return h('label', text)
					}
				}, {
					title: '审批结果',
					key: 'authStatus',
					align: 'center',
					minWidth: 120,
					render: (h, params) => {
						const row = params.row
						const color = row.authStatus == '1' ? '#2b85e4' : row.authStatus == '2' ? '#19be6b' :
							row.authStatus == '3' ? '#ff0000' : '';
						const text = row.authStatus == '1' ? '待审核' : row.authStatus == '2' ? '通过' :
							row.authStatus == '3' ? '不通过' : ''
						return h('label', {
							style: {
								color: color
							}
						}, text)
					}
				}, {
					title: "审核操作",
					slot: 'action',
					minWidth: 180,
					align: 'center',
					fixed: 'right'
				}, ],
			}
		},
		mounted() {
			this.goPageFirst(1)
		},
		methods: {
			goPageFirst: function(page) {
				this.loading = true
				var _this = this
				getList({
					pageNum: page,
					pageSize: 10,
					username: this.accountNumber,
					startTime: this.startTime ? this.startTime + " 00:00:00" : "",
					endTime: this.endTime ? this.endTime + " 23:59:59" : "",
					authStatus: this.approveResultCode,
				}).then(res => {
					if (res.code == '0000') {
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.data = res.data.records;
						this.total = res.data.total
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					_this.loading = false
					this.searchloading = false
				})
			},
			search: function() {
				this.searchloading = true
				this.goPageFirst(1)
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			getTime: function(times, type) {
				this.startTime = times[0];
				this.endTime = times[1];
			},
			//审核
			examine(id, authStatus) {
				this.$Modal.confirm({
					title: authStatus == '2' ? '确认执行审核通过？' : '确认执行审核不通过？',
					onOk: () => {
						examine({
							id,
							authStatus
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								this.goPageFirst(this.page)
							} else {
								throw res
							}
						}).catch((err) => {
							this.goPageFirst(this.page)
						})
					}
				});
			},
		}
	}
</script>

<style scoped="scoped">
	.search_head_i {
		margin-top: 30px;
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}

	.search_box {
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
	}

	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 100px;
	}
</style>