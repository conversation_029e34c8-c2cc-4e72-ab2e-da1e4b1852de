<template>
  <!--  登录日志  -->
  <div>
    <Card>
      <div class="search_head">
        <Form ref="form" :label-width="0" :model="form" :rules="rule" inline>
            <Input
              v-model="form.wholesalerName"
              :placeholder="$t('sys.wholesalerName')"
              clearable
              style="width: 200px; margin-right: 10px"
            />
          <FormItem prop="startTime">
            <DatePicker
              @on-change="getTime"
              :editable="false"
              type="datetimerange"
              :placeholder="$t('common.timeSection')"
              clearable
              style="width: 300px; margin: 0 10px 0 0"
            ></DatePicker>
          </FormItem>
            <Select
              v-model="form.optType"
              :placeholder="$t('common.optType')"
              clearable
              style="text-align: left; width: 150px; margin-right: 10px"
            >
              <Option
                v-for="item in optList"
                :value="item.key"
                :key="item.key"
                >{{ item.value }}</Option
              >
            </Select>

          <Button
            type="primary"
            v-preventReClick
            icon="md-search"
            @click="searchByCondition()"
            >{{ $t("common.search") }}</Button
          >
          <Button
            v-has="'export'"
            type="success"
            icon="ios-cloud-download-outline"
            size="large"
            style="margin-left: 20px"
            @click="exportTable()"
            >导出</Button
          >
        </Form>
      </div>

      <div style="margin-top: 20px">
        <Table
          :columns="columns"
          :data="tableData"
          :ellipsis="true"
          :loading="loading"
        >
          <!--  <template slot-scope="{ row, index }" slot="opType">
            <span class="text">{{row.opType}}</span>
          </template>
          <template slot-scope="{ row, index }" slot="serviceUrl">
            <span class="text">{{row.serviceUrl}}</span>
          </template> -->
        </Table>
      </div>

      <div class="table-botton" style="margin-top: 15px">
        <Page
          :total="total"
          :current.sync="currentPage"
          show-total
          show-elevator
          @on-change="goPage"
        />
      </div>
    </Card>
  </div>
</template>

<script>
import { exportLogList, getLogList } from "@/api/system/logs";
import i18n from "@/locale";
export default {
  data() {
    return {
      optList: [
        {
          key: "1",
          value: i18n.t("sys.logInto"),
        },
        {
          key: "2",
          value: i18n.t("sys.logOut"),
        },
      ],
      columns: [
        {
          title: i18n.t("sys.optAccoount"),
          key: "username",
          align: "center",
        },
        {
          title: i18n.t("sys.optType"),
          key: "loginType",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center",
        render: (h, params) => {
            return h('span',params.row.loginType === '1'?'登入':'登出')
          }
        },
        {
          title: i18n.t("sys.serviceUrl"),
          key: "ip",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center",
        },
        {
          title: i18n.t("sys.optTime"),
          key: "time",
          align: "center",
        },
      ],
      public: [
        {
          title: i18n.t("common.optResult"),
          key: "result",
          align: "center",
    
        },
      ],
      tableData: [],
      loading: false,
      currentPage: 1,
      page: 0,
      total: 0,
      form: {
        wholesalerName: "",
        startTime: null,
        endTime: null,
        optType: "",
      },
      rule: {
        startTime: [
          {
            required: true,
            message: "时间不能为空",
          },
        ],
      },
    };
  },
  computed: {},
  methods: {
    // 获取列表
    goPageFirst: function (page) {
      this.$refs["form"].validate((valid) => {
        if (valid) {
             if(page === 0){
            this.currentPage =1;
          }
          this.page = page;
          this.loading = true;
          var data = {
            endTime: this.form.endTime,
            loginType: this.form.optType,
            page: page,
            pageSize: 10,
            startTime: this.form.startTime,
            username: this.form.wholesalerName.replace(/\s+$/, ""),
          };
          getLogList(data)
            .then((res) => {
              if (res && res.code == "0000") {
                this.tableData = res.data.records;
                this.total = res.data.total;
              } else {
                throw res;
              }
            })
            .catch((err) => {
                 this.tableData = [];
                this.total = 0;
              console.log(err);
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    getTime: function (times, type) {
      this.form.startTime = times[0];
      this.form.endTime = times[1];
    },

    exportTable() {


      this.$refs["form"].validate((valid) => {
        if (valid) {

     exportLogList({
        endTime: this.form.endTime,
        loginType: this.form.optType,
        startTime: this.form.startTime,
        username: this.form.wholesalerName.replace(/\s+$/, ""),
      })
        .then((res) => {
          const content = res.data;
          const fileName = "登录日志.csv"; // 导出文件名
          if ("download" in document.createElement("a")) {
            // 支持a标签download的浏览器
            const link = document.createElement("a"); // 创建a标签
            let url = URL.createObjectURL(content);
            link.download = fileName;
            link.href = url;
            link.click(); // 执行下载
            URL.revokeObjectURL(url); // 释放url
          } else {
            // 其他浏览器
            navigator.msSaveBlob(content, fileName);
          }
        })
        .catch(() => (this.downloading = false));
        }
      })

 
    },

    searchByCondition: function () {
      this.goPageFirst(0);
    },
    // 分页跳转
    goPage(page) {
      this.goPageFirst(page);
    },
  },
  mounted() {
    this.columns = this.columns.concat(this.public);
  },
  watch: {},
};
</script>
<style>
.search_head {
  width: 100%;
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: flex-start;
}

.search-btn {
  width: 100px !important;
}

.text {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  /* text-align: left; */
}
</style>
