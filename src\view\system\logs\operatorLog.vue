<template>
  <!--  操作日志  -->
  <div class="operation-log">
    <Card>
      <div class="search_head">
        <!-- <span>时间范围：</span>
        <DatePicker @on-change="getStartTime" type="date" placeholder="开始时间" style="margin: 0 10px 0 0;"></DatePicker>
        至
        <DatePicker @on-change="getEndTime" type="date" placeholder="截至时间" style="margin: 0 10px 0 10px;"></DatePicker> -->

        <Form ref="form" :label-width="0" :model="form" :rules="rule" inline>
          <Input
            v-model="form.wholesalerName"
            placeholder="请输入账号..."
            clearable
            style="width: 200px; margin-right: 10px"
          />
          <Input
            v-model="form.modularName"
            placeholder="请输入模块名称..."
            clearable
            style="width: 200px; margin-right: 10px"
          />
          <FormItem prop="startTime">
            <DatePicker
              @on-change="getTime"
              :editable="false"
              type="datetimerange"
              placeholder="选择时间段"
              clearable
              style="width: 300px; margin: 0 10px 0 0"
            ></DatePicker>
          </FormItem>
          <Button
            type="primary"
            v-preventReClick
            icon="md-search"
            @click="searchByCondition()"
            >搜索</Button
          >
          <Button
            type="success"
            icon="ios-cloud-download-outline"
            size="large"
            style="margin-left: 20px"
            @click="exportTable()"
            >导出</Button
          >
        </Form>
      </div>

      <div style="margin-top: 20px">
        <Table
          :columns="columns"
          :data="tableData"
          :ellipsis="true"
          :loading="loading"
        ></Table>
      </div>

      <div class="table-botton" style="margin-top: 15px">
        <Page
          :total="total"
          :current.sync="currentPage"
          show-total
          show-elevator
          @on-change="goPage"
        />
      </div>
    </Card>
  </div>
</template>

<script>
import { getOperateList, exportOperateLogList } from "@/api/system/logs";
import i18n from "@/locale";
import dayjs from "dayjs";
export default {
  data() {
    return {
      columns: [
        {
          title: "操作账号",
          key: "username",
          align: "center",
        },
        {
          title: "模块名称",
          key: "moduleName",
          align: "center",
        },
        {
          title: "操作类型",
          key: "operationType",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center",
          render: (h, params) => {
            let obj = {
              1: "新增",
              2: "删除",
              3: "编辑",
              4: "审核",
              5: "导出",
            };
            return h("span", obj[params.row.operationType]);
          },
        },
        {
          title: "操作内容",
          key: "content",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center",
        },

        {
          title: "IP地址",
          key: "ip",
          tooltip: true,
          tooltipTheme: "light",
          tooltipMaxWidth: 600,
          align: "center",
        },
        {
          title: "操作时间",
          key: "time",
          align: "center",
        },
      ],
      public: [
        {
          title: "操作结果",
          key: "result",
          align: "center",
        },
      ],
      tableData: [],
      loading: false,
      currentPage: 1,
      page: 0,
      total: 0,
      form: {
        wholesalerName: "",
        modularName: "",
        startTime: null,
        endTime: null,
      },
      rule: {
        startTime: [
          {
            required: true,
            message: "时间不能为空",
          },
        ],
      },
    };
  },
  computed: {},
  methods: {
    // 获取列表
    goPageFirst: function (page) {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (page === 0) {
            this.currentPage = 1;
          }
          this.page = page;
          this.loading = true;
          var data = {
            page: page,
            pageSize: 10,
            username: this.form.wholesalerName.replace(/\s+$/, ""),
            moduleName: this.form.modularName.replace(/\s+$/, ""),
            startTime: this.form.startTime,
            endTime: this.form.endTime,
          };
          getOperateList(data)
            .then((res) => {
              if (res && res.code == "0000") {
                this.tableData = res.data.records;
                this.total = res.data.total;
              } else {
                throw res;
              }
            })
            .catch((err) => {
              this.tableData = [];
              this.total = 0;
              console.log(err);
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    getTime: function (times, type) {
      this.form.startTime = times[0];
      this.form.endTime = times[1];
    },
    // getStartTime: function(time, type) {
    //   this.startTime = time
    // },
    // getEndTime: function(time, type) {
    //   this.endTime = time
    // },
    searchByCondition: function () {
      this.goPageFirst(0);
    },
    // 分页跳转
    goPage(page) {
      this.goPageFirst(page);
    },

    exportTable() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          exportOperateLogList({
            username: this.form.wholesalerName.replace(/\s+$/, ""),
            moduleName: this.form.modularName.replace(/\s+$/, ""),
            startTime: this.form.startTime,
            endTime: this.form.endTime,
          })
            .then((res) => {
              const content = res.data;
              const fileName = `操作日志-${dayjs().format(
                "YYYY年MM月DD日 HH时mm分ss秒"
              )}.csv`; // 导出文件名
              if ("download" in document.createElement("a")) {
                // 支持a标签download的浏览器
                const link = document.createElement("a"); // 创建a标签
                let url = URL.createObjectURL(content);
                link.download = fileName;
                link.href = url;
                link.click(); // 执行下载
                URL.revokeObjectURL(url); // 释放url
              } else {
                // 其他浏览器
                navigator.msSaveBlob(content, fileName);
              }
            })
            .catch(() => (this.downloading = false));
        }
      });
    },
  },
  mounted() {
    this.columns = this.columns.concat(this.public);
  },
  watch: {},
};
</script>
<style>
.search_head {
  width: 100%;
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: flex-start;
}

.search-btn {
  width: 100px !important;
}

 .ivu-tooltip-content .ivu-tooltip-inner{
max-height: 600px;
  overflow-y: auto;
}


</style>

