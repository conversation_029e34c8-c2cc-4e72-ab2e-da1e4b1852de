<template>
	<!--  角色管理  -->
	<div>
		<Card>
			<div class="search_head">
				<span style="font-weight: bold;">{{$t('sys.roleName')}}：</span><Input v-model="roleName" :placeholder="$t('sys.enterName')" clearable
					style="width: 200px ;margin-right: 10px;" />
				<Button class="search-bt" v-preventReClick type="primary" icon="md-search"
					@click="searchByCondition()">{{$t('common.search')}}</Button>
				<Button v-has="'add'" icon="md-add" type="success" @click="showUserModal(null,0)">{{$t('sys.newRole')}}</Button>
			</div>
			<div style="margin-top:20px">
				<Table :columns="columns" :data="tableData" :ellipsis="true" :loading="loading">
					<template slot-scope="{ row }" slot="priv">
						<a v-has="'view'" href="#" @click="searchLimit(row.id)">{{$t('sys.viewPermissions')}}</a>
					</template>
				</Table>
			</div>
			<div class="table-botton" style="margin-top:15px">
				<Page :page-size="pageSize" :total="total" :current.sync="currentPage" show-total show-elevator
					@on-change="goPage" />
			</div>
		</Card>
		<!-- 权限抽屉 -->
		<Drawer :title="$t('sys.rolePermissionManagement')" v-model="drawer" width="350" :mask-closable="true" :styles="styles"
			@on-close="drawer = false;setLoading = false">
			<Tree v-has="{'have':['setPriv','view']}" :data="treeData" ref="treeOne" show-checkbox multiple
				:empty-text="emptyText"></Tree>
			<Tree v-has="{'have':['view'],'haveNot':['setPriv']}" :data="treeData" ref="treeOne" :show-checkbox="false"
				multiple :empty-text="emptyText"></Tree>
			<div v-has="['setPriv']" class="demo-drawer-footer">
				<Button style="margin-right: 8px" @click="drawer = false;setLoading = false">{{$t('common.cancel')}}</Button>
				<Button type="primary" v-preventReClick @click="drawer = false;toSetRoleLimits()">{{$t('common.determine')}}</Button>
			</div>
		</Drawer>

		<Modal :title="$t('sys.editAccount')" v-model="userModal" :mask-closable="false" @on-cancel="cancelModal">
			<Form v-if="userModal" ref="editForm" :model="modalData" :rules="rules" @keydown.enter.native="addUser">
				<FormItem prop="roleName">
					<div class="input_modal">
						<span style="width: 2%;color: red;">*</span><span style="width: 13%;">{{$t('sys.roleName')}}：</span>
						<Input v-model="modalData.roleName" :placeholder="$t('sys.enterName')" style="width: 85%;" />
					</div>
				</FormItem>
			</Form>
			<div slot="footer" style="width: 100%; display: flex;align-items: center;justify-content:flex-end;">
				<Button @click="cancelModal">{{$t('common.cancel')}}</Button>
				<Button type="primary" v-preventReClick @click="addUser">{{$t('common.determine')}}</Button>
			</div>
		</Modal>

	</div>
</template>

<script>
	import list from './index.js';
	import jsonTest from './test.json'
	import {
		getRoleList,
		getRoleLimits,
		getUserLimits,
		setRoleLimits,
		addRole
	} from '@/api/system/privilege'
	export default {
		data() {
			var validateRoleName = (rule, value, callback) => {
				var regx = /^[^\s]+(\s+[^\s]+)*$/;
				if (!value || value.length == 0) {
					callback(new Error(this.$t('sys.enterName')));
				} else if (!regx.test(value)) {
					callback(new Error(this.$t('sys.characterSpaces')));
				} else {
					callback();
				}
			};
			return {
				rules: {
					roleName: [{
						validator: validateRoleName,
						trigger: 'blur'
					}]
				},
				corpId: '',
				userModal: false,
				modalData: {},
				roleName: '',
				roleId: '',
				setLoading: false,
				getLoading: false,
				phonenum: '',
				// 权限树相关设置
				emptyText: '未查询到任何权限数据',
				drawer: false, //模态
				allLimitsData: [], //当前登录用户的权限
				treeData: [], //权限列表

				//模拟数据
				data4: [],
				styles: {
					height: 'calc(100% - 55px)',
					overflow: 'auto',
					paddingBottom: '53px',
					position: 'static'
				},
				columns: [{
					title: this.$t('sys.persona'),
					key: 'roleName',
					align: 'center'
				}],
				public: [{
					title: this.$t('sys.purview'),
					slot: 'priv',
					align: 'center'
				}],
				tableData: [],
				loading: false,
				currentPage: 1,
				pageSize: 10,
				total: 0,
			}
		},
		computed: {},
		methods: {
			//获取列表
			searchByCondition: function() {
				this.goPageFirst(0)
			},
			goPageFirst: function(page) {
				this.page = page
				this.loading = true
				getRoleList({
					"pageSize": this.pageSize,
					"page": page,
					"roleName": this.roleName.trim(),
				}).then(res => {
					if (res.code === '0000') {
						this.tableData = res.data.records
						this.total = res.data.total
						this.loading = false
					} else {
						throw resp
					}
				}).catch(err => {
					this.loading = false
				})
			},
			showUserModal: function(list, type) {
				this.optType = type
				if (type == 0) { //新增
					this.modalData = {
						roleName: null,
					}
				} else { //修改
					this.modalData = list
				}
				this.userModal = true
			},
			cancelModal: function() {
				this.modalData = [{
					roleName: '',
					corp: ''
				}]
				this.userModal = false
			},
			addUser: function() {
				this.$refs.editForm.validate((valid) => {
					if (valid) {
						addRole(this.modalData).then(res => {
							if (res.code === '0000') {
								this.$Notice.success({
									title: this.$t('common.Successful'),
									desc: this.$t('sys.successAddedRole')
								})
								this.cancelModal()
								this.goPageFirst(0)
							} else {
								throw resp
							}
						}).catch(err => {
							this.loading = false
						})
					}
				})
			},
			goPage(page) {
				this.goPageFirst(page)
			},
			//点击查看权限
			searchLimit: function(params) {
				this.roleId = params
				//获取树形权限
				this.showDrawer(params)
			},
			//权限树
			showDrawer(roleId) {
				this.getLoading = true
				getUserLimits({
					userId: this.$store.state.user.userId,
					isEnglish: this.corpId && this.$i18n.locale === 'en-US' ? 1 : undefined
				}).then(res => {
					if (res.code === '0000') {
						var privList = res.data
						var allData = JSON.parse(JSON.stringify(privList).replace(/privilName/g, "title"));
						getRoleLimits({
							roleId: roleId,
							isEnglish: this.corpId && this.$i18n.locale === 'en-US' ? 1 : undefined
						}).then(resp => {
							this.treeData = this.getTreeDataUtil(allData, resp.data)
							this.drawer = true
							this.getLoading = false
						}).catch(err => {
							this.getLoading = false
							this.error(this.$t('sys.failedObtainRole'))
						})
					}
				}).catch(err => {
					this.getLoading = false
				}).finally(() => {
					this.getLoading = false
					this.loading = false
				})
			},
			getTreeDataUtil: function(allData, roleData) {

				try {
					for (var i = 0; i < roleData.length; i++) {
						for (var j = 0; j < allData.length; j++) {
							if (roleData[i].id == allData[j].id) {
								if (!roleData[i].children) { //匹配了并且无子菜单
									allData[j].expand = true
									allData[j].checked = true
									break
								} else { //匹配了并且有子菜单
									//二级菜单
									for (var k = 0; k < roleData[i].children.length; k++) {
										for (var l = 0; l < allData[j].children.length; l++) {
											if (roleData[i].children[k].id == allData[j].children[l].id) {
												if (!roleData[i].children[k].children) {
													allData[j].children[l].expand = true
													allData[j].expand = true
													allData[j].children[l].checked = true
													break
												} else {
													//三级菜单
													for (var m = 0; m < roleData[i].children[k].children.length; m++) {
														for (var n = 0; n < allData[j].children[l].children
															.length; n++) {
															if (roleData[i].children[k].children[m].id == allData[j]
																.children[l].children[n].id) {

																if (!roleData[i].children[k].children[m].children) {
																	allData[j].children[l].children[n].expand = true
																	allData[j].expand = true
																	allData[j].children[l].expand = true
																	allData[j].children[l].children[n].checked = true
																	break
																} else {

																	// 四级菜单
																	for (var o = 0; o < roleData[i].children[k]
																		.children[m].children.length; o++) {
																		for (var p = 0; p < allData[j].children[l]
																			.children[n].children.length; p++) {
																			if (roleData[i].children[k].children[m]
																				.children[o].id == allData[j].children[
																					l].children[n].children[p].id) {
																				allData[j].children[l].children[n]
																					.children[p].expand = true
																				allData[j].expand = true
																				allData[j].children[l].expand = true
																				allData[j].children[l].children[n]
																					.expand = true
																				allData[j].children[l].children[n]
																					.children[p].checked = true
																				break

																			}
																		}
																	}

																}
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					return allData
				} catch (e) {
					throw 'error'
				}
			},
			toSetRoleLimits: function() {
				this.setLoading = true
				var privilIdList = []
				try {
					privilIdList = this.$refs.treeOne.getCheckedAndIndeterminateNodes().map(function(item) {
						return {
							privilegeId: item.id,
							category: item.category
						}
					});
					setRoleLimits({
						roleId: this.roleId,
						privileges: privilIdList
					}).then(res => {
						if (res.code === '0000') {
							this.$Notice.success({
								title: this.$t('common.Successful'),
								desc: this.$t('sys.rolePpermissionsUpdated')
							})
						}
						this.setLoading = false
					}).catch(err => {
						this.setLoading = false
						this.loading = false
					})
				} catch (e) {
					this.setLoading = false
					this.loading = false
				}
			},
			error(nodesc) {
				this.$Notice.error({
					title: this.$t('sys.wrong'),
					desc: nodesc ? nodesc : this.$t('sys.Serverwrong'),
				});
			},
		},
		beforeMount: function() {
			var btnPriv = this.$route.meta.permTypes
			if (btnPriv.includes('view') || btnPriv.includes('setPriv')) {
				this.columns = this.columns.concat(this.public)
			}
		},
		mounted() {
			let lang = this.$i18n.locale
			this.corpId = sessionStorage.getItem("corpId")
			this.goPageFirst(0)
		},
		watch: {
			'getLoading': function(value) {
				if (value == true) {
					this.loadmsg = this.$Message.loading({
						content: this.$t('sys.obtainPermissionData'),
						duration: 0
					});
				} else {
					setTimeout(this.loadmsg, 1);
				}
			},

			'setLoading': function(value) {
				if (value == true) {
					this.loadmsg = this.$Message.loading({
						content: this.$t('sys.obtainPermissionData'),
						duration: 0
					});
				} else {
					setTimeout(this.loadmsg, 1);
				}
			}
		},
	}
</script>
<style>
	.search_head {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}

	.search-bt {
		width: 100px;
		margin-right: 10px;
	}

	.demo-drawer-footer {
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		border-top: 1px solid #e8e8e8;
		padding: 10px 16px;
		text-align: right;
		background: #fff;
	}
</style>