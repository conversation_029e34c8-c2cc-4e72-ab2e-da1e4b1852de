// 延时器
export function timeout (time) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve()
    }, time)
  })
}

// 模拟接口请求
export function request () {
  return jest.fn(
    (params) =>
      utils.response({})
  )
}
// 模拟接口响应
export function response (data) {
  return new Promise((resolve, reject) => {
    resolve(data)
  })
}
// 自定义mock-修改单一api响应数据
export function mockApi (mock, api, data) {
  mock[api].mockImplementation(() => utils.response(data))
}
