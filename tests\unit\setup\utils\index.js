import { timeout, request, response, mockApi } from "./api"; //api封装方法
// import { 
// 	getTablesHeader,// 获取表头
// 	getTablesData, // 获取表格数据
// 	getTablesAction, // 获取表格操作列
// 	getButton, // 获取按钮
// 	getTableButton, // 获取表格按钮
// 	getModalTitles, // 获取弹窗标题
// 	getModalCloses, // 获取弹窗关闭按钮
// 	getNotificationsContent,// 获取Notification提示
// 	removeNotifications, // 移除Notification提示
// 	getConfirmsContent, // 获取Confirm气泡确认框内容
// 	getConfirmButton, // 获取Confirm气泡确认框按钮
// 	getMessageContent,// 获取Message信息内容
// 	getFormItems, // 获取表单项
// 	getFormErrors, // 获取表单校验失败信息
// 	getSelect, // 获取下拉框
// } from "./iview-ui"; //api封装方法
global.utils = {
	// api
	timeout,
	request,
	response,
	mockApi,
	// iview-ui
	// getTablesHeader,
	// getTablesData,
	// getTablesAction,
	// getButton,
	// getTableButton,
	// getModalTitles,
	// getModalCloses,
	// getNotificationsContent,
	// removeNotifications,
	// getConfirmsContent,
	// getConfirmButton,
	// getFormItems,
	// getFormErrors,
	// getSelect,
};

