// import $ from "jquery";

// /**
//  * 获取全部表格-表头
//  * @param {wrapper}
//  * @param {scrollable}
//  * @returns {Object}
//  */
// export function getTablesHeader(wrapper) {
//   let result = {};
//   let tables = wrapper.findAll(".iv-table");

//   for (let tableIndex = 0; tableIndex < tables.length; tableIndex++) {
//     result["table-" + tableIndex] = [];
//     let headers;
//     headers = tables.at(tableIndex).find(".iv-table__header").findAll("th");

//     for (let headerIndex = 0; headerIndex < headers.length; headerIndex++) {
//       let title = headers.at(headerIndex).find(".cell").text();
//       result["table-" + tableIndex].push(title);
//     }
//   }

//   return result;
// }

// /**
//  * 获取全部表格-数据
//  * @param {wrapper}
//  * @param {scrollable}
//  * @returns {Object}
//  */
// export function getTablesData(wrapper) {
//   let result = {};
//   let tables = wrapper.findAll(".iv-table");

//   for (let tableIndex = 0; tableIndex < tables.length; tableIndex++) {
//     result["table-" + tableIndex] = {};
//     let headers;
//     headers = tables.at(tableIndex).find(".iv-table__header").findAll("th");

//     let titles = [];
//     let operation = false;
//     for (let headerIndex = 0; headerIndex < headers.length; headerIndex++) {
//       let title = headers.at(headerIndex).find(".cell").text();
//       titles.push(title);
//       if (
//         headerIndex === headers.length - 1 &&
//         headers.at(headerIndex).find(".cell").text().includes("操作")
//       ) {
//         operation = true;
//       }
//     }

//     let rows = tables
//       .at(tableIndex)
//       .find(".iv-table__body")
//       .findAll(".iv-table__row");
//     for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
//       result["table-" + tableIndex]["row-" + rowIndex] = {};
//       let tds = rows.at(rowIndex).findAll("td");
//       for (let tdIndex = 0; tdIndex < tds.length; tdIndex++) {
//         if (tdIndex < tds.length - 1 || !operation) {
//           let td = tds.at(tdIndex);
//           // 由于图片使用的el-image，它会异步渲染真实图片，优先渲染el-image__placeholder，所以同步代码中是拿不到真实图片的
//           // 解决方案：使用el-image时，添加placeholder插槽，自定义传入图片资源地址

//           if (td.findAll("img").length) {
//             result["table-" + tableIndex]["row-" + rowIndex][titles[tdIndex]] =
//               td.find("img").attributes("src");
//           }
//           if (td.findAll("video").length) {
//             result["table-" + tableIndex]["row-" + rowIndex][titles[tdIndex]] =
//               td.find("video").attributes("src");
//           }
//           if (!td.findAll("img").length && !td.findAll("video").length) {
//             result["table-" + tableIndex]["row-" + rowIndex][titles[tdIndex]] =
//               td.text();
//           }
//         }
//       }
//     }
//   }

//   return result;
// }

// /**
//  * 获取表格操作列
//  * @param {wrapper}
//  * @returns {Object}
//  */
// export function getTablesAction(wrapper) {
//   let result = {};
//   let tables = wrapper.findAll(".iv-table");

//   for (let tableIndex = 0; tableIndex < tables.length; tableIndex++) {
//     result["table-" + tableIndex] = {};
//     let headers = tables.at(tableIndex).find(".iv-table__header").findAll("th");
//     let operation = headers
//       .at(headers.length - 1)
//       .text()
//       .includes("操作");

//     if (operation) {
//       let rows = tables.at(tableIndex).findAll(".iv-table__row");

//       for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
//         let actions = rows.at(rowIndex).findAll(".cell");
//         result["table-" + tableIndex]["row-" + rowIndex] = [];

//         for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {
//           if (actionIndex === actions.length - 1) {
//             let buttons = actions.at(actionIndex).findAll(".iv-button");
//             for (
//               let buttonIndex = 0;
//               buttonIndex < buttons.length;
//               buttonIndex++
//             ) {
//               result["table-" + tableIndex]["row-" + rowIndex].push(
//                 buttons.at(buttonIndex).text(),
//               );
//             }
//           }
//         }
//       }
//     }
//   }

//   return result;
// }

// /**
//  * 获取按钮
//  * @param {wrapper}
//  * @param {name}
//  * @param {index}
//  * @returns {Dom}
//  */
// export function getButton(wrapper, name, index) {
//   let result;
//   let buttons = wrapper.findAll(".iv-button");

//   try {
//     for (let buttonIndex = 0; buttonIndex < buttons.length; buttonIndex++) {
//       let button = buttons.at(buttonIndex);
//       let buttonName = button.find("span").text();
//       if (buttonName === name) {
//         result = button;
//         throw new Error();
//       }
//     }
//   } catch (error) {
//     return result;
//   }
// }

// /**
//  * 获取表格操作列按钮
//  * @param {wrapper}
//  * @param {name}
//  * @param {rowNumber}
//  * @param {tableNumber}
//  * @returns {Dom}
//  */
// export function getTableButton(wrapper, name, rowNumber, tableNumber) {
//   let result;
//   let tables = wrapper.findAll(".iv-table");

//   try {
//     if (!tableNumber && tableNumber !== 0) {
//       for (let tableIndex = 0; tableIndex < tables.length; tableIndex++) {
//         let rows = tables.at(tableIndex).findAll(".iv-table__row");
//         findButton(rows);
//       }
//     } else {
//       let rows = tables.at(tableNumber).findAll(".iv-table__row");
//       findButton(rows);
//     }
//   } catch (error) {
//     return result;
//   }

//   // 获取button
//   function findButton(rows) {
//     for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
//       let actions = rows.at(rowIndex).findAll(".cell");

//       for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {
//         if (actionIndex === actions.length - 1) {
//           let titles = [];
//           let buttons = actions.at(actionIndex).findAll(".iv-button");
//           for (
//             let buttonIndex = 0;
//             buttonIndex < buttons.length;
//             buttonIndex++
//           ) {
//             let title = buttons.at(buttonIndex).text();
//             titles.push(title);
//           }
//           if (rowNumber || rowNumber === 0) {
//             if (rowIndex === rowNumber && titles.includes(name)) {
//               result = actions
//                 .at(actionIndex)
//                 .findAll(".iv-button")
//                 .at(titles.indexOf(name));
//               throw new Error();
//             }
//           } else {
//             if (titles.includes(name)) {
//               result = actions
//                 .at(actionIndex)
//                 .findAll(".iv-button")
//                 .at(titles.indexOf(name));
//               throw new Error();
//             }
//           }
//         }
//       }
//     }
//   }
// }

// /**
//  * 获取弹窗标题-自定义title命名为modalTitle
//  * @param {wrapper}
//  * @returns {Array}
//  */
// export async function getModalTitles(wrapper) {
//   await wrapper.vm.$nextTick();
//   let result = [];
//   let root = $("body");

//   if (Array.from(root.find(".iv-modal__wrapper")).length) {
//     // 兼容el-dialog组件，append-to-body为true
//     Array.from(root.find(".iv-modal__wrapper")).forEach(modal => {
//       let title = $(modal).find(".iv-modal__title").text();
//       result.push(title);
//     });
//   } else if (wrapper.findAll(".iv-modal__wrapper").length) {
//     // 兼容el-dialog组件，append-to-body为false
//     let elDialog = wrapper.findAll(".iv-modal__wrapper");
//     for (let elIndex = 0; elIndex < elDialog.length; elIndex++) {
//       let title = elDialog.at(elIndex).find(".iv-modal__title").text();
//       result.push(title);
//     }
//   } else if (wrapper.findAll(".eb-modal-main").length) {
//     // 兼容eb-dialog组件
//     let ebDialog = wrapper.findAll(".eb-modal-main");
//     for (let ebIndex = 0; ebIndex < ebDialog.length; ebIndex++) {
//       let title = ebDialog.at(ebIndex).find(".eb-dmodal-title").text();
//       result.push(title);
//     }
//   }

//   return result;
// }

// /**
//  * 获取弹窗标题-自定义title命名为modalTitle
//  * @param {wrapper}
//  * @returns {Array}
//  */
// export async function getModalCloses(wrapper, index) {
//   await wrapper.vm.$nextTick();
//   let result = [];
//   let root = $("body");

//   if (Array.from(root.find(".iv-modal__wrapper")).length) {
//     // 兼容el-dialog组件，append-to-body为true
//     Array.from(root.find(".iv-modal__wrapper")).forEach(modal => {
//       let title = $(modal).find(".iv-modal__headerbtn");
//       result.push(title);
//     });
//   } else if (wrapper.findAll(".iv-modal__wrapper").length) {
//     // 兼容el-dialog组件，append-to-body为false
//     let elDialog = wrapper.findAll(".iv-modal__wrapper");
//     for (let elIndex = 0; elIndex < elDialog.length; elIndex++) {
//       let title = elDialog.at(elIndex).find(".iv-modal__headerbtn");
//       result.push(title);
//     }
//   } else if (wrapper.findAll(".eb-modal-main").length) {
//     // 兼容eb-dialog组件
//     let ebDialog = wrapper.findAll(".eb-modal-main");
//     for (let ebIndex = 0; ebIndex < ebDialog.length; ebIndex++) {
//       let title = ebDialog.at(ebIndex).find(".eb-modal-close");
//       result.push(title);
//     }
//   }

//   return index || index === 0 ? result[index] : result[result.length - 1];
// }

// /**
//  * 获取全部notification内容
//  * @param {wrapper}
//  * @returns {Array}
//  */
// export async function getNotificationsContent(wrapper) {
//   await wrapper.vm.$nextTick();
//   let result = [];

//   let root = $("body");
//   Array.from(root.find(".iv-Notice")).forEach(Notice => {
//     let title = $(Notice).find(".iv-Notice__title").text();
//     let message = $(Notice).find(".iv-Notice__content").text();
//     result.push({ title, message });
//   });

//   return result;
// }

// /**
//  * 删除全部notification
//  * @param {wrapper}
//  */
// export async function removeNotifications(wrapper) {
//   await wrapper.vm.$nextTick();

//   let root = $("body");
//   Array.from(root.find(".iv-Notice")).forEach(Notice => {
//     Notice.parentNode.removeChild(Notice);
//   });
// }

// /**
//  * 获取confirm内容
//  * @param {wrapper}
//  * @returns {Array}
//  */
// export async function getConfirmsContent(wrapper) {
//   await wrapper.vm.$nextTick();
//   let result = [];

//   let root = $("body");
//   Array.from(root.find(".iv-message-box__wrapper")).forEach(confirm => {
//     let title = $(confirm).find(".iv-message-box__title").text();
//     let content = $(confirm).find(".iv-message-box__message").text();
//     result.push({ title, content });
//   });

//   return result;
// }

// /**
//  * 获取confirm按钮
//  * @param {wrapper}
//  * @param {name}
//  * @returns {Dom}
//  */
// export async function getConfirmButton(wrapper, name) {
//   await wrapper.vm.$nextTick();
//   let result;

//   let root = $("body");
//   try {
//     Array.from(root.find(".iv-message-box")).forEach(confirm => {
//       let buttons = $(confirm).find(".iv-button");
//       Array.from(buttons).forEach(button => {
//         // 此处要去除按钮首尾空格，源码中按钮文案有换行
//         if ($(button).text().trim() === name) {
//           result = $(button);
//           throw new Error();
//         }
//       });
//     });
//   } catch (error) {
//     return result;
//   }
// }

// /**
//  * 获取message内容
//  * @param {wrapper}
//  * @returns {Array}
//  */
// export async function getMessageContent(wrapper) {
//   await wrapper.vm.$nextTick();
//   let result = [];

//   let root = $("body");
//   Array.from(root.find(".iv-message")).forEach(confirm => {
//     let content = $(confirm).find(".iv-message__content").text();
//     result.push(content);
//   });

//   return result;
// }

// /**
//  * 获取全部表单项信息
//  * @param {wrapper}
//  * @returns {Array}
//  */
// export async function getFormItems(wrapper) {
//   await wrapper.vm.$nextTick();
//   let res = [];

//   // 后面的元素会覆盖前面的
//   let types = [
//     "iv-radio",
//     "iv-radio-group",
//     "iv-checkbox",
//     "iv-checkbox-group",
//     "iv-input",
//     "iv-input-number",
//     "iv-select",
//     "iv-cascader",
//     "iv-switch",
//     "iv-slider",
//     "iv-date-editor--time-select",
//     "iv-date-editor--time",
//     "iv-date-editor--timerange",
//     "iv-date-editor--date",
//     "iv-date-editor--dates",
//     "iv-date-editor--week",
//     "iv-date-editor--month",
//     "iv-date-editor--months",
//     "iv-date-editor--year",
//     "iv-date-editor--years",
//     "iv-date-editor--daterange",
//     "iv-date-editor--monthrange",
//     "el-date-editor--datetime",
//     "iv-date-editor--datetimerange",
//     "iv-upload",
//     "iv-rate",
//     "iv-color-picker",
//     "iv-transfer",
//   ];
//   let formItems = $(
//     $("body").find(".iv-form")[$("body").find(".iv-form").length - 1],
//   ).find(".iv-form-item");
//   if (!formItems.length) {
//     formItems = $(
//       $(wrapper.html()).find(".iv-form")[
//         $(wrapper.html()).find(".iv-form").length - 1
//       ],
//     ).find(".iv-form-item");
//   }
//   Array.from(formItems).forEach(formItem => {
//     let required = false;
//     let classArr = $(formItem).attr("class").split(" ");

//     if (classArr.filter(item => item.includes("required")).length) {
//       required = true;
//     }

//     let label = $(formItem).find(".iv-form-item__label").text();

//     let disabled = $(formItem).html().includes("disabled");

//     let type = "";
//     let htmlContent = $(formItem).find(".iv-form-item__content").html();
//     types.forEach(item => {
//       if (htmlContent.includes(item)) {
//         if (item === "iv-date-editor--time-select") {
//           type = "iv-time-select";
//         } else if (
//           item === "iv-date-editor--time" ||
//           item === "iv-date-editor--timerange"
//         ) {
//           type = "iv-time-picker";
//         } else if (
//           item === "iv-date-editor--date" ||
//           item === "iv-date-editor--dates" ||
//           item === "iv-date-editor--week" ||
//           item === "iv-date-editor--month" ||
//           item === "iv-date-editor--months" ||
//           item === "iv-date-editor--year" ||
//           item === "iv-date-editor--years" ||
//           item === "iv-date-editor--daterange" ||
//           item === "iv-date-editor--monthrange" ||
//           item === "iv-date-editor--datetime" ||
//           item === "iv-date-editor--datetimerange"
//         ) {
//           type = "iv-date-picker";
//         } else {
//           type = item;
//         }
//       }
//     });

//     res.push({
//       label: label,
//       required: required,
//       type: type,
//       disabled: disabled,
//     });
//   });

//   return res;
// }

// /**
//  * 获取全部表单报错信息
//  * @param {wrapper}
//  * @returns {Array}
//  */
// export async function getFormErrors(wrapper) {
//   await wrapper.vm.$nextTick();

//   let result = [];

//   let formItems = $(
//     $("body").find(".iv-form")[$("body").find(".iv-form").length - 1],
//   ).find(".iv-form-item");
//   if (!formItems.length) {
//     formItems = $(
//       $(wrapper.html()).find(".iv-form")[
//         $(wrapper.html()).find(".iv-form").length - 1
//       ],
//     ).find(".iv-form-item");
//   }
//   Array.from(formItems).forEach(formItem => {
//     let field = $(formItem).find(".iv-form-item__label").attr("for");
//     let label = $(formItem).find(".iv-form-item__label").text();
//     let error = $(formItem).find(".iv-form-item__error").text().trim();

//     result.push({
//       field,
//       label,
//       error,
//     });
//   });

//   return result;
// }

// /**
//  * 获取select下拉框
//  * @param {wrapper}
//  * @param {index}
//  * @returns {Dom}
//  */
// export async function getSelect(wrapper, index) {
//   await wrapper.vm.$nextTick();
//   let select = wrapper.findAll(".iv-select").at(index);

//   return select;
// }