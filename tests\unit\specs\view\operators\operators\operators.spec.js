import { mount } from "@vue/test-utils";
import operators from "@/view/operators/index.vue";
import mockData from "./mockData";

const operatorsApi = require("@/../tests/unit/setup/api/operators.mock");

utils.mockApi(
  operatorsApi,
  "opsearch",
  mockData.success.opsearch,
);

// describe("运营商列表", () => {
//   const wrapper = mount(operators);
//   const _this = wrapper.vm;

//   it("运营商列表-查询失败", async () => {
//     utils.mockApi(
//       operatorsApi,
//       "opsearch",
//       mockData.failure.opsearch,
//     );
//     _this.pageList = [];
//     _this.total = 0;
//     _this.loading = false;
//     await utils.getButton(wrapper, "搜索").trigger("click");
//     expect(_this.data).toEqual([]);
//     expect(_this.total).toBe(0);
//     expect(_this.loading).toBe(true);
//   });

//   it("运营商列表-查询成功", async () => {
//     utils.mockApi(
//       operatorsApi,
//       "opsearch",
//       mockData.success.opsearch,
//     );
//     _this.pageList = [];
//     _this.total = 0;
//     _this.loading = false;
//     await utils.getButton(wrapper, "搜索").trigger("click");
//     let expectData = mockData.success.getOpsearch.data;
//     expect(_this.data).toEqual(expectData.list);
//     expect(_this.total).toBe(expectData.total);
//     expect(_this.loading).toBe(false);
//   });

//   it("运营商列表-表头", () => {
//     let headers = utils.getTablesHeader(wrapper);
//     let firstTableHeaders = headers["table-0"];
//     let expectData = ["国家名称", "所属大洲", "MCC", "是否为热门国家", "运营商", "操作"];
//     expect(firstTableHeaders).toEqual(expectData);
//   });

  
// });