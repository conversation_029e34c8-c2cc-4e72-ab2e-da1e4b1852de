const mockData = {
	success:{//成功响应
		//国家关联组查询接口
		upccList:{ 
			code: "0000",
			msg: "Success",
			data:{
				countId: null,
				current: 1,
				hitCount: false,
				maxLimit: null,
				optimzeCountSql: true,
				orders: [],
				pages: 1, //页数		
				searchCount: true,
				size: 10, //当前页10条数据
				total: 2,
				records:[
					{
						createTime: "2023-03-15T09:02:00.000+0000",
						signId: "1",
						supportHotspot: "1",
						templateDesc: "测试无上限模板2",
						templateId: "P23031517021451",
						templateName: "xjc测试无上限模板",
						updateTime: "2023-03-15T09:02:00.000+0000",
					},
					{
						createTime: "2023-03-15T09:00:59.000+0000",
						signId: "25",
						supportHotspot: "1",
						templateDesc: "测试档位模板2",
						templateId: "P23031517014745",
						templateName: "xjc测试档位模板2",
						updateTime: "2023-03-15T09:00:59.000+0000",
					}
				]
			}
		}
	},
	failure:{//失败响应
		upccList: {
				code: 500,
				data: null,
				message: "操作失败",
		},
	}
}
export default mockData;