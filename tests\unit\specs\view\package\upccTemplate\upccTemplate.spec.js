import { mount } from "@vue/test-utils";
import upcc from "@/view/package/upccTemplate/index.vue";
import mockData from "./mockData";

const upccTemplateApi = require("@/../tests/unit/setup/api/upccTemplate.mock");

utils.mockApi(
  upccTemplateApi,
  "upccList",
  mockData.success.upccList,
);

describe("UPCC速度模板列表", () => {
  const wrapper = mount(upcc);
  const _this = wrapper.vm;

  it("UPCC速度模板列表-查询失败", async () => {
    utils.mockApi(
      upccTemplateApi,
      "upccList",
      mockData.failure.upccList,
    );
    _this.pageList = [];
    _this.total = 0;
    _this.loading = false;
    await utils.getButton(wrapper, "搜索").trigger("click");
    expect(_this.data).toEqual([]);
    expect(_this.total).toBe(0);
    expect(_this.loading).toBe(true);
  });

  it("UPCC速度模板列表-查询成功", async () => {
    utils.mockApi(
      upccTemplateApi,
      "upccList",
      mockData.success.upccList,
    );
    _this.pageList = [];
    _this.total = 0;
    _this.loading = false;
    await utils.getButton(wrapper, "搜索").trigger("click");
    let expectData = mockData.success.getUpccList.data;
    expect(_this.data).toEqual(expectData.list);
    expect(_this.total).toBe(expectData.total);
    expect(_this.loading).toBe(false);
  });

  it("素材列表页-表头", () => {
    let headers = utils.getTablesHeader(wrapper);
    let firstTableHeaders = headers["table-0"];
    let expectData = ["模板ID", "模板名称", "模板描述", "是否支持热点", "签约模板ID", "操作"];
    expect(firstTableHeaders).toEqual(expectData);
  });

  
});