// import { mount } from "@vue/test-utils";
// import associationGroup from "@/view/productMngr/associationGroup/index.vue";
// import mockData from "./mockData";

// const associationGroupApi = require("@/../tests/unit/setup/api/associationGroup.mock");

// utils.mockApi(
//   associationGroupApi,
//   "getCardPoolGroup",
//   mockData.success.getCardPoolGroup,
// );

// describe("关联组列表", () => {
//   const wrapper = mount(associationGroup);
//   const _this = wrapper.vm;

//   it("关联组列表-查询失败", async () => {
//     utils.mockApi(
//       materialListApi,
//       "getMaterialList",
//       mockData.failure.getMaterialList,
//     );
//     _this.pageList = [];
//     _this.total = 0;
//     _this.loading = false;
//     await utils.getButton(wrapper, "搜索").trigger("click");
//     expect(_this.pageList).toEqual([]);
//     expect(_this.total).toBe(0);
//     expect(_this.loading).toBe(true);
//   });

//   it("关联组列表-查询成功", async () => {
//     utils.mockApi(
//       materialListApi,
//       "getMaterialList",
//       mockData.success.getMaterialList,
//     );
//     _this.pageList = [];
//     _this.total = 0;
//     _this.loading = false;
//     await utils.getButton(wrapper, "搜索").trigger("click");
//     let expectData = mockData.success.getMaterialList.data;
//     expect(_this.pageList).toEqual(expectData.list);
//     expect(_this.total).toBe(expectData.total);
//     expect(_this.loading).toBe(false);
//   });

//   it("素材列表页-表头", () => {
//     let headers = utils.getTablesHeader(wrapper);
//     let firstTableHeaders = headers["table-0"];
//     let expectData = ["素材名称", "素材", "所属分类", "创建时间", "操作"];
//     expect(firstTableHeaders).toEqual(expectData);
//   });

  
// });
// const Storage = require('dom-storage');
// global.localStorage = new Storage(null, {strict: true});
// window.localStorage = global.localStorage;